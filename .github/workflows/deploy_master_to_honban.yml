# This is a basic workflow to help you get started with Actions

name: Deploy master to honban server

# Controls when the workflow will run
on:
  # Triggers the workflow on push or pull request events but only for the master branch
  push:
    branches: [ master ]

# A workflow run is made up of one or more jobs that can run sequentially or in parallel
jobs:
  # This workflow contains a single job called "build"
  build:
    # The type of runner that the job will run on
    runs-on: ubuntu-latest

    # Steps represent a sequence of tasks that will be executed as part of the job
    steps:
      - name: Deploy to admin01
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.ADMIN8_SERVER_IP }}
          username: ${{ secrets.ADMIN_SERVER_USERNAME }}
          port: ${{ secrets.ADMIN_SERVER_SSH_PORT }}
          key: ${{ secrets.ADMIN8_SERVER_SSH_PRIVATE_KEY }}
          script: |
            cd /var/www/bot
            git pull
            cd reactjspro/
            export PATH=/home/<USER>/node-v16.14.2-linux-x64/bin:$PATH
            npm install
            npm run build
            
      - name: Deploy to admin02
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.ADMIN02_SERVER_IP }}
          username: ${{ secrets.ADMIN_SERVER_USERNAME }}
          port: ${{ secrets.ADMIN_SERVER_SSH_PORT }}
          key: ${{ secrets.ADMIN8_SERVER_SSH_PRIVATE_KEY }}
          script: |
            cd /var/www/bot
            git pull
            cd reactjspro/
            export PATH=/home/<USER>/node-v16.14.2-linux-x64/bin:$PATH
            npm install
            npm run build
