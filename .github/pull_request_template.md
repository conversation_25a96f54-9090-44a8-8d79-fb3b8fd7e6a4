## チケットへのリンク

* https://example.com

## 実装種類

- [ ] バグ対応（Branch名は「bug/」となっているか？）
- [ ] 機能改善
- [ ] 新機能

## Bug分析

- [ ] バグの再現は行ったか(事象再現する環境、対象データ、前提条件、手順の明確化)
- [ ] 原因究明
    - [ ] ロジック間違い
    - [ ] 環境、設定ミス
    - [ ] 仕様問題
    - [ ] 単純ミス
    - [ ] その他
- 原因詳細

## やったこと

* このプルリクで何をしたのか？

## やらないこと

* このプルリクでやらないことは何か？（あれば。無いなら「無し」でOK）（やらない場合は、いつやるのかを明記する。）

## 影響範囲

* どの機能、どの画面、どの処理に影響するか(ソースの呼び出し元を洗い出して調査すること)

## 動作確認
#### 再現に必要な条件
- ボット
   - [ ] 通常ボットで確認（bot_id=17）
   - [ ] 親ボットで確認（bot_id=）
   - [ ] 子ボットで確認（bot_id=, parent_bot_id=）
- その他の手順


### 画面ごと(個別)
- [ ] 

### 画面ごと(共通)
- [ ] バグそのものの改善確認 OR 機能追加の確認
- [ ] その他影響範囲の確認
- [ ] リグレーションテストの実施(直接影響しない箇所、関連の基本機能、基本画面の操作)
- [ ] 静的エラーが出ていないことの確認（VSCodeの拡張機能を使って、未定義の変数を利用していないかなどを確認）
- [ ] ブラウザの検証モードでエラーが出ていないことの確認

### チェックリスト
https://www.notion.so/activalues/3581545c5f234fb7b131dc0b78a391b6
「管理画面共通」と「該当機能」をチェックし、確認したことを以下に記載する
管理画面共通
- [ ] 

該当機能
- [ ] 

## レビュー前チェックリスト(必須)
以下の項目を全てチェックしてからレビュワーに依頼してください
動作確認
- [ ] 影響範囲を記載した
- [ ] 画面ごと(個別)の確認事項を全て確認した
- [ ] 画面ごと(共通)の確認事項を全て確認した
- [ ] チェックリストを確認し、必要事項を記載・確認した

リリースに必要な観点
- [ ] 日本語チェック(日本語ネイティブが違和感がないか確認する)
- [ ] リリース履歴に投稿する準備はできているか（必要あれば変更前の画面のスクショを撮る）

## その他(任意)

- [ ] データ移行必要 => データ移行の準備
- [ ] DB変更 => テーブル追加、テーブル変更、テーブル削除必要
- [ ] サービス再起動、サーバ再起動必要
- [ ] サーバーの設定変更、施設ごとの設定変更必要

## レビュワーへの参考情報(任意・実装上の懸念点や注意点などあれば記載）

