<script>
const _paging = <?php echo json_encode($paging) ?>;
</script>

<!-- CSS Styles for Batch-specific Elements -->
<style>
/* Status badges with specific colors */
.status-badge {
    padding: 4px 8px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: bold;
}

.status-pending { background: #6c757d; color: white; }
.status-running { background: #ffc107; color: black; }
.status-success { background: #28a745; color: white; }
.status-failed { background: #dc3545; color: white; }
.status-skipped { background: #17a2b8; color: white; }

/* Child row styling for DataTables */
.child-detail-row {
    background: #f8f9fa !important;
}

.child-detail-row td {
    border-top: none !important;
    padding: 0 !important;
}

.child-row-content {
    padding: 15px;
    background: #f8f9fa;
    border-radius: 5px;
    border-left: 3px solid #007bff;
}

.child-row-content .info-group {
    margin-bottom: 15px;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    background: white;
}

.child-row-content .info-group-header {
    margin: 0;
    padding: 10px 15px;
    background: #e9ecef;
    border-bottom: 1px solid #dee2e6;
    font-size: 14px;
}

.child-row-content .info-group-body {
    padding: 15px;
}

.child-row-content .info-row {
    display: flex;
    margin-bottom: 8px;
}

.child-row-content .info-label {
    width: 150px;
    font-weight: bold;
    color: #495057;
}

.child-row-content .info-value {
    flex: 1;
    color: #212529;
}

.child-row-content .error-message {
    color: #dc3545;
    font-family: monospace;
    font-size: 12px;
}

.js-data-table td, .js-data-table th {
    vertical-align: middle !important;
}
</style>


<?php 
$success_message = Session::instance()->get_once('adminbatch_success_message');
$error_message = Session::instance()->get_once('adminbatch_error_message');
?>
<?php if ($success_message): ?>
    <div class="alert alert-success">
        <i class="fa fa-check-circle"></i> <?php echo HTML::chars($success_message); ?>
    </div>
<?php endif; ?>
<?php if ($error_message): ?>
    <div class="alert alert-danger">
        <i class="fa fa-exclamation-circle"></i> <?php echo HTML::chars($error_message); ?>
    </div>
<?php endif; ?>

<!-- Filter Section -->
<div class="content-container light-gray">
        <div class="form-group">
            <label class="control-label col-md-1" style="width: 80px;">実行日範囲</label>
            <div class="col-md-4" style="width: 270px;">
                <input name="start_date" id="start_date" value="<?php echo HTML::chars($filters['start_date']); ?>"
                    style="float:left;height: 28px;" class="form-control form-control-inline input-small date-picker"
                    size="16" data-date-format="yyyy-mm-dd" type="text" />
                <input name="end_date" id="end_date" value="<?php echo HTML::chars($filters['end_date']); ?>"
                    style="height: 28px; float:left; margin-left:10px;"
                    class="form-control form-control-inline input-small date-picker" size="16" data-date-format="yyyy-mm-dd"
                    type="text" />
            </div>
            <div class="col-md-2">
                <select id="batch_type" name="batch_type" class="form-control">
                    <?php foreach ($batch_type_options as $value => $label): ?>
                        <option value="<?php echo HTML::chars($value); ?>" 
                                <?php echo ($filters['batch_type'] == $value) ? 'selected' : ''; ?>>
                            <?php echo HTML::chars($label); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-2">
                <select id="execution_status" name="execution_status" class="form-control">
                    <?php foreach ($execution_status_options as $value => $label): ?>
                        <option value="<?php echo HTML::chars($value); ?>" 
                                <?php echo ($filters['execution_status'] == $value) ? 'selected' : ''; ?>>
                            <?php echo HTML::chars($label); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
        </div>
        
        <div class="form-group">
        <label class="control-label col-md-1" style="width: 80px;">
            <?php echo __('admin.common.label.keyword') ?>
        </label>
            <div class="col-md-4">
                <input name="keyword" id="keyword" value="<?php echo HTML::chars($filters['keyword']); ?>" 
                       class="form-control" type="text" placeholder="バッチ名、タイプなど" />
            </div>
            <div class="col-md-1 ml20">
                <span class="btn-smaller btn-yellow" id="searchButton"><i class="fa fa-search mr10"></i>
                    <?php echo __('admin.common.button.search') ?>
                </span>
            </div>
        </div>
</div>

<!-- Manual Execution Section -->
<div class="content-container" style="padding-left: 0;">
    <div style="display: flex;justify-content: end;">
        <div>
            <div class="col-md-2" style="width: auto; min-width: 250px;">
                <select id="manual_batch_type" name="manual_batch_type" class="form-control" required style="width: 100%;">
                    <option value="">バッチ選択</option>
                    <?php foreach ($batch_type_options as $value => $label): ?>
                        <?php if (!empty($value)): ?>
                            <option value="<?php echo HTML::chars($value); ?>">
                                <?php echo HTML::chars($label); ?>
                            </option>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-1" style="width: auto;">
                <button type="button" class="btn-smaller btn-red" id="manualExecuteButton" style="white-space: nowrap; padding: 5px 12px;">
                    <i class="fa fa-play mr10"></i><?php echo __('admin.batch.button.run.manual') ?>
                </button>
            </div>

        </div>
    </div>
</div>

<!-- Hidden input for manual batch type submission -->
<input type="hidden" id="manual_batch_type_submit" name="manual_batch_type_submit" value="" />


<!-- Data Table -->
<div class="content-container white border">
    <div class="portlet-body">
        <table class="table table-striped table-bordered table-hover js-data-table">
            <thead>
                <tr>
                    <th width="15%">実行日</th>
                    <th width="20%">バッチ</th>
                    <th width="20%">タイプ</th>
                    <th width="15%">ステータス</th>
                    <th width="15%">優先度</th>
                    <th width="15%">詳細情報</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($executions as $execution): ?>
                    <tr class="execution-row" data-execution-id="<?php echo $execution['id']; ?>" data-execution-details="<?php echo HTML::chars(json_encode($execution)); ?>">
                        <td class="text-center"><?php echo HTML::chars($execution['execution_date']); ?></td>
                        <td><?php echo HTML::chars($execution['batch_name'] ?? '-'); ?></td>
                        <td>
                            <?php 
                            echo HTML::chars($execution['batch_type']);
                            ?>
                        </td>
                        <td class="text-center">
                            <span class="status-badge <?php echo $execution['status_info']['class']; ?>">
                                <?php echo HTML::chars($execution['status_info']['label']); ?>
                            </span>
                        </td>
                        <td class="text-center"><?php echo HTML::chars($execution['priority_label']); ?></td>
                        <td class="text-center">
                            <button class="btn btn-blue btn-info toggle-details" data-target="details-<?php echo $execution['id']; ?>">
                                <i class="fa fa-plus"></i>
                            </button>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        
    </div>
</div>



