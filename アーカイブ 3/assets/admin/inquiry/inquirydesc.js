$(function(){
	talkmgr.load();
	if ($("#message").val() != '') {
		alertautomessage($("#message").val());
	}

	$('select').change(function() {
		var val = $(this).val();
		var obj = $(this).parent().next().next().find('.pc');
		var pan = $(this).parent().next().next().find('.skill');
		if (val == '') {
			obj.val($('#url').val());
			obj.css("width", "");
			pan.css("display", "none");
		}
		else {
			obj.css("width", "");
			obj.val('');
			pan.css("display", "block");
		}
		/*
		if (val == 'BTN_DETAIL') {
			obj.val($('#url').val());
			obj.css("width", "");
			pan.css("display", "none");
		}
		else if (val == 'BTN_ACCESS') {
			obj.val($('#map_url').val());
			obj.css("width", "");
			pan.css("display", "none");
		}
		else if (val == 'BTN_TEL') {
			obj.val($('#tel').val());
			obj.css("width", "");
			pan.css("display", "none");
		}
		else if (val == 'BTN_POSTBACK') {
			obj.css("width", "");
			obj.val('');
			pan.css("display", "block");
		}
		*/
	});
	$(document).on('click','.alert-dismissable', function(e){
		e.preventDefault();
		_skill_item = $(this);
		$("#skill_title").html($(this).parent().parent().prev().prev().children('select').find("option:selected").text());
		showSkillBox(_skill_item);
	});

	$('.js-tranlate').click(function(){
		if ($('.js-tranlate-from-lang').attr('data-value') == '') return;
		const text = $('.js-tranlate-from-lang').find('.talkappi-dropdown-selected-text').text();
		talkmgr.confirm(text + TalkappiMessageAdmin.inquirydesc.label.translate_confirm_title, '', function(result){
			if (result.button == 'confirm') {
				talkmgr.submit('translate');
			}
		});
	});

	talkmgr.on('select', '.js-brief', function(e){
		if (e.data.code == 0) {
			$('.js-sell-point').slideUp(0);
		}
		else {
			$('.js-sell-point').slideDown(0);
		}
	});


	$("#saveButton").click(function(){
		if (!checkTranslate()) {
			return;
		}
		talkmgr.warning(TalkappiMessageAdmin.common.message.save_confirm, TalkappiMessageAdmin.common.message.save_confirm_detail, function(result){
			if (result.button == 'confirm') {
				let i = 0;
				let savedData = [];
				const extra_info = $(".js-description-section-main-container").find(".js-description-section-container");
				extra_info.each(function () {
					if ($(this).hasClass('for-clone')) return;
					const title = $($(this).find(".js-survey-description-section-input")).val();
					const description = talkmgr.data($(this).find(".js-survey-section-contents-textarea"));
					const fold = $(this).find(".js-talkappi-radio-option.selected").attr("data-value");
					const extra =
					{
						"title" : title,
						"description": description,
						"fold": fold || "show",
					}
					if (title !== "" || description !== "") {
						savedData.push(extra);
					}
					i++;
				});
		
				$("#description_extra").val(JSON.stringify(savedData));
		
				const description = talkmgr.data('.js-survey-summary-textarea');
				$("#description").val(description);
		
				// 送信完了画面
				let completeAreaTitle = $(".js-complete-title").val(); // 受付完了しました。
				let completeAreaIcon = ""; // アイコン
				let completeAreaDesc = ""; // この度は...
		
				if (!$($(".js-complete-summary-textarea")).val() == "") {
					completeAreaDesc = $(".js-complete-summary-textarea").val();
				} else {
					completeAreaDesc = " ";
				}
		
				const completeArea =
				{
					'title': completeAreaTitle,
					'description': completeAreaDesc,
				}
		
				$("#complete_area").val(JSON.stringify(completeArea));
				
				// アクション
				let actions = []
				let action = {};
				let actionTitle = $(".js-action-input").val(); //HPに戻る
				let actionUrl = $(".js-description-action-input").val(); // url
				
				actionTitle ? action['title'] = actionTitle : null;
				actionUrl ? action['url'] = actionUrl : null;
				
				Object.keys(action).length && actions.push(action);
				$("#actions").val(JSON.stringify(actions));
		
				const mainpic = [];
				$('.js-main-pic-upload').each(function() {
					const pic = talkmgr.data(this);
					if (pic != '') {
						mainpic.push(pic);
					}
				});
				$("#inquiry_image").val(JSON.stringify(mainpic));
				talkmgr.submit();
			};
		});
	});

	$('#flg_mobile').on('switchChange.bootstrapSwitch', function (event, state) {
		if ($(this).is(':checked')) {
			$("#btn1_url_sp").show();
			$("#btn2_url_sp").show();
			$("#btn3_url_sp").show();
		}
		else {
			if ($("input[name='btn1_url_sp']").val()=='') $("#btn1_url_sp").hide();
			if ($("input[name='btn2_url_sp']").val()=='') $("#btn2_url_sp").hide();
			if ($("input[name='btn3_url_sp']").val()=='') $("#btn3_url_sp").hide();
		}
	});
	
	$(document).on("click",'.js-back-to-inquirys' ,function(e) {
		talkmgr.url(`/${(_inquiry_div === '9' || _inquiry_div === '') ? 'admininquiry' : 'adminorder'}/inquirys`);
	});

	window.talkappi_admin_setupMultilingualReflect({
		from_lang_cd: _from_lang_cd,
		to_lang_cds: _to_lang_cds,
		has_reflect_without_translating: false,
		has_native_translate: false,
		admin_lang: _admin_lang_cd,
	  });

	  function checkTranslate() {
		if ($('input[name="translate"]').length > 0) {
		  const validateResult = window.talkappi_admin_validateMultilingualReflect();
		  if (validateResult !== true) {
			alertmessage('error', TalkappiMessageAdmin.common.message.error, TalkappiMessageAdmin.common.message.error[validateResult]);
			return false;
		  } else {
			return true;
		  }
		}
		return true;
	  }
});

$(document).ready(function () {
	
	function createSummer() {
    // リッチテキストの設定
    $('.js-section-editor').summernote({
      height: 380,
      fontsize:'11',  // 初期値 = 12
      lang: 'ja-JP',
			fontSizes: ['10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23', '24'],
      toolbar: [
        ['style', ['style']],
        ['font', ['bold', 'underline','italic', 'clear']],
        ['color', ['color']],
        ['fontsize', ['fontsize']],	
        ['para', ['paragraph']],
        // ['insert', ['link', 'picture']],
        ['insert', ['link']],
      ],
      onImageUpload: function(files, editor, welEditable) {
        // sendFile(files[0], editor, welEditable);
      },
	  onCreateLink : function(originalLink) {
        return originalLink; // return original link 
      }
    });
    // function sendFile(file, editor, welEditable) {

    //   const inquiry_id = Number(location.search.replace(/[^0-9]/g, ''));
    //   // const no = $('.js-survey-title-editor').parents(".js-survey-input-container.cloned").data("inquiryno");

    //   data = new FormData();
    //   data.append("file", file);
    //   data.append("inquiry_id", inquiry_id);
    //   // data.append("no", no);
    //   $.ajax({
    //     dataType: "json",
    //     data: data,
    //     type: "POST",
    //     url: "/ajax/uploadinquirydesc",
    //     cache: false,
    //     contentType: false,
    //     processData: false,
    //     success: function (url) {
    //       //アップロードが成功した後の画像を書き込む処理
    //       editor.insertImage(welEditable, url.url);
    //     }
    //   });
    // }
  }
  

	{ // 入力 => プレビューに反映
		// 問合せフォームタイトル
		$(document).on("input", ".js-survey-title-input", function (e) {
			$(e.target).parents(".suevey-languages-info-container").find(".js-preview-title").text($(e.target).val());
		});
		// 問合せフォーム概要
		$(document).on("input", ".js-survey-summary-textarea", function (e) {
			if ($(e.target).val()) {
				$(".js-preview-main-desc").show();
			} else { 
				$(".js-preview-main-desc").hide();
			}
			$(e.target).parents(".suevey-languages-info-container").find(".js-preview-summary-contents").text($(e.target).val());
		});
		// セクション タイトル
		$(document).on("input", ".js-survey-description-section-input", function(e){
			const pre = $(e.target).parents(".suevey-languages-info-container").find(".js-preview-description-title");
			const text = $(e.target).parents(".suevey-languages-info-container").find(".js-survey-description-section-input");
			for(let i = 0; i < pre.length - 1 ;i++){
				$(pre[i]).text($(text[i]).val());
			}
		});
		// セクション 内容
		// inputの場合
		$(document).on("input", ".js-survey-section-contents-textarea", function(e){
			const pre = $(e.target).parents(".suevey-languages-info-container").find(".js-preview-description-contents");
			const val = $(e.target).parents(".suevey-languages-info-container").find(".js-survey-section-contents-textarea");
      const html = $(e.target).parents(".suevey-languages-info-container").find(".js-input-des-section.preview-contents");
      for (let i = 0; i < pre.length - 1; i++){
        if ($(val[i]).val() !== "") {
          // テキストエリアに文字列がある場合
          $(pre[i]).text($(val[i]).val());
        } else { 
          // テキストエリアに文字列がない場合 = summernote利用時
          // (+i)+1 -> 1つ目は「問合せ概要」の内容が入るので1つずらす
          $(pre[i]).html(html.eq((+i)+1).html());
        }
      }
		});
		// 完了画面
		// 完了タイトル
		$(document).on("input", ".js-complete-title", function(e){
			$(e.target).parents(".suevey-languages-info-container").find(".js-preview-complete-title").text($(e.target).val());
		});
		// 完了概要
		$(document).on("input", ".js-complete-summary-textarea", function(e){
			$(e.target).parents(".suevey-languages-info-container").find(".js-preview-complete-summary-contents").text($(e.target).val());
		});
		// action_表示テキスト
		$(document).on("input", ".js-action-input", function(e){
			$($(".js-preview-action-btn")[0]).text($($(".js-action-input")[0]).val());
		});
		// 初期表示を切り替え->プレビューに反映
		$(document).on("click", ".js-talkappi-radio-option", function(){
			const preview = $(".js-preview-description-container").eq($(".js-description-section-container").index($(this).parents(".js-description-section-container")));
			if ($(this).attr("data-value") === "show") {
				preview.find(".js-preview-description-contents").slideDown();
				preview.find(".js-preview-fold").removeClass("rotate-fold-icon");
			} else { 
				preview.find(".js-preview-description-contents").slideUp();
				preview.find(".js-preview-fold").addClass("rotate-fold-icon");
			}
		});
	}

	{	// ヘッダー画像追加 // まだ
		$(document).on("click", ".js-add-header-icon", function(e){
			$(e.target).parents(".js-survey-header-image-container").find(".js-header-modal-container").removeClass("display-none");
		});
	}

	{ // ヘッダー追加のモーダル　閉じる
		// 背景
		$(document).on("click", ".js-survey-languages-modal-background", function(e){
			$(e.target).parents(".js-header-modal-container").addClass("display-none");
			// もし画像があったら表示する
			if ($(".js-survey-uploaded-image").attr("src")) {
				$(".js-uploaded-header-image-container").removeClass("display-none");
			} 
		});
		// キャンセルボタン
		$(document).on("click", ".js-header-modal-close-icon", function(e){
			$(e.target).parents(".js-header-modal-container").addClass("display-none");
			// もし画像があったら表示する
			if ($(".js-survey-uploaded-image").attr("src")) {
				$(".js-uploaded-header-image-container").removeClass("display-none");
			} 
		});
		// バツ印
		$(document).on("click", ".js-survey-questions-modal-cancel-icon", function(e){
			$(e.target).parents(".js-header-modal-container").addClass("display-none");
			// もし画像があったら表示する
			if ($(".js-survey-uploaded-image").attr("src")) {
				$(".js-uploaded-header-image-container").removeClass("display-none");
			} 
		});
	}

	{ // 説明セクションを追加する // アクションを追加する
		// 説明セクションを追加する
		$(document).on("click", ".js-survey-add-section", function () {
			const copySection = $(".js-description-section-container.for-clone").clone(true).removeClass("display-none").removeClass("for-clone");
			copySection.insertBefore(".js-description-section-container.for-clone");
			copySection.find('.description-section-title-container').find('h2').text(`${TalkappiMessageAdmin.inquiry_survey.section.title}${copySection.index()}`);
			// previewもコピーして追加
			$(".js-preview-description-container.for-clone").clone(true).removeClass("display-none").removeClass("for-clone").appendTo(".js-preview-main-container");
		});
		// アクションを追加する
		$(document).on("click", ".js-add-action", function () {
			$(".js-description-action-main-container").show();
			// previewもコピーして追加
			$($(".js-complete-btn.cloned")[0]).show();
			$(this).hide();
		});
	}

	function saveImage() {
		// 「OK」を押したらプレビューに表示される
		$(document).on("click", ".js-survey-save-header-image.active", function(e){
			// モーダルをdisplay-none
			$(e.target).parents(".js-header-modal-container").addClass("display-none");

			$('.js-survey-header-image-add').find("span").text("");

			// ヘッダー画像にimageの名前を保存
			const text = $(e.target).parents(".js-header-modal-container").find(".js-survey-header-image-text").val();
			if(text) { // 名前が設定されていたら
				if (!location.href.match(/complete/)) {
					// 回答画面ヘッダー
					$(e.target).parents(".js-suevey-languages-info-container").find(".survey-header-image-add").find(".js-image-name").text(text);
				} else {
					// 完了画面アイコン
					$('.js-survey-header-image-add').find("span").text(text);
				}
			} else { // 名前が設定されていなかったら
				const imageTitleDefault = $(e.target).parents(".js-header-modal-container").find(".js-survey-header-image-text").attr("placeholder");
				if (!location.href.match(/complete/)) {
					// 回答画面ヘッダー
					$(".js-image-name").text(imageTitleDefault);
				} else {
					// 完了画面アイコン
					$('.js-survey-header-image-add').find("span").text(imageTitleDefault);
				}
			}

			if ($(".js-uploaded-header-image-container").hasClass("display-none")) {
				// 名前を外す
				$(".js-survey-header-image-text").val("a");
				// 写真を外す
				$(".js-survey-uploaded-image").attr("src", "");
				// button active
				$(".js-survey-save-header-image").addClass("active");
				// プレビューから写真を外す
				$(".js-survey-preview-header-image").attr("src", "");
			} 

			// プレビューに反映
			const imageSrc = $(e.target).parents(".js-header-modal-container").find("#preview").attr("src");
			if (!location.href.match(/complete/)) {
				$(e.target).parents(".js-suevey-languages-info-container").find(".js-survey-preview-header-image").attr("src",imageSrc);
			} else {
				$('.js-preview-complete-image').find("img").attr("src",imageSrc);
			}
		});
	}

	{ // 画像アップロード
		$(document).on('change', "#myImage" , function(e) {
			var reader = new FileReader();
			if(e.target.files[0].size < 2000000){ // 2MB以内(仮:10MB)
				reader.onload = function (e) {
					$("#preview").attr('src', e.target.result);
				}
				reader.readAsDataURL(e.target.files[0]);

				if ($(e.target).parents(".js-header-modal-container").find(".js-survey-header-image-text") !== "") {
					$(e.target).parents(".js-header-modal-container").find(".js-survey-header-image-text").val("");
					$(e.target).parents(".js-header-modal-container").find(".js-survey-header-image-text").attr("placeholder", e.target.files[0].name);
				} else {
					$(e.target).parents(".js-header-modal-container").find(".js-survey-header-image-text").attr("placeholder", e.target.files[0].name);
				}

				// 画像のプレビューが見れる
				$(e.target).parents(".js-header-modal-container").find(".survey-uploaded-header-image-container").removeClass("display-none");
				$(e.target).parents(".js-header-modal-container").find(".js-survey-save-header-image").addClass("active");

				if(("#preview"))

				saveImage();

			} else {
				alert(TalkappiMessageAdmin.inquirydesc.label.upload_image_oversize);
				return false;
			}
		});
	}

	{ // モーダルの削除アイコン
		$(document).on("click", ".js-survey-header-image-modal-delete-icon", function (e) {
			// display-none
			$(".js-uploaded-header-image-container").addClass("display-none");
			$(".js-survey-save-header-image").addClass("active");
			if ($("#page").val() == 'input') $("input[name='inquiry_image']").val('');
			// save
			saveImage();
		});
	}

	{
		if ($(".js-survey-preview-header-image").attr("src") !== "") {
			$(".js-uploaded-header-image-container").removeClass("display-none")
		}
	}

	{ // セクションの削除アイコンをクリック
		$(document).on("click", ".js-delete-section", function(e){
			const that = this;
			talkmgr.warning_delete(TalkappiMessageAdmin.common.message.delete, TalkappiMessageAdmin.common.message.delete_confirm, function(result){
				if (result.button == 'confirm') {
					$(that).parents('.js-description-section-container').remove();
					$(".description-section-main-container").find(".description-section-container:not(.for-clone)").each(function(index, element){
						$(element).find('.description-section-title-container').find('h2').text(`${TalkappiMessageAdmin.inquiry_survey.section.title}${index + 1}`);
					})
				}
			});
		});
	}

	$(document).on("click", ".js-delete-action", function(){
		talkmgr.modal(
			{
				"title": TalkappiMessageAdmin.inquirydesc.label.delete_action_title,
				"description":"",
				"confirm":"",
				"buttons":[
					{
						"caption": TalkappiMessageAdmin.common.label.delete_confirm_caption, 
						"type":"confirm", 
						"color":"red"
					}, {
						"caption": TalkappiMessageAdmin.common.label.cancel, 
						"type":"close", 
						"color":"white"
					}]
				}, function(result){
			if (result.button == 'confirm') {
            	$(".js-description-action-main-container").hide();
            	$(".js-action-input").val("");
            	$(".js-description-action-input").val("");
            	$($(".js-complete-btn.cloned")[0]).hide();
				$(".js-add-action").show();
			}
		});
	});

	{ // 送信完了
		// 表示
		if ($(".js-desc-header-container").find(".js-title-setting-container").length !== 0) {
			$(".js-desc-header-container").find(".js-title-setting-container").slideUp(0);
			$(".js-desc-header-container").find(".js-description-section-main-container").slideUp(0);
			$(".js-desc-header-container").find(".js-survey-add-section").slideUp(0);
		}

		// 折り畳み
		$(document).on("click", ".js-fold-title", function () {
			const state = $(this).attr('slide-state');
			if (state == undefined || state == "close") {
				$(this).attr('slide-state', 'expand');
				$(this).text(TalkappiMessageAdmin.inquirydesc.label.fold_close);
			} else {
				$(this).attr('slide-state', 'close');
				$(this).text(TalkappiMessageAdmin.inquirydesc.label.fold_expand);
			}
			$(".js-desc-header-container").find(".js-title-setting-container").slideToggle();
			$(".js-desc-header-container").find(".js-description-section-main-container").slideToggle();
			$(".js-desc-header-container").find(".js-survey-add-section").slideToggle();
			// if ($(".js-desc-header-container").find(".js-fold-title").text().indexOf("開") >= 0) {
			// 	$(".js-desc-header-container").find(".js-fold-title").text("閉じる");
			// 	// $(".js-preview-description-container").css("margin","13px 0 0 0");
			// } else {
			// 	$(".js-desc-header-container").find(".js-fold-title").text("展開する");
			// 	// $(".js-preview-description-container").css("margin","0");
			// }
			// $(".js-preview-summary-contents").slideToggle();
			// $(".js-preview-description-contents").slideToggle();
		});

		// プレビューの概要、説明セクション => デフォルトは折り畳み
		// if ($(".js-desc-header-container").length !== 0) {
		// 	$(".js-preview-summary-contents").slideUp(0);
		// 	$(".js-preview-description-contents").slideUp(0);
		// 	$(".js-preview-description-container").css("margin","0");
		// }
	}

	{	// 完了画面　画像初期表示
		const icon = JSON.parse(JSON.stringify(complete_area)).icon;
		if (icon !== undefined) {
			$(".js-preview-complete-image").find('img').attr('src', icon);
			// URLを'/'区切りで配列に格納 -> 配列の最後の要素を画像名として格納
			const imageName = icon.split('/').slice(-1)[0];
			$('.js-add-header-icon').find('span').text(imageName);
		}
	}

	{ 
		// プレビューの概要・セクションの右側の ^ をクリック -> 表示非表示切り替え
		$(document).on("click", ".js-preview-fold", function () {
			$(this).toggleClass("rotate-fold-icon");
			$(this).parents(".js-preview-description-container").find(".js-preview-description-contents").slideToggle();
			$(this).parents(".js-preview-main-desc").find(".js-preview-summary-contents").slideToggle();
		});
	}

	let is_json = data => {
		try {
			JSON.parse(data);
		} catch (error) {
			return false;
		}
		return true;
	}
	const main_pic = _main_pic && is_json(_main_pic) ? JSON.parse(_main_pic) : _main_pic;
	const initSwiper = function (pic) { 
		const swiper2 = new Swiper(".swiper-thumbnail", {
			slidesPerView: 4,
			spaceBetween: 10,
			observer: true, 
			observeParents: true
		});
		const swiper = new Swiper(".swiper-carousel", {
			pagination: {
				el: ".swiper-pagination",
				clickable: true,
			},
			navigation: {
				nextEl: ".swiper-button-next",
				prevEl: ".swiper-button-prev" 
			},
			thumbs: {
				swiper: swiper2
			}
		});
		// 画像が1枚 => CSSで非表示
		if ((Array.isArray(pic) && pic.length <= 1) || !Array.isArray(pic)) {
			$(".js-preview-swiper-small").css("display", "none");
			$(".js-swiper-button-next").css("display", "none");
			$(".js-swiper-button-prev").css("display", "none");
			$(document).off('mouseenter', '.swiper-carousel');
			$(document).off('mouseleave', '.swiper-carousel');
		} else if (pic.length > 1) { 
			$(".js-preview-swiper-small").css("display", "flex");
			$(".js-swiper-button-next").css("display", "flex");
			$(".js-swiper-button-prev").css("display", "flex");

			$(document).on('mouseenter', '.swiper-carousel', function(e) {
				if (window.innerWidth > 480) {
					$('.swiper-button-next').fadeIn();
					$('.swiper-button-prev').fadeIn();
				};
			});
	
			$(document).on('mouseleave', '.swiper-carousel', function(e) {
				if (window.innerWidth > 480) {
					$('.swiper-button-next').fadeOut();
					$('.swiper-button-prev').fadeOut();
				}
			});
		}
	}
	const updatePreview = function (pics) { 
		const pre_pic = pics && is_json(pics) ? JSON.parse(pics) : pics;
		$(".js-swiper-wrapper").eq(0).children().remove();
		$(".js-swiper-wrapper").eq(1).children().remove();
		for (pic of pre_pic) { 
			const mainImgs = $('<div class="swiper-slide"><img class="main-img" src="' + pic + "?" + _item_upd_timestamp + '"></div>').appendTo($(".js-swiper-wrapper").eq(0))
			const prevorwSmallImgs = $('<div class= "swiper-slide" style="width: 44.6667px;"><img class="secondary-img" src="' + pic + "?" + _item_upd_timestamp + '"></div>').appendTo($(".js-swiper-wrapper").eq(1))
		}
		initSwiper(pre_pic);
	}
	const setMultiMainPic = function () { 
		const DATA_MAX = 2;
		
		const checkAddMainPic = function() {
			let neednew = true;
			$('.js-main-pic-upload').each(function () {
				const pic = talkmgr.data(this);
				if (pic == '') {
					neednew = false;
				}
			});
			if (neednew) {
				const item = $(`<div class="flex-x-between js-main-pic-item" style="margin-bottom:10px;"></div>`).appendTo($('.js-main-pic-container'));
				talkmgr.init(talkmgr.create({type:'fileupload', class:"js-main-pic-upload", filetype:'img', max: DATA_MAX, url:'', text:TalkappiMessageAdmin.inquirydesc.label.main_picture_add, uploadnow:'inquiry', ratio: `16:9${TalkappiMessageAdmin.inquirydesc.label.main_picture_recommend_aspect_ratio_label}` }).appendTo($(item)));	
			}
		}
		talkmgr.on('change', '.js-main-pic-upload', function () {
			checkAddMainPic();
			// 		
			const mainpic = [];
			$('.js-main-pic-upload').each(function() {
				const pic = talkmgr.data(this);
				if (pic != '') {
					mainpic.push(pic);
				}
			});
			$("#inquiry_image").val(JSON.stringify(mainpic));
			// プレビューに反映する処理
			updatePreview(mainpic);
		});
	
		// init
		if (Array.isArray(main_pic)) {
			for (pic of main_pic) {
				const item = $(`<div class="flex-x-between js-main-pic-item" style="margin-bottom:10px;"></div>`).appendTo($('.js-main-pic-container'));
				talkmgr.init(talkmgr.create({ type: 'fileupload', class: "js-main-pic-upload", filetype: 'img', max: DATA_MAX, url: pic, text: pic, uploadnow: 'inquiry', ratio: `16:9${TalkappiMessageAdmin.inquirydesc.label.main_picture_recommend_aspect_ratio_label}` }).appendTo($(item)));
			}
		} else if(main_pic) { 
			const item = $(`<div class="flex-x-between js-main-pic-item" style="margin-bottom:10px;"></div>`).appendTo($('.js-main-pic-container'));
			talkmgr.init(talkmgr.create({ type: 'fileupload', class: "js-main-pic-upload", filetype: 'img', max: DATA_MAX, url: main_pic, text: main_pic, uploadnow: 'inquiry', ratio: `16:9${TalkappiMessageAdmin.inquirydesc.label.main_picture_recommend_aspect_ratio_label}` }).appendTo($(item)));
		}
		const item = $(`<div class="flex-x-between js-main-pic-item" style="margin-bottom:10px;"></div>`).appendTo($('.js-main-pic-container'));
		talkmgr.init(talkmgr.create({ type: 'fileupload', class: "js-main-pic-upload", filetype: 'img', max: DATA_MAX, url: '', text: TalkappiMessageAdmin.inquirydesc.label.main_picture_add, uploadnow: 'inquiry', ratio: `16:9${TalkappiMessageAdmin.inquirydesc.label.main_picture_recommend_aspect_ratio_label}` }).appendTo($(item)));
		
		$("#sortable").sortable({
			// items: '.class',
			stop: function (e, ui) {
				let pics = []
				$('.js-main-pic-upload').each(function() {
					const pic = talkmgr.data(this);
					if (pic != '') {
						pics.push(pic)
					}
				});
				$("#inquiry_image").val(JSON.stringify(pics));
				updatePreview(pics);
			}
		});
	}

	setMultiMainPic();
	initSwiper(main_pic);
});