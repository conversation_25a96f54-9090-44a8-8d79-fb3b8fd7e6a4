$(document).ready(function(){
	talkmgr.load();

	const colors = ["#FFDCE5", "#CFF2D7","#D3EEFF","#FFF0BB","#EDE0FF","#FFE6D6"]
	const _inquirycalendar_modal =
	`<div class="inquirycalendar-modal-container" data-id="" style="display:none;">
		<div class="inquirycalendar-modal-title">
			<h4>${TalkappiMessageAdmin.inquirycalendar.label.new_edit}</h4>
			<span class="icon-cancel-large js-modal-cancel pointer"></span>
		</div>
		<hr>
		<div class="form-group flex" style="margin-bottom: unset; align-items:center;">
			<label class="control-label col-md-2" style="width: unset;">${TalkappiMessageAdmin.inquirycalendar.label.calendar_title}</label>
			<div class="col-md-5" style="position: relative;">
				<div class="talkappi-multitext js-edit-calendar-title" data-name="calendar_name" data-max-input="30" data-language='${_display_langs}' data-value='{}' title="${TalkappiMessageAdmin.inquirycalendar.label.calendar_title}">
					<input type="text" class="form-control talkappi-textinput talkappi-multitext-show-dialog js-def-language-text js-inquirycalendar-title" placeholder="" autocomplete="off" style="margin-top: unset;">
				</div>
			</div>
		</div>

		<hr>
		<div class="js-inquirycalendar-group">
			<div class='js-inquirycalendar-list'>
			</div>
			<div class="add-reservation-container">
				<div class="image-action-group js-add-reservation">
					<img src="./../assets/admin/css/img/icon-add.svg" width="12" height="12">
					<span>${TalkappiMessageAdmin.inquirycalendar.label.add_form_message}</span>
				</div>
				<span class="max-reservations-hint">${TalkappiMessageAdmin.inquirycalendar.message.max_reservations_hint}</span>
			</div>
		</div>
		<hr>
		<div class="dialog-btn-container">
			<span class="btn-larger btn-blue js-modal-ok">${TalkappiComponentsLocale.dialog_button_ok}</span>
			<span class="btn-larger btn-white js-modal-cancel">${TalkappiComponentsLocale.dialog_button_cancel}</span>
		</div>
	</div>`;

	const inquirycalendar_pulldown_and_settings = (index, lists, select_inquiry_id, select_maximum_id, select_color, objDisplayName={}) => {
		// ここでForm/枠リストを取得し、プルダウンと設定を返す
		let html = `<div class="flexbox-x-axis js-reservation-list" data-index=${index} style="margin-bottom: 10px; margin-left:10px; gap: 12px;">
		<p class="selected-calendar-flex">${TalkappiMessageAdmin.inquirycalendar.label.reservation} ${index}</p>
		<select class="talkappi-dropdown-container pulldown selected-calendar-input js-inquiry-pulldown" name="inquiry_id">`
		for (list of lists) {
			html += `<option value="${list.inquiry_id}" ${list.inquiry_id == select_inquiry_id ? 'selected' : ''}>${list.inquiry_name}</option>`
		}
		html += `</select>`;
		let displayName = TalkappiMessageAdmin.inquirycalendar.label.set_display_name;
		html += `<select class="talkappi-dropdown-container pulldown selected-calendar-input js-maximum-pulldown" name="maximum_id">`;
		if (select_inquiry_id != null) {
			displayName = _get_display_name_text(objDisplayName);
		} else {
			// get default maximum options
			const _default_maximum_ids = _inquiry_maximum_mapping[lists[0].inquiry_id];
			select_maximum_id = _default_maximum_ids[0];
			for (maximum_id of _default_maximum_ids) {
				let selected = '';
				if (maximum_id == select_maximum_id) {
					selected = 'selected';
				}
				html += `<option value="${maximum_id}" ${selected}>${_all_maximum_options[maximum_id]}</option>`
			}
		}
		html += `</select>`;

		// edit calendar title
		const displayNameJSON = JSON.stringify(objDisplayName);
		html += `<div class="talkappi-multitext js-edit-name" data-name="display_name" data-max-input="12" data-language='${_display_langs}' data-value='${displayNameJSON}' title="${TalkappiMessageAdmin.inquirycalendar.label.display_name}"><a href="javascript:void(0);" class="multi-text-label js-def-language-label">` + displayName + `</a></div>`;

		html += `<div class="mycheckbox selected-calendar-flex">`
		for (color of colors) {
			html +=
			`<label class="my-radio">
				<input type="radio" name="color_${index}" value="${color}" ${color == select_color ? 'checked' : ''}>
				<span class="radio-mark" style="background: ${color};"></span>
			</label>`
		}
		html += `</div>
		<div class="delete-icon js-reservation-delete pointer">
			<span class="icon-delete"></span>
		</div>
		</div>`
		return html
	}

	/**
	 * 
	 * @param {number} inquiry_id 
	 * @param {jQuery} maximumPulldown 
	 * @param {number} selected_id 
	 * @param {object} objDisplayName 
	 */
	const _generate_maximum_options = (inquiry_id, maximumPulldown, selected_id=null) => {
		if (inquiry_id != null && _inquiry_maximum_mapping[inquiry_id]) {
			maximumPulldown.empty();
			// fetch maximum list
			const maximum_ids = _inquiry_maximum_mapping[inquiry_id];
			let _selected_id = selected_id;
			if (selected_id == null) {
				_selected_id = maximum_ids[0];
			}
			let html = '';
			for (maximum_id of maximum_ids) {
				let selected = '';
				if (maximum_id == _selected_id) {
					selected = 'selected';
				}
				html += `<option value="${maximum_id}" ${selected}>${_all_maximum_options[maximum_id]}</option>`
			}
			maximumPulldown.append($(html));
		}
	}

	$('body').append(_inquirycalendar_modal);

	$(document).on("click",'.js-modal-cancel', function(){
		$(".inquirycalendar-modal-container").hide();
		$(".js-talkappi-modal-background").hide();
		$('div.js-inquirycalendar-list').empty()
		$(".js-span-pulldown").val("all")
		$(".js-edit-calendar-title").attr("data-value", "{}")//TODO
		$('.inquirycalendar-modal-container').attr('data-id','')
	})
	
	$(document).on("click",'.js-add-reservation', function(){
		console.log('.js-add-reservation clicked!');
		const targetElement = $(this).parent().parent().children("div.js-inquirycalendar-list")
		const currentCalendarCount = targetElement.children("div").length;
		if ( currentCalendarCount < 4){	// 最大四つまで追加できます。
			targetElement.append(inquirycalendar_pulldown_and_settings( currentCalendarCount + 1, _inquiry_options));
		} else {
			talkmgr.toast('error', TalkappiMessageAdmin.common.label.message_title_error, TalkappiMessageAdmin.inquirycalendar.message.error.four);
		}
		_check_max_reservations_in_modal();	
	})

	$(document).on("change", '.js-inquiry-pulldown', function(e){
		// clear maximum list UI
		const maximumPulldown = $(e.target).siblings('.js-maximum-pulldown');
		// fetch maximum list
		// update maximum list UI
		_generate_maximum_options($(e.target).val(), maximumPulldown);
	})

	$(document).on("click",'.js-reservation-delete', function(){
		// remove target reservation UI and update reservation list UI
		$(this).parents('.flexbox-x-axis.js-reservation-list').remove();
		$('.js-reservation-list').each(function(index, element){
			// reindexing reservation list
			$(element).attr('data-index', index + 1);
			$(element).find('p.selected-calendar-flex').text(TalkappiMessageAdmin.inquirycalendar.label.reservation + ' ' + (index + 1));
			const colorLabelCheckbox = $(element).find('div.selected-calendar-flex.mycheckbox').children('label').children('input')
			colorLabelCheckbox.attr('name', 'color_' + (index + 1));
		});
		_check_max_reservations_in_modal();	
	})

	$(document).on("click", '.js-edit', function(){
		const id = ($(this).data('inquirycalendar-id'))
		$('.inquirycalendar-modal-container').attr('data-id',id)
		// ここで既存カレンダー情報を取得・編集する		
		$(".js-edit-calendar-title").attr('data-value', JSON.stringify(_calendar_title));
		_refresh_display_name_text($(".js-edit-calendar-title"));
		const targetElement = $("div.js-inquirycalendar-list");
		for ( reservation of _calendar_data){
			const currentCalendarCount = targetElement.children("div").length;
			targetElement.append(
				inquirycalendar_pulldown_and_settings(currentCalendarCount + 1,
				_inquiry_options,
				reservation.inquiry_id,
				reservation.maximum_id,
				reservation.color, 
				reservation.name
			))

			const appendedCalendar = targetElement.children("div").last();
			_generate_maximum_options(reservation.inquiry_id, appendedCalendar.find('.js-maximum-pulldown'), reservation.maximum_id);
			$(".inquirycalendar-modal-container").show();
			$(".js-talkappi-modal-background").show(); 
		}
		_check_max_reservations_in_modal();	
	})

	CalendarRemarkModalContent.init(_bot_langs, _calendar_remark);

	$(document).on('click', '.js-edit-remark', function() {
		const _this = $(this);
		const dialog = {
			"title": TalkappiMessageAdmin.inquirycalendar.label.remark_edit,
			"buttons": [
					{ "caption": "OK", "type": "save", "color": "blue" },
					{ "caption": TalkappiMessageAdmin.common.label.cancel, "type": "close", "color": "white" },
			]
		};
		const content = CalendarRemarkModalContent.create();
		TalkappiModal.modal(dialog, content.html(), function(result) {
			if (result.button == 'save') {
				const formData = new URLSearchParams();
				const calendar_id = _this.data('inquirycalendar-id');
				const remarkData = CalendarRemarkModalContent.data();
				formData.append('remark', JSON.stringify(remarkData));
				formData.append('calendar_id', calendar_id);
				talkmgr.block();
				fetch('/admininquiry/save_calendarremark', {
					method: 'POST',
					headers: {
						'Content-Type': 'application/x-www-form-urlencoded',
						Accept: 'application/json',
					},
					body: formData
				})
				.then(response => response.json())
				.then(responseData => {
					if (responseData.result == 'success') {
						talkmgr.toast(`S|${TalkappiMessageAdmin.common.message.success.save_success}`);
            setTimeout(() => {
              location.reload();
            }, 500);
					} else {
						talkmgr.toast(`"E|${TalkappiMessageAdmin.common.message.error.save_error}`);
						talkmgr.unblock();
					}
				})
				.catch((error) => {
					console.error(error);
					talkmgr.unblock();
					talkmgr.toast(`"E|${TalkappiMessageAdmin.common.message.error.save_error}`);
				})
			}
		})
		talkmgr.init('.js-remark-textarea');
	})

	$(document).on("click", '.js-modal-ok',function(){
		let error = 0
		const id = $('.inquirycalendar-modal-container').attr('data-id').length != 0 ? $('.inquirycalendar-modal-container').attr('data-id') : null;
		const title = $(".js-inquirycalendar-title").val();
		if (title.length == 0) {
			talkmgr.toast('error', TalkappiMessageAdmin.common.label.message_title_error, TalkappiMessageAdmin.inquirycalendar.message.error.title_required);
			error += 1;
		} 
		const calendar_title = $(".js-edit-calendar-title").attr('data-value');
		// check if the input is exceeded the limit
		if (!_check_display_name_length(calendar_title, TalkappiMessageAdmin.inquirycalendar.label.calendar_title)) return false;
		const rows = [];
		$('.js-reservation-list').each(function(){
			// construct rows for saving calendar
			inquiry_id = $(this).find("select[name='inquiry_id']").val();
			maximum_id = $(this).find("select[name='maximum_id']").val();
			display_name = $(this).find(".js-edit-name").attr('data-value');
			if (!_check_display_name_length(display_name, TalkappiMessageAdmin.inquirycalendar.label.display_name + ' ' + $(this).data('index'))) return false;
			color = $(this).find(`input[name='color_${$(this).data('index')}']:checked`).val();
			if (color == undefined){
				talkmgr.toast('error', TalkappiMessageAdmin.common.label.message_title_error, TalkappiMessageAdmin.inquirycalendar.message.error.color_required) 
				error += 1
			}
			rows.push({inquiry_id: inquiry_id, maximum_id: maximum_id, name: JSON.parse(display_name), color: color});
		})
		if (rows.length == 0){
			talkmgr.toast('error', TalkappiMessageAdmin.common.label.message_title_error, TalkappiMessageAdmin.inquirycalendar.message.error.reservation_required);
			error += 1;
		}
		if(error > 0) {
			return false;
		}
		saveInquirycalendar(id,calendar_title,rows)
			.done(function(res){
				talkmgr.url(`/admininquiry/calendar?id=${res.id}`);
				talkmgr.toast('info', TalkappiMessageAdmin.common.label.message_title_info, TalkappiMessageAdmin.inquirycalendar.message.success.update)
			})
			.fail(function(res){
				talkmgr.toast('error', TalkappiMessageAdmin.common.label.message_title_error, TalkappiMessageAdmin.maximumcalendar.message.error.update_fail)
			})
	})

	function _multitext_modal_close_handler(e) {
		if ($(e.target).hasClass('js-multitext-modal-ok')) {
			// check if the input is exceeded the limit
			const inputs = $(e.target).parent().siblings('div').find('input');
			inputs.each(function(index, element) {
				const maxInput = $(element).data('max-input');
				if ($(element).val().length > maxInput) {
					talkmgr.toast('error', TalkappiMessageAdmin.common.label.message_title_error, TalkappiMessageAdmin.common.message.error.validation_maxlength);
					// TODO prevent further event handler?
					return false;
				}
			});
			$('.js-edit-name').each(function(index, element) {
				_refresh_display_name_text($(element));
			});
			_refresh_display_name_text($('.js-edit-calendar-title'));
		}
		setTimeout(function(){
			talkmgr.blockBackground(true);
		}, 5);
	}


	$(document).on("click", "div.multitext-modal-container>div.dialog-btn-container>span.btn-larger,div.multitext-modal-container>div.multitext-modal-title>span.js-multitext-modal-cancel", 
		_multitext_modal_close_handler
	);

	const clipboards = new ClipboardJS('.js-copy');
	clipboards.on('success', function(e) {
		talkmgr.toast('info', TalkappiMessageAdmin.common.message.info, TalkappiMessageAdmin.common.message.success.clipboard_copy_success);
		e.clearSelection();
	});
	clipboards.on('error', function(e) {
		talkmgr.toast('error', TalkappiMessageAdmin.common.message.error, TalkappiMessageAdmin.common.message.success.clipboard_copy_error);
	});

});

function _check_max_reservations_in_modal() {
	const currentCalendarCount = $("div.js-inquirycalendar-list").children("div.js-reservation-list").length;
	if ( currentCalendarCount >= 4){	// 最大四つまで追加できます。
		// hide add button
		$(".add-reservation-container").children("div.image-action-group").hide();
		// display hint
		$(".add-reservation-container").children("span.max-reservations-hint").show();
		return false;
	} else {
		// display add button
		$(".add-reservation-container").children("div.image-action-group").show();
		// hide hint
		$(".add-reservation-container").children("span.max-reservations-hint").hide();
		return true;
	}
}

function _check_display_name_length(display_name, type) {
	const objDisplayName = JSON.parse(display_name);
	for (let lang_cd of JSON.parse(_display_langs)) {
		if (objDisplayName[lang_cd]) {
			displayName = objDisplayName[lang_cd];
			if (displayName.length > 30) {
				talkmgr.toast('error', TalkappiMessageAdmin.common.label.message_title_error, type + ' ' + TalkappiMessageAdmin.common.message.error.validation_maxlength);
				error += 1;
				return false;
			}
		}
	}
	return true;
}

function _refresh_display_name_text(labelEditDisplayName) {
	const objDisplayName = JSON.parse(labelEditDisplayName.attr('data-value'));
	const elementDefLanguage = labelEditDisplayName.children('.js-def-language-label,.js-def-language-text');
	if ( elementDefLanguage.hasClass('js-def-language-label') && ! elementDefLanguage.text() ) {
		const displayName = _get_display_name_text(objDisplayName);
		elementDefLanguage.text(displayName);
	} else if (elementDefLanguage.hasClass('js-def-language-text') && ! elementDefLanguage.val()) {
		const displayName = _get_display_name_text(objDisplayName, '');
		elementDefLanguage.val(displayName);
	}
}

function _get_display_name_text(objDisplayName, defaultDisplayName=TalkappiMessageAdmin.inquirycalendar.label.set_display_name) {
	let displayName = defaultDisplayName;
	if (objDisplayName[_lang_cd]) {
		displayName = objDisplayName[_lang_cd];
	} else for (let lang_cd of JSON.parse(_display_langs)) {
		if (objDisplayName[lang_cd]) {
			displayName = objDisplayName[lang_cd];
			break;
		}
	}
	return displayName;
}

function fetchFormList(){
	// ここでajax通信を行い、フォームリストを取得する
	return $.ajax({
		type: 'get',
        dataType:'json',
		url: `/ajax/formlist_for_calendars`
	})
}

function fetchMaximumList(inquiry_id){
	// ここでajax通信を行い、枠リストを取得する
	return $.ajax({
		type: 'get',
        dataType:'json',
		url: `/ajax/maximumlist_for_inquirycalendar?inquiry=${inquiry_id}`
	})
}

function fetchInquirycalendar(id){
	return $.ajax({
		type: 'get',
        dataType:'json',
		url: `/ajax/inquirycalendar?id=${id}`
	})
}

function saveInquirycalendar(id,title,calendar_data){
	return $.ajax({
		type:'post',
        dataType:'json',
		data:{id: id, title: title, calendar_data: calendar_data},
		url: '/ajax/save_inquirycalendar'
	})
}

const CalendarRemarkModalContent = function () {
	let _remark_data = {};
	let _langs= {};

	const _init = (display_lang, remarkData) => {
		_langs = display_lang;
		if (remarkData) {
			_remark_data = remarkData;
		}
	}

	const _initEvent = () => {
		$(document).off('click', '.lang-button-wrapper .button-tab li');
		$(document).on('click', '.lang-button-wrapper .button-tab li', function() {
			const prevLang = $('.lang-button-wrapper .button-tab li.active').data('lang');
			const currentSummerNodeValue = talkmgr.data('.js-remark-textarea');
			_remark_data[prevLang] = currentSummerNodeValue;
			const lang = $(this).data('lang');
			$('.lang-button-wrapper .button-tab li').removeClass('active');
			$(this).addClass('active');
			$('.js-remark-textarea').remove();
			const textarea = _createSummerNote(lang);
			$('.textarea-wrapper').append(textarea);
			talkmgr.init('.js-remark-textarea');
		});
	}
	
	const _create = () => {
		const contentOuter = $('<div></div>');
		const langButtonWrapper = $('<div class="lang-button-wrapper" style="margin-bottom:12px"></div>');
		const nav = $('<nav class="button-tab"><ul style="gap:6px"></ul></nav>');
		Object.keys(_langs).forEach((lang, index) => {
			const langButton = $('<li style="margin:0;" data-lang="' + lang + '">' + _langs[lang] + '</li>');
			if (index == 0) {
				langButton.addClass('active');
			}
			nav.find("ul").append(langButton);
		});
		langButtonWrapper.append(nav);
		contentOuter.append(langButtonWrapper);
		const textareaWrapper = $('<div class="textarea-wrapper" style="overflow-y:auto;max-height:80%"></div>');
		const textarea = _createSummerNote(Object.keys(_langs)[0]);
		textareaWrapper.append(textarea);
		contentOuter.append(textareaWrapper);
		return contentOuter;
	}

	const _createSummerNote = (lang) => {
		const langRemark = _remark_data[lang] ?? '';
		const summerNoteNode = $(`<div class="summernote-edit js-remark-textarea" data-edit="summer" data-name="remark-textarea" data-value='${langRemark}' title="" data-upload-image="1" data-position="bottom"></div>`);
		return summerNoteNode;
	}

	const _data = () => {
		const lang = $('.lang-button-wrapper .button-tab li.active').data('lang');
		_remark_data[lang] = talkmgr.data('.js-remark-textarea');
		return _remark_data;
	}

	return {
		init: (display_langs, remarkData = null) => {
			_init(display_langs, remarkData);
			_initEvent();
		},
		create: () => {
			return _create();
		},
		data: () => {
			return _data();
		}
	}
}();