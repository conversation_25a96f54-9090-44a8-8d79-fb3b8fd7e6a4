$(function(){
	talkmgr.load();
	if (talkmgr.data('cancelable') == 0) $('.js-cancelpolicy-group').slideUp(0);
	if (talkmgr.data('modifiable') == 0) $('.js-modifypolicy-group').slideUp(0);
	if (talkmgr.data('remind_flg') == 0) $('.js-remind-group').slideUp(0);
	if (talkmgr.data('secret_mode') == 0) $('.js-password').slideUp(0);
	if (talkmgr.data('input_policy') == 0 ) $('.js-input-policy-opening').slideUp(0);
	if (talkmgr.data('receipt_setting') == 0) $('.js-tax-rate').slideUp(0);
	if (talkmgr.data('2_step') == 0) $('.js-2-step-limit').slideUp(0);
	checkAwsVarify($('.js-mail-from').val());
	talkmgr.on('select', 'scene_cd', function(e){
		$.ajax({
			url: "/ajax/scenetemplate",
			type: "post",
			dataType: "json",
			data: { "scene_cd": e.data.code, "func_type_cd":'inquiry'},
			success: function (data) {
				$('.js-template-cd').empty();
				if (data.length == 0) data = {};
				talkmgr.init('.js-template-cd', '', data);
			},
			error: function (XMLHttpRequest, textStatus, errorThrown) {
				// alert(errorThrown);
				Metronic.unblockUI('.page-container');
			}
		});
	});

	talkmgr.on('select', 'public_url', function(e){
		if (e.data.code == 0) {
			$('.js-simple-url').slideDown(0);
			$('.js-parameter-url').slideUp(0);
		}
		else {
			$('.js-simple-url').slideUp(0);
			$('.js-parameter-url').slideDown(0);
		}
	});

	talkmgr.on('select', 'cancelable', function(e){
		if (e.data.code == 0) {
			$('.js-cancelpolicy-group').slideUp(0);
		}
		else {
			$('.js-cancelpolicy-group').slideDown(0);
		}
	});

	talkmgr.on('select', 'modifiable', function(e){
		if (e.data.code == 0) {
			$('.js-modifypolicy-group').slideUp(0);
		}
		else {
			$('.js-modifypolicy-group').slideDown(0);
		}
	});

	talkmgr.on('select', 'need_signature', function(e) {
		if (e.data.code == 0) {
			$('.js-mail-signature-setting').slideUp(0);
		}
		else {
			$('.js-mail-signature-setting').slideDown(0);
		}
	})

	$(document).on("click", ".js-edit-signature", (e) => {
		e.preventDefault();
		const signature = talkmgr.data('mail_signature');
		if (signature == 0) {
			window.open(`/admin/signatures`, '_blank');
		} else {
			window.open(`/admin/signature?id=${signature}`, '_blank');
		}
	})

	talkmgr.on('select', 'remind_flg', function(e){
		if (e.data.code == 0) {
			$('.js-remind-group').slideUp(0);
		}
		else {
			$('.js-remind-group').slideDown(0);
		}
	});

	talkmgr.on('select', 'secret_mode', function(e){
		if (e.data.code == 0) {
			$('.js-password').slideUp(0);
		}
		else {
			$('.js-password').slideDown(0);
		}
	});

	talkmgr.on('select', 'input_policy', function(e){
		if (e.data.code == 0) {
			$('.js-input-policy-opening').slideUp(0);
		}
		else {
			$('.js-input-policy-opening').slideDown(0);
		}
	});

	talkmgr.on('select', '2_step', function(e){
		if (e.data.code == 0) {
			$('.js-2-step-limit').slideUp(0);
		} else {
			$('.js-2-step-limit').slideDown(0);
		}
	});

	talkmgr.on('select', 'receipt_setting', function(e){
		if (e.data.code == 0) {
			$('.js-tax-rate').slideUp(0);
		} else {
			$('.js-tax-rate').slideDown(0);
			$("input[name='tax_rate']").val($("#default_tax_rate").val());
		}
	});
	
	if ($('input[name="receiption_id_prefix"]').val() == '?') {
		$('#random-receiption-id').prop('checked', true);
		$('input[name="receiption_id_prefix"]').val('');
		$('input[name="receiption_id_prefix"]').prop('disabled', true);
		$('input[name="receiption_id_prefix"]').css('color', '#BBB');
	}
	$('#random-receiption-id').change(function() {
        if(this.checked) {
            $('input[name="receiption_id_prefix"]').prop('disabled', true);
			$('input[name="receiption_id_prefix"]').css('color', '#BBB');
        } else {
            $('input[name="receiption_id_prefix"]').prop('disabled', false);
			$('input[name="receiption_id_prefix"]').css('color', 'inherit');
        }
    });

	$(document).on("click",'.js-action-save' ,function(e) {
		let error = false;
		if ($("input[name='inquiry_name'").val() == '') {
			talkmgr.toast('error', TalkappiMessageAdmin.common.message.error, TalkappiMessageAdmin.inquiry.message.error.inquiry_name_error);
			return;
		}
		/*
		if ($("input[name='user_in_charge'").val() == '' && $("#param_user_in_charge_required").val() == 1) {
			talkmgr.toast('error', 'エラー', '担当者を選択してください。');
			return;
		}
		*/
		// 担当者未選択時のエラー
		if ($("#selected-items-0").val() == '' && $("#param_user_in_charge_required").val() == 1) {
			talkmgr.toast('error', TalkappiMessageAdmin.common.message.error, TalkappiMessageAdmin.inquiry.message.error.user_in_charge_error);
			return;
		}
		if ($("input[name='member_mail_from'").val() != '') {
			if (!talkmgr.checkEmailFormat($("input[name='member_mail_from'").val())) {
				talkmgr.toast('error', TalkappiMessageAdmin.common.message.error, TalkappiMessageAdmin.inquiry.message.error.from_email_error);
				return;
			}
		}
		if ($("input[name='member_mail_replyto'").val() != '') {
			if (!talkmgr.checkEmailFormat($("input[name='member_mail_replyto'").val())) {
				talkmgr.toast('error', TalkappiMessageAdmin.common.message.error, TalkappiMessageAdmin.inquiry.message.error.reply_email_error);
				return;
			}
		}
		// if ($("input[name='start_date'").val() == '') {
		// 	talkmgr.toast('error', 'エラー', '実施期間を選択してください。');
		// 	return;
		// }
		// if ($("input[name='end_date'").val() == '') {
		// 	talkmgr.toast('error', 'エラー', '実施期間を選択してください。');
		// 	return;
		// }
		// if ($("input[name='start_date'").val() > $("input[name='end_date'").val()) {
		// 	talkmgr.toast('error', 'エラー', '実施期間の設定が間違いです。');
		// 	return;
		// }

		// 管理者送信ユーザー
		if (talkmgr.data('user_mail_template') != '') {
			const users = [];
			$('.js-selected-users .js-selected-user').each(function () {
				users.push($(this).data('userId'));
			});
			$('input[name="mail_users"]').val(users.join(','));
		}
		else {
			$('input[name="mail_users"]').val('');

		}

		if ($("input[name='secret_mode']").prop('checked') == true && $("input[name='password']").val().trim()=='') {
			talkmgr.toast('error', TalkappiMessageAdmin.common.message.error, TalkappiMessageAdmin.inquiry.message.error.limit_public_password_error);
			return;
		}
		
		if ($("input[name='2_step']").val() == 1 && $("input[name='pay_in_hours']").val().trim() == '') {
			talkmgr.toast('error', TalkappiMessageAdmin.common.message.error, TalkappiMessageAdmin.inquiry.message.error.please_input_pay_limit);
			return;
		}

		if ($("input[name='receipt_setting']").val() == 1 && $("input[name='tax_rate']").val().trim() == '') {
			talkmgr.toast('error', TalkappiMessageAdmin.common.message.error, TalkappiMessageAdmin.inquiry.message.error.please_input_receipt_tax_rate);
			return;
		}

		if(!$("input[name='member_mail_template']").val()){
			const lang_cds = []
			$.each(_inquiry_entries, function(i, value) {
				if(JSON.parse(value.input_rules).send_mail){
					lang_cds.push(value.lang_cd);
					error = true;
				}
			});
			const new_lang_cds = lang_cds.filter((e, i) => lang_cds.indexOf(e) === i);
			let _lang_msg = [];
			new_lang_cds.forEach(function(e, i){
				_lang_msg.push(_bot_lang[e]);
			});
			if(error){
				talkmgr.toast('error', TalkappiMessageAdmin.common.message.error, TalkappiMessageAdmin.inquiry.message.error.member_mail_template + '<br/>' + TalkappiMessageAdmin.inquiry_survey.message.error.lang_type(_lang_msg.join(', ')));
				return;
			}
		}

		let cancel_policy = {};
		let validate = true;
		if($('[data-name="cancelable"]').attr("data-value") === "1"){
			$('.js-radio-group').find('.js-span-setting').each(function(){
				if ($(this).hasClass('active')) {
					if ($(this).hasClass('js-rd-cancel-policy')) {
						if (_only_talkappipay == 0) {
							talkmgr.toast('error', TalkappiMessageAdmin.common.message.error, TalkappiMessageAdmin.inquiry.message.error.cancel_policy_talkappipay_limit);
							validate = false;
							return;
						}
						cancel_policy = TalkappiPolicy.data('.js-cancel-policy');
						$('#cancel_policy').val(JSON.stringify({policy:cancel_policy}));
					}
					else {
						$(this).find('input').each(function() {
							cancel_policy[$(this).attr('data-prop')] = $(this).val();
							if($(this).attr('data-prop') === 'cancel_deadline_time' && !$(this).val()){
								cancel_policy[$(this).attr('data-prop')] = "00:00";
							}
						});
						$('#cancel_policy').val(JSON.stringify(cancel_policy));
					}
					return;
				}
			});
		}
		else {
			$('#cancel_policy').val(JSON.stringify(cancel_policy));
		}
		if (!validate) return;
		if ($('[data-name="modifiable"]').length > 0) {
			const modify_policy = {};
			if($('[data-name="modifiable"]').attr("data-value") === "1"){
				$('.js-radio-group-modify').find('.js-span-setting').each(function(){
					if ($(this).hasClass('active')) {
						$(this).find('input').each(function() {
							modify_policy[$(this).attr('data-prop')] = $(this).val();
							if($(this).attr('data-prop') === 'deadline_time' && !$(this).val()){
								modify_policy[$(this).attr('data-prop')] = "00:00";
							}
						});
						return;
					}
				});
			}
			$('#modify_policy').val(JSON.stringify(modify_policy));
		}

		
		if ($('[data-name="remind_flg"]').length > 0) {
			const remind = [];
			if($('[data-name="remind_flg"]').attr("data-value") === "1"){
				$('.js-remind-group').find('.js-remind-day').each(function() {
					remind.push({remind_day:$(this).val(), remind_time:$(this).parent().find('.js-remind-time').val()});
				});
			}
			$('#remind').val(JSON.stringify(remind));
		}

		// 公開時間制限
		if ($('[data-name="input_policy"]').length > 0) {
			let input_policy_opening = [];
			if($('[data-name="input_policy"]').attr("data-value") === "1"){
				const _input = $('.js-input-policy-opening').find('input').val();
				if(_input){
					input_policy_opening = _input.split(',');
				}
			}
			$('#input_policy_opening').val(JSON.stringify(input_policy_opening));
		}

		// サポート言語が選択されていないとき
		if (!$("[data-name='support_lang_cd']").attr("data-value") || JSON.parse($("[data-name='support_lang_cd']").attr("data-value")).length === 0
		) {
			talkmgr.toast('error', TalkappiMessageAdmin.common.message.error, TalkappiMessageAdmin.inquiry.message.error.support_lang);
			return;
		}

		// フォーム設問編集にPMS連携の設定があれば、基本設定でPMS連携をオフにできないようにする
		if ($('[data-name="assist"]').attr("data-value") === "0" && _has_pms_linked_inquiryentry) {
			talkmgr.toast('error', TalkappiMessageAdmin.common.message.error, TalkappiMessageAdmin.inquiry.message.error.pms);
			return;
		}

		if (talkmgr.data('need_signature') == '0') {
			$('input[name="inquiry_mail_signature"]').val('')
		} else {
			const mail_signature_value = talkmgr.data('mail_signature');
			$('input[name="inquiry_mail_signature"]').val(mail_signature_value)
		}

		checkMailTemplateLangIsIncludeDisplayLang(() => {
			talkmgr.warning(TalkappiMessageAdmin.common.message.save_confirm, TalkappiMessageAdmin.common.message.save_confirm_detail, function(result){
				if (result.button == 'confirm') {
					// 期間：時刻のデフォルト値
					$("#start_time").val() === "" && $("#start_time").val("00:00");
					$("#end_time").val() === ""  && $("#end_time").val("23:59");
					talkmgr.submit('save');
				};
			});
		});
	});

	$(document).on("click",'.js-action-back', function(e) {
		talkmgr.url(`/${(_inquiry_div === '9' || _inquiry_div === '') ? 'admininquiry' : 'adminorder'}/inquirys`);
	});
	
	$(document).on("click",'.js-action-delete' ,function(e) {
		talkmgr.modal(
			{
				"title": TalkappiMessageAdmin.inquiry.label.delete_action_title,
				"description": TalkappiMessageAdmin.inquiry.label.delete_action_description,
				"confirm": TalkappiMessageAdmin.inquiry.label.delete_action_confirm,
				"buttons":[
					{
						"caption": TalkappiMessageAdmin.inquiry.label.delete_confirm_caption, 
						"type":"confirm", 
						"color":"red"
					}, {
						"caption": TalkappiMessageAdmin.common.label.cancel, 
						"type":"close", 
						"color":"white"
					}]
			}, function(result)
			{
				if (result.button == 'confirm') {
					talkmgr.submit('delete');
				}
			}
		);
	});

	$(document).on("click", '.js-scene-template', function (e) {
		let scene = $(this).prev().prev().attr('data-value');
		let template =  $(this).prev().attr('data-value');
    	talkmgr.confirm(TalkappiMessageAdmin.inquiry.label.confirm_title, '', function(result){
			if (result.button == 'confirm') {
				if (scene.length !== 0 && template.length !== 0) {
					talkmgr.url(`/admin/bottheme?type=inquiry&scene=${scene}&template=${template}`, true);
				} else if (scene.length !== 0 && template.length == 0) { 
					talkmgr.url(`/admin/bottheme?type=inquiry&scene=${scene}`, true);
				} else if  (scene.length == 0 && template.length == 0) {
					talkmgr.url(`/admin/botscenes`, true);
				}
			}
		});	
	});

	$(document).on("click", '.js-edit', function (e) {
		let msg = $(this).prev().attr('data-value');
    	talkmgr.confirm(TalkappiMessageAdmin.inquiry.label.confirm_title, '', function(result){
			if (result.button == 'confirm') {
				if (msg.length !== 0) {
					talkmgr.url(`/admin/msgnew?cd=${msg}`, true);
				} else { 
					talkmgr.url('/admin/msglist?type=mal', true);
				}
			}
		});	
	});
	
	var clipboard = new ClipboardJS('.copy');
	clipboard.on('success', function(e) {
		talkmgr.toast('info', TalkappiMessageAdmin.common.message.info, TalkappiMessageAdmin.common.message.success.clipboard_copy_success);
		e.clearSelection();
	});
	clipboard.on('error', function(e) {
		talkmgr.toast('error', TalkappiMessageAdmin.common.message.error, TalkappiMessageAdmin.common.message.error.clipboard_copy_error);
	});

	$(document).on("click", ".js-span-setting", function () {
		// unactive
		$(this).parent().find(".icon-form-single-option-off").removeClass("active");
		$(this).parent().find("span").css("color", "#3d3f45");
		$(this).parent().find("input").css("color", "#a1a4aa");
		$(this).parent().find(".js-span-setting").removeClass("active");
		// active
		$(this).find(".icon-form-single-option-off").addClass("active");
		$(this).addClass("active");
		$(this).find("span").css("color", "#000");
		$(this).find("input").css("color", "#000");
		if ($(this).hasClass('js-rd-cancel-policy')) {
			$('.js-cancel-policy').show();
		}
		else {
			$('.js-cancel-policy').hide();
		}
	});

	$( "input[name='remind1_day'],input[name='remind2_day']" ).blur( function(e){
		const day = $(this).val();
		if (day < 0 || day > 365) $(this).val('');
	});

	$( "input[name='remind1_time'],input[name='remind2_time']" ).blur( function(e){
		const day = $(this).val();
		if (day < 0 || day > 23) $(this).val('');
	});

	$( "input[name='member_mail_from']" ).blur( function(e){
		const sender = $(this).val();
		checkAwsVarify(sender);
	});


});


// 横田追加分
$(document).ready(function(){

	if (talkmgr.data('user_mail_template') != '') {
		$(".js-select-user-container").slideDown(0);
	}

	const bot_id = $('input[name="bot_id_token"]').val();
	talkmgr.userSelect({bot_id}, function(e){
		const mail_users = $('input[name="mail_users"]').val();
		if (mail_users != '') {
			const selected_users = mail_users.split(',');
			for(user of e.data) {
				if (selected_users.indexOf(user.code.toString()) >= 0) {
					$('.js-selected-users').append('<li class="js-selected-user" data-user-id="' + user.code + '">' + user.name + '；</li>');
				}
			}
			$('.js-selected-users').slideDown(0);
		}
	});

	talkmgr.on('select', 'user_mail_template', function(e){
		if (e.data.code == '') {
			$(".js-select-user-container").slideUp(0);
		}
		else {
			$(".js-select-user-container").slideDown(0);
		}
	});

	$(document).on("click", ".js-add-user-container", function (e) {
		const users = [];
		$('.js-selected-users .js-selected-user').each(function () {
			users.push($(this).data('user-id'));
		});
		talkmgr.userSelect({user_list:users}, function(e){
			if (e.button !== 'confirm') return;
			$('.js-selected-users').empty();
			$('.js-selected-users').slideUp(0);
			for(user of e.data) {
				$('.js-selected-users').append('<li class="js-selected-user" data-user-id="' + user.user_id + '">' + user.name + '；</li>');
				$('.js-selected-users').slideDown(0);
			}
		});
	});

	// 期間：時刻の初期表示
	_start_time.length === 0 && $("#start_time").val("");
	_end_time.length === 0 && $("#end_time").val("");

	const withSetting = function(container){
		const _labels = []
		container.find(".lines-container").each(function(){
			if(
				$(this).hasClass("js-password") || 
				$(this).hasClass("js-tax-rate") ||
				$(this).hasClass("js-2-step-limit") ||
				$(this).hasClass("js-input-policy-opening")
				){
				return;
			}
			if (
				($(this).find("input").val() && $(this).find("input").val() !== "0" && $(this).find("input").val() !== '[]') ||
				($(this).find("textarea").val() && $(this).find("textarea").val().trim() !== "")
			  ) {
				_labels.push('<span class="flexbox">' + $(this).find(".basic-label").text().trim() + '<span class="icon-success"></span></span>');
			}
		});
		_labels.join(' ');
		container.find(".js-with-settings").append(_labels.join(' '));
		container.find(".js-with-settings").css("color", "#000");
		if(_labels.length === 0){
			container.find(".js-with-settings").text(TalkappiMessageAdmin.common.label.not_set);
			container.find(".js-with-settings").css("color", "#A1A4AA");
		}
	}

	$(".js-section-container").each(function(){
		withSetting($(this));
	});

	$(document).on("click", ".js-fold", function(e){
		e.stopPropagation();
		const _container = $(this).parents(".js-section-container");
		if(_container.hasClass("hide-section")){ // 展開する
			_container.find(".js-section-contents").slideDown();
			_container.removeClass("hide-section");
			_container.find(".js-fold-text").text(TalkappiMessageAdmin.inquiry_survey.fold);
			_container.find(".js-with-settings").text("");
		} else { // 折りたたむ
			_container.find(".js-section-contents").slideUp();
			_container.addClass("hide-section");
			_container.find(".js-fold-text").text(TalkappiMessageAdmin.inquiry_survey.expand);
			withSetting(_container);
		}
	});

	_inquiry_data.cancel_policy.policy ??= [];
	TalkappiPolicy.data('.js-cancel-policy', _inquiry_data.cancel_policy.policy);
});

function checkAwsVarify (sender) {
	if (sender == "" || sender == undefined ) {
		$('.js-send-address').show();
		$('.js-aws-mail-error').hide();
		$('.js-mail-from').css("color","unset");
		return;
	}
	$.ajax({
		url: "/ajax/verify_ses_mail_check_id?email=" + sender,
		dataType: "json",
		async: true,
		success:function(data) {
			if (data.success) {
				$('.js-send-address').hide();
				$('.js-aws-mail-error').hide();
				$('.js-aws-varify').show();
				// $('.js-mail-from').css("color","unset");
			}
			else {
				$('.js-send-address').show();
				$('.js-aws-mail-error').show();
				$('.js-aws-varify').hide();
				// $('.js-mail-from').css("color","red");
				// talkmgr.toast('error', TalkappiMessageAdmin.common.message.error, TalkappiMessageAdmin.inquiry.message.error.aws_varify);
			}
		},
		error:function(XMLHttpRequest,textStatus, errorThrown) {
			console.log('error', TalkappiMessageAdmin.common.message.error, TalkappiMessageAdmin.common.message.operation_failed);
		}
	});
}

function checkMailTemplateLangIsIncludeDisplayLang(onSuccessCallback) {
	const display_langs = talkmgr.data('.js-language');
	const user_mail_template = $('input[name="user_mail_template"]').val();
	const member_mail_template = $('input[name="member_mail_template"]').val();
	if (!user_mail_template && !member_mail_template) {
		onSuccessCallback && onSuccessCallback();
	} else {
		const params = {};
		if (user_mail_template) {
			params.user_mail_template = user_mail_template;
		}
		if (member_mail_template) {
			params.member_mail_template = member_mail_template;
		}
		$.ajax({
			url: "/ajax/get_mail_template_langs",
			type: "post",
			data: params,
			success: function(response) {
				const responseObj = JSON.parse(response);
				if (responseObj['result'] == 'true' && responseObj['data'] && typeof responseObj['data'] === 'object') {
					const datas = responseObj['data'];
					let totalFlag = true;
					let errorMsg = '';
					Object.keys(datas).forEach((msg_cd, _) => {
						const mail_langs = datas[msg_cd];
						const template_msg_name = (msg_cd === user_mail_template) ? _user_mail_template_list[msg_cd] : _member_mail_template_list[msg_cd];
						const currentFlag = display_langs.every((display_lang) => mail_langs.includes(display_lang));
						if (!currentFlag) {
							totalFlag = false;
							errorMsg += TalkappiMessageAdmin.inquiry.message.error.mail_template_error_message(template_msg_name);
						}
					})
					if (!totalFlag) {
						talkmgr.toast('error', TalkappiMessageAdmin.inquiry.message.error.mail_template_lang_error, errorMsg, 0, 0);
					} else {
						onSuccessCallback && onSuccessCallback();
					}
				} else {
					talkmgr.toast('error', TalkappiMessageAdmin.common.message.error.unexpected_error, '');
				}
			},
			error: function (XMLHttpRequest, textStatus, errorThrown) {
				console.log(errorThrown);
			}
		})
	}
}