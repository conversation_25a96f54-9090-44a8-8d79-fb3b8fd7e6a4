$(function(){
	talkmgr.load();
	_admin_params.paging = _paging;

	talkmgr.initable('.js-data-table', {
        paging: {
			..._admin_params.paging,
			sizeChange: function (p) {
				_admin_params.paging = p;
				talkmgr.submit();
            },
            pageChange: function (p) {
				_admin_params.paging = p;
				talkmgr.submit();
            },
			all:1,
        },
    });

	// Function to format the child row content
	function formatChildRow(executionData) {
		return '<div class="child-row-content">' +
			'<div class="batch-details-container">' +
				'<div class="batch-details-row">' +
					'<!-- Execution Results Card -->' +
					'<div class="batch-detail-card">' +
						'<div class="card-header">' +
							'<i class="fa fa-bar-chart"></i> ' + TalkappiMessageAdmin.batch.label.execution_results +
						'</div>' +
						'<div class="card-body">' +
							'<div class="detail-item">' +
								'<span class="detail-label">' + TalkappiMessageAdmin.batch.label.execution_time + '</span>' +
								'<span class="detail-value">' + (executionData.duration_formatted || '-') + '</span>' +
							'</div>' +
							'<div class="detail-item">' +
								'<span class="detail-label">' + TalkappiMessageAdmin.batch.label.exit_code + '</span>' +
								'<span class="detail-value">' + (executionData.exit_code || '-') + '</span>' +
							'</div>' +
							'<div class="detail-item">' +
								'<span class="detail-label">' + TalkappiMessageAdmin.batch.label.records_processed + '</span>' +
								'<div class="detail-value">' +
									'<div class="record-stats">' +
										'<span class="stat-item">' + TalkappiMessageAdmin.batch.label.processed + ': <strong>' + (executionData.records_processed ? parseInt(executionData.records_processed).toLocaleString() : '0') + '</strong></span>' +
										'<span class="stat-item">' + TalkappiMessageAdmin.batch.label.success + ': <strong class="text-success">' + (executionData.records_success ? parseInt(executionData.records_success).toLocaleString() : '0') + '</strong></span>' +
										'<span class="stat-item">' + TalkappiMessageAdmin.batch.label.failed + ': <strong class="text-danger">' + (executionData.records_failed ? parseInt(executionData.records_failed).toLocaleString() : '0') + '</strong></span>' +
									'</div>' +
								'</div>' +
							'</div>' +
							(executionData.error_message ?
								'<div class="detail-item error-item">' +
									'<span class="detail-label">' + TalkappiMessageAdmin.batch.label.error_info + '</span>' +
									'<div class="detail-value error-message">' +
										'<span class="error-code">[' + (executionData.error_code || '') + ']</span>' +
										'<span class="error-text">' + executionData.error_message + '</span>' +
									'</div>' +
								'</div>' : '') +
						'</div>' +
					'</div>' +

					'<!-- Retry Control Card -->' +
					'<div class="batch-detail-card">' +
						'<div class="card-header">' +
							'<i class="fa fa-repeat"></i> ' + TalkappiMessageAdmin.batch.label.retry_control +
						'</div>' +
						'<div class="card-body">' +
							'<div class="detail-item">' +
								'<span class="detail-label">' + TalkappiMessageAdmin.batch.label.retry_count + '</span>' +
								'<span class="detail-value">' +
									'<span class="retry-count">' + (executionData.retry_count ? parseInt(executionData.retry_count).toLocaleString() : '0') + '</span>' +
									' / ' +
									'<span class="max-retries">' + (executionData.max_retries ? parseInt(executionData.max_retries).toLocaleString() : '0') + '</span>' +
								'</span>' +
							'</div>' +
							(executionData.next_retry_time ?
								'<div class="detail-item">' +
									'<span class="detail-label">' + TalkappiMessageAdmin.batch.label.next_retry + '</span>' +
									'<span class="detail-value">' + executionData.next_retry_time + '</span>' +
								'</div>' : '') +
							(executionData.retry_reason ?
								'<div class="detail-item">' +
									'<span class="detail-label">' + TalkappiMessageAdmin.batch.label.retry_reason + '</span>' +
									'<span class="detail-value">' + executionData.retry_reason + '</span>' +
								'</div>' : '') +
							'<div class="detail-item">' +
								'<span class="detail-label">' + TalkappiMessageAdmin.batch.label.daily_success_count + '</span>' +
								'<span class="detail-value">' + (executionData.daily_success_count ? parseInt(executionData.daily_success_count).toLocaleString() : '0') + '</span>' +
							'</div>' +
						'</div>' +
					'</div>' +
				'</div>' +
				'<div class="batch-details-row">' +

					'<!-- Timestamp Card -->' +
					'<div class="batch-detail-card">' +
						'<div class="card-header">' +
							'<i class="fa fa-clock-o"></i> ' + TalkappiMessageAdmin.batch.label.timestamp +
						'</div>' +
						'<div class="card-body">' +
							'<div class="detail-item">' +
								'<span class="detail-label">' + TalkappiMessageAdmin.batch.label.scheduled_time + '</span>' +
								'<span class="detail-value">' + (executionData.scheduled_time || '-') + '</span>' +
							'</div>' +
							'<div class="detail-item">' +
								'<span class="detail-label">' + TalkappiMessageAdmin.batch.label.start_time + '</span>' +
								'<span class="detail-value">' + (executionData.actual_start_time || '-') + '</span>' +
							'</div>' +
							'<div class="detail-item">' +
								'<span class="detail-label">' + TalkappiMessageAdmin.batch.label.end_time + '</span>' +
								'<span class="detail-value">' + (executionData.actual_end_time || '-') + '</span>' +
							'</div>' +
							'<div class="detail-item">' +
								'<span class="detail-label">' + TalkappiMessageAdmin.batch.label.created_at + '</span>' +
								'<span class="detail-value">' + (executionData.created_at || '-') + '</span>' +
							'</div>' +
							'<div class="detail-item">' +
								'<span class="detail-label">' + TalkappiMessageAdmin.batch.label.updated_at + '</span>' +
								'<span class="detail-value">' + (executionData.updated_at || '-') + '</span>' +
							'</div>' +
						'</div>' +
					'</div>' +

					'<!-- System Information Card -->' +
					'<div class="batch-detail-card">' +
						'<div class="card-header">' +
							'<i class="fa fa-cog"></i> ' + TalkappiMessageAdmin.batch.label.system_info +
						'</div>' +
						'<div class="card-body">' +
							'<div class="detail-item">' +
								'<span class="detail-label">' + TalkappiMessageAdmin.batch.label.execution_type + '</span>' +
								'<span class="detail-value">' + (executionData.type_label || '-') + '</span>' +
							'</div>' +
							'<div class="detail-item">' +
								'<span class="detail-label">' + TalkappiMessageAdmin.batch.label.execution_server + '</span>' +
								'<span class="detail-value">' + (executionData.execution_server || '-') + '</span>' +
							'</div>' +
							'<div class="detail-item">' +
								'<span class="detail-label">' + TalkappiMessageAdmin.batch.label.process_id + '</span>' +
								'<span class="detail-value">' + (executionData.process_id || '-') + '</span>' +
							'</div>' +
							'<div class="detail-item">' +
								'<span class="detail-label">' + TalkappiMessageAdmin.batch.label.created_by + '</span>' +
								'<span class="detail-value">' + (executionData.created_by || '-') + '</span>' +
							'</div>' +
							'<div class="detail-item">' +
								'<span class="detail-label">' + TalkappiMessageAdmin.batch.label.updated_by + '</span>' +
								'<span class="detail-value">' + (executionData.updated_by || '-') + '</span>' +
							'</div>' +
						'</div>' +
					'</div>' +
				'</div>' +
			'</div>' +
		'</div>';
	}
	
	$('#searchButton').click(function() {
		talkmgr.submit();
	});

	$('#manualExecuteButton').click(function() {
		var batchTypeSelect = $('#manual_batch_type');
		if (!batchTypeSelect.val()) {
			talkmgr.toast('error', TalkappiMessageAdmin.common.message.error, TalkappiMessageAdmin.batch.message.error.select_batch_required);
			return;
		}

		var selectedOption = batchTypeSelect.find('option:selected');
		talkmgr.warning(TalkappiMessageAdmin.batch.message.confirm.manual_execute_title, TalkappiMessageAdmin.batch.message.confirm.manual_execute_detail + selectedOption.text(), function(result){
			if (result.button == 'confirm') {
				// Set the hidden input value for form submission
				$('#manual_batch_type_submit').val(batchTypeSelect.val());
				// Submit using talkmgr.submit instead of $.ajax
				talkmgr.submit('manual_execute');
			}
		});
	});

	$(document).on('click', '.toggle-details', function(e) {
		e.preventDefault();
		
		var $button = $(this);
		var $icon = $button.find('i');
		var $row = $button.closest('tr');
		var executionId = $row.attr('data-execution-id');
		var $existingDetailRow = $row.next('.child-detail-row');
		
		// Close all other open detail rows
		$('.child-detail-row').each(function() {
			var $detailRow = $(this);
			var $parentRow = $detailRow.prev('.execution-row');
			if ($parentRow.attr('data-execution-id') !== executionId) {
				$detailRow.remove();
				$parentRow.find('.toggle-details i').removeClass('fa-minus').addClass('fa-plus');
			}
		});
		
		// Toggle the clicked row's child row
		if ($existingDetailRow.length) {
			// Hide child row
			$existingDetailRow.remove();
			$icon.removeClass('fa-minus').addClass('fa-plus');
			$button.blur();
		} else {
			// Show child row
			try {
				var executionData = JSON.parse($row.attr('data-execution-details'));
				var childContent = formatChildRow(executionData);
				
				// Create child row element
				var $childRow = $('<tr class="child-detail-row">');
				$childRow.append('<td colspan="6">' + childContent + '</td>');
				
				// Insert after current row
				$row.after($childRow);
				$icon.removeClass('fa-plus').addClass('fa-minus');
			} catch (e) {
				console.error('Error parsing execution data:', e);
				
				// Create error child row
				var $errorRow = $('<tr class="child-detail-row">');
				$errorRow.append('<td colspan="6"><div class="child-row-content"><p>' + TalkappiMessageAdmin.batch.label.data_load_failed + '</p></div></td>');
				$row.after($errorRow);
				$icon.removeClass('fa-plus').addClass('fa-minus');
			}
		}
	});

	// Running status notification and auto-refresh
	if ($('.status-running').length > 0) {
		// Show notification
		var notification = $('<div>')
			.css({
				'position': 'fixed',
				'top': '20px',
				'right': '20px',
				'background': '#17a2b8',
				'color': 'white',
				'padding': '10px 15px',
				'border-radius': '5px',
				'z-index': '1000',
				'box-shadow': '0 2px 10px rgba(0,0,0,0.1)'
			})
			.text(TalkappiMessageAdmin.batch.message.notification.running_batch_auto_refresh)
			.appendTo('body');
		
		// Hide notification after 3 seconds
		setTimeout(function() {
			notification.fadeOut(500);
		}, 3000);
		
		// Auto-refresh after 30 seconds
		setTimeout(function() {
			talkmgr.warning(TalkappiMessageAdmin.batch.message.confirm.running_batch_refresh_title, TalkappiMessageAdmin.batch.message.confirm.running_batch_refresh_detail, function(result){
				if (result.button == 'confirm') {
					location.reload();
				}
			});
		}, 30000);
	}
});
