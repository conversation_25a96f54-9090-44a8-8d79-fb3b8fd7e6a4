@charset "utf-8";

body {
  overflow-y: hidden;
}

/* Overwrite layout.css file */
@media (min-width: 992px){
  .page-header-fixed .page-sidebar-wrapper {
    position: absolute;
    box-shadow: 1px 0px 8px rgba(227, 229, 232, 0.8);
    overflow-y: none;
  }
}

/* Chat window styles */
.portlet-body {
  background-color: #E5E5E5;
}

.chats li {
  padding: 5px 10px;
}

.chats li.out .message {
  margin-left: auto;
  width: 70%;
  text-align: left;
  padding: 0px;
  background: #fff;
}

.chats li.in .message {
  margin-right: auto;
  width: 70%;
  text-align: left;
  padding: 0px;
  background: #fff;
}

.chats li.out .message .body .file,
.chats li.in .message .body .file {
  word-break:break-all;
  display:flex;
  align-items:center;
  column-gap:6px;
}

.chats li.out .message .body .file img,
.chats li.in .message .body .file img {
  width: 48px;
  height: 48px;
}

.chats li.out .message .body .file div,
.chats li.in .message .body .file div {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.chats li.out .message .body .file div .filename,
.chats li.in .message .body .file div .filename {
  font-weight:400;
  font-size:12px;
  line-height:18px;
  color:#000000;
}

.chats li.out .message .body .file div .filesize,
.chats li.in .message .body .file div .filesize {
  font-size:12px;
  line-height:14px;
  color:#A1A4AA;
}

.message-bottom {
  display: flex;
  justify-content: space-between;
  margin-top: 4px;
  width: 70%;
  text-align: left;
  padding: 0px;
}

.chats li.out .avatar {
  margin-right: 0px;
}

.chats li.out .message-bottom {
  margin-left: auto;
  margin-right: 50px;
}

.chats li.in .message-bottom {
  margin-right: auto;
  margin-left: 50px;
}

.chats li.comment .message,
.chats li .name,
.chats li .datetime {
  color: #A1A4AA;
}

.chats li .body {
  padding: 5px 12px;
  word-break: break-all;
}

#mail_chatuser .message {
  background-color: #E5E5E5;
  text-align: right;
}

.message-bottom_blue,
.message-bottom_blue:hover,
.message-bottom_blue:focus {
  color: #245BD6;
  background-color: #D3EEFF;
}
.message-bottom_red,
.message-bottom_red:hover,
.message-bottom_red:focus {
  color: #E53361;
  background-color: #FFDCE5;
}
.message-bottom_orange,
.message-bottom_orange:hover,
.message-bottom_orange:focus {
  color: #FF9551;
  background-color: #FFE6D6;
}
.message-bottom_green,
.message-bottom_green:hover,
.message-bottom_green:focus {
  color: #32CE55;
  background-color: #CFF2D7;
}
.message-bottom_gray,
.message-bottom_gray:hover,
.message-bottom_gray:focus {
  color: #1B1B1E;
  background-color: #A1A4AA;
}

.message-bottom-tag {
  padding: 2px 4px;
  border-radius: 4px;
}

.message-image {
  width: 100%;
  height: auto;
}

/* Header styles */
.page-header.navbar .top-menu .navbar-nav > li.dropdown-user .dropdown-toggle {
  padding-top: 20px;
}

.page-header {
  display: flex;
  flex-direction: column;
}

.page-header.navbar {
  height: 100px;
}

.page-header.navbar .page-top {
  margin-left: 0px;
  width: fit-content;
  height: 66px;
}

.page-header-inner {
  display: flex;
  justify-content: space-between;
  height: 66px;
}

.page-header.navbar .page-logo {
  display: flex;
  align-items: center;
  width: fit-content;
  height: 66px;
}

.page-header-fixed .page-container {
  margin-top: 100px;
}

.header-filter-container {
  margin: 0;
  padding-right: 20px;
  height: 34px;
  display: flex;
  align-items: center;
}

.header-filter-condition {
  padding: 4px;
  border-radius: 4px;
  width: 120px;
  border: 1px solid #E3E5E8;
}

.header-filter-title {
  padding-left: 20px;
  padding-right: 12px;
  font-weight:300; 
  margin-bottom: 0;
}

.header-filter-spacing {
  margin-left: 2px;
}

.filter-not-active {
  border: none;
  padding: 1px 7px;
  background-color: #fff;
  color: #3D3F45;
  font-weight: 300;
}

.filter-not-active:hover {
  background-color: #E3E5E8;
}

.filter-active {
  border: none;
  padding: 1px 7px;
  background-color: #E3E5E8;
  color: #000;
  font-weight: 400;
}

.filter-active:hover {
  background-color: rgba(61, 63, 69, 0.25);
}

@media (max-width: 991px) {
  .header-filter-container {
    display: none;
  }
}

.filter-details-button {
  margin-left: 10px;
  border: none;
  border-radius: 16px;
  padding: 5px 12px;
}

.chat-logo-default {
  width: 40px;
  height: 40px;
  margin: 0;
  border-radius: 50%;
}

h1 {
  font-size: 16px;
  color: #000;
  font-weight: 500;
  margin: 0;
  margin-left: 12px;
}

.page-header.navbar .top-menu .navbar-nav > li.dropdown-user > .dropdown-toggle > .username {
  color: #000;
  font-weight: 400;
}

.page-header.navbar .top-menu .navbar-nav > li.dropdown-user > .dropdown-toggle > .username > .username-status {
  color: #32CE55;
  font-weight: 400;
}

.page-header.navbar .top-menu .navbar-nav > li.dropdown-dark .dropdown-menu.dropdown-menu-default > li a, .page-header.navbar .top-menu .navbar-nav > li.dropdown-dark .dropdown-menu .dropdown-menu-list > li a {
  color: #fff;
}

.page-header.navbar .top-menu .navbar-nav > li.dropdown-dark .dropdown-menu {
  background-color: #3D3F45;
  color: #fff;
  width: 240px;
}

.page-header.navbar .top-menu .navbar-nav > li.dropdown-dark .dropdown-menu:after {
  border-bottom-color: #3D3F45;
}

.status-operator-languages {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
  color: #C8CACE;
}

/* Details modal styles */
.modal-form-container {
  display: flex;
  flex-direction: column;
}

.modal-buttons-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12px;
}

.modal-title-group {
  min-width: 200px;
}

.modal-buttons-group {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.modal-button {
  background-color: #EBEDF2;
  padding: 3px 12px;
  border-radius: 12px;
  cursor: pointer;
}

.modal-button.active {
  background-color: #D3EEFF;
}

/* Chat styles */
.chat-header-title {
  margin-bottom: 0px;
  font-size: 16px;
  font-weight: 400;
}

.chat-header-spacing {
  margin-right: 12px;
}

.chat-header-text {
  margin-bottom: 0px;
  font-size: 12px;
}

.chat-memo-textarea {
  max-width: 400px;
  flex: 2;
}

.chat-header-button {
  background-color: #D3EEFF;
  color: #000;
  border: none;
  font-size: 11px;
}

.chat-memo-input {
  max-width: 250px;
  flex: 1
}

.chat-memo-button {
  font-size: 12px;
  color: #fff;
  border: none;
  border-radius: 4px;
  padding: 5px 12px;
}

.chat-memo-support-button {
  background-color: #fff;
  border: 1px solid #C8CACE;
  color: #3D3F45;
}

.chat-statusbar-container {
  min-height: 40px;
  display: flex; 
  justify-content: space-between; 
  align-items: center;
  padding: 0px 16px;
}

.chat-status-button {
  border-radius: 4px;
  padding: 5px 12px;
  border: none;
}

/* Sidebar styles */
.page-sidebar .page-sidebar-menu .sub-menu li > .sidebar-user-container {
  padding: 10px 8px 10px 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.page-sidebar .page-sidebar-menu .sub-menu li, .page-sidebar-closed.page-sidebar-fixed .page-sidebar:hover .page-sidebar-menu .sub-menu li {
  /* Overwrite layout.css file */
  margin-top: 0 !important; 
  border-bottom: 1px solid #EBEDF2;
}

.sidebar-search-icon {
  height: 100%; 
  width: 100%; 
  display: flex;
  cursor: pointer;
}

.sidebar-user-left {
  display: flex;
  align-items: center;
}

.sidebar-user-name {
  padding: 10px;
  position: relative;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  color: #000;
  flex-shrink: 1;
  display: block;
  align-items: center;
}

.sidebar-user-wrapper {
  position: relative;
}

.sidebar-user-avatar {
  position: relative;
  border-radius: 50%; 
  margin: 0px;
  border: 1px solid #E3E5E8;
}

.sidebar-user-sns {
  position: absolute;
  bottom: -5px;
  right: -5px;
}

.sidebar-user-time {
  background-color: #F6F7F9;
  font-size: 10px;
  color: #000;
  padding: 1px 5px;
  flex: none;
  margin-left: auto;
}

.sidebar-user-online {
  background-color: #32CE55;
  height: 5px;
  width: 5px;
  border-radius: 50%;
  position: absolute;
  top: 10px;
  right: 0px;
}

.sidebar-status-badge {
  color: #fff;
  border-radius: 8px;
  padding: 0px 5px;
  font-size: 11px;
  margin-right: 10px;
  flex: none;
}

/* Input form styles */
.input-form-container {
  background-color: #fff;
  padding: 0;
}

.input-button-container { 
  display: flex;  
  justify-content: space-between; 
  align-items: flex-end;
  padding: 10px; 
  background-color: #F6F7F9;
} 
 
.input-button-left { 
  background-color: #E3E5E8; 
  border-radius: 2px; 
  border: none; 
  margin-right: 8px; 
  padding: 4px 8px; 
} 
 
.input-button-left > a { 
  color: #3D3F45; 
}


.input-button-spacing {
  margin-right: 12px;
}

.input-button-right {
  background-color: #fff;
  border: 1px solid #E3E5E8;
  border-radius: 16px;
  padding: 5px 12px;
}

.input-button-send {
  border: none;
  border-radius: 4px;
  padding: 5px 12px;
  font-size: 13px;
}

.input-button-switch {
  padding: 0;
  background-color: transparent;
}

.input-button-switch > .switch-container {
  background-color: #fff;
  border: 1px solid #E3E5E8;
  border-radius: 16px;
  padding: 5px 12px;
}

/* Colors */
.sidebar-green {
  background-color: #32CE55;
  color: #fff;
}

.sidebar-orange {
  background-color: #FF9551;
}

.sidebar-red {
  background-color: #E53361;
}

.sidebar-blue {
  background-color: #245BD6;
  color: #fff;
}

.sidebar-yellow {
  background-color: #FFF0BB;
  color: #3D3F45;
}

.sidebar-gray {
  background-color: #A1A4AA;
}

.switch-span {
  left: 0;
}

.switch-span.on {
  left: auto;
}

.dropdown-menu li > .msgli {
  border-bottom: 1px solid #EBEDF2;
  padding: 8px 0px;
  min-height: 40px;
  color: #245BD6;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;
}

.dropdown-menu li > .msgli span.msgname {
  white-space: pre-wrap;
  max-width: 80%;
}

.dropdown-menu li > .msgli:hover {
  background-color: #FFFFFF;
  color: #245BD6;
}

.dropdown-menu.quick-message {
  width: 375px;
  box-shadow: none;
  box-shadow: 1px 2px 8px 0px rgba(61, 63, 69, 0.24);
}

.quick-message-title {
  padding: 12px;
  box-shadow: 0px -1px 0px 0px #EBEDF2 inset;
  margin-bottom: 0;
}

.quick-messages-cates .quick-messages-cate {
  width:108px;
  padding:10px 12px;
} 
.quick-messages-cates .quick-messages-cate:hover,
.quick-messages-cates .quick-messages-cate.active {
  background-color: #F6F7F9;
}
.quick-messages-items {
  box-shadow: 1px 0px 0px 0px #EBEDF2 inset;
  overflow-y: auto; 
  height: 456px;
  max-height: calc(100vh - 200px);
  display: flex;
  flex-direction: column;
  flex: 1;
}
.quick-messages-items ul {
  padding: 0;
  list-style: none;
}

.quick-messages-items .sub-cate .sub-cate-header-title {
  font-size: 14px;
  color: #3D3F45;
}
.quick-messages-items .sub-cate .sub-cate-header {
  display: flex;
  align-items: center;
  gap: 6px;
  background-color: #F6F7F9;
  padding: 6px 12px;
  box-shadow: 0px -1px 0px 0px #EBEDF2 inset;
  cursor: pointer;
}
.quick-messages-items .sub-cate .sub-cate-header-icon {
  width: 12px;
  height: 12px;
  background-image: url(../images/icon-unfold.svg);
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
}
.quick-messages-items .sub-cate .sub-cate-items {
  padding: 0 12px;
}
.quick-messages-items .sub-cate.fold .sub-cate-header-icon {
  background-image: url(../images/icon-fold.svg);
}
.quick-messages-items .sub-cate.fold .sub-cate-items {
  display: none;
}

/* Mobile対応 */
@media (max-width: 991px) {
  .chat-page-sidebar,
  .page-header,
  #member-info,
  #member-info-open,
  #clear-btn,
  #switch-mode-1,
  #send-option {
    display: none;
  }

  .page-content-wrapper .chat-page-content.page-content {
    padding: 0 !important;
    height: 100vh;
  }

  select,
  textarea,
  input {
    font-size: 16px !important;
  }
  .quick-messages-items {
    max-height: 200px;
  }
}

/* Request modal */
.request-btn {
  color: #fff;
  width: 100%;
}

.request-modal-form-container {
  display: grid;
  grid-template-columns: auto auto;
  grid-row: auto auto;
  grid-column-gap: 10px;
  grid-row-gap: 10px;
}

.request-red,
.request-red:focus,
.request-red:hover {
  background-color: #E53361;
  border: 1px solid #E53361;
  color: #fff;
}
.request-orange,
.request-orange:focus,
.request-orange:hover {
  background-color: #FF9551;
  border: 1px solid #FF9551;
  color: #fff;
}
.request-green,
.request-green:focus,
.request-green:hover {
  background-color: #32CE55;
  border: 1px solid #32CE55;
  color: #fff;
}
.request-gray,
.request-gray:focus,
.request-gray:hover {
  background-color: #3D3F45;
  border: 1px solid #3D3F45;
  color: #fff;
}

.not-active {
  color: #000;
  background-color: #fff;
  border: 1px solid #3D3F45;
}

.request-header {
  text-align: center;
  margin-bottom: 15px;
}

.request-modal-header {
  padding: 15px 0 0 0;
  margin-left: 15px;
  margin-right: 15px;
}

.request-modal-footer {
  border-top: none;
}

.intent-title {
  font-size: 15px;
  font-weight: 500;
}

.intent-description {
  overflow: hidden;
  white-space: pre-line;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1; 
  -webkit-box-orient: vertical;
}

#msg_preview_box {
  position: absolute;
  bottom: 50px;
  left: 50%;
  transform: translateX(-50%);
  margin: 0;
}