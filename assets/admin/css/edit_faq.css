/* Begin Common */

.faq-edit {
  list-style: none;
  padding: 0;
  margin: 0;
  resize: vertical;
}

.faq-edit:focus {
  outline: none;
  box-shadow: 0 0 0 1px #245BD6;
}

/*  */
.edit-answer-title {
  min-width: 109px;
  margin: 0;
}
.edit-answer-title-input {
  max-width:500px;
  min-height: 67px;
  white-space: pre-wrap;
  word-wrap: break-word;
  display:block;
}

/*  */

.edit-menu-container {
  width: 80%;
  min-width: 450px;
  padding: 12px 24px 24px 23px;
  border-radius: 4px;
  border: solid 1px #e3e5e8;
  background: #f6f7f9;
}
.faq-edit-container {
  display: flex;
  min-height: 80vh;
}

.faq-edit-container .left-container {
  padding: 37px 48px;
  padding: 0 48px 37px 48px;
  width: 70%;
}

.edit-faq-main-container {
  position: sticky;
  top: -15px;
  z-index: 99;
  background: #fff;
  padding: 28px 0;
  border-bottom: 1px solid #e3e5e8;
}

.menu-edit-title {
  width: 84px;
  min-width: 84px;
}

.edit-faq-answer {
  box-shadow: inset 0 1px 0 0 #e3e5e8;
  padding: 16px 0 28px 0;
}
.edit-faq-answer:first-child {
  box-shadow: none;
}

.edit-faq-menu {
  box-shadow: inset 0 1px 0 0 #e3e5e8;
  padding: 12px 0 0 0;
}

.disabled-add-selection {
  /* background: #e3e5e8 !important;
  color: #a1a4aa !important; */
}
/* ラベル削除　確認用モーダル */
.sdelete-icon-modal {
  width: 400px;
  height: 246px;
  background: #fff;
  position: fixed;
  top: calc(50% - 246px/2);
  left: calc(50% - 400px/2);
  z-index: 9999;
}
/* ラベル削除　確認用モーダル */
/* ボタン間の擬似線 */
.menu-modal-cancel-button {
  margin: 0 80px 0 0;
  position: relative;
}
.menu-modal-cancel-button::after {
  content: "";
  display: inline-block;
  width: 1px;
  height: 28px;
  background: #e3e5e8;
  position: absolute;
  top: -3px; 
  right: -40px;
}
.add-img-container {
  display: flex;
  align-items: center;
  cursor: pointer;
}
.add-img-container img {
  width: 20px;
  height: 20px;
  padding: 4px;
  border-radius: 4px;
  background: #e3e5e8;
}
/* 最終編集 */
.faq-edit-history {
  width: 320px;
  height: 24px;
  padding: 0 16px;
  border-radius: 13px;
  background: #ebedf2;
}
/* プレビュー画面 */
.faq-preview-container {
  min-height: 540px;
  width: 320px;
  border-radius: 10px;
  border: solid 1px #e3e5e8;
}
.faq-preview-header {
  height: 44px;
  padding: 0 12px;
  border-bottom: solid 1px #e3e5e8;
  display: flex;
  align-items: center;
}
.faq-preview-main {
  min-height: 540px;
  padding: 12px !important;
  border-radius: 0 0 10px 10px;
  background: #ebedf2;
  overflow-y: hidden;
}

.faq-preview-main:hover {
  overflow-y: scroll;
}

.faq-preview-faq-title {
  width: fit-content;
  width: -moz-fit-content;
  max-width: 265px;
  padding: 10px 12px !important;
  margin: 0 0 0 auto !important;
  border-radius: 10px 10px 0 10px;
  background: #a1a4aa;
}
.preview-faq-answer {
  width: fit-content;
  width: -moz-fit-content;
  max-width: 265px;
  padding: 10px 12px !important;
  margin: 12px 0 0 0 !important;
  border-radius: 10px 10px 10px 0;
  background: #fff;
  word-wrap: break-word;
  white-space: pre-wrap;
}
.preview-faq-answer img {
  max-width: 200px;
}
.preview-faq-answer-image {
  width: auto;
  width: 265px;
  height: 180px;
  object-fit: cover;
  margin: 12px 0 0 0 !important;
  border-radius: 10px 10px 10px 0;
}
.preview-faq-main-title {
  width: fit-content;
  width: -moz-fit-content;
  max-width: 265px;
  padding: 10px 12px !important;
  margin: 12px 0 0 0 !important;
  border-radius: 10px 10px 10px 0;
  background: #fff;
}
.preview-faq-menu-main-title {
  word-wrap: break-word;
  white-space: pre-wrap;
}
.preview-faq-icon-container {
  display: flex;
  flex-wrap: wrap;
}
.preview-faq-icon {
  width: fit-content;
  width: -moz-fit-content;
  min-height: 28px;
  padding: 0 16px;
  margin: 12px 10px 0 0;
  border-radius: 14px;
  border: solid 1px #c8cace;
  display: flex;
  align-items: center;
  cursor: pointer;
}
.menu-text {
  max-width:500px;
  height: 100px;
  white-space: pre-wrap;
  word-wrap: break-word;
  display:block;
}

/* 多言語翻訳参照 */
.trans-ref-container {
  max-width:500px;
  height: auto;
  padding: 7px 12px;
  border-radius: 4px;
  border: solid 1px #e3e5e8;
  background-color: #f6f7f9;
}
.menu-added-icon {
  cursor: move;
  min-height: 28px;
  height: auto;
  padding: 3px 16px !important;
  margin: 8px 10px 0 0 !important;
}
.btn-smaller-round.alert {
  font-size: 11px;
  letter-spacing: -.5px;
}

/* 日本語翻訳参照 */
.ref-container {
  background: #fff;
  word-wrap: break-word;
  padding: 12px;
  border-top: 8px solid #E3E5E8;
}
.ref-container:nth-child(1) {
  border-top: none;
}
.faq-preview-ref.faq-preview-main {
  padding: 0 !important;
}
.ref-text-main {
  word-wrap: break-word;
  white-space: pre-wrap;
}