@charset "utf-8";

/* Overwrite admin.css file to fix smaller screen UI */
@media screen and (min-width: 992px) {
  .content-container {
    min-width: auto;
  }
}

input:invalid {
  border: 1px solid red;
}

.calendar.image.round.btn:before {
  background: url("/assets/admin/css/img/icon-calender.svg") no-repeat;
}

.full-view {
  height: 100vh;
  max-height: 100vh;
  width: 100vw;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 10;
  background-color: white;
  padding: 24px;
  overflow-y: scroll;
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: 16px;
}
.breadcrumb-item,
.breadcrumb-item:visited {
  color: #000;
}

.calendar-icon {
  background: url("/assets/admin/css/img/icon-calendar-white.svg") no-repeat;
  height: 12px;
  width: 12px;
}

.edit-cal-input-button {
  flex: none;
}

.readonly-input {
  height: auto;
}

/* Customize full calendar */
:root {
  --fc-button-text-color: #3d3f45;
  --fc-button-bg-color: #e3e5e8;
  --fc-button-border-color: #e3e5e8;
  --fc-button-hover-bg-color: #c7cbd1;
  --fc-button-hover-border-color: #c7cbd1;
  --fc-button-active-bg-color: #245BD6;
  --fc-button-active-border-color: #245BD6;
  --fc-button-focus-bg-color: #c7cbd1;
  --fc-button-focus-border-color: #c7cbd1;

  --fc-event-bg-color: #c7cbd1;
  --fc-event-border-color: #c7cbd1;
}

.header-title {
  background-color: #ebedf2;
}
.header-title a {
  color: #3d3f45;
  font-weight: 300;
  width: 100%;
}
.resource-title {
  line-break: anywhere;
}

th.fc-day > .fc-scrollgrid-sync-inner > .fc-col-header-cell-cushion {
  padding: 2px 0px;
}

.fc-button:focus {
  background-color: #c7cbd1 !important;
  border: 1px transparent solid !important;
  box-shadow: none !important;
}
.fc-button-active:focus {
  background-color: #245BD6 !important;
}
.fc-next-button,
.fc-prev-button,
.fc-next-button:focus,
.fc-prev-button:focus {
  background-color: transparent !important;
  border: none !important;
  box-shadow: none !important;
}
.fc-button-active {
  color: #fff !important;
}
.fc .fc-timegrid-slot {
  height: 80px;
  border-bottom: 0;
}
.fc-custom-event {
  color: #3d3f45;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  height: 100%;
  overflow: auto;
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}
.fc-custom-event::-webkit-scrollbar {
  display: none;
}
.fc-custom-event-time {
  word-break: break-word;
}
.fc-custom-event-link {
  width: fit-content;
}
.fc-custom-event-link:hover {
  text-decoration: underline;
}
.fc-custom-maximum {
  display: flex;
  justify-content: space-between;
  flex-wrap: nowrap;
  gap: 3px;
}
.fc .fc-col-header-cell-cushion {
  padding: 0;
}

.fc-resource-selling-title {
  cursor: pointer; 
  width: 100%; 
  color: #fff; 
  padding: 0px 4px; 
  line-break: auto; 
  height: 42px; 
  text-align: center;
  display: flex;
  justify-content: center; 
  align-items: center;
}

/* modal css  */
.maximumcalendars-modal-container {
  width: 800px;
  max-height: 80%;
  padding: 25px 38px;
  margin: 0;
  border-radius: 4px;
  overflow-y: auto;
  -ms-overflow-style: none; /* IE, Edge */
  scrollbar-width: none; /* Firefox */
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 999;
  background: #fff;
  display: flex;
  flex-direction: column;
}
.maximumcalendars-modal-title {
  display: flex;
  padding: 8px 4px;
}
.maximumcalendars-modal-title h4 {
  font-size: 16px;
}
.maximumcalendars-input {
  margin: 20px;
}
.my-radio {
  position: relative;
  padding-left: 28px;
  cursor: pointer;
  user-select: none;
  bottom: 10px;
  left: 10px;
}
/* inputは非表示にする */
.my-radio input {
  display: none;
}
/* 常に表示する枠線の円 */
.radio-mark {
  position: absolute;
  top: 0; /* 上からの位置 */
  left: 0;
  height: 22px; /* 大きさ */
  width: 22px; /* 大きさ */
  /* border: solid 2px #d4dae2; 線 */
  border-radius: 50%;
  box-sizing: border-box;
  /* background: green; */
}
/* 選択時に重ねる円 */
.radio-mark:after {
  content: "✔︎";
  position: absolute;
  border-radius: 50%;
  top: 3px;
  /* bottom: 2px;*/
  right: 5px;
  opacity: 0; /* 透明にしておく */
}
/* 選択時に重ねた円の透明を解除 */
.my-radio input:checked + .radio-mark:after {
  opacity: 1;
}
.selected-calendar-flex {
  flex: none;
  margin-right: 16px;
}
.selected-calendar-input {
  flex: 1;
}

.event-selling-stopped {
  background-color: #EBEDF2 !important;
  border-color: #EBEDF2 !important;
}
.event-selling-stopped > .fc-event-main > .fc-custom-event > span {
  color: #A1A4AA;
}

.event-sold-out {
  background-color: #EBEDF2 !important;
  border-color: #EBEDF2 !important;
}

.event-sold-out-style-maximum {
  background-color: #d3d3d3 !important;
  border-color: #d3d3d3 !important;
}

.event-sold-out > .fc-event-main > .fc-custom-event > span {
  color: #A1A4AA;
}

.event-not-reserved-style-maximum {
  background-color: #D3EEFF !important;
  border-color: #D3EEFF !important;
}

.event-reserved-and-remaining-style-maximum {
  background-color: #FFF0BB !important;
  border-color: #FFF0BB !important;
}

.event-set-by-0-style-maximum {
  background-color: #f5d2d7 !important;
  border-color: #f5d2d7 !important;
}

.fc-timeline-event:has(.monthly-view),
.event-sold-out:has(.monthly-view),
.event-selling-stopped:has(.monthly-view) {
  background-color: #fff !important;
  border-color: #fff !important;
}

.fc-daily-calendar-name.fc-maximum-name {
  overflow: hidden;
  display: -webkit-box;
  text-overflow: ellipsis; /* 3点リーダ */
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 4; /* 4行 */
  line-clamp: 4;
  white-space: normal;
  flex: 1;
  min-width: 0;
}

.fc-on-sales:hover,
.fc-display-result:hover,
.fc-change-stock:hover {
  text-decoration: underline;
}

.fc-daygrid-dot-event:has(.monthly-view):hover {
  background: none;
}

.fc .fc-datagrid-cell-cushion {
  padding: 0px;
  white-space: unset;
  height: 100%;
}

.fc-timeline-event {
  margin: 9px 1px 1px 1px;
}

th[role='columnheader'] {
  display: none;
}

thead[role='rowgroup'] {
  background-color: #ebedf2;
}

.slot-label-css a, .fc-daygrid-day-number {
  color: #000;
  font-size: 10px;
  font-weight: 300;
}

@media only screen and (max-width: 768px) {
  .modal-image-container {
    width: auto;
    height: auto !important;
    min-height: 342px !important;
  }
  .modal-image-container .flexbox-x-axis {
    display: block;
  }
  .content-container {
    padding: 0 0 20px 0;
  }
  .section-container:has(#calendar){
    padding: 0;
  }
  .resource-name {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .fc-custom-maximum {
    flex-direction: column;
  }
  .fc-custom-maximum span {
    min-width: auto !important;
  }
  .page-content-wrapper .page-content {
    padding: 0 !important;
  }
  .full-view {
    padding: 24px 0;
  }

  .js-display-history {
    display: none;
  }

  .js-last-update {
    display: none;
  }
}