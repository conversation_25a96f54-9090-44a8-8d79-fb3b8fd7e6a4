/* Full screen view  */
.page-content-wrapper .page-content {
  padding: 0 !important;
  margin-left: unset !important;
}
.page-content-header,
.page-sidebar-wrapper {
  display: none;
}

.full-view {
  height: calc(100vh);
  min-height: 100vh;
  width: 100vw;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 10;
  background-color: white;
  padding: 0px;
  display: flex;
  flex-direction: column;
}

/* Congestion main container */
.congestion-main-container {
  display: flex;
  flex: 1;
  overflow-y: hidden;
}

.congestion-main-left {
  display: flex;
  flex-direction: column;
  width: 100%;
}

/* Congestion header part */
.congestion-header-container {
  display: flex;
  padding: 24px;
  padding-bottom: 0;
  align-items: center;
}

.congestion-page-header {
  font-size: 28px;
  color: #000000;
}

.congestion-date {
  font-size: 20px;
  color: #77797D;
}

.congestion-clock {
  font-size: 44px;
  color: #000000;
}

.congestion-qr-container {
  margin: 0 0 0 auto;
  display: flex;
  flex-direction: row-reverse;
  max-width: 500px;
  height: 100px;
}

.congestion-qr-image-container {
  height: 100px;
  min-width: 100px;
  border: 1px solid #E3E5E8;
  border-radius: 12px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.congestion-qr-image {
  height: 80px;
  width: 80px;
}

.congestion-qr-text-container {
  margin: 0 12px 0 0;
  display: flex;
  justify-content: center;
  flex-direction: column;
  text-align: end
}

.congestion-qr-text-title {
  font-size: 18px;
  line-height: 27px;
}

.qr-description-1 {
  font-size: 12px;
  line-height: 20px;
  color: #77797D;
}

.qr-description-2 {
  font-size: 12px;
  line-height: 20px;
}

/* Congestion main part */
.congestion-panels-container {
  width: 100%;
  height: 100%;
  padding: 24px;
}

.swiper {
  width: 100%;
  height: 100%;
  padding: 24px;
  max-height: 550px;
}

/* Swiper card */
.swiper-slide {
  border: 1px solid #e3e5e8;
  border-radius: 8px;
  width: 320px !important;
}

.swiper-slide2 {
  width: 100% !important;
  display: flex;
}

/* Swiper card - Image + Title */
.panel-top {
  position: relative;
  height: 60%;
  background: rgba(107, 114, 128, 1);
  width: 100%;
  border-radius: 8px 8px 0 0;
}

.panel-top2 {
  height: 100%;
  flex: 1;
  border-radius: 8px 0 0 8px;
}

.panel-image {
  height: 100%;
  width: 100%;
  border-radius: 8px 8px 0 0;
  object-fit: cover;
  position: relative;
}

.panel-image2 {
  background-size: cover !important;
  border-radius: 8px 0 0 8px;
}

.panel-image::after {
  content: "";
  background: rgba(0, 0, 0, 0.5);
  width: 100%;
  height: 100%;
  position: absolute;
  display: block;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  border-radius: 8px 8px 0 0;
}

.panel-image2::after {
  border-radius: 8px 0 0 8px;
}

.panel-top-main {
  position: absolute;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  top: 0;
  padding: 1rem;
  justify-content: center;
  align-items: center;
}

.panel-top-title {
  height: 90px;
  text-align: center;
  color: #fff;
  font-size: 1.875rem;
  line-height: 2.25rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 4;
  overflow: hidden;
}

.panel-top-title2 {
  font-size: 44px;
  line-height: normal;
  height: auto;
}

.panel-congestion-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  border-radius: 0.5rem;
  margin: 0 0 1rem 0;
  height: 53px;
}

.panel-congestion-container2 {
  height: 83px;
  width: 90%;
}

.panel-congestion-title {
  color: #fff;
  font-size: 18px;
  line-height: 1.75rem;
  padding: 0.75rem 0;
}

.panel-congestion-title2 {
  font-size: 30px;
  line-height: normal;
}

.panel-congestion-current {
  color: #fff;
  margin: 0 0 6px 0;
}

.panel-congestion-current2 {
  font-size: 28px;
}

.panel-congestion-updated-container {
  height: 40px; 
  display: flex; 
  justify-content: center; 
  flex-direction: column; 
  align-items: center;
}

.panel-congestion-updated-container2 {
  flex-direction: row;
  gap: 16px;
  font-size: 24px;
}

.panel-congestion-updated-color {
  color: #fff;
}

/* Swiper card - Description */
.panel-bottom {
  padding: 16px 12px;
  height: 40%;
}

.panel-bottom2 {
  height: 100%;
  flex: 1;
}

.congestion-panel-tags {
  display: flex;
  gap: 10px;
  padding: 0;
  flex-wrap: wrap;
}

.congestion-panel-tag {
  height: 28px;
  background: #ebedf2;
  border-radius: 2px;
  display: flex;
  align-items: center;
  padding: 0 6px;
  flex: none;
}

.congestion-panel-tag2 {
  height: auto;
  padding: 4px 12px;
  font-size: 24px;
}

.congestion-panel-tag.red {
  background: #ffdce5;
}

.congestion-panel-text {
  margin: 16px 0 0 0;
  color: #3d3f45;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 6;
  font-size: 15px;
  line-height: 22px;
  white-space: pre-wrap;
}

.congestion-panel-text2 {
  -webkit-line-clamp: 11;
  font-size: 28px;
  line-height: normal;
}

/* Congestion list right side */
.congestion-list-container {
  width: 37.5%;
  border-radius: 8px;
  padding: 12px;
}

.congestion-list-header {
  margin: 0 0 10px;
  font-size: 24px;
  font-weight: 400;
}

.congestion-item-left {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  flex-basis: 75%;
  margin-right: 10px;
}

.congestion-item-right {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  flex-basis: 25%;
  min-width: 200px;
}

.congestion-list-wrapper {
  height: 100%;
}

.congestion-list-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #e3e5e8;
  padding-top: 10px;
  padding-bottom: 10px;
}

.congestion-list-title {
  margin: 0;
  font-size: 16px;
  font-weight: 400;
}

.congestion-list-status {
  font-size: 16px;
  font-weight: 700;
  text-align: center;
}

.congestion-list-tags {
  margin-top: 6px;
  margin-bottom: 0;
  flex: none;
}

.congestion-list-time {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  font-weight: 400;
  color: #a1a4aa;
  text-align: end;
}

.promotion-image {
  height: 100%;
  width: 100%;
  object-fit: cover;
}

/* Congestion bus schedule */
.congestion-bus-container {
  background-color: #f6f7f9;
  flex: 1;
}

.congestion-bus-wrapper {
  padding: 12px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.congestion-bus-title {
  margin: 0;
  font-size: 20px;
  font-weight: 400;
}

.next-bus-wrapper {
  display: flex;
  width: 100%;
  justify-content: space-between;
  align-items: center;
  gap: 24px;
  height: 100%;
  margin-top: 8px;
}

.next-bus {
  background: #fff;
  flex: 1;
  padding: 6px 12px;
  border-radius: 12px;
}

.bottom-spacing-sm {
  padding-bottom: 6px;
}

.bus-text-small {
  font-size: 14px;
}
