@charset "utf-8";
/* talkappi admin site css version2 */

label {
  font-weight: 300;
  font-size: 12px;
}

.btn {
  font-size: 12px;
}

.btn.default.active {
  background-image: none;
  background-color: #245BD6;
  color: #fff;
}

.btn-2x {
  height: 40px;
  min-width: 108px;
}

.btn-2x.image {
  height: 40px;
  width: 40px;
}

.disabled.btn {
  color: #c8cace;
  background-color: #ebedf2;
  border: solid 1px #c8cace
}

.primary.btn {
  color: #fff;
  background-color: #245BD6;
}

.secondary.btn {
  color: #000;
  background-color: #fff;
  border: solid 1px #c8cace
}

.danger.btn {
  color: #e53361;
  background-color: #fff;
  border: solid 1px #e53361
}

.unselected {
  color: #000 !important;
  background-color: #e3e5e8 !important;
}

.bootstrap-select .caret:before {
  content: "\f107";
  display: inline-block;
  border: 0;
  font-family: "Font Awesome 5 Free";
  font-style: normal;
  font-weight: normal;
}

.form-body {
  /* 20210616 陳　線は不要のため、削除 */
  /* border-bottom: 1px solid #e3e5e8; */
  font-size: 12px;
  margin-bottom: 20px;
}

/*透明模様の定義*/
.bg-img-transparent {
  margin: 0;
  background-position: 0px 0px, 4px 4px;
  background-size: 8px 8px;
  background-image: -webkit-linear-gradient(45deg, #eee 25%, transparent 25%, transparent 75%, #ccc 75%, #ccc 100%), -webkit-linear-gradient(45deg, #ccc 25%, white 25%, white 75%, #ccc 75%, #eee 100%);
  background-image: linear-gradient(45deg, #ccc 25%, transparent 25%, transparent 75%, #ccc 75%, #ccc 100%), linear-gradient(45deg, #ccc 25%, white 25%, white 75%, #ccc 75%, #ccc 100%);
}

/* 20210616 陳 
管理画面レイアウト崩れるため、コメントアウト */
/* #page-wrapper {
	padding:20px;
} */
.autocomplete-off {
  cursor: text !important;
  background-color: #fff !important;
}

.page-content-header {
  display: flex;
  align-items: center;
  height: 60px;
  background: #f6f7f9;
  padding-left: 300px;
  border-bottom: 1px solid #e3e5e8;
  transition: all 0.7s ease 0s;
}

.page-content-disaster-header {
  padding-left: 286px;
  transition: all 0.7s ease 0s;
}

.portlet.header {
  display: block;
  background: #f6f7f9;
  padding: 15px 15px 0px 15px;
  border-bottom: 1px solid #e3e5e8;
  margin-bottom: 0;
}

.portlet.menu {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  margin-bottom: 15px;
}

.portlet-body {
  font-size: 12px;
}

/* 20210616 陳
管理画面レイアウト崩れるため、コメントアウト */
/* .page-content {
	padding: 0;
} */

.form-control {
  font-size: 12px;
}

.form-body-title {
  margin-left: 30px;
  font-size: 13px;
}

.form-actions {
  margin-top: 10px;
}

.normal-panel {
  width: 100%;
  max-width: 500px;
  padding: 5px;
  border-radius: 4px;
  border: solid 1px #ebedf2;
  background: #f6f7f9;
  position: relative;
}

.image-select {
  width: 100%;
  max-width: 500px;
  height: 28px;
  padding: 0 4px;
  border-radius: 4px;
  border: solid 1px #e3e5e8;
  background: #fff;
  display: flex;
  align-items: center;
}

.image-select .add-icon {
  width: 20px;
  height: 20px;
  padding: 4px;
  border-radius: 4px;
  background: #e3e5e8;
  margin: 0 0 0 auto;
}

.add-icon {
  width: 20px;
  height: 20px;
  margin: 0 12px 0 0;
  padding: 4px;
  border-radius: 4px;
  background: #e3e5e8;
}

.result-title {
  font-family: HiraginoSans-W4;
  font-size: 14px;
}

.kind-label {
  color: #000;
  background-color: #d3eeff;
  margin-left: 10px;
  font-size: 11px;
  padding-top: 5px;
}

.required-label {
  color: #000;
  background-color: #cff2d7;
  margin-left: 10px;
  font-size: 11px;
  padding-top: 5px;
}

.tag-group-btn-delete {
  width: 14px;
  height: 14px;
  margin-left: 4px;
  background-image: url(/assets/admin/css/img/icon-cancel-small.svg);
  cursor: pointer;
}

button.btn.dl-btn {
  color: #000;
  background-color: #fff;
  border: solid 1px #e3e5e8;
  margin: 12px 0.5em !important;
}

.dl-btn i {
  color: #245BD6;
}

.graph-btn {
  height: 28px;
  color: #000;
  border-radius: 14px;
  background-color: #e3e5e8;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 12px !important;
  margin-bottom: 12px !important;
  user-select: none;
  padding: 0px 16px;
}

/* google chart pie flickering bug fix */
svg>g>g:last-child {
  pointer-events: none
}

.graph-btn-selected {
  background-color: #245BD6 !important;
  color: #fff !important;
}

.graph-btn-wrapper {
  display: flex;
  margin-top: 32px;
  margin-bottom: 12px;
  flex-wrap: wrap;
}

.chart-custom-btn-margin {
  margin-top: 14px !important;
  margin-bottom: 14px !important;
}

.radio-hide {
  display: none !important;
}

.chart-hide {
  position: absolute;
  left: -9999px;
}

.q-separator {
  border-bottom: 1px solid #e3e5e8;
  margin-bottom: 32px;
}

.pagination-container {
  margin-top: 10px !important;
  margin-bottom: 20px;
}


.pagination {
  display: flex;
}

.fullwidth-textarea {
  width: 100%;
  min-height: 79px;
  padding: 8px 12px;
  border-radius: 4px;
  border: solid 1px #e3e5e8;
  background: #fff;
}

.page-num {
  height: 20px;
  text-align: center;
  display: flex;
  align-items: center;
  color: black !important;
  border: none !important;
  padding: 0 13px;
  font-size: 12px;
  user-select: none;
}

a.page-num:hover {
  background-color: white !important;
}

a:focus, a:active {
  outline: none !important;

}

a:focus {
  outline: none;
}

.next-prev-btn {
  width: 20px !important;
  height: 20px !important;
  border-radius: 4px !important;
  background-color: #e3e5e8 !important;
  cursor: pointer !important;
  padding: 0 !important;
}

.next-btn {
  background-image: url(/assets/admin/css/img/icon-next-page.svg);
  background-repeat: no-repeat;
  background-position: center;
}

.next-btn.disabled {
  background-image: url(/assets/admin/css/img/icon-next-page-disabled.svg);
}

.prev-btn {
  background-image: url(/assets/admin/css/img/icon-previous-page.svg);
  background-repeat: no-repeat;
  background-position: center;
}

.prev-btn.disabled {
  background-image: url(/assets/admin/css/img/icon-previous-page-disabled.svg);
}

.icon-more-info-on {
  width: 20px !important;
  height: 20px !important;
  background-image: url(/assets/admin/css/img/icon-more-info-on.svg);
}

.lang-preview-btn {
  width: 84px;
  height: 28px;
  margin: 16px 14px 12px 11px;
  padding: 0;
  color: #000;
  border-radius: 4px;
  border: solid 1px #c8cace;
  background-color: #fff;
}

.qr-code-btn {
  padding: 0;
  height: 28px;
  padding-right: 14px;
  padding-left: 10px;
  color: grey !important;
  margin-right: 12px !important;
}

.qr-code-btn.action {
  color: #333333;
}

.grey.btn.qr-code-btn:active, .grey.btn.qr-code-btn:hover {
  background-color: #E5E5E5;
}



.jscode {
  margin-right: 21px;
}

.jscode-container {
  padding: 12px;
  border-radius: 4px;
  border: solid 1px #ebedf2;
  background-color: #f6f7f9;
  max-width: 510px;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

/* PC用CSS */

/* アンケート一覧のプルダウン */
.surveys-pulldown {
  height: 28px;
}

.select2-choice {
  height: 28px !important;
  padding: 0 0 0 8px !important;
}

/* アンケート一覧のボタン(検索など) */
.surveys-btn {
  height: 28px;
  padding: 0 14px;
  display: flex;
  align-items: center;
}

/* アンケート一覧の分類タグ */
.category-tag {
  height: 20px;
  line-height: 20px;
  padding: 0 7px;
  margin: 0 12px 10px 0;
  border-radius: 0 !important;
  color: #000;
  background: #cff2d7;
}

/* アンケート一覧の詳細ボックス */
.surveys-details-area {
  display: flex;
  flex-wrap: wrap;
}

/* アンケート一覧の詳細アイコン 大枠(aタグ) */
.surveys-details-icon {
  /* width: fit-content;
  margin: 5px 0 14px 0;
  display: block; */
}

/* アンケート一覧の詳細アイコン 中身(spanタグ)*/
.surveys-details-icon-contents {
  /* padding: 6px 11px;
  margin: 10px 12px 0 0;
  border-radius: 12px;
  color: #000;
  background: #e3e5e8; */
}

/* アンケート一覧　SVGアイコンの右側スペース*/
.surveys-details-icon-img {
  margin: 0 6px 0 0;
  vertical-align: text-bottom;
}

/* アンケート一覧のカレンダー */
/* 一番外側のコンテナ */
.surveys-calender-container {
  width: fit-content;
  width: -moz-fit-content;
  height: 28px;
  padding: 0 0 0 10px !important;
  margin: 0 10px;
  border-radius: 4px;
  border: solid 1px #e3e5e8;
  background: #fff;
  display: flex;
  align-items: center;
}

/* 入力部分 */
.surveys-calender-input-date {
  width: 100px !important;
  height: 26px;
  padding: 0 12px;
  border: none;
}

.bs-select.form-control.surveys-pulldown button {
  height: 28px;
}

/* 990px以下に適用されるCSS（タブレット用） */
/* 990px => Bootstrapに合わせた */
@media screen and (max-width: 990px) {

  /* アンケート一覧のカレンダー */
  /* 一番外側のコンテナ */
  .surveys-calender-container {
    width: calc(100% - 20px);
    /* margin分を引く */
  }

  /* 入力部分 */
  .surveys-calender-input-date {
    width: 100% !important;
  }
}

/* 共通部品(横田作成 8/10~ */
/* 一般 */
.left-aligned {
  margin: 0 auto 0 0 !important;
}

.right-aligned {
  margin: 0 0 0 auto !important;
}

/* position */
.relative {
  position: relative;
}

/* display系 */
.display-none {
  display: none !important;
}

.flexbox {
  display: -webkit-flex;
  display: flex;
}

.flexbox-inline {
  display: -webkit-inline-flex;
  display: inline-flex;
}

/* flex, 縦横中央 */
.flexbox-center {
  display: -webkit-flex;
  display: flex;
  -webkit-align-items: center;
  /* 縦方向中央揃え（Safari用） */
  align-items: center;
  /* 縦方向中央揃え */
  -webkit-justify-content: center;
  /* 横方向中央揃え（Safari用） */
  justify-content: center;
  /* 横方向中央揃え */
}

/* flex, 縦中央 */
.flexbox-x-axis {
  display: -webkit-flex;
  display: flex;
  -webkit-align-items: center;
  /* 縦方向中央揃え（Safari用） */
  align-items: center;
  /* 縦方向中央揃え */
}

.flexbox-inline-x-axis {
  display: -webkit-inline-flex;
  display: inline-flex;
  -webkit-align-items: center;
  /* 縦方向中央揃え（Safari用） */
  align-items: center;
  /* 縦方向中央揃え */
}

/* flex, 横中央 */
.flexbox-y-axis {
  display: -webkit-flex;
  display: flex;
  -webkit-justify-content: center;
  /* 横方向中央揃え（Safari用） */
  justify-content: center;
  /* 横方向中央揃え */
}

.flexbox-baselines {
  align-items: baseline;
  -webkit-align-items: baseline;
}

.hor-center {
  margin-left: auto !important;
  margin-right: auto !important;
  text-align: center;
}

/* icon-round-corners-small flexbox-x-axis icon-background-lightish-blue */
/* font関係 */
/* font(基礎) */
.font-standard {
  font-size: 12px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  color: #000;
  -webkit-font-smoothing: auto;
}

/* .font-size */
.font-size-v2 {
  font-size: 13px;
}

.font-size-v3 {
  font-size: 14px;
}

.font-size-v4 {
  font-size: 11px !important;
}

.font-size-v5 {
  font-size: 16px;
}

.font-size-6 {
  font-size: 10px;
}

/* font-family */
.font-family-v1 {
  font-family: HiraginoSans-W3;
}

.font-family-v2 {
  font-family: KozGoPr6N;
}

.font-family-v3 {
  font-family: HiraginoSans-W5;
}

.font-family-v4 {
  font-family: HiraginoSans-W4;
}

.font-family-v5 {
  font-family: PingFangSC;
}

/* color */
.font-color-v1 {
  color: #245BD6;
}

.font-color-v2 {
  /* charcoal-grey */
  color: #3d3f45;
}

.font-color-v3 {
  /* bluey-grey */
  color: #a1a4aa;
}

.font-color-v4 {
  color: #fff !important;
}

.font-color-v5 {
  color: #000 !important;
}

.font-color-v6 {
  color: #e53361;
}

/* weight */
.font-weight-v1 {
  font-weight: 500;
}

/* ポインター */
.pointer {
  cursor: pointer;
}

/* ライン系 */
.line-none {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

.line-none:focus {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

.border-none {
  border: none !important;
}

/* ボタン系*/
/* ボタン　コンテナ　横並び */
.submit-btn-container {
  display: -webkit-flex;
  display: flex;
  -webkit-align-items: center;
  align-items: center;
}

.btn-flexible-width {
  padding: 0 20px;
  height: 40px;
  margin: 0 14px 0 0;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  white-space: nowrap;
}

/* ボタン(小) SVG部分 */
.btn-smaller .icon-add-white-svg {
  margin: 0 6px 0 0;
}

/* ボタン(小、角丸)　SVG有 */
.btn-smaller-round {
  width: fit-content;
  width: -moz-fit-content;
  min-height: 24px;
  max-width: 320px;
  padding: 0 12px;
  border-radius: 12px;
  display: -webkit-flex;
  display: flex;
  -webkit-align-items: center;
  /* 縦方向中央揃え（Safari用） */
  align-items: center;
  /* 縦方向中央揃え */
  cursor: pointer;
  word-break: break-all;
}

/* ボタン(青) OKボタン*/
.btn-blueーconfirm {
  background: #245BD6;
  color: #fff;
  border: solid 1px #245BD6;
  height: 28px;
  width: 43px;
  border-radius: 4px;
  padding: 5px, 12px, 5px, 12px;
  display: -webkit-flex;
  display: flex;
  -webkit-align-items: center;
  /* 縦方向中央揃え（Safari用） */
  align-items: center;
  /* 縦方向中央揃え */
  cursor: pointer;
  justify-content: center;
}

/* ボタン(青) */
.btn-blue {
  background: #245BD6;
  color: #fff;
  border: solid 1px #245BD6;
}

/* ボタン(水色、薄青) */
.btn-lightblue {
  background: #d3eeff;
  color: #000;
  border: solid 1px #d3eeff;
}

/* ボタン(背景：グレー　文字色：黒) */
.btn-gray-black {
  background: #ebedf2;
  color: #000;
  border: solid 1px #c8cace;
}

/* ボタン(背景：グレー　文字色：白) */
.btn-gray-white {
  background: #c8cace;
  color: #fff;
  border: solid 1px #c8cace;
}

/* ボタン(背景：グレー　文字色：白) => アクティブ(青) */
.btn-gray-white.active {
  background: #245BD6;
  color: #fff;
  border: solid 1px #245BD6;
}

/* ボタン(白) */
.btn-white {
  background: #fff;
  color: #000 !important;
  border: solid 1px #c8cace;
}

/* ボタン(赤) */
.btn-red {
  background: #e53361;
  color: #fff;
  border: solid 1px #e53361;
}

.btn-light-green {
  border: solid 1px #32ce55;
  background-color: #cff2d7;
}

.btn-light-blue {
  border: solid 1px #245BD6;
  background-color: #d3eeff;
}

.btn-light-yellow {
  border: solid 1px #ffcc16;
  background-color: #fff0bb;
}

/* ボタン(削除アイコン) */
.btn-delete-icon {
  width: 40px;
  height: 40px;
  border-radius: 4px;
  border: solid 1px #e53361;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

/* ボタン(削除アイコン)(小) */
.btn-delete-icon-small {
  width: 32px;
  height: 32px;
  border-radius: 4px;
  border: solid 1px #e53361;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

/* ボタン(小) 四角 */
.btn-smaller-square {
  height: 28px !important;
  padding-right: 0 !important;
  padding: 8px 12px !important;
  border-radius: 4px;
  background: #ebedf2;
}

.btn-smaller-square a {
  color: #3d3f45 !important;
}

.btn-smaller-square.active {
  border-bottom: none !important;
  background: #245BD6;
}

.btn-smaller-square.active a {
  color: #fff !important;
}

/* ボタン(極小) 四角 */
.btn-mini-square {
  height: 20px !important;
  margin: 0;
  padding: 0 7px !important;
  border-radius: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* モーダルウィンドウ(小) */
/* 削除など */
.modal-smaller-container {
  width: 521px;
  height: 245px;
  padding: 28px 28px 0 37px;
  border-radius: 4px;
  border: solid 1px #e3e5e8;
  background: #fff;
  z-index: 999;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.modal-small-title-container {
  width: 100%;
  display: flex;
  align-items: flex-end;
}

.modal-small-title {
  font-size: 15px;
  font-family: HiraginoSans-W5;
  margin: 0 auto 0 0 !important;
}

/* モーダルウィンドウ（画像用）BEGIN */
/* 動画のCSSを共通化数場合はfileに変更 */
/* 例）modal-file-upload-container */
.modal-image-container {
  width: 664px;
  height: 342px;
  min-height: 342px;
  padding: 28px 28px 0 37px;
  border-radius: 4px;
  border: solid 1px #e3e5e8;
  background: #fff;
  z-index: 999;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.modal-image-preview {}

.modal-image-input-type {
  width: fit-content;
  width: -moz-fit-content;
  position: relative;
  cursor: pointer;
}

.modal-image-input-type::after {
  content: "";
  display: block;
  width: 100%;
  height: 2px;
  background: #e3e5e8;
  position: absolute;
  bottom: -10px;
}

.modal-image-current-type {
  position: relative;
}

.modal-image-current-type::after {
  content: "";
  display: block;
  width: 100%;
  height: 2px;
  background: #245BD6;
  position: absolute;
  bottom: -10px;
  z-index: 999;
}

.image-input-url {
  width: 100%;
  height: 28px;
  margin: 40px 0 0 0;
  padding: 0 12px;
  border: 1px solid #E3E5E8;
  border-radius: 4px;
}

.modal-image-button {
  position: absolute;
  bottom: 32px;
  display: -webkit-flex;
  display: flex;
  -webkit-align-items: center;
  align-items: center;
}

/* モーダルウィンドウ（画像用）END */
.show-image-display {
  position: absolute;
  bottom: 0;
  background-color: #3d3f45;
  width: 144px;
  height: 24px;
  opacity: 0.6;
  color: #fff;
  z-index: inherit;
  cursor: pointer;
}

.show-image-display div:first-of-type {
  border-right: 1px #fff solid;
  padding: 4px 12px;
}

.show-image-display div:last-of-type {
  padding: 4px 11px;
}

.image-disabled-cover {
  position: absolute;
  height: 46px;
  width: 144px;
  top: 0;
  z-index: inherit;
}

/* モーダル用背景(グレイアウト) */
.modal-background {
  height: 100vh;
  width: 100vw;
  background: rgba(61, 63, 69, 0.72);
  z-index: 99;
  position: fixed;
  top: 0;
  left: 0;
}

/* プルダウン 開始*/
.dropdown-container {
  position: relative;
  height: 28px;
  padding: 0 12px;
  border-radius: 4px;
  background: #fff;
  border: solid 1px #e3e5e8;
  display: flex;
  align-items: center;
  cursor: pointer;
}

/* 長めのinput text */
.text-input-longer {
  width: 100%;
  max-width: 500px;
  height: 28px;
  padding: 8px 12px;
  border-radius: 4px;
  border: solid 1px #e3e5e8;
  font-size: 12px;
  background: #fff;
  resize: none;
}

/* 長さ指定なし */
.text-input {
  height: 28px;
  padding: 8px 12px;
  border-radius: 4px;
  border: solid 1px #e3e5e8;
  font-size: 12px;
  background: #fff;
}

.text-input-mini {
  width: 6em;
  border: none;
}

/* プルダウンの長さ指定 */
.dropdown-longer {
  min-width: 200px;
  margin: 0 16px 0 0;
}

.dropdown-shorter {
  min-width: 121px
}

.dropdown-middle {
  min-width: 180px;
  width: 180px;
}

.dropdown-mini {
  min-width: 90px;
}

.dropdown-selected {
  width: 100%;
  display: flex;
  align-items: center;
}

/* 選択した文字 */
.dropdown-selected-text {
  margin: 0 auto 0 0;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  font-size: 12px;
}

.dropdown-options {
  margin: 0;
  border: #e3e5e8;
  box-shadow: 1px 2px 8px 0 rgb(61 63 69 / 25%);
  background: #fff;
  position: absolute;
  top: 27px;
  left: -2px;
  display: none;
  z-index: 1000;
}

/* 選択肢 */
.dropdown-option {
  min-height: 36px;
  padding: 0 12px;
  display: flex;
  align-items: center;
  font-size: 12px;
  /* overflow: hidden; */
  /* white-space: nowrap; */
  position: relative;
  flex-wrap: wrap;
  border-top: 1px solid #b5b5b5;
  border-left: 1px solid #b5b5b5;
}

.dropdown-option.current-type {
  background: rgba(211, 238, 255, 0.6);
}

.dropdown-option:hover {
  background-color: rgba(61, 63, 69, 0.08);
}

/* プルダウン　終了 */
/* アイコン　開始 */
/* アイコン(小, 角丸) */
.icon-round-corners-small {
  height: 24px;
  padding: 4px 11px !important;
  border-radius: 12px !important;
  color: #000;
  width: fit-content;
  width: -moz-fit-content;
  cursor: pointer;
}

/* アイコン(中, 角丸) */
.icon-round-corners-middle {
  height: 28px;
  padding: 0 16px !important;

  margin: 0 10px 0 0 !important;
  border-radius: 14px;
  color: #fff;
  cursor: pointer;
  display: flex;
  align-items: center;
  font-size: 12px;
}

.icon-round-corners-middle.active {
  color: #fff !important;
  background: #245BD6;
}

.icon-round-corners-middle.unselected {
  background-color: #e3e5e8 !important;
}

/*　ボタンタグ　*/
.button-round-corners {
  height: 24px;
  border-radius: 12px;
  color: #000000;
  font-size: 12px;
  background: #EBEDF2;
}

.button-round-corners-item {
  padding: 3px 12px;
  height: 24px;
  border-radius: 12px;
}

.button-round-corners-item.active {
  background: #245BD6;
  color: #FFFFFF;
}

.icon-background-pale-blue {
  background: #e3e5e8 !important;
}

.icon-round-corners-small.active {
  background: #cff2d7 !important;
  color: #333333 !important;
}

.icon-background-light-blue {
  background: #d3eeff;
}

.icon-background-light-pink {
  background: #eee3ec;
}

.icon-background-lightish-blue {
  background: #245BD6;
}

.background-pale-gray {
  background: #f6f7f9;
}

.background-white {
  background: #fff;
}

/*  */
.add-image-container {
  width: 144px;
  height: 72px;
  margin: 0 12px;
  border: solid 1px #e3e5e8;
  background-color: #f6f7f9;
}

/* アイコン　終了 */
/* チェックボックス */
.checkbox-small-v1-container {
  width: 90%;
  height: 32px;
  margin: 0 10px 0 auto;
  border-bottom: 1px solid #e3e5e8;
  display: flex;
  align-items: center;
}

.checkbox-small-v1-label-off {
  width: 24px;
  height: 6px;
  margin: 3px 0 3px 13px;
  border-radius: 8px;
  background: #c8cace;

  position: relative;
  cursor: pointer;
}

.checkbox-small-v1-span-off {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: solid 1px #c8cace;
  background: #f6f7f9;

  position: absolute;
  top: -3px;
  cursor: pointer;
}

.checkbox-small-v1-label-on {
  width: 24px;
  height: 6px;
  margin: 3px 0 3px 13px;
  border-radius: 8px;
  background: #d3eeff;

  position: relative;
  cursor: pointer;
}

.checkbox-small-v1-span-on {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #245BD6;
  border: 1px solid #245BD6;
  position: absolute;
  top: -3px;
  right: 0;
  cursor: pointer;
  margin-right: 0;
}

/* コード 自動生成*/
.code-readonly {
  width: 200px;
  height: 28px;
  padding: 0 12px;
  border-radius: 4px;
  border: solid 1px #e3e5e8;
  background: #f6f7f9;
  display: flex;
  align-items: center;
}

.settings-container {
  padding: 32px 24px 16px 24px;
  border: solid 1px #e3e5e8;
}

.settings-container .setting-container {
  padding: 0 0 24px 0;
}

.icon-form-description {
  width: 14px;
  height: 14px;
  background-image: url(/assets/admin/css/img/icon-form-description.svg);
  background-repeat: no-repeat;
  cursor: pointer;
}

.icon-form-description.active {
  background-image: url(/assets/admin/css/img/icon-form-description-active.svg);
}

.icon-download {
  margin: 0;
  padding: 0;
  margin-right: 6px;
}

.icon-form-description-container {
  display: none;
  margin: 16px 0 0 0;
}

.icon-form-description-container:focus {
  box-shadow: none;
}

.form-description-input {
  height: 32px;
  max-width: 100% !important;
  background: #f6f7f9;
  border: none;
  /* margin: 16px 0 0 0; */
  padding: 0 12px;
}

.survey-survey-questions-container .survey-editing-container h1,
.survey-survey-questions-container .survey-editing-container h2,
.survey-survey-questions-container .survey-editing-container h3,
.survey-survey-questions-container .survey-editing-container h4,
.survey-survey-questions-container .survey-editing-container h5,
.survey-survey-questions-container .survey-editing-container h6,
h1, p {
  margin: 0;
}

.pulldown-list-children2 {
  position: absolute;
  left: 180px;
  min-width: 120px;
  top: 0px;
  background: rgb(255, 255, 255);
  box-shadow: rgb(61 63 69 / 25%) 1px 2px 8px 0px;
}

.sentaku:active {
  pointer-events: active;
}

.input-text {
  box-shadow: inset 0 -1px 0 0 #e3e5e8 !important;
}

/* チェックボックス */
.icon-check {
  margin-right: 4px;
  min-width: 12px;
  height: 12px;
  border-radius: 2px;
  border: solid 1px #c8cace;
  background: #fff;
  display: inline-block;
}

.icon-check::before {
  content: none
}

.icon-check.active {
  display: inline-block;
  background-image: url(/assets/admin/css/img/icon-check.svg);
  background-repeat: no-repeat;
  border: solid 0px;
}

/* バツ印（大） */
.icon-cancel-large {
  width: 24px;
  height: 24px;
  margin: 0 0 0 auto;
  background-image: url(/assets/admin/css/img/icon-cancel-large.svg);
  background-repeat: no-repeat;
}

/* 削除（ゴミ箱） */
.icon-delete {
  width: 12px;
  height: 12px;
  background-image: url(/assets/admin/css/img/icon-delete.svg);
  background-repeat: no-repeat;
}

/* カレンダー */
.icon-calender {
  width: 12px;
  height: 12px;
  padding: 0 0 0 13px;
  background-image: url(/assets/admin/css/img/icon-calender.svg);
  background-repeat: no-repeat;
}

/* カレンダー */
.icon-calender-gray {
  width: 20px;
  height: 20px;
  background-image: url(/assets/admin/css/img/calendar.svg);
  background-repeat: no-repeat;
}

/* 拡大　開く */
.icon-form-zoom-in {
  width: 12px;
  height: 12px;
  background-image: url(/assets/admin/css/img/icon-form-zoom-in.svg);
  background-repeat: no-repeat;
}

/* 拡大　開く */
.icon-import {
  width: 12px;
  height: 12px;
  background-image: url(/assets/admin/css/img/import.svg);
  background-repeat: no-repeat;
}

.icon-import-white {
  width: 12px;
  height: 12px;
  background-image: url(/assets/admin/css/img/import-white.svg);
  background-repeat: no-repeat;
}

/* > マーク */
.pulldown-more {
  width: 14px;
  height: 14px;
  margin: 0 0 0 auto;
  background-image: url(/assets/admin/css/img/icon-breadcrumb.svg);
  background-repeat: no-repeat;
}

/* ドロップダウン like check mark */
.icon-drop-down-close {
  width: 14px;
  height: 14px;
  background-image: url(/assets/admin/css/img/icon-drop-down-close.svg);
  background-repeat: no-repeat;
}

/* ドロップダウン like check mark */
.icon-drop-down-open {
  width: 14px;
  height: 14px;
  background-image: url(/assets/admin/css/img/icon-drop-down-open.svg);
  background-repeat: no-repeat;
}

/* ドラッグ可能アイコン */
.icon-drag {
  width: 20px;
  height: 20px;
  background-image: url(/assets/admin/css/img/icon-drag.svg);
  background-repeat: no-repeat;
}

/* × */
.icon-cancel-small {
  width: 14px;
  height: 14px;
  background-image: url(/assets/admin/css/img/icon-cancel-small.svg);
  background-repeat: no-repeat;
}

/* ◯ */
.icon-form-single-option-off {
  width: 16px;
  height: 16px;
  background-image: url(/assets/admin/css/img/icon-form-single-option-off2.svg);
  background-repeat: no-repeat;
}

/* ◯ */
.icon-form-single-option-off.active {
  background-image: url(/assets/admin/css/img/icon-check-single2.svg);
}

/* + マーク */
.icon-edit-bar-add-q {
  width: 14px;
  height: 14px;
  background-image: url(/assets/admin/css/img/icon-edit-bar-add-q.svg);
  background-repeat: no-repeat;
}

.icon-preview-mail {
  width: 14px;
  height: 14px;
  background-image: url(/assets/admin/css/img/icon-preview-mail.svg);
  background-repeat: no-repeat;
}

.icon-preview-mail-sent {
  width: 14px;
  height: 14px;
  background-image: url(/assets/admin/css/img/icon-preview-mail-sent.svg);
  background-repeat: no-repeat;
}

.icon-add {
  width: 12px;
  height: 12px;
  background-image: url(/assets/admin/css/img/icon-add.svg);
  background-repeat: no-repeat;
}

.icon-filter {
  width: 12px;
  height: 12px;
  background-image: url(/assets/admin/css/img/icon-filter.svg);
  background-repeat: no-repeat;
}

.icon-add-white {
  width: 12px;
  height: 12px;
  background-image: url(/assets/admin/css/img/icon-add-white.svg);
  background-repeat: no-repeat;
}

.icon-minus-white {
  width: 12px;
  height: 12px;
  background-image: url(/assets/admin/css/img/icon-minus-white.svg);
  background-repeat: no-repeat;
}

/* i マーク */
.icon-detail {
  width: 15px;
  height: 15px;
  background-image: url(/assets/admin/css/img/icon-detail.svg);
  background-repeat: no-repeat;
  cursor: pointer;
}

.icon-detail-box {
  display: none;
  width: 320px;
  border: solid 1px #ebedf2;
  background: #e3ffe1dd;
  font-size: 12px;
  position: absolute;
  top: 20px;
  left: 60px;
  padding: 12px;
  border-radius: 4px;
  z-index: 99;
  text-align: left;
}

/* よく使う項目 */
.icon-form-fleq {
  width: 20px !important;
  height: 20px;
  background-image: url(/assets/admin/css/img/icon-form-fleq.svg);
  background-repeat: no-repeat;
}

/* 選択 */
.icon-form-options {
  width: 20px !important;
  height: 20px;
  background-image: url(/assets/admin/css/img/icon-form-options.svg);
  background-repeat: no-repeat;
}

/* 単一選択 */
.icon-form-single-option-on {
  width: 20px !important;
  height: 20px;
  background-image: url(/assets/admin/css/img/icon-form-single-option-on.svg);
  background-repeat: no-repeat;
}

/* 単一選択(大) */
.icon-form-single-option-on-large {
  width: 20px !important;
  height: 20px;
  background-image: url(/assets/admin/css/img/icon-form-single-option-on-large.svg);
  background-repeat: no-repeat;
}

/* 複数選択 */
.icon-form-multi-option-on {
  width: 20px !important;
  height: 20px;
  background-image: url(/assets/admin/css/img/icon-form-multi-option-on.svg);
  background-repeat: no-repeat;
}

/* プルダウン */
.icon-form-pulldown-option-on {
  width: 20px !important;
  height: 20px;
  background-image: url(/assets/admin/css/img/icon-form-pulldown-option-on.svg);
  background-repeat: no-repeat;
}

/* 長文 */
.icon-form-writing-text {
  width: 20px !important;
  height: 20px;
  background-image: url(/assets/admin/css/img/icon-form-writing-text.svg);
  background-repeat: no-repeat;
}

/* 短文 */
.icon-form-writing-short-text {
  width: 20px !important;
  height: 20px;
  background-image: url(/assets/admin/css/img/icon-form-writing-short-text.svg);
  background-repeat: no-repeat;
}

/* アクション追加 */
.icon-add-action {
  width: 12px !important;
  height: 12px;
  background-image: url(/assets/admin/css/img/action-unactive.svg);
  background-repeat: no-repeat;
}

.icon-add-action.active {
  width: 12px !important;
  height: 12px;
  background-image: url(/assets/admin/css/img/action-active.svg);
  background-repeat: no-repeat;
}

/* カレンダー */
.icon-form-calendar {
  width: 20px !important;
  height: 20px;
  background-image: url(/assets/admin/css/img/icon-form-calendar.svg);
  background-repeat: no-repeat;
}

/* ファイルアップロード */
.icon-form-upload {
  width: 20px !important;
  height: 20px;
  background-image: url(/assets/admin/css/img/icon-form-upload.svg);
  background-repeat: no-repeat;
}

/* フリースペース */
.icon-form-fq {
  width: 20px !important;
  height: 20px;
  background-image: url(/assets/admin/css/img/icon-form-fq.svg);
  background-repeat: no-repeat;
}

/* マトリクス */
.icon-form-matrix-option-on {
  width: 20px !important;
  height: 20px;
  background-image: url(/assets/admin/css/img/icon-form-matrix-option-on.svg);
  background-repeat: no-repeat;
}

/* マトリクス */
.icon-form-scr {
  width: 20px !important;
  height: 20px;
  background-image: url(/assets/admin/css/img/icon-form-scr.svg);
  background-repeat: no-repeat;
}

/* 分岐アイコン（非アクティブ） */
.icon-jump-unactive {
  width: 12px !important;
  height: 12px;
  background-image: url(/assets/admin/css/img/icon-jump-unactive.svg);
  background-repeat: no-repeat;
}

/* 分岐アイコン（アクティブ） */
.icon-jump-active {
  width: 12px !important;
  height: 12px;
  background-image: url(/assets/admin/css/img/icon-jump-active.svg);
  background-repeat: no-repeat;
}

/* 検索アイコン */
.icon-search {
  width: 12px !important;
  height: 12px;
  background-image: url(/assets/admin/css/img/Search.svg);
  background-repeat: no-repeat;
}

/* 改ページアイコン（非アクティブ） */
.icon-form-page-unactive {
  width: 12px !important;
  height: 12px;
  background-image: url(/assets/admin/css/img/icon-form-page-unactive.svg);
  background-repeat: no-repeat;
}

/* 改ページアイコン（アクティブ） */
.icon-form-page-active {
  width: 12px !important;
  height: 12px;
  background-image: url(/assets/admin/css/img/icon-form-page-active.svg);
  background-repeat: no-repeat;
}

/*  */
.icon-output {
  width: 12px !important;
  height: 12px;
  background-image: url(/assets/admin/css/img/icon-output.svg);
  background-repeat: no-repeat;
}

/* 折り畳みアイコン */
.icon-fold-open {
  width: 12px !important;
  height: 12px;
  display: block;
  background-image: url(/assets/admin/css/img/icon-fold-open.svg);
  background-repeat: no-repeat;
}

.icon-fold-open.active {
  background-image: url(/assets/admin/css/img/icon-fold-close.svg);
}

.icon-error {
  width: 16px !important;
  height: 16px;
  display: block;
  background-image: url(/assets/admin/css/img/icon-error.svg);
  background-repeat: no-repeat;
}

.icon-success {
  width: 16px !important;
  height: 16px;
  display: block;
  background-image: url(/assets/admin/css/img/icon-success.svg);
  background-repeat: no-repeat;
}

.icon-loading {
  width: 16px !important;
  height: 16px;
  display: block;
  background-image: url(/assets/admin/css/img/icon-loading.svg);
  background-repeat: no-repeat;
}

/* セクションの折り畳みアイコン */
.icon-fold-section-open {
  width: 12px !important;
  height: 12px;
  display: block;
  background-image: url(/assets/admin/css/img/icon-fold-section-close.svg);
  background-repeat: no-repeat;
}

.icon-fold-section-open.active {
  background-image: url(/assets/admin/css/img/icon-fold-section-open.svg);
}

/* 写真アイコン */
.icon-form-photo {
  width: 12px !important;
  height: 12px;
  display: block;
  background-image: url(/assets/admin/css/img/icon-form-photo-unactive.svg);
  background-repeat: no-repeat;
}

.icon-form-photo.active {
  background-image: url(/assets/admin/css/img/icon-form-photo.svg);
}

.icon-return {
  width: 12px;
  height: 12px;
  background-image: url(/assets/admin/css/img/icon-return.svg);
  background-repeat: no-repeat;
}

.icon-link-2 {
  width: 12px;
  height: 12px;
  background-image: url(/assets/admin/css/img/icon-link.svg);
  background-repeat: no-repeat;
}

.input-des-section {
  border-radius: 4px;
  border: solid 1px #e3e5e8 !important;
  min-height: 77px !important;
  margin: 0 !important;
  padding: 6px 12px !important;
}

/* placeholder */
.edit-container ::placeholder {
  font-family: HiraginoSans-W3;
  font-size: 12px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  color: #a1a4aa;
}

/* 旧Edge対応 */
.edit-container ::-ms-input-placeholder {
  font-family: HiraginoSans-W3;
  font-size: 12px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  color: #a1a4aa;
}

/* IE対応 */
.edit-container :-ms-input-placeholder {
  font-family: HiraginoSans-W3;
  font-size: 12px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  color: #a1a4aa;
}

/* 言語選択 各言語 */
.select-lang li {
  height: 25px;
  padding: 0 6px;
  font-size: 12px;
  font-weight: 500;
  border-bottom: 1px solid #e3e5e8;
  cursor: pointer;
}

/* 言語選択　現在の言語 */
.select-lang li.current-lang {
  border-bottom: 2px solid #245BD6;
}

.select-lang li.current-lang a {
  color: #245BD6;
}

/* フォーカス青線 */
.focus-blue-line {
  border: 1px solid #245BD6 !important;
  /* z-index: 10; */
}

/* 分岐条件　OR タイトル */
.branch-or-title {
  width: 48px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #ebedf2;
  border: 1px solid #e3e5e8;
  border-radius: 4px 0 0 4px;
}

.branch-or-title-input {
  border-left: none;
  border-radius: 0 4px 4px 0;
}

/* 日付詳細設定　含む、除く */
.checkbox-v2-container {
  height: 24px;
  border-radius: 12px;
  background: #e3e5e8;
  height: 28px;
  border-radius: 14px;
}

.checkbox-v2-options {
  height: 24px;
  border-radius: 12px;
  background: #245BD6;
  color: #fff;
  height: 28px;
  border-radius: 14px;
}

/* 日時詳細設定 */
.modal-button-container {
  position: fixed;
  bottom: 12px;
  z-index: 9999;
}

/* 日付詳細設定　OK後表示 */
.added-date-detailed-container {
  min-height: 32px;
  border-radius: 4px;
  padding: 0 16px;
  background: #f6f7f9;
}

/* プレビュー画面(スマホ） */
.preview-sm-header {
  width: 320px;
  height: 44px;
  padding: 0 12px;
  border-radius: 10px 10px 0 0;
  border: solid 1px #e3e5e8;
  background: #fff;
}

.preview-sm-main {
  width: 320px;
  margin: 0;
  border: 1px solid #e3e5e8;
  height: 550px;
  overflow-y: scroll;
  -ms-overflow-style: none;
  /* IE, Edge 対応 */
  scrollbar-width: none;
  /* Firefox 対応 */
}

.preview-sm-main::-webkit-scrollbar {
  /* Chrome, Safari 対応 */
  display: none;
}

/* 画面スクロール */
/* メニュー・ヘッダー固定、それ以外スクロール */
/* .page-content {
  height: 100%;
  max-height: calc(100vh - 60px);
  overflow-y: scroll;
  overflow-x: hidden;
} */

/* 一時的修正、今後削除予定 */
.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-primary,
.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-primary {
  color: #fff;
  background: #245BD6;
}

.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-success,
.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-success {
  color: #fff;
  background: #245BD6;
  font-size: 12px;
}

.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-warning,
.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-warning {
  color: #333333;
  background: #e5e5e5;
  font-size: 12px;
}

/* 基本設定、多言語情報などナビタブ */
.top-nav li a {
  padding: 8px 11px;
  display: block;
}

.top-nav>ul {
  display: flex;
  margin: 0;
  padding: 0;
}

.top-nav>ul>li {
  margin: 0 2px 0 0;
  padding: 1px 1px;
  background: #f6f7f9;
  border-radius: 4px 4px 0 0;
  border: solid #e3e5e8;
  border-width: 1px 1px 0 1px;
  list-style: none;
  font-size: 12px;
}

.top-nav>ul>li a {
  color: #3d3f45;
}

.top-nav ul li.active {
  box-shadow: 0 3px 0px -1px #fff;
  background: #fff;
}

.top-nav ul li.active a {
  color: #000;
}

.graph_custom {
  width: 100%;
}

.graph_custom tr td {
  height: 40px;
}

.graph_custom tr td:first-of-type {
  width: 27.5%;
}

.graph_custom [type=radio], .graph_custom [type=checkbox] {
  margin: 0;
  position: relative;
  top: 2px;
  margin-right: 8px;
}

.label_flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
}

.cat_flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
}

.device-setting-container {
  width: 496px;
  min-height: 118px;
  padding: 16px 0;
  padding-right: 16px;
  border-radius: 4px;
  border: solid 1px #e3e5e8;
  background-color: #f6f7f9;
}

.device-setting-container .device-setting-container-title {
  padding-left: 16px;
}

.device-setting-container:first-of-type {
  margin-right: 12px;
}

.device-setting-size-container {

  border-radius: 4px;
  border: solid 1px #e3e5e8;
  background-color: #fff;
  padding: 0 8px;
  margin-right: 12px;
}

.device-setting-size-input {
  border: none;
  border-radius: 4px;
  height: 28px;
  width: 38px;
  padding: 0;
}

.device-setting-size-container .device-setting-size-input:focus {
  outline: none;
  box-shadow: none;

}

.device-setting-size-letter {
  color: #a1a4aa;
  width: 13px;
  display: inline-block;
}

.color-select-container {
  height: 28px;
  width: 84px !important;
}

.color-select-display {
  height: 28px !important;
  padding: 0 14px !important;
}

.color-select-display i {
  top: 3px;
}

.select-number-container {
  width: 72px;
  padding-right: 6px;
  padding-left: 12px;
  height: 28px;
  border-radius: 4px;
  border: solid 1px #e3e5e8;
  background-color: #fff;
}

.z-index-container {
  width: 145px;
  height: 28px;
}

.chatwindow-prev {
  width: 370px;
  margin-left: 114px;
  margin-top: 24px;
}

.uf-inquiry-prev {
  width: 436px;
  margin-left: 114px;
  margin-top: 58px;

}

.chatwindow-prev-header {
  position: relative;
  height: 44px;
  border-radius: 10px 10px 0 0;
  border: solid 1px #e3e5e8;
  background-color: #fff;
  display: -webkit-flex;
  display: flex;
  -ms-flex-align: center;
  align-items: center;
}

.chatwindow-prev-bot-name {
  height: 60px;
  background-color: #ebedf2;
  display: -webkit-flex;
  display: flex;
  -ms-flex-align: center;
  align-items: center;
  position: relative;
  border: solid 1px #e3e5e8;
  border-top: none;
  color: transparent;
}

.chatwindow-prev-bot-name img {
  border-radius: 50%;
  width: 44px;
  height: 44px;
  margin: 0 10px 0 12px;
}

.chatwindow-prev-bot-name svg {
  border-radius: 50%;
  color: #fff !important;
  display: block;
  float: right;
  font-size: 15px;
  line-height: 16px;
  margin: 2px 0 0 0;
  text-align: center;
  border: 0;
  width: 20px;
  height: 20px;
  position: absolute;
  top: 4px;
  right: 8px;
  background: none;
  cursor: pointer;
}

.chatwindow-prev-bot-main {
  min-height: 300px;
  border: solid 1px #e3e5e8;
  background-color: #ebedf2;
}

.chatwindow-prev-bot-main-message {
  width: 239px;
  font-family: HiraginoSans-W3;
  font-size: 14px;
  margin: 10px 119px 0 12px;
  padding: 10px 12px 10px 12px;
  border-radius: 10px 10px 10px 0;
  border: solid 1px #e3e5e8;
  background-color: #fff;
}

.chatwindow-prev-bot-footer {
  border-radius: 0 0 10px 10px;
  border: solid 1px #ebedf2;
  background-color: #fff;
  padding: 0 20px;
}

.chatwindow-prev-bot-footer-top {
  border-bottom: solid 1px #ebedf2;
  font-family: HiraginoSans-W3;
  font-size: 12px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  color: #000;
  padding: 16px 0;
}

.chatwindow-prev-bot-footer-bottom {
  padding-bottom: 16px;
}

.chatwindow-prev-bot-footer-bottom-title {
  padding: 16px 0px;
}

.chatwindow-prev-update {
  width: inherit;
  height: 24px;
  border-radius: 13px;
  background-color: #ebedf2;
  border: none;
  margin-bottom: 12px;
}

.prev-lang-wrap {
  display: flex;
  display: -ms-flexbox;
  display: -webkit-flex;
  overflow-x: auto;
  padding-bottom: 5px;

}

.prev-lang-wrap::-webkit-scrollbar {
  padding-top: 2px;
  height: 10px;
}

.prev-lang-label {
  white-space: nowrap;
  margin: 0;
  border-radius: 12px;
  padding: 3.5px 12px;
  background-color: #e3e5e8;
  cursor: pointer;
  margin-right: 10px;
}

.prev-lang-label:last-of-type {
  margin: 0;
}


input[type="radio"]:checked+label.prev-lang-label {
  background-color: #fff0bb;
}

.category-separator {
  display: flex;
  align-items: center;
  margin: 32px 0;
}

.category-separator h2 {
  margin: 0;
}

.category-separator:after {
  content: "";
  flex-grow: 1;
  height: 1px;
  display: block;
  margin-left: 12px;
  background: #ebedf2;
}


.font-familiy-box {
  width: 100%;
  height: 28px;
  padding: 8px 12px;
  border-radius: 4px;
  border: solid 1px #e3e5e8;
  font-size: 12px;
  background: #fff;
}

.separator {
  content: "";
  flex-grow: 1;
  height: 1px;
}

.survey-inquiry-category-title {
  min-width: 131px;
  margin: 0 20px 0 25px !important;
}

.input-group.color.colorpicker-default:nth-of-type(2) {
  margin-left: 12px;
}

.uf-inquiry-survey-title {
  font-size: 12px;
  width: 100%;
  border-radius: 8px;
  border: solid 1px transparent;
  background-color: #fff;
  margin-bottom: 10px;
  padding: 20px 35px;
  color: #3d3f45;
}

.uf-inquiry-survey-border-top {
  border-top: solid 6px transparent;
}

.uf-inquiry-survey-main-btn {
  margin-right: 16px;
  width: 171px;
  height: 48px;
  border-radius: 4px;
  background-color: transparent;
  display: flex;
  text-align: center;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  text-align: center;
  color: transparent;

}

.uf-inquiry-survey-sub-btn {
  width: 120px;
  height: 48px;
  border-radius: 4px;
  border: solid 1px transparent;
  background-color: transparent;
  display: flex;
  text-align: center;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  text-align: center;
  color: transparent;

}

.prev-icon-switch {
  position: absolute;
  right: 12px;
  width: 24px;
  height: 24px;
  padding: 6px;
  border-radius: 4px;
  background-color: #e3e5e8;
  cursor: pointer;
}

.uf-inquiry-survey-title-top {
  border-bottom: solid 1px transparent;
  background-color: transparent;
  padding-bottom: 10px;

}

.uf-inquiry-survey-title-top span {
  font-family: HiraginoSans-W5;
  margin-left: 16px;
  font-size: 16px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  color: #000;
}

.uf-template-pdwn {
  height: 28px;
  min-width: 181px;
  border-radius: 4px;
  padding: 0 12px;
  border: solid 1px #e3e5e8;
  background-color: #fff;
}

.uf-template-input {
  width: 278px;
  padding-left: 12px;
  height: 28px;
  border-radius: 4px;
  border: solid 1px #e3e5e8;
  background-color: #fff;
}

input.bool-chkbox {
  margin: 0 !important;
  margin-right: 6px !important;
  cursor: pointer;
}

.round-prev {
  border-radius: 50%;
  object-fit: cover;
}

.round-prev.large {
  width: 80px;
  height: 80px;
}

.round-prev.medium {
  width: 44px;
  height: 44px;
}

/* ダッシュボード（top）用CSS */
.dashboard.cards {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.dashboard.card {
  padding: 0px 5px;
  -webkit-box-flex: 1;
  -ms-flex: 1 1 150px;
  flex: 1 1 150px;
}

.dashboard.card-container {
  border-radius: 4px;
  background: #ffffff;
  padding: 15px 15px 10px 15px;
  margin-bottom: 20px;
}

.dashboard.card-container .number h3 {
  margin: 0 0 10px 0;
  padding: 0;
  font-size: 30px;
  color: #3D3F45;
  font-weight: bold;
}

.dashboard.card-container .progress-info {
  margin: 5px 0;
  padding: 5px 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  font-size: 14px;
  font-weight: 500;
  color: #3D3F45;
  border-bottom: 1px solid #e9e9e9;
}

.dashboard.card-container .status-title {
  color: #3D3F45;
  padding: 5px 0 0 0;
  font-size: 12px;
  font-weight: 400;
}

/* desc.container用 */
.desc-container {
  margin-top: 16px;
}

.desc-container-title {
  font-size: 12px;
  font-family: Hiragino Sans;
  color: #000000;
  margin: 0 0 0 24px !important;
  width: 109px;
  min-width: 109px;
}

.desc-container-modal {
  height: 122px;
  background: #F6F7F9;
  border: 1px solid #E3E5E8;
  border-radius: 4px;
  margin-bottom: 24px;
  margin-left: 24px;
  padding-bottom: 130px;
}

.desc-container-modal-close {
  height: 10px;
  padding-bottom: 38px;
}

.desc-container-modal-title {
  font-family: Hiragino Sans;
  font-size: 12px;
  margin: 12px;
  font-weight: 500;
}

.desc-container-modal-item {
  color: #3D3F45;
  margin-right: 20px;
}

.select-box {
  height: 28px;
  width: 181px;
  border-radius: 4px;
  border: 1px solid #E3E5E8;
  padding: 5px 8px;
}

.desc-control-label {
  margin-left: 30px;
  font-family: Hiragino Sans;
  font-weight: 400;
  color: #3D3F45;
}

.section-title {
  font-family: Hiragino Sans;
  font-style: normal;
  font-weight: 500;
  font-size: 12px;
  color: #000000;
}

.preview-description-button {
  width: 275px;
  height: 44px;
  padding: 10px 16px;
  margin: 9px 12px;
  background: #3D3F45;
  box-shadow: 1px 2px 8px rgba(61, 63, 69, 0.24);
  border-radius: 6px;
  font-size: 16px;
  text-align: center;
  color: #FFFFFF;
  font-weight: 500;
  font-family: Hiragino Sans;
}

/* result.container用 */
.result-title {
  font-family: Hiragino Sans;
  font-weight: 500;
  font-size: 14px;
}

.result-main-container {
  background-color: #F6F7F9;
  border-radius: 4px;
  padding: 12px 12px 0 12px;
  margin-bottom: 12px;
}

.result-container {
  padding-bottom: 12px;
}

.result-container-title {
  width: 84px;
  font-family: Hiragino Sans;
}

.word-break {
  word-break: break-word;
  resize: none;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}

.unread-dot {
  height: 6px;
  width: 6px;
  background-color: #e53361;
  border-radius: 50%;
  display: inline-block;
}


.edit-container {
  width: 100%;
  background: #fff;
  font-size: 12px;
}

.edit-container,
.edit-container h4,
.edit-container p,
.edit-container ul {
  margin: 0;
  padding: 0;
}

.flexbox-space-top {
  margin: 16px 0 0 0;
}

.selling-select-all-container {
  background-color: #F6F7F9;
  padding: 12px;
  justify-content: space-between;
}

/* Maximum calendar CSS */
.fc .fc-popover {
  /* To match z-index of modal-image-container */
  z-index: 999;
}

.fc-timegrid-event .fc-event-main,
.fc .fc-daygrid-day-number {
  padding: 0;
}

.fc-custom-event {
  font-size: 11px;
  width: 100%;
  border-radius: 2px;
  color: #000;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 3px;
}

#table1members.table>thead, #table0list.table>thead, #table2list.table>thead
{
  position: sticky;
  top: 0;
  background: white;
  z-index: 2;
}

#table1members.table>thead>tr>th {
  font-size: 12px;
  border-bottom: 1px solid #ddd;
}

#table1members.table>tbody>tr>td {
  font-size: 12px;
  border-top: 0;
  border-bottom: 1px solid #ddd;
}

  
.fc-event-blue
 {
  /* default color of full calendar event */
  background-color: #D3EEFF !important;
  border-color: #D3EEFF !important;
}

.fc-event-yellow {
  background-color: #FFF0BB !important;
  border-color: #FFF0BB !important;
}

.fc-event-gray{
  background-color: #d3d3d3 !important;
  border-color: #d3d3d3 !important;
}

.fc-event-gray-white {
  background-color: #F6F7F9 !important;
  border-color: #F6F7F9 !important;
}

.fc-stop-1 {
  color: #A1A4AA;
}
.fc-event-pink{
  background-color: #f5d2d7 !important;
  border-color: #f5d2d7 !important;
}

.fc-event-blue:hover,
.fc-event-pink:hover,
.fc-event-gray:hover,
.fc-event-gray-white:hover {
  background-color: #bdcdb7 !important;
  border-color: #bdcdb7 !important;
}



.fc-day-title-header {
  display: flex;
  justify-content: space-between;
  width: 100%;
  padding: 4px;
}

.fc-day-title-text {
  color: #000;
}

.fc-selling-status-red {
  color: #E53361;
}

.fc-selling-status-orange {
  color: #FF9551;
}

.fc-resource-selling-status-green {
  background-color: #32ce55;
}

.fc-resource-selling-status-red {
  background-color: #E53361;
}

.fc-resource-selling-status-orange {
  background-color: #FF9551;
}

.fc-resource-selling-status-gray {
  background-color: #3D3F45;
  color: #A1A4AA;
}

.fc .fc-col-header-cell-cushion {
  width: 100%;
}

/* バナー */
#close-banner {
  right: 0.45%;
  top: 9%;
  position: absolute;
  opacity: 0;
  width: 1.1%;
  height: 0;
  padding-bottom: 1.1%;
}

.page-navi {
  padding-bottom: 10px;
}

.page-navi .sep {
  margin: 0 5px;
}

/* Scroll Top Bottom */
.scroll-to-top {
  bottom: 40px;
}

.scroll-to-bottom {
  display: inline-block;
  padding: 2px;
  text-align: center;
  position: fixed;
  z-index: 10001;
  bottom: 10px;
  display: none;
  right: 10px;
}

.scroll-to-bottom>i {
  display: inline-block;
  color: #687991;
  font-size: 32px;
  opacity: 0.7;
  filter: alpha(opacity=70);
}

.scroll-to-bottom:hover {
  cursor: pointer;
}

.scroll-to-bottom:hover>i {
  opacity: 1;
  filter: alpha(opacity=100);
}

@media (min-width: 992px) {

  /* 992px */
  .scroll-to-bottom {
    right: 10px;
  }
}

@media (max-width: 991px) {

  /* 991px */
  .scroll-to-bottom {
    right: 10px;
  }

  .scroll-to-bottom>i {
    font-size: 28px;
  }
}