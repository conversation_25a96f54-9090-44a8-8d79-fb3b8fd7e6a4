.page-content-wrapper .page-content {
  padding: 0 !important;
  margin-left: unset !important;
}

.page-content-header,
.page-sidebar-wrapper {
  display: none;
}

.maximum-screen-main-container {
  display: flex;
  flex: 1;
  overflow-y: hidden;
  background: #fff;
}

.maximum-screen-main-left {
  display: flex;
  flex-direction: column;
  width: 62.5%;
  overflow: hidden;
  position: relative;
  justify-content: flex-end;
}

.maximum-screen-header-container {
  display: flex;
  padding: 24px;
  padding-bottom: 0;
  align-items: flex-start;
  justify-content: space-between; 
  order: 1;
}

.maximum-screen-page-header {
  font-size: 28px;
  color: #000000;
}

.maximum-screen-date {
  font-size: 20px;
  color: #77797D;
  text-align: center;
}

.maximum-screen-clock {
  font-size: 44px;
  color: #000000;
  line-height: 1;
}

.maximum-screen-title {
  font-size: 25px;
  text-align: right;
  line-height: 1;
  padding: 0 24px;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.maximum-screen-content-wrapper {
  order: 2;
  padding-top: 20px;
}

.content-container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}

.maximum-screen-content-container {
  position: relative; 
  overflow: hidden;
  flex-wrap: wrap;
  flex-direction: column;
}

#slide_1, #slide_2 {
  position: absolute;
  width: 100%;
  left: 100%;
}

#slide_1 {
  left: 0;
}

.date-slot-container {
  display: flex;
}

.date-container {
  padding: 20px; 
  margin-right: 20px;
  width: 400px !important;
  white-space: nowrap;
  margin-left: auto; 
  margin-right: auto;
  text-align: center;
}

.slots-container {
  flex-grow: 1;
  display: flex;
  flex-wrap: wrap; 
  justify-content: flex-start;
  align-items: stretch; 
  align-content: flex-start;
}

.today {
  font-size: 20px;
  line-height: 1.4;
  margin-bottom: 10px;
}

.day {
  font-size: 16px;
}

.slot {
  display: flex;
  background-color: #ebedf2;
  border-radius: 5px; 
  margin: 5px; 
  padding: 10px; 
  box-sizing: border-box; 
  text-align: left; 
  justify-content: space-between; 
  flex-direction: row;
  align-items: center;
  min-height: 50px;
  font-size: 20px;
}

.no-availability {
  color: #f00;
  font-size: 10px;
  width: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.low-availability {
  color: #555555;
  font-size: 15px;
  font-weight: bold;
  width: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.availability {
  color: #0f0;
  float: right;
  font-size: 10px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px; 
}

.past-slot .availability {
  color: #aaa;
  background-color: #ebedf2;
  font-size: 10px;
}

.slot-time {
  display: block;
  float: left;
}

.invisible-slot {
  visibility: hidden;
}

.past-slot .slot-time {
  color: lightgray; 
}

.slot-availability {
  background-color: #ebedf2;
  display: flex;
  align-items: center;
  justify-content: center;
}

.past-slot .slot-availability {
  background-color: #e5e5e5;
}

.indicator-container {
  display: flex;
  justify-content: center;
  order: 3;
  margin-bottom: 30px;
}

.indicator-container.busy-period {
  margin-top: 930px;
}

.indicator-container.off-peak-period {
  margin-top: 930px;
}

.indicator {
  width: 10px;
  height: 10px;
  background-color: #D3D3D3;
  border-radius: 50%;
  margin: 0 5px;
}

.indicator.active {
  background-color: #696969;
}

.grey-bar {
  height: 1.5px;
  background-color: #eeeeee;
  width: 100%;
}

.promotion-image-container {
  width: 37.5%;
  height: 100vh;
  overflow: hidden;
}

.promotion-image {
  width: 100%;
  height: 100%;
  object-fit: cover; 
  opacity: 1;
  padding: 10px;
}

.fade-in {
  opacity: 1;
  transition: opacity 2s ease-in-out;
}

.fade-out {
  opacity: 0;
  transition: opacity 2s ease-in-out;
}

.page-footer {
  display: none;
}

.talkappibot {
  display: none;
}