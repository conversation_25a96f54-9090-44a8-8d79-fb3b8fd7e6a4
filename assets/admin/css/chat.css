@charset "utf-8";

.req-task-info {
	padding:2px;
	font-size:10px;
	border-radius:5px;
}

.confirm{
	background:#ddd;
}

/* CSS Document */
.chats li .comment {
  float: center;
  color: #DDD;
}

.chats li.comment .message {
    border-bottom: 1px dotted #ddd;
    color: #aaa;
    margin: 4px 70px;
    text-align: center;
}

.chats li.comment .error{
    border-bottom: 1px dotted #ddd;
    color: #ff80c0;
    margin: 4px 70px;
    text-align: center;
}
/* ---------- GENERAL ---------- */
#msg_preview1 {
    position: absolute;
    bottom: 140px;
    right:40px;
    width: 266px;
    height: 320px;
    color: #555;
    background-color: #f3f3f3;
    border-left: 0px solid #cecece;
    border-radius: 10px;
    padding:10px;
    overflow: hidden;
    z-index: 10000;
    display: none;
}
#menu:hover + #msg_preview {
    display: block;
}
#live-chat {
	display: none;
	background: #e9e9e9;
	color: #9a9a9a;
	bottom: 0;
	font-size: 12px;
	right: 24px;
	position: fixed;
	height: 600px;
	width: 360px;
	box-shadow: rgba(0, 0, 0, 0.75) 2px 7px 20px -4px;
	z-index: 9999;
}

.xscroll-wrapper {
  overflow-x: auto;
  overflow-y: hidden;
  display: inline-flex;
  max-width: 100%;
  flex-wrap: nowrap;
  flex-shrink: 0;
  -webkit-overflow-scrolling: touch;
  overflow-scrolling: touch;
  padding: 0 0 3px;
  margin: 0;
}
.xscroll-wrapper .card {
  min-width: 245px;
  max-width: 245px;
  margin: 0 5px;
  background-color: #fff;
  box-shadow: 1px 1px 3px rgba(0,0,0,0.1);
  border-radius: 4px;
  overflow: hidden;
  text-align:left;
}
.xscroll-wrapper .card:first-child {
  margin-left: 0;
}
.xscroll-wrapper .card .image {
  display: block;
  width: 100%;
  height: 160px;
  object-fit: cover;
}
.xscroll-wrapper .card .item-caption {
  color: #333;
  font-size: 12px;
  font-weight: bold;
  line-height: 1.3;
  margin: 10px 0 5px;
  padding: 0 10px;
  min-height: 47px;
  text-align: left;
  overflow: hidden;
}
.xscroll-wrapper .card .item-description {
  color: #333;
  font-size: 11px;
  line-height: 1.5;
  height: 80px;
  margin: 0 0 10px;
  padding: 0 7px;
  overflow: hidden;
  flex-grow: 3;
}
.xscroll-wrapper .card .item-button,
.xscroll-wrapper .card .item-button-horizontal,
.xscroll-wrapper .card .item-button-horizontal-icon {
  margin: 0;
  padding: 0;
  text-align: inherit;
}

.xscroll-wrapper .card .item-button-horizontal,
.xscroll-wrapper .card .item-button-horizontal-icon {
  display: flex;
  justify-content: space-around;
}

.xscroll-wrapper .card .item-button .button,
.xscroll-wrapper .card .item-button-horizontal .button,
.xscroll-wrapper .card .item-button-horizontal-icon .button {
  color:  #4b84ab;
  display: block;
  width: 100%;
  box-sizing: border-box;
  margin: 0;
  padding: 12px 10px;
  text-align: center;
  font-size: 14px;
  line-height: 1;
  text-decoration: none;
  border: 0;
  border-top: #ccc 1px solid;
  border-radius: 0;
  transition: .4s;
}
.xscroll-wrapper .card .item-button .button:hover {
  background-color: #ccc;
}

.xscroll-wrapper .card .item-button .button,
.xscroll-wrapper .card .item-button-horizontal .button {
  width: 100%;
  padding: 12px 10px;
}

.xscroll-wrapper .card .item-button-horizontal .button.col2,
.xscroll-wrapper .card .item-button-horizontal-icon .button.col2 {
  /* 2 colums */
  width: 50%;
}
.xscroll-wrapper .card .item-button-horizontal .button.col3,
.xscroll-wrapper .card .item-button-horizontal-icon .button.col3 {
  /* 3 colums */
  width: 33.3%;
}
.xscroll-wrapper .card .item-button-horizontal-icon .button {
  padding: 42px 10px 10px;
}
.xscroll-wrapper .card .item-button .button:hover,
.xscroll-wrapper .card .item-button-horizontal .button:hover,
.xscroll-wrapper .card .item-button-horizontal-icon .button:hover {
  background-color: #ccc;
}
.xscroll-wrapper .card .item-button .button-map,
.xscroll-wrapper .card .item-button .button-web,
.xscroll-wrapper .card .item-button .button-phone,
.xscroll-wrapper .card .item-button-horizontal-icon .button-map,
.xscroll-wrapper .card .item-button-horizontal-icon .button-web,
.xscroll-wrapper .card .item-button-horizontal-icon .button-phone {
  background-size: 22px 22px;
  background-repeat: no-repeat;
}
.xscroll-wrapper .card .item-button .button-map,
.xscroll-wrapper .card .item-button .button-web,
.xscroll-wrapper .card .item-button .button-phone {
  background-position: 10px 8px
}
.xscroll-wrapper .card .item-button-horizontal-icon .button-map,
.xscroll-wrapper .card .item-button-horizontal-icon .button-web,
.xscroll-wrapper .card .item-button-horizontal-icon .button-phone {
  background-position: 50% 10px
}
.xscroll-wrapper .card .item-button .button-map,
.xscroll-wrapper .card .item-button-horizontal-icon .button-map {
  background-image: url(./icon_map.png);
}
.xscroll-wrapper .card .item-button .button-web,
.xscroll-wrapper .card .item-button-horizontal-icon .button-web {
  background-image: url(./icon_web.png);
}
.xscroll-wrapper .card .item-button .button-phone,
.xscroll-wrapper .card .item-button-horizontal-icon .button-phone {
  background-image: url(./icon_phone.png);
}

.webchat-list {
	line-height:18px;
	vertical-align: middle;
}

.message-image {
	max-width: 640px;
}
.page-sidebar-closed.page-sidebar-fixed .page-sidebar:hover .page-sidebar-menu .sub-menu > li:hover > a, .page-sidebar-closed.page-sidebar-fixed .page-sidebar:hover .page-sidebar-menu .sub-menu > li.open > a, .page-sidebar-closed.page-sidebar-fixed .page-sidebar:hover .page-sidebar-menu .sub-menu > li.active > a, .page-sidebar .page-sidebar-menu .sub-menu > li:hover > a, .page-sidebar .page-sidebar-menu .sub-menu > li.open > a, .page-sidebar .page-sidebar-menu .sub-menu > li.active > a {
  background-color: #EBEDF2;
}

.s-chat-box {
    font-family: arial;
    width: 120px;
    height: 300px;
    line-height: 50px;
    text-align: center;
    position: fixed;
    bottom: 40px;
    right: 30px;
    z-index: 99999;
    cursor: pointer;
    background-size: 40px;
    background-repeat: no-repeat;
    background-position: 50%;
    display:none;
}
.s-chat-box-opener {
    font-family: arial;
    width: 55px;
    height: 55px;
    line-height: 50px;
    text-align: center;
    position: fixed;
    bottom: 20px;
    right: 30px;
    z-index: 99999;
    border-radius: 100%;
    cursor: pointer;
    background-size: 40px;
    background-repeat: no-repeat;
    background-position: 50%;
    background-image: url(/assets/talkappi/talkappi.png);
}

th {
	text-align: center;
}

.right {
	text-align: right;
}

.grid-menu{
	float:right;
    display:flex;
    flex-direction: row;
    width:100%;
    min-width:256px;
    height:100%;
    padding:0px;
    margin: 0px;
    background: rgba(255, 255, 255, 1);
    justify-content: flex-start;
    align-items:baseline;
    flex-wrap: wrap;
    align-content: flex-start;
}
.grid-item{
    background-color: rgba(0,0,0,0);
    width: 86px;
    height: 86px;
    margin: 0px;
    text-align: center;
}

.button-item {
	display:inline-block;
	min-height:22px;
	height:auto;
	margin-right:6px;
	margin-top:10px;
	background-color:#ffffff;
	color: #57606f;
	border: solid 1px #57606f;
	font-size:13px !important;
	padding:3px 6px 3px 6px;
	white-space: break-spaces;
	text-align:left;
	line-height:18px;
}

.external-link {
	width:14px;
	margin-left:5px;
}

.delete_chat {
	color:red;
}

.filter-condition {
  float:left;margin-top:26px;
}

.chats li.out .avatar {
  margin-right: 5px;
}

.chats li .avatar {
  height: 35px;
  width: 35px;
}

.chats li.in .message {
  margin-left: 50px;
}

.chats li.out .message {
  margin-right: 50px;
}

@media (max-width: 991px) {
  .filter-condition {
    display: none;
  }
}

/* additional */
.talkappi-waiting {
  display: none;
  z-index: 9998;
  position:fixed;
  height: 100vh;
  width: 100vw;
  top: 0;
  left: 0;
}
.talkappi-waiting img {
  position:fixed;
  z-index: 9999;
  bottom:50%;
  left:50%;
  margin-left:-32px;
  margin-bottom:-32px;
  width:64px;
}

#btn-plus {
  padding: 0;
  background-color: transparent;
  border: none;
}
#btn-plus.on {
  color: rgba(50, 113, 255, 1);
}
#plus-menu {
  display: none;
  position: absolute;
  bottom: 53px;
  left: 0;
  background-color: #FFFFFF;
  width: 100%;
  z-index: 10;
}
#plus-menu .plus-menu-item {
  height: 40px;
  padding: 0 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  margin-bottom: 0px;
  box-shadow: 0px -1px 0px 0px #EBEDF2 inset;
}
#plus-menu .plus-menu-item i {
  transition: all 200ms;
}
#plus-menu .plus-menu-item.on i {
  transform: rotate(180deg);
}
#plus-menu .menu {
  padding: 0 12px;
  margin-bottom: 0;
  list-style: none;
  display: none;
  box-shadow: 0px -1px 0px 0px #EBEDF2 inset;
}
#plus-menu .menu[data-menu-target="template-menu"] {
  padding: 0;
}
#plus-menu .menu .quick-message-title {
  display: none;
}
#plus-menu .menu li {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
#plus-menu .menu li span#clear {
  color: #245BD6;
}
#plus-menu .menu li a.msgli {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex: 1;
  font-size: 14px;
  min-height: 40px;
  padding: 8px 0;
}
#plus-menu .menu li a.msgli span.msgname {
  white-space: pre-wrap;
  max-width: 80%;
}
#plus-menu .menu li:not(:last-of-type) {
  box-shadow: 0px -1px 0px 0px #EBEDF2 inset;
}
#plus-menu .menu li.unknown_chat .unknown_chat_count {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  margin-left: 6px;
  width: 20px;
  height: 20px;
  border-radius: 8px;
  background-color: #E53361;
  color: #FFFFFF;
}