@charset "utf-8";

/* add to webchat-f.css */
#live-chat-container .chat-content .out .body {
  background-color: #45af7f;
  color: #fff;
}

/* overwrite webchat.css */
.talkappi-img-circle {
  border-radius: 8px;
}


/* ----------------------------------------
common
---------------------------------------- */
html, body {
  height: 100%;
}
body {
  -webkit-text-size-adjust: 100%;
}
#chat-entrance #chat-header,
#live-chat-container #chat-header {
  min-height: 42px;
  padding: 8px 52px 8px 16px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}
#chat-entrance,
#live-chat-container {
	background: #e9e9e9;
    color: #9a9a9a;
    font: 100%/1.5em "Droid Sans", sans-serif;
    margin: 0;
  min-width: 320px;
  min-height: 100%;
  box-sizing: border-box;
  position: relative;
}
#chat-header #chat-logo-img {
  float: none;
  margin-right: 0;
  border-radius: 100%;
  position: absolute;
  top: 8px;
  left: 16px;
}
#chat-header h4 {
  font-size: 16px;
  margin-top: 5px;
  padding-left: 52px;
  line-height: 1em;
}
#chat-header h4 small{
  font-size: 65%;
}
.chat-close {
  border: 0;
  display: block;
  width: 24px;
  height: 24px;
  position: absolute;
  top: 16px;
  right: 16px;
  background: none;
  cursor: pointer;
}
.chat-close span {
  display: block;
  width: 0;
  height: 0;
  overflow: hidden;
  text-indent: -100%;
}
.chat-close span::before,
.chat-close span::after {
  content: "";
  display: block;
  width: 100%;
  height: 2px;
  position: absolute;
  top: 10px;
  left: 0;
  background: rgba(255, 255, 255, 0.5);
}
.chat-close span::before {
  -moz-transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  -webkit-transform: rotate(-45deg);
  transform: rotate(-45deg);
}
.chat-close span::after {
  -moz-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
}
.poweredby {
  font-size: 12px;
  text-align: right;
}
.poweredby img {
  width: 100px;
  padding-left: 5px;
  height: auto;
  display: inline;
  vertical-align: middle;
}


/* ----------------------------------------
#chat-entrance
---------------------------------------- */
#chat-entrance {
  padding-bottom: 50px;
}
#chat-entrance .welcome-message {
  color: #666;
  font-size: 16px;
  text-align: center;
  padding: 40px 10px 20px;
}
#chat-entrance .interface-list {
  max-width: 360px;
  list-style-type: none;
  margin: 0 auto;
  padding: 0 10px;
}
#chat-entrance .interface-list li {
  margin-bottom: 5px;
}
#chat-entrance .interface-list li a {
  background-color: rgba(255,255,255,.5);
  box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.3);
  display: block;
  min-height: 70px;
  padding: 0;
  position: relative;
}
#chat-entrance .icon {
  width: 60px;
  height: 60px;
  position: absolute;
  top: 5px;
  left: 8px;
}
#chat-entrance .icon img {
  width: 100%;
}
#chat-entrance .name {
  color: #333;
  font-size: 16px;
  padding: 24px 80px 0;
}
#chat-entrance .chat-button {
  font-size: 12px;
  padding: 5px 10px;
  border-radius: 24px;
  position: absolute;
  right: 10px;
  top: 18px;
}
#chat-entrance footer {
  box-sizing: border-box;
  width: 100%;
  padding: 0 10px ;
  position: absolute;
  bottom: 10px;
}


/* ----------------------------------------
Chat UI
---------------------------------------- */
#live-chat-container {
  font-size: 14px;
  overflow: hidden;
}
#live-chat-container #chat-header {
  position: fixed;
  box-sizing: border-box;
  width: 100%;
  min-height: 60px;
  top: 0;
  z-index: 9999;
}
#live-chat-container .chat-content-wrapper {
  margin: 0px 0 0px;
  overflow-y: scroll;
  -webkit-overflow-scrolling: touch;
  overflow-scrolling: touch;
}
#live-chat-container .chat-content {
  box-sizing: border-box;
  margin: 0;
  /* padding: 0 10px; */
  overflow-y: auto;
}
#live-chat-container .chat-content > li {
  list-style: none;
  margin: 30px 0;
  position: relative;
  /* padding-bottom: 18px; */
}
#live-chat-container .chat-content .body {
  display: inline-block;
  padding: 6px 12px 8px;
  border-radius: 15px;
  max-width: 80%;
  text-align: left;
}
#live-chat-container .chat-content .datetime {
  display: inline-block;
  font-size: 12px;
  line-height: 1;
  white-space: nowrap;
  position: absolute;
  bottom: 0;
}
#live-chat-container .chat-content .out {
  text-align: right;
}
#live-chat-container .chat-content .out .datetime {
  right: 0;
}
#live-chat-container .chat-content .out .body {
  color: #fff;
  background-color: #666;
  border-bottom-right-radius: 0;
  box-shadow: -1px 1px 3px rgba(0,0,0,0.1);
}
#live-chat-container .chat-content .in .body {
  color: #333;
  background-color: #fff;
  border-bottom-left-radius: 0;
  box-shadow: 1px 1px 3px rgba(0,0,0,0.1);
}

.body {
	word-wrap: break-word;
	word-break: break-all;
}
/* ----------
Carousel
---------- */
#live-chat-container .xscroll-wrapper {
  overflow-x: scroll;
  overflow-y: hidden;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  flex-shrink: 0;
  -webkit-overflow-scrolling: touch;
  overflow-scrolling: touch;
  padding: 0 0 3px;
  margin: 0;
}
#live-chat-container .xscroll-wrapper .card {
  min-width: 242px;
  max-width: 242px;
  margin: 0 5px;
  background-color: #fff;
  box-shadow: 1px 1px 3px rgba(0,0,0,0.1);
  border-radius: 4px;
  overflow: hidden;
}
#live-chat-container .xscroll-wrapper .card:first-child {
  margin-left: 0;
}
#live-chat-container .xscroll-wrapper .card .image {
  display: block;
  width: 100%;
  height: 160px;
  object-fit: cover;
}
#live-chat-container .xscroll-wrapper .card .item-caption {
  color: #333;
  font-size: 12px;
  font-weight: bold;
  line-height: 1.3;
  margin: 10px 0 5px;
  padding: 0 10px;
  height: 47px;
  text-align: left;
  overflow: hidden;
}
.xscroll-wrapper .card .item-description {
  color: #333;
  font-size: 11px;
  line-height: 1.5;
  height: 80px;
  margin: 0 0 10px;
  padding: 0 7px;
  overflow: hidden;
  flex-grow: 3;
}
.xscroll-wrapper .card .item-button,
.xscroll-wrapper .card .item-button-horizontal,
.xscroll-wrapper .card .item-button-horizontal-icon {
  margin: 0;
  padding: 0;
  text-align: inherit;
}

.xscroll-wrapper .card .item-button-horizontal,
.xscroll-wrapper .card .item-button-horizontal-icon {
  display: flex;
  justify-content: space-around;
}

.xscroll-wrapper .card .item-button .button,
.xscroll-wrapper .card .item-button-horizontal .button,
.xscroll-wrapper .card .item-button-horizontal-icon .button {
  color:  #4b84ab;
  display: block;
  width: 100%;
  box-sizing: border-box;
  margin: 0;
  padding: 12px 10px;
  text-align: center;
  font-size: 14px;
  line-height: 1;
  text-decoration: none;
  border: 0;
  border-top: #ccc 1px solid;
  border-radius: 0;
  transition: .4s;
}
.xscroll-wrapper .card .item-button .button:hover {
  background-color: #ccc;
}

.xscroll-wrapper .card .item-button .button,
.xscroll-wrapper .card .item-button-horizontal .button {
  width: 100%;
  padding: 12px 10px;
}

.xscroll-wrapper .card .item-button-horizontal .button.col2,
.xscroll-wrapper .card .item-button-horizontal-icon .button.col2 {
  /* 2 colums */
  width: 50%;
}
.xscroll-wrapper .card .item-button-horizontal .button.col3,
.xscroll-wrapper .card .item-button-horizontal-icon .button.col3 {
  /* 3 colums */
  width: 33.3%;
}
.xscroll-wrapper .card .item-button-horizontal-icon .button {
  padding: 42px 10px 10px;
}
.xscroll-wrapper .card .item-button .button:hover,
.xscroll-wrapper .card .item-button-horizontal .button:hover,
.xscroll-wrapper .card .item-button-horizontal-icon .button:hover {
  background-color: #ccc;
}
.xscroll-wrapper .card .item-button .button-map,
.xscroll-wrapper .card .item-button .button-web,
.xscroll-wrapper .card .item-button .button-phone,
.xscroll-wrapper .card .item-button-horizontal-icon .button-map,
.xscroll-wrapper .card .item-button-horizontal-icon .button-web,
.xscroll-wrapper .card .item-button-horizontal-icon .button-phone {
  background-size: 22px 22px;
  background-repeat: no-repeat;
}
.xscroll-wrapper .card .item-button .button-map,
.xscroll-wrapper .card .item-button .button-web,
.xscroll-wrapper .card .item-button .button-phone {
  background-position: 10px 8px
}
.xscroll-wrapper .card .item-button-horizontal-icon .button-map,
.xscroll-wrapper .card .item-button-horizontal-icon .button-web,
.xscroll-wrapper .card .item-button-horizontal-icon .button-phone {
  background-position: 50% 10px
}
.xscroll-wrapper .card .item-button .button-map,
.xscroll-wrapper .card .item-button-horizontal-icon .button-map {
  background-image: url(./icon_map.png);
}
.xscroll-wrapper .card .item-button .button-web,
.xscroll-wrapper .card .item-button-horizontal-icon .button-web {
  background-image: url(./icon_web.png);
}
.xscroll-wrapper .card .item-button .button-phone,
.xscroll-wrapper .card .item-button-horizontal-icon .button-phone {
  background-image: url(./icon_phone.png);
}


/* ----------
#live-chat-container .chat-footer
---------- */
#live-chat-container .chat-footer {
  box-sizing: border-box;
  width: 100%;
  position: fixed;
  bottom: 0;
  height: 76px;
  background: #fff;
  box-shadow: 0 -1px 3px rgba(0,0,0,0.1);
}

#live-chat-container .chat-footer footer {
  color: #eee;
  background-color: #666;
  width: 100%;
  padding: 5px 0;
  box-shadow: 0 -1px 3px rgba(0,0,0,0.1);
  position: absolute;
  bottom: 0
}
#live-chat-container .poweredby {
  font-size: 10px;
  text-align: center;
  line-height: 1;
}
#live-chat-container .poweredby img {
  height: 18px;
  width: auto;
}


/* ----------
#chat-form
---------- */
#chat-form {
  box-sizing: border-box;
  width: 100%;
  padding: 0;
  position: fixed;
  bottom: 38px;
}
#chat-form .menu-icon, #chat-form .menu-image,
#chat-form .chat-send {
  display: block;
  cursor: pointer;
  position: absolute;
  top: -1px;
}
#chat-form .menu-icon {
  width: 28px;
  height: 28px;
	float: none;
	margin: 0;
}
#chat-form .menu-image {
  width: 28px;
  height: 28px;
  float: none;
  margin: 0;
}
#chat-form .chat-send {
  width: 26px;
  height: 26px;
  padding: 1px;
}
#chat-form .menu-icon svg,
#chat-form .menu-image svg,
#chat-form .chat-send svg {
  fill: #999;
}
#chat-form .menu-icon {
  left: 10px;
}
#chat-form .menu-image {
  left: 48px;
}
#chat-form .chat-send {
  right: 10px;
}

#chat-form fieldset {
  display: block;
  box-sizing: border-box;
  width: 100%;
  padding: 0 20px 0 80px;
}
#chat-form .input-field {
  color: #333;
  margin: 0;
}
#chat-form .chat-message {
  margin: 0;
}
#chat-form input[type="text"] {
  box-sizing: border-box;
  width: 100%;
  padding: 5px;
  border-radius: 4px;
	outline: none;
  /* background: #e9e9e9; */
}
#chat-form input[type="text"],
#chat-form input[type="text"]::placeholder {
  line-height: 1;
}
#chat-form input[type="text"]:focus {
  /* background-color: #fff; */
  /* border: #999 1px solid; */
  /* box-shadow: 0 0 1px rgba(0,0,0,0.5); */
}