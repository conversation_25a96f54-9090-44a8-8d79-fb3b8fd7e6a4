/* 全画面　親要素 */
.edit-container {
  width: 100%;
  background: #fff;
  font-size: 12px;
}

/* 質問の折り畳み */
.edit-container .position-relative {
  position: relative;
  padding: 5px 24px !important;
}
/* 折り畳み後のアイコンコンテナ */
.edit-container .folded-icon {
  position: absolute;
  top: -20px;
  right: 10px;
}
/* ドラッグアイコン */
.survey-icon-draggable {
  position: absolute;
  left: -24px;
  top: 5px;
  display: none;
  cursor: move;
}

.edit-container ::marker,
.survey-top-nav ::marker {
  display: none;
  content: none;
}

.edit-container ::-webkit-resizer {
  display: none;
}

.edit-container :focus {
  outline: none;
  box-shadow: 0 0 0 1px #245BD6;
}

/* .survey-basic-category-title {
  width: 109px;
  margin: 0 0 0 25px !important;
} */

/* ページ　共通　開始 */
.edit-container,
.edit-container h4,
.edit-container p,
.edit-container ul {
  margin: 0;
  padding: 0;
}
.rich-text-editor {
  -ms-overflow-style: none;    /* IE, Edge 対応 */
  scrollbar-width: none;       /* Firefox 対応 */
}
.rich-text-editor ::-webkit-scrollbar {  /* Chrome, Safari 対応 */
  display: none;
}
.note-editable {
  -ms-overflow-style: none;    /* IE, Edge 対応 */
  scrollbar-width: none;       /* Firefox 対応 */
}
.note-editable ::-webkit-scrollbar {  /* Chrome, Safari 対応 */
  display: block;
}

.page-container {
  width: 100%;
  padding: 33px 36px !important;
  margin: 0 !important;
  border: solid 1px #e3e5e8;
  display: flex;
}

.page-container .left-container {
  width: 100%;
  /* max-width: 800px; */
}

.linkbox {
  position: relative;
}
.linkbox a {
  position: absolute;
  top: 0;
  left: 0;
  height:100%;
  width: 100%;
}

.survey-add-dest-user-container {
  margin: 16px 0 0 135px;
  display: none;
  width: fit-content;
  width: -moz-fit-content;
}
.survey-add-dest-user {
  min-width: 72px;
  height: 24px;
  background: #d3eeff;
  border-radius: 12px;
  padding: 6px 12px;
  display: flex;
  align-items: center;
}

/* 基本設定、多言語情報などナビタブ */
.survey-top-nav li a {
  padding: 8px 11px;
  display: block;
}

.survey-top-nav > ul {
  display:flex;
  margin:0;
  padding: 0;
}

.survey-top-nav > ul > li {
  margin:0 2px 0 0;
  padding: 1px 1px;
  background: #f6f7f9;
  border-radius:4px 4px 0 0;
  border: solid #e3e5e8;
  border-width: 1px 1px 0 1px;
}
.survey-top-nav > ul > li a {
  color: #3d3f45;
}
.survey-top-nav ul li.active {
  box-shadow: 0 3px 0px -1px #fff;
  background: #fff;
}
.survey-top-nav ul li.active a {
  color: #000;
}

/* ページ　共通　終了 */

.survey-space-top-1 {
  margin: 16px 0 0 0;
}
.survey-space-top-2 {
  margin: 24px 0 0 0;
}
.survey-space-top-3 {
  margin: 6px 0 0 0 !important;
}
.survey-space-top-4 {
  margin: 12px 0 24px 0 !important;
}
.survey-space-top-5 {
  margin: 10px 0 0 0 !important;
}
.margin-bottom-1 {
  margin: 0 0 30px 0;
}
.survey-space-top-bottom-1 {
  margin: 24px 0 24px 0;
}
.survey-space-top-bottom-2 {
  margin: 0 0 0 49px !important;
  width: 109px;
  min-width: 109px;
}
.survey-space-top-bottom-3 {
  margin: 16px 0 0 8px;
}
.survey-space-top-bottom-4 {
  margin: 54px 0 0 20px !important;
}
.survey-space-top-bottom-5 {
  margin: 12px 0 16px 0 !important;
}
.survey-space-top-bottom-6 {
  margin: 40px 0 16px 0 !important;
}
.survey-space-top-bottom-7 {
  margin: 24px 0 16px 0 !important;
}
.survey-space-left-1 {
  margin: 0 0 0 16px;
}
.survey-space-left-2 {
  margin: 0 0 0 12px;
}
.survey-space-left-3 {
  padding: 0 0 0 10px !important; 
}

.survey-space-right-1 {
  margin: 0 5px 0 0;
}
.survey-space-right-2 {
  margin: 0 10px 0 0 !important;
}
.survey-space-right-3 {
  margin: 0 12px 0 0;
}
.survey-space-right-4 {
  margin: 0 8px 0 0; 
}
.survey-space-right-5 {
  padding: 0 40px 0 0 !important;
}
.survey-space-around-1 {
  margin: 0 12px;
}
.survey-space-around-2 {
  margin: 0 12px 0 16px !important;
}
.survey-space-around-3 {
  margin: 32px 0 0 24px;
}
.survey-space-around-4 {
  margin: 0 24px 0 0;
}
.survey-space-around-5 {
  margin: 24px 0 0 24px;
}
.survey-space-around-6 {
  margin: 0 12px 0 9px!important;
}
.survey-space-all-around {
  margin: 25px 0 5px 0 !important; 
}
.survey-space-all-around-2 {
  margin: 5px 12px 14px 0 !important;
}
.survey-space-all-around-3 {
  margin: 0 12px 10px 0;
}
.survey-space-all-around-4 {
  margin: 16px 0 0 36px;
}
.survey-space-all-around-5 {
  margin: 12px 0 0 12px;
}
.inner-space-1 {
  padding: 16px 24px;
}
.inner-space-2 {
  padding: 20px 0 0 12px;
}
.inner-space-3 {
  padding: 6px 12px;
}
.inner-space-4 {
  padding: 14px 0;
}
.inner-space-5 {
  padding: 20px 14px;
}
.survey-position-absolute-v1 {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
}
.survey-position-absolute-v2 {
  position: absolute;
  top: -8px;
  right: 0;
}

.survey-preview-notes {
  padding: 10px 14px 20px 14px;
  position:relative;
}

.survey-preview-notes::before {
  content: "";
  display: block;
  width: 262px;
  height: 1px;
  background: #e3e5e8;
  position: absolute;
  top: 0;
}
.border-none {
  border: none;
}
.survey-height-32 {
  height: 32px;
}
.survey-height-28 {
  height: 28px;
}
.survey-box-shadow-1 {
  box-shadow: inset 0 -1px 0 0 #e3e5e8 !important;
}
/* 選択肢のinput */
/* overflow: hidden; */
.survey-pointer {
  cursor: pointer;
}
/* border-botttom */
.border-bottom-pale-blue-1 {
  border-bottom: 1px solid #e3e5e8;
}
.border-bottom-pale-blue-2 {
  border-bottom: 2px solid #e3e5e8;
}
.border-bottom-lightish-blue-1 {
  border-bottom: 1px solid #245BD6;
}
.border-bottom-lightish-blue-2 {
  border-bottom: 2px solid #245BD6;
}
.height-100 {
  height: 100%;
}
.width-100 {
  width: 100%;
}
.survey-width-50 {
  width: 50px;
}
.survey-width-30 {
  width: 30px;
}
.survey-width-430 {
  width: 430px;
}
.survey-width-108 {
  width: 108px;
}
.survey-width-140 {
  width: 140px;
}
.survey-width-155 {
  width: 155px !important;
}
.width-96 {
  width: 96px;
}
.survey-width-fit {
  width: fit-content !important;
  max-width: 200%;
  min-width: 260px;
}
.small-label-text-center {
  line-height: 24px;
  padding: 0 11px;
}

/* 共通　終了 */
/* 個別　開始 */
/* 分類　プルダウンのコンテイナ */
.survey-lines-category-container {
  width: 500px;
  height: 52px;
  padding: 12px;
  border-radius: 4px;
  border: solid 1px #e3e5e8;
  background: #f6f7f9;
  margin: 10px 0 0 135px;

  display: flex;
  align-items: center;
}

.survey-period-date-container {
  width: 132px;
  height: 28px;
  padding: 0 8px;
  margin: 0 8px 0 0;
  border-radius: 4px;
  border: solid 1px #e3e5e8;
  display: flex;
  align-items: center;
  font-size: 12px;
}
/* カレンダーにフォーカス => 青線 */
.survey-period-date-container:focus-within {
  border: 1px solid #245BD6;
}
.survey-period-date-container svg {
  width: 25px;
}

/* 公開URL　挿入されるエリアのコンテナ */
.public-url-input-container {
  width: 85%;
}
/* 公開URL　挿入されるエリア */
.public-url-area {
  width: 100%;
  max-width: 500px;
	height: auto;
  /* height: 54px; */
  border-radius: 4px;
  padding: 8px !important;
  border: solid 1px #ebedf2;
  background: #f6f7f9;

  position: relative;
}
.survey-copy-url {
  cursor: pointer;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  right: 20px;
}
/* 基本設定　終了 */

/* 多言語設定　開始 */
.suevey-languages-info-container {
  padding: 33px 36px;
  border: solid 1px #e3e5e8;
}
.suevey-languages-info-container .left-container {
  width: 100%;
  max-width: 750px;
  padding: 0 42px 0 0;
  font-size: 12px;
}

.description-section-container {
  padding: 16px 0 0 0;
  position: relative;
}
.description-section-container::before {
  content: "";
  display: block;
  width: 100%;
  height: 1px;
  background:#e3e5e8;
  position: absolute;
  top: 30px;
  left: 26px;
}
.js-desc-header-container .description-section-container::before {
  left: 0;
}

/* アンケート概要のtextarea */
.survey-summary-textarea {
  width: 100%;
  max-width: 500px;
  min-height: 79px;
  /* height: 120px; */
  padding: 8px 12px;
  border-radius: 4px;
  border: solid 1px #e3e5e8;
  background: #fff;
}

/* モーダルウィンドウ　コンテナ */
.suevey-languages-info-container .header-modal-window-container {
  width: 664px;
  padding: 36px 24px 40px 35px;
  border-radius: 4px;
  border: solid 1px #e3e5e8;
  background: #fff;

  z-index: 9999;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.preview-contents {
  display: none;
  background: #fff;
}

/* モーダルウィンドウ　メインコンテナ　パソコン */
.suevey-languages-info-container .left-container .header-modal-container  h4.pc {
  padding: 0 24px 12px 0;
  border-bottom: 2px solid #e3e5e8;
}
.suevey-languages-info-container .left-container .header-modal-container  h4.link {
  padding: 0 0 12px 0;
  border-bottom: 2px solid #e3e5e8;
}
.suevey-languages-info-container .left-container .header-modal-container  h4.pc.current-type {
  border-bottom: 2px solid #245BD6;
}
/* モーダルウィンドウ　ドラッグ&ドロップ*/
.survey-drag-or-click {
  width: 546px;
  height: 60px;
  margin: 24px 0 0 0;
  border-radius: 4px;
  border: dashed 1px #245BD6;
  cursor: pointer;
}
.survey-drag-or-click svg {
  margin: 0 9px 0 0;
}
/* モーダル　写真アップロード後の編集コンテナ */
.survey-uploaded-header-image-container {
  width: 546px;
  display: flex;
  box-shadow: inset 0 1px 0 0 #e3e5e8;
}

.survey-uploaded-image {
  width: 105px;
  height: 70px;
  margin: 8px 12px 24px 0;
  border-radius: 4px;
  background: #f6f7f9;
}
.survey-uploaded-image-edit-title {
  border: none;
  width: 387px;
  height: 24px;
  margin: 14px auto 0 0;
  font-family: KozGoPr6N;
  font-size: 12px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  color: #a1a4aa;
}

/* 右側(プレビュー) */
/* ヘッダー */
.suevey-languages-info-container .right-container .header-container {
  width: 320px;
  height: 44px;
  padding: 0 12px;
  border-radius: 10px 10px 0 0;
  border: solid 1px #e3e5e8;
  background: #fff;
}

/* メイン */
.suevey-languages-info-container .right-container .main-container {
  width: 320px;
  height: 495px;
  padding: 5px 10px 9px 10px;
  background: #ebedf2;
  overflow-y: scroll;
}
/*  */
.suevey-languages-info-container .right-container .main-container .survey-main {
  border-radius: 10.5px;
  border: solid 0.9px #e3e5e8;
  background: #fff;
  overflow: hidden;
}

.suevey-languages-info-container .right-container .main-container::-webkit-scrollbar {
  display: none;
}

/* 上のピンクの部分 */
.suevey-languages-info-container .right-container .main-container .survey-main .main-header {
  height: 5px;
}
/* タイトルコンテナ */
.suevey-languages-info-container .right-container .main-container .survey-main .title-container {
  margin: 14px 0 0 17px;
}
/* ロゴ */
.desc-preview-logo {
  width: 48px;
  height: 48px;
  margin: 0 10px 0 0;
  border-radius: 50%;
}

.suevey-languages-info-container .right-container .main-container .survey-main .survey-preview-icon {
  width: 48px;
  min-height: 48px;
  max-height: 48px;
  object-fit: cover;
  border: none;
  border-radius: 50%;
}
.suevey-languages-info-container .survey-preview-header-image {
  object-fit: cover;
  border: none;
  width: 272px;
  min-height: 136px;
  max-height: 136px;
  margin: 9px 13px 0 13px;
  border-radius: 12px;
}

/* inquiry プレビュー　メイン画像のみ */
.suevey-languages-info-container .main-img-container {
  width: 100%;
  height: 0;
  padding-bottom: 66.66%;
  position: relative;
  background-size: cover;
  border-radius: 12px;
  box-sizing: content-box;
  overflow: hidden;
}
.suevey-languages-info-container .main-img-container::before {
  content: '';
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  background-color: rgba(217, 217, 217, 0.01);
  filter: blur(99px);
  backdrop-filter: blur(99px);
}
.suevey-languages-info-container .main-img-container .main-img {
  width: 100%;
  aspect-ratio: 3/2;
  position: relative;
  z-index: 2;
  object-fit: cover;
}

/* プレビューの内容 */
.suevey-languages-info-container .right-container .main-container .survey-main .contents-container {
  margin: 0 14px;
}

.suevey-languages-info-container .right-container .main-container a {
  text-decoration: underline;
  color: inherit;
}

/* 線 */
.suevey-languages-info-container .right-container .main-container .survey-main .contents-container div {
  box-shadow: inset 0 1px 0 0 #e3e5e8;
  padding: 12px 0 12px 0;
}
/* 線 */
.suevey-languages-info-container .right-container .main-container .survey-main .contents-container div:first-child {
  box-shadow: none;
}

/* プレビューの改行 */
.suevey-languages-info-container .right-container .main-container .preview-description-contents,
.suevey-languages-info-container .right-container .main-container .preview-summary-contents {
  white-space: pre-wrap;
}

/* フッター(「回答する」のコンテナ) */
.suevey-languages-info-container .right-container .footer-container {
  width: 320px;
  height: 55px;
  border-radius: 0 0 10px 10px;
  background: #245BD6;
}

/* 多言語設定　終了 */
/* アンケート質問　開始 */
.survey-survey-questions-container {
  width: 100%;
  padding: 33px 36px;
  border: solid 1px #e3e5e8;
}

.survey-survey-questions-container .left-container {
  width: 100%;
  max-width: 800px; /* メモ：仮 */
}

/* 行 flex */
.survey-survey-questions-container .survey-survey-questions-lines-container {
  width: 100%;
  margin: 16px 0 0 24px;
  display: flex;
  align-items: center;
}

/* アンケート名、アンケート質問　入力コンテナ */
.survey-survey-questions-container .survey-survey-questions-lines-container .survey-title-input-container,
.survey-survey-questions-container .survey-survey-questions-lines-container .survey-summary-textarea-container {
  width: 100%;
  max-width: 641px;
}

/* アンケート名　入力 */
.survey-survey-questions-container .survey-survey-questions-lines-container .survey-title-input-container .survey-title-input {
  width: 100%;
  max-width: 641px;
  min-height: 28px;
  padding: 5px 12px;
  border-radius: 4px;
  border: solid 1px #e3e5e8;
  background: #f6f7f9;
  white-space: pre-wrap;
  word-wrap: break-word;
}

/* アンケート説明　テキストエリア */
.survey-survey-questions-container .survey-survey-questions-lines-container .survey-summary-textarea-container .survey-summary-textarea {
  width: 100%;
  max-width: 641px;
  min-height: auto;
  padding: 5px 50px 5px 12px;
  border-radius: 4px;
  border: solid 1px #e3e5e8;
  background: #f6f7f9;
}

/* 質問編集セクション */
.survey-survey-questions-container .left-container .survey-editing-container {
  width: 100%;
  margin: 40px 0 0 0;
  position: relative;
}
/* セクション 1 / 2 */
.survey-survey-questions-container .left-container .survey-editing-container .survey-section-num {
  padding: 1px 7px 2px;
  background: #e3e5e8;
  border: solid 1px #e3e5e8;
  border-radius: 4px 4px 0 0;
  position: absolute;
  top: -23px;
  left: -1px;
}

/* 質問編集セクション */
.survey-survey-questions-container .left-container .survey-questions-border {
  width: 100%;
  max-width: 750px;
  height: 1px;
  margin: 24px;
  background: #e3e5e8;
}

.survey-survey-questions-container .left-container .survey-fold-icon {
  padding: 4px;
  margin: 0 12px 0 0;
  border-radius: 4px;
  background: #e3e5e8;
}

/* 質問入力コンテナ */
.survey-survey-questions-container .left-container .survey-editing-container .survey-input-container {
  width: 100%;
  margin: 24px 0 0 48px;
  display: flex;
}
.survey-editing-container.section-mltipul .survey-input-container {
  margin: 24px 0 0 24px !important;
}
.survey-survey-questions-container .left-container .survey-editing-container.section-mltipul {
  /* width: 774px; */
  margin-left: 25px;
  padding: 0 24px 20px 0;
  border-radius: 0 4px 4px 4px;
  border: solid 1px #e3e5e8;
  background: #f6f7f9;
}

/* extent setting coupon display 
<AUTHOR>
.item-extend-setting-container {
  display: flex;
  height: 32px;
  margin-top: 12px;
  padding: 8px 12px;
  justify-content: space-between;
  align-items: center;
  align-self: stretch;
  background: #F6F7F9;
}

.display-coupons-label {
  cursor: pointer;
  display: flex;
  height: 24px;
  padding: 3px 12px;
  justify-content: center;
  align-items: center;
  align-content: center;
  flex-wrap: wrap;

  border-radius: 12px;
  background: #E3E5E8;
}

.display-coupons-label.yellow {
  background: #FFF0BB;
}

/* multi-selector modal title */
.common-modal-container>.common-modal-title>h4 {
  line-break: anywhere;
  max-width: calc(100% - 24px);
}

.common-modal-container>.common-modal-title>h4>p {
  width: auto;
}

/* multi-selector modal height flex */
.common-modal-container>.common-modal-content {
  height: calc(80vh - 188px);
}

.common-modal-container>.common-modal-content>.modal-select-container {
  height: 100%;
  max-height: unset;
}
/* extent setting coupon display END*/

/* 質問入力コンテナの順番 */
.survey-survey-questions-container .left-container .survey-editing-container .survey-input-container .survey-editing-order-select {
  width: 60px;
  height: 28px;
  padding: 0 8px;
  margin: 0 25px 0 0;
  border-radius: 4px;
  border: solid 1px #e3e5e8;
  background: #fff;
}

/* 質問のコンテナ */
.survey-survey-questions-container .left-container .survey-editing-container .survey-input-container .survey-editing-input-container {
  width: calc(100% - 100px);
  max-width: 641px;
  padding: 16px 0;
  border-radius: 4px;
  border: solid 1px #e3e5e8;
  background: #fff;
}

/* 質問入力のタイトル　コンテナ */
.survey-survey-questions-container .left-container .survey-editing-container .survey-input-container .survey-editing-input-container .title-container {
  width: 100%;
  display: flex;
  align-items: flex-start;
}

.to-open-summernote-editor {
  position: absolute;
  left: 0;
  bottom: -25px;
  background: #3d3f45;
  color: #fff;
  padding: 4px 8px;
  border-radius:0 0 4px 4px;
  cursor: pointer;
  z-index: 10;
}

.to-open-summernote-editor span {
  padding: 0 0 0 6px;
}

/* 質問タイトルの入力　INPUT */
.survey-title {
  width: 100%;
  max-width: 410px;
  min-height: 32px;
  margin: 0 12px 0 0;
  padding: 0 12px;
  border: none;
  overflow: hidden;
  text-overflow: ellipsis;
  overflow-wrap: break-word;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.survey-title:focus {
  outline: none;
  border: none;
  box-shadow: inset 0 -1px 0 0 #e3e5e8;
}

.option-focus {
  outline: none;
  border: none;
  box-shadow: inset 0 -1px 0 0 #e3e5e8;
}

.survey-title-focus {
  box-shadow: inset 0 -1px 0 0 #e3e5e8;
  background: #f6f7f9;
  min-height: 32px;
  height: fit-content;
  padding: 10px;
}

/* 質問形式　短文 */
.survey-survey-questions-container .short-text-input {
  width: 100%;
  /* width: 593px; */
  height: 32px;
  border: none;
  box-shadow: inset 0 -1px 0 0 #e3e5e8;
  background: #fff;
}

/* 短文　入力形式、最大・最低文字数 メモ */
.survey-survey-questions-container .select-type .short-text-count input,
.survey-short-text-input {
  width: 55px;
  height: 28px;
  padding: 0 6px;
  border-radius: 4px;
  border: solid 1px #e3e5e8;
  background: #fff;
}

/* 長文 */
.long-text-input {
  width: 100%;
  height: 32px;
  border: none;
  box-shadow: inset 0 -1px 0 0 #e3e5e8;
  background: #fff;
  margin: 16px 0 16px 0;
}

.long-text-input:focus {
  box-shadow: inset 0 -1px 0 0 #e3e5e8;
}

/* 添付ファイルのコンテナ */
.survey-attachment-container p {
  width: 100%;
  height: 28px;
  border-radius: 4px;
  border: solid 1px #e3e5e8;
  background: #fff;
}
.survey-attachment-container p span {
  margin: 0 14px 0 16px;
}

/* マトリクス */
.matrix-icon-draggable {
  opacity: 0;
  cursor: move;
  margin-left:2px;
  margin-right:7px;
  margin-bottom:1px;
}

.sortable-matrix {
  margin-left: -26px;
}

.matrix-icon-draggable:hover { 
  opacity: 1;
  cursor: move;
}

.matrix-column-container > .column {
  margin: 36px 8px 16px 2px;
}

.matrix-column-container > .mtx-option-input {
  padding: 8px 0 8px 10px;
}

.matrix-count-max {
  margin-left: -10px !important;
}

/* 質問右下のボタン群 */
.survey-btn {
  width: 44px;
  position: relative;
  display: flex;
  /* justify-content: end; */
  cursor: pointer;
}
.survey-btn * {
  position: relative;
  z-index: 2;
}
.survey-btn::before {
  content: '';
  width: 24px;
  height: 24px;
  position: absolute;
  left: -6px;
  top: -6px;
  border-radius: 4px;
  background-color: rgba(61, 63, 69, 0.25);
  z-index: -1;
}
.survey-btn::after {
  content: "";
  display: inline-block;
  width: 1px;
  height: 16px;
  background: #e3e5e8;
  position: absolute;
  top: -2px;
  right: 16px;
}
.survey-btn:hover::before {
  z-index: 0;
}

/* 「質問を追加する」「セクションを追加する」コンテナ */
.survey-survey-questions-container .left-container .survey-add-container {
  margin: 32px 0 0 48px;
  position: relative;
}
/* セクションの「質問を追加する」コンテナ */
.survey-survey-questions-container .left-container .survey-editing-container .survey-add-container {
  margin: 32px 0 0 0;
}

.btn-add-survey {
  margin: 32px 0 0 48px;
  display: flex;
  align-items: center;
  width: 200px;
  cursor: pointer;
}
.btn-add-survey-multi-section {
  margin: 32px 0 0 24px;
}
.btn-add-survey img,
.btn-add-section img {
  width: 20px;
  height: 20px;
  padding: 4px;
  margin: 0 12px 0 0;
  border-radius: 4px;
  background: #e3e5e8;
}
.btn-add-section {
  margin: 32px 0 0 0;
  display: flex;
  align-items: center;
  width: 200px;
  cursor: pointer;
}

/* 分岐条件モーダルの右上の削除アイコン */
.icon-delete-branch {
  margin: 0 24px 0 auto;
  cursor: pointer;
}

/* 分岐条件のコンテナ */
.survey-setting-dest-user,
.survey-branch-modal-container {
  min-width: 787px;
  max-width: 1000px;
  min-height: 600px;
  max-height: 100%;
  padding: 25px 38px 0 38px;
  margin: 0;
  border-radius: 4px;
  /* overflow-y: scroll; */
  -ms-overflow-style: none;    /* IE, Edge 対応 */
  scrollbar-width: none;       /* Firefox 対応 */
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  
  z-index: 999;
  background: #fff;
}

.survey-branch-modal-container::-webkit-scrollbar {  /* Chrome, Safari 対応 */
  display:none;
}

.survey-survey-questions-container .left-container .survey-questions-jump-section-setting-container.rich-text-editor {
  width: 860px;
  max-width: 80vh;
  height: fit-content;
}
.survey-branch-container {
  /* min-height: 244px; */
  height: fit-content;
  margin: 10px 0 0 0;
  border-radius: 4px;
  background: #f6f7f9;
  position: relative;
  padding: 0 0 24px 0;
}

.add-branch-pulldown-container {
  margin: 12px 0;
  display: flex;
}

/* 「分岐条件を追加する」ボタン */
.btn-add-branch {
  /* width: 713px; */
  height: 44px;
  margin: 12px 0 0 0;
  padding: 16px 24px;
  border-radius: 4px;
  background: #f6f7f9;
  display: flex;
  align-items: center;
  cursor: pointer;
}

.survey-setting-dest-user .container {
  width: 100%;
  height: 458px;
  padding: 0;
  display: flex;
}

.survey-setting-dest-user .select {
  width: 351px;
  height: 458px;
  margin: 0 21px 0 0;
  background: #f6f7f9;
}
.survey-setting-dest-user .select ul {
  max-height: 406px;
  padding: 0 0 15px 0;
  display: flex;
  flex-wrap: wrap;
  overflow-y: scroll;
}
.survey-setting-dest-user .select div {
  overflow-y: scroll;
  height: calc(100% - 52px);
}
/* .survey-setting-dest-user .select div::-webkit-scrollbar div {
  width: 6px;
} */

.table-search::-webkit-scrollbar {
  width: 6px;
}

.survey-setting-dest-user .title {
  margin: 0 auto 20px 0;
}

.survey-setting-dest-user .select ul::-webkit-scrollbar-track, 
.survey-setting-dest-user .selected ul::-webkit-scrollbar-track { 
  background: transparent;
  border: none;
}

.survey-setting-dest-user .select ul::-webkit-scrollbar-thumb,
.survey-setting-dest-user .selected ul::-webkit-scrollbar-thumb {
  border-radius: 8px;
  background: rgba(61, 63, 69, 0.24);
}

.survey-setting-dest-user .select .select-header {
  width: 100%;
  height: 52px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  box-shadow: 0 1px 0 0 #e3e5e8;
}

.survey-setting-dest-user .select .select-header .survey-search-dest-user {
  width: 319px;
  height: 28px;
  padding: 8px 12px 8px 10px;
  border-radius: 4px;
  border: solid 1px #e3e5e8;
  background: #fff;
}

.survey-setting-dest-user .select .select-header .icon-search {
  position: absolute;
  right: 20px;
}

.survey-setting-dest-user .select .survey-select-user {
  width: 43%;
  padding: 8px 0 8px 0;
  margin: 0 0 0 20px;
  display: flex;
  align-items: center;
}

.survey-setting-dest-user .select .uncheck {
  min-width: 12px;
  height: 12px;
  border-radius: 2px;
  border: solid 1px #c8cace;
  background: #fff;
  display: inline-block;
}

.survey-setting-dest-user .selected {
  width: 351px;
  height: 457px;
  border: solid 1px #e3e5e8;
  background: #fff;
}
.survey-setting-dest-user .selected h1 {
  height: 12px;
  margin: 12px 0 26px 20px;
  font-family: HiraginoSans-W3;
  font-size: 12px;
}

.selected-user-list {
  overflow-y: scroll;
  cursor: pointer;
  max-height: 406px;
}

.survey-setting-dest-user .selected .js-selected-user-list {
  margin: 18px 0 18px 20px;
}

.survey-setting-dest-user .selected .js-selected-user-list img {
  margin: 0 8px 0 0;
}

.show-selected-user {
  line-height: 24px;
  margin: 0 12px 0 0;
  background: #d3eeff;
  border-radius: 12px;
  padding: 0 12px !important;
  display: flex;
  flex-wrap: wrap;
}

/* アンケート質問　終了 */

.survey-header-image-modal-delete-icon {
  width: 50px;
  display: flex;
  justify-content: center;
}

/* セクションの分岐条件　質問のプルダウン */

/* アンケート一覧の分類タグ検索コンテナ */
.bs-select.form-control.surveys-pulldown button {
  height: 28px;
}

/* WYSIWYGエディタ　summernote Summernote */
.note-color-row button {
  height: 20px;
  width: 20px;
  border: none;
  margin: 1px;
  border-radius: 0px;
}

.note-color .note-recent-color {
  padding: 0;
  margin: 0;
  display: flex;
  align-items: center;
}
.note-color .note-recent-color i {
  padding: 0;
  margin: 0;
  width: 30px;
  height: 27px;
  border-radius: 2px 0px 0px 2px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.note-editable {
  overflow: scroll;
  padding: 16px 12px;
  max-height: calc(100vh - 300px);
  /* white-space: pre-wrap; */
  /* word-wrap: break-word; */
}

.note-editable:focus {
  box-shadow: none;
}

.btn-toolbar {
  margin: 0;
}

.note-toolbar.btn-toolbar {
  padding: 8px 10px;
  background: #f5f5f5;
}

.note-editor {
  margin: 0 0 100px 0;
}

.note-color-reset {
  text-align: center;
}

.note-image-dialog .modal-content,
.note-link-dialog .modal-content {
  height: 336px;
  padding: 16px 15px;
}

.note-link-dialog .row-fluid,
.note-image-dialog .row-fluid {
  width: 90%;
  margin: 0 0 0 30px;
}

.note-image-dialog .modal-content .form-group input,
.note-link-dialog .modal-content .form-group input {
  margin: 0 0 16px 0;
  width: 90%;
}

.note-link-dialog .modal-content .form-group {
  margin: 0;
}

.note-link-dialog .modal-content .checkbox label {
  display: flex;
  align-items: center;
}

.note-link-dialog .modal-content .checkbox label input {
  margin: 0 0 0 -20px;
}

.note-image-dialog .modal-header,
.note-image-dialog .modal-footer,
.note-link-dialog .modal-header,
.note-link-dialog .modal-footer {
  border: none;
}

.note-image-dialog .modal-footer,
.note-link-dialog .modal-footer {
  text-align: left;
}

.note-image-dialog .modal-footer button.disabled,
.note-link-dialog .modal-footer button.disabled {
  background: #ebedf2;
}
.note-image-dialog .modal-footer button,
.note-link-dialog .modal-footer button {
  background: #245BD6;
  margin: 0 0 0 30px !important;
  color: #fff;
}

.tooltip.fade.bottom.in {
  display: none !important;
}

.note-dropdown-menu .dropdown-menu .dropdown-style {
  min-width: 175px !important;
}

.note-popover .popover .popover-content .note-para .dropdown-menu, .note-toolbar .note-para .dropdown-menu {
  width: 238px;
}
.note-style.btn-group .dropdown-menu {
  min-width: fit-content;
  max-width: -moz-fit-content;
}

.dropdown > .dropdown-menu:before, .dropdown-toggle > .dropdown-menu:before, .btn-group > .dropdown-menu:before,
.dropdown > .dropdown-menu:before, .dropdown-toggle > .dropdown-menu:before, .btn-group > .dropdown-menu:after {
  content: none !important;
}

.dropdown-menu {
  box-shadow: 1px 2px 8px 0 rgba(61, 63, 69, 0.25) !important;
}

.note-fontsizes.btn-group ul {
  min-width: fit-content;
  min-width: -moz-fit-content;
}

/* プレビュー部分 */
.inquiry-preview-footer {
  width: 320px;
  height: 55px;
  border-radius: 0 0 10px 10px;
  border: solid #e3e5e8;
  border-width: 0 1px 1px 1px;
  background: #fff;
}

.inquiry-preview-main {
  box-shadow: inset 0 -1px 0 0 #e3e5e8;
  border: solid #e3e5e8;
  border-width: 0 1px 1px 1px;
  width: 320px;
  height: 495px;
  overflow-y: scroll;
  border-radius: 0 0 10px 10px;

  -ms-overflow-style: none;    /* IE, Edge 対応 */
  scrollbar-width: none;       /* Firefox 対応 */
}
.inquiry-preview-main::-webkit-scrollbar {  /* Chrome, Safari 対応 */
  display:none;
}

.form-description-input {
  -ms-overflow-style: none;    /* IE, Edge 対応 */
  scrollbar-width: none;       /* Firefox 対応 */
}
.form-description-input::-webkit-scrollbar {  /* Chrome, Safari 対応 */
  display:none;
}
/* 説明部分 */

/* ::before */
.submit-admin-button {
  position: relative;
}

.submit-admin-button::before {
  content: "";
  display: block;
  width: 777px;
  height: 1px;
  background-color: #e3e5e8;
  position: absolute;
  top: -12px;
  left: -38px;
}

.public-url-link {
  display: block;
  width: 90%;
  white-space: pre-wrap;
  word-wrap: break-word;
  color: #000000;
  cursor: pointer;
}

/* 送信完了　プレビュー　送信ボタン */
.complete-submit-button {
  width: 189px !important;
  height: 43px !important;
  margin: 27px auto 34px auto !important;
}

.page-sidebar-menu img,
.page-content-header img {
  display: initial !important;
}

.desc-header-container .survey-space-top-bottom-2 {
  margin: 0 !important;
  padding: 0 0 0 10px !important;
  width: 0px;
  min-width: 109px;
}

.modal-container-context {
  height: 420px;
  overflow-y: scroll;
  bottom: 20px;
  border: 1px solid #e3e5e8;
  padding: 15px;
  -ms-overflow-style: none;    /* IE, Edge 対応 */
  scrollbar-width: none;       /* Firefox 対応 */
}

.modal-container-context::-webkit-scrollbar {  /* Chrome, Safari 対応 */
  display:none;
}

/* フォーカスアウト お名前、住所 */
.survey-name-container.focusout .name-full-container,
.survey-name-container.focusout .name-separate-container,
.survey-address-container.focusout {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
.survey-address-container.focusout > div.country {
  width: 100%;
  padding: 0 50% 0 0;
}
.survey-name-container.focusout .name-full-container > div,
.survey-name-container.focusout .name-separate-container > div,
.survey-address-container.focusout > div {
  display: block;
  width: 50%;
}
.survey-name-container.focusout .name-full-container > div > div,
.survey-name-container.focusout .name-full-container > div > input,
.survey-name-container.focusout .name-separate-container > div > div,
.survey-name-container.focusout .name-separate-container > div > input,
.survey-address-container.focusout > div > div,
.survey-address-container.focusout > div > input {
  width: 90%;
  background: #fff;
}

.survey-name-container.focusout .name-full-container > div > div,
.survey-name-container.focusout .name-full-container > div > input:nth-child(1),
.survey-name-container.focusout .name-separate-container > div > div,
.survey-name-container.focusout .name-separate-container > div > input:nth-child(1)  {
  padding: 0 !important;
}
.survey-name-container.focusout .name-full-container > div > input:nth-child(2),
.survey-name-container.focusout .name-separate-container > div > input:nth-child(2),
.survey-address-container.focusout > div > input:nth-child(2) {
  border-radius: 2px;
  border: solid 1px #e3e5e8;
}
.survey-name-container.focusout .dropdown-container {
  display: none;
}
/* 住所用 */
.survey-address-container.focusout > :last-child {
  width: 100%;
}
.survey-address-container.focusout > :last-child > input {
  width: 95%;
  max-width: initial;
}

.placeholder-input {
  border: none;
  width: 83%;
}
.placeholder-input:focus {
  border: none;
  box-shadow: none;
}

/* SCR */
.js-scr-container .evaluation-box {
  grid-column-gap: 2px;
  background: #F6F7F9;
  border: 1px solid #E3E5E8;
  display: flex;
  gap: 7px;
  padding: 5px 7px 5px 8px;
  margin: 0 16px;
  font-size: 12px;
}

.js-scr-container .evaluation-box span:first-child{
  width: 12px;
  text-align: center;
}

/* クーポン追加用*/
.js-coupon-setting,
.js-add-coupon {
    padding: 12px 20px;
    margin: 24px 0 0 48px;
    font-size: 12px;
    border-radius: 4px;
    background: #f6f7f9;
}

.js-coupon-setting .selected_icon {
    margin-right: 10px;
    width: 25px;
    height: 25px;
}

.description-section-header {
  padding-top: 18px;
  margin-top: 18px;
  position: relative;
  gap: 52px;
}

.description-section-header::before {
  content: "";
  display: block;
  width: 100%;
  height: 1px;
  background: #e3e5e8;
  position: absolute;
  top: 0;
  left: 26px;
}