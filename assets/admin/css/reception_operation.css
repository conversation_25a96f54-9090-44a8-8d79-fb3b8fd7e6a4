html {
    touch-action: manipulation;
}

:root {
    --talkappi-blue: #245BD6;
    --activalues-red: #E53361;
    --solid-orange: #FF9551;
    --solid-green: #32CE55;
    --gray-white: #F6F7F9;
    --pale-gray: #E3E5E8;
    --common-gray: #C8CACE;
}

.page-content-wrapper .page-content {
    padding: 0 !important;
    margin-left: unset !important;
}

.line-tab li {
    padding: 0px 24px;
}

/* ヘッダーとメニューサイドバーを非表示 */
.page-content-header,
.page-sidebar-wrapper,
.page-footer {
    display: none;
}

/* ページ内のデザイン */
.reception-content-container {
    overflow: scroll;
    height: calc(100vh);
    min-height: 100vh;
    width: 100vw;
    background-color: white;
    padding: 20px;
    display: flex;
    flex-direction: column;
    padding-bottom: 14em;
}

.reception-info-wrapper {
    display: flex;
    gap:12px;
    justify-content: space-between;
    font-size: 1.5em;
}

.reception-header-title {
    text-align: center;
    font-weight: bold;
    line-height: 1.0;
    margin: 2em;
}

.reception-title-ja {
    display: block;
    font-size: 3em;
    letter-spacing: normal;
}

.reception-title-en {
    display: block;
    font-size: 2em;
}

.reception-info-wrapper .reception-info-content {
    display: flex;
    height: 52px;
    background-color: var(--gray-white);
    padding: 12px;
    border-radius: 4px;
    width: 100%;
    height: 4em;
}

.reception-info-wrapper .reception-info-title {
    align-self: center;
    margin-right: 1em;
}

.reception-info-wrapper .reception-info-detail {
    align-self: center;
    margin: 0 0 0 auto;
    font-weight: 500;
    color: var(--solid-orange);
    font-size: 24px;
}

.reception-option-wrapper .reception-info-content {
    display: flex;
    height: 52px;
    background-color: var(--gray-white);
    margin: 6px;
    padding: 12px;
    border-radius: 4px;
    width: 100%;
    height: 6em;
}

.receipt-button {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    position: fixed;
    bottom: 0;
    left: 0;
    padding: 0.3em;
    width: 100%;
    font-size: 3em;
    letter-spacing: 0.2em;
    text-align: center;
    font-weight: bold;
}

.receipt-text-ja {
    font-size: 1em;
    font-weight: bold;
}

.receipt-text-en {
    font-size: 0.6em;
}

.receipt-button.accept{
    pointer-events: auto;
    color: white;
    background: var(--solid-green);
}

.receipt-button.pause{
    pointer-events: none;
    color: white;
    background: var(--common-gray);
}

/* ローディング・通信画面 */
.loading-container {
    display: none; /* デフォルト非表示 */
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.7);
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-container .loading {
    display: none; /* デフォルト非表示 */
    align-items: center;
    flex-direction: column;
}

.loading-container .loading .loading-status {
    font-size: 2.5em;
    margin: 1em;
}

.loading-container .loading>img {
    height: 100px;
    width: 100px;
}

.loading-container .loading.error .back-button {
    padding: 16px 40px;
    font-size: 32px;
    border: 3px solid #3D3F45;
    border-radius: 99px;
    cursor: pointer;
}

.capacity-container .title-ja, .entry-container .title-ja {
    font-size: 24px;
}

.capacity-container .title-en, .entry-container .title-en {
    font-size: 20px;
}

.capacity-container {
    display: flex;
    padding: 1.25rem 2.5rem;
    background-color: var(--gray-white);
    justify-content: space-between;
    margin: 20px 0px;
    border-radius: 8px;
}

.capacity-container .counter-num {
    margin: auto 32px;
    font-size: 32px;
    font-weight: 600;
}

.capacity-container > .title {
    font-size: 32px;
    margin: auto 0;
    display: flex;
    align-items: start;
    gap: 6px;
}

/* reception_entry 共通コンテナスタイル */
.entry-container {
    padding: 1.25rem 2.5rem;
    background-color: var(--gray-white);
    margin: 20px 0px;
    border-radius: 8px;
}

/* タイトル全体のラッパー */
.entry-title-wrapper {
    display: flex;
    align-items: flex-start;
    gap: 8px; /* 必須マークとタイトルの間隔 */
}

/* 必須マーク */
.entry-required-label {
    color: var(--activalues-red);
    font-size: 20px;
    margin-top: 4px; /* 必須マークの位置調整 */
}

/* 日本語の選択肢 */
.entry-option-ja {
    font-size: 20px;
}

/* 英語の選択肢（小さくする） */
.entry-option-en {
    font-size: 16px;
}

/* ========================= */
/* 画像付きのラジオボタン選択肢 */
/* ========================= */

.entry-option-text--image {
    display: flex;
    flex-direction: column; /* 縦に並べる */
    align-items: start; /* 中央揃え */
    margin-top: 12px;
}

.radio-group--image {
    display: grid;
    grid-template-columns: repeat(2, 1fr); /* 2列レイアウト */
}

.radio-item--image {
    display: flex;
    flex-direction: column;
    align-items: start;
    cursor: pointer;
    padding: 12px;
    border: 2px solid transparent;
}

/* 標準のラジオボタンを隠す */
.radio-item--image input[type="radio"] {
    display: none;
}

/* 選択時のスタイル（画像付き） */
.radio-item--image:has(input[type="radio"]:checked) {
    background-color: #CDEFE9;
    border-color: #1CA38B;
    border-radius: 4px;
}

/* 画像 */
.radio-item-image {
    width: 100%;
    aspect-ratio: 3 / 2;
    object-fit: cover;
}

/* 画像付きの選択肢テキスト */
.radio-item--image .radio-item-text {
    font-size: 24px;
    margin-top: 12px;
}

/* ========================= */
/* 画像なしのラジオボタン（ボタンスタイル） */
/* ========================= */

.entry-option-text--button {
    display: flex;
    flex-direction: column; /* 縦に並べる */
    align-items: center; /* 中央揃え */
    gap: 4px;
}

.radio-group {
    display: flex;
    flex-wrap: wrap;
    gap: 0px 24px;
}
.radio-item--button {
    display: inline-block;
    padding: 16px 24px;
    margin-top: 20px;
    margin-bottom: 0px;
    font-size: 24px;
    background-color: #E3E5E8;
    border-radius: 16px;
    cursor: pointer;
}

/* ボタン型ラジオの非表示 */
.radio-item--button input[type="radio"] {
    display: none;
}

/* 選択時のスタイル（ボタン型） */
.radio-item--button:has(input[type="radio"]:checked) {
    background-color: #1b1b1e;
    color: white;
}

.error-reporter-wrapper {
    display: none;
    position: fixed;
    width: 100%;
    height: 60px;
    bottom: 120px;
    left: 0;
    right: 0;
    justify-content: center;
    align-items: center;
    gap: 24px;
}
.error-reporter-wrapper .error-reporter-message {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 24px;
    color: #E53361;
}
.error-reporter-wrapper .error-reporter-message::before {
    content: '';
    display: block;
    width: 32px;
    height: 32px;
    background-image: url(/assets/admin/images/inquiry-error.svg);
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
}
.error-reporter-wrapper .error-handler-button {
    padding: 4px 12px;
    display: flex;
    gap: 6px;
    align-items: center;
    background-color: #E3E5E8;
    border-radius: 999px;
    font-size: 16px;
    cursor: pointer;
}
.error-reporter-wrapper .error-handler-button::before {
    content: '';
    display: block;
    width: 16px;
    height: 16px;
    background-image: url(/assets/admin/images/icon-refresh.svg);
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}

#connecting, #reconnecting {
    animation: rotate 2s linear infinite;
    transform-origin: center;
}

.text-entry {
    border: 1px solid #E3E5E8;
    transition: border 0.2s ease-in-out;
}

.text-entry:focus {
    border: 1px solid #000;
    outline: none;
}