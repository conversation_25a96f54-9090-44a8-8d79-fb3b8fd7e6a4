@charset "utf-8";

.txt_column_head_content {
  font-family: 'Hiragino Sans';
  font-style: normal;
  font-weight: 300;
  font-size: 12px;
  line-height: 18px;
  /* identical to box height */
  color: #000000;
  
  word-break: keep-all;
}

.modal_content_medium_float_panel {
  /* タグ追加 */
  /* Auto layout */
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 24px;
  gap: 24px;

  position: relative;
  /* width: 516px; */
  width: 636px;
  /* height: 212px; */

  /* pure white */

  background: #FFFFFF;
  border-radius: 4px;
}

/* .upload_frame_1301 { */
.container_item_in_float_panel_frame {
  flex-direction: column;
  align-items: flex-start;
  padding: 0px;
  gap: 6px;

  /* width: 806px; */
  height: 52px;
  /* Inside auto layout */
  flex: none;
  /* order: 1; */
  align-self: stretch;
  /* flex-grow: 1; */
}
.rows_flex {
  display: flex;
  height: auto;
  gap: 12px;
}

.top-shadow {
  /* inner shadow top 1px */
  box-shadow: inset 0px 1px 0px #EBEDF2;
}

.txt_title_float_panel {
  /* CSVから入力 */
  /* width: 94px; */
  height: 23px;

  font-family: 'Hiragino Sans';
  font-style: normal;
  font-weight: 500;
  font-size: 15px;
  line-height: 22px;
  /* identical to box height */
  color: #000000;
  /* Inside auto layout */
  flex: none;
  order: 0;
  flex-grow: 0;
}
.txt_count_title_big_float {
  font-weight: 400;
  font-size: 14px;
  line-height: 21px;
  /* identical to box height */
  color: #000000;
  /* Inside auto layout */
  flex: none;
  flex-grow: 0;
}
.csv-modal-title {
  /* Frame 1323 */
  /* Auto layout */
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-start;
  padding: 0px;
  gap: 24px;

  /* width: 806px; */
  height: 24px;
  /* Inside auto layout */
  flex: none;
  order: 0;
  align-self: stretch;
  flex-grow: 0;
}

.csv-modal-title.stretch {
  align-items: stretch;
  /* width: 806px; */
}

.report_header_container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 0px;
  gap: 12px;
}

.table-header-pulldown {
  display: flex;
}

.row_tag_opt {
  /* Frame 1335 */
  /* Auto layout */
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0px;
  /* gap: 12px; */
  gap: 6px;

  /* width: 98px; */
  height: 20px;
  /* Inside auto layout */
  flex: none;
  order: 1;
  flex-grow: 0;
}
.tags_group>.row_tag_opt {
  gap: 0px;
}

.tags_group>.row_tag_opt>.txt_tag_gray {
  background: none;
}

.btn_tag_w_ico>.txt_tag_gray {
  background: none;
}


.txt_title_small {
  /* 重要指標 */
  /* width: 48px;
  height: 18px; */
  font-family: 'Hiragino Sans';
  font-style: normal;
  font-weight: 400;
  font-size: 12px;
  line-height: 18px;
  /* identical to box height */
  color: #000000;
  /* Inside auto layout */
  flex: none;
  /* order: 0; */
  flex-grow: 0;
}
.txt_label_small {
  /* width: 48px; */
  height: 18px;

  font-family: 'Hiragino Sans';
  font-style: normal;
  font-weight: 300;
  font-size: 12px;
  line-height: 18px;
  /* identical to box height */
  /* talkappi dark grey */
  color: #3D3F45;
  /* Inside auto layout */
  flex: none;
  /* order: 0; */
  flex-grow: 0;
}
.txt_value {
  /* width: 212px; */
  height: 18px;

  font-family: 'Hiragino Sans';
  font-style: normal;
  font-weight: 300;
  font-size: 12px;
  line-height: 18px;
  /* identical to box height */
  /* pure black */
  color: #000000;
  /* Inside auto layout */
  flex: none;
  /* order: 1; */
  flex-grow: 0;
}
.txt_value.title {
  height: 23px;
  font-size: 15px;
  line-height: 22px;
}

.txt_tag_blue {
  /* 追加 */
  /* width: 33px;
  height: 17px; */
  font-family: 'Hiragino Sans';
  font-style: normal;
  font-weight: 300;
  font-size: 11px;
  line-height: 16px;
  /* identical to box height */
  color: #000000;
  /* Inside auto layout */
  flex: none;
  order: 0;
  flex-grow: 0;
}

/* report related above */

.txt_tag_gray {
  /* text */
  /* Auto layout */
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 1px 7px;

  /* width: 74px; */
  height: 20px;
  /* pale grey */
  background: #EBEDF2;
  /* pure black */
  color: #000000;
  border-radius: 2px;
  /* Inside auto layout */
  flex: none;
  /* order: 1; */
  flex-grow: 0;
}

.txt_paging {
  /* limit_suffix 件/ページに表示 */
  /* total/start/limit 364 件中 1 から 25 まで表示 */
  /* width: 89px;
  width: 165px; */

  height: 18px;

  font-family: 'Hiragino Sans';
  font-style: normal;
  font-weight: 400;
  font-size: 12px;
  line-height: 18px;
  /* identical to box height */
  /* letter-spacing: -0.0857143px; */
  color: #000000;
  /* Inside auto layout */
  flex: none;
  /* order: 1; */
  flex-grow: 0;
}

.table_action_container {
  /* Frame 1345 */
  /* Auto layout */
  display: flex;
  flex-direction: row;
  align-items: center;
  /* padding: 0px; */
  padding: 55px 0px 0px 0px;
  gap: 12px;

  width: 349px;
  height: 28px;
  /* Inside auto layout */
  flex: none;
  order: 0;
  flex-grow: 0;
}

.group_btn_large_general_frame {
  /* Frame 53 */
  /* Auto layout */
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  padding: 0px;
  gap: 12px;

  width: 228px;
  height: 40px;
  /* Inside auto layout */
  flex: none;
  /* order: 5; */
  flex-grow: 0;
}

.pure_white_diff {
  /* pure white */
  background: #FFFFFF;
}
.txt_page_num_diff {
    width: 9px;
    height: 18px;
    /* identical to box height */
    text-align: center;
}


.tab {
  /* tab */
  box-sizing: border-box;
  /* Auto layout */
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 0px 1px;

  /* width: 54px; */
  /* width: 66px; */
  height: 29px;
  /* pure white */
  background: #FFFFFF;
  /* inactive tab
  grey white
  background: #F6F7F9; */
  /* pale grey for line */
  /* border: 1px solid #E3E5E8; */
  border-radius: 4px 4px 0px 0px;
  /* Inside auto layout */
  flex: none;
  /* order increasing */
  order: 0;
  flex-grow: 0;
}
.tab_bar_text {
  /* Frame 60 */
  /* Auto layout */
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 5px 8px;
  /* padding: 5px 8px 4px; */

  /* width: 52px; */
  /* width: 64px; */
  height: 28px;
  /* Inside auto layout */
  flex: none;
  order: 0;
  flex-grow: 0;
}
.tab.inactive {
  background: #F6F7F9;
  cursor: pointer;
}
.tab.inactive>.tab_bar_text {
  padding: 5px 8px 4px;
}
.tab.inactive .txt_tab {
  font-weight: 300;
  color: #3D3F45;
}
.tab.inactive>.tab-bar-shadow {
  background: #E3E5E8;
}

.tab-bar-shadow {
  /* Rectangle 18 */
  /* width: 52px; */
  /* width: 64px; */
  height: 1px;
  /* pure white */
  background: #FFFFFF;
  /* inactive tab
  pale grey for line
  background: #E3E5E8; */
  /* Inside auto layout */
  flex: none;
  order: 1;
  align-self: stretch;
  flex-grow: 0;
}
.txt_tab {
  /* tab off */
  /* width: 36px; */
  height: 18px;

  font-family: 'Hiragino Sans';
  font-style: normal;
  font-weight: 400;
  font-size: 12px;
  line-height: 18px;
  /* identical to box height */
  text-align: center;
  /* pure black */
  color: #000000;
  flex: none;
  order: 0;
  flex-grow: 0;
}


.row_sparse {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-start;
  /* flex: none; */
}
.column_title_content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 20px 24px;
  gap: 12px;
  border-radius: 4px;

}
.dashboard-num-container {
  /* Frame 1457 */
  /* Auto layout */
  display: flex;
  padding: 0px;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: 8px;
}
.row_item {
  /* Frame 1447 */
  /* Auto layout */
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0px;
  gap: 12px;

  /* width: 344px; */
  height: 28px;
  /* Inside auto layout */
  flex: none;
  /* order: 0; */
  flex-grow: 0;
}
.row_item.multi {
  height: auto;
  align-items: flex-start;
}
.wrap>.row_item {
  width: 492px;
  gap: 6px;
  /* width: 50%; */
  /* flex-grow: 1; */
}
.row_item>a.txt_key {
  font-weight: 500 !important;
  /* identical to box height */
  text-decoration-line: underline;
}
.row_item>a.label {
  font-weight: default !important;
  /* identical to box height */
  text-decoration-line: none;
  color: #000000;
}
.row_item>.txt_key {
  font-weight: 300;
  font-size: 15px;
  color: #000000;
}
.column_item {
  /* Frame 1451 */
  /* Auto layout */
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  padding: 0px;
  gap: 8px;
}

.dashboard.card-container {
  margin: unset;
}

.dashboard.card {
  padding: unset;
}

.dashboard_frame {
  /* Frame 171~174 */
  /* Auto layout */
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 2px 16px;

  gap: 4px;

  /* pure white */
  background: #FFFFFF;
  border-radius: 4px;
  /* Inside auto layout */
  flex: none;
  flex-grow: 1;
}

.row_dashboard_label {
  /* Frame 167 */
  /* Auto layout */
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0px;
  gap: 12px;
  /* width: 238px;
  height: 21px; */
  /* Inside auto layout */
  flex: none;
  /* order: 1; */
  align-self: stretch;
  flex-grow: 0;
}

.txt_indic {
  /* 2755 */
  /* width: 54px;
  height: 41px; */
  padding: 10px 0px;

  font-family: 'SF Compact Display';
  font-style: normal;
  font-weight: 700;
  font-size: 32px;
  line-height: 41px;
  /* identical to box height, or 128% */
  letter-spacing: -1px;
  /* talkappi dark grey */
  color: #3D3F45;
  /* Inside auto layout */
  flex: none;
  /* order: 0; */
  flex-grow: 0;
}

.txt_indic.pct {
  font-weight: 300;
  font-size: 28px;
}

.txt_label_indic_i18n {
  /* 利用者数 */
  /* width: 185px;
  height: 21px; */
  font-family: 'Hiragino Sans';
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 21px;
  /* identical to box height */
  /* talkappi dark grey */
  color: #3D3F45;
  /* Inside auto layout */
  flex: none;
  /* order: 0; */
  flex-grow: 1;
}

.txt_label_indic {
  /* CUSTOMERS */
  /* width: 238px;
  height: 18px; */
  padding: 9px 0px 0px;
  /* padding: 18px 0px; */

  font-family: 'Hiragino Sans';
  font-style: normal;
  font-weight: 300;
  font-size: 12px;
  line-height: 18px;
  /* identical to box height */
  /* talkappi dark grey */
  color: #3D3F45;
  /* Inside auto layout */
  flex: none;
  /* order: 0; */
  align-self: stretch;
  /* flex-grow: 0; */
  flex-grow: 1;
}

.line_dotted {
  /* Vector 160 */
  /* width: 312px;
  height: 0px; */
  /* common grey */
  border: 1px dashed #C8CACE;
  /* Inside auto layout */
  flex: none;
  /* order: 1; */
  flex-grow: 1;
}
.color_label_status {
  /* text */
  /* Auto layout */
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  /* padding: 1px 7px; */
  padding: 1px 6px;
  /* with ico
  padding: 1px 3px 1px 7px;
  gap: 4px; */

  /* width: 38px; */
  /* width: 50px; */
  height: 20px;

  /* light blue */
  background: #D3EEFF;
  word-break: keep-all;
  border-radius: 2px;

  /* Inside auto layout */
  flex: none;
  order: 1;
  flex-grow: 0;
}
.color_label_status.txt_tag_blue {
  height: 19px;
}
.color_label_status.disabled {
  /* pale grey */
  background: #EBEDF2;
}
.tag_basic_flex {
  /* text */
  /* Auto layout */
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 1px 7px;
  /* width: 74px; */
  height: 20px;
  /* pale grey */
  background: #EBEDF2;
  border-radius: 2px;
  /* Inside auto layout */
  flex: none;
  order: 0;
  flex-grow: 0;
}

.tag_basic {
  padding: 2px 6px;
  background: #EBEDF2;
  border-radius: 2px;
}

tr.project>td .tag_basic {
  background: #D3EEFF; 
  /* light blue */
}

td>.inline {
  display: inline;
}

.button_container {
  /* Frame 1344 */
  /* Frame 1450 */
  /* Auto layout */
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  padding: 0px;
  gap: 12px;

  /* width: 227px; */
  height: 28px;
  /* Inside auto layout */
  flex: none;
  order: 1;
  flex-grow: 0;
}

/* return button in report detail page and result page */
.btn_wrapper {
  /* Frame 1441 */
  /* Auto layout */
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 24px;

  /* width: 124px;
  height: 89px; */
  /* Inside auto layout */
  flex: none;
  /* order: 4; */
  flex-grow: 0;
}

/* width:461px; height: 30px;flex-wrap: wrap;gap: 3px; */
.input_tags {        
  height: auto !important;
  width: 461px !important;
  flex-wrap: wrap !important;
  gap: 3px  !important;
  border: 1px solid #E3E5E8;
  border-radius: 4px;
  flex-grow: 1 !important;
}

input.borderless {
  /* padding: 1px; */
  border: none;
  position: absolute;
  width: 100%;
  height: 100%;
  inset: 0px;
  z-index: 1;
}

.btn_small_white {
  /* small */
  box-sizing: border-box;
  /* Auto layout */
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 5px 12px;

  /* width: 72px; */
  height: 28px;
  /* pure white */
  background: #FFFFFF;
  /* common grey */
  border: 1px solid #C8CACE;
  border-radius: 4px;
  /* Inside auto layout */
  flex: none;
  order: 1;
  flex-grow: 0;
}

.btn_label_round {
  /* text label small */
  /* Auto layout */
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 3px 10px 3px 8px;
  /* width: 94px; */
  height: 24px;
  /* light blue */
  background: #EBEDF2;
  border-radius: 12px;
  /* Inside auto layout */
  flex: none;
  order: 1;
  flex-grow: 0;
}

.btn_label_w_ico {
  /* text label small */
  /* Auto layout */
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 3px 10px 3px 8px;
  gap: 4px;
  /* width: 94px; */
  height: 24px;
  /* light blue */
  background: #D3EEFF;
  border-radius: 12px;
  /* Inside auto layout */
  flex: none;
  order: 1;
  flex-grow: 0;
  
  cursor: pointer;
}

.btn_w_ico {
  /* button with icon */
  /* Auto layout */
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 5px 12px 5px 10px;
  gap: 6px;
  height: 28px;
  background: #245BD6;
  border-radius: 4px;
  /* Inside auto layout */
  flex: none;
  flex-grow: 0;
}

.btn_w_ico.vivid.red {
  /* ActiValues red */
  background: #E53361;
}

.btn_w_ico.vivid {
  /* pure white */
  color: #FFFFFF;
}

.btn_w_ico.disabled {
  /* common grey */
  background: #C8CACE;
  border: none ;
}

.btn_w_ico.pale_grey {
  /* pale grey */
  background: #EBEDF2 !important;
  border: none !important;
}

.btn_tag_w_ico {
  /* text */
  /* Auto layout */
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 1px 6px;
  gap: 2px;

  /* width: 50px; */
  height: 20px;
  /* pale grey */
  background: #EBEDF2;
  border-radius: 2px;
  /* Inside auto layout */
  flex: none;
  order: 0;
  flex-grow: 0;
}

tr.project>td>span.btn_tag_w_ico {
  background: #D3EEFF;
  /* light blue */
}

.btn_tag_w_ico.cancel {
  padding: 1px 3px 1px 7px;
  gap: 4px;
}
.btn_tag_w_ico.round {
  border-radius: 12px;
}
.btn_tag_w_ico.cancel.attach {
  /* light green */
  background: #CFF2D7;
}

.btn_tag_w_ico.detach {
  /* light red */
  background: #FFDCE5;
  text-decoration: line-through;
}

.txt_btn_w_ico {
  /* 追加 */
  /* width: 48px;
  width: 63px;72px */
  height: 18px;
  font-size: 12px;
  line-height: 18px;
  /* identical to box height */
  text-align: center;
  /* pure white */
  color: #FFFFFF;
  /* pure black */
  /* color: #000000; */
  /* Inside auto layout */
  flex: none;
  /* order: 1; */
  flex-grow: 0;
  
  /* cursor: pointer; */
}

.btn_w_ico.pale_grey>.txt_btn_w_ico {
  /* pure black */
  color: #000000;
}


.btn_extra_small_blue {
  /* extra small */
  /* Auto layout */
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-self: center;
  padding: 1px 4px;

  height: 19px;
  /* talkappi blue */
  background: #245BD6;
  /* pure white */
  color: #FFFFFF;

  border-radius: 4px;
  /* Inside auto layout */
  flex: none;
  flex-grow: 0;
  /* cursor: pointer; */
}

.btn_large_blue {
  /* large */
  /* Auto layout */
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 10px 24px 9px;
  gap: 10px;

  /* width: 118px; */
  /* width: 118px; */
  height: 40px;
  /* talkappi blue */
  background: #245BD6;
  color: #FFFFFF;

  border-radius: 4px;
  /* Inside auto layout */
  flex: none;
  /* order: 0; */
  flex-grow: 0;
  
  cursor: pointer;
}

.btn_large_white {
  /* large */
  box-sizing: border-box;
  /* Auto layout */
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 10px 24px 9px;
  gap: 10px;

  width: 108px;
  height: 40px;

  /* pure white */
  background: #FFFFFF;
  /* common grey */
  border: 1px solid #C8CACE;
  border-radius: 4px;
  /* Inside auto layout */
  flex: none;
  /* order: 1; */
  flex-grow: 0;
  
  cursor: pointer;
}

.btn_white_diff {
  /* large */
  box-sizing: border-box;
  /* Auto layout */
  /* pure white */
  background: #FFFFFF;
  /* common grey */
  border: 1px solid #C8CACE;
  /* Inside auto layout */
}

.btn_large_blue.delete {
  /* large */
  box-sizing: border-box;
  /* Auto layout */
  /* pure white */
  background: #FFFFFF;
  border: 1px solid #e53361;
  /* Inside auto layout */
}

.btn_w_ico.delete {
  /* pure white */
  background: #FFFFFF;
  border: 1px solid #e53361;
}

.txt_btn_large {
  /* 一覧に戻る */
  /* width: 70px; */
  height: 21px;

  font-family: 'Hiragino Sans';
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 21px;
  /* identical to box height */
  text-align: center;
  /* talkappi dark grey */
  color: #3D3F45;
  /* Inside auto layout */
  flex: none;
  /* order: 0; */
  flex-grow: 0;
}

.txt_btn_large_highlight_diff {
  font-weight: 500;
  /* pure white */
  color: #FFFFFF;
}

table>tbody>tr>td input#attach_tag:focus {
  outline: none !important;
  border:2px #245BD6;
  box-shadow: 0 0 10px #245BD6;
}


.table-scrollable {
  overflow-y: auto;
  max-height: 563px;
}


#table1preview_wrapper>.table-scrollable,
#table3preview_wrapper>.table-scrollable {
  border: 0px;
}

#table1members.table>thead, #table0list.table>thead, #table2list.table>thead
{
  position: sticky;
  top: 0;
  background: white;
  z-index: 2;
}

#table1members.table>thead>tr>th {
  border-bottom: 1px solid #ddd;
}

#table1members.table tr>:last-child,
#table1preview_wrapper>.table-scrollable>.dataTables_scroll>.dataTables_scrollHead table>thead>tr>:last-child,
#table1preview_wrapper>.table-scrollable>.dataTables_scroll>.dataTables_scrollBody>table>tbody>tr>:last-child {
  position: sticky;
  right: 0;
  z-index: 1;
  background-color: white;
  border-left: 1px solid #ddd;
  border-right: 1px solid #ddd;
  opacity: 95%;
}

#table1preview_wrapper>.table-scrollable>.dataTables_scroll>.dataTables_scrollBody>table>tbody>tr>:nth-last-child(2),
#table1preview_wrapper>.table-scrollable>.dataTables_scroll>.dataTables_scrollHead table>thead>tr>:nth-last-child(2) {
  border-right: 0;
}

#table1preview_wrapper>.table-scrollable>.dataTables_scroll>.dataTables_scrollBody, 
#table3preview_wrapper>.table-scrollable>.dataTables_scroll>.dataTables_scrollBody {
  border-bottom: 0px !important;
}

.btn_large_blue.disabled {
  background: #C8CACE !important;
}

.disabled {
  color: #A1A4AA;
  /* pale grey for line */
  /* background: #E3E5E8; */
  cursor: default;
}

#upload_csv>.talkappi-show-container {
  position: relative;
}

.csv_import_float_panel {
  position: relative;
}

.csv_import_float_panel div.import_progress {
  position: absolute;
  opacity: 0.5;
  height: 6px;
  width: 0;
  top: 0;
  left: 0;
  transition: all 0.1s;
  background-color: green;
}

.icon-email-blue {
  background: url("/assets/admin/css/img/icon-email-blue.svg") no-repeat;
  height: 12px;
  width: 12px;
}
td .disabled.btn {
  border: none;
  cursor: none;
  /* cursor: not-allowed; */
  pointer-events: none;

}

.detail>i {
  width: 15px;
  height: 15px;
  background: url("/assets/admin/css/img/icon-detail.svg") no-repeat;
}

.icon-cancel {
  background: url("/assets/admin/css/img/icon-cancel-small.svg") no-repeat;
  height: 14px;
  width: 14px;
}

.icon-undo {
  background: url("/assets/admin/css/img/icon-undo-black.svg") no-repeat;
  /* height: 12px;
  width: 12px; */
}

.icon-check.partial {
  display: inline-block;
  background-image: url(/assets/admin/css/img/icon-fold-selected.svg);
  background-repeat: no-repeat;
  border: solid 0px;
}

.btn_w_ico.tag.disabled>i {
  background: url("/assets/admin/css/img/icon-tag-disabled.svg") no-repeat;
  height: 12px;
  width: 12px;
}
.btn_w_ico.tag.vivid>i {
  background: url("/assets/admin/css/img/icon-tag-white.svg") no-repeat;
  height: 12px;
  width: 12px;
}
.btn_w_ico.tag>i {
    background: url("/assets/admin/css/img/icon-tag-blue.svg") no-repeat;
    height: 12px;
    width: 12px;
}

.btn_w_ico.add.disabled>i {
    background: url("/assets/admin/css/img/icon-add-disabled.svg") no-repeat;
    height: 12px;
    width: 12px;
}

.btn_w_ico.add>i {
  background: url("/assets/admin/css/img/icon-add.svg") no-repeat;
  height: 12px;
  width: 12px;
}

.btn_w_ico.add.disabled>i {
  background: url("/assets/admin/css/img/icon-add.svg") no-repeat;
  filter: brightness(0) saturate(100%) invert(88%) sepia(7%) saturate(11%) hue-rotate(180deg) brightness(92%) contrast(85%);
  height: 12px;
  width: 12px;
}

.btn_w_ico.delete.disabled>i {
  background: url("/assets/admin/css/img/icon-delete-disabled.svg") no-repeat;
  height: 12px;
  width: 12px;
}
.disabled.delete.image.round.btn:before{
  background: url("/assets/admin/css/img/icon-delete-disabled.svg") no-repeat;
  height: 12px;
  width: 12px;
}
.restore>i {
  background: url("/assets/admin/css/img/icon-undo-white.svg") no-repeat;
  height: 12px;
  width: 12px;
}
.delete>i {
  background: url("/assets/admin/css/img/icon-delete.svg") no-repeat;
  height: 12px;
  width: 12px;
}

.disabled.edit.image.round.btn:before {
  background:url("/assets/admin/css/img/icon-edit-disabled.svg") no-repeat;
}

.result.image.round.btn:before {
  background:url("/assets/admin/css/img/icon-navi-inquiry-enabled.svg") no-repeat;
}

.clear.image.round.btn:before {
  background:url("/assets/admin/css/img/icon-delete.svg") no-repeat;
}

.copy.image.round.btn:before {
  background:url("/assets/admin/css/img/icon-copy.svg") no-repeat;
}

.review.image.round.btn:before {
  background:url("/assets/admin/css/img/icon-search.svg") no-repeat;
}

.disabled.result.image.round.btn:before {
  background:url("/assets/admin/css/img/icon-navi-inquiry-disabled.svg") no-repeat;
}

.report.image.round.btn:before {
  background:url("/assets/admin/images/icon-navi-dashboard-enabled.svg") no-repeat;
}

.disabled.report.image.round.btn:before {
  background:url("/assets/admin/images/icon-navi-dashboard-disabled.svg") no-repeat;
}

.icon-sort {
  background: url("/assets/admin/css/img/icon-sort.svg") no-repeat;
  height: 20px;
  width: 20px;
}
.icon-check-unselected {
  background: url("/assets/admin/css/img/icon-check-unselected.svg") no-repeat;
  height: 12px;
  width: 12px;
}
.icon-import-blue {
  background: url("/assets/admin/css/img/icon-import-blue.svg") no-repeat;
  height: 12px;
  width: 12px;
}
.drop-down-close-icon {
  background: url("/assets/admin/css/img/icon-drop-down-close.svg") no-repeat;
  height: 12px;
  width: 12px;
} 