@charset "utf-8";
/* talkappi admin site css */

html,body {
  height: 100vh;
}

/* スクロールバー */
::-webkit-scrollbar {
  /* width: 10px; */
}

/*スクロールバーの動く部分*/
::-webkit-scrollbar-thumb {
  /* border-radius: 10px;
  background: rgba(10, 28, 68, 0.3); */
}

body {
  font-family: ヒラギノ角ゴシック,'Hiragino Kaku Gothic',system-ui,源ノ角ゴジックJP, メイリオ, Meiryo, 'ＭＳ Ｐゴシック', 'MS PGothic',sans-serif;
  /* bodyのスクロールバーを非表示 */
  /* overflow-y: scroll; */
  /* -ms-overflow-style: none; */    /* IE, Edge 対応 */
  /*scrollbar-width: none;*/       /* Firefox 対応 */
}
/* bodyのスクロールバーを非表示 */
body::-webkit-scrollbar {  /* Chrome, Safari 対応 */
  /* display:none; */
}

a,
a:hover,
a:active,
a:visited,
a:focus {
  text-decoration: none;
  color: #245BD6
}

a:hover {
  text-decoration: underline;
}

table td {
  word-wrap: break-word;
  word-break: break-all;
}

.bold {
  font-weight:bold;
}

/* control */
label {
  font-weight: 300;
  font-size: 12px;
  color: #000;
}
a label {
  color:#245BD6;
}
label span {
  /* margin-right:5px; */
}
input {
  /* height: 28px; */
  font-size: 12px;
}
input[type="text"]:focus{
    outline: none;
    /* border: none !important; */
    /* box-shadow: 0 0 0 1px #245BD6; */
    transition: unset;
}
select{
  height:28px !important;
  padding:0 8px !important;
}
.readonly-input {
  width: 100%;
  height:28px;
  padding: 5px 12px;
  border-radius: 4px;
  border: solid 1px #e3e5e8;
  background: #f6f7f9;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.control-label {
  line-height: 12px;
}
.form-control {
  height: 28px;
  font-size: 12px;
}

.form-body {
	margin-top:12px;
}

/*
.form-actions {
  padding:16px 12px 8px 12px;
}
*/

/* label width */
.label-fix-2 {
  text-align: left !important;
  max-width: 48px;
}
.label-fix-4 {
  text-align: left !important;
  max-width: 72px;
}
.label-fix-6 {
  text-align: left !important;
  max-width: 96px;
}
.label-fix-8 {
  text-align: left !important;
  max-width: 120px;
}
.label-fix-10 {
  text-align: left !important;
  max-width: 148px;
}
.label-fix-12 {
  text-align: left !important;
  max-width: 180px;
}
.label-fix-14 {
  text-align: left !important;
  width: 200px;
}
.warp-line {
  line-height:28px;
}

/* container */
.col-md-1 p, .col-md-2 p, .col-md-3 p, .col-md-4 p, .col-md-5 p, .col-md-6 p, .col-md-7 p, .col-md-8 p, .col-md-9 p, .col-md-10 p, .col-md-11 p, .col-md-12 p {
  margin:4px 4px 0 4px;
}
/*
.col-md-1, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6, .col-md-7, .col-md-8, .col-md-9, .col-md-10, .col-md-11, .col-md-12 {
  display:flex;
}
*/
.content-container {
  padding: 12px 20px;
  font-size: 12px;
  border-radius: 4px;
}

@media screen and (min-width: 992px) {
  .content-container {
    min-width: 980px;
  }
}

.search-conditions {
  display: flex;
  gap: 12px;
}

.content-container.white {
  background-color: #fff;
}
.content-container.white.border {
  border: 1px solid #e3e5e8;
}
.content-container.light-gray{
  background: #f6f7f9;
}
.content-container.table {
  padding: 32px;
  background-color: #fff;
}
.content-container .form-group {
  margin-left: 0;
  margin-right: 0;
}
.content-container h2 {
  font-family: HiraginoSans-W5;
  font-size: 13px;
  color: #000;
  -webkit-font-smoothing: auto;
  margin-bottom: 20px;
}
.content-container h4 {
  font-family: HiraginoSans-W5;
  font-size: 12px;
  color: #000;
  -webkit-font-smoothing: auto;
  margin-bottom: 20px;
}
.content-container ::marker {
  display: none;
  content: none;
}
.content-container ::-webkit-resizer {
  display: none;
}
.content-container :focus {
  outline: none;
  /* box-shadow: 0 0 0 1px #245BD6; */
}
.section-container {
  padding: 16px 12px;
}
.section-container.white {
  background-color: #fff;
}
.section-container.bottom-line {
  border-bottom: 1px solid #e3e5e8;
}
.actions-container{
  display: flex;
  align-items: center;
}
.component-container {
  width:100%;
  padding: 12px;
  border-radius: 4px;
  border: solid 1px #e3e5e8;
  background: #f6f7f9;
}
.actions-group-container {
  display:flex;
  align-items: center;
}
/*
.actions-group-container span {
  margin-right:8px;
}
*/
/* tab */

.line-tab {
	padding: 15px 0px;
}
.line-tab ul{
	display: flex;
  align-items: center;
  margin: 0;
  padding: 0;
  cursor: pointer;
  list-style:none;
}
.line-tab ul li{
	height: 25px;
  padding: 0 6px;
  font-size: 12px;
  font-weight: 500;
  border-bottom: 1px solid #e3e5e8;
  cursor: pointer;
}
.line-tab ul li.active{
	border-bottom: 2px solid #245BD6;
} 
.button-tab {
	padding: 15px 0px;
}
.button-tab ul{
	display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin: 0;
  padding: 0;
  cursor: pointer;
  list-style:none;
}
.button-tab ul li{
	height: 28px;
  font-size: 12px;
  border-radius: 14px;
  padding: 5px 10px;
  color: #3d3f45;
  background-color: #ebedf2;
  cursor: pointer;
  margin: 0 10px 15px 0px;
}
.button-tab ul li a{
  color: #3d3f45;
}
.button-tab ul li.active{
  color: #fff;
  background-color: #245BD6;
}
.button-tab ul li.active a{
  color: #fff;
}

/* pannel */
.small-table-pannel {
	position: relative;
	margin:2px;
	padding:2px;
	font-size:10px;
	border-radius:5px;
	background:#e3e5e8;
}
.small-table-pannel .pannel-close {
    width: 12px;
    height: 12px;
    opacity: 1;
    position: absolute;
    top: 1px;
    right: 4px;
    cursor: pointer;
}
.small-table-pannel .pannel-close span::before,
.small-table-pannel .pannel-close span::after {
  content: "";
  display: block;
  width: 100%;
  height: 1px;
  position: absolute;
  top: 8px;
  left: 0;
  background:#616161;
}
.small-table-pannel .pannel-close span::before {
  -moz-transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  -webkit-transform: rotate(-45deg);
  transform: rotate(-45deg);
}
.small-table-pannel .pannel-close span::after {
  -moz-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
}
.small-table-pannel .label{
	color:#000;
	margin:1px;
	padding:1px;
	font-size:10px;
	border-radius:0px;
	background-color: #FFDCE5;
  padding:2px 4px;
  border-radius: 4px;
  margin-left:2px;
}

/* button */
.btn {
  height:24px;
  font-size:12px;
  padding: 3px 11px;
  border-width: 0;
  border-radius: 0px;
  color:#000;
  cursor: pointer;
  display: inline-flex;
  width: fit-content;
  align-items: center;
}
.round.btn {
  background-color: #e3e5e8;
  border-radius: 12px;
  font-weight: 300;
  margin-right: 4px;
}
.image.round.btn {
  position:relative;
  padding: 3px 11px 3px 24px;
}
.image.round.btn:before {
  content:"";
  display:block;
  background-size: 16px 16px;
  width:16px;
  height:16px;
  position:absolute;
  left:8px;
  top:6px;
}

.selected.light-gray.btn {
  background-color: #cff2d7 ;
}
.white.btn {
  color: #000;
  background-color: #FFFFFF;
  border-color:#e3e5e8;
}
.yellow.btn {
  background-color: #FFB848;
}
.light-gray.btn {
  color: #000;
  background-color: #E5E5E5;
}
.light-red.btn {
  color: #000;
  background-color: #FFDCE5;
}
.light-green.btn {
  color: #000;
  background-color: #CFF2D7;
}
.light-blue.btn {
  color: #000;
  background-color: #D3EEFF;
}
.light-yellow.btn {
  color: #000;
  background-color: #FFF0BB;
}
.light-purple.btn {
  color: #000;
  background-color: #EDE0FF;
}
.light-orange.btn {
  color: #000;
  background-color: #FFE6D6;
}

.add.image.round.btn:before {
  background:url("/assets/admin/css/img/icon-add.svg") no-repeat;
}
.copy.image.round.btn:before {
  background:url("/assets/admin/css/img/icon-copy.svg") no-repeat;
}
.edit.image.round.btn:before {
  background:url("/assets/admin/css/img/icon-edit.svg") no-repeat;
}
.setting.image.round.btn:before {
  background:url("/assets/admin/css/img/icon-setting.svg") no-repeat;
}
.delete.image.round.btn:before {
  background:url("/assets/admin/css/img/icon-delete.svg") no-repeat;
}
.public.image.round.btn:before {
  background:url("/assets/admin/css/img/icon-public.svg") no-repeat;
}
.link.image.round.btn:before {
  background:url("/assets/admin/css/img/icon-link.svg") no-repeat;
}
.result.image.round.btn:before {
  background:url("/assets/admin/css/img/icon-result.svg") no-repeat;
}
.detail.image.round.btn:before {
  background:url("/assets/admin/css/img/icon-search.svg") no-repeat;
}
.preview.image.round.btn:before {
  background:url("/assets/admin/css/img/icon-preview.svg") no-repeat;
}
.send.image.round.btn:before {
  background:url("/assets/admin/css/img/icon-send_small.svg") no-repeat;  
}
.icon-action-section {
  margin-left:20px;
  width: 12px;
  height: 12px;
  display: block;
  background-repeat: no-repeat;
  cursor: pointer;
}
.icon-action-section.toggle {
  background-image: url(/assets/admin/css/img/icon-fold-section-close.svg);
}
.icon-action-section.toggle.open {
  background-image: url(/assets/admin/css/img/icon-fold-section-open.svg);
}
.icon-action-section.delete {
  background-image: url(/assets/admin/css/img/icon-delete.svg);
}

.spam-user {
  margin-left:10px !important;
  height: 20px !important;
  font-size: 10px !important;
  color: #b2b2b2 !important;
  background-color: #f2f2f2 !important;
}
.spam-user.block {
  color: #000000;
  background-color: #FFDCE5 !important;
}

/* button with span icon */
button span {
  margin-right: 6px;
}

/* バツ印（大） */
.icon-cancel-large {
  width: 24px;
  height: 24px;
  margin: 0 0 0 auto;
  background-image: url(/assets/admin/css/img/icon-cancel-large.svg);
  background-repeat: no-repeat;
}
/* 拡大　開く */
.icon-form-zoom-in {
  width: 12px;
  height: 12px;
  background-image: url(/assets/admin/css/img/icon-form-zoom-in.svg);
  background-repeat: no-repeat;
}
.icon-export {
  width: 12px;
  height: 12px;
  background-image: url(/assets/admin/css/img/icon_export.svg);
  background-repeat: no-repeat;
}
.icon-generative-ai {
  width: 12px;
  height: 12px;
  background-image: url(/assets/admin/css/img/icon-generative-ai.svg);
  background-repeat: no-repeat;
}
.icon-generative-ai-grey {
  width: 12px;
  height: 12px;
  background-image: url(/assets/admin/css/img/icon-generative-ai-grey.svg);
  background-repeat: no-repeat;
}
.icon-generative-ai-dark-grey {
  width: 12px;
  height: 12px;
  background-image: url(/assets/admin/css/img/icon-generative-ai-dark-grey.svg);
  background-repeat: no-repeat;
}
.icon-processing {
  width: 16px;
  height: 16px;
  background-size: 100%;
  background-position: center;
  background-image: url(/assets/admin/css/img/icon-processing.gif);
  background-repeat: no-repeat;
}
.icon-delete {
  width: 12px;
  height: 12px;
  background-image: url(/assets/admin/css/img/icon-delete.svg);
  background-repeat: no-repeat;
}
.icon-add-white {
  width: 12px;
  height: 12px;
  background-image: url(/assets/admin/css/img/icon-add-white.svg);
  background-repeat: no-repeat;
}
.icon-add-gray {
  width: 12px;
  height: 12px;
  background-image: url(/assets/admin/css/img/icon-add.svg);
  background-repeat: no-repeat;
}
.icon-add {
  width: 12px;
  height: 12px;
  background-image: url(/assets/admin/css/img/icon-add.svg);
  background-repeat: no-repeat;
}
.icon-remove {
  width: 12px;
  height: 12px;
  background-image: url(/assets/admin/css/img/icon-cancel-small.svg);
  background-size: 12px 12px;
  background-repeat: no-repeat;
}
.icon-edit {
    width: 12px;
    height: 12px;
    background: url("/assets/admin/css/img/icon-edit.svg");
    background-repeat: no-repeat;
  }

.btn.category{
  height: 28px;
  font-size: 12px;
  border-radius: 14px;
  padding: 5px 10px;
  color: #3d3f45;
  background-color: #ebedf2;
  margin: 0 5px;
  cursor: pointer;
}
.btn.category.active{
  color: #FFF;
  background-color: #245BD6;
}
.btn.tag {
  height: 28px;
  font-size: 12px;
  border-radius: 14px;
  padding: 5px 10px;
  color: #3d3f45;
  background-color: #ebedf2;
  margin: 0 5px;
  cursor: pointer;
}
.btn.tag.remove {
  height: 28px;
}

/* action button */

.action-button {
  border-radius: 4px;
  border:none;
  display: -webkit-flex;
  display: flex;
  -webkit-align-items: center; /* 縦方向中央揃え（Safari用） */
  align-items: center; /* 縦方向中央揃え */
  justify-content: center;
  background-color: #E5E5E5;
  cursor: pointer;
}
.action-button span {
  margin-right: 6px;
}
.action-button.page {
  min-width: 108px;
  height: 40px;
  padding: 0 16px;
  margin: 0 0 0 14px;
}
.action-button.section {
  min-width: 84px;
  height: 30px;
  padding: 0 12px;
  margin: 0 0 0 12px;
}
.action-button.group {
  min-width: 60px;
  height: 26px;
  margin: 0 0 0 8px;
  padding: 0 8px;
}

.icon-only {
  min-width:unset !important;
  width:unset !important;
}
.icon-only span {
  margin-right:unset !important;
}

/* group action button */
.image-action-group {
  margin: 12px 0 12px 0;
  display: flex;
  align-items: center;
  width: 200px;
  cursor: pointer;
}
.image-action-group img {
  width: 20px;
  height: 20px;
  padding: 4px;
  margin: 0 12px 0 0;
  border-radius: 4px;
  background: #e3e5e8;
}

/* data-table */
.custom-data-table-header {
  display:none;
  align-items: baseline;
  position: absolute;
}

/* additional */
.talkappi-waiting {
  display: none;
  z-index: 9998;
  position:fixed;
  height: 100vh;
  width: 100vw;
  top: 0;
  left: 0;
}
.talkappi-waiting img {
  position:fixed;
  z-index: 9999;
  bottom:50%;
  left:50%;
  margin-left:-32px;
  margin-bottom:-32px;
  width:64px;
}
.flex {
  display: flex;
}
.flex-x-between {
  display: -webkit-flex;
  display: flex;
  justify-content: space-between;
  -webkit-align-items: center; /* 縦方向中央揃え（Safari用） */
  align-items: center; /* 縦方向中央揃え */
}
.for-clone{
  display: none;
}

/* extra */

/* 右側(プレビュー) */
.mobile-preview {
  width:320px;
}

.preview-sticky {
  position: sticky;
  top: 10px;
}

.mobile-preview-left-content {
  width:calc(100% - 400px);;
}
@media screen and (max-width: 991px) {
  .mobile-preview {
    display: none;
  }
  .mobile-preview-left-content {
    width:100%;
  }
}

/* ヘッダー */
.mobile-preview .header-container {
  display:flex;
  align-items: center;
  width: 320px;
  height: 44px;
  padding: 0 12px;
  border-radius: 10px 10px 0 0;
  border: solid 1px #e3e5e8;
  background: #fff;
}
.mobile-preview .footer-container {
  display:flex;
  align-items: center;
  font-size:8px;
  width: 320px;
  height: 32px;
  padding: 0 12px;
  border-radius: 0px 0px 10px 10px;
  border: solid 1px #e3e5e8;
  background: #fff;
}
/* メイン */
.mobile-preview .main-container {
  width: 320px;
  height: 495px;
  padding: 5px 10px 9px 10px;
  background: #ebedf2;
  overflow-y: scroll;
  border: solid 1px #e3e5e8;
  border-top: none;
}
/* a:has(img) */
.mobile-preview .main-container:has(.ref-container) {
  padding: inherit;
}
.mobile-preview .main-container::-webkit-scrollbar {
  display: none;
}
/* 回答 */
.mobile-preview .main-container .answer {
  width: fit-content;
  width: -moz-fit-content;
  max-width: 265px;
  padding: 10px 12px !important;
  margin: 12px 0 0 0 !important;
  border-radius: 10px 10px 10px 0;
  background: #fff;
  word-wrap: break-word;
}
.webchat-list {
  text-decoration: none !important;
}
.mobile-preview .main-container .answer .button-container{
  display: flex;
  flex-wrap:wrap;
}
.mobile-preview .main-container .answer .list-container{
  display: flex;
  flex-direction: column;
}

.mobile-preview .main-container .button-container .button {
  width: fit-content;
  width: -moz-fit-content;
  min-height: 26px;
  padding: 3px 6px 3px 6px;
  margin-right: 6px;
  margin-top: 10px;
  border-radius: 12px;
  color: #57606f;
  border: solid 1px #57606f;
  display: flex;
  align-items: center;
  cursor: pointer;
}
.mobile-preview .main-container .answer img {
  max-width: 200px;
}

/* survey preview */
.mobile-preview .main-container .survey-main {
  border-radius: 10.5px;
  border: solid 0.9px #e3e5e8;
  background: #fff;
  overflow: hidden;
}
/* 上のピンクの部分 */
.mobile-preview .main-container .survey-main .main-header {
  height: 5px;
}
/* タイトルコンテナ */
.mobile-preview .main-container .survey-main .title-container {
  margin: 14px 0 0 17px;
}
/* ロゴ */
.desc-preview-logo {
  width: 48px;
  height: 48px;
  margin: 0 10px 0 0;
  border-radius: 50%;
}

.mobile-preview .main-container .survey-main .survey-preview-icon {
  width: 48px;
  min-height: 48px;
  max-height: 48px;
  object-fit: cover;
  border: none;
  border-radius: 50%;
}
.mobile-preview .survey-preview-header-image {
  object-fit: cover;
  border: none;
  width: 272px;
  min-height: 136px;
  max-height: 136px;
  margin: 9px 13px 0 13px;
  border-radius: 12px;
}

/* プレビューの内容 */
.mobile-preview .main-container .survey-main .contents-container {
  margin: 0 14px;
}

.mobile-preview .main-container a {
  text-decoration: underline;
  color: inherit;
}

/* 線 */
.mobile-preview .main-container .survey-main .contents-container div {
  box-shadow: inset 0 1px 0 0 #e3e5e8;
  padding: 12px 0 12px 0;
}
/* 線 */
.mobile-preview .main-container .survey-main .contents-container div:first-child {
  box-shadow: none;
}

/* プレビューの改行 */
.mobile-preview .main-container .preview-description-contents,
.mobile-preview .main-container .preview-summary-contents {
  white-space: pre-wrap;
}

/* フッター(「回答する」のコンテナ) */
.mobile-preview .footer-survey-container {
  width: 320px;
  height: 55px;
  border-radius: 0 0 10px 10px;
  border: solid 1px #e3e5e8;
  background: #245BD6;
}

/* 
  delete future 
*/
.btn-red-border {
  background: #fff;
  border: solid 1px #e53361;
  color: #fff;
  min-width:unset !important;
  width:unset !important;
}

.btn-red-border span {
  margin-right:unset !important;
}

/* old top tab */
.tabbable-line {
  margin-bottom: 10px;
}
.tabbable-line>.nav-tabs>li.active {
  border-bottom: 0px solid #F3565D;
  position: relative;
  background-color: #f2f6f9;
}
.tabbable-line>.nav-tabs>li.active>a {
  border: 0;
  color: #5b9bd1;
  background-color: #f2f6f9;
  font-weight: bold;
}
.tabbable-line>.nav-tabs>li.open>a>i,
.tabbable-line>.nav-tabs>li:hover>a>i {
  color: #a6a6a6;
  background-color: #f2f6f9;
}
.tabbable-line>.nav-tabs>li.open,
.tabbable-line>.nav-tabs>li:hover {
  border-bottom: 0px solid #fbcdcf;
  background-color: #f2f6f9;
}
.tabbable-line>.nav-tabs>li.open>a,
.tabbable-line>.nav-tabs>li:hover>a {
  border: 0;
  background: none !important;
  color: #333;
  background-color: #f2f6f9;
}
.nav-tabs>li>a {
  margin-right: 2px;
  line-height: 1.42857143;
  border: 1px solid transparent;
  border-radius: 0px 0px 0 0;
}
.nav>li>a {
  position: relative;
  display: block;
  padding: 10px 10px;
}

/* old button style */
button.btn{
  display: inline-block;
  margin-bottom: 0;
  font-size: 12px;
  font-weight: 400;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  -ms-touch-action: manipulation;
  touch-action: manipulation;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  background-image: none;
  border: 1px solid transparent;
  border-radius: 4px;
  height: 28px;
  padding: 5px 12px;
}

button.btn.blue {
  color: #FFFFFF;
  background-color: #245BD6;
}

button.btn.default, button.btn.default:hover {
  color: #3D3F45;
  background-color: #FFFFFF;
  border: 1px solid #C8CACE;
}

.portlet>label>.btn {
  height: 32px;
}

/* button margin */
.mr10 {
  margin-right: 10px;
}

.ml10 {
  margin-left: 10px;
}

.mr20 {
  margin-right: 20px;
}

.ml20 {
  margin-left: 20px;
}

.input-small {
  width: 116px !important;
}

/* old container */

.page-container {
  padding: 10px 10px 0 10px;
}
.portlet {
  padding: 12px 20px 15px 20px;
  font-size: 12px;
}
.portlet.chatbot {
  overflow: auto;
}
.portlet.white {
  padding: 12px 20px 15px 20px;
  background-color: #fff;
}
.portlet.light-gray{
  background: #f6f7f9;
  padding: 12px 20px 15px 20px;
}
.portlet.flex {
	display: flex;
	align-items: flex-end;
	justify-content: space-between;
}
.portlet .form-group {
  margin-left: 0;
  margin-right: 0;
}
.portlet h2 {
  font-family: HiraginoSans-W5;
  font-size: 18px;
  color: #000;
  -webkit-font-smoothing: auto;
  margin-bottom: 20px;
}
.portlet h4 {
  font-family: HiraginoSans-W5;
  font-size: 12px;
  color: #000;
  -webkit-font-smoothing: auto;
  margin-bottom: 20px;
}
.portlet.light {
  padding: 0px 5px;
}
.portlet.light .portlet-body {
  padding-top: 18px;
}
.row {
  margin-left: -10px;
  margin-right: -10px;
}
.col-xs-1,
.col-sm-1,
.col-md-1,
.col-lg-1,
.col-xs-2,
.col-sm-2,
.col-md-2,
.col-lg-2,
.col-xs-3,
.col-sm-3,
.col-md-3,
.col-lg-3,
.col-xs-4,
.col-sm-4,
.col-md-4,
.col-lg-4,
.col-xs-5,
.col-sm-5,
.col-md-5,
.col-lg-5,
.col-xs-6,
.col-sm-6,
.col-md-6,
.col-lg-6,
.col-xs-7,
.col-sm-7,
.col-md-7,
.col-lg-7,
.col-xs-8,
.col-sm-8,
.col-md-8,
.col-lg-8,
.col-xs-9,
.col-sm-9,
.col-md-9,
.col-lg-9,
.col-xs-10,
.col-sm-10,
.col-md-10,
.col-lg-10,
.col-xs-11,
.col-sm-11,
.col-md-11,
.col-lg-11,
.col-xs-12,
.col-sm-12,
.col-md-12,
.col-lg-12 {
  padding-right: 10px;
  padding-left: 10px;
}


/* ボタン(大) */
.btn-larger {
  min-width: 108px;
  height: 40px;
  padding: 0 16px;
  margin: 0 0 0 14px;
  border-radius: 4px;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

/* ボタン(中) */
.btn-medium {
  width: unset !important;
  min-width: 85px;
  height: 32px;
  padding: 0 12px;
  margin: 0 0 0 12px;
  border-radius: 4px;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

/* ボタン(小、四角) SVG有 */
.btn-smaller {
  min-width: 66px;
  height: 28px;
  margin: 0 0 0 8px;
  padding: 0 8px;
  border-radius: 4px;
  display: -webkit-flex;
  display: flex;
  -webkit-align-items: center; /* 縦方向中央揃え（Safari用） */
  align-items: center; /* 縦方向中央揃え */
  justify-content: center;
  cursor: pointer;
}

.x-first {
  margin-left: 0px !important;
}

/* ボタン(青) */
.btn-blue {
  background: #245BD6;
  color: #fff;
  border: solid 1px #245BD6;
}
/* ボタン(白) */
.btn-white {
  background: #fff;
  color: #000;
  border: solid 1px #c8cace;
}
/* ボタン(黄色) */
.btn-yellow {
  background: #FFB848;
  color: #fff;
  border: solid 1px #FFB848;
}
.btn-red-border {
  border: solid 1px #e53361 !important;
  background: #fff;
  min-width:unset !important;
  width:unset !important;
}

.mr10 {
  margin-right: 10px;
}

.ml10 {
  margin-left: 10px;
}

.mr20 {
  margin-right: 20px;
}

.ml20 {
  margin-left: 20px;
}

/* 整理　陳整理 */
.setting-header {
  margin: 16px 0;
  font-size: 13px;
  font-family: HiraginoSans-W5;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  letter-spacing: normal;
  color: #000;
  -webkit-font-smoothing: auto;
  max-width: fit-content;
  border-bottom: #bdb8b8;
  border-bottom-style: dotted;
  border-bottom-width: thin;
} 

.basic-label {
  position: relative;
  line-height: 12px;
  width: 109px;
  min-width: 109px;
  margin: 0 0 0 25px !important;
  font-size: 12px;
  font-family: HiraginoSans-W3;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  letter-spacing: normal;
  color: #000;
  -webkit-font-smoothing: auto;
  display: flex; align-items: center;
}

.edit_link {
  cursor: pointer;
  color: #245BD6;
  font-size: 12px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  font-family: HiraginoSans-W3;
  margin-left: 32px;
}

.lines-container {
  min-height: 28px;
  height: auto;
  background: #fff;
  display: -webkit-flex;
  display: flex;
  -webkit-align-items: center;
  align-items: baseline;
  margin: 20px 0 0 0;
}
.line-content {
  line-height: 28px;
}

li{
  list-style-type: none;
}

/* 混雑状況　開始 */
/* タブレット横置き、PC */
.congestion-container {
  width: 100%;
  /* max-height: 650px; */
  background: #fff;
  display: flex;
}

.current-congestion {
  width: 60%;
  padding: 30px 0 24px 0;
  background: rgba(61, 63, 69, 0.72);
  background-size: cover;
}
.congestion-setting {
  width: 40%;
  padding: 0 24px 24px 24px ;
}

.congestion-place {
  width: 80%;
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 600;
  font-size: 44px;
  line-height: 52px;
  text-align: center;
  color: #fff;
}

.congestion-sub-title {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-size: 32px;
  line-height: 38px;
  color: #fff;
  margin: 32px 0 0 0;
}
.congestion-setting .congestion-sub-title {
  color: #000;
}

.congestion-button {
  width: 100%;
  max-width: 500px;
  height: 116px;
  border-radius: 24px;
  background: #32CE55;
  margin: 24px 0 0 0;
  cursor: pointer;
  
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-size: 44px;
  line-height: 52px;
  text-align: center;
}
.congestion-button.crowded {
  background: #FFDCE5;
  border: 1px solid #E53361;
}
.congestion-button.slightly-crowded {
  background: #FFE6D6;
  border: 1px solid #FF9551;
}
.congestion-button.vacant {
  background: #CFF2D7;
  border: 1px solid #32CE55;
}
.congestion-button.restricted {
  background: #FFF4A5;
  border: 1px solid #ebc400
  ;
}

.crowded-1 {
  color:#FFF;
  background: #32CE55;
}
.crowded-2 {
  color:#FFF;
  background: #FF9551;
}
.crowded-3 {
  color:#FFF;
  background: #E53361;
}
.crowded-4 {
  color:#FFF;
  background: #ebc400
  ;
}

.congestion-his {
  color: #fff;
  margin: 24px 0 0 0;
}

/* タブレット */
@media screen and (max-width: 992px) {
  .congestion-container {
    display: flex;
    flex-direction: column;
  }
  .current-congestion {
    width: 100%;
    height: 400px;
  }
  .congestion-setting {
    width: 100%;
  }
  .congestion-button {
    height: 92px;
  }
}
/* スマホ */
@media screen and (max-width: 767px) {
  .current-congestion {
    height: 260px;
  }
  .congestion-setting {
    padding: 0 0 24px 0;
  }
  .congestion-place {
    font-size: 28px;
    font-weight: 600;
    font-size: 28px;
    line-height: 33px;
  }
  .congestion-sub-title {
    font-size: 18px;
    font-weight: 400;
    font-size: 18px;
    line-height: 21px;
  }
  .congestion-button {
    width: calc(100% - 24px);
    min-height: 55px;
    max-height: 55px;
    border-radius: 8px;

    font-weight: 400;
    font-size: 20px;
    line-height: 23px;
  }
}

/* 混雑状況　終了 */
.summernote-preview.summernote-desc {
  background: #fff;
  border-radius: 4px;
  border: solid 1px #e3e5e8;
  min-height: 77px;
  margin: 0;
  padding: 6px 12px;
  box-shadow: none;
}

.form-control.summernoteless-input-desc {
  width: 100%;
  max-width: 500px;
  min-height: 79px;
  padding: 8px 12px;
  border-radius: 4px;
  border: solid 1px #e3e5e8;
  background: #fff;
  overflow: auto;
  resize: none;
}

.form-control.summernoteless-input-desc:focus,
.text-input-longer:focus {
  outline: none;
  box-shadow: 0 0 0 1px #245BD6;
}

.select2-container-multi .select2-choices {
  min-height: 28px;
}

.select2-container-multi .select2-choices .select2-search-field input {
  padding: 0;
  margin: 0;
}

@media screen and (max-width: 990px) {
  .system_link_logo {
    max-width: 300px;
  }
}

@media screen and (min-width: 991px) {
  .system_link_logo {
    max-height: 150px;
  }
}

.back-ground-gray {
  background-color:#F6F7F9;
  border: 1px solid #E3E5E8;
  border-radius:4px;
  padding:12px;
  margin-bottom:10px;
} 
/* admin/sitepage の画像選択ラジオボタン */
.template-select-radio input[type="radio"] {
  display: none;
}

.template-select-radio label img {
  margin: 1px 6px;
}

.template-select-radio  input[type="radio"] + label img {
  border: none;
}
.template-select-radio  input[type="radio"]:checked + label img {
  border: 2px solid #245BD6;
  box-sizing:content-box;
}

/* アラート */

.edit-menu-container .title {
  width: 84px;
  min-width: 84px;
}

.edit-menu-container .previous-alert {
  height: 24px;
  padding:0 16px;
  background: #E3E5E8;
  border-radius: 13px;
  width: fit-content;
  align-items: center;
  display: flex;
}