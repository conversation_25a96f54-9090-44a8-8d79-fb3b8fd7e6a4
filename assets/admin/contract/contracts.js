$(function(){
  $(window).on('beforeunload', function() {
    talkmgr.unblock();
	});
  
  talkmgr.load();

  $(document).on('click', '.js-new-contract', function() {
    talkmgr.url('/admincontract/contract')
  })

  $(document).on('click', '.js-edit-contract', function() {
    const url = $(this).data('url');
    talkmgr.url(url);
  })

  $(document).on('click', '.js-account-name', function() {
    const url = $(this).data('url');
    talkmgr.url(url);
  })

  $(document).on('click', '.js-search', function() {
    talkmgr.submit();
  })

  $(document).on('click', '.js-export-sales', function() {
    const dialog = {
      "title": "売上データのエクスポート",
      "body": "エクスポートする売上データの期間を選択してください。",
      "buttons": [
        {
          "caption": "キャンセル",
          "type": "close",
          "color": "white"
        },
        {
          "caption": "エクスポート",
          "type": "confirm",
          "color": "blue"
        }
      ]
    };
    const content = createModalContent();
    TalkappiModal.modal(dialog, content, function(result) {
      if (result.button === 'confirm') {
        const startDate = $('.sales-modal-body').find('.js-sales-start-date').val();
        const endDate = $('.sales-modal-body').find('.js-sales-end-date').val();
        if (startDate === '' || endDate === '') {
          talkmgr.toastError('開始日と終了日を入力してください。');
          return;
        }
        const type = $('.sales-modal-body').find('input[name="type"]').val();
        const formData = new URLSearchParams();
        formData.append('start_date', startDate);
        formData.append('end_date', endDate);
        formData.append('type', type);
        fetch(`/admincontract/exportsales`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            Accept: 'application/json',
          },
          body: formData
        })
        .then(response => response.json())
        .then(responseData => {
          if (responseData.result === 'success') {
            const data = responseData.data;
            const csvs = [];
            if (type === 'segment') {
              csvs.push('総計 \n');
              csvs.push(convertToCsv(data.headers, data.totalDatas));
              csvs.push('初期費用 \n');
              csvs.push(convertToCsv(data.headers, data.initialDatas));
              csvs.push('月額費用 \n');
              csvs.push(convertToCsv(data.headers, data.monthlyDatas));
              csvs.push('その他 \n');
              csvs.push(convertToCsv(data.headers, data.otherDatas));
            } else {
              csvs.push(convertToCsv(data.headers, data.totalDatas));
            }
            downloadCsv(csvs, type);
          }
        })
      }
    })
    talkmgr.init($('.sales-modal-body').find('.js-sales-start-date'));
    talkmgr.init($('.sales-modal-body').find('.js-sales-end-date'));
    talkmgr.init($('.sales-modal-body').find('.js-sales-type'));
  })

  function createModalContent() {
    const outer = $('<div></div>');
    const content = $('<div class="sales-modal-body"></div>');
    const formGroup1 = $('<div class="form-group"></div>');
    const label1 = $('<label>開始月</label>');
    const startDateInput = $('<input type="text" class="talkappi-datepicker js-sales-start-date" data-name="start_date" data-date-format="yyyy-mm" placeholder="yyyy-mm" value="">');
    formGroup1.append(label1);
    formGroup1.append(startDateInput);
    const formGroup2 = $('<div class="form-group"></div>');
    const label2 = $('<label>終了月</label>');
    const endDateInput = $('<input type="text" class="talkappi-datepicker js-sales-end-date" data-name="start_date" data-date-format="yyyy-mm" value="">');
    formGroup2.append(label2);
    formGroup2.append(endDateInput);
    const formGroup3 = $('<div class="form-group"></div>');
    const label3 = $('<label>種類</label>');
    const pulldown = talkmgr.create({type: 'pulldown', label: 'type', value: "segment", source: {'segment':'セグメント別', 'item':'品目別', 'all': '全データ'}, class: 'js-sales-type', style: 'width: 100px;'});
    formGroup3.append(label3);
    formGroup3.append(pulldown);
    content.append([formGroup1, formGroup2, formGroup3]);
    outer.append(content);
    return $(outer).html();
  }

  function convertToCsv(headers, datas) {
    let csv = headers.join(',') + '\n';
    datas.forEach(data => {
      csv += data.join(',') + '\n';
    });
    return csv;
  }

  function downloadCsv(csvs, type) {
    const blob = new Blob(csvs, { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.setAttribute('href', url);
    a.setAttribute('download', `売上データ-${type === 'segment' ? 'セグメント別' : (type === 'item') ? '品目別' : '全データ'}.csv`);
    a.click();
    window.URL.revokeObjectURL(url);
  }
});