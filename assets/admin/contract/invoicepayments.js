$(function(){
  talkmgr.load();

  $(document).on('click', '.js-pdf-download', function() {
    const invoicepaymentID = $(this).data('id');
    talkmgr.url(`/admincontract/invoicepaymentpdf?id=${invoicepaymentID}`, true);
  })

  $(document).on('click', '.js-pdf-preview', function() {
    const invoicepaymentID = $(this).data('id');

    talkmgr.block();
    fetch(`/admincontract/invoicepaymentpdf?id=${invoicepaymentID}&preview=1`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        Accept: 'application/json',
      },
    })
    .then(response => response.json())
    .then(responseData => {
      if (responseData.result == 'success') {
        talkmgr.modal({
          title: 'PDFプレビュー',
          description: `<iframe id='js-pdf' src="${'data:application/pdf;base64,' + responseData.data}" width="100%" height="100%"></iframe>`,
          buttons: [],
        }, (result) => {
        });
        $('.talkappi-modal-container').css('width', '80vw');
        $('.talkappi-modal-container').css('height', '80vh');
        $('.talkappi-modal-container').find('p').css('height', '-webkit-fill-available');
      } else {
        talkmgr.toastError('エラーが発生しました');
      }
    })
    .catch((error) => {
      console.error(error);
      talkmgr.toastError(`エラーが発生しました.`);
    })
    .finally(() => {
      talkmgr.unblock();
    });
  });

  $(document).on('click', '.js-delete', function() {
    const invoicepaymentID = $(this).data('id');
    talkmgr.warning_delete('該当請求書を削除しますか？', '', (event) => {
      if (event.button == 'confirm') {
        const formBody = new URLSearchParams();
        formBody.append('id', invoicepaymentID);
        talkmgr.block();
        fetch(`/admincontract/invoicepaymentdelete`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            Accept: 'application/json',
          },
          body: formBody,
        })
        .then(response => response.json())
        .then(responseData => {
          if (responseData.result == 'success') {
            talkmgr.toast("S|削除しました");
            setTimeout(() => {
              location.reload();
            }, 500);
          } else {
            talkmgr.unblock();
            talkmgr.toastError('エラーが発生しました');
          }
        })
        .catch((error) => {
          talkmgr.unblock();
          talkmgr.toastError('エラーが発生しました');
        })
      }
    })
  })

  $(document).on('click', '.js-invalid', function() {
    const invoicepaymentID = $(this).data('id');
    talkmgr.confirm('該当請求書を無効にしますか？', '', (event) => {
      if (event.button == 'confirm') {
        const formBody = new URLSearchParams();
        formBody.append('id', invoicepaymentID);
        talkmgr.block();
        fetch(`/admincontract/setinvoicepaymentinvalid`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            Accept: 'application/json',
          },
          body: formBody,
        })
        .then(response => response.json())
        .then(responseData => {
          if (responseData.result == 'success') {
            talkmgr.toast("S|請求書を無効にしました");
            setTimeout(() => {
              location.reload();
            }, 500);
          } else {
            talkmgr.unblock();
            talkmgr.toastError('エラーが発生しました');
          }
        })
        .catch((error) => {
          talkmgr.unblock();
          talkmgr.toastError('エラーが発生しました');
        })
      }
    });
  })

  $(document).on('click', '.js-regenerate', function() {
    const invoicepaymentID = $(this).data('id');
    talkmgr.confirm('該当請求書を再生成しますか？', '', (event) => {
      if (event.button == 'confirm') {
        const formBody = new URLSearchParams();
        formBody.append('id', invoicepaymentID);
        talkmgr.block();
        fetch(`/admincontract/invoicepaymentregenerate`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            Accept: 'application/json',
          },
          body: formBody,
        })
        .then(response => response.json())
        .then(responseData => {
          if (responseData.result == 'success') {
            talkmgr.toast("S|再生成しました");
            setTimeout(() => {
              location.reload();
            }, 500);
          } else {
            talkmgr.unblock();
            talkmgr.toastError('エラーが発生しました');
          }
        })
        .catch((error) => {
          talkmgr.unblock();
          talkmgr.toastError('エラーが発生しました');
        })
      }
    })
  });

  $(document).on('click', '.js-send-mail', function() {
    const invoicepaymentID = $(this).data('id');
    talkmgr.confirm('該当請求書をメールで送信しますか？', '', (event) => {
      if (event.button == 'confirm') {
        const formBody = new URLSearchParams();
        formBody.append('id', invoicepaymentID);
        talkmgr.block();
        fetch(`/admincontract/sendinvoicepayment`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            Accept: 'application/json',
          },
          body: formBody,
        })
        .then(response => response.json())
        .then(responseData => {
          if (responseData.result == 'success') {
            talkmgr.toast("S|送信しました");
            setTimeout(() => {
              location.reload();
            }, 500);
          } else {
            talkmgr.toastError(responseData.error);
            setTimeout(() => {
              location.reload();
            }, 500);
          }
        })
        .catch((error) => {
          talkmgr.unblock();
          talkmgr.toastError(error);
        })
      }
    })
  })

  $(document).on('click', '.js-change-sendstatus', function() {
    const invoicepaymentID = $(this).data('id');
    talkmgr.confirm('該当請求書のステータスを送付済みに変更しますか？', '', (event) => {
      if (event.button == 'confirm') {
        const formBody = new URLSearchParams();
        formBody.append('id', invoicepaymentID);
        talkmgr.block();
        fetch(`/admincontract/invoicepaymentchangestatustodone`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            Accept: 'application/json',
          },
          body: formBody,
        })
        .then(response => response.json())
        .then(responseData => {
          if (responseData.result == 'success') {
            talkmgr.toast("S|送付済みに変更しました");
            setTimeout(() => {
              location.reload();
            }, 500);
          } else {
            talkmgr.unblock();
            talkmgr.toastError('エラーが発生しました');
          }
        })
        .catch((error) => {
          talkmgr.unblock();
          talkmgr.toastError('エラーが発生しました');
        })
      }
    })
  })

  $(document).on('click', '.js-change-receiptstatus', function() {
    const invoicepaymentID = $(this).data('id');
    talkmgr.confirm('該当請求書を入金済みに変更しますか？', '', (event) => {
      if (event.button === 'confirm') {
        const formBody = new URLSearchParams();
        formBody.append('id', invoicepaymentID);
        talkmgr.block();
        fetch(`/admincontract/setinvoicepaymentreceipt`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            Accept: 'application/json',
          },
          body: formBody,
        })
        .then(response => response.json())
        .then(responseData => {
          if (responseData.result === 'success') {
            talkmgr.toast("S|入金済みに変更しました");
            setTimeout(() => {
              location.reload();
            }, 500);
          } else {
            talkmgr.unblock();
            talkmgr.toastError('エラーが発生しました');
          }
        })
        .catch((error) => {
          talkmgr.unblock();
          talkmgr.toastError('エラーが発生しました');
        })
      }
    });
  });

  $('.js-export-invoicepayments-csv').on('click', function() {
    const dialog = {
      "title": "請求書データのエクスポート",
      "body": "エクスポートする請求書データの期間を選択してください。",
      "buttons": [
        {
          "caption": "キャンセル",
          "type": "close",
          "color": "white"
        },
        {
          "caption": "エクスポート",
          "type": "confirm",
          "color": "blue"
        }
      ]
    };
    const content = createModalContent();
    TalkappiModal.modal(dialog, content, function(result) {
      if (result.button == 'confirm') {
        const startDate = $('.sales-modal-body').find('.js-start-date').val();
        const endDate = $('.sales-modal-body').find('.js-end-date').val();
        const formData = new URLSearchParams();
        formData.append('start_date', startDate);
        formData.append('end_date', endDate);
        fetch(`/admincontract/exportinvoicepaymentscsv`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            Accept: 'application/json',
          },
          body: formData
        })
        .then(response => response.json())
        .then(responseData => {
          if (responseData.result === 'success') {
            const data = responseData.data;
            const csvs = [convertToCsv(data.headers, data.datas)];
            let label = '';
            if (startDate) {
              label += `${startDate}~`;
            }
            if (endDate) {
              if (label) {
                label += endDate;
              } else {
                label += `~${endDate}`;
              }
            }
            if (label === '') {
              label = '全期間';
            }
            const filename = `請求書データ_${label}.csv`;
            downloadCsv(csvs, filename);
          } else {
            talkmgr.toastError('エラーが発生しました');
          }
        })
        .catch((error) => {
          console.error(error);
          talkmgr.toastError('エラーが発生しました');
        })
      }
    });
    talkmgr.init($('.sales-modal-body').find('.js-start-date'));
    talkmgr.init($('.sales-modal-body').find('.js-end-date'));
    talkmgr.init($('.sales-modal-body').find('.js-data-type'));
  });

  function createModalContent() {
    const outer = $('<div></div>');
    const content = $('<div class="sales-modal-body"></div>');
    const formGroup1 = $('<div class="form-group"></div>');
    const label1 = $('<label>期間</label>');
    formGroup1.append(label1);
    const period = $('<div style="display:flex;gap:10px;"></div>');
    const startDateInput = $('<input type="text" class="talkappi-datepicker js-start-date" data-name="start_date" data-date-format="yyyy-mm-dd" placeholder="yyyy-mm-dd" value="">');
    const endDateInput = $('<input type="text" class="talkappi-datepicker js-end-date" data-name="start_date" data-date-format="yyyy-mm-dd" placeholder="yyyy-mm-dd" value="">');
    period.append([startDateInput, $('<span>〜</span>'), endDateInput]);
    formGroup1.append(period);
    content.append([formGroup1]);
    outer.append(content);
    return $(outer).html();
  }

  function convertToCsv(headers, datas) {
    let csv = headers.join(',') + '\n';
    datas.forEach(data => {
      csv += data.join(',') + '\n';
    });
    return csv;
  }

  function downloadCsv(csvs, filename) {
    const blob = new Blob(csvs, { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.setAttribute('href', url);
    a.setAttribute('download', filename);
    a.click();
    window.URL.revokeObjectURL(url);
  }
});