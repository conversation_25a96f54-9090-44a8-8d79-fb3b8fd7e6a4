$(function(){
  talkmgr.load();

  // init multiinput
  MultiInput.init('.js-multi-input');
  // init address input
  AddressInput.init('.js-address-input');

  Diff.init($('.js-diff-container'), 'invoice', _invoice_code, _seq);

  Check.init(_invoice_code, 'invoice', _seq);

  setupAutocomplete(document.querySelector('input[name="invoice_code"]') , _existed_codes);

  talkmgr.on('select', '.js-generation-pd', function(e) {
    const code = e.data.code;
    if (code != _seq) {
      const url = new URL(location.href);
      url.searchParams.set('seq', code);
      talkmgr.block();
      talkmgr.url(url);
    }
  })

  // save
  $(document).on('click', '.js-action-save', function(){
    if (!validation()) {
      return;
    }
    MultiInput.data('.js-multi-input');
    AddressInput.data('.js-address-input');
    const formData = new URLSearchParams();
    formData.append('invoice_code', $("input[name='invoice_code']").val());
    formData.append('seq', _seq);
    formData.append('ver_start_date', $("input[data-name='ver_start_date']").val());
    formData.append('ver_end_date', $("input[data-name='ver_end_date']").val());
    formData.append('invoice_name', $("input[name='invoice_name']").val());
    formData.append('department_name', $("input[name='department_name']").val());
    formData.append('invoice_recipient_to', $("input[name='invoice_recipient_to']").val());
    formData.append('invoice_recipient_cc', $("input[name='invoice_recipient_cc']").val());
    formData.append('invoice_address', $("input[name='invoice_address']").val());
    formData.append('send_method', $("input[name='send_method']").val());
    formData.append('invoice_send_timing', $("input[name='invoice_send_timing']").val());
    formData.append('invoice_span', $("input[name='invoice_span']").val());
    formData.append('payment_timing', $("input[name='payment_timing']").val());
    formData.append('payment_method', $("input[name='payment_method']").val());
    if (formData.get('send_method') == 'mail' && formData.get('invoice_recipient_to') == '[]') {
      talkmgr.toastError('請求書送付先を入力してください');
      return;
    }
    const fetchURL = `/admincontract/invoicepost` + (_invoice_code === '' ? '' : `?id=${_invoice_code}`);
    talkmgr.block();
    fetch(fetchURL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        Accept: 'application/json',
      },
      body: formData,
    })
    .then(response => response.json())
    .then(responseData => {
      if (responseData.result == 'success') {
        const invoiceCode = responseData.invoice_code;
        if (invoiceCode) {
          talkmgr.toast("S|保存しました");
          setTimeout(() => {
            location.href = `/admincontract/invoice?id=${invoiceCode}&seq=${_seq}`;
          }, 1000);
        } else {
          const newInvoiceCode = responseData.new_invoice_code;
          talkmgr.toast("S|更新しました");
          setTimeout(() => {
            if (newInvoiceCode) {
              location.href = `/admincontract/invoice?id=${newInvoiceCode}&seq=${_seq}`;
            } else {
              location.reload();
            }
          }, 1000);
        }
      } else {
        throw new Error(responseData.error);
      }
    })
    .catch((error) => {
      talkmgr.unblock();
      talkmgr.toastError(`エラーが発生しました: ${error}`);
    });
  });

  // delete
  $(document).on('click', '.js-action-delete', function() {
    const type = $(this).data('type');
    let message = '削除';
    if (type == 'invalid') {
      message = '無効';
    }
    talkmgr.warning(`該当請求先を${message}しますか？`, '', (event) => {
      if (event.button == 'confirm') {
        const formBody = new URLSearchParams();
        formBody.append('invoice_code', _invoice_code);
        formBody.append('seq', _seq);
        formBody.append('type', type);
        talkmgr.block();
        fetch(`/admincontract/invoicedelete`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            Accept: 'application/json',
          },
          body: formBody,
        })
        .then(response => response.json())
        .then(responseData => {
          if (responseData.result == 'success') {
            talkmgr.toast(`S|${message}しました`);
            setTimeout(() => {
              location.href = '/admincontract/invoices';
            }, 1000);
          } else {
            throw new Error(responseData.error);
          }
        })
        .catch((error) => {
          talkmgr.unblock();
          talkmgr.toastError('エラーが発生しました');
        });
      }
    })
  })

  // back
  $(document).on('click', '.js-action-back', function(){
    talkmgr.block();
    location.href = '/admincontract/invoices';
  });

  $(document).on('click', '.js-goto-checklist', function() {
    talkmgr.block();
    talkmgr.url('/admincontract/checklist');
  })
});

const validation = function() {
  const ver_start_date = $("input[data-name='ver_start_date']").val();
  if (_seq != 1 && ver_start_date === '') {
    talkmgr.toastError(`世代開始日を入力してください`);
    return false;
  }
  if ($("input[name='invoice_code']").val() === '') {
    talkmgr.toastError(`請求先コードを入力してください`);
    return false;
  }
  if ($("input[name='invoice_code']").val() !== '' && /^[A-Z0-9]+$/.test($("input[name='invoice_code']").val()) === false) {
    talkmgr.toastError(`請求先コードは半角の大文字と数字を入力してください`);
    return false;
  }
  if ($("input[name='invoice_name']").val() === '') {
    talkmgr.toastError(`請求先名を入力してください`);
    return false;
  }
  if ($("input[name='department_name']").val() === '') {
    talkmgr.toastError(`請求先部門名を入力してください`);
    return false;
  }
  if (!MultiInput.validate('.js-multi-input')) {
    return false;
  }
  const addressError = AddressInput.data('.js-address-input');
  if (addressError !== '') {
    talkmgr.toastError(addressError);
    return false;
  }
  return true;
}

const MultiInput = function(){
  const _html = `<div class="multiinput-wrapper" style="display:flex;gap:6px;align-items:center">
  <input type="email" value="" class="text-input-longer">
  <div class="js-delete-input" style="display:flex;align-items:center;justify-content:center;;width:24px;height:24px;cursor:pointer;"><span class="icon-delete"></span></div>
  </div>`;
  const _addButton = `<div class="js-multiinput-add-button btn round light-blue" style="display:flex;align-items:center;gap:2px"><span class="icon-add"></span>追加</div>`;
  const _hiddenInput = `<input type="hidden" name="{name}">`;

  const _init = function(item, placeholder = undefined) {
    const targets = $(item);
    if (targets.length === 0) {
      return;
    }
    targets.each((i, e) => {
      const target = $(e);
      target.attr('style', 'display:flex;flex-direction:column;gap:6px;flex:1');
      const dataName = target.data('name');
      if (!dataName) {
        return;
      }
      try {
        const value = target.data('value');
        target.html = '';
        if (value && Array.isArray(value) && value.length > 0) {
          value.forEach((v, i) => {
            const html = $(_html);
            target.append(html);
            html.find('input').val(v);
            if (placeholder) {
              html.find('input').attr('placeholder', placeholder);
            }
          });
        } else {
          const html = $(_html);
          if (placeholder) {
            html.attr('placeholder', placeholder);
          }
          target.append(html);
        }
        // add addbutton
        target.append(_addButton);
        // add hidden input
        const hiddenInput = $(_hiddenInput.replace(/{name}/g, dataName));
        hiddenInput.val(JSON.stringify(value));
        target.append(hiddenInput);
      } catch (error) {
        console.error(error);
        return;
      }
    })
  }

  const _initEvent = function() {
    $(document).off('click', '.js-multiinput-add-button');
    $(document).on('click', '.js-multiinput-add-button', function(){
      const target = $(this).closest('.multiinput-wrapper');
      const key = target.find('input').length + 1;
      const html = _html.replace(/{key}/g, key);
      $(this).before(html);
    });
    $(document).off('click', '.js-delete-input');
    $(document).on('click', '.js-delete-input', function(){
      $(this).closest('.multiinput-wrapper').remove();
    });
  }

  const _isValidEmail = (email) => {
    const regex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    return regex.test(email);
  }

  const _validate = (item) => {
    const targets = $(item);
    if (targets.length === 0) {
      return;
    }
    let errorMsg = undefined;
    targets.each((i, e) => {
      const target = $(e);
      target.find('.multiinput-wrapper').each((i, e) => {
        const value = $(e).find('input').val();
        if (value && !_isValidEmail(value)) {
          errorMsg = 'メールアドレスの形式が正しくありません';
          return;
        }
      });
    });
    console.log(errorMsg);
    return errorMsg;
  }

  const _data = function(item) {
    const targets = $(item);
    if (targets.length === 0) {
      return;
    }
    targets.each((i, e) => {
      const target = $(e);
      let data = [];
      let errorFlg = false;
      target.find('.multiinput-wrapper').each((i, e) => {
        const value = $(e).find('input').val();
        if (value) {
          if (!_isValidEmail(value)) {
            errorFlg = true;
            return;
          }
          data.push(value);
        }
      });
      if (errorFlg) {
        talkmgr.toastError('メールアドレスの形式が正しくありません');
        return;
      }
      target.find('input[type="hidden"]').val(JSON.stringify(data));
    });
  }

  return {
    init: function(item) {
      _init(item);
      _initEvent();
    },
    data: function(item) {
      _data(item);
    },
    validate: function(item) {
      const errorMsg = _validate(item);
      if (errorMsg) {
        talkmgr.toastError(errorMsg);
        return false;
      }
      return true;
    }
  }
}();

const AddressInput = function(){
  const _ITEM_LIST = [
    {name: 'postal_code', label: '郵便番号', require: true},
    {name: 'prefectures', label: '都道府県', require: true},
    {name: 'city', label: '市区町村', require: true}, 
    {name: 'building', label: '建物', require: false},
  ];

  const _itemTemplate = `<div class="lines-container" style="margin-top:0">
    <div class="basic-label">{label}</div>
  </div>`;

  const HIDDEN_INPUT = `<input type="hidden" name="{name}" />`;

  const _init = function(item) {
    const targets = $(item);
    if (targets.length === 0) {
      return;
    }
    targets.each((i, e) => {
      const target = $(e);
      target.attr('style', 'display:flex;flex-direction:column;gap:18px;flex:1');
      target.children().remove();
      const dataName = target.data('name');
      if (!dataName) {
        return;
      }
      const value = target.data('value');
      _ITEM_LIST.forEach((item) => {
        const input = $(`<input type="text" class="text-input-longer" name="${item.name}" />`);
        if (value && value[item.name]) {
          input.val(value[item.name]);
        }
        const html = _itemTemplate.replace(/{label}/g, item.label);
        let _html = $(html);
        _html.append(input);
        target.append(_html);
      })
      // add hidden input
      const hiddenInput = $(HIDDEN_INPUT.replace(/{name}/g, dataName));
      hiddenInput.val(JSON.stringify(value));
      target.append(hiddenInput);
    })
  }

  const _data = function(item) {
    const targets = $(item);
    if (targets.length === 0) {
      return '';
    }
    let errorMsg = '';
    targets.each((i, e) => {
      const target = $(e);
      let data = {};
      _ITEM_LIST.forEach((item) => {
        let value = target.find(`input[name="${item.name}"]`).val();
        if (item.name === 'postal_code') {
          value = value.replace(/[^0-9]/g, '');
        }
        data[item.name] = value;
      });

      Object.keys(data).forEach((key) => {
        if (!data[key] && _ITEM_LIST.find(item => item.name === key).require) {
          const label = _ITEM_LIST.find(item => item.name === key).label;
          errorMsg = `「${label}」を入力してください`;
          return;
        }
      });
      if (errorMsg === '') {
        $(target).find('input[type="hidden"]').val(JSON.stringify(data));
      } else {
        return;
      }
    });
    return errorMsg;
  }

  return {
    init: function(item) {
      _init(item);
    },
    data: function(item) {
      return _data(item);
    }
  }
}();

function htmlEntities( str, proc ) {
  if ( 'encode' === proc ) {
    str = $('<div/>').text( str ).html();
    return str.replace( '\'', '&apos;' ).replace( '"', '&quot;' );
  } else {
    return $("<div/>").html( str ).text();
  }
}