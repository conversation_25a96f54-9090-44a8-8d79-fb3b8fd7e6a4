const Diff = (() => {
  let _target = null;
  let _format_diff_datas = [];
  let _diff_type = null;
  let _item_options_map = {};

  const _init = async (target, type, code, seq = null) => {
    _target = target;
    _diff_type = type;
    if (typeof _item_options === 'object') {
      Object.keys(_item_options).forEach(key => {
        const item = _item_options[key];
        _item_options_map[item.code] = item;
        if (item.items) {
          Object.keys(item.items).forEach(subKey => {
            _item_options_map[subKey] = item.items[subKey];
          })
        }
      })
    }
    await _fetch_diff(type, code, seq);
  }

  const _initEvent = () => {
    $(document).off('click', '.js-delete-diff');
    $(document).on('click', '.js-delete-diff', function() {
      const id = $(this).data('id');
      if (!id) {
        return;
      }
      talkmgr.warning_delete('選択した変更履歴を削除しますか？', '', (e) => {
        if (e.button === 'confirm') {
          _delete_diff(id);
        }
      });
    });
  }
  
  const _fetch_diff = async (type, code, seq = null) => {
    let requestURL = `/admincontract/getdiffs?type=${type}&code=${code}`;
    if (seq) {
      requestURL += `&seq=${seq}`;
    }
    const res = await fetch(requestURL);
    const responseData = await res.json();
    if (responseData.result === 'success') {
      _format_diff(responseData.data);
    } else {
      console.error(responseData.error);
    }
  }

  const _format_diff = (datas) => {
    datas.forEach(data => {
      let diff_detail = [];
      if (_diff_type === 'contract') {
        diff_detail = _format_contract_diff(data.diff);
      } else if (_diff_type === 'client') {
        diff_detail = _format_client_diff(data.diff);
      } else if (_diff_type === 'invoice') {
        diff_detail = _format_invoice_diff(data.diff);
      } else {
        return;
      }
      _format_diff_datas.push({
        'id': data.id,
        'no': data.no,
        'diff_detail': diff_detail,
        'upd_user': data.upd_user,
        'upd_time': data.upd_time
      });
    });
  }

  const _format_contract_diff = (diff) => {
    let diffEls = [];
    if (diff.client_code) {
      if (diff.client_code.from && diff.client_code.to) {
        diffEls.push(`<div class="diff-item">取引先コード: ${diff.client_code.from} -> ${diff.client_code.to}</div>`);
      } else if (diff.client_code.new) {
        diffEls.push(`<div class="diff-item">取引先コード: 新規 - ${diff.client_code.new}</div>`);
      }
    }
    if (diff.invoice_code) {
      if (diff.invoice_code.from && diff.invoice_code.to) {
        diffEls.push(`<div class="diff-item">請求先コード: ${diff.invoice_code.from} -> ${diff.invoice_code.to}</div>`);
      } else if (diff.invoice_code.new) {
        diffEls.push(`<div class="diff-item">請求先コード: 新規 - ${diff.invoice_code.new}</div>`);
      }
    }
    if (diff.sales_representative) {
      if (diff.sales_representative.from && diff.sales_representative.to) {
        diffEls.push(`<div class="diff-item">営業担当: ${diff.sales_representative.from} -> ${diff.sales_representative.to}</div>`);
      } else if (diff.sales_representative.new) {
        diffEls.push(`<div class="diff-item">営業担当: 新規 - ${diff.sales_representative.new}</div>`);
      }
    }
    if (diff.cs_representative) {
      if (diff.cs_representative.from && diff.cs_representative.to) {
        diffEls.push(`<div class="diff-item">CS担当: ${diff.cs_representative.from} -> ${diff.cs_representative.to}</div>`);
      } else if (diff.cs_representative.new) {
        diffEls.push(`<div class="diff-item">CS担当: 新規 - ${diff.cs_representative.new}</div>`);
      }
    }
    if (diff.tax_rate) {
      if (diff.tax_rate.from && diff.tax_rate.to) {
        diffEls.push(`<div class="diff-item">消費税率: ${diff.tax_rate.from} -> ${diff.tax_rate.to}</div>`);
      } else if (diff.tax_rate.new) {
        diffEls.push(`<div class="diff-item">消費税率: 新規 - ${diff.tax_rate.new}</div>`);
      }
    }
    if (diff.memo) {
      if (diff.memo.from && diff.memo.to) {
        diffEls.push(`<div class="diff-item">メモ: ${diff.memo.from} -> ${diff.memo.to}</div>`);
      } else {
        diffEls.push(`<div class="diff-item">メモ: 新規 - ${diff.memo.new}</div>`);
      }
    }
    if (diff.items) {
      Object.keys(diff.items).forEach(seq => {
        const itemWrap = $('<div class="diff-item"></div>').text(`品目 - SEQ: ${seq}`);
        const item = diff.items[seq];
        Object.keys(item).forEach(key => {
          let itemName = key;
          if (key === 'item') {
            itemName = '品目';
          } else if (key === 'billing_account') {
            itemName = '請求対象アカウント';
          } else if (key === 'cost_type') {
            itemName = '費用種類';
          } else if (key === 'cost') {
            itemName = '費用';
          } else if (key === 'billing_start_date') {
            itemName = '請求開始日';
          } else if (key === 'billing_end_date') {
            itemName = '請求終了日';
          } else if (key === 'cost_detail') {
            itemName = '費用内訳';
          }
          let action = '';
          if (item[key]['new'] !== undefined) {
            action = `新規 - ${itemName}: `;
            const value = item[key]['new'];
            if (key === 'item') {
              action += `${_item_options_map[value]?.name}`;
            } else if (key === 'billing_account') {
              const bots = value.split(',');
              bots.forEach(bot => {
                action += `${_bot_options.find(item => item.code == bot)?.name}, `;
              })
              action = action.slice(0, -2);
            } else if (key === 'cost_type') {
              action += `${_cost_type_options.find(item => item.code == value).text}`;
            } else {
              action += `${value}`;
            }
          } else if (item[key]['delete'] !== undefined) {
            const value = item[key]['delete'];
            action = `削除 - ${itemName}: `;
            if (key === 'item') {
              action += `${_item_options_map[value]?.name}`;
            } else if (key === 'billing_account') {
              const bots = value.split(',');
              bots.forEach(bot => {
                action += `${_bot_options.find(item => item.code == bot)?.name}, `;
              })
              action = action.slice(0, -2);
            } else if (key === 'cost_type') {
              action += `${_cost_type_options.find(item => item.code == value).text}`;
            } else {
              action += `${value}`;
            }
          } else if (item[key]['from'] !== undefined) {
            action = `編集 - ${itemName}: `;
            const from = item[key]['from'];
            const to = item[key]['to'];
            if (key === 'item') {
              action += `${_item_options_map[from]?.name} -> ${_item_options_map[to]?.name}`;
            } else if (key === 'billing_account') {
              const fromBots = from.split(',');
              const toBots = to.split(',');
              let fromText = '';
              let toText = '';
              fromBots.forEach(bot => {
                fromText += `${_bot_options.find(item => item.code == bot)?.name}, `;
              })
              toBots.forEach(bot => {
                toText += `${_bot_options.find(item => item.code == bot)?.name}, `;
              })
              fromText = fromText.slice(0, -2);
              toText = toText.slice(0, -2);
              action += `${fromText} -> ${toText}`;
            } else if (key === 'cost_type') {
              action += `${_cost_type_options.find(item => item.code == from).text} -> ${_cost_type_options.find(item => item.code == to).text}`;
            } else {
              action += `${from} -> ${to}`;
            }
          }
          itemWrap.append(`<div>${action}</div>`);
        })
        diffEls.push(itemWrap);
      })
    }
    return diffEls;
  }

  const _format_client_diff = (diff) => {
    let diffEls = [];
    if (diff.client_name) {
      diffEls.push(`<div class="diff-item">取引先名: ${diff.client_name.from} -> ${diff.client_name.to}</div>`);
    }
    if (diff.client_name_for_search) {
      diffEls.push(`<div class="diff-item">取引先名（検索用）: ${diff.client_name_for_search.from} -> ${diff.client_name_for_search.to}</div>`);
    }
    if (diff.department_name) {
      diffEls.push(`<div class="diff-item">取引先部門名: ${diff.department_name.from} -> ${diff.department_name.to}</div>`);
    }
    if (diff.department_name_for_search) {
      diffEls.push(`<div class="diff-item">取引先部門名（検索用）: ${diff.department_name_for_search.from} -> ${diff.department_name_for_search.to}</div>`);
    } 
    if (diff.country) {
      diffEls.push(`<div class="diff-item">取引国: ${diff.country.from} -> ${diff.country.to}</div>`);
    }
    if (diff.segment) {
      const from = _segment_maps[diff.segment.from];
      const to = _segment_maps[diff.segment.to];
      diffEls.push(`<div class="diff-item">セグメント: ${from} -> ${to}</div>`);
    }
    return diffEls;
  }

  const _format_invoice_diff = (diff) => {
    let diffEls = [];
    if (diff.invoice_code) {
      diffEls.push(`<div class="diff-item">請求先コード: ${diff.invoice_code.from} -> ${diff.invoice_code.to}</div>`);
    }
    if (diff.ver_start_date) {
      diffEls.push(`<div class="diff-item">世代開始日: ${diff.ver_start_date.from} -> ${diff.ver_start_date.to}</div>`);
    }
    if (diff.ver_end_date) {
      diffEls.push(`<div class="diff-item">世代終了日: ${diff.ver_end_date.from} -> ${diff.ver_end_date.to}</div>`);
    }
    if (diff.invoice_name) {
      diffEls.push(`<div class="diff-item">請求先名: ${diff.invoice_name.from} -> ${diff.invoice_name.to}</div>`);
    }
    if (diff.department_name) {
      diffEls.push(`<div class="diff-item">請求先部門名: ${diff.department_name.from} -> ${diff.department_name.to}</div>`);
    }
    if (diff.invoice_recipient_to) {
      diffEls.push(`<div class="diff-item">請求書送付先TO: ${diff.invoice_recipient_to.from} -> ${diff.invoice_recipient_to.to}</div>`);
    }
    if (diff.invoice_recipient_cc) {
      diffEls.push(`<div class="diff-item">請求書送付先CC: ${diff.invoice_recipient_cc.from} -> ${diff.invoice_recipient_cc.to}</div>`);
    }
    if (diff.postal_code) {
      diffEls.push(`<div class="diff-item">郵便番号: ${diff.postal_code.from} -> ${diff.postal_code.to}</div>`);
    }
    if (diff.prefectures) {
      diffEls.push(`<div class="diff-item">都道府県: ${diff.prefectures.from} -> ${diff.prefectures.to}</div>`);
    }
    if (diff.city) {
      diffEls.push(`<div class="diff-item">市区町村: ${diff.city.from} -> ${diff.city.to}</div>`);
    }
    if (diff.building) {
      diffEls.push(`<div class="diff-item">建物: ${diff.building.from} -> ${diff.building.to}</div>`);
    }
    if (diff.send_method) {
      const from = _send_method_options.find(item => item.code === diff.send_method.from).text;
      const to = _send_method_options.find(item => item.code === diff.send_method.to).text;
      diffEls.push(`<div class="diff-item">送付方法: ${from} -> ${to}</div>`);
    }
    if (diff.payment_timing) {
      const from = _payment_timing_options.find(item => item.code === diff.payment_timing.from).text;
      const to = _payment_timing_options.find(item => item.code === diff.payment_timing.to).text;
      diffEls.push(`<div class="diff-item">支払日タイミング: ${from} -> ${to}</div>`);
    }
    if (diff.payment_method) {
      const from = _payment_method_options.find(item => item.code === diff.payment_method.from).text;
      const to = _payment_method_options.find(item => item.code === diff.payment_method.to).text;
      diffEls.push(`<div class="diff-item">支払方法: ${from} -> ${to}</div>`);
    }
    return diffEls;
  }

  const _render_diff = () => {
    if (_target.find('.js-diff-table').length == 0) {
      const diffTable = $('<table class="table table-striped table-bordered table-hover js-diff-table"></table>');
      const diffThead = $('<thead></thead>');
      diffThead.append('<tr><th>No.</th><th>編集詳細</th><th>編集ユーザー</th><th>操作</th></tr>');
      diffTable.append(diffThead);
      diffTable.append('<tbody></tbody>');
      diffTable.appendTo(_target);
      talkmgr.initable(_target.find('.js-diff-table'), {
        ordering: false,
        order: [
          [0, 'desc']
        ],
        pageLength: 5,
      });
    }
    const table = _target.find('.js-diff-table').DataTable();
    table.clear();
    _format_diff_datas.forEach(data => {
      table.row.add([
        data.no, 
        $(`<div class="diff-detail"></div>`).append(data.diff_detail).prop('outerHTML'),
        data.upd_user + '<br>' + data.upd_time, 
        `<div class="btn round image delete js-delete-diff" data-id="${data.id}">削除</div>`
      ]);
    });
    table.draw();
  }

  const _delete_diff = (id) => {
    const requestURL = `/admincontract/deletediff`;
    const formData = new URLSearchParams();
    formData.append('id', id);
    talkmgr.block();
    fetch(requestURL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        Accept: 'application/json',
      },
      body: formData
    })
    .then(response => response.json())
    .then(responseData => {
      if (responseData.result === 'success') {
        _format_diff_datas = _format_diff_datas.filter(data => data.id != id);
        _render_diff();
        talkmgr.toast("S|削除しました");
      } else {
        talkmgr.toastError(responseData.error);
      }
    })
    .catch(error => {
      talkmgr.toastError(error);
    })
    .finally(() => {
      talkmgr.unblock();
    })
  }

  return {
    init: async (target, type, code, seq = null) => {
      if (!type || !code || target.length === 0) {
        target.hide();
        return;
      }
      _initEvent();
      await _init(target, type, code, seq);
      if (_format_diff_datas.length > 0) {
        _render_diff();
      } else {
        _target.hide();
      }
    },
    render: () => {
      _render_diff();
    }
  }
})();