$(function(){
  talkmgr.load();

  HistorySection.init($('.js-diffs'), _diffs);

  $(document).on('click', '.js-action-save', function() {
    const diff_datas = HistorySection.data();
    if (diff_datas === null) return;
    const commonData = getCommonData();
    if (commonData === null) return;
    const client_code = _client_code;
    const version = _version;
    const formData = new URLSearchParams();
    formData.append('attach_date', commonData.attach_date);
    formData.append('create_date', commonData.create_date);
    formData.append('remark', commonData.remark);
    formData.append('very_remark', commonData.very_remark);
    formData.append('diff_data', JSON.stringify(diff_datas));
    const fetchURL = `/admincontract/editcontractattachmentpost?client_code=${client_code}&version=${version}`;
    talkmgr.block();
    fetch(fetchURL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        Accept: 'application/json',
      },
      body: formData,
    })
    .then(response => response.json())
    .then(responseData => {
      console.log(responseData);
      if (responseData.result == 'success') {
        talkmgr.toast("S|更新しました");
        setTimeout(() => {
          location.reload();
        }, 1000);
      } else {
        talkmgr.unblock();
        talkmgr.toastError('エラーが発生しました');
      }
    })
  });

  $(document).on('click', '.js-action-back', function() {
    talkmgr.url(`/admincontract/contractattachment?client_code=${_client_code}`);
  })

  $(document).on('click', '.js-action-delete', function() {
    const client_code = _client_code;
    const version = _version;
    talkmgr.confirm('削除しますか？', '', (event) => {
      if (event.button == 'confirm') {
        const formData = new URLSearchParams();
        formData.append('client_code', client_code);
        formData.append('version', version);
        const fetchURL = `/admincontract/deletecontractattachmentpost`;
        talkmgr.block();
        fetch(fetchURL, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            Accept: 'application/json',
          },
          body: formData,
        })
        .then(response => response.json())
        .then(responseData => {
          if (responseData.result == 'success') {
            talkmgr.toast("S|削除しました");
            setTimeout(() => {
              talkmgr.url(`/admincontract/contractattachment?client_code=${client_code}`);
            }, 1000);
          } else {
            talkmgr.unblock();
            talkmgr.toastError('エラーが発生しました');
          }
        }) 
      }
    })
  })
});

const getCommonData = function() {
  let commonData = {};
  const attach_date = $('input[name="attach_date"]').val();
  if (attach_date === '') {
    talkmgr.toastError('別紙Bの添付日を入力してください');
    return null;
  }
  commonData.attach_date = attach_date;
  const create_date = $('input[name="create_date"]').val();
  if (create_date === '') {
    talkmgr.toastError('別紙Bの作成日を入力してください');
    return null;
  } 
  commonData.create_date = create_date;
  commonData.remark = $('textarea[name="remark"]').val();
  commonData.very_remark = $('textarea[name="very_remark"]').val();
  return commonData;
}

const HistorySection = function() {

  const _init = function(target, datas) {
    const itemsContainer = $('<div></div>').addClass('diffs-items-container');
    const items = datas.map(data => {
      return _create_item(data);
    })
    itemsContainer.append(items);
    const addBtn = $('<div class="btn round image add js-add_diff">編集履歴を追加</div>');
    target.append([itemsContainer, addBtn]);
  }

  const _create_item = function(diff_data) {
    const date = diff_data?.date ?? '';
    const message = diff_data.message ?? '';
    const itemWrapper = $('<div class="item-wrapper"></div>');
    const timepicker = talkmgr.create({type: 'datepicker', label: 'diff_time', value: date, class: `js-diff_time`}).css({'margin-right': 0});
    const input = $('<input type="text">').attr("name", "diff_message").addClass('text-input-longer').val(message);
    const deleteBtn = $('<div class="btn round image delete js-delete_diff">削除</div>');
    itemWrapper.append(timepicker).append(input).append(deleteBtn);
    if ((new Date(date)).getTime() <= (new Date(_create_date)).getTime()) {
      itemWrapper.append($('<div class="error" style="color:red">').text('※作成日以降の編集履歴は別紙PDFに反映されません'));
    }
    talkmgr.init(timepicker);
    return itemWrapper;
  }

  const _init_events = function() {
    $(document).on('click', '.diffs-items-container .js-delete_diff', function() {
      const item = $(this).closest('.item-wrapper');
      item.remove();
    })

    $(document).on('click', '.js-add_diff', function() {
      const newItem = _create_item({});
      $('.diffs-items-container').append(newItem);
    })

    $(".diffs-items-container").sortable({
      items: ".item-wrapper"
    })
  }

  const _data = function() {
    let diff_datas = [];
    let invalid = true;
    let error = '';
    $('.diffs-items-container .item-wrapper').each(function(index, item) {
      const date = $(item).find('.js-diff_time').val();
      const message = $(item).find('input[name="diff_message"]').val();
      if (date === '') {
        invalid = false;
        error = `${index + 1}目の日付を入力してください`;
        return;
      }
      if (message === '') {
        invalid = false;
        error = `${index + 1}目の内容を入力してください`;
        return;
      }
      diff_datas.push({date, message});
    })
    if (!invalid) {
      talkmgr.toastError(error);
      return null;
    }
    return diff_datas;
  }

  return {
    init: (target, datas) => {
      _init(target, datas);
      _init_events();
    },
    data: () => {
      return _data();
    }
  }
}();