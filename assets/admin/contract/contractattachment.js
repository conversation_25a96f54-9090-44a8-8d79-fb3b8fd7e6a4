$(function(){
  $(window).on('beforeunload', function() {
    talkmgr.unblock();
	});

  talkmgr.load();

  $('.monthly-item-table-section').find('.monthly-item-table-wrapper').slideUp();

  $(document).on('click', '.js-create', function() {
    const url = $(this).data('href');
    talkmgr.url(url);
  })

  $(document).on('click', '.js-download', function() {
    const attachmentInfo = $(this).closest('.attachment-info');
    const client_code = attachmentInfo.data('client_code');
    const version = attachmentInfo.data('version');
    if (!client_code || !version) {
      talkmgr.toastError('別紙Bの取引先コード、またはバージョンが取得できませんでした');
      return;
    }
    talkmgr.url(`/admincontract/contractattachmentpdf?client_code=${client_code}&version=${version}`, true);
  })

  $(document).on('click', '.js-preview', function() {
    const attachmentInfo = $(this).closest('.attachment-info');
    const client_code = attachmentInfo.data('client_code');
    const version = attachmentInfo.data('version');
    if (!client_code || !version) {
      talkmgr.toastError('別紙Bの取引先コード、またはバージョンが取得できませんでした');
      return;
    }
    talkmgr.block();
    fetch(`/admincontract/contractattachmentpdf?client_code=${client_code}&version=${version}&preview=1`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        Accept: 'application/json',
      },
    })
    .then(response => response.json())
    .then(responseData => {
      if (responseData.result == 'success') {
        talkmgr.modal({
          title: 'PDFプレビュー',
          description: `<iframe id='js-pdf' src="${'data:application/pdf;base64,' + responseData.data}" width="100%" height="100%"></iframe>`,
          buttons: [],
        }, (result) => {
        });
        $('.talkappi-modal-container').css('width', '80vw');
        $('.talkappi-modal-container').css('height', '80vh');
        $('.talkappi-modal-container').find('p').css('height', '-webkit-fill-available');
      } else {
        talkmgr.toastError('エラーが発生しました');
      }
    })
    .catch((error) => {
      console.error(error);
      talkmgr.toastError(`エラーが発生しました.`);
    })
    .finally(() => {
      talkmgr.unblock();
    });
  })

  $(document).on('click', '.js-edit', function() {
    const attachmentInfo = $(this).closest('.attachment-info');
    const client_code = attachmentInfo.data('client_code');
    const version = attachmentInfo.data('version');
    if (!client_code || !version) {
      talkmgr.toastError('別紙Bの取引先コード、またはバージョンが取得できませんでした');
      return;
    }
    talkmgr.url(`/admincontract/editcontractattachment?client_code=${client_code}&version=${version}`);
  })

  $(document).on('click', '.js-section-toggle-icon', function() {
    const section = $(this).closest('.monthly-item-table-section');
    if ($(this).hasClass('open')) {
      section.find('.monthly-item-table-wrapper').slideDown();
    } else {
      section.find('.monthly-item-table-wrapper').slideUp();
    }
  })
});