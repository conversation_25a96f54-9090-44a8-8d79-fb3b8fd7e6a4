$(function(){
  $(window).on('beforeunload', function() {
    talkmgr.unblock();
	});

  talkmgr.load();

  $(document).on('click', '.js-search', function() {
    talkmgr.submit();
  });
  
  $(document).on('click', '.js-new-client', function() {
    talkmgr.block();
    location.href = '/admincontract/client';
  })

  $(document).on('click', '.js-contractattachment', function() {
    const url = $(this).data('url');
    talkmgr.url(url);
  })

  $(document).on('click', '.js-edit-client', function() {
    const url = $(this).data('url');
    talkmgr.url(url);
  })
});