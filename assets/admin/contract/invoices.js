$(function(){
  $(window).on('beforeunload', function() {
    talkmgr.unblock();
	});
  
  talkmgr.load();

  $(document).on('click', '.js-search', function() {
    talkmgr.submit();
  })

  $(document).on('click', '.js-new-invoice', function(){
    talkmgr.block();
    talkmgr.url('/admincontract/invoice', false);
  })

  $(document).on('click', '.js-edit-invoice', function(){
    const url = $(this).data('url');
    talkmgr.block();
    talkmgr.url(url, false);
  })

  $(document).on('click', '.js-new-generation', function() {
    const _this = $(this);
    const invoice_code = _this.data('invoice_code');
    const ver_start_date = _this.data('start_date');
    const ver_end_date = _this.data('end_date');
    const dialog = {
      "title": "新世代を作成しますか",
      "body": "古い世代の終了日と新しい世代の開始日を入力してください。",
      "buttons": [
        {
          "caption": "キャンセル",
          "type": "close",
          "color": "white"
        },
        {
          "caption": "OK",
          "type": "confirm",
          "color": "blue"
        }
      ]
    };
    const content = createModalContent(ver_start_date, ver_end_date);
    TalkappiModal.modal(dialog, content, function(result) {
      if (result.button === 'confirm') {
        const oldGenerationEndDate = $('.new-generation-modal-body').find('.js-old-generation-end-date').val();
        const newGenerationStartDate = $('.new-generation-modal-body').find('.js-new-generation-start-date').val();
        if (oldGenerationEndDate === '') {
          talkmgr.toastError('古い世代の終了日を入力してください。');
          return;
        }
        if (newGenerationStartDate === '') {
          talkmgr.toastError('新しい世代の開始日を入力してください。');
          return;
        }
        if (oldGenerationEndDate > newGenerationStartDate) {
          talkmgr.toastError('新しい世代の開始日は古い世代の終了日より後にしてください。');
          return;
        }
        if (oldGenerationEndDate === newGenerationStartDate) {
          talkmgr.toastError('新しい世代の開始日は古い世代の終了日と同じにはできません。');
          return;
        }
        const formBody = new URLSearchParams();
        formBody.append('invoice_code', invoice_code);
        formBody.append('end_date', oldGenerationEndDate);
        formBody.append('start_date', newGenerationStartDate);
        fetch(`/admincontract/new_invoice_generation`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            Accept: 'application/json',
          },
          body: formBody,
        })
        .then(response => response.json())
        .then(responseData => {
          if (responseData.result == 'success') {
            const data = responseData.data;
            talkmgr.url(`/admincontract/invoice?id=${data.invoice_code}&seq=${data.seq}`, false);
          } else {
            throw new Error(responseData.error);
          }
        })
        .catch((error) => {
          talkmgr.unblock();
          talkmgr.toastError('エラーが発生しました');
        })
      }
    });
    talkmgr.init($('.new-generation-modal-body').find('.js-old-generation-end-date'));
    talkmgr.init($('.new-generation-modal-body').find('.js-new-generation-start-date'));
  })

  function formatDate(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0'); // Month is zero-based
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  function createModalContent(start_date, end_date) {
    let today = new Date();
    let next_day = new Date();
    next_day.setDate(next_day.getDate() + 1);
    if (!end_date) {
      if (start_date) {
        today = new Date(start_date);
        today.setDate(today.getDate() + 1);
        next_day = new Date(start_date);
        next_day.setDate(next_day.getDate() + 2);
      }
    } else {
      today = new Date(end_date);
      today.setDate(today.getDate() + 1);
      next_day = new Date(end_date);
      next_day.setDate(next_day.getDate() + 2);
    }

    const outer = $('<div></div>');
    const content = $('<div class="new-generation-modal-body"></div>');
    const description = $('<p style="margin-bottom:20px;">古い世代の終了日と新しい世代の開始日を入力してください。</p>');
    const formGroup1 = $('<div class="form-group"></div>');
    const label1 = $('<label>古い世代の終了日</label>');
    const startDateInput = $(`<input type="text" class="talkappi-datepicker js-old-generation-end-date" data-name="old-generation-end-date" data-date-format="yyyy-mm-dd" placeholder="yyyy-mm-dd" value="${formatDate(today)}">`);
    formGroup1.append(label1);
    formGroup1.append(startDateInput);
    const formGroup2 = $('<div class="form-group"></div>');
    const label2 = $('<label>新しい世代の開始日</label>');
    const endDateInput = $(`<input type="text" class="talkappi-datepicker js-new-generation-start-date" data-name="new-generation-start-date" data-date-format="yyyy-mm-dd" value="${formatDate(next_day)}">`);
    formGroup2.append(label2);
    formGroup2.append(endDateInput);
    content.append([description, formGroup1, formGroup2]);
    outer.append(content);
    return $(outer).html();
  }
});