$(function(){
  $(window).on('beforeunload', function() {
    talkmgr.unblock();
	});

  talkmgr.load();

  if (_latest_version.attach_date) {
    $('input[name="attach_date"]').val(_latest_version.attach_date);
  }
  if (_latest_version.create_date) {
    $('input[name="create_date"]').val(_latest_version.create_date);
  }

  $(document).on('click', '.js-action-save', function() {
    if (!_client_code) {
      talkmgr.toastError('取引先コードが取得できませんでした。');
      return;
    }
    const data = saveData();
    if (data === false) return;
    const formData = new URLSearchParams();
    Object.keys(data).forEach(key => formData.append(key, data[key]));
    formData.append('client_code', _client_code);
    formData.append('attachment_type', talkmgr.data('attachment_type'));
    talkmgr.block();
    fetch('/admincontract/contractattachmentpost', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        Accept: 'application/json',
      },
      body: formData,
    })
    .then(response => response.json())
    .then(responseData => {
      if (responseData.result == 'success') {
        talkmgr.toast('S|発行しました。');
        setTimeout(function() {
          talkmgr.url('/admincontract/contractattachment?client_code=' + _client_code);
        }, 1000);
      } else {
        talkmgr.unblock();
        const error = responseData.error ?? 'エラーが発生しました';
        talkmgr.toastError(error);
      }
    })
  })

  $(document).on('click', '.js-action-return', function() {
    talkmgr.url('/admincontract/contractattachment?client_code=' + _client_code);
  });

  function saveData() {
    let message = '';
    let data = {
      'attach_date': $('input[name="attach_date"]').val(),
      'create_date': $('input[name="create_date"]').val(),
    };
    if (data.attach_date == '') {
      message = 'Please select attach date.';
    }
    if (!message && data.create_date == '') {
      message = 'Please select create date.';
    }
    if (message) {
      talkmgr.toastError(message);
      return false;
    }
    return data;
  }
});