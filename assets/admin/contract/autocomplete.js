function setupAutocomplete(inputElement, suggestions) {
  const css = `
    .autocomplete-items {
      border: 1px solid #d4d4d4;
      border-bottom: none;
      border-top: none;
      z-index: 99;
      position: absolute;
      max-height: 200px;
      overflow-y: auto;
    }

    .autocomplete-items div {
      padding: 10px;
      cursor: pointer;
      background-color: #fff;
      border-bottom: 1px solid #d4d4d4;
    }

    .autocomplete-items div:hover {
      background-color: #e9e9e9;
    }

    .autocomplete-active {
      background-color: #1e90ff !important;
      color: white;
    }
  `;

  const style = document.createElement("style");
  style.innerText = css;
  document.head.appendChild(style);

  let currentFocus;
  
  const rect = inputElement.getBoundingClientRect();

  inputElement.setAttribute("autocomplete", "off");

  inputElement.addEventListener("input", function () {
    let autocompleteBox, suggestionItem, i, val = this.value;
    closeAllLists();
    if (!val) return false;
    currentFocus = -1;

    autocompleteBox = document.createElement("DIV");
    autocompleteBox.setAttribute("class", "autocomplete-items");
    this.parentNode.appendChild(autocompleteBox);
    for (i = 0; i < suggestions.length; i++) {
      if (suggestions[i].substr(0, val.length).toUpperCase() === val.toUpperCase()) {
        suggestionItem = document.createElement("DIV");
        suggestionItem.innerHTML = "<strong>" + suggestions[i].substr(0, val.length) + "</strong>";
        suggestionItem.innerHTML += suggestions[i].substr(val.length);
        suggestionItem.innerHTML += "<input type='hidden' value='" + suggestions[i] + "'>";

        suggestionItem.addEventListener("click", function () {
          inputElement.value = this.getElementsByTagName("input")[0].value;
          closeAllLists();
        });

        autocompleteBox.appendChild(suggestionItem);
      }
    }
    autocompleteBox.setAttribute("style", `width: ${rect.width}px; left: ${rect.left}px; top: ${rect.top + rect.height + 2}px; border-radius: 4px; border: solid 1px #e3e5e8;`);
  });

  inputElement.addEventListener("keydown", function (e) {
    let items = document.getElementsByClassName("autocomplete-items")[0];
    if (items) items = items.getElementsByTagName("div");
    if (e.keyCode == 40) {
      currentFocus++;
      addActive(items);
    } else if (e.keyCode == 38) {
      currentFocus--;
      addActive(items);
    } else if (e.keyCode == 13) {
      e.preventDefault();
      if (currentFocus > -1) {
        if (items) items[currentFocus].click();
      }
    }
  });

  function addActive(items) {
    if (!items) return false;
    removeActive(items);
    if (currentFocus >= items.length) currentFocus = 0;
    if (currentFocus < 0) currentFocus = items.length - 1;
    items[currentFocus].classList.add("autocomplete-active");
  }

  function removeActive(items) {
    for (let i = 0; i < items.length; i++) {
      items[i].classList.remove("autocomplete-active");
    }
  }

  function closeAllLists(elmnt) {
    const items = document.getElementsByClassName("autocomplete-items");
    for (let i = 0; i < items.length; i++) {
      if (elmnt != items[i] && elmnt != inputElement) {
        items[i].parentNode.removeChild(items[i]);
      }
    }
  }

  document.addEventListener("click", function (e) {
    closeAllLists(e.target);
  });
}
