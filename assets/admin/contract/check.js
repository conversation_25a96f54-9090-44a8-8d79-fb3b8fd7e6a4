const Check = function () {
  let _target = null;
  let _code = null;
  let _seq = null
  let _type = null;
  let _check_id = null;

  let _beforeValidation = null;

  const _init = function (code, type, seq) {
    _code = code;
    _type = type;
    _seq = seq;
    _fetchCheckStatus();
  }

  const _fetchCheckStatus = function () {
    let requestURL = '/admincontract/getcheckstatus?code=' + _code + '&type=' + _type;
    if (_seq) {
      requestURL += '&seq=' + _seq;
    }
    fetch(requestURL)
    .then(response => response.json())
    .then(data => {
      if (data.result === 'success') {
        if (data.data) {
          _check_id = data.data.id;
          _setCheckStatus(data.data);
        }
      }
    })
    .catch(error => console.error(error));
  }

  const _setCheckStatus = function (status) {
    const complete_time = status.complete_time;
    const last_complete_time = status.last_complete_time;
    _target.append('<div class="section-container"><div class="setting-header">チェック</div></div>');
    const checkflgs = [
      {'name': 'cs_checkflg', 'value': status.cs_checkflg, 'user': status.cs_checkuser},
      {'name': 'sales_checkflg', 'value': status.sales_checkflg, 'user': status.sales_checkuser},
      {'name': 'accountant_checkflg', 'value': status.accountant_checkflg, 'user': status.accountant_checkuser},
      {'name': 'admin_checkflg', 'value': status.admin_checkflg, 'user': status.admin_checkuser}
    ];
    checkflgs.forEach(checkflg => {
      const name = checkflg.name;
      let title = 'チェック';
      switch (name) {
        case 'cs_checkflg':
          title = 'CSチェック';
          break;
        case 'sales_checkflg':
          title = '営業チェック';
          break;
        case 'accountant_checkflg':
          title = '経理チェック';
          break;
        case 'admin_checkflg':
          title = '管理者チェック';
          break;
      }
      const value = checkflg.value;
      const user = checkflg.user;
      if (value !== null) {
        const lineContainer = $('<div class="lines-container"></div>');
        const basicLabel = $('<div class="basic-label"></div>');
        const checkContainer = $('<div class="check-container" style="display:flex;align-items:center;gap:4px;"></div>');
        const checkStatus = $('<div class="check-status"></div>');
        const checkButton = $(`<div class="btn light-blue round js-checkbutton" data-type="${name}">チェック</div>`);
        checkButton.on('click', function (e) {
          e.preventDefault();
          const type = $(this).data('type');
          talkmgr.confirm('チェックを完了しますか？', '<span style="color: red;">データが正しく保存されているか確認してください。</span>', function (event) {
            if (event.button == 'confirm') {
              _checkAction(type);
            }
          })
        });
        basicLabel.text(title);
        if (value === '1') {
          checkStatus.append('<img src="/assets/common/images/icon_success.svg" />');
          checkContainer.append(checkStatus);
          checkContainer.append(`<div>${user}により、チェックされました。</div>`);
        } else {
          checkStatus.append('<img src="/assets/common/images/icon_fail.svg" />');
          checkContainer.append(checkStatus);
          checkContainer.append(checkButton);
        }
        lineContainer.append(basicLabel);
        lineContainer.append(checkContainer);
        _target.append(lineContainer);
      }
    });
    if (status.warnings) {
      const warnings = JSON.parse(status.warnings);
      const allWarning = $('<div class="btn round image delete js-all-warning-delete">全てのアラートを削除</div>');
      allWarning.on('click', function (e) {
        e.preventDefault();
        talkmgr.confirm('全てのアラートを削除しますか？', '全てのアラートを削除すると元に戻すことはできません。', function (event) {
          if (event.button === 'confirm') {
            _warningDeleteAction(-1);
          }
        });
      });
      const warningsDiv = warnings.map((warning, seq) => {
        const warningItemWrapper = $('<div class="warning-item-wrapper"></div>').css({'display': 'flex', 'gap': '4px', 'align-items': 'center'}).data('seq', seq);
        const warningItem = $('<div class="warning-item"></div>').html(warning).css({'color': 'red'});
        const deleteItem = $('<div class="btn round image delete js-warning-delete">削除</div>');
        deleteItem.on('click', function (e) { 
          e.preventDefault();
          talkmgr.confirm('アラートを削除しますか？', 'アラートを削除すると元に戻すことはできません。', function (event) {
            if (event.button === 'confirm') {
              _warningDeleteAction(seq); 
            }
          });
        });
        warningItemWrapper.append(warningItem).append(deleteItem);
        return warningItemWrapper;
      });
      const warningWrapper = $('<div class="warning-wrapper"></div>').append(allWarning).append(warningsDiv).css({'display': 'flex', 'flex-direction': 'column', 'gap': '4px'});
      const basicLabel = $('<div class="basic-label"></div>').text('アラート');
      _target.append($('<div class="lines-container"></div>').append(basicLabel).append(warningWrapper));
    }
    if (complete_time) {
      _target.append(`<div class="lines-container"><div class='basic-label'>チェック完了日時</div><div>${complete_time}</div></div>`);
    }
    if (last_complete_time) {
      _target.append(`<div class="lines-container"><div class='basic-label'>前回チェック完了日時</div><div>${last_complete_time}</div></div>`);
    }
  }

  const _checkAction = function(type) {
    if (_beforeValidation && !_beforeValidation()) return;
    const requestURL = `/admincontract/checktoggle`;
    const warnings = _getCheckWarning();
    const formData = new URLSearchParams();
    formData.append('id', _check_id);
    formData.append('type', type);
    if (warnings.length > 0) {
      formData.append('warnings', JSON.stringify(warnings));
    }
    talkmgr.block();
    fetch(requestURL, {
      method: 'POST',
      body: formData
    })
    .then(response => response.json())
    .then(responseData => {
      if (responseData.result === 'success') {
        const data = responseData.new_data;
        if (data && Array.isArray(data) && data.length > 0) {
          let invoiceText = data.join(',') + 'の請求書が発行ました。';
          if (responseData.send_data && Array.isArray(responseData.send_data) && responseData.send_data.length > 0) {
            invoiceText += `${responseData.send_data.length}件の請求書が発行ました`;
          }
          talkmgr.toast(`S|チェックが完了しました。${invoiceText}`);
          setTimeout(() => {
            location.reload();
          }, 1000);
        } else {
          talkmgr.toast('S|チェックが完了しました。');
          setTimeout(() => {
            location.reload();
          }, 500);
        }
      } else {
        talkmgr.unblock();
        talkmgr.toast(`E|${responseData.error}`);
      }
    })
    .catch(error => {
      talkmgr.unblock();
      talkmgr.toast('E|エラーが発生しました。');
    });
  }

  const _warningDeleteAction = function(seq) {
    const requestURL = `/admincontract/deletewarning`;
    const formData = new URLSearchParams();
    formData.append('id', _check_id);
    formData.append('seq', seq);
    talkmgr.block();
    fetch(requestURL, {
      method: 'POST',
      body: formData
    })
    .then(response => response.json())
    .then(responseData => {
      if (responseData.result === 'success') {
        const seq = responseData.data;
        talkmgr.toast('S|アラートを削除しました。');
        setTimeout(() => {
          if (seq !== -1) {
            $('.js-checkcontainer').find('.warning-item-wrapper').hide();
          } else {
            $('.js-checkcontainer').find('.warning-item-wrapper').eq(seq).hide();
          }
          talkmgr.unblock();
        }, 500);
      } else {
        talkmgr.unblock();
        talkmgr.toast(`E|${responseData}`);
      }
    })
    .catch(error => {
      talkmgr.unblock();
      talkmgr.toast('E|エラーが発生しました。');
    })
  }

  let _getCheckWarning = function () {
    return [];
  }

  return {
    init: function (code, type, seq, checkWarning = null, beforeValidation = null) {
      if (!code) return;
      if (['client', 'invoice', 'contract'].indexOf(type) === -1) return;
      if (type === 'invoice' && !seq) return;
      if ($('.js-checkcontainer').length === 0) return;
      _target = $('.js-checkcontainer');
      if (checkWarning) {
        _getCheckWarning = checkWarning;
      }
      if (beforeValidation) {
        _beforeValidation = beforeValidation;
      }
      _init(code, type, seq);
    }
  }
}();