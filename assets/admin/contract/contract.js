$(function(){
  talkmgr.load();

  const setClientCode = $('select[name="client_code"]').val();
  if (setClientCode) {
    $('.js-goto-client').attr('href', `/admincontract/client?id=${setClientCode}`).removeClass('hide');
  } else {
    $('.js-goto-client').attr('href', 'javascript:void(0);').addClass('hide');
  }
  const setInvoiceCode = $('select[name="invoice_code"]').val();
  if (setInvoiceCode) {
    $('.js-goto-invoice').attr('href', `/admincontract/invoice?id=${setInvoiceCode}`).removeClass('hide');
  } else {
    $('.js-goto-invoice').attr('href', 'javascript:void(0);').addClass('hide');
  }
  
  $(document).on('change', 'select[name="client_code"]', function() {
    const newValue = $(this).val();
    if (newValue) {
      $('.js-goto-client').attr('href', `/admincontract/client?id=${newValue}`).removeClass('hide');
    } else {
      $('.js-goto-client').attr('href', 'javascript:void(0);').addClass('hide');
    }
  });

  $(document).on('change', 'select[name="invoice_code"]', function() {
    const newValue = $(this).val();
    if (newValue) {
      $('.js-goto-invoice').attr('href', `/admincontract/invoice?id=${newValue}`).removeClass('hide');
    } else {
      $('.js-goto-invoice').attr('href', 'javascript:void(0);').addClass('hide');
    }
  });

  const validateBeforeCheck = function() {
    if ($('.js-contract-container').find('select[name="client_code"]').val() === '') {
      talkmgr.toastError('取引先コードを設定してください');
      return false;
    }
    if ($('.js-contract-container').find('select[name="invoice_code"]').val() === '') {
      talkmgr.toastError('請求先コードを設定してください');
      return false
    }
    return true;
  }

  SeqSection.init($('.js-seq-sections'), _datas);
  SeqSection.setItemSet();

  Diff.init($('.js-diff-container'), 'contract', $('input[name="contract_id"]').val());

  Check.init($("input[name='contract_id']").val(), 'contract', undefined, checkUsageDate, validateBeforeCheck);

  talkmgr.data('.js-show_item_remarks', SeqSection.getShowRemark() ? 1 : 0);

  talkmgr.on('select', '.js-invoice-code', function(e) {
    const target = $(e.target.object[0]);
    const code = e.data.code;
    const invoiceSeq = _invoice_options.find(item => item.code == code).seq;
    const invoiceCheck = target.parent().find('.js-check-invoice');
    invoiceCheck.removeClass('hide');
    invoiceCheck.attr('data-seq', invoiceSeq).attr('data-code', code);
  });

  $(document).on('click', '.js-check-invoice', function() {
    const _this = $(this);
    const seq = _this.attr('data-seq');
    const code = _this.attr('data-code');
    talkmgr.url(`/admincontract/invoice?seq=${seq}&id=${code}`, true);
  });

  $(document).on('click', '.js-action-save', function() {
    let message = commonValidation();
    if (!message) {
      message = SeqSection.validation();
    }
    if (message) {
      talkmgr.toastError(message);
      return;
    }
    const commonData = getCommonData();
    const data = SeqSection.data();
    let contract_setting = {'seq_index': 0};
    let contractSet = [];
    let contractSetFlat = [];
    $('.js-contract_set-container').find('.js-contract-set').each((_, item) => {
      const setData = jsonParse($(item).attr('data-setdata'));
      if (setData) {
        if (!setData.setName) {
          talkmgr.toastError('セット名を入力してください');
          return;
        }
        if (setData.setSeqs.length < 2) {
          talkmgr.toastError('2つ以上の品目を選択してください');
          return;
        }
        if (setData.setSeqs.length !== Array.from(new Set(setData.setSeqs)).length) {
          talkmgr.toastError('品目が重複しています');
          return;
        }
        if (setData.setSeqs.some(seq => contractSetFlat.includes(seq))) {
          talkmgr.toastError('すでに同じ品目がセットに含まれています');
          return;
        }
        contractSet.push(setData);
        contractSetFlat.push(...setData.setSeqs);
      }
    })
    if (contractSet.length > 0) {
      contract_setting['contract_set'] = contractSet;
    }
    let billingPeriodResult = [];
    data.forEach(item => {
      const result = checkBillingPeriod(item);
      if (parseInt(item.seq) > parseInt(contract_setting.seq_index)) {
        contract_setting.seq_index = item.seq;
      }
      if (result) {
        billingPeriodResult.push(result);
      }
    });
    if (billingPeriodResult.length > 0) {
      talkmgr.confirm('現在の契約では、以下のような異常な設定になっている可能性があります。問題がないことを確認した上で、OKをクリックして保存を続行します。', `<div style="color:red">${billingPeriodResult.join('<br />')}</div>`, function(e) {
        if (e.button == 'confirm') {
          const postData = data.map(item => {
            return {...commonData, ...item};
          });
          contractPostAction(postData, contract_setting);
        }
      })
    } else {
      const postData = data.map(item => {
        return {...commonData, ...item};
      });
      contractPostAction(postData, contract_setting);
    }
  })

  const contractPostAction = function(postData, contract_setting) {
    const contract_id = $('input[name="contract_id"]').val();
    const formData = new URLSearchParams();
    formData.append('data', JSON.stringify(postData));
    formData.append('contract_setting', JSON.stringify(contract_setting));
    const fetchURL = contract_id ? `/admincontract/contractpost?id=${contract_id}` : '/admincontract/contractpost';
    talkmgr.block();
    fetch(fetchURL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        Accept: 'application/json',
      },
      body: formData,
    })
    .then(response => response.json())
    .then(responseData => {
      if (responseData.result == 'success') {
        const contractId = responseData.contract_id;
        if (contractId) {
          talkmgr.toast("S|保存しました");
          setTimeout(() => {
            location.href = `/admincontract/contract?id=${contractId}`;
          }, 1000);
        } else {
          if (responseData.message === 'invoicepayment') {
            talkmgr.toast("S|更新しました。請求書を作成しました。");
            setTimeout(() => {
              location.reload();
            }, 1000);
          } else {
            talkmgr.toast("S|更新しました");
            setTimeout(() => {
              location.reload();
            }, 1000);
          }
        }
      } else {
        talkmgr.unblock();
        talkmgr.toastError('エラーが発生しました');
      }
    })
    .catch((error) => {
      talkmgr.unblock();
      console.error(error);
      talkmgr.toastError('エラーが発生しました');
    })
  }

  $(document).on('click', '.js-action-back', function() {
    talkmgr.url('/admincontract/contracts');
  })

  $(document).on('click', '.js-goto-checklist', function() {
    talkmgr.block();
    talkmgr.url('/admincontract/checklist');
  })

  $(document).on('click', '.js-action-delete', function() {
    const type = $(this).data('type');
    let message = '削除';
    if (type === 'invalid') {
      message = '無効';
    }
    talkmgr.warning(`該当契約を${message}しますか？`, '', (event) => {
      if (event.button == 'confirm') {
        const formBody = new URLSearchParams();
        formBody.append('id', $('input[name="contract_id"]').val());
        formBody.append('type', type);
        talkmgr.block();
        fetch(`/admincontract/contractdelete`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            Accept: 'application/json',
          },
          body: formBody,
        })
        .then(response => response.json())
        .then(responseData => {
          if (responseData.result == 'success') {
            talkmgr.toast(`S|${message}しました`);
            setTimeout(() => {
              location.href = '/admincontract/contracts';
            }, 1000);
          }
        })
        .catch((error) => {
          talkmgr.unblock();
          console.error(error);
          talkmgr.toastError('エラーが発生しました');
        })
      }
    });
  })

  $(document).on('click', '.js-user-select', function() {
    const wrapper = $(this).closest('.selected_users_name_wrapper');
    const type = wrapper.attr('data-type');
    const name = `${type}_representative`;
    representative_select(wrapper, type, name);
  })

  talkmgr.on('select', '.js-show_item_remarks', function(e) {
    SeqSection.setShowRemark(e.data === 1);
  })

  function representative_select(target, type, name) {
    const _this = $(target);
    const value = $(`input[name='${name}']`).val();
    let selected_users_value = [];
    if (value) {
      selected_users_value = value.split(',');
    }
    const title = type == 'sales' ? '営業担当' : 'CS担当';
    talkmgr.selectDialog('', title, _user_options, selected_users_value, function(e) {
      if (e.button == 'confirm') {
        let selected_users = [];
        let spans = [];
        if (e.data.length > 1) {
          talkmgr.toastError('1名のみ選択可能です');
          return;
        }
        e.data.forEach(item => {
          selected_users.push(item.code);
          spans.push(`<span class="btn round light-blue js-user-select">${item.name}</span>`);
        });
        $(`input[name='${name}']`).val(selected_users.join(','));
        spans.push(`<span class="btn round light-blue js-user-select">設定</span>`);
        _this.html(spans);
      }
    });
  }

  $(document).on('click', '.js-contract-set', function() {
    const _this = $(this);
    let options = {};
    const flatItemOptions = SeqSection.getFlatItemOptions();
    $('.seq-section-container').each((_, item) => {
      const seq = $(item).attr('data-seq');
      const itemValue = $(item).find('input[name="item"]').val();
      const itemName = flatItemOptions[itemValue];
      options[seq] = `SEQ:${seq} - ${itemName}`;
    })
    const setData = jsonParse(_this.attr('data-setdata'));
    const dialog = {
      "title": "品目をセットする",
      "body": "品目をセットします。",
      "buttons": [
        {
          "caption": "キャンセル",
          "type": "close",
          "color": "white"
        },
        {
          "caption": "OK",
          "type": "confirm",
          "color": "blue"
        }
      ]
    }
    TalkappiModal.modal(dialog, createContractSetModal.init(options), function(result) {
      if (result.button === 'confirm') {
        const data = createContractSetModal.data();
        if (data.setName === '') {
          talkmgr.toastError('セット名を入力してください');
          return;
        }
        if (data.setSeqs.length < 2) {
          talkmgr.toastError('2つ以上の品目を選択してください');
          return;
        }
        if (data.setSeqs.length !== Array.from(new Set(data.setSeqs)).length) {
          talkmgr.toastError('品目が重複しています');
          return;
        }
        let contractSet = [];
        $('.js-contract_set-container').find('.js-contract-set').each((_, item) => {
          const setData = jsonParse($(item).attr('data-setdata'));
          if (setData) {
            contractSet.push(...setData.setSeqs);
          }
        })
        if (data.setSeqs.some(seq => contractSet.includes(seq))) {
          talkmgr.toastError('すでに同じ品目がセットに含まれています');
          return;
        }
        if (setData) {
          _this.attr('data-setdata', JSON.stringify(data));
          _this.text(data.setName);
        } else {
          const setItem = $('<div class="contract-set-item"></div>');
          setItem.append($('<span>').addClass('js-contract-set btn round light-blue').text(`セット: ${data.setName}`).attr('data-setdata', JSON.stringify(data)));
          setItem.append('<span class="btn round image delete js-contract-set-delete">削除</span>');
          _this.before(setItem);
        }
        SeqSection.setItemSet();
      }
    })
    $('.contract-set-modal-content').html(createContractSetModal.html(setData));
    createContractSetModal.initEvents();
  })

  $(document).on('click', '.js-contract-set-delete', function() {
    talkmgr.confirm('セットを削除しますか？', '', (event) => {
      if (event.button == 'confirm') {
        $(this).closest('.contract-set-item').remove();
        SeqSection.setItemSet();
      }
    }) 
  })
});

const checkBillingPeriod = function(data) {
  if (data.billing_start_date !== null && data.billing_end_date !== null) {
    const start = new Date(data.billing_start_date);
    const startDay = start.getDate();
    const end = new Date(data.billing_end_date);
    const endYear = end.getFullYear();
    const endMonth = end.getMonth() + 1;
    const endDay = end.getDate();
    const lastDayOfEndMonth = new Date(endYear, endMonth, 0).getDate();
    if (start.getTime() > end.getTime()) {
      return `SEQ:${data.seq} 請求開始日が請求終了日より後になっています`;
    }
    if (startDay === 1) {
      if (endDay !== lastDayOfEndMonth) {
        return `SEQ:${data.seq} 請求開始日と請求終了日が中途半端です`;
      } 
    } else {
      if (endDay !== (startDay - 1)) {
        return `SEQ:${data.seq} 請求開始日と請求終了日が中途半端です`;
      }
    }
  }
  return;
}

const commonValidation = function() {
  let message = '';
  const check_list = [
    {'name':'client_code','label':'取引先コード'},
    {'name':'sales_representative','label':'営業担当'},
    {'name':'cs_representative','label':'CS担当'}
  ];
  check_list.forEach(item => {
    if (message) return;
    if (item.name === 'client_code' || item.name === 'invoice_code') {
      $('.js-contract-container').find(`select[name='${item.name}']`).each((_, input) => {
        const value = $(input).val();
        if (value === '') {
          message = `${item.label}を設定してください`;
          return;
        }
      })
    } else {
      const value = $('.js-contract-container').find(`input[name='${item.name}']`).val();
      if (value === '') {
        message = `${item.label}を設定してください`;
        return;
      }
    }
  })
  return message;
}

const getCommonData = function() {
  const data = {
    'client_code': $('.js-contract-container').find('select[name="client_code"]').val(),
    'invoice_code': $('.js-contract-container').find('select[name="invoice_code"]').val(),
    'sales_representative': $('.js-contract-container').find('input[name="sales_representative"]').val(),
    'cs_representative': $('.js-contract-container').find('input[name="cs_representative"]').val(),
    'tax_rate': $('.js-contract-container').find('input[name="tax_rate"]').val(),
    'memo': $('.js-contract-container').find('textarea[name="memo"]').val(),
  }
  return data;
}

const checkUsageDate = function() {
  const checkItemList = ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '13'];
  const flat_item_options = {};
  Object.keys(_item_options).forEach(key => {
    const item = _item_options[key];
    if (item) {
      flat_item_options[key] = {
        'name': item.name,
      }
      if (item.items) {
        Object.keys(item.items).forEach(child => {
          flat_item_options[child] = {
            'name': item.items[child].name,
          }
        })
      }
    }
  })
  let warning = [];
  _datas.forEach(data => {
    const item = data.item;
    if(!checkItemList.includes(item)) return;
    const botIDs = data.billing_account.split(',');
    botIDs.forEach(botID => {
      if (data.billing_start_date !== null) {
        if (!_servicein_datas[botID] || !_servicein_datas[botID][item] || _servicein_datas[botID][item].length === 0) {
          warning.push(`<a href="/admin/contract?id=${botID}">請求対象アカウント: ${_bot_options.find(item => item.code == botID)?.name ?? '未設定請求対象アカウント'}</a> 品目: ${flat_item_options[item].name} 利用期間: 未設定`);
        }
      } else {
        if (_servicein_datas[botID] && _servicein_datas[botID][item] && _servicein_datas[botID][item].length > 0) {
          warning.push(`請求対象アカウント: ${_bot_options.find(item => item.code == botID)?.name ?? '未設定請求対象アカウント'} 品目: ${flat_item_options[item].name} 請求開始日: 未設定`);
        }
      }
    })
  })
  return warning;
}

const getSetDatas = function() {
  let setDatas = [];
  $('.js-contract_set-container').find('.js-contract-set').each((_, item) => {
    const setData = jsonParse($(item).attr('data-setdata'));
    if (setData) {
      setDatas.push(setData);
    }
  })
  return setDatas;
}

const jsonParse = function(data) {
  try {
    return JSON.parse(data);
  } catch (e) {
    return null;
  }
}

const SeqSection = function() {
  let _show_remark = false;
  let _item_relation = {};
  let _flat_item_options = {};

  const _DEFAILT_DATA = {
    item: '',
    seq: '',
    billing_account: '',
    cost_type: '',
    cost: '',
    cost_detail: '',
    billing_start_date: '',
    billing_end_date: '',
    item_remarks: '',
  }

  const ITEMS = [
    {'name':'item','label':'品目','type':'pulldown'},
    {'name':'billing_account','label':'請求対象アカウント','type':'select'},
    {'name':'cost_type', 'label': '費用種類', 'type': 'pulldown'},
    {'name':'cost','label':'費用','type':'number'},
    {'name':'cost_detail', 'label': '費用内訳', 'type': 'cost_detail'},
    {'name': 'usage_date', 'label': '利用期間', 'type': 'usage_date'},
    {'name':'billing_start_date','label':'請求開始日','type':'date'},
    {'name':'billing_end_date','label':'請求終了日','type':'date'},
    {'name':'item_remarks','label':'請求書表示用備考','type':'text'},
  ]

  const CONTAINER_TEMPLATE = '<div style="width:100%" class="seq-section-container"></div>';
  const LINE_CONTAINER = '<div class="lines-container"></div>';
  const SECTION_HEADER = '<div class="setting-header"></div>';
  const BASIC_LABEL = '<div class="basic-label"></div>';

  const _create_section = function(data, index = 1) {
    const container = $(CONTAINER_TEMPLATE);
    const seq = data.seq || index;
    container.attr('data-seq', seq);
    const flexXBetween = $('<div class="flex-wrapper js-seq-section-header" style="display:flex;align-items:center;gap:10px;"></div>');
    const deleteButton = $('<div class="btn round image delete js-delete-section">削除</div>');
    const header = $(SECTION_HEADER).text(`SEQ: ${seq}`);
    flexXBetween.append(header).append(deleteButton);
    container.append(flexXBetween);
    const itemWrapper = [];
    ITEMS.forEach(item => {
      const lineContainer = $(LINE_CONTAINER);
      const basicLabel = $(BASIC_LABEL);
      const name = item.name;
      const label = item.label;
      const type = item.type;
      const value = data[name] ? data[name] : '';
      basicLabel.text(label);
      if (type === "number") {
        const input = $("<input type='number'>").attr('name', name).attr('placeholder', label).addClass('text-input-longer');
        if (value) {
          input.val(value);
        }
        lineContainer.append(basicLabel).append(input);
        itemWrapper.push(lineContainer);
      } else if (type === 'date') {
        const _value = value ?? '';
        const datepicker = talkmgr.create({type: 'datepicker', label: name, value: _value, class: `js-${name}`});
        lineContainer.append(basicLabel).append(datepicker);
        talkmgr.init(datepicker);
        itemWrapper.push(lineContainer);
      } else if (type === 'pulldown') {
        if (name === 'item') {
          lineContainer.append(basicLabel)
          const pulldownWrapper = $('<div class="pulldown-wrapper" style="display:flex;gap:10px;"></div>');
          pulldownWrapper.append('<input type="hidden" name="item" value="">');
          lineContainer.append(pulldownWrapper);
          let code = value;
          const parentCode = _item_relation[code]?.["parent"] ?? code;
          let parentSource = [];
          Object.keys(_item_options).forEach(key => {
            parentSource.push({code: key, text: _item_options[key].name, sort: _item_options[key].sort});
          })
          parentSource = parentSource.sort((a, b) => a.sort - b.sort);
          const parentPulldown = $('<select>', { class: 'form-control js-parent-item' });
          pulldownWrapper.append(parentPulldown);
          parentPulldown.append($('<option>'));
          parentSource.forEach(item => {
            const option = $('<option>', { value: item.code, text: item.text });
            if (item.code == parentCode) {
              option.attr('selected', 'selected');
            }
            parentPulldown.append(option);
          })
          parentPulldown.select2({
            placeholder: "品目を選択してください",
            allowClear: true,
            width: '240px'
          })
          .on('change', function() {
            const selectedValue = $(this).val();
            const hasSecondLevel = _item_relation[selectedValue] && ((_item_relation[selectedValue]["children"] && _item_relation[selectedValue]["children"].length > 0) || _item_relation[selectedValue]["parent"]);
            pulldownWrapper.find('.js-children-item').remove();
            pulldownWrapper.find('input[name="item"]').val(selectedValue == '11' ? '' : selectedValue);
            if (hasSecondLevel) {
              const childrenSource = _item_relation[selectedValue]["children"];
              const childrenPulldown = $('<select>', { class: 'form-control js-children-item' });
              pulldownWrapper.append(childrenPulldown);
              childrenPulldown.append($('<option>'));
              childrenSource.forEach(item => {
                const option = $('<option>', { value: item.code, text: item.text });
                if (item.code == code) {
                  option.attr('selected', 'selected');
                }
                childrenPulldown.append(option);
              })
              childrenPulldown.select2({
                placeholder: "品目を選択してください",
                allowClear: true,
                width: '240px'
              })
              .trigger('change')
              .on('change', function() { 
                let selectingValue = $(this).val();
                if (selectingValue == '') {
                  const selectedValue = pulldownWrapper.find('input[name="item"]').val();
                  selectingValue = _item_relation[selectedValue]?.["parent"] ?? '';
                }
                pulldownWrapper.find('input[name="item"]').val(selectingValue == '11' ? '' : selectingValue);
              })
            }
          })
          .trigger('change')
          pulldownWrapper.find('input[name="item"]').val(code);
        }
        if (name === 'cost_type') {
          let _value = value ? value : '2';
          const costTypeSource = _cost_type_options;
          const costTypePulldown = talkmgr.create({type: 'pulldown', value: _value, source: costTypeSource, label: name, style: 'width:200px', class: 'js-cost_type'});
          lineContainer.append(basicLabel).append(costTypePulldown);
          talkmgr.init(costTypePulldown);
        }
        itemWrapper.push(lineContainer);
      } else if (type === 'select') {
        if (name === 'billing_account') {
          const jsBillingAccount = $('<div class="js-billing_account"></div>');
          const hiddenInput = $('<input type="hidden" name="billing_account">');
          const billingAccountWrapper = $('<div class="billing_account-wrapper"></div>');
          const selectButton = $('<span class="btn round light-blue js-bot-select">設定</span>');
          if (value) {
            const bots = value.split(',');
            bots.forEach(bot => {
              const botName = _bot_options.find(item => item.code == bot)?.name;
              if (!botName) return;
              billingAccountWrapper.append(`<span class="btn round light-blue js-bot-select">${botName}</span>`);
            });
            hiddenInput.val(value);
          }
          billingAccountWrapper.append(selectButton);
          jsBillingAccount.append(hiddenInput).append(billingAccountWrapper);
          lineContainer.append(basicLabel).append(jsBillingAccount);
          itemWrapper.push(lineContainer);
        }
      } else if (type === 'text') {
        if (name === 'item_remarks') {
          const textArea = $('<textarea></textarea>').attr('name', name).attr('placeholder', label).attr('rows', 2).addClass('form-control').addClass('js-item_remarks').css('width', '500px').css('max-width', '100%'); 
          if (value) {
            textArea.val(value);
          }
          lineContainer.append(basicLabel).append(textArea);
          if (!_show_remark) {
            lineContainer.addClass('hide');
          }
          itemWrapper.push(lineContainer);
        }
      } else if (type === 'cost_detail') {
        const costDetail = $('<div class="cost-detail"></div>');
        const botValues = data['billing_account'] ? data['billing_account'].split(',') : [];
        if (botValues.length >= 2) {
          botValues.forEach(bot_id => {
            const bot_name = _bot_options.find(item => item.code == bot_id)?.name;
            const cost = value[bot_id] ?? '';
            if (!bot_name) return;
            const costDetailItem = $('<div class="cost-detail-item"></div>');
            const costDetailLabel = $('<div class="cost-detail-label"></div>').text(bot_name).attr('data-bot_id', bot_id);
            const costDetailValue = $('<input class="cost-detail-value text-input" type="number" />').attr('data-cost', '').val(cost);
            costDetailItem.append(costDetailLabel).append(costDetailValue);
            costDetail.append(costDetailItem);
          })
        } else {
          lineContainer.hide();
        }
        lineContainer.append(basicLabel).append(costDetail);
        itemWrapper.push(lineContainer);
      } else if (type === 'usage_date') {
        const botValues = data['billing_account'] ? data['billing_account'].split(',') : [];
        const item = data['item'];
        let divs = [];
        botValues.forEach(bot_id => {
          const bot_name = _bot_options.find(item => item.code == bot_id)?.name;
          if (!bot_name) return;
          if (!_servicein_datas[bot_id] || !_servicein_datas[bot_id][item]) return;
          let usageData = _servicein_datas[bot_id][item];
          usageData = usageData.sort((a, b) => Date(b.start_date).getTime() - Date(a.start_date).getTime());
          const dataDivs = usageData.map(usage => {
            const usageStartDate = usage.start_date;
            const usageEndDate = usage.end_date ? usage.end_date : '現在';
            const usageDate = `${usageStartDate} ~ ${usageEndDate}`;
            const usageDiv = $('<div></div>').text(`${usageDate}`).css({'border': '1px solid #ddd', 'padding': '4px 8px', 'border-radius': '4px'});
            return usageDiv;
          })
          if (dataDivs.length > 0) {
            const usageWrapper = $('<div class="usage-wrapper"></div>').css({'display': 'flex', 'align-items': 'baseline', 'gap': '10px'});
            usageWrapper.append(`<div class="usage-header">${bot_name}</div>`);
            const dateWrapper = $('<div class="usage-date-wrapper"></div>').css({'display': 'flex', 'flex-direction': 'column', 'gap': '4px'});
            dateWrapper.append(dataDivs);
            usageWrapper.append(dateWrapper);
            divs.push(usageWrapper);
          }
        });
        if (divs.length > 0) {
          const usageContainer = $('<div class="usage-container"></div>').css({'display': 'flex', 'flex-direction': 'column', 'gap': '10px'});
          usageContainer.append(divs);
          lineContainer.append(basicLabel).append(usageContainer);
          itemWrapper.push(lineContainer);
        } else {
          lineContainer.hide();
        }
      }
    })
    container.append(itemWrapper);
    return container;
  }

  const _init = function(target, datas) {
    const sortedItemOptions = Object.keys(_item_options).sort((a, b) => _item_options[a].sort - _item_options[b].sort);
    sortedItemOptions.forEach(key => {
      const option = _item_options[key];
      if (option.items) {
        _item_relation[key] = { "children": [] };
        _flat_item_options[key] = option.name;
        const children = Object.keys(option.items).sort((a, b) => option.items[a].sort - option.items[b].sort);
        children.forEach(child => {
          _item_relation[key]["children"].push({
            code: child,
            text: option.items[child].name,
          });
          _item_relation[child] = { "parent": key };
          _flat_item_options[child] = option.items[child].name;
        })
      } else {
        _item_relation[key] = {};
        _flat_item_options[key] = option.name;
      }
    })
    let sections = [];
    if (!datas || datas.length === 0) {
      [_DEFAILT_DATA].forEach((data, index) => {
        sections.push(_create_section(data, index + 1));
      })
    } else {
      const showRemark = datas.some(data => data.item_remarks !== '');
      _show_remark = showRemark;
      datas.forEach((data, index) => {
        sections.push(_create_section(data, index + 1));
      })
    }
    target.children().remove();
    target.append(sections);

    // add button
    const addButton = $('<div style="display:flex;align-items:center;gap:4px;margin-top:20px" class="js-add-seq-section pointer"><div style="margin-right:0" class="add-icon"><span style="margin-top:0" class="icon-add"></span></div>追加</div>');
    target.append(addButton);
  }

  const _initevents = function () {
    // unbind events
    $(document).off('click', '.js-bot-select');
    $(document).off('click', '.js-delete-section');
    $(document).off('click', '.js-add-seq-section');
    $(document).off('click', '.js-check-invoice');

    // bind events
    $(document).on('click', '.js-delete-section', function() {
      const _this = $(this);
      const seq = _this.closest('.seq-section-container').attr('data-seq');
      const setDatas = getSetDatas();
      const set = setDatas.find(item => item.setSeqs.includes(seq));
      if (set) {
        talkmgr.toastError('セットに含まれている品目は削除できません');
        return;
      }
      talkmgr.confirm(`SEQ${seq}を削除しますか?`, '', function(e) {
        if (e.button == 'confirm') {
          _this.closest('.seq-section-container').remove();
        }
      });
    });

    $(document).on('click', '.js-bot-select', function() {
      const _this = $(this);
      const setDatas = getSetDatas();
      const container = _this.closest('.seq-section-container');
      const seq = container.attr('data-seq');
      const set = setDatas.find(item => item.setSeqs.includes(seq));
      const isFirstContainer = container.is('.seq-section-container:first-child');
      const value = _this.closest('.js-billing_account').find("input[name='billing_account']").val();
      let selected_bots = [];
      if (value) {
        selected_bots = value.split(',');
      }
      talkmgr.selectDialog('', '請求対象アカウント', _bot_options, selected_bots, function(e) {
        if (e.button == 'confirm') {
          let selecting_bots = [];
          let spans = [];
          let costDetails = [];
          let existedCostDetails = {};
          container.find('.cost-detail').children().each((_, item) => {
            const bot_id = $(item).find('.cost-detail-label').attr('data-bot_id');
            const cost = $(item).find('.cost-detail-value').val();
            existedCostDetails[bot_id] = cost;
          });
          e.data.forEach(item => {
            selecting_bots.push(item.code);
            const cost = existedCostDetails[item.code] ? existedCostDetails[item.code] : '';
            spans.push(`<span class="btn round light-blue js-bot-select">${item.name}</span>`);
            const costDetailItem = $('<div class="cost-detail-item"></div>');
            const costDetailLabel = $('<div class="cost-detail-label"></div>').text(item.name).attr('data-bot_id', item.code);
            const costDetailValue = $('<input class="cost-detail-value text-input" type="number" />').attr('data-cost', '').val(cost);
            costDetailItem.append(costDetailLabel).append(costDetailValue);
            costDetails.push(costDetailItem);
          });
          _this.closest('.js-billing_account').find("input[name='billing_account']").val(selecting_bots.join(','));  
          spans.push(`<span class="btn round light-blue js-bot-select">設定</span>`);
          _this.parent('.billing_account-wrapper').html(spans);
          container.find('.cost-detail').children().remove();
          if (selecting_bots.length >=2) {
            container.find('.cost-detail').append(costDetails);
            container.find('.cost-detail').closest('.lines-container').show();
          } else {
            container.find('.cost-detail').closest('.lines-container').hide();
          }
          if (isFirstContainer) {
            const otherContainers = $('.seq-section-container').not(container);
            otherContainers.each((_, otherContainer) => {
              const otherBillingAccount = $(otherContainer).find('.js-billing_account');
              const otherCostDetail = $(otherContainer).find('.cost-detail');
              const copyCostDetails = costDetails.map(item => item.clone());
              if (otherBillingAccount.find('input[name="billing_account"]').val() === '') {
                otherBillingAccount.find('input[name="billing_account"]').val(selecting_bots.join(','));
                otherBillingAccount.find('.billing_account-wrapper').html(spans);
                otherCostDetail.children().remove();
                if (selecting_bots.length >= 2) {
                  otherCostDetail.append(copyCostDetails);
                  otherCostDetail.closest('.lines-container').show();
                } else {
                  otherCostDetail.closest('.lines-container').hide();
                }
              }
            })
          }
          if (set) {
            const setSeqs = set.setSeqs;
            const otherSeqs = setSeqs.filter(item => item != seq);
            $('.js-seq-sections').find('.seq-section-container').each((_, section) => {
              const sectionSeq = $(section).attr('data-seq');
              if (otherSeqs.find(seq => seq == sectionSeq)) {
                const otherBillingAccount = $(section).find('.js-billing_account');
                const otherCostDetail = $(section).find('.cost-detail');
                const copyCostDetails = costDetails.map(item => item.clone());
                otherBillingAccount.find('input[name="billing_account"]').val(selecting_bots.join(','));
                otherBillingAccount.find('.billing_account-wrapper').html(spans);
                otherCostDetail.children().remove();
                if (selecting_bots.length >= 2) {
                  otherCostDetail.append(copyCostDetails);
                  otherCostDetail.closest('.lines-container').show();
                } else {
                  otherCostDetail.closest('.lines-container').hide();
                }
              }
            })
          }
        }
      });
    })

    $(document).on('click', '.js-add-seq-section', function() {
      const container = $(this).closest('.js-seq-sections');
      const duplicateValue = _getDuplicateValue();
      const defaultValue = {..._DEFAILT_DATA, ...duplicateValue};
      let lastSeq = container.find('.seq-section-container').last().attr('data-seq');
      const maxSeq = Math.max(parseInt(lastSeq) || 0, parseInt(_seq_index) || 0);
      $(this).before(_create_section(defaultValue, maxSeq + 1));
    });

    talkmgr.on('change', '.js-billing_start_date', function(e) {
      const newDate = e.data;
      const container = $(e.target.object).closest('.seq-section-container');
      const seq = container.attr('data-seq');
      const setDatas = getSetDatas();
      const set = setDatas.find(item => item.setSeqs.includes(seq));
      if (set) {
        const otherSeqs = set.setSeqs.filter(item => item != seq);
        otherSeqs.forEach(otherSeq => {
          const otherContainer = $('.seq-section-container[data-seq="' + otherSeq + '"]');
          const otherBillingStartDate = otherContainer.find('input[data-name="billing_start_date"]');
          otherBillingStartDate.val(newDate);
        })
      }
    })

    talkmgr.on('change', '.js-billing_end_date', function(e) {
      const newDate = e.data;
      const container = $(e.target.object).closest('.seq-section-container');
      const seq = container.attr('data-seq');
      const setDatas = getSetDatas();
      const set = setDatas.find(item => item.setSeqs.includes(seq));
      if (set) {
        const otherSeqs = set.setSeqs.filter(item => item != seq);
        otherSeqs.forEach(otherSeq => {
          const otherContainer = $('.seq-section-container[data-seq="' + otherSeq + '"]');
          const otherBillingEndDate = otherContainer.find('input[data-name="billing_end_date"]');
          otherBillingEndDate.val(newDate);
        })
      }
    })
  }

  const _getDuplicateValue = function() {
    const firstSection = $('.seq-section-container').eq(0);
    let value = {
      'billing_account': '',
      'billing_start_date': '',
      'billing_end_date': ''
    }
    if (firstSection.length > 0) {
      for (const name in value) {
        if (name === 'billing_account') {
          value[name] = firstSection.find(`input[name='${name}']`).val();
        } else {
          value[name] = firstSection.find(`input[data-name='${name}']`).val();
        }
      }
    }
    return value;
  }

  const _validation = function() {
    let message = '';
    const sections = $('.seq-section-container');
    sections.each((index, section) => {
      if (message) return;
      const seq = $(section).attr('data-seq');
      const lines = $(section).find('.lines-container');
      lines.each((_, line) => {
        if (message) return;
        const input = $(line).find('input');
        const name = input.attr('name');
        const value = input.val();
        if (name === 'item' && value === '') {
          message = `SEQ:${seq} 品目を設定してください`;
          return;
        }
        if (name === 'billing_account' && value === '') {
          message = `SEQ:${seq} 請求対象アカウントを設定してください`;
          return;
        }
      });
    });
    return message;
  }

  const _data = function() {
    const sections = $('.seq-section-container');
    let datas = [];
    sections.each((index, section) => {
      let _data = {
        seq: $(section).attr('data-seq'),
      };
      for (const name in _DEFAILT_DATA) {
        if (name === 'seq') {
          continue;
        }
        if (name === 'cost') {
          const value = $(section).find(`input[name='${name}']`).val();
          _data[name] = value ? value : 0;
          continue;
        }
        if (name === 'billing_start_date' || name === 'billing_end_date') {
          const value = $(section).find(`input[data-name='${name}']`).val();
          _data[name] = value ? value : null;
          continue;
        }
        if (name === 'item_remarks') {
          if (!_show_remark) {
            _data[name] = '';
          } else {
            const value = $(section).find(`textarea[name='${name}']`).val();
            _data[name] = value ? value : '';
          }
          continue;
        }
        if (name === 'cost_detail') {
          const costDetail = {};
          $(section).find('.cost-detail-item').each((_, item) => {
            const bot_id = $(item).find('.cost-detail-label').attr('data-bot_id');
            const cost = $(item).find('.cost-detail-value').val();
            if (cost !== '') {
              costDetail[bot_id] = cost;
            }
          });
          _data[name] = Object.keys(costDetail).length > 0 ? JSON.stringify(costDetail) : '';
          continue;
        }
        _data[name] = $(section).find(`input[name='${name}']`).val();
      }
      datas.push(_data);
    });
    return datas;
  }

  const _setItemSet = function() {
    const setDatas = getSetDatas();
    $('.js-seq-sections').find('.seq-section-container').each((_, section) => {
      const seq = $(section).attr('data-seq');
      const set = setDatas.find(item => item.setSeqs.includes(seq));
      $(section).find('.contract-set-name').remove();
      if (set) {
        $(section).find('.js-seq-section-header').append(`<span class="btn round light-blue contract-set-name">セット: ${set.setName}</span>`);
        $(section).find('.js-parent-item').addClass('disabled');
        $(section).find('.js-children-item').addClass('disabled');
      } else {
        $(section).find('.js-parent-item').removeClass('disabled');
        $(section).find('.js-children-item').removeClass('disabled');
      }
    })
  }

  return {
    init: function(target, datas = undefined) {
      _init(target, datas);
      _initevents();
    },
    validation: function() {
      return _validation();
    },
    data: function() {
      return _data();
    },
    setShowRemark: function(newValue) {
      _show_remark = newValue;
      $('.js-item_remarks').each((_, item) => {
        if (_show_remark) {
          $(item).closest('.lines-container').removeClass('hide');
        } else {
          $(item).closest('.lines-container').addClass('hide');
        }
      })
    },
    getShowRemark: function() {
      return _show_remark;
    },
    getFlatItemOptions: function() {
      return _flat_item_options;
    },
    setItemSet: function() {
      _setItemSet();
    }
  }
}();

const createContractSetModal = function() {

  let _itemOptions = null;
  
  const _init = function(itemOptions) {
    _itemOptions = itemOptions;
    return '<div><div class="contract-set-modal-content"></div></div>';
  }

  const _createModalContent = function(setData = null) {
    const content =$('.contract-set-modal-content');
    const setName = setData == null ? '' : setData.setName;
    const setSeqs = setData == null ? [] : setData.setSeqs;
    const setNameWrapper = $('<div class="lines-container"></div>');
    const setNameLabel = $('<div class="basic-label">セット名</div>');
    const setNameInput = $('<input type="text" class="text-input-longer" name="setName" placeholder="「INQUIRY+ORDER」は特別処理で、使用しないでください" />');
    setNameInput.attr('value', setName);
    setNameWrapper.append(setNameLabel).append(setNameInput);
    content.append(setNameWrapper);
    const setItemWrapper = $('<div class="lines-container"></div>');
    const setItemLabel = $('<div class="basic-label">セットSEQ</div>');
    const setItemSelectWrapper = $('<div></div>').css({'display':'flex','gap':'10px','flex-direction':'column'});
    let selectedSet = setSeqs.length > 0 ? setSeqs : ['', ''];
    selectedSet.forEach(value => {
      const pulldown = talkmgr.create({
        type: 'pulldown',
        label: 'item',
        value: value,
        source: _itemOptions,
        class: 'js-set-item',
        style: 'width: 200px;'
      })
      setItemSelectWrapper.append(pulldown);
      talkmgr.init(pulldown);
    })
    const addButton = $('<div class="action-button section btn-white js-button-add">追加</div>').css({'margin-left': '0'});
    setItemSelectWrapper.append(addButton);
    setItemWrapper.append(setItemLabel).append(setItemSelectWrapper);
    content.append(setItemWrapper);
    return content.html();
  }

  const _initEvents = function() {
    $(document).off('click', '.js-button-add');

    $(document).on('click', '.js-button-add', function() {
      const pulldown = talkmgr.create({
        type: 'pulldown',
        label: 'item',
        value: '',
        source: _itemOptions,
        class: 'js-set-item',
        style: 'width: 200px;'
      })
      $(this).before(pulldown);
      talkmgr.init(pulldown);
    })
  }

  const _data = function() {
    const setName = $('.contract-set-modal-content').find('input[name="setName"]').val();
    let setSeqs = [];
    $('.contract-set-modal-content').find('.js-set-item').each((_, item) => {
      const seq = $(item).attr('data-value');
      if (seq) {
        setSeqs.push(seq);
      }
    })
    return {
      setName,
      setSeqs
    }
  }

  return {
    init: function(itemOptions) {
      return _init(itemOptions);
    },
    initEvents: function() {
      _initEvents();
    },
    html: function(setData) {
      return _createModalContent(setData);
    },
    data: function() {
      return _data();
    }
  }
}()