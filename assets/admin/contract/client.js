$(function(){
  talkmgr.load();

  initialSegment();

  Diff.init($('.js-diff-container'), 'client', _client_code);

  Check.init(_client_code, 'client', null);

  $(document).on('click', '.js-user-select', function() {
    const wrapper = $(this).closest('.selected_users_name_wrapper');
    const type = wrapper.attr('data-type');
    const name = `${type}_representative`;
    representative_select(wrapper, type, name);
  })

  $(document).on('click', '.js-action-back', function() {
    talkmgr.block();
    talkmgr.url('/admincontract/clients');
  })

  $(document).on('click', '.js-goto-checklist', function() {
    talkmgr.block();
    talkmgr.url('/admincontract/checklist');
  })

  $(document).on('click', '.js-goto-contractattachment', function() {
    talkmgr.block();
    talkmgr.url('/admincontract/contractattachment?client_code=' + _client_code);
  })

  $(document).on('click', '.js-action-save', function() {
    const data = saveData();
    const formData = new URLSearchParams();
    Object.keys(data).forEach(key => {
      formData.append(key, data[key]);
    })
    const fetchURL = `/admincontract/clientpost` + (_client_code === '' ? '' : `/?id=${_client_code}`);
    talkmgr.block();
    fetch(fetchURL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        Accept: 'application/json',
      },
      body: formData,
    })
    .then(response => response.json())
    .then(responseData => {
      if (responseData.result == 'success') {
        const clientCode = responseData.client_code;
        if (clientCode) {
          talkmgr.toast("S|保存しました");
          setTimeout(() => {
            location.href = `/admincontract/client?id=${clientCode}`;
          }, 1000);
        } else {
          talkmgr.toast("S|更新しました");
          setTimeout(() => {
            location.reload();
          }, 1000);
        }
      } else {
        const error = responseData.error;
        talkmgr.unblock();
        let text = 'エラーが発生しました';
        if (error == 'duplicate') {
          text = '取引先コードが重複しています';
        } else {
          text = error;
        }
        talkmgr.toastError(text);
      }
    })
    .catch((error) => {
      talkmgr.unblock();
      talkmgr.toastError('エラーが発生しました');
    })
  })

  $(document).on('click', '.js-action-delete', function() {
    const type = $(this).data('type');
    let message = '削除';
    if (type == 'invalid') {
      message = '無効';
    }
    talkmgr.warning(`該当取引先を${message}しますか？`, '', (event) => {
      if (event.button == 'confirm') {
        const formBody = new URLSearchParams();
        formBody.append('id', _client_code);
        formBody.append('type', type);
        talkmgr.block();
        fetch(`/admincontract/clientdelete`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            Accept: 'application/json',
          },
          body: formBody,
        })
        .then(response => response.json())
        .then(responseData => {
          if (responseData.result == 'success') {
            talkmgr.toast(`S|${message}しました`);
            setTimeout(() => {
              location.href = '/admincontract/clients';
            }, 1000);
          } else {
            throw new Error(responseData.error);
          }
        })
        .catch((error) => {
          talkmgr.unblock();
          talkmgr.toastError('エラーが発生しました');
        })
      }
    })
  })

  talkmgr.on('select', '.js-parent-segment', function(e) {
    const target = $(e.target.object);
    const selectedItem = e.data.code;
    if (target.closest('.js-segment').find('.js-child-segment').length) {
      target.closest('.js-segment').find('.js-child-segment').remove();
    }
    if (_segment_options[selectedItem]['items']) {
      let childrenSources = [];
      const childItems = _segment_options[selectedItem].items;
      Object.keys(childItems).forEach(key => {
        childrenSources.push({code: key, text: childItems[key].name});
      });
      childrenSources = childrenSources.sort((a, b) => a.code - b.code);
      const childFirstValue = childrenSources[0]['code'];
      const childrenPulldown = talkmgr.create({type:'pulldown', value:childFirstValue, source:childrenSources, class:'js-child-segment', style:'width:200px;'});
      target.closest('.js-segment').append(childrenPulldown);
      talkmgr.init(childrenPulldown);
      target.closest('.js-segment').find('input[name="segment"]').val(childFirstValue);
    } else {
      target.closest('.js-segment').find('input[name="segment"]').val(selectedItem);
    }
  })

  talkmgr.on('select', '.js-child-segment', function(e) {
    const target = $(e.target.object);
    const selectedItem = e.data.code;
    target.closest('.js-segment').find('input[name="segment"]').val(selectedItem);
  })

  function initialSegment() {
    $('.js-segment').children().remove();
    const hiddenInput = `<input type="hidden" name="segment" value="${_segment}">`;
    $('.js-segment').append(hiddenInput);
    if (_segment.length == 4) {
      const parentSegment = _segment.substring(0, 2);
      let parentSources = [];
      Object.keys(_segment_options).forEach(key => {
        parentSources.push({code: key, text: _segment_options[key].name});
      })
      parentSources = parentSources.sort((a, b) => a.code - b.code);
      const parentPulldown = talkmgr.create({type:'pulldown', value:parentSegment, source:parentSources, class:'js-parent-segment', style:'width:200px;'});
      $('.js-segment').append(parentPulldown);
      talkmgr.init(parentPulldown);
      let childrenSources = [];
      const childItems = _segment_options[parentSegment].items;
      if (childItems) {
        Object.keys(childItems).forEach(key => {
          childrenSources.push({code: key, text: childItems[key].name});
        })
        childrenSources = childrenSources.sort((a, b) => a.code - b.code);
        const childrenPulldown = talkmgr.create({type:'pulldown', value:_segment, source:childrenSources, class:'js-child-segment', style:'width:200px;'});
        $('.js-segment').append(childrenPulldown);
        talkmgr.init(childrenPulldown);
      }
    } else {
      let source = [];
      Object.keys(_segment_options).forEach(key => {
        source.push({code: key, text: _segment_options[key].name});
      });
      source = source.sort((a, b) => a.code - b.code);
      const pulldown = talkmgr.create({type:'pulldown', value:_segment, source:source, class:'js-parent-segment', style:'width:200px;'});
      $('.js-segment').append(pulldown);
      talkmgr.init(pulldown);
    }
  }

  function saveData() {
    let message = '';
    let data = {};
    $('.lines-container').each((_, container) => {
      const inputs = $(container).find('input');
      inputs.each((_, input) => {
        const name = $(input).attr('name');
        const value = $(input).val();
        data[name] = value;
      })
    });
    if (data['client_code'] == '') {
      message = '取引先コードを入力してください';
    }
    if (data['client_code'] != '' && /^[A-Z0-9]+$/.test(data['client_code']) == false) {
      message = '取引先コードは半角の大文字と数字を入力してください';
    }
    if (!message && data['client_name'] == '') {
      message = '取引先名を入力してください';
    }
    if (!message && data['client_name_for_search'] == '') {
      message = '取引先名(検索用)を入力してください';
    }
    if (message) {
      talkmgr.toastError(message);
      return;
    }
    return data;
  }
  
  function representative_select(target, type, name) {
    const _this = $(target);
    const value = $(`input[name='${name}']`).val();
    let selected_users_value = [];
    if (value) {
      selected_users_value = value.split(',');
    }
    const title = type == 'sales' ? '営業担当' : 'CS担当';
    talkmgr.selectDialog('', title, _user_options, selected_users_value, function(e) {
      if (e.button == 'confirm') {
        let selected_users = [];
        let spans = [];
        e.data.forEach(item => {
          selected_users.push(item.code);
          spans.push(`<span class="btn round light-blue js-user-select">${item.name}</span>`);
        });
        $(`input[name='${name}']`).val(selected_users.join(','));
        spans.push(`<span class="btn round light-blue js-user-select">設定</span>`);
        _this.html(spans);
      }
    });
  }
});