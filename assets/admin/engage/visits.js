$(function(){
  talkmgr.load();
  
  let table = $("table.js-data-table").DataTable({
    "bDestroy":true,
    "pageLength": -1,
    "paging": false,
    "searching": false,
    "info": false,
    "order": [0, 'desc']
  });
  _renderTableWithData(table, visitsgroups);

  $(document).on('click', '#searchButton', function(){
    talkmgr.submit();
  })

  const _fnUploadOKBtnClickHandler = function() {
    const filename = $("#talkappi-fup-modal-preview-title").text();

    setTimeout(function(){
      $.ajax({
        url:"/ajax/upload_csv_base64",
        type:"post",
        dataType:"json",
        data:{'type': 'csv', 'filename': filename.trim(), "csv_base64": $( "#upload_csv>input" ).val()},
        success:function(data){
            const linesSuccess = data.import_result?.info?.lines_success ?? 0;
            if (data.success && linesSuccess > 0) {
              talkmgr.toast('success', TalkappiMessageAdmin.common.message.success, MemberLocale.import_success);
              _rerenderTable(table);
            } else {
              talkmgr.toast('error', TalkappiMessageAdmin.common.message.error, MemberLocale.import_fail);
            }
        },
        error:function(XMLHttpRequest, textStatus, errorThrown) {
            alertmessage('error', TalkappiMessageAdmin.common.message.error, TalkappiMessageAdmin.common.message.error.operation_failed);
        }
      });
    }, 700);
  };
  $(document).on('click', "#talkappi-fup-modal-ok-btn", _fnUploadOKBtnClickHandler);

  const _fnCSVImport = function() {
    //TODO CSV import
    // console.log('TODO CSV import');
    // $('#boxCSVImport').modal('show');
    $('#boxCSVImport #upload_csv img.js-talkappi-fup-icon').trigger('click');
  }
  $( ":button#import" ).on( 'click', _fnCSVImport );
})

function _renderTableWithData(table, visitsgroups) {
  table.clear();
  visitsgroups.forEach(function(visitsgroup) {
    let operationHtml = 
    '<div style="display: flex; align-items: center; gap: 10px;">' +
        '<a class="link-animate" href="/' + _path + '/visitgroups?date=' + encodeURIComponent(visitsgroup.checkin_date) + '">' +
            '<div style="margin-top: 2px;" class="btn round image detail js-memo">' + MemberLocale.details + '</div>' +
        '</a>' +
        '<div style="margin-top: 2px;" class="btn round js-button-csv">' +
            '<span class="icon-export"></span>' + MemberLocale.export_all +
        '</div>' +
    '</div>';
    table.row.add([
        visitsgroup.checkin_date,
        '<a class="link-animate" style="text-decoration:underline;" href="/' + _path + '/visitgroups?date=' + encodeURIComponent(visitsgroup.checkin_date) + '">' + MemberLocale.count_of_reservation(visitsgroup.count) + '</a>',
        MemberLocale.room_list,
        MemberLocale.kitchen_remarks,
        operationHtml
    ]);
  });
  table.draw();
}

function _rerenderTable(table) {
  const requestData = {
    'start_date': $('input[name="start_date"]').val(),
    'end_date': $('input[name="end_date"]').val(),
    'bot_id': _mgt_bot_id,
  };
  talkmgr.servicePass('engage', 'getnewestvisits', 'POST', requestData)
  .then(response => response.json())
  .then(responseData => {
    if (responseData.result === 'success') {
      _renderTableWithData(table, responseData.data)
    } else {
      throw new Error('Error');
    }
  })
  .catch(error => {
    console.error('Error fetching newest visits groups:', error);
  })
}