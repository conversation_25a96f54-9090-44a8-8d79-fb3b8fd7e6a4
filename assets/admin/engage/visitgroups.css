.visitgroups {
  display: flex;
  flex-direction: column;
  gap: 17px;
}
.visit-group {
  display: none;
}
.visit-group.filtered {
  display: block;
}

.member-info {
  background-color: #FFFFFF;
  border: 1px #E3E5E8 solid;
  box-sizing: border-box;
}
.member-info-container {
  display: flex;
}
.member-info-container .info-item {
  padding: 12px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex: 1;
}

.member-info .room {
  width: 168px;
  box-sizing: border-box;
  border-right: 1px #E3E5E8 solid;
}
.member-info .room .room-number {
  font: 15px;
  color: #000000;
  font-weight: 600;
}

.member-info .customer-info-1 {
  border-right: 1px #E3E5E8 solid;
}

.member-info .visit-history-info {
  border-right: 1px #E3E5E8 solid;
}

.member-info .customer-info-1 .name {
  display: flex;
  gap: 12px;
}
.member-info .customer-info-1 .name a {
  text-decoration: underline;
}
.member-info .customer-info-1 .info {
  display: flex;
  gap: 4px;
}
.member-info .customer-info-1 .info span {
  padding-right: 4px;
  border-right: 1px solid #646464;
}
.member-info .customer-info-1 .info span:last-child {
  padding-right: 0;
  border-right: none;
}

.member-info .customer-info-2 {
  border-right: 1px #E3E5E8 solid;
}
.member-info .customer-info-2 ul {
  padding: 0;
  margin: 0;
}
.member-info .customer-info-2 ul li {
  display: flex;
  align-items: center;
}
.member-info .customer-info-2 ul li::before {
  content: "◻";
  display: block;
}

.member-info .visit-history-info {

}
.member-info .visit-history-info .visit-times-info {
  font-weight: 600;
}

.ref-info {
  background-color: #FFFFFF;
  border: 1px solid orange;
  box-sizing: border-box;
  display: none;
}

.ref-info-container {
  display: flex;
}

.ref-info-title {
  display: flex;
  padding: 8px;
  align-items: center;
  align-self: stretch;
  background-color: #FFE6D6;
  border-right: 1px solid orange;
  writing-mode: vertical-rl;
  justify-content: center;
}

.ref-info-item {
  display: flex;
  flex-direction: column;
  flex: 1;
  border-right: 1px solid orange;
  /* gap: 4px; */
}

.ref-info-item:last-child {
  border-right: none;
}

.ref-info-item .item-head {
  padding: 8px;
  background-color: #FFE6D6;
  border-bottom: 1px solid orange;
}

.ref-info-item .item-body {
  padding: 8px;
  background: linear-gradient(0deg, rgba(255, 230, 214, 0.20) 0%, rgba(255, 230, 214, 0.20) 100%), #FFF;
}

.ref-info-item .item-body .item-data {
  font-size: 12px;
  color: #000000;
}

.no-data {
  color: #A1A4AA;
}

.visit-info {
  background-color: #FFFFFF;
  border: 1px #E3E5E8 solid;
  border-top: none;
  box-sizing: border-box;
}
.visit-info-container {
  display: flex;
}
.visit-info-item {
  display: flex;
  flex-direction: column;
  flex: 1;
  border-right: 1px solid #E3E5E8;
  gap: 4px;
}
.visit-info-item:last-child {
  border-right: none;
}
.visit-info-item .item-head {
  padding: 8px;
  background-color: #F6F7F9;
  border-bottom: 1px solid #E3E5E8;
}
.visit-info-item .item-head:last-child {
  border-right: none;
}
.visit-info-item .item-body {
  padding: 8px;
}
.visit-info-item .item-body .item-data {
  font-size: 12px;
  color: #000000;
}
.plan-info .reservation-channel {
  color: #3D3F45;
  font-size: 11px;
}
.payment-info .pre-payment-data {
  display: flex;
  align-items: center;
}
.payment-info .pre-payment-data::before {
  content: "◻";
  display: block;
}
.transportAndArrival-info .item-body,
.dinner-info .item-body {
  display: grid;
  grid-template-rows: 1fr 1fr;
  height: 100%;
  padding: 0;
}
.transportAndArrival-info .transportation-pickup-info,
.dinner-info .dinner-time-info {
  border-bottom: 1px solid #E3E5E8;
  padding: 8px;
}
.transportAndArrival-info .arrived-time-info,
.dinner-info .addition-food-info {
  padding: 8px;
}
.food_notes .item-body,
.customer_notes .item-body,
.room_notes .item-body,
.other_notes .item-body,
.tourist_spot .item-body,
.support-history-info .item-body
{
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-start;
  height: 100%;
}
.visit-info-item .item-body .edit-btn {
  padding: 0;
  border: none;
  background-color: unset;
  color: #245BD6;
  text-decoration: underline;
  cursor: pointer;
}
.additional-info ul {
  padding: 0;
  margin: 0;
}
.additional-info ul li {
  display: flex;
  align-items: flex-start;
}
.additional-info ul li::before {
  content: "◻";
  display: block;  
}
.additional-info ul li span {
  font-size: 12px;
  color: #000000;
}
.additional-info ul li span.info-key {
  display: flex;
  align-items: center;
  gap: 2px;
  margin-right: 2px;
}
.additional-info ul li span.info-key::after {
  content: "|";
  display: block;
}
.support-history-info .support-history-items {
  display: flex;
  flex-direction: column;
  gap: 4px;
}
.support-history-info .support-history-items .support-item {
  border-radius: 4px;
  padding: 4px;
  background-color: #EBEDF2;
}

.modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
}

.modal > .modal-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 20px;
  background-color: #F6F7F9;
  width: 350px;
  height:200px;
  border-radius: 5px;
}

.modal-buttons {
  gap:10px;
}

button {

}

#js-save-member-notes-modal,
#js-close-member-notes-modal {
  display: flex;
  border-radius: 5px;
  height: 2.5em;
  align-items: center;
  padding: 10px 15px;
  margin-top: 10px;
}