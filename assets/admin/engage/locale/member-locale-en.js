const MemberLocale = function () {
  return {
    food_notes_label: '料理備考',
    customer_notes_label: '顧客備考',
    room_notes_label: '部屋備考',
    other_notes_label: 'その他備考',
    tourist_spot_label: '観光地',
    count_of_reservation: (count) => `${count} group`,
    room_list: 'Room List',
    kitchen_remarks: 'Kitchen remarks',
    details: 'Details',
    export_all: 'Batch export',
    import_success: 'Data import was successful.',
    import_fail: 'Failed to import data.',
  }
}();