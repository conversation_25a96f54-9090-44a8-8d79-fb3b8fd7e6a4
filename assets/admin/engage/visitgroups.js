$(function(){
  talkmgr.load();

  talkmgr.servicePass('engage', 'getvisitgroupsdata', 'POST', {
    'date': _date,
    'bot_id': _mgt_bot_id
  })
  .then(response => {
    talkmgr.block();
    return response.json()
  })
  .then(responseData => {
    if (responseData['result'] === 'success') {
      VisitGroup.init(responseData['data']);
      VisitGroup.render();
    } else {
      throw new Error(responseData['error']);
    }
  })
  .catch(error => {
    console.error(error);
    talkmgr.toast('error', 'エラー', error);
  })
  .finally(() => {
    talkmgr.unblock();
  })

  $(document).on("keydown", ".js-search", function(event) {
    if(event.keyCode == 13) {
      event.preventDefault();
      return false;
    }
    const newKeyword = $(this).val();
    VisitGroup.filter(newKeyword);
  });

  $(document).on("keyup", ".js-search", function(event) {
    const newKeyword = $(this).val();
    VisitGroup.filter(newKeyword);
  });

  $(document).on('click', '.ref-switch', function() {
    const container = $(this).find('.ref-switch-container');
    if (container.hasClass('ref-switch-on')) {
      container.removeClass('ref-switch-on').addClass('ref-switch-off');
      $('.ref-info').hide(); 
    } else {
      container.removeClass('ref-switch-off').addClass('ref-switch-on');
      $('.ref-info').show(); 
    }
  });  

  talkmgr.on('select', 'sort', function(e){
    let sortDesc = 'asc';
    if (e.data.code === '01') {
      // arrival_time asc
      sortDesc = 'asc';
    } else if (e.data.code === '02') {
      // arrival_time desc
      sortDesc = 'desc';
    } else if (e.data.code === '03') {
      // room_number asc
      sortDesc = 'r_asc';
    }
    VisitGroup.sort(sortDesc);
  })

  $(document).on("click", ".js-edit-food_notes", function () {
    createModal($(this), "料理備考");
  });
  $(document).on("click", ".js-edit-customer_notes", function () {
    createModal($(this), "顧客備考");
  });
  $(document).on("click", ".js-edit-room_notes", function () {
    createModal($(this), "部屋備考");
  });
  $(document).on("click", ".js-edit-other_notes", function () {
    createModal($(this), "その他備考");
  });
  $(document).on("click", ".js-edit-tourist_spot", function () {
    createModal($(this), "観光地");
  });

  // cancel 処理
  $(document).on("click", "#js-close-member-notes-modal", function () {
    $(".js-member-notes-modal").hide();
  });

  // save 処理
  $(document).on("click", "#js-save-member-notes-modal", function () {
    var source_element = $(".js-member-notes-modal").data('sourceElement');
    var visit_id = $(source_element).closest('[data-visit_id]').data('visit_id');
    var updated_note = $(".js-member-notes-modal textarea").val();

    let column_name;
    if ($(source_element).hasClass("js-edit-food_notes")) {
      column_name = "food_notes";
    } else if ($(source_element).hasClass("js-edit-room_notes")) {
      column_name = "room_notes";
    } else if ($(source_element).hasClass("js-edit-other_notes")) {
      column_name = "other_notes";
    } else if ($(source_element).hasClass("js-edit-tourist_spot")) {
      column_name = "tourist_spot";
    } else if ($(source_element).hasClass("js-edit-customer_notes")) {
      column_name = "customer_notes";
    }

    // DBに登録する
    $.ajax({
      url: '/ajax/visitrgroups_notes',
      method: 'POST',
      data: {
        column_name: column_name,
        value: updated_note,
        visit_id: visit_id
      },
      success: function (response) {
        if (JSON.parse(response).result === 'success') {
          // テーブルに反映
          VisitGroup.update(visit_id, column_name, updated_note);
          $(".js-member-notes-modal").remove();
        }
      },
      error: function (error) {
        talkmgr.toast('error', 'エラー', '保存できませんでした。時間をおいて再度お試しください。');
        console.log(error);
      }
    });
  });
})

const VisitGroup = function() {
  let VisitGroupLang;

  let _datas = [];

  let _forFiltedDataMap;

  let _filteredCustomerIDs = [];

  let _keyword = "";

  let _sort = "asc";

  const visitGroupContainer = function() {
    const html = '<div class="visit-group"></div>';
    return $(html);
  }

  const memberInfoContainer = function() {
    const html = '<div class="member-info"><div class="member-info-container"></div></div>';
    return $(html);
  }

  const refInfoContainer = function() {
    const html = `<div class="ref-info">
      <div class="ref-info-container"></div>
    </div>`;
    return $(html);
  }

  const visitInfoContainer = function() {
    const html = `<div class="visit-info">
      <div class="visit-info-container"></div>
    </div>`;
    return $(html);
  }

  const memberInfoRoomItem = function(room_no, room_name, customers_detail, checkin_date, checkout_date) {

    const formatDate = function(date) {
      const parts = date.split('-');
      return parts[0] + '年' + parts[1] + '月' + parts[2] + '日';
    };
    
    const checkinAndoutDateString = function(checkin_date, checkout_date) {
      let resultString = "";
      if (checkin_date && checkout_date) {
        const formattedCheckin = formatDate(checkin_date);
        const formattedCheckout = formatDate(checkout_date);
    
        const checkinPrefix = checkin_date.substring(0, 7); // YYYY-MM
        const checkoutPrefix = checkout_date.substring(0, 7); // YYYY-MM
    
        resultString += formattedCheckin;
      
        if (checkinPrefix === checkoutPrefix) {
          resultString += "〜" + formattedCheckout.substring(5);
        } else {
          resultString += "〜" + formattedCheckout;
        }
      } else {
        resultString = checkin_date ? formatDate(checkin_date) + "〜" : "";
      }
      return resultString;
    };

    const formatPersonnelAssignment = function(customers_detail) {
      let personelAssignmentStr = customers_detail;
      if (customers_detail === null) {
        personelAssignmentStr = '-';
      } else if (typeof customers_detail === 'object') {
        let totalNum = 0;
        personelAssignmentStr = '';
        Object.keys(customers_detail).forEach(item => {
          let itemCount = customers_detail[item];
          if (itemCount > 0) {
            totalNum += itemCount;
            let itemStr = item === 'male' ? '男' : (item === 'female' ? '女' : '子');
            personelAssignmentStr += ` ${itemStr}${itemCount},`
          }
        })
        if (totalNum > 0) {
          personelAssignmentStr = personelAssignmentStr.slice(0, -1);
          personelAssignmentStr = `${totalNum}名 | ` + personelAssignmentStr;
        } else {
          personelAssignmentStr = '-';
        }
      }
      return personelAssignmentStr
    }

    const html = `<div class="room info-item">
                    <div class="room-number">
                      ${room_no} ${room_name || ''}
                    </div>
                    <div class="room-customers-count">
                      ${formatPersonnelAssignment(customers_detail)}
                    </div>
                    <div class="checkin-out-time">
                      ${checkinAndoutDateString(checkin_date, checkout_date)}
                    </div>
                  </div>`;
    return $(html);
  }

  const memberInfo1Item = function(name, furigana, infos, customer_id, tel) {
    const html = `<div class="customer-info-1 info-item">
      <div class="name">
        <a class="link-animate" href="/engage/memberdetail?id=${customer_id}">${name}</a>
        <span>${furigana}</span>
      </div>
      <div class="info"></div>
      <div class="info2"></div>
    </div>`;
    const htmlObj = $(html);
    htmlObj.find('.info').append(infos.map(info => {
      return `<span>${info}</span>`;
    }))
    if (tel) {
      htmlObj.find('.info2').append(`連絡先: ${tel}`)
    }
    return htmlObj;
  }

  const memberInfoVisitItem = function(visit_times, last_visit_info) {
    const html = `<div class="visit-history-info info-item">
      <div class="visit-times-info">来店${visit_times}回目</div>
    </div>`;
    const htmlObj = $(html);
    if (last_visit_info != null) {
      const last_visit_info_html = `<div class="last-time-info">
        <div>前回のご来店: ${last_visit_info.checkin_date}~${last_visit_info.checkout_date}</div>
        <div>前回のご部屋: ${last_visit_info.room}</div>
      </div>`;
      htmlObj.append($(last_visit_info_html));
    }
    return htmlObj;
  }

  const memberVisitButtons = function(visitId) {
    const html = `<div class="member-visit info-item">
      <div class='button-first-line' style="display: flex; flex-direction: row;">${_iconLink('/engage/visit?id=' + visitId + '&' + 'status=preCheckin', 'icon-reservation.svg', 'チェックイン前')}　${_iconLink('/engage/visit?id=' + visitId + '&' + 'status=checkin', 'icon-checkin.svg', 'チェックイン時へ')}</div>
      <div class='button-second-line' style="display: flex; flex-direction: row; margin-top: 12px">${_iconLink('/engage/visit?id=' + visitId + '&' + 'status=dining', 'icon-food.svg', 'ダイニングへ')}　${_iconLink('/engage/visit?id=' + visitId + '&' + 'status=checkout', 'icon-checkout.svg', 'チェックアウトへ')}</div>
    </div>`;
    const htmlObj = $(html);
    return htmlObj;
  }

  const _iconLink = function(href, icon, text) {
    // Return the HTML string for the link
    return `<a href=${href} target="_blank" style="display: flex; width: 150px; height: 24px; padding: 4px 12px; justify-content: center; align-items: center; gap: 6px; text-decoration: none; border-radius: 99px; background: #E3E5E8;">
              <img src="${'/assets/admin/engage/img/' + icon}" style="width: 20px; height: 20px; flex-shrink: 0;">
              <span style="color: #000; white-space: nowrap;">${text}</span>
            </a>`;
  }

  const refInfoTitle = function(titleText) {
    const html = `<div class="ref-info-title">${titleText}</div>`;
    return $(html);
  };
  
  const refInfoItem = function (type, title) {
    const html = `<div class="ref-info-item ${type}">
      <div class="item-head">${title}</div>
      <div class="item-body"></div>
    </div>`;
    return $(html);
  };

  const refInfoNotesInfoItem = function(column_name, notes, theItemObj = null) {
    let content = (!notes || notes.trim() === '') ? '<span class="no-data">なし</span>' : _util_escapeSpecialHtmls(notes);
    if (!theItemObj) {
      const column_name_label = `${column_name}_label`;
      const class_name = column_name;
      const item = refInfoItem(class_name, VisitGroupLang[column_name_label]);
      item.find('.item-body').append(`
          <div class="item-data">${content}</div>
      `);
      return item;
    } else {
      theItemObj.find('.item-body').find('.item-data').remove();
      theItemObj.find('.item-body').append(`
          <div class="item-data">${content}</div>
      `);
    }
  }

  const visitInfoItem = function (type, title, hasButton = false, buttonClass, buttonName) {
    const html = `<div class="visit-info-item ${type}">
      <div class="item-head">${title}</div>
      <div class="item-body"></div>
    </div>`;
    const htmlObj = $(html);
    if (hasButton) {
      // todo：「編集」ボタンで保存するデータをjson形式に変更する。対応完了するまで「編集」ボタンはhide。
      // htmlObj.find('.item-body').append(`<div class="edit-btn ${buttonClass}">${buttonName}</div>`);
      htmlObj.find('.item-body').append(`<div class="edit-btn ${buttonClass}"></div>`);
    }
    return htmlObj;
  }

  const visitInfoPlanInfoItem = function(plan, reservation_channel) {
    const item = visitInfoItem("plan-info", "プラン/媒体");
    item.find('.item-body').append(`
      <div class="item-data">${plan}</div>
      <div class="reservation-channel">${reservation_channel}</div>
    `);
    return item;
  }

  const visitInfoPaymentInfoItem = function(isPrepay, paymentMethod) {
    const item = visitInfoItem("payment-info", "入金状況");
    item.find('.item-body').append(`
      <div class="item-data pre-payment-data">前入金${isPrepay ? "あり" : "なし"}</div>
      <div class="item-data payment-method-data">${paymentMethod}</div>
    `);
    return item;
  }

  const visitInfoTransportAndArrivalItem = function(transportation, pickup, arrivalTime) {
    const item = visitInfoItem("transportAndArrival-info", "交通/到着");
    item.find('.item-body').append(`
    <div class="transportation-pickup-info">
      <div class="item-data">${transportation === null ? "" : transportation}</div>
      <div class="item-data">${pickup ? "送迎あり" : "送迎なし"}</div>
    </div>
    <div class="arrived-time-info">
      <div class="item-data">${arrivalTime}</div>
    </div>`);
    return item;
  }

  const visitInfoDinnerInfoItem = function(dinnerTime, additionalFoods) {
    const item = visitInfoItem("dinner-info", "夕食/追加料理");
    item.find('.item-body').append(`
    <div class="dinner-time-info">
      <div class="item-data">${dinnerTime}</div>
    </div>
    <div class="addition-food-info">
      <div class="item-data">${additionalFoods === null ? 'なし' : additionalFoods}</div>
    </div>`);
    return item;
  }

  const visitInfoNotesInfoItem = function(column_name, notes, theItemObj = null) {
    if (!theItemObj) {
      const column_name_label = `${column_name}_label`;
      const class_name = column_name;
      const edit_button_className = `js-edit-${column_name}`;
      const item = visitInfoItem(class_name, VisitGroupLang[column_name_label], true, edit_button_className, "編集");
      item.find('.item-body').find('.edit-btn').before(`
        <div class="item-data">${notes === '' ? 'なし' : _util_escapeSpecialHtmls(notes).replace(/\n/g, '<br>')}</div>
      `);
      return item;
    } else {
      theItemObj.find('.item-body').find('.item-data').remove();
      theItemObj.find('.item-body').find('.edit-btn').before(`
        <div class="item-data">${notes === '' ? 'なし' : _util_escapeSpecialHtmls(notes).replace(/\n/g, '<br>')}</div>
      `);
    }
  }

  const visitInfoTransportInfoItem = function(infos) {
    const item = visitInfoItem("transportation-info", "交通/到着情報");
    let value = `${infos.car_type ?? ''}${infos.car_number ? '<br>'+infos.car_number : ''}`;
    if (value === '') { 
      value = 'なし'
    }
    item.find('.item-body').append(`<div class="item-data">${value}</div>`);
    return item;
  }

  const visitInfoBodyInfoItem = function(infos) {
    const item = visitInfoItem("body-info", "サポート");
    let str = 'なし';
    if (infos) {
      const bodyInfos = Object.values(infos).find(info => info.key === 'body');
      if (bodyInfos?.value) {
        str = bodyInfos.value;
      }
    }
    item.find('.item-body').append(`<div class="item-data">${str}</div>`);
    return item;
  }

  const visitInfoSupportHistoryInfoItem = function(supportHistoryItems) {
    const item = visitInfoItem("support-history-info", "手配対応", true, "js-add-support-history", "追加");
    if (supportHistoryItems === null) {
      item.find('.item-body').find('.edit-btn').before('<div class="item-data">なし</div>');
    } else {
      item.find('.item-body').find('.edit-btn').before('<div class="support-history-items"></div>');
      const newSorts = Object.keys(supportHistoryItems).sort((a, b) => a - b);
      const supportItems = newSorts.map(index => {
        const support = supportHistoryItems[index];
        const date = new Date(support.date);
        return `<div class="support-item">${date.getMonth() + 1}/${date.getDate()} ${support.task}</div>`;
      })
      item.find('.item-body').find('.support-history-items').append(supportItems);
    }
    return item;
  }

  const _generateMap = function() {
    _forFiltedDataMap = new Map();
    _datas.forEach(data => {
      const member_info = data.member_info;
      const customer_id = member_info.customer_id;
      const member_info_keys = Object.keys(member_info);
      member_info_keys.forEach(keyName => {
        if (member_info[keyName] !== null) {
          const the_member_info = member_info[keyName];
          const value = _convertToString(the_member_info);
          if (value !== "") {
            _checkAndSetMap(value, customer_id, _forFiltedDataMap);
          }
        }
      })
      
      const visit_info = data.visit_info;
      const visit_info_keys = Object.keys(visit_info);
      visit_info_keys.forEach(keyName => {
        if (visit_info[keyName] !== null) {
          const the_visit_info = visit_info[keyName];
          let value = "";
          if (keyName === "additional_infos") {
            Object.values(the_visit_info).forEach(info => {
              _checkAndSetMap(info.key, customer_id, _forFiltedDataMap);
              _checkAndSetMap(info.value, customer_id, _forFiltedDataMap);
            })
          } else if (keyName === "support_history") {
            Object.values(the_visit_info).forEach(info => {
              _checkAndSetMap(info.task, customer_id, _forFiltedDataMap);
            })
          } else {
            value = _convertToString(the_visit_info);
            if (value !== "") {
              _checkAndSetMap(value, customer_id, _forFiltedDataMap);
            }
          }
        }
      })
    })
  }

  const _checkAndSetMap = function(value, customer_id, map) {
    if (map.has(value)) {
      let v = map.get(value);
      if (!v.includes(customer_id)) {
        map.set(value, [...v, customer_id]);
      }
    } else {
      map.set(value, [customer_id]);
    }
  }

  const _convertToString = function(data) {
    let convertedValue = "";
    if (typeof data === "string") {
      convertedValue = data;
    } else if (typeof data === "number") {
      convertedValue = `${data}`;
    } else if (typeof data === "object") {
      if (Array.isArray(data)) {
        convertedValue = data.join("##");
      }
    }
    return convertedValue;
  }

  const _render = function() {
    $('#visitgroups').empty();
    const visitGroupItems = _datas.map((visitGroupData, index) => {
      let visitGroupContainerHtmlObj = visitGroupContainer();
      const memberInfo = visitGroupData.member_info;
      visitGroupContainerHtmlObj.attr('data-member_id', memberInfo.customer_id);
      visitGroupContainerHtmlObj.attr('data-visit_id', visitGroupData.visit_id);
      let memberInfoContainerHtmlObj = memberInfoContainer();
      memberInfoContainerHtmlObj.find('.member-info-container').append([
        memberInfoRoomItem(memberInfo.room_no, memberInfo.room_name, memberInfo.customers_detail, visitGroupData.visit_info.checkin_date, visitGroupData.visit_info.checkout_date),
        memberInfo1Item(memberInfo.customer_name, memberInfo.customer_furigana, memberInfo.infos, memberInfo.customer_id, memberInfo.tel),
        memberInfoVisitItem(memberInfo.visit_times, memberInfo.last_visit_info),
        memberVisitButtons(visitGroupData.visit_id)
      ]);
      // ref info
      const refInfo = visitGroupData.ref_info;
      let refInfoContainerHtmlObj = refInfoContainer();
      refInfoContainerHtmlObj.find('.ref-info-container').append([
        refInfoTitle('満室御礼参照'),
        refInfoNotesInfoItem('customer_notes', refInfo.customer_notes_pms),
        refInfoNotesInfoItem('room_notes', refInfo.room_notes_pms),
        refInfoNotesInfoItem('food_notes', refInfo.food_notes_pms),
        refInfoNotesInfoItem('other_notes', refInfo.other_notes_pms),
      ]);
      // visit info
      const visitInfo = visitGroupData.visit_info;
      let visitInfoContainerHtmlObj = visitInfoContainer();
      visitInfoContainerHtmlObj.find('.visit-info-container').append([
        visitInfoPlanInfoItem(visitInfo.plan, visitInfo.reservation_channel),
        visitInfoPaymentInfoItem(visitInfo.is_prepay, visitInfo.payment_method),
        visitInfoTransportAndArrivalItem(visitInfo.transportation, visitInfo.pickup, visitInfo.arrival_time),
        visitInfoTransportInfoItem(memberInfo),
        visitInfoBodyInfoItem(visitInfo.additional_infos),
        visitInfoSupportHistoryInfoItem(visitInfo.support_history),
        visitInfoDinnerInfoItem(visitInfo.dinner_time, visitInfo.additional_food),
        visitInfoNotesInfoItem('room_notes', visitInfo.room_notes),
        visitInfoNotesInfoItem('food_notes', visitInfo.food_notes),
        visitInfoNotesInfoItem('other_notes', visitInfo.other_notes),
      ]);

      visitGroupContainerHtmlObj.append([
        memberInfoContainerHtmlObj,
        refInfoContainerHtmlObj,
        visitInfoContainerHtmlObj
      ]);
      return visitGroupContainerHtmlObj;
    });
    $('#visitgroups').append(visitGroupItems);
  }

  const _filterData = function(keyword = null, type = null) {
    if (keyword !== null) {
      _keyword = keyword.trim().replaceAll('##', '');
    }
    let filteredCustomerIDs = [];
    if (_keyword !== "" && _keyword !== null) {
      let temp = [];
      _forFiltedDataMap.forEach((value, key) => {
        if (key.includes(_keyword)) {
          temp.push(...value);
        }
      })
      filteredCustomerIDs = [...new Set(temp)];
    } else {
      filteredCustomerIDs = _datas.map(data => data.member_info.customer_id);
    }
    return filteredCustomerIDs;
  }

  const _reFreshUI = function() {
    $('#visitgroups .visit-group').each(function(index, el){
      const customer_id = $(el).attr('data-member_id');
      const foundIndex = _filteredCustomerIDs.findIndex(id => id == customer_id);
      if (foundIndex !== -1) {
        $(el).addClass('filtered');
        $(el).css("order", foundIndex);
      } else {
        $(el).removeClass('filtered');
      }
    })
  }

  const _util_escapeSpecialHtmls = function(input) {
    if (typeof input !== 'string') { return input; }
    const specialCharacters = {
      '&': '&amp;',
      '<': '&lt;',
      '>': '&gt;',
      '"': '&quot;',
      "'": '&#39;'
      // Add more special characters and their HTML entities if needed
    };
    return input.replace(/[&<>"']/g, match => specialCharacters[match]);
  }

  const _update = function(visit_id, column_name, updated_note) {
    let newVisitGroupsData = _datas.map((item) => {
      if (item.visit_id == visit_id) {
        let visit_data = item;
        visit_data.visit_info[column_name] = updated_note;
        return visit_data;
      } else {
        return item;
      }
    })

    if (column_name == "support_history") {
      // todo
    } else if (["customer_notes", "food_notes", "room_notes", "other_notes", "tourist_spot"].includes(column_name)) {
      const visitGroupDiv = $('#visitgroups').find(`div[data-visit_id="${visit_id}"]`);
      if (visitGroupDiv.length == 1) {
        const item = $(visitGroupDiv).find(`.${column_name}`);
        visitInfoNotesInfoItem(column_name, updated_note, $(item));
      }
    }
     
    _datas = newVisitGroupsData;
    _generateMap();
  }

  return {
    init: function(visitGroupsData) {
      _datas = visitGroupsData;
      _filteredCustomerIDs = getSortedCustomerIDs(_datas, 'r_asc');
      _keyword = "";
      VisitGroupLang = MemberLocale;
      _generateMap();
    },
    render: function(force = false) {
      if (force) {
        _keyword = '';
      }
      _filteredCustomerIDs = _filterData();
      _render();
      _reFreshUI();
    },
    filter: function(keyword) {
      const result = _filterData(keyword);
      if (_filteredCustomerIDs !== result) {
        _filteredCustomerIDs = result;
        _reFreshUI();
      }
    },
    sort: function(sortDesc) {
      _sort = sortDesc;
      _filteredCustomerIDs = getSortedCustomerIDs(_datas, _sort);
      _reFreshUI();
    },
    update: function(visit_id, column_name, updated_note) {
      _update(visit_id, column_name, updated_note);
    }
  }
} ();

const createModal = function (button_element, title) {
  var visit_id = $(button_element).closest('[data-visit_id]').data('visit_id');
  var note = $(button_element).siblings('.item-data').html().replace(/<br\s*[\/]?>/gi, '\n');
  var modal = $('<div>').addClass('modal js-member-notes-modal').data('sourceElement', button_element);
  var modalContent = $('<div>').addClass('modal-content').attr("data-member_id", visit_id);

  // タイトルとメッセージ
  var modalTitle = $('<p style="margin-bottom:0.5em">').text(title);
  var modalMessage = $('<textarea>').attr('type', 'text').addClass('text-input-longer').css({
    "overflow-y": "auto",
    "height": "8rem"
  }).val(note);

  // 保存・キャンセルボタン
  var saveButton = $('<button>')
    .attr('type', 'button')
    .attr('id', 'js-save-member-notes-modal')
    .addClass('btn-blue')
    .text('保存');

  var cancelButton = $('<button>')
    .attr('type', 'button')
    .attr('id', 'js-close-member-notes-modal')
    .addClass('btn-white')
    .text('キャンセル');

  var buttonsContainer = $('<div>').addClass('modal-buttons').css({ "display": "flex" });
  buttonsContainer.append(saveButton, cancelButton);

  modalContent.append(modalTitle, modalMessage, buttonsContainer);
  modal.append(modalContent);

  $('.js-member-notes-modal').remove();
  $('body').append(modal);
  $(".js-member-notes-modal").show();
}

function updateVisitInfo(visit_id, column_name, updated_note) {
  var data = JSON.parse(fakeData);
  var visit_data = data.find(function(item) {
    return item.visit_id == visit_id;
  });
  if (visit_data) {
    visit_data.visit_info[column_name] = updated_note;
  }
  return data;
}

function getSortedCustomerIDs(datas, sort) {

  return datas
    .sort((a, b) => {
      if (sort === 'r_asc') {
        // room_noでソート
        const getRoomValue = (roomNo) => {
          // engage_room_number_order：定義がある場合は定義のインデックスを返す
          const customIndex = _engage_room_number_order.indexOf(roomNo);
          if (customIndex !== -1) return customIndex;

          // 部屋番号が数字の場合はその値を返す  
          if (!isNaN(roomNo)) return parseInt(roomNo, 10);

          // 数字に変換可能な文字列はその値を返す
          const numericMatch = roomNo.match(/\d+/);    
          if (numericMatch) return parseInt(numericMatch[0], 10);

          // 上記に該当しない場合:並べ替えず、そのまま返す
          return 10000;
        };

        const aValue = getRoomValue(a.member_info.room_no);
        const bValue = getRoomValue(b.member_info.room_no);

        // 小さい順にソート
        return aValue - bValue;
      } else {
        // arrival_timeでソート
        const aTime = parseInt(a.visit_info.arrival_time.replace(/:/g, ''), 10);
        const bTime = parseInt(b.visit_info.arrival_time.replace(/:/g, ''), 10);
        return sort === 'asc' ? aTime - bTime : bTime - aTime;
      }
    })
    .map(data => data.member_info.customer_id);
}
