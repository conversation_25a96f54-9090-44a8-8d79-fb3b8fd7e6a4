$(function(){
  talkmgr.load();

  $("table.js-data-table").dataTable({
    "bDestroy":true,
    "paging": false,
    "searching": false,
    "info": false,
    "order": [
      [4, "desc"]
  ]
  })

  $('#member_filter_date_btn').click(function() {
    $('#page').val(1);
    talkmgr.submit("filter");
  })

  $('#next_page').click(function(){
    $('#page').val(Number($('#page').val()) + 1);
    talkmgr.submit("filter");
  });

  $('#pervious_page').click(function(){
    $('#page').val(Number($('#page').val()) - 1);
    talkmgr.submit("filter");
  });

  $('.goto_page').click(function(){
    $('#page').val($(this).attr('value'));
    talkmgr.submit("filter");
  });

  const filter_condition = JSON.parse($('#member_filter_condition').val());
  const filter_now = JSON.parse($('#filter_condition').val());

  $("#filter_button").click(function () {
    const dialog = {
			"title": TalkappiMessageAdmin.member.label.all_conditions,
			"buttons": [
				{ "caption": "OK", "type": "filter", "color": "blue" },
				{ "caption": TalkappiMessageAdmin.common.label.cancel, "type": "close", "color": "white" },
			]
		};


    TalkappiModal.modal(dialog, dialogContent(filter_condition), function (result) {
      
      let age_filter = findCheckStatus('age_filter');
      let area_filter = findCheckStatus('area_filter');
      let car_filter = findCheckStatus('car_filter');
      let member_type_filter = findCheckStatus('member_type_filter');
      let purpose_filter = findCheckStatus('purpose_filter');
      let reservation_channel_filter = findCheckStatus('reservation_channel_filter');

      let filter_condition_now = {age: age_filter, area: area_filter, car: car_filter, member_type: member_type_filter, purpose: purpose_filter, reservation_channel: reservation_channel_filter}
      const filter_condition_simple = {};

      for (const key in filter_condition_now) {
          if (filter_condition_now.hasOwnProperty(key)) {
              filter_condition_simple[key] = filter_condition_now[key].filter(item => item.checked).map(item => item.name);
          }
      }

      $('#filter_condition').val(JSON.stringify(filter_condition_simple));

			if (result.button == 'filter') {
        $('#page').val(1);
        talkmgr.submit("filter");
			}
		});

    $(".common-modal-container").addClass("filter-modal");

    // keep current checkbox when open filter modal
    let age_filter_now = filter_now.age;
    if (age_filter_now.length > 0) {
      age_filter_now.forEach(element => {
        $('#age_filter').find('#' + element).each(function(){
          $(this).prop('checked', true)
        })
      });
    }

    let area_filter_now = filter_now.area;
    if (area_filter_now.length > 0) {
      area_filter_now.forEach(element => {
        $('#area_filter').find('#' + element).each(function(){
          $(this).prop('checked', true)
        })
      });
    }

    let car_filter_now = filter_now.car;
    if (car_filter_now.length > 0) {
      car_filter_now.forEach(element => {
        $('#car_filter').find('#' + element).each(function(){
          $(this).prop('checked', true)
        })
      });
    }

    let member_type_filter_now = filter_now.member_type;
    if (member_type_filter_now.length > 0) {
      member_type_filter_now.forEach(element => {
        $('#member_type_filter').find('#' + element).each(function(){
          $(this).prop('checked', true)
        })
      });
    }
    
    let purpose_filter_now = filter_now.purpose;
    if (purpose_filter_now.length > 0) {
      purpose_filter_now.forEach(element => {
        $('#purpose_filter').find("input[id='" + element + "']").each(function(){
          $(this).prop('checked', true)
        })
      });
    }

    let reservation_channel_filter_now = filter_now.reservation_channel;
    if (reservation_channel_filter_now.length > 0) {
      reservation_channel_filter_now.forEach(element => {
        $('#reservation_channel_filter').find("input[id='" + element + "']").each(function(){
          $(this).prop('checked', true)
        })
      });
    }

    // すべてcheckboxチェック時のevent
    checkAllcheckboxEvents('age_filter', 'checkbox_age_all');
    checkAllcheckboxEvents('area_filter', 'checkbox_area_all');
    checkAllcheckboxEvents('car_filter', 'checkbox_car_all');
    checkAllcheckboxEvents('member_type_filter', 'checkbox_member_type_all');
    checkAllcheckboxEvents('purpose_filter', 'checkbox_purpose_all');
    checkAllcheckboxEvents('reservation_channel_filter', 'checkbox_reservation_channel_all');

    $('.common-modal-content').find('input').each(function(){
      $(this).click(function(){
        $(".common-modal-container").scrollTop( 0 );
      });
    });

    // when not all checkbox be checked, checkbox_all will unchecked
    uncheckAllcheckboxEvents('age_filter', 'checkbox_age_all');
    uncheckAllcheckboxEvents('area_filter', 'checkbox_area_all');
    uncheckAllcheckboxEvents('car_filter', 'checkbox_car_all');
    uncheckAllcheckboxEvents('member_type_filter', 'checkbox_member_type_all');
    uncheckAllcheckboxEvents('purpose_filter', 'checkbox_purpose_all');
    uncheckAllcheckboxEvents('reservation_channel_filter', 'checkbox_reservation_channel_all');

    let height = $("body").find(".common-modal-container").height();
    $("body").find(".common-modal-container").css({ "height": "632px",  "width": "660px", "padding-left": "36px", "padding-right": "36px", "padding-bottom": "64px", "overflow": "scroll"});
    $("body").find(".common-modal-content").css({"overflow": "scroll", "height": "500px"})

  });
});

// create filter dialog 
const dialogContent = (filter_condition) => {
	const html = $(`
		<div>
			<div class="age-filter-box" style="display: flex; flex-direction: row;  border-style: solid; border-color: #E3E5E8; border-width: 1px;">
        <div style="display: flex; flex-direction: column; width: 94px; height: 94px; background: #F6F7F9; justify-content: center;">
          <div style="text-align: center;">${TalkappiMessageAdmin.member.label.age_group}</div>
        </div>
        <div style="width: 492px; height: 94px; padding: 12px; display: flex; flex-direction: row; flex-wrap: wrap;" id="age_filter"></div>
      </div>
      
      <div class="area-filter-box" style="display: flex; flex-direction: row; border-style: solid; border-color: #E3E5E8; border-width: 1px;">
          <div style="display: flex; flex-direction: column; width: 94px; background: #F6F7F9; justify-content: center;">
            <div style="text-align: center;">${TalkappiMessageAdmin.member.label.area}</div>
          </div>
          <div style="width: 492px; height: 432px; padding: 12px; display: flex; flex-direction: row; flex-wrap: wrap;" id="area_filter"></div>
      </div>

      <div class="car-filter-box" style="display: flex; flex-direction: row; border-style: solid; border-color: #E3E5E8; border-width: 1px;">
          <div style="display: flex; flex-direction: column; width: 94px; background: #F6F7F9; justify-content: center;">
            <div style="text-align: center;">${TalkappiMessageAdmin.member.label.about_car}</div>
          </div>
          <div style="width: 492px; height: 42px; padding: 12px; display: flex; flex-direction: row; flex-wrap: wrap;" id="car_filter"></div>
      </div>

      <div class="member-type-filter-box" style="display: flex; flex-direction: row; border-style: solid; border-color: #E3E5E8; border-width: 1px;">
          <div style="display: flex; flex-direction: column; width: 94px; background: #F6F7F9; justify-content: center;">
            <div style="text-align: center;">${TalkappiMessageAdmin.member.label.member_status}</div>
          </div>
          <div style="width: 492px; height: 42px; padding: 12px; display: flex; flex-direction: row; flex-wrap: wrap;" id="member_type_filter"></div>
      </div>

      <div class="purpose-filter-box" style="display: flex; flex-direction: row; border-style: solid; border-color: #E3E5E8; border-width: 1px;">
          <div style="display: flex; flex-direction: column; width: 94px; background: #F6F7F9; justify-content: center;">
            <div style="text-align: center;">${TalkappiMessageAdmin.member.label.purpose}</div>
          </div>
          <div style="width: 492px; padding: 12px; display: flex; flex-direction: row; flex-wrap: wrap; row-gap:30px; padding-bottom: 30px;" id="purpose_filter"></div>
      </div>

      <div class="reservation-channel-filter-box" style="display: flex; flex-direction: row; border-style: solid; border-color: #E3E5E8; border-width: 1px;">
          <div style="display: flex; flex-direction: column; width: 94px; background: #F6F7F9; justify-content: center;">
            <div style="text-align: center;">${TalkappiMessageAdmin.member.label.reservation}<br>${TalkappiMessageAdmin.member.label.channel}</div>
          </div>
          <div style="width: 492px; padding: 12px; display: flex; flex-direction: row; flex-wrap: wrap; row-gap:0px; padding-bottom: 30px;" id="reservation_channel_filter"></div>
      </div>
		</div>
		`);

    let lang_cd = "ja";
    if ($('#lang_cd').val() === "en") {
      lang_cd = "en";
    }
    const age_filter = filter_condition.age;
    html.find("#age_filter").append(checkBox(TalkappiMessageAdmin.member.label.all, "checkbox_age_all"))
    for(const key in age_filter) {
      html.find("#age_filter").append(checkBox(age_filter[key][lang_cd], key))
    }

    const area_filter = filter_condition.area;
    html.find("#area_filter").append(checkBox(TalkappiMessageAdmin.member.label.all, "checkbox_area_all"))
    Object.keys(area_filter)
      .sort((a, b) => parseInt(a) - parseInt(b))
      .forEach(key => {
        html.find("#area_filter").append(checkBox(area_filter[key][lang_cd], key))
      });

    const car_filter = filter_condition.car;
    html.find("#car_filter").append(checkBox(TalkappiMessageAdmin.member.label.all, "checkbox_car_all"))
    for (const key in car_filter) {
      html.find("#car_filter").append(checkBox(car_filter[key][lang_cd], key))
    }

    const member_filter = filter_condition.member_type;
    html.find("#member_type_filter").append(checkBox(TalkappiMessageAdmin.member.label.all, "checkbox_member_type_all"))
    for (const key in member_filter) {
      html.find("#member_type_filter").append(checkBox(member_filter[key][lang_cd], key))
    }

    const purpose_filter = filter_condition.purpose;
    if ( Array.isArray(purpose_filter) && purpose_filter.length == 0) {
      html.find("#purpose_filter").append(TalkappiMessageAdmin.member.label.no_filter_condition);
    } else {
      html.find("#purpose_filter").append(checkBox(TalkappiMessageAdmin.member.label.all, "checkbox_purpose_all"));
      for (const key in purpose_filter) {
        html.find("#purpose_filter").append(checkBox(purpose_filter[key][lang_cd], key));
      }
    }

    const reservation_channel_filter = filter_condition.reservation_channel;
    if (Array.isArray(reservation_channel_filter) && reservation_channel_filter.length == 0) {
      html.find("#reservation_channel_filter").append(TalkappiMessageAdmin.member.label.no_filter_condition)
    } else {
      html.find("#reservation_channel_filter").append(checkBox(TalkappiMessageAdmin.member.label.all, "checkbox_reservation_channel_all"))
      for (const key in reservation_channel_filter) {
          if (key.trim() !== "") {
            html.find("#reservation_channel_filter").append(checkBox(reservation_channel_filter[key][lang_cd], key));
          }
      }
    }
    
	return html.html();
};


const checkBox = (name, label) => {
  const html = $(`
    <div style="width:148px;">
        <input type="checkbox" id="${label}" name="${label}" style="" />
        <label for="${label}" style="">${name}</label>
    </div>
  `);
  return html;
}

function uncheckAllcheckboxEvents(element_id, checkbox_all_id) {
  $('#' + element_id).find('input').not('#' + checkbox_all_id).each(function() {
      $(this).click(function() {
          if (!$(this).prop('checked')) {
              $('#' + checkbox_all_id).prop('checked', false);
          }
      });
  });
}

function checkAllcheckboxEvents(element_id, checkbox_all_id) {
  $('#' + checkbox_all_id).click(function () {
    if ($(this).prop('checked')) {
      $('#' + element_id).find('input').each(function(){
        $(this).prop('checked', true)
      });
    } else {
      $('#' + element_id).find('input').each(function(){
        $(this).prop('checked', false)
      });
    }
  });
}

function findCheckStatus(element_id) {
  let result = [];
  $('#' + element_id).find('input').each(function(){
    result.push({
      name: $(this).prop('name'),
      checked: $(this).prop('checked')
    })
  });
  return result;
}
