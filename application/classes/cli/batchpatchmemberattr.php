<?php defined('SYSPATH') or die('No direct script access.');

class CLI_BatchpatchMemberattr
{
    public $_model;

    public $_query_times;
    
    public $_method;
    public $_m_bot_intent_key_value;
    public function __construct()
    {
        ob_end_flush();
        $this->_model= Model::factory('apimodel');
    }
    
    public function _call_method($params)
    {
        $method = array_shift($params);
        return call_user_func_array(array($this, $method), $params);
    }
    
    /*
    private function patch_member_attr_oneday($log_table_name, $bot_id, $cur_day)
    {
        // 取出当日所有符合条件的log_id
        ++$this->_query_times;
        $sql = "SELECT
                a.log_id
            FROM $log_table_name a
            WHERE
                bot_id = :bot_id
                AND vir_is_intent_inquiry = 1
                AND vir_is_member_msg =1
                AND vir_is_score_between01 = 1
                AND vir_int_log_time = :cur_day
                AND (a.answer_type = 0 or a.answer_type = 1 or a.answer_type = 2)
            ";

        $query = DB::query(Database::SELECT, $sql);
        $query->parameters(array(
            ':bot_id' => $bot_id,
            ':cur_day' => $cur_day,
            ));
        $results = $query->execute()->as_array();
        $count = count($results);
        if ($count == 0) {
            return ;
        }

        $this->write_log(" $cur_day count=$count \n");
        // 逐个
        for ($i=0; $i<$count; $i++) {
            $cur_log_id = $results[$i]["log_id"];
            $this->write_log("   $cur_log_id ");
            $ret = $this->_model->create_update_member_attr("chatbot", $log_table_name, $cur_log_id, -1, -1, -2);
            $this->write_log(" $ret \n");
        }
    }

    function patch_member_attr_one_table($no, $bot_id, $cur_bot_class_cd, $start_day, $end_day) {
        try {
            // 计算log_table名
            $log_table_id = $bot_id;
            if ($log_table_id == 401002 || $log_table_id == 401003) {
                $log_table_id = 401001;
            } else {
                $parent_bot_id = $this->_model->get_grp_parent_bot_id($log_table_id);
                if ($parent_bot_id != -1) {
                    $log_table_id = $parent_bot_id;
                }
            }
            $log_table_name = "t_bot_log_$log_table_id";

            if ($start_day == "-1" || $start_day == "") {
                $start_day = "2018-01-01";
            }
            if ($end_day == "-1" || $end_day == "") {
                $end_day = date('Y-m-d');
            }
            $end_day_int = intval(str_replace('-', '', $end_day));

            $cur_day = $start_day;
            $cur_date_int = intval(str_replace('-', '', $cur_day));
            $this->write_log("$no will process bot_id=$bot_id log_table_name=$log_table_name \n");
            $memory_usage = memory_get_usage();
            $this->write_log("  begin memory_usage=$memory_usage \n");
            while ($cur_date_int <= $end_day_int) {
                $memory_usage = memory_get_usage();
                $cur_query_times = $this->_query_times;
                $this->write_log("    query_times=$cur_query_times memory_usage=$memory_usage \n");
                    $this->patch_member_attr_oneday($log_table_name, $bot_id, $cur_date_int);

                $cur_day = date("Y-m-d",strtotime("+1 day",strtotime($cur_day)));
                $cur_date_int = intval(str_replace('-', '', $cur_day));
            }
            $memory_usage = memory_get_usage();
            $this->write_log("  end memory_usage=$memory_usage \n");
        } catch (Exception $e) {
            $memory_usage = memory_get_usage();
            $this->write_log("  end memory_usage=$memory_usage \n");
            $this->write_log($e);
            $this->write_log("\n");
        }
    }
    */

    public function patch_member_attr_one_table($no, $bot_id, $cur_bot_class_cd, $start_day, $end_day)
    {
        try {
            // 计算log_table名
            $log_table_id = $bot_id;
            if ($log_table_id == 401002 || $log_table_id == 401003) {
                $log_table_id = 401001;
            } else {
                $parent_bot_id = $this->_model->get_grp_parent_bot_id($log_table_id);
                if ($parent_bot_id != -1) {
                    $log_table_id = $parent_bot_id;
                }
            }
            $log_table_name = "t_bot_log_$log_table_id";
            $this->write_log("$no will process log_table_name=$log_table_name for bot_id=$bot_id \n");
            $memory_usage = memory_get_usage();
            $this->write_log("  memory_usage=$memory_usage at begin\n");

            $sql_log_time = "";
            if ($start_day == "-1" || $start_day == "") {
            } else {
                $start_day_int = intval(str_replace('-', '', $start_day));
                $sql_log_time = " AND vir_int_log_time >= $start_day_int";
            }
            if ($end_day == "-1" || $end_day == "") {
            } else {
                $end_day_int = intval(str_replace('-', '', $end_day));
                $sql_log_time = " AND vir_int_log_time <= $end_day_int";
            }

            ++$this->_query_times;

            if ($this->_method == 1) {
                // 这个sql文用不到vir_index,如果没有 INNER JOIN m_bot_intent mbi就能用到，所以...
                $sql = "SELECT 
                            a.log_id,
                            a.bot_id,
                            a.member_id,
                            mbi.attr
                        FROM $log_table_name a
                        INNER JOIN m_bot_intent mbi
                            ON mbi.intent_class_cd = :bot_class_cd
                                AND mbi.intent_cd = a.intent_cd
                                AND mbi.sub_intent_cd = a.sub_intent_cd
                                AND mbi.lang_cd = a.lang_cd
                                AND mbi.attr IS NOT NULL 
                                AND mbi.attr <> ''
                        WHERE 
                            a.bot_id = :bot_id
                            AND a.vir_is_intent_inquiry = 1
                            AND a.vir_is_member_msg =1
                            AND a.vir_is_score_between01 = 1
                            AND (a.answer_type = 0 or a.answer_type = 1 or a.answer_type = 2)
                            $sql_log_time
                ";
            } elseif ($this->_method == 2) {
                $sql = "SELECT
                            a.log_id,
                            a.bot_id,
                            a.member_id,
                            a.intent_cd,
                            a.sub_intent_cd,
                            a.lang_cd
                        FROM $log_table_name a
                        WHERE 
                            a.bot_id = :bot_id
                            AND a.vir_is_intent_inquiry = 1
                            AND a.vir_is_member_msg =1
                            AND a.vir_is_score_between01 = 1
                            AND (a.answer_type = 0 or a.answer_type = 1 or a.answer_type = 2)
                            $sql_log_time
                ";
            } elseif ($this->_method == 3) {
                $sql = "SELECT 
                            a.log_id,
                            a.bot_id,
                            a.member_id,
                            mbi.attr
                        FROM $log_table_name a
                        INNER JOIN wuzhao_m_bot_intent mbi
                            ON mbi.intent_class_cd = :bot_class_cd
                                AND mbi.intent_cd = a.intent_cd
                                AND mbi.sub_intent_cd = a.sub_intent_cd
                                AND mbi.lang_cd = a.lang_cd
                        WHERE 
                            a.bot_id = :bot_id
                            AND a.vir_is_intent_inquiry = 1
                            AND a.vir_is_member_msg =1
                            AND a.vir_is_score_between01 = 1
                            AND (a.answer_type = 0 or a.answer_type = 1 or a.answer_type = 2)
                            $sql_log_time
                ";
            }
            /* 这个sql会出： Allowed memory size of 134217728 bytes exhausted (tried to allocate 1 bytes)
            $sql = "SELECT tempa.*,mbi.faq_id
                    FROM
                    (
                        SELECT
                            a.log_id,
                            a.bot_id,
                            a.member_id,
                                    t.bot_class_cd,
                            a.intent_cd,
                            a.sub_intent_cd,
                            a.lang_cd
                        FROM $log_table_name a
                        WHERE
                            a.bot_id = :bot_id
                            AND a.vir_is_intent_inquiry = 1
                            AND a.vir_is_member_msg =1
                            AND a.vir_is_score_between01 = 1
                            AND (a.answer_type = 0 or a.answer_type = 1 or a.answer_type = 2)
                            $sql_log_time
                    ) tempa
                    INNER JOIN m_bot_intent mbi
                        ON mbi.intent_class_cd = :bot_class_cd
                        AND mbi.intent_cd = tempa.intent_cd
                        AND mbi.sub_intent_cd = tempa.sub_intent_cd
                        AND mbi.lang_cd = tempa.lang_cd
            ";
            */

            $query = DB::query(Database::SELECT, $sql);
            $query->parameters(array(
                ':bot_id' => $bot_id,
                ':bot_class_cd' => $cur_bot_class_cd,
                ));
            $results = $query->execute()->as_array();
            $count = count($results);
            $this->write_log(" count=$count \n");
    
            $memory_usage = memory_get_usage();
            $this->write_log("  memory_usage=$memory_usage after query all log_id\n");

            if ($count == 0) {
                return ;
            }

            // 逐个
            for ($i=0; $i<$count; $i++) {
                $cur_log_id = $results[$i]["log_id"];
                $member_id = $results[$i]["member_id"];
        
                if ($this->_method == 1 || $this->_method == 3) {
                    $faq_attr = $results[$i]["attr"];
                } elseif ($this->_method == 2) {
                    $faq_attr = $this->get_attr_by_key($cur_bot_class_cd, $results[$i]["intent_cd"], $results[$i]["sub_intent_cd"], $results[$i]["lang_cd"]);
                    if ($faq_attr == null || $faq_attr == "") {
                        continue;
                    }
                    $results[$i]["attr"] = $faq_attr;
                }
                // $this->write_log("   log_id=$cur_log_id member_id=$member_id faq_attr=$faq_attr ");
                if ($this->_method == 1 || $this->_method == 3) {
                    // 2022.06.19 #27615 增加bot_class_cd
                    $ret = $this->_model->create_update_member_attr("chatbot", $log_table_name, $cur_log_id, -2, $results[$i], $cur_bot_class_cd);
                    //$this->write_log(" $ret \n");
                } elseif ($this->_method == 2) {
                    // 2022.06.19 #27615 增加bot_class_cd
                    $ret = $this->_model->create_update_member_attr("chatbot", $log_table_name, $cur_log_id, -2, $results[$i], $cur_bot_class_cd);
                    //$this->write_log(" $ret \n");
                }
            }
            $memory_usage = memory_get_usage();
            $this->write_log("  memory_usage=$memory_usage after process all log_id\n");
        } catch (Exception $e) {
            $memory_usage = memory_get_usage();
            $this->write_log("  memory_usage=$memory_usage when exception\n");
            $this->write_log($e);
            $this->write_log("\n");
        }
    }

    private function get_attr_by_key($bot_class_cd, $intent_cd, $sub_intent_cd, $lang_cd)
    {
        $key = "$bot_class_cd$intent_cd$sub_intent_cd$lang_cd";
        if (array_key_exists($key, $this->_m_bot_intent_key_value)) {
            return $this->_m_bot_intent_key_value[$key];
        } else {
            return null;
        }
    }
    
    public function patch_member_attr($method = 3, $bot_id = -1, $start_day = -1, $end_day = -1, $server = "dev")
    {
        $this->write_log("Usage:\n");
        $this->write_log("  cd /home/<USER>/workseki/api-mgr/\n");
        $this->write_log("  php cli.php batchpatchmemberattr/patch_member_attr/method/bot_id/start_date/end_date/server\n");
        $this->write_log("    parameter method: 1 or 2 or 3\n");
        $this->write_log("    parameter bot_id: -1 is all bot\n");
        $this->write_log("    parameter start_day: -1 is every day\n");
        $this->write_log("    parameter end_day: -1 is every day\n");
        $this->write_log("    parameter server: honban or dev\n");

        $this->write_log("  sample1: php cli.php batchpatchmemberattr/patch_member_attr/3/-1/-1/-1/dev\n");
        $this->write_log("  sample2: php cli.php batchpatchmemberattr/patch_member_attr/3/17/2022-01-13/2022-01-14/honban\n");
        $this->write_log("  sample3: php cli.php batchpatchmemberattr/patch_member_attr/3/17,88/2022-01-13/2022-01-14/honban\n");
        if ($server == "honban") {
            Database::$default = 'honban';
        }

        $this->_method = $method;
        $this->write_log("=======START patch_member_attr bot_id=$bot_id end_day=$end_day server=$server method=$method\n");

        if ($this->_method == 2) {
            // 缓存m_bot_intent
            $this->write_log("=======begin search m_bot_intent for cache \n");
            $this->_m_bot_intent_key_value = [];
            $sql = "SELECT 
                        CONCAT(a.intent_class_cd,a.intent_cd,a.sub_intent_cd,a.lang_cd) AS mkey,
                        a.attr
                    FROM m_bot_intent a
                    WHERE
                                a.attr IS NOT NULL 
                                AND a.attr <> ''
            ";
            $query = DB::query(Database::SELECT, $sql);
            $query->parameters(array(
                ));
            $results = $query->execute()->as_array();
            $count = count($results);
            $this->write_log(" count=$count \n");
            for ($i=0; $i<$count; $i++) {
                $this->_m_bot_intent_key_value[$results[$i]["mkey"]] = $results[$i]["attr"];
            }
        } elseif ($this->_method == 3) {
            try {
                $this->write_log("will drop wuzhao_m_bot_intent\n");
                $sql = "DROP TABLE `wuzhao_m_bot_intent`";
                $query = DB::query(Database::UPDATE, $sql);
                $query->parameters(array(
                    ));
                $results = $query->execute();
            } catch (Exception $e) {
                $this->write_log($e);
                $this->write_log("\n");
            }

            try {
                $this->write_log("will create wuzhao_m_bot_intent\n");
                $sql = "CREATE TABLE wuzhao_m_bot_intent 
                            SELECT a.intent_class_cd,a.intent_cd,a.sub_intent_cd,a.lang_cd,a.attr 
                            FROM m_bot_intent a
                            WHERE
                                a.attr IS NOT NULL 
                                AND a.attr <> ''
                        ";
                $query = DB::query(Database::UPDATE, $sql);
                $query->parameters(array(
                ));
                $results = $query->execute();
            } catch (Exception $e) {
                $this->write_log($e);
                $this->write_log("\n");
            }

            try {
                $this->write_log("will add index for wuzhao_m_bot_intent\n");
                $sql = "ALTER TABLE `wuzhao_m_bot_intent` ADD INDEX( `intent_class_cd`, `intent_cd`, `sub_intent_cd`, `lang_cd`)";
                $query = DB::query(Database::UPDATE, $sql);
                $query->parameters(array(
                    ));
                $results = $query->execute();
            } catch (Exception $e) {
                $this->write_log($e);
                $this->write_log("\n");
            }
        }

        $this->_query_times = 0;
        $bot_datas_array = [];
        if ($bot_id == -1 || $bot_id == "") {
            // 不指定bot_id，就全部bot_id（不包含bot_id 0)
            ++$this->_query_times;
            $sql = "SELECT 
                        bot_id,
                        bot_class_cd
                    FROM t_bot
                    WHERE 
                        delete_flg != 1
                    ORDER BY
                        bot_id
                ";

            $query = DB::query(Database::SELECT, $sql);
            $query->parameters(array(
                ));
            $results = $query->execute()->as_array();
            $count = count($results);
            for ($i=0; $i<$count; $i++) {
                if ($results[$i]["bot_id"] != 0) {
                    $bot_datas_array[] = $results[$i];
                }
            }
        } else {
            ++$this->_query_times;
            $sql = "SELECT 
                        bot_id,
                        bot_class_cd
                    FROM t_bot
                    WHERE
                        bot_id in ($bot_id)
                        AND delete_flg != 1
                    ORDER BY
                        bot_id
                ";

            $query = DB::query(Database::SELECT, $sql);
            $query->parameters(array(
                ));
            $results = $query->execute()->as_array();
            $count = count($results);
            for ($i=0; $i<$count; $i++) {
                if ($results[$i]["bot_id"] != 0) {
                    $bot_datas_array[] = $results[$i];
                }
            }
        }

        $bot_count = count($bot_datas_array);
        $no = 1;
        for ($i=0; $i<$bot_count; $i++) {
            $cur_bot_id = $bot_datas_array[$i]["bot_id"];
            $cur_bot_class_cd = $bot_datas_array[$i]["bot_class_cd"];
            $this->patch_member_attr_one_table($no, $cur_bot_id, $cur_bot_class_cd, $start_day, $end_day);
            ++$no;
        }
        
        $this->write_log("=======END patch_member_attr \n");
    }
    
    
    private function write_log($log)
    {
        /*
        $file =  APPPATH . "../../files/log/batch_" . date('Y-m-d') . ".log";
        $f = fopen($file, 'a');
        $log = date('Y-m-d H:i:s') . ' ' . $log . PHP_EOL;
        fwrite($f, $log);
        fclose($f);
        */
        echo(date('Y-m-d H:i:s') . '  ');
        echo($log);
    }
}
