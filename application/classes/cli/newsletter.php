<?php defined('SYSPATH') or die('No direct script access.');

class CLI_Newsletter {

	public $_model;
	public $_newletter_model;
	public $_log_file;

	public function __construct()
	{
		$this->_model = new Model_Basemodel();
		$this->_newletter_model = new Model_Newslettermodel();
	}

	private function _logEcho($_to_echo)	//, $line=false
	{
		Log::instance()->add(Log::DEBUG, $_to_echo);
	}

	private function _sendnewsletter($bot_id, $receivers, $title, $body, $tpl_params, $link_id, $member_id, $sender = NULL, $reply_to = NULL, $is_track_event = 0, $test_mode = 0) {	//$link_id = NULL, $member_id = NULL
		$data = [
			'bot_id' => $bot_id,
			'receiver' => $receivers,
			//TODO一旦記録せず,記録するかどうかは画面で設定できるようにしたい。デフォルトは記録しない
			'is_track_event' => $is_track_event
		];

		//test_mode
		if ($test_mode) {
			$data['test_mode'] = 1;
		}

		//TODO set service type and link_id
		// メルマガ 07,t_mail_task.mail_task_id
		$data['type'] = '09';
		if ($link_id != NULL) {
			$data['link_id'] = $link_id;
		}
		if ($member_id != NULL) {
			$data['member_id'] = (string)$member_id;
		}
		if ($sender != NULL) {
			$data['sender'] = $sender;
		}
		if ($reply_to != NULL) {
			$data['replyto'] = $reply_to;
		}

		if ( isset($tpl_params) && $tpl_params != null && count($tpl_params) > 0 ) {

			$data['params'] = $tpl_params;
		} 
		$data['title'] = $title;
		$data['body'] = $body;
		
		// $this->_logEcho('---------action_newslettersendingtest----------');

		return $this->_model->post_enginehook('service', 'sendmail', '', $data);
	}

	public function _call_method($mail_task_id)	//_business_method	$regist_task	$regist_task_data
	{
		$regist_task_data = $mail_task_id;
		$mail_task_id = $regist_task_data;
		$mail_task = ORM::factory('mailtask', $mail_task_id);
		$bot_id = $mail_task->bot_id;
		$sender = NULL;
		$reply_to = NULL;
		$is_track_event = 0;
		$test_mode = 0;
		$sender_name = NULL;
		$mail_task_data = json_decode($mail_task->mail_task_data);
		if ( isset($mail_task_data->sender_name) && $mail_task_data->sender_name != NULL && !empty($mail_task_data->sender_name) ) {
			$sender_name = $mail_task_data->sender_name;
		}
		if ( isset($mail_task_data->sender) && $mail_task_data->sender != NULL && !empty($mail_task_data->sender) ){
			$sender = $mail_task_data->sender;
			if ( $sender_name ){
				$sender = "=?utf-8?B?". base64_encode($sender_name) . "?=" . "<" .$sender . ">";
			}
		} else if ( $sender_name ) {
			// get default sender from bot
			// 'json_mail_setting'=>array('送信メール設定','{"default":{"type":"ses","sender":"talkappi<<EMAIL>>"}}'),
			$json_mail_setting = $this->_model->get_bot_setting($bot_id, 'json_mail_setting');
			if ($json_mail_setting) {
				$mail_setting = json_decode($json_mail_setting);
				if ( isset($mail_setting->default) && isset($mail_setting->default->sender) && $mail_setting->default->sender != NULL && !empty($mail_setting->default->sender) ){
					$default_sender = $mail_setting->default->sender;
					$pattern = '/[a-z0-9_\-\+\.]+@[a-z0-9\-]+\.([a-z]{2,4})(?:\.[a-z]{2})?/i';
					preg_match_all($pattern, $default_sender, $matches);
					if ( $matches && isset($matches[0]) && $matches[0] && count($matches[0]) > 0 ){
						$sender = $matches[0][0];
						$sender = "=?utf-8?B?". base64_encode($sender_name) . "?=" . "<" .$sender . ">";
					} else {
						$this->_logEcho('Sending mail extracting default sender failed '.$pattern.PHP_EOL);
					}
				}
			}
		}
		if ( isset($mail_task_data->reply_to) && $mail_task_data->reply_to != NULL && !empty($mail_task_data->reply_to) ) {
			$reply_to = $mail_task_data->reply_to;
		}
		$body_url_shorten = $mail_task->body;
		if ( isset($mail_task_data->is_track_event) && $mail_task_data->is_track_event != NULL && $mail_task_data->is_track_event == 1 ){
			$is_track_event = $mail_task_data->is_track_event;
			//urlの短縮
			$body_url_shorten = preg_replace_callback(
				"|(href=\")(https?://[\w!?/+\-_~;.,*&@#$%()'\[\]:;=]+)|",
				function ($m) { return $m[1].($this->_model->post_shorten_url(str_replace('&amp;', '&', $m[2]))); },
				$mail_task->body);
		}

		if ( isset($mail_task_data->test_mode) && $mail_task_data->test_mode != NULL && $mail_task_data->test_mode == 1 ){
			$test_mode = $mail_task_data->test_mode;
		}

		if ( $mail_task->status > 1 ) {
			$this->_model->log_error(__FUNCTION__, "API service.sendmail failure: unexpected mail task status=" . $mail_task->status);
			return '05';
		} else if ( $mail_task->status == 0 ) {
			//	TODO 仮登録
			$this->_model->log_error(__FUNCTION__, "API service.sendmail failure: unexpected mail task draft status=" . $mail_task->status);
			return '00';
		} else {

		}

		//TODO regist email sending task

		//members of this task
		$mail_task_members = DB::select('recipient.mail_task_id', 'member.mail_member_id', 'member.bot_id', 'member.email', 'first_name', 'last_name', 'bot.bot_name')
			->from(DB::expr('t_mail_task_recipient as recipient'))
			->join(DB::expr('t_mail_member as member'), 'INNER')
			->on('recipient.mail_member_id', '=','member.mail_member_id')
			->join(DB::expr('t_mail_member_bot as mb'), 'INNER')
			->on('member.mail_member_id', '=','mb.mail_member_id')
			// ->join(DB::expr('t_mail_extend as extend'), 'LEFT')
			// ->on('bot.bot_id', '=','extend.bot_id')
			->join(DB::expr('t_bot as bot'), 'INNER')
			->on('mb.bot_id', '=','bot.bot_id')
			->where('recipient.mail_task_id', '=', $mail_task_id)
			// ->where('recipient.mail_task_id', '=', $mail_task_id)
			->execute()
			->as_array();

		$extend_project_id = 0;
		if ( $mail_task->project_id != NULL ) {
			$extend_project_id = $mail_task->project_id;
		}
		$extend_template_params = db::select('extend_no', 'template_param')
			->from(DB::expr('t_mail_extend'))
			->where('bot_id', '=', $bot_id)
			->where('project_id', '=', $extend_project_id)
			->where('template_param', 'IS NOT', NULL)
			->execute()->as_array('extend_no', 'template_param')
			;

		$success = true;	//complete success
		$failed = true;		//all failed

		// $this->_logEcho('Sending mail to members count '.count($mail_task_members).'...<br/>');

		foreach($mail_task_members as $member) {
			$member_extend_array = db::select('extend_no', 'value')
				->from('t_mail_member_extend')
				->where('mail_member_id', '=', $member['mail_member_id'])
				->execute()->as_array('extend_no', 'value')
				;
			$params = [
				'bot' => $member['bot_name'],
				'name' => $member['first_name'].' '.$member['last_name'],
				'username' => $member['first_name'].' '.$member['last_name'],
			];
			foreach($member_extend_array as $extend_no=>$value) {
				$params['extend'.$extend_no] = $value;
				if( isset($extend_template_params[$extend_no]) && $extend_template_params[$extend_no] ) {
					$params[$extend_template_params[$extend_no]] = $value;
				}
			}
			// $this->_logEcho('Sending mail to member '. $member['email'].'...<br/>');
			//unsubscribe link
			if ( isset($mail_task_data->unsubscribe_enable) && $mail_task_data->unsubscribe_enable != NULL && $mail_task_data->unsubscribe_enable == 1 ) {
				$body_url_shorten = $this->_newletter_model->append_unsubscribe_link($body_url_shorten, $mail_task_id, $member['mail_member_id'], $member['email'], $bot_id);
			}

			$engine_response = $this->_sendnewsletter($bot_id, $member['email'], $mail_task->title, $body_url_shorten, $params, $mail_task_id, $member['mail_member_id'], $sender, $reply_to, $is_track_event, $test_mode);
			if ( isset($engine_response['success']) && $engine_response['success'] == 'False') {
				Log::instance()->add(Log::DEBUG, 'API service.sendmail failure=' . json_encode($engine_response));
				$this->_model->log_error(__FUNCTION__, "API service.sendmail failure=" . json_encode($engine_response, JSON_UNESCAPED_UNICODE));

				if( isset($engine_response['error_message']) ) {
					$engine_error_msg = $engine_response['error_message'];
					if( isset($engine_error_msg['message']) && isset($engine_error_msg['code']) && $engine_error_msg['code'] == 'MessageRejected' ) {
						$engine_error_msg_detail = $engine_error_msg['message'];
						if (str_starts_with($engine_error_msg_detail, 'Email address is not verified. The following identities failed the check in region ')
							&& str_ends_with($engine_error_msg_detail, ': '.$sender)
						) {
							//TODO record error of invalid sender
						}
					}
				}
				
				$recipient_status = 3;
				$success &= false;
			} else {
				$recipient_status = 2;
				if ($test_mode) {
					$recipient_status = 9;
				}
				$failed &= false;
			}
			// $this->_logEcho('Saving recipient '.$member['email'].'...<br/>');
			DB::update('t_mail_task_recipient')
				->set(['send_status'=>$recipient_status, 'send_time'=>date('Y/m/d H:i:s', time())])
				->where('mail_task_id', '=', $mail_task_id)
				->where('mail_member_id', '=', $member['mail_member_id'])
				->execute();
			$this->_logEcho('Saved recipient status='.$recipient_status.', parent task failed='.$failed.',success='.$failed.'...<br/>');
		}

		// actually send email to tester addresses on test mode
		if ( $test_mode && isset($mail_task_data->test_mail_addresses) && $mail_task_data->test_mail_addresses != NULL && $mail_task_data->test_mail_addresses ) {
			//test members of this task
			$mail_task_test_members = DB::select('member.mail_member_id', 'member.bot_id', 'member.email', 'first_name', 'last_name', 'bot.bot_name')
			->from(DB::expr('t_mail_member as member'))
			->join(DB::expr('t_mail_member_bot as mb'), 'INNER')
			->on('member.mail_member_id', '=','mb.mail_member_id')
			->join(DB::expr('t_bot as bot'), 'INNER')
			->on('mb.bot_id', '=','bot.bot_id')
			->where('member.email', 'IN', $mail_task_data->test_mail_addresses)
			->where('mb.bot_id', '=', $bot_id)
			->execute()
			->as_array('email');

			$unregistered_emails = array_diff($mail_task_data->test_mail_addresses, array_keys($mail_task_test_members));
			foreach($unregistered_emails as $index=>$unregistered_email) {
				$mail_tester_id = 'Tester'.($index);
				$tester_track_event = 0;
				$params = null;
				//unsubscribe link
				if ( isset($mail_task_data->unsubscribe_enable) && $mail_task_data->unsubscribe_enable != NULL && $mail_task_data->unsubscribe_enable == 1 ) {
					$body_url_shorten = $this->_newletter_model->append_unsubscribe_link($body_url_shorten, $mail_task_id, $mail_tester_id, $unregistered_email, $bot_id);
				}
				$engine_response = $this->_sendnewsletter($bot_id, $unregistered_email, $mail_task->title, $body_url_shorten, $params, $mail_task_id, $mail_tester_id, $sender, $reply_to, $tester_track_event);
				if ( isset($engine_response['success']) && $engine_response['success'] == 'False') {
					Log::instance()->add(Log::DEBUG, 'API service.sendmail failure=' . json_encode($engine_response));
					$this->_model->log_error(__FUNCTION__, "API service.sendmail failure=" . json_encode($engine_response, JSON_UNESCAPED_UNICODE));

					if( isset($engine_response['error_message']) ) {
						$engine_error_msg = $engine_response['error_message'];
						if( isset($engine_error_msg['message']) && isset($engine_error_msg['code']) && $engine_error_msg['code'] == 'MessageRejected' ) {
							$engine_error_msg_detail = $engine_error_msg['message'];
							if (str_starts_with($engine_error_msg_detail, 'Email address is not verified. The following identities failed the check in region ')
								&& str_ends_with($engine_error_msg_detail, ': '.$sender)
							) {
								//TODO record error of invalid sender
							}
						}
					}
					$test_status = 3;
					$success &= false;
				} else {
					$test_status = 2;
					$failed &= false;
				}
				$this->_logEcho('Test email sent to unregisstered address, status='.$test_status.', parent task failed='.$failed.',success='.$failed.'...<br/>');
			}

			foreach($mail_task_test_members as $tester_address=>$member) {
				// really send email to tester address
				//handle template parameters linked to each mail member: bot, name, extend attributes, etc.

				$member_extend_array = db::select('extend_no', 'value')
					->from('t_mail_member_extend')
					->where('mail_member_id', '=', $member['mail_member_id'])
					->execute()->as_array('extend_no', 'value')
					;
				$params = [
					'bot' => $member['bot_name'],
					'name' => $member['first_name'].' '.$member['last_name'],
					'username' => $member['first_name'].' '.$member['last_name'],
				];
				foreach($member_extend_array as $extend_no=>$value) {
					$params['extend'.$extend_no] = $value;
					if( isset($extend_template_params[$extend_no]) && $extend_template_params[$extend_no] ) {
						$params[$extend_template_params[$extend_no]] = $value;
					}
				}
				$mail_tester_id = $member['mail_member_id'];
				$tester_track_event = 0;
				//unsubscribe link
				if ( isset($mail_task_data->unsubscribe_enable) && $mail_task_data->unsubscribe_enable != NULL && $mail_task_data->unsubscribe_enable == 1 ) {
					$body_url_shorten = $this->_newletter_model->append_unsubscribe_link($body_url_shorten, $mail_task_id, $member['mail_member_id'], $member['email'], $bot_id);
				}
				$engine_response = $this->_sendnewsletter($bot_id, $tester_address, $mail_task->title, $body_url_shorten, $params, $mail_task_id, $mail_tester_id, $sender, $reply_to, $tester_track_event);
				if ( isset($engine_response['success']) && $engine_response['success'] == 'False') {
					Log::instance()->add(Log::DEBUG, 'API service.sendmail failure=' . json_encode($engine_response));
					$this->_model->log_error(__FUNCTION__, "API service.sendmail failure=" . json_encode($engine_response, JSON_UNESCAPED_UNICODE));

					if( isset($engine_response['error_message']) ) {
						$engine_error_msg = $engine_response['error_message'];
						if( isset($engine_error_msg['message']) && isset($engine_error_msg['code']) && $engine_error_msg['code'] == 'MessageRejected' ) {
							$engine_error_msg_detail = $engine_error_msg['message'];
							if (str_starts_with($engine_error_msg_detail, 'Email address is not verified. The following identities failed the check in region ')
								&& str_ends_with($engine_error_msg_detail, ': '.$sender)
							) {
								//TODO record error of invalid sender
							}
						}
					}
					
					$test_status = 3;
					$success &= false;
				} else {
					$test_status = 2;
					$failed &= false;
				}
				$this->_logEcho('Test email sent to regisstered address, status='.$test_status.', parent task failed='.$failed.',success='.$failed.'...<br/>');
			}
		}

		// regist task_status_cd
		// 	'00' => '仮登録',
		// 	'01' => '新規',
		// 	'02' => '実行中',
		// 	'03' => '完了（成功）',
		// 	'04' => '完了（警告あり）',
		// 	'05' => '完了（失敗）',
		if ($success) {
			$mail_task->status = 3;
			$regist_task_status = '03';
		} else if ($failed) {
			$mail_task->status = 5;
			$regist_task_status = '05';
		} else {
			$mail_task->status = 4;
			$regist_task_status = '04';
			//TODO warnings handling
		}
		$mail_task->upd_time = date('Y/m/d H:i:s', time());
		$mail_task->save();
		return $regist_task_status;
	}
}