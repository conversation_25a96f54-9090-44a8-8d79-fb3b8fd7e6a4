<?php
defined('SYSPATH') or die('No direct script access.');

class CLI_Report extends CLI_BaseCli
{
	public $_div = 'report';

	public function __construct()
	{
		parent::__construct();
		$this->_model = new Model_Reportmodel();
	}

	public function createbotdailyreport($bot_id = 0, $report_date = '')
	{
		$this->_write_log("=======START createbotdailyreport");
		$until_date = date('Y-m-d', strtotime("-1 day"));
		if ($report_date == '') {
			$report_date = date('Y-m-d', strtotime("-1 day"));
		}
		if ($bot_id == '') {
			$bots = ORM::factory('bot')->where('bot_id', '>', 0)->where('delete_flg', '=', 0)->order_by('bot_id')->find_all();
		} else {
			$e = '=';
			if (strpos($bot_id, 'gt') !== FALSE) $e = '>=';
			if (strpos($bot_id, 'lt') !== FALSE) $e = '<=';
			$bot_id = str_replace(['gt', 'lt'], '', $bot_id);
			$bots = ORM::factory('bot')->where('bot_id', $e, $bot_id)->where('delete_flg', '=', 0)->order_by('bot_id')->find_all();
		}
		foreach ($bots as $bot) {
			$bot_id = intval($bot->bot_id);
			$bot_id_tbl = $this->_model->get_log_table($bot_id);
			if (!$this->_model->check_table_exist("t_bot_log$bot_id_tbl")) continue;
			$this->_model->init($bot_id);
			try {
				while ($report_date <= $until_date) {
					DB::delete('t_bot_report')->where('bot_id', '=', $bot_id)->where('report_date', '=', $report_date)->execute();
					// 00 frequent_faq
					$json_faq_setting = $this->_model->get_bot_setting($bot_id, 'json_faq_setting', true);
					if ($json_faq_setting['freq_days_1'] == 0) {
						$start_date_1 = null;
					} else {
						$start_date_1 = date("Y-m-d", strtotime("-" . $json_faq_setting['freq_days_1']  . "day", strtotime($report_date)));
					}
					if ($json_faq_setting['freq_days_2'] == 0) {
						$start_date_2 = null;
					} else {
						$start_date_2 = date("Y-m-d", strtotime("-" . $json_faq_setting['freq_days_2']  . "day", strtotime($report_date)));
					}
					$freq_1 = $this->_model->create_bot_report_ranking($bot_id, $start_date_1, $report_date);
					$freq_2 = $this->_model->create_bot_report_ranking($bot_id, $start_date_2, $report_date);
					$result = ['frequent_faq' => $freq_1, 'frequent_faq_30' => $freq_2];
					// context report
					$faq_context_setting = $this->_model->get_bot_tpl_message($bot_id, 'faq_context_setting', 'ja', true);
					if (is_array($faq_context_setting)) {
						foreach ($faq_context_setting as $k => $v) {
							$freq_1 = $this->_model->create_bot_report_ranking($bot_id, $start_date_1, $report_date, $k);
							$freq_2 = $this->_model->create_bot_report_ranking($bot_id, $start_date_2, $report_date, $k);
							$result['frequent_faq_' . $k] = $freq_1;
							$result['frequent_faq_30_' . $k] = $freq_2;
						}
					}
					$this->_model->create_report_orm($bot_id, $report_date, '00', json_encode($result, JSON_UNESCAPED_UNICODE));
					// 01 top faq
					$result = $this->_model->create_bot_report_freq($bot_id, null, $report_date);
					$this->_model->create_report_orm($bot_id, $report_date, '01', json_encode($result));
					// 02 bot daily
					$result = $this->_model->create_bot_report_daily($bot_id, $report_date);
					$this->_model->create_report_orm($bot_id, $report_date, '02', json_encode($result));
					$report_date = date("Y-m-d", strtotime("+1 day", strtotime($report_date)));
				}
				$this->_write_log("======= createbotdailyreport " . $bot_id);
			} catch (Exception $e) {
				$this->_write_log("======= createbotdailyreport " . $bot_id . " error occured!");
			}
		}
		$this->_write_log("=======END createbotdailyreport");
		$this->_model->log_info(__FUNCTION__, "パラメータ:report_date=$report_date 結果:success", 0);
	}
}
