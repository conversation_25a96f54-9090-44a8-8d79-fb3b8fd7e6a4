<?php
defined('SYSPATH') or die('No direct script access.');

class CLI_BaseCli
{
	public $_model;
	public $_aws;
	public $_admin_model;
	public $_div;
	public $_bot;
	public $_bot_id;

	public function __construct()
	{
		ob_end_flush();
		$this->_admin_model = new Model_Adminmodel();
		$this->_aws = new Model_Aws();
		$this->_model = new Model_Batchmodel();
	}

	function _call_method($params)
	{
		$method = array_shift($params);
		return call_user_func_array(array($this, $method), $params);
	}

	function _write_log($log)
	{
		$file = APPPATH . "../../files/log/" . $this->_div . "_" . date('Y-m-d') . ".log";
		$f = fopen($file, 'a');
		$log = date('Y-m-d H:i:s') . ' ' . $log . PHP_EOL;
		fwrite($f, $log);
		fclose($f);
		echo ($log);
	}
}
