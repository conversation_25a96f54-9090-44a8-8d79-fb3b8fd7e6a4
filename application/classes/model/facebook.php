<?php defined('SYSPATH') OR die('No direct script access.');
class Model_Facebook extends Model
{
	function post($message, $image_url, $token)
	{
		$base_url = 'https://graph.facebook.com/v3.2/me/photos?access_token=';
		$data = [
			'caption' => $message,
			"url" => $image_url
		];
		
		$header = [
			'Content-Type: application/json',
		];
		$curl = curl_init();
		curl_setopt($curl, CURLOPT_URL, $base_url . $token);
		curl_setopt($curl, CURLOPT_CUSTOMREQUEST, 'POST'); // post
		curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($data));
		curl_setopt($curl, CURLOPT_HTTPHEADER, $header); // リクエストにヘッダーを含める
		curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
		curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($curl, CURLOPT_HEADER, true);
		$response = curl_exec($curl);
		Log::instance()->add(Log::DEBUG, "FACEBOOK POST:" . $response);	
	}
}

?>
