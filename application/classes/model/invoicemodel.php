<?php defined('SYSPATH') OR die('No direct script access.');

class Model_Invoicemodel extends Model_Basemodel
{
	function get_access_token()
	{
		$file_content = file_get_contents(APPPATH . "config/moneyforward.key");
		$token_data = json_decode($file_content, true);
		$access_token = $token_data['access_token'];
		if ($token_data['created_at'] + $token_data['expires_in'] < time()) {
			// $ curl -d client_id=[CLIENT_ID] -d client_secret=[CLIENT_SECRET] -d grant_type=refresh_token -d refresh_token=[REFRESH_TOKEN] -X POST https://invoice.moneyforward.com/oauth/token
			$data = $this->get_env('moneyforward_config');
			$data['grant_type'] = 'refresh_token';
			$data['refresh_token'] = $token_data['refresh_token'];
			$url = 'https://invoice.moneyforward.com/oauth/token';
			$header = ['Content-Type: application/json; charset=UTF-8'];
			$token_data = $this->curl($url, $header, $data);
			if (array_key_exists('access_token', $token_data)) {
				file_put_contents(APPPATH . "config/moneyforward.key", json_encode($token_data));
				$access_token = $token_data['access_token'];
			}
			else {
				return null;
			}
		}
		return $access_token;
	}
	
}

?>
