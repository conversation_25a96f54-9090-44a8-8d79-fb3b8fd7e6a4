<?php defined('SYSPATH') or die('No direct script access.');

class Model_Reportmodel extends Model_Basemodel
{
	function create_report_orm($bot_id, $report_date, $report_type_cd, $content)
	{
		$orm = ORM::factory('botreport');
		$orm->bot_id = $bot_id;
		$orm->report_type_cd = $report_type_cd;
		$orm->report_date = $report_date;
		$orm->content = $content;
		$orm->save();
	}

	function create_bot_report_ranking($bot_id, $start_date, $end_date, $context_id = NULL, $lang_cd = "", $count = 10)
	{
		$sql = "SELECT COUNT(*) AS c, intent_cd, sub_intent_cd FROM t_bot_log$this->_log_ext 
		WHERE " . $this->_create_bot_cond($bot_id) . " AND intent_cd LIKE 'inquiry.%' AND member_msg<>'' AND score > 0 AND score <=1 AND answer_type > 0 ";
		if ($start_date != NULL) $sql = $sql . " AND log_time>=:start_date ";
		if ($end_date != NULL) $sql = $sql . " AND log_time<:end_date ";
		if ($context_id != NULL) $sql = $sql . " AND context_id=:context_id ";
		if ($lang_cd != '') $sql = $sql . " AND lang_cd=:lang_cd ";

		if ($count == -1) {
			$sql = $sql . " GROUP BY intent_cd, sub_intent_cd ORDER BY c DESC, intent_cd, sub_intent_cd ";
		} else {
			$sql = $sql . " GROUP BY intent_cd, sub_intent_cd ORDER BY c DESC, intent_cd, sub_intent_cd LIMIT 0, " . $count;
		}
		$sql = "SELECT a.c, a.intent_cd, a.sub_intent_cd, '' AS s, '' AS no_c, b.question, t.facility_question_title, b.lang_cd FROM ($sql) a 
		INNER JOIN m_bot_intent b ON a.intent_cd = b.intent_cd AND a.sub_intent_cd = b.sub_intent_cd ";
		if ($lang_cd != '') $sql = $sql . " AND b.lang_cd=:lang_cd ";
		// 日本学生支援機構
		if ($bot_id == 701001) {
			$sql = $sql . " AND b.intent_type_cd NOT LIKE '09%' ";
		}
		$sql = $sql . " LEFT JOIN t_bot_intent t ON a.intent_cd = t.intent_cd AND a.sub_intent_cd = t.sub_intent_cd AND t.lang_cd=b.lang_cd AND t.bot_id=:bot_id
		ORDER BY a.c DESC, a.intent_cd, a.sub_intent_cd, b.lang_cd";

		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':bot_id' => $bot_id,
			':lang_cd' => $lang_cd,
			':start_date' => $start_date,
			':end_date' => $end_date,
			':context_id' => $context_id,
		));
		$logs = $query->execute()->as_array();
		$ret = [];
		$bot_arr = [];
		$grp_bot_id = $this->get_grp_bot_id($bot_id);
		if ($grp_bot_id > 0) {
			$bot_arr[] = $bot_id;
			$template_bot = $this->get_bot_setting_self($bot_id, 'template_bot');
			if ($template_bot != '') $bot_arr[] = $template_bot;
			$bot_arr[] = $grp_bot_id;
		}
		foreach ($logs as $log) {
			$record = [];
			$record['intent_cd'] = $log['intent_cd'];
			$record['sub_intent_cd'] = $log['sub_intent_cd'];
			$record['lang_cd'] = $log['lang_cd'];
			//$record['context_id'] = $log['context_id'];
			if ($grp_bot_id > 0) {
				$record['question'] = $this->get_faq_question_title($bot_arr, $log['intent_cd'], $log['sub_intent_cd'], $log['lang_cd']);
				if ($record['question'] == '') $record['question'] = $log['question'];
			} else {
				if ($log['facility_question_title'] == '') {
					$record['question'] = $log['question'];
				} else {
					$record['question'] = $log['facility_question_title'];
				}
			}
			$ret[] = $record;
		}
		return $ret;
	}

	function create_bot_report_freq($bot_id, $start_date, $end_date, $lang_cd = '')
	{
		$sql = "SELECT COUNT(*) AS c, intent_cd, sub_intent_cd FROM t_bot_log$this->_log_ext
		WHERE " . $this->_create_bot_cond($bot_id) . " AND intent_cd LIKE 'inquiry.%' AND member_msg<>'' AND score > 0 AND score <=1 AND answer_type > 0 ";
		if ($start_date != NULL) $sql = $sql . " AND log_time>=:start_date ";
		if ($end_date != NULL) $sql = $sql . " AND log_time<:end_date ";
		if ($lang_cd != '') $sql = $sql . " AND lang_cd=:lang_cd ";
		$sql = $sql . " GROUP BY intent_cd, sub_intent_cd ORDER BY c DESC, intent_cd, sub_intent_cd";

		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':bot_id' => $bot_id,
			':lang_cd' => $lang_cd,
			':start_date' => $start_date,
			':end_date' => $end_date,
		));
		$log = $query->execute()->as_array();
		return $log;
	}

	function create_bot_report_daily($bot_id, $start_date)
	{
		$last_date = date("Y-m-d", strtotime("-1 day", strtotime($start_date)));
		$total_data_array = $this->get_bot_report($bot_id, $last_date, '02');
		if ($total_data_array == NULL) {
			$total_data_array = [
				'all' => [
					'member' => 0,
					'repeater' => 0,
					'log' => 0,
					'log_answer' => 0,
					'log_correct' => 0
				],
				'month' => [
					'member' => 0,
					'repeater' => 0,
					'log' => 0,
					'log_answer' => 0,
					'log_correct' => 0
				],
				'week' => [
					'member' => 0,
					'repeater' => 0,
					'log' => 0,
					'log_answer' => 0,
					'log_correct' => 0
				],
				'day' => [
					'member' => 0,
					'repeater' => 0,
					'log' => 0,
					'log_answer' => 0,
					'log_correct' => 0
				],
			];
		}
		$params = array(
			':start_date' => $start_date,
			':end_date' => date("Y-m-d", strtotime("+1 day", strtotime($start_date))),
			':intent_cd' => 'input.unknown',
			':score_min' => 0,
			':score_max' => 0.85,
			':bot_id' => $bot_id,
		);

		$bot_id_tbl = $this->get_log_table($bot_id);

		$data_array = ['member' => 0, 'member_faq' => 0, 'member_inquiry' => 0, 'member_survey' => 0, 'member_very' => 0, 'follow' => 0, 'follow_faq' => 0, 'follow_inquiry' => 0, 'follow_survey' => 0, 'follow_very' => 0];
		// お客様人数
		$sql = "SELECT sns_id FROM t_bot_member WHERE is_tester=0 AND regist_date >= :start_date AND regist_date < :end_date AND bot_id=:bot_id";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters($params);
		$result = $query->execute('slave')->as_array();
		foreach ($result as $r) {
			if ($r['sns_id'] == 'faq01') {
				$data_array['member_faq'] = $data_array['member_faq'] + 1;
			} else if ($r['sns_id'] == 'inquiry01') {
				$data_array['member_inquiry'] = $data_array['member_inquiry'] + 1;
			} else if ($r['sns_id'] == 'survey01') {
				$data_array['member_survey'] = $data_array['member_survey'] + 1;
			} else if ($r['sns_id'] == 'very01') {
				$data_array['member_very'] = $data_array['member_very'] + 1;
			} else {
				$data_array['member'] = $data_array['member'] + 1;
			}
		}

		// お客様人数REPEAT
		$sql = "SELECT t.bot_id, COUNT(DISTINCT t.member_id) AS member_count FROM t_bot_log$bot_id_tbl t INNER JOIN t_bot_member m ON t.bot_id = m.bot_id AND t.member_id = m.member_id AND m.is_tester=0 AND m.regist_date < :start_date WHERE t.bot_id=:bot_id AND t.member_msg <> '' AND log_time >= :start_date AND log_time < :end_date";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters($params);
		$result = $query->execute('slave')->as_array();
		$data_array['repeater'] = $result[0]['member_count'];

		// Follow数
		$sql = "SELECT sns_id FROM t_bot_follow WHERE follow_time >= :start_date AND follow_time < :end_date AND bot_id=:bot_id";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters($params);
		$result = $query->execute('slave')->as_array();
		foreach ($result as $r) {
			if ($r['sns_id'] == 'faq01') {
				$data_array['follow_faq'] = $data_array['follow_faq'] + 1;
			} else if ($r['sns_id'] == 'inquiry01') {
				$data_array['follow_inquiry'] = $data_array['follow_inquiry'] + 1;
			} else if ($r['sns_id'] == 'survey01') {
				$data_array['follow_survey'] = $data_array['follow_survey'] + 1;
			} else if ($r['sns_id'] == 'very01') {
				$data_array['follow_very'] = $data_array['follow_very'] + 1;
			} else {
				$data_array['follow'] = $data_array['follow'] + 1;
			}
		}
		
		//　全件数
		$sql = "SELECT bot_id, COUNT(log_id) AS log_count FROM t_bot_log$bot_id_tbl log WHERE log.is_tester=0 AND log.member_msg <> '' AND log.log_time >= :start_date AND log.log_time < :end_date AND bot_id=:bot_id";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters($params);
		$result = $query->execute('slave')->as_array();
		$data_array['log'] = $result[0]['log_count'];

		/*
		//　有効全件数
		$sql = "SELECT bot_id, COUNT(log_id) AS log_count FROM t_bot_log$bot_id_tbl log WHERE log.is_tester=0 AND log.member_msg <> '' AND intent_cd<>'welcome' AND intent_cd<>'member_start_chat' AND intent_cd<>'member_end_chat' AND log.log_time >= :start_date AND log.log_time < :end_date AND bot_id=:bot_id";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters($params);
		$result = $query->execute('slave')->as_array();
		$data_array['reallog'] = $result[0]['log_count'];
		
		// 返答件数
		$sql = "SELECT bot_id, COUNT(log_id) AS log_count FROM t_bot_log$bot_id_tbl log WHERE log.is_tester=0 AND log.member_msg <> '' AND intent_cd=:intent_cd AND log.log_time >= :start_date AND log.log_time < :end_date AND bot_id=:bot_id";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters($params);
		$result = $query->execute('slave')->as_array();
		$data_array['log_answer'] = $data_array['reallog'] - $result[0]['log_count'];
		*/

		// 返答件数
		$sql = "SELECT bot_id, COUNT(log_id) AS log_count FROM t_bot_log$bot_id_tbl log WHERE log.is_tester=0 AND log.member_msg <> '' AND intent_cd<>:intent_cd AND log.log_time >= :start_date AND log.log_time < :end_date AND bot_id=:bot_id";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters($params);
		$result = $query->execute('slave')->as_array();
		$data_array['log_answer'] = $result[0]['log_count'];

		// 正解回答
		$sql = "SELECT bot_id, COUNT(log_id) AS log_count FROM t_bot_log$bot_id_tbl log WHERE log.is_tester=0 AND log.member_msg <> '' AND (score>=:score_max OR score=0 AND intent_cd<>'input.unknown') AND log.log_time >= :start_date AND log.log_time < :end_date AND bot_id=:bot_id";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters($params);
		$result = $query->execute('slave')->as_array();
		$data_array['log_correct'] = $result[0]['log_count'];

		// リクエスト件数
		// 宿泊予約件数
		// INQUIRY予約件数

		$week = date('w', strtotime($start_date));
		$day = date('d', strtotime($start_date));
		$this->_group_report($total_data_array, $data_array, 'all');
		if ($day == '01') {
			$total_data_array['month'] = $data_array;
		} else {
			$this->_group_report($total_data_array, $data_array, 'month');
		}
		if ($week == 1) {
			$total_data_array['week'] = $data_array;
		} else {
			$this->_group_report($total_data_array, $data_array, 'week');
		}
		$total_data_array['day'] = $data_array;
		$total_data_array['upd_time'] = date('Y-m-d H:i:s');
		return $total_data_array;
	}

	function get_faq_question_title($bot_arr, $intent_cd, $sub_intent_cd, $lang_cd) 
	{
		$first = true;
		foreach($bot_arr as $bot_id) {
			$sql = "SELECT facility_question_title, inherit FROM t_bot_intent t WHERE bot_id=:bot_id AND intent_cd=:intent_cd AND sub_intent_cd=:sub_intent_cd AND lang_cd=:lang_cd";
			$query = DB::query(Database::SELECT, $sql);
			$query->parameters(array(
					':bot_id' => $bot_id,
					':intent_cd' => $intent_cd,
					':sub_intent_cd' => $sub_intent_cd,
					':lang_cd' => $lang_cd,
			));
			$logs = $query->execute()->as_array();
			if (count($logs) > 0) {
				$log = $logs[0];
				if ($first) {
					if ($log['inherit'] == 0) return $log['facility_question_title'];
				}
				else {
					if ($log['facility_question_title'] != '') return $log['facility_question_title'];
				}
			}
		}
		return '';
	}
	private function _group_report(&$total_data_array, $data_array, $group) {
		$total_data_array[$group]['member'] = $total_data_array[$group]['member'] + $data_array['member'];
		$total_data_array[$group]['member_faq'] = $total_data_array[$group]['member_faq'] + $data_array['member_faq'];
		$total_data_array[$group]['member_inquiry'] = $total_data_array[$group]['member_inquiry'] + $data_array['member_inquiry'];
		$total_data_array[$group]['member_survey'] = $total_data_array[$group]['member_survey'] + $data_array['member_survey'];
		$total_data_array[$group]['member_very'] = $total_data_array[$group]['member_very'] + $data_array['member_very'];
		$total_data_array[$group]['repeater'] = $total_data_array[$group]['repeater'] + $data_array['repeater'];
		$total_data_array[$group]['follow'] = $total_data_array[$group]['follow'] + $data_array['follow'];
		$total_data_array[$group]['follow_faq'] = $total_data_array[$group]['follow_faq'] + $data_array['follow_faq'];
		$total_data_array[$group]['follow_inquiry'] = $total_data_array[$group]['follow_inquiry'] + $data_array['follow_inquiry'];
		$total_data_array[$group]['follow_survey'] = $total_data_array[$group]['follow_survey'] + $data_array['follow_survey'];
		$total_data_array[$group]['follow_very'] = $total_data_array[$group]['follow_very'] + $data_array['follow_very'];
		$total_data_array[$group]['log'] = $total_data_array[$group]['log'] + $data_array['log'];
		$total_data_array[$group]['log_answer'] = $total_data_array[$group]['log_answer'] + $data_array['log_answer'];
		$total_data_array[$group]['log_correct'] = $total_data_array[$group]['log_correct'] + $data_array['log_correct'];
	}
}

