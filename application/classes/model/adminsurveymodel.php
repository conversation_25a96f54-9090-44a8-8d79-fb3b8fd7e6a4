<?php defined('SYSPATH') OR die('No direct script access.');

class Model_Adminsurveymodel extends Model_Basemodel
{	
    function find_coupondesc($coupon_id, $lang_cd){
		$sql = "SELECT *
                FROM t_coupon_description t
				WHERE t.coupon_id = :coupon_id
                AND t.lang_cd = :lang_cd ";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
            ':coupon_id' => $coupon_id,
            ':lang_cd' => $lang_cd,
        ));
		$result = $query->execute()->as_array()[0];
        if ($result) return $result;
        return false;
	}

    public function save_coupondesc($coupon_id, $lang_cd, $post)
	{	
        DB::insert('t_coupon_description', array('coupon_id', 'lang_cd', 'title', 'sub_title', 
        'image', 'style', 'section', 'button_pc', 'button_sp'))
        ->values(array(
            $coupon_id, 
            $lang_cd,
            $post['title'],
            $post['sub_title'],
            $post['image'],
            $post['style'],
            json_encode($post['section'], JSON_UNESCAPED_UNICODE),
            json_encode($post['button_pc'], JSON_UNESCAPED_UNICODE),
            json_encode($post['button_sp'], JSON_UNESCAPED_UNICODE),
        ))->execute();
    }

    public function update_coupondesc($coupon_id, $lang_cd, $post)
	{	
		DB::update('t_coupon_description')->set([
            'title'=>$post['title'],
            'sub_title'=>$post['sub_title'],
            'image'=>$post['image'],
            'style'=>$post['style'],
            'section'=>json_encode($post['section'], JSON_UNESCAPED_UNICODE),
            'button_pc'=>json_encode($post['button_pc'], JSON_UNESCAPED_UNICODE),
            'button_sp'=>json_encode($post['button_sp'], JSON_UNESCAPED_UNICODE)
            ])->where('coupon_id', '=', $coupon_id)->where('lang_cd', '=', $lang_cd)->execute();
    }

    public function auto_translate($lang, $target, $lang_cd_src)
	{	
        $data = [];
        if ($target == null || $target == '') return $data;

        $decodedTarget = json_decode($target, true);
        if ($decodedTarget) {
            for ($i=0; $i < count($decodedTarget); $i++) {
                foreach($decodedTarget[$i] as $k=>$v) {
                    if ($k == 'action') {
                        $data[$i][$k] = $v;
                    } else {
                        $data[$i][$k] = $this->translate($v, $lang, $lang_cd_src);
    
                    }
                }
            }
        }
        return $data;
    }

    public function format_coupons($present) {
        $formatedCoupons = [];
        if ($present != NULL && $present != '[]') {
            $coupons = json_decode($present, true);
            if ($coupons) {
                if (is_array($coupons)) {
                    $formatedCoupons = $coupons;
                } else {
                    $formatedCoupons[] = ['id' => $present];
                }
            }
        }
        return $formatedCoupons;
    }

    public function get_undeleted_coupons($present) {
		$valid_coupons= [];
        $formatedCoupons = $this->format_coupons($present);
        foreach ($formatedCoupons as $coupon) {
            $coupon_id = $coupon['id'];
            $orm = ORM::factory('coupon', $coupon_id);
            $valid_coupons['01'] = __('admin.surveymenu.label.couponresult');
            if (isset($orm->coupon_id)) {
                if ($orm->delete_flg ==0) {
                    $valid_coupons[$coupon_id] = $orm->coupon_name;
                }
            } else {
                $orm = ORM::factory('product', $coupon_id);
                $desc = ORM::factory('productdescription')->where('product_id', '=', $coupon_id)->where('lang_cd', '=', 'ja')->find();
                if (isset($orm->product_id) && $orm->delete_flg == 0) {
                    $valid_coupons[$coupon_id] = $desc->product_name;
                }
            }
        }
		return $valid_coupons;
	}
}