<?php defined('SYSPATH') OR die('No direct script access.');

class Model_Surveymodel extends Model_Basemodel
{
	function get_entry_data($entry_data, $lang_cd) {
		$entry_data = json_decode($entry_data);
		if (is_array($entry_data)) {
			return $entry_data;
		}
		else {
			return array_values($this->get_code_div_kv($entry_data, $lang_cd));
		}
	}
	
	function get_max_member_no($bot_id) {
		$sql = "SELECT max(member_no) AS member_no FROM t_bot_member WHERE bot_id=:bot_id";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':bot_id' => $bot_id,
		));
		$results = $query->execute()->as_array();
		return $results[0]['member_no'] + 1;
	}
	
	function get_max_coupon_order_no() {
		$date_str = date('Ymd');
		$sql = "SELECT order_no FROM t_bot_order WHERE order_no LIKE 'CPN" . $date_str . "%' ORDER BY order_no DESC LIMIT 0, 1";
		$query = DB::query(Database::SELECT, $sql);
		$results = $query->execute()->as_array();
		if (count($results) == 0) {
			return "CPN" . date('Ymd') . '0001';
		}
		else {
			$no = substr($results[0]['order_no'], 11);
			return "CPN" . date('Ymd') . sprintf("%04d", intval($no) + 1);
		}
	}
	
	function get_coupon_orders($bot_id, $product_id) {
		$sql = "SELECT COUNT(num) AS ordercount FROM t_bot_order WHERE bot_id=:bot_id AND product_id=:product_id";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
				':product_id' => $product_id,
		));
		$results = $query->execute()->as_array();
		if (count($results) == 0) {
			return 0;
		}
		else {
			return $results[0]['ordercount'];
		}
	}
	
	function check_coupon_issue($bot_id, $coupon_id) {
		$product = ORM::factory('product', $coupon_id);
		$product_data = json_decode($product->product_data, true);
		if ($product_data['stock_type'] == 3) {
			$use_count = $this->get_coupon_orders($bot_id, $coupon_id);
			if ($product_data['stock'] <= $use_count) {
				return false;
			}
		}
		return true;
	}
	
	function get_all_coupon_usecount($bot_id, $product_id) {
		$sql = "SELECT SUM(num) AS usecount FROM t_bot_order WHERE bot_id=:bot_id AND product_id=:product_id";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
				':product_id' => $product_id,
		));
		$results = $query->execute()->as_array();
		if (count($results) == 0) {
			return 0;
		}
		else {
			return $results[0]['usecount'];
		}
	}
	
	function use_coupon($bot_id, $product_id, $member_id) {
		$sql = "UPDATE t_bot_order SET num = num + 1, use_date=:use_date WHERE bot_id=:bot_id AND product_id=:product_id AND member_id=:member_id";
		$query = DB::query(Database::UPDATE, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
				':product_id' => $product_id,
				':member_id' => $member_id,
				':use_date' => date('Y-m-d H:i:s'),
		));
		$results = $query->execute();
	}
	
	function check_coupon_stock($bot_id, $coupon_id, $order_id) {
		$order = ORM::factory('botorder', $order_id);
		$product = ORM::factory('product', $coupon_id);
		$product_data = json_decode($product->product_data, true);
		if ($product_data['stock_type'] == 1) {
			return $order->num;
		}
		else if ($product_data['stock_type'] == 2) {
			$use_count = $this->get_all_coupon_usecount($bot_id, $coupon_id);
			if ($product_data['stock'] <= $use_count) {
				return -1;
			}
			else {
				return $use_count;
			}
		}
		else if ($product_data['stock_type'] == 3) {
			if ($product_data['stock'] <= $order->num) {
				return -1;
			}
			else {
				return $order->num;
			}
		}
		else {
			return -1;
		}
	}
	
	function check_coupon_stock2_old($bot_id, $coupon_id, $coupon_data, $order_num) {
		$product_data = json_decode($coupon_data, true);
		if ($product_data['stock_type'] == 1) {
			return $order_num;
		}
		else if ($product_data['stock_type'] == 2) {
			$use_count = $this->get_all_coupon_usecount($bot_id, $coupon_id);
			if ($product_data['stock'] <= $use_count) {
				return -1;
			}
			else {
				return $use_count;
			}
		}
		else if ($product_data['stock_type'] == 3) {
			if ($product_data['stock'] <= $order_num) {
				return -1;
			}
			else {
				return $order_num;
			}
		}
		else {
			return -1;
		}
	}
	
	function check_coupon_stock2($bot_id, $coupon_id, $coupon_data, $order_num) {
		$product_data = json_decode($coupon_data, true);
		if ($product_data['stock_type'] == 2) {
			$use_count = $this->get_all_coupon_usecount($bot_id, $coupon_id);
			if ($product_data['stock'] <= $use_count) {
				return -1;
			}
		}
		
		if ($product_data['stock_type_member'] == 2) {
			if ($product_data['stock_member'] <= $order_num) {
				return -2;
			}
			else {
				return $order_num;
			}
		}
		else {
			return $order_num;
		}
	}

	
	function get_member_coupons($bot_id, $member_id, $class_cd, $lang_cd) {
		
		$sql = 'SELECT c.product_id coupon_id, c.product_data coupon_data, c.start_date, c.end_date, d.product_name coupon_name, o.order_id, o.use_date, o.num
				FROM t_product c LEFT JOIN t_product_description d ON c.product_id=d.product_id AND d.lang_cd=:lang_cd
				INNER JOIN t_bot_order o ON c.product_id=o.product_id AND o.item_div=7 AND o.member_id=:member_id
				WHERE c.class_cd=:class_cd AND c.delete_flg=0 AND ';
		$grp_bot_id = $this->get_grp_bot_id($bot_id);
		if ($grp_bot_id > 0) {
			$sql = $sql . "(c.bot_id=:bot_id OR c.bot_id=$grp_bot_id)";
		}
		else {
			$sql = $sql . 'c.bot_id=:bot_id';
		}
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
				':class_cd' => $class_cd,
				':member_id' => $member_id,
				':lang_cd' => $lang_cd,
		));
		$results = $query->execute();
		return $results->as_array();
	}
	
	function get_products($bot_id, $item_div, $class_cd, $lang_cd, $keyword = NULL)
	{
		$sql = "SELECT a.product_id, a.product_cd, a.class_cd, a.country_cd, a.product_status_cd, a.start_date, a.end_date, a.link_type_cd, a.link_key, a.upd_user,
		b.product_name, b.product_image, b.description, b.sell_point,b.notes, d.sort_no1, d.recommend, d.lang_display, d.public_flg
		FROM t_product a
		LEFT JOIN t_product_description b ON a.product_id = b.product_id AND b.lang_cd=:lang_cd
		LEFT JOIN t_item_display d ON d.item_id = a.product_id AND d.item_div = $item_div" ;
		if ($keyword != NULL) {
			$sql = "$sql INNER JOIN t_keyword k ON a.product_id = k.find_id AND k.keyword LIKE '%$keyword%' ";
		}
		$sql = "$sql WHERE a.bot_id = :bot_id AND a.item_div = $item_div AND a.class_cd LIKE '$class_cd%' AND a.delete_flg = 0 ORDER BY a.class_cd, a.product_cd";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
				':lang_cd' => $lang_cd,
		));
		$results = $query->execute();
		return $results->as_array();
	}

	function get_survey_report($survey_id, $start_date, $end_date, $type) {
		$formatted_start_date = date('Y-m-d');
		$formatted_end_date = date('Y-m-d');

		$type_format = $type == 'monthly' ? "%Y-%m" : "%Y-%m-%d";

		// 言語別用
		// $sql = "SELECT date_format(s.end_time, '%Y-%m') AS month, date_format(s.end_time, '%Y-%m-%d') as day, s.lang_cd as lang
		// 一旦総数を表示
		$sql = "SELECT date_format(s.end_time, :type_format) as time, count(s.id) as total
		FROM t_survey_result s
		WHERE survey_id=:survey_id";

		if ($start_date != '') {
			$formatted_start_date = date("Y-m-d", strtotime($start_date));
			$sql .= ' AND date_format(s.end_time, "%Y-%m-%d") >= :formatted_start_date';
		}
		if ($end_date != '') {
			$formatted_end_date = date("Y-m-d", strtotime("+1 day", strtotime($end_date)));
			$sql .= ' AND date_format(s.end_time, "%Y-%m-%d") < :formatted_end_date';
		}
		$sql .= " GROUP BY time ORDER BY time ASC";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':type_format' => $type_format,
			':survey_id' => $survey_id,
			':formatted_start_date' => $formatted_start_date,
			':formatted_end_date' => $formatted_end_date
		));
		$results = $query->execute()->as_array();
		return $results;
 	}

	 function get_all_survey($bot_id) {
		$sql = "SELECT survey_id, survey_name, support_lang_cd
		FROM t_survey
		WHERE delete_flg=0 AND bot_id=:bot_id";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':bot_id' => $bot_id
		));
		$results = $query->execute()->as_array();
		return $results;
	}

	function get_survey_entry_with_no($survey_id, $lang_cd) {
		$sql = "
			SELECT no, input_rules
			FROM t_survey_entry
			WHERE survey_id=:survey_id AND lang_cd=:lang_cd
		";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':survey_id' => $survey_id,
			':lang_cd' => $lang_cd,
		));
		$results = $query->execute()->as_array();
		return $results;
	}

	// should retrive results of all entries(nos), for filtering
	function get_survey_result_name_and_email_and_extends($survey_id, $lang_cd, $nos, $filtering=false) {
		if (empty($nos)) return [];
		$result_details = $filtering ? ", 'entry_result', e.entry_result, 'entry_extra_info', e.entry_extra_info" : '';
		$sql = "
			SELECT r.id, JSON_ARRAYAGG(JSON_OBJECT('no', e.no, 'entry_data', e.entry_data$result_details)) as result
			FROM t_survey_result r
			INNER JOIN t_survey_result_entry e ON r.id=e.result_id AND e.no IN :nos
			WHERE r.survey_id=:survey_id AND r.lang_cd=:lang_cd
			GROUP BY r.id
		";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':nos' => $nos,
			':survey_id' => $survey_id,
			':lang_cd' => $lang_cd
		));
		$results = $query->execute()->as_array();
		return $results;
	}
}

?>
