<?php defined('SYSPATH') OR die('No direct script access.');

class Model_Inquirymodel extends Model_Basemodel
{
	function get_entry_data($entry_data, $lang_cd) {
		$entry_data = json_decode($entry_data);
		if (is_array($entry_data)) {
			return $entry_data;
		}
		else {
			return array_values($this->get_code_div_kv($entry_data, $lang_cd));
		}
	}
	
	function get_max_member_no($bot_id) {
		$sql = "SELECT max(member_no) AS member_no FROM t_bot_member WHERE bot_id=:bot_id";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':bot_id' => $bot_id,
		));
		$results = $query->execute()->as_array();
		return $results[0]['member_no'] + 1;
	}
	
	function get_coupon_from_code($bot_id, $coupon_id_arr, $coupon_code, $lang_cd) {
		$sql = "SELECT coupon_id, coupon_name, coupon_data FROM t_coupon WHERE bot_id=:bot_id AND input_code=:input_code ";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':bot_id' => $bot_id,
			':input_code' => $coupon_code,
		));
		$results = $query->execute()->as_array();
		$coupons = [];
		foreach($results as $r) {
			if (in_array($r['coupon_id'], $coupon_id_arr)) {
				$coupons[$r['coupon_id']] = json_decode($r['coupon_data'], true);
				$coupons[$r['coupon_id']]['coupon_name'] = $r['coupon_name'];
			}
		}
		if (count($coupons) != 1) return ["error"=>'check.notfound'];
		$coupon_id = key($coupons);
		$coupon_data = $coupons[$coupon_id];
		$coupon_data['coupon_id'] = $coupon_id;
		$coupon_data['coupon_code'] = $coupon_code;
		$desc = ORM::factory('coupondescription')->where('coupon_id', '=', $coupon_id)->where('lang_cd', '=', $lang_cd)->find();
		if (isset($desc->title)) {
			$coupon_data['title'] = $desc->title;
		}
		else {
			$coupon_data['title'] = $coupons[$coupon_id]['coupon_name'] ;
		}
		// check coupon
		$coupon_model = new Model_Couponmodel();
		if ($coupon_model->check_coupon_issue($coupon_id) == false) return ["error"=>'check.issue'];
		return $coupon_data;
	}

	function get_max_coupon_order_no() {
		$date_str = date('Ymd');
		$sql = "SELECT order_no FROM t_bot_order WHERE order_no LIKE 'CPN" . $date_str . "%' ORDER BY order_no DESC LIMIT 0, 1";
		$query = DB::query(Database::SELECT, $sql);
		$results = $query->execute()->as_array();
		if (count($results) == 0) {
			return "CPN" . date('Ymd') . '0001';
		}
		else {
			$no = substr($results[0]['order_no'], 11);
			return "CPN" . date('Ymd') . sprintf("%04d", intval($no) + 1);
		}
	}
	
	function get_all_coupon_usecount($bot_id, $product_id) {
		$sql = "SELECT SUM(num) AS usecount FROM t_bot_order WHERE bot_id=:bot_id AND product_id=:product_id";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
				':product_id' => $product_id,
		));
		$results = $query->execute()->as_array();
		if (count($results) == 0) {
			return 0;
		}
		else {
			return $results[0]['usecount'];
		}
	}
	
	function use_coupon($bot_id, $product_id, $member_id) {
		$sql = "UPDATE t_bot_order SET num = num + 1, use_date=:use_date WHERE bot_id=:bot_id AND product_id=:product_id AND member_id=:member_id";
		$query = DB::query(Database::UPDATE, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
				':product_id' => $product_id,
				':member_id' => $member_id,
				':use_date' => date('Y-m-d H:i:s'),
		));
		$results = $query->execute();
	}
	
	function check_coupon_stock($bot_id, $coupon_id, $order_id) {
		$order = ORM::factory('botorder', $order_id);
		$product = ORM::factory('product', $coupon_id);
		$product_data = json_decode($product->product_data, true);
		if ($product_data['stock_type'] == 1) {
			return $order->num;
		}
		else if ($product_data['stock_type'] == 2) {
			$use_count = $this->get_all_coupon_usecount($bot_id, $coupon_id);
			if ($product_data['stock'] <= $use_count) {
				return -1;
			}
			else {
				return $use_count;
			}
		}
		else if ($product_data['stock_type'] == 3) {
			if ($product_data['stock'] <= $order->num) {
				return -1;
			}
			else {
				return $order->num;
			}
		}
		else {
			return -1;
		}
	}
	
	function check_coupon_stock2_old($bot_id, $coupon_id, $coupon_data, $order_num) {
		$product_data = json_decode($coupon_data, true);
		if ($product_data['stock_type'] == 1) {
			return $order_num;
		}
		else if ($product_data['stock_type'] == 2) {
			$use_count = $this->get_all_coupon_usecount($bot_id, $coupon_id);
			if ($product_data['stock'] <= $use_count) {
				return -1;
			}
			else {
				return $use_count;
			}
		}
		else if ($product_data['stock_type'] == 3) {
			if ($product_data['stock'] <= $order_num) {
				return -1;
			}
			else {
				return $order_num;
			}
		}
		else {
			return -1;
		}
	}
	
	function check_coupon_stock2($bot_id, $coupon_id, $coupon_data, $order_num) {
		$product_data = json_decode($coupon_data, true);
		if ($product_data['stock_type'] == 2) {
			$use_count = $this->get_all_coupon_usecount($bot_id, $coupon_id);
			if ($product_data['stock'] <= $use_count) {
				return -1;
			}
		}
		
		if ($product_data['stock_type_member'] == 2) {
			if ($product_data['stock_member'] <= $order_num) {
				return -2;
			}
			else {
				return $order_num;
			}
		}
		else {
			return $order_num;
		}
	}
	
	function get_member_coupons($bot_id, $member_id, $class_cd, $lang_cd) {
		
		$sql = 'SELECT c.product_id coupon_id, c.product_data coupon_data, c.start_date, c.end_date, d.product_name coupon_name, o.order_id, o.use_date, o.num
				FROM t_product c LEFT JOIN t_product_description d ON c.product_id=d.product_id AND d.lang_cd=:lang_cd
				INNER JOIN t_bot_order o ON c.product_id=o.product_id AND o.item_div=7 AND o.member_id=:member_id
				WHERE c.class_cd=:class_cd AND c.delete_flg=0 AND ';
		$grp_bot_id = $this->get_grp_bot_id($bot_id);
		if ($grp_bot_id > 0) {
			$sql = $sql . "(c.bot_id=:bot_id OR c.bot_id=$grp_bot_id)";
		}
		else {
			$sql = $sql . 'c.bot_id=:bot_id';
		}
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
				':class_cd' => $class_cd,
				':member_id' => $member_id,
				':lang_cd' => $lang_cd,
		));
		$results = $query->execute();
		return $results->as_array();
	}

	function check_unique($inquiry_id, $no, $value) {
		$sql = 'SELECT count(1) as c FROM t_inquiry_result_entry
				WHERE no=:no AND result_id IN (SELECT id FROM t_inquiry_result WHERE inquiry_id=:inquiry_id) AND entry_result=:value';
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':inquiry_id' => $inquiry_id,
				':no' => $no,
				':value' => $value
		));
		$results = $query->execute()->as_array();
		return $results[0]['c'];
	}

	function get_all_inquiry($bot_id) {
		$sql = "SELECT inquiry_id, inquiry_name, support_lang_cd, label_id
		FROM t_inquiry
		WHERE delete_flg=0 AND bot_id=:bot_id";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':bot_id' => $bot_id
		));
		$results = $query->execute()->as_array();
		return $results;
	}

	function get_inquiry_result_name_and_email($inquiry_id, $lang_cd, $nos, $filtering=false) {
		if (empty($nos)) return [];
		$result_details = $filtering ? ", 'entry_result', e.entry_result, 'entry_extra_info', e.entry_extra_info" : '';
		$sql = "
			SELECT r.id, JSON_ARRAYAGG(JSON_OBJECT('no', CONVERT(e.no, CHAR), 'entry_data', e.entry_data$result_details)) as result
			FROM t_inquiry_result r
			INNER JOIN t_inquiry_result_entry e ON r.id=e.result_id AND e.no IN :nos
			WHERE r.inquiry_id=:inquiry_id AND r.lang_cd=:lang_cd
			GROUP BY r.id
		";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':nos' => $nos,
			':inquiry_id' => $inquiry_id,
			':lang_cd' => $lang_cd
		));
		$results = $query->execute()->as_array();
		return $results;
	}

	public function save_inquirycalendar($bot_id, $calendar_id, $data, $user_id) {
		try {
			$save_sql = "UPDATE t_inquiry_calendar SET ";
			if (isset($data['title'])) {
				$save_sql .= "title=:title, ";
			}
			if (isset($data['remark'])) {
				$save_sql .= "remark=:remark, ";
			}
			if (isset($data['calendar_data'])) {
				$save_sql .= "calendar_data=:calendar_data, ";
			}
			$save_sql .= " upd_user=:upd_user, upd_time=:upd_time WHERE id=:id AND bot_id=:bot_id";
			$query = DB::query(Database::UPDATE, $save_sql);
			$value = [
				':bot_id' => $bot_id,
				':id' => $calendar_id,
				':upd_user' => $user_id,
				':upd_time' => date('Y-m-d H:i:s')
			];
			if (isset($data['title'])) {
				$value[':title'] = json_encode($data['title'], JSON_UNESCAPED_UNICODE);
			}
			if (isset($data['remark'])) {
				$value[':remark'] = json_encode($data['remark'], JSON_UNESCAPED_UNICODE);
			}
			if (isset($data['calendar_data'])) {
				$value[':calendar_data'] = json_encode($data['calendar_data'], JSON_UNESCAPED_UNICODE);
			}
			$query->parameters($value);
			$query->execute();
		} catch (\Throwable $th) {
			throw $th;
		}
	}

	public function create_supportmemoview($supports,  $inquiry_status_list) {
		$labelcolor = ['01'=>'#d84a38', '02'=>'#FFB848', '03'=>'#1BBC9B', '04'=>'#95A5A6', '05'=>'#CC00B2', '06'=>'#798a57'];
		$view_html = '';
		foreach($supports as $support) {
			if ($support['support_type_cd'] != '') {
				$support_label = '<span class="label" style="color:#FFF;background-color:' . $labelcolor[$support['support_type_cd']] . '">' . $inquiry_status_list[$support['support_type_cd']] . '</span>';
				$close = '';
			}
			else {
				$support_label = '';
				$close = '<div class="icon-cancel-small js-memo-delete" no="' . $support['no'] . '" style="margin: 0 0 0 auto;"></div>';
			}
			$memo = '';
			if(in_array($support['support_type_cd'], ['send_mail', 'received_mail'], true)){
				$comment = '<div style="width: calc(100% - 20px);font-size:11px;" class="js-memo-mail memo-mail" no="'. $support['id'] .'">'. date('Y/m/d H:i', strtotime($support['upd_time']))  . ' ' . $support['title'] . '<br/>' . $memo .'</div>';
				if($support['support_type_cd'] == 'send_mail'){
					$close = '<div class="icon-preview-mail-sent js-memo-mail" no="' . $support['id'] . '" style="margin: 0 0 0 auto;"></div>';
				} else if($support['support_type_cd'] == 'received_mail') {
					$close = '<div class="icon-preview-mail js-memo-mail" no="' . $support['id'] . '" style="margin: 0 0 0 auto;"></div>';
				}
			} else {
				if ($support['memo'] != null) $memo = nl2br($support['memo']);
				$comment = '<div style="width: calc(100% - 20px);font-size:11px;">'. substr($support['upd_time'], 0, 16) . ' ' . $support['name'] . $support_label . '<br/>' . nl2br($support['memo']) .'</div>';
			}
			$view_html .= '<div class="small-table-pannel" style="display: flex;padding: 8px;">' . $comment . $close . '</div>';
		}
		return $view_html;
	}

}

?>
