<?php defined('SYSPATH') OR die('No direct script access.');

class Model_Snsmodel extends Model_Basemodel
{
	private $_qr_service = 'https://api.qrserver.com/v1/create-qr-code/?size=250x250&ecc=H&data=';
	
	public function init_bot($bot)
	{
		$this->_bot = $bot;
		if ($bot != null) $this->get_log_table($bot->bot_id);
	}

	public function send_message($recipient, $message, $is_translate)
	{
		$member = ORM::factory("botmember")->where("bot_id", "=", $this->_bot->bot_id)->where("member_id", "=", $recipient)->find();
		$sns_type_cd = $member->sns_type_cd;
		$lang_cd = $member->lang_cd;
		
		$messages = array($message, $message, $message);
		if ($is_translate) {
			$messages = $this->_translate($message, $lang_cd);
		}		
		
		if ($sns_type_cd == "fb") {
			$message_data = array("text" => $messages[2]);
			$res = $this->_send_facebook_msg($member, $message_data);
		}
		else if ($sns_type_cd == "ln") {
			$message_data = array(["type" => "text", "text" => $messages[2]]);
			$res = $this->_send_line_msg($recipient, $message_data);
		}
		else if ($sns_type_cd == "wc") {
			$message_data = array("msgtype" => "text", "text" => ["content" => $messages[2]]);
			$res = $this->_send_wechat_msg($recipient, $message_data);
		}
		else if ($sns_type_cd == "wb") {
			$res = ['result'=>'success'];
		}
		if ($res['result'] == 'success') {
			$this->_writelog($recipient, $messages[1], $messages[0]);
			return true;
		}
		else {
			$this->log_error(__FUNCTION__, json_encode($res['message'], JSON_UNESCAPED_UNICODE));
			return false;
		}
	}

	public function send_image_message($recipient, $url)
	{
		$member = ORM::factory("botmember")->where("bot_id", "=", $this->_bot->bot_id)->where("member_id", "=", $recipient)->find();
		$sns_type_cd = $member->sns_type_cd;
		$lang_cd = $member->lang_cd;

		if ($sns_type_cd == "fb") {
			$message = array("attachment" => ["type" => "image", "payload" => ["url" => $url, "is_reusable" => true]]);
			$res = $this->_send_facebook_msg($member, $message);
		}
		else if ($sns_type_cd == "ln") {
			$message = array("type" => "image", "originalContentUrl" => $url, "previewImageUrl" => $url);
			$res = $this->_send_line_msg($recipient, array($message));
		}
		else if ($sns_type_cd == "wc") {
			//$article = ["title"=>'', "description"=>'', "url"=>'', "picurl"=>$url];
			//$message = array("msgtype" => "image", "image" =>  ["media_id" => "O-j45x4tnKdjNo9hOZPsmzYJJaDwqD8HKItdssE_7vbkZEaYDL6lTpjxKOnGhyfw"]);
			//$this->_send_wechat_msg($recipient, json_encode($message, JSON_UNESCAPED_UNICODE));
		}
		else if ($sns_type_cd == "wb") {
			$res = ['result'=>'success'];
		}
		if ($res['result'] == 'success') {
			$message = array("type" => "image", "image" => $url, "url" => $url);
			$log_message = json_encode($message, JSON_UNESCAPED_UNICODE);
			$this->_writelog($recipient, $log_message, $log_message);
			return true;
		}
		else {
			$this->log_error(__FUNCTION__, json_encode($res['message'], JSON_UNESCAPED_UNICODE));
			return false;
		}
	}

	public function send_file_message($recipient, $url, $filename = NULL, $filesize = NULL)
	{
		$member = ORM::factory("botmember")->where("bot_id", "=", $this->_bot->bot_id)->where("member_id", "=", $recipient)->find();
		$sns_type_cd = $member->sns_type_cd;
		$lang_cd = $member->lang_cd;
		
		if ($sns_type_cd == "fb") {
			$message_data = array("text" => $url);
			$res = $this->_send_facebook_msg($member, $message_data);
		}
		else if ($sns_type_cd == "ln") {
			$message_data = array(["type" => "text", "text" => $url]);
			$res = $this->_send_line_msg($recipient, $message_data);
		}
		else if ($sns_type_cd == "wc") {
			$message_data = array("msgtype" => "text", "text" => ["content" => $url]);
			$res = $this->_send_wechat_msg($recipient, $message_data);
		}
		else if ($sns_type_cd == "wb") {
			$res = ['result'=>'success'];
		}
		if ($res['result'] == 'success') {
			// $message = array("type" => "file", "file" => $url, "url" => $url);
			$message = array("type" => "file", "file" => $url, "url" => $url, "name" => $filename, "size" => $filesize);
			$log_message = json_encode($message, JSON_UNESCAPED_UNICODE);
			$this->_writelog($recipient, $log_message, $log_message);
			return true;
		}
		else {
			$this->log_error(__FUNCTION__, json_encode($res['message'], JSON_UNESCAPED_UNICODE));
			return false;
		}
	}
	
	public function send_message_cd($recipient, $msg_cd, $params=NULL)
	{	
		//Log::instance()->add(Log::DEBUG, "send_message_cd:" . $recipient . " msg_cd=" . $msg_cd);
		$msg = $this->get_bot_message_orm($this->_bot->bot_id, $msg_cd); if ($msg == null) return;
		
		$member = ORM::factory("botmember")->where("bot_id", "=", $this->_bot->bot_id)->where("member_id", "=", $recipient)->find();
		$sns_type_cd = $member->sns_type_cd;
		$lang_cd = $member->lang_cd;
		
		if ($sns_type_cd == "fb") {
			$message = $this->_create_facebook_msg($lang_cd, $msg, $params, $recipient);
			$res = $this->_send_facebook_msg($member, $message);
		}
		else if ($sns_type_cd == "ln") {
			$message = $this->_create_line_msg($lang_cd, $msg, $params, $recipient);
			$res = $this->_send_line_msg($recipient, [$message]);
		}
		else if ($sns_type_cd == "wc") {
			$message = $this->_create_wechat_msg($lang_cd, $msg, $params, $recipient);
			$res = $this->_send_wechat_msg($recipient, $message);
		}
		else if ($sns_type_cd == "wb") {
			$res = ['result'=>'success'];
		}
		
		if ($res['result'] == 'success') {
			$log_message = array();
			$log_message = $this->_create_msg_log($lang_cd, $msg, $params, $recipient);
			if (is_array($log_message)) {
				foreach($log_message as $msg) {
					$this->_writelog($recipient, $msg, $msg, $msg_cd);
				}
			}
			else {
				$this->_writelog($recipient, $log_message, $log_message, $msg_cd);
			}
			return true;
		}
		else {
			$this->log_error(__FUNCTION__, json_encode($res['message'], JSON_UNESCAPED_UNICODE));
			return false;
		}
	}

	public function send_message_cd_list($recipient, $msg_cd_list, $params=NULL)
	{
		$member = ORM::factory("botmember")->where("bot_id", "=", $this->_bot->bot_id)->where("member_id", "=", $recipient)->find();
		$sns_type_cd = $member->sns_type_cd;
		$lang_cd = $member->lang_cd;
		
		if ($sns_type_cd == "ln") {
			$messages = [];
			$log_messages = [];
			foreach($msg_cd_list as $msg_cd) {
				$msg = $this->get_bot_message_orm($this->_bot->bot_id, $msg_cd); 
				if ($msg == null) continue;
				$message = $this->_create_line_msg($lang_cd, $msg, $params, $recipient);
				$messages[] = $message;
				$log_message = $this->_create_msg_log($lang_cd, $msg, $params, $recipient);
				if (is_array($log_message)) {
					foreach($log_message as $m) {
						$log_messages[] = [$log_message, $msg_cd];
					}
				}
				else {
					$log_messages[] = [$log_message, $msg_cd];
				}
			}
			$this->_send_line_msg($recipient, $messages);
			foreach($log_messages as $log_message) {
				$this->_writelog($recipient, $log_message[0], $log_message[0], $log_message[1]);
			}
		}
		else {
			foreach($msg_cd_list as $msg_cd) {
				$msg = $this->get_bot_message_orm($this->_bot->bot_id, $msg_cd);
				if ($msg == null) continue;
				if ($sns_type_cd == "fb") {
					$message = $this->_create_facebook_msg($lang_cd, $msg, $params, $recipient);
					$res = $this->_send_facebook_msg($member, $message);
				}
				else if ($sns_type_cd == "ln") {
					$message = $this->_create_line_msg($lang_cd, $msg, $params, $recipient);
					$this->_send_line_msg($recipient, array($message));
				}
				else if ($sns_type_cd == "wc") {
					$message = $this->_create_wechat_msg($lang_cd, $msg, $params, $recipient);
					$this->_send_wechat_msg($recipient, $message);
				}
				else if ($sns_type_cd == "wb") {
				}
				$log_message = $this->_create_msg_log($lang_cd, $msg, $params, $recipient);
				if (is_array($log_message)) {
					foreach($log_message as $msg) {
						$this->_writelog($recipient, $msg, $msg, $msg_cd);
					}
				}
				else {
					$this->_writelog($recipient, $log_message, $log_message, $msg_cd);
				}
			}
		}
	}
	
	public function send_message_cd_by_webuser($recipient, $msg_cd, $params=NULL)
	{
		$msg = $this->get_bot_message_orm($this->_bot->bot_id, $msg_cd); if ($msg == null) return;
		
		$member = ORM::factory("botmember")->where("bot_id", "=", $this->_bot->bot_id)->where("member_id", "=", $recipient)->find();
		$sns_type_cd = $member->sns_type_cd;
		$lang_cd = $member->lang_cd;

		if ($sns_type_cd == "fb") {
			$message = $this->_create_facebook_msg($lang_cd, $msg, $params, $recipient);
			$res = $this->_send_facebook_msg($member, $message);
		}
		else if ($sns_type_cd == "ln") {
			$message = $this->_create_line_msg($lang_cd, $msg, $params, $recipient);
			$res = $this->_send_line_msg($recipient, array($message));
		}
		else if ($sns_type_cd == "wc") {
			$message = $this->_create_wechat_msg($lang_cd, $msg, $params, $recipient);
			$res = $this->_send_wechat_msg($recipient, $message);
		}
		else if ($sns_type_cd == "wb") {
			$res = ['result'=>'success'];
		}
		
		if ($res['result'] == 'success') {
			$log_message = array();
			$log_message = $this->_create_msg_log($lang_cd, $msg, $params, $recipient);
			if (is_array($log_message)) {
				foreach($log_message as $msg) {
					$this->_writelog($recipient, $msg, $msg, $msg_cd, '', TRUE, FALSE);
				}
			}
			else {
				$this->_writelog($recipient, $log_message, $log_message, $msg_cd, '', TRUE, FALSE);
			}
			return true;
		}
		else {
			$this->log_error(__FUNCTION__, json_encode($res['message'], JSON_UNESCAPED_UNICODE));
			return false;
		}
	}
	
	private function _send_line_msg($recipient, $message)
	{
		$base_url = $this->get_setting('line_push_url');
		$env = $this->get_bot_setting($this->_bot->bot_id, 'json_sns_link_line');
		$env = json_decode($env, true);
		$data = [
				'to' => $recipient,
				"messages" => $message
		];

		$header = [
				'Content-Type: application/json; charset=UTF-8',
				'Authorization: Bearer ' . $env['channel_access_token']
		];
		$response = $this->curl($base_url, $header, $data);
		return ['result'=>'success'];
	}

	private function _create_line_msg($lang_cd, $msg, $params, $member_id)
	{
		$message = [];

		if ($msg->msg_type_cd == "txt") {
			$msg_desc = $this->_get_message_desc($msg, $lang_cd, $params);
			$message = array("type" => "text", "text" => $msg_desc->content);
		}
		else if ($msg->msg_type_cd == "lst" || $msg->msg_type_cd == "btn" || $msg->msg_type_cd == "mnu") {
			$msg_desc_array = $this->_get_message_desc($msg, $lang_cd, $params);
			$items = [];
			$title = "";
			$btn_name = $this->get_bot_tpl_message($this->_bot->bot_id, 'btn_name', $lang_cd);
			$button_config = json_decode($btn_name, true);
			foreach($msg_desc_array as $msg_desc) {
				if ($title=="") $title = $msg_desc->title;
				$data = ["t_title"=>$msg_desc->content, "t_payload"=>$this->_replace_tpl($msg_desc->url, $params)];
				$item = [
						"type"=>'postback',
						"data"=>json_encode($data, JSON_UNESCAPED_UNICODE),
						"label"=>$this->get_button_name($msg_desc->content, $button_config),
						"displayText"=>$msg_desc->content,
					];
				$items[] = array("type"=>"action", "action"=>$item);
			}
			$message = [
					"type"=>"text",
					"text"=>$title,
					"quickReply"=>array("items"=>$items)
				];
		}
		else if ($msg->msg_type_cd == "img") {
			$msg_desc = $this->_get_message_desc($msg, $lang_cd, $params);
			if ($msg_desc->url == '') {
				$message = array("type" => "image", "originalContentUrl" => $msg_desc->msg_image, "previewImageUrl" => $msg_desc->msg_image);
			}
			else {
				//map image				
				$aws_model = Model::factory('aws');
				$base_url = '';
				$msg_data = json_decode($msg->msg_data, true);
				if (is_array($msg_data) && array_key_exists('image_option', $msg_data)) {
					$image_option = $msg_data['image_option'];
				}
				else {
					$image_option = 'normal';
				}
				
				if ($image_option == 'line_imagemap') {
					$msg_desc = $this->_get_message_desc($msg, $lang_cd, $params);
					$ex = $aws_model->exist($msg->bot_id . "/message/" . $msg->msg_cd . '/' . $lang_cd . '/1040');
					if ($ex) {
						$base_url = $this->get_env('cdn_url') . $msg->bot_id . "/message/" . $msg->msg_cd . '/' . $lang_cd;
					}
					else {
						$ex = $aws_model->exist($msg->bot_id . "/message/" . $msg->msg_cd . '/1040');
						if ($ex) {
							$base_url = $this->get_env('cdn_url') . $msg->bot_id . "/message/" . $msg->msg_cd;
						}
					}
					$skill = $this->trans_skill($msg_desc->url, $lang_cd, $this->_bot->bot_id, $member_id);
					if ($skill['type'] == 'uri') {
						$action = [
								"type"=>"uri",
								"linkUri"=>$skill['uri'],
								"area"=>["x"=>0,"y"=>0,"width"=>1040,"height"=>1040]
							];
					}
					else {
						$action = [
								"type"=>"postback",
								"data"=>$msg_desc->url,
								"area"=>["x"=>0,"y"=>0,"width"=>1040,"height"=>1040]
							];
					}
					$message = [
							"type"=>"imagemap",
							"baseUrl"=>$base_url,
							"altText"=>$msg_desc->title,
							"baseSize"=>["width"=>1040, "height"=>1040],
							"actions"=>[$action]
					];
				}
				else if ($image_option == 'line_carousel') {
					$msg_desc_array = $this->_get_message_desc_lst($msg, $lang_cd, $params);
					$contents = [];
					$alt_txt = 'Message';
					foreach($msg_desc_array as $msg_desc) {
						//最初のタイトルを待受画面の表示内容にする
						if($alt_txt == 'Message' && $msg_desc->title != '') {
							$alt_txt = $msg_desc->title;
						}
						$hero = [
								"type"=>"image",
								"url"=>$msg_desc->msg_image,
								"size"=>"full",
								"aspectRatio"=>"1:1",
								"aspectMode"=>"cover",
						];
						$skill = $this->trans_skill($msg_desc->url, $lang_cd, $this->_bot->bot_id, $member_id);
						if ($skill['type'] == 'uri') {
							$hero["action"] = [
								"type"=>"uri",
								"uri"=>$skill['uri']
							];
						}
						else {
							$hero["action"] = [
								"type"=>"postback",
								"data"=>$msg_desc->url
							];
						}
						$contents[] = ['type'=>'bubble', 'hero'=>$hero];
					}
					$message = ["type"=>"flex","altText"=>$alt_txt, "contents"=>["type"=>"carousel", "contents"=>$contents]];
				}
				else {
					$msg_desc = $this->_get_message_desc($msg, $lang_cd, $params);
					$message = array("type" => "image", "originalContentUrl" => $msg_desc->msg_image, "previewImageUrl" => $msg_desc->msg_image);
				}
				/*
				$ex = $aws_model->exist($msg->bot_id . "/message/" . $msg->msg_cd . '/' . $lang_cd . '/1040');
				if ($ex) {
					$base_url = $this->get_env('cdn_url') . $msg->bot_id . "/message/" . $msg->msg_cd . '/' . $lang_cd;					
				}
				else {
					$ex = $aws_model->exist($msg->bot_id . "/message/" . $msg->msg_cd . '/1040');
					if ($ex) {
						$base_url = $this->get_env('cdn_url') . $msg->bot_id . "/message/" . $msg->msg_cd;
					}
				}
				if ($base_url == '') {
					// carousel images
					//Log::instance()->add(Log::DEBUG, 'carousel image');
					$msg_desc_array = $this->_get_message_desc_lst($msg, $lang_cd, $params);
					$contents = [];
					foreach($msg_desc_array as $msg_desc) {
						$hero = [
								"type"=>"image",
								"url"=>$msg_desc->msg_image,
								"size"=>"full",
								"aspectRatio"=>"20:13",
								"aspectMode"=>"cover",
								"action"=>[
										"type"=>"uri",
										"uri"=>$msg_desc->url
								]
						];
						$contents[] = ['type'=>'bubble', 'hero'=>$hero];
					}
					$message = ["type"=>"flex","altText"=>"talkappi", "contents"=>["type"=>"carousel", "contents"=>$contents]];
				}
				else {
					$message = [
								"type"=>"imagemap",
								"baseUrl"=>$base_url,
								"altText"=>$msg_desc->title,
								"baseSize"=>["width"=>1040, "height"=>1040],
								"actions"=>[
									[
										"type"=>"uri",
										"linkUri"=>$msg_desc->url,
										"area"=>["x"=>0,"y"=>0,"width"=>1040,"height"=>1040]
									]
								]
							];
				}
				*/
			}
		}
		else if ($msg->msg_type_cd == "car") {
			$msg_desc_array = $this->_get_message_desc($msg, $lang_cd, $params);
			$msg_desc_array = $this->result2array($msg_desc_array);
			$btn_name = $this->get_bot_tpl_message($this->_bot->bot_id, 'btn_name', $lang_cd);
			$button_config = json_decode($btn_name, true);
			$cards = [];
			$altText = '';
			foreach($msg_desc_array as $msg_desc) {
				if ($altText == '') $altText = $msg_desc['title'];
				$actions = [];
				for($i=1; $i<=3; $i++) {
					if ($msg_desc['btn' . $i . '_name'] != null && $msg_desc['btn' . $i . '_name'] != "") {
						$skill = $this->trans_skill($msg_desc['btn' . $i . '_url'], $lang_cd, $this->_bot->bot_id, $member_id);
						$button_name = $this->get_button_name($msg_desc['btn' . $i . '_name'], $button_config);
						if ($skill['type'] == 'uri') {
							$actions[] = [
									"type" => "button", 
									"height"=> "sm",
									"action"=> [
											"type"=>"uri",
											"label"=>$button_name,
											"uri"=>$skill['uri']
										]
									];
						}
						else {
							$actions[] = [
									"type" => "button",
									"height"=> "sm",
									"action"=> [
											"type"=>"postback",
											"label"=>$button_name,
											"displayText"=>$button_name,
											"data"=>json_encode(["t_title"=>$button_name,"t_payload"=>$skill['uri']])
									]
							];
						}	
					}
				}

				$hero = ["type"=>"image", 
						"url"=>$msg_desc['msg_image'],
						"size"=>"full",
						"aspectRatio"=>"20:13",
						"aspectMode"=>"cover"
				];
				$body = [
						"type"=>"box",
						"layout"=>"vertical",
						"contents"=>[
								[
									"type"=>"box",
									"layout"=>"horizontal",
									"contents"=>[[
										"type"=>"text",
										"text"=>$msg_desc['title'],
										"wrap"=>true,
										"weight"=>"bold"
									]]
								],
								[
									"type"=>"box",
									"layout"=>"horizontal",
									"contents"=>[[
											"type"=>"text",
											"text"=>$msg_desc['content'],
											"wrap"=>true,
											"size"=>"xs",
											"color"=>"#aaaaaa"
									]]
								]
						]
				];
				$footer = [
						"type"=>"box",
						"layout"=>"vertical",
						"contents"=>$actions
				];
				$styles = [
						"header"=> [
						"backgroundColor"=>"#ff0000"
						]
				];
				$cards[] = ["type"=>"bubble", "hero"=>$hero, "body"=>$body, "footer"=>$footer, "styles"=>$styles];
			}
			
			if ($altText == '') $altText = 'Image messsage';
			$message = [
					"type" => "flex",
					"altText" => $altText,
					"contents" => ["type"=>"carousel", "contents"=>$cards],
			];
		}
		else if ($msg->msg_type_cd == "xxx") {
			$message = json_decode($msg->raw);
		}
		return $message;
	}
	
	function line_qrcode($scene_cd, $bot)
	{
		//$client_id = $env['client_id'];
		
		$base_url = $this->get_env('base_url'). "line?id=$scene_cd";
		$scene_path = $this->get_scene_path($scene_cd);
		$qr_file = $scene_path . "ln-" . $scene_cd . ".png";
		file_put_contents($qr_file, file_get_contents($this->_qr_service . $base_url));
		/*
		$ch = curl_init('http://example.com/image.php');
		$fp = fopen('/my/folder/flower.gif', 'wb');
		curl_setopt($ch, CURLOPT_FILE, $fp);
		curl_setopt($ch, CURLOPT_HEADER, 0);
		curl_exec($ch);
		curl_close($ch);
		fclose($fp);
		 */
	}

	function line_richmenu_alias($richmenu_id, $richmenu_alias)
	{
		$data = ["richMenuAliasId"=>$richmenu_alias, "richMenuId"=>$richmenu_id];
		$base_url = $this->get_setting('line_richmenu_url') . '/alias';
		$env = $this->get_bot_setting($this->_bot->bot_id, 'json_sns_link_line');
		$env = json_decode($env, true);
		$header = [
				'Content-Type: application/json; charset=UTF-8',
				'Authorization: Bearer ' . $env['channel_access_token']
		];
		
		$response = $this->curl_delete($base_url . '/' . $richmenu_alias, $header);
		$response = $this->curl($base_url, $header, $data);
		if (array_key_exists('message', $response)) {
			$this->log_error(__FUNCTION__, $response['message']);
			Log::instance()->add(Log::DEBUG, __CLASS__.">".__FUNCTION__." request $base_url error, response message: ".$response['message']);
			return false;
		}
		return true;
	}
	
	function line_richmenu($data, $image_url)
	{
		$ext = strtolower(substr($image_url, strrpos($image_url, '.') + 1));
		if ($ext != 'png' && $ext != 'jpeg' && $ext != 'jpg') {
			if ( strrpos($ext, '?') ) { // timestamp params handling
				// https://cdn.talkappi.com/232001/message/main_menu_line_2320010014_1_ja.jpg?1716542592?1716542617?1716542669?1716542835?1716542957?1716542965?1716542970?1716542977?1716542998?1716543003?1716543024?1716770165?1716770620?1716770990?1716770996?1716881506
				$ext = explode('?', $ext)[0];
				if ($ext != 'png' && $ext != 'jpeg' && $ext != 'jpg') {
					Log::instance()->add(Log::DEBUG, __CLASS__.">".__FUNCTION__." error: $ext file type is not supported");
					return "error: $ext file is not supported";
				}
			} else {
				Log::instance()->add(Log::DEBUG, __CLASS__.">".__FUNCTION__." error: $ext file type is not supported");
				return "error: $ext file is not supported";
			}
		}
		if ($ext == "png") {
			$base_image = imagecreatefrompng($image_url);
		}
		else {
			$base_image = imagecreatefromjpeg($image_url);
			$ext = "jpeg";
		}
		if ($base_image === false) {
			Log::instance()->add(Log::DEBUG, __CLASS__.">".__FUNCTION__." error: imagecreatefrom$ext($image_url) error");
			return "error: imagecreatefrom$ext error";
		}
		$image_width = imagesx($base_image);
		$image_height = imagesy($base_image);
		//TODO image width/height check
		$data["size"]=["width"=>$image_width, "height"=>$image_height];
		//$data["size"]=["width"=>2500, "height"=>1686];
		$base_url = $this->get_setting('line_richmenu_url');
		$base_url_data = $this->get_setting('line_richmenu_url_data');
		$env = $this->get_bot_setting($this->_bot->bot_id, 'json_sns_link_line');
		$env = json_decode($env, true);		
		$header = [
				'Content-Type: application/json; charset=UTF-8',
				'Authorization: Bearer ' . $env['channel_access_token']
		];
		
		$response = $this->curl($base_url, $header, $data);
		$menu_id = "";
		if (array_key_exists('richMenuId', $response)) {
			$menu_id = $response['richMenuId'];
			$base_url_data = $base_url_data . '/' . $menu_id . '/content';

			$header = [
					'Content-Type: image/' . $ext,
					'Authorization: Bearer ' . $env['channel_access_token']
			];
			$stream = file_get_contents($image_url);
			$response = $this->curl($base_url_data, $header, $stream, true);
			if (array_key_exists('message', $response)) {
				$this->log_error(__FUNCTION__, $response['message']);
				Log::instance()->add(Log::DEBUG, __CLASS__.">".__FUNCTION__." request $base_url_data error, response message: ".$response['message']);
				$menu_id = $response['message'];
			}
		}
		else {
			$this->log_error(__FUNCTION__, $response['message']);
			Log::instance()->add(Log::DEBUG, __CLASS__.">".__FUNCTION__." request $base_url error, response message: ".$response['message']);
			$menu_id =  $response['message'] . " ";
			foreach ($response['details'] as $index=>$response_detail) {
				$this->log_error(__FUNCTION__, $response_detail['message']);
				Log::instance()->add(Log::DEBUG, __CLASS__.">".__FUNCTION__." request error detail $index: ".$response_detail['property']."=>".$response_detail['message']);
				$menu_id =  $menu_id . $response_detail['property'] . " " . $response_detail['message'];
			}
		}
		return $menu_id;
	}
	
	function line_richmenu_delete($menu_id)
	{
		$url = $this->get_setting('line_richmenu_url') . '/' . $menu_id;
		$env = $this->get_bot_setting($this->_bot->bot_id, 'json_sns_link_line');
		$env = json_decode($env, true);
		$header = [
				'Content-Type: application/json; charset=UTF-8',
				'Authorization: Bearer ' . $env['channel_access_token']
		];
		$curl = curl_init();
		curl_setopt($curl, CURLOPT_URL, $url);
		curl_setopt($curl, CURLOPT_CUSTOMREQUEST, 'DELETE');
		curl_setopt($curl, CURLOPT_HTTPHEADER, $header);
		curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
		curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($curl, CURLOPT_HEADER, true);
		$response = curl_exec($curl);
		$header_size = curl_getinfo($curl, CURLINFO_HEADER_SIZE);
		$body = substr($response, $header_size);
		curl_close($curl);
		Log::instance()->add(Log::DEBUG, "CURL RESULT:" . $body);
		return json_decode($body, true);
	}
	
	private function _create_facebook_msg($lang_cd, $msg, $params, $member_id)
	{
		$message = [];

		if ($msg->msg_type_cd == "txt" || $msg->msg_type_cd == "tpl" || $msg->msg_type_cd == "mal") {
			$msg_desc = $this->_get_message_desc($msg, $lang_cd, $params);
			$message = array("text" => $msg_desc->content);
		}
		else if ($msg->msg_type_cd == "mnu" || $msg->msg_type_cd == "lst" || $msg->msg_type_cd == "btn") {
			$msg_desc_array = $this->_get_message_desc($msg, $lang_cd, $params);
			$items = [];
			$title = "";
			$btn_name = $this->get_bot_tpl_message($this->_bot->bot_id, 'btn_name', $lang_cd);
			$button_config = json_decode($btn_name, true);
			foreach($msg_desc_array as $msg_desc) {
				if ($title=="") $title = $msg_desc->title;
				$item = array(
						"content_type"=>'text',
						"title"=>$this->get_button_name($msg_desc->content, $button_config),
						"payload"=>$this->_replace_tpl($msg_desc->url, $params),
				);
				$items[] = $item;
			}
			$message = array(
					"text"=>$title,
					"quick_replies"=>$items);
		}
		else if ($msg->msg_type_cd == "img") {
			$msg_desc = $this->_get_message_desc($msg, $lang_cd, $params);
			return array("attachment" => ["type" => "image", "payload" => ["url" => $msg_desc->msg_image, "is_reusable" => true]]);
		}
		else if ($msg->msg_type_cd == "car") {
			$msg_desc_array = $this->_get_message_desc($msg, $lang_cd, $params);
			$msg_desc_array = $this->result2array($msg_desc_array);
			$template_array = [];
			$btn_name = $this->get_bot_tpl_message($this->_bot->bot_id, 'btn_name', $lang_cd);
			$button_config = json_decode($btn_name, true);
			foreach($msg_desc_array as $msg_desc) {
				$actions = [];
				for($i=1; $i<=3; $i++) {
					if ($msg_desc['btn' . $i . '_name'] != null && $msg_desc['btn' . $i . '_name'] != "") {
						$skill = $this->trans_skill($msg_desc['btn' . $i . '_url'], $lang_cd, $this->_bot->bot_id, $member_id);
						if ($skill['type'] == 'uri') {
							$actions[] = array("type" => "web_url", "title" => $this->get_button_name($msg_desc['btn' . $i . '_name'], $button_config), "url"=>$skill['uri']);
						}
						else {
							$actions[] = array("type" => "postback", "title" => $this->get_button_name($msg_desc['btn' . $i . '_name'], $button_config), "payload"=>$skill['uri']);
						}
					}
				}
				$default = array("type" => "web_url", "url"=>$msg_desc['url'],
						"messenger_extensions"=>true, "webview_height_ratio"=>"tall", "fallback_url"=>$msg_desc['url']);
				$element = array(
					"title"=>$msg_desc['title'],
					"subtitle"=>$msg_desc['content'],
					"image_url"=>$msg_desc['msg_image'],
					//"default_action"=>$default,
					"buttons"=>$actions,
				);
				$template_array[] = $element;
			}
			$template = array(
				"template_type"=>"generic",
				"elements"=>$template_array,
			);
			$message = array("attachment" =>
				["type" => "template",
				"payload" => $template]
			);
		}
		return $message;
	}

	private function _send_facebook_msg($member, $message)
	{
		$response = $this->_send_facebook_msg_response($member, $message);
		if (array_key_exists('error', $response) && $response['error']['code']==10) {
			$response = $this->_send_facebook_msg_tag($member, $message);
		}
		if (array_key_exists('error', $response)) {
			$this->log_error(__FUNCTION__, json_encode($response, JSON_UNESCAPED_UNICODE));
			return ['result'=>'error', 'message'=>$response];
		}
		else {
			return ['result'=>'success'];
		}
	}
	
	private function _send_facebook_msg_response($member, $message)
	{
		$base_url = $this->get_setting('facebook_push_url');
		$env = $this->get_sns_setting_by_sns_id($this->_bot->bot_id, 'json_sns_link_facebook', $member->sns_id);
		$base_url = $base_url . $env['page_access_token'];
		$data = [
				'messaging_type' => 'RESPONSE',
				'recipient' => ['id'=>$member->member_id],
				'message'=>$message
		];
		$header = [
				'Content-Type: application/json',
		];
		$response = $this->curl($base_url, $header, $data);
		return $response;
	}
	
	private function _send_facebook_msg_tag($member, $message)
	{
		$base_url = $this->get_setting('facebook_push_url');
		$env = $this->get_sns_setting_by_sns_id($this->_bot->bot_id, 'json_sns_link_facebook', $member->sns_id);
		$base_url = $base_url . $env['page_access_token'];
		$data = [
				'messaging_type' => 'MESSAGE_TAG',
				'tag' => "CONFIRMED_EVENT_UPDATE",
				'recipient' => ['id'=>$member->member_id],
				'message' => $message
		];
		$header = [
				'Content-Type: application/json',
		];
		$response = $this->curl($base_url, $header, $data);
		return $response;
	}
	
	function facebook_qrcode($scene_cd, $bot)
	{
		$env = $this->get_bot_setting($this->_bot->bot_id, 'json_sns_link_line');
		$env = json_decode($env, true);
		$base_url = "http://m.me/" . $env['ref'] . "?ref=" . $scene_cd;
		$scene_path = $this->get_scene_path($scene_cd);
		$qr_file = $scene_path . "fb-" . $scene_cd . ".png";
		file_put_contents($qr_file, file_get_contents($this->_qr_service . $base_url));
	}
	
	function facebook_qrcode_v1($scene_cd, $bot)
	{
		$base_url = $this->get_setting('facebook_qrcode_url');
		$env = $this->get_bot_setting($this->_bot->bot_id, 'json_sns_link_line');
		$env = json_decode($env, true);
		$base_url = $base_url . $env['page_access_token'];
		$data = [
				'type' => "standard",
				"data" => ['ref'=>$scene_cd],
				"image_size"=>1000
		];
		
		$header = [
				'Content-Type: application/json',
		];
		$response = $this->curl($base_url, $header, $data);
		$scene_path = $this->get_scene_path($scene_cd);
		$qr_file = $scene_path . "fb-" . $scene_cd . ".png";
		file_put_contents($qr_file, file_get_contents($response['uri']));
	}
	
	// 投稿
	function facebook_post($message, $image_url, $token)
	{
		$base_url = 'https://graph.facebook.com/v3.2/me/photos?access_token=';
		$data = [
				'caption' => $message,
				"url" => $image_url
		];
		
		$header = [
				'Content-Type: application/json',
		];
		
		$base_url = $base_url . $token;
		$response = $this->curl($base_url, $header, $data);
	}

	private function _send_wechat_msg($recipient, $message)
	{
		$access_token = $this->_wechat_get_token($this->_bot);
		//todo 下記は変数下記間違いのため、今後テストの上、コメントアウトを外す予定　ー＞　戚さん
		// $message_send = mb_convert_encoding($message_send, "UTF-8", "auto");
		
		// 絵文字
		//$str = preg_replace("#(\\\ue[0-9a-f]{3})#ie","addslashes('\\1')",$str);

		$base_url = $this->get_setting('wechat_push_url') . $access_token['access_token'];
		$message["touser"] = $recipient;
		$header = [
				'Content-Type: application/json;charset=UTF-8',
		];
		$response = $this->curl($base_url, $header, $message);
		return ['result'=>'success'];
	}

	private function _create_wechat_msg($lang_cd, $msg, $params, $recipient)
	{
		$message = [];

		if ($msg->msg_type_cd == "txt") {
			$msg_desc = $this->_get_message_desc($msg, $lang_cd, $params);
			$message = array("msgtype" => "text", "text" => ["content" => $msg_desc->content]);
		}
		else if ($msg->msg_type_cd == "mnu" || $msg->msg_type_cd == "lst" || $msg->msg_type_cd == "btn") {
			$msg_desc_array = $this->_get_message_desc($msg, $lang_cd, $params);
			$title = "";
			$content = "";
			$btn_name = $this->get_bot_tpl_message($this->_bot->bot_id, 'btn_name', $lang_cd);
			$button_config = json_decode($btn_name, true);
			$base_url = $this->get_env('engine_url');
			$env = $this->get_bot_setting($this->_bot->bot_id, 'json_sns_link_wechat');
			$env = json_decode($env, true);
			foreach($msg_desc_array as $msg_desc) {
				if ($title=="") $title = $msg_desc->title;
				$url = $this->_replace_tpl($msg_desc->url, $params);
				$content = $content . '<a href="' . $base_url . 'wechat?mid=' . $recipient . '&wxappid=' . $env['sns_id'] . '&cmd=' . $url . '">' . $this->get_button_name($msg_desc->content, $button_config) . '</a>' . PHP_EOL;
			}
			$content = $title . PHP_EOL . $content;
			$message = array("msgtype" => "text", "text" => ["content" => $content]);
		}
		else if ($msg->msg_type_cd == "img") {
			$msg_desc = $this->_get_message_desc($msg, $lang_cd, $params);
			$article = ["title"=>$msg_desc->title, "description"=>$msg_desc->content, "url"=>$msg_desc->url, "picurl"=>$msg_desc->msg_image];
			$message = array("msgtype" => "image", "image" =>  ["media_id" => "O-j45x4tnKdjNo9hOZPsmzYJJaDwqD8HKItdssE_7vbkZEaYDL6lTpjxKOnGhyfw"]);
		}
		else if ($msg->msg_type_cd == "car") {
			$msg_desc_array = $this->_get_message_desc($msg, $lang_cd, $params);
			$template_array = array();
// 			foreach($msg_desc_array as $msg_desc) {
// 				$article = ["title"=>$msg_desc->title, "description"=>$msg_desc->content, "url"=>$msg_desc->url, "picurl"=>$msg_desc->msg_image];
// 				$template_array[] = $article;
// 			}
			$article = ["title", "description", "https://activalues.com", "https://s3-ap-northeast-1.amazonaws.com/contents.talkappi.com/17/50050.jpeg"];
			$template_array[] = $article;
			$article = ["title", "description", "https://activalues.com", "https://s3-ap-northeast-1.amazonaws.com/contents.talkappi.com/17/50050.jpeg"];
			$template_array[] = $article;
		}
		else if ($msg->msg_type_cd == "04") {
		}
		return $message;
	}
	
	public function wechat_qrcode($scene_cd, $bot, $country='', $timezone='', $skill='')
	{	
		$access_token = $this->_wechat_get_token($bot);
		$base_url = $this->get_setting('wechat_qrcode_url') . $access_token['access_token'];
		$header = [
				'Content-Type: application/json;charset=UTF-8',
		];
		$data = [
			"action_name"=>"QR_LIMIT_STR_SCENE",
			"action_info"=>[
			"scene"=> [
				"scene_str"=>$scene_cd . ',' . $country . ',' . $timezone . '|' . $skill,
				],
			],
		];
		$filename = '';
		$scene_path = $this->get_scene_path($scene_cd);
		if ($skill == '') {
			$response = $this->curl($base_url, $header, $data);
			if ($country == '') {
				$qr_file = $scene_path . "wc-" . $scene_cd . ".png";
			}
			else {
				$timezone = str_replace('/', '_', $timezone);
				$qr_file = $scene_path . "wc-$scene_cd-$country-$timezone.png";
			}
		}
		else {
			$data["action_name"] = "QR_STR_SCENE";
			$response = $this->curl($base_url, $header, $data);
			$filename = time();
			$qr_file = $scene_path . "$filename.png";
		}
		$img_url = 'https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=' . $response['ticket'];
		file_put_contents($qr_file, file_get_contents($img_url));
		return $filename;
	}

	function webchat_qrcode($scene_cd, $bot)
	{
		$scene_path = $this->get_scene_path($scene_cd);
		$base_url = $this->get_env('base_url') . "bot?id=" . $scene_cd;
		$qr_file = $scene_path . "wb-t-" . $scene_cd . ".png";
		file_put_contents($qr_file, file_get_contents($this->_qr_service . $base_url));
		$base_url = $this->get_env('base_url') . "bot/webchat?id=" . $scene_cd;
		$qr_file = $scene_path . "wb-" . $scene_cd . ".png";
		file_put_contents($qr_file, file_get_contents($this->_qr_service . $base_url));
		/*
		 $ch = curl_init('http://example.com/image.php');
		 $fp = fopen('/my/folder/flower.gif', 'wb');
		 curl_setopt($ch, CURLOPT_FILE, $fp);
		 curl_setopt($ch, CURLOPT_HEADER, 0);
		 curl_exec($ch);
		 curl_close($ch);
		 fclose($fp);
		 */
	}

	function very_qrcode($scene_cd, $bot)
	{
		$scene_path = $this->get_scene_path($scene_cd);
		$base_url = $this->get_env('very_url') . "?bot_id=" . $bot->bot_id;
		$qr_file = $scene_path . "vt-" . $scene_cd . ".png";
		file_put_contents($qr_file, file_get_contents($this->_qr_service . $base_url));
	}
	
	private function _wechat_get_token($bot) 
	{
		$token_url = $this->get_setting('wechat_token_url');
		$env = $this->get_bot_setting($bot->bot_id, 'json_sns_link_wechat');
		$env = json_decode($env, true);
		$token_url = $token_url . 'appid=' . $env['sns_id'] . '&secret=' . $env['app_secret'];
		$curl = curl_init();
		curl_setopt($curl, CURLOPT_URL, $token_url);
		curl_setopt($curl, CURLOPT_CUSTOMREQUEST, 'GET');
		curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
		curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
		curl_setopt($curl, CURLOPT_HEADER, true);
		curl_setopt($curl, CURLOPT_VERBOSE, 1);
		$response = curl_exec($curl);
		$header_size = curl_getinfo($curl, CURLINFO_HEADER_SIZE);
		$body = substr($response, $header_size);
		$access_token = json_decode($body, true);
		curl_close($curl);
		return $access_token;
	}
	public function wechat_add_media($file_info, $bot)
	{
		$access_token = $this->_wechat_get_token($bot);
		if (!isset($access_token['access_token'])) {
			Log::instance()->add(Log::DEBUG, "get access token error:" . $access_token['errmsg']);
		}
		$base_url = $this->get_setting('wechat_api_url') . 'material/add_material?access_token=' . $access_token['access_token'] . '&type=image';
		$header = [
				'Content-Type: application/json;charset=UTF-8',
		];
		//$form_info = json_encode(["filename"=>$file_info["name"], "content_type"=>$file_info["type"], "filelength"=>$file_info["size"]]);
		//$data = ["media" => "@". $file_info['tmp_name'], "form-data"=>$form_info];
		//$filename = APPPATH . "../../files/" . $file_info["name"];
		//copy($file_info['tmp_name'], $filename);
		$arr = explode('.', $file_info);
		$ext = strtolower(end($arr));
		$filename = time() . '.' . $ext;
		$temp_file = APPPATH."../../files/temp/" . $filename; 
		$content = file_get_contents($file_info);
		file_put_contents($temp_file, $content);
		$data = ["media" => new CURLFile($temp_file, 'image', $filename)];
		$curl = curl_init();
		curl_setopt($curl, CURLOPT_URL, $base_url);
		curl_setopt($curl, CURLOPT_HEADER, FALSE);
		curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, FALSE);
		curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, FALSE);
		if (!empty($data)){
			curl_setopt($curl, CURLOPT_POST, 1);
			curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
		}
		curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
		$output = curl_exec($curl);
		curl_close($curl);

		// Delete the temporary file after use
		if (file_exists($temp_file)) {
			unlink($temp_file);
		}

		$result = json_decode($output, true);
		Log::instance()->add(Log::DEBUG, "wechat_add_media:" . json_encode($result, JSON_UNESCAPED_UNICODE));
		return $result;
	}
	
	public function wechat_get_media($media_id, $bot)
	{
		$access_token = $this->_wechat_get_token($bot);
		$base_url = $this->get_setting('wechat_api_url') . 'material/get_material?access_token=' . $access_token['access_token'];
		$header = [
				'Content-Type: application/json;charset=UTF-8',
		];
		$data = ["media_id" => $media_id];
		$response = $this->curl($base_url, $header, $data);
		//return $response['media_id'];
		return '';
	}
	
	public function wechat_get_media_list($bot, $offset, $count)
	{
		$access_token = $this->_wechat_get_token($bot);
		$base_url = $this->get_setting('wechat_api_url') . 'material/batchget_material?access_token=' . $access_token['access_token'];
		$header = [
				'Content-Type: application/json;charset=UTF-8',
		];
		$data = ["type" => "image", "offset"=>$offset, "count"=>$count];
		$response = $this->curl($base_url, $header, $data);
		return $response;
	}
	
	public function wechat_exec($bot, $url, $data)
	{
		$access_token = $this->_wechat_get_token($bot);
		$base_url = $this->get_setting('wechat_api_url') . $url . '?access_token=' . $access_token['access_token'];
		$header = [
				'Content-Type: application/json;charset=UTF-8',
		];
		$response = $this->curl($base_url, $header, $data, true);
		return $response;
	}
	
	private function _translate($message, $lang_cd)
	{
		$google_lang_cd = $lang_cd;
		$org = 'Original:';
		if ($lang_cd == 'cn') { $google_lang_cd = "zh-CN"; $org = '原文：'; }
		if ($lang_cd == 'tw') { $google_lang_cd = "zh-TW"; $org = '原文：'; }
		if ($lang_cd == 'kr') { $google_lang_cd = "ko";  $org = 'Original：'; }
		$google = Model::factory('google');
		$message_t = $google->translate($message, $google_lang_cd);
		$message_send = $message_t . "\n※" . $org . $message;
		return array($message, $message_t, $message_send);
	}

	private function _create_msg_log($lang_cd, $msg, $params, $member_id)
	{
		$message = "";
		if ($msg->msg_type_cd == "txt" || $msg->msg_type_cd == "tpl" || $msg->msg_type_cd == "mal") {
			$msg_desc_array = $this->_get_message_desc_lst($msg, $lang_cd, $params);
			$messages = array();
			foreach($msg_desc_array as $msg) {
				$messages[] = $msg->content;
			}
			return $messages;
		}
		else if ($msg->msg_type_cd == "img") {
			$msg_desc = $this->_get_message_desc($msg, $lang_cd, $params);
			$message = json_encode(array("type" => "image", "image" => $msg_desc->msg_image, "url" => $msg_desc->url, "title"=>$msg_desc->title), JSON_UNESCAPED_UNICODE);
		}
		else if ($msg->msg_type_cd == "car") {
			$msg_desc_array = $this->_get_message_desc($msg, $lang_cd, $params);
			$msg_desc_array = $this->result2array($msg_desc_array);
			$template_array = [];
			$btn_name = $this->get_bot_tpl_message($this->_bot->bot_id, 'btn_name', $lang_cd);
			$button_config = json_decode($btn_name, true);
			foreach($msg_desc_array as $msg_desc) {
				$actions = [];
				for($i=1; $i<=3; $i++) {
					if ($msg_desc['btn' . $i . '_name'] != null && $msg_desc['btn' . $i . '_name'] != "") {
						$skill = $this->trans_skill($msg_desc['btn' . $i . '_url'], $lang_cd, $this->_bot->bot_id, $member_id);
						if ($skill['type'] == 'uri') {
							$actions[] = array("type" => "url", "title" => $this->get_button_name($msg_desc['btn' . $i . '_name'], $button_config), "url"=>$skill['uri']);
						}
						else {
							$actions[] = array("type" => "postback", "title" => $this->get_button_name($msg_desc['btn' . $i . '_name'], $button_config), "url"=>$skill['uri']);
						}
					}
				}
				$log_item = array(
					"title"=>$msg_desc['title'],
					"content"=>$msg_desc['content'],
					"image"=>$msg_desc['msg_image'],
					"url"=>$msg_desc['url'],
					"buttons"=>$actions,
				);
				$template_array[] = $log_item;
			}
			$message = json_encode(array(
				"type"=>"card",
				"title"=>"",
				"items"=>$template_array,
				), JSON_UNESCAPED_UNICODE);
		}
		else if ($msg->msg_type_cd == "lst" || $msg->msg_type_cd == "btn" || $msg->msg_type_cd == "mnu") {
			$msg_desc_array = $this->_get_message_desc($msg, $lang_cd, $params);
			$template_array = [];
			$title = "";
			$btn_name = $this->get_bot_tpl_message($this->_bot->bot_id, 'btn_name', $lang_cd);
			$button_config = json_decode($btn_name, true);
			foreach($msg_desc_array as $msg_desc) {
				if ($title=="") $title = $msg_desc->title;
				$log_item = array(
						"title"=>$this->get_button_name($msg_desc->content, $button_config),
						"content"=>$this->_replace_tpl($msg_desc->url, $params),
				);
				$template_array[] = $log_item;
			}
			if ($msg->msg_type_cd == "lst") {
				$type = "list";
			}
			else {
				$type = 'button';
			}
			$message = json_encode(array(
					"type"=>$type,
					"title"=>$title,
					"items"=>$template_array,
			), JSON_UNESCAPED_UNICODE);
		}
		else if ($msg->msg_type_cd == "xxx") {
			// image表示のみ
			//$message = json_decode($msg->raw);
		}
		return $message;
	}
	
	private function _replace_tpl($str, $params) 
	{
		if ($params == null) return $str;
		$temp = $str;
		foreach ($params as $k=>$v) {
			$temp = str_replace("{" . $k . "}", $v, $temp);
		}
		return $temp;
	}
	
	private function _get_message_desc($msg, $lang_cd, $params)
	{
		if ($msg->msg_type_cd == "txt") {
			$msg_desc = ORM::factory("botmsgdesctxt")->where("msg_id", "=", $msg->msg_id)->where("lang_cd", "=", $lang_cd)->where("no","=",1)->find();
			/*
			foreach ($params as $k=>$v) {
				$msg_desc->content = str_replace("{" . $k . "}", $v, $msg_desc->content);
			}
			*/
			return $msg_desc;
		}
		else if ($msg->msg_type_cd == "tpl" || $msg->msg_type_cd == "mal") {
			$msg_desc = ORM::factory("botmsgdesctpl")->where("msg_id", "=", $msg->msg_id)->where("lang_cd", "=", $lang_cd)->where("no","=",1)->find();
			/*
			foreach ($params as $k=>$v) {
				$msg_desc->content = str_replace("{" . $k . "}", $v, $msg_desc->content);
			}
			*/
			return $msg_desc;
		}
		else if ($msg->msg_type_cd == "mnu" || $msg->msg_type_cd == "lst" || $msg->msg_type_cd == "btn") {
			$msg_desc_array = ORM::factory("botmsgdesclst")->where("msg_id", "=", $msg->msg_id)->where("lang_cd", "=", $lang_cd)->order_by("no")->find_all();
			/*
			foreach($msg_desc_array as $msg_desc) {
				foreach ($params as $k=>$v) {
					$msg_desc->title = str_replace("{" . $k . "}", $v, $msg_desc->title);
					$msg_desc->content = str_replace("{" . $k . "}", $v, $msg_desc->content);
					$msg_desc->url = str_replace("{" . $k . "}", $v, $msg_desc->url);
				}
			}
			*/
			return $msg_desc_array;
		}
		else if ($msg->msg_type_cd == "img") {
			$msg_desc = ORM::factory("botmsgdescimg")->where("msg_id", "=", $msg->msg_id)->where("lang_cd", "=", $lang_cd)->where("no","=",1)->find();
			return $msg_desc;
		}
		else if ($msg->msg_type_cd == "car") {
			$msg_desc_array = ORM::factory("botmsgdesccar")->where("msg_id", "=", $msg->msg_id)->where("lang_cd", "=", $lang_cd)->order_by("no")->find_all();
			/*
			$template_array = array();
			foreach($msg_desc_array as $msg_desc) {
				foreach ($params as $k=>$v) {
					$msg_desc->title = str_replace("{" . $k . "}", $v, $msg_desc->title);
					$msg_desc->content = str_replace("{" . $k . "}", $v, $msg_desc->content);
					$msg_desc->btn1_url= str_replace("{" . $k . "}", $v, $msg_desc->btn1_url);
					$msg_desc->btn2_url= str_replace("{" . $k . "}", $v, $msg_desc->btn2_url);
					$msg_desc->btn3_url= str_replace("{" . $k . "}", $v, $msg_desc->btn3_url);
				}
			}
			*/
			return $msg_desc_array;
		}
		return null;
	}
	
	private function _get_message_desc_lst($msg, $lang_cd, $params)
	{
		if ($msg->msg_type_cd == "txt") {
			$msg_desc_array = ORM::factory("botmsgdesctxt")->where("msg_id", "=", $msg->msg_id)->where("lang_cd", "=", $lang_cd)->order_by("no")->find_all();
			/*
			 foreach ($params as $k=>$v) {
			 $msg_desc->content = str_replace("{" . $k . "}", $v, $msg_desc->content);
			 }
			 */
			return $msg_desc_array;
		}
		else if ($msg->msg_type_cd == "tpl" || $msg->msg_type_cd == "mal") {
			$msg_desc_array = ORM::factory("botmsgdesctpl")->where("msg_id", "=", $msg->msg_id)->where("lang_cd", "=", $lang_cd)->order_by("no")->find_all();
			/*
			 foreach ($params as $k=>$v) {
			 $msg_desc->content = str_replace("{" . $k . "}", $v, $msg_desc->content);
			 }
			 */
			return $msg_desc_array;
		}
		else if ($msg->msg_type_cd == "mnu" || $msg->msg_type_cd == "lst" || $msg->msg_type_cd == "btn") {
			$msg_desc_array = ORM::factory("botmsgdesclst")->where("msg_id", "=", $msg->msg_id)->where("lang_cd", "=", $lang_cd)->order_by("no")->find_all();
			/*
			 foreach($msg_desc_array as $msg_desc) {
			 foreach ($params as $k=>$v) {
			 $msg_desc->title = str_replace("{" . $k . "}", $v, $msg_desc->title);
			 $msg_desc->content = str_replace("{" . $k . "}", $v, $msg_desc->content);
			 $msg_desc->url = str_replace("{" . $k . "}", $v, $msg_desc->url);
			 }
			 }
			 */
			return $msg_desc_array;
		}
		else if ($msg->msg_type_cd == "img") {
			$msg_desc = ORM::factory("botmsgdescimg")->where("msg_id", "=", $msg->msg_id)->where("lang_cd", "=", $lang_cd)->order_by("no")->find_all();
			return $msg_desc;
		}
		else if ($msg->msg_type_cd == "car") {
			$msg_desc_array = ORM::factory("botmsgdesccar")->where("msg_id", "=", $msg->msg_id)->where("lang_cd", "=", $lang_cd)->order_by("no")->find_all();
			/*
			 $template_array = array();
			 foreach($msg_desc_array as $msg_desc) {
			 foreach ($params as $k=>$v) {
			 $msg_desc->title = str_replace("{" . $k . "}", $v, $msg_desc->title);
			 $msg_desc->content = str_replace("{" . $k . "}", $v, $msg_desc->content);
			 $msg_desc->btn1_url= str_replace("{" . $k . "}", $v, $msg_desc->btn1_url);
			 $msg_desc->btn2_url= str_replace("{" . $k . "}", $v, $msg_desc->btn2_url);
			 $msg_desc->btn3_url= str_replace("{" . $k . "}", $v, $msg_desc->btn3_url);
			 }
			 }
			 */
			return $msg_desc_array;
		}
		return null;
	}
	
	function writelog($member_id, $msg, $msg_org, $intent_cd, $from_user=FALSE, $be_member_msg = FALSE)
	{
		$this->_writelog($member_id, $msg, $msg_org, '', $intent_cd, $from_user, $be_member_msg);
	}

	private function _writelog($member_id, $msg, $msg_org, $msg_cd='', $intent_cd='', $from_user=FALSE, $be_member_msg = FALSE)
	{
		/*
		if ($this->_log_ext == '') {
			$member = ORM::factory('botmember')->where('bot_id', '=', $this->_bot->bot_id)->where('member_id', '=', $member_id)->find();
			$member_name = $this->get_member_name($member);
			$chat_mode = $member->chat_mode;
			$is_tester = $member->is_tester;
			$sns_type_cd = $member->sns_type_cd;
			$lang_cd = $member->lang_cd;
			$member_tags = $member->tags;
			
			$user_id = NULL;
			if ($from_user == FALSE) {
				//$user = Session::instance()->get('user', NULL);
				//if ($user != NULL) $user_id = $user->user_id;
				$user_id = $member->chat_user_id;
			}
			//if ($chat_mode == 0 && $msg_cd == 'change_chatmode.0') $user_id = NULL;
			
			$orm = ORM::factory('botlog');
			$orm->bot_id = $this->_bot->bot_id;
			$orm->sns_type_cd = $sns_type_cd;
			$orm->lang_cd = $lang_cd;
			$orm->member_id = $member_id;
			if ($intent_cd == '') {
				if ($msg_cd == '') {
					$orm->intent_cd = 'input.chat';
				}
				else {
					$orm->intent_cd = 'input.chat.' . $msg_cd;
				}
			}
			else {
				$orm->intent_cd = $intent_cd;
			}
			
			//$orm->log_time = date("Y-m-d H:i:s") . "." . substr(explode(".", microtime(true))[1], 0, 3);
			$orm->log_time = $this->get_mstime();
			$orm->score = 0;
			$orm->member_msg = '';
			$orm->bot_msg = $msg_org;
			if ($msg_org != $msg) $orm->bot_msg_t = $msg;
			$orm->is_tester = $is_tester;
			$orm->user_id = $user_id;
			$orm->save();
			
			$logchat = ORM::factory('botlogchat');
			$logchat->log_id = $orm->log_id;
			$logchat->bot_id = $orm->bot_id;
			$logchat->sns_type_cd = $sns_type_cd;
			$logchat->lang_cd = $lang_cd;
			$logchat->member_id = $member_id;
			$logchat->intent_cd = $orm->intent_cd;
			$logchat->log_time = $orm->log_time;
			$logchat->score = 0;
			$logchat->member_msg = '';
			$logchat->bot_msg = $msg_org;
			if ($msg_org != $msg) $logchat->bot_msg_t = $msg;
			$logchat->is_tester = $is_tester;
			$logchat->user_id = $user_id;
			// only in t_log_chat
			$logchat->member_name = $member_name;
			$logchat->country_cd = $member->country_cd;
			$logchat->member_tag = $member_tags;
			$logchat->chat_mode = $chat_mode;
			$logchat->save();
		}
		else {
		*/
			$follow = ORM::factory('botfollow')->where('bot_id', '=', $this->_bot->bot_id)->where('member_id', '=', $member_id)->order_by('follow_time', 'DESC')->find();
			$scene_cd = '';
			if (isset($follow->member_id)) $scene_cd = $follow->scene;
			$member = ORM::factory('botmember')->where('bot_id', '=', $this->_bot->bot_id)->where('member_id', '=', $member_id)->find();
			
			if (!isset($member->member_id)) return;
			
			/*
			// add 2020/12/23 parent+son 
			if (!isset($member->member_id)) {
				$member = NULL;
				$grp_bot_id = $this->get_grp_bot_id($this->_bot->bot_id);
				if ($grp_bot_id == 0) {
					$members = ORM::factory('botmember')->where('member_id', '=', $member_id)
					->where('bot_id', '>=', $this->_bot->bot_id)->where('bot_id', '<=', $this->_bot->bot_id+999)->order_by('last_talk_date', 'DESC')->find_all();
					if (count($members) > 0) $member = $members[0];
				}
			}
			if ($member == NULL) return;
			// end 2020/12/23
			*/
			
			$member_name = $this->get_member_name($member);
			$chat_mode = $member->chat_mode;
			$request_flg = $member->request_flg;
			$is_tester = $member->is_tester;
			$sns_type_cd = $member->sns_type_cd;
			$lang_cd = $member->lang_cd;
			$member_tags = $member->tags;
			$user_id = NULL;
			
			if ($intent_cd == 'member_click_link') {
				$score = 1;
				$answer_type = 1;
			}
			else {
				$score = 0;
				$answer_type = 9;
			}
			if ($from_user == FALSE) {
				$user_id = $member->chat_user_id;
			}
			if ($intent_cd == '') {
				if ($msg_cd == '') {
					$intent_cd = 'input.chat';
				}
				else {
					$intent_cd = 'input.chat.' . $msg_cd;
				}
			}
			if ($from_user == TRUE && $intent_cd == '') {
				$intent_cd = "push.message";
			}
			$bot_msg_t = '';
			if ($msg_org != $msg) $bot_msg_t = $msg;
			
			$logchat = ORM::factory('botlogchat');
			$logchat->bot_id = $this->_bot->bot_id;
			$logchat->scene_cd = $scene_cd;
			$logchat->sns_type_cd = $sns_type_cd;
			$logchat->lang_cd = $lang_cd;
			$logchat->member_id = $member_id;
			$logchat->intent_cd = $intent_cd;
			$logchat->log_time = $this->get_mstime();
			$logchat->score = $score;
			$logchat->answer_type = $answer_type;
			$logchat->member_msg = '';
			if ($be_member_msg == true) {
				$logchat->member_msg = $msg_org;
				$logchat->member_msg_t = $bot_msg_t;
			}
			else {
				$logchat->bot_msg = $msg_org;
				$logchat->bot_msg_t = $bot_msg_t;
			}
			$logchat->is_tester = $is_tester;
			$logchat->user_id = $user_id;
			// only in t_log_chat
			$logchat->member_name = $member_name;
			$logchat->country_cd = $member->country_cd;
			$logchat->member_tag = $member_tags;
			$logchat->chat_mode = $chat_mode;
			$logchat->request_flg = $request_flg;
			$logchat->save();
			
			$tbl_ext = $this->get_log_table_fix($this->_bot->bot_id);
			if ($be_member_msg == true) {
				DB::insert("t_bot_log$tbl_ext",
						['log_id','bot_id','scene_cd','sns_type_cd','lang_cd','member_id','intent_cd','log_time','score','answer_type','bot_msg','member_msg','member_msg_t','is_tester','user_id'])->
						values([$logchat->log_id,$this->_bot->bot_id,$scene_cd,$sns_type_cd,$lang_cd,$member_id,$intent_cd,$logchat->log_time,$score,$answer_type,'',$msg_org,$bot_msg_t,$is_tester,$user_id])->execute();
			}
			else {
				DB::insert("t_bot_log$tbl_ext",
						['log_id','bot_id','scene_cd','sns_type_cd','lang_cd','member_id','intent_cd','log_time','score','member_msg','bot_msg','bot_msg_t','is_tester','user_id'])->
						values([$logchat->log_id,$this->_bot->bot_id,$scene_cd,$sns_type_cd,$lang_cd,$member_id,$intent_cd,$logchat->log_time,0,'',$msg_org,$bot_msg_t,$is_tester,$user_id])->execute();
						
			}
			/*
			 $orm = ORM::factory('botlog');
			 $orm->log_id = $logchat->log_id;
			 $orm->bot_id = $this->_bot->bot_id;
			 $orm->sns_type_cd = $sns_type_cd;
			 $orm->lang_cd = $lang_cd;
			 $orm->member_id = $member_id;
			 $orm->intent_cd = $intent_cd;
			 $orm->log_time = $logchat->log_time;
			 $orm->score = 0;
			 $orm->member_msg = '';
			 $orm->bot_msg = $msg_org;
			 $orm->bot_msg_t = $bot_msg_t;
			 $orm->is_tester = $is_tester;
			 $orm->user_id = $user_id;
			 $orm->save();
			 */		
		//}
	}
	
}

?>
