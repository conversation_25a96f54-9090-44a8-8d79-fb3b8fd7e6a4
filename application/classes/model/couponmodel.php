<?php defined('SYSPATH') OR die('No direct script access.');

class Model_Couponmodel extends Model_Basemodel
{	
	function get_max_member_no($bot_id) {//メンバーnoの更新
		$sql = "SELECT max(member_no) AS member_no FROM t_bot_member WHERE bot_id=:bot_id";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':bot_id' => $bot_id,
		));
		$results = $query->execute()->as_array();
		return $results[0]['member_no'] + 1;
	}

	function check_coupon_issue($coupon_id) { //クーポンの発行上限回数が超えていないか確認
		$coupon = ORM::factory('coupon', $coupon_id);
		$coupon_data = json_decode($coupon->coupon_data, true);
		$issue_count = $this->get_coupon_issue($coupon_id);
		if (isset($coupon_data['stock_type']) AND $coupon_data['stock_type'] == '2') {
			if ($coupon_data['stock'] <= $issue_count) {
				return false;
			}
		}
		return true;
	}

	function get_coupon_issue($coupon_id) { //クーポン発行回数を取得
		$sql = "SELECT COUNT(result_no) AS issuecount FROM t_coupon_result WHERE coupon_id=:coupon_id
		AND member_id IN (SELECT member_id FROM t_bot_member WHERE 1)";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':coupon_id' => $coupon_id
		));
		$results = $query->execute()->as_array();
		if (count($results) == 0 || $results == NULL) {
			return 0;
		}
		else {
			return $results[0]['issuecount'];
		}
	}

	function check_coupon_use_amount($coupon_id) { //クーポンの総利用上限回数が超えていないか確認
		$coupon = ORM::factory('coupon', $coupon_id);
		$coupon_data = json_decode($coupon->coupon_data, true);
		$use_amount = $this->_get_coupon_use_amount($coupon_id);
		if (isset($coupon_data['use_amount_type']) AND $coupon_data['use_amount_type'] == '2') {
			if ($coupon_data['use_amount'] <= $use_amount) {
				return 0;
			}
		}
		return 1;
	}

	function check_expiration_after_issue($coupon_id, $member_id) {
		$coupon = ORM::factory('coupon', $coupon_id);
		$coupon_data = json_decode($coupon->coupon_data, true);
		if (!array_key_exists('expiration_date', $coupon_data)) return true;
		// 発行した時間も確認するため、日数ではなく秒数で数える
		$expiration_date = $coupon_data['expiration_date']* 24 * 60;
		$result_no = $this->get_coupon_result_no($member_id, $coupon_id);
		if ($result_no) {
			$coupon_result = ORM::factory('couponresult', $result_no);
			$today = new DateTime('now');
			$diff = $today->diff(new DateTime($coupon_result->get_date));
			$total_minutes = ($diff->days * 24 * 60); 
			$total_minutes += ($diff->h * 60); 
			$total_minutes += $diff->i;
			if ($total_minutes > $expiration_date) {
				return false;
			}
		}
		return true;
	}

	function get_coupon_expire_day($coupon_id, $member_id) {
		$coupon = ORM::factory('coupon', $coupon_id);
		$end_date = $coupon->end_date ? $coupon->end_date : '';
		
		$coupon_data = json_decode($coupon->coupon_data, true);
		$expireAfterIssueIsSet = array_key_exists('expiration_date', $coupon_data);
		$result_no = $this->get_coupon_result_no($member_id, $coupon_id);

		if (!$result_no || !$expireAfterIssueIsSet) return  $end_date;

		$coupon_result = ORM::factory('couponresult', $result_no);
		$today = date("Y-m-d");
		if ($coupon->end_date >= $today) {
			$expireDay = date('Y-m-d', strtotime($today . " + ".$coupon_data['expiration_date']. "day"));
			if ($coupon->end_date >= $expireDay) {
				$end_date = date('Y-m-d H:i:s', strtotime($coupon_result->get_date . " + ".$coupon_data['expiration_date']. "day"));
			}
		} else if ($coupon->end_date === null) {
			if ($coupon->start_date && $today < $coupon->start_date) {
				$end_date = str_replace('{date}', $coupon_data['expiration_date'], __('survey.coupon.expire.after.issue'));

			} else {
				if ($result_no) {
					$end_date = date('Y-m-d H:i:s', strtotime($coupon_result->get_date . " + ".$coupon_data['expiration_date']. "day"));
				}
			}
		}

		return $end_date;
	}

	function _get_coupon_use_amount($coupon_id) { //クーポン総利用回数を取得
		$sql = "SELECT COUNT(a.use_num) AS useamount
				FROM t_coupon_use_result a
				INNER JOIN t_coupon_result b ON a.result_no = b.result_no WHERE b.coupon_id=:coupon_id
				AND b.member_id IN (SELECT member_id FROM t_bot_member WHERE 1)";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':coupon_id' => $coupon_id
		));
		$results = $query->execute()->as_array();
		if (count($results) == 0 || $results == NULL) {
			return 0;
		}
		else {
			return $results[0]['useamount'];
		}
	}

	function get_coupon_usecount($member_id, $coupon_id) { //ユーザーのクーポン利用回数を取得
		$sql = "SELECT COUNT(u.seq) AS usecount FROM t_coupon_result r INNER JOIN t_coupon_use_result u ON r.result_no = u.result_no WHERE r.member_id=:member_id AND r.coupon_id=:coupon_id";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':member_id' => $member_id,
				':coupon_id' => $coupon_id
		));
		$results = $query->execute()->as_array();
		if (count($results) == 0 || $results == NULL) {
			return 0;
		}
		else {
			return $results[0]['usecount'];
		}
	}

	function check_coupon_stock($member_id, $coupon_id) {//ユーザーごとの利用上限回数が超えていないか確認
		//ユーザーがクーポン所持するかどうかをチェックする
		$has_coupon = $this->get_coupon_result_no($member_id, $coupon_id);
		if ($has_coupon === 0) {
			return NULL;
		}

		//ユーザーが利用上限超えているかをチェックする
		$coupon = ORM::factory('coupon', $coupon_id);
		$coupon_data = json_decode($coupon->coupon_data, true);
		$use_count = $this->get_coupon_usecount($member_id, $coupon_id);
		if (isset($coupon_data['stock_type_member']) && $coupon_data['stock_type_member'] == '2') {
			if ($coupon_data['stock_member'] <= $use_count) {
				return -2;
			} else {
				return $use_count;
			};
		} else {
			return $use_count;
		}
	}

	function get_coupon_result_no($member_id, $coupon_id) { //ユーザーがクーポンを持っていいるかを確認、持てればresult_noを返す
		$sql = "SELECT result_no FROM t_coupon_result WHERE member_id=:member_id AND coupon_id=:coupon_id ORDER BY result_no DESC LIMIT 0, 1";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':member_id' => $member_id,
				':coupon_id' => $coupon_id
		));
		$results = $query->execute()->as_array();
		if (count($results) == 0 || $results == NULL) {
			return 0;
		}
		else {
			return $results[0]['result_no'];
		}
	}

	function use_coupon($result_no, $use_facility) {//クーポンを使う
		$result = ORM::factory('couponuseresult');
		$result->result_no= $result_no;
		$result->seq = $this->get_max_coupon_seq_no($result_no);
		$result->use_date = date('Y-m-d H:i:s');
		$result->use_facility = $use_facility;
		$result->use_num= 1;
		$result->save();
	}

	function get_max_coupon_seq_no($result_no) {
		$sql = "SELECT COUNT(seq) AS usecount FROM t_coupon_use_result WHERE result_no=:result_no";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':result_no' => $result_no,
		));
		$results = $query->execute()->as_array();
		if (count($results) == 0 || $results == NULL) {
			return 1;
		}
		else {
			return $results[0]['usecount']+1;
		}
	}
}

?>
