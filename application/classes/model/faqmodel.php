<?php defined('SYSPATH') OR die('No direct script access.');

class Model_Faqmodel extends Model_Basemodel
{
	function get_report($bot_id, $lang_cd) {
		$sql="SELECT COUNT(*) AS c, a.intent_cd, a.sub_intent_cd, b.question,
				t.answer1_type_cd, t.answer1, t.answer2_type_cd, t.answer2, t.answer3_type_cd, t.answer3, t.answer4_type_cd, t.answer4, t.answer5_type_cd, t.answer5,
				t.answer6_type_cd, t.answer6, t.answer7_type_cd, t.answer7, t.answer8_type_cd, t.answer8, t.answer9_type_cd, t.answer9
				FROM t_bot_log a
				INNER JOIN (SELECT DISTINCT intent_cd, sub_intent_cd, question FROM m_bot_intent WHERE lang_cd = :lang_cd) AS b
					ON a.intent_cd = b.intent_cd AND a.sub_intent_cd = b.sub_intent_cd 
				INNER JOIN t_bot_intent t ON t.intent_cd = a.intent_cd AND t.sub_intent_cd = a.sub_intent_cd AND t.lang_cd = :lang_cd AND t.bot_id = :bot_id
				WHERE t.bot_id = :bot_id  AND a.intent_cd LIKE 'inquiry.%' AND a.member_msg<>'' AND a.score > 0 AND score <> 90 ";
		if ($lang_cd!= '') $sql = $sql . " AND a.lang_cd=:lang_cd ";
		$sql = $sql . " GROUP BY a.intent_cd, a.sub_intent_cd, b.question, t.answer1_type_cd, t.answer1, t.answer2_type_cd, t.answer2, t.answer3_type_cd, t.answer3, t.answer4_type_cd, t.answer4, t.answer5_type_cd, t.answer5 ORDER BY c DESC, a.intent_cd, a.sub_intent_cd LIMIT 0 , 10";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
				':lang_cd' => $lang_cd,
		));
		$log = $query->execute()->as_array();
		return $log;
	}
	
	function get_report_self($bot_id, $lang_cd, $start_date, $end_date) {
		$sql="SELECT COUNT(*) AS c, a.intent_cd, a.sub_intent_cd, b.question,
				t.answer1_type_cd, t.answer1, t.answer2_type_cd, t.answer2, t.answer3_type_cd, t.answer3, t.answer4_type_cd, t.answer4, t.answer5_type_cd, t.answer5,
				t.answer6_type_cd, t.answer6, t.answer7_type_cd, t.answer7, t.answer8_type_cd, t.answer8, t.answer9_type_cd, t.answer9				
				FROM t_bot_log a
				INNER JOIN (SELECT DISTINCT intent_cd, sub_intent_cd, question FROM m_bot_intent WHERE lang_cd = :lang_cd) AS b
					ON a.intent_cd = b.intent_cd AND a.sub_intent_cd = b.sub_intent_cd
				INNER JOIN t_bot_intent t ON t.intent_cd = a.intent_cd AND t.sub_intent_cd = a.sub_intent_cd AND t.lang_cd = :lang_cd AND t.bot_id = :bot_id
				WHERE t.bot_id = :bot_id  AND a.intent_cd LIKE 'inquiry.%' AND a.member_msg<>'' AND a.score > 0 AND score <> 90 
				 AND a.lang_cd=:lang_cd AND log_time>=:start_date AND log_time<:end_date ";
		$sql = $sql . " GROUP BY a.intent_cd, a.sub_intent_cd, b.question, t.answer1_type_cd, t.answer1, t.answer2_type_cd, t.answer2, t.answer3_type_cd, t.answer3, t.answer4_type_cd, t.answer4, t.answer5_type_cd, t.answer5 ORDER BY c DESC, a.intent_cd, a.sub_intent_cd LIMIT 0 , 10";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
				':lang_cd' => $lang_cd,
				':start_date' => $start_date,
				':end_date' => $end_date,
		));
		$log = $query->execute()->as_array();
		return $log;
	}
	
	function get_faq_ref($bot_id, $lang_cd=NULL, $start_date=NULL, $end_date=NULL)
	{
		if ($end_date != NULL) $end_date = date("Y-m-d",strtotime("+1 day",strtotime($end_date)));
		$sql = "SELECT t.intent_cd, t.sub_intent_cd, m.question, count(access_time) as access_count
                FROM t_faq_ref t
				LEFT JOIN m_bot_intent m ON t.intent_cd = m.intent_cd AND m.lang_cd='ja'
				WHERE t.bot_id=:bot_id ";
		if ($lang_cd != NULL) $sql = $sql . " AND t.lang_cd = :lang_cd";
		if ($start_date != NULL) $sql = $sql . " AND access_time>=:start_date ";
		if ($end_date != NULL) $sql = $sql . " AND access_time<:end_date ";
		$sql = $sql . " GROUP BY t.intent_cd, t.sub_intent_cd, m.question";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
				':lang_cd' => $lang_cd,
				':start_date' => $start_date,
				':end_date' => $end_date,
		));
		$results = $query->execute()->as_array();
		return $results;
	}
	
	function get_faq_types($bot_id, $lang_cd, $faq_div) {
		//$faq_div = $this->get_bot_setting($bot_id, 'div_item_class_0');
		$ret = [];
		$bot = ORM::factory('bot', $bot_id);
		$class_types = explode(',', $bot->class_type_cd);
		if ($faq_div == '') {
			$faq_types = $this->get_bot_class_types($bot, $lang_cd);
			foreach($faq_types as $it) {
				if (in_array($it['class_cd'], $class_types)) $ret[] = $it;
			}
		}
		else {
			$faq_class = $this->get_code_div($faq_div, $lang_cd);
			foreach($faq_class as $item) {
				if (strlen($item['class_cd']) > 2) continue;
				if (!in_array($item['class_cd'], $class_types)) continue;
				$ret[] = $item;
			}
		}
		return $ret;
		
		/*
		$sql="SELECT distinct(m.intent_type_cd) FROM t_bot_intent t
				INNER JOIN m_bot_intent m
					ON m.intent_cd = t.intent_cd AND m.sub_intent_cd = t.sub_intent_cd AND m.lang_cd = t.lang_cd
				WHERE t.bot_id = :bot_id ";
		if ($lang_cd != '') $sql = $sql . " AND t.lang_cd=:lang_cd AND t.answer1<>''";
		$sql = $sql . " ORDER BY m.intent_type_cd";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
				':lang_cd' => $lang_cd,
		));
		$result = $query->execute()->as_array('intent_type_cd', 'intent_type_cd');
		$ret = [];
		foreach($faq_types as $it) {
			if (array_key_exists($it['class_cd'], $result)) $ret[] = $it;
		}
		return $ret;
		*/
	}
	
	function get_context_faq_types($bot_id, $lang_cd, $class_cd) {
		$ret = [];
		$bot = ORM::factory('bot', $bot_id);
		//$class_types = explode(',', $bot->class_type_cd);
		$arr = explode(',', $class_cd);
		$new_arr = [];
		foreach($arr as $a) {
			$new_arr[] = "'" . $a . "'";
		}
		$class_cd = implode(',', $new_arr);
		$bot_class_cd = $bot->bot_class_cd;
		$div_faq_choice = $this->get_bot_setting($bot->bot_id, 'div_item_class_0');
		if ($div_faq_choice == '') {
			$sql = "SELECT class_cd, name, grid_pic_url, word
				FROM m_class_code WHERE code_div = 9001" . $bot_class_cd . " AND parent_cd IN ($class_cd) AND lang_cd=:lang_cd ORDER BY sort";
		}
		else {
			$sql = "SELECT class_cd, name, grid_pic_url, word
			FROM m_class_code WHERE code_div = $div_faq_choice AND parent_cd IN ($class_cd) AND lang_cd=:lang_cd ORDER BY sort";
		}
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':lang_cd' => $lang_cd,
		));
		$results = $query->execute()->as_array();
		return $results;
	}
	
	function get_faq($bot_id, $faq_div, $type, $lang_cd, $all_son=0) {
		$sql="SELECT m.intent_type_cd, t.intent_cd, t.sub_intent_cd, t.facility_question_title, t.answer1_type_cd, t.answer1, t.answer2_type_cd, t.answer2, t.answer3_type_cd, t.answer3, 
			t.answer4_type_cd, t.answer4, t.answer5_type_cd, t.answer5,t.answer6_type_cd, t.answer6, t.answer7_type_cd, t.answer7, t.answer8_type_cd, t.answer8, t.answer9_type_cd, t.answer9,
			t.answer_summary_title,t.answer1_summary,t.answer2_summary,t.answer3_summary,t.answer4_summary,t.answer5_summary,t.answer6_summary,t.answer7_summary,t.answer8_summary,t.answer9_summary,
 			m.question, t.sort_no FROM t_bot_intent t
				INNER JOIN m_bot_intent m
					ON m.intent_cd = t.intent_cd AND m.sub_intent_cd = t.sub_intent_cd AND m.lang_cd = t.lang_cd ";
		if ($all_son == 0) {
			$sql = $sql . " WHERE t.bot_id = :bot_id ";
		}
		else {
			$max_bot_id = intval($bot_id) + 1000;
			$sql = $sql . " WHERE t.bot_id > $bot_id AND t.bot_id < $max_bot_id ";
		}
		if ($lang_cd != '') $sql = $sql . " AND t.lang_cd=:lang_cd AND t.answer1<>''";
		if ($faq_div == '') {
			$sql = $sql . " AND m.intent_type_cd=$type ";
		}
		else {
			$sql = $sql . " AND concat(',',t.intent_type_cd) LIKE '%,$type%' ";
		}
		$sql = $sql . " ORDER BY t.sort_no, t.intent_cd, t.sub_intent_cd LIMIT 0 , 1000";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
				':intent_type_cd' => $type,
				':lang_cd' => $lang_cd,
		));
		$log = $query->execute()->as_array();
		return $log;
	}
	
	function get_faq_one($bot_id, $intent_cd, $lang_cd, $all_son = 0) {
		$sql="SELECT t.intent_cd, t.sub_intent_cd, t.facility_question_title, t.answer1_type_cd, t.answer1, t.answer2_type_cd, t.answer2, t.answer3_type_cd, t.answer3,
			t.answer4_type_cd, t.answer4, t.answer5_type_cd, t.answer5, t.answer6_type_cd, t.answer6, t.answer7_type_cd, t.answer7, t.answer8_type_cd, t.answer8, t.answer9_type_cd, t.answer9, 
			t.answer_summary_title,t.answer1_summary,t.answer2_summary,t.answer3_summary,t.answer4_summary,t.answer5_summary,t.answer6_summary,t.answer7_summary,t.answer8_summary,t.answer9_summary,
			m.intent_type_cd, m.question FROM t_bot_intent t
				INNER JOIN m_bot_intent m
					ON m.intent_cd = t.intent_cd AND m.sub_intent_cd = t.sub_intent_cd AND m.lang_cd = t.lang_cd AND m.intent_cd=:intent_cd ";
		if ($all_son == 0) {
			$sql = $sql . " WHERE t.bot_id = :bot_id ";
		}
		else {
			$max_bot_id = intval($bot_id) + 1000;
			$sql = $sql . " WHERE t.bot_id > $bot_id AND t.bot_id < $max_bot_id ";
		}
		if ($lang_cd!= '') $sql = $sql . " AND t.lang_cd=:lang_cd AND t.answer1<>''";
		$sql = $sql . " ORDER BY t.intent_cd, t.sub_intent_cd LIMIT 0 , 1000";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
				':intent_cd' => $intent_cd,
				':lang_cd' => $lang_cd,
		));
		$log = $query->execute()->as_array();
		return $log;
	}

	function get_faq_by_full_question($bot_id, $question, $lang_cd) {
		$sql="SELECT t.intent_cd, t.sub_intent_cd, t.facility_question_title, t.answer1_type_cd, t.answer1, t.answer2_type_cd, t.answer2, t.answer3_type_cd, t.answer3,
			t.answer4_type_cd, t.answer4, t.answer5_type_cd, t.answer5, t.answer6_type_cd, t.answer6, t.answer7_type_cd, t.answer7, t.answer8_type_cd, t.answer8, t.answer9_type_cd, t.answer9, 
			t.answer_summary_title,t.answer1_summary,t.answer2_summary,t.answer3_summary,t.answer4_summary,t.answer5_summary,t.answer6_summary,t.answer7_summary,t.answer8_summary,t.answer9_summary,
			m.intent_type_cd, m.question FROM t_bot_intent t
				INNER JOIN m_bot_intent m
					ON m.intent_cd = t.intent_cd AND m.sub_intent_cd = t.sub_intent_cd AND m.lang_cd = t.lang_cd AND m.question=:question
				WHERE t.bot_id = :bot_id ";
		if ($lang_cd!= '') $sql = $sql . " AND t.lang_cd=:lang_cd AND t.answer1<>''";
		$sql = $sql . " ORDER BY t.intent_cd, t.sub_intent_cd LIMIT 0 , 1000";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
				':question' => $question,
				':lang_cd' => $lang_cd,
		));
		$log = $query->execute()->as_array();
		return $log;
	}
	
	function get_faq_by_full_question_m($question, $lang_cd) {
		$sql="SELECT m.intent_cd, m.sub_intent_cd, m.question FROM m_bot_intent m WHERE m.question=:question AND m.lang_cd=:lang_cd";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':question' => $question,
				':lang_cd' => $lang_cd,
		));
		$log = $query->execute()->as_array();
		return $log;
	}
	
	function get_faq_by_tag($bot_id, $tag, $lang_cd) {
		$sql="SELECT t.intent_cd, t.sub_intent_cd, t.facility_question_title, t.answer1_type_cd, t.answer1, t.answer2_type_cd, t.answer2, t.answer3_type_cd, t.answer3,
			t.answer4_type_cd, t.answer4, t.answer5_type_cd, t.answer5, t.answer6_type_cd, t.answer6, t.answer7_type_cd, t.answer7, t.answer8_type_cd, t.answer8, t.answer9_type_cd, t.answer9,
			t.answer_summary_title,t.answer1_summary,t.answer2_summary,t.answer3_summary,t.answer4_summary,t.answer5_summary,t.answer6_summary,t.answer7_summary,t.answer8_summary,t.answer9_summary,
			m.intent_type_cd, m.question FROM t_bot_intent t
				INNER JOIN m_bot_intent m
					ON m.intent_cd = t.intent_cd AND m.sub_intent_cd = t.sub_intent_cd AND m.lang_cd = t.lang_cd 
				WHERE t.bot_id = :bot_id AND t.tags LIKE :tag";
		if ($lang_cd!= '') $sql = $sql . " AND t.lang_cd=:lang_cd AND t.answer1<>''";
		$sql = $sql . " ORDER BY t.intent_cd, t.sub_intent_cd LIMIT 0 , 1000";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
				':tag' => '%'.$tag.'%',
				':lang_cd' => $lang_cd,
		));
		$log = $query->execute()->as_array();
		return $log;
	}
	
	function get_faq_skill($bot_id, $faq_div, $lang_cd, $bot) {
		$bot_class_cd = $bot->bot_class_cd;
		if ($bot_id == 0) {
			$sql="SELECT t.intent_cd, t.sub_intent_cd, t.lang_cd, t.skill, m.question, m.intent_type_cd, '' AS facility_question_title, m.level,m.item_ids FROM t_intent_skill t
					INNER JOIN m_bot_intent m ON m.intent_cd = t.intent_cd AND m.sub_intent_cd = t.sub_intent_cd AND m.lang_cd=:lang_cd AND m.intent_class_cd=:bot_class_cd
					WHERE t.bot_id = :bot_id
					ORDER BY t.intent_cd, t.sub_intent_cd, t.lang_cd";
			//if ($lang_cd!= '') $sql = $sql . " AND t.lang_cd=:lang_cd";
		}
		else {
			if ($faq_div == '') {
				$intent_type_cd = 'm.intent_type_cd';
			}
			else {
				$intent_type_cd = 'b.intent_type_cd';
			}
			$sql="SELECT t.intent_cd, t.sub_intent_cd, t.lang_cd, t.skill, m.question, $intent_type_cd, b.facility_question_title FROM t_intent_skill t
				INNER JOIN m_bot_intent m ON m.intent_cd = t.intent_cd AND m.sub_intent_cd = t.sub_intent_cd AND m.lang_cd=:lang_cd AND m.intent_class_cd=:bot_class_cd
				LEFT JOIN t_bot_intent b ON b.intent_cd = t.intent_cd AND b.sub_intent_cd = t.sub_intent_cd AND b.lang_cd=:lang_cd AND t.bot_id = b.bot_id
				WHERE t.bot_id = :bot_id 
				ORDER BY t.intent_cd, t.sub_intent_cd, t.lang_cd";
			//if ($lang_cd!= '') $sql = $sql . " AND t.lang_cd=:lang_cd";
		}
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
				':lang_cd' => $lang_cd,
				':bot_class_cd' => $bot_class_cd,
		));
		$logs = $query->execute()->as_array();
		$keys = [];
		foreach($logs as $log) {
			$keys[] = $log['intent_cd'] . '-' . $log['sub_intent_cd'] . '-' . $log['lang_cd'];
		}
		$skills = [];
		foreach($logs as $log) {
			if ($log['lang_cd'] != '' && $lang_cd != $log['lang_cd']) {
				continue;
			}
			if ($bot_id == 0) {
				if ($log['level'] >= 3) {
					$item_ids = explode(',', $log['item_ids']);
					if (!in_array($bot->bot_id, $item_ids)) continue;
				}
			}
			$skills[$log['intent_cd'] . '-' . $log['sub_intent_cd'] . '-' . $log['lang_cd']] = $log;
		}
		return $skills;
	}
	
	function get_faq_relation($bot_id, $intent_cd, $lang_cd) {
		$sql = "SELECT intent_ft FROM m_intent_relation";
		$query = DB::query(Database::SELECT, $sql);
		$relation = $query->execute()->as_array();
		$relations = [];
		$intent_cd = str_replace('inquiry.', '', $intent_cd);
		foreach($relation as $rel) {
			$rel_arr = explode(' ', $rel['intent_ft']);
			if (in_array($intent_cd, $rel_arr)) {
				foreach($rel_arr as $a) {
					if ($a != $intent_cd) $relations[] = "'inquiry.$a'";
				}
				break;
			}
		}
		if (count($relations) == 0) return [];
		
		$sql="SELECT t.intent_cd, t.sub_intent_cd, t.facility_question_title, t.answer1_type_cd, t.answer1, t.answer2_type_cd, t.answer2, t.answer3_type_cd, t.answer3,
			t.answer4_type_cd, t.answer4, t.answer5_type_cd, t.answer5, t.answer6_type_cd, t.answer6, t.answer7_type_cd, t.answer7, t.answer8_type_cd, t.answer8, t.answer9_type_cd, t.answer9,
			t.answer_summary_title,t.answer1_summary,t.answer2_summary,t.answer3_summary,t.answer4_summary,t.answer5_summary,t.answer6_summary,t.answer7_summary,t.answer8_summary,t.answer9_summary,
			m.intent_type_cd, m.question FROM t_bot_intent t
			INNER JOIN m_bot_intent m ON m.intent_cd = t.intent_cd AND m.sub_intent_cd = t.sub_intent_cd AND m.lang_cd = t.lang_cd 
			WHERE t.bot_id = :bot_id AND t.lang_cd=:lang_cd AND t.answer1<>'' AND t.intent_cd IN (" . implode(',', $relations) . ")";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
				':lang_cd' => $lang_cd,
		));
		$log = $query->execute()->as_array();
		return $log;
	}
	
	function find_keyword($bot_id, $keyword, $lang_cd, $context=null) {
		$bot = ORM::factory('bot', $bot_id);
		if ($context != null) {
			$faq_context_setting = $this->get_bot_tpl_message($bot_id, 'faq_context_setting', $lang_cd);
			$faq_context_setting = json_decode($faq_context_setting, true);
			$contexts = ORM::factory('botcontext')->where('bot_id', '=', $bot_id)->find_all();
			$conext_dict = [];
			foreach($contexts as $c) {
				if (array_key_exists($c->context_id, $faq_context_setting)) {
					$faq_context_setting[$c->context_id]['category'] = $c->intent_type_cd;
				}
			}
			$filter_faq_type_cd = explode(',', $faq_context_setting[$context]['category']);
		}
		else {
			$filter_faq_type_cd = explode(',', $bot->class_type_cd);
		}
		$keyword = str_replace("　", " ", $keyword);
		$keyword = preg_replace("/\s+/", " ", trim($keyword));
		$keys = explode(" ", $keyword);
		$sql="SELECT m.question, t.facility_question_title, t.intent_cd, t.sub_intent_cd, t.intent_type_cd, m.intent_type_cd AS m_intent_type_cd FROM t_bot_intent t
				INNER JOIN m_bot_intent m
					ON m.intent_cd = t.intent_cd AND m.sub_intent_cd = t.sub_intent_cd AND m.lang_cd = t.lang_cd 
				WHERE t.bot_id = :bot_id ";
		if ($lang_cd!= '') $sql = $sql . " AND t.lang_cd=:lang_cd AND t.answer1<>'' ";
		foreach($keys as $key) {
			$sql = $sql . " AND (m.question LIKE '%$key%' OR t.facility_question_title LIKE '%$key%') ";
		}
		$sql = $sql . " ORDER BY t.intent_cd, t.sub_intent_cd LIMIT 0 , 1000";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
				':lang_cd' => $lang_cd,
		));
		$questions = $query->execute()->as_array();
		
		$intent_key = [];
		$result = [];
		foreach($questions as $q) {
			if ($context != null) {
				foreach($filter_faq_type_cd as $cd) {
					if (strpos(',' . $q['intent_type_cd'], ',' . $cd) !== false) {
						$intent_key[] = $q['intent_cd'] . '#' . $q['sub_intent_cd'];
						if ($q['facility_question_title'] != '') {
							$result[] = $q['facility_question_title'];
						}
						else {
							$result[] = $q['question'];
						}
						break;
					}
				}
			}
			else {
				foreach($filter_faq_type_cd as $cd) {
					$temp_cd = $q['intent_type_cd'];
					if ($temp_cd == '') $temp_cd = $q['m_intent_type_cd'];
					if (strpos(',' . $temp_cd, ',' . $cd) !== false) {
						$intent_key[] = $q['intent_cd'] . '#' . $q['sub_intent_cd'];
						if ($q['facility_question_title'] != '') {
							$result[] = $q['facility_question_title'];
						}
						else {
							$result[] = $q['question'];
						}
						break;
					}
				}
			}
		}
		
		$sql="SELECT DISTINCT m.question, t.intent_cd, t.sub_intent_cd, m.intent_type_cd FROM m_bot_intent m
				INNER JOIN t_intent_skill t
					ON m.intent_cd = t.intent_cd AND m.sub_intent_cd = t.sub_intent_cd 
				WHERE (t.bot_id = :bot_id OR (t.bot_id=0 AND t.skill NOT LIKE '%RECOMMEND_ITEMS_BY_%')) AND m.lang_cd=:lang_cd AND m.intent_class_cd = :bot_class_cd";
		foreach($keys as $key) {
			$sql = $sql . " AND m.question LIKE '%$key%' ";
		}
		$sql = $sql . " ORDER BY t.intent_cd, t.sub_intent_cd LIMIT 0 , 1000";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
				':lang_cd' => $lang_cd,
				':bot_class_cd' => $bot->bot_class_cd,
		));
		$questions = $query->execute()->as_array();
		
		foreach($questions as $q) {
			if (!in_array($q['intent_cd'] . '#' . $q['sub_intent_cd'], $intent_key)) {
				if ($context != null) {
					foreach($filter_faq_type_cd as $cd) {
						if (strpos(',' . $q['intent_type_cd'], ',' . $cd) !== false) {
							$result[] = $q['question'];
							break;
						}
					}
				}
				else {
					foreach($filter_faq_type_cd as $cd) {
						$temp_cd = $q['intent_type_cd'];
						if ($temp_cd == '') $temp_cd = $q['m_intent_type_cd'];
						if (strpos(',' . $temp_cd, ',' . $cd) !== false) {
							$result[] = $q['question'];
							break;
						}
					}
				}
			}
		}
		
		return $result;
	}
	
	function get_freq_report($bot_id) {
		$sql="SELECT content FROM t_bot_report WHERE bot_id = :bot_id AND report_type_cd = :report_type_cd ORDER BY report_date DESC LIMIT 0, 1";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
				':report_type_cd' => '00',
		));
		return $query->execute()->as_array();
	}
	
	function get_bot_freq_intent($bot_id, $intent_cd, $sub_intent_cd, $lang_cd) {
		$sql="SELECT lang_cd FROM t_bot_intent WHERE bot_id = :bot_id AND intent_cd = :intent_cd 
				AND sub_intent_cd = :sub_intent_cd AND lang_cd = :lang_cd";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
				':intent_cd' => $intent_cd,
				':sub_intent_cd' => $sub_intent_cd,
				':lang_cd' => $lang_cd,
		));
		return $query->execute()->as_array();
	}
	
	function get_freq_setting($bot_id, $lang_cd, $context) {
		if ($context == null) {
			$faq_frequent_msg = 'faq_frequent';
		}
		else {
			$faq_frequent_msg = 'faq_frequent' . '_' . $context;
		}
		$content = $this->get_bot_tpl_message($bot_id, $faq_frequent_msg, $lang_cd);
		if ($content == '') {
			$grp_bot_id = $this->get_grp_bot_id($bot_id);
			if ($grp_bot_id > 0) $content = $this->get_bot_tpl_message($grp_bot_id, $faq_frequent_msg, $lang_cd);
		}

		$freqs = json_decode($content, true);
		if ($freqs == null || $freqs['use'] == 0) return null;
		
		$sql="SELECT t.intent_cd, t.sub_intent_cd, t.facility_question_title, m.question FROM t_bot_intent t
			INNER JOIN m_bot_intent m ON t.intent_cd=m.intent_cd AND t.sub_intent_cd=m.sub_intent_cd AND t.lang_cd=m.lang_cd
			WHERE t.bot_id = :bot_id AND t.lang_cd = :lang_cd ";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
				':lang_cd' => $lang_cd,
		));
		$all = $query->execute()->as_array();
		$dict = [];
		foreach($all as $q) {
			if ($q['facility_question_title'] == '') {
				$dict[$q['intent_cd'] . '#' . $q['sub_intent_cd']] = $q['question'];
			}
			else {
				$dict[$q['intent_cd'] . '#' . $q['sub_intent_cd']] = $q['facility_question_title'];
			}
		}
		// parent
		$grp_bot_id = $this->get_grp_bot_id($bot_id);
		if ($grp_bot_id > 0) {
			$sql="SELECT t.intent_cd, t.sub_intent_cd, t.facility_question_title, m.question FROM t_bot_intent t
				INNER JOIN m_bot_intent m ON t.intent_cd=m.intent_cd AND t.sub_intent_cd=m.sub_intent_cd AND t.lang_cd=m.lang_cd
				WHERE t.bot_id = :bot_id AND t.lang_cd = :lang_cd ";
			$query = DB::query(Database::SELECT, $sql);
			$query->parameters(array(
					':bot_id' => $grp_bot_id,
					':lang_cd' => $lang_cd,
			));
			$all = $query->execute()->as_array();
			foreach($all as $q) {
				if (!array_key_exists($q['intent_cd'] . '#' . $q['sub_intent_cd'], $dict)) {
					if ($q['facility_question_title'] == '') {
						$dict[$q['intent_cd'] . '#' . $q['sub_intent_cd']] = $q['question'];
					}
					else {
						$dict[$q['intent_cd'] . '#' . $q['sub_intent_cd']] = $q['facility_question_title'];
					}
				}
			}
		}
		
		$result = ['frequent_faq'=>[], 'frequent_faq_ai'=>[]];
		foreach($freqs['frequent_faq'] as $r) {
			if (array_key_exists($r['intent_cd'] . '#' . $r['sub_intent_cd'], $dict)) {
				$r['question'] = $dict[$r['intent_cd'] . '#' . $r['sub_intent_cd']];
				$result['frequent_faq'][] = $r;
			}
		}
		foreach($freqs['frequent_faq_ai'] as $r) {
			if (array_key_exists($r['intent_cd'] . '#' . $r['sub_intent_cd'], $dict)) {
				$r['question'] = $dict[$r['intent_cd'] . '#' . $r['sub_intent_cd']];
				$result['frequent_faq_ai'][] = $r;
			}
		}
		return $result;
	}
	
	// wuzhao add begin #111
	function get_intent_list_aimai($lang_cd, $bot_id, $keywords, $all_son=0)
	{
		$keywords_with_synonym = $keywords;
		$sql = "SELECT concat(replace(:keywords_with_synonym,' ',','),',',replace(group_concat(synonym),' ',',')) AS SYNONYM_RET
				FROM `m_bot_synonym`
				WHERE
				LANG_CD = :lang_cd
				AND MATCH(synonym) AGAINST(replace(:keywords_with_synonym,' ',',') IN BOOLEAN MODE)
				";
		// AND find_in_set(example,replace(:keywords_with_synonym,' ',',')) >0
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':lang_cd' => $lang_cd,
				':keywords_with_synonym' => $keywords_with_synonym,
		));
		$results = $query->execute()->as_array();
		if (count($results) > 0 && $results[0]["SYNONYM_RET"] != "") {
			$keywords_with_synonym = $results[0]["SYNONYM_RET"];
		}
		$sql = "SELECT a.question,
				(length(c.question_ft)-length(replace(c.question_ft,' ','')) + 1) as keyword_num,
				MATCH(c.question_ft) AGAINST(:keywords IN BOOLEAN MODE)
				+ (case when (select find_in_set(a.ft_keyword1,:keywords_with_synonym)) > 0 then a.ft_keyword1_weight else 0 end ) 
				+ (case when (select find_in_set(a.ft_keyword2,:keywords_with_synonym)) > 0 then a.ft_keyword2_weight else 0 end ) 
				+ (case when (select find_in_set(a.ft_keyword3,:keywords_with_synonym)) > 0 then a.ft_keyword3_weight else 0 end ) 
				+ MATCH(d.additional_info_for_search) AGAINST(:keywords_with_synonym IN BOOLEAN MODE) 
				as score1,
				MATCH(d.answer_ft) AGAINST(:keywords IN BOOLEAN MODE) as score2,
				a.intent_type_cd,b.intent_type_cd as t_intent_type_cd,b.intent_cd, b.sub_intent_cd,b.facility_question_title,
				b.answer1_type_cd,b.answer1,b.answer2_type_cd,b.answer2,b.answer3_type_cd,b.answer3,b.answer4_type_cd,b.answer4,b.answer5_type_cd,b.answer5,
				b.answer6_type_cd,b.answer6,b.answer7_type_cd,b.answer7,b.answer8_type_cd,b.answer8,b.answer9_type_cd,b.answer9,
				b.answer_summary_title,b.answer1_summary,b.answer2_summary,b.answer3_summary,b.answer4_summary,b.answer5_summary,b.answer6_summary,b.answer7_summary,b.answer8_summary,b.answer9_summary
				FROM m_bot_intent a
				INNER JOIN t_bot_intent b
					ON a.intent_cd = b.intent_cd
					AND a.lang_cd = b.lang_cd
					AND a.sub_intent_cd = b.sub_intent_cd
				INNER JOIN m_bot_intent_fulltext c
					ON a.intent_class_cd = c.intent_class_cd
					AND a.intent_type_cd = c.intent_type_cd
					AND a.intent_cd = c.intent_cd
					AND a.sub_intent_cd = c.sub_intent_cd
					AND a.lang_cd = c.lang_cd
				INNER JOIN t_bot_intent_fulltext d
					ON b.bot_id = d.bot_id
					AND b.intent_cd = d.intent_cd
					AND b.sub_intent_cd = d.sub_intent_cd
					AND b.area_cd = d.area_cd
					AND b.lang_cd = d.lang_cd
				WHERE ";
		if ($all_son == 0) {
			$sql = $sql . " b.bot_id = :bot_id ";
		}
		else {
			$max_bot_id = intval($bot_id) + 1000;
			$sql = $sql . " b.bot_id > $bot_id AND b.bot_id < $max_bot_id ";
		}
		$sql = $sql . "
					AND b.lang_cd=:lang_cd
					AND b.intent_cd<>:original_intent_cd
					AND (
						MATCH(d.question_answer_ft) AGAINST(:keywords IN BOOLEAN MODE)
						OR MATCH(d.additional_info_for_search) AGAINST(:keywords_with_synonym IN BOOLEAN MODE)
					)
					AND (
						  :type_cd = ''
						  OR
						  find_in_set(a.intent_type_cd,:type_cd) > 0
						)
				ORDER BY
					score1 DESC,
					score2 DESC,
					keyword_num
				";
		
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
				':keywords' => $keywords,
				':lang_cd' => $lang_cd,
				':original_intent_cd' =>'',
				':keywords_with_synonym' => $keywords_with_synonym,
				':type_cd' => '',
		));
		$results = $query->execute();
		return $results->as_array();
	}
	
	function get_intent_list_aimai_m($lang_cd, $keywords, $bot_class_cd)
	{
		$keywords_with_synonym = $keywords;
		$sql = "SELECT concat(replace(:keywords_with_synonym,' ',','),',',replace(group_concat(synonym),' ',',')) AS SYNONYM_RET
				FROM `m_bot_synonym`
				WHERE
				LANG_CD = :lang_cd
				AND MATCH(synonym) AGAINST(replace(:keywords_with_synonym,' ',',') IN BOOLEAN MODE)
				";
		// AND find_in_set(example,replace(:keywords_with_synonym,' ',',')) >0
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':lang_cd' => $lang_cd,
				':keywords_with_synonym' => $keywords_with_synonym,
		));
		$results = $query->execute()->as_array();
		if (count($results) > 0 && $results[0]["SYNONYM_RET"] != "") {
			$keywords_with_synonym = $results[0]["SYNONYM_RET"];
		}
		$sql = "SELECT a.question,
				(length(a.question_ft)-length(replace(a.question_ft,' ','')) + 1) as keyword_num,
				MATCH(a.question_ft) AGAINST(:keywords IN BOOLEAN MODE)
				+ (case when (select find_in_set(a.ft_keyword1,:keywords_with_synonym)) > 0 then a.ft_keyword1_weight else 0 end )
				+ (case when (select find_in_set(a.ft_keyword2,:keywords_with_synonym)) > 0 then a.ft_keyword2_weight else 0 end )
				+ (case when (select find_in_set(a.ft_keyword3,:keywords_with_synonym)) > 0 then a.ft_keyword3_weight else 0 end )
				as score1,
				a.intent_type_cd,a.intent_cd, a.sub_intent_cd,
				'txt' AS answer1_type_cd,a.answer1,'txt' AS answer2_type_cd,a.answer2,'txt' AS answer3_type_cd,a.answer3,'txt' AS answer4_type_cd,a.answer4,'txt' AS answer5_type_cd,a.answer5,
				'txt' AS answer6_type_cd,a.answer6,'txt' AS answer7_type_cd,a.answer7,'txt' AS answer8_type_cd,a.answer8,'txt' AS answer9_type_cd,a.answer9,
				'' AS answer_summary_title,'' AS answer1_summary,'' AS answer2_summary,'' AS answer3_summary,'' AS answer4_summary,'' AS answer5_summary,'' AS answer6_summary,'' AS answer7_summary,'' AS answer8_summary,'' AS answer9_summary
				FROM m_bot_intent a
				WHERE a.lang_cd=:lang_cd
					AND MATCH(a.question_ft) AGAINST(:keywords IN BOOLEAN MODE)
					AND a.intent_cd<>:original_intent_cd
					AND a.intent_class_cd=:bot_class_cd
				ORDER BY
					score1 DESC,
					keyword_num
				";
		
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':keywords' => $keywords,
				':lang_cd' => $lang_cd,
				':original_intent_cd' =>'',
				':keywords_with_synonym' => $keywords_with_synonym,
				':bot_class_cd' => $bot_class_cd,
		));
		$results = $query->execute();
		return $results->as_array();
	}
	
	function get_intent_list_aimai_skill($lang_cd, $keywords, $bot_class_cd, $bot_ids)
	{
		$keywords_with_synonym = $keywords;
		$sql = "SELECT concat(replace(:keywords_with_synonym,' ',','),',',replace(group_concat(synonym),' ',',')) AS SYNONYM_RET
				FROM `m_bot_synonym`
				WHERE
				LANG_CD = :lang_cd
				AND MATCH(synonym) AGAINST(replace(:keywords_with_synonym,' ',',') IN BOOLEAN MODE)
				";
		// AND find_in_set(example,replace(:keywords_with_synonym,' ',',')) >0
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':lang_cd' => $lang_cd,
				':keywords_with_synonym' => $keywords_with_synonym,
		));
		$results = $query->execute()->as_array();
		if (count($results) > 0 && $results[0]["SYNONYM_RET"] != "") {
			$keywords_with_synonym = $results[0]["SYNONYM_RET"];
		}
		$conds = [];
		foreach($bot_ids as $id) {
			$conds[] = ' b.bot_id = ' . $id . ' ';
		}
		$bot_cond = implode(' OR ', $conds);
		$sql = "SELECT a.question, (length(c.question_ft)-length(replace(c.question_ft,' ','')) + 1) as keyword_num,
				MATCH(c.question_ft) AGAINST(:keywords IN BOOLEAN MODE) 
				+ (case when (select find_in_set(a.ft_keyword1,:keywords_with_synonym)) > 0 then a.ft_keyword1_weight else 0 end ) 
				+ (case when (select find_in_set(a.ft_keyword2,:keywords_with_synonym)) > 0 then a.ft_keyword2_weight else 0 end ) 
				+ (case when (select find_in_set(a.ft_keyword3,:keywords_with_synonym)) > 0 then a.ft_keyword3_weight else 0 end ) 
				as score1,
				0 as score2,
				a.intent_type_cd, b.intent_cd, b.sub_intent_cd, b.skill
				FROM m_bot_intent a
				INNER JOIN t_intent_skill b
					ON a.intent_cd = b.intent_cd
					AND a.sub_intent_cd = b.sub_intent_cd
					AND ($bot_cond)
				INNER JOIN m_bot_intent_fulltext c
					ON a.intent_class_cd = c.intent_class_cd
					AND a.intent_type_cd = c.intent_type_cd
					AND a.intent_cd = c.intent_cd
					AND a.sub_intent_cd = c.sub_intent_cd
					AND a.lang_cd = c.lang_cd				
				WHERE 
				a.lang_cd=:lang_cd 
				AND a.level <> 4
				AND a.intent_class_cd=:bot_class_cd 
				AND MATCH(c.question_ft) AGAINST(:keywords IN BOOLEAN MODE)
				ORDER BY b.bot_id DESC, score1 DESC, score2 DESC, keyword_num ";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':keywords' => $keywords,
				':keywords_with_synonym' => $keywords_with_synonym,
				':lang_cd' => $lang_cd,
				':bot_class_cd' => $bot_class_cd,
		));
		$results = $query->execute();
		return $results->as_array();
	}

	// copy from adminmodel
	function get_faq_log_count($bot_id, $scene_cd, $context_id, $lang_cd, $search_div, $start_date=NULL, $end_date=NULL) {
		if ($search_div == 1) {
			$column = "keyword";
		}
		else if ($search_div == 2) {
			$column = "content";
		}
		else {
			$column = "content";
		}
		$today = date("Y-m-d");
		$faq_setting = $this->get_bot_setting($bot_id, 'json_faq_setting', true);
		$term = "-1 month";
		$excepts = [];
		if (array_key_exists('api_getkeywords', $faq_setting)) {
			if (array_key_exists('term', $faq_setting['api_getkeywords'])) {
				$term = $faq_setting['api_getkeywords']['term'];
			}
			if (array_key_exists('excepts', $faq_setting['api_getkeywords'])) {
				$excepts = $faq_setting['api_getkeywords']['excepts'];
			}
		}
		if ($start_date == NULL) $start_date = date("Y-m-d",strtotime($term, strtotime($today)));
		$sql = "SELECT count($column) as c, $column as content, lang_cd FROM x_faq_log WHERE " .  $this->_create_bot_cond($bot_id) . " AND search_div=:search_div AND $column != ''";
		if ($start_date != NULL) $sql = $sql . " AND search_date>=:start_date ";
		if ($end_date != NULL) $sql = $sql . " AND search_date<:end_date ";
		if ($scene_cd != '') $sql = $sql . " AND scene_cd=:scene_cd ";
		if ($context_id != NULL) $sql = $sql . " AND context_id=:context_id ";
		$sql = $sql . " AND lang_cd=:lang_cd ";
		$sql = $sql . " GROUP BY $column,lang_cd ORDER BY c desc";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
				':scene_cd' => $scene_cd,
				':context_id' => $context_id,
				':lang_cd' => $lang_cd,
				':search_div' => $search_div,
				':start_date' => $start_date,
				':end_date' => $end_date,
		));
		$results = $query->execute()->as_array();
		$ret = [];
		foreach($results as $r) {
			if (in_array($r['content'], $excepts)) continue;
			$words = explode(' ', $r['content']);
			if (count($words) >= 4) continue;
			$ret[] = $r;
		}
		return $ret;
	}

	function get_msg_type_by_cd($bot_id, $msg_cd) {
		$sql = "SELECT a.msg_id, a.msg_type_cd FROM t_bot_msg a
		WHERE (a.bot_id=:bot_id OR a.bot_id=0) AND a.msg_cd=:msg_cd
		ORDER BY bot_id ASC";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
				':msg_cd' => $msg_cd,
		));

		$results = $query->execute();
		return $results->as_array();
	}

	function get_menu_title_by_msg_id($msg_id, $lang_cd) {
		$sql = "SELECT a.content, a.title, a.url FROM t_bot_msg_desc_lst a
		WHERE a.msg_id=:msg_id AND a.lang_cd=:lang_cd";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':msg_id' => $msg_id,
				':lang_cd' => $lang_cd,
		));

		$results = $query->execute();
		return $results->as_array();
	}
}

?>
