<?php defined('SYSPATH') OR die('No direct script access.');

class Model_Apimodel extends Model
{
	// 2021.11.16 #12049 暗号化
	public $_aes256_key = "talkappi321";
	// 2021.11.16 #12049 暗号化 end
	// 2021.12.13 #是否把索引放在另一个表
	public $_full_index_another_table = "true";
	// 2021.12.13 #是否把索引放在另一个表 end

	// $type_cd: 检索条件$type_cd, 可以是"01,02,03“这种格式
	// $alias_m_bot_intent,比如为a
	// 2021.07.08 拡張機能 #14198 FAQの分類が複数設定できるようにしたい
	function create_intent_type_cd_sql($source, $type_cd, $alias_m_bot_intent, $alias_t_bot_intent) {
		/*
			$sql_type_cd = "
						  :type_cd = ''
						  OR
						  find_in_set(a.intent_type_cd,:type_cd) > 0
			";
			return $sql_type_cd;
		*/

		$sql_type_cd = "1 = 1";
		if ($type_cd != "") {
			$array_type_cd = explode(",", $type_cd);
			for ($i=0; $i<count($array_type_cd); $i++) {
				$cur_type_cd = $array_type_cd[$i];
				$cur_sql =  "concat(',',$alias_m_bot_intent.intent_type_cd) like concat('%,','$cur_type_cd', '%')";
				$cur_sql_t =  "concat(',',$alias_t_bot_intent.intent_type_cd) like concat('%,','$cur_type_cd', '%')";
				if ($i == 0) {
					$sql_type_cd = $cur_sql;
					$sql_type_cd_t = $cur_sql_t;
				} else {
					$sql_type_cd = "$sql_type_cd 
					OR $cur_sql";
					$sql_type_cd_t = "$sql_type_cd_t 
					OR $cur_sql_t";
				}
			}
			$sql_type_cd = " ($alias_t_bot_intent.intent_type_cd = '' AND ($sql_type_cd))
			OR ($alias_t_bot_intent.intent_type_cd != '' AND ($sql_type_cd_t))";
		}
		// error_log("create_intent_type_cd_sql() for $source() " . " type_cd=" . $type_cd . " alias_m_bot_intent=" . $alias_m_bot_intent);
		// error_log($sql_type_cd);
		return $sql_type_cd;
	}

	// 2022.09.06 begin
	function create_sql_for_single_code($source,$alias_field, $value) {
		// $value只包含1个cd,需要存在于DB
		//return " AND MATCH($alias_field) AGAINST('$value' IN BOOLEAN MODE)";
		error_log("coming create_sql_for_single_code($source,$alias_field, $value)");
		return " AND concat(' ', $alias_field, ' ') like concat('% ', '$value', ' %')";
	}

	function create_sql_for_multi_code_exist_all($source,$alias_field, $value) {
		// $value包含的各cd必须都存在于DB
		error_log("coming create_sql_for_multi_code_exist_all($source,$alias_field, $value)");
		$parts = explode(",", $value);
		$num = count($parts);
		//return " AND MATCH($alias_field) AGAINST('$value' IN BOOLEAN MODE) = $num ";
		$sql = "";
		for ($i=0; $i<count($parts); $i++) {
			$cur_cd = $parts[$i];
			$cur_sql =  " concat(' ', $alias_field, ' ') like concat('% ', '$cur_cd', ' %')";
			if ($i == 0) {
				$sql = $cur_sql;
			} else {
				$sql = "$sql 
				AND $cur_sql";
			}
		}
		// error_log("sql is $sql ");
		return " AND ( $sql ) ";
	}
	
	function create_sql_for_multi_code_exist_atleast_one($source,$alias_field, $value) {
		// $value包含的各cd只要有一个存在于DB
		error_log("coming create_sql_for_multi_code_exist_atleast_one($source,$alias_field, $value)");
		//myIsam return " AND MATCH($alias_field) AGAINST('$value' IN BOOLEAN MODE) ";
		// for innodb
		$parts = explode(",", $value);
		$num = count($parts);
		$sql = "";
		for ($i=0; $i<count($parts); $i++) {
			$cur_cd = $parts[$i];
			$cur_sql =  " concat(' ', $alias_field, ' ') like concat('% ', '$cur_cd', ' %')";
			if ($i == 0) {
				$sql = $cur_sql;
			} else {
				$sql = "$sql 
				OR $cur_sql";
			}
		}
		//error_log("sql is $sql ");
		return " AND ( $sql ) ";
	}
	// 2022.09.06 end

	// 2022.09.14 begin #31551
    private function is_event_date_between_t_item_event_date($event_date, $t_item_event_date) {
		error_log("coming is_event_date_between_t_item_event_date() event_date=$event_date t_item_event_date=$t_item_event_date");
        $event_date_obj = json_decode($event_date);
        if ($event_date_obj == null) {
            $event_date_begin = substr($event_date, 0, 10);
            $event_date_end = $event_date_begin;
        } else {
            $ret = json_encode($event_date_obj);
            $event_date_begin = substr($event_date_obj->startDate, 0, 10);
            $event_date_end = substr($event_date_obj->endDate, 0, 10);
        }

		error_log("event_date_begin is $event_date_begin");
		error_log("event_date_end is $event_date_end");
        $array_t_item_event_date = explode(" ", $t_item_event_date);
        for ($i=0; $i<count($array_t_item_event_date); $i++) {
            $cur_t_item_event_date = $array_t_item_event_date[$i];
            $array_cur_t_item_event_date = explode("~", $cur_t_item_event_date);
            if (count($array_cur_t_item_event_date) == 1) {
                $cur_t_item_event_date_begin = $cur_t_item_event_date;
                $cur_t_item_event_date_end = $cur_t_item_event_date_begin;
            } else {
                $cur_t_item_event_date_begin = $array_cur_t_item_event_date[0];
                $cur_t_item_event_date_end = $array_cur_t_item_event_date[1];
            }

			error_log("cur_t_item_event_date_begin is $cur_t_item_event_date_begin");
            error_log("cur_t_item_event_date_end is $cur_t_item_event_date_end");

            if ( strcmp($event_date_begin, $cur_t_item_event_date_end) >0 || strcmp($event_date_end, $cur_t_item_event_date_begin) <0) {
                // out
                error_log("result is out");
                continue;
            } else {
                error_log("result is in");
				return 1;
            }
        }
		error_log("final result is out");
		return 0;
    }
	// 2022.09.14 end #31551

	function get_intents_keyword($bot_id, $lang_cd, $user_input, $type_cd)
	{
		// 对指定关键字检索t表的hit_keyword1,2,3,4,5
		// engine使用者
		//	通过api_get(t_bot_intent_hit_keywords_search)
		//		flow.js:identify_intent_by_bot_keyword_search() 关键字检索t表的hit_keyword1,2,3,4,5识别意图
		
		$sql_type_cd = $this->create_intent_type_cd_sql("get_intents_keyword", $type_cd, "a", "b");

		// 2021.12.13 #19261 t_bot_intent增加show_survey字段
		$sql = "SELECT b.bot_id,b.intent_cd,b.lang_cd,a.question,b.facility_question_title,b.sub_intent_cd,b.area_cd,b.show_survey
				FROM m_bot_intent a
				INNER JOIN t_bot_intent b
					ON a.intent_cd = b.intent_cd
					AND a.lang_cd = b.lang_cd
					AND a.sub_intent_cd = b.sub_intent_cd
				WHERE
					b.bot_id = :bot_id
					AND a.level <> 4
					AND b.lang_cd=:lang_cd
					AND (
							(b.hit_keyword1 <> '' OR b.hit_keyword2 <> '' OR b.hit_keyword3 <> '' OR b.hit_keyword4 <> '' OR b.hit_keyword5 <> '')
							AND
							(b.hit_keyword1 is NULL OR b.hit_keyword1 = '' OR :user_input like concat('%',b.hit_keyword1,'%'))
							AND
							(b.hit_keyword2 is NULL OR b.hit_keyword2 = '' OR :user_input like concat('%',b.hit_keyword2,'%'))
							AND
							(b.hit_keyword3 is NULL OR b.hit_keyword3 = '' OR :user_input like concat('%',b.hit_keyword3,'%'))
							AND
							(b.hit_keyword4 is NULL OR b.hit_keyword4 = '' OR :user_input like concat('%',b.hit_keyword4,'%'))
							AND
							(b.hit_keyword5 is NULL OR b.hit_keyword5 = '' OR :user_input like concat('%',b.hit_keyword5,'%'))
					)
					AND ($sql_type_cd)
				ORDER BY b.intent_cd ";

		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':bot_id' => $bot_id,
			':lang_cd' => $lang_cd,
			':user_input' => $user_input,
			':type_cd' => $type_cd,
		));
		$results = $query->execute();
		return $results->as_array();
	}

	function get_intents_by_intent_cd($bot_info, $condition)
	{
		// 对指定关键字检索出指定intent_cd的所有sub_intent_cd
		// engine使用者
		//	通过api_get(search_sub_intent)
		//		split_intent.js:
		
		$type_cd = "";
		if (isset($condition["type_cd"])) {
			$type_cd = $condition["type_cd"];
		}

		// 查找所有同义词
		// 2021.11.27 m_bot_synonym增加bot_id
		$bot_id = $bot_info['bot_id'];
		$parent_bot_id = $this->get_grp_parent_bot_id($bot_id);
		if ($parent_bot_id == -1) {
			$parent_bot_id = $bot_id;
		}
		$base_bot_id = 0;
		$keywords_with_synonym = $condition['keywords'];
		$sql = "SELECT concat(replace(:keywords_with_synonym,' ',','),',',replace(group_concat(synonym),' ',',')) AS SYNONYM_RET
				FROM `m_bot_synonym` 
				WHERE 
				    bot_id in (:bot_id, :base_bot_id, :parent_bot_id)
					AND LANG_CD = :lang_cd
					AND MATCH(synonym) AGAINST(replace(:keywords_with_synonym,' ',',') IN BOOLEAN MODE)
				";
				//AND find_in_set(example,replace(:keywords_with_synonym,' ',',')) >0
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':bot_id' => $bot_id,
			':parent_bot_id' => $parent_bot_id,
			':base_bot_id' => $base_bot_id,

			':lang_cd' => $condition['lang_cd'],
			':keywords_with_synonym' => $keywords_with_synonym,
		));
		$results = $query->execute()->as_array();
		if (count($results) > 0 && $results[0]["SYNONYM_RET"] != "") {
			$keywords_with_synonym = $results[0]["SYNONYM_RET"];
		}
		$keywords_with_synonym = str_replace(" ",',',$keywords_with_synonym);
		
		$sql_type_cd = $this->create_intent_type_cd_sql("get_intents_by_intent_cd", $type_cd, "a", "b");
		if ($this->_full_index_another_table == "false") {
			$sql = "SELECT
					b.bot_id,b.intent_cd,b.lang_cd,a.question,b.facility_question_title,b.sub_intent_cd,b.area_cd,b.show_survey,
					(length(a.question_ft)-length(replace(a.question_ft,' ','')) + 1) as keyword_num,
					MATCH(a.question_ft) AGAINST(:keywords IN BOOLEAN MODE) 
					+ (case when (select find_in_set(a.ft_keyword1,:keywords_with_synonym)) > 0 then a.ft_keyword1_weight else 0 end ) 
					+ (case when (select find_in_set(a.ft_keyword2,:keywords_with_synonym)) > 0 then a.ft_keyword2_weight else 0 end ) 
					+ (case when (select find_in_set(a.ft_keyword3,:keywords_with_synonym)) > 0 then a.ft_keyword3_weight else 0 end ) 
					+ MATCH(b.additional_info_for_search) AGAINST(:keywords_with_synonym IN BOOLEAN MODE) 
					as score1,
					MATCH(b.answer_ft) AGAINST(:keywords IN BOOLEAN MODE) as score2
				FROM m_bot_intent a
				INNER JOIN t_bot_intent b
					ON a.intent_cd = b.intent_cd
					AND a.lang_cd = b.lang_cd
					AND a.sub_intent_cd = b.sub_intent_cd
				WHERE
					b.bot_id = :bot_id
					AND a.level <> 4
					AND b.intent_cd = :intent_cd
					AND b.lang_cd=:lang_cd
					AND ($sql_type_cd)
				ORDER BY
					score1 DESC,
					score2 DESC,
					keyword_num,
					b.intent_cd ";
		} else {
			$sql = "SELECT
					b.bot_id,b.intent_cd,b.lang_cd,a.question,b.facility_question_title,b.sub_intent_cd,b.area_cd,b.show_survey,
					(length(c.question_ft)-length(replace(c.question_ft,' ','')) + 1) as keyword_num,
					MATCH(c.question_ft) AGAINST(:keywords IN BOOLEAN MODE) 
					+ (case when (select find_in_set(a.ft_keyword1,:keywords_with_synonym)) > 0 then a.ft_keyword1_weight else 0 end ) 
					+ (case when (select find_in_set(a.ft_keyword2,:keywords_with_synonym)) > 0 then a.ft_keyword2_weight else 0 end ) 
					+ (case when (select find_in_set(a.ft_keyword3,:keywords_with_synonym)) > 0 then a.ft_keyword3_weight else 0 end ) 
					+ MATCH(d.additional_info_for_search) AGAINST(:keywords_with_synonym IN BOOLEAN MODE) 
					as score1,
					MATCH(d.answer_ft) AGAINST(:keywords IN BOOLEAN MODE) as score2
				FROM m_bot_intent a
				INNER JOIN t_bot_intent b
					ON a.intent_cd = b.intent_cd
					AND a.lang_cd = b.lang_cd
					AND a.sub_intent_cd = b.sub_intent_cd
				INNER JOIN m_bot_intent_fulltext c
					ON a.intent_class_cd = c.intent_class_cd
					AND a.intent_type_cd = c.intent_type_cd
					AND a.intent_cd = c.intent_cd
					AND a.sub_intent_cd = c.sub_intent_cd
					AND a.lang_cd = c.lang_cd
				INNER JOIN t_bot_intent_fulltext d
					ON b.bot_id = d.bot_id
					AND b.intent_cd = d.intent_cd
					AND b.sub_intent_cd = d.sub_intent_cd
					AND b.area_cd = d.area_cd
					AND b.lang_cd = d.lang_cd
				WHERE
					b.bot_id = :bot_id
					AND a.level <> 4
					AND b.intent_cd = :intent_cd
					AND b.lang_cd=:lang_cd
					AND ($sql_type_cd)
				ORDER BY
					score1 DESC,
					score2 DESC,
					keyword_num,
					b.intent_cd ";
		}


		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':bot_id' => $bot_info['bot_id'],
			':intent_cd' => $condition['intent_cd'],
			':lang_cd' => $condition['lang_cd'],
			':keywords' => $condition['keywords'],
			':keywords_with_synonym' => $keywords_with_synonym,
			':type_cd' => $type_cd,
		));
		$results = $query->execute();
		return $results->as_array();
	}

	function get_time_diff($t1,$t2)
	{
		return "+".round($t2-$t1,3)." seconds";
	}

	// wuzhao add begin #111
	// 2022.01.21 #20895 增加limit参数
	function get_intent_list_aimai($lang_cd, $bot_id, $keywords, $original_intent_cd="", $type_cd="", $limit=11)
	{
		$parent_bot_id = $this->get_grp_parent_bot_id($bot_id);
		if ($parent_bot_id == -1) {
			$parent_bot_id = $bot_id;
		}
		$base_bot_id = 0;

		//error_log("comming in get_intent_list_aimai");
		//error_log("lang_cd=$lang_cd");
		//error_log("bot_id=$bot_id");
		//error_log("keywords=$keywords");
		//error_log("original_intent_cd=$original_intent_cd");
		//error_log("type_cd=$type_cd");
		// 根据用户输入文本全文检索m，t表,按score排序
		// engine使用者
		//	通过botapi.js get_intents_by_bot_question_answer_aimai_search
		//		flow.js:关键字检索m表的ft_keyword1,2,3识别意图
		
		// 查找所有同义词
 // Log::instance()->add(Log::DEBUG, "begin query get_intent_list_aimai");
 // $begin_time = microtime(true);
 // Log::instance()->add(Log::DEBUG, $lang_cd);
 // Log::instance()->add(Log::DEBUG, $bot_id);
 // Log::instance()->add(Log::DEBUG, $keywords);
		$keywords_with_synonym = $keywords;
		$sql = "SELECT concat(replace(:keywords_with_synonym,' ',','),',',replace(group_concat(synonym),' ',',')) AS SYNONYM_RET
				FROM `m_bot_synonym` 
				WHERE 
				bot_id in (:bot_id, :base_bot_id, :parent_bot_id)
				AND LANG_CD = :lang_cd
				AND MATCH(synonym) AGAINST(replace(:keywords_with_synonym,' ',',') IN BOOLEAN MODE)
				";
				// AND find_in_set(example,replace(:keywords_with_synonym,' ',',')) >0
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':bot_id' => $bot_id,
			':parent_bot_id' => $parent_bot_id,
			':base_bot_id' => $base_bot_id,

			':lang_cd' => $lang_cd,
			':keywords_with_synonym' => $keywords_with_synonym,
		));
		$results = $query->execute()->as_array();
		if (count($results) > 0 && $results[0]["SYNONYM_RET"] != "") {
			$keywords_with_synonym = $results[0]["SYNONYM_RET"];
		}
		$keywords_with_synonym = str_replace(" ",',',$keywords_with_synonym);
		
 // Log::instance()->add(Log::DEBUG, $this->get_time_diff($begin_time, microtime(true)));
 // Log::instance()->add(Log::DEBUG, $keywords_with_synonym);
		// 2020.12.3 #6889
		$template_bot_id = $this->get_template_bot_id($bot_id);
		if ($bot_id == $parent_bot_id) {
			$sql_bot_id = "b.bot_id = :bot_id";
		} else {
			// 当前为子bot，就同时检索父bot，并且当父bot和子BOT里都有记录时过滤掉父bot的记录
			// 2022.03.30 项文静：子ボットにて曖昧検索結果を表示される時に、テンプレートボットに登録されているFAQの回答も表示されたいですが、可能でしょうか
			// 2022.06.25 #28022
			$sql_bot_id = "	(b.bot_id = :bot_id 

							  or 

			                  (:template_bot_id != ''
							    AND :template_bot_id != :bot_id
								AND b.bot_id = :template_bot_id 
								AND b.inherit = 1
								AND NOT EXISTS 
									(SELECT * FROM t_bot_intent c 
									WHERE c.bot_id=:bot_id  
									AND c.intent_cd=b.intent_cd 
									AND c.sub_intent_cd = b.sub_intent_cd
									AND c.area_cd = b.area_cd
									AND c.lang_cd = b.lang_cd)
							  )

							  or 

							  (b.bot_id = :parent_bot_id 
								AND b.inherit = 1
								AND NOT EXISTS 
									(SELECT * FROM t_bot_intent c 
									WHERE c.bot_id=:bot_id  
									AND c.intent_cd=b.intent_cd 
									AND c.sub_intent_cd = b.sub_intent_cd
									AND c.area_cd = b.area_cd
									AND c.lang_cd = b.lang_cd)
								AND NOT EXISTS 
									(SELECT * FROM t_bot_intent c 
									WHERE c.bot_id=:template_bot_id  
									AND c.intent_cd=b.intent_cd 
									AND c.sub_intent_cd = b.sub_intent_cd
									AND c.area_cd = b.area_cd
									AND c.lang_cd = b.lang_cd)
							  )
							)";
		}
		// 2020.12.3 #6889 end

		$sql_type_cd = $this->create_intent_type_cd_sql("get_intent_list_aimai", $type_cd, "a", "b");
        if ($this->_full_index_another_table == "false") {
			$sql = "SELECT a.question,a.level,
				(length(a.question_ft)-length(replace(a.question_ft,' ','')) + 1) as keyword_num,
				MATCH(a.question_ft) AGAINST(:keywords IN BOOLEAN MODE) 
				+ (case when (select find_in_set(a.ft_keyword1,:keywords_with_synonym)) > 0 then a.ft_keyword1_weight else 0 end ) 
				+ (case when (select find_in_set(a.ft_keyword2,:keywords_with_synonym)) > 0 then a.ft_keyword2_weight else 0 end ) 
				+ (case when (select find_in_set(a.ft_keyword3,:keywords_with_synonym)) > 0 then a.ft_keyword3_weight else 0 end ) 
				+ MATCH(b.additional_info_for_search) AGAINST(:keywords_with_synonym IN BOOLEAN MODE) 
				as score1,
				MATCH(b.answer_ft) AGAINST(:keywords IN BOOLEAN MODE) as score2,
				b.*
				FROM m_bot_intent a
				INNER JOIN t_bot_intent b
					ON a.intent_cd = b.intent_cd
					AND a.lang_cd = b.lang_cd
					AND a.sub_intent_cd = b.sub_intent_cd
				WHERE
					$sql_bot_id
					AND a.level <> 4
					AND b.lang_cd=:lang_cd
					AND b.intent_cd<>:original_intent_cd
					AND (
							MATCH(b.question_answer_ft) AGAINST(:keywords IN BOOLEAN MODE)
							OR MATCH(b.additional_info_for_search) AGAINST(:keywords_with_synonym IN BOOLEAN MODE)
						)
					AND ($sql_type_cd)
				ORDER BY
					score1 DESC,
					score2 DESC,
					keyword_num,
					a.intent_class_cd,
					a.intent_type_cd,
					a.intent_cd,
					a.sub_intent_cd
				LIMIT $limit
				";
		} else {
			$sql = "SELECT a.question,a.level,
				(length(c.question_ft)-length(replace(c.question_ft,' ','')) + 1) as keyword_num,
				MATCH(c.question_ft) AGAINST(:keywords IN BOOLEAN MODE) 
				+ (case when (select find_in_set(a.ft_keyword1,:keywords_with_synonym)) > 0 then a.ft_keyword1_weight else 0 end ) 
				+ (case when (select find_in_set(a.ft_keyword2,:keywords_with_synonym)) > 0 then a.ft_keyword2_weight else 0 end ) 
				+ (case when (select find_in_set(a.ft_keyword3,:keywords_with_synonym)) > 0 then a.ft_keyword3_weight else 0 end ) 
				+ MATCH(d.additional_info_for_search) AGAINST(:keywords_with_synonym IN BOOLEAN MODE) 
				as score1,
				MATCH(d.answer_ft) AGAINST(:keywords IN BOOLEAN MODE) as score2,
				b.*
				FROM m_bot_intent a
				INNER JOIN t_bot_intent b
					ON a.intent_cd = b.intent_cd
					AND a.lang_cd = b.lang_cd
					AND a.sub_intent_cd = b.sub_intent_cd
				INNER JOIN m_bot_intent_fulltext c
					ON a.intent_class_cd = c.intent_class_cd
					AND a.intent_type_cd = c.intent_type_cd
					AND a.intent_cd = c.intent_cd
					AND a.sub_intent_cd = c.sub_intent_cd
					AND a.lang_cd = c.lang_cd
				INNER JOIN t_bot_intent_fulltext d
					ON b.bot_id = d.bot_id
					AND b.intent_cd = d.intent_cd
					AND b.sub_intent_cd = d.sub_intent_cd
					AND b.area_cd = d.area_cd
					AND b.lang_cd = d.lang_cd
				WHERE
					$sql_bot_id
					AND a.level <> 4
					AND b.lang_cd=:lang_cd
					AND b.intent_cd<>:original_intent_cd
					AND (
							MATCH(d.question_answer_ft) AGAINST(:keywords IN BOOLEAN MODE)
							OR MATCH(d.additional_info_for_search) AGAINST(:keywords_with_synonym IN BOOLEAN MODE)
						)
					AND ($sql_type_cd)
				ORDER BY
					score1 DESC,
					score2 DESC,
					keyword_num,
					a.intent_class_cd,
					a.intent_type_cd,
					a.intent_cd,
					a.sub_intent_cd
				LIMIT $limit
				";
		}

		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':bot_id' => $bot_id,
			':parent_bot_id' => $parent_bot_id,
			':template_bot_id' => $template_bot_id,
			':keywords' => $keywords,
			':lang_cd' => $lang_cd,
			':original_intent_cd' =>$original_intent_cd,
			':keywords_with_synonym' => $keywords_with_synonym,
			':type_cd' => $type_cd,
		));
		$results = $query->execute();
 // Log::instance()->add(Log::DEBUG, $this->get_time_diff($begin_time, microtime(true)));
 // Log::instance()->add(Log::DEBUG, "end query get_intent_list_aimai");
		return $results->as_array();
	}

	// wuzhao add begin #201
	function get_intent_relation($bot_id, $intent_cd, $lang_cd, $type_cd)
	{
		// 根据用户输入文本全文检索m，t表,按score排序
		// engine使用者
		//	通过api_get(action_get_intent_relation)
		//		inquiry.js :

		$sql = "SELECT a.intent_ft
				FROM m_intent_relation a
				WHERE
					MATCH(a.intent_ft) AGAINST(:intent_cd IN BOOLEAN MODE)
				";
//					a.intent_ft LIKE :keyword

		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
//			':keyword' => '%' . $intent_cd . '%',
			':intent_cd' => $intent_cd,
		));

		$results = $query->execute()->as_array();
		if (count($results) > 0) {
			$intents = $results[0]['intent_ft'];
			$intents = str_replace(" ", "','inquiry.", $intents);
			$intents = "'inquiry." . $intents . "'";

			$sql_type_cd = $this->create_intent_type_cd_sql("get_intent_relation", $type_cd, "a", "b");
			$sql = "SELECT b.bot_id,b.intent_cd,b.lang_cd,a.question,b.facility_question_title,b.sub_intent_cd,b.area_cd,b.show_survey
			FROM m_bot_intent a
			INNER JOIN t_bot_intent b
				ON a.intent_cd = b.intent_cd
				AND a.lang_cd = b.lang_cd
				AND a.sub_intent_cd = b.sub_intent_cd
			WHERE
				b.bot_id = :bot_id
				AND a.level <> 4
				AND b.lang_cd=:lang_cd
				AND b.intent_cd<>:original_intent_cd
				AND b.intent_cd IN ($intents)
				AND ($sql_type_cd)
			LIMIT 6
			";

			$query = DB::query(Database::SELECT, $sql);
			$query->parameters(array(
				':bot_id' => $bot_id,
				':lang_cd' => $lang_cd,
				':original_intent_cd' => 'inquiry.' . $intent_cd,
				':type_cd' => $type_cd,
			));
			$results = $query->execute();

			return $results->as_array();
		} else {
			return NULL;
		}
	}
	// wuzhao add end #201

	// wuzhao 2020.01.15 #2809
	function is_exist_faq_definition($condition) {
		$bot_id = $condition["bot_id"];
		$lang_cd = $condition["lang_cd"];
		$intent_cd = $condition["intent_cd"];
		$sub_intent_cd = "";
		if (isset($condition["sub_intent_cd"])) {
			$sub_intent_cd = $condition["sub_intent_cd"];
		}
		// 2020.01.17 暂时去掉这个条件，否则好多出不来 AND a.sub_intent_cd = :sub_intent_cd

		$sql = "SELECT a.*
				FROM m_bot_intent a
				WHERE
					a.intent_cd = :intent_cd
					AND a.lang_cd = :lang_cd
					AND a.level <> 4
					AND (
						  a.level != 3
						  OR
						  (a.item_ids = '' OR a.item_ids IS NULL)
						  OR
						  find_in_set(:bot_id,a.item_ids) > 0
						)
				";

		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':bot_id' => $bot_id,
			':lang_cd' => $lang_cd,
			':intent_cd' => $intent_cd,
			':sub_intent_cd' => $sub_intent_cd,
		));

		$results = $query->execute()->as_array();
		
		return $results;		
	}
	// wuzhao 2020.01.15 #2809 end

	function get_faq($post, $bot_info)
	{
		// 根据指定intent_cd,sub_intent_cd，area_cd检索出inquiry
		// engine使用者
		//	通过botapi.js search_faq(),common_func.js search_faq()
		//		flow.js :
		//		inquiry.js :
		//		inquiry_detail.js :
		//		inquiry_select_echo.js :
		
		$sub_intent_cd = "";
		if (isset($post["sub_intent_cd"])) {
			$sub_intent_cd = $post["sub_intent_cd"];
		}
		$area_cd = "";
		if (isset($post["area_cd"])) {
			$area_cd = $post["area_cd"];
		}
		$type_cd = "";
		if (isset($post["type_cd"])) {
			$type_cd = $post["type_cd"];
		}
		$child_bot_list = "";
		if (isset($post["child_bot_list"])) {
			$child_bot_list = $post["child_bot_list"];
		}
		// 2022.06.25 #28022 begin
		$inherit_check = "0";
		if (isset($post["inherit_check"])) {
			$inherit_check = $post["inherit_check"];
		}
		// 2022.06.25 #28022 end

		$sql = "SELECT a.*,b.question,b.default_action_skill,
				case
					when :sub_intent_cd <> '' and :area_cd <> '' and a.sub_intent_cd = :sub_intent_cd and a.area_cd = :area_cd then 999

					when :sub_intent_cd <> '' and :area_cd <> '' and a.sub_intent_cd = :sub_intent_cd and a.area_cd = '' then 998
					when :sub_intent_cd <> '' and :area_cd <> '' and find_in_set(:sub_intent_cd, replace(a.sub_intent_cd,'-',',')) > 0 and a.area_cd = '' then 997
					when :sub_intent_cd <> '' and :area_cd <> '' and a.sub_intent_cd = '' and a.area_cd = :area_cd then 996

					when :sub_intent_cd = '' and :area_cd <> '' and a.sub_intent_cd = '' and a.area_cd = :area_cd then 995
					when :sub_intent_cd <> '' and :area_cd = '' and a.sub_intent_cd = :sub_intent_cd and a.area_cd = '' then 994
					when :sub_intent_cd <> '' and :area_cd = '' and find_in_set(:sub_intent_cd, replace(a.sub_intent_cd,'-',',')) > 0 and a.area_cd = '' then 993

					when :sub_intent_cd = '' and :area_cd = '' and a.sub_intent_cd = '' and a.area_cd = '' then 993

					when :area_cd <> '' and a.area_cd = :area_cd then 992
					when :sub_intent_cd <> '' and a.sub_intent_cd = :sub_intent_cd then 991
					when :sub_intent_cd <> '' and find_in_set(:sub_intent_cd, replace(a.sub_intent_cd,'-',',')) > 0 then 990

					else a.default_sub_intent
				end sort1
				FROM t_bot_intent a
				INNER JOIN m_bot_intent b
					ON a.intent_cd = b.intent_cd
					AND a.lang_cd = b.lang_cd
					AND a.sub_intent_cd = b.sub_intent_cd
					AND b.level <> 4
				WHERE
				";

			if ($child_bot_list == "") {
				$sql = "$sql a.bot_id = :bot_id ";
			} else {
				$sql = "$sql a.bot_id in ($child_bot_list) ";
			}

			// 2022.06.25 #28022 begin
			if ($inherit_check == "1") {
				$sql = "$sql AND a.inherit = 1 ";
			}
			// 2022.06.25 #28022 end

			$sql_type_cd = $this->create_intent_type_cd_sql("get_faq", $type_cd, "b", "a");
			$sql = "$sql 
					AND a.intent_cd = :intent_cd
					AND a.lang_cd = :lang_cd
					AND (
						  :sub_intent_cd = ''
						  OR
						  a.sub_intent_cd = ''
						  OR
						  a.sub_intent_cd = :sub_intent_cd
						  OR
						  find_in_set(:sub_intent_cd, replace(a.sub_intent_cd,'-',',')) > 0
						)
					AND (
						  :area_cd = ''
						  OR
						  a.area_cd = ''
						  OR
						  a.area_cd = :area_cd
						)
					AND ($sql_type_cd)
				ORDER BY
					a.bot_id,
					sort1 DESC,
					a.sub_intent_cd
				";

		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':bot_id' => $bot_info["bot_id"],
			':intent_cd' => $post["intent_cd"],
			':lang_cd' => $post["lang"],
			':sub_intent_cd' => $sub_intent_cd,
			':area_cd' => $area_cd,
			':type_cd' => $type_cd,
			':child_bot_list' => $child_bot_list,
		));

		$results = $query->execute()->as_array();
		$counts = count($results);
		$intent_cd = $post["intent_cd"];
		
		return $results;
	}


	// 此函数已经不用，因为default_action_skill已经移动到t_intent_skill表里了
	function get_default_action_skill($bot_id, $post)
	{
		// 根据指定intent_cd,sub_intent_cd检索出inquiry
		// engine使用者
		//		inquiry.js :
		
		$sub_intent_cd = "";
		if (isset($post["sub_intent_cd"])) {
			$sub_intent_cd = $post["sub_intent_cd"];
		}
		$type_cd = "";
		if (isset($post["type_cd"])) {
			$type_cd = $post["type_cd"];
		}
		
		$sql_type_cd = $this->create_intent_type_cd_sql("get_default_action_skill", $type_cd, "b");
		$sql = "SELECT b.default_action_skill,
				case
					when :sub_intent_cd <> '' and b.sub_intent_cd = :sub_intent_cd then 999

					when :sub_intent_cd <> '' and find_in_set(:sub_intent_cd, replace(b.sub_intent_cd,'-',',')) > 0 then 997
					when :sub_intent_cd <> '' and b.sub_intent_cd = '' then 996

					when :sub_intent_cd = '' and b.sub_intent_cd = '' then 995
					when :sub_intent_cd <> '' and b.sub_intent_cd = :sub_intent_cd then 994
					when :sub_intent_cd <> '' and find_in_set(:sub_intent_cd, replace(b.sub_intent_cd,'-',',')) > 0 then 993

					when :sub_intent_cd <> '' and b.sub_intent_cd = :sub_intent_cd then 991
					when :sub_intent_cd <> '' and find_in_set(:sub_intent_cd, replace(b.sub_intent_cd,'-',',')) > 0 then 990

					else 0
				end sort1
				FROM m_bot_intent b
				INNER JOIN t_bot t
					ON t.bot_id = :bot_id
					AND t.bot_class_cd = b.intent_class_cd
				WHERE
					b.intent_cd = :intent_cd
					AND b.level <> 4
					AND b.lang_cd = :lang_cd
					AND (
						  :sub_intent_cd = ''
						  OR
						  b.sub_intent_cd = ''
						  OR
						  b.sub_intent_cd = :sub_intent_cd
						  OR
						  find_in_set(:sub_intent_cd, replace(b.sub_intent_cd,'-',',')) > 0
						)
					AND ($sql_type_cd)
				ORDER BY
					sort1 DESC,
					b.sub_intent_cd
				";

		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':bot_id' => $bot_id,
			':intent_cd' => $post["intent_cd"],
			':lang_cd' => $post["lang"],
			':sub_intent_cd' => $sub_intent_cd,
			':type_cd' => $type_cd,
		));

		$results = $query->execute();
		return $results->as_array();
	}

	// $type_cd是单个cd
	// 2021.07.22 改善 #14586 選択式ボットの質問のソート順にしたい。
	function search_inquiry_by_type($lang_cd, $bot_id, $type_cd)
	{
		// 2022.06.25 #28022 begin
		$bot_id_origin = $bot_id;
		$parent_bot_id = $this->get_grp_parent_bot_id($bot_id_origin);
		$sql_inherit = "";
		// 2022.06.25 #28022 end
		// $bot_id = $this->get_all_bot_id_list_for_group_parent_bot($bot_id);
		$bot_id = $this->get_all_bot_id_list_for_group_bot($bot_id); // 2021.06.14 改为当前为子的时候，也查父bot
		// 2022.06.25 #28022 begin
		if ($parent_bot_id != -1) {
			// group bot
			if ($parent_bot_id == $bot_id_origin) {
				// 父bot
			} else if ($parent_bot_id != $bot_id_origin) {
				// 是子bot,需要过滤掉父bot的不可继承的intent
				$sql_inherit = "AND (b.bot_id <> :parent_bot_id OR b.inherit = 1) ";
			}
		}
		// 2022.06.25 #28022 end
		
		$sql = "SELECT distinct a.intent_cd,a.lang_cd,a.question,
				b.facility_question_title,a.sub_intent_cd,'' as area_cd, b.sort_no,b.show_survey
				FROM m_bot_intent a
				INNER JOIN t_bot_intent b
					ON a.intent_cd = b.intent_cd
					AND a.lang_cd = b.lang_cd
					AND a.sub_intent_cd = b.sub_intent_cd
				WHERE
					b.bot_id in ($bot_id)
					$sql_inherit
					AND a.level <> 4
					AND b.lang_cd=:lang_cd
					AND (
						   (b.intent_type_cd = '' AND concat(',',a.intent_type_cd) like concat('%,',:type_cd, '%'))
					    OR (b.intent_type_cd != '' AND concat(',',b.intent_type_cd) like concat('%,',:type_cd, '%'))
						)
				ORDER BY b.sort_no asc, a.intent_cd
				";

		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':bot_id' => $bot_id,
			':parent_bot_id' => $parent_bot_id,
			':lang_cd' => $lang_cd,
			':type_cd' =>$type_cd,
		));
		$results = $query->execute();
		return $results->as_array();
	}

	function get_intent_ques_by_cd($bot_id, $intent_cd, $lang_cd) {
		$sql = "SELECT facility_question_title FROM t_bot_intent 
			WHERE bot_id=:bot_id AND intent_cd=:intent_cd AND lang_cd=:lang_cd";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
				':intent_cd' => $intent_cd,
				':lang_cd' => $lang_cd,
		));
		$results = $query->execute()->as_array();
		if (count($results) > 0) return $results[0]['facility_question_title'];
		else return "";
	}
	
	// 使用者：recommend_facility_cd.js
	// 根据用户输入检索匹配全process_mode的keyword的item
	function get_items_by_keyword($condition)
	{
		// 如果用户输入的文本能匹配到t_keyword.keyword(以逗号隔开的多个关键字)里的某一个关键字
		// 或者用户输入的文本包含t_keyword.hit_keyword1-5里的指定的所有关键字
		// 那么就取t_keyword.find_id对应的t_item_description
		// engine使用者
		//	通过botapi.get_recommend_facility_cd
		//		recommend_facility_cd.js
		//		flow.js
		$lang_cd = $condition["lang_cd"];
		$keyword = $condition["keyword"];
		$bot_id = $condition["bot_id"];
		$mode = "";
		if (array_key_exists("mode", $condition)) {
			$mode = $condition["mode"];
		}		

		$original_bot_id = $bot_id;
		$bot_id = $this->get_bot_id_list_include_template_bot($bot_id);
		$template_bot_id = $this->get_template_bot_id($bot_id);
		$user_input = $keyword;
		$keyword = ",$keyword,";
		// 2020.10.12 #6073
		if ($mode == "") {
			// 默认模式，只取item_status_cd = "01"的数据
			/*
			$sql_status_cd_end_date = 
			" AND (a.end_date is NULL OR DATE_FORMAT(a.end_date,'%Y-%m-%d') >= :cur_date)
			  AND a.item_status_cd in ('01','03','04')
			";
			*/
			// 2021.03.26 #9905改为03,04也显示
			$sql_status_cd_end_date = 
			" AND (a.end_date is NULL 
				OR (a.item_status_cd in ('01','08') AND DATE_FORMAT(a.end_date,'%Y-%m-%d') >= :cur_date)
				OR a.item_status_cd in ('03','04','06','07')
			  )
			  AND a.item_status_cd in ('01','03','04','06','07','08')
			";
		} else {	// $mode="keyword"
			$sql_status_cd_end_date = 
			" AND (a.end_date is NULL 
				OR (a.item_status_cd in ('01','08') AND DATE_FORMAT(a.end_date,'%Y-%m-%d') >= :cur_date)
				OR a.item_status_cd in ('02','03','04','05','06','07')
			  )
			  AND a.item_status_cd in ('01','02','03','04','05','06','07','08')
			";
		}
			
		$sql = "SELECT DISTINCT a.item_id, a.country_cd, a.item_status_cd, a.item_cd, a.item_div, a.class_cd, a.location_lat, a.location_lon, a.link_id, a.end_date, a.item_data,
					a.position_x, a.position_y, a.position_z,
					b.lang_cd, b.item_name item_name, b.sell_point, b.sell_point_line, b.tel, b.url, b.description, b.item_image, b.item_image_thumb, b.reserve1, r.sort_no1,
					b.btn1_name, b.btn1_url, b.btn2_name, b.btn2_url, b.btn3_name, b.btn3_url, b.btn1_url_lang_cd, b.btn2_url_lang_cd, b.btn3_url_lang_cd,
					b.btn1_url_sp, b.btn2_url_sp, b.btn3_url_sp, b.show_image
				FROM t_item a
				INNER JOIN t_item_description b
					ON a.item_id = b.item_id
				INNER JOIN t_item_display r
					ON a.item_id = r.item_id
					AND a.item_div = r.item_div
					AND find_in_set(:lang_cd, r.lang_display)
					AND r.public_flg = 1
				INNER JOIN t_item_keyword k
					ON a.item_id = k.item_id
					AND a.item_div = k.item_div
					AND k.keyword_type = 1
				WHERE
				r.bot_id in ($bot_id)
				AND (r.bot_id = :original_bot_id OR (r.bot_id = :template_bot_id AND r.item_div != 2))
				AND (a.start_date is NULL OR DATE_FORMAT(a.start_date,'%Y-%m-%d') <= :cur_date)
				$sql_status_cd_end_date
				AND (a.regular_start = :regular_date_kara 
					OR a.regular_end = :regular_date_kara 
					OR (a.regular_start <= a.regular_end AND a.regular_start <= :regular_date AND :regular_date <= a.regular_end)
					OR (a.regular_start > a.regular_end AND (a.regular_start <= :regular_date OR :regular_date <= a.regular_end))
				)
				AND	b.lang_cd = :lang_cd
				AND (k.keyword LIKE :keyword
						OR (
							(k.hit_keyword1 <> '' OR k.hit_keyword2 <> '' OR k.hit_keyword3 <> '' OR k.hit_keyword4 <> '' OR k.hit_keyword5 <> '')
							AND
							(k.hit_keyword1 is NULL OR k.hit_keyword1 = '' OR :user_input like concat('%',k.hit_keyword1,'%'))
							AND
							(k.hit_keyword2 is NULL OR k.hit_keyword2 = '' OR :user_input like concat('%',k.hit_keyword2,'%'))
							AND
							(k.hit_keyword3 is NULL OR k.hit_keyword3 = '' OR :user_input like concat('%',k.hit_keyword3,'%'))
							AND
							(k.hit_keyword4 is NULL OR k.hit_keyword4 = '' OR :user_input like concat('%',k.hit_keyword4,'%'))
							AND
							(k.hit_keyword5 is NULL OR k.hit_keyword5 = '' OR :user_input like concat('%',k.hit_keyword5,'%'))
						)
					)
				AND a.delete_flg <> 1 ORDER BY r.sort_no1 ";

		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':item_status_cd' => '01',
			':bot_id' => $bot_id,
			':lang_cd' => $lang_cd,
			':keyword' => '%' . $keyword . '%',
			':user_input' => $user_input,
			':cur_date' => date('Y-m-d'),
			':regular_date' => date('m-d'),
			':regular_date_kara' => "",
			':original_bot_id' => $original_bot_id,
			':template_bot_id' => $template_bot_id,
		));
		$results = $query->execute();
		return $results->as_array();
	}

	// 2021.03.10 根据用户输入检索指定grp父bot的所有子bot(用户输入与子bot的item的关键字能全匹配时)
	function get_items_from_grp_child_bot_by_keyword($condition)
	{
		// 备注：这里不检索template_bot的items
		$lang_cd = $condition["lang_cd"];
		$keyword = $condition["keyword"];
		$bot_id = $condition["bot_id"];	// 父bot_id

		$user_input = $keyword;
		$keyword = ",$keyword,";

		// 取出指定父bot的所有t_item里定义的子bot
		$all_child_bot_array = $this->get_all_child_bot_from_t_item($condition);
		if (count($all_child_bot_array) == 0) {
			// 指定父bot没有子bot
			return null;
		}
		$all_child_bot = $all_child_bot_array[0]["bot_id"];
		for ($i=1; $i<count($all_child_bot_array); $i++) {
			$cur_child_bot = $all_child_bot_array[$i]["bot_id"];
			$all_child_bot = "$all_child_bot,$cur_child_bot";
		}

		// 检索子bot的items
		$sql_status_cd_end_date = 
			" AND (a.end_date is NULL OR DATE_FORMAT(a.end_date,'%Y-%m-%d') >= :cur_date)
			AND a.item_status_cd in ('01','03','04','06','07','08')
			";
		$sql = "SELECT bot_id,group_concat(item_id) AS items FROM

				(SELECT DISTINCT r.bot_id,a.item_id,r.sort_no1
				FROM t_item a
				INNER JOIN t_item_display r
					ON a.item_id = r.item_id
					AND a.item_div = r.item_div
					AND find_in_set(:lang_cd, r.lang_display)
					AND r.public_flg = 1
				INNER JOIN t_item_keyword k
					ON a.item_id = k.item_id
					AND a.item_div = k.item_div
					AND k.keyword_type = 1
				WHERE
					r.bot_id in ($all_child_bot)
					AND (a.start_date is NULL OR DATE_FORMAT(a.start_date,'%Y-%m-%d') <= :cur_date)
					$sql_status_cd_end_date
					AND (a.regular_start = :regular_date_kara 
						OR a.regular_end = :regular_date_kara 
						OR (a.regular_start <= a.regular_end AND a.regular_start <= :regular_date AND :regular_date <= a.regular_end)
						OR (a.regular_start > a.regular_end AND (a.regular_start <= :regular_date OR :regular_date <= a.regular_end))
					)
					AND (k.keyword LIKE :keyword
							OR (
								(k.hit_keyword1 <> '' OR k.hit_keyword2 <> '' OR k.hit_keyword3 <> '' OR k.hit_keyword4 <> '' OR k.hit_keyword5 <> '')
								AND
								(k.hit_keyword1 is NULL OR k.hit_keyword1 = '' OR :user_input like concat('%',k.hit_keyword1,'%'))
								AND
								(k.hit_keyword2 is NULL OR k.hit_keyword2 = '' OR :user_input like concat('%',k.hit_keyword2,'%'))
								AND
								(k.hit_keyword3 is NULL OR k.hit_keyword3 = '' OR :user_input like concat('%',k.hit_keyword3,'%'))
								AND
								(k.hit_keyword4 is NULL OR k.hit_keyword4 = '' OR :user_input like concat('%',k.hit_keyword4,'%'))
								AND
								(k.hit_keyword5 is NULL OR k.hit_keyword5 = '' OR :user_input like concat('%',k.hit_keyword5,'%'))
							)
						)
					AND a.delete_flg <> 1 
				ORDER BY 
			        r.bot_id,r.sort_no1) ct

				GROUP BY
				    bot_id
			";

		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':item_status_cd' => '01',
			':lang_cd' => $lang_cd,
			':keyword' => '%' . $keyword . '%',
			':user_input' => $user_input,
			':cur_date' => date('Y-m-d'),
			':regular_date' => date('m-d'),
			':regular_date_kara' => "",
		));
		$results = $query->execute();
		return $results->as_array();
	}

	// 检索出指定group 父bot_id的所有子bot,lang_cd的所有用于部分匹配用户输入的keyword(process_mode==4)，及相应item信息
	function get_items_keyword_aimai_for_grp_child_bot($condition)
	{
		$bot_id = $condition['bot_id'];
		$lang_cd = $condition['lang_cd'];
		// 只取item_status_cd = "01"的数据
		$sql_status_cd_end_date = 
			" AND (a.end_date is NULL OR DATE_FORMAT(a.end_date,'%Y-%m-%d') >= :cur_date)
			  AND a.item_status_cd in ('01','03','04','06','07','08')
			";

		// 取出指定父bot的所有t_item里定义的子bot
		$all_child_bot_array = $this->get_all_child_bot_from_t_item($condition);
		if (count($all_child_bot_array) == 0) {
			// 指定父bot没有子bot
			return null;
		}
		$all_child_bot = $all_child_bot_array[0]["bot_id"];
		for ($i=1; $i<count($all_child_bot_array); $i++) {
			$cur_child_bot = $all_child_bot_array[$i]["bot_id"];
			$all_child_bot = "$all_child_bot,$cur_child_bot";
		}

		// 检索子bot的关键字
		$sql = "SELECT DISTINCT r.bot_id,a.item_id,k.keyword
				FROM t_item a
				INNER JOIN t_item_display r
					ON a.item_id = r.item_id
					AND a.item_div = r.item_div
					AND find_in_set(:lang_cd, r.lang_display)
					AND r.public_flg = 1
				INNER JOIN t_item_keyword k
					ON a.item_id = k.item_id
					AND a.item_div = k.item_div
					AND k.keyword_type = 1
					AND k.process_mode = 4
				WHERE
					r.bot_id in ($all_child_bot)
					AND (a.start_date is NULL OR DATE_FORMAT(a.start_date,'%Y-%m-%d') <= :cur_date)
					$sql_status_cd_end_date
					AND (a.regular_start = :regular_date_kara 
						OR a.regular_end = :regular_date_kara 
						OR (a.regular_start <= a.regular_end AND a.regular_start <= :regular_date AND :regular_date <= a.regular_end)
						OR (a.regular_start > a.regular_end AND (a.regular_start <= :regular_date OR :regular_date <= a.regular_end))
					)
					AND a.delete_flg <> 1 
				";

		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':item_status_cd' => '01',
			':bot_id' => $bot_id,
			':lang_cd' => $lang_cd,
			':cur_date' => date('Y-m-d'),
			':regular_date' => date('m-d'),
			':regular_date_kara' => "",
		));
		$results = $query->execute();
		return $results->as_array();
	}

	function get_item_description_aimai_for_grp_child_bot($condition)
	{
		$bot_id = $condition['bot_id'];
		$lang_cd = $condition['lang_cd'];
		$keywords = $condition['keywords'];

		$sql_status_cd_end_date = 
			" AND (a.end_date is NULL OR DATE_FORMAT(a.end_date,'%Y-%m-%d') >= :cur_date)
			AND a.item_status_cd in ('01','03','04','06','07','08')
			";

		// 取出指定父bot的所有t_item里定义的子bot
		$all_child_bot_array = $this->get_all_child_bot_from_t_item($condition);
		if (count($all_child_bot_array) == 0) {
			// 指定父bot没有子bot
			return null;
		}
		$all_child_bot = $all_child_bot_array[0]["bot_id"];
		for ($i=1; $i<count($all_child_bot_array); $i++) {
			$cur_child_bot = $all_child_bot_array[$i]["bot_id"];
			$all_child_bot = "$all_child_bot,$cur_child_bot";
		}

		if ($this->_full_index_another_table == "false") {
		$sql = "SELECT bot_id,group_concat(item_id) AS items
				FROM
				(
					SELECT DISTINCT r.bot_id,a.item_id,r.sort_no1
					FROM t_item a
					INNER JOIN t_item_description b
						ON a.item_id = b.item_id
					INNER JOIN t_item_display r
						ON a.item_id = r.item_id
						AND a.item_div = r.item_div
						AND find_in_set(:lang_cd, r.lang_display)
						AND r.public_flg = 1
					WHERE
					r.bot_id in ($all_child_bot)
					AND (a.start_date is NULL OR DATE_FORMAT(a.start_date,'%Y-%m-%d') <= :cur_date)
					$sql_status_cd_end_date
					AND (a.regular_start = :regular_date_kara 
						OR a.regular_end = :regular_date_kara 
						OR (a.regular_start <= a.regular_end AND a.regular_start <= :regular_date AND :regular_date <= a.regular_end)
						OR (a.regular_start > a.regular_end AND (a.regular_start <= :regular_date OR :regular_date <= a.regular_end))
						)
					AND	b.lang_cd = :lang_cd
					AND a.delete_flg <> 1
					AND MATCH(b.itemname_sellpoint_ft) AGAINST(:keywords IN BOOLEAN MODE)
					ORDER BY 
			        r.bot_id,r.sort_no1
				) t1
				GROUP BY bot_id
				";
		} else {
		$sql = "SELECT bot_id,group_concat(item_id) AS items
				FROM
				(
					SELECT DISTINCT r.bot_id,a.item_id,r.sort_no1
					FROM t_item a
					INNER JOIN t_item_description b
						ON a.item_id = b.item_id
					INNER JOIN t_item_display r
						ON a.item_id = r.item_id
						AND a.item_div = r.item_div
						AND find_in_set(:lang_cd, r.lang_display)
						AND r.public_flg = 1
					INNER JOIN t_item_description_fulltext d
						ON b.item_id = d.item_id
						AND b.lang_cd = d.lang_cd
					WHERE
					r.bot_id in ($all_child_bot)
					AND (a.start_date is NULL OR DATE_FORMAT(a.start_date,'%Y-%m-%d') <= :cur_date)
					$sql_status_cd_end_date
					AND (a.regular_start = :regular_date_kara 
						OR a.regular_end = :regular_date_kara 
						OR (a.regular_start <= a.regular_end AND a.regular_start <= :regular_date AND :regular_date <= a.regular_end)
						OR (a.regular_start > a.regular_end AND (a.regular_start <= :regular_date OR :regular_date <= a.regular_end))
						)
					AND	b.lang_cd = :lang_cd
					AND a.delete_flg <> 1
					AND MATCH(d.itemname_sellpoint_ft) AGAINST(:keywords IN BOOLEAN MODE)
					ORDER BY 
			        r.bot_id,r.sort_no1
				) t1
				GROUP BY bot_id
				";
		}

		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':item_status_cd' => '01',
			':bot_id' => $bot_id,
			':lang_cd' => $lang_cd,
			':keywords' => $keywords,
			':cur_date' => date('Y-m-d'),
			':regular_date' => date('m-d'),
			':regular_date_kara' => "",
		));

		$results = $query->execute();
		return $results->as_array();
	}	
	// 201.03.10 end

	// 2021.07.01 #13950 根据t_class_mapping调整class_cd参数 begin
	function get_class_cd_mapping($condition)
	{
		$bot_id = $condition['bot_id'];
		$item_div = $condition['item_div'];
		$class_cd = $condition['class_cd'];

		$sql = "SELECT class_cd_mapping
				FROM t_class_mapping a
				WHERE
					a.bot_id = :bot_id
					AND a.item_div = :item_div
					AND a.class_cd_dialogflow = :class_cd
				";

		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':bot_id' => $bot_id,
			':item_div' => $item_div,
			':class_cd' => $class_cd,
		));

		$results = $query->execute();
		return $results->as_array();
	}	
	// 2021.07.01 #13950 根据t_class_mapping调整class_cd参数 end

	// wuzhao add for #1561
	// 2020.07.27 此函数不用了，因为t_item_class和t_item_class_type表已经不存在了
	function get_item_info_by_keyword($bot_info, $facility_cd, $lang_cd)
	{
		$bot_id = $bot_info['bot_id'];

        // 2020.10.12 #6073
		$sql_status_cd_end_date = 
			" AND (a.end_date is NULL OR DATE_FORMAT(a.end_date,'%Y-%m-%d') >= :cur_date)
			AND a.item_status_cd in ('01','03','04','06','07','08')
			";
		$sql = "SELECT DISTINCT 
					k.keyword,
					a.item_id,a.item_class_cd,a.item_class_type_cd, a.item_div, a.class_cd, a.item_status_cd, a.end_date, a.item_data,
					ic.item_class_name,
					ict.item_class_type_name
				FROM t_item a
				INNER JOIN t_item_description b
					ON a.item_id = b.item_id
				INNER JOIN t_item_display r
					ON  a.item_id = r.item_id
					AND a.item_div = r.item_div
					AND find_in_set(:lang_cd, r.lang_display)
					AND r.public_flg = 1
				INNER JOIN t_item_keyword k
					ON a.item_id = k.item_id
					AND a.item_div = k.item_div
					AND k.keyword_type = 1
					AND k.process_mode = 0
				LEFT JOIN t_item_class ic
					ON ic.facility_cd = :facility_cd
				  	AND ic.lang_cd = :lang_cd
					AND ic.item_class_cd = a.item_class_cd
			    LEFT JOIN t_item_class_type ict
					ON ict.facility_cd = :facility_cd
					AND ict.item_class_cd = ic.item_class_cd
					AND ict.lang_cd = :lang_cd
					AND ict.item_class_type_cd = a.item_class_type_cd
				WHERE
					r.bot_id = :bot_id
					AND (a.start_date is NULL OR DATE_FORMAT(a.start_date,'%Y-%m-%d') <= :cur_date)
					$sql_status_cd_end_date
					AND (a.regular_start = :regular_date_kara 
						OR a.regular_end = :regular_date_kara 
						OR (a.regular_start <= a.regular_end AND a.regular_start <= :regular_date AND :regular_date <= a.regular_end)
						OR (a.regular_start > a.regular_end AND (a.regular_start <= :regular_date OR :regular_date <= a.regular_end))
					)
					AND a.delete_flg <> 1 
				";

		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':item_status_cd' => '01',
			':bot_id' => $bot_id,
			':facility_cd' => $facility_cd,
			':lang_cd' => $lang_cd,
			':cur_date' => date('Y-m-d'),
			':regular_date' => date('m-d'),
			':regular_date_kara' => "",
		));
		$results = $query->execute();
		return $results->as_array();
	}

	// wuzhao add for #1561
	// flow.js 根据用户输入文本和t_keyword表来检索t_item表，根据t_item的type名字来替换用户输入文本，并且获得关联items
	function get_item_info_by_keyword_by_class_cd($bot_info, $facility_cd, $code_div, $lang_cd)
	{
		$bot_id = $bot_info['bot_id'];
        // 2020.10.12 #6073
		$sql_status_cd_end_date = 
			" AND (a.end_date is NULL OR DATE_FORMAT(a.end_date,'%Y-%m-%d') >= :cur_date)
			AND a.item_status_cd in ('01','03','04','06','07','08')
			";
		$sql = "SELECT DISTINCT 
					k.keyword,
					a.item_id,a.class_cd, a.item_div, a.class_cd,
					ic.word as name
				FROM t_item a
				INNER JOIN t_item_description b
					ON a.item_id = b.item_id
				INNER JOIN t_item_display r
					ON a.item_id = r.item_id
					AND a.item_div = r.item_div
					AND find_in_set(:lang_cd, r.lang_display)
					AND r.public_flg = 1
				INNER JOIN t_item_keyword k
					ON a.item_id = k.item_id
					AND a.item_div = k.item_div
					AND k.keyword_type = 1
					AND k.process_mode = 0
				INNER JOIN m_class_code ic
					ON ic.code_div = (
						SELECT tbs.setting_value FROM t_bot_setting tbs
						WHERE (tbs.bot_id = a.bot_id OR tbs.bot_id=0)
						AND tbs.setting_cd = concat('div_item_class_',a.item_div)
						ORDER BY tbs.bot_id DESC
						LIMIT 1
					)
				  	AND ic.lang_cd = :lang_cd
					  AND (CASE WHEN LOCATE(' ',a.class_cd) = 0 THEN a.class_cd
							ELSE LEFT(a.class_cd, LOCATE(' ',a.class_cd)-1)
							END) = ic.class_cd
				WHERE
					r.bot_id in ($bot_id)
					AND (a.start_date is NULL OR DATE_FORMAT(a.start_date,'%Y-%m-%d') <= :cur_date)
					$sql_status_cd_end_date
					AND (a.regular_start = :regular_date_kara 
						OR a.regular_end = :regular_date_kara 
						OR (a.regular_start <= a.regular_end AND a.regular_start <= :regular_date AND :regular_date <= a.regular_end)
						OR (a.regular_start > a.regular_end AND (a.regular_start <= :regular_date OR :regular_date <= a.regular_end))
					)
					AND a.delete_flg <> 1 
				";
//				AND concat(' ', a.class_cd, ' ') like concat('% ', ic.class_cd, ' %')

		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':item_status_cd' => '01',
			':bot_id' => $bot_id,
			':facility_cd' => $facility_cd,
			':code_div' => $code_div,
			':lang_cd' => $lang_cd,
			':cur_date' => date('Y-m-d'),
			':regular_date' => date('m-d'),
			':regular_date_kara' => "",
		));
		$results = $query->execute();
		return $results->as_array();
	}

	// flow.js 根据用户输入文本和t_keyword表来检索t_item表，获得access关联items
	function get_item_info_by_keyword_for_access($bot_info, $facility_cd, $lang_cd)
	{
		$bot_id = $bot_info['bot_id'];
        // 2020.10.12 #6073
		$sql_status_cd_end_date = 
			" AND (a.end_date is NULL OR DATE_FORMAT(a.end_date,'%Y-%m-%d') >= :cur_date)
			AND a.item_status_cd in ('01','03','04','06','07','08')
			";
		$sql = "SELECT DISTINCT 
					k.keyword,
					a.item_id,a.class_cd, a.item_div, a.class_cd
				FROM t_item a
				INNER JOIN t_item_description b
					ON a.item_id = b.item_id
				INNER JOIN t_item_display r
					ON a.item_id = r.item_id
					AND a.item_div = r.item_div
					AND find_in_set(:lang_cd, r.lang_display)
					AND r.public_flg = 1
				INNER JOIN t_item_keyword k
					ON a.item_id = k.item_id
					AND a.item_div = k.item_div
					AND k.keyword_type = 1
					AND k.process_mode = 2
				WHERE
					r.bot_id in ($bot_id)
					AND (a.start_date is NULL OR DATE_FORMAT(a.start_date,'%Y-%m-%d') <= :cur_date)
					$sql_status_cd_end_date
					AND (a.regular_start = :regular_date_kara 
						OR a.regular_end = :regular_date_kara 
						OR (a.regular_start <= a.regular_end AND a.regular_start <= :regular_date AND :regular_date <= a.regular_end)
						OR (a.regular_start > a.regular_end AND (a.regular_start <= :regular_date OR :regular_date <= a.regular_end))
					)
					AND a.delete_flg <> 1 
				";

		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':item_status_cd' => '01',
			':bot_id' => $bot_id,
			':facility_cd' => $facility_cd,
			':lang_cd' => $lang_cd,
			':cur_date' => date('Y-m-d'),
			':regular_date' => date('m-d'),
			':regular_date_kara' => "",
		));
		$results = $query->execute();
		return $results->as_array();
	}

	// 此函数用于从用户输入判断出group的子bot一览。这里不看t_item_description和t_item_display
	function get_t_keyword_with_bot_info($condition)
	{
        // 2020.10.12 #6073
		$sql_status_cd_end_date = 
			" AND (a.end_date is NULL OR DATE_FORMAT(a.end_date,'%Y-%m-%d') >= :cur_date)
			AND a.item_status_cd in ('01','03','04','06','07','08')
			";
		$sql = "SELECT DISTINCT 
					k.keyword,
					c.bot_id
				FROM t_item_keyword k
				INNER JOIN t_item a
					ON a.item_id = k.item_id
					AND (a.start_date is NULL OR DATE_FORMAT(a.start_date,'%Y-%m-%d') <= :cur_date)
					$sql_status_cd_end_date
					AND (a.regular_start = :regular_date_kara 
						OR a.regular_end = :regular_date_kara 
						OR (a.regular_start <= a.regular_end AND a.regular_start <= :regular_date AND :regular_date <= a.regular_end)
						OR (a.regular_start > a.regular_end AND (a.regular_start <= :regular_date OR :regular_date <= a.regular_end))
					)
				INNER JOIN t_bot c
					ON a.item_cd = c.facility_cd
				WHERE
					k.item_div = 3
				AND a.item_div = k.item_div
				AND k.keyword_type = 1
				AND process_mode = 3

				AND a.delete_flg <> 1 
				";

		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':item_status_cd' => '01',
			':cur_date' => date('Y-m-d'),
			':regular_date' => date('m-d'),
			':regular_date_kara' => "",
		));
		$results = $query->execute();
		return $results->as_array();
	}

	// 2020.12.27 #7473
	// 此函数用于取出指定父bot的所有t_item里定义的子bot
	function get_all_child_bot_from_t_item($condition)
	{
        // 2020.10.12 #6073
		$sql_status_cd_end_date = 
			" AND (a.end_date is NULL OR DATE_FORMAT(a.end_date,'%Y-%m-%d') >= :cur_date)
			AND a.item_status_cd in ('01','03','04','06','07','08')
			";
		$sql = "SELECT 
					c.bot_id
				FROM  t_item a
				INNER JOIN t_item_display r
					ON a.item_id = r.item_id
					AND a.item_div = r.item_div
					AND r.bot_id = :bot_id
					AND r.public_flg = 1
				INNER JOIN t_bot c
					ON a.item_cd = c.facility_cd
				WHERE
					a.item_div = 3
					AND (a.start_date is NULL OR DATE_FORMAT(a.start_date,'%Y-%m-%d') <= :cur_date)
					$sql_status_cd_end_date
					AND (a.regular_start = :regular_date_kara 
						OR a.regular_end = :regular_date_kara 
						OR (a.regular_start <= a.regular_end AND a.regular_start <= :regular_date AND :regular_date <= a.regular_end)
						OR (a.regular_start > a.regular_end AND (a.regular_start <= :regular_date OR :regular_date <= a.regular_end))
					)
					AND a.delete_flg <> 1 
				ORDER BY
					c.bot_id
				";

		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':bot_id' => $condition["bot_id"],
			':item_status_cd' => '01',
			':cur_date' => date('Y-m-d'),
			':regular_date' => date('m-d'),
			':regular_date_kara' => "",
		));
		$results = $query->execute();
		return $results->as_array();
	}

	// flow.js keyword暧昧检索item功能
	// 检索出指定bot_id,lang_cd的所有用于部分匹配用户输入的keyword(process_mode==4)，及相应item信息
	function get_items_keyword_aimai($condition)
	{
		$original_bot_id = $condition['bot_id'];
		$bot_id = $condition['bot_id'];
		$bot_id = $this->get_bot_id_list_include_template_bot($bot_id);
		$template_bot_id = $this->get_template_bot_id($bot_id);
		$lang_cd = $condition['lang_cd'];
		$mode = "";
		if (array_key_exists("mode", $condition)) {
			$mode = $condition["mode"];
		}		

		// 2020.10.12 #6073
		if ($mode == "") {
			// 默认模式，只取item_status_cd = "01"的数据
			/*
			$sql_status_cd_end_date = 
			" AND (a.end_date is NULL OR DATE_FORMAT(a.end_date,'%Y-%m-%d') >= :cur_date)
			  AND a.item_status_cd in ('01','03','04')
			";
			*/
			// 2021.03.26 #9905改为03,04也显示
			$sql_status_cd_end_date = 
			" AND (a.end_date is NULL 
				OR (a.item_status_cd in ('01','08') AND DATE_FORMAT(a.end_date,'%Y-%m-%d') >= :cur_date)
				OR a.item_status_cd in ('03','04','06','07')
			  )
			  AND a.item_status_cd in ('01','03','04','06','07','08')
			";
		} else {	// mode: "all_item_status_cd"
			$sql_status_cd_end_date = 
			" AND (a.end_date is NULL 
				OR (a.item_status_cd in ('01','08') AND DATE_FORMAT(a.end_date,'%Y-%m-%d') >= :cur_date)
				OR a.item_status_cd in ('02','03','04','05','06','07')
			  )
			  AND a.item_status_cd in ('01','02','03','04','05','06','07','08')
			";
		}

		$sql = "SELECT DISTINCT a.item_id, a.country_cd, a.item_status_cd, a.item_cd, a.item_div, a.class_cd, a.location_lat, a.location_lon, a.link_id, a.end_date, a.item_data,
					a.position_x, a.position_y, a.position_z,
					b.lang_cd, b.item_name item_name, b.sell_point, b.sell_point_line, b.tel, b.url, b.description, b.item_image, b.item_image_thumb, b.reserve1, r.sort_no1,
					b.btn1_name, b.btn1_url, b.btn2_name, b.btn2_url, b.btn3_name, b.btn3_url, b.btn1_url_lang_cd, b.btn2_url_lang_cd, b.btn3_url_lang_cd,
					b.btn1_url_sp, b.btn2_url_sp, b.btn3_url_sp, b.show_image,
					k.keyword
				FROM t_item a
				INNER JOIN t_item_description b
					ON a.item_id = b.item_id
				INNER JOIN t_item_display r
					ON a.item_id = r.item_id
					AND a.item_div = r.item_div
					AND find_in_set(:lang_cd, r.lang_display)
					AND r.public_flg = 1
				INNER JOIN t_item_keyword k
					ON a.item_id = k.item_id
					AND a.item_div = k.item_div
					AND k.keyword_type = 1
					AND k.process_mode = 4
				WHERE
					r.bot_id in ($bot_id)
					AND (r.bot_id = :original_bot_id OR (r.bot_id = :template_bot_id AND r.item_div != 2))
					AND (a.start_date is NULL OR DATE_FORMAT(a.start_date,'%Y-%m-%d') <= :cur_date)
					$sql_status_cd_end_date
					AND (a.regular_start = :regular_date_kara 
						OR a.regular_end = :regular_date_kara 
						OR (a.regular_start <= a.regular_end AND a.regular_start <= :regular_date AND :regular_date <= a.regular_end)
						OR (a.regular_start > a.regular_end AND (a.regular_start <= :regular_date OR :regular_date <= a.regular_end))
					)
					AND a.delete_flg <> 1 
				";

		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':item_status_cd' => '01',
			':bot_id' => $bot_id,
			':lang_cd' => $lang_cd,
			':cur_date' => date('Y-m-d'),
			':regular_date' => date('m-d'),
			':regular_date_kara' => "",
			':original_bot_id' => $original_bot_id,
			':template_bot_id' => $template_bot_id,
		));
		$results = $query->execute();
		return $results->as_array();
	}

	function get_t_faq_replace($bot_id, $facility_cd, $lang_cd)
	{
		$parent_bot_id = $this->get_grp_parent_bot_id($bot_id);
		if ($parent_bot_id == -1) {
			$parent_bot_id = $bot_id;
		}

		$sql = "SELECT *
				FROM t_faq_replace r
				WHERE
					(r.bot_id = :bot_id OR r.bot_id = :parent_bot_id)
				AND (r.lang_cd = :lang_cd OR r.lang_cd = '' OR r.lang_cd is NULL)
				ORDER BY
					r.bot_id,
					length(r.src_word) DESC
				";

		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':bot_id' => $bot_id,
			':parent_bot_id' => $parent_bot_id,
			':lang_cd' => $lang_cd,
		));
		$results = $query->execute();
		return $results->as_array();
	}

	function get_m_bot_engine_redirect($lang_cd)
	{
		$sql = "SELECT *
				FROM m_bot_engine_redirect r
				WHERE
					r.lang_cd = :lang_cd
				";

		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':lang_cd' => $lang_cd,
		));
		$results = $query->execute();
		return $results->as_array();
	}

	function get_m_engine_action_mapping($bot_id, $facility_cd, $lang_cd, $intent)
	{
		$sql = "SELECT *
				FROM m_engine_action_mapping r
				WHERE
					r.action = :intent
				";

		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':bot_id' => $bot_id,
			':intent' => $intent,
		));
		$results = $query->execute();
		return $results->as_array();
	}

    // wuzhao add for #1562
	function get_m_bot_skill_mapping($lang_cd)
	{
		$sql = "SELECT *
				FROM m_bot_skill_mapping a
				WHERE
					a.lang_cd = :lang_cd
				";

		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':lang_cd' => $lang_cd,
		));
		$results = $query->execute();
		return $results->as_array();
	}

	function get_bot_message($bot_id, $msg_cd, $lang_cd)
	{
		// 取父bot_id
		$parent_bot_id = $this->get_grp_parent_bot_id($bot_id);

		// 先判别msg_cd的类别
		$sql = "SELECT 
			a.msg_id, a.msg_cd, a.bot_id, a.msg_type_cd, a.msg_data
		FROM t_bot_msg a
		WHERE
			a.bot_id = :bot_id
			AND	a.msg_cd = :msg_cd 
		ORDER BY 
			bot_id DESC
		LIMIT 1";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
				':msg_cd' => $msg_cd,
				':lang_cd' => $lang_cd,
		));

		$results = $query->execute()->as_array();

		if (count($results) > 0) {
			return $this->get_bot_message_detail($results, $bot_id, $msg_cd, $lang_cd);
		}

		// 没取到数据，试图查看t_bot_setting看是否有template_bot
		$base_bot_id = 0;
		$results = $this->get_bot_setting($bot_id, "template_bot");
		if (count($results) > 0) {
			$base_bot_id = $results[0]["setting_value"];

			$query = DB::query(Database::SELECT, $sql);
			$query->parameters(array(
				':bot_id' => $base_bot_id,
				':msg_cd' => $msg_cd,
				':lang_cd' => $lang_cd,
			));
	
			$results = $query->execute()->as_array();

			if (count($results) > 0) {
				return $this->get_bot_message_detail($results, $base_bot_id, $msg_cd, $lang_cd);
			}
		}

		// 取父bot_id
		$parent_bot_id = $this->get_grp_parent_bot_id($bot_id);
		if ($parent_bot_id != -1 && $parent_bot_id != $bot_id) {
			$query = DB::query(Database::SELECT, $sql);
			$query->parameters(array(
				':bot_id' => $parent_bot_id,
				':msg_cd' => $msg_cd,
				':lang_cd' => $lang_cd,
			));
	
			$results = $query->execute()->as_array();

			if (count($results) > 0) {
				return $this->get_bot_message_detail($results, $parent_bot_id, $msg_cd, $lang_cd);
			}
		}

		// 取共通设定(bot_id=0)
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':bot_id' => 0,
			':msg_cd' => $msg_cd,
			':lang_cd' => $lang_cd,
		));

		$results = $query->execute()->as_array();

		if (count($results) > 0) {
			return $this->get_bot_message_detail($results, 0, $msg_cd, $lang_cd);
		}

		// 返回空结果
		return $results;
	}

	function get_bot_message_detail($results,  $bot_id, $msg_cd, $lang_cd) {
		// 然后从相应的表里读取实际内容
		$table_name = "";
		$order_by = "";
		if ($results[0]["msg_type_cd"] == "txt") {
			$table_name = "t_bot_msg_desc_txt";
			$order_by = "ORDER BY no";
		} else if ($results[0]["msg_type_cd"] == "img") {
			$table_name = "t_bot_msg_desc_img";
			$order_by = "ORDER BY no";
		} else if ($results[0]["msg_type_cd"] == "mov") {
			$table_name = "t_bot_msg_desc_img";
			$order_by = "ORDER BY no";
		} else if ($results[0]["msg_type_cd"] == "car") {
			$table_name = "t_bot_msg_desc_car";
			$order_by = "ORDER BY no";
		} else if ($results[0]["msg_type_cd"] == "lst") {
			$table_name = "t_bot_msg_desc_lst";
			$order_by = "ORDER BY no";
		} else if ($results[0]["msg_type_cd"] == "btn") {
			$table_name = "t_bot_msg_desc_lst";
			$order_by = "ORDER BY no";
		} else if ($results[0]["msg_type_cd"] == "mnu") {
			$table_name = "t_bot_msg_desc_lst";
			$order_by = "ORDER BY no";
		} else if ($results[0]["msg_type_cd"] == "mal") {
			$table_name = "t_bot_msg_desc_tpl";
		} else if ($results[0]["msg_type_cd"] == "tpl") {
			$table_name = "t_bot_msg_desc_tpl";
		}
		
		$sql = "SELECT 
			:bot_id as bot_id, a.msg_data, :msg_cd as msg_cd, :msg_type_cd as msg_type_cd, b.*
			FROM 
				$table_name b
			INNER JOIN t_bot_msg a
				ON a.bot_id = :bot_id
				AND a.msg_id = b.msg_id
			WHERE
				b.msg_id = :msg_id
				AND b.lang_cd = :lang_cd
			$order_by
		";

		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':bot_id' => $bot_id,
			':msg_id' => $results[0]["msg_id"],
			':msg_type_cd' => $results[0]["msg_type_cd"],
			':msg_cd' => $msg_cd,
			':lang_cd' => $lang_cd,
		));

		$results2 = $query->execute();

		if ($results[0]["msg_type_cd"] == "mnu" && count($results2) == 0) {
			// 2019.11.25 seki要求取mnu message的时候，如果定义了messagecd，没有定义desc，也应该算是定义了。
			return $results;
		}

		return $results2->as_array();
	}

	// 此函数被engine的menu_change_hotel.js调用。疑似早已经不使用。
	function get_items($lang_cd, $keyword = NULL, $class_cd='01', $property_list = NULL, $start=0, $records=8, $sort = 'recommend DESC')
	{
        // 2020.10.12 #6073
		$sql_status_cd_end_date = 
			" AND (a.end_date is NULL OR DATE_FORMAT(a.end_date,'%Y-%m-%d') >= :cur_date)
			AND a.item_status_cd in ('01','03','04','06','07','08')
			";
		$sql = "SELECT a.item_id, a.item_class_cd, a.item_class_type_cd, a.country_cd,  a.item_status_cd, a.item_cd, a.item_div, a.class_cd, a.location_lat, a.location_lon, a.link_id, a.item_data,
				a.position_x, a.position_y, a.position_z,
				b.item_name item_name, b.sell_point, b.item_image, b.item_image_thumb, b.reserve1, b.show_image, c.recommend recommend
				FROM t_item a ";

		if ($property_list != NULL) {
			$sql = "$sql INNER JOIN t_item_property p ON a.item_id = p.item_id";
		}
		$sql = "$sql
				LEFT JOIN t_item_description b
				ON a.item_id = b.item_id
				LEFT JOIN t_item_analysis c
				ON a.item_id = c.item_id";

		if ($keyword != NULL) {
			$sql = "$sql
				INNER JOIN t_item_keyword k
				ON a.item_id = k.item_id
			AND k.keyword LIKE '%$keyword%' ";
		}
		$sql = "$sql
				WHERE
				b.lang_cd = :lang_cd
				AND (a.start_date is NULL OR DATE_FORMAT(a.start_date,'%Y-%m-%d') <= :cur_date)
				$sql_status_cd_end_date
				AND (a.regular_start = :regular_date_kara 
					OR a.regular_end = :regular_date_kara 
					OR (a.regular_start <= a.regular_end AND a.regular_start <= :regular_date AND :regular_date <= a.regular_end)
					OR (a.regular_start > a.regular_end AND (a.regular_start <= :regular_date OR :regular_date <= a.regular_end))
				)
				AND a.delete_flg <> 1";
		if ($class_cd != NULL) {
			$sql = "$sql
				AND a.item_class_cd = :item_class_cd";
		}
		if ($property_list != NULL) {
			$sql = "$sql AND p.property_cd IN ($property_list)";
		}

		$sql = "$sql ORDER BY $sort
				limit $start, $records";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':item_status_cd' => '01',
				':item_class_cd' => $class_cd,
				':lang_cd' => $lang_cd,
				':cur_date' => date('Y-m-d'),
				':regular_date' => date('m-d'),
				':regular_date_kara' => "",
		));
		$results = $query->execute();
		return $results->as_array();
	}

	/*wuzhao delete 2019.08.06
	function get_item($lang_cd, $item_cd)
	{
		$sql = 'SELECT a.item_id, a.item_cd, a.item_class_cd, a.bot_id,
				b.item_name, b.description, b.sell_point, b.item_image, b.item_image_thumb,
				b.tel, b.url
				FROM t_item a
				LEFT JOIN t_item_description b
				ON a.item_id = b.item_id AND b.lang_cd = :lang_cd
				WHERE a.item_cd = :item_cd
				AND (a.start_date is NULL OR DATE_FORMAT(a.start_date,"%Y-%m-%d") <= :cur_date)
				AND (a.end_date is NULL OR DATE_FORMAT(a.end_date,"%Y-%m-%d") >= :cur_date)
				AND (a.regular_start = :regular_date_kara 
					OR a.regular_end = :regular_date_kara 
					OR (a.regular_start <= a.regular_end AND a.regular_start <= :regular_date AND :regular_date <= a.regular_end)
					OR (a.regular_start > a.regular_end AND (a.regular_start <= :regular_date OR :regular_date <= a.regular_end))
					)
				';
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':item_cd' => $item_cd,
				':lang_cd' => $lang_cd,
				':cur_date' => date('Y-m-d'),
				':regular_date' => date('m-d'),
				':regular_date_kara' => "",
		));
		$results = $query->execute();
		return $results->as_array();
	}
	*/

	function get_experiences($lang_cd, $keyword = NULL, $class_cd='01', $property_list = NULL, $start=0, $records=8, $sort = '')
	{
		$sql = "SELECT a.experience_id, a.experience_class_cd, a.experience_class_type_cd, a.country_cd,  a.experience_status_cd, a.experience_cd,
				b.experience_name experience_name, b.sell_point, b.experience_image, b.experience_image_thumb, b.reserve1
				FROM t_experience a ";

		if ($property_list != NULL) {
			$sql = "$sql INNER JOIN t_item_property p ON a.item_id = p.item_id";
		}
		$sql = "$sql
		LEFT JOIN t_experience_description b
		ON a.experience_id = b.experience_id";

		if ($keyword != NULL) {
			$sql = "$sql
			INNER JOIN t_item_keyword k
			ON a.item_id = k.item_id
			AND k.keyword LIKE '%$keyword%' ";
		}
		$sql = "$sql
		WHERE
		b.lang_cd = :lang_cd
		AND a.experience_status_cd = :experience_status_cd
		AND a.delete_flg = 0";
		if ($class_cd != NULL) {
			$sql = "$sql
			AND a.experience_class_cd = :experience_class_cd";
		}
		if ($property_list != NULL) {
			$sql = "$sql AND p.property_cd IN ($property_list)";
		}

		//$sql = "$sql ORDER BY $sort
		//limit $start, $records";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':experience_status_cd' => '01',
				':experience_class_cd' => $class_cd,
				':lang_cd' => $lang_cd,
		));
		$results = $query->execute();
		return $results->as_array();
	}

	function get_experience($lang_cd, $experience_cd)
	{
		$sql = 'SELECT a.experience_id, a.experience_cd, a.bot_id,
				b.experience_name, b.description, b.sell_point, b.experience_image, b.experience_image_thumb
				FROM t_experience a
				LEFT JOIN t_experience_description b
				ON a.experience_id = b.experience_id AND b.lang_cd = :lang_cd
				WHERE a.experience_cd = :experience_cd';
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':experience_cd' => $experience_cd,
				':lang_cd' => $lang_cd,
		));
		$results = $query->execute();
		return $results->as_array();
	}

	function get_experience_stock($experience_cd)
	{
		$sql = 'SELECT a.experience_id, b.stock_date, b.description
				FROM t_experience_stock b
				LEFT JOIN t_experience a
				ON a.experience_id = b.experience_id
				WHERE a.experience_cd = :experience_cd
				AND b.stock_status_cd = :stock_status_cd
                AND b.stock_date > :stock_date';
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':experience_cd' => $experience_cd,
				':stock_status_cd' => '02',
				':stock_date' => date('Y-m-d'),
		));
		$results = $query->execute();
		return $results->as_array();
	}

	function get_item_relation($lang_cd, $bot_info, $class_cd, $class_type_cd=NULL, $class_type_sub_cd=NULL)
	{
		// 此函数已经不使用
		$original_bot_id = $bot_info['bot_id'];
		$bot_id = $bot_info['bot_id'];
		$bot_id = $this->get_bot_id_list_include_template_bot($bot_id);
		$template_bot_id = $this->get_template_bot_id($bot_id);
        // 2020.10.12 #6073
		$sql_status_cd_end_date = 
			" AND (a.end_date is NULL OR DATE_FORMAT(a.end_date,'%Y-%m-%d') >= :cur_date)
			AND a.item_status_cd in ('01','03','04','06','07','08')
			";

		$sql = "SELECT DISTINCT a.item_id, a.item_cd, a.item_div, a.class_cd, a.location_lat, a.location_lon, a.link_id, a.item_data,
				a.position_x, a.position_y, a.position_z,
				b.item_name, b.description, b.sell_point, b.item_image, b.item_image_thumb, b.sell_point_line , b.tel, b.url, b.show_image
				FROM t_item a
				LEFT JOIN t_item_description b
				ON a.item_id = b.item_id AND b.lang_cd = :lang_cd
				INNER JOIN t_item_display r
					ON a.item_id = r.item_id
					AND a.item_div = r.item_div
					AND find_in_set(:lang_cd, r.lang_display)
					AND r.public_flg = 1
				WHERE
				r.bot_id in ($bot_id)
				AND a.delete_flg <> 1
				AND (r.bot_id = :original_bot_id OR (r.bot_id = :template_bot_id AND r.item_div != 2))
				AND a.item_class_cd = :class_cd
				AND (a.start_date is NULL OR DATE_FORMAT(a.start_date,'%Y-%m-%d') <= :cur_date)
				$sql_status_cd_end_date
				AND (a.regular_start = :regular_date_kara 
					OR a.regular_end = :regular_date_kara 
					OR (a.regular_start <= a.regular_end AND a.regular_start <= :regular_date AND :regular_date <= a.regular_end)
					OR (a.regular_start > a.regular_end AND (a.regular_start <= :regular_date OR :regular_date <= a.regular_end))
					)
				";
		if ($class_type_cd != NULL) $sql = $sql . ' AND a.item_class_type_cd = :class_type_cd';
		if ($class_type_sub_cd != NULL) $sql = $sql . ' AND a.item_class_type_sub_cd = :class_type_sub_cd';
		$sql = $sql . " ORDER BY r.sort_no1";

		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
				':lang_cd' => $lang_cd,
				':class_cd' => $class_cd,
				':class_type_cd' => $class_type_cd,
				':class_type_sub_cd' => $class_type_sub_cd,
				':cur_date' => date('Y-m-d'),
				':regular_date' => date('m-d'),
				':regular_date_kara' => "",
				':original_bot_id' => $original_bot_id,
				':template_bot_id' => $template_bot_id,
		));
		$results = $query->execute();
		return $results->as_array();
	}

	function get_item_relation_new($lang_cd, $bot_info, $class_cd, $class_type_cd=NULL, $item_id_history=NULL, $class_type_sub_cd=NULL)
	{
		// 如果class_cd(只能是单个cd)有指定
		//		那么该cd必须等于t_item.item_class_cd(只能是单个cd)
		// 如果class_type_cd(只能是单个cd)有指定，
		//		那么这个cd必须在t_item.item_class_type_cd(可能是空格隔开的多个cd)里存在
		// 如果class_type_sub_cd(可以为以逗号隔开的多个cd)有指定，
		//		那么这复数个cd必须在t_item.item_class_type_sub_cd(可能是空格隔开的多个cd)里同时存在
		// 本查询对于sort_no1为0的item做随机排序
		// engine使用者
		//	通过botapi.get_facility_recommend_spots_new
		//		skill\recommend_activity_hotel.js
        //		skill\recommend_facility_activity.js
        //		skill\recommend_facility_food.js
        //		skill\recommend_facility_shopping.js
    	//		skill\recommend_shopping.js
    	//		skill\recommend_restaurant.js
		//	通过botapi.get_facility_recommend_spots_new
		//		skill\recommend_facility_spots.js
        //		skill\recommend_facility_spots_sub.js
		//	通过botapi.get_facility_class_type_sub_cd_new
		//		skill\recommend_facility_spots_sub2.js
		
		$original_bot_id = $bot_info['bot_id'];
		$bot_id = $bot_info['bot_id'];
		$bot_id = $this->get_bot_id_list_include_template_bot($bot_id);
		$template_bot_id = $this->get_template_bot_id($bot_id);
        // 2020.10.12 #6073
		$sql_status_cd_end_date = 
			" AND (a.end_date is NULL OR DATE_FORMAT(a.end_date,'%Y-%m-%d') >= :cur_date)
			AND a.item_status_cd in ('01','03','04','06','07','08')
			";

		$sql = "SELECT DISTINCT a.item_id, a.item_cd, a.item_div, a.class_cd, a.location_lat, a.location_lon, a.link_id, a.item_status_cd,a.end_date, a.item_data,
				a.position_x, a.position_y, a.position_z,
				b.item_name, b.description, b.sell_point, b.item_image, b.item_image_thumb, b.sell_point_line, b.tel, b.url,
				b.btn1_name, b.btn1_url, b.btn2_name, b.btn2_url, b.btn3_name, b.btn3_url, b.btn1_url_lang_cd, b.btn2_url_lang_cd, b.btn3_url_lang_cd,
				b.btn1_url_sp, b.btn2_url_sp, b.btn3_url_sp, b.show_image,
				case
					when a.item_id in ($item_id_history) then 0
					when r.sort_no1 <> 0 then sort_no1
					else 9999
				end sort1,
				case
					when a.item_id in ($item_id_history) then 0
					when r.sort_no1 <> 0 then 0
					else FLOOR(1000 + (RAND() * (2000 - 1000 + 1)))
				end sort2
				FROM t_item a
				INNER JOIN t_item_description b
				ON a.item_id = b.item_id AND b.lang_cd = :lang_cd
				INNER JOIN t_item_display r
					ON a.item_id = r.item_id
					AND a.item_div = r.item_div
					AND find_in_set(:lang_cd, r.lang_display)
					AND r.public_flg = 1
				WHERE
				r.bot_id in ($bot_id)
				AND a.delete_flg <> 1
				AND (r.bot_id = :original_bot_id OR (r.bot_id = :template_bot_id AND r.item_div != 2))
				AND a.item_class_cd = :class_cd
				AND (a.start_date is NULL OR DATE_FORMAT(a.start_date,'%Y-%m-%d') <= :cur_date)
				$sql_status_cd_end_date
				AND (a.regular_start = :regular_date_kara 
					OR a.regular_end = :regular_date_kara 
					OR (a.regular_start <= a.regular_end AND a.regular_start <= :regular_date AND :regular_date <= a.regular_end)
					OR (a.regular_start > a.regular_end AND (a.regular_start <= :regular_date OR :regular_date <= a.regular_end))
					)
				";
		//if ($class_type_cd != NULL) $sql = $sql . ' AND a.item_class_type_cd = :class_type_cd';
		//if ($class_type_sub_cd != NULL) $sql = $sql . ' AND a.item_class_type_sub_cd = :class_type_sub_cd';
		if ($class_type_cd != NULL) $sql = $sql . $this->create_sql_for_single_code("get_item_relation_new", "a.item_class_type_cd", $class_type_cd);
		if ($class_type_sub_cd != NULL) {
			$sql = $sql . $this->create_sql_for_multi_code_exist_all("get_item_relation_new", "a.item_class_type_sub_cd", $class_type_sub_cd);
		}
		$sql = $sql . "	ORDER BY sort1, sort2";

		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
				':lang_cd' => $lang_cd,
				':class_cd' => $class_cd,
				':class_type_cd' => $class_type_cd,
				':item_id_history' => $item_id_history,
				':class_type_sub_cd' => $class_type_sub_cd,
				':cur_date' => date('Y-m-d'),
				':regular_date' => date('m-d'),
				':regular_date_kara' => "",
				':original_bot_id' => $original_bot_id,
				':template_bot_id' => $template_bot_id,
		));
		$results = $query->execute();
		return $results->as_array();
	}

	function get_items_by_condition($condition, $lang_cd, $bot_info, $class_cd=NULL, $class_type_cd=NULL, $class_type_sub_cd=NULL, $features =NULL, $item_id_history=NULL)
	{
		// 如果class_cd(可以为以逗号隔开的多个cd)有指定，
		//		那么这次cd中的某一个必须等于t_item.item_class_cd（只能是单个cd）
		// 如果class_type_cd(可以为以逗号隔开的多个cd)有指定，
		//		那么这复数个cd中的某一个必须在t_item.item_class_type_cd(可能是空格隔开的多个cd)里存在
		// 如果class_type_sub_cd(可以为以逗号隔开的多个cd)有指定
		//		那么这复数个cd必须在t_item.item_class_type_sub_cd(可能是空格隔开的多个cd)里同时存在
		// 如果features(可以为以逗号隔开的多个cd)有指定，
		//		那么这复数个cd中的某一个必须在t_item.features_cd(可能是空格隔开的多个cd)里存在
		// 本查询对于sort_no1为0的item做随机排序
		// engine使用者
		//	通过botapi.get_items_by_condition
		//		skill\recommend_items_by_class_type.js
		//		skill\recommend_items_by_class_type_sub.js
		//		skill\recommend_items_by_feature.js
		$original_bot_id = $bot_info['bot_id'];
		$bot_id = $bot_info['bot_id'];
		$bot_id = $this->get_bot_id_list_include_template_bot($bot_id);
		$template_bot_id = $this->get_template_bot_id($bot_id);
		$item_class_type_sub_cd_process_type = $condition['item_class_type_sub_cd_process_type'];
        // 2020.10.12 #6073
		$sql_status_cd_end_date = 
			" AND (a.end_date is NULL OR DATE_FORMAT(a.end_date,'%Y-%m-%d') >= :cur_date)
			AND a.item_status_cd in ('01','03','04','06','07','08')
			";

		// 2022.07.14 begin #28773
		// 增加items_need_display参数
		if (isset($condition['items_need_display'])) {
			$items_need_display = $condition['items_need_display'];
		} else {
			$items_need_display = "";
		}
		if ($items_need_display == "") {
			$items_need_display = "0";
		}
		// 2022.07.14 end #28773
		$sql = "SELECT DISTINCT a.item_id, a.item_cd, a.item_div, a.class_cd, a.location_lat, a.location_lon, a.link_id, a.item_status_cd,a.end_date, a.item_data,
				a.position_x, a.position_y, a.position_z,
				b.item_name, b.description, b.sell_point, b.item_image, b.item_image_thumb, b.sell_point_line, b.tel, b.url,
				b.btn1_name, b.btn1_url, b.btn2_name, b.btn2_url, b.btn3_name, b.btn3_url, b.btn1_url_lang_cd, b.btn2_url_lang_cd, b.btn3_url_lang_cd,
				b.btn1_url_sp, b.btn2_url_sp, b.btn3_url_sp, b.show_image,
				case
					when a.item_id in ($item_id_history) then 0
					when a.item_id in ($items_need_display) then 1
					when r.sort_no1 <> 0 then sort_no1
					else 9999
				end sort1,
				case
					when a.item_id in ($item_id_history) then 0
					when r.sort_no1 <> 0 then 0
					else FLOOR(1000 + (RAND() * (2000 - 1000 + 1)))
				end sort2
				FROM t_item a
				INNER JOIN t_item_description b
				ON a.item_id = b.item_id AND b.lang_cd = :lang_cd
				INNER JOIN t_item_display r
					ON a.item_id = r.item_id
					AND a.item_div = r.item_div
					AND find_in_set(:lang_cd, r.lang_display)
					AND r.public_flg = 1
				WHERE
				r.bot_id in ($bot_id) 
				AND a.delete_flg <> 1
				AND (r.bot_id = :original_bot_id OR (r.bot_id = :template_bot_id AND r.item_div != 2))
				AND (a.start_date is NULL OR DATE_FORMAT(a.start_date,'%Y-%m-%d') <= :cur_date)
				$sql_status_cd_end_date
				AND (a.regular_start = :regular_date_kara 
					OR a.regular_end = :regular_date_kara 
					OR (a.regular_start <= a.regular_end AND a.regular_start <= :regular_date AND :regular_date <= a.regular_end)
					OR (a.regular_start > a.regular_end AND (a.regular_start <= :regular_date OR :regular_date <= a.regular_end))
					)
				";
		//if ($class_type_cd != NULL) $sql = $sql . " AND a.item_class_type_cd in ($class_type_cd)";
		//if ($class_type_sub_cd != NULL) $sql = $sql . " AND a.item_class_type_sub_cd in ($class_type_sub_cd)";
		$sql_condition = "1 = 1";
		if ($class_cd != NULL) $sql_condition = $sql_condition . " AND a.item_class_cd in ($class_cd)";
		if ($class_type_cd != NULL) $sql_condition = $sql_condition . $this->create_sql_for_multi_code_exist_atleast_one("get_items_by_condition", "a.item_class_type_cd", $class_type_cd);
		$class_type_sub_cd_num = 1;
		if ($class_type_sub_cd != NULL) {
			$sql_condition = $sql_condition . $this->create_sql_for_multi_code_exist_all("get_items_by_condition", "a.item_class_type_sub_cd", $class_type_sub_cd);
		}
		if ($features != NULL) $sql_condition = $sql_condition . $this->create_sql_for_multi_code_exist_atleast_one("get_items_by_condition", "a.features_cd", $features);

		$sql = $sql . " AND (a.item_id in ($items_need_display) OR ( $sql_condition ) ) ";

		$sql = $sql . "	ORDER BY sort1, sort2";

		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
				':lang_cd' => $lang_cd,
				':features' => $features,
				':class_type_cd' => $class_type_cd,
				':class_type_sub_cd' => $class_type_sub_cd,
				':cur_date' => date('Y-m-d'),
				':regular_date' => date('m-d'),
				':regular_date_kara' => "",
				':class_type_sub_cd_num' => $class_type_sub_cd_num,
				':original_bot_id' => $original_bot_id,
				':template_bot_id' => $template_bot_id,
		));
		$results = $query->execute()->as_array();

		if (count($results) == 0 && $item_class_type_sub_cd_process_type == "2") {
			// 为2的时候，只要t_item.item_class_type_sub_cd包含任意一个item_class_type_sub_cd参数就检索出来
			$query = DB::query(Database::SELECT, $sql);
			$query->parameters(array(
					':bot_id' => $bot_id,
					':lang_cd' => $lang_cd,
					':features' => $features,
					':class_type_cd' => $class_type_cd,
					':class_type_sub_cd' => $class_type_sub_cd,
					':cur_date' => date('Y-m-d'),
					':regular_date' => date('m-d'),
					':regular_date_kara' => "",
					':class_type_sub_cd_num' => 1,
					':original_bot_id' => $original_bot_id,
					':template_bot_id' => $template_bot_id,
			));
			$results = $query->execute()->as_array();
		}
		return $results;
	}

	// recommend_items_by_class_cd.js
	// mode不为空(validonly)代表是menu的检索,代表regular_date外的不显示
	// inquiry.js
	// action_select_item.js
	// 特别式样，根据mode参数来决定 regular_date外的是否显示，并计算season_onoff
	// 2022.09.14  #31551 增加event_date参数
	function get_items_by_class_cd($bot_info, $condition, $item_id_history)
	{
		// 如果class_cd(可以为以逗号隔开的多个cd)有指定，
		//		那么这次cd中的某一个必须等于t_item.class_cd（可能是空格隔开的多个cd）
		// item_class_type_sub_cd(可以为以逗号隔开的多个cd)有指定
		//		那么这复数个cd必须在t_item.item_class_type_sub_cd(可能是空格隔开的多个cd)里同时存在
		// 如果features(可以为以逗号隔开的多个cd)有指定，
		//		那么这复数个cd中的某一个必须在t_item.features_cd(可能是空格隔开的多个cd)里存在
		// 本查询对于sort_no1为0的item做随机排序
		// t_item.area_cd可以是以逗号隔开的多个area_cd
		
		$original_bot_id = $bot_info['bot_id'];
		$bot_id = $bot_info['bot_id'];
		$bot_id = $this->get_bot_id_list_include_template_bot($bot_id);
		$template_bot_id = $this->get_template_bot_id($bot_id);
		$lang_cd = $condition["lang_cd"];

		$item_div = NULL;
		$class_cd = NULL;
		$features_cd = NULL;
		$class_type_sub_cd = NULL;
		$mode = "";
		if (isset($condition['item_div'])) {
			$item_div = $condition['item_div'];
		}
		if (isset($condition['class_cd'])) {
			$class_cd = $condition['class_cd'];
		}
		if (isset($condition['features_cd'])) {
			$features_cd = $condition['features_cd'];
		}
		if (isset($condition['item_class_type_sub_cd'])) {
			$class_type_sub_cd = $condition['item_class_type_sub_cd'];
		}
		if (isset($condition['mode'])) {
			$mode = $condition['mode'];
		}
		// 2023.04.15 begin #42259
		$order_by = "";
		$lat = "";
		$lng = "";
		if (isset($condition['order_by'])) {
			$order_by = $condition['order_by'];
			if (isset($condition['lat'])) {
				$lat = $condition['lat'];
			}
			if (isset($condition['lng'])) {
				$lng = $condition['lng'];
			}
			if ($lat == "0" || $lng == "0") {
				$order_by = "";
			}
			if ($lat == "" || $lng == "") {
				$order_by = "";
			}
		}
		$sql_distance = "";
		$sql_distance_orderby = "";
		if ($order_by == "distance") {
			$sql_distance = "
				,ROUND(
					6378.138 * 2 * ASIN(
						SQRT(
							POW(
								SIN(
									(
										:lat * PI() / 180 - a.location_lat * PI() / 180
									) / 2
								),
								2
							) + COS(:lat * PI() / 180) * COS(a.location_lat * PI() / 180) * POW(
								SIN(
									(
										:lng * PI() / 180 - a.location_lon * PI() / 180
									) / 2
								),
								2
							)
						)
					) * 1000) distance
			";
			$sql_distance_orderby = ",distance";
		}
		// 2023.04.15 end #42259
	
		// 2022.07.14 begin #28773
		// 增加items_need_display参数
		if (isset($condition['items_need_display'])) {
			$items_need_display = $condition['items_need_display'];
		} else {
			$items_need_display = "";
		}
		if ($items_need_display == "") {
			$items_need_display = "0";
		}
		// 2022.07.14 end #28773

		// 2019.11.21 add area_cd
		$area_cd = "";
		if (isset($condition['area_cd'])) {
			$area_cd = $condition['area_cd'];
		}
		$sql_area_cd = "";
		if ($area_cd != "") {
			//2020.06.03 改为支持多个area_cd $sql_area_cd = " AND a.area_cd = :area_cd ";
			$sql_area_cd = " AND concat(',', a.area_cd) like concat('%,', :area_cd, '%') ";
		}
		$sql_regular = "";
		if ($mode == "") {
			// #4691 regular_date外的也显示
		} else {
			// #4691 regular_date外的不显示
			$sql_regular = " AND (a.regular_start = :regular_date_kara 
				OR a.regular_end = :regular_date_kara 
				OR (a.regular_start <= a.regular_end AND a.regular_start <= :regular_date AND :regular_date <= a.regular_end)
				OR (a.regular_start > a.regular_end AND (a.regular_start <= :regular_date OR :regular_date <= a.regular_end))
				)
			";
		}
        // 2020.10.12 #6073
		$sql_status_cd_end_date = 
			" AND (a.end_date is NULL OR DATE_FORMAT(a.end_date,'%Y-%m-%d') >= :cur_date)
			AND a.item_status_cd in ('01','03','04','06','07','08')
			";

		// 2022.09.14  begin #31551 增加event_date参数
		$event_date = "";
		if (isset($condition['event_date'])) {
			$event_date = $condition['event_date'];
		}
		if ($event_date == null || $event_date == NULl) {
			$event_date = "";
		}
		$sql_event_date = "";
		if ($event_date != "") {
			$sql_event_date = " AND JSON_VALID(a.item_data) = 1
				AND JSON_TYPE(JSON_EXTRACT(a.item_data, '$.event_date')) <> 'NULL'
				AND JSON_EXTRACT(a.item_data, '$.event_date') <> '' 
				";
		}
		// 2022.09.14  end #31551

		$sql = "SELECT DISTINCT a.item_id, a.item_cd, a.item_div, a.class_cd,  a.location_lat, a.location_lon, a.link_id, a.item_status_cd,a.end_date, a.item_data,
				a.position_x, a.position_y, a.position_z,
				case when JSON_VALID(a.item_data) = 1
					AND JSON_TYPE(JSON_EXTRACT(a.item_data, '$.event_date')) <> 'NULL'
					AND JSON_EXTRACT(a.item_data, '$.event_date') <> '' then JSON_UNQUOTE(JSON_EXTRACT(a.item_data, '$.event_date'))
					else ''
				end t_item_event_date,
				b.item_name, b.description, b.sell_point, b.item_image, b.item_image_thumb, b.sell_point_line, b.tel, b.url,
				b.btn1_name, b.btn1_url, b.btn2_name, b.btn2_url, b.btn3_name, b.btn3_url, b.btn1_url_lang_cd, b.btn2_url_lang_cd, b.btn3_url_lang_cd,
				b.btn1_url_sp, b.btn2_url_sp, b.btn3_url_sp, b.show_image,
				case
					when a.item_id in ($item_id_history) then 0
					when a.item_id in ($items_need_display) then 1
					when r.sort_no1 <> 0 then sort_no1
					else 9999
				end sort1,
				case
					when a.item_id in ($item_id_history) then 0
					when r.sort_no1 <> 0 then 0
					else FLOOR(1000 + (RAND() * (2000 - 1000 + 1)))
				end sort2,
				case
					when a.regular_start = :regular_date_kara then 1
					when a.regular_end = :regular_date_kara then 1
					when a.regular_start <= a.regular_end AND a.regular_start <= :regular_date AND :regular_date <= a.regular_end then 1
					when a.regular_start > a.regular_end AND (a.regular_start <= :regular_date OR :regular_date <= a.regular_end) then 1
					else 0
				end season_onoff
				$sql_distance
				FROM t_item a
				INNER JOIN t_item_description b
				ON a.item_id = b.item_id AND b.lang_cd = :lang_cd
				INNER JOIN t_item_display r
					ON a.item_id = r.item_id
					AND a.item_div = r.item_div
					AND find_in_set(:lang_cd, r.lang_display)
					AND r.public_flg = 1
				WHERE r.bot_id in ($bot_id) 
				AND a.delete_flg <> 1
				AND (r.bot_id = :original_bot_id OR (r.bot_id = :template_bot_id AND r.item_div != 2))
				AND (a.start_date is NULL OR DATE_FORMAT(a.start_date,'%Y-%m-%d') <= :cur_date)
				$sql_status_cd_end_date
				$sql_regular
				$sql_event_date
				";
		//if ($class_cd != NULL) $sql = $sql . " AND a.class_cd in ($class_cd)";

		$sql_condition = "1 = 1 $sql_area_cd ";
		if ($item_div != NULL) {
			$sql_condition = $sql_condition . " AND a.item_div = '$item_div'";
		}
		if ($class_cd != NULL) {
			$class_cd_parts = explode(",", $class_cd);
			$class_cd_num = count($class_cd_parts);
			if ($class_cd_num == 1) {
				// 只指定了1个class_cd,那么就模糊匹配
				$sql_condition = $sql_condition . " AND concat(' ', a.class_cd) like concat('% ', '$class_cd', '%')";
			} else {
				// 指定了多个class_cd,那么就完全匹配
				$sql_condition = $sql_condition . $this->create_sql_for_multi_code_exist_atleast_one("get_items_by_class_cd", "a.class_cd", $class_cd);
			}
		}
		if ($class_type_sub_cd != NULL) {
			$sql_condition = $sql_condition . $this->create_sql_for_multi_code_exist_all("get_items_by_class_cd", "a.item_class_type_sub_cd", $class_type_sub_cd);
		}
		if ($features_cd != NULL) {
			$sql_condition = $sql_condition . $this->create_sql_for_multi_code_exist_atleast_one("get_items_by_class_cd", "a.features_cd", $features_cd);;
		}
		$sql = $sql . " AND (a.item_id in ($items_need_display) OR ( $sql_condition ) ) ";

		$sql = $sql . "	ORDER BY sort1 $sql_distance_orderby , sort2";

		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
				':lang_cd' => $lang_cd,
				':class_cd' => $class_cd,
				':features_cd' => $features_cd,
				':class_type_sub_cd' => $class_type_sub_cd,
				':area_cd' => $area_cd,

				':cur_date' => date('Y-m-d'),
				':regular_date' => date('m-d'),
				':regular_date_kara' => "",
				':original_bot_id' => $original_bot_id,
				':template_bot_id' => $template_bot_id,

				':lat' => $lat,
				':lng' => $lng,

		));
		$results = $query->execute()->as_array();

		// 2022.09.14  begin #31551 增加event_date参数
		if ($event_date != "") {
			if (count($results) > 0) {
				$results_filter = [];
				for ($i=0; $i<count($results); $i++) {
					if ($this->is_event_date_between_t_item_event_date($event_date, $results[$i]["t_item_event_date"]) == 1) {
						// in
						$results_filter[] = $results[$i];
					} else {
						// out
						error_log($results[$i]["item_id"] . " event_date is out ");
					}
				}
				$results = $results_filter;
			}
		}
		// 2022.09.14  end #31551 增加event_date参数
		return $results;
	}

	// 2021.11.04 多个class_cd时改为暧昧
	// action_select_item.js
	function get_items_by_class_cd_aimai($bot_info, $condition, $item_id_history)
	{
		// 如果class_cd(可以为以逗号隔开的多个cd)有指定，
		//		那么这次cd中的某一个必须等于t_item.class_cd（可能是空格隔开的多个cd）
		// item_class_type_sub_cd(可以为以逗号隔开的多个cd)有指定
		//		那么这复数个cd必须在t_item.item_class_type_sub_cd(可能是空格隔开的多个cd)里同时存在
		// 如果features(可以为以逗号隔开的多个cd)有指定，
		//		那么这复数个cd中的某一个必须在t_item.features_cd(可能是空格隔开的多个cd)里存在
		// 本查询对于sort_no1为0的item做随机排序
		// t_item.area_cd可以是以逗号隔开的多个area_cd
		
		$original_bot_id = $bot_info['bot_id'];
		$bot_id = $bot_info['bot_id'];
		$bot_id = $this->get_bot_id_list_include_template_bot($bot_id);
		$template_bot_id = $this->get_template_bot_id($bot_id);
		$lang_cd = $condition["lang_cd"];

		$item_div = NULL;
		$class_cd = NULL;
		$features_cd = NULL;
		$class_type_sub_cd = NULL;
		$mode = "";
		if (isset($condition['item_div'])) {
			$item_div = $condition['item_div'];
		}
		if (isset($condition['class_cd'])) {
			$class_cd = $condition['class_cd'];
		}
		if (isset($condition['features_cd'])) {
			$features_cd = $condition['features_cd'];
		}
		if (isset($condition['item_class_type_sub_cd'])) {
			$class_type_sub_cd = $condition['item_class_type_sub_cd'];
		}
		if (isset($condition['mode'])) {
			$mode = $condition['mode'];
		}
	
		// 2019.11.21 add area_cd
		$area_cd = "";
		if (isset($condition['area_cd'])) {
			$area_cd = $condition['area_cd'];
		}
		$sql_area_cd = "";
		if ($area_cd != "") {
			//2020.06.03 改为支持多个area_cd $sql_area_cd = " AND a.area_cd = :area_cd ";
			$sql_area_cd = " AND concat(',', a.area_cd) like concat('%,', :area_cd, '%') ";
		}
		$sql_regular = "";
		if ($mode == "") {
			// #4691 regular_date外的也显示
		} else {
			// #4691 regular_date外的不显示
			$sql_regular = " AND (a.regular_start = :regular_date_kara 
				OR a.regular_end = :regular_date_kara 
				OR (a.regular_start <= a.regular_end AND a.regular_start <= :regular_date AND :regular_date <= a.regular_end)
				OR (a.regular_start > a.regular_end AND (a.regular_start <= :regular_date OR :regular_date <= a.regular_end))
				)
			";
		}
        // 2020.10.12 #6073
		$sql_status_cd_end_date = 
			" AND (a.end_date is NULL OR DATE_FORMAT(a.end_date,'%Y-%m-%d') >= :cur_date)
			AND a.item_status_cd in ('01','03','04','06','07','08')
			";

		$sql = "SELECT DISTINCT a.item_id, a.item_cd, a.item_div, a.class_cd,  a.location_lat, a.location_lon, a.link_id, a.item_status_cd,a.end_date, a.item_data,
				a.position_x, a.position_y, a.position_z,
				b.item_name, b.description, b.sell_point, b.item_image, b.item_image_thumb, b.sell_point_line, b.tel, b.url,
				b.btn1_name, b.btn1_url, b.btn2_name, b.btn2_url, b.btn3_name, b.btn3_url, b.btn1_url_lang_cd, b.btn2_url_lang_cd, b.btn3_url_lang_cd,
				b.btn1_url_sp, b.btn2_url_sp, b.btn3_url_sp, b.show_image,
				case
					when a.item_id in ($item_id_history) then 0
					when r.sort_no1 <> 0 then sort_no1
					else 9999
				end sort1,
				case
					when a.item_id in ($item_id_history) then 0
					when r.sort_no1 <> 0 then 0
					else FLOOR(1000 + (RAND() * (2000 - 1000 + 1)))
				end sort2,
				case
					when a.regular_start = :regular_date_kara then 1
					when a.regular_end = :regular_date_kara then 1
					when a.regular_start <= a.regular_end AND a.regular_start <= :regular_date AND :regular_date <= a.regular_end then 1
					when a.regular_start > a.regular_end AND (a.regular_start <= :regular_date OR :regular_date <= a.regular_end) then 1
					else 0
				end season_onoff
				FROM t_item a
				INNER JOIN t_item_description b
				ON a.item_id = b.item_id AND b.lang_cd = :lang_cd
				INNER JOIN t_item_display r
					ON a.item_id = r.item_id
					AND a.item_div = r.item_div
					AND find_in_set(:lang_cd, r.lang_display)
					AND r.public_flg = 1
				WHERE r.bot_id in ($bot_id) 
				AND a.delete_flg <> 1
				AND (r.bot_id = :original_bot_id OR (r.bot_id = :template_bot_id AND r.item_div != 2))
				AND (a.start_date is NULL OR DATE_FORMAT(a.start_date,'%Y-%m-%d') <= :cur_date)
				$sql_status_cd_end_date
				$sql_regular
				$sql_area_cd
				";
		//if ($class_cd != NULL) $sql = $sql . " AND a.class_cd in ($class_cd)";
		if ($item_div != NULL) {
			$sql = $sql . " AND a.item_div = '$item_div'";
		}

		if ($class_cd != NULL) {
			$class_cd_parts = explode(",", $class_cd);
			$class_cd_num = count($class_cd_parts);
			if ($class_cd_num == 1) {
				// 只指定了1个class_cd,那么就模糊匹配
				$sql = $sql . " AND concat(' ', a.class_cd) like concat('% ', '$class_cd', '%')";
			} else {
				$sql_class_cd = "1 = 1";
				// 指定了多个class_cd,那么也模糊匹配
				for ($i=0; $i<$class_cd_num; $i++) {
					$cur_class_cd = $class_cd_parts[$i];
					$cur_sql =  " concat(' ', a.class_cd) like concat('% ', '$cur_class_cd', '%') ";
					if ($i == 0) {
						$sql_class_cd = $cur_sql;
					} else {
						$sql_class_cd = " $sql_class_cd 
						OR $cur_sql ";
					}
				}
	
				$sql = $sql . " AND ( $sql_class_cd )";
			}
		}
		if ($class_type_sub_cd != NULL) {
			$class_type_sub_cd_parts = explode(",", $class_type_sub_cd);
			$class_type_sub_cd_num = count($class_type_sub_cd_parts);
			$sql = $sql . $this->create_sql_for_multi_code_exist_all("get_items_by_class_cd_aimai", "a.item_class_type_sub_cd", $class_type_sub_cd);
		}
		if ($features_cd != NULL) {
			$sql = $sql . $this->create_sql_for_multi_code_exist_atleast_one("get_items_by_class_cd_aimai", "a.features_cd", $features_cd);
		}
		$sql = $sql . "	ORDER BY sort1, sort2";

		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
				':lang_cd' => $lang_cd,
				':class_cd' => $class_cd,
				':features_cd' => $features_cd,
				':class_type_sub_cd' => $class_type_sub_cd,
				':area_cd' => $area_cd,

				':cur_date' => date('Y-m-d'),
				':regular_date' => date('m-d'),
				':regular_date_kara' => "",
				':original_bot_id' => $original_bot_id,
				':template_bot_id' => $template_bot_id,
		));
		$results = $query->execute();
		return $results->as_array();
	}

	function get_hot_item_relation($lang_cd, $bot_info)
	{
		// 查询t_item_relation表里recommend值为1(推荐)的所有数据
		// engine使用者
		//	通过botapi.get_recommend_hot_spots
		//		skill\recommend_hot_spots.js
		$original_bot_id = $bot_info['bot_id'];
		$bot_id = $bot_info['bot_id'];
		$bot_id = $this->get_bot_id_list_include_template_bot($bot_id);
		$template_bot_id = $this->get_template_bot_id($bot_id);
        // 2020.10.12 #6073
		$sql_status_cd_end_date = 
			" AND (a.end_date is NULL OR DATE_FORMAT(a.end_date,'%Y-%m-%d') >= :cur_date)
			AND a.item_status_cd in ('01','03','04','06','07','08')
			";

		$sql = "SELECT DISTINCT 
					r.sort_no1,
					a.item_id, a.item_cd, a.item_div, a.class_cd, a.location_lat, a.location_lon, a.link_id, a.item_status_cd,a.end_date, a.item_data,
					a.position_x, a.position_y, a.position_z,
					b.item_name, b.description, b.sell_point, b.item_image, b.item_image_thumb, b.sell_point_line, b.tel, b.url,
					b.btn1_name, b.btn1_url, b.btn2_name, b.btn2_url, b.btn3_name, b.btn3_url, b.btn1_url_lang_cd, b.btn2_url_lang_cd, b.btn3_url_lang_cd,
					b.btn1_url_sp, b.btn2_url_sp, b.btn3_url_sp, b.show_image
				FROM t_item a
				INNER JOIN t_item_description b
					ON a.item_id = b.item_id AND b.lang_cd = :lang_cd
				INNER JOIN t_item_display r
					ON a.item_id = r.item_id
					AND a.item_div = r.item_div
					AND find_in_set(:lang_cd, r.lang_display)
					AND r.public_flg = 1
				WHERE 
				r.bot_id in ($bot_id)
				AND a.delete_flg <> 1
				AND (r.bot_id = :original_bot_id OR (r.bot_id = :template_bot_id AND r.item_div != 2))
				AND (a.start_date is NULL OR DATE_FORMAT(a.start_date,'%Y-%m-%d') <= :cur_date)
				$sql_status_cd_end_date
				AND (a.regular_start = :regular_date_kara 
						OR a.regular_end = :regular_date_kara 
						OR (a.regular_start <= a.regular_end AND a.regular_start <= :regular_date AND :regular_date <= a.regular_end)
						OR (a.regular_start > a.regular_end AND (a.regular_start <= :regular_date OR :regular_date <= a.regular_end))
						)
				AND r.recommend = 1
				ORDER BY 
					r.sort_no1";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
				':lang_cd' => $lang_cd,
				':cur_date' => date('Y-m-d'),
				':regular_date' => date('m-d'),
				':regular_date_kara' => "",
				':original_bot_id' => $original_bot_id,
				':template_bot_id' => $template_bot_id,
		));
		$results = $query->execute();
		return $results->as_array();
	}

	function get_item_class($lang_cd, $item_cd, $kind)
	{
		$sql = 'SELECT a.item_class_cd, a.item_class_name, a.skill_name
				FROM t_item_class a
				WHERE a.facility_cd = :facility_cd
				  AND a.lang_cd = :lang_cd';
		if ($kind==NULL) {
			$sql = $sql . '	ORDER BY a.seq';
			$params = array(
					':facility_cd' => $item_cd,
					':lang_cd' => $lang_cd);
		}
		else {
			$sql = $sql . '	AND a.kind = :kind';
			$sql = $sql . '	ORDER BY a.seq';
			$params = array(
					':facility_cd' => $item_cd,
					':lang_cd' => $lang_cd,
					':kind' => $kind,
					);
		}
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters($params);
		$results = $query->execute();
		return $results->as_array();
	}

	function get_item_classtype($lang_cd, $item_cd, $class_cd)
	{
		$sql = 'SELECT a.item_class_type_cd, a.item_class_type_name
				FROM t_item_class_type a
				WHERE a.facility_cd = :facility_cd
				  AND a.item_class_cd = :item_class_cd
				  AND a.lang_cd = :lang_cd
				ORDER BY a.seq';
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':facility_cd' => $item_cd,
				':item_class_cd' => $class_cd,
				':lang_cd' => $lang_cd,
		));
		$results = $query->execute();
		return $results->as_array();
	}

	// flow.js 根据用户输入暧昧检索item显示给用户
	// flow.js
	// recommend_item_portal.js
	function get_item_description_aimai($lang_cd, $bot_info, $keywords, $limit=6)
	{
		$original_bot_id = $bot_info['bot_id'];
		$bot_id = $bot_info['bot_id'];
		$bot_id = $this->get_bot_id_list_include_template_bot($bot_id);
		$template_bot_id = $this->get_template_bot_id($bot_id);
        // 2020.10.12 #6073
		$sql_status_cd_end_date = 
			" AND (a.end_date is NULL OR DATE_FORMAT(a.end_date,'%Y-%m-%d') >= :cur_date)
			AND a.item_status_cd in ('01','03','04','06','07','08')
			";
		// 2022.08.16
		if ($this->_full_index_another_table == "false") {
		$sql = "SELECT item_id, lang_cd, country_cd, item_status_cd, end_date, item_data,
					position_x, position_y,position_z,item_cd, item_div, class_cd, location_lat, location_lon, link_id, item_name, sell_point, 
					sell_point_line, tel, url, description, item_image, item_image_thumb, reserve1, btn1_name, btn1_url, btn2_name, btn2_url, btn3_name, btn3_url, 
					btn1_url_lang_cd, btn2_url_lang_cd, btn3_url_lang_cd, btn1_url_sp, btn2_url_sp, btn3_url_sp,score1, MAX(sort2) sort3
				FROM
				(
					SELECT DISTINCT a.item_id, b.lang_cd, a.country_cd, a.item_status_cd, a.item_cd, a.item_div, a.class_cd, a.location_lat, a.location_lon, a.link_id, a.end_date, a.item_data,
						a.position_x, a.position_y, a.position_z,
						b.item_name item_name, b.sell_point, b.sell_point_line, b.tel, b.url, b.description, b.item_image, b.item_image_thumb, b.reserve1, b.btn1_name, b.btn1_url, b.btn2_name, b.btn2_url, b.btn3_name, b.btn3_url, b.btn1_url_lang_cd, b.btn2_url_lang_cd, b.btn3_url_lang_cd, b.btn1_url_sp, b.btn2_url_sp, b.btn3_url_sp,b.show_image,
						MATCH(b.itemname_sellpoint_ft) AGAINST(:keywords IN BOOLEAN MODE) as score1, FLOOR(1000 + (RAND() * (2000 - 1000 + 1))) as sort2
					FROM t_item a
					INNER JOIN t_item_description b
						ON a.item_id = b.item_id
					INNER JOIN t_item_display r
						ON a.item_id = r.item_id
						AND a.item_div = r.item_div
						AND find_in_set(:lang_cd, r.lang_display)
						AND r.public_flg = 1
					WHERE
					r.bot_id in ($bot_id)
					AND (r.bot_id = :original_bot_id OR (r.bot_id = :template_bot_id AND r.item_div != 2))
					AND (a.start_date is NULL OR DATE_FORMAT(a.start_date,'%Y-%m-%d') <= :cur_date)
					$sql_status_cd_end_date
					AND (a.regular_start = :regular_date_kara 
						OR a.regular_end = :regular_date_kara 
						OR (a.regular_start <= a.regular_end AND a.regular_start <= :regular_date AND :regular_date <= a.regular_end)
						OR (a.regular_start > a.regular_end AND (a.regular_start <= :regular_date OR :regular_date <= a.regular_end))
						)
					AND	b.lang_cd = :lang_cd
					AND a.delete_flg <> 1
					AND MATCH(b.itemname_sellpoint_ft) AGAINST(:keywords IN BOOLEAN MODE)
				) t1
				GROUP BY item_id, lang_cd, country_cd, item_status_cd,position_x,position_y, position_z,
						 item_cd, item_div, class_cd, location_lat, location_lon, link_id, end_date, item_data, 
						item_name, sell_point, sell_point_line, tel, url, description, item_image, item_image_thumb, reserve1, 
						score1
				ORDER BY
					score1 DESC,
					sort3 
				LIMIT $limit
				";
		} else {
		$sql = "SELECT item_id, lang_cd, country_cd, item_status_cd, end_date, item_data,
					position_x, position_y,position_z,item_cd, item_div, class_cd, location_lat, location_lon, link_id, item_name, sell_point, 
					sell_point_line, tel, url, description, item_image, item_image_thumb, reserve1, btn1_name, btn1_url, btn2_name, btn2_url, btn3_name, btn3_url, 
					btn1_url_lang_cd, btn2_url_lang_cd, btn3_url_lang_cd, btn1_url_sp, btn2_url_sp, btn3_url_sp,score1, MAX(sort2) sort3
				FROM
				(
					SELECT DISTINCT a.item_id, b.lang_cd, a.country_cd, a.item_status_cd, a.item_cd, a.item_div, a.class_cd, a.location_lat, a.location_lon, a.link_id, a.end_date, a.item_data,
						a.position_x, a.position_y, a.position_z,
						b.item_name item_name, b.sell_point, b.sell_point_line, b.tel, b.url, b.description, b.item_image, b.item_image_thumb, b.reserve1, b.btn1_name, b.btn1_url, b.btn2_name, b.btn2_url, b.btn3_name, b.btn3_url, b.btn1_url_lang_cd, b.btn2_url_lang_cd, b.btn3_url_lang_cd, b.btn1_url_sp, b.btn2_url_sp, b.btn3_url_sp,b.show_image,
						MATCH(d.itemname_sellpoint_ft) AGAINST(:keywords IN BOOLEAN MODE) as score1, FLOOR(1000 + (RAND() * (2000 - 1000 + 1))) as sort2
					FROM t_item a
					INNER JOIN t_item_description b
						ON a.item_id = b.item_id
					INNER JOIN t_item_display r
						ON a.item_id = r.item_id
						AND a.item_div = r.item_div
						AND find_in_set(:lang_cd, r.lang_display)
						AND r.public_flg = 1
					INNER JOIN t_item_description_fulltext d
						ON b.item_id = d.item_id
						AND b.lang_cd = d.lang_cd
					WHERE
					r.bot_id in ($bot_id)
					AND (r.bot_id = :original_bot_id OR (r.bot_id = :template_bot_id AND r.item_div != 2))
					AND (a.start_date is NULL OR DATE_FORMAT(a.start_date,'%Y-%m-%d') <= :cur_date)
					$sql_status_cd_end_date
					AND (a.regular_start = :regular_date_kara 
						OR a.regular_end = :regular_date_kara 
						OR (a.regular_start <= a.regular_end AND a.regular_start <= :regular_date AND :regular_date <= a.regular_end)
						OR (a.regular_start > a.regular_end AND (a.regular_start <= :regular_date OR :regular_date <= a.regular_end))
						)
					AND	b.lang_cd = :lang_cd
					AND a.delete_flg <> 1
					AND MATCH(d.itemname_sellpoint_ft) AGAINST(:keywords IN BOOLEAN MODE)
				) t1
				GROUP BY item_id, lang_cd, country_cd, item_status_cd,position_x,position_y, position_z,
						 item_cd, item_div, class_cd, location_lat, location_lon, link_id, end_date, item_data, 
						item_name, sell_point, sell_point_line, tel, url, description, item_image, item_image_thumb, reserve1, 
						score1
				ORDER BY
					score1 DESC,
					sort3 
				LIMIT $limit
				";
		}

		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':item_status_cd' => '01',
			':bot_id' => $bot_id,
			':lang_cd' => $lang_cd,
			':keywords' => $keywords,
			':cur_date' => date('Y-m-d'),
			':regular_date' => date('m-d'),
			':regular_date_kara' => "",
			':original_bot_id' => $original_bot_id,
			':template_bot_id' => $template_bot_id,
		));

		$results = $query->execute();
		return $results->as_array();
	}

	// carousel_detail.js,floor_info.js,floor_map.js,floor_map.js,recommend_item_aimai.js,
	function get_item_description($condition)
	{
		$item_id = $condition['item_id'];
		$lang_cd = $condition['lang_cd'];
		// 获得某个指定item_id的t_item_description
		// engine使用者
		//	通过botapi.get_item_description
		//		skill\recommend_item_aimai.js
		// 2020.10.12 #6073
		// 指定特定item_id的时候，所有item_status_cd都检索
		$sql_status_cd_end_date = 
			" AND (a.end_date is NULL 
				OR (a.item_status_cd in ('01','08') AND DATE_FORMAT(a.end_date,'%Y-%m-%d') >= :cur_date)
				OR a.item_status_cd in ('02','03','04','05','06','07')
			  )
			  AND a.item_status_cd in ('01','02','03','04','05','06','07','08')
			";
		// 2020.10.12 #6073
		$sql = "SELECT a.item_div, a.class_cd, a.item_cd, a.location_lat, a.location_lon, a.link_id, a.position_x, a.position_y, a.position_z, a.item_status_cd, a.end_date, a.item_data,
					b.item_id, b.lang_cd,
					b.item_name item_name, b.sell_point, b.sell_point_line, b.tel, b.url, b.description, b.item_image, b.item_image_thumb, b.reserve1,
					b.btn1_name, b.btn1_url, b.btn2_name, b.btn2_url, b.btn3_name, b.btn3_url, b.btn1_url_lang_cd, b.btn2_url_lang_cd, b.btn3_url_lang_cd,
					b.btn1_url_sp, b.btn2_url_sp, b.btn3_url_sp, b.show_image
				FROM t_item_description b
				INNER JOIN t_item a
				ON a.item_id = b.item_id
				WHERE
					b.lang_cd = :lang_cd
				AND b.item_id = :item_id
				AND a.delete_flg <> 1
				AND (a.start_date is NULL OR DATE_FORMAT(a.start_date,'%Y-%m-%d') <= :cur_date)
				$sql_status_cd_end_date
				AND (a.regular_start = :regular_date_kara 
					OR a.regular_end = :regular_date_kara 
					OR (a.regular_start <= a.regular_end AND a.regular_start <= :regular_date AND :regular_date <= a.regular_end)
					OR (a.regular_start > a.regular_end AND (a.regular_start <= :regular_date OR :regular_date <= a.regular_end))
					)
				";

		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':item_id' => $item_id,
			':lang_cd' => $lang_cd,
			':cur_date' => date('Y-m-d'),
			':regular_date' => date('m-d'),
			':regular_date_kara' => "",
		));

		$results = $query->execute();
		return $results->as_array();
	}
	// wuzhao add end #111

	function search_recommend_items_by_item_id($condition)
	{
		$item_id = $condition["item_id"];
		$lang_cd = $condition["lang_cd"];
		$mode = "";
		if (array_key_exists("mode", $condition)) {
			$mode = $condition["mode"];
		}		
		// 获得某个指定item_id的t_item_description
		// engine使用者
		//		skill\recommend_items_by_item_id.js
		//		skill\action_service_select.js
        // 2020.10.12 #6073
		if ($mode == "") {
			// 默认模式，只取item_status_cd = "01"的数据
			/*
			$sql_status_cd_end_date = 
			" AND (a.end_date is NULL OR DATE_FORMAT(a.end_date,'%Y-%m-%d') >= :cur_date)
			  AND a.item_status_cd in ('01','03','04')
			";
			*/
			// 2021.03.26 #9905改为03,04也显示
			$sql_status_cd_end_date = 
			" AND (a.end_date is NULL 
				OR (a.item_status_cd in ('01','08') AND DATE_FORMAT(a.end_date,'%Y-%m-%d') >= :cur_date)
				OR a.item_status_cd in ('03','04','06','07')
			  )
			  AND a.item_status_cd in ('01','03','04','06','07','08')
			";
		} else {	// mode:keyword
			$sql_status_cd_end_date = 
			" AND (a.end_date is NULL 
				OR (a.item_status_cd in ('01','08') AND DATE_FORMAT(a.end_date,'%Y-%m-%d') >= :cur_date)
				OR a.item_status_cd in ('02','03','04','05','06','07')
			  )
			  AND a.item_status_cd in ('01','02','03','04','05','06','07','08')
			";
		}
		$sql = "SELECT distinct a.item_div, a.class_cd, a.item_cd, a.location_lat, a.location_lon, a.link_id, a.item_status_cd, a.end_date, a.item_data,
					a.position_x, a.position_y, a.position_z,
					b.item_id, b.lang_cd,
					b.item_name item_name, b.sell_point, b.sell_point_line, b.tel, b.url, b.description, b.item_image, b.item_image_thumb, b.reserve1,
					b.btn1_name, b.btn1_url, b.btn2_name, b.btn2_url, b.btn3_name, b.btn3_url, b.btn1_url_lang_cd, b.btn2_url_lang_cd, b.btn3_url_lang_cd,
					b.btn1_url_sp, b.btn2_url_sp, b.btn3_url_sp, b.show_image,
					find_in_set(a.item_id,:item_id) as sort1
				FROM t_item_description b
				INNER JOIN t_item a
				ON a.item_id = b.item_id
				INNER JOIN t_item_display r
					ON a.item_id = r.item_id
					AND a.item_div = r.item_div
					AND find_in_set(:lang_cd, r.lang_display)
					AND r.public_flg = 1
				WHERE
					b.lang_cd = :lang_cd
				AND a.delete_flg <> 1
				AND find_in_set(b.item_id,:item_id) > 0
				AND (a.start_date is NULL OR DATE_FORMAT(a.start_date,'%Y-%m-%d') <= :cur_date)
				$sql_status_cd_end_date
				AND (a.regular_start = :regular_date_kara 
					OR a.regular_end = :regular_date_kara 
					OR (a.regular_start <= a.regular_end AND a.regular_start <= :regular_date AND :regular_date <= a.regular_end)
					OR (a.regular_start > a.regular_end AND (a.regular_start <= :regular_date OR :regular_date <= a.regular_end))
					)
				ORDER BY
					sort1
				";

		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':item_id' => $item_id,
			':lang_cd' => $lang_cd,
			':cur_date' => date('Y-m-d'),
			':regular_date' => date('m-d'),
			':regular_date_kara' => "",
		));

		$results = $query->execute();
		return $results->as_array();
	}
	
	/*wuzhao delete 2019.08.06
	function get_bot_items($bot_id, $lang_cd)
	{
		//根据指定bot_id(可以是逗号隔开的复数个bot_id)，获得该bot_id某种lang_cd下的t_item_description
		//engine使用者
		//	通过botapi.get_facilitys 
		//		skill\access_method.js
		//		skill\bot_welcome.js
		//		skill\bot_welcome_sub.js
		//		skill\bot_welcome_sub_input_unknown.js
		$sql = "SELECT a.item_id, a.item_cd, b.lang_cd,
					b.item_name item_name, b.sell_point, b.sell_point_line, b.tel, b.url, b.description,
					b.item_image, b.item_image_thumb, b.reserve1
				FROM t_item a 
				INNER JOIN t_item_description b
					ON a.item_id = b.item_id
				WHERE
					b.lang_cd = :lang_cd
				AND find_in_set(a.item_cd,:bot_id) > 0
				";

		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':lang_cd' => $lang_cd,
				':bot_id' => $bot_id,
		));

		$results = $query->execute();
		return $results->as_array();
	}
	*/

	// wuzhao add begin #63
	function get_users($bot_id)
	{
		$sql = "SELECT *
                 FROM t_user WHERE bot_id = :bot_id";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
		));
		$results = $query->execute();
		return $results->as_array();
	}

	// 2021.05.01 增加intent_div,说是flow里通过设定intent_div，从而细分发送的邮件目标
	function get_users_by_condition($condition)
	{
		$bot_id = $condition["bot_id"];
		// 2021.11.09 父bot能接受子bot的通知
		$parent_bot_id = $this->get_grp_parent_bot_id($bot_id);
		// 2021.11.09 父bot能接受子bot的通知 end

		$intent_cd = $condition["intent_cd"];
		$intent_div = "";
		if (isset($condition['intent_div'])) {
			$intent_div = $condition['intent_div'];
		}
		if ($intent_div == "") {
			$sql_intent_div = "";
		} else {
			$sql_intent_div = "AND (b.intent_div = '' OR b.intent_div = :intent_div)";
		}
		// 2021.08.10 #15052 begin 
		$scene_cd = "";
		if (isset($condition['scene_cd'])) {
			$scene_cd = $condition['scene_cd'];
		}
		// 2021.08.10 #15052 end
		$sql = "SELECT DISTINCT a.*, b.send_type
                FROM t_user a
				LEFT JOIN t_user_notify b
				ON a.bot_id = b.bot_id
				AND a.user_id = b.user_id
				WHERE (a.bot_id = :bot_id OR a.bot_id = :parent_bot_id)
				AND b.intent_cd = :intent_cd
				AND (b.scene_cd = '' OR b.scene_cd = :scene_cd)
				$sql_intent_div
				";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
				':parent_bot_id' => $parent_bot_id,
				':intent_cd' => $intent_cd,
				':intent_div' => $intent_div,
				':scene_cd' => $scene_cd,
		));
		$results = $query->execute();
		return $results->as_array();
	}
	// wuzhao add end #63

	// wuzhao add begin #232
	function get_access_method($bot_id, $lang_cd, $access_from, $access_to, $sightseeing_spot_access_to)
	{
		$sql = "SELECT a.access_method
				FROM t_access_method a
				WHERE
					a.bot_id = :bot_id
					and a.lang_cd = :lang_cd
				";

		if ($access_from != NULL) {
			$sql = "$sql AND a.access_from = :access_from ";
		} else {
			$sql = "$sql AND (a.access_from IS NULL OR a.access_from = '')";
		}
		if ($access_to != NULL) {
			$sql = "$sql AND a.access_to = :access_to ";
		} else {
			$sql = "$sql AND (a.access_to IS NULL OR a.access_to = '')";
		}
		if ($sightseeing_spot_access_to != NULL) {
			$sql = "$sql AND a.sightseeing_spot_access_to = :sightseeing_spot_access_to ";
		} else {
			$sql = "$sql AND (a.sightseeing_spot_access_to IS NULL OR a.sightseeing_spot_access_to = '')";
		}

		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':bot_id' => $bot_id,
			':lang_cd' => $lang_cd,
			':access_from' => $access_from,
			':access_to' => $access_to,
			':sightseeing_spot_access_to' => $sightseeing_spot_access_to,
		));

		$results = $query->execute();
		return $results->as_array();
	}
	// wuzhao add end #232

	// wuzhao add begin for menu_group
	// 2021.10.18 改善 #17388 begin 增加$sns_cd参数
	function get_menu_group($bot_id, $menu_group_id, $lang_cd, $sns_cd)
	{
		$v = is_numeric ($menu_group_id) ? true : false;
		if ($v == false) {
			return $this->get_menu_group_from_t_bot_msg($bot_id, $menu_group_id, $lang_cd, $sns_cd);
		}

		$sql = "SELECT *
				FROM t_bot_menu_group a
				WHERE
					a.bot_id = :bot_id
					AND a.menu_group_id = :menu_group_id
					AND a.lang_cd = :lang_cd
					AND (a.menu_sub_group_text IS NOT NULL AND a.menu_sub_group_text != '')
				ORDER BY
					a.sortno
				";

		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':bot_id' => $bot_id,
			':menu_group_id' => $menu_group_id,
			':lang_cd' => $lang_cd,
		));

		$results = $query->execute()->as_array();

		if (count($results) > 0) {
			return $results;
		}

		// 没取到数据，试图查看t_bot_setting看是否有basebot
		$base_bot_id = 0;
		$results = $this->get_bot_setting($bot_id, "template_bot");
		if (count($results) > 0) {
			$base_bot_id = $results[0]["setting_value"];

			$query = DB::query(Database::SELECT, $sql);
			$query->parameters(array(
				':bot_id' => $base_bot_id,
				':menu_group_id' => $menu_group_id,
				':lang_cd' => $lang_cd,
			));
	
			$results = $query->execute()->as_array();

			if (count($results) > 0) {
				return $results;
			}
		}

		// 取父bot_id
		$parent_bot_id = $this->get_grp_parent_bot_id($bot_id);
		if ($parent_bot_id != -1 && $parent_bot_id != $bot_id) {
			$query = DB::query(Database::SELECT, $sql);
			$query->parameters(array(
				':bot_id' =>  $parent_bot_id,
				':menu_group_id' => $menu_group_id,
				':lang_cd' => $lang_cd,
			));
	
			$results = $query->execute()->as_array();

			if (count($results) > 0) {
				return $results;
			}
		}

		// 最后试图从bot_id=0检索
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':bot_id' => 0,
			':menu_group_id' => $menu_group_id,
			':lang_cd' => $lang_cd,
		));
		
		$results = $query->execute()->as_array();
		return $results;
	}

	// 2021.10.18 改善 #17388 begin 增加$sns_cd参数
	function get_menu_group_from_t_bot_msg($bot_id, $menu_group_id, $lang_cd, $sns_cd)
	{
		// 2022.04.04 #21724 增加style字段
		// 取bot的设定
		$sql = "SELECT 
					bot_id AS bot_id,
					a.msg_cd AS menu_group_id,
					b.lang_cd AS lang_cd,
					b.title AS menu_title,
					b.content AS menu_sub_group_text,
					b.url AS menu_sub_group_cmd,
					b.style AS style,
					3 AS display_mode
				FROM t_bot_msg a
				INNER JOIN t_bot_msg_desc_lst b
					ON a.msg_id = b.msg_id
				WHERE
					a.msg_cd = :menu_group_id
					AND (b.content IS NOT NULL AND b.content != '')
					AND b.lang_cd = :lang_cd
					AND a.delete_flg = 0
					AND b.delete_flg = 0
					AND bot_id = :bot_id
					AND (:sns_cd = '' OR b.sns_cd IS NULL OR b.sns_cd = '' OR find_in_set(:sns_cd, b.sns_cd))
				ORDER BY 
					b.no
			";

		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':bot_id' => $bot_id,
			':menu_group_id' => $menu_group_id,
			':lang_cd' => $lang_cd,
			':sns_cd' => $sns_cd, // 2021.10.18 改善 #17388 begin 增加$sns_cd参数
		));

		$results = $query->execute()->as_array();

		if (count($results) > 0) {
			return $results;
		}

		// 没取到数据，试图查看t_bot_setting看是否有template_bot
		$base_bot_id = 0;
		$results = $this->get_bot_setting($bot_id, "template_bot");
		if (count($results) > 0) {
			$base_bot_id = $results[0]["setting_value"];

			$query = DB::query(Database::SELECT, $sql);
			$query->parameters(array(
				':bot_id' => $base_bot_id,
				':menu_group_id' => $menu_group_id,
				':lang_cd' => $lang_cd,
				':sns_cd' => $sns_cd, // 2021.10.18 改善 #17388 begin 增加$sns_cd参数
			));
	
			$results = $query->execute()->as_array();

			if (count($results) > 0) {
				return $results;
			}
		}

		// 取父bot_id
		$parent_bot_id = $this->get_grp_parent_bot_id($bot_id);
		if ($parent_bot_id != -1 && $parent_bot_id != $bot_id) {
			$query = DB::query(Database::SELECT, $sql);
			$query->parameters(array(
				':bot_id' =>  $parent_bot_id,
				':menu_group_id' => $menu_group_id,
				':lang_cd' => $lang_cd,
				':sns_cd' => $sns_cd, // 2021.10.18 改善 #17388 begin 增加$sns_cd参数
			));
	
			$results = $query->execute()->as_array();

			if (count($results) > 0) {
				return $results;
			}
		}

		// 取共通设定(bot_id=0)
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':bot_id' => 0,
			':menu_group_id' => $menu_group_id,
			':lang_cd' => $lang_cd,
			':sns_cd' => $sns_cd, // 2021.10.18 改善 #17388 begin 增加$sns_cd参数
		));
		
		$results = $query->execute()->as_array();
		return $results;
	}	
	// wuzhao add end for menu_group

	// wuzhao add begin for tl-lincoln reserve
	function get_tl_reserve_data($bot_id, $post)
	{
		if (array_key_exists("tl_reserve_id", $post)) {
			$sql = "SELECT *
					FROM t_tl_reserve a
					WHERE
						a.sns_type_cd = :sns_type_cd
						and a.sns_id = :sns_id
						and a.member_id = :member_id
						and a.bot_id = :bot_id
						and a.hotel_code = :hotel_code
						and a.tl_reserve_id = :tl_reserve_id
					ORDER BY
						a.reserve_time DESC
					";

			$query = DB::query(Database::SELECT, $sql);
			$query->parameters(array(
				':sns_type_cd' => $post['sns_type_cd'],
				':sns_id' => $post['sns_id'],
				':member_id' => $post['member_id'],
				':bot_id' => $bot_id,
				':hotel_code' => $post['hotel_code'],
				':tl_reserve_id' => $post['tl_reserve_id'],
			));

			$results = $query->execute();
			return $results->as_array();
		} else {
			$sql = "SELECT a.*,b.record_count
					FROM t_tl_reserve a
					inner join
						(SELECT tl_reserve_id,MAX(reserve_time) as max_reserve_time,count(reserve_time) as record_count
						FROM t_tl_reserve a
						WHERE
							a.sns_type_cd = :sns_type_cd
							and a.sns_id = :sns_id
							and a.member_id = :member_id
							and a.bot_id = :bot_id
							and a.hotel_code = :hotel_code
						GROUP BY
							a.tl_reserve_id
						) b
					on
						a.tl_reserve_id = b.tl_reserve_id
						and a.reserve_time = b.max_reserve_time
					order by
						a.reserve_time DESC
					";

			$query = DB::query(Database::SELECT, $sql);
			$query->parameters(array(
				':sns_type_cd' => $post['sns_type_cd'],
				':sns_id' => $post['sns_id'],
				':member_id' => $post['member_id'],
				':bot_id' => $bot_id,
				':hotel_code' => $post['hotel_code'],
			));

			$results = $query->execute();
			return $results->as_array();
		}
	}
	// wuzhao add end for tl-lincoln reserve


	function get_nearby_hotel($post)
	{
		$sql = "SELECT *
				FROM
					(SELECT *,
						ROUND(
						6378.138 * 2 * ASIN(
							SQRT(
								POW(
									SIN(
										(
											:lat * PI() / 180 - a.lat * PI() / 180
										) / 2
									),
									2
								) + COS(:lat * PI() / 180) * COS(a.lat * PI() / 180) * POW(
									SIN(
										(
											:lng * PI() / 180 - a.lng * PI() / 180
										) / 2
									),
									2
								)
							)
						) * 1000) distance
					FROM t_bot_hotel a
					WHERE
						display_flg = '表示') b
				WHERE
					(:range = 0 OR b.distance <= :range)
				ORDER BY
					b.sort ASC,
					b.distance
				";

		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':lat' => $post["lat"],
			':lng' => $post["lng"],
			':range' => $post["range"],
		));

		$results = $query->execute();
		return $results->as_array();
	}

	function get_ots_hotel($post)
	{
		$sql = "SELECT *
				FROM
					(SELECT *,
						ROUND(
								6378.138 * 2 * ASIN(
									SQRT(
										POW(
											SIN(
												(
													:lat * PI() / 180 - a.lat * PI() / 180
												) / 2
											),
											2
										) + COS(:lat * PI() / 180) * COS(a.lat * PI() / 180) * POW(
											SIN(
												(
													:lng * PI() / 180 - a.lng * PI() / 180
												) / 2
											),
											2
										)
									)
								) * 1000) distance
						FROM t_bot_hotel a
						WHERE
							little_area_code = :area
							AND (
									(:type = 'Resorthotel' and resort_hotel = 1)
									OR (:type = 'Cityhotels' and city_hotel = 1)
									OR (:type = 'Businesshotels' and business_hotel = 1)
									OR (:type = 'Condominium' and condominium = 1)
									OR (:type = 'Japanesestylehotels' and hotel = 1)
									OR (:type = 'GuestHouses' and minshuku = 1)
									OR (:type = 'Pensions' and pension = 1)
									OR (:type = 'DormitoryGuesthouse' and dormitory_guesthouse = 1)
									OR (:type = 'Rentalhouse' and single_loan = 1)
									OR (:type = 'Othertypehotels' and other = 1)
								)
							AND display_flg = '表示') b
				ORDER BY
				    b.sort ASC,
					b.distance
				";

		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':area' => $post["area"],
			':type' => $post["type"],
			':lat' => $post["lat"],
			':lng' => $post["lng"],
		));

		$results = $query->execute();
		return $results->as_array();
	}

	function get_nearby_business_office($post)
	{
		$sql = "SELECT *
				FROM
					(SELECT *,
						ROUND(
						6378.138 * 2 * ASIN(
							SQRT(
								POW(
									SIN(
										(
											:lat * PI() / 180 - a.lat * PI() / 180
										) / 2
									),
									2
								) + COS(:lat * PI() / 180) * COS(a.lat * PI() / 180) * POW(
									SIN(
										(
											:lng * PI() / 180 - a.lng * PI() / 180
										) / 2
									),
									2
								)
							)
						) * 1000) distance
					FROM t_bot_business_office a
					) b
				WHERE
					(:range = 0 OR b.distance <= :range)
				ORDER BY
					b.distance
				";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':lat' => $post["lat"],
			':lng' => $post["lng"],
			':range' => $post["range"],
		));

		$results = $query->execute();
		return $results->as_array();
	}

	function get_all_business_office($post)
	{
		$sql = "SELECT *
				FROM t_bot_business_office a
				ORDER BY
					a.no
				";
		$query = DB::query(Database::SELECT, $sql);

		$results = $query->execute();
		return $results->as_array();
	}

	function get_autoanswer_flow($bot_id, $post)
	{
		$node_id = NULL;
		if (array_key_exists("node_id", $post)) {
			$node_id = $post["node_id"];
		}

			$sql = "SELECT a.bot_id, a.flow_id, a.start_node_id, a.intent_cd, a.description, a.interruptible_flg, a.flow_text, g.content flow_text_content,
							b.node_id, b.node_text, d.content node_text_content,b.node_post_text, b.node_interruptible_flg, b.node_register_log_id_flg, 
							f.content node_post_text_content,
							c.action_id, c.action_type, c.action_parameters, c.action_text, e.content action_text_content, c.action_next_node_id, c.action_next_skill, c.action_input_parameter_name, c.action_input_parameter_value,
							c.action_input_check_expression,c.action_input_parameter_type,c.action_next_node_id_when_cancel,c.action_next_skill_when_cancel,c.action_show_cancel_flg,c.action_can_click_forever,
							c.action_image, h.content action_image_content,
							a.flow_status_flg, b.node_enter_status, c.action_execute_status,
							a.mail_title,i.content mail_title_content
					FROM t_autoanswer_flow a
					INNER JOIN t_autoanswer_node b
						ON a.bot_id = b.bot_id
						AND a.flow_id = b.flow_id
					LEFT JOIN t_autoanswer_action c
						ON a.bot_id = c.bot_id
						AND a.flow_id = c.flow_id
						AND b.node_id = c.node_id
					LEFT JOIN t_autoanswer_text d
						ON a.bot_id = d.bot_id
						AND a.flow_id = d.flow_id
						AND b.node_text = d.text_id
						AND d.lang_cd = :lang_cd
					LEFT JOIN t_autoanswer_text f
						ON a.bot_id = f.bot_id
						AND a.flow_id = f.flow_id
						AND b.node_post_text = f.text_id
						AND f.lang_cd = :lang_cd
					LEFT JOIN t_autoanswer_text e
						ON a.bot_id = e.bot_id
						AND a.flow_id = e.flow_id
						AND c.action_text = e.text_id
						AND e.lang_cd = :lang_cd
					LEFT JOIN t_autoanswer_text g
						ON a.bot_id = g.bot_id
						AND a.flow_id = g.flow_id
						AND a.flow_text = g.text_id
						AND g.lang_cd = :lang_cd
					LEFT JOIN t_autoanswer_text h
						ON a.bot_id = h.bot_id
						AND a.flow_id = h.flow_id
						AND c.action_image = h.text_id
						AND h.lang_cd = :lang_cd
					LEFT JOIN t_autoanswer_text i
						ON a.bot_id = i.bot_id
						AND a.flow_id = i.flow_id
						AND a.mail_title = i.text_id
						AND i.lang_cd = 'ja'
					WHERE
						a.bot_id = :bot_id
						AND a.flow_id = :flow_id
					";
			if ($node_id != NULL && $node_id !="") {
				$sql = "$sql AND b.node_id = :node_id ";
			} else {
				$sql = "$sql AND b.node_id = a.start_node_id ";
			}

			$sql = "$sql ORDER BY c.action_id ";

			$query = DB::query(Database::SELECT, $sql);
			$query->parameters(array(
				':bot_id' => $bot_id,
				':flow_id' => $post['flow_id'],
				':node_id' => $node_id,
				':lang_cd' => $post['lang_cd'],
			));

			$results = $query->execute()->as_array();

			if (count($results) != 0) {
				return $results;
			}
			
			// 2020.03.11 试图取group parent bot的flow
			$parent_bot_id = $this->get_grp_parent_bot_id($bot_id);
			if ($parent_bot_id != -1 && $parent_bot_id != $bot_id) {
				// 是group，而且还没取过父bot的flow
				$query = DB::query(Database::SELECT, $sql);
				$query->parameters(array(
					':bot_id' => $parent_bot_id,
					':flow_id' => $post['flow_id'],
					':node_id' => $node_id,
					':lang_cd' => $post['lang_cd'],
				));

				$results = $query->execute()->as_array();
				if (count($results) != 0) {
					return $results;
				}
			}
	
			// 最后才尝试去取bot_id=0的flow
			$query = DB::query(Database::SELECT, $sql);
			$query->parameters(array(
				':bot_id' => 0,
				':flow_id' => $post['flow_id'],
				':node_id' => $node_id,
				':lang_cd' => $post['lang_cd'],
			));

			$results = $query->execute()->as_array();
			return $results;
	}

	function get_autoanswer_text($condition)
	{
		$bot_id = $condition["bot_id"];
		$flow_id = $condition["flow_id"];
		$lang_cd = $condition["lang_cd"];

			$sql = "SELECT a.bot_id, a.flow_id, a.start_node_id, a.intent_cd,
							e.text_id, e.content text_content
					FROM t_autoanswer_flow a
					LEFT JOIN t_autoanswer_text e
						ON a.bot_id = e.bot_id
						AND a.flow_id = e.flow_id
						AND e.lang_cd = :lang_cd
					WHERE
						a.bot_id = :bot_id
						AND a.flow_id = :flow_id
					";

			$query = DB::query(Database::SELECT, $sql);
			$query->parameters(array(
				':bot_id' => $bot_id,
				':flow_id' => $flow_id,
				':lang_cd' => $lang_cd,
			));

			$results = $query->execute()->as_array();

			if (count($results) != 0) {
				return $results;
			}

			// 2020.03.11 试图取group parent bot的flow
			$parent_bot_id = $this->get_grp_parent_bot_id($bot_id);
			if ($parent_bot_id != -1 && $parent_bot_id != $bot_id) {
				// 是group，而且还没取过父bot的flow
				$query = DB::query(Database::SELECT, $sql);
				$query->parameters(array(
					':bot_id' => $parent_bot_id,
					':flow_id' => $flow_id,
					':lang_cd' => $lang_cd,
				));

				$results = $query->execute()->as_array();
				if (count($results) != 0) {
					return $results;
				}
			}
				
			$query = DB::query(Database::SELECT, $sql);
			$query->parameters(array(
				':bot_id' => 0,
				':flow_id' => $flow_id,
				':lang_cd' => $lang_cd,
			));

			$results = $query->execute()->as_array();
			return $results;
	}

	function get_keyvalue_map($bot_id, $post)
	{
		$parent_bot_id = $this->get_grp_parent_bot_id($bot_id);
		if ($parent_bot_id == -1) {
			$parent_bot_id = $bot_id;
		}
		$purpose = "parameter";
		if (array_key_exists("purpose", $post)) {
			$purpose = $post["purpose"];
		}
		$sql_purpose = "";
		if ($purpose == "parameter") {
			$sql_purpose = "AND (a.intent_name = '' or a.intent_name = :intent_name)
							AND (a.purpose_type = 1 OR a.purpose_type = 3)
			";
		} else if ($purpose == "bot") {
			// bot分析时不用看intent_name
			$sql_purpose = "AND (a.purpose_type = 2 OR a.purpose_type = 3)";
		}
		$sql = "SELECT a.*
				FROM m_keyvalue_map a
				WHERE
					(a.bot_id = 0 OR a.bot_id = :bot_id OR a.bot_id = :parent_bot_id)
					$sql_purpose
				ORDER BY a.bot_id
				";

		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':bot_id' => $bot_id,
			':parent_bot_id' => $parent_bot_id,
			':intent_name' => $post["intent_name"],
		));

		$results = $query->execute();
		return $results->as_array();
	}

	function get_car_data($post)
	{
		$persons = $post["persons"];
		$suitcase = $post["suitcase"];
		$lang_cd = $post["lang_cd"];

		$sql = "SELECT DISTINCT a.*,b.*
				FROM t_bot_car_class a
				INNER JOIN t_bot_car_type b
					ON a.type_cd = b.type_cd
				INNER JOIN t_bot_car_person_suitcase c
					ON a.no = c.no
				WHERE
					c.person >= :persons
					AND c.suitcase >= :suitcase
					AND (
						(:lang_cd = 'ja' AND a.display_ja = 1 AND b.display_ja = 1)
						OR
						(:lang_cd = 'en' AND a.display_en = 1 AND b.display_en = 1)
						OR
						(:lang_cd = 'tw' AND a.display_tw = 1 AND b.display_tw = 1)
						OR
						(:lang_cd = 'kr' AND a.display_kr = 1 AND b.display_kr = 1)
						OR
						(:lang_cd = 'cn' AND a.display_cn = 1 AND b.display_cn = 1)
					)
				ORDER BY
					a.comfortable_person, a.comfortable_person_suitcase
				";

		$query = DB::query(Database::SELECT, $sql);

		$query->parameters(array(
			':persons' => $persons,
			':suitcase' => $suitcase,
			':lang_cd' => $lang_cd,
		));

		$results = $query->execute();
		return $results->as_array();
	}
	
	function get_ownedmedia_data($condition, $bot_info)
	{
		$area = "";
		$subarea =  "";
		$category =  "";
		$subcategory =  "";
		$keyword =  "";
		$keyword_array =  "";
		$lang_cd = $condition["lang_cd"];
		if (isset($condition['area'])) {
			$area = $condition['area'];
		}
		if (isset($condition['subarea'])) {
			$subarea = $condition['subarea'];
		}
		if (isset($condition['category'])) {
			$category = $condition['category'];
		}
		if (isset($condition['subcategory'])) {
			$subcategory = $condition['subcategory'];
		}
		if (isset($condition['keyword'])) {
			$keyword = $condition['keyword'];
		}
		if (isset($condition['keyword_array'])) {
			$keyword_array = $condition['keyword_array'];
		}


		$sql = "";
		if ($keyword_array == "") {
			$sql = "SELECT a.*, 1 as score_title, 1 as score_tag, 1 as score_desc
			";
		} else {
			$sql = " SELECT 
				a.*, 
				CASE WHEN a.pickup=1 THEN 0 ELSE MATCH(a.search_title_ft) AGAINST(:keyword_array IN BOOLEAN MODE) END AS score_title,
				CASE WHEN a.pickup=1 THEN 0 ELSE MATCH(a.search_tag_ft) AGAINST(:keyword_array IN BOOLEAN MODE) END AS score_tag,
				CASE WHEN a.pickup=1 THEN 0 ELSE MATCH(a.search_desc_ft) AGAINST(:keyword_array IN BOOLEAN MODE) END AS score_desc
			";
		};

		$sql = "$sql FROM t_bot_ots_ownedmedia a
				WHERE
					a.bot_id = :bot_id
					AND a.lang_cd = :lang_cd
					AND a.is_publish = 1
					AND a.title IS NOT NULL
					AND a.title <> ''
					AND (a.pickup = 1
						OR (1=1
				";

		if ($area == "") {
		} else {
			$sql = "$sql AND a.parent_area like '%$area%'
			";
		};

		if ($subarea == "" || $subarea == "allsubarea") {
		} else {
			$sql = "$sql AND a.child_area like '%$subarea%'
			";
		};
		if ($category == "") {
		} else {
			$sql = "$sql AND a.parent_cat like '%$category%'
			";
		};
		if ($subcategory == "" || $subcategory == "allsubcategory") {
		} else {
			$sql = "$sql AND a.child_cat like '%$subcategory%'
			";
		};
		/*
		if ($keyword == "") {
		} else {
			$sql = "$sql AND a.title like '%$keyword%' OR a.description like '%$keyword%' OR a.tag like '%$keyword%'
			";
		};
		*/
		if ($keyword_array == "") {
		} else {
			$sql = "$sql 
					AND (MATCH(a.search_title_ft) AGAINST(:keyword_array IN BOOLEAN MODE)
								OR MATCH(a.search_tag_ft) AGAINST(:keyword_array IN BOOLEAN MODE)
								OR MATCH(a.search_desc_ft) AGAINST(:keyword_array IN BOOLEAN MODE)
							)
			";
		};
		$sql = "$sql ))
		";

		$sql = "$sql
				ORDER BY
					pickup,
					score_title DESC,
					score_tag DESC,
					score_desc DESC,
					a.post_date DESC
				";

		$query = DB::query(Database::SELECT, $sql);

		$query->parameters(array(
			':bot_id' => $bot_info['bot_id'],
			':lang_cd' => $lang_cd,
			':area' => $area,
			':subarea' => $subarea,
			':category' => $category,
			':subcategory' => $subcategory,
			':keyword' => $keyword,
			':keyword_array' => $keyword_array,
		));

		$results = $query->execute();
		return $results->as_array();
	}


	function search_bot_stock_day($condition)
	{
		$stock_cd = "";
		$after_days = "";
		if (isset($condition['stock_cd'])) {
			$stock_cd = $condition['stock_cd'];
		}
		if (isset($condition['after_days'])) {
			$after_days = $condition['after_days'];
		}
		if ($stock_cd == "") {
			return NULL;
		}
		// 先取出bot的stock_id
		$stock_id = "";
		$stock_type_cd = "";
		$sql = 'SELECT *
				FROM t_bot_stock a
				WHERE a.bot_id = :bot_id
				AND a.stock_cd = :stock_cd
				';
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $condition["bot_id"],
				':stock_cd' => $condition["stock_cd"],
		));
		$results = $query->execute()->as_array();
		if (count($results) > 0) {
			$stock_id = $results[0]['stock_id'];
			$stock_type_cd = $results[0]['stock_type_cd'];
		} else {
			return NULL;
		}

		// 根据stock_id取出day一览
		if ($stock_type_cd == "02") {
			$begin_date = date('Y-m-d');
			$begin_time = date('H:i:s');

			if ($after_days != "") {
				$begin_date = date('Y-m-d',strtotime("+$after_days day"));
				$begin_time = "00:00:00";
			}
			$sql = 'SELECT 
						distinct stock_date
					FROM 
						t_bot_stock_time a
					WHERE 
						a.stock_id = :stock_id
						AND a.stock > 0
						AND (a.stock_date > :stock_date 
							OR (a.stock_date = :stock_date AND a.start_time <= :stock_time AND a.end_time > :stock_time)
							OR (a.stock_date = :stock_date AND a.start_time >= :stock_time)
							)
					ORDER BY 
						a.stock_date
					';
			$query = DB::query(Database::SELECT, $sql);
			$query->parameters(array(
					':stock_id' => $stock_id,
					':stock_date' => $begin_date,
					':stock_time' => $begin_time,
				));
			$results = $query->execute()->as_array();
		} else if ($stock_type_cd == "01") {
			// TODO
			return NULL;
		}

		return $results;
	}
	
	function search_bot_stock_time($condition)
	{
		// 先取出bot的stock_id
		$stock_id = "";
		$stock_type_cd = "";
		$sql = 'SELECT *
				FROM t_bot_stock a
				WHERE a.bot_id = :bot_id
				';
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $condition["bot_id"],
		));
		$results = $query->execute()->as_array();
		if (count($results) > 0) {
			$stock_id = $results[0]['stock_id'];
			$stock_type_cd = $results[0]['stock_type_cd'];
		} else {
			return NULL;
		}

		// 根据stock_id取出day一览
		if ($stock_type_cd == "02") {
			$sql = 'SELECT 
						start_time,
						end_time,
						stock
					FROM 
						t_bot_stock_time a
					WHERE 
						a.stock_id = :stock_id
						AND a.stock_date = :stock_date 
					ORDER BY 
						a.start_time
					';
			$query = DB::query(Database::SELECT, $sql);
			$query->parameters(array(
					':stock_id' => $stock_id,
					':stock_date' => $condition["stock_date"],
				));
			$results = $query->execute()->as_array();
		} else if ($stock_type_cd == "01") {
			// TODO
			return NULL;
		}

		return $results;
	}

	// recommend_items_by_location_areacd.js
	// select_area_by_class_code.js
	// select_child_bot.js
	// 2022.09.14  #31551 增加event_date参数
	function search_items_by_location_areacd($bot_info, $item_id_history, $condition)
	{
		$original_bot_id = $bot_info['bot_id'];
		$bot_id = $bot_info['bot_id'];
		$bot_id = $this->get_bot_id_list_include_template_bot($bot_id);
		$template_bot_id = $this->get_template_bot_id($bot_id);

		$bot_id_list = "";
		$item_div = "";
		$areacd = "";
		$class_cd = "";
		$class_type_sub_cd = "";
		$lat =  "";
		$lng =  "";
		$lang_cd = $condition["lang_cd"];
		if (isset($condition['areacd'])) {
			$areacd = $condition['areacd'];
		}
		if (isset($condition['class_cd'])) {
			$class_cd = $condition['class_cd'];
		}
		if (isset($condition['item_class_type_sub_cd'])) {
			$class_type_sub_cd = $condition['item_class_type_sub_cd'];
		}
		if (isset($condition['lat'])) {
			$lat = $condition['lat'];
		}
		if (isset($condition['lng'])) {
			$lng = $condition['lng'];
		}
		if (isset($condition['item_div'])) {
			$item_div = $condition['item_div'];
		}
		if (isset($condition['bot_id_list'])) {
			$bot_id_list = $condition["bot_id_list"];
		}

		// 2021.09.30 #16758 begin
		$flg_partly_introduce = "";
		if (isset($condition['flg_partly_introduce'])) {
			$flg_partly_introduce = $condition['flg_partly_introduce'];
		}

		if ($flg_partly_introduce == 1) {
			// 改为即使指定了$bot_id_list也所有都检索，只不过$bot_id_list里有的放最前面显示
			$sql_bot_id_list = "
				LEFT JOIN t_bot d
				ON a.item_cd = d.facility_cd
			";

			if ($bot_id_list != "") {
				// 存在于$bot_id_list的放最前面
				$sql_sort_by_bot_id_list = "case 
					when d.bot_id is not null then 0
					else 9999
					end sort_by_bot_id_list,
				";
			} else {
				$sql_sort_by_bot_id_list = "0 as sort_by_bot_id_list,";
			}

			$sql_condition_bot_id_list = "";
			if ($bot_id_list != "") {
				// 去掉有bot_id，但却不存在于$bot_id_list的bot
				$sql_condition_bot_id_list = "
					AND (d.bot_id is null OR d.bot_id in ($bot_id_list))
				";
			} else {
			}
		} else {
			// 老式样
			$sql_bot_id_list = "";
			if ($bot_id_list != "") {
				$sql_bot_id_list = "
					INNER JOIN t_bot d
					ON a.item_cd = d.facility_cd
					AND d.bot_id in ($bot_id_list)
				";
			}

			$sql_sort_by_bot_id_list = "0 as sort_by_bot_id_list,";

			$sql_condition_bot_id_list = "";
		}
		// 2021.09.30 #16758 end

		$sql_class_cd = "";
		if ($class_cd != "") {
			/*
			$sql_class_cd = "
			AND a.class_cd like concat(:class_cd, '%')
			";
			*/
			/* 2019.12.19
			$sql_class_cd = "
			AND concat(' ', a.class_cd) like concat('% ', :class_cd, '%')
			";
			*/
			$class_cd_parts = explode(",", $class_cd);
			$class_cd_num = count($class_cd_parts);
			if ($class_cd_num == 1) {
				// 只指定了1个class_cd,那么就模糊匹配
				$sql_class_cd = "
				AND concat(' ', a.class_cd) like concat('% ', :class_cd, '%')
				";
			} else {
				// 指定了多个class_cd,那么就完全匹配
				$sql_class_cd = $this->create_sql_for_multi_code_exist_atleast_one("search_items_by_location_areacd", "a.class_cd", $class_cd);
			}
		}

		$sql_area_cd = "";
		if ($areacd != "") {
			// 2020.06.03 支持复数area_cd $sql_area_cd = " AND a.area_cd = :area_cd ";
			$sql_area_cd = " AND concat(',', a.area_cd) like concat('%,', :area_cd, '%') ";
		}

		$sql_class_type_sub_cd = "";
		if ($class_type_sub_cd != "") {
			$class_type_sub_cd_parts = explode(",", $class_type_sub_cd);
			$class_type_sub_cd_num = count($class_type_sub_cd_parts);
			$sql_class_type_sub_cd = $this->create_sql_for_multi_code_exist_all("search_items_by_location_areacd", "a.item_class_type_sub_cd", $class_type_sub_cd);;
		}

        // 2020.10.12 #6073
		$sql_status_cd_end_date = 
			" AND (a.end_date is NULL OR DATE_FORMAT(a.end_date,'%Y-%m-%d') >= :cur_date)
			AND a.item_status_cd in ('01','03','04','06','07','08')
			";

		// 2022.09.14  begin #31551 增加event_date参数
		$event_date = "";
		if (isset($condition['event_date'])) {
			$event_date = $condition['event_date'];
		}
		if ($event_date == null || $event_date == NULl) {
			$event_date = "";
		}
		$sql_event_date = "";
		if ($event_date != "") {
			$sql_event_date = " AND JSON_VALID(a.item_data) = 1
				AND JSON_TYPE(JSON_EXTRACT(a.item_data, '$.event_date')) <> 'NULL'
				AND JSON_EXTRACT(a.item_data, '$.event_date') <> '' 
				";
		}
		// 2022.09.14  end #31551

		if ($lat != "") {
			$sql = "SELECT * 
					FROM (
						SELECT DISTINCT a.item_id, a.item_cd, a.item_div, a.class_cd,  a.location_lat, a.location_lon, a.link_id, a.item_status_cd, a.end_date, a.item_data,
						a.position_x, a.position_y, a.position_z,
						case when JSON_VALID(a.item_data) = 1
							AND JSON_TYPE(JSON_EXTRACT(a.item_data, '$.event_date')) <> 'NULL'
							AND JSON_EXTRACT(a.item_data, '$.event_date') <> '' then JSON_UNQUOTE(JSON_EXTRACT(a.item_data, '$.event_date'))
							else ''
						end t_item_event_date,
						b.item_name, b.description, b.sell_point, b.item_image, b.item_image_thumb, b.sell_point_line, b.tel, b.url,
						b.btn1_name, b.btn1_url, b.btn2_name, b.btn2_url, b.btn3_name, b.btn3_url, b.btn1_url_lang_cd, b.btn2_url_lang_cd, b.btn3_url_lang_cd,
						b.btn1_url_sp, b.btn2_url_sp, b.btn3_url_sp, b.show_image,
						$sql_sort_by_bot_id_list
						case
							when a.item_id in ($item_id_history) then 0
							else 9999
						end sort1,
						ROUND(
						6378.138 * 2 * ASIN(
							SQRT(
								POW(
									SIN(
										(
											:lat * PI() / 180 - a.location_lat * PI() / 180
										) / 2
									),
									2
								) + COS(:lat * PI() / 180) * COS(a.location_lat * PI() / 180) * POW(
									SIN(
										(
											:lng * PI() / 180 - a.location_lon * PI() / 180
										) / 2
									),
									2
								)
							)
						) * 1000) distance
						FROM t_item a
						INNER JOIN t_item_description b
						ON a.item_id = b.item_id AND b.lang_cd = :lang_cd
						INNER JOIN t_item_display r
							ON a.item_id = r.item_id
							AND a.item_div = r.item_div
							AND find_in_set(:lang_cd, r.lang_display)
							AND r.public_flg = 1
						$sql_bot_id_list
						WHERE r.bot_id in ($bot_id)
						AND a.delete_flg <> 1
						AND (r.bot_id = :original_bot_id OR (r.bot_id = :template_bot_id AND r.item_div != 2))
						AND (a.start_date is NULL OR DATE_FORMAT(a.start_date,'%Y-%m-%d') <= :cur_date)
						$sql_status_cd_end_date
						AND (a.regular_start = :regular_date_kara 
							OR a.regular_end = :regular_date_kara 
							OR (a.regular_start <= a.regular_end AND a.regular_start <= :regular_date AND :regular_date <= a.regular_end)
							OR (a.regular_start > a.regular_end AND (a.regular_start <= :regular_date OR :regular_date <= a.regular_end))
							)
						AND (:item_div = '' OR a.item_div = :item_div)
						$sql_class_cd
						$sql_class_type_sub_cd
						$sql_area_cd
						$sql_condition_bot_id_list
						$sql_event_date
					) b
					ORDER BY sort_by_bot_id_list, sort1, distance
					LIMIT 100
					";
		} else {
				$sql = "SELECT DISTINCT a.item_id, a.item_cd, a.item_div, a.class_cd, a.location_lat, a.location_lon, a.link_id, a.item_status_cd, a.end_date, a.item_data,
					a.position_x, a.position_y, a.position_z,
					case when JSON_VALID(a.item_data) = 1
						AND JSON_TYPE(JSON_EXTRACT(a.item_data, '$.event_date')) <> 'NULL'
						AND JSON_EXTRACT(a.item_data, '$.event_date') <> '' then JSON_EXTRACT(a.item_data, '$.event_date')
						else ''
					end t_item_event_date,
					b.item_name, b.description, b.sell_point, b.item_image, b.item_image_thumb, b.sell_point_line, b.tel, b.url,
					b.btn1_name, b.btn1_url, b.btn2_name, b.btn2_url, b.btn3_name, b.btn3_url, b.btn1_url_lang_cd, b.btn2_url_lang_cd, b.btn3_url_lang_cd,
					b.btn1_url_sp, b.btn2_url_sp, b.btn3_url_sp, b.show_image,
					$sql_sort_by_bot_id_list
					case
						when a.item_id in ($item_id_history) then 0
						when r.sort_no1 <> 0 then sort_no1
						else 9999
					end sort1,
					case
						when a.item_id in ($item_id_history) then 0
						when r.sort_no1 <> 0 then 0
						else FLOOR(1000 + (RAND() * (2000 - 1000 + 1)))
					end sort2
					FROM t_item a
					INNER JOIN t_item_description b
					ON a.item_id = b.item_id AND b.lang_cd = :lang_cd
					INNER JOIN t_item_display r
						ON a.item_id = r.item_id
						AND a.item_div = r.item_div
						AND find_in_set(:lang_cd, r.lang_display)
						AND r.public_flg = 1
					$sql_bot_id_list
					WHERE r.bot_id in ($bot_id) 
					AND a.delete_flg <> 1
					AND (r.bot_id = :original_bot_id OR (r.bot_id = :template_bot_id AND r.item_div != 2))
					AND (a.start_date is NULL OR DATE_FORMAT(a.start_date,'%Y-%m-%d') <= :cur_date)
					$sql_status_cd_end_date
					AND (a.regular_start = :regular_date_kara 
						OR a.regular_end = :regular_date_kara 
						OR (a.regular_start <= a.regular_end AND a.regular_start <= :regular_date AND :regular_date <= a.regular_end)
						OR (a.regular_start > a.regular_end AND (a.regular_start <= :regular_date OR :regular_date <= a.regular_end))
						)
					AND (:item_div = '' OR a.item_div = :item_div)
					$sql_class_cd
					$sql_class_type_sub_cd
					$sql_area_cd
					$sql_condition_bot_id_list
					$sql_event_date
					ORDER BY sort_by_bot_id_list, sort1, sort2";
		}

		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':bot_id' => $bot_id,
			':lang_cd' => $lang_cd,
			':item_id_history' => $item_id_history,
			':area_cd' => $areacd,
			':class_cd' => $class_cd,
			':class_type_sub_cd' => $class_type_sub_cd,
			':item_div' => $item_div,
			':lat' => $lat,
			':lng' => $lng,
			':cur_date' => date('Y-m-d'),
			':regular_date' => date('m-d'),
			':regular_date_kara' => "",
			':original_bot_id' => $original_bot_id,
			':template_bot_id' => $template_bot_id,
		));
		$results = $query->execute()->as_array();

		// 2022.09.14  begin #31551 增加event_date参数
		if ($event_date != "") {
			if (count($results) > 0) {
				$results_filter = [];
				for ($i=0; $i<count($results); $i++) {
					if ($this->is_event_date_between_t_item_event_date($event_date, $results[$i]["t_item_event_date"]) == 1) {
						// in
						$results_filter[] = $results[$i];
					} else {
						// out
						error_log($results[$i]["item_id"] . " event_date is out ");
					}
				}
				$results = $results_filter;
			}
		}
		// 2022.09.14  end #31551 增加event_date参数
		return $results;
	}

	function read_t_bot()
	{
		$sql = "SELECT *
				FROM t_bot
				";

		$query = DB::query(Database::SELECT, $sql);
		$results = $query->execute();
		return $results->as_array();
	}

	function read_t_bot_line_richmenu_id()
	{
		$sql = "SELECT *
				FROM t_bot_line_richmenu_id
				ORDER BY
					no,sub_no
				";

		$query = DB::query(Database::SELECT, $sql);
		$results = $query->execute();
		return $results->as_array();
	}
	function read_t_bot_scene()
	{
		$sql = "SELECT *
				FROM t_bot_scene
				";

		$query = DB::query(Database::SELECT, $sql);
		$results = $query->execute();
		return $results->as_array();
	}

	function read_t_bot_setting()
	{
		$results = array();
		$sql = 'SELECT * FROM t_bot_setting ORDER BY bot_id, setting_cd';
		$query = DB::query(Database::SELECT, $sql);
		$settings = $query->execute()->as_array();
		$setting_dict = array();
		foreach($settings as $setting) {
			$setting_dict[$setting['bot_id'] . '-' . $setting['setting_cd']] = $setting['setting_value'];
		}
		// 2022.12.05 #34580 begin
		//$config = Kohana::$config->load('botsetting.' . 'ja');
		{
			$sql = 'SELECT * FROM t_bot_setting WHERE bot_id = 0';
			$query = DB::query(Database::SELECT, $sql);
			$base_settings = $query->execute()->as_array();
			$config = array();
			foreach($base_settings as $setting) {
				$config[$setting['setting_cd']] = $setting['setting_value'];
			}
		}
		// 2022.12.05 #34580 end
		$bots = ORM::factory('bot')->where('delete_flg', '=', '0')->find_all();
		foreach($bots as $bot) {
			foreach($config as $k=>$v) {
				// 2022.12.05 #34580 begin
				//$default_value = $v[1];
				$default_value = $v;
				// 2022.12.05 #34580 end
				$row = array();
				$row['bot_id'] = $bot->bot_id;
				$row['setting_cd'] = $k;
				if (array_key_exists($row['bot_id'] . '-' . $k, $setting_dict)) {
					$row['setting_value'] = $setting_dict[$row['bot_id'] . '-' . $k];
				}
				else {
					$gotted = 0;
					if ($k == "survey_flg") {
						// 取template bot_id
						$template_bot_id = $this->get_template_bot_id($bot->bot_id);
						if ($template_bot_id != "" && $template_bot_id !=$bot->bot_id) {
							if (array_key_exists($template_bot_id . '-' . $k, $setting_dict)) {
								// template 存在就使用template bot的，如果template bot也没值就是用父bot的
								$row['setting_value'] = $setting_dict[$template_bot_id . '-' . $k];
								$gotted = 1;
							}
						}
					}
					if ($gotted == 0) {
						// 取父bot_id
						$parent_bot_id = $this->get_grp_parent_bot_id($bot->bot_id);
						if ($parent_bot_id == -1 || $k == "flg_show_botlist" || $k == "template_bot" || $k == "msg_rcm_line_richmenu") {
							// 父bot不存在就使用配置文件里配置的默认值,另外flg_show_botlist也不继承
							// 2020.12.25 template_bot也不继承，目前apimodel.php里取template_bot设定都是直接查询db的并没有判断是否可以从父bot取得template_bot
							// 2020.12.25 msg_rcm_line_richmenu也不继承
							$row['setting_value'] = $default_value;
							$gotted = 1;
						} else {
							if (array_key_exists($parent_bot_id . '-' . $k, $setting_dict)) {
								// 父bot存在就使用父bot的，如果父bot也没值就是用配置文件里配置的默认值
								$row['setting_value'] = $setting_dict[$parent_bot_id . '-' . $k];
								$gotted = 1;
							}
							else {
								// 父bot不存在就使用配置文件里配置的默认值
								$row['setting_value'] = $default_value;
								$gotted = 1;
							}						
						}
					}
				}
				$results[] = $row;
			}
		}
		return $results;
	}

	// 2021.01.03 #3541
	function read_bot_setting_multilang_by_bot_id($condition)
	{
		$bot_id = $condition["bot_id"];
		$lang_cd = $condition["lang_cd"];
		// 再从t_bot_msg表读取
		$results = array();
		$results = $this->read_bot_setting_multilang_from_t_bot_msg_by_bot_id($bot_id, $lang_cd, 't_bot_msg_desc_txt','bot_name', $results, 'item_name');
		$results = $this->read_bot_setting_multilang_from_t_bot_msg_by_bot_id($bot_id, $lang_cd, 't_bot_msg_desc_img','welcome_image', $results, 'item_image');
		$results = $this->read_bot_setting_multilang_from_t_bot_msg_by_bot_id($bot_id, $lang_cd, 't_bot_msg_desc_txt','bot_description', $results, 'sell_point');
		$results = $this->read_bot_setting_multilang_from_t_bot_msg_by_bot_id($bot_id, $lang_cd, 't_bot_msg_desc_txt','bot_contact', $results, 'tel');

		return $results;
	}
	function read_bot_setting_multilang_from_t_bot_msg_by_bot_id($bot_id, $lang_cd, $msg_table, $msg_cd, &$ret, $to_field_name) {
		if ($msg_table == 't_bot_msg_desc_txt') {
			$sql = "SELECT a.bot_id, 
					b.lang_cd, b.content,
					c.facility_cd
				FROM t_bot_msg a 
				INNER JOIN t_bot_msg_desc_txt b
					ON a.msg_id = b.msg_id
				INNER JOIN t_bot c
					ON a.bot_id = c.bot_id
				WHERE
					a.msg_cd = :msg_cd
					AND a.bot_id = :bot_id
					AND b.lang_cd = :lang_cd
				";
		} else if ($msg_table == 't_bot_msg_desc_img') {
			$sql = "SELECT a.bot_id, 
					b.lang_cd, b.msg_image AS content,
					c.facility_cd
				FROM t_bot_msg a 
				INNER JOIN t_bot_msg_desc_img b
					ON a.msg_id = b.msg_id
				INNER JOIN t_bot c
					ON a.bot_id = c.bot_id
				WHERE
					a.msg_cd = :msg_cd
					AND a.bot_id = :bot_id
					AND b.lang_cd = :lang_cd
				";
		}
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':msg_cd' => $msg_cd,
			':bot_id' => $bot_id,
			':lang_cd' => $lang_cd,
			));
		$results = $query->execute()->as_array();

		// 合并到$ret里去
		foreach($results as &$msg) {
			$find = false;
			foreach($ret as &$cur_record) {
				if ($cur_record["bot_id"] == $msg["bot_id"] && $cur_record["lang_cd"] == $msg["lang_cd"]) {
					$cur_record[$to_field_name] = $msg["content"];
					$find = true;
					break;
				}
			}
			if ($find == false) {
				$new_record = array();
				$new_record["bot_id"] = $msg["bot_id"];
				$new_record["lang_cd"] = $msg["lang_cd"];
				$new_record["item_cd"] = $msg["facility_cd"];
				$new_record[$to_field_name] = $msg["content"];
				$ret[] = $new_record;
			}
		}
		return $ret;
	}
	// 2021.01.03 #3541 end

	function read_bot_setting_multilang()
	{
		/*
		// 先从t_item表读取所有bot
		$sql = 'SELECT a.item_id, a.item_cd, a.item_class_cd, a.bot_id,
					b.item_name, b.description, b.sell_point, b.item_image, b.item_image_thumb,
					b.tel, b.url, b.lang_cd
				FROM t_item a
				INNER JOIN t_bot c
					ON c.facility_cd = a.item_cd
				INNER JOIN t_item_description b
					ON a.item_id = b.item_id
				WHERE
					(a.start_date is NULL OR DATE_FORMAT(a.start_date,"%Y-%m-%d") <= :cur_date)
					AND (a.end_date is NULL OR DATE_FORMAT(a.end_date,"%Y-%m-%d") >= :cur_date)
					AND (a.regular_start = :regular_date_kara 
						OR a.regular_end = :regular_date_kara 
						OR (a.regular_start <= a.regular_end AND a.regular_start <= :regular_date AND :regular_date <= a.regular_end)
						OR (a.regular_start > a.regular_end AND (a.regular_start <= :regular_date OR :regular_date <= a.regular_end))
						)
				ORDER BY
					a.item_id, b.lang_cd
				';
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':cur_date' => date('Y-m-d'),
				':regular_date' => date('m-d'),
				':regular_date_kara' => "",
		));
		$results = $query->execute()->as_array();
		*/
		// 再从t_bot_msg表读取
		// 2022.09.30 提高检索性能 begin
		/*
		$results = array();
		$results = $this->read_bot_setting_multilang_from_t_bot_msg('t_bot_msg_desc_txt','bot_name', $results, 'item_name');
		$results = $this->read_bot_setting_multilang_from_t_bot_msg('t_bot_msg_desc_img','welcome_image', $results, 'item_image');
		$results = $this->read_bot_setting_multilang_from_t_bot_msg('t_bot_msg_desc_txt','bot_description', $results, 'sell_point');
		$results = $this->read_bot_setting_multilang_from_t_bot_msg('t_bot_msg_desc_txt','bot_contact', $results, 'tel');

		return $results;
		*/
        $map = array();
		$this->read_bot_setting_multilang_from_t_bot_msg_new('t_bot_msg_desc_txt','bot_name', $map, 'item_name');
		$this->read_bot_setting_multilang_from_t_bot_msg_new('t_bot_msg_desc_img','welcome_image', $map, 'item_image');
		$this->read_bot_setting_multilang_from_t_bot_msg_new('t_bot_msg_desc_txt','bot_description', $map, 'sell_point');
		$this->read_bot_setting_multilang_from_t_bot_msg_new('t_bot_msg_desc_txt','bot_contact', $map, 'tel');
        $results = array();
        foreach ($map as &$record) {
            $results[] = $record;
        }
		return $results;
		// 2022.09.30 提高检索性能 end
	}
	// 2022.09.30 提高检索性能
    public function read_bot_setting_multilang_from_t_bot_msg_new($msg_table, $msg_cd, &$map, $to_field_name)
    {
        // 2021.03.23 #9504 增加bot_contact_context_*
        // 2021.05.11 清水希望增加bot_contact_{scene_cd}
        // error_log("read_bot_setting_multilang_from_t_bot_msg_new() begin 11 msg_table=$msg_table msg_cd=$msg_cd to_field_name=$to_field_name");
        if ($msg_table == 't_bot_msg_desc_txt' && $msg_cd == 'bot_contact') {
            $sql = "SELECT a.bot_id, a.msg_cd,
					b.lang_cd, b.content,
					c.facility_cd
				FROM t_bot_msg a 
				INNER JOIN t_bot_msg_desc_txt b
					ON a.msg_id = b.msg_id
				INNER JOIN t_bot c
					ON a.bot_id = c.bot_id
				WHERE
					a.msg_cd = :msg_cd
					OR a.msg_cd like 'bot_contact_%'
				";
        } elseif ($msg_table == 't_bot_msg_desc_txt') {
            $sql = "SELECT a.bot_id, 
					b.lang_cd, b.content,
					c.facility_cd
				FROM t_bot_msg a 
				INNER JOIN t_bot_msg_desc_txt b
					ON a.msg_id = b.msg_id
				INNER JOIN t_bot c
					ON a.bot_id = c.bot_id
				WHERE
					a.msg_cd = :msg_cd
				";
        } elseif ($msg_table == 't_bot_msg_desc_img') {
            $sql = "SELECT a.bot_id, 
					b.lang_cd, b.msg_image AS content,
					c.facility_cd
				FROM t_bot_msg a 
				INNER JOIN t_bot_msg_desc_img b
					ON a.msg_id = b.msg_id
				INNER JOIN t_bot c
					ON a.bot_id = c.bot_id
				WHERE
					a.msg_cd = :msg_cd
				";
        }
        $query = DB::query(Database::SELECT, $sql);
        $query->parameters(array(
            ':msg_cd' => $msg_cd,
            ));
        // error_log("read_bot_setting_multilang_from_t_bot_msg_new() begin 12");
        $results = $query->execute()->as_array();
        // error_log("read_bot_setting_multilang_from_t_bot_msg_new() begin 13 count=" . count($results));

        // 合并到$ret里去
        foreach ($results as &$msg) {
            $find = false;
            $key = $msg["lang_cd"] . "_" .  $msg["bot_id"];
            //if (array_key_exists($key, $map)) {
			// isset 速度更快
            if (isset($map[$key])) {
                $find = true;

                $cur_record =  &$map[$key];
                // 2021.03.23 #9504 begin增加bot_contact_context_*的判断
                if ($msg_cd == 'bot_contact') {
                    $cur_record[$msg["msg_cd"]] = $msg["content"];
                    if ($msg["msg_cd"] == 'bot_contact') {
                        $cur_record[$to_field_name] = $msg["content"];
                    }
                } else {
                    $cur_record[$to_field_name] = $msg["content"];
                }
            }

            if ($find == false) {
                $new_record = array();
                $new_record["bot_id"] = $msg["bot_id"];
                $new_record["lang_cd"] = $msg["lang_cd"];
                $new_record["item_cd"] = $msg["facility_cd"];
                // 2021.03.23 #9504 begin增加bot_contact_context_*的判断
                if ($msg_cd == 'bot_contact') {
                    $new_record[$msg["msg_cd"]] = $msg["content"];
                    if ($msg["msg_cd"] == 'bot_contact') {
                        $new_record[$to_field_name] = $msg["content"];
                    }
                } else {
                    $new_record[$to_field_name] = $msg["content"];
                }
                //$ret[] = $new_record;
                $map[$key] = $new_record;
            }
        }
        // error_log("read_bot_setting_multilang_from_t_bot_msg_new() begin 14");
        return 1;
    }	
	function read_bot_setting_multilang_from_t_bot_msg($msg_table, $msg_cd, &$ret, $to_field_name) {
		// 2021.03.23 #9504 增加bot_contact_context_*
		// 2021.05.11 清水希望增加bot_contact_{scene_cd}
		if ($msg_table == 't_bot_msg_desc_txt' && $msg_cd == 'bot_contact') {
			$sql = "SELECT a.bot_id, a.msg_cd,
					b.lang_cd, b.content,
					c.facility_cd
				FROM t_bot_msg a 
				INNER JOIN t_bot_msg_desc_txt b
					ON a.msg_id = b.msg_id
				INNER JOIN t_bot c
					ON a.bot_id = c.bot_id
				WHERE
					a.msg_cd = :msg_cd
					OR a.msg_cd like 'bot_contact_%'
				";
		} else if ($msg_table == 't_bot_msg_desc_txt') {
			$sql = "SELECT a.bot_id, 
					b.lang_cd, b.content,
					c.facility_cd
				FROM t_bot_msg a 
				INNER JOIN t_bot_msg_desc_txt b
					ON a.msg_id = b.msg_id
				INNER JOIN t_bot c
					ON a.bot_id = c.bot_id
				WHERE
					a.msg_cd = :msg_cd
				";
		} else if ($msg_table == 't_bot_msg_desc_img') {
			$sql = "SELECT a.bot_id, 
					b.lang_cd, b.msg_image AS content,
					c.facility_cd
				FROM t_bot_msg a 
				INNER JOIN t_bot_msg_desc_img b
					ON a.msg_id = b.msg_id
				INNER JOIN t_bot c
					ON a.bot_id = c.bot_id
				WHERE
					a.msg_cd = :msg_cd
				";
		}
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':msg_cd' => $msg_cd,
			));
		$results = $query->execute()->as_array();

		// 合并到$ret里去
		foreach($results as &$msg) {
			$find = false;
			foreach($ret as &$cur_record) {
				if ($cur_record["bot_id"] == $msg["bot_id"] && $cur_record["lang_cd"] == $msg["lang_cd"]) {
					// 2021.03.23 #9504 begin增加bot_contact_context_*的判断
					if ($msg_cd == 'bot_contact') {
						$cur_record[$msg["msg_cd"]] = $msg["content"];
						if ($msg["msg_cd"] == 'bot_contact') {
							$cur_record[$to_field_name] = $msg["content"];
						}
					} else {
						$cur_record[$to_field_name] = $msg["content"];
					}
					$find = true;
					break;
				}
			}
			if ($find == false) {
				$new_record = array();
				$new_record["bot_id"] = $msg["bot_id"];
				$new_record["lang_cd"] = $msg["lang_cd"];
				$new_record["item_cd"] = $msg["facility_cd"];
				// 2021.03.23 #9504 begin增加bot_contact_context_*的判断
				if ($msg_cd == 'bot_contact') {
					$new_record[$msg["msg_cd"]] = $msg["content"];
					if ($msg["msg_cd"] == 'bot_contact') {
						$new_record[$to_field_name] = $msg["content"];
					}
				} else {
					$new_record[$to_field_name] = $msg["content"];
				}
				$ret[] = $new_record;
			}
		}
		return $ret;
	}

	// 2021.07.28 #14782 
	// 2021.08.09 #15050 add t_bot_msg add scene_cd
	// 2022.05.03 add delete_flg
	function read_bot_line_richmenu_id()
	{
		$sql = "SELECT 
					a.bot_id,
					a.msg_id,
					a.msg_cd, 
					a.msg_type_cd,
					a.msg_data, 
					a.start_date,
					a.end_date,
					a.scene_cd
				FROM  t_bot_msg a
				WHERE 
					a.msg_type_cd = 'rcm'
					AND a.msg_data <> ''
					AND start_date IS NOT NULL 
					AND end_date IS NOT NULL
					AND start_date <> ''
					AND end_date <> ''
					AND delete_flg = 0
				ORDER BY
					a.bot_id,
					a.scene_cd DESC,
					a.start_date
				";

		$query = DB::query(Database::SELECT, $sql);
		$results = $query->execute()->as_array();

		return $results;
	}

	function read_bot_line_richmenu_id_backup_20210728()
	{
		$sql = "SELECT 
					a.bot_id, 
					a.setting_value,
					b.msg_cd, 
					b.msg_data, 
					d.msg_cd msg_cd_out_service, 
					d.msg_data msg_data_out_service,

					c.msg_cd msg_cd_bot0, 
					c.msg_data msg_data_bot0, 

					e.msg_cd msg_cd_out_service_bot0, 
					e.msg_data msg_data_out_service_bot0
				FROM t_bot_setting  a
				LEFT JOIN t_bot_msg b
					ON b.bot_id = a.bot_id
					AND a.setting_value = b.msg_cd
				LEFT JOIN t_bot_msg d
					ON d.bot_id = a.bot_id
					AND concat(a.setting_value, '_out_service') = d.msg_cd
				LEFT JOIN t_bot_msg c
					ON c.bot_id = 0
					AND a.setting_value = c.msg_cd
				LEFT JOIN t_bot_msg e
					ON e.bot_id = 0
					AND concat(a.setting_value, '_out_service') = e.msg_cd
				WHERE 
					a.setting_cd = 'msg_rcm_line_richmenu'
				ORDER BY
					a.bot_id
				";

		$query = DB::query(Database::SELECT, $sql);
		$results = $query->execute()->as_array();

		return $results;
	}

	function read_multi_lang()
	{
		$sql = "SELECT 
			a.msg_cd,b.*
			FROM 
				t_bot_msg a
			INNER JOIN t_bot_msg_desc_txt b
				ON a.msg_id = b.msg_id
			WHERE
				a.bot_id = 0
			AND a.msg_type_cd = 'txt'
			ORDER BY
				b.lang_cd,
				a.msg_id
		";

		$query = DB::query(Database::SELECT, $sql);
		/*
		$query->parameters(array(
			':msg_cd' => $msg_cd,
			));
		*/
		$results = $query->execute()->as_array();

		return $results;
	}	
	function read_table($condition)
	{
		$tablename = $condition["tablename"];
		if ($tablename == 't_bot_setting') {
			return $this->read_t_bot_setting();
		}
		
		$sql = "SELECT *
				FROM $tablename
				";

		if (isset($condition['where'])) {
			$where = $condition['where'];
			$sql = "$sql WHERE $where";
		}

		if (isset($condition['orderby'])) {
			$orderby = $condition['orderby'];
			$sql = "$sql ORDER BY $orderby";
		}

		$query = DB::query(Database::SELECT, $sql);
		$results = $query->execute();
		return $results->as_array();
	}

	function update_table($condition)
	{
		$tablename = $condition["tablename"];
		$update  = $condition["update"];
		$where  = $condition["where"];
		$sql = "UPDATE $tablename
				SET $update
				WHERE $where
				";

		$query = DB::query(Database::UPDATE, $sql);
		$query->execute();
	}

	function facebook_feed($condition)
	{
		$sql = "INSERT INTO t_bot_post
				(member_id, post_type, post_time, bot_id, lang_cd, post_msg, post_image_url)
				VALUES
				(:member_id, :post_type, :post_time, :bot_id, :lang_cd, :post_msg, :post_image_url) 
				";
		$query = DB::query(Database::UPDATE, $sql);
		$query->parameters(array(
			':member_id' => $condition["member_id"],
			':post_type' => $condition["post_type"],
			':post_time' => date('Y-m-d H:i:s'),
			':bot_id' => $condition["bot_id"],
			':lang_cd' => $condition["lang_cd"],
			':post_msg' => $condition["post_msg"],
			':post_image_url' => $condition["post_image_url"],
		));

		$query->execute();
		$results = 1;
		return $results;
	}

	// 2022.08.11 begin
	function set_member_lang_cd($condition)
	{
		$sql = "UPDATE t_bot_member
				SET
					lang_cd = :lang_cd,
					last_talk_date = :last_talk_date
				WHERE
					bot_id = :bot_id
					AND member_id = :member_id
				";
		$query = DB::query(Database::UPDATE, $sql);
		$query->parameters(array(
			':bot_id' => $condition["bot_id"],
			':member_id' => $condition["member_id"],
			':lang_cd' => $condition["lang_cd"],
			':last_talk_date' => $condition["last_talk_date"],
		));

		$results = $query->execute();
		return $results;
	}
	// 2022.08.11 end
	
	function set_member_last_access_time($bot_id, $condition)
	{
		$sql = "UPDATE t_bot_member
				SET
					last_access_time = from_unixtime(:last_access_time / 1000)
				WHERE
					bot_id = :bot_id
					AND member_id = :member_id
				";
		$query = DB::query(Database::UPDATE, $sql);
		$query->parameters(array(
			':bot_id' => $bot_id,
			':member_id' => $condition["member_id"],
			':last_access_time' => $condition["watermark"],
		));

		$results = $query->execute();
		return $results;
	}

    // 2020.08.10 #4982 begin
	function set_member_line_induce_flg($condition)
	{
		$sql = "UPDATE t_bot_member
				SET
					line_induce_flg = :line_induce_flg
				WHERE
					bot_id = :bot_id
					AND member_id = :member_id
				";
		$query = DB::query(Database::UPDATE, $sql);
		$query->parameters(array(
			':bot_id' => $condition["bot_id"],
			':member_id' => $condition["member_id"],
			':line_induce_flg' => $condition["line_induce_flg"],
		));

		$results = $query->execute();
		return $results;
	}
    // 2020.08.10 #4982 end

	function set_member_follow($condition)
	{
		$bot_id_list = $condition["bot_id_list"];
		$sql = "UPDATE t_bot_member
				SET
					follow = :follow
				WHERE
					bot_id in ($bot_id_list)
					AND member_id = :member_id
				";
		$query = DB::query(Database::UPDATE, $sql);
		$query->parameters(array(
			':member_id' => $condition["member_id"],
			':follow' => $condition["follow"],
		));

		$results = $query->execute();
		return $results;
	}

	// t_bot_service保存时，如果member_mail变量存在，那么就把member_mail内容设置到email字段
	function set_member_email($bot_id, $condition)
	{
		$sql = "UPDATE t_bot_member
				SET
					email = :member_mail
				WHERE
					bot_id = :bot_id
					AND member_id = :member_id
				";
		$query = DB::query(Database::UPDATE, $sql);
		$query->parameters(array(
			':bot_id' => $bot_id,
			':member_id' => $condition["member_id"],
			':member_mail' => $condition["member_mail"],
		));

		$results = $query->execute();
		return $results;
	}

	// 2021.10.14 #17343 新增t_bot_member.order_name_kanji
	// 2022.02.10 #21552 增加privacy
	function set_member_email_phone_name($bot_id, $member_id, $checkin_tel, $checkin_mail, $checkin_name, $name_kanji, $privacy)
	{
		// 2022.02.10 begin
		$sql_fields_update = "";
		if ($privacy == 1) {
			$sql_fields_update = "
					order_privacy = 1,
					email = :member_mail,
					phone = :member_tel,
					order_name = :order_name,
					order_name_kanji = :order_name_kanji

			";
		} else {
			$sql_fields_update = "
					order_privacy = 0
			";
		}
		// 2022.02.10 end
		// 2021.10.20 #17468 予約者情報入力省略が子ボット間で連動できない: 去掉bot_id=:bot_id条件
		$sql = "UPDATE t_bot_member
				SET
					$sql_fields_update

				WHERE
					member_id = :member_id
				";
		$query = DB::query(Database::UPDATE, $sql);
		$query->parameters(array(
			':bot_id' => $bot_id,
			':member_id' => $member_id,
			':member_mail' => $checkin_mail,
			':member_tel' => $checkin_tel,
			':order_name' => $checkin_name,
			':order_name_kanji' => $name_kanji,
		));

		$results = $query->execute();
		return $results;
	}

	function set_member_tags($bot_id, $condition)
	{
		$sql = "UPDATE t_bot_member
				SET
					tags = :tags
				WHERE
					bot_id = :bot_id
					AND member_id = :member_id
				";
		$query = DB::query(Database::UPDATE, $sql);
		$query->parameters(array(
			':bot_id' => $bot_id,
			':member_id' => $condition["member_id"],
			':tags' => $condition["tags"],
		));

		$results = $query->execute();
		return $results;
	}

	// 2021.08.25 #15426 begin
	function set_member_is_tester($condition)
	{
		$sql = "UPDATE t_bot_member
				SET
				is_tester = :is_tester
				WHERE
					bot_id = :bot_id
					AND member_id = :member_id
				";
		$query = DB::query(Database::UPDATE, $sql);
		$query->parameters(array(
			':bot_id' => $condition["bot_id"],
			':member_id' => $condition["member_id"],
			':is_tester' => $condition["is_tester"],
		));

		$results = $query->execute();
		return $results;
	}
	// 2021.08.25 #15426 end

	// 2023.06.23 #49572 begin
	function set_member_name_by_t_user($condition) {
		$user_id = $condition["user_id"];
		$bot_id = $condition["bot_id"];
		$member_id = $condition["member_id"];
		// 从t_user根据user_id检索出name
		$user = ORM::factory('user', $user_id);
		// 从t_bot根据bot_id检索出bot_name
		$bot = ORM::factory('bot', $bot_id);
		// 写入t_bot_member的link_id,name
		$sql = "UPDATE t_bot_member
				SET
					name = :name,
					link_id = :link_id
				WHERE
					bot_id = :bot_id
					AND member_id = :member_id
				";
		$query = DB::query(Database::UPDATE, $sql);
		$query->parameters(array(
			':bot_id' => $bot_id,
			':member_id' => $member_id,
			':link_id' => $user_id,
			':name' => $bot->bot_name . " " . $user->name,
		));

		$results = $query->execute();
		return $results;
	}
	// 2023.06.23 #49572 end

	function get_member_notify_msg($condition)
	{
		$sql = "SELECT group_concat(a.msg_cd) as msg_cds
			FROM t_bot_msg a
			WHERE a.bot_id = :bot_id 
			AND a.msg_class_cd = '81' 
			AND a.start_date IS NOT NULL 
			AND a.end_date IS NOT NULL
			AND DATE_FORMAT(a.start_date,'%Y-%m-%d') <= :cur_date 
			AND DATE_FORMAT(a.end_date,'%Y-%m-%d') >= :cur_date
			AND DATE_FORMAT(a.start_date, '%Y-%m-%d %H:%i:%s') >= :last_talk_date
			";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':bot_id' => $condition["bot_id"],
			':member_id' => $condition["member_id"],
			':last_talk_date' => $condition["last_talk_date"],
			':cur_date' => date('Y-m-d'),
		));

		$results = $query->execute();
		return $results->as_array();
	}

	function update_log($condition)
	{
		$sql = "UPDATE t_bot_log_chat
				SET
					answer_type = :answer_type
				WHERE
					log_id = :log_id
				";
		$query = DB::query(Database::UPDATE, $sql);
		$query->parameters(array(
			':answer_type' => $condition["answer_type"],
			':log_id' => $condition["log_id"],
		));
		$results = $query->execute();

		$table_name = $condition["table_name"];
		$sql = "UPDATE $table_name
				SET
					answer_type = :answer_type
				WHERE
					log_id = :log_id
				";
		$query = DB::query(Database::UPDATE, $sql);
		$query->parameters(array(
			':answer_type' => $condition["answer_type"],
			':log_id' => $condition["log_id"],
		));
		$results = $query->execute();

		return $results;
	}

	// 2021.08.12 增加score
	function update_log_intent_cd($condition)
	{
		$sql = "UPDATE t_bot_log_chat
				SET
					intent_cd = :intent_cd,
					sub_intent_cd = :sub_intent_cd,
					area_cd = :area_cd,
					answer_type = :answer_type,
					score = :intent_score
				WHERE
					log_id = :log_id
				";
		$query = DB::query(Database::UPDATE, $sql);
		$query->parameters(array(
			':intent_cd' => $condition["intent_cd"],
			':sub_intent_cd' => $condition["sub_intent_cd"],
			':area_cd' => $condition["area_cd"],
			':answer_type' => $condition["answer_type"],
			':log_id' => $condition["log_id"],
			':intent_score' => $condition["intent_score"],
		));
		$results = $query->execute();

		$table_name = $condition["table_name"];
		$sql = "UPDATE $table_name
				SET
					intent_cd = :intent_cd,
					sub_intent_cd = :sub_intent_cd,
					area_cd = :area_cd,
					answer_type = :answer_type,
					score = :intent_score
				WHERE
					log_id = :log_id
				";
		$query = DB::query(Database::UPDATE, $sql);
		$query->parameters(array(
			':intent_cd' => $condition["intent_cd"],
			':sub_intent_cd' => $condition["sub_intent_cd"],
			':area_cd' => $condition["area_cd"],
			':answer_type' => $condition["answer_type"],
			':log_id' => $condition["log_id"],
			':intent_score' => $condition["intent_score"],
		));
		$results = $query->execute();

		// 2022.01.13 ユーザー属性自動付け機能 begin
		$this->create_update_member_attr("chatbot", $table_name, $condition["log_id"]);
		// 2022.01.13 ユーザー属性自動付け機能 end

		return $results;
	}
	
	public function insert_t_member_mail_certify($condition) {
		$sql = "INSERT INTO t_member_mail_certify
				(mail, bot_id, flow_id)
				VALUES
				(:mail, :bot_id, :flow_id) 
				";
		$query = DB::query(Database::UPDATE, $sql);
		$query->parameters(array(
			':mail' => $condition["mail"],
			':bot_id' => $condition["bot_id"],
			':flow_id' => $condition["flow_id"],
		));

		$query->execute();
		$results = 1;
		return $results;
	}

	function t_bot_service_get_mail_send_list($condition)
	{
		/*
		$sql = "SELECT * FROM t_bot_service
				WHERE
					reserve_date = :cur_date
					AND reserve_time > :cur_time
					AND upd_time IS NOT NULL
					AND DATE_FORMAT(upd_time,'%Y-%m-%d') <> :cur_date
					AND intent_cd = :intent_cd
					AND service_status_cd = :service_status_cd
					AND sent_flg = 0
				";
				*/
		$sql = "SELECT * FROM t_bot_service
				WHERE
					reserve_date = :next_date
					AND intent_cd = :intent_cd
					AND service_status_cd = :service_status_cd
					AND sent_flg = 0
				";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':next_date' => date('Y-m-d',strtotime("+1 days")),
				':service_status_cd' => "03",
				':intent_cd' => "service.restaurant_reserve",
		));
		$results = $query->execute();
		return $results->as_array();
	}

	function t_bot_service_update_mail_sent($condition)
	{
		$sql = "UPDATE t_bot_service
				SET
				sent_flg = 1,
				upd_div = 3
				WHERE
					service_id = :service_id
				";
		$query = DB::query(Database::UPDATE, $sql);
		$query->parameters(array(
			':service_id' => $condition["service_id"],
		));

		$results = $query->execute();
		return $results;
	}

	function update_t_bot_service_service_data($condition)
	{
		$sql = "UPDATE t_bot_service
				SET
				service_data = :service_data,
				upd_time = :upd_time
				WHERE
					service_id = :service_id
				";
		$query = DB::query(Database::UPDATE, $sql);
		$query->parameters(array(
			':service_id' => $condition["service_id"],
			':service_data' => $condition["service_data"],
			':upd_time' => $condition["upd_time"],
		));

		$results = $query->execute();
		return $results;
	}

	function delete_t_bot_service_data($condition)
	{
		$sql = "DELETE FROM t_bot_service
				WHERE
					service_id = :service_id
				";
		$query = DB::query(Database::UPDATE, $sql);
		$query->parameters(array(
			':service_id' => $condition["service_id"],
		));

		$results = $query->execute();
		return $results;
	}

	function t_bot_service_get_list($condition)
	{
		$bot_id = $condition["bot_id"];
		$member_id = $condition["member_id"];
		$intent_cd = $condition["intent_cd"];

		$sql = "SELECT * FROM t_bot_service
				WHERE
					bot_id = :bot_id
					AND member_id = :member_id
					AND intent_cd = :intent_cd
				ORDER BY
					service_id DESC
				";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
				':member_id' => $member_id,
				':intent_cd' => $intent_cd,
		));
		$results = $query->execute();
		return $results->as_array();
	}

	function search_member_last_facility($id, $bot_id_list)
	{
		$sql = "SELECT * FROM t_bot_member
				WHERE
					member_id = :id
					AND bot_id in ($bot_id_list)
				ORDER BY
					last_talk_date DESC
				LIMIT 1
				";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':id' => $id,
		));
		$results = $query->execute();
		return $results->as_array();
	}

	function get_m_bot_area($bot_id, $condition) {
		$parent_area_id = "";
		$child_area_id = "";
		$lang_cd = $condition["lang_cd"];

		if (isset($condition['parent_area_id'])) {
			$parent_area_id = $condition['parent_area_id'];
		}
		if (isset($condition['child_area_id'])) {
			$child_area_id = $condition['child_area_id'];
		}

		if ($parent_area_id == "") {
			$sql = "SELECT distinct a.* 
			FROM m_bot_area_parent a
			INNER JOIN t_bot_ots_ownedmedia b
				ON a.bot_id = b.bot_id
				AND b.lang_cd = :lang_cd
				AND b.is_publish = 1
				AND b.title IS NOT NULL
				AND b.title <> ''
				AND b.parent_area = a.$lang_cd
			WHERE
				a.bot_id = :bot_id
			ORDER BY
				a.sort_no
			";
			$query = DB::query(Database::SELECT, $sql);
			$query->parameters(array(
				':bot_id' => $bot_id,
				':lang_cd' => $lang_cd,
			));
			$results = $query->execute();
			return $results->as_array();
		} else {
			$sql = "SELECT distinct c.* 
			FROM m_bot_area_parent a
			INNER JOIN m_bot_area_child c
			ON a.bot_id = c.bot_id
			AND a.parent_area_id = c.parent_area_id
			INNER JOIN t_bot_ots_ownedmedia b
				ON a.bot_id = b.bot_id
				AND b.lang_cd = :lang_cd
				AND b.is_publish = 1
				AND b.title IS NOT NULL
				AND b.title <> ''
				AND b.parent_area = a.$lang_cd
				AND b.child_area = c.$lang_cd
			WHERE
				a.bot_id = :bot_id
				AND a.parent_area_id = :parent_area_id
			ORDER BY
				c.sort_no
			";
			$query = DB::query(Database::SELECT, $sql);
			$query->parameters(array(
				':bot_id' => $bot_id,
				':lang_cd' => $lang_cd,
				':parent_area_id' => $parent_area_id,
			));
			$results = $query->execute();
			return $results->as_array();
		}
	}

	function get_parent_area_name($bot_id, $condition) {
		$parent_area_id = $condition['parent_area_id'];

		$sql = "SELECT a.*
			FROM m_bot_area_parent a
			WHERE
				a.bot_id = :bot_id
				AND a.parent_area_id = :parent_area_id
			";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':bot_id' => $bot_id,
			':parent_area_id' => $parent_area_id,
		));
		$results = $query->execute();
		return $results->as_array();
	}

	function get_child_area_name($bot_id, $condition) {
		$parent_area_id = $condition['parent_area_id'];
		$child_area_id = $condition['child_area_id'];

		$sql = "SELECT a.*
			FROM m_bot_area_child a
			WHERE
				a.bot_id = :bot_id
				AND a.parent_area_id = :parent_area_id
				AND a.child_area_id = :child_area_id
			";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':bot_id' => $bot_id,
			':parent_area_id' => $parent_area_id,
			':child_area_id' => $child_area_id,
		));
		$results = $query->execute();
		return $results->as_array();
	}

	function regist_ref_info($condition) {
        $lang_cd = $condition["lang_cd"];
        $snstype = $condition["snstype"];
        $div = $condition["div"];
        $ref = $condition["ref"];
        $bot_id = $condition["bot_id"];
		$ref_ids = $condition["ref_ids"];
		$refdatetime = $condition["datetime"];
		$member_id = $condition["member_id"];

		$ids = explode(",", $ref_ids);
		$l_ids = count($ids);		
		$divs = explode(",", $div);
		$l_divs = count($divs);		

		for ($i=0; $i<$l_ids; $i++) {
			$id = $ids[$i];
			if ($l_divs != $l_ids) {
				$ref_div = $div;
			} else {
				$ref_div = $divs[$i];
			}
			$sql = "INSERT INTO t_item_ref
			(bot_id, item_id, ref_div, lang_cd, sns_type_cd, ref_type_cd, access_time, member_id)
			VALUES 
			(:bot_id, :id, :div, :lang_cd, :snstype, :ref, :refdatetime, :member_id)
			";
			$query = DB::query(Database::UPDATE, $sql);
			$query->parameters(array(
				':bot_id' => $bot_id,
				':id' => $id,
				':div' => $ref_div,
				':lang_cd' => $lang_cd,
				':snstype' => $snstype,
				':ref' => $ref,
				':refdatetime' => $refdatetime,
				':member_id' => $member_id,
			));

			$results = $query->execute();
		}
		
		return 0;
	}

	// 2022.05.10 #25938
	function regist_ref_info_bulk($condition) {
		$data_obj = json_decode($condition["data"]);
		$length = count($data_obj);
		error_log("comming regist_ref_info_bulk() num=$length");
		for ($x=0; $x<$length; $x++) {
			$cur_data = $data_obj[$x];
			$cur_condition = [];
			$cur_condition["lang_cd"] = $cur_data->lang_cd;
			$cur_condition["snstype"] = $cur_data->snstypecd;
			$cur_condition["div"] = $cur_data->divs;
			$cur_condition["ref"] = $cur_data->type;
			$cur_condition["bot_id"] = $cur_data->bot_id;
			$cur_condition["ref_ids"] = $cur_data->ref_ids;
			$cur_condition["datetime"] = $cur_data->datetime;
			$cur_condition["member_id"] = $cur_data->member_id;
			$this->regist_ref_info($cur_condition);
		}

		return 0;
	}

	// 2021.08.05 begin
	function get_m_class_code($condition) {
		$lang_cd = $condition["lang_cd"];
		$code_div = $condition['code_div'];
		$class_cd = $condition['class_cd'];

		$sql = "
			SELECT
				*
			FROM m_class_code m
			WHERE
				m.code_div = :code_div
				AND m.lang_cd = :lang_cd
				AND m.class_cd = :class_cd
			";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':code_div' => $code_div,
			':class_cd' => $class_cd,
			':lang_cd' => $lang_cd,
		));
		$results = $query->execute();
		return $results->as_array();		
	}
	// 2021.08.05 end

	// class_code.js
	// class_code_product.js
	// class_code_skill.js
	function get_m_class_code_child($bot_info, $condition) {
		// CLASS_CODE命令
		$original_bot_id = $bot_info['bot_id'];
		$bot_id = $bot_info['bot_id'];
		$bot_id = $this->get_bot_id_list_include_template_bot($bot_id);
		$template_bot_id = $this->get_template_bot_id($bot_id);

		$lang_cd = $condition["lang_cd"];
		
		$item_div = $condition['item_div'];
		$code_div = $condition['code_div'];
		$class_cd = "";

		$features_cd = NULL;
		$class_type_sub_cd = NULL;

		if (isset($condition['class_cd'])) {
			$class_cd = $condition['class_cd'];
		}
		if (isset($condition['features_cd'])) {
			$features_cd = $condition['features_cd'];
		}
		if (isset($condition['item_class_type_sub_cd'])) {
			$class_type_sub_cd = $condition['item_class_type_sub_cd'];
		}
		
		// 2019.11.21 add area_cd
		$area_cd = "";
		if (isset($condition['area_cd'])) {
			$area_cd = $condition['area_cd'];
		}
		$sql_area_cd = "";
		if ($area_cd != "") {
			// 2020.06.03 支持复数area_cd $sql_area_cd = " AND a.area_cd = :area_cd ";
			$sql_area_cd = " AND concat(',', a.area_cd) like concat('%,', :area_cd, '%') ";
		}


		// 2021.04.24 begin 支持指定多个class_cd
		$sql_parent_class_cd = "AND m.parent_cd = :class_cd";
		$sql_class_cd_sort = " 1 AS class_cd_sort";
		if ($class_cd != "") {
			$class_cd_parts = explode(",", $class_cd);
			$class_cd_num = count($class_cd_parts);
			if ($class_cd_num > 1) {
				// 指定了多个class_cd,
				for ($i=0; $i<$class_cd_num; $i++) {
					$cur_class_cd = $class_cd_parts[$i];
					$cur_sql =  "m.parent_cd = '$cur_class_cd'";
					if ($i == 0) {
						$sql_parent_class_cd = $cur_sql;
						$sql_class_cd_sort = "CASE WHEN parent_cd = '$cur_class_cd' THEN $i ";
					} else {
						$sql_parent_class_cd = "$sql_parent_class_cd OR $cur_sql";
						$sql_class_cd_sort = "$sql_class_cd_sort WHEN parent_cd = '$cur_class_cd' THEN $i ";
					}
				}
				$sql_parent_class_cd = "AND ($sql_parent_class_cd)";
				$sql_class_cd_sort = "$sql_class_cd_sort END AS class_cd_sort";
			}
		}
		// 2021.04.24 begin

        // 2020.10.12 #6073
		$sql_status_cd_end_date = 
			" AND (a.end_date is NULL OR DATE_FORMAT(a.end_date,'%Y-%m-%d') >= :cur_date)
			AND a.item_status_cd in ('01','03','04','06','07','08')
			";

		// 2023.01.11 36392 begin
		$event_date = "";
		$event_date_begin = "";
		$event_date_end = "";
		if (isset($condition['event_date'])) {
			$event_date = $condition['event_date'];
		}
		if ($event_date == null || $event_date == NULl) {
			$event_date = "";
		}
		if ($event_date != "") {
			$event_date_obj = json_decode($event_date);
			if ($event_date_obj == null) {
				$event_date_begin = substr($event_date, 0, 10);
				$event_date_end = $event_date_begin;
			} else {
				$ret = json_encode($event_date_obj);
				$event_date_begin = substr($event_date_obj->startDate, 0, 10);
				$event_date_end = substr($event_date_obj->endDate, 0, 10);
			}
		}
		$sql_event_date = "";
		if ($event_date != "") {
			$sql_event_date = " AND JSON_VALID(a.item_data) = 1
				AND JSON_TYPE(JSON_EXTRACT(a.item_data, '$.event_date')) <> 'NULL'
				AND JSON_EXTRACT(a.item_data, '$.event_date') <> '' 
				AND check_item_event_date(JSON_UNQUOTE(JSON_EXTRACT(a.item_data, '$.event_date')), '$event_date_begin', '$event_date_end') = 1
				";
		}
		// 2023.01.11 36392 end

		$sql_num = "SELECT COUNT(DISTINCT a.item_id)
				FROM t_item a
				INNER JOIN t_item_description b
				ON a.item_id = b.item_id AND b.lang_cd = :lang_cd
				INNER JOIN t_item_display r
						ON a.item_id = r.item_id
						AND a.item_div = r.item_div
						AND find_in_set(:lang_cd, r.lang_display)
						AND r.public_flg = 1
				WHERE r.bot_id in ($bot_id) 
					AND a.delete_flg <> 1
					AND (r.bot_id = :original_bot_id OR (r.bot_id = :template_bot_id AND r.item_div != 2))
					AND a.item_div = :item_div
					AND (a.start_date is NULL OR DATE_FORMAT(a.start_date,'%Y-%m-%d') <= :cur_date)
					$sql_status_cd_end_date
					AND (a.regular_start = :regular_date_kara 
						OR a.regular_end = :regular_date_kara 
						OR (a.regular_start <= a.regular_end AND a.regular_start <= :regular_date AND :regular_date <= a.regular_end)
						OR (a.regular_start > a.regular_end AND (a.regular_start <= :regular_date OR :regular_date <= a.regular_end))
						)
					AND concat(' ', a.class_cd) like concat('% ', m.class_cd, '%')
					$sql_area_cd
					$sql_event_date
				";
		if ($class_type_sub_cd != NULL) {
			$class_type_sub_cd_parts = explode(",", $class_type_sub_cd);
			$class_type_sub_cd_num = count($class_type_sub_cd_parts);
			$sql_num = $sql_num . $this->create_sql_for_multi_code_exist_all("get_m_class_code_child", "a.item_class_type_sub_cd", $class_type_sub_cd);;
		}
		if ($features_cd != NULL) {
			$sql_num = $sql_num . $this->create_sql_for_multi_code_exist_atleast_one("get_m_class_code_child", "a.features_cd", $features_cd);
		}
		$sql_num = "($sql_num) AS content_num";

		$sql = "
			SELECT
				*,$sql_class_cd_sort
			FROM (
				SELECT m.*, 
					$sql_num
				FROM m_class_code m
				WHERE
					m.code_div = :code_div
					$sql_parent_class_cd
					AND m.lang_cd = :lang_cd
			) k
			WHERE 
				k.content_num > 0
			ORDER BY
				class_cd_sort,
				sort
			";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':item_div' => $item_div,
			':code_div' => $code_div,
			':class_cd' => $class_cd,
			':area_cd' => $area_cd,

			':features_cd' => $features_cd,
			':class_type_sub_cd' => $class_type_sub_cd,
			

			':bot_id' => $bot_id,
			':lang_cd' => $lang_cd,
			':cur_date' => date('Y-m-d'),
			':regular_date' => date('m-d'),
			':regular_date_kara' => "",
			':original_bot_id' => $original_bot_id,
			':template_bot_id' => $template_bot_id,
		));
		$results = $query->execute();
		return $results->as_array();
	}

	function get_m_class_code_child_product($condition) {
		// CLASS_CODE命令
		$bot_id = $condition['bot_id'];
		$lang_cd = $condition["lang_cd"];
		$code_div = $condition['code_div'];
		$class_cd = $condition['class_cd'];
		$link_key = "";
		if (isset($condition["link_key"])) {
			$link_key = $condition['link_key'];
		}
		if ($link_key == "") {
			$sql_link_key = " AND a.link_key = '' ";
		} else if ($link_key == "all_link_key") {
			$sql_link_key = " ";
		} else {
			$sql_link_key = " AND a.link_key = :link_key ";
		}

		// AND a.code_div = :code_div  // 2020.01.28 t_product.code_div字段删除
		$sql_num = "SELECT COUNT(DISTINCT a.product_id)
				FROM t_product a
				INNER JOIN t_product_description b
					ON a.product_id = b.product_id 
					AND b.lang_cd = :lang_cd
				LEFT JOIN t_item_display c
					ON a.bot_id = c.bot_id
					AND a.product_id = c.item_id
					AND c.item_div = 5
				WHERE a.bot_id = :bot_id 
					AND a.delete_flg = 0
					AND b.delete_flg = 0
					AND (c.lang_display IS NULL OR trim(c.lang_display) = '' OR find_in_set(:lang_cd, c.lang_display))
					AND (c.public_flg = 1 OR c.public_flg IS NULL)
					AND concat(' ', a.class_cd) like concat('% ', m.class_cd, '%') 
					AND (a.start_date is NULL OR DATE_FORMAT(a.start_date,'%Y-%m-%d') <= :cur_date)
					AND (a.end_date is NULL OR DATE_FORMAT(a.end_date,'%Y-%m-%d') >= :cur_date)
					AND (
						a.regular_start is NULL
						OR a.regular_end is NULL
						OR a.regular_start = :regular_date_kara 
						OR a.regular_end = :regular_date_kara 
						OR (a.regular_start <= a.regular_end AND a.regular_start <= :regular_date AND :regular_date <= a.regular_end)
						OR (a.regular_start > a.regular_end AND (a.regular_start <= :regular_date OR :regular_date <= a.regular_end))
					)
					$sql_link_key
				";
		$sql_num = "($sql_num) AS content_num";

		$sql = "
			SELECT
				* 
			FROM (
				SELECT m.*, 
					$sql_num
				FROM m_class_code m
				WHERE
					m.code_div = :code_div
					AND m.parent_cd = :class_cd
					AND m.lang_cd = :lang_cd
			) k
			WHERE 
				k.content_num > 0
			ORDER BY
				sort
			";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':code_div' => $code_div,
			':class_cd' => $class_cd,
			':link_key' => $link_key,

			':bot_id' => $bot_id,
			':lang_cd' => $lang_cd,

			':cur_date' => date('Y-m-d'),
			':regular_date' => date('m-d'),
			':regular_date_kara' => "",
		));
		$results = $query->execute();
		return $results->as_array();
	}

	// select_area_by_class_code.js
	function get_child_area_by_class_code($bot_info, $condition) {
		$code_div = $condition["code_div"];
		$area_cd = $condition["area_cd"];
		$class_cd = "";
		if (isset($condition["class_cd"])) {
			$class_cd = $condition["class_cd"];
		}

		$original_bot_id = $bot_info['bot_id'];
		$bot_id = $bot_info['bot_id'];
		$bot_id = $this->get_bot_id_list_include_template_bot($bot_id);
		$template_bot_id = $this->get_template_bot_id($bot_id);

		$item_div = $condition["item_div"];
		$lang_cd = $condition["lang_cd"];
		$bot_id_list = $condition["bot_id_list"];
		
		$sql_bot_id_list = "";
		if ($bot_id_list != "") {
			$sql_bot_id_list = "
			INNER JOIN t_bot d
			ON a.item_cd = d.facility_cd
			AND d.bot_id in ($bot_id_list)
			";
		}

		$sql_class_cd = "";
		if ($class_cd != "") {
			$sql_class_cd = "
			AND concat(' ', a.class_cd) like concat('% ', :class_cd, '%')
			";
		}

        // 2020.10.12 #6073
		$sql_status_cd_end_date = 
			" AND (a.end_date is NULL OR DATE_FORMAT(a.end_date,'%Y-%m-%d') >= :cur_date)
			AND a.item_status_cd in ('01','03','04','06','07','08')
			";

		// 2023.01.11 36392 begin
		$event_date = "";
		$event_date_begin = "";
		$event_date_end = "";
		if (isset($condition['event_date'])) {
			$event_date = $condition['event_date'];
		}
		if ($event_date == null || $event_date == NULl) {
			$event_date = "";
		}
		if ($event_date != "") {
			$event_date_obj = json_decode($event_date);
			if ($event_date_obj == null) {
				$event_date_begin = substr($event_date, 0, 10);
				$event_date_end = $event_date_begin;
			} else {
				$ret = json_encode($event_date_obj);
				$event_date_begin = substr($event_date_obj->startDate, 0, 10);
				$event_date_end = substr($event_date_obj->endDate, 0, 10);
			}
		}
		$sql_event_date = "";
		if ($event_date != "") {
			$sql_event_date = " AND JSON_VALID(a.item_data) = 1
				AND JSON_TYPE(JSON_EXTRACT(a.item_data, '$.event_date')) <> 'NULL'
				AND JSON_EXTRACT(a.item_data, '$.event_date') <> '' 
				AND check_item_event_date(JSON_UNQUOTE(JSON_EXTRACT(a.item_data, '$.event_date')), '$event_date_begin', '$event_date_end') = 1
				";
		}
		// 2023.01.11 36392 end

		$sql_num = "SELECT COUNT(DISTINCT a.item_id)
				FROM t_item a
				INNER JOIN t_item_description b
				ON a.item_id = b.item_id AND b.lang_cd = :lang_cd
				INNER JOIN t_item_display r
						ON a.item_id = r.item_id
						AND a.item_div = r.item_div
						AND find_in_set(:lang_cd, r.lang_display)
						AND r.public_flg = 1
				$sql_bot_id_list
				WHERE
					r.bot_id in ($bot_id) 
				AND a.delete_flg <> 1
				AND (r.bot_id = :original_bot_id OR (r.bot_id = :template_bot_id AND r.item_div != 2))
				AND a.item_div = :item_div
				AND (a.start_date is NULL OR DATE_FORMAT(a.start_date,'%Y-%m-%d') <= :cur_date)
				$sql_status_cd_end_date
				AND (a.regular_start = :regular_date_kara 
					OR a.regular_end = :regular_date_kara 
					OR (a.regular_start <= a.regular_end AND a.regular_start <= :regular_date AND :regular_date <= a.regular_end)
					OR (a.regular_start > a.regular_end AND (a.regular_start <= :regular_date OR :regular_date <= a.regular_end))
					)
				AND concat(',', a.area_cd) like concat('%,', m.class_cd, '%')
				$sql_class_cd
				$sql_event_date
				";
		$sql_num = "($sql_num) AS content_num";
		

		$sql = "SELECT * 
				FROM (
					SELECT m.*, 
						$sql_num
					FROM m_class_code m
					WHERE
						m.code_div = :code_div
						AND m.parent_cd = :area_cd
						AND m.lang_cd = :lang_cd
				) k
				WHERE 
					k.content_num > 0
				ORDER BY
					sort
				";

		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':code_div' => $code_div,
			':area_cd' => $area_cd,
			':class_cd' => $class_cd,

			':bot_id' => $bot_id,

			':item_div' => $item_div,
			':lang_cd' => $lang_cd,
			':cur_date' => date('Y-m-d'),
			':regular_date' => date('m-d'),
			':regular_date_kara' => "",
			':original_bot_id' => $original_bot_id,
			':template_bot_id' => $template_bot_id,
		));
		$results = $query->execute();
		return $results->as_array();		
	}

	// $intent_type_cd是单个cd
	// 2021.07.08 拡張機能 #14198 FAQの分類が複数設定できるようにしたい
	function get_child_faq_by_class_code($condition) {
		$code_div = $condition["code_div"];
		$intent_type_cd = $condition["intent_type_cd"];

		$bot_id = $condition['bot_id'];
		// 2022.06.25 #28022 begin
		$bot_id_origin = $bot_id;
		$parent_bot_id = $this->get_grp_parent_bot_id($bot_id_origin);
		$sql_inherit = "";
		// 2022.06.25 #28022 end
		// 2020.04.13 $bot_id = $this->get_bot_id_list_include_template_bot($bot_id);
		// $bot_id = $this->get_all_bot_id_list_for_group_parent_bot($bot_id);
		$bot_id = $this->get_all_bot_id_list_for_group_bot($bot_id); // 2021.06.14 改为当前为子的时候，也查父bot
		// 2022.06.25 #28022 begin
		if ($parent_bot_id != -1) {
			// group bot
			if ($parent_bot_id == $bot_id_origin) {
				// 父bot
			} else if ($parent_bot_id != $bot_id_origin) {
				// 是子bot,需要过滤掉父bot的不可继承的intent
				$sql_inherit = "AND (b.bot_id <> :parent_bot_id OR b.inherit = 1) ";
			}
		}
		// 2022.06.25 #28022 end

		$lang_cd = $condition["lang_cd"];
		
		$sql_num = "SELECT COUNT(DISTINCT a.intent_cd, a.sub_intent_cd)
				FROM m_bot_intent a
				INNER JOIN t_bot_intent b
					ON a.intent_cd = b.intent_cd
					AND a.lang_cd = b.lang_cd
					AND a.sub_intent_cd = b.sub_intent_cd
				WHERE
					b.bot_id in ($bot_id)
					$sql_inherit
					AND a.level <> 4
					AND b.lang_cd=:lang_cd
					AND (
						       (b.intent_type_cd = '' AND concat(',',a.intent_type_cd) like concat('%,',m.class_cd, '%'))
					        OR (b.intent_type_cd != '' AND concat(',',b.intent_type_cd) like concat('%,',m.class_cd, '%'))
						)

				";
		$sql_num = "($sql_num) AS content_num";
		

		$sql = "SELECT * 
				FROM (
					SELECT m.*, 
						$sql_num
					FROM m_class_code m
					WHERE
						m.code_div = :code_div
						AND m.parent_cd = :intent_type_cd
						AND m.lang_cd = :lang_cd
				) k
				WHERE 
					k.content_num >= 0
				ORDER BY
					sort
				";

		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':code_div' => $code_div,
			':intent_type_cd' => $intent_type_cd,

			':bot_id' => $bot_id,
			':parent_bot_id' => $parent_bot_id,

			':lang_cd' => $lang_cd,
		));
		$results = $query->execute();
		return $results->as_array();
	}

	function get_class_cd_name($condition) {
		$sql = "SELECT * 
				FROM m_class_code m
				WHERE
					m.code_div = :code_div
					AND m.class_cd = :class_cd
					AND m.lang_cd = :lang_cd
				";

		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':code_div' => $condition["code_div"],
			':class_cd' => $condition["class_cd"],
			':lang_cd' => $condition["lang_cd"],
		));

		$results = $query->execute();
		return $results->as_array();
	}

	// 2023.01.11 #36344 begin
	function get_faq_selection_extend($condition) {
		$sql = "SELECT * 
				FROM t_bot_selection_faq_extend m
				WHERE
					m.code_div = :code_div
					AND m.class_cd = :class_cd
					AND m.lang_cd = :lang_cd
				";

		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':code_div' => $condition["code_div"],
			':class_cd' => $condition["class_cd"],
			':lang_cd' => $condition["lang_cd"],
		));

		$results = $query->execute();
		return $results->as_array();
	}
	// 2023.01.11 #36344 end

	// 2020.03.30 #3788
	function get_def_intent($bot_id, $lang_cd, $check_def_intent, $results) {
		if (count($results) == 0) {
			return $results;
		}

		if (strpos($results[0]["skill"], "[") != 0) {
			error_log("is not [] skill format");
			return $results;
		}
		if (strpos($results[0]["skill"], "DEF_INTENT") == FALSE) {
			return $results;
		}
		$skill_def = json_decode($results[0]["skill"]);
		error_log($skill_def[0]->skill);
		if ($skill_def[0]->skill == "DEF_INTENT") {
			$skill_intent_cd = $skill_def[0]->params->intent_cd;

			if ($check_def_intent == "true") {
				// 要检查是否该可用
				$sql = "SELECT *
						FROM t_bot_def_intent a
						WHERE
							a.bot_id = :bot_id
							AND a.intent_cd = :intent_cd
						";

				// 先用当前bot取
				$query = DB::query(Database::SELECT, $sql);
				$query->parameters(array(
					':bot_id' => $bot_id,
					':intent_cd' => $skill_intent_cd,
				));

				$skill_results = $query->execute()->as_array();		
				if (count($skill_results) > 0) {
					// 可用
				} else {
					// 不可用
					// 返回空数组
					return [];
				}
			}

			$condition = array(
				"bot_id" => $bot_id,
				"lang_cd" => $lang_cd,
				"intent_cd" => $skill_intent_cd,
				"sub_intent_cd" => "",
				"check_def_intent" => $check_def_intent
			);
			return $this->get_t_intent_skill($bot_id, $condition);
		}
	}
	// 2020.03.30 #3788 end

	function get_t_intent_skill($bot_id, $condition)
	{
		$sub_intent_cd = "";
		if (isset($condition["sub_intent_cd"])) {
			$sub_intent_cd = $condition["sub_intent_cd"];
		}
		if ($sub_intent_cd == "NONE_SUB_INTENT_CD") {
			$sub_intent_cd = "";
		}
		$lang_cd = "";
		if (isset($condition["lang_cd"])) {
			$lang_cd = $condition["lang_cd"];
		}
		$check_def_intent = "false";
		if (isset($condition["check_def_intent"])) {
			$check_def_intent = $condition["check_def_intent"];
		}

		$sql = "SELECT skill
				FROM t_intent_skill a
				WHERE
					a.bot_id = :bot_id
					AND (a.lang_cd = :lang_cd OR a.lang_cd = '' OR a.lang_cd is NULL)
					AND a.intent_cd = :intent_cd
					AND (a.sub_intent_cd = :sub_intent_cd
						  OR
						  find_in_set(:sub_intent_cd, replace(a.sub_intent_cd,'-',',')) > 0
						)
				ORDER BY
					a.lang_cd DESC,
					a.sub_intent_cd
				";

		// 先用当前bot取
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':bot_id' => $bot_id,
			':lang_cd' => $lang_cd,
			':intent_cd' => $condition['intent_cd'],
			':sub_intent_cd' => $sub_intent_cd,
		));

		$results = $query->execute()->as_array();

		if (count($results) > 0) {
			// 2020.03.30 #3788
			$results_def = $this->get_def_intent($bot_id, $lang_cd, $check_def_intent, $results);
			if (count($results_def) > 0) {
				return $results_def;
			}
			// return $results;
			// 2020.03.30 #3788 end
		}

		// 2020.04.28 add begin
		$template_bot_id = $this->get_template_bot_id($bot_id);
		if ($template_bot_id != "" && $template_bot_id != $bot_id) {
			// 从template_bot_id取
			$query = DB::query(Database::SELECT, $sql);
			$query->parameters(array(
				':bot_id' => $template_bot_id,
				':lang_cd' => $lang_cd,
				':intent_cd' => $condition['intent_cd'],
				':sub_intent_cd' => $sub_intent_cd,
			));
			
			$results = $query->execute()->as_array();
			if (count($results) > 0) {
				// 2020.03.30 #3788
				$results_def = $this->get_def_intent($bot_id, $lang_cd, $check_def_intent, $results);
				if (count($results_def) > 0) {
					return $results_def;
				}
				// return $results;
				// 2020.03.30 #3788 end
			}
		}
		// 2020.04.28 add end
		
		// 取父bot_id
		$parent_bot_id = $this->get_grp_parent_bot_id($bot_id);
		if ($parent_bot_id != -1 && $parent_bot_id != $bot_id) {
			// 从parent_bot_id取
			$query = DB::query(Database::SELECT, $sql);
			$query->parameters(array(
				':bot_id' => $parent_bot_id,
				':lang_cd' => $lang_cd,
				':intent_cd' => $condition['intent_cd'],
				':sub_intent_cd' => $sub_intent_cd,
			));
			
			$results = $query->execute()->as_array();
			if (count($results) > 0) {
				// 2020.03.30 #3788
				$results_def = $this->get_def_intent($bot_id, $lang_cd, $check_def_intent, $results);
				if (count($results_def) > 0) {
					return $results_def;
				}
				// return $results;
				// 2020.03.30 #3788 end
			}
		}

		// 最后从bot_id=0取
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':bot_id' => 0,
			':lang_cd' => $lang_cd,
			':intent_cd' => $condition['intent_cd'],
			':sub_intent_cd' => $sub_intent_cd,
		));
		
		$results = $query->execute()->as_array();
		// 2020.03.30 #3788
		$results_def = $this->get_def_intent($bot_id, $lang_cd, $check_def_intent, $results);
		return $results_def;
		// return $results;
		// 2020.03.30 #3788 end
	}	


	function get_all_t_intent_skill($condition)
	{
		$bot_id = $condition["bot_id"];
		$sub_intent_cd = "";
		if (isset($condition["sub_intent_cd"])) {
			$sub_intent_cd = $condition["sub_intent_cd"];
		}
		if ($sub_intent_cd == "NONE_SUB_INTENT_CD") {
			$sub_intent_cd = "";
		}
		$lang_cd = "";
		if (isset($condition["lang_cd"])) {
			$lang_cd = $condition["lang_cd"];
		}
		$check_def_intent = "false";
		if (isset($condition["check_def_intent"])) {
			$check_def_intent = $condition["check_def_intent"];
		}

		$bot_id_list = $bot_id;
		$template_bot_id = $this->get_template_bot_id($bot_id);
		if ($template_bot_id != "" && $template_bot_id != $bot_id) {
			$bot_id_list = "$bot_id_list,$template_bot_id";
		}
		$parent_bot_id = $this->get_grp_parent_bot_id($bot_id);
		if ($parent_bot_id != -1 && $parent_bot_id != $bot_id) {
			$bot_id_list = "$bot_id_list,$parent_bot_id";
		}
		$base_bot_id = 0;
		$bot_id_list = "$bot_id_list,$base_bot_id";

		$sql = "SELECT a.bot_id, a.sub_intent_cd, a.skill, b.question,
				CASE 
					WHEN a.bot_id = :bot_id THEN 1
					WHEN a.bot_id = :template_bot_id THEN 2
					WHEN a.bot_id = :parent_bot_id THEN 3
					WHEN a.bot_id = :base_bot_id THEN 4
					END AS sort
				FROM t_intent_skill a
				INNER JOIN m_bot_intent b
					ON a.intent_cd = b.intent_cd
					AND a.sub_intent_cd = b.sub_intent_cd
					AND b.lang_cd = :lang_cd
					AND b.level <> 4
					AND (
						  b.level != 3
						  OR
						  (b.item_ids = '' OR b.item_ids IS NULL)
						  OR
						  b.item_ids in ($bot_id_list)
						)
				WHERE
					a.bot_id in ($bot_id_list)
					AND (a.lang_cd = :lang_cd OR a.lang_cd = '' OR a.lang_cd is NULL)
					AND a.intent_cd = :intent_cd
					AND (:sub_intent_cd = '' OR a.sub_intent_cd = '')
				ORDER BY
					sort,
					a.lang_cd DESC,
					a.sub_intent_cd
				";

		// 先用当前bot取
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':bot_id' => $bot_id,
			':template_bot_id' => $template_bot_id,
			':parent_bot_id' => $parent_bot_id,
			':base_bot_id' => $base_bot_id,
			':lang_cd' => $lang_cd,
			':intent_cd' => $condition['intent_cd'],
			':sub_intent_cd' => $sub_intent_cd,
		));

		$results = $query->execute()->as_array();

		$final_ret = [];
		$all_sub_intent_cd = [];
		if (count($results) > 0) {
			for ($i=0; $i<count($results); $i++) {
				$cur = [];
				$cur[0] = $results[$i];
				$cur_sub_intent_cd = $cur[0]["sub_intent_cd"];
				if (in_array($cur_sub_intent_cd,$all_sub_intent_cd)) {
					continue;
				}
	
				$results_def = $this->get_def_intent($cur[0]["bot_id"], $lang_cd, $check_def_intent, $cur);
				if (count($results_def) > 0) {
					$cur_ret = [];
					$cur_ret["bot_id"] = $cur[0]["bot_id"];
					$cur_ret["sub_intent_cd"] = $cur_sub_intent_cd;
					$cur_ret["skill"] = $results_def["0"]["skill"];
					$cur_ret["question"] = $results_def["0"]["question"];

					$final_ret[] =$cur_ret;
					$all_sub_intent_cd[] = $cur_sub_intent_cd;
				}
			}
		}

		return $final_ret;
		// return $results;
		// 2020.03.30 #3788 end
	}

	function read_t_product($condition) {
		$bot_id = $condition['bot_id'];
//		$code_div = $condition['code_div'];	// 2020.01.28 t_product表的code_div字段删除
		$class_cd = $condition['class_cd'];
		$product_cds = $condition['product_cds'];
		$lang_cd = $condition['lang_cd'];
		$link_key = "";
		if (isset($condition["link_key"])) {
			$link_key = $condition['link_key'];
		}

		if ($link_key == "") {
			$sql_link_key = " AND a.link_key = '' ";
		} else if ($link_key == "all_link_key") {
			$sql_link_key = " ";
		} else {
			$sql_link_key = " AND a.link_key = :link_key ";
		}

		//				AND a.code_div = :code_div
		$sql = "SELECT a.product_cd,a.link_key,a.product_data,discount_type,discount_value,a.class_cd,
				b.*,
				c.sort_no1,c.sort_no2,c.sort_no3,c.sort_no4,c.sort_no5
			FROM t_product a
			INNER JOIN t_product_description b
			ON a.product_id = b.product_id
			LEFT JOIN t_item_display c
			ON a.bot_id = c.bot_id
			AND a.product_id = c.item_id
			AND c.item_div = 5
			WHERE
				a.bot_id = :bot_id
				AND a.delete_flg = 0
				AND b.delete_flg = 0
				AND concat(' ', a.class_cd) like concat('% ', :class_cd, '%') 
				AND (:product_cds = '' OR find_in_set(a.product_cd, :product_cds) > 0)
				AND b.lang_cd = :lang_cd
				AND (c.lang_display IS NULL OR trim(c.lang_display) = '' OR find_in_set(:lang_cd, c.lang_display))
				AND (c.public_flg = 1 OR c.public_flg IS NULL)
				AND (a.start_date is NULL OR DATE_FORMAT(a.start_date,'%Y-%m-%d') <= :cur_date)
				AND (a.end_date is NULL OR DATE_FORMAT(a.end_date,'%Y-%m-%d') >= :cur_date)
				AND (
					a.regular_start is NULL
					OR a.regular_end is NULL
					OR a.regular_start = :regular_date_kara 
					OR a.regular_end = :regular_date_kara 
					OR (a.regular_start <= a.regular_end AND a.regular_start <= :regular_date AND :regular_date <= a.regular_end)
					OR (a.regular_start > a.regular_end AND (a.regular_start <= :regular_date OR :regular_date <= a.regular_end))
				)
				$sql_link_key
			ORDER BY
				c.sort_no1,
				c.sort_no2,
				c.sort_no3,
				c.sort_no4,
				c.sort_no5,
				b.product_id
			";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':bot_id' => $bot_id,
//			':code_div' => $code_div,
			':class_cd' => $class_cd,
			':product_cds' => $product_cds,
			':lang_cd' => $lang_cd,
			':link_key' => $link_key,
			':cur_date' => date('Y-m-d'),
			':regular_date' => date('m-d'),
			':regular_date_kara' => "",
		));
		$results = $query->execute();
		return $results->as_array();
	}

	function read_t_product_only($condition) {
		$bot_id = $condition['bot_id'];
//		$code_div = $condition['code_div'];	// 2020.01.28 t_product表的code_div字段删除
		$class_cd = $condition['class_cd'];
		$product_cds = $condition['product_cds'];
		$link_key = "";
		if (isset($condition["link_key"])) {
			$link_key = $condition['link_key'];
		}

		if ($link_key == "") {
			$sql_link_key = " AND a.link_key = '' ";
		} else if ($link_key == "all_link_key") {
			$sql_link_key = " ";
		} else {
			$sql_link_key = " AND a.link_key = :link_key ";
		}

		//				AND a.code_div = :code_div
		$sql = "SELECT a.product_cd,a.link_key,a.product_data,a.discount_type,a.discount_value,a.tags,a.class_cd
			FROM t_product a
			WHERE
				a.bot_id = :bot_id
				AND a.delete_flg = 0
				AND concat(' ', a.class_cd) like concat('% ', :class_cd, '%') 
				AND (:product_cds = '' OR find_in_set(a.product_cd, :product_cds) > 0)
				AND (a.start_date is NULL OR DATE_FORMAT(a.start_date,'%Y-%m-%d') <= :cur_date)
				AND (a.end_date is NULL OR DATE_FORMAT(a.end_date,'%Y-%m-%d') >= :cur_date)
				AND (
					a.regular_start is NULL
					OR a.regular_end is NULL
					OR a.regular_start = :regular_date_kara 
					OR a.regular_end = :regular_date_kara 
					OR (a.regular_start <= a.regular_end AND a.regular_start <= :regular_date AND :regular_date <= a.regular_end)
					OR (a.regular_start > a.regular_end AND (a.regular_start <= :regular_date OR :regular_date <= a.regular_end))
				)
				$sql_link_key
			";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':bot_id' => $bot_id,
//			':code_div' => $code_div,
			':class_cd' => $class_cd,
			':product_cds' => $product_cds,
			':link_key' => $link_key,
			':cur_date' => date('Y-m-d'),
			':regular_date' => date('m-d'),
			':regular_date_kara' => "",
		));
		$results = $query->execute();
		return $results->as_array();
	}

	// 取得指定child bot list里有t_bot_order数据的所有bot
	function get_group_child_bots_have_t_bot_order($condition) {
		$child_bot_list = $condition['bot_list'];
		$member_id = "";
		if (array_key_exists("member_id", $condition)) {
			$member_id = $condition['member_id'];
		}

		$sql = "SELECT distinct a.bot_id
				FROM t_bot_order a
				WHERE
					a.bot_id in ($child_bot_list) 
					AND a.item_div = 5
			";

	
		if ($member_id != "") {
			$sql = "$sql AND a.member_id = :member_id ";
		}
		$sql = "$sql ORDER BY a.bot_id ";

		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':member_id' => $member_id,
		));
		$results = $query->execute();
		return $results->as_array();
	}

	// 2021.08.21 #12594 t_bot_order表增加pay_data字段
	// 2021.09.24 改善 #16604 t_bot_order表增加order_data_history字段
	function read_t_bot_order_latest($condition) {
		$bot_id = $condition['bot_id'];
		$member_id = "";
		$order_id = "";
		$agent_reserve_id = "";
		if (array_key_exists("member_id", $condition)) {
			$member_id = $condition['member_id'];
		}
		if (array_key_exists("order_id", $condition)) {
			$order_id = $condition['order_id'];
		}
		if (array_key_exists("agent_reserve_id", $condition)) {
			$agent_reserve_id = $condition['agent_reserve_id'];
		}

		$sql = "SELECT a.bot_id, a.dest_bot_id, a.order_id, a.member_id, a.lang_cd, a.order_no as agent_reserve_id,a.order_status_cd, a.order_data as reserve_data, a.order_date,a.cancel_date,
					b.no as agent_notification_number, b.order_type_div as reserve_type, b.link_id as tl_reserve_id,
					b.cancel_link_id as tl_cancel_reserve_id, b.order_date as tl_date, b.order_data as tl_result, b.pay_data as kessai_info, a.pay_data as pay_data,
					a.order_data_history
				FROM t_bot_order a
				LEFT JOIN 
					(
						SELECT 
							c.* 
						FROM t_bot_order_his c
						INNER JOIN
							(
								SELECT order_id,MAX(order_date) as max_order_date
								FROM t_bot_order_his
								GROUP BY
									order_id
							) d
						ON
							c.order_id = d.order_id
							AND c.order_date = d.max_order_date
					) b
					ON a.order_id = b.order_id
				WHERE
					1 = 1
					AND a.item_div = 5
					
			";

		// 2020.06.24 改为如果指定了order_id就不看别的条件
        if ($order_id != "") {
            $sql = "$sql AND a.order_id = :order_id ";
        } else if ($agent_reserve_id != "") {
			// 2022.01.28 改为允许只指定agent_reserve_id
			$sql = "$sql AND a.order_no = :agent_reserve_id ";
		} else {
			$sql = "$sql AND a.bot_id = :bot_id ";
			if ($member_id != "") {
				$sql = "$sql AND a.member_id = :member_id ";
			}
			if ($agent_reserve_id != "") {
				$sql = "$sql AND a.order_no = :agent_reserve_id ";
			}
		}
		$sql = "$sql ORDER BY a.order_date DESC ";

		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':bot_id' => $bot_id,
			':member_id' => $member_id,
			':order_id' => $order_id,
			':agent_reserve_id' => $agent_reserve_id,
		));
		$results = $query->execute();
		return $results->as_array();
	}

	// 当预约内容发生了变化时，需要临时保存的时候，调用此函数
	public function save_tl_pre_reserve_data($post)
	{
		$reserve_data = json_decode($post['reserve_data']);
		// 2021.08.21 #12594 begin
		$payment_type_cd = "";
		if (array_key_exists("payment_type_cd", $reserve_data)) {
			$payment_type_cd = $reserve_data->payment_type_cd;
		}
		$card_type = "";
		if (array_key_exists("card_type", $reserve_data)) {
			$card_type = $reserve_data->card_type;
		}
		// 2021.08.21 #12594 end

		$order_id = "";
		if (array_key_exists("order_id", $post)) {
			$order_id = $post['order_id'];
		}

		if ($order_id == "") {
			// 新预约
			$order = ORM::factory('botorder');
			$order->order_status_cd = "00";

			if (array_key_exists("link_type_cd", $post)) {
				$order->link_type_cd = $post['link_type_cd'];
			}
			$order->payment_type_div = $payment_type_cd;
	
		} else {
			// 既存预约
			
			$order = ORM::factory('botorder', $order_id);
			if ($order->order_status_cd == "01") {
				// 2021.08.21 #12594 begin
				error_log("first change payment_type_cd= $payment_type_cd card_type= $card_type end" );
				if ($payment_type_cd == "2" && $card_type == "jtb") {
					// 第一次变更
					$order->order_status_cd = "20";
				} else {
					$order->order_status_cd = "04";	//预约确定后的假预约 2020.12.22 #7206

					// 2022.04.01 改为普通预约不再做假预约数据
					return ;
				}
				// 2021.08.21 #12594 end

				// 2021.09.24 改善 #16604 begin
				// 记录变更履历
				$temp_order_data_history = json_decode("[]");
				$temp_order_data_history[0] = json_decode($order->order_data);
				$order->order_data_history = json_encode($temp_order_data_history, JSON_UNESCAPED_UNICODE);
				// 2021.09.24 改善 #16604 end
			} else if ($order->order_status_cd == "02") {
				// 2021.08.21 #12594 begin
				error_log("second change payment_type_cd= $payment_type_cd card_type= $card_type end" );
				if ($payment_type_cd == "2" && $card_type == "jtb") {
					$order->order_status_cd = "20";
				} else {
					$order->order_status_cd = "05"; //预约变更后的假预约 2020.12.22 #7206
					// 2022.04.01 改为普通预约不再做假预约数据
					return ;
				}
				// 2021.08.21 #12594 end

				// 2021.09.24 改善 #16604 begin
				// 记录变更履历
				if ($order->order_data_history == null || $order->order_data_history == "") {
					error_log("second change 01");
					$temp_order_data_history = json_decode("[]");
				} else {
					error_log("second change 02");
					$temp_order_data_history = json_decode($order->order_data_history);
				}
				$temp_order_data_history[count($temp_order_data_history)] = json_decode($order->order_data);
				$order->order_data_history = json_encode($temp_order_data_history, JSON_UNESCAPED_UNICODE);
				// 2021.09.24 改善 #16604 end
			} else if ($order->order_status_cd == "04") {
				$order->order_status_cd = "04"; //预约确定后的假预约 2020.12.22 #7206
			} else if ($order->order_status_cd == "05") {
				$order->order_status_cd = "05"; //预约变更后的假预约 2020.12.22 #7206
			} else if ($order->order_status_cd == "20") {
				$order->order_status_cd = "20"; //JTB预约变更后的假预约 2021.08.21 #12594
			} else {
				// 新规假预约
				// 2021.08.21 #12594 begin
				if ($payment_type_cd == "2" && $card_type == "jtb") {
					$order->order_status_cd = "11";
				} else {
					$order->order_status_cd = "00";
				}
				// 2021.08.21 #12594 end
				//$order->order_status_cd = "00";
			}
		}

		$order->bot_id =  $post['bot_id'];
		if (isset($post['dest_bot_id']) && $post['dest_bot_id'] != $post['bot_id'] && $post['dest_bot_id'] != "") {
			$order->dest_bot_id =  $post['dest_bot_id'];
		}

		$order->member_id = $post['member_id'];
		$order->lang_cd = $post['lang_cd'];
		$order->order_no = $post['agent_reserve_id'];

		$order->checkin_date = $reserve_data->checkin_date;
		$order->checkout_date = $reserve_data->checkout_date;
		$order->adult_num = $reserve_data->adult_num;
		$order->child_num = $reserve_data->child_num;
		$order->checkin_name = $reserve_data->checkin_name;
		$order->checkin_name_kana =  $reserve_data->checkin_name_kana;
		$order->checkin_tel = $reserve_data->checkin_tel;
		$order->checkin_mail = $reserve_data->checkin_mail;
		$order->num = $reserve_data->num;

		$order->order_date = date('Y-m-d H:i:s');
		// 2022.04.01 begin
		$reserve_data->order_date = $order->order_date;
		$order->order_data = json_encode($reserve_data, JSON_UNESCAPED_UNICODE);
		// $order->order_data = $post['reserve_data'];
		// 2022.04.01 end

		$order->save();

		$ret = array();
		$ret['order_id'] = $order->order_id;

		// 2020.01.17 将电话号码和邮件地址存到t_bot_member里去
		// 2020.11.26 #6844 当privacy==1的时候才保存
		if (isset($reserve_data->privacy) && $reserve_data->privacy == 1) {
			// 2021.10.14 #17343 新增t_bot_member.order_name_kanji
			// 2022.02.10 #21552 增加privacy
			$this->set_member_email_phone_name($post['bot_id'], $post['member_id'], $reserve_data->checkin_tel, $reserve_data->checkin_mail, $reserve_data->checkin_name, $reserve_data->name_kanji, 1);
		} else {
			// 2022.02.10 #21552 增加privacy
			$this->set_member_email_phone_name($post['bot_id'], $post['member_id'], "", "", "", "", 0);
		}

		return $ret;
	}

	// 2021.08.21 #12594 begin
	public function save_reserve_data_card_info($post) {
		$order_id = $post['order_id'];
		$action = $post['action'];	// "regist"
		$status = $post['status']; // ok ,error

		$order = ORM::factory('botorder', $post['order_id']);
		if ($action == "regist") {
			// engine注册新预约
			if ($status == "error") {
				$order->order_status_cd = "12";
			} else if ($status == "ok") {
				$order->order_status_cd = "13";
			}
			$pay_data = $post['pay_data'];	// string类型
			$order->pay_data = $pay_data;
			$order->save();
		} else if ($action == "pay_notify") {
			// jtb通知付款情况
			if ($status == "error") {
				$order->order_status_cd = "16";
			} else if ($status == "ok") {
				$order->order_status_cd = "01";
			}
			$pay_data = $post['pay_data'];	// string类型
			$order->pay_data = $pay_data;
			$order->save();

		} else if ($action == "cancel_notify") {
			// jtb通知预约取消
			if ($status == "ok") {
				$order->order_status_cd = "31";
			}
			$pay_data = $post['pay_data'];	// string类型
			$order->pay_data = $pay_data;
			$order->save();

		// 下面是engine内部状态变化
		} else if ($action == "reserve_confirm_failure") {
			// 信用卡付款登记成功后，tl-lincoln新规预约失败
			$order->order_status_cd = "14";
			$order->save();
		} else if ($action == "reserve_change_notify_failure") {
			// tl-lincoln预约变更失败后，通知jtb失败，尴尬了
			$order->order_status_cd = "23";
			$order->save();
		} else if ($action == "reserve_change_notify_success") {
			// tl-lincoln预约变更失败后，通知jtb成功
			$order->order_status_cd = "02";
			$order->save();
		} else if ($action == "reserve_cancel_failure") {
			// 取消tl-lincoln预约失败
			if ($order->order_status_cd == "16") {
				// jtb付款失败后，尴尬了
				$order->order_status_cd = "18";
			} else if ($order->order_status_cd == "31") {
				// jtb退款成功后，尴尬了
				$order->order_status_cd = "33";
			// 2021.12.25 jtb 增加错误处理 begin
			} else if ($order->order_status_cd == "41") {
				// talkappi自己检查出timetou后，尴尬了
				$order->order_status_cd = "43";
			// 2021.12.25 jtb 增加错误处理 end
			}
			$order->save();
		// 2021.12.25 jtb 增加错误处理 begin
		} else if ($action == "talkappi_time_out_check") {
			if ($order->order_status_cd == "15") {
				if ($status == "timeout") {
					$order->order_status_cd = "41";
					$pay_data = $post['pay_data'];	// string类型
					$order->pay_data = $pay_data;
					$order->save();
				}
			} else {
				// 出错了，不应该进到这里来
				$ret = array();
				$ret['order_id'] = $order->order_id;
				$ret['err'] = "1";
				return $ret;
			}
		// 2021.12.25 jtb 增加错误处理 end
		}

		$ret = array();
		$ret['order_id'] = $order->order_id;
		return $ret;
	}
	// 2021.08.21 #12594 end

	public function save_tl_reserve_confirm_data($post)
	{
		$order = ORM::factory('botorder', $post['order_id']);
		// 2021.08.21 #12594 begin
        if ($order->order_status_cd == "13") {
			// jtb登记成功，tl-lincoln新规预约成功
            $order->order_status_cd = "15";
		} else {
			$order->order_status_cd = "01";
		}
		// 2021.08.21 #12594 end
		$order->save();

		$orderhis = ORM::factory('botorderhis');
		$orderhis->order_id = $post['order_id'];
		$orderhis->no = $post['agent_notification_number'];
		$orderhis->order_type_div = 1;	// 预约登录
		$orderhis->link_id = $post['tl_reserve_id'];
		$orderhis->order_data = $post['tl_result'];

		if (isset($post["kessai_info"])) {
			$orderhis->pay_data = $post["kessai_info"];
		}

		$orderhis->save();

		$ret = array();
		$ret['no'] = $orderhis->no;
		return $ret;
	}

	public function save_tl_reserve_cancel_data($post)
	{
		$tl_cancel_reserve_id = "";
		if (array_key_exists("tl_cancel_reserve_id", $post)) {
			$tl_cancel_reserve_id = $post['tl_cancel_reserve_id'];
		}
		$order_data = "";
		if (array_key_exists("tl_result", $post)) {
			$tl_result = $post['tl_result'];
		}

		$order = ORM::factory('botorder', $post['order_id']);
		// 2021.08.21 #12594 begin
        if ($order->order_status_cd == "16") {
			// jtb付款失败，engine取消tl-lincoln成功
            $order->order_status_cd = "17";
        } else if ($order->order_status_cd == "31") {
			// jtb取消退款成功，engine取消tl-lincoln成功
			$order->order_status_cd = "03";
		// 2021.12.25 jtb 增加talkappi timeout 处理 begin
		} else if ($order->order_status_cd == "41") {
			// talkappi check timeout ，engine取消tl-lincoln成功
			$order->order_status_cd = "42";
		// 2021.12.25 jtb 增加talkappi timeout 处理 end
		} else {
			$order->order_status_cd = "03";
		}
		// 2021.08.21 #12594 end
		$order->cancel_date = date('Y-m-d H:i:s');
		$order->save();

		if ($post['tl_reserve_id'] != null) {
			$orderhis = ORM::factory('botorderhis');
			$orderhis->order_id = $post['order_id'];
			$orderhis->no = $post['agent_notification_number'];
			$orderhis->order_type_div = 3;	// 取消
			$orderhis->link_id = $post['tl_reserve_id'];
			$orderhis->cancel_link_id	 = $tl_cancel_reserve_id;
			$orderhis->order_data = $tl_result;

			// 2020.09.15 #5256
			if (isset($post["kessai_info"])) {
				$orderhis->pay_data = $post["kessai_info"];
			}
			// 2020.09.15 #5256 end
	
			$orderhis->save();
		}

		$ret = array();
		return $ret;
	}

	// 预约变更成功后，调用此函数修改预约信息
	public function save_tl_reserve_change_data($post)
	{
		$reserve_data = json_decode($post['reserve_data']);

		$order = ORM::factory('botorder', $post['order_id']);
		$order->bot_id = $post['bot_id'];
		$order->member_id = $post['member_id'];
		$order->lang_cd = $post['lang_cd'];
		$order->order_no = $post['agent_reserve_id'];
		if ($post['tl_reserve_id'] != null) {
			// 2021.08.09 #12594 新增21
            if ($order->order_status_cd == "20") {
				// jtb付款后，tl-lincoln预约变更成功
				$order->order_status_cd = "21";
            } else {
				$order->order_status_cd = "02";	// 2020.12.22 由01改为02 #7206
			}

			// 2021.07.23  #13647 预约变更时清一下remind 标志，因为需要重新remind
			$order->remind = 0;
		} else {
			$order->order_status_cd = "00";
		}
		//$order->link_type_cd = "tl";
	
		$order->checkin_date = $reserve_data->checkin_date;
		$order->checkout_date = $reserve_data->checkout_date;
		$order->adult_num = $reserve_data->adult_num;
		$order->child_num = $reserve_data->child_num;
	
		$order->checkin_name = $reserve_data->checkin_name;
		$order->checkin_name_kana =  $reserve_data->checkin_name_kana;
		$order->checkin_tel = $reserve_data->checkin_tel;
		$order->checkin_mail = $reserve_data->checkin_mail;
		$order->num = $reserve_data->num;
	

		// 2022.04.01 普通预约改为在这里存放历史数据 begin
		$payment_type_cd = "";
		if (array_key_exists("payment_type_cd", $reserve_data)) {
			$payment_type_cd = $reserve_data->payment_type_cd;
		}
		$card_type = "";
		if (array_key_exists("card_type", $reserve_data)) {
			$card_type = $reserve_data->card_type;
		}
		if ($payment_type_cd == "2" && $card_type == "jtb") {
		} else {
			// 2022.04.01 普通预约改为在这里存放历史数据 begin
			// 2021.09.24 改善 #16604 begin
			$temp_order_data = json_decode($order->order_data);
            if (array_key_exists("order_date", $temp_order_data)) {
            } else {
				// patch
				$temp_order_data->order_date = $order->order_date; 
			}

			// 记录变更履历
			if ($order->order_data_history == null || $order->order_data_history == "") {
				error_log("first change " . $order->order_id);
				$temp_order_data_history = json_decode("[]");
			} else {
				error_log("not first change " . $order->order_id);
				$temp_order_data_history = json_decode($order->order_data_history);
			}
			$temp_order_data_history[count($temp_order_data_history)] = $temp_order_data;
			$order->order_data_history = json_encode($temp_order_data_history, JSON_UNESCAPED_UNICODE);
			// 2021.09.24 改善 #16604 end
		}

		/*
		if (array_key_exists("dest_bot_id", $reserve_data)
			&&  $reserve_data->dest_bot_id !=$reserve_data->bot_id 
			&& $reserve_data->dest_bot_id != "") {
			$order->dest_bot_id =  $reserve_data->['dest_bot_id'];
		}
		*/
		$order->order_date = date('Y-m-d H:i:s');
		$reserve_data->order_date = $order->order_date;
		$order->order_data = json_encode($reserve_data, JSON_UNESCAPED_UNICODE);
		// 2022.04.01 $order->order_data = $post['reserve_data'];
		// 2022.04.01 普通预约改为在这里存放历史数据 end
		
		$order->save();

		if ($post['tl_reserve_id'] != null) {
			$orderhis = ORM::factory('botorderhis');
			$orderhis->order_id = $post['order_id'];
			$orderhis->no =  $post['agent_notification_number'];
			$orderhis->order_type_div = 2;	// 変更
			$orderhis->link_id = $post['tl_reserve_id'];
			$orderhis->order_data = $post['tl_result'];

			// 2020.09.15 #5256
			if (isset($post["kessai_info"])) {
				$orderhis->pay_data = $post["kessai_info"];
			}
			// 2020.09.15 #5256 end

			$orderhis->save();
		}

		$ret = array();

		// 2020.01.17 将电话号码和邮件地址存到t_bot_member里去
		// 2020.11.26 当privacy==1的时候才保存
		if (isset($reserve_data->privacy) && $reserve_data->privacy == 1) {
			// 2022.02.10 #21552 增加privacy
			$this->set_member_email_phone_name($post['bot_id'], $post['member_id'], $reserve_data->checkin_tel, $reserve_data->checkin_mail, $reserve_data->checkin_name, $reserve_data->name_kanji, 1);
		} else {
			// 2022.02.10 #21552 增加privacy
			$this->set_member_email_phone_name($post['bot_id'], $post['member_id'], "", "", "", "", 0);
		}

		return $ret;
	}

	// 给用户发送邮件后，修改t_bot_order的发送邮件标记
	public function update_t_bot_order_mail_sent($post)
	{
		$order_id = $post['order_id'];
		$type = $post['type'];
		$result = $post['result'];

		$order = ORM::factory('botorder', $post['order_id']);

		// 2021.07.23 #14647 mail_sent标志
		if ($result == 0) {
			// 发送邮件失败
			$order->mail_sent = -1;
		} else {
			$order->mail_sent = 1;
		}

		$order->save();
		$ret = array();
		return $ret;
	}

	// 2021.07.24 #13647
	// "00" 假预约
	// "01" 预约确定
	// "02" 预约变更
	// "03" 预约取消
	// "04" 预约确定后的假预约(之后变02)
	// "05" 预约变更后的假预约(之后变02)

	// 2021.08.20 #12594
	// "11" 假预约 & 待信用卡付款登记 (备注：乃中间状态)
	// "12" 假预约 & 信用卡付款登记失败 (备注：在用户情报入力画面显示给用户看)
	// "13" 假预约 & 信用卡付款登记成功 & 待预约确定 (备注：乃中间状态，接着做预约确定，预约确定失败变14，成功变15)
	// "14"         信用卡付款登记成功 & 预约确定失败 
	// "15"         信用卡付款登记成功 & 预约确定成功 & 待付款(备注：乃中间状态，之后付款成功变01，付款失败变16, talkappi自己检查出timeout变41,42）
	// "01"         信用卡付款登记成功 & 预约确定成功 & 付款成功
	// "16"         信用卡付款登记成功 & 预约确定成功 & 付款失败 & 待取消(备注：乃中间状态，之后做预约取消）
	// "17"         信用卡付款登记成功 & 预约确定成功 & 付款失败 & 取消成功
	// "18"         信用卡付款登记成功 & 预约确定成功 & 付款失败 & 取消失败     // ???尴尬了

	// "41"         talkappi自己检查出timeout，待取消(备注：乃中间状态，之后做预约取消）
	// "42"         talkappi自己检查出timeout，取消成功
	// "43"         talkappi自己检查出timeout，取消失败

	// "20" 信用卡付款后,预约变更的假预约 (备注：乃中间状态)
	// "21" 信用卡付款后,预约变更成功，待通知JTB (备注：乃中间状态)
	// "02" 信用卡付款后,预约变更成功，通知JTB成功
	// "23" 信用卡付款后,预约变更成功，通知JTB失败  // ???尴尬了

	// "31" 信用卡付款后，信用卡已退款 & 待tl-lincoln取消(取消后变03)
	// "03" 信用卡付款后，信用卡已退款 & tl-lincoln取消成功
	// "33" 信用卡付款后，信用卡已退款 & tl-lincoln取消失败    // ???尴尬了
	// 
	function t_bot_order_reserve_get_remind_list($condition)
	{
		/*
		AND b.setting_cd = 'json_reserve_settings'
		AND JSON_EXTRACT(b.setting_value, '$.remind_mail_checkin') IS NOT NULL
		AND JSON_EXTRACT(b.setting_value, '$.remind_mail_checkin.before_days') IS NOT NULL
		AND a.checkin_date = (select date_format(DATE_SUB(curtime(), interval -JSON_EXTRACT(b.setting_value, '$.remind_mail_checkin.before_days') DAY),'%Y-%m-%d'))
		*/

		$sql = "SELECT a.* 
				FROM 
					t_bot_order a
				INNER JOIN t_bot_setting b
					ON a.bot_id = b.bot_id
					AND b.setting_cd = 'reserve_remind'
					AND JSON_EXTRACT(b.setting_value, '$.before_days') IS NOT NULL
					AND JSON_EXTRACT(b.setting_value, '$.send_time') IS NOT NULL
					AND a.checkin_date = date_format(DATE_SUB(curtime(), interval -JSON_EXTRACT(b.setting_value, '$.before_days') DAY),'%Y-%m-%d')
					AND date_format(curtime(),'%H:%i') >=  JSON_EXTRACT(b.setting_value, '$.send_time')
				WHERE
					item_div = 5
					AND (order_status_cd = '01' OR order_status_cd = '02')
					AND remind = 0
				";
		// 用于测试的sql
		/*
		$sql = "SELECT * FROM t_bot_order
				WHERE
					item_div = 5
					AND (order_status_cd = '01' OR order_status_cd = '02')
					AND order_id = 1624
					AND remind = 0
				";
		*/
		try {
			$sql = "SELECT a.* 
				FROM 
					t_bot_order a
				INNER JOIN t_bot_setting b
					ON a.bot_id = b.bot_id
					AND b.setting_cd = 'reserve_remind'
					AND JSON_EXTRACT(b.setting_value, '$.before_days') IS NOT NULL
					AND JSON_EXTRACT(b.setting_value, '$.send_time') IS NOT NULL
					AND a.checkin_date = date_format(DATE_SUB(curtime(), interval -JSON_EXTRACT(b.setting_value, '$.before_days') DAY),'%Y-%m-%d')
					AND date_format(curtime(),'%H:%i') >=  JSON_EXTRACT(b.setting_value, '$.send_time')
				WHERE
					item_div = 5
					AND (order_status_cd = '01' OR order_status_cd = '02')
					AND remind = 0
				";
			$query = DB::query(Database::SELECT, $sql);
			$query->parameters(array(
					':next_date' => date('Y-m-d',strtotime("+1 days")),
			));
			$results = $query->execute();
			return $results->as_array();
		} catch (Exception $e) {
			// 因为reserve_remind的json内容可能是错误的
			error_log("apimodel.php t_bot_service_get_mail_send_list() exception." . $e->getMessage());
			return [];
		}
	}
	
	// 给用户发送remind邮件后，修改t_bot_order的remind发送邮件标记
	public function update_t_bot_order_remind($post)
	{
		$order_id = $post['order_id'];
		$result = $post['result'];

		$order = ORM::factory('botorder', $post['order_id']);

		// 2021.07.24 #13647 mail_sent标志
		if ($result == 0) {
			// 发送邮件失败
			$order->remind = $result;
		} else {
			$order->remind = $result;
		}

		$order->save();
		$ret = array();
		return $ret;
	}	

	// 2021.09.16 #16233 ユーザー誘導仕組み構築 begin
    function get_bot_induce($condition)
	{
		$bot_id = $condition['bot_id'];

		$sql = "SELECT *
			FROM t_bot_induce a
			WHERE
				a.bot_id = :bot_id
			";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':bot_id' => $bot_id,
		));
		$results = $query->execute()->as_array();;
        if (count($results) > 0) {
            return $results;
        }

		// 取父bot_id
		$parent_bot_id = $this->get_grp_parent_bot_id($bot_id);
        if ($parent_bot_id != -1 && $parent_bot_id != $bot_id) {
			$query = DB::query(Database::SELECT, $sql);
			$query->parameters(array(
				':bot_id' => $parent_bot_id,
			));
			$results = $query->execute()->as_array();
			return $results;
        }
		return  $results;
    }

    function get_t_bot_induce_result($condition)
    {
		$sql = "SELECT *
			FROM t_bot_induce_result a
			WHERE
				a.induce_id = :induce_id
				AND a.member_id = :member_id
			";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':induce_id' => $condition["induce_id"],
			':member_id' => $condition["member_id"],
		));
		$results = $query->execute()->as_array();;
		return $results;
    }

    function save_t_bot_induce_result($condition)
    {
		$sql = "INSERT INTO t_bot_induce_result
				(induce_id, member_id, result)
				VALUES
				(:induce_id, :member_id, :result) 
				";
		$query = DB::query(Database::UPDATE, $sql);
		$query->parameters(array(
			':induce_id' => $condition["induce_id"],
			':member_id' => $condition["member_id"],
			':result' => $condition["result"],
		));

		$query->execute();
		$results = 1;
		return $results;
    }
	// 2021.09.16 #16233 ユーザー誘導仕組み構築 end

	function save_t_bot_action_status($condition) {
		$sql = "INSERT INTO t_bot_action_status
				(bot_id, member_id, action, status, time, action_data)
				VALUES
				(:bot_id, :member_id, :action, :status, :time, :action_data) 
				";
		$query = DB::query(Database::UPDATE, $sql);
		$query->parameters(array(
			':bot_id' => $condition["bot_id"],
			':member_id' => $condition["member_id"],
			':action' => $condition["action"],
			':status' => $condition["status"],
			':time' => $condition["time"],
			':action_data' => $condition["action_data"],
		));

		$query->execute();
		$results = 1;
		return $results;

	}
	
	function t_bot_retargeting_get_list($condition)
	{
		$sql = "SELECT a.*, c.retarget_eclipse_time, c.message_cd, d.sns_type_cd, d.sns_id, d.lang_cd
				FROM t_bot_action_status a
				INNER JOIN 
				(SELECT bot_id,member_id,max(seq) AS max_seq 
					FROM `t_bot_action_status` 
					GROUP BY bot_id,member_id) b
					ON a.bot_id=b.bot_id
					AND a.member_id=b.member_id
					AND a.seq = b.max_seq
					AND a.sent_flg = 0
				INNER JOIN t_bot_retargeting c
					ON a.bot_id=c.bot_id
					AND a.action = c.action
					AND a.status = c.status
					AND c.message_cd IS NOT NULL 
					AND c.message_cd <> ''
				INNER JOIN t_bot_member d
					ON a.bot_id = d.bot_id
					AND a.member_id = d.member_id
					AND d.follow = 1
				WHERE 
					TIMESTAMPDIFF(SECOND,a.time,CURTIME()) > c.retarget_eclipse_time
				";
					// AND a.member_id='ca38a0f6-1129-1905-5ce2-687a34e8569f'
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
		));
		$results = $query->execute();
		return $results->as_array();
	}

	function t_bot_action_status_update($condition)
	{
		$sql = "UPDATE t_bot_action_status
				SET
					sent_flg = 1
				WHERE
					seq = :seq
				";
		$query = DB::query(Database::UPDATE, $sql);
		$query->parameters(array(
			':seq' => $condition["seq"],
		));

		$results = $query->execute();
		return $results;
	}

	function product_bus_search($condition) {
		$sql = "SELECT a.*,b.*
			FROM t_product_bus a
			INNER JOIN t_product_bus_info b
			ON a.route_id = b.route_id
			WHERE
				a.bot_id = :bot_id
				AND b.departure = :departure
				AND b.destination = :destination
			ORDER BY
			    b.step
			";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':bot_id' => $condition["bot_id"],
			':departure' => $condition["departure"],
			':destination' => $condition["destination"],
		));
		$results = $query->execute();
		return $results->as_array();
	}

	public function get_grp_parent_bot_id($bot_id) {
		if ($bot_id >= 201000 && $bot_id < 299999) {
			if ($bot_id % 1000 == 0) {
				return $bot_id;
			} else {
				return intval(intval($bot_id) / 1000) * 1000;
			}
		} else if ($bot_id >= 2001000 && $bot_id < 2999999) {
            if ($bot_id % 1000 == 0) {
                return $bot_id;
            } else {
                return intval($bot_id / 1000) * 1000;
            }
        } else {
			return -1;
		}
	}

	public function get_bot_id_list_include_template_bot($bot_id) {
		$results = $this->get_bot_setting($bot_id, "template_bot");
		if (count($results) > 0) {
			$base_bot_id = $results[0]["setting_value"];
			// 2021.09.02 有人在非父子bot里也设置了template_bot begin
			if ($base_bot_id == "") {
				return $bot_id;
			}
			// 2021.09.02 有人在非父子bot里也设置了template_bot end
			if ($base_bot_id == $bot_id) {
				return $bot_id;
			} else {
				return "$bot_id,$base_bot_id";
			}
		} else {
			return $bot_id;
		}
	}

	public function get_template_bot_id($bot_id) {
		$results = $this->get_bot_setting($bot_id, "template_bot");
		if (count($results) > 0) {
			$base_bot_id = $results[0]["setting_value"];
			return $base_bot_id;
		} else {
			return "";
		}
	}

	public function get_all_bot_id_list_for_group_parent_bot($bot_id) {
		$parent_bot_id = $this->get_grp_parent_bot_id($bot_id);
		if ($parent_bot_id != $bot_id) {
			// 不是group parent bot id
			return $bot_id;
		}

		$begin_bot_id = $parent_bot_id - 0;
		$end_bot_id = $begin_bot_id + 999;

		$sql = "SELECT group_concat(a.bot_id) AS bot_id_list
			FROM t_bot a
			WHERE
				a.bot_id BETWEEN $begin_bot_id AND $end_bot_id
			";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
		));
		$results = $query->execute()->as_array();

		if (count($results) > 0) {
			$bot_id_list = $results[0]["bot_id_list"];
			return $bot_id_list;
		} else {
			return $bot_id;
		}
	}

	public function get_all_bot_id_list_for_group_bot($bot_id) {
		$parent_bot_id = $this->get_grp_parent_bot_id($bot_id);
		if ($parent_bot_id == -1) {
			// 不是group bot id
			return $bot_id;
		}

		$begin_bot_id = $parent_bot_id - 0;
		$end_bot_id = $begin_bot_id + 999;

		$sql = "SELECT group_concat(a.bot_id) AS bot_id_list
			FROM t_bot a
			WHERE
				a.bot_id BETWEEN $begin_bot_id AND $end_bot_id
			";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
		));
		$results = $query->execute()->as_array();

		if (count($results) > 0) {
			$bot_id_list = $results[0]["bot_id_list"];
			return $bot_id_list;
		} else {
			return $bot_id;
		}
	}

	public function get_bot_setting($bot_id, $setting_cd) {
		$sql = "SELECT a.*
			FROM t_bot_setting a
			WHERE
				a.bot_id = :bot_id
				AND a.setting_cd = :setting_cd
			";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':bot_id' => $bot_id,
			':setting_cd' => $setting_cd,
		));
		$results = $query->execute();
		return $results->as_array();
	}

	public function get_bot_division_data($condition) {
		$sql = "SELECT a.*
			FROM t_division_contact a
			WHERE
				a.bot_id = :bot_id
				and a.division_id = :bot_division_id
				AND a.lang_cd = :lang_cd
			";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':bot_id' => $condition["bot_id"],
			':bot_division_id' => $condition["bot_division_id"],
			':lang_cd' => $condition["lang_cd"],
		));
		$results = $query->execute();
		return $results->as_array();
	}

	public function get_task($condition) {
		$sql = "SELECT a.*,b.*
			FROM t_task a
			INNER JOIN t_task_dtl b
			ON a.task_id = b.task_id
			";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
		));
		$results = $query->execute();
		return $results->as_array();
	}

	public function get_follow_times($condition) {
		$sql = "SELECT count(*) as follow_times
			FROM t_bot_follow a
			WHERE
				member_id = :member_id
				AND bot_id = :bot_id
				AND sns_id not in ('faq01','inquiry01','survey01')
			";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':bot_id' => $condition["bot_id"],
			':member_id' => $condition["member_id"],
		));
		$results = $query->execute();
		return $results->as_array();
	}

	// menu_group.js 为venus特别制作的功能
	function get_floor_facility_menu($condition)
	{
		$bot_id = $condition["bot_id"];
		$lang_cd = $condition["lang_cd"];
		$menu_group_id = $condition["menu_group_id"];
		$item_div = $condition["item_div"];
		$class_cd = $condition["class_cd"];
        // 2020.10.12 #6073
		$sql_status_cd_end_date = 
			" AND (a.end_date is NULL OR DATE_FORMAT(a.end_date,'%Y-%m-%d') >= :cur_date)
			AND a.item_status_cd in ('01','03','04','06','07','08')
			";

		$sql_num = "SELECT COUNT(DISTINCT a.item_id)
				FROM t_item a
				INNER JOIN t_item_description b
				ON a.item_id = b.item_id AND b.lang_cd = :lang_cd
				INNER JOIN t_item_display r
						ON a.item_id = r.item_id
						AND a.item_div = r.item_div
						AND find_in_set(:lang_cd, r.lang_display)
						AND r.public_flg = 1
				WHERE
					r.bot_id = :bot_id 
				AND a.item_div = :item_div
				AND a.delete_flg <> 1
				AND (a.start_date is NULL OR DATE_FORMAT(a.start_date,'%Y-%m-%d') <= :cur_date)
				$sql_status_cd_end_date
				AND (a.regular_start = :regular_date_kara 
					OR a.regular_end = :regular_date_kara 
					OR (a.regular_start <= a.regular_end AND a.regular_start <= :regular_date AND :regular_date <= a.regular_end)
					OR (a.regular_start > a.regular_end AND (a.regular_start <= :regular_date OR :regular_date <= a.regular_end))
					)
				AND concat(' ', a.class_cd) like concat('% ', :class_cd, '%')
				AND a.area_cd = SUBSTRING(n.content, 1, 2)
				";
		$sql_num = "($sql_num) AS content_num";

		// 取当前bot的数据
		$sql = "SELECT * 
				FROM (
					SELECT 
						m.bot_id AS bot_id,
						m.msg_cd AS menu_group_id,
						n.lang_cd AS lang_cd,
						n.title AS menu_title,
						n.content AS menu_sub_group_text,
						n.url AS menu_sub_group_cmd,
						3 AS display_mode,
						n.no,
						$sql_num
					FROM t_bot_msg m
					INNER JOIN t_bot_msg_desc_lst n
						ON m.msg_id = n.msg_id
					WHERE
						m.msg_cd = :menu_group_id
						AND n.lang_cd = :lang_cd
						AND n.delete_flg = 0
						AND n.delete_flg = 0
						AND m.bot_id = :t_bot_msg_bot_id
				) k
				WHERE 
					k.content_num > 0
				ORDER BY
					no
				";

		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':bot_id' => $bot_id,
			':t_bot_msg_bot_id' => $bot_id,
			':menu_group_id' => $menu_group_id,
			':lang_cd' => $lang_cd,
			':class_cd' => $class_cd,
			':item_div' => $item_div,
			':cur_date' => date('Y-m-d'),
			':regular_date' => date('m-d'),
			':regular_date_kara' => "",
		));

		$results = $query->execute()->as_array();

		if (count($results) > 0) {
			return $results;
		}

		// 没取到数据，试图查看t_bot_setting看是否有basebot
		$base_bot_id = 0;
		$results = $this->get_bot_setting($bot_id, "template_bot");
		if (count($results) > 0) {
			$base_bot_id = $results[0]["setting_value"];

			$query = DB::query(Database::SELECT, $sql);
			$query->parameters(array(
				':bot_id' => $bot_id,
				':t_bot_msg_bot_id' => $base_bot_id,
	
				':menu_group_id' => $menu_group_id,
				':lang_cd' => $lang_cd,
				':class_cd' => $class_cd,
				':item_div' => $item_div,
				':cur_date' => date('Y-m-d'),
				':regular_date' => date('m-d'),
				':regular_date_kara' => "",
			));
	
			$results = $query->execute()->as_array();

			if (count($results) > 0) {
				return $results;
			}
		}

		// 取共通设定(bot_id=0)
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':bot_id' => $bot_id,
			':t_bot_msg_bot_id' => "0",
			':menu_group_id' => $menu_group_id,
			':lang_cd' => $lang_cd,
			':class_cd' => $class_cd,
			':item_div' => $item_div,
			':cur_date' => date('Y-m-d'),
			':regular_date' => date('m-d'),
			':regular_date_kara' => "",
		));
		
		$results = $query->execute()->as_array();
		return $results;
	}		

	public function read_t_task($condition) {
		$sql = "SELECT *
			FROM t_task a
			WHERE
				task_status_cd = '01'
				AND scheduled_time <= :cur_time
				AND process_time is NULL
				AND (task_type_cd = '01' OR task_type_cd = '02')
			";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':cur_time' => date('Y-m-d H:i:s')
		));
		$results = $query->execute();
		return $results->as_array();
	}

	public function update_t_task_process_time($condition) {
		$utimestamp = microtime(true);
		$timestamp = floor($utimestamp);
		$milliseconds = round(($utimestamp - $timestamp) * 1000000);

		$sql = "UPDATE t_task
				SET
					process_time = :cur_time,
					task_status_cd = '02'
				WHERE
					task_id = :task_id
				";
		$query = DB::query(Database::UPDATE, $sql);
		$cur_time = date('Y-m-d H:i:s.') . $milliseconds;

		$query->parameters(array(
			':task_id' => $condition["task_id"],
			':cur_time' => $cur_time,
		));

		$results = $query->execute();
		return $results;
	}

	public function update_t_task_task_status_cd($condition) {
		$utimestamp = microtime(true);
		$timestamp = floor($utimestamp);
		$milliseconds = round(($utimestamp - $timestamp) * 1000000);

		$sql = "UPDATE t_task
				SET
					finish_time = :cur_time,
					task_status_cd = :task_status_cd
				WHERE
					task_id = :task_id
				";
		$query = DB::query(Database::UPDATE, $sql);
		$cur_time = date('Y-m-d H:i:s.') . $milliseconds;

		$query->parameters(array(
			':task_id' => $condition["task_id"],
			':task_status_cd' => $condition["task_status_cd"],
			':cur_time' => $cur_time,
		));

		$results = $query->execute();
		return $results;
	}

	public function save_t_task_log($condition) {
		$sql = "INSERT INTO t_task_log
				(task_id, log_level, log_content)
				VALUES
				(:task_id, :log_level, :log_content) 
				";
		$query = DB::query(Database::UPDATE, $sql);
		$query->parameters(array(
			':task_id' => $condition["task_id"],
			':log_level' => $condition["log_level"],
			':log_content' => $condition["log_content"],
		));

		$query->execute();
		$results = 1;
		return $results;
	}

	// 2020.12.21 add 
	public function save_t_task_push_result($condition) {
		$sql = "SELECT member_id 
				FROM t_task_push_result
				WHERE 
				   task_id = :task_id
				   AND member_id = :member_id
				";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':task_id' => $condition["task_id"],
			':member_id' => $condition["member_id"],
		));
		$results = $query->execute()->as_array();
		if (count($results) > 0) {
			$sql = "UPDATE t_task_push_result
					SET bot_id=:bot_id, process_time=:process_time, process_result=:process_result
					WHERE 
						task_id = :task_id
						AND member_id = :member_id
					";
			$query = DB::query(Database::UPDATE, $sql);
			$query->parameters(array(
				':task_id' => $condition["task_id"],
				':member_id' => $condition["member_id"],
				':bot_id' => $condition["bot_id"],
				':process_result' => $condition["process_result"],
				':process_time' => date('Y-m-d H:i:s'),
			));

			$query->execute();
			$results = 1;
			return $results;
		} else {
			$sql = "INSERT INTO t_task_push_result
					(task_id, member_id, bot_id, process_result)
					VALUES
					(:task_id, :member_id, :bot_id, :process_result) 
					";
			$query = DB::query(Database::UPDATE, $sql);
			$query->parameters(array(
				':task_id' => $condition["task_id"],
				':member_id' => $condition["member_id"],
				':bot_id' => $condition["bot_id"],
				':process_result' => $condition["process_result"],
			));

			$query->execute();
			$results = 1;
			return $results;
		}
	}

	// 2020.12.21 add 
	public function get_t_task_push_result($condition) {
		$sql = "SELECT member_id 
				FROM t_task_push_result
				WHERE 
				   task_id = :task_id
				   AND (process_result = 0 OR process_result = 1)
				";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':task_id' => $condition["task_id"],
		));

		$results = $query->execute();
		return $results->as_array();
	}

	function get_member_list_for_task_by_member_ids($condition)
	{
		$member_id = $condition["member_id"];
		$bot_id = $condition["bot_id"];

		// 2021.08.26 add bot_id_list
		$bot_id_list = "";
        if (isset($condition["bot_id_list"])) {
			$bot_id_list = $condition["bot_id_list"];
        }

		if ($bot_id_list != "") {
			$sql_bot_id = " AND bot_id IN ($bot_id_list) ";
		} else {
        	// 2020.12.21 #batch送信当bot_id为父bot_id时所有父子bot都送，当bot_id为子bot时，只送子bot
            $parent_bot_id = $this->get_grp_parent_bot_id($bot_id);
            if ($parent_bot_id != -1 && $parent_bot_id == $bot_id) {
                $begin = $parent_bot_id;
                $end = $parent_bot_id - 0 + 999;
                $sql_bot_id = " AND bot_id BETWEEN $begin AND $end ";
            } else {
                $sql_bot_id = " AND bot_id = :bot_id ";
            }
            // 2020.12.21 #batch送信 end
        }

		$sql = "SELECT * FROM t_bot_member
				WHERE
					member_id in ($member_id)
					$sql_bot_id
					AND follow = 1
				";
		$sql = "$sql ORDER BY last_talk_date ASC";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':member_id' => $member_id,
				':bot_id' => $bot_id,
		));
		$results = $query->execute();
		return $results->as_array();
	}

	// 2023.06.28 #47553 增加t_member_rc的关联检索
	public function get_member_list_for_task($condition) {
		$bot_id = $condition["bot_id"];
		$sns_type_cd = $condition["sns_type_cd"];
		$lang_cd = $condition["lang_cd"];
		$regist_date_from = $condition["regist_date_from"];
		$regist_date_to = $condition["regist_date_to"];
		$last_talk_time_from = $condition["last_talk_time_from"];
		$last_talk_time_to = $condition["last_talk_time_to"];
		// 2021.08.26 add bot_id_list
		$bot_id_list = "";
        if (isset($condition["bot_id_list"])) {
			$bot_id_list = $condition["bot_id_list"];
        }
		// 2022.04.04 #24090 begin
		$user_attr_cd = $condition["user_attr_cd"];
		$user_attr_cond = $condition["user_attr_cond"];
		// 2022.04.04 #24090 end

		// 2022.06.24 #27943 begin
		$cond_scene_cd = "";
        if (isset($condition["cond_scene_cd"])) {
			$cond_scene_cd = $condition["cond_scene_cd"];
        }
		// 2022.06.24 #27943 end

		if ($bot_id_list != "") {
			$sql_bot_id = " AND a.bot_id IN ($bot_id_list) ";
		} else {
			// 2020.12.21 #batch送信当bot_id为父bot_id时所有父子bot都送，当bot_id为子bot时，只送子bot
			$parent_bot_id = $this->get_grp_parent_bot_id($bot_id);
			if ($parent_bot_id != -1 && $parent_bot_id == $bot_id) {
				$begin = $parent_bot_id;
				$end = $parent_bot_id - 0 + 999;
				$sql_bot_id = " AND a.bot_id BETWEEN $begin AND $end ";
			} else {
				$sql_bot_id = " AND a.bot_id = :bot_id ";
			}
			// 2020.12.21 #batch送信 end
		}

		$sql = "SELECT 
				a.member_id,
				a.sns_type_cd,
				a.sns_id,
				a.lang_cd,
				a.bot_id,
				a.is_tester,
				b.region,
				b.prefecture,
				b.city,
				b.village
			FROM t_bot_member a
			LEFT JOIN t_member_rc b
				ON a.bot_id = b.bot_id
				AND a.member_id= b.member_id
			WHERE
			    1 = 1
				$sql_bot_id
				AND a.follow = 1
			";
		if ($sns_type_cd != "") {
			$sql = "$sql AND find_in_set(sns_type_cd,:sns_type_cd)";
		}
		if ($lang_cd != "") {
			$sql = "$sql AND find_in_set(lang_cd,:lang_cd)";
		}
		if ($regist_date_from != "") {
			$sql = "$sql AND regist_date >= :regist_date_from";
		}
		if ($regist_date_to != "") {
			$sql = "$sql AND regist_date <= :regist_date_to";
		}
		if ($last_talk_time_from != "") {
			$sql = "$sql AND last_talk_date >= :last_talk_time_from";
		}
		if ($last_talk_time_to != "") {
			$sql = "$sql AND last_talk_date <= :last_talk_time_to";
		}
		// 2022.04.04 #24090 begin
		if ($user_attr_cd != "") {
			$array_user_attr_cd = explode(",", $user_attr_cd);
			$num_user_attr_cd  = count($array_user_attr_cd);
			if ($user_attr_cond == "OR") {
				$sql = "$sql AND EXISTS (
					SELECT 
					  tma.attr
					FROM
					  t_member_attr tma
					WHERE
					  tma.member_id = a.member_id
					  AND find_in_set(tma.attr,'$user_attr_cd') > 0
					)
				";
			} else if ($user_attr_cond == "AND") {
				$sql = "$sql AND (
					SELECT 
						COUNT(1)
					FROM
						t_member_attr tma
					WHERE
						tma.member_id = a.member_id
						AND find_in_set(tma.attr,'$user_attr_cd') > 0
				) = $num_user_attr_cd ";
			}
		}
		// 2022.04.04 #24090 end
		// 2022.06.24 #27943 begin
        if ($cond_scene_cd != "") {
			$sql = "$sql AND EXISTS (
				SELECT 
					tbf.scene
				FROM
				  t_bot_follow tbf
				WHERE
				  tbf.member_id = a.member_id
				  AND tbf.bot_id = a.bot_id
				  AND tbf.scene like :cond_scene_cd
				  AND tbf.sns_id not in ('faq01','inquiry01','survey01')
				)
			";
		}
		// 2022.06.24 #27943 end
		$sql = "$sql ORDER BY a.last_talk_date ASC";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':bot_id' => $bot_id,
			':sns_type_cd' => $sns_type_cd,
			':lang_cd' => $lang_cd,
			':regist_date_from' => $regist_date_from,
			':regist_date_to' => $regist_date_to,
			':last_talk_time_from' => $last_talk_time_from,
			':last_talk_time_to' => $last_talk_time_to,
			':cond_scene_cd' => $cond_scene_cd,
		));
		$results = $query->execute();
		return $results->as_array();
	}

	// 2021.01.02 #7482
	public function save_t_bot_memeber_room($condition) {
		$bot_id = $condition["bot_id"];
		$member_id = $condition["member_id"];
		$room = $condition["room"];

		$sql = "SELECT seq 
				FROM t_bot_member_room
				WHERE 
					bot_id = :bot_id
					AND member_id = :member_id
				ORDER BY
					seq DESC
				";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':bot_id' => $condition["bot_id"],
			':member_id' => $condition["member_id"],
		));
		$results = $query->execute()->as_array();
		if (count($results) > 0) {
			$latest_seq = $results[0]["seq"] + 1;
		} else {
			$latest_seq = 1;
		}
		$sql = "INSERT INTO t_bot_member_room
					(bot_id, member_id, seq, room)
				VALUES
					(:bot_id, :member_id, :latest_seq, :room) 
					";
		$query = DB::query(Database::UPDATE, $sql);
		$query->parameters(array(
			':bot_id' => $bot_id,
			':member_id' => $member_id,
			':latest_seq' => $latest_seq,
			':room' => $room,
		));

		$query->execute();
		$results = 1;
		return $results;
	}

	public function read_t_bot_memeber_room($condition) {
		$bot_id = $condition["bot_id"];
		$member_id = $condition["member_id"];

		$sql = "SELECT *
			FROM t_bot_member_room a
			WHERE
			    1 = 1
				AND bot_id = :bot_id
				AND member_id = :member_id
			ORDER BY
				seq DESC
			";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':bot_id' => $bot_id,
			':member_id' => $member_id,
		));
		$results = $query->execute();
		return $results->as_array();
	}
	// 2021.01.02 #7482 end

	function read_t_survey($condition)
	{
		$bot_id = $condition["bot_id"];
		$lang_cd = $condition["lang_cd"];
		$survey_cds = $condition["survey_cds"];

		$sql_survey_cds = "";
		if ($survey_cds != "") {
			$survey_cds_temp = str_replace(",",'","',$survey_cds);
			$survey_cds_temp = "\"$survey_cds_temp\"";
			$sql_survey_cds = " AND a.survey_cd in ($survey_cds_temp) ";
		}

		// 2021.08.13 子bot继承父bot的survey
		$parent_bot_id = $this->get_grp_parent_bot_id($bot_id);
		if ($parent_bot_id == -1) {
			$parent_bot_id = $bot_id;
		}
		if ($bot_id == $parent_bot_id) {
			// 为父bot或者为无父子bot
			$sql_bot_id = "a.bot_id = :bot_id";
		} else {
			// 当前为子bot，就同时检索父bot，并且当父bot和子BOT里都有记录时过滤掉父bot的记录
			$sql_bot_id = "	(a.bot_id = :bot_id or a.bot_id = :parent_bot_id) ";
		}

		$sql = "SELECT a.survey_cd, a.start_date, a.end_date,
					b.*
				FROM t_survey a
				INNER JOIN t_survey_description b
				ON a.survey_id = b.survey_id AND b.lang_cd = :lang_cd
				WHERE $sql_bot_id 
					AND a.delete_flg = 0
					AND b.delete_flg = 0
					$sql_survey_cds
					AND (a.start_date is NULL OR DATE_FORMAT(a.start_date,'%Y-%m-%d') <= :cur_date)
					AND (a.end_date is NULL OR DATE_FORMAT(a.end_date,'%Y-%m-%d') >= :cur_date)
				";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
				':parent_bot_id' => $parent_bot_id,
				':lang_cd' => $lang_cd,
				':cur_date' => date('Y-m-d'),
			));
		$results = $query->execute();
		return $results->as_array();
	}

	// 2021.01.22 #7876
	function read_coupon_info($condition) {
		$bot_id = $condition['bot_id'];
		$coupon_id = $condition['coupon_id'];
		$lang_cd = $condition['lang_cd'];

		$parent_bot_id = $this->get_grp_parent_bot_id($bot_id);
		if ($parent_bot_id == -1) {
			// 无父子bot			
			$parent_bot_id = $bot_id;
		}
		if ($bot_id == $parent_bot_id) {
			// 为父bot或者为无父子bot
			$sql_bot_id = "a.bot_id = :bot_id";
		} else {
			// 当前为子bot，就同时检索父bot，并且当父bot和子BOT里都有记录时过滤掉父bot的记录
			$sql_bot_id = "	(a.bot_id = :bot_id or a.bot_id = :parent_bot_id) ";
		}

		$sql = "SELECT a.bot_id, a.product_data,a.item_div,a.class_cd,
				b.*,
				c.sort_no1,c.sort_no2,c.sort_no3,c.sort_no4,c.sort_no5
			FROM t_product a
			INNER JOIN t_product_description b
			ON a.product_id = b.product_id
			LEFT JOIN t_item_display c
			ON a.bot_id = c.bot_id
			AND a.product_id = c.item_id
			AND c.item_div = 7
			WHERE
				$sql_bot_id
				AND a.item_div = 7
				AND a.delete_flg = 0
				AND b.delete_flg = 0
				AND a.product_id = :coupon_id
				AND b.lang_cd = :lang_cd
				AND (c.public_flg = 1 OR c.public_flg IS NULL)
				AND (c.lang_display IS NULL OR trim(c.lang_display) = '' OR find_in_set(:lang_cd, c.lang_display))
				AND (a.start_date is NULL OR DATE_FORMAT(a.start_date,'%Y-%m-%d') <= :cur_date)
				AND (a.end_date is NULL OR DATE_FORMAT(a.end_date,'%Y-%m-%d') >= :cur_date)
				AND (
					a.regular_start is NULL
					OR a.regular_end is NULL
					OR a.regular_start = :regular_date_kara 
					OR a.regular_end = :regular_date_kara 
					OR (a.regular_start <= a.regular_end AND a.regular_start <= :regular_date AND :regular_date <= a.regular_end)
					OR (a.regular_start > a.regular_end AND (a.regular_start <= :regular_date OR :regular_date <= a.regular_end))
				)
			ORDER BY
				c.sort_no1,
				c.sort_no2,
				c.sort_no3,
				c.sort_no4,
				c.sort_no5,
				b.product_id
			";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':bot_id' => $bot_id,
			':parent_bot_id' => $parent_bot_id,
			':coupon_id' => $coupon_id,
			':lang_cd' => $lang_cd,
			':cur_date' => date('Y-m-d'),
			':regular_date' => date('m-d'),
			':regular_date_kara' => "",
		));
		$results = $query->execute();
		return $results->as_array();
	}

	public function createInvitecode()
	{
		// 生成字母和数字组成的6位字符串
		$str = range('A', 'Z');
		// 去除大写的O，以防止与0混淆 
		unset($str[array_search('O', $str)]);
		$arr = array_merge(range(0, 9), $str);
		shuffle($arr);
		$invitecode = '';
		$arr_len = count($arr);

		for ($i = 0; $i < 6; $i++) {
			$rand = mt_rand(0, $arr_len - 1);
			$invitecode .= $arr[$rand];
		}
		return $invitecode;
	}

	function create_user_coupon($condition) {
		$bot_id = $condition['bot_id'];
		$member_id = $condition['member_id'];
		$coupon_id = $condition['coupon_id'];
		$lang_cd = $condition['lang_cd'];
		error_log("Comming create_user_coupon() bod_id=$bot_id coupon_id=$coupon_id member_id=$member_id lang_cd=$lang_cd");

		// 读取coupon信息
		$results_product = $this->read_coupon_info($condition);
		if (count($results_product) == 0) {
			// 没有coupon信息，直接返回
			error_log("DB里没有指定coupon信息，返回-1。");
			$ret = array();
			$ret['result'] = -1;
			return $ret;
		}

		// 先检查t_bot_order表是否已经生成过了coupon记录
		$sql = "SELECT * 
			FROM t_bot_order a
			WHERE
				a.bot_id = :bot_id
				AND a.item_div = 7
				AND a.product_id = :coupon_id
				AND a.member_id = :member_id
			";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':bot_id' => $results_product[0]["bot_id"],
			':coupon_id' => $coupon_id,
			':lang_cd' => $lang_cd,
			':member_id' => $member_id,
		));
		$results = $query->execute()->as_array();
		if (count($results) > 0) {
			// 已经生成过了，直接返回
			error_log("already create coupon record,返回-3");
			$ret = array();
			$ret['result'] = -3;
			$ret['coupon_info'] = $results;
			return $ret;
		}
		
		// 2021.03.07 检查使用数目是否已经到达上限
		// 检查可用次数
		$product_data_obj = json_decode($results_product[0]["product_data"]);
		$stock_type = $product_data_obj->stock_type;
		if ($stock_type == 1) {
			// 无限回数
			error_log("无限使用回数");
		} else if ($stock_type == 2) {
			// 限定全体用户使用回数
			$stock = $product_data_obj->stock;
			error_log("限定全体用户使用回数=$stock");
			$sql2 = "SELECT sum(num) AS num_sum
				FROM t_bot_order a
				WHERE
					a.bot_id = :bot_id
					AND a.item_div = 7
					AND a.product_id = :coupon_id
				";
			$query2 = DB::query(Database::SELECT, $sql2);
			$query2->parameters(array(
				':bot_id' => $results_product[0]["bot_id"],
				':coupon_id' => $coupon_id,
			));
			$results2 = $query2->execute()->as_array();
			if (count($results2) == 0) {
				$total_use_num = 0;
			} else {
				$total_use_num = $results2[0]["num_sum"];
			}
			error_log("全体用户已使用回数". $total_use_num);
			if ($total_use_num >= $stock) {
				// 超额，直接返回
				error_log("全体用户已使用回数超过了限定全体用户使用回数,返回-2");
				$ret = array();
				$ret['result'] = -2;
				return $ret;
			}
		} else if ($stock_type == 3) {
			// 2021.12.10 #19154 增加发行回数上限

			// 限定发行回数，取得已发行回数
			$stock = $product_data_obj->stock;
			$sql2 = "SELECT * 
				FROM t_bot_order a
				WHERE
					a.bot_id = :bot_id
					AND a.item_div = 7
					AND a.product_id = :coupon_id
				";
			$query2 = DB::query(Database::SELECT, $sql2);
			$query2->parameters(array(
				':bot_id' => $results_product[0]["bot_id"],
				':coupon_id' => $coupon_id,
				':lang_cd' => $lang_cd,
				':member_id' => $member_id,
			));
			$results2 = $query2->execute()->as_array();
			$issued_num = count($results2);
			if ($issued_num >= $stock) {
				// 超过了发行回数
				error_log("当前发行回数=$issued_num,超过了发行回数 $stock ，返回-4");
				$ret = array();
				$ret['result'] = -4;
				return $ret;
			}
		}	

		// 还没生成，就生成一条
		error_log("begin create coupon record");
		$order = ORM::factory('botorder');
		$order->item_div = 7;
		$order->order_no = "";
		$order->order_status_cd = "00";
		$order->bot_id =  $results_product[0]["bot_id"];
		$order->member_id = $member_id;
		$order->lang_cd = $lang_cd;
		$order->product_id = $coupon_id;
		$order->num = 0;
		$order->link_id = $this->createInvitecode();
		$order->save();
			
		// 再次检索
		$results = $query->execute()->as_array();
		return $results;
	}

	function check_coupon_and_lock($condition)
	{
		$bot_id = $condition['bot_id'];
		$member_id = $condition['member_id'];
		$coupon_id = $condition['coupon_id'];
		$lang_cd = $condition['lang_cd'];
		error_log("Comming check_coupon_and_lock() bod_id=$bot_id coupon_id=$coupon_id member_id=$member_id lang_cd=$lang_cd");

		// 读取coupon信息
		$results_product = $this->read_coupon_info($condition);
		if (count($results_product) == 0) {
			// 没有coupon信息，直接返回
			error_log("DB里没有指定coupon信息，返回-1。");
			$ret = array();
			$ret['result'] = -1;
			return $ret;
		}

		// 先检查t_bot_order表是否为该用户已经生成过了coupon记录
		$sql = "SELECT * 
			FROM t_bot_order a
			WHERE
				a.bot_id = :bot_id
				AND a.item_div = 7
				AND a.product_id = :coupon_id
				AND a.member_id = :member_id
			";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':bot_id' => $results_product[0]["bot_id"],
			':coupon_id' => $coupon_id,
			':lang_cd' => $lang_cd,
			':member_id' => $member_id,
		));
		$results_order = $query->execute()->as_array();
		if (count($results_order) == 0) {
			// 还没生成过，直接返回
			error_log("t_bot_order里没有为该用户发行该coupon信息，返回-2。");
			$ret = array();
			$ret['result'] = -2;
			return $ret;
		}

		// 检查可用次数
		$product_data_obj = json_decode($results_product[0]["product_data"]);
		{
			$stock_type = $product_data_obj->stock_type;
			if ($stock_type == 2) {
				// 限定全体用户使用回数
				$stock = $product_data_obj->stock;
				error_log("限定全体用户使用回数=$stock");
				$sql2 = "SELECT sum(num) AS num_sum
					FROM t_bot_order a
					WHERE
						a.bot_id = :bot_id
						AND a.item_div = 7
						AND a.product_id = :coupon_id
					";
				$query2 = DB::query(Database::SELECT, $sql2);
				$query2->parameters(array(
					':bot_id' => $results_product[0]["bot_id"],
					':coupon_id' => $coupon_id,
				));
				$results2 = $query2->execute()->as_array();
				if (count($results2) == 0) {
					$total_use_num = 0;
				} else {
					$total_use_num = $results2[0]["num_sum"];
				}
				error_log("全体用户已使用回数". $total_use_num);
				if ($total_use_num >= $stock) {
					// 超额，直接返回
					error_log("全体用户已使用回数超过了限定全体用户使用回数,返回-3");
					$ret = array();
					$ret['result'] = -3;
					return $ret;
				}		
			}	
		}

		$stock_type_member = $product_data_obj->stock_type_member;
		if ($stock_type_member == 1) {
			// 无限回数
			error_log("无限回数");
		/* 2021.03.07 去掉了这个所有用户总回数功能
		} else if ($stock_type_member == 2) {
			//总回数
			$stock = $product_data_obj->stock;
			error_log("所有用户的总回数". $stock);

			$sql = "SELECT sum(num) AS num_sum
				FROM t_bot_order a
				WHERE
					a.bot_id = :bot_id
					AND a.item_div = 7
					AND a.product_id = :coupon_id
				";
			$query = DB::query(Database::SELECT, $sql);
			$query->parameters(array(
				':bot_id' => $results_product[0]["bot_id"],
				':coupon_id' => $coupon_id,
				':lang_cd' => $lang_cd,
				':member_id' => $member_id,
			));
			$results = $query->execute()->as_array();
			if (count($results) == 0) {
				// 还没生成过，直接返回
				$ret = array();
				$ret['result'] = -3;
				return $ret;
			}
			$num = $results[0]["num_sum"];
			error_log("当前使用回数". $num);
			if ($num >= $stock) {
				// 超额，直接返回
				$ret = array();
				$ret['result'] = -4;
				return $ret;
			}
		*/
		} else if ($stock_type_member == 2) {
			// 人回数
			$stock_member = $product_data_obj->stock_member;
			error_log("限定人回数". $stock_member);
			$sql = "SELECT sum(num)  AS num_sum
				FROM t_bot_order a
				WHERE
					a.bot_id = :bot_id
					AND a.item_div = 7
					AND a.product_id = :coupon_id
					AND a.member_id = :member_id
				";
			$query = DB::query(Database::SELECT, $sql);
			$query->parameters(array(
				':bot_id' => $results_product[0]["bot_id"],
				':coupon_id' => $coupon_id,
				':lang_cd' => $lang_cd,
				':member_id' => $member_id,
			));
			$results = $query->execute()->as_array();
			if (count($results) == 0) {
				// 还没生成过，直接返回
				error_log("没有为该用户发行过该coupon,返回-5");
				$ret = array();
				$ret['result'] = -5;
				return $ret;
			}
			$num = $results[0]["num_sum"];
			error_log("该用户该coupon当前已使用回数". $num);
			if ($num >= $stock_member) {
				error_log("当前已使用回数超过了限定人回数，返回-6");
				// 超额，直接返回
				$ret = array();
				$ret['result'] = -6;
				return $ret;
			}
		} else {
				// 不认识的类型，直接返回
				error_log("无法识别的类型 stock_type_member=$stock_type_member,返回-7");
				$ret = array();
				$ret['result'] = -7;
				return $ret;
		}

		// lock
		$order_id = $results_order[0]["order_id"];
		$num = $results_order[0]["num"] + 1;
		$use_date = date('Y-m-d H:i:s');
		error_log("修改该用户该coupon使用次数为". $num);

		$sql = "UPDATE t_bot_order
				SET
					use_date = :use_date,
					num = :num
				WHERE
					order_id = :order_id
					AND member_id = :member_id
				";
		$query = DB::query(Database::UPDATE, $sql);
		$query->parameters(array(
			':order_id' => $order_id,
			':use_date' => $use_date,
			':num' => $num,
			':member_id' => $member_id,
		));

		$results = $query->execute();
		$ret = array();
		$ret['result'] = 1;
		return $ret;
	}

	function coupon_unlock($condition)
	{
		$bot_id = $condition['bot_id'];
		$member_id = $condition['member_id'];
		$coupon_id = $condition['coupon_id'];
		$lang_cd = $condition['lang_cd'];
		error_log("Comming coupon_unlock() bod_id=$bot_id coupon_id=$coupon_id member_id=$member_id lang_cd=$lang_cd");

		// 读取coupon信息
		$results_product = $this->read_coupon_info($condition);
		if (count($results_product) == 0) {
			// 没有coupon信息，直接返回
			error_log("DB里没有指定coupon信息，返回-1。");
			$ret = array();
			$ret['result'] = -1;
			return $ret;
		}

		// 先检查t_bot_order表是否为该用户已经生成过了coupon记录
		$sql = "SELECT * 
			FROM t_bot_order a
			WHERE
				a.bot_id = :bot_id
				AND a.item_div = 7
				AND a.product_id = :coupon_id
				AND a.member_id = :member_id
			";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':bot_id' => $results_product[0]["bot_id"],
			':coupon_id' => $coupon_id,
			':lang_cd' => $lang_cd,
			':member_id' => $member_id,
		));
		$results = $query->execute()->as_array();
		if (count($results) == 0) {
			// 还没生成过，直接返回
			error_log("t_bot_order里没有为该用户发行该coupon信息，返回-2。");
			$ret = array();
			$ret['result'] = -2;
			return $ret;
		}

		// unlock
		$order_id = $results[0]["order_id"];
		$num = $results[0]["num"] - 1;
		error_log("该用户该coupon使用次数为". $results[0]["num"]);
		error_log("修改该用户该coupon使用次数为". $num);

		$sql = "UPDATE t_bot_order
				SET
					num = :num
				WHERE
					order_id = :order_id
					AND member_id = :member_id
				";
		$query = DB::query(Database::UPDATE, $sql);
		$query->parameters(array(
			':order_id' => $order_id,
			':num' => $num,
			':member_id' => $member_id,
		));

		$results = $query->execute();
		$ret = array();
		$ret['result'] = 1;
		return $ret;
	}	
	// 2021.01.22 #7876 end

	// 2021.05.07 #9478 begin 增加province
	function get_province_by_city($condition)
	{
		$city = $condition["city"];

		$sql = "SELECT province
				FROM m_city 
				WHERE find_in_set(:city,lower(replace(city,' ',','))) > 0
				";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':city' => $city,
			));
		$results = $query->execute();
		return $results->as_array();
	}
	// 2021.05.07 #9478 begin 增加province

	// 2021.11.01 #17801 法人预约 begin
	function get_corp_info($condition) {
		$dest_bot_id = $condition["dest_bot_id"];
		$corp_id = $condition["corp_id"];
		$corp_member_id = $condition["corp_member_id"];

		// 取父bot_id
		$parent_bot_id = $this->get_grp_parent_bot_id($dest_bot_id);
		if ($parent_bot_id == -1) {
			$parent_bot_id = $dest_bot_id;
		}

		// 取得法人情报
		$sql = "SELECT *
				FROM t_bot_corp 
				WHERE 
				bot_id = :bot_id
				AND corp_id = :corp_id
				";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $parent_bot_id,
				':corp_id' => $corp_id,
			));
		$corp_result = $query->execute()->as_array();
		
		$sql = "SELECT *
				FROM t_bot_corp_member
				WHERE 
				bot_id = :bot_id
				AND corp_id = :corp_id
				AND corp_member_id = :corp_member_id
				";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $parent_bot_id,
				':corp_id' => $corp_id,
				':corp_member_id' => $corp_member_id,
			));
		$corp_member_result = $query->execute()->as_array();

		return [
			"corp_result" => $corp_result[0],
			"corp_member_result" => $corp_member_result[0],
		];
	}
	// 2021.11.01 #17801 法人预约 begin

	// 2021.11.16 #12049 暗号化
    public function talkappi_encrypt($input) {
		$sql = "SELECT 
                (HEX(AES_ENCRYPT(:input, :key))) AS encrypt_ret
			FROM DUAL
			";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':input' => $input,
			':key' => $this->_aes256_key,
		));

		$results = $query->execute();
		return $results->as_array()[0]["encrypt_ret"];
    }

    public function talkappi_decrypt($input) {
		/*
		$sql = "SELECT 
					convert(
						AES_DECRYPT(UNHEX(:input), :key)
						USING utf8mb4
					) AS decrypt_ret
			FROM DUAL
			";
		*/
		// 不能用USING utf8，否则会把"bot_contact":"📞03-2260-1686"里的电话这个特殊符号截断的
		// 要用也要用utf8mb4
		$sql = "SELECT 
					AES_DECRYPT(UNHEX(:input), :key) AS decrypt_ret
				FROM DUAL
			";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':input' => $input,
			':key' => $this->_aes256_key,
		));

		$results = $query->execute();
		return $results->as_array()[0]["decrypt_ret"];
    }
	// 2021.11.16 #12049 暗号化 end

	// 2021.12.25 jtb 增加错误处理 begin
    public function get_t_bot_order_jtb_timeout($condition) {
        $diff_time = 90;	// 90分钟以前的
        if (array_key_exists('diff_time', $condition)) {
            $diff_time = $condition["diff_time"];
        }


        $check_time = date('Y-m-d H:i:s', strtotime("-$diff_time minutes"));

        $sql = "SELECT a.pay_data->>'$.latest_time' as latest_time,
				a.order_id,
				a.pay_data->>'$.latest_action' as latest_action
            FROM `t_bot_order` a
            WHERE 
            a.pay_data IS NOT NULL
            AND a.pay_data != ''
            AND a.order_data->>'$.payment_type_cd' = '2'
            AND a.order_data->> '$.card_type' = 'jtb'
            AND a.pay_data->>'$.latest_action' = 'regist'
            AND a.order_status_cd='15'
            AND a.pay_data->>'$.latest_time' < :check_time
            ORDER BY a.order_id  DESC
		";
        $query = DB::query(Database::SELECT, $sql);
        $query->parameters(array(
            ':check_time' => $check_time,
        ));
        $results = $query->execute()->as_array();
		return $results;
    }	
	// 2021.12.25 jtb 增加错误处理 end

	// 2022.01.13 ユーザー属性自動付け機能
	// bot_class_cd = -1 代表根据t_bot表查询
	// end_log_time = -1 代表不检查log_time
	// $upd_user = -1 代表是engine
	// 2022.06.19 #27615 增加bot_class_cd
    public function create_update_member_attr($type, $log_table_name, $log_id, $upd_user = -1, $log_result = NULL, $cur_bot_class_cd = -1) {
		$debug = false;
		if ($debug) {
			error_log("Comming create_update_member_attr() type=$type log_table_name=$log_table_name log_id=$log_id ");
		}

		/*
		$sleep_time = 20;
		if ($debug) {
			error_log("Comming create_update_member_attr() will sleep $sleep_time second");
		}
		sleep($sleep_time);
		*/
		// 1. 检索相应的log_table
		if ($log_result == NULL) {
			$sql = "SELECT 
						a.bot_id,
						a.member_id,
						mbi.attr
					FROM $log_table_name a
					INNER JOIN t_bot t
						ON a.bot_id = t.bot_id
					INNER JOIN m_bot_intent mbi
						ON mbi.intent_class_cd = t.bot_class_cd
							AND mbi.intent_cd = a.intent_cd
							AND mbi.sub_intent_cd = a.sub_intent_cd
							AND mbi.lang_cd = a.lang_cd
							AND mbi.attr IS NOT NULL 
							AND mbi.attr <> ''
					WHERE 
						a.log_id = :log_id
						AND a.vir_is_intent_inquiry = 1
						AND a.vir_is_member_msg =1
						AND a.vir_is_score_between01 = 1
						AND (a.answer_type = 0 or a.answer_type = 1 or a.answer_type = 2)

			";
			$query = DB::query(Database::SELECT, $sql);
			$query->parameters(array(
				':log_id' => $log_id,
			));
			$log_results = $query->execute()->as_array();

			if (count($log_results) == 0) {
				if ($debug) {
					error_log("create_update_member_attr() return -1 : log is not fullfill condition");
				}
				return -1;
			}
			$log_result = $log_results[0];
		} else {
		}


		$faq_attr = $log_result["attr"];
		$bot_id = $log_result["bot_id"];
		$member_id = $log_result["member_id"];

		// 2022.06.19 #27615 增加bot_class_cd,code_div begin
		if ($cur_bot_class_cd == -1) {
            $sql = "SELECT 
                        bot_class_cd
                    FROM t_bot
                    WHERE
                        bot_id = :bot_id
                ";

            $query = DB::query(Database::SELECT, $sql);
            $query->parameters(array(
				':bot_id' => $bot_id,
                ));
            $results = $query->execute()->as_array();
			$cur_bot_class_cd = $results[0]["bot_class_cd"];
		}
		// 2022.06.19 #27615 增加bot_class_cd,code_div end

		$code_div = "9998$cur_bot_class_cd";

		// 4. 处理各attr
		$array_faq_attr = explode(",", $faq_attr);
		for ($i=0; $i<count($array_faq_attr); $i++) {
			$cur_attr = $array_faq_attr[$i];
			$cur_history_new = [
				"bot_id"=>"$bot_id",
				"type"=>$type,
				"log_id"=>"$log_id",
			];

			$cur_history_new_json = json_decode(json_encode($cur_history_new));

			// 放入memberattr
			$member_attr = ORM::factory('memberattr')
			->where('member_id', '=', $member_id)
			->where('code_div', '=', $code_div)
			->where('attr', '=', $cur_attr)
			->find();
	
			if ($member_attr->member_id == NULL) {
				// 新规
				$member_attr->member_id = $member_id;
				$member_attr->code_div = $code_div;
				$member_attr->attr = $cur_attr;
				$member_attr->delete_flg = 0;
				$action_history_db = json_decode("[]");
				$action_history_db[count($action_history_db)] = $cur_history_new_json;
				$member_attr->action_history = json_encode($action_history_db);
				$member_attr->upd_user = $upd_user;
				$member_attr->upd_time = date('Y-m-d H:i:s');
				$member_attr->save();
			} else {
				// 既存
				$action_history_db = json_decode($member_attr->action_history);
				// 检查是否已经放过本log_id
				{
					$action_history_db_array = json_decode($member_attr->action_history, true);
					$flg_exist = false;
					foreach ($action_history_db_array as $history) {
						if ($history["log_id"] == $log_id) {
							$flg_exist = true;
							break;
						}
					}
					if ($flg_exist == true) {
						// error_log("log_id=$log_id is already exist in member_id=$member_id attr=$cur_attr");
						if ($debug) {
							error_log("create_update_member_attr() log_id=$log_id is already exist in member_id=$member_id attr=$cur_attr");
						}
						continue;
					}
				}

				// 修改db
				$action_history_db[count($action_history_db)] = $cur_history_new_json;
				$fields['action_history'] = json_encode($action_history_db);
				$fields['upd_user'] = $upd_user;
				$fields['upd_time'] = date('Y-m-d H:i:s');

				DB::update('t_member_attr')->set($fields)
				->where('member_id', '=', $member_id)
				->where('code_div', '=', $code_div)
				->where('attr', '=', $cur_attr)
				->execute();
			}
			//
		}

		if ($debug) {
			error_log("create_update_member_attr() return 0 : success");
		}
		return 0;
    }	
	// 2022.01.13 ユーザー属性自動付け機能 end

	// 2022.03.22 #22751 
	// 2022.06.04 #27013
	function search_item_crowd_status($condition)
	{
		$lang_cd = $condition["lang_cd"];
		$bot_id = $condition["bot_id"];
		$item_ids = "";
		if (array_key_exists("item_ids", $condition)) {
			$item_ids = $condition["item_ids"];
		}		

		$original_bot_id = $bot_id;
		$bot_id = $this->get_bot_id_list_include_template_bot($bot_id);
		$template_bot_id = $this->get_template_bot_id($bot_id);

		$sql_status_cd_end_date = 
			" AND (a.end_date is NULL 
				OR (a.item_status_cd in ('01','08') AND DATE_FORMAT(a.end_date,'%Y-%m-%d') >= :cur_date)
				OR a.item_status_cd in ('03')
			)
			AND a.item_status_cd in ('01','03','08')
			";

		$sql_item_ids = "";
		if ($item_ids != "") {
			$sql_item_ids = "AND a.item_id in ($item_ids)";
		}
		try {
			$sql = "SELECT 
					tics_main.bot_id,
					tics_main.item_id,
					tics_main.status_cd,
					tics_main.restrict_time,
					tics_max_time.item_status_cd,
					tics_max_time.item_data,
					tics_max_time.item_name,
					tics_max_time.upd_time_max
				FROM t_item_congestion tics_main
				INNER JOIN (
					SELECT r.bot_id,a.item_id,a.item_data,a.item_status_cd,b.item_name, MAX(tics.upd_time) AS upd_time_max
					FROM t_item a
					INNER JOIN t_item_description b
						ON a.item_id = b.item_id
					INNER JOIN t_item_display r
						ON a.item_id = r.item_id
						AND a.item_div = r.item_div
						AND find_in_set(:lang_cd, r.lang_display)
						AND r.public_flg = 1
					INNER JOIN t_item_congestion tics
						ON a.item_id = tics.item_id
						AND r.bot_id = tics.bot_id
					WHERE
						r.bot_id in ($bot_id)
					AND (r.bot_id = :original_bot_id OR (r.bot_id = :template_bot_id AND r.item_div != 2))

					AND b.lang_cd = :lang_cd

					AND a.delete_flg <> 1			
					$sql_item_ids

					AND JSON_VALID(a.item_data)
			        AND JSON_EXTRACT(a.item_data, '$.congestion_flg') = '1'

					AND (a.start_date is NULL OR DATE_FORMAT(a.start_date,'%Y-%m-%d') <= :cur_date)
					$sql_status_cd_end_date
					AND (a.regular_start = :regular_date_kara 
						OR a.regular_end = :regular_date_kara 
						OR (a.regular_start <= a.regular_end AND a.regular_start <= :regular_date AND :regular_date <= a.regular_end)
						OR (a.regular_start > a.regular_end AND (a.regular_start <= :regular_date OR :regular_date <= a.regular_end))
					)

					GROUP BY r.bot_id,a.item_id
				) tics_max_time
				ON 
					tics_main.bot_id = tics_max_time.bot_id
				AND tics_main.item_id = tics_max_time.item_id
				AND tics_main.upd_time = tics_max_time.upd_time_max
				";

			$query = DB::query(Database::SELECT, $sql);
			$query->parameters(array(
				':item_status_cd' => '01',
				':bot_id' => $bot_id,
				':lang_cd' => $lang_cd,
				':item_ids' => $item_ids,
				':cur_date' => date('Y-m-d'),
				':regular_date' => date('m-d'),
				':regular_date_kara' => "",
				':original_bot_id' => $original_bot_id,
				':template_bot_id' => $template_bot_id,
			));
			$results = $query->execute();
			return $results->as_array();
		} catch (Exception $e) {
			// 因为reserve_remind的json内容可能是错误的
			error_log("apimodel.php search_item_crowd_status() exception." . $e->getMessage());
			return [];
		}
	}

	// 2023.07.02 #49950 add
	function search_item_schedule_description($condition)
	{
		$bot_id = $condition["bot_id"];
		$item_id = $condition["item_id"];

		try {
			$sql = "SELECT 
					*
				FROM t_schedule_description tsd
				WHERE
						tsd.bot_id = :bot_id
					AND tsd.item_id = :item_id
					AND tsd.type in ('businesshours','congestion')
				";

			$query = DB::query(Database::SELECT, $sql);
			$query->parameters(array(
				':bot_id' => $bot_id,
				':item_id' => $item_id,
			));
			$results = $query->execute();
			return $results->as_array();
		} catch (Exception $e) {
			error_log("apimodel.php search_item_schedule_description() bot_id=$bot_id,item_id=$item_id,exception." . $e->getMessage());
			return [];
		}
	}	

	// 2022.05.04 line的rich menu变化后，取出需要修改menu的用户 begin
	function search_bot_active_user_of_line($condition)
	{
		$bot_id = $condition["bot_id"];
		$sns_id = $condition["sns_id"];
		try {
			$sql = "SELECT 
					a.member_id,
					a.lang_cd,
					(
						SELECT 
							c.scene
						FROM
							t_bot_follow c
						WHERE
							c.sns_type_cd = 'ln'
							AND c.member_id = a.member_id
							AND c.bot_id = a.bot_id
							AND c.follow_time = 
								(	SELECT
										MAX(follow_time)
									FROM
										t_bot_follow d
									WHERE
										d.sns_type_cd = 'ln'
										AND d.member_id = a.member_id
										AND d.bot_id = a.bot_id
								)
					) AS scene_cd
				FROM t_bot_member a
				WHERE
					1 = 1
					AND a.bot_id = :bot_id
					AND a.follow = 1
					AND a.sns_type_cd = 'ln'
					AND sns_id = :sns_id
					AND a.last_talk_date = 
						(SELECT 
							MAX(last_talk_date) 
						FROM t_bot_member b
 						WHERE
						 	b.member_id = a.member_id
							AND b.sns_id = :sns_id
						)
				";

			$query = DB::query(Database::SELECT, $sql);
			$query->parameters(array(
				':bot_id' => $bot_id,
				':sns_id' => $sns_id,
			));
			$results = $query->execute();
			return $results->as_array();
		} catch (Exception $e) {
			error_log("apimodel.php search_bot_active_user_of_line() exception." . $e->getMessage());
			return [];
		}
	}

	function search_bot_user_of_line($condition)
	{
		$bot_id_list = $condition["bot_id_list"];
		$sns_id = $condition["sns_id"];
		try {
			$sql = "SELECT 
						distinct a.member_id as id
				FROM t_bot_member a
				WHERE
					1 = 1
					AND a.bot_id in ($bot_id_list)
					AND a.sns_id = :sns_id
					AND a.sns_type_cd = 'ln'
				";

			$query = DB::query(Database::SELECT, $sql);
			$query->parameters(array(
				':sns_id' => $sns_id,
			));
			$results = $query->execute();
			return $results->as_array();
		} catch (Exception $e) {
			error_log("apimodel.php search_bot_user_of_line() exception." . $e->getMessage());
			return "error";
		}
	}
	// 2022.05.04 end

	// 2022.09.20 #23807 begin
	function create_task($condition)
	{
		$task = ORM::factory('task');
		$task->bot_id = $condition["bot_id"];
		$task->task_name = $condition["task_name"];
		$task->task_type_cd = $condition["task_type_cd"];
		$task->task_class_cd = $condition["task_class_cd"];
		$task->task_register = $condition["task_register"];
		$task->repeat_cd = $condition["repeat_cd"];
		$task->task_data = $condition["task_data"];
		$task->scheduled_time = $condition["scheduled_time"];
		$task->task_status_cd = $condition["task_status_cd"];
		$task->upd_user = $condition["upd_user"];
		$task->upd_time = $condition["upd_time"];
		$task->create_program = $condition["create_program"];
		$task->save();

		$ret = $task->task_id;
		return $ret;
	}
	// 2022.09.20 #23807 end

	// 2022.12.06 #32792 begin
	function add_x_mail_log($condition)
	{
		$action = $condition["action"];
		$data = $condition["data"];
		$sender = $condition["sender"];
		$send_status = $condition["send_status"];
		$title = $condition["send_title"];
		$body = $condition["send_body"];
		$error_info = $condition["error_info"];

        $data_obj = json_decode($data);

		$task = ORM::factory('maillog');
		if (isset($data_obj->type)) {
			$task->type = $data_obj->type;
		} else {
			$task->type = "";
		}
		if (isset($data_obj->bot_id)) {
			$task->bot_id = $data_obj->bot_id;
		}
		if (isset($data_obj->link_id)) {
			$task->link_id = $data_obj->link_id;
		} else {
			$task->link_id = "";
		}
		if (isset($data_obj->member_id)) {
			$task->member_id = $data_obj->member_id;
		} else {
			$task->member_id = "";
		}
		$task->sender = $sender;
		if (isset($data_obj->receiver)) {
			$task->receiver = $data_obj->receiver;
		} else {
			$task->receiver = "";
		}
		if (isset($data_obj->receiver_cc)) {
			$task->receiver_cc = $data_obj->receiver_cc;
		} else {
			$task->receiver_cc = "";
		}
		if (isset($data_obj->receiver_bcc)) {
			$task->receiver_bcc = $data_obj->receiver_bcc;
		} else {
			$task->receiver_bcc = "";
		}
		if (isset($data_obj->replyto)) {
			$task->replyto = $data_obj->replyto;
		} else {
			$task->replyto = "";
		}
		$task->title = $title;
		$task->body	 = $body;
		$task->api_param = $data;
		$task->send_user = -1;
		$task->send_status = $send_status;
		$task->error_info = $error_info;
		$task->save();

		$ret = $task->id;
		return $ret;
	}
	// 2022.12.06 #32792 end

	// 2022.12.10 begin flow标记
	function save_flow_data($condition)
	{
		$bot_id = $condition["bot_id"];
		$member_id = $condition["member_id"];
		$lf = $condition["_lf"];
		$flow_data = $condition["flow_data"];

		$flowdata_orm = ORM::factory('flowdata')
			->where('bot_id', '=', $bot_id)
			->where('member_id', '=', $member_id)
			->where('lf', '=', $lf)
			->find();

		$flowdata_orm->bot_id = $bot_id;
		$flowdata_orm->member_id = $member_id;
		$flowdata_orm->lf = $lf;
		$flowdata_orm->flow_data = $flow_data;

		$flowdata_orm->save();

		$ret = [];
		$ret["id"] = $flowdata_orm->id;
		return $ret;
	}

	function read_flow_data($condition)
	{
		$bot_id = $condition["bot_id"];
		$member_id = $condition["member_id"];
		$lf = $condition["_lf"];

		$sql = "SELECT *
				FROM x_flow_data 
				WHERE 
					bot_id = :bot_id
					AND member_id = :member_id
					AND lf = :lf
				ORDER BY
				   id DESC
				LIMIT 1
				";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
				':member_id' => $member_id,
				':lf' => $lf,
			));
		$ret = $query->execute()->as_array();

		return $ret;
	}
	// 2022.12.10 end flow标记

	// 2023.01.26 #36870 begin
	function set_flow_data_extra_data($bot_id, $member_id, $lf, $data)
	{
		$flowdata_orm = ORM::factory('flowdata')
			->where('bot_id', '=', $bot_id)
			->where('member_id', '=', $member_id)
			->where('lf', '=', $lf)
			->find();

		$flowdata_orm->extra_data = json_encode($data, JSON_UNESCAPED_UNICODE);;

		$flowdata_orm->save();

		return 1;
	}

	function update_log_table_for_flow($condition)
	{
		$bot_id = $condition["bot_id"];
		$member_id = $condition["member_id"];
		$lf = $condition["_lf"];
		$link_type = $condition["link_type"];
		$link_id = $condition["link_id"];

		error_log("update_log_table_for_flow begin bot_id=$bot_id member_id=$member_id lf=$lf link_type=$link_type link_id=$link_id ");

		$flowdata_orm = ORM::factory('flowdata')
			->where('bot_id', '=', $bot_id)
			->where('member_id', '=', $member_id)
			->where('lf', '=', $lf)
			->find();

		$extra_data_obj = json_decode($flowdata_orm->extra_data);
		if (isset($extra_data_obj->log_id_t) && isset($extra_data_obj->log_table_m)) {
			$table_name = $extra_data_obj->log_table_m;
			$log_id = $extra_data_obj->log_id_t;
			$sql = "UPDATE t_bot_log_chat
					SET
						link_type = :link_type,
						link_id = :link_id
					WHERE
						log_id = :log_id
					";
			$query = DB::query(Database::UPDATE, $sql);
			$query->parameters(array(
				':link_type' => $link_type,
				':link_id' => $link_id,
				':log_table' => $table_name,
				':log_id' => $log_id
			));
			$results = $query->execute();

			$sql = "UPDATE $table_name
					SET
						link_type = :link_type,
						link_id = :link_id
					WHERE
						log_id = :log_id
					";
			$query = DB::query(Database::UPDATE, $sql);
			$query->parameters(array(
				':link_type' => $link_type,
				':link_id' => $link_id,
				':log_table' => $table_name,
				':log_id' => $log_id
			));
			$results = $query->execute();
		}
		error_log("update_log_table_for_flow end bot_id=$bot_id member_id=$member_id lf=$lf link_type=$link_type link_id=$link_id ");

		return 1;
	}
	// 2023.01.26 #36870 end

// 2023.04.23 #43257 begin
	function search_t_pay_client_info($condition)
	{
		$client_id = $condition["client_id"];

		$sql = "SELECT *
				FROM t_pay_client 
				WHERE 
					client_id = :client_id
					AND delete_flg = 0
				";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':client_id' => $client_id,
			));
		$ret = $query->execute()->as_array();

		return $ret;
	}

	function create_t_pay_transaction($condition)
	{
		$paytransaction = ORM::factory('paytransaction');
		$paytransaction->transaction_type = $condition["transaction_type"];
		$paytransaction->client_id = $condition["client_id"];
		$paytransaction->bot_id = $condition["bot_id"];
		$paytransaction->service_type = $condition["service_type"];
		$paytransaction->service_name = $condition["service_name"];
		$paytransaction->link_id = $condition["link_id"];
		if (isset($condition["charge_date"])) {
			$paytransaction->charge_date = $condition["charge_date"];
		}
		if (isset($condition["charge_fix_date"])) {
			$paytransaction->charge_fix_date = $condition["charge_fix_date"];
		}
		if (isset($condition["cancel_date"])) {
			$paytransaction->cancel_date = $condition["cancel_date"];
		}
		if (isset($condition["change_date"])) {
			$paytransaction->change_date = $condition["change_date"];
		}
		$paytransaction->amount = $condition["amount"];
		$paytransaction->pay_type_cd = $condition["pay_type_cd"];
		$paytransaction->pay_type_detail = $condition["pay_type_detail"];
		$paytransaction->acti_commision = $condition["acti_commision"];
		$paytransaction->agency_commision = $condition["agency_commision"];
		$paytransaction->result_cd = $condition["result_cd"];
		if (isset($condition["result_info"])) {
			$paytransaction->result_info = $condition["result_info"];
		}
		$paytransaction->upd_user = $condition["upd_user"];
		$paytransaction->upd_time = date('Y-m-d H:i:s');
		$paytransaction->save();

		$ret = [];
		$ret["transaction_id"] = $paytransaction->transaction_id;
		return $ret;
	}

	function search_t_pay_transaction($condition)
	{
		$transaction_id = "";
		if (isset($condition["transaction_id"])) {
			$transaction_id = $condition["transaction_id"];
		}
		$client_id = $condition["client_id"];
		$bot_id = $condition["bot_id"];
		$service_type = $condition["service_type"];
		$link_id = $condition["link_id"];
		$transaction_type = $condition["transaction_type"];
		$result_cd = "";
		if (isset($condition["result_cd"])) {
			$result_cd = $condition["result_cd"];
		}

		$sql = "SELECT *
				FROM t_pay_transaction
				WHERE 
					(:transaction_id = '' OR transaction_id = :transaction_id)
					AND client_id = :client_id
					AND bot_id = :bot_id
					AND service_type = :service_type
					AND link_id = :link_id
					AND transaction_type = :transaction_type
					AND (:result_cd = '' OR result_cd = :result_cd)
				";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':transaction_id' => $transaction_id,
				':client_id' => $client_id,
				':bot_id' => $bot_id,
				':service_type' => $service_type,
				':link_id' => $link_id,
				':transaction_type' => $transaction_type,
				':result_cd' => $result_cd,
			));
		$ret = $query->execute()->as_array();

		return $ret;
	}
// 2023.04.23 #43257 end	

	// 2023.04.26 #43410 begin
	function add_x_engineapi_log($condition)
	{
		$type = $condition["type"];
		$link_id = $condition["link_id"];
		$action = $condition["action"];
		$data = $condition["data"];
		$status = $condition["status"];
		$error_info = $condition["error_info"];

        $data_obj = json_decode($data);

		$task = ORM::factory('engineapilog');

		$task->type = $type;
		$task->link_id = $link_id;
		$task->action = $action;
		$task->api_param = $data;
		$task->send_user = -1;
		$task->status = $status;
		$task->error_info = $error_info;
		$task->save();

		$ret = $task->id;
		return $ret;
	}
	// 2023.04.26 #43410 begin

	// 2023.05.10 begin
	function add_amazon_ses_event($condition)
	{
		$message_id = $condition["message_id"];
		$event_type = $condition["event_type"];
		$notification = $condition["notification"];

		$task = ORM::factory('mailsesevents');
		$task->message_id = $message_id;
		$task->event_type = $event_type;
		$task->notification = $notification;

		$task->save();

		$ret = $task->id;
		return $ret;
	}
	// 2023.05.10 begin

	// 2023.05.14 #43185 begin
	function search_user_device_token($condition)
	{
		$user_ids = $condition["user_ids"];
		// 2023.05.29 #47908 begin
		$parts = explode(",", $user_ids);
		$num = count($parts);
		$sql_user_id = "";
		for ($i=0; $i<count($parts); $i++) {
			$cur_user_id = $parts[$i];
			$cur_sql =  " concat(',', user_id, ',') like concat('%,', '$cur_user_id', ',%')";
			if ($i == 0) {
				$sql_user_id = $cur_sql;
			} else {
				$sql_user_id = "$sql_user_id 
				OR $cur_sql";
			}
		}
		$sql = "SELECT user_id,device_token
				FROM t_user_device
				WHERE 
					($sql_user_id)
					AND delete_flg = 0
				";
		// 2023.05.29 #47908 end

		/*
		$sql = "SELECT user_id,device_token
				FROM t_user_device
				WHERE 
					t_user_device.user_id in ($user_ids)
					AND delete_flg = 0
				";
		*/
		//error_log("search_user_device_token() sql=" . sql);
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			));
		$ret = $query->execute()->as_array();

		return $ret;
	}	
	// 2023.05.14 #43185 end

	// 2023.05.20 #38860 chatgpt begin
	function search_user_latest_log($condition) {
		$bot_id = $condition["bot_id"];
		$member_id = $condition["member_id"];

		$log_table_id = $bot_id;
		if ($log_table_id == 401002 || $log_table_id == 401003) {
			$log_table_id = 401001;
		} else {
			$parent_bot_id = $this->get_grp_parent_bot_id($log_table_id);
			if ($parent_bot_id != -1) {
				$log_table_id = $parent_bot_id;
			}
		}
		$table_name = "t_bot_log_$log_table_id";

		$sql = "SELECT 
					a.*,b.extent_inf
				FROM $table_name a
				LEFT JOIN t_bot_log_extent b
				 ON a.log_id = b.log_id
				WHERE 
					a.bot_id = :bot_id
					AND a.member_id = :member_id
				ORDER By
					a.log_time DESC
				LIMIT 20
				";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':bot_id' => $bot_id,
			':member_id' => $member_id,
		));
		$ret = $query->execute()->as_array();

		return $ret;

	}
	function change_ai($condition) {
		$bot_id = $condition["bot_id"];
		$member_id = $condition["member_id"];
		$ai = $condition["ai"];

		$record = ORM::factory('memberextent')->where('bot_id', '=', $bot_id)->where('member_id', '=', $member_id)->find();
		if (isset($record->bot_id)) {
			$up_array = [];
			$up_array['ai'] = $condition["ai"];
			$query = DB::update('t_member_extent')->
				set($up_array)->
				where('bot_id', '=', $bot_id)->
				where('member_id', '=', $member_id);
			$result = $query->execute();
		} else {
			$record->bot_id = $bot_id;
			$record->member_id = $member_id;
			$record->ai = $ai;
			$record->save();
		}

		return 1;

	}
	function add_x_openai_cost($condition) {
		$bot_id = $condition["bot_id"];
		$model = $condition["model"];
		$date = $condition["date"];
		$prompt_tokens = $condition["prompt_tokens"];
		$completion_tokens = $condition["completion_tokens"];
		$total_tokens = $condition["total_tokens"];


		$record = ORM::factory('openaicost')->where('bot_id', '=', $bot_id)->where('model', '=', $model)->where('date', '=', $date)->find();
		if (isset($record->bot_id)) {
			$up_array = [];
			$up_array['prompt_tokens'] = $record->prompt_tokens + $prompt_tokens;
			$up_array['completion_tokens'] = $record->completion_tokens + $completion_tokens;
			$up_array['total_tokens'] = $record->total_tokens + $total_tokens;

			$query = DB::update('x_openai_cost')->
				set($up_array)->
				where('bot_id', '=', $bot_id)->
				where('model', '=', $model)->
				where('date', '=', $date);
			$result = $query->execute();
		} else {
			$record->bot_id = $bot_id;
			$record->model = $model;
			$record->date = $date;
			$record->prompt_tokens += $prompt_tokens;
			$record->completion_tokens += $completion_tokens;
			$record->total_tokens += $total_tokens;
			$record->save();
		}

		return 1;

	}	
	// 2023.05.20 #38860 chatgpt end

	// 2023.06.06 begin
	function get_x_openai_cost($condition) {
		$bot_id = $condition["bot_id"];
		$model = $condition["model"];
		$begin_date = $condition["begin_date"];
		$end_date = $condition["end_date"];

		$sql = "SELECT 
					model, 
					sum(total_tokens) as total_tokens_ym
				FROM x_openai_cost a
				WHERE 
					a.bot_id = :bot_id
					AND a.model  = :model 
					AND DATE_FORMAT(a.date,'%Y-%m-%d')  >= :begin_date
					AND DATE_FORMAT(a.date,'%Y-%m-%d')  <= :end_date
				";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':bot_id' => $bot_id,
			':model' => $model,
			':begin_date' => $begin_date,
			':end_date' => $end_date,
		));
		$ret = $query->execute()->as_array();
		return $ret;
	}
	// 2023.06.06 end

	// 2023.06.10  #48647 begin
	function search_puzzle($condition) {
		$bot_id = $condition["bot_id"];
		$puzzle_id = $condition["puzzle_id"];
		$lang_cd = $condition["lang_cd"];
		$member_id = "";
		if (isset($condition["member_id"])) {
			$member_id = $condition["member_id"];
		}

		// 读取puzzle数据
		$sql = "SELECT 
					a.*, 
					b.*
				FROM t_bot_puzzle a
				INNER JOIN t_bot_puzzle_description b
				  ON a.id = b.id
				  AND b.lang_cd = :lang_cd
				WHERE 
					a.bot_id = :bot_id
					AND a.puzzle_id = :puzzle_id
				";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':bot_id' => $bot_id,
			':puzzle_id' => $puzzle_id,
			':lang_cd' => $lang_cd,
			':cur_date' => date('Y-m-d'),
		));
		$record = $query->execute()->as_array();

		$ret = [];
		$ret["puzzle_data"] = $record;
		if (count($record) == 0) {
			return $ret;
		}

		// 读取用户的answer状态
		if ($member_id != "") {
			$temp_condition = [
				"id" => $record[0]["id"],
				"member_id" => $member_id,
			];
			$ret["member_puzzle_result"] = $this->get_member_puzzle_status($temp_condition);
		}

		// 读取limit信息
		if ($record[0]["setting"] != null) {
			$temp_obj = json_decode($record[0]["setting"]);
			if (property_exists($temp_obj, 'limit') && property_exists($temp_obj->limit, 'type')) {
				if ($temp_obj->limit->type == "day" && property_exists($temp_obj->limit, 'num')) {
					$sql = "SELECT sum(1) as bingo_num
							FROM t_bot_puzzle_result c
							WHERE
								c.id = :id
								AND c.answer_status = 1
								AND DATE_FORMAT(c.bingo_date,'%Y-%m-%d') = :cur_date
							";
					$query = DB::query(Database::SELECT, $sql);
					$query->parameters(array(
						':id' => $record[0]["id"],
						':cur_date' => date('Y-m-d'),
					));
					$temp_record = $query->execute()->as_array();
					$ret["limit"] = [
						"type" => $temp_obj->limit->type,
						"num" => intVal($temp_obj->limit->num),
						"bingo_num" => intVal($temp_record[0]["bingo_num"]),
					];
				} else {
					// 其它limit尚未设计
				}
			}
		}
		return $ret;
	}

	function insert_update_puzzle_answer_result($condition) {

		try {
            Database::instance()->begin();

			$bot_id = $condition["bot_id"];
			$puzzle_id = $condition["puzzle_id"];
			$lang_cd = $condition["lang_cd"];
			$member_id = $condition["member_id"];

			$id = $condition["id"];
			$answer_status = $condition["answer_status"];
			$bingo_date = null;
			if ($answer_status == 1) {
				$bingo_date = date('Y-m-d H:i:s');
			}

			// 锁记录
			$sql = "SELECT *
					FROM t_bot_puzzle
					WHERE id = :id
					FOR UPDATE";
			$query = DB::query(Database::SELECT, $sql);
			$query->parameters(array(
				':id' => $id,
			));
			$record = $query->execute()->as_array();
			if (count($record) == 0 || $record[0]["bot_id"] != $bot_id || $record[0]["puzzle_id"] != $puzzle_id) {
				$results = array();
				$results['result'] = "data not exist";    // -2表示指定id,puzzle_id数据不存在
				Database::instance()->rollback();
				return $results;
			}

			// 检查用户bingo情况
			$puzzle_info = $this->search_puzzle($condition);
			if (isset($puzzle_info["member_puzzle_result"]) 
				&& count($puzzle_info["member_puzzle_result"]) > 0 
				&& $puzzle_info["member_puzzle_result"][0]["answer_status"] == 1
				) {
					$results = array();
					$results['result'] = "already bingo";
					Database::instance()->rollback();
					return $results;
			}

			//检查limit情况
			if (isset($puzzle_info["limit"]) 
				&& $puzzle_info["limit"]["bingo_num"] >= $puzzle_info["limit"]["num"]
				) {
					$results = array();
					$results['result'] = "no zaiko";
					Database::instance()->rollback();
					return $results;
			}

			// 登录
			if (isset($puzzle_info["member_puzzle_result"]) && count($puzzle_info["member_puzzle_result"]) > 0) {
				//既存
				$sql = "UPDATE t_bot_puzzle_result
						SET
							answer_status = :answer_status,
							bingo_date = :bingo_date
						WHERE
							id = :id
							AND member_id = :member_id
						";
				$query = DB::query(Database::UPDATE, $sql);
				$query->parameters(array(
					':id' => $id,
					':member_id' => $member_id,
					':answer_status' => $answer_status,
					':bingo_date' => $bingo_date,
				));
				$query->execute();

				$results = array();
				$results['result'] = "ok";
				error_log("insert_update_puzzle_answer_result() commit update");
				Database::instance()->commit();
				return $results;
			} else {
				// 新规
				$sql = "INSERT INTO t_bot_puzzle_result
						(id, member_id, answer_status, bingo_date)
						VALUES
						(:id, :member_id, :answer_status, :bingo_date) 
						";
				$query = DB::query(Database::UPDATE, $sql);
				$query->parameters(array(
					':id' => $id,
					':member_id' => $member_id,
					':answer_status' => $answer_status,
					':bingo_date' => $bingo_date,
				));
				$query->execute();

				$results = array();
				$results['result'] = "ok";
				Database::instance()->commit();
				error_log("insert_update_puzzle_answer_result() commit insert");
				return $results;
			}			
		} catch (Exception $e) {
			Database::instance()->rollback();
			error_log("insert_update_puzzle_answer_result() exception", $e->getMessage());
			$results = array();
			$results['result'] = "exception";
			return $results;
		}
	}

	function get_member_puzzle_status($condition) {
		$id = $condition["id"];
		$member_id = $condition["member_id"];

		// 取出puzzle细节，当天答对人数
		$sql = "SELECT 
					a.*
				FROM t_bot_puzzle_result a
				WHERE
					a.id = :id
					AND member_id = :member_id
				";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':id' => $id,
			':member_id' => $member_id,
		));
		$ret = $query->execute()->as_array();
		return $ret;
	}
	// 2023.06.06  #48647 end

	// 2023.06.28 #47533 begin
	function get_rc_area($condition) {
		$region = $condition["region"];
		$prefecture = $condition["prefecture"];
		$lang_cd = $condition["lang_cd"];

		$sql = "SELECT 
					a.*
				FROM m_rc_area a
				WHERE
					a.region = :region
					AND a.prefecture = :prefecture
				ORDER BY
					a.area
				";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':region' => $region,
			':prefecture' => $prefecture,
		));
		$ret = $query->execute()->as_array();
		return $ret;
	}

	function get_rc_municipality($condition) {
		$region = $condition["region"];
		$prefecture = $condition["prefecture"];
		$area = $condition["area"];
		$lang_cd = $condition["lang_cd"];

		$sql = "SELECT 
					a.*,
					b.area_ja,
					b.area_en,
					b.area_cn,
					b.area_tw,
					b.area_kr
				FROM m_rc_municipality a
				INNER JOIN m_rc_area b
					ON a.region = b.region
					AND a.prefecture = b.prefecture
					AND a.area = b.area
				WHERE
					a.region = :region
					AND a.prefecture = :prefecture
					AND a.area = :area
				ORDER BY
					a.city
				";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':region' => $region,
			':prefecture' => $prefecture,
			':area' => $area,
		));
		$ret = $query->execute()->as_array();
		return $ret;
	}

	function update_t_member_rc($condition) {
		$bot_id = $condition["bot_id"];
		$member_id = $condition["member_id"];
		$region = $condition["region"];
		$prefecture = $condition["prefecture"];
		$city = $condition["city"];
		$village = $condition["village"];

		$record = ORM::factory('memberrc')->where('bot_id', '=', $bot_id)->where('member_id', '=', $member_id)->find();
		if (isset($record->bot_id)) {
			$up_array = [];
			$up_array['region'] = $condition["region"];
			$up_array['prefecture'] = $condition["prefecture"];
			$up_array['city'] = $condition["city"];
			$up_array['village'] = $condition["village"];
			$query = DB::update('t_member_rc')->
				set($up_array)->
				where('bot_id', '=', $bot_id)->
				where('member_id', '=', $member_id);
			$result = $query->execute();
		} else {
			$record->bot_id = $bot_id;
			$record->member_id = $member_id;
			$record->region = $region;
			$record->prefecture = $prefecture;
			$record->city = $city;
			$record->village = $village;
			$record->save();
		}

		return 1;

	}
	// 2023.06.28 #47533 end	
}
?>




