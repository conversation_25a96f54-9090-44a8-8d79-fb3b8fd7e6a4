<?php defined('SYSPATH') OR die('No direct script access.');

class Model_Maximummodel extends Model_Basemodel
{
	function get_maximum_all($bot_id) {
		$sql = "SELECT	m.id, m.name
		FROM t_bot_maximum as m
		WHERE m.bot_id =:bot_id AND m.span = 'all'";

		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
		));
		$results = $query->execute()->as_array('id', 'name');
		return $results;
	}

	function get_maximum_1d1h($bot_id) {
		$sql = "SELECT id, name, span FROM t_bot_maximum WHERE bot_id=:bot_id AND (span='1d' OR span='1h')";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
		));
		$results = $query->execute()->as_array();
		return $results;
	}

	function get_maximum_1d($bot_id) {
		$sql = "SELECT id, name, span FROM t_bot_maximum WHERE bot_id=:bot_id AND span='1d'";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
		));
		$results = $query->execute()->as_array();
		return $results;
	}
	
	function get_maximum_1h($bot_id) {
		$sql = "SELECT id, name, span FROM t_bot_maximum WHERE bot_id=:bot_id AND span='1h'";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
		));
		$results = $query->execute()->as_array();
		return $results;
	}

	
	function get_maximums($bot_id, $entries) {
		if (count($entries) == 0) {
			return [];
		}
		$entry_maximum_list = [];
		foreach($entries as $entry) {
			$entry_data = json_decode($entry->entry_data, true);
			foreach($entry_data as $e) {
				if (!is_array($e) || !array_key_exists('maximum', $e)) continue;
				$maximum_id = $e['maximum'];
				$sql = "SELECT t.id, t.span, t.maximum_data, t.extra_data FROM t_bot_maximum t 
				WHERE t.bot_id=:bot_id AND t.id=:maximum_id 
				ORDER BY t.id";
				$query = DB::query(Database::SELECT, $sql);
				$query->parameters(array(
						':bot_id' => $bot_id,
						':maximum_id' => $maximum_id,
				));
				$results = $query->execute()->as_array();
				if (count($results) > 0) $entry_maximum_list[$results[0]['id']] = $results[0];
			}
		}
		return $entry_maximum_list;
	}

	function get_inquiry_maximum_ids($inquiry_id) {
		$inquiry_entry = ORM::factory('inquiryentry')->where('inquiry_id', '=', $inquiry_id)->where('lang_cd', '=', 'ja')->order_by('no')->find_all();
		$maximum_list = [];
		foreach($inquiry_entry as $entry) {
			$entry_data = json_decode($entry->entry_data, true);
			if (is_array($entry_data)) {
				foreach($entry_data as $e) {
					if (!is_array($e) || !array_key_exists('maximum', $e)) continue;
					$maximum_list[] = $e['maximum'];
				}
			}
		}
		return $maximum_list;
	}

	function get_maximum_remains($bot_id, $entries, $admin=false) {
		if (count($entries) == 0) {
			return [];
		}
		/*
		$maximum_arr = [];
		$maximum_rules = [];
		foreach($entries as $entry) {
			$entry_data = json_decode($entry->entry_data, true);
			foreach($entry_data as $e) {
				if (is_array($e) && array_key_exists('maximum', $e)) {
					$maximum_arr[] = $e['maximum'];
					$maximum_rules[$e['maximum']] = json_decode($entry->input_rules, true);
					break;
				}
			}
		}
		$in = implode(',', $maximum_arr);
		$sql = "SELECT t.id, t.span, t.maximum_data, r.day, r.time, r.maximum, r.threshold FROM t_bot_maximum_remains r 
		INNER JOIN t_bot_maximum t ON t.bot_id=r.bot_id AND t.id=r.id WHERE t.bot_id=:bot_id AND t.id IN ($in) ORDER BY r.id, span, day";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
		));
		$results = $query->execute()->as_array();

		$ret = [];
		foreach($results as $r) {
			if ($r['span'] == 'all') {
				$ret[$r['id']] = ['span'=>$r['span'], 'maximum'=>$r['maximum'], 'threshold'=>$r['threshold']];
			}
			else if ($r['span'] == '1d') {
				if (!$this->_filter_period($maximum_rules[$r['id']], $r['day'], '')) continue;
				if (array_key_exists($r['id'], $ret)) {
					$ret[$r['id']]['period'][$r['day']] = ['maximum'=>$r['maximum'], 'threshold'=>$r['threshold']];
				}
				else {
					$ret[$r['id']] = ['span'=>$r['span'], 'period'=>[$r['day']=>['maximum'=>$r['maximum'], 'threshold'=>$r['threshold']]]];
				}
			}
			else if ($r['span'] == '1h') {
				if (!$this->_filter_period($maximum_rules[$r['id']], $r['day'], $r['time'])) continue;
				if (array_key_exists($r['id'], $ret)) {
					$ret[$r['id']]['period'][$r['day']][$r['time']] = ['maximum'=>$r['maximum'], 'threshold'=>$r['threshold']];
				}
				else {
					$ret[$r['id']] = ['span'=>$r['span'], 'period'=>[$r['day']=>[$r['time']=>['maximum'=>$r['maximum'], 'threshold'=>$r['threshold']]]]];
				}
			}
		}
		return $ret;
		*/

		$entry_maximum_list = [];
		foreach($entries as $entry) {
			$entry_data = json_decode($entry->entry_data, true);
			foreach($entry_data as $e) {
				if (!is_array($e) || !array_key_exists('maximum', $e)) continue;
				$maximum_id = $e['maximum'];
				$maximum_rules = json_decode($entry->input_rules, true);
				$sql = "SELECT t.id, t.span, t.maximum_data, r.day, r.time, r.maximum, r.threshold FROM t_bot_maximum_remains r 
				INNER JOIN t_bot_maximum t ON t.bot_id=r.bot_id AND t.id=r.id 
				WHERE t.bot_id=:bot_id AND t.id=:maximum_id AND r.stop=0 
				ORDER BY r.id, span, r.day, r.time";
				$query = DB::query(Database::SELECT, $sql);
				$query->parameters(array(
						':bot_id' => $bot_id,
						':maximum_id' => $maximum_id,
				));
				$results = $query->execute()->as_array();
				$ret = [];
				foreach($results as $r) {
					if ($r['span'] == 'all') {
						$ret = ['maximum'=>$r['maximum'], 'threshold'=>$r['threshold']];
					}
					else if ($r['span'] == '1d') {
						if (!$this->_filter_period($maximum_rules, $r['day'], '', $admin)) continue;
						$ret[$r['day']] = ['maximum'=>$r['maximum'], 'threshold'=>$r['threshold']];
					}
					else if ($r['span'] == '1h') {
						if (!$this->_filter_period($maximum_rules, $r['day'], $r['time'], $admin)) continue;
						$ret[$r['day']][$r['time']] = ['maximum'=>$r['maximum'], 'threshold'=>$r['threshold']];
					}
				}
				if (count($ret) > 0) {
					$entry_maximum_list[$entry->no][] = ['maximum_id'=>$maximum_id, 'remains'=>['span'=>$r['span'], 'period'=>$ret]];
				}
				else {
					$entry_maximum_list[$entry->no] =  [['maximum_id'=>$maximum_id, 'remains'=>[]]];
				}
			}
		}
		return $entry_maximum_list;
	}

	private function _filter_period($rule, $date, $time, $admin=false) {
		if (!array_key_exists('periods', $rule)) {
			return true;
		}
		foreach($rule['periods'] as $period) {
			if (array_key_exists('start_date', $period)) {
				if ($date < $period['start_date']) continue;
			}
			if (array_key_exists('end_date', $period)) {
				if ($date > $period['end_date']) continue;
			}
			$today = date('Y-m-d');

			//過去の日付は除外する
			if ($date < date('Y-m-d', strtotime($today))) continue;

			if ($admin == false) {
				if (array_key_exists('start_after_days', $period)) {
					if ($date < date('Y-m-d', strtotime('+' . $period['start_after_days'] . ' day', strtotime($today)))) continue;
				}
				if (array_key_exists('end_after_days', $period)) {
					if ($date > date('Y-m-d', strtotime('+' . $period['end_after_days'] . ' day', strtotime($today)))) continue;
				}
				if (array_key_exists('limit', $period)) {
					if (array_key_exists('start_before_days', $period['limit'])) {
						if ($date < date('Y-m-d', strtotime('+' . $period['limit']['start_before_days'] . ' day', strtotime($today)))) continue;
						if ($date == date('Y-m-d', strtotime('+' . $period['limit']['start_before_days'] . ' day', strtotime($today))) && 
							array_key_exists('from_time', $period['limit'])) {
							if (date('H:i') < $period['limit']['from_time']) continue;
						}
					}
					if (array_key_exists('before_days', $period['limit'])) {
						if ($date < date('Y-m-d', strtotime('+' . $period['limit']['before_days'] . ' day', strtotime($today)))) continue;
						if ($date == date('Y-m-d', strtotime('+' . $period['limit']['before_days'] . ' day', strtotime($today))) && 
							array_key_exists('until_time', $period['limit'])) {
							if (date('H:i') > $period['limit']['until_time']) continue;
						}
					}
					if (array_key_exists('before_hours', $period['limit'])) {
						$now = date('Y-m-d H:i:s');
						$time_from = "";
						if (strpos($time, "-") === false) {
							$time_from = $time;
						}
						else {
							$time_from = substr($time, 0, strpos($time, "-"));
						}
						if (date('Y-m-d H:i:s',strtotime($date . ' ' . $time_from)) < date('Y-m-d H:i:s', strtotime('+' . $period['limit']['before_hours'] . ' hour', strtotime($now)))) continue;
					}
				}
			}
			return true;
		}
		return false;
	}

	//予約枠の残数を更新する
	function execute_maximum($bot_id, $maximum_info, $member_id, $result_id, $order_status_cd, $order_id = null, $user_id = null) {
		if (!array_key_exists('num', $maximum_info)) $maximum_info['num'] = 1;
		$order_info = null;
		if (isset($maximum_info['order_info'])) {
			$order_info = $maximum_info['order_info'];
			unset($maximum_info['order_info']);
		}
		$lock = 'SELECT * FROM t_bot_maximum_remains WHERE bot_id=:bot_id AND id=:maximum_id AND stop=0 ';
		$sql = "UPDATE t_bot_maximum_remains SET maximum=maximum-:num WHERE bot_id=:bot_id AND id=:maximum_id AND stop=0 AND maximum>=:num";
		$params = [
			':bot_id' => $bot_id,
			':maximum_id' => $maximum_info['maximum_id'],
			':num' => intval($maximum_info['num'])];
		if (array_key_exists('day', $maximum_info)) {
			$sql = $sql . " AND day=:day";
			$lock = $lock . " AND day=:day";
			$params[':day'] = $maximum_info['day'];
		}
		if (array_key_exists('time', $maximum_info)) {
			$time = explode(',', $maximum_info['time']);
				if (count($time) > 1) {
				$sub = [];
				foreach($time as $t) {
					$sub[] = " time='" . $t . "' ";
				}
				$sql = $sql . " AND (" . implode(" OR ", $sub) . ")";
				$lock = $lock . " AND (" . implode(" OR ", $sub) . ")";
			}
			else {
				$sql = $sql . " AND time=:time";
				$lock = $lock . " AND time=:time";
				$params[':time'] = $maximum_info['time'];
			}
		}
		$lock = $lock . ' FOR UPDATE';
		$lock_query = DB::query(Database::SELECT, $lock);
		$lock_query->parameters($params);
		$lock_query->execute();

		$query = DB::query(Database::UPDATE, $sql);
		$query->parameters($params);
		$ret = $query->execute();
		if ($ret == 0) return false;

		//maximum orderテーブルに登録する
		if ($order_id == null) {
			$order = ORM::factory('maximumorder');
			$order->bot_id = $bot_id;
			$order->maximum_id = $maximum_info['maximum_id'];
			if (array_key_exists('day', $maximum_info)) {
				$order->day = $maximum_info['day'];
			}
			if (array_key_exists('time', $maximum_info)) {
				$order->time = $maximum_info['time'];
			}
			$order->member_id = $member_id;
			$order->order_no = $this->get_max_order_no();
			$order->order_status_cd = $order_status_cd;
			$order->order_date = date('Y-m-d H:i:s');
			$order->num = $maximum_info['num'];
			$order->link_id = $result_id;
			$order->order_data = json_encode($maximum_info, JSON_UNESCAPED_UNICODE);
			if ($order_info != null) $order->order_info = json_encode($order_info, JSON_UNESCAPED_UNICODE);
			$order->save();
		}
		else {
			$order = ORM::factory('maximumorder', $order_id);
			$order->order_status_cd = $order_status_cd;
			if (array_key_exists('day', $maximum_info)) {
				$order->day = $maximum_info['day'];
			}
			if (array_key_exists('time', $maximum_info)) {
				$order->time = $maximum_info['time'];
			}
			$order->upd_user = $user_id;
			$order->upd_time = date('Y-m-d H:i:s');
			$order->save();
		}
		return true;
	}

	function restore_maximum_only($bot_id, $maximum_info) {
		$lock = 'SELECT * FROM t_bot_maximum_remains WHERE bot_id=:bot_id AND id=:maximum_id';
		$sql = "UPDATE t_bot_maximum_remains SET maximum=maximum+:num WHERE bot_id=:bot_id AND id=:maximum_id";
		$params = [
			':bot_id' => $bot_id,
			':maximum_id' => $maximum_info['maximum_id'],
			':num' => $maximum_info['num']];
		if (array_key_exists('day', $maximum_info)) {
			$sql = $sql . " AND day=:day";
			$lock = $lock . " AND day=:day";
			$params[':day'] = $maximum_info['day'];
		}
		if (array_key_exists('time', $maximum_info)) {
			$time = explode(',', $maximum_info['time']);
				if (count($time) > 1) {
				$sub = [];
				foreach($time as $t) {
					$sub[] = " time='" . $t . "' ";
				}
				$sql = $sql . " AND (" . implode(" OR ", $sub) . ")";
				$lock = $lock . " AND (" . implode(" OR ", $sub) . ")";
			}
			else {
				$sql = $sql . " AND time=:time";
				$lock = $lock . " AND time=:time";
				$params[':time'] = $maximum_info['time'];
			}
		}

		$lock = $lock . ' FOR UPDATE';
		$lock_query = DB::query(Database::SELECT, $lock);
		$lock_query->parameters($params);
		$lock_query->execute();

		$query = DB::query(Database::UPDATE, $sql);
		$query->parameters($params);
		$ret = $query->execute();
		if ($ret == 0) throw new Exception('maximum_restore_error');
	}

	function restore_maximum($bot_id, $maximum_info, $result_id, $user_id, $delete=false) {
		$orm = ORM::factory('maximumorder')->where('bot_id', '=', $bot_id)->where('link_id', '=', $result_id)->where('maximum_id', '=', $maximum_info['maximum_id'])->find();
		if ($orm->order_status_cd == '03') {
			if ($delete) {
				$pairs = ['delete_flg'=>1, 'upd_time'=>date('Y-m-d H:i:s'), 'upd_user'=>$user_id];
				DB::update('t_maximum_order')->set($pairs)->where('link_id', '=', $result_id)->where('maximum_id', '=', $maximum_info['maximum_id'])->execute();
			}
			return;
		}
		$lock = 'SELECT * FROM t_bot_maximum_remains WHERE bot_id=:bot_id AND id=:maximum_id';
		$sql = "UPDATE t_bot_maximum_remains SET maximum=maximum+:num WHERE bot_id=:bot_id AND id=:maximum_id";
		$params = [
			':bot_id' => $bot_id,
			':maximum_id' => $maximum_info['maximum_id'],
			':num' => $orm->num];
		if (array_key_exists('day', $maximum_info)) {
			$sql = $sql . " AND day=:day";
			$lock = $lock . " AND day=:day";
			$params[':day'] = $maximum_info['day'];
		}
		if (array_key_exists('time', $maximum_info)) {
			$time = explode(',', $maximum_info['time']);
				if (count($time) > 1) {
				$sub = [];
				foreach($time as $t) {
					$sub[] = " time='" . $t . "' ";
				}
				$sql = $sql . " AND (" . implode(" OR ", $sub) . ")";
				$lock = $lock . " AND (" . implode(" OR ", $sub) . ")";
			}
			else {
				$sql = $sql . " AND time=:time";
				$lock = $lock . " AND time=:time";
				$params[':time'] = $maximum_info['time'];
			}
		}

		$lock = $lock . ' FOR UPDATE';
		$lock_query = DB::query(Database::SELECT, $lock);
		$lock_query->parameters($params);
		$lock_query->execute();

		$query = DB::query(Database::UPDATE, $sql);
		$query->parameters($params);
		$ret = $query->execute();
		if ($ret == 0) throw new Exception('maximum_restore_error');

		$pairs = ['order_status_cd'=>'03', 'upd_time'=>date('Y-m-d H:i:s'), 'upd_user'=>$user_id];
		if ($delete) $pairs['delete_flg'] = 1;
		DB::update('t_maximum_order')->set($pairs)->where('link_id', '=', $result_id)->where('maximum_id', '=', $maximum_info['maximum_id'])->execute();
	}

	function cancel_inquiry($bot_id, $result_id, $user_id, $delete=false) {
		$orm = ORM::factory('inquiryresult', $result_id);
		// キャンセル済みの削除対応
		if ($orm->status_cd == '03' && $delete && $orm->delete_flg == 0) {
			$orm = ORM::factory('inquiryresult', $result_id);
			$orm->delete_flg = 1;
			$orm->upd_time = date('Y-m-d H:i:s');
			$orm->upd_user = $user_id;
			$orm->save();
			return false;
		}
		if ($orm->status_cd == '03' || $orm->delete_flg == 1) return false;
		$result_entries = ORM::factory('inquiryresultentry')->where('result_id', '=', $result_id)->find_all();
		$maximum_list = [];
		foreach($result_entries as $result_entry) {
			if ($result_entry->entry_extra_info == '') continue;
			$entry_extra_info = json_decode($result_entry->entry_extra_info, true);
			if (!array_key_exists('maximum_id', $entry_extra_info)) continue;
			$maximum_list[] = $entry_extra_info;
		}
		// #30490
		if (count($maximum_list) == 0) {
			$maximum_orders = ORM::factory('maximumorder')->where('link_id', '=', $result_id)->find_all();
			foreach($maximum_orders as $mo) {
				if ($mo->order_status_cd == '03') {
					if ($delete) {
						$pairs = ['delete_flg'=>1, 'upd_time'=>date('Y-m-d H:i:s'), 'upd_user'=>$user_id];
						DB::update('t_maximum_order')->set($pairs)->where('link_id', '=', $result_id)->where('maximum_id', '=', $mo->maximum_id)->execute();
					}
					continue;
				}
				$order_data = json_decode($mo->order_data, true);
				if (is_array($order_data) && isset($order_data['maximums'])) {
					foreach($order_data['maximums'] as $om) {
						$m = ['maximum_id'=>$mo->maximum_id, 'num'=>1];
						if (isset($om['day'])) $m['day'] = $om['day'];
						if (isset($om['time'])) $m['time'] = $om['time'];
						$maximum_list[] = $m;
					}
				}
				else {
					$num = 0;
					// 内訳価格指定枠数の場合
					if (isset($order_data['items'])) {
						foreach ($order_data['items'] as $om) {
							if (isset($om['maximum_num'])) {
								$num += $om['maximum_num'];
							}
						}
					}
					if ($num == 0) $num = $mo->num;
					$m = ['maximum_id'=>$mo->maximum_id, 'num'=>$num];
					if ($mo->day != '') $m['day'] = $mo->day;
					if ($mo->time != '') $m['time'] = $mo->time;
					$maximum_list[] = $m;
				}
				$pairs = ['order_status_cd'=>'03', 'upd_time'=>date('Y-m-d H:i:s'), 'upd_user'=>$user_id];
				if ($delete) $pairs['delete_flg'] = 1;
				DB::update('t_maximum_order')->set($pairs)->where('link_id', '=', $result_id)->where('maximum_id', '=', $mo->maximum_id)->execute();		
			}
			foreach($maximum_list as $m) {
				$this->restore_maximum_only($bot_id, $m);
			}
		}
		else {
			foreach($maximum_list as $m) {
				$this->restore_maximum($bot_id, $m, $result_id, $user_id, $delete);
			}
		}

		if ($delete) {
			$orm = ORM::factory('inquiryresult', $result_id);
			$orm->status_cd = '03';
			$orm->delete_flg = 1;
			$orm->upd_time = date('Y-m-d H:i:s');
			$orm->upd_user = $user_id;
			$orm->save();
		}
		else {
			$orm = ORM::factory('inquiryresult', $result_id);
			$orm->status_cd = '03';
			$orm->upd_time = date('Y-m-d H:i:s');
			$orm->upd_user = $user_id;
			$orm->save();
		}
		DB::delete('t_inquiry_reminder')->where('result_id', '=', $result_id)->execute();
		// payment キャンセル
		$this->cancel_payment($bot_id, $result_id, $user_id);
	}

	function cancel_payment($bot_id, $result_id, $user_id) {
		$orm = ORM::factory('inquiryresult', $result_id);
		if ($orm->result_data != '') {
			$result_data = json_decode($orm->result_data, true);
			if (array_key_exists('payment', $result_data)) {
				if (array_key_exists('TALKAPPI_PAYMENT', $result_data['payment'])) {
          /*
					$sid = strval($result_id);
					if ($orm->ver != NULL) $sid = $sid . '-' . $orm->ver;
          */
					$sid = $this->get_inquiry_result_cd($result_id);
					$payment = $result_data['payment']['TALKAPPI_PAYMENT'];
					$payment_card_setting = $this->get_bot_setting($bot_id, 'json_payment_setting', true);
					if ($payment['vender'] == 'metaps') {
						$payment_setting = $payment_card_setting['metaps'];
						$data = ['IP'=>$payment_setting['ip'], "PASS"=>$payment_setting['ip_pass'], "SID"=>$sid];
						$cancel_url = $payment_setting['cancel_url'] . '?';
						foreach($data as $k=>$v) {
							$cancel_url = $cancel_url . $k . '=' . $v . '&';
						}
						$result = $this->curl_get($cancel_url);
						if (strpos($result, 'C-CHECK:OK') === false) {
							if (strpos($result, 'STATUS=4') === false) {
								throw new Exception('payment_cancel_error');
							}
						}
					}
					else if ($payment['vender'] == 'amazon') {
						$amazonpay_model = new Model_Amazonpay();
						if ($user_id != 0 && $user_id != null) {
							$reason = 'Canceled by user = ' . $user_id;
						}
						else {
							$reason = 'Canceled by member';
						}
						$amazonpay_model->cancel($bot_id, $result_data['payment']['TALKAPPI_PAYMENT']['charge_id'], $result_data['amount'], $reason);
					}
					else if ($payment['vender'] == 'gmo') {
						$param = $result_data['payment']['TALKAPPI_PAYMENT'];
						$engine_result = $this->post_enginehook('service', 'gmo_pay_cancel','', $param);
						if ($engine_result == null || $engine_result['success'] == 'False') {
							throw new Exception('payment_cancel_error');
						}
					}
					else if ($payment['vender'] == 'talkappi') {
						$param = $result_data['payment']['TALKAPPI_PAYMENT'];
						$param['cancel_date'] = date('Y-m-d H:i:s');
						//TODO: 暫定対応。今後各呼出元の修正を行う際に、言語の引き渡すを行う予定
						$param['lang_cd'] = 'ja';
						$engine_result = $this->post_enginehook('service', 'talkappi_pay_cancel','', $param);
						if ($engine_result == null || $engine_result['success'] == 'False') {
							throw new Exception('payment_cancel_error');
						}
					}
				}
				else {
					// 新版フォマード
					$sid = $this->get_inquiry_result_cd($result_id);
					$payment = $result_data['payment'];
					$payment_card_setting = $this->get_bot_setting($bot_id, 'json_payment_setting', true);
					if ($payment['type'] == '2' && $payment['vender'] == 'metaps') {
						$payment_setting = $payment_card_setting['metaps'];
						$data = ['IP'=>$payment_setting['ip'], "PASS"=>$payment_setting['ip_pass'], "SID"=>$sid];
						$cancel_url = $payment_setting['cancel_url'] . '?';
						foreach($data as $k=>$v) {
							$cancel_url = $cancel_url . $k . '=' . $v . '&';
						}
						$result = $this->curl_get($cancel_url);
						if (strpos($result, 'C-CHECK:OK') === false) {
							if (strpos($result, 'STATUS=4') === false) {
								throw new Exception('payment_cancel_error');
							}
						}
					} else if ($payment['type'] == '4' && $payment['vender'] == 'amazon') {
						$amazonpay_model = new Model_Amazonpay();
						if ($user_id != 0 && $user_id != null) {
							$reason = 'Canceled by user = ' . $user_id;
						}
						else {
							$reason = 'Canceled by member';
						}
						try {
							$amazonpay_model->cancel($bot_id, $payment['charge_id'], $payment['amount'], $reason);
						}
						catch(Exception $e) {
							throw new Exception('payment_cancel_error');
						}
					} else if ($payment['type'] == '2' && ($payment['vender'] == 'gmo' || $payment['vender'] == 'talkappi')) {
						$param = $payment;
						$param['cancel_date'] = date('Y-m-d H:i:s');
						//TODO: 暫定対応。今後各呼出元の修正を行う際に、言語の引き渡すを行う予定
						$param['lang_cd'] = 'ja';
						$engine_result = $this->post_enginehook('service', 'talkappi_pay_cancel','', $param);
						if ($engine_result == null || $engine_result['success'] == 'False') {
							throw new Exception('payment_cancel_error');
						}
					}
				}
			}
		}
	}

	function get_inquiry_amount($result_id) {
		$result = ORM::factory('inquiryresult', $result_id);
		if ($result->result_data != '') {
			$result_data = json_decode($result->result_data, true);
			if (array_key_exists('payment', $result_data)) {
				// metaps
				if (array_key_exists('K1', $result_data['payment'])) {
					return $result_data['payment']['K1'];
				}
			}
		}
		return 0;
		/*
		$result_entry = ORM::factory('inquiryresultentry')->where('result_id', '=', $result_id)->find_all();
		foreach($result_entry as $re) {
			if ($re->entry_extra_info != '') {
				$entry_extra_info = json_decode($re->entry_extra_info, true);
				if (array_key_exists('total', $entry_extra_info)) {
					return $entry_extra_info['total'];
				}
			}
		}
		return '';
		*/
	}

	function update_order_info($result_id, $result_string, $mail) {
		$sql = "UPDATE t_maximum_order SET order_info=:result_string, order_mail=:mail WHERE link_id=:result_id";
		$params = [
			':result_id' => $result_id,
			':result_string' => $result_string,
			':mail' => $mail,
		];
		$query = DB::query(Database::UPDATE, $sql);
		$query->parameters($params);
		$query->execute();
	}
	
	function get_max_order_no() {
		$date_str = date('Ymd');
		$sql = "SELECT order_no FROM t_maximum_order WHERE order_no LIKE 'TMS" . $date_str . "%' ORDER BY order_no DESC LIMIT 0, 1";
		$query = DB::query(Database::SELECT, $sql);
		$results = $query->execute()->as_array();
		if (count($results) == 0) {
			return "TMS" . date('Ymd') . '0001';
		}
		else {
			$no = substr($results[0]['order_no'], 11);
			return "TMS" . date('Ymd') . sprintf("%04d", intval($no) + 1);
		}
	}
	
	function check_maximum_remains($bot_id, $entries, $use_list) {
		$remains = $this->get_maximum_remains($bot_id, $entries);
		foreach($use_list as $k=>$v) {
			foreach($remains as &$remain) {
				foreach($remain as &$maxi) {
					if ($maxi['maximum_id'] == $k) {
						if ($maxi['remains']['span'] == 'all') {
							if (intval($maxi['remains']['period']['maximum']) < intval($v)) {
								return $remains;
							}
							$maxi['remains']['period']['maximum'] = $maxi['remains']['period']['maximum'] - intval($v);
						}
						else if ($maxi['remains']['span'] == '1d') {
							if (array_key_exists($v['day'], $maxi['remains']['period'])) {
								if (intval($maxi['remains']['period'][$v['day']]["maximum"]) < intval($v['num'])) {
									return $remains;
								}
								$maxi['remains']['period'][$v['day']]["maximum"] = $maxi['remains']['period'][$v['day']]["maximum"] - intval($v['num']);
							}
						}
						else if ($maxi['remains']['span'] == '1h') {
							if (array_key_exists($v['day'], $maxi['remains']['period']) && array_key_exists($v['time'], $maxi['remains']['period'][$v['day']])) {
								if (intval($maxi['remains']['period'][$v['day']][$v['time']]["maximum"]) < intval($v['num'])) {
									return $remains;
								}
								$maxi['remains']['period'][$v['day']][$v['time']]["maximum"] = $maxi['remains']['period'][$v['day']][$v['time']]["maximum"] - intval($v['num']);
							}
						}
					}
				}
			}
		}
		return;
	}

	function get_inquiry_max_result_cd($inquiry_id)
	{
		$inquiry = ORM::factory('inquiry', $inquiry_id);
		$inquiry_data = json_decode($inquiry->inquiry_data, true);
		$receiption_id_prefix = '';
		if (is_array($inquiry_data) && array_key_exists('receiption_id_prefix', $inquiry_data)) {
			$receiption_id_prefix = $inquiry_data['receiption_id_prefix'];
		}
		if ($receiption_id_prefix == '') return null;
		$receiption_id_prefix = strtoupper($receiption_id_prefix);
		$result = preg_match('/N{2,}/', $receiption_id_prefix, $m);
		$nformat = $m[0];
		if (!$result) return null;
		$ptr = strpos($receiption_id_prefix, $nformat);
		$prefix_head = str_replace($nformat, '', $receiption_id_prefix);
		$sql = "SELECT MAX(result_cd) AS no FROM t_inquiry_result WHERE inquiry_id = :inquiry_id AND result_cd LIKE :result_cd";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':inquiry_id' => $inquiry_id,
				':result_cd'=>$prefix_head . '%'
		));
		$results = $query->execute()->as_array();
		if ($results[0]['no'] == NULL) {
			$no = 1;
		}
		else {
			$no = intval(substr($results[0]['no'], $ptr, strlen($nformat)));
			$no++;
		}
		$cd = str_replace($nformat, sprintf("%0" . strlen($nformat) . "d", $no), $receiption_id_prefix);
		$cd = str_replace('YYYYMMDD', str_replace('-', '', date('Y-m-d')), $cd);
		$cd = str_replace('YYYYMM', str_replace('-', '', date('Y-m')), $cd);
		$cd = str_replace('YYMM', substr(str_replace('-', '', date('Y-m')), 2), $cd);
		return $cd;
	}

	function get_inquiry_result_cd($result_id) {
		$result = ORM::factory('inquiryresult', $result_id);
		if ($result->result_cd == null) return $result->id;
		return $result->result_cd;
	}

	function get_inquiry_result_tpl($bot_id, $result, $result_string, $result_tags, $type, $lang_cd='ja') {
		$result_id = $this->get_inquiry_result_cd($result->id);
		$content = $this->get_bot_tpl_message($bot_id, 'inquiry.result', $lang_cd);
		$content = str_replace('{start_time}', substr($result->start_time,0, 16), $content);
		$content = str_replace('{end_time}', substr($result->end_time, 11, 5), $content);
		$content = str_replace('{reception_time}', substr($result->end_time, 0, 16), $content);
		$diff = round((strtotime($result->end_time) - strtotime($result->start_time)) / 60, 1);
		$content = str_replace('{duration}', $diff, $content);
		$content = str_replace('{question_answer}', trim($result_string), $content);
		$content = str_replace('{reception_id}', $result_id, $content);
		if ($type == 'confirm') {
			if (array_key_exists('payment', $result_tags)) {
				$talkappi_payment = $result_tags['payment']; 
				if (isset($result_tags['total_amount']) && $result_tags['total_amount'] > 0 && $talkappi_payment != NULL && isset($talkappi_payment['vender']) && $talkappi_payment['vender'] == 'talkappi') {
					$info = $this->get_bot_tpl_message($bot_id, 'talkappipay.info', $lang_cd, true);
					$content = $content . PHP_EOL . PHP_EOL . str_replace('\n', PHP_EOL, $info['chargeback']);
				}
			}
			$inquiry_data = json_decode($result->result_data, true);
			if (is_array($inquiry_data) && isset($inquiry_data['receipt_url'])) {
				$receipt_url = $inquiry_data['receipt_url'];
				if ($receipt_url != '') {
					$inquiry_receipt = $this->get_bot_tpl_message($bot_id, 'inquiry.receipt', $lang_cd);
					$content = $content . PHP_EOL . str_replace('{receipt_url}', $receipt_url, $inquiry_receipt);
				}
			}
		}
		return $content;
	}

	function get_inquiry_mail_subject($bot_id, $message_cd, $inquiry_id, $result_id, $kind, $lang_cd, $title='') {
		$result_id = $this->get_inquiry_result_cd($result_id);
		$title_category = $this->get_bot_tpl_message($bot_id, 'mail.title_catetory', $lang_cd);
		$title_category = json_decode($title_category, true);
		$kind_text = '';
		if ($kind != '') $kind_text = $title_category[$kind];
		$content = $this->get_bot_mal_message($bot_id, $message_cd, $lang_cd);
		if ($content != null) {
			$subject = $content['subject'];
			//エンドユーザーへ送信
			if ($title === '') {
				$subject = $subject . $kind_text;
				$subject = str_replace('{reception_id}', $result_id, $subject);
			}
			//管理者へ送信
			else {
				$subject = str_replace('{reception_id}', $result_id, $subject);
				$subject = str_replace('{inquiry_name}', $title, $subject);
				$subject = str_replace('{inquiry_status}', $kind_text, $subject);
			}
			return $subject;
		}
		else {
			return '';
		}
	}

	function get_inquiry_result_html($result_id, $lang_cd=null) {

		$result_html = '<table class="td" cellspacing="0" cellpadding="6" border="1" style="color: #737373; border-collapse: collapse; border-color: #e4e4e4; vertical-align: middle; width: 100%; font-family: \'Helvetica Neue\', Helvetica, Roboto, Arial, sans-serif;"><tbody>';
		$result_html_key = '<tr class="order_item"><td class="td" style="color: #737373; border-collapse: collapse; border-color: #e4e4e4; padding: 12px; text-align: left; vertical-align: middle; font-family: \'Helvetica Neue\', Helvetica, Roboto, Arial, sans-serif; word-wrap: break-word;" align="left">';
		$result_html_value = '<td class="td" style="color: #737373; border-collapse: collapse; border-color: #e4e4e4; padding: 12px; text-align: left; vertical-align: middle; font-family: \'Helvetica Neue\', Helvetica, Roboto, Arial, sans-serif;" align="left">';
	 
		$inquiry_result = ORM::factory('inquiryresult', $result_id);
		if ($lang_cd == null) $lang_cd = $inquiry_result->lang_cd;
		$inquiry_id = $inquiry_result->inquiry_id;
		$entry_result = ORM::factory('inquiryresultentry')->where('result_id', '=', $result_id)->find_all();
		$entry_result_arr = [];
		foreach($entry_result as $er) {
			$entry_result_arr[$er->no] = $er;
		}
		$entries = ORM::factory('inquiryentry')->where('inquiry_id', '=', $inquiry_id)->where('lang_cd', '=', $lang_cd)->find_all();
		$entry_arr = [];
		foreach($entries as $e) {
			$entry_arr[$e->no] = $e;
		}
		$inquiry_section = ORM::factory('inquirysection')->where('inquiry_id', '=', $inquiry_id)->where('lang_cd', '=', $lang_cd)->order_by('no')->find_all();
		$entry_sort = [];
		foreach($inquiry_section as $is) {
			$entry_sort = array_merge($entry_sort, explode(',', $is->entries));
		}
		$num = 1;
		$result_sum = '';
		foreach($entry_sort as $no) {
			if (!array_key_exists($no, $entry_result_arr)) continue;
			if ($entry_arr[$no]->entry_type_cd == 'frs') {
				$entry_title = str_replace('¥', '', preg_replace('/\/\?.*\?\//', '', strip_tags($entry_arr[$no]->title)));
			}
			else {
				$entry_title = strip_tags($entry_arr[$no]->title);
			}
			if ($entry_result_arr[$no]->entry_extra_info == '') {
				$result_html = $result_html . $result_html_key . $entry_title . '</td>' . $result_html_value . nl2br($entry_result_arr[$no]->entry_data) . '</td></tr>';
			}
			else {
				$entry_extra_info = json_decode($entry_result_arr[$no]->entry_extra_info, true);
				if (array_key_exists('coupon', $entry_extra_info)) {
					$result_html = $result_html . $result_html_key . $entry_title . '</td>' . $result_html_value . nl2br($entry_result_arr[$no]->entry_data . strip_tags(str_replace('<br>', PHP_EOL, '-¥' . number_format($entry_extra_info['coupon']['discount_result'])))) . '</td></tr>';
				}
				else if (array_key_exists('detail', $entry_extra_info)) {
					$result_html = $result_html . $result_html_key . $entry_title . '</td>' . $result_html_value . nl2br($entry_result_arr[$no]->entry_data . strip_tags(str_replace('<br>', PHP_EOL, $entry_extra_info['detail']))) . '</td></tr>';
				}
				else {
					$result_html = $result_html . $result_html_key . $entry_title . '</td>' . $result_html_value . nl2br($entry_result_arr[$no]->entry_data) . '</td></tr>';
				}
				if (array_key_exists('total', $entry_extra_info)) {
					$result_sum = nl2br(strip_tags($entry_extra_info['total']));
				}
			}
		}

		//合計金額分解の一時的な対応
		if ($result_sum != '') {
			$result_sum_array = explode(":", $result_sum);
			if (count($result_sum_array) !== 2) {
				$result_sum_array = explode("：", $result_sum);
			}
			if (count($result_sum_array) == 2) {
				$result_html = $result_html . $result_html_key . $result_sum_array[0] . '</td>' . $result_html_value . $result_sum_array[1] . '</td></tr>';
			}
		}

		$result_html = $result_html . '</tbody></table>';
		return $result_html;
	}

	function get_inquiry_result_text($result_id, $lang_cd=null) {
		$content = '';
		$tags = [];
		$inquiry_result = ORM::factory('inquiryresult', $result_id);
		if ($lang_cd == null) $lang_cd = $inquiry_result->lang_cd;
		$inquiry_id = $inquiry_result->inquiry_id;
		$entry_result = ORM::factory('inquiryresultentry')->where('result_id', '=', $result_id)->find_all();
		$entry_result_arr = [];
		foreach($entry_result as $er) {
			$entry_result_arr[$er->no] = $er;
		}
		$entries = ORM::factory('inquiryentry')->where('inquiry_id', '=', $inquiry_id)->where('lang_cd', '=', $lang_cd)->find_all();
		$entry_arr = [];
		foreach($entries as $e) {
			$entry_arr[$e->no] = $e;
		}
		$inquiry_section = ORM::factory('inquirysection')->where('inquiry_id', '=', $inquiry_id)->where('lang_cd', '=', $lang_cd)->order_by('no')->find_all();
		$entry_sort = [];
		foreach($inquiry_section as $is) {
			$entry_sort = array_merge($entry_sort, explode(',', $is->entries));
		}
		$num = 1;
		//$maximum_amount_label = $this->get_bot_tpl_message($inquiry_result->bot_id, 'maximum_amount_label', $lang_cd, true);
		//$tax_label = $maximum_amount_label['LABEL_TAX'];
		foreach($entry_sort as $no) {
			if (!array_key_exists($no, $entry_result_arr)) continue;
			if ($entry_arr[$no]->entry_type_cd == 'frs') {
				$entry_title = str_replace('¥', '', preg_replace('/\/\?.*\?\//', '', strip_tags($entry_arr[$no]->title)));
			}
			else {
				$entry_title = strip_tags($entry_arr[$no]->title);
			}
			$content = $content . ($num++) . '.' . $entry_title . PHP_EOL;
			$content = $content . $entry_result_arr[$no]->entry_data . PHP_EOL;
			if ($entry_arr[$no]->entry_type_cd == 'txt' && $entry_arr[$no]->input_rules != '') {
				$input_rules = json_decode($entry_arr[$no]->input_rules, true);
				if (array_key_exists('type', $input_rules)) {
					$tags[$input_rules['type']] = $entry_result_arr[$no]->entry_result;
				}
			}
			if ($entry_arr[$no]->entry_type_cd == 'spl' && $entry_arr[$no]->input_rules != '') {
				$input_rules = json_decode($entry_arr[$no]->input_rules, true);
				if (array_key_exists('type', $input_rules)) {
					if (!isset($tags[$input_rules['type']])) $tags[$input_rules['type']] = $entry_result_arr[$no]->entry_result;
				}
			}
			if ($entry_result_arr[$no]->entry_extra_info != '') {
				$entry_extra_info = json_decode($entry_result_arr[$no]->entry_extra_info, true);
				if (array_key_exists('coupon', $entry_extra_info)) {
					$content = $content . strip_tags(str_replace('<br>', PHP_EOL, '-¥' . number_format($entry_extra_info['coupon']['discount_result']))) . PHP_EOL;
				}
				if (array_key_exists('detail', $entry_extra_info)) {
					if (trim($entry_extra_info['detail']) != '') $content = $content . strip_tags(str_replace('<br>', PHP_EOL, $entry_extra_info['detail'])) . PHP_EOL;
				}
				if (array_key_exists('total', $entry_extra_info)) {
					$content = $content . strip_tags(str_replace('<br>', PHP_EOL, $entry_extra_info['total'])) . PHP_EOL;
					$tags['total_amount'] = strip_tags(str_replace('<br>', PHP_EOL, $entry_extra_info['total']));
				}
				if (array_key_exists('total_raw', $entry_extra_info)) {
					$tags['total_amount'] = $entry_extra_info['total_raw'];
				}
			}
			if ($entry_arr[$no]->entry_type_cd == 'opt') {
				$input_rules = json_decode($entry_arr[$no]->input_rules, true);
				if (array_key_exists('type', $input_rules)) {
					if ($input_rules['type'] == 'payment') {
						$tags['payment'] = $input_rules['options'][$entry_result_arr[$no]->entry_result - 1];
					}
				}
			}
			$content = $content . PHP_EOL;
		}
		return [$content, $tags];
	}

	function send_inquiry_result_mail($result_id, $type, $category, $div) {
		return $this->_send_inquiry_mail($result_id, NULL, $type, $category, $div);
	}
	function send_inquiry_order_mail($order_id, $type,  $category) {
		return $this->_send_inquiry_mail(NULL, $order_id, $type, $category);
	}
	private function _send_inquiry_mail($result_id, $order_id, $type, $category, $div = 3) {
		$member_mail = '';
		if ($result_id == NULL) {
			$order = ORM::factory('maximumorder', $order_id);
			$result_id = $order->link_id;
			$member_mail = $order->order_mail;
		}
		list($result_txt, $tags) = $this->get_inquiry_result_text($result_id);
		if ($member_mail == '' && isset($tags['mail'])) $member_mail = $tags['mail'];
		$inquiry_result = ORM::factory('inquiryresult', $result_id);
		$bot_id = $inquiry_result->bot_id;
		$inquiry_id = $inquiry_result->inquiry_id;
		$result_txt = $this->get_inquiry_result_tpl($bot_id, $inquiry_result, $result_txt, $tags, $type, $inquiry_result->lang_cd);
		$inquiry_orm = ORM::factory('inquiry', $inquiry_id);

		$inquiry_desc_member = ORM::factory('inquirydescription')->where('inquiry_id', '=', $inquiry_id)->where('lang_cd', '=', $inquiry_result->lang_cd)->find();
		$param = ['bot_id'=>$bot_id, 'type'=>'04', 'configKey'=>'inquiry', 'link_id'=>$result_id, 'receiver'=>$member_mail, 'lang_cd'=>$inquiry_result->lang_cd];
		if ($type == 'confirm') {
			$param['message_cd'] = $inquiry_orm->member_mail_template;
		}
		else if ($type == 'cancel') {
			if ($inquiry_result->upd_user == null) {
				$param['message_cd'] = 'inquiry.cancel_complete';
			}
			else {
				$param['message_cd'] = 'inquiry.cancel_complete_by_admin';
			}
		}
		else if ($type == 'remind') {
			$param['message_cd'] = 'inquiry.remind';
		}
		else if ($type == 'thanks') {
			$param['message_cd'] = $category;
		}
		else if ($type == 'pay') {
			$param['message_cd'] = 'inquiry.2step_pay';
		}
		else if ($type == 'cancelpay') {
			$param['message_cd'] = 'inquiry.2step_pay_cancel';
		}
		else if ($type == 'paycomplete') {
			$param['message_cd'] = 'inquiry.2step_pay_complete';
		}
		else if ($type == 'payovertime') {
			$param['message_cd'] = 'inquiry.2step_pay_overtime_cancel';
		}
		$inquiry = ORM::factory('inquiry', $inquiry_id);
		if ($inquiry->member_mail_from != '') {
			$param['sender'] = $inquiry->member_mail_from;
		}
		if ($inquiry->member_mail_replyto != '') {
			$param['replyto'] = $inquiry->member_mail_replyto;
		}
		$cancel_url = '';
		$pay_url = '';
		$inquiry_receipt = '';
		$result_data = json_decode($inquiry_result->result_data, true);
		$inquiry_url = $this->get_env('inquiry_url');
		if ($type == 'confirm') {
			$token_data = [];
			$token_data['facility_cd'] = $inquiry_result->scene_cd;
			if ($inquiry->renew_time == NULL || $inquiry_result->start_time < $inquiry->renew_time) {
				$token_data['inquiry_id'] = $inquiry_id;
				$token_data['id'] = $inquiry_result->member_id;
			}
			else {
				$token_data['id'] = $inquiry_id;
				$token_data['member_id'] = $inquiry_result->member_id;
			}
			$token_data['bot_id'] = $bot_id;
			$token_data['channel'] = 'wb';
			$token_data['line_id'] = '';
			$token_data['lang_cd'] = $inquiry_result->lang_cd;
			$token_data['result_id'] = $inquiry_result->id;
			$token_data['mail'] = $member_mail;
			$token_data['content'] = $result_txt;
			if (substr($inquiry_url, strlen($inquiry_url) - 1) == '/') {
				$cancel_url = $inquiry_url . 'inquiry/cancel?id=' . $this->set_token_data($inquiry_result->member_id, $token_data, '1 year');
			}
			else {
				$cancel_url = $inquiry_url . '/cancel?id=' . $this->set_token_data($inquiry_result->member_id, $token_data, '1 year');
			}
		}
		else if ($type == 'pay') {
			if (substr($inquiry_url, strlen($inquiry_url) - 1) == '/') {
				$pay_url = $inquiry_url . 'inquiry/pay?id=' . $result_data['2_step_token'];
			}
			else {
				$pay_url = $inquiry_url . '/pay?id=' . $result_data['2_step_token'];
			}
		}

		$result_sum = '';
		$inquiry_result_entry = ORM::factory('inquiryresultentry')->where('result_id', '=', $result_id)->find_all();
		foreach($inquiry_result_entry as $re) {
			if ($re->entry_extra_info =='') continue;
			$entry_extra_info = json_decode($re->entry_extra_info, true);
			if (is_array($entry_extra_info) && array_key_exists('total', $entry_extra_info)) {
				$result_sum = $entry_extra_info['total'];
				$result_sum = str_replace('合計金額:', '', $result_sum);
				break;
			}
		}

		$param['params'] = 
		[
			'inquiry.id'=>$result_id,
			'reception_id'=>$inquiry_result->id,
			'inquiry.title'=>$inquiry_desc_member->title,
			'inquiry.title_html'=>nl2br($inquiry_desc_member->title),
			'facility_name'=>$this->get_bot_txt_message($bot_id, 'bot_name', $inquiry_result->lang_cd),
			'facility_logo'=>$this->get_bot_img_message($bot_id, 'facility_logo_banner', $inquiry_result->lang_cd),
			'cancel_url'=>$cancel_url,
			'pay_url'=>$pay_url,
			'reception_time'=>substr($inquiry_result->end_time, 0, 16),
			'inquiry.result'=>$result_txt,
			'inquiry.receipt'=>$inquiry_receipt,
			'result_html'=>$this->get_inquiry_result_html($result_id)
		];
		if (isset($tags['name_separate'])) {
			$d = json_decode($tags['name_separate'], true);
			$param['params']['send_member_name'] = $d['last_name'] . ' ' . $d['first_name'];
		}
		else if (isset($tags['name_full'])) {
			$d = json_decode($tags['name_full'], true);
			$param['params']['send_member_name'] = $d['full_name'];
		}
		if (isset($tags['tel'])) {
			$param['params']['send_member_tel'] = $tags['tel'];
		}
		if (isset($tags['mail'])) {
			$param['params']['send_member_mail'] = $tags['mail'];
		}
		if ($type == 'pay' || $type == 'payovertime') {
			$param['params']['pay_limit'] = $result_data['payment']['limit'];
		}
		$ret = true;
		$temp_category = $category;
		if ($category == 'cancel') {
			$temp_category = '';
		}
		$param['title'] = $this->get_inquiry_mail_subject($bot_id, $param['message_cd'], $inquiry_id, $result_id, $temp_category, $param['lang_cd']);
		if ($div == 1 || $div == 3) {
			// send to member
			if ($member_mail != '') {
				$data = $this->post_enginehook('service', 'sendmail','', $param);
				if ($data == null || $data['success'] == 'False') {
					$ret = false;
					Log::instance()->add(Log::DEBUG, 'sendmembermail failure=' . json_encode($data));
				}
			}
		}
		$action_user = $this->action_mail($result_id);
		if (($div == 2 || $div == 3) && ($inquiry->mail_users != '' || count($action_user) > 0)) {
			// send to user
			$inquiry_desc_user = ORM::factory('inquirydescription')->where('inquiry_id', '=', $inquiry_id)->where('lang_cd', '=', 'ja')->find();
			$mail_users = $inquiry->mail_users == ''?[]:explode(',', $inquiry->mail_users);
			$mail_users = array_unique(array_merge($mail_users, $action_user));
			$users = ORM::factory('user')->where('user_id', 'IN', $mail_users)->find_all();
			$param['params']['inquiry.title'] = $inquiry_desc_user->title;
			$param['params']['inquiry.title_html'] = nl2br($inquiry_desc_user->title);
			$param['params']['facility_name'] = $this->get_bot_txt_message($bot_id, 'bot_name', 'ja');
			$param['params']['facility_logo'] = $this->get_bot_img_message($bot_id, 'facility_logo_banner', 'ja');

			$inquiry_admin_url = $this->get_env('admin_url');
			$facility_cd = $this->get_bot_setting($bot_id, 'default_scene_cd');
			$admin_url = $inquiry_admin_url . "admininquiry/inquiryresultdetail?id=" . $result_id . "&facility_cd=" . $facility_cd;
			//一旦不要　2023/12/15
			//$admin_cancel_url = $inquiry_admin_url . 'admin/maximumorders?id=' . $order->maximum_id;
			$param['params']['admin_url'] = $admin_url;
			//$param['params']['admin_cancel_url'] = $admin_cancel_url;

			if ($type == 'confirm') {
				$param['message_cd'] = $inquiry->user_mail_template;
			}
			else if ($type == 'cancel') {
				if ($inquiry_result->upd_user == null) {
					$param['message_cd'] = 'inquiry.cancel_complete_admin';
				}
				else {
					$param['message_cd'] = 'inquiry.cancel_complete_by_admin_to_user';
				}
			}
			$param['lang_cd'] = 'ja';
			$param['title'] = $this->get_inquiry_mail_subject($bot_id, $param['message_cd'], $inquiry_id, $result_id, $category, $param['lang_cd'], $inquiry->inquiry_name);
			foreach($users as $user) {
				$param['receiver'] = $user->email;
				$data = $this->post_enginehook('service', 'sendmail','', $param);
				if ($data['success'] == 'False') {
					$ret = false;
					Log::instance()->add(Log::DEBUG, 'sendusermail failure=' . json_encode($data));
				}
			}
		}
		return $ret;
	}

	function action_mail($result_id) {
		$mail_users = [];
		$result = ORM::factory('inquiryresult', $result_id);
		$inquiry_id = $result->inquiry_id;
		$lang_cd = $result->lang_cd;
		$inquiry_action = ORM::factory('inquiryentryaction')->where('inquiry_id', '=', $inquiry_id)->where('lang_cd', '=', $lang_cd)->order_by('no')->find_all();
		if (count($inquiry_action) == 0) return $mail_users;
		$entry_actions = [];
		foreach($inquiry_action as $entry) {
			$entry_actions[$entry->no] = json_decode($entry->actions, true);
		}
		$result_entry = ORM::factory('inquiryresultentry')->where('result_id', '=', $result_id)->find_all();
		foreach($result_entry as $entry) {
			$real_text = $entry->entry_data;
			if (isset($entry_actions[$entry->no])) {
				foreach($entry_actions[$entry->no] as $action) {
					if ($action['connection'] == 'OR') {
						$fit = false;
					}
					else {
						$fit = true;
					}
					foreach($action['option'] as $option) {
						if ($option['entry_no'] != $entry->no) continue;
						if ($option['condition'] == 'equal') {
							if ($action['connection'] == 'OR' && $real_text == $option['value']) {
								$fit = true;
								break;
							}
							if ($action['connection'] == 'AND' && $real_text != $option['value']) {
								$fit = false;
								break;
							}
						}
						else if ($option['condition'] == 'not_equal') {
							if ($action['connection'] == 'OR' && $real_text != $option['value']) {
								$fit = true;
								break;
							}
							if ($action['connection'] == 'AND' && $real_text == $option['value']) {
								$fit = false;
								break;
							}
						}
						else if ($option['condition'] == 'include') {
							if ($action['connection'] == 'OR' && strpos($real_text, $option['value']) !== false) {
								$fit = true;
								break;
							}
							if ($action['connection'] == 'AND' && strpos($real_text, $option['value']) === false) {
								$fit = false;
								break;
							}
						}
						else if ($option['condition'] == 'not_include') {
							if ($action['connection'] == 'OR' && strpos($real_text, $option['value']) === false) {
								$fit = true;
								break;
							}
							if ($action['connection'] == 'AND' && strpos($real_text, $option['value']) !== false) {
								$fit = false;
								break;
							}
						}
						else if ($option['condition'] == 'equal_or_greater') {
							if ($action['connection'] == 'OR' && $real_text >= $option['value']) {
								$fit = true;
								break;
							}
							if ($action['connection'] == 'AND' && $real_text < $option['value']) {
								$fit = false;
								break;
							}
						}
						else if ($option['condition'] == 'greater') {
							if ($action['connection'] == 'OR' && $real_text > $option['value']) {
								$fit = true;
								break;
							}
							if ($action['connection'] == 'AND' && $real_text <= $option['value']) {
								$fit = false;
								break;
							}
						}
						else if ($option['condition'] == 'equal_or_smaller') {
							if ($action['connection'] == 'OR' && $real_text <= $option['value']) {
								$fit = true;
								break;
							}
							if ($action['connection'] == 'AND' && $real_text > $option['value']) {
								$fit = false;
								break;
							}
						}
						else if ($option['condition'] == 'smaller') {
							if ($action['connection'] == 'OR' && $real_text < $option['value']) {
								$fit = true;
								break;
							}
							if ($action['connection'] == 'AND' && $real_text >= $option['value']) {
								$fit = false;
								break;
							}
						}
					}
					if ($fit) {
						foreach($action['actions'] as $ac) {
							if ($ac['action'] == 'user_mail') {
								$mail_users = array_merge($mail_users, $ac['param']['mail_users']);
							}
						}
					}
				}
			}
		}
		return $mail_users;
	}
}

?>
