<?php defined('SYSPATH') OR die('No direct script access.');

class Model_Notice extends ORM
{
	protected $_table_name = 't_notice';
	protected $_primary_key = 'notice_id';


	public function find_all_notices($bot_id){
		$sql = 'SELECT n.notice_id, n.notice_name, n.notice_type_cd, n.notice_tag_cd, n.notice_status_cd,
				n.bot_id, n.create_time, n.display_start_date, n.display_end_date, n.upd_time, n.delete_flg,
				u.user_id, u.name, GROUP_CONCAT(d.lang_cd) AS languages
			FROM t_notice n
			LEFT JOIN t_notice_description d ON n.notice_id = d.notice_id
			JOIN t_user u ON n.upd_user = u.user_id
			WHERE (n.bot_id = :bot_id OR n.bot_id = 0) AND n.delete_flg = 0
			GROUP BY n.notice_id
			ORDER BY n.notice_id ASC';
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(':bot_id' => $bot_id));
		return $query->execute()->as_array();
	}

	public function find_notices_on_very($bot_id){
		$sql = "SELECT vs.lang_cd, vs.value
			FROM t_very_settings vs
			WHERE vs.bot_id = :bot_id AND (vs.setting = 'notice' OR vs.setting = 'half_modal')";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(':bot_id' => $bot_id));
		$results = $query->execute()->as_array();

		$very_notices_by_lang = [];
		foreach ($results as $result) {
			$lang_cd = $result['lang_cd'];
			$notice_ids = json_decode($result['value'], true);
		
			if (!empty($notice_ids)) {
				foreach ($notice_ids as $notice) {
					if ($notice['notice_id'] == '') continue;
					$very_notices_by_lang[$lang_cd][] = $notice['notice_id'];
				}
			}
		}
		return $very_notices_by_lang;
	}

	public function has_authority_show_notice($bot_id, $notice_id){
		$orm = $this->where('notice_id', '=', $notice_id)->find();
		if (!$orm) return false;
		return $orm->bot_id == $bot_id;
	}

	public function find_notice($notice_id){
		$notice = $this->where('notice_id', '=', $notice_id)->find();
		return $notice;
	}

	public function new_notice($post, $bot_id, $user_id){
		$this->notice_name = $post['notice_name'];
		$this->notice_tag_cd = $post['notice_tag_cd'];
		$this->notice_type_cd = $post['notice_type_cd'];
		$this->notice_status_cd = $post['notice_status_cd'];
		$display_date = $this->_format_display_date($post);
		$this->display_start_date = $display_date['start_date'];
		$this->display_end_date = $display_date['end_date'];
		$this->bot_id = $bot_id;
		$this->upd_user = $user_id;
		return $this->save();
	}

	public function update_notice($post, $notice_id, $bot_id, $user_id){
		$display_date = $this->_format_display_date($post);

		DB::update('t_notice')
			->set(array('notice_name'=> $post['notice_name'],
					'notice_tag_cd' => $post['notice_tag_cd'],
					'notice_type_cd' => $post['notice_type_cd'],
					'notice_status_cd' => $post['notice_status_cd'],
					'display_start_date' => $display_date['start_date'],
					'display_end_date' => $display_date['end_date'],
					'bot_id' => $bot_id,
					'upd_user' => $user_id,
					'upd_time'=> date('Y-m-d H:i:s',time())
					))
			->where('notice_id', '=', $notice_id)
			->execute();
	}

	public function update_time_and_user($notice_id,$user_id){
		DB::update('t_notice')
		->set(array(
				'upd_user' => $user_id,
				'upd_time'=> date('Y-m-d H:i:s',time())
		))
		->where('notice_id', '=', $notice_id)
		->execute();
	}

	public function delete_notice($notice_id){
		DB::update('t_notice')
			->set(array('delete_flg'=> 1))
			->where('notice_id', '=', $notice_id)
			->execute();
	}


	private function _format_display_date($post){
		$range = json_decode($post["range"], true);
		if (strlen($range['starTime']) == 4) $range['startTime'] = "0" . $range['startTime'];
		if (strlen($range['endTime']) == 4) $range['endTime'] = "0" . $range['endTime'];
		if ($range['startDate'] == '') {
			$start_date = NULL;
		}
		else {
			$start_date = $range['startDate'] . ' ' . $range['startTime'];
		}
		if ($range['endDate'] == '') {
			$end_date = NULL;
		}
		else {
			$end_date = $range['endDate'] . ' ' . $range['endTime'];
		}
		return ['start_date'=>$start_date, 'end_date'=>$end_date];
	}
}

?>




