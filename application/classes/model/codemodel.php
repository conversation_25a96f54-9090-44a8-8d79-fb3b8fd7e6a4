<?php defined('SYSPATH') OR die('No direct script access.');

class Model_Codemodel extends Model
{
	function get_codes($mst_type, $lang_cd)
	{
		$results = DB::select('mst_cd', 'mst_name')
			->from('m_code')
			->where('mst_type', '=', $mst_type)
			->where('lang_cd', '=', $lang_cd)
			->where('delete_flg', '=', 0)
			->order_by('sort_no')
			->execute();
		return $results->as_array('mst_cd', 'mst_name');
	}
	function get_property_type($class_cd, $lang_cd)
	{
		$results = DB::select('property_type_cd', 'property_type_name')
		->from('m_item_property_type')
		->where('class_cd', '=', $class_cd)
		->where('lang_cd', '=', $lang_cd)
		->where('delete_flg', '=', 0)
		->order_by('property_type_cd')
		->execute();
		return $results->as_array('property_type_cd', 'property_type_name');
	}
	
	function get_property($class_cd, $property_type_cd, $lang_cd)
	{
		$results = DB::select('property_cd', 'property_name')
		->from('m_item_property')
		->where('class_cd', '=', $class_cd)
		->where('property_type_cd', '=', $property_type_cd)
		->where('lang_cd', '=', $lang_cd)
		->where('delete_flg', '=', 0)
		->order_by('property_type_cd')
		->execute();
		return $results->as_array('property_cd', 'property_name');
	}
	
	function get_countries($lang_cd)
	{
		$results = DB::select('country_cd', 'country_name')
		->from('m_country')
		->where('lang_cd', '=', $lang_cd)
		->where('delete_flg', '=', 0)
		->order_by('sort_no')
		->execute();
		return $results->as_array('country_cd', 'country_name');
	}
	
}

?>




