<?php defined('SYSPATH') OR die('No direct script access.');

class Model_Newslettermodel extends Model_Basemodel
{
	function is_newsletter_project_enabled($bot_id, $newsletter_settings=NULL) {
		if ($newsletter_settings == NULL) {
			$bot_setting = $this->get_bot_setting_dict($bot_id);
			$flag = 'flg_newsletter';
			if (isset($bot_setting) && is_array($bot_setting) && count($bot_setting) > 0) {
				if (isset($bot_setting[$flag]) && $bot_setting[$flag] && $bot_setting[$flag] > 0) {
					$newsletter_setting_cd = 'json_newsletter_setting';
					if (isset($bot_setting[$newsletter_setting_cd]) && $bot_setting[$newsletter_setting_cd]){
						$newsletter_settings = json_decode($bot_setting[$newsletter_setting_cd], true);
					}			
				}
			}
		}
		return $newsletter_settings && key_exists('project_management', $newsletter_settings) && $newsletter_settings['project_management'] == 1;	
	}

	function is_sender_address_customizable($bot_id, $newsletter_settings=NULL) {
		if ($newsletter_settings == NULL) {
			$bot_setting = $this->get_bot_setting_dict($bot_id);
			$flag = 'flg_newsletter';
			if (isset($bot_setting) && is_array($bot_setting) && count($bot_setting) > 0) {
				if (isset($bot_setting[$flag]) && $bot_setting[$flag] && $bot_setting[$flag] > 0) {
					$newsletter_setting_cd = 'json_newsletter_setting';
					if (isset($bot_setting[$newsletter_setting_cd]) && $bot_setting[$newsletter_setting_cd]){
						$newsletter_settings = json_decode($bot_setting[$newsletter_setting_cd], true);
					}			
				}
			}
		}
		return $newsletter_settings && key_exists('sender_address_customize', $newsletter_settings) && $newsletter_settings['sender_address_customize'] == 1;	
	}
	
	function get_newsletter_projects($bot_id, $filter_start_date, $filter_end_date, $user_id) {
		$sql = "SELECT a.bot_id, a.project_id, a.project_name, a.description, a.person_in_charge, a.upd_user, a.upd_time, GROUP_CONCAT(u.name) AS person_in_charge_name, 
				(SELECT count(mail_member_id) FROM t_mail_member member WHERE member.project_id=a.project_id) as member_count,
				(SELECT count(tag_no) FROM t_mail_tag tag WHERE tag.project_id=a.project_id) as tag_count,
				(SELECT count(mail_task_id) FROM t_mail_task task WHERE task.project_id=a.project_id) as task_count
				FROM t_mail_project a 
				LEFT JOIN t_user u ON FIND_IN_SET(u.user_id, a.person_in_charge)
				WHERE a.bot_id = :bot_id AND a.delete_flg = 0";
		if (isset($filter_start_date) && $filter_start_date != NULL) {
			$sql .= " AND a.upd_time >= :start_date";
		}
		if (isset($filter_end_date) && $filter_end_date != NULL) {
			$endDate = new DateTime($filter_end_date);
			$filter_end_date_inclusive = $endDate->format('Y-m-d') . ' 23:59:59';
			$sql .= " AND a.upd_time <= :end_date";
		}
		if (isset($user_id) && $user_id != NULL) {
			$sql = $sql . " AND FIND_IN_SET(:user_id, a.person_in_charge) > 0 ";
		}
		$sql = $sql . " GROUP BY a.project_id ";

		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
				':start_date' => $filter_start_date,
				':end_date' => $filter_end_date,
				':user_id' => $user_id,
		));
		$results = $query->execute();
		return $results->as_array();
	}

	/**
	 * Summary of can_user_access_project
	 * @param mixed $project_id valid newsletter project id, not NULL, positive int
	 * @param mixed $user login user
	 * @param mixed $bot_id
	 * @param mixed $newsletter_settings
	 * @return bool
	 */
	public function can_user_access_project($project_id, $user, $bot_id, $newsletter_settings=NULL){
		if (!$newsletter_settings) {
			$json_newsletter_setting = $this->get_bot_setting($bot_id, 'json_newsletter_setting');
			$newsletter_settings = json_decode($json_newsletter_setting, true);
		}
		$checking_user_in_charge = $newsletter_settings && array_key_exists('user_in_charge_required', $newsletter_settings) && $newsletter_settings['user_in_charge_required'] == 1 && $user->auth_all_contents_flg == 0;
		if ($checking_user_in_charge) {
			$projects = $this->get_newsletter_projects($bot_id, null, null, $user->user_id);
			return in_array($project_id, array_column($projects, 'project_id'));
		}		
		return true;
	}

    public function append_unsubscribe_link($body, $task_id, $member_id, $email, $bot_id=0, $sub_status=1) {
		$params = [
			'mail_task_id' => $task_id,
			'email' => $email,
			'member_id' => $member_id,
			'sub_status' => $sub_status,
			'bot_id' => $bot_id,
		];
		$subscribe_config = $this->get_env('subscribe_config');
		$salt = $subscribe_config['encrypt_salt'];
		$apps_url = $this->get_env('apps_url');
		$subscribe_token = bin2hex(openssl_encrypt(json_encode($params), 'AES-256-CBC',  $salt, OPENSSL_RAW_DATA));
		$unsubscribe_link = $apps_url.'subscribe/unsubscribe?token='.$subscribe_token;
		$unsubscribe_link = preg_replace_callback(
			"|(href=\")(https?://[\w!?/+\-_~;.,*&@#$%()'\[\]:;=]+)|",
			function ($m) { return $m[1].($this->post_shorten_url(str_replace('&amp;', '&', $m[2]))); },
			$unsubscribe_link);
		if ($unsubscribe_link) {
			$body .= '<br/>メルマガの配信停止をご希望の場合は、お手数ですが下記のURLよりお手続きをお願いいたします。<br/><a ses:no-track href="'.$unsubscribe_link.'">配信停止リンク</a><br/>配信停止がシステムに反映されるまで、メルマガが送信されることがございますが、何卒ご容赦ください。';
		}
		return $body;
	}
}

?>




