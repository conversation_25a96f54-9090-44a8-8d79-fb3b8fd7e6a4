<?php defined('SYSPATH') or die('No direct script access.');

class Model_Adminbatchmodel extends Model_Basemodel
{

    public function get_batch_executions($bot_id, $filters = array(), $offset = 0, $limit = 50)
    {
        $query = DB::select(
            'be.id',
            'be.bot_id', 
            'be.batch_type',
            'be.batch_name',
            'be.execution_date',
            'be.execution_status',
            'be.execution_priority',
            'be.execution_type',
            'be.scheduled_time',
            'be.actual_start_time',
            'be.actual_end_time',
            'be.duration_seconds',
            'be.exit_code',
            'be.records_processed',
            'be.records_success',
            'be.records_failed',
            'be.error_code',
            'be.error_message',
            'be.retry_count',
            'be.max_retries',
            'be.next_retry_time',
            'be.retry_reason',
            'be.daily_success_count',
            'be.execution_server',
            'be.process_id',
            'be.created_at',
            'be.updated_at',
            'be.created_by',
            'be.updated_by'
        )
        ->from(array('t_batch_executions', 'be'))
        ->where('be.bot_id', '=', $bot_id)
        ->order_by('be.id', 'ASC'); // same as datatable sort setting

        $this->_apply_filters($query, $filters);

        if ($limit > 0) {
            $query->limit($limit)->offset($offset);
        }

        $executions = $query->execute()->as_array();

        // Format data for display
        foreach ($executions as &$execution) {
            $execution['status_info'] = $this->format_execution_status($execution['execution_status']);
            $execution['priority_label'] = $this->format_execution_priority($execution['execution_priority']);
            $execution['type_label'] = $this->format_execution_type($execution['execution_type']);
            $execution['duration_formatted'] = $this->format_duration($execution['duration_seconds']);
        }

        return $executions;
    }

    public function get_batch_executions_count($bot_id, $filters = array())
    {
        $query = DB::select(array(DB::expr('COUNT(*)'), 'count'))
            ->from(array('t_batch_executions', 'be'))
            ->where('be.bot_id', '=', $bot_id);

        $this->_apply_filters($query, $filters);

        $result = $query->execute()->get('count');
        return (int)$result;
    }

    public function get_batch_definitions()
    {
        // jtb
        $query = DB::select(
            'bd.batch_type',
            'bd.batch_name',
            'max_retry_attempts',
            'bd.manual_execution_enabled',
            'bd.is_enabled'
        )
        ->from(array('t_batch_definitions', 'bd'))
        ->where('bd.is_enabled', '=', value: 1)
        ->order_by('bd.batch_type', 'ASC');

        return $query->execute()->as_array();
    }

    public function trigger_manual_execution($bot_id, $batch_type, $batch_name, $max_retry_attempts)
    {
        try {
            // Validate bot_id and batch_type
            if (empty($bot_id) || empty($batch_type)) {
                throw new Exception('Invalid bot_id or batch_type');
            }
            
            $execution_id = $this->_create_manual_execution_record($bot_id, $batch_type, $batch_name, $max_retry_attempts);
            
            return array(
                'success' => true,
                'message' => '手動実行タスクを追加しました、実行されるまでしばらくお待ちください。',
                'execution_id' => $execution_id
            );
        } catch (Exception $e) {
            error_log('Manual execution failed: ' . $e->getMessage());
            return array(
                'success' => false,
                'message' => $e->getMessage()
            );
        }
    }

    private function _create_manual_execution_record($bot_id, $batch_type, $batch_name, $max_retry_attempts)
    {
        $pms_config = $this->get_pms_jtb_dch_hotel_config($bot_id);
        if (!$pms_config) {
            throw new Exception('関連ホテル情報取得に失敗しました。');
        }

        $hotel_id = $pms_config['hotel_id'];
        $execution_id = DB::insert('t_batch_executions')
            ->columns(array(
                'bot_id',
                'hotel_id',
                'batch_type', 
                'batch_name',
                'execution_date',
                'execution_status',
                'execution_priority',
                'execution_type',
                'scheduled_time',
                'max_retries',
                'created_at',
                'updated_at',
                'created_by'
            ))
            ->values(array(
                $bot_id,
                $hotel_id,
                $batch_type,
                $batch_name,
                date('Y-m-d'),
                '0', // Pending
                '1', // Manual priority
                '2', // Manual execution
                date('Y-m-d H:i:s'),
                $max_retry_attempts,
                date('Y-m-d H:i:s'),
                date('Y-m-d H:i:s'),
                'manual'
            ))
            ->execute();

        return $execution_id[0]; // Return the inserted ID
    }

    public function get_batch_type_options($bot_id)
    {
        $options = array('' => __('admin.batch.label.batch_select'));

        // filter valid batch type of current bot_id
        $pms_config = $this->get_pms_jtb_dch_hotel_config($bot_id);
        if ($pms_config) { // jtb
            $jtb_query = DB::select('batch_type', 'batch_name')
                    ->from('t_batch_definitions')
                    ->where('reference_type','=', 'jtb')
                    ->execute();

            foreach ($jtb_query as $row) {
                $options[$row['batch_type']] = $row['batch_name'];
            }
        } else {
            // Add some default batch types for testing when no PMS config is available
            $default_query = DB::select('batch_type', 'batch_name')
                    ->from('t_batch_definitions')
                    ->execute();

            foreach ($default_query as $row) {
                $options[$row['batch_type']] = $row['batch_name'];
            }
        }

        return $options;
    }

    public function get_execution_status_options()
    {
        return array(
            '' => __('admin.batch.label.status'),
            '0' => __('admin.batch.label.status_pending'),
            '1' => __('admin.batch.label.status_running'),
            '2' => __('admin.batch.label.status_success'),
            '3' => __('admin.batch.label.status_failed'),
            '9' => __('admin.batch.label.status_skipped')
        );
    }

    public function format_execution_status($status)
    {
        $status_map = array(
            '0' => array('label' => __('admin.batch.label.status_pending'), 'class' => 'status-pending'),
            '1' => array('label' => __('admin.batch.label.status_running'), 'class' => 'status-running'),
            '2' => array('label' => __('admin.batch.label.status_success'), 'class' => 'status-success'),
            '3' => array('label' => __('admin.batch.label.status_failed'), 'class' => 'status-failed'),
            '9' => array('label' => __('admin.batch.label.status_skipped'), 'class' => 'status-skipped')
        );

        return isset($status_map[$status]) ? $status_map[$status] : array('label' => __('admin.batch.label.status_unknown'), 'class' => 'status-unknown');
    }

    public function format_execution_priority($priority)
    {
        $priority_map = array(
            '1' => '高',
            '2' => '通常', 
            '3' => '低'
        );

        return isset($priority_map[$priority]) ? $priority_map[$priority] : '不明';
    }

    public function format_execution_type($type)
    {
        $type_map = array(
            '1' => '自動実行',
            '2' => '手動実行',
            '3' => '再試行実行'
        );

        return isset($type_map[$type]) ? $type_map[$type] : '不明';
    }

    private function _apply_filters($query, $filters)
    {
        if (!empty($filters['start_date'])) {
            $query->where('be.execution_date', '>=', $filters['start_date']);
        }

        if (!empty($filters['end_date'])) {
            $query->where('be.execution_date', '<=', $filters['end_date']);
        }

        if (!empty($filters['batch_type'])) {
            $query->where('be.batch_type', '=', $filters['batch_type']);
        }

        if (isset($filters['execution_status']) && $filters['execution_status'] !== '') {
            $query->where('be.execution_status', '=', $filters['execution_status']);
        }

        // Add keyword search functionality
        if (!empty($filters['keyword'])) {
            $keyword = '%' . $filters['keyword'] . '%';
            $query->where_open()
                  ->or_where('be.batch_name', 'LIKE', $keyword)
                  ->or_where('be.batch_type', 'LIKE', $keyword)
                  ->or_where('be.error_message', 'LIKE', $keyword)
                  ->or_where('be.error_code', 'LIKE', $keyword)
                  ->or_where('be.retry_reason', 'LIKE', $keyword)
                  ->or_where('be.execution_server', 'LIKE', $keyword)
                  ->or_where('be.created_by', 'LIKE', $keyword)
                  ->or_where('be.updated_by', 'LIKE', $keyword)
                  ->where_close();
        }
    }

    public function format_duration($seconds)
    {
        if (empty($seconds) || $seconds <= 0) {
            return '-';
        }
        
        $hours = floor($seconds / 3600);
        $minutes = floor(($seconds % 3600) / 60);
        $secs = $seconds % 60;
        
        if ($hours > 0) {
            return sprintf('%d時間%d分%d秒', $hours, $minutes, $secs);
        } elseif ($minutes > 0) {
            return sprintf('%d分%d秒', $minutes, $secs);
        } else {
            return sprintf('%d秒', $secs);
        }
    }

    // check for having pms jtb integration
    public function get_pms_jtb_dch_hotel_config($bot_id)
	{
		$sql = "SELECT bot_id, setting_value FROM t_bot_setting WHERE setting_cd = 'json_pms_jtb_dch_setting' AND bot_id = :bot_id";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters([
			':bot_id' => $bot_id,
		]);
		$results = $query->execute()->as_array();
		if (count($results) == 0) return false;

		$setting = json_decode($results[0]['setting_value'], true);
		if ($setting == null) return false;

		$ocp_apim_subscription_key = $setting['ocp_apim_subscription_key'] ?? '';
		$hotel_id = $setting['hotel_id'] ?? '';
		if ($ocp_apim_subscription_key == '' || $hotel_id == '') return false;

		return ['hotel_id' => $hotel_id];
	}
}
