<?php

defined('SYSPATH') OR die('No direct script access.');

class Model_Marketitemmodel extends Model_Basemodel
{
	private $_time_slot_avoid = [
		['start' => '11:00', 'end' => '13:00'],
		['start' => '16:00', 'end' => '19:00'], //start 16:00 
	];

	const DIV = 'tabelog';

	const ITEM_TBL = 't_market_item';

	const DESC_TBL = 't_market_item_description';

	const MAPPING_TBL = 'm_class_code';

	private $_calls_per_second = 2; // 1秒間に2回実行

	private $_item_div = '17'; // Tabelog item division

	private $_admin_model = null;

	private $_big_genre_mapping = null;

	public function __construct()
	{
		parent::__construct();
		$this->_admin_model = new Model_Adminmodel();
		$this->_get_genres_mapping();
	}

	protected function _get_genres_mapping()
	{
		$class_code_table = self::MAPPING_TBL;
		$genre_mapping_sql = "SELECT * FROM $class_code_table AS code WHERE code_div=999920 AND lang_cd='ja' AND parent_cd='';";
		$genre_mapping_query = DB::query(Database::SELECT, $genre_mapping_sql);
		$this->_big_genre_mapping = $genre_mapping_query->execute()->as_array('class_cd', 'name');
	}

	public function _is_valid_exec_time($time = null)
	{
		if ($time === null) {
			$time = date('H:i');
		}
		foreach ($this->_time_slot_avoid as $limit) {
			if ($time >= $limit['start'] && $time <= $limit['end']) {
				return false;
			}
		}
		return true;
	}

	private function _crawling_item($item_id)
	{
		// ここにクロール処理を実装
		$this->process_tabelog_link($item_id);
	}

	private function _crawling_items($item_ids, $test=false)
	{
		$progress = 0; // クロールの終了条件
		$count = count($item_ids);
		$now_seconds = time();
		$last_seconds = $now_seconds;
		$calls_per_second = 0;
		$now = date('H:i:s');
		$info = "Crawling($count) items started[$now]...";
		$this->_write_log($info);
		foreach ( $item_ids as $item_id) {
			if (!$this->_is_valid_exec_time()) {
				// クロールが実行可能な時間ではない場合、待機
				$info = "Crawling not allowed at this time ".json_encode($this->_time_slot_avoid).", please wait until next time slot and execute again...";
				$this->_write_log($info);
				return $progress;
			}
			// 1秒間に2回 `_crawling_item` を呼び出す
			$this->_crawling_item($item_id);

			$progress++;
			$calls_per_second ++;
			if ($progress % 15 == 0) {
				$now = date('H:i:s');
				echo "Crawled $progress/$count items so far[$now]...".PHP_EOL;
			}
			if ($test && $progress > 50) { //TODO delete after testing
				$now = date('H:i:s');
				echo "[Test Mode]Crawled $progress/$count items at last[$now].".PHP_EOL;
				return $progress; // for testing purpose, return after 10 items
			}
			$now_seconds = time();
			while ($calls_per_second >= $this->_calls_per_second && $now_seconds - $last_seconds < 1) {
				// 1秒未満の場合は待機
				usleep(200000); // マイクロ秒単位で待機(0.1seconds)
				// 1秒間に2回実行したら、次の秒まで待機
				$now_seconds = time();
			}
			if ($calls_per_second >= $this->_calls_per_second) {
				$calls_per_second = 0; // カウンターをリセット
				$last_seconds = $now_seconds;
			}
		}
		$now = date('H:i:s');
		$info = "Crawled $progress/$count items at last[$now].";
		$this->_write_log($info);
		return $progress;
	}

	function filter_market_items($item_div, $class_cd, $area_cd, $start_date, $end_date, $keyword, $start, $size, $lang_cd, $status_cd, $lat, $lng, $distance, $show = true, $item_data = null)
	{
		$join = " INNER ";
		if ($keyword == '') {
			$join = " LEFT ";
		}
		$sql = "SELECT  m.item_id, m.item_div,m.link_type_cd, m.item_cd, m.item_name, m.class_cd, m.start_time, m.end_time, m.upd_user, m.upd_time, d.description, ST_Distance_Sphere(point($lng, $lat), geo_point)/1000 AS distance
				FROM t_market_item m $join JOIN t_market_item_description d ON m.item_id=d.item_id AND d.lang_cd=:lang_cd WHERE m.item_div=:item_div AND m.delete_flg = 0 ";
		if ($distance != '') $sql = $sql . " AND ST_Distance_Sphere(point($lng, $lat), geo_point)/1000 <=:distance ";
		if ($class_cd != '') $sql = $sql . " AND concat(' ', m.class_cd) LIKE '% " . $class_cd . "%'";
		if ($area_cd != '') $sql = $sql . " AND m.area_cd  LIKE '" . $area_cd . "%'";
		if ($item_data) $sql = $sql . " AND m.item_data  = :item_data";
		if ($keyword != '') $sql = $sql . " AND d.description  LIKE :keyword";
		//if ($status_cd != '') $sql = $sql . " AND item_status_cd =:item_status_cd ";
		if ($show) {
			if ($start_date != '') $sql = $sql . " AND (m.end_time is NULL OR m.end_time >= :start_date)";
			if ($end_date != '') $sql = $sql . " AND (m.start_time is NULL OR m.start_time <= :end_date)";
		}
		else {
			if ($start_date != '' || $end_date != '') {
				$sql = $sql . " AND ( ";
				$sub = "";
				if ($start_date != '') $sub = $sub . " m.end_time is NOT NULL AND m.end_time < :start_date ";
				if ($end_date != '') {
					if ($sub != '') $sub = $sub . " OR ";
					$sub = $sub . " m.start_time is NOT NULL AND m.start_time > :end_date ";
				}
				$sql = $sql . $sub . ") ";
			}
		}
		$sql = $sql . " LIMIT $start, $size";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':lang_cd' => $lang_cd,
				':item_status_cd' => $status_cd,
				':item_div' => $item_div,
				':item_data' => $item_data,
				':start_date' => $start_date,
				':end_date' => $end_date,
				':distance' => $distance,
				':keyword' => "%$keyword%",
		));
		$compiled = $query->compile();
		// echo "Compiled market items filtering SQL: $compiled".PHP_EOL;
		$results = $query->execute();
		return $results->as_array();
	}

	function get_bot_location_mapping($bot_id0, $bot_id1){
		$sql = "SELECT JSON_OBJECTAGG(bot_id, location) json FROM (SELECT bot_id, JSON_ARRAYAGG(setting_value) location FROM t_bot_setting WHERE bot_id BETWEEN $bot_id0 AND $bot_id1 AND setting_cd LIKE 'location_l%' GROUP BY bot_id) location_data;";
		$query = DB::query(Database::SELECT, $sql);
		$results = $query->execute()->get('json', null);
		return $results ? json_decode($results, true) : [];
	}

	function count_filtered_tabelog_market_items($locations, $distance)
	{
		$distance = $distance * 1000; // Convert km to meters
		$sql = "SELECT COUNT(*) c FROM t_market_item WHERE item_div=17 AND delete_flg = 0 ";
		if ($locations) {
			$sql = $sql . " AND (0 ";
			foreach ($locations as $location) {
				$lat = $location[0];
				$lng = $location[1];
				$sql = $sql . " OR ST_Distance_Sphere(point($lng, $lat), geo_point) <=$distance ";
			}
			$sql = $sql . ")";
		}
		$query = DB::query(Database::SELECT, $sql);
		$compiled = $query->compile();
		// echo "Compiled market items filtering SQL: $compiled".PHP_EOL;
		$results = $query->execute();
		return $results->get('c', 0);
	}

	function flag_filtered_tabelog_market_items($locations, $distance)
	{
		$distance = $distance * 1000; // Convert km to meters
		$sql = "UPDATE t_market_item SET item_data=3 WHERE item_div=17 AND item_data!=3 AND delete_flg = 0 ";
		if ($locations) {
			$sql = $sql . " AND (0 ";
			foreach ($locations as $location) {
				$lat = $location[0];
				$lng = $location[1];
				$sql = $sql . " OR ST_Distance_Sphere(point($lng, $lat), geo_point) <=$distance ";
			}
			$sql = $sql . ")";
		}
		$query = DB::query(Database::UPDATE, $sql);
		$compiled = $query->compile();
		// echo "Compiled market items filtering SQL: $compiled".PHP_EOL;
		$updated = $query->execute();
		return $updated;
	}

	protected function _get_batch_todo_items($item_data){
		$table = self::ITEM_TBL;
		$last_inserted_sql = "SELECT link_id, item_id FROM $table WHERE item_div=17 AND link_type_cd='ta' AND item_data = '$item_data'";
		$last_inserted_query = DB::query(Database::SELECT, $last_inserted_sql);
		$last_inserted_items = $last_inserted_query->execute()->as_array('link_id', 'item_id');
		
		$info = "Last batch link todo($item_data) market items: ".count($last_inserted_items).PHP_EOL;
		echo($info);
		return $last_inserted_items;
	}

	protected function _set_batch_link_items_flag($item_ids, $item_data){
		$table = self::ITEM_TBL;

		// $update_sql = "UPDATE $table SET item_data = '$item_data' WHERE item_div=17 AND link_type_cd='ta' AND item_data != '$item_data'";	
		$update_query = DB::update($table)
			->set(['item_data' => $item_data, 'upd_time' => DB::expr('NOW()'), 'upd_user' => -1])
			->where('item_div', '=', '17')
			->where('link_type_cd', '=', 'ta')
			->where('item_data', '!=', $item_data)
			;

		if ($item_ids && is_array($item_ids) && count($item_ids) > 0) {
			$update_query->where('item_id', 'IN', $item_ids);
		}
		$last_updated = $update_query->execute();
		echo("Updated $last_updated rows in $table to set batch link TODO flag".PHP_EOL);
		return $last_updated;
	}

	protected function _reset_batch_link_items_flag($item_ids, $item_data){
		$table = self::ITEM_TBL;
		// $update_sql = "UPDATE $table SET item_data = '$item_data' WHERE item_div=17 AND link_type_cd='ta' AND item_data != '$item_data'";	
		$update_query = DB::update($table)
			->set(['item_data' => '', 'upd_time' => DB::expr('NOW()'), 'upd_user' => 0])
			->where('item_div', '=', '17')
			->where('link_type_cd', '=', 'ta')
			->where('item_data', '=', $item_data)
			;

		if ($item_ids && is_array($item_ids) && count($item_ids) > 0) {
			$update_query->where('item_id', 'IN', $item_ids);
		}
		$last_updated_items = $update_query->execute();
		echo("Updated $last_updated_items rows in $table to reset($item_data) flag".PHP_EOL);
		return $last_updated_items;
	}

	private function estimate_crawling_time($item_count) {
		$estimated_time0 = $item_count * 2.5 / 60; // 2 seconds per item
		$estimated_time1 = $item_count * 3 / 60; // 3 seconds per item for link + crawling
		return "Estimated time cost: about $estimated_time0~$estimated_time1 min";
	}

	function batch_process_tabelog_update_continue($test=false) {
		$batch_crawling_todo_items = $this->_get_batch_todo_items(1); // get batch crawling to update items with item_data = 1
		if (count($batch_crawling_todo_items) == 0) {
			$info = "No batch crawling todo items found with item_data = 1, nothing to process.";
			$this->_write_log($info);
			return 0;
		}
		$item_ids = [];
		foreach ($batch_crawling_todo_items as $link_id=>$item_id) {
			$item_ids[] = $item_id;
		}
		$item_count = count($item_ids);
		$info = "Found $item_count market items to process,".$this->estimate_crawling_time($item_count);
		$this->_write_log($info);
		return $this->_crawling_items($item_ids, $test);
	}

	function process_flagged_tabelog_market_items_to_link($test=false) {
		$batch_crawling_todo_items = $this->_get_batch_todo_items(3); // get batch crawling to update items with item_data = 1
		if (count($batch_crawling_todo_items) == 0) {
			echo "No batch crawling todo items found with item_data = 3, nothing to process.".PHP_EOL;
			return 0;
		}
		$item_ids = [];
		foreach ($batch_crawling_todo_items as $link_id=>$item_id) {
			$item_ids[] = $item_id;
		}
		$item_count = count($item_ids);
		echo "Found $item_count market items to process,".$this->estimate_crawling_time($item_count).PHP_EOL;
		return $this->_crawling_items($item_ids, $test);
	}

	function check_flagged_tabelog_market_items_to_link($item_data = 2) {
		// get batch crawling done items with item_data
		$batch_link_todo_items = $this->_get_batch_todo_items($item_data); // get batch crawling to update items with item_data = 1
		return $batch_link_todo_items;
	}

	function link_flagged_tabelog_market_items($bot_id, $location, $distance=0.3) {
		$bot = ORM::factory('bot', $bot_id);
		$bot_name = $bot->bot_name;
		echo "Linking Tabelog for bot $bot_id:$bot_name, all catagories, distance: $distance(km)".PHP_EOL;
		if ($location) {
			$lat_bot = $location[0];
			$lng_bot = $location[1];
		}else {
			$lat_bot = $this->_admin_model->get_bot_setting($bot_id, 'location_lat');
			$lng_bot = $this->_admin_model->get_bot_setting($bot_id, 'location_lon');
		}
		// get crawling data ready items with item_data = 2 and within bot location range
		$results = $this->filter_market_items($this->_item_div, '', '', '', '', '', 0, 99999, 'ja', '01', $lat_bot, $lng_bot, $distance, true, 2);
		$item_ids = [];
		foreach($results as $r) {
			$item_ids[] = $r['item_id'];
		}
		$lang_display = '';
		if ($bot->lang_cd) {
			$lang_array = $this->get_bot_lang($bot);
			$lang_display = implode(',', array_keys($lang_array));
		}
		foreach($item_ids as $item_id) {
			DB::delete('t_item_display')->where('item_id', '=', $item_id)->where('item_div', '=', $this->_item_div)->where('bot_id', '=', $bot_id)->execute();
			$orm = ORM::factory('itemdisplay');
			$orm->bot_id = $bot_id;
			$orm->item_id = $item_id;
			$orm->item_div = $this->_item_div;
			$orm->sort_no1 = 0;
			$orm->sort_no2 = 0;
			$orm->sort_no3 = 0;
			$orm->sort_no4 = 0;
			$orm->sort_no5 = 0;
			$orm->lang_display = $lang_display;
			$orm->save();
		}
		return count($item_ids);
	}

	function finish_linked_tabelog_market_items() {
		return $this->_reset_batch_link_items_flag(null, 2); // reset item_data to '' for batch link + crawling update ready flag
	}


	function batch_process_tabelog_link_continue($bot_id, $check_only=false) {
		$batch_crawling_todo_items = $this->_get_batch_todo_items(3); // get batch crawling todo items with item_data = 3
		$item_ids = [];
		foreach ($batch_crawling_todo_items as $link_id=>$item_id) {
			$item_ids[] = $item_id;
		}
		$item_count = count($item_ids);
		$estimated_time0 = $item_count * 2.5 / 60; // 2 seconds per item
		$estimated_time1 = $item_count * 3 / 60; // 3 seconds per item for link + crawling
		echo "Found $item_count market items to CONTINUE process, estimated time cost: about $estimated_time0~$estimated_time1 min".PHP_EOL;
		$progress = $this->_crawling_items($item_ids);
		if($check_only) {
			return $progress;
		}
		$bot = ORM::factory('bot', $bot_id);
		$lang_array = $this->get_bot_lang($bot);
		$batch_link_todo_items = $this->_get_batch_todo_items(2); // get batch link todo items with item_data = 2
		foreach($batch_link_todo_items as $batch_link_todo_items=>$item_id) {
			DB::delete('t_item_display')->where('item_id', '=', $item_id)->where('item_div', '=', $this->_item_div)->where('bot_id', '=', $bot_id)->execute();
			$orm = ORM::factory('itemdisplay');
			$orm->bot_id = $bot_id;
			$orm->item_id = $item_id;
			$orm->item_div = $this->_item_div;
			$orm->sort_no1 = 0;
			$orm->sort_no2 = 0;
			$orm->sort_no3 = 0;
			$orm->sort_no4 = 0;
			$orm->sort_no5 = 0;
			$orm->lang_display = implode(',', array_keys($lang_array));
			$orm->save();
		}
		$this->_reset_batch_link_items_flag($item_ids, 2); // reset item_data to '' for batch link + crawling update ready flag
	}

	function get_process_tabelog_link_list($bot_id, $location, $distance) {
		$bot = ORM::factory('bot', $bot_id);
		$bot_name = $bot->bot_name;
		echo "Calculating Tabelog link for bot $bot_id:$bot_name, all catagories, distance: $distance(km)".PHP_EOL;
		if ($location) {
			$lat_bot = $location[0];
			$lng_bot = $location[1];
		}else {
			$lat_bot = $this->_admin_model->get_bot_setting($bot_id, 'location_lat');
			$lng_bot = $this->_admin_model->get_bot_setting($bot_id, 'location_lon');
		}
		$results = $this->filter_market_items($this->_item_div, '', '', '', '', '', 0, 99999, 'ja', '01', $lat_bot, $lng_bot, $distance, 1);
		$item_ids = [];
		foreach($results as $r) {
			$item_ids[] = $r['item_id'];
		}
		return $item_ids;
	}

	function batch_process_tabelog_link($bot_id, $class_cd, $area_cd, $distance, $check_only=false)//$start_date, $end_date, $keyword, $lang_cd, $status_cd, , $show
	{
		$bot = ORM::factory('bot', $bot_id);
		$bot_name = $bot->bot_name;
		$genre_big = 'all categories';
		if ($class_cd && isset($this->_big_genre_mapping[$class_cd])) {
			$genre_big = $class_cd.':'.$this->_big_genre_mapping[$class_cd];
		} else if ($class_cd){
			echo "Class code $class_cd not found in genre mapping, Abort.".PHP_EOL;
		}
		echo "Processing Tabelog link for bot $bot_id:$bot_name, $genre_big, distance: $distance(km)".PHP_EOL;
		$lat_bot = $this->_admin_model->get_bot_setting($bot_id, 'location_lat');
		$lng_bot = $this->_admin_model->get_bot_setting($bot_id, 'location_lon');
		$lang_array = $this->get_bot_lang($bot);
		$results = $this->filter_market_items($this->_item_div, $class_cd, $area_cd, '', '', '', 0, 99999, 'ja', '01', $lat_bot, $lng_bot, $distance, 1);
		$item_ids = [];
		foreach($results as $r) {
			$item_ids[] = $r['item_id'];
		}
		$item_count = count($item_ids);
		if ($item_count == 0) {
			$info = "No market items found for bot_id: $bot_id, class_cd: $class_cd, area_cd: $area_cd, distance: $distance";
			echo $info.PHP_EOL;	
			Log::instance()->add(Log::INFO, $info);
			return;
		}
		echo "Found $item_count market items to process,".$this->estimate_crawling_time($item_count).PHP_EOL;
		$updated_count = $this->_set_batch_link_items_flag($item_ids, 3); // set item_data to '3' for link + crawling TODO items
		$progress = $this->_crawling_items($item_ids);
		if($check_only) {
			return $progress;
		}
		foreach($results as $r) {
			DB::delete('t_item_display')->where('item_id', '=', $r['item_id'])->where('item_div', '=', $this->_item_div)->where('bot_id', '=', $bot_id)->execute();
			$orm = ORM::factory('itemdisplay');
			$orm->bot_id = $bot_id;
			$orm->item_id = $r['item_id'];
			$orm->item_div = $this->_item_div;
			$orm->sort_no1 = 0;
			$orm->sort_no2 = 0;
			$orm->sort_no3 = 0;
			$orm->sort_no4 = 0;
			$orm->sort_no5 = 0;
			$orm->lang_display = implode(',', array_keys($lang_array));
			$orm->save();
		}
		$this->_reset_batch_link_items_flag($item_ids, 2); // reset item_data to '' for batch link + crawling update ready flag
	}

	public function process_tabelog_link($item_id, $lang_array=null) {
		if ($lang_array === null) {
			$lang_array = ['ja' => '日本語', 'en' => 'English', 'tw' => '繁體中文', 'kr' => '한국어', 'cn' => '简体中文'];
		}
		// get top url for shop
		$market_item_orm = ORM::factory('marketitem', $item_id);
		if (!isset($market_item_orm->item_id)) {
			$info = "Market item not found for item_id: $item_id";
			Log::instance()->add(Log::ERROR, $info);
			echo $info.PHP_EOL;
			return null;
		}
		$link_data = $market_item_orm->link_data;
		$item_data = $market_item_orm->item_data;
		$link_data_json = json_decode($link_data, true);
		if (!$link_data_json || !isset($link_data_json['link'])) {
			// No top_url found, cannot translate
			Log::instance()->add(Log::ERROR, "Market item link_data.link not found for item_id: $item_id");
			return null;
		}
		$top_url = $link_data_json['link'];
		// echo "Processing Tabelog link for item_id: $item_id, top_url: $top_url".PHP_EOL;
		list($images, $restaurant_name_en, $reservation_available) = $this->_fetch_shop_info($top_url);
		// Log::instance()->add(Log::DEBUG, "Fetched HTML content from $top_url".PHP_EOL.
		// 	"Images: " . json_encode($images) . PHP_EOL .
		// 	"Restaurant Name EN: $restaurant_name_en" . PHP_EOL .
		// 	"Reservation Available: $reservation_available");
		$link_data_json['reservation_available'] = $reservation_available;
		if ($images) {
			$link_data_json['restaurant_image_url_array'] = $images;
		}
		$market_item_orm->link_data = json_encode($link_data_json, JSON_UNESCAPED_UNICODE);
		if ($item_data == 3) {
			$market_item_orm->item_data = 2; // set item_data to 2 as batch link TODO flag
		} else if ($item_data == 1) {
			// if item_data is 1, it means the item is already linked, so we don't change it
			$market_item_orm->item_data = ''; // reset item_data to empty as batch link + crawling update ready flag
		}
		$market_item_orm->upd_time = date('Y-m-d H:i:s');
		$market_item_orm->save();

		$item_desc_orm_ja = ORM::factory('marketitemdescription', [
			'item_id' => $item_id,
			'lang_cd' => 'ja'
		]);
		if (!isset($item_desc_orm_ja->item_id)) {
			Log::instance()->add(Log::ERROR, "Market item description not found for item_id: $item_id, lang_cd: ja");
			return null;
		}
		$business_hours_ja = $item_desc_orm_ja->description;

		// translate item name and description
		// echo "Translating business hours for item_id: $item_id to ".json_encode(array_keys($lang_array)).PHP_EOL;
		foreach($lang_array as $lang_cd => $_code) {
			if ($lang_cd == 'ja') continue; // 日本語はスキップ

			if(!in_array($lang_cd, ['en', 'tw', 'kr', 'cn'])) {
				// Only translate to English, Chinese, and Korean
				continue;
			}

			$business_hours_translated = $this->_admin_model->translate($business_hours_ja, $lang_cd, 'ja');
			$market_item_desc_orm = ORM::factory('marketitemdescription', [
				'item_id' => $item_id,
				'lang_cd' => $lang_cd
			]);
			$update = true;
			
			// Log::instance()->add(Log::ERROR, "Market item description item_id: $item_id, ".$market_item_desc_orm->item_id.", lang_cd:". $market_item_desc_orm->lang_cd);

			if (!isset($market_item_desc_orm->item_id)) {
				$market_item_desc_orm->item_id = $item_id;
				$market_item_desc_orm->lang_cd = $lang_cd;
				$market_item_desc_orm->title = $restaurant_name_en??'';
				$update = false;
			}
			$market_item_desc_orm->description = $business_hours_translated;
			if ($update) {
				$update_array = array(
					'title' => $restaurant_name_en??DB::expr('title'),
					'description' => $business_hours_translated,
					'upd_time' => DB::expr('NOW()')
				);
				DB::update('t_market_item_description')
					->set($update_array)
					->where('item_id', '=', $item_id)
					->where('lang_cd', '=', $lang_cd)
					->execute();
			} else {			
				$market_item_desc_orm->save();
			}
		}
	}

	protected function _fetch_shop_info($top_url=null) {
		if ($top_url==null || empty($top_url)) {
			// Log an error or handle the failure gracefully
			Log::instance()->add(Log::ERROR, "No top URL provided for fetching shop info");
			$top_url = 'https://tabelog.com/tokyo/A1304/A130401/13289246'; // default URL
			// $top_url = 'https://tabelog.com/aomori/A0203/A020301/2011730/'; // carousel URL
			// $top_url = 'https://tabelog.com/tokyo/A1331/A133101/13280847/'; // hot pepper booking URL
			// $top_url = 'https://tabelog.com/wakayama/A3001/A300101/30007036/'; // tabelog booking URL
			// $top_url = 'https://tabelog.com/wakayama/A3005/A300501/30003756/'; // No booking URL
		}
		// echo "Fetching shop info from @file_get_contents($top_url)".PHP_EOL;
		$html = @file_get_contents($top_url);
		if ($html === false || empty($html)) {
			// Log an error or handle the failure gracefully
			$info = "Failed to fetch HTML content from $top_url";
			Log::instance()->add(Log::ERROR, $info);
			// echo $info.PHP_EOL;
			return array(null, null);
		}
		// echo "html length: ".strlen($html).PHP_EOL;
		$images = null;
		$regex = '~<img[^>]+src="((https://tblg.k-img.com/resize/660x370c/restaurant/images/Rvw/)[0-9]+/[0-9a-z]+\.jpg\?token=[0-9a-z]+&(amp;)?api=v[0-9]+)[^"]*"[^>]*>~i';
		preg_match_all($regex, $html, $matches);
		if ($matches){
			$images = [];
			foreach ($matches[1] as $_index=>$match) {
				if ($match) {
					$image_url = preg_replace('/&amp;/i', "&", $match);
					// echo "Found match image URL $image_url".'?1749023121332'.PHP_EOL;
					$images[] =$image_url;
					if (count($images) >= 10) {
						break; // limit to 10 images
					}
				}
			}
		}
		if (!$images) {
			// $this->_write_log("No SELECTED images found in $top_url");
			// echo "No SELECTED images found in $top_url, finding PLAIN images...".PHP_EOL;
			//<a href="https://tblg.k-img.com/restaurant/images/Rvw/202340/640x640_rect_251e4031870a608f22e907bc8d92b50a.jpg" data-id="202340558" title="バーやまざき - " class="js-imagebox-trigger">
			// <img width="125" height="125" alt="バーやまざき - " src="https://tblg.k-img.com/restaurant/images/Rvw/202340/150x150_square_251e4031870a608f22e907bc8d92b50a.jpg" class="loading" data-was-processed="true">
			$regex = '~<a[^>]+href="((https://tblg.k-img.com/restaurant/images/Rvw/)[0-9]+/[0-9a-z_]+\.jpg)([^"]*)"[^>]*>~i';
			preg_match_all($regex, $html, $matches);
			if ($matches){
				$images = [];
				foreach ($matches[1] as $_index=>$match) {
					if ($match) {
						$image_url = $match;
						// echo "Found match image URL $match".PHP_EOL;
						$images[] = $image_url;
						if (count($images) >= 10) {
							break; // limit to 10 images
						}
					}
				}
			}
		}
		// <div class="rstdtl-side-yoyaku__booking js-rstdtl-side-yoyaku__reserve">
		// <a class="js-booking-form-open c-btn c-btn--full c-btn--primary" href="/booking/form/new?member=2&amp;rcd=30002359&amp;visit_date=20250619&amp;visit_time=1900&amp;lid=yoyaku_rstdtl_side_calendar"><span class="js-booking-container-button">予約する</span></a>
		$reserve_box_regex = '~<div class="rstdtl-side-yoyaku__booking js-rstdtl-side-yoyaku__reserve">.*<div class="js-side-calendar-wrapper rstdtl-side-yoyaku__calendar">~is';
		$calendar_wrapper_regex = '/(js-side-calendar-wrapper)/i';
		$booking_btn_regex = '/(js-booking-container-button)/i';

		preg_match_all($reserve_box_regex, $html, $matches_reserve);
		$reservation_available = 0;
		if ($matches_reserve){
			// print_r($matches_reserve);
			// echo PHP_EOL;
			foreach ($matches_reserve[0] as $_index=>$match) {
				if ($match) {
					$reservation_available = 1; // found reservation link
					// echo "Found reservation link: $reservation_available".PHP_EOL;
				}
			}
		}
		if (!$reservation_available) {
			// echo "No reservation link found in $top_url".PHP_EOL;
			// Log::instance()->add(Log::INFO, "Static HTML content from $top_url:$html");
		}

		//<div class="rdheader-rstname"><a href="https://tabelog.com/en/okinawa/A4702/A470202/47020005/" property=""></a><span class="pillow-word">Nishikiya, where you can enjoy dining on the rooftop terrace or in an old folk house</span><h2 class="display-name"><span>Nishikiya</span></h2><span class="alias">(錦屋)</span></div>
		$en_name_regex = '~<h2 class="display-name">[^<]+<span>([^<]+)</span>[^<]+</h2>~i';
		$en_top_url = str_replace('https://tabelog.com/', 'https://tabelog.com/en/', $top_url);
		$en_html = file_get_contents($en_top_url);
		preg_match_all($en_name_regex, $en_html, $matches_en);
		$restaurant_name_en = null;
		if ($matches_en){
			foreach ($matches_en[1] as $_index=>$match) {
				if ($match) {
					$restaurant_name_en = $match;
				}
			}
		}

		return array(
			$images, 
			$restaurant_name_en,
			$reservation_available,
		);
	}

	private function _write_log($log)
	{
		$file = APPPATH . "../../files/log/" . self::DIV . "_" . date('Y-m-d') . ".log";
		$f = fopen($file, 'a');
		$log = date('Y-m-d H:i:s') . ' ' . $log . PHP_EOL;
		fwrite($f, $log);
		fclose($f);
		echo ($log);
	}

}
?>