<?php defined('SYSPATH') OR die('No direct script access.');

class Model_Utilmodel extends Model_Basemodel
{
	function clear_dir($path) 
	{
		if(file_exists($path)) {
			$dh = opendir($path);
			while ($file = readdir($dh)) {
				if($file != "." && $file != "..") {
					$fullpath = $path . "/" . $file;
					if(!is_dir($fullpath)) {
						unlink($fullpath);
					}
				}
			}
			closedir($dh);
		}
	}
	
	function remove_dir($path) 
	{
		rmdir($path);
	}
	
	function zip_path($path, $zipfile)
	{
		if(!file_exists($path)) {
			return false;
		}
		$zip = new ZipArchive();
		$zip->open($zipfile, ZipArchive::CREATE|ZipArchive::OVERWRITE);
		$files = new RecursiveIteratorIterator(new RecursiveDirectoryIterator($path));
		foreach ($files as $name => $file) {
			if ($file->isDir()) {
				continue;
			}
			$zip->addFile($file->getRealPath(), $file->getFileName());
		}
		$zip->close();
		return true;
	}
	
	function bot2csv($bot_id, $table, $path)
	{
		if ($table == 't_item_description') {
			$sql = "select * from $table where item_id in (select item_id from t_item where bot_id = $bot_id)";
		}
		else if ($table == 't_product_description') {
			$sql = "select * from $table where product_id in (select product_id from t_product where bot_id = $bot_id)";
		}
		else if ($table == 't_bot_msg_desc_txt' || $table == 't_bot_msg_desc_lst' ||
				 $table == 't_bot_msg_desc_img' || $table == 't_bot_msg_desc_car' || $table == 't_bot_msg_desc_tpl') {
			$sql = "select * from $table where msg_id in (select msg_id from t_bot_msg where bot_id = $bot_id)";
		}
		else {
			$sql = "select * from $table where bot_id = $bot_id";
		}
		$query = DB::query(Database::SELECT, $sql);
		$data = $query->execute()->as_array();
		if (count($data) == 0) return;
		$f = fopen($path . '/' . $table . '.csv', 'w');
		fputcsv($f, array_keys($data[0]), ",");
		foreach($data as $log) {
			fputcsv($f, $log, ",");
		}
		fclose($f);
	}
	
	function csv2bot($bot_id, $table, $path)
	{
		/*
		if ($bot_id != null) {
			DB::delete($table)->where('bot_id','=',$bot_id)->execute();
		}
		*/
		
		if(file_exists($path . '/' . $table . '.csv')) {
			$f = fopen($path . '/' . $table . '.csv', 'r');
			$fields = fgetcsv($f);
			while(!feof($f))
			{
				$row = fgetcsv($f);
				if ($row === false) break;
				try {
					$db_fields = [];
					$db_row = [];
					for($i=0; $i<count($fields); $i++) {
						if ($fields[$i] == 'upd_user') {
							$db_fields[] = $fields[$i];
							$db_row[] = intval($row[$i]);
						}
						else if ($fields[$i] == 'start_date') {
							if ($row[$i] != '') {
								$db_fields[] = $fields[$i];
								$db_row[] = $row[$i];
							}
						}
						else if ($fields[$i] == 'end_date') {
							if ($row[$i] != '') {
								$db_fields[] = $fields[$i];
								$db_row[] = $row[$i];
							}
						}
						else if ($fields[$i] == 'upd_time') {
							if ($row[$i] != '') {
								$db_fields[] = $fields[$i];
								$db_row[] = $row[$i];
							}
						}
						else {
							$db_fields[] = $fields[$i];
							$db_row[] = $row[$i];
						}
					}
					DB::insert($table, $db_fields)->values($db_row)->execute();
				}
				catch (Exception $e)
				{
					Log::instance()->add(Log::DEBUG, "bot import:" . $e->getMessage());
				}
			}
			fclose($f);
		}
	}
}

?>




