<?php defined('SYSPATH') OR die('No direct script access.');
# Includes the autoloader for libraries installed with composer
require DOCROOT. '/vendor/autoload.php';

# Imports the Google Cloud client library
use GeoIp2\Database\Reader;

class Model_Geoip extends Model
{
	public function __construct()
	{

	}
	
	public function getCountry($ipaddress) 
	{
		$reader = new Reader(DOCROOT. '/vendor/geoip2/geoip2/maxmind-db/GeoLite2-Country.mmdb');
		$record = $reader->country($ipaddress);
		return $record;
	}
	
	public function getCity($ipaddress)
	{
		$reader = new Reader(DOCROOT. '/vendor/geoip2/geoip2/maxmind-db/GeoLite2-City.mmdb');
		$record = $reader->city($ipaddress);
		return $record;
	}
}

?>
