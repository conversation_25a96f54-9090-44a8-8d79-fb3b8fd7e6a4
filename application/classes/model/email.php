<?php defined('SYSPATH') OR die('No direct script access.');
# Includes the autoloader for libraries installed with composer
require DOCROOT. '/vendor/autoload.php';

use <PERSON><PERSON>Mailer\PHPMailer\PHPMailer;
use Aws\Ses\SesClient;
use Aws\Exception\AwsException;

class Model_Email extends Model_Basemodel
{
	private $sesclient = NULL;
	private $sender_email = '';
	private $_bot_id;
	public function __construct()
	{
		$this->_bot_id = Session::instance()->get('bot_id', NULL);
		if ($this->_bot_id == null) $this->_bot_id = 0;
	}
	
	function send($to, $to_text, $subject, $body, $key='default') 
	{
		if (!is_array($to)) {
			$to = [$to];
			$to_text = [$to_text];
		}
		
		for($i=0; $i<count($to); $i++) {
			if ($to_text[$i] != '') {
				$to[$i] = "=?utf-8?B?". base64_encode($to_text[$i]) . "?=" . "<" .$to[$i] . ">";
			}
		}

		$param = ['bot_id'=>$this->_bot_id, 'configKey'=>$key, 'receiver'=>implode(',', $to), 'title'=>$subject, 'body'=>$body];
		$data = $this->post_enginehook('service', 'sendmail','', $param);
		if ($data['success'] == 'False') {
			Log::instance()->add(Log::DEBUG, 'API service.sendmail failure=' . json_encode($data));
			$this->log_error(__FUNCTION__, "API service.sendmail failure=." . json_encode($data, JSON_UNESCAPED_UNICODE));
			return false;
		}
		else {
			return true;
		}
		//return $this->send_php($to, $to_text, $subject, $body);
		//return $this->send_aws($to, $subject, $body);
	}
	
	function send_ex($to, $cc, $bcc, $subject, $body, $key='default') {
		if (!is_array($to)) {
			$to = ['email'=>$to, 'name'=>$to];
		}
		if (!is_array($cc)) {
			$cc = ['email'=>$cc, 'name'=>$cc];
		}
		if (!is_array($bcc)) {
			$bcc = ['email'=>$bcc, 'name'=>$bcc];
		}
		$send_to = [];
		foreach($to as $t) {
			$send_to[] = "=?utf-8?B?". base64_encode($t['name']) . "?=" . "<" .$t['email'] . ">";
		}
		$send_cc = [];
		foreach($cc as $t) {
			$send_cc[] = "=?utf-8?B?". base64_encode($t['name']) . "?=" . "<" .$t['email'] . ">";
		}
		$send_bcc = [];
		foreach($cc as $t) {
			$send_bcc[] = "=?utf-8?B?". base64_encode($t['name']) . "?=" . "<" .$t['email'] . ">";
		}
		
		$param = ['bot_id'=>$this->_bot_id, 'configKey'=>$key, 'title'=>$subject, 'body'=>$body,
				'receiver'=>implode(',', $send_to), 'receiver_cc'=>implode(',', $send_cc),'receiver_bcc'=>implode(',', $send_bcc)
		];
		$data = $this->post_enginehook('service', 'sendmail','', $param);
		if ($data['success'] == 'False') {
			Log::instance()->add(Log::DEBUG, 'API service.sendmail failure=' . json_encode($data));
			$this->log_error(__FUNCTION__, "API service.sendmail failure=." . json_encode($data, JSON_UNESCAPED_UNICODE));
			return false;
		}
		else {
			return true;
		}
	}
	
	function send_message($to, $to_text, $msg_cd, $lang_cd, $params, $key='default')
	{
		if (!is_array($to)) {
			$to = [$to];
			$to_text = [$to_text];
		}
		
		for($i=0; $i<count($to); $i++) {
			if ($to_text[$i] != '') {
				$to[$i] = "=?utf-8?B?". base64_encode($to_text[$i]) . "?=" . "<" .$to[$i] . ">";
			}
		}
		
		$param = ['bot_id'=>$this->_bot_id, 'configKey'=>$key, 'receiver'=>implode(',', $to), 'message_cd'=>$msg_cd, 'lang_cd'=>$lang_cd, 'params'=>$params];
		$data = $this->post_enginehook('service', 'sendmail','', $param);
		if ($data['success'] == 'False') {
			Log::instance()->add(Log::DEBUG, 'API service.sendmail failure=' . json_encode($data));
			$this->log_error(__FUNCTION__, "API service.sendmail failure=." . json_encode($data, JSON_UNESCAPED_UNICODE));
			return false;
		}
		else {
			return true;
		}
	}

	function send_aws($to, $subject, $body, $cc = [], $bcc = [])
	{
		try {
			if (!is_array($to)) $to = [$to];
			$char_set = 'UTF-8';
			if (strpos($body, "<a href=") === false) {
				$body = [
						'Text' => [
								'Charset' => $char_set,
								'Data' => $body,
						]
				];
			}
			else {
				$body = [
						'Html' => [
								'Charset' => $char_set,
								'Data' => $body,
						]
				];
			}
			$result = $this->sesclient->sendEmail([
					'Destination' => [
							'ToAddresses' => $to,
							'CcAddresses' => $cc,
							'BccAddresses' => $bcc,
					],
					'ReplyToAddresses' => [$this->sender_email],
					'Source' => $this->sender_email,
					'Message' => [
							'Body' => $body,
							'Subject' => [
									'Charset' => $char_set,
									'Data' => $subject,
							],
					],
					// If you aren't using a configuration set, comment or delete the
					// following line
					//'ConfigurationSetName' => $configuration_set,
			]);
			Log::instance()->add(Log::DEBUG, 'sendmail result:' . json_encode($result));
			if (array_key_exists('MessageId', $result)) {
				$messageId = $result['MessageId'];
				Log::instance()->add(Log::DEBUG, 'sendmail success:' . $messageId);
				return true;
			}
			else {
				return false;
			}
		} catch (AwsException $e) {
			// output error message if fails
			//Log::instance()->add(Log::DEBUG, 'sendmail error:' . $e->getMessage());
			//Log::instance()->add(Log::DEBUG, 'sendmail ses error:' . $e->getAwsErrorMessage());
			return false;
		}
	}
	
	function send_php($to, $cc, $bcc, $subject, $body)
	{
		// PHPMailerのインスタンス生成
		$mail = new PHPMailer();
		$mail->isSMTP(); // SMTPを使うようにメーラーを設定する
		
		$config = $this->get_system_config('email');
		$mail->SMTPAuth = $config['SMTPAuth'];
		$mail->Host = $config['Host'];
		$mail->Username = $config['Username'];
		$mail->Password = $config['Password'];
		$mail->SMTPSecure = $config['SMTPSecure'];
		$mail->Port = $config['Port'];
		// メール内容設定
		$mail->CharSet = $config['CharSet'];
		$mail->Encoding = $config['Encoding'];
		$mail->setFrom($config['From'], $config['FromText']);
		foreach($to as $t) {
			$mail->addAddress($t['email'], $t['name']);
		}
		foreach($cc as $c) {
			$mail->addCC($c);
		}
		foreach($bcc as $b) {
			$mail->addBcc($b);
		}
		//$mail->addAddress($to[''], $to_text); //受信者を追加する
		//$mail->addReplyTo（'<EMAIL>','Information'）;
		//$mail->addCC（'<EMAIL>'）; // CCで追加
		//$mail->addBcc('<EMAIL>'); // BCCで追加
		$mail->Subject = $subject; // メールタイトル
		if (strpos($body, "<a href=") === false) {
			$mail->isHTML(false);    // HTMLフォーマットの場合はコチラを設定します
			$mail->Body = $body; // メール本文
		}
		else {
			$mail->isHTML(true);
			$mail->Body = nl2br($body); // メール本文
		}
		// メール送信の実行
		$ret = $mail->send();
		//Log::instance()->add(Log::DEBUG, 'sendmail result:' . $ret);
		return $ret;
	}
	
}

?>
