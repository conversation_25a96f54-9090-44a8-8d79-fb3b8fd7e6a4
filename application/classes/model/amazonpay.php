<?php defined('SYSPATH') or die('No direct script access.');

//require 'vendor/amzn/amazon-pay-sdk-php/Amazon/Pay/API/Client.php';
require DOCROOT . '/vendor/autoload.php';

use Amazon\Pay\API\Client;

class Model_Amazonpay extends Model_Basemodel
{
    public function signature($bot_id, $service_type, $token)
    {
        $amazonpay_config = $this->config($bot_id);
        $client = new Client($amazonpay_config);
        $base_url = $this->get_env($service_type . '_url');
        $complete_url = '';
        if ($service_type == 'inquiry') {
            if ($base_url == '/inquiry') {
                $base_url = 'http://localhost/';
            }
            if (strpos($base_url, 'https://inquiry') === false) $base_url = str_replace('/inquiry', '/', $base_url);
            $complete_url = $base_url . 'inquiry/complete?token=' . $token;
        }
        $payload = '{"storeId":"' .  $amazonpay_config['store_id'] . '","webCheckoutDetails":{"checkoutReviewReturnUrl":"' . $base_url . 'amazonpay/review?token=' . $token . '","checkoutResultReturnUrl":"' . $complete_url . '"},"scopes":["name","email","phoneNumber","postalCode","shippingAddress","billingAddress"]}';
        $signature = $client->generateButtonSignature($payload);
        return ['sandbox' => $amazonpay_config['sandbox'], 'merchantId' => $amazonpay_config['merchant_id'], 'signature' => $signature, 'payload' => $payload, 'publicKeyId' => $amazonpay_config['public_key_id'], 'ledgerCurrency' => $amazonpay_config['currency_code']];
    }

    public function config($bot_id)
    {
        $setting = $this->get_bot_setting($bot_id, 'json_payment_setting', true);
        $amazonpay_config = $setting['amazon'];
        $key_path = APPPATH . "../../files/pem/$bot_id/amazon.pem";
        $amazonpay_config['private_key'] = $key_path;
        return $amazonpay_config;
    }
    public function client($bot_id)
    {
        return new Client($this->config($bot_id));
    }

    public function execute($bot_id, $checkout_session_id, $charge_amount_amount)
    {
        $amazonpay_config = $this->config($bot_id);
        $payload = array(
            'chargeAmount' => array(
                'amount' => $charge_amount_amount,
                'currencyCode' => $amazonpay_config['currency_code']
            ),
        );
        $client = new Client($amazonpay_config);
        $result = $client->completeCheckoutSession($checkout_session_id, $payload);
        Log::instance()->add(Log::DEBUG, 'amazon pay completeCheckoutSession:' . json_encode($result, JSON_UNESCAPED_UNICODE));
        if ($result['status'] == 200) {
            $amazonpay_result = json_decode($result['response'], true);
            $charge_id = $amazonpay_result['chargeId'];
            $payload = array(
                'captureAmount' => array(
                    'amount' => $charge_amount_amount,
                    'currencyCode' => $amazonpay_config['currency_code']
                ),
                'softDescriptor' => 'Descriptor'
            );
            $headers = array('x-amz-pay-Idempotency-Key' => uniqid());
            $result = $client->captureCharge($charge_id, $payload, $headers);
            Log::instance()->add(Log::DEBUG, 'amazon pay captureCharge:' . json_encode($result, JSON_UNESCAPED_UNICODE));
            if ($result['status'] == 200) {
                return $charge_id;
            } else {
                return false;
            }
        } else {
            return false;
        }
    }

    public function cancel($bot_id, $charge_id, $charge_amount_amount, $cancellation_reason)
    {
        $amazonpay_config = $this->config($bot_id);
        $client = new Client($amazonpay_config);

        /*
        $payload = array(
            'cancellationReason' => $cancellation_reason
        );
        $result = $client->cancelCharge($charge_id, $payload);
        Log::instance()->add(Log::DEBUG, 'amazon cancel ' . json_encode($result, JSON_UNESCAPED_UNICODE));
        return json_decode($result['response'], true);
        */
        $payload = array(
            'chargeId' => $charge_id,
            'refundAmount' => array(
                'amount' => $charge_amount_amount,
                'currencyCode' => $amazonpay_config['currency_code']
            ),
            'softDescriptor' => 'Descriptor'
        );
        $headers = array('x-amz-pay-Idempotency-Key' => uniqid());
        $result = $client->createRefund($payload, $headers);
        Log::instance()->add(Log::DEBUG, 'amazon cancel createRefund:' . json_encode($result, JSON_UNESCAPED_UNICODE));
        if ($result['status'] == 200 || $result['status'] == 201 ) {
            $amazonpay_result = json_decode($result['response'], true);
            return $amazonpay_result['refundId'];
        } else {
            return false;
        }
    }
}
