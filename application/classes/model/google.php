<?php defined('SYSPATH') OR die('No direct script access.');
# Includes the autoloader for libraries installed with composer
require DOCROOT. '/vendor/autoload.php';

# Imports the Google Cloud client library
use Google\Cloud\Translate\TranslateClient;

class Model_Google extends Model
{
	# Your Google Cloud Platform project ID
	private $projectId = 'talkappi-463f1';
	
	function translate($text, $target)
	{
		if ($target == 'js') return $text;
		
		if ($target == 'cn') { 
			$target = "zh-CN";
		}
		else if ($target == 'tw') { 
			$target = "zh-TW";
		}
		else if ($target == 'kr') {
			$target = "ko";
		}
		else if ($target == 'cb') {
			$target = "ceb";
		}
		else if ($target == 'hw') {
			$target = "haw";
		}
		else if ($target == 'hn') { 
			$target = "hmn";
		}
		# Instantiates a client
		$translate = new TranslateClient([
			'projectId' => $this->projectId
		]);
		//Log::instance()->add(Log::DEBUG, 'translate:');
		# The text to translate
		//$text = 'Hello, world!';
		# The target language
		//$target = 'ru';
		
		$format = 'text';
		# Translates some text into Russian
		$translation = $translate->translate($text, [
				'target' => $target,
				'format' => $format
		]);
		
		return $translation['text'];
	}
	
	function shorturl($longUrl)
	{
		//return $longUrl;
		$api_key= 'AIzaSyAbC-sYTUJiIcLOOyP2uaStVeRlZWvOChI';
		
		/* returns the shortened url */
		$connectURL = "https://firebasedynamiclinks.googleapis.com/v1/shortLinks?key=$api_key";
		
		/*
		$data = [
				'longDynamicLink' => "https://talkappi.page.link/?link=$longUrl",
				'suffix' => ['option'=>'SHORT']
		];
		*/
		
		$data = ['dynamicLinkInfo' => 
					[
							'dynamicLinkDomain' => "talkappi.page.link",
							'link' => $longUrl,
					],
				'suffix' => ['option'=>'SHORT']
				];
		
		$header = [
				'Content-Type: application/json',
		];
		
		$curl = curl_init();
		$timeout = 5;
		
		curl_setopt($curl, CURLOPT_URL, $connectURL);
		curl_setopt($curl, CURLOPT_CUSTOMREQUEST, 'POST');
		curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($data));
		curl_setopt($curl, CURLOPT_HTTPHEADER, $header);
		curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
		curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($curl, CURLOPT_HEADER, true);
		curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, $timeout);
		$response= curl_exec($curl);
		$header_size = curl_getinfo($curl, CURLINFO_HEADER_SIZE);
		curl_close($curl);
		$body = substr($response, $header_size);
		//Log::instance()->add(Log::DEBUG, "SHORTURL-BODY:" . $body);	
		
		$result = json_decode($body, true);
		if (array_key_exists('shortLink', $result)) return $result['shortLink'];
		return $longUrl;
	}
	
	private function shorturl_old($long_url)
	{
		$base_url = 'https://www.googleapis.com/urlshortener/v1/url?key=AIzaSyCnU2XrntlPM0FPuipSJJKhAJ15OLD1_TA';
		$data = [
				'longUrl' => $long_url
		];
		$header = [
				'Content-Type: application/json; charset=UTF-8'
		];
		$curl = curl_init();
		curl_setopt($curl, CURLOPT_URL, $base_url);
		curl_setopt($curl, CURLOPT_CUSTOMREQUEST, 'POST'); // post
		curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($data));
		curl_setopt($curl, CURLOPT_HTTPHEADER, $header); // リクエストにヘッダーを含める
		curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
		curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($curl, CURLOPT_HEADER, true);
		$response = curl_exec($curl);
		$header_size = curl_getinfo($curl, CURLINFO_HEADER_SIZE);
		curl_close($curl);
		$body = substr($response, $header_size);
		$result = json_decode($body, true);
		return $result['id'];
	}
}

?>
