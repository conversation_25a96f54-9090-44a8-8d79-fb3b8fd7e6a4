<?php defined('SYSPATH') OR die('No direct script access.');
use AWS\CRT\HTTP\Message;

class Model_Admincontractmodel extends Model_Basemodel
{
  public function get_clients($representative = NULL, $check_status = NULL, $show_invalid = NULL, $show_delete = NULL) 
  {
    $client_checks = $this->get_all_checks('client');
    $sql = "SELECT DISTINCT a.* FROM m_client a";
    if ($representative !== NULL) {
      $sql .= " INNER JOIN t_contract b ON a.client_code = b.client_code AND (b.sales_representative = :representative OR b.cs_representative = :representative)";
    }
    $sql .= " WHERE 1=1";
    if ($show_invalid === NULL) {
      $sql .= " AND a.invalid_flg = 0";
    }
    if ($show_delete === NULL) {
      $sql .= " AND a.delete_flg = 0";
    }
    $query = DB::query(Database::SELECT, $sql);
    $query->parameters(array(
      ':representative' => $representative
    ));
    $result = $query->execute('contract')->as_array();
    $clients = [];
    foreach ($result as $row) {
      $client = $row;
      if (isset($client_checks[$row['client_code']])) {
        $current_client_checkstatus = $client_checks[$row['client_code']];
        $client['check_status'] = $current_client_checkstatus;
        $client['last_check_time'] = $current_client_checkstatus['complete_time'];
        if ($check_status === NULL) {
          $clients[] = $client;
        } else {
          if ($current_client_checkstatus['cs_checkflg'] == $check_status || $current_client_checkstatus['sales_checkflg'] == $check_status || $current_client_checkstatus['admin_checkflg'] == $check_status || $current_client_checkstatus['accountant_checkflg'] == $check_status) {
            $clients[] = $client;
          }
        }
      } else {
        $client['check_status'] = NULL;
        $client['last_check_time'] = NULL;
        if ($check_status === NULL) {
          $clients[] = $client;
        }
      }
    }
    return $clients;
  }

  public function get_clients_option() {
    $sql = "SELECT client_code, client_name, department_name FROM m_client WHERE delete_flg = 0 AND invalid_flg = 0";
    $query = DB::query(Database::SELECT, $sql);
    $result = $query->execute('contract')->as_array();
    $options = [];
    foreach ($result as $row) {
      $options[] = ['code' => $row['client_code'], 'text' => $row['client_name'], 'depart' => $row['department_name']];
    }
    return $options;
  }

  public function get_client_by_code($client_code)
  {
    $sql = "SELECT * FROM m_client WHERE client_code = :client_code AND delete_flg = 0 AND invalid_flg = 0";
    $query = DB::query(Database::SELECT, $sql);
    $query->parameters(array(':client_code' => $client_code));
    $result = $query->execute('contract')->as_array();
    return $result;
  }

  public function delete_client_by_code($client_code, $type, $user_id)
  {
    try {
      $sql = "UPDATE m_client ";
      if ($type == 'delete') {
        $sql .= "SET delete_flg = 1, delete_user = :user, delete_time = :time ";
      } else if ($type == 'invalid') {
        $sql .= "SET invalid_flg = 1, invalid_user = :user, invalid_time = :time ";
      } else {
        throw new Exception('Invalid type');
      }
      $sql .= "WHERE client_code = :client_code";
      $query = DB::query(Database::UPDATE, $sql);
      $query->parameters([
        ':client_code' => $client_code,
        ':user' => $user_id,
        ':time' => date('Y-m-d H:i:s')
      ]);
      $query->execute('contract');
    } catch (\Throwable $th) {
      throw $th;
    }
  }

  public function create_new_client($post) {
    try {
      if ($post['client_code'] == '') {
        throw new Exception('Client code is required');
      }
      $existed = $this->get_client_by_code($post['client_code']);
      if (count($existed) > 0) {
        throw new Exception('duplicate');
      }
      $sql = "INSERT INTO m_client (client_code, client_name, client_name_for_search, department_name, department_name_for_search, country, segment) 
      VALUES (:client_code, :client_name, :client_name_for_search, :department_name, :department_name_for_search, :country, :segment)";
      $query = DB::query(Database::INSERT, $sql);
      $query->parameters(array(
        ':client_code' => $post['client_code'],
        ':client_name' => $post['client_name'],
        ':client_name_for_search' => $post['client_name_for_search'],
        ':department_name' => $post['department_name'],
        ':department_name_for_search' => $post['department_name_for_search'],
        ':country' => $post['country'],
        ':segment' => $post['segment']
      ));
      $query->execute('contract');
      return $post['client_code'];
    } catch (\Throwable $th) {
      throw $th;
    }
  }

  public function update_client($client_code, $post) {
    try {
      $sql = "UPDATE m_client 
      SET client_name = :client_name, client_name_for_search = :client_name_for_search, department_name = :department_name, department_name_for_search = :department_name_for_search, country = :country, segment = :segment 
      WHERE client_code = :client_code";
      $query = DB::query(Database::UPDATE, $sql);
      $query->parameters(array(
        ':client_code' => $client_code,
        ':client_name' => $post['client_name'],
        ':client_name_for_search' => $post['client_name_for_search'],
        ':department_name' => $post['department_name'],
        ':department_name_for_search' => $post['department_name_for_search'],
        ':country' => $post['country'],
        ':segment' => $post['segment']
      ));
      $query->execute('contract');
    } catch (\Throwable $th) {
      throw $th;
    }
  }

  public function diff_client($client_code, $post_data) {
    $diff = [];
    $existed_client_data = $this->get_client_by_code($client_code);
    if (count($existed_client_data) == 0) {
      return $diff;
    }
    $existed_client_data = $existed_client_data[0];
    foreach ($existed_client_data as $key => $value) {
      if ($key == 'client_code' || $key == 'sales_representative' || $key == 'cs_representative') {
        continue;
      }
      if ($value != $post_data[$key]) {
        $diff[$key] = [
          'from' => $value,
          'to' => $post_data[$key]
        ];
      }
    }
    return $diff;
  }

  public function get_invoices($representative = NULL, $check_status = NULL, $start_date = NULL, $end_date = NULL, $show_invalid = NULL, $show_delete = NULL) {
    $invoice_status = $this->get_all_checks('invoice');
    if ($end_date != '') {
      $end_date = date('Y-m-d', strtotime($end_date . ' +1 day'));
    }
    $sql = "SELECT DISTINCT i.* FROM m_invoice i";
    if ($representative !== NULL) {
      $sql .= " INNER JOIN t_contract c ON i.invoice_code = c.invoice_code AND (c.sales_representative = :representative OR c.cs_representative = :representative)";
    }
    $sql .= " WHERE 1=1 ";
    if ($start_date != '') {
      $sql .= " AND (i.ver_start_date is NULL OR i.ver_start_date >= :start_date)";
    }
    if ($end_date != '') {
      $sql .= " AND (i.ver_end_date is NULL OR i.ver_end_date <= :end_date)";
    }
    if ($show_invalid === NULL) {
      $sql .= " AND i.invalid_flg = 0";
    }
    if ($show_delete === NULL) {
      $sql .= " AND i.delete_flg = 0";
    }
    $query = DB::query(Database::SELECT, $sql);
    $query->parameters(array(
      ':representative' => $representative,
      ':start_date' => $start_date,
      ':end_date' => $end_date
    ));
    $result = $query->execute('contract')->as_array();
    $invoices = [];
    foreach($result as $row) {
      $invoice = $row;
      if (isset($invoice_status[$row['invoice_code']])) {
        $current_invoice_checkstatus = $invoice_status[$row['invoice_code']];
        if ($current_invoice_checkstatus['seq'] == $row['seq']) {
          $invoice['check_status'] = $current_invoice_checkstatus;
          $invoice['last_check_time'] = $current_invoice_checkstatus['complete_time'];
          if ($check_status === NULL) {
            $invoices[] = $invoice;
          } else {
            if ($current_invoice_checkstatus['cs_checkflg'] == $check_status || $current_invoice_checkstatus['sales_checkflg'] == $check_status || $current_invoice_checkstatus['admin_checkflg'] == $check_status || $current_invoice_checkstatus['accountant_checkflg'] == $check_status) {
              $invoices[] = $invoice;
            }
          }
        } else {
          $invoice['check_status'] = NULL;
          $invoice['last_check_time'] = NULL;
          if ($check_status === NULL) {
            $invoices[] = $invoice;
          }
        }
      } else {
        $invoice['check_status'] = NULL;
        $invoice['last_check_time'] = NULL;
        if ($check_status === NULL) {
          $invoices[] = $invoice;
        }
      }
    }
    return $invoices;
  }

  public function get_invoices_option() {
    $sql = "SELECT invoice_code, invoice_name, seq, department_name FROM m_invoice WHERE invalid_flg = 0 AND delete_flg = 0 ORDER BY invoice_code, seq";
    $query = DB::query(Database::SELECT, $sql);
    $result = $query->execute('contract')->as_array();
    $options = [];
    foreach ($result as $row) {
      $options[] = ['code' => $row['invoice_code'], 'text' => $row['invoice_name'] . ($row['seq'] >= 2 ? '(第' . $row['seq'] . '世代)':''), 'seq' => $row['seq'], 'depart' => $row['department_name']];
    }
    return $options;
  }

  public function get_invoice_by_code($invoice_code, $seq) {
    $sql = "SELECT * FROM m_invoice WHERE invoice_code = :invoice_code AND seq = :seq AND delete_flg = 0 AND invalid_flg = 0";
    $query = DB::query(Database::SELECT, $sql);
    $query->parameters(array(':invoice_code' => $invoice_code, ':seq' => $seq));
    $result = $query->execute('contract')->as_array();
    if (count($result) > 0) {
      return $result[0];
    }
    return null;
  }

  public function get_invoice_gens($invoice_code) {
    $sql = "SELECT seq, concat(invoice_name, '(', department_name, ')') as name FROM m_invoice WHERE invoice_code = :invoice_code AND delete_flg = 0 AND invalid_flg = 0 ORDER BY seq";
    $query = DB::query(Database::SELECT, $sql);
    $query->parameters(array(':invoice_code' => $invoice_code));
    $result = $query->execute('contract')->as_array('seq');
    return $result;
  }

  public function check_invoice_existed($invoice_code) {
    $sql = "SELECT invoice_code FROM m_invoice WHERE invoice_code = :invoice_code";
    $query = DB::query(Database::SELECT, $sql);
    $query->parameters(array(':invoice_code' => $invoice_code));
    $result = $query->execute('contract')->as_array();
    return count($result) > 0;
  }

  public function create_new_invoice($post) {
    try {
      if ($post['invoice_code'] == '') {
        throw new Exception('Invoice code is required');
      }
      if ($post['seq'] == '') {
        throw new Exception('Seq is required');
      }
      $existed = $this->get_invoice_by_code($post['invoice_code'], $post['seq']);
      if ($existed) {
        throw new Exception('Existed invoice code');
      }
      $sql = "INSERT INTO m_invoice (invoice_code, seq, invoice_name, department_name, invoice_recipient_to, invoice_recipient_cc, invoice_address, send_method, invoice_send_timing, invoice_span, payment_timing, payment_method, ver_start_date, ver_end_date) 
      VALUES (:invoice_code, :seq, :invoice_name, :department_name, :invoice_recipient_to, :invoice_recipient_cc, :invoice_address, :send_method, :invoice_send_timing, :invoice_span, :payment_timing, :payment_method, :ver_start_date, :ver_end_date)";
      $query = DB::query(Database::INSERT, $sql);
      $query->parameters(array(
        ':invoice_code' => $post['invoice_code'],
        ':seq' => $post['seq'],
        ':invoice_name' => $post['invoice_name'],
        ':department_name' => $post['department_name'],
        ':invoice_recipient_to' => $post['invoice_recipient_to'],
        ':invoice_recipient_cc' => $post['invoice_recipient_cc'],
        ':invoice_address' => $post['invoice_address'],
        ':send_method' => $post['send_method'],
        ':invoice_send_timing' => $post['invoice_send_timing'],
        ':invoice_span' => $post['invoice_span'],
        ':payment_timing' => $post['payment_timing'],
        ':payment_method' => $post['payment_method'],
        ':ver_start_date' => $post['ver_start_date'] == '' ? NULL : $post['ver_start_date'],
        ':ver_end_date' => $post['ver_end_date']  == '' ? NULL : $post['ver_end_date']
      ));
      $query->execute('contract');
      return $post['invoice_code'];
    } catch (\Throwable $th) {
      throw $th;
    }
  }

  public function update_invoice($invoice_code, $post) {
    try {
      Database::instance('contract')->begin();
      $sql = "UPDATE m_invoice 
      SET invoice_name = :invoice_name, 
      department_name = :department_name, 
      invoice_recipient_to = :invoice_recipient_to, 
      invoice_recipient_cc = :invoice_recipient_cc, 
      invoice_address = :invoice_address, 
      send_method = :send_method, 
      invoice_send_timing = :invoice_send_timing, 
      invoice_span = :invoice_span,
      payment_timing = :payment_timing,
      payment_method = :payment_method,
      ver_start_date = :ver_start_date,
      ver_end_date = :ver_end_date
      WHERE invoice_code = :invoice_code AND seq = :seq";
      $query = DB::query(Database::UPDATE, $sql);
      $query->parameters(array(
        ':invoice_code' => $invoice_code,
        ':invoice_name' => $post['invoice_name'],
        ':department_name' => $post['department_name'],
        ':invoice_recipient_to' => $post['invoice_recipient_to'],
        ':invoice_recipient_cc' => $post['invoice_recipient_cc'],
        ':invoice_address' => $post['invoice_address'],
        ':send_method' => $post['send_method'],
        ':invoice_send_timing' => $post['invoice_send_timing'],
        ':invoice_span' => $post['invoice_span'],
        ':seq' => $post['seq'],
        ':payment_timing' => $post['payment_timing'],
        ':payment_method' => $post['payment_method'],
        ':ver_start_date' => $post['ver_start_date'] == '' ? NULL : $post['ver_start_date'],
        ':ver_end_date' => $post['ver_end_date'] == '' ? NULL : $post['ver_end_date']
      ));
      $query->execute('contract');
      if ($post['invoice_code'] != $invoice_code && $this->check_if_can_delete('invoice', $invoice_code)) {
        // update invoice_code in m_invoice
        $update_invoice_code_sql = "UPDATE m_invoice SET invoice_code = :new_invoice_code WHERE invoice_code = :invoice_code";
        $update_invoice_code_query = DB::query(Database::UPDATE, $update_invoice_code_sql);
        $update_invoice_code_query->parameters(array(
          ':new_invoice_code' => $post['invoice_code'],
          ':invoice_code' => $invoice_code
        ));
        $update_invoice_code_query->execute('contract');
        // update t_diff
        $update_diff_sql = "UPDATE t_diff SET code = :new_invoice_code WHERE code = :invoice_code AND type = 'invoice'";
        $update_diff_query = DB::query(Database::UPDATE, $update_diff_sql);
        $update_diff_query->parameters(array(
          ':new_invoice_code' => $post['invoice_code'],
          ':invoice_code' => $invoice_code
        ));
        $update_diff_query->execute('contract');
        // update t_check
        $update_check_sql = "UPDATE t_check SET code = :new_invoice_code WHERE code = :invoice_code AND type = 'invoice'";
        $update_check_query = DB::query(Database::UPDATE, $update_check_sql);
        $update_check_query->parameters(array(
          ':new_invoice_code' => $post['invoice_code'],
          ':invoice_code' => $invoice_code
        ));
        $update_check_query->execute('contract');
      }
      Database::instance('contract')->commit();
    } catch (\Throwable $th) {
      Database::instance('contract')->rollback();
      throw $th;
    }
  }
  public function diff_invoice($invoice_code, $seq, $post_data) {
    $diff = [];
    $existed_invoice_data = $this->get_invoice_by_code($invoice_code, $seq);
    if (!$existed_invoice_data) {
      return $diff;
    }
    foreach ($existed_invoice_data as $key => $value) {
      if ($key == 'seq') {
        continue;
      }
      if ($key == 'invoice_address') {
        $existed_invoice_address = json_decode($value, true);
        $post_invoice_address = json_decode($post_data[$key], true);
        foreach ($existed_invoice_address as $address_key => $address_value) {
          if ($address_value != $post_invoice_address[$address_key]) {
            $diff[$address_key] = [
              'from' => $address_value,
              'to' => $post_invoice_address[$address_key]
            ];
          }
        }
      } else {
        if ($value != $post_data[$key]) {
          $diff[$key] = [
            'from' => $value,
            'to' => $post_data[$key]
          ];
        }
      }
    }
    return $diff;
  }

  public function delete_invoice_by_code($invoice_code, $seq, $type, $user_id) {
    try {
      $sql = "UPDATE m_invoice ";
      if ($type == 'delete') {
        $sql .= "SET delete_flg = 1, delete_user = :user, delete_time = :time ";
      } else if ($type == 'invalid') {
        $sql .= "SET invalid_flg = 1, invalid_user = :user, invalid_time = :time ";
      } else {
        throw new Exception('Invalid type');
      }
      $sql .= "WHERE invoice_code = :invoice_code AND seq = :seq";
      $query = DB::query(Database::DELETE, $sql);
      $query->parameters([
        ':invoice_code' => $invoice_code, 
        ':seq' => $seq,
        ':user' => $user_id,
        ':time' => date('Y-m-d H:i:s')
      ]);
      $query->execute('contract');
    } catch (\Throwable $th) {
      throw $th;
    }
  }
  
  public function create_new_generation_for_invoice($invoice_code, $old_generation_end_date, $new_generation_start_date) {
    Database::instance('contract')->begin();
    try {
      // get max seq
      $sql = "SELECT * FROM m_invoice WHERE invoice_code = :invoice_code ORDER BY seq DESC LIMIT 1";
      $query = DB::query(Database::SELECT, $sql);
      $query->parameters(array(':invoice_code' => $invoice_code));
      $old_gen_result = $query->execute('contract')->as_array();
      $new_seq = 1;
      if (count($old_gen_result) > 0) {
        $old_gen_result = $old_gen_result[0];
        $new_seq = intval($old_gen_result['seq']) + 1;
      } else {
        throw new Exception('No old generation found: invoice_code = ' . $invoice_code);
      }
      // insert new generation
      $sql = "INSERT INTO m_invoice (invoice_code, seq, invoice_name, department_name, invoice_recipient_to, invoice_recipient_cc, invoice_address, send_method, invoice_send_timing, invoice_span, ver_start_date) 
      VALUES (:invoice_code, :seq, :invoice_name, :department_name, :invoice_recipient_to, :invoice_recipient_cc, :invoice_address, :send_method, :invoice_send_timing, :invoice_span, :ver_start_date)";
      $query = DB::query(Database::INSERT, $sql);
      $query->parameters(array(
        ':invoice_code' => $invoice_code, 
        ':seq' => $new_seq,
        ':ver_start_date' => date('Y-m-d', strtotime($new_generation_start_date)),
        ':invoice_name' => $old_gen_result['invoice_name'],
        ':department_name' => $old_gen_result['department_name'],
        ':invoice_recipient_to' => $old_gen_result['invoice_recipient_to'],
        ':invoice_recipient_cc' => $old_gen_result['invoice_recipient_cc'],
        ':invoice_address' => $old_gen_result['invoice_address'],
        ':send_method' => $old_gen_result['send_method'],
        ':invoice_send_timing' => $old_gen_result['invoice_send_timing'],
        ':invoice_span' => $old_gen_result['invoice_span']
      ));
      $query->execute('contract');
      // update old generation ver_end_date
      $sql = "UPDATE m_invoice 
      SET ver_end_date = :ver_end_date
      WHERE invoice_code = :invoice_code AND seq = :seq";
      $query = DB::query(Database::UPDATE, $sql);
      $query->parameters(array(
        ':invoice_code' => $invoice_code,
        ':seq' => $old_gen_result['seq'],
        ':ver_end_date' => date('Y-m-d', strtotime($old_generation_end_date))
      ));
      $query->execute('contract');
      Database::instance('contract')->commit();
      return $new_seq;
    } catch (\Throwable $th) {
      Database::instance('contract')->rollback();
      throw $th;
    }
  }

  public function get_contracts($representative = NULL, $check_status = NULL, $show_invalid = NULL, $show_delete = NULL) {
    $contract_status = $this->get_all_checks('contract');
    $sql = "SELECT tc.*, mc.segment
    FROM t_contract tc 
    INNER JOIN m_client mc ON tc.client_code = mc.client_code AND mc.delete_flg = 0 AND mc.invalid_flg = 0
    WHERE 1=1";
    if (!is_null($representative)) {
      $sql .= " AND (tc.sales_representative = :representative OR tc.cs_representative = :representative)";
    }
    if ($show_invalid === NULL) {
      $sql .= " AND tc.invalid_flg = 0";
    }
    if ($show_delete === NULL) {
      $sql .= " AND tc.delete_flg = 0";
    }
    $sql .= " ORDER BY tc.contract_id, tc.seq ASC";
    $query = DB::query(Database::SELECT, $sql);
    $query->parameters(array(
      ':representative' => $representative
    ));
    $result = $query->execute('contract')->as_array();
    $contracts = [];
    foreach($result as $row) {
      $contract = $row;
      if (isset($contract_status[$row['contract_id']])) {
        $current_contract_checkstatus = $contract_status[$row['contract_id']];
        $contract['check_status'] = $current_contract_checkstatus;
        $contract['last_check_time'] = $current_contract_checkstatus['complete_time'];
        if ($check_status === NULL) {
          $contracts[] = $contract;
        } else {
          if ($current_contract_checkstatus['cs_checkflg'] == $check_status || $current_contract_checkstatus['sales_checkflg'] == $check_status || $current_contract_checkstatus['admin_checkflg'] == $check_status || $current_contract_checkstatus['accountant_checkflg'] == $check_status) {
            $contracts[] = $contract;
          }
        }
      } else {
        $contract['check_status'] = NULL;
        $contract['last_check_time'] = NULL;
        if ($check_status === NULL) {
          $contracts[] = $contract;
        }
      }
    }
    return $contracts;
  }

  public function get_contracts_by_client_code($client_code = NULL) {
    $sql = "SELECT * FROM t_contract WHERE client_code = :client_code ORDER BY seq";
    $query = DB::query(Database::SELECT, $sql);
    $query->parameters(array(':client_code' => $client_code));
    $result = $query->execute('contract')->as_array();
    return $result;
  }

  public function get_master_code_options($code_div, $structure = false) {
    if (!$structure) {
      return $this->get_code_div_kv($code_div, 'ja');
    } else {
      $items = $this->get_code_div_kv_with_parent_cd_with_sort($code_div, 'ja');
      $options = [];
      foreach ($items as $item) {
        if ($item['parent_cd']) {
          if (!isset($options[$item['parent_cd']])) {
            $options[$item['parent_cd']] = [
              'items' => []
            ];
          }
          $options[$item['parent_cd']]['items'][$item['class_cd']] = ['code' => $item['class_cd'], 'name' => $item['name'], 'sort' => $item['sort'], 'parent_cd' => $item['parent_cd']];
        } else {
          if (!isset($options[$item['class_cd']])) {
            $options[$item['class_cd']] = [];
          }
          $options[$item['class_cd']]['code'] = $item['class_cd'];
          $options[$item['class_cd']]['name'] = $item['name'];
          $options[$item['class_cd']]['sort'] = $item['sort'];
        }
      }
      return $options;
    }
  }

  function get_code_div_kv_with_parent_cd_with_sort($code_div, $lang_cd='ja')
	{
		$sql = "SELECT parent_cd, class_cd, name, sort FROM m_class_code WHERE code_div = :code_div AND lang_cd=:lang_cd ORDER BY sort, parent_cd, class_cd";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':code_div' => $code_div,
				':lang_cd' => $lang_cd,
		));
		$results = $query->execute()->as_array();
		return $results;
	}

  public function get_all_lang_items_dict() {
    $sql = "SELECT class_cd, name, lang_cd
    FROM m_class_code
    WHERE code_div = 888812";
    $query = DB::query(Database::SELECT, $sql);
    $result = $query->execute()->as_array();
    $dict = [];
    foreach ($result as $row) {
      if (!isset($dict[$row['class_cd']])) {
        $dict[$row['class_cd']] = [];
      }
      $dict[$row['class_cd']][$row['lang_cd']] = $row['name'];
    }
    return $dict;
  }

  public function get_contract_by_id($contract_id) {
    $sql = "SELECT * FROM t_contract WHERE contract_id = :contract_id AND invalid_flg=0 AND delete_flg=0 ORDER BY seq";
    $query = DB::query(Database::SELECT, $sql);
    $query->parameters(array(':contract_id' => $contract_id));
    $result = $query->execute('contract')->as_array();
    return $result;
  }

  public function get_contract_setting($contract_id, $setting_key)
  {
    $sql = "SELECT * FROM t_contract_setting WHERE contract_id = :contract_id AND setting_key = :setting_key";
    $query = DB::query(Database::SELECT, $sql);
    $query->parameters(array(':contract_id' => $contract_id, ':setting_key' => $setting_key));
    $result = $query->execute('contract')->as_array();
    if (count($result) > 0) {
      $result = $result[0];
      $decode_result = json_decode($result['setting_value'], true);
      return $decode_result;
    } else {
      return NULL;
    }
  }

  public function get_client_code_by_bot_id($bot_id) {
    $sql = "SELECT DISTINCT client_code FROM t_contract 
    WHERE FIND_IN_SET(:bot_id, billing_account) AND (billing_end_date IS NULL OR billing_end_date >= NOW())";
    $query = DB::query(Database::SELECT, $sql);
    $query->parameters(array(':bot_id' => $bot_id));
    $result = $query->execute('contract')->as_array()[0]["client_code"];
    return $result;
  }

  public function get_invoice_code_by_bot_id($bot_id) {
    $sql = "SELECT DISTINCT invoice_code FROM t_contract 
    WHERE FIND_IN_SET(:bot_id, billing_account) AND (billing_end_date IS NULL OR billing_end_date >= NOW())";
    $query = DB::query(Database::SELECT, $sql);
    $query->parameters(array(':bot_id' => $bot_id));
    $result = $query->execute('contract')->as_array()[0]["invoice_code"];
    return $result;
  }

  public function format_contract_data($datas) {
    $formatted = [];
    $common_data = [
      'client_code' => '',
      'invoice_code' => '',
      'sales_representative' => '',
      'cs_representative' => '',
      'tax_rate' => '',
      'memo' => '',
    ];
    foreach ($datas as $index => $data) {
      if ($index === 0) {
        $common_data = [
          'client_code' => $data['client_code'],
          'invoice_code' => $data['invoice_code'],
          'sales_representative' => $data['sales_representative'],
          'cs_representative' => $data['cs_representative'],
          'tax_rate' => $data['tax_rate'] ?? '',
          'memo' => $data['memo'] ?? '',
          'items' => []
        ];
      }
      $formatted[] = [
        'seq' => $data['seq'],
        'item' => $data['item'],
        'billing_account' => $data['billing_account'],
        'cost_type' => $data['cost_type'],
        'cost' => $data['cost'] ?? '',
        'cost_detail' => $data['cost_detail'] === NULL ? '' : json_decode($data['cost_detail'], true),
        'billing_start_date' => $data['billing_start_date'],
        'billing_end_date' => $data['billing_end_date'],
        'item_remarks' => $data['item_remarks'] ?? '',
      ];
    }
    $common_data['items'] = $formatted;
    return $common_data;
  }

  public function create_contract($post) {
    try {
      $new_contract_id = intval($this->get_max_contract_id()) + 1;
      $datas = json_decode($post['data'], true);
      Database::instance('contract')->begin();
      foreach($datas as $data) {
        $sql = "INSERT INTO t_contract (
          contract_id, 
          seq, 
          client_code,
          invoice_code,
          item,
          billing_account,
          cost_type,
          cost,
          cost_detail,
          billing_start_date,
          billing_end_date,
          tax_rate,
          memo,
          item_remarks,
          sales_representative,
          cs_representative
        ) 
        VALUES (
          :contract_id, 
          :seq, 
          :client_code,
          :invoice_code,
          :item,
          :billing_account,
          :cost_type,
          :cost,
          :cost_detail,
          :billing_start_date,
          :billing_end_date,
          :tax_rate,
          :memo,
          :item_remarks,
          :sales_representative,
          :cs_representative
        )";
        $query = DB::query(Database::INSERT, $sql);
        $query->parameters(array(
          ':contract_id' => $new_contract_id,
          ':seq' => $data['seq'],
          ':client_code' => $data['client_code'],
          ':invoice_code' => $data['invoice_code'] == '' ? NULL : $data['invoice_code'],
          ':item' => $data['item'],
          ':billing_account' => $data['billing_account'],
          ':cost_type' => $data['cost_type'],
          ':cost' => $data['cost'],
          ':cost_detail' => $data['cost_detail'],
          ':billing_start_date' => $data['billing_start_date'],
          ':billing_end_date' => $data['billing_end_date'],
          ':tax_rate' => $data['tax_rate'],
          ':memo' => $data['memo'],
          ':item_remarks' => $data['item_remarks'],
          ':sales_representative' => $data['sales_representative'],
          ':cs_representative' => $data['cs_representative']
        ));
        $query->execute('contract');
      }
      Database::instance('contract')->commit();
      return $new_contract_id;
    } catch (\Throwable $th) {
      Database::instance('contract')->rollback();
      throw $th;
    }
  }

  public function update_contract($contract_id, $post, $diff) {
    $datas = json_decode($post['data'], true);
    if (count($datas) == 0) {
      // 品目を全部削除されたら、該当契約を削除する
      try {
        Database::instance('contract')->begin();
        $delete_sql = "DELETE FROM t_contract WHERE contract_id = :contract_id";
        $delete_query = DB::query(Database::DELETE, $delete_sql);
        $delete_query->parameters(array(':contract_id' => $contract_id));
        $delete_query->execute('contract');
        Database::instance('contract')->commit();
      } catch (\Throwable $th) {
        Database::instance('contract')->rollback();
        throw $th;
      }
    } else {
      try {
        Database::instance('contract')->begin();
        // update / insert
        foreach ($datas as $data) {
          $insert_or_update_sql = "INSERT INTO t_contract (
            contract_id, 
            seq, 
            client_code,
            invoice_code,
            item,
            billing_account,
            cost_type,
            cost,
            cost_detail,
            billing_start_date,
            billing_end_date,
            tax_rate,
            memo,
            item_remarks,
            sales_representative,
            cs_representative
          )
          VALUES (
            :contract_id, 
            :seq, 
            :client_code,
            :invoice_code,
            :item,
            :billing_account,
            :cost_type,
            :cost,
            :cost_detail,
            :billing_start_date,
            :billing_end_date,
            :tax_rate,
            :memo,
            :item_remarks,
            :sales_representative,
            :cs_representative
          )
          ON DUPLICATE KEY UPDATE
            client_code = :client_code,
            invoice_code = :invoice_code,
            item = :item,
            billing_account = :billing_account,
            cost_type = :cost_type,
            cost = :cost,
            cost_detail = :cost_detail,
            billing_start_date = :billing_start_date,
            billing_end_date = :billing_end_date,
            tax_rate = :tax_rate,
            memo = :memo,
            item_remarks = :item_remarks,
            sales_representative = :sales_representative,
            cs_representative = :cs_representative";
          $insert_or_update_query = DB::query(Database::UPDATE, $insert_or_update_sql);
          $insert_or_update_query->parameters(array(
            ':contract_id' => $contract_id,
            ':seq' => $data['seq'],
            ':client_code' => $data['client_code'],
            ':invoice_code' => $data['invoice_code'] == '' ? NULL : $data['invoice_code'],
            ':item' => $data['item'],
            ':billing_account' => $data['billing_account'],
            ':cost_type' => $data['cost_type'],
            ':cost' => $data['cost'],
            ':cost_detail' => $data['cost_detail'] == '' ? NULL : $data['cost_detail'],
            ':billing_start_date' => $data['billing_start_date'],
            ':billing_end_date' => $data['billing_end_date'],
            ':tax_rate' => $data['tax_rate'],
            ':memo' => $data['memo'],
            ':item_remarks' => $data['item_remarks'],
            ':sales_representative' => $data['sales_representative'],
            ':cs_representative' => $data['cs_representative']
          ));
          $insert_or_update_query->execute('contract');
        }
        // delete
        if (isset($diff['items'])) {
          foreach ($diff['items'] as $seq => $diff_item) {
            if (isset($diff_item['item']) && isset($diff_item['item']['delete'])) {
              $delete_sql = "DELETE FROM t_contract WHERE contract_id = :contract_id AND seq = :seq";
              $delete_query = DB::query(Database::DELETE, $delete_sql);
              $delete_query->parameters(array(':contract_id' => $contract_id, ':seq' => $seq));
              $delete_query->execute('contract');
            }
          }
        }
        Database::instance('contract')->commit();
      } catch (\Throwable $th) {
        Database::instance('contract')->rollback();
        throw $th;
      }
    }
  }

  public function upsert_contract_setting($contract_id, $data, $user) {
    try {
      Database::instance('contract')->begin();
      $setting_data = json_decode($data, true);
      $delete_sql = "DELETE FROM t_contract_setting WHERE contract_id = :contract_id";
      $delete_query = DB::query(Database::DELETE, $delete_sql);
      $delete_query->parameters([
        ':contract_id' => $contract_id
      ]);
      $delete_query->execute('contract');
      foreach ($setting_data as $setting_key => $setting_value) {
        $insert_sql = "INSERT INTO t_contract_setting (contract_id, setting_key, setting_value, upd_user, upd_time) VALUES (:contract_id, :setting_key, :setting_value, :upd_user, :upd_time)";
        $insert_query = DB::query(Database::INSERT, $insert_sql);
        $insert_query->parameters([
          ':contract_id' => $contract_id,
          ':setting_key' => $setting_key,
          ':setting_value' => is_array($setting_value) ? json_encode($setting_value, JSON_UNESCAPED_UNICODE) : $setting_value,
          ':upd_user' => $user,
          ':upd_time' => date('Y-m-d H:i:s')
        ]);
        $insert_query->execute('contract');
      }
      Database::instance('contract')->commit();
    } catch (\Throwable $th) {
      Database::instance('contract')->rollback();
      throw $th;
    }
  }

  public function newcontract_log($post_data) {
    $datas = json_decode($post_data['data'], true);
    $upd_diff = [
      'items' => []
    ];
    foreach ($datas as $data) {
      $upd_diff['items'][$data['seq']] = [
        'item' => [
          'new' => $data['item']
        ],
        'billing_account' => [
          'new' => $data['billing_account']
        ],
        'cost_type' => [
          'new' => $data['cost_type']
        ],
        'cost' => [
          'new' => $data['cost']
        ],
        'cost_detail' => [
          'new' => $data['cost_detail']
        ]
      ];
      if ($data['billing_start_date'] != NULL) {
        $upd_diff['items'][$data['seq']]['billing_start_date'] = [
          'new' => $data['billing_start_date']
        ];
      }
      if ($data['billing_end_date'] != NULL) {
        $upd_diff['items'][$data['seq']]['billing_end_date'] = [
          'new' => $data['billing_end_date']
        ];
      }
      if ($data['item_remarks'] != NULL) {
        $upd_diff['items'][$data['seq']]['item_remarks'] = [
          'new' => $data['item_remarks']
        ];
      }
      if ($data['client_code'] && !isset($upd_diff['client_code'])) {
        $upd_diff['client_code'] = [
          'new' => $data['client_code']
        ];
      }
      if ($data['invoice_code'] && !isset($upd_diff['invoice_code'])) {
        $upd_diff['invoice_code'] = [
          'new' => $data['invoice_code']
        ];
      }
      if ($data['sales_representative'] && !isset($upd_diff['sales_representative'])) {
        $upd_diff['sales_representative'] = [
          'new' => $data['sales_representative']
        ];
      }
      if ($data['cs_representative'] && !isset($upd_diff['cs_representative'])) {
        $upd_diff['cs_representative'] = [
          'new' => $data['cs_representative']
        ];
      }
      if ($data['tax_rate'] && !isset($upd_diff['tax_rate'])) {
        $upd_diff['tax_rate'] = [
          'new' => $data['tax_rate']
        ];
      }
      if ($data['memo'] && !isset($upd_diff['memo'])) {
        $upd_diff['memo'] = [
          'new' => $data['memo']
        ];
      }
    }
    return $upd_diff;
  }

  public function diff_contract($contract_id, $post_data) {
    $datas = json_decode($post_data['data'], true);
    $existed_contract_data = $this->get_contract_by_id($contract_id);
    $format_data = $this->format_contract_data($existed_contract_data);
    $upd_diff = [
      'items' => []
    ];
    if (count($datas) == 0) {
      foreach ($format_data['items'] as $item) {
        $upd_diff['items'][$item['seq']] = [
          'item' => [
            'delete' => $item['item']
          ],
          'billing_account' => [
            'delete' => $item['billing_account']
          ],
          'cost_type' => [
            'delete' => $item['cost_type']
          ],
          'cost' => [
            'delete' => $item['cost']
          ],
          'cost_detail' => [
            'delete' => $item['cost_detail']
          ]
        ];
        if ($item['billing_start_date'] != NULL) {
          $upd_diff['items'][$item['seq']]['billing_start_date'] = [
            'delete' => $item['billing_start_date']
          ];
        }
        if ($item['billing_end_date'] != NULL) {
          $upd_diff['items'][$item['seq']]['billing_end_date'] = [
            'delete' => $item['billing_end_date']
          ];
        }
      }
      return $upd_diff;
    } else {
      foreach ($datas as $data) {
        if (!isset($upd_diff['client_code']) && $format_data['client_code'] != $data['client_code']) {
          $upd_diff['client_code'] = [
            'from' => $format_data['client_code'],
            'to' => $data['client_code']
          ];
        }
        if (!isset($upd_diff['invoice_code']) && $format_data['invoice_code'] != $data['invoice_code']) {
          $upd_diff['invoice_code'] = [
            'from' => $format_data['invoice_code'],
            'to' => $data['invoice_code']
          ];
        }
        if (!isset($upd_diff['sales_representative']) && $format_data['sales_representative'] != $data['sales_representative']) {
          $upd_diff['sales_representative'] = [
            'from' => $format_data['sales_representative'],
            'to' => $data['sales_representative']
          ];
        }
        if (!isset($upd_diff['cs_representative']) && $format_data['cs_representative'] != $data['cs_representative']) {
          $upd_diff['cs_representative'] = [
            'from' => $format_data['cs_representative'],
            'to' => $data['cs_representative']
          ];
        }
        if (!isset($upd_diff['tax_rate']) && $format_data['tax_rate'] != $data['tax_rate']) {
          $upd_diff['tax_rate'] = [
            'from' => $format_data['tax_rate'],
            'to' => $data['tax_rate']
          ];
        }
        if (!isset($upd_diff['memo']) && $format_data['memo'] != $data['memo']) {
          $upd_diff['memo'] = [
            'from' => $format_data['memo'],
            'to' => $data['memo']
          ];
        }
        foreach($format_data['items'] as $item) {
          if ($item['seq'] == $data['seq']) {
            $upd_diff['items'][$item['seq']] = [];
            foreach($item as $key => $value) {
              if ($key == 'seq') {
                continue;
              }
              if ($key == 'cost_detail') {
                if ($item['cost_detail'] == '' && $data['cost_detail'] == '') {
                  continue;
                }
                if (
                  ($item['cost_detail'] == '' && $data['cost_detail'] != '') || 
                  ($item['cost_detail'] != '' && $data['cost_detail'] == '')
                ) {
                  $upd_diff['items'][$item['seq']]['cost_detail'] = [
                    'from' => $item['cost_detail'],
                    'to' => $data['cost_detail']
                  ];
                } else {
                  $existed_cost_detail = $item['cost_detail'];
                  $post_cost_detail = json_decode($data['cost_detail'], true);
                  ksort($existed_cost_detail);
                  ksort($post_cost_detail);
                  $existed_cost_detail_str = json_encode($existed_cost_detail, JSON_UNESCAPED_UNICODE);
                  $post_cost_detail_str = json_encode($post_cost_detail, JSON_UNESCAPED_UNICODE);
                  if ($existed_cost_detail_str != $post_cost_detail_str) {
                    $upd_diff['items'][$item['seq']]['cost_detail'] = [
                      'from' => $existed_cost_detail,
                      'to' => $post_cost_detail
                    ];
                  }
                }
                continue;
              }
              if ($value != $data[$key]) {
                $upd_diff['items'][$item['seq']][$key] = [
                  'from' => $item[$key],
                  'to' => $data[$key]
                ];
              }
            }
            if (!empty($upd_diff['items'][$item['seq']]) && !isset($upd_diff['items'][$item['seq']]['item'])) {
              $upd_diff['items'][$item['seq']]['item'] = [
                'fixed' => $item['item']
              ];
            }
          }
        }
        foreach($format_data['items'] as $item) {
          // delete item
          if (!isset($upd_diff['items'][$item['seq']])) {
            $upd_diff['items'][$item['seq']] = [
              'item' => [
                'delete' => $item['item']
              ],
              'billing_account' => [
                'delete' => $item['billing_account']
              ],
              'cost_type' => [
                'delete' => $item['cost_type']
              ],
              'cost' => [
                'delete' => $item['cost']
              ],
              'cost_detail' => [
                'delete' => $item['cost_detail']
              ]
            ];
            if ($item['billing_start_date'] != NULL) {
              $upd_diff['items'][$item['seq']]['billing_start_date'] = [
                'delete' => $item['billing_start_date']
              ];
            }
            if ($item['billing_end_date'] != NULL) {
              $upd_diff['items'][$item['seq']]['billing_end_date'] = [
                'delete' => $item['billing_end_date']
              ];
            }
          }
        }
        if (!isset($upd_diff['items'][$data['seq']])) {
          // new item
          $upd_diff['items'][$data['seq']] = [
            'item' => [
              'new' => $data['item']
            ],
            'billing_account' => [
              'new' => $data['billing_account']
            ],
            'cost_type' => [
              'new' => $data['cost_type']
            ],
            'cost' => [
              'new' => $data['cost']
            ],
            'cost_detail' => [
              'new' => $data['cost_detail']
            ]
          ];
          if ($data['billing_start_date'] != NULL) {
            $upd_diff['items'][$data['seq']]['billing_start_date'] = [
              'new' => $data['billing_start_date']
            ];
          }
          if ($data['billing_end_date'] != NULL) {
            $upd_diff['items'][$data['seq']]['billing_end_date'] = [
              'new' => $data['billing_end_date']
            ];
          }
          if ($data['item_remarks'] != NULL) {
            $upd_diff['items'][$data['seq']]['item_remarks'] = [
              'new' => $data['item_remarks']
            ];
          }
        }
      }

      foreach ($datas as $data) {
        if (count($upd_diff['items'][$data['seq']]) == 0) {
          unset($upd_diff['items'][$data['seq']]);
        }
      }
      if (count($upd_diff['items']) == 0) {
        unset($upd_diff['items']);
      }
      return $upd_diff;
    }
  }

  public function check_if_need_recheck($diff_contract) {
    $flag = false;
    if (isset($diff_contract['items'])) {
      foreach ($diff_contract['items'] as $item) {
        if (isset($item['item']) || isset($item['cost']) || isset($item['billing_start_date']) || isset($item['billing_end_date'])) {
          $flag = true;
          break;
        }
      }
    } 
    return $flag;
  }

  public function delete_contract_by_id($contract_id, $type, $user_id) {
    try {
      $sql = "UPDATE t_contract ";
      if ($type == 'delete') {
        $sql .= "SET delete_flg = 1, delete_user = :user, delete_time = :time ";
      } else if ($type == 'invalid') {
        $sql .= "SET invalid_flg = 1, invalid_user = :user, invalid_time = :time ";
      } else {
        throw new Exception('Invalid type');
      }
      $sql .= "WHERE contract_id = :contract_id";
      $query = DB::query(Database::DELETE, $sql);
      $query->parameters([
        ':contract_id' => $contract_id, 
        ':user' => $user_id,
        ':time' => date('Y-m-d H:i:s')
      ]);
      $query->execute('contract');
    } catch (\Throwable $th) {
      throw $th;
    }
  }

  public function get_invoice_payments() {
    $invoices = $this->get_all_invoices();
    $sql = "SELECT ti.* FROM t_invoice ti ORDER BY ti.invoice_id";
    $query = DB::query(Database::SELECT, $sql);
    $results = $query->execute('contract')->as_array();
    foreach ($results as &$result) {
      if (isset($invoices[$result['invoice_code']])) {
        $invoice = $invoices[$result['invoice_code']];
        $invoice_date = $result['invoice_date'];
        foreach ($invoice as $value) {
          if (
            ($value['ver_start_date'] == NULL || strtotime($value['ver_start_date']) <= strtotime($invoice_date)) && 
            ($value['ver_end_date'] == NULL || strtotime($value['ver_end_date']) >= strtotime($invoice_date))
          ) {
            $result['send_method'] = $value['send_method'];
            $result['seq'] = $value['seq'];
            $result['invoice_name'] = $value['invoice_name'];
            $result['department_name'] = $value['department_name'];
            break;
          }
        }
        if (!isset($result['send_method'])) {
          $result['invoice_name'] = NULL;
          $result['department_name'] = NULL;
          $result['seq'] = NULL;
          $result['send_method'] = NULL;
        }
      } else {
        $result['invoice_name'] = NULL;
        $result['department_name'] = NULL;
        $result['seq'] = NULL;
        $result['send_method'] = NULL;
      }
    }
    return $results;
  }

  public function get_invoice_payments_by_invoice_code($invoice_code) {
    $invoices = $this->get_all_invoices();
    $sql = "SELECT ti.* FROM t_invoice ti WHERE ti.invoice_code=:invoice_code ORDER BY ti.invoice_id";
    $query = DB::query(Database::SELECT, $sql);
    $query->parameters(array(':invoice_code' => $invoice_code));
    $results = $query->execute('contract')->as_array();
    foreach ($results as &$result) {
      if (isset($invoices[$result['invoice_code']])) {
        $invoice = $invoices[$result['invoice_code']];
        $invoice_date = $result['invoice_date'];
        foreach ($invoice as $value) {
          if (
            ($value['ver_start_date'] == NULL || strtotime($value['ver_start_date']) <= strtotime($invoice_date)) && 
            ($value['ver_end_date'] == NULL || strtotime($value['ver_end_date']) >= strtotime($invoice_date))
          ) {
            $result['send_method'] = $value['send_method'];
            $result['seq'] = $value['seq'];
            $result['invoice_name'] = $value['invoice_name'];
            $result['department_name'] = $value['department_name'];
            break;
          }
        }
        if (!isset($result['send_method'])) {
          $result['invoice_name'] = NULL;
          $result['department_name'] = NULL;
          $result['seq'] = NULL;
          $result['send_method'] = NULL;
        }
      } else {
        $result['invoice_name'] = NULL;
        $result['department_name'] = NULL;
        $result['seq'] = NULL;
        $result['send_method'] = NULL;
      }
    }
    return $results;
  }

  private function get_valid_invoice($invoice_code, $setDate) {
    $current_date = date('Y-m-d');
    if ($setDate != NULL) {
      $current_date = date('Y-m-d', strtotime($setDate));
    }
    $sql = "SELECT * 
    FROM m_invoice 
    WHERE invoice_code = :invoice_code 
    AND (ver_start_date IS NULL OR ver_start_date <= :current_date) AND (ver_end_date IS NULL OR ver_end_date >= :current_date) 
    AND delete_flg = 0 AND invalid_flg = 0
    ORDER BY seq DESC LIMIT 1";
    $query = DB::query(Database::SELECT, $sql);
    $query->parameters(array(':invoice_code' => $invoice_code, ':current_date' => $current_date));
    $result = $query->execute('contract')->as_array();
    if (count($result) > 0) {
      return $result[0];
    } else {
      return null;
    }
  }

  public function get_all_invoices() {
    $invoices = [];
    $sql = "SELECT * 
    FROM m_invoice 
    WHERE delete_flg = 0 AND invalid_flg = 0
    ORDER BY invoice_code, seq DESC";
    $query = DB::query(Database::SELECT, $sql);
    $results = $query->execute('contract')->as_array();
    foreach ($results as $result) {
      $invoices[$result['invoice_code']][] = $result;
    }
    return $invoices;
  }

  public function get_invoices_by_invoice_code($invoice_code) {
    $invoices = [];
    $sql = "SELECT * FROM m_invoice WHERE invoice_code = :invoice_code ORDER BY seq DESC";
    $query = DB::query(Database::SELECT, $sql);
    $query->parameters(array(':invoice_code' => $invoice_code));
    $results = $query->execute('contract')->as_array();
    foreach ($results as $result) {
      $invoices[$result['invoice_code']][] = $result;
    }
    return $invoices;
  }

  public function delete_invoice_payment_by_id($invoice_id) {
    try {
      $check_sql = "SELECT status FROM t_invoice WHERE invoice_id = :invoice_id";
      $check_query = DB::query(Database::SELECT, $check_sql);
      $check_query->parameters(array(':invoice_id' => $invoice_id));
      $check_result = $check_query->execute('contract')->as_array();
      if (count($check_result) == 0) {
        throw new Exception('No existed invoice payment found: ' . $invoice_id);
      }
      if ($check_result[0]['status'] == '1') {
        throw new Exception('This invoice payment has been sent: '. $invoice_id);
      }
      $sql = "DELETE FROM t_invoice WHERE invoice_id = :invoice_id";
      $query = DB::query(Database::DELETE, $sql);
      $query->parameters(array(':invoice_id' => $invoice_id));
      $query->execute('contract');
    } catch (\Throwable $th) {
      throw $th;
    }
  }

  public function get_invoice_payment_for_pdf($invoice_id) {
    $sql = "SELECT a.*
    FROM t_invoice a
    WHERE invoice_id = :invoice_id";
    $query = DB::query(Database::SELECT, $sql);
    $query->parameters(array(':invoice_id' => $invoice_id));
    $result = $query->execute('contract')->as_array();
    if (count($result) == 0) {
      return NULL;
    }
    $result = $result[0];
    $invoice_master = $this->get_valid_invoice($result['invoice_code'], $result['invoice_date']);
    if ($invoice_master == NULL) {
      return NULL;
    }
    $contract_id = $result['contract_id'];
    $invoice_name = $invoice_master['invoice_name'];
    $department_name = $invoice_master['department_name'];
    $invoice_address = json_decode($invoice_master['invoice_address'], true);
    $payment_method = $invoice_master['payment_method'];
    $invoice_details = json_decode($result['invoice_details'], true);
    $pdf_data = [
      'title' => $result['invoice_number'],
      'name' => $invoice_name,
      'department' => $department_name,
      'postcode' => $invoice_address['postal_code'] ?? '',
      'address' => $invoice_address['prefectures'] . $invoice_address['city'],
      'building' => $invoice_address['building'] ?? '',
      'invoice_code' => $result['invoice_number'],
      'invoice_date' => $result['invoice_date'],
      'payment_due_date' => $result['payment_due_date'],
      'remark' => '',
      'invoice_details' => $invoice_details
    ];
    if ($payment_method == 'robot_payment') {
      $pdf_data['remark'] = '<div>※毎月10⽇にご指定の⼝座から振替させていただきます。10日が口座振替システムの休業日の場合、翌営業日の振替となります。</div><div>※振替名義は「JCB)ロボットペイ」でございます。弊社名義ではございませんのでご注意願います。</div>';
      $pdf_data['bank_transfer_style'] = 'style="display:none;"';
    } else if ($this->check_if_invoice_order_start($contract_id, $invoice_details)) {
      $pdf_data['remark'] = '<div>2025年7月より、「talkappi INQUIRY」は「talkappi INQUIRY」と「talkappi ORDER」の2つのサービスに分割されました。</div><div>なお、2025年6月以前より「talkappi INQUIRY」をご契約いただいているお客様につきましては、引き続き両サービスをご利用いただけます。</div><div>本件に伴う請求金額の変更はございません。</div>';
    } else {
      $pdf_data['bank_transfer_style'] = '';
    }
    return $pdf_data;
  }

  private function check_if_invoice_order_start($contract_id, $invoice_details) {
    $sql = "SELECT setting_value FROM t_contract_setting WHERE contract_id = :contract_id AND setting_key = 'contract_set'";
    $query = DB::query(Database::SELECT, $sql);
    $query->parameters(array(':contract_id' => $contract_id));
    $result = $query->execute('contract')->as_array();
    if (count($result) == 0) return false;
    
    $setting_value = json_decode($result[0]['setting_value'], true);
    
    $invoice_seqs = array_column($invoice_details, 'seq');
    $flag = false;
    foreach ($setting_value as $setting) {
      if ($setting['setName'] === 'INQUIRY+ORDER') {
        $sets = $setting['setSeqs'];
        if (!empty(array_intersect($invoice_seqs, $sets))) {
          $flag = true;
          break;
        }
      }
    }    
    return $flag;
  }

  public function get_invoicepayment_by_id($invoice_id) {
    $sql = "SELECT * FROM t_invoice WHERE invoice_id = :invoice_id";
    $query = DB::query(Database::SELECT, $sql);
    $query->parameters(array(':invoice_id' => $invoice_id));
    $result = $query->execute('contract')->as_array();
    if (count($result) > 0) {
      return $result[0];
    } else {
      return null;
    }
  }

  public function generate_invoicepayment_by_id($contract_id, $setDate = NULL, $invoicedate_check_skip = false) {
      $results = $this->generate_invoicepayment($setDate, $contract_id, $invoicedate_check_skip);
      $generate_datas = $results['invoices'];
      $inserted_ids = [];
      foreach ($generate_datas as $data) {
        try {
          $inserted_ids[] = $this->create_or_update_invoicepayment($data);
        } catch (\Throwable $th) {
          Log::instance()->add(Log::ERROR, "ERROR: Contract ID: " . $contract_id . " - " . $th->getMessage());
        }
      }
      return $inserted_ids;
  }

  private function is_samemonth($start_date, $end_date) {
    $start_month = date('Y-m', strtotime($start_date));
    $end_month = date('Y-m', strtotime($end_date));
    return $start_month == $end_month;
  }

  private function generate_payment_invoice_dates($send_timing, $span, $cost_type, $billing_start_date, $billing_end_date, $current_date, $new_flg) {
    try {
      $y = date('Y', strtotime($current_date));
      $m = date('m', strtotime($current_date));
      $first_billing_start_date = $billing_start_date;
      $timestamp = strtotime($first_billing_start_date);
      $start_date = date('d', $timestamp);
      $invoice_date = "";
      $invoice_billing_start_date = "";
      $invoice_billing_end_date = "";
      $need_divide = false;
      if ($cost_type == '2' && intval($span) === 1 && $start_date != '01' && $new_flg) { // 月額費用 && スパンは１ヶ月
        $need_divide = true;
      }

      // generate billing_start_date and billing_end_date by span and cost_type
      if ($cost_type == '1' || $cost_type == '3') {
        // 初期費用の場合：初回請求開始日と請求終了日同じ、請求日は初回請求開始日
        // その他（単発）の場合は、基本的に初期費用と同じ
        $invoice_billing_start_date = $billing_start_date;
        $invoice_billing_end_date = $billing_end_date;
      } else {
        // 月額費用とその他（継続発生）、その他（周期発生）の処理
        if ($span == 0) {
          // 日割りの場合は、請求開始日と請求終了日は実際のもので、計算がいらない
          $invoice_billing_start_date = $billing_start_date;
          $invoice_billing_end_date = $billing_end_date;
        } else {
          $correct_date = $y . "-" . $m . "-" . $start_date;
          if ($send_timing == "1" || $send_timing == "2") {
            $invoice_billing_start_date = $correct_date;
            $invoice_billing_end_date = date('Y-m-d', strtotime($correct_date . ' +'. $span .' month - 1 day'));
            if ($need_divide) {
              if ($this->is_samemonth($invoice_billing_start_date, $first_billing_start_date)) {
                $invoice_billing_start_date = date('Y-m-01', strtotime($invoice_billing_start_date));
                $invoice_billing_end_date = date('Y-m-t', strtotime($invoice_billing_start_date));
              } else {
                if ($billing_end_date && $this->is_samemonth($invoice_billing_end_date, $billing_end_date)) {
                  $invoice_billing_start_date = date('Y-m-01', strtotime($invoice_billing_start_date));
                  $invoice_billing_end_date = date('Y-m-d', strtotime($billing_end_date));
                } else {
                  $invoice_billing_start_date = date('Y-m-01', strtotime($invoice_billing_start_date));
                  $invoice_billing_end_date = date('Y-m-t', strtotime($invoice_billing_start_date));
                }
              }
            }
          } else if ($send_timing == "3" || $send_timing == "4" || $send_timing == "5") {
            if ($start_date == "01") {
              $invoice_billing_start_date = date('Y-m-d', strtotime($correct_date . ' -'. ($span-1) .' month'));
              $invoice_billing_end_date = date('Y-m-t', strtotime($correct_date));
            } else {
              $invoice_billing_start_date = date('Y-m-d', strtotime($correct_date . ' -'. $span .' month'));
              $invoice_billing_end_date = date('Y-m-d', strtotime($correct_date . ' -1 day'));
            }
            if ($need_divide) {
              if ($this->is_samemonth($invoice_billing_end_date, $first_billing_start_date)) {
                $invoice_billing_start_date = date('Y-m-d', strtotime($first_billing_start_date));
                $invoice_billing_end_date = date('Y-m-t', strtotime($first_billing_start_date));
              } else {
                if ($billing_end_date && $this->is_samemonth($invoice_billing_end_date, $billing_end_date)) {
                  $invoice_billing_start_date = date('Y-m-01', strtotime($billing_end_date));
                  $invoice_billing_end_date = date('Y-m-d', strtotime($billing_end_date));
                } else {
                  $invoice_billing_start_date = date('Y-m-01', strtotime($invoice_billing_end_date));
                  $invoice_billing_end_date = date('Y-m-t', strtotime($invoice_billing_end_date));
                }
              }
            }
          } else if ($send_timing == '6') {
            // 請求開始前月　最終日
            $actural_start_date = date('Y-m-'.$start_date, strtotime(date('Y-m-01', strtotime($correct_date)) . ' +1 month'));
            $invoice_billing_start_date = $actural_start_date;
            $invoice_billing_end_date = date('Y-m-d', strtotime($actural_start_date . ' +'. $span .' month -1 day'));
            if ($need_divide) {
              if ($this->is_samemonth($invoice_billing_start_date, $first_billing_start_date)) {
                $invoice_billing_start_date = date('Y-m-d', strtotime($first_billing_start_date));
                $invoice_billing_end_date = date('Y-m-t', strtotime($first_billing_start_date));
              } else {
                if ($billing_end_date && $this->is_samemonth($invoice_billing_end_date, $billing_end_date)) {
                  $invoice_billing_start_date = date('Y-m-01', strtotime($billing_end_date));
                  $invoice_billing_end_date = date('Y-m-d', strtotime($billing_end_date));
                } else {
                  $invoice_billing_start_date = date('Y-m-01', strtotime($invoice_billing_start_date));
                  $invoice_billing_end_date = date('Y-m-t', strtotime($invoice_billing_start_date));
                }
              }
            }
          } else {
            throw new Exception('Invalid send_timing: ' . $send_timing);
          }
        }
      }

      // generate invoice_date by billing_invoice_period and send_timing
      if ($send_timing == "1") {
        // 請求開始月 最初日
        $invoice_date = date('Y-m-01', strtotime($invoice_billing_start_date));
        if (date("Y-m", strtotime($current_date)) == date("Y-m", strtotime($first_billing_start_date)) && date('d', strtotime($first_billing_start_date)) > 1) {
          $invoice_date = $invoice_billing_start_date;
        }
      } else if ($send_timing == "2") {
        // 請求開始月 最終日
        $invoice_date = date('Y-m-t', strtotime(date('Y-m-01', strtotime($invoice_billing_start_date))));
      } else if ($send_timing == "4") {
        // 請求終了月 最初日
        $invoice_date = date('Y-m-01', strtotime($invoice_billing_end_date));
      } else if ($send_timing == "5") {
        // 請求終了月 最終日
        $invoice_date = date('Y-m-t', strtotime(date('Y-m-01', strtotime($invoice_billing_end_date))));
      } else if ($send_timing == "3") {
        // 請求終了月 翌月初日
        $invoice_date = date('Y-m-01', strtotime(date('Y-m-01', strtotime($invoice_billing_end_date)) . ' +1 month'));
      } else if ($send_timing == '6') {
        // 請求開始前月 最終日
        $invoice_date = date('Y-m-t', strtotime(date('Y-m-01', strtotime($invoice_billing_start_date)) . ' -1 month'));
      } else {
        throw new Exception('Invalid send_timing: ' . $send_timing);
      }

      return [
        'invoice_date' => $invoice_date,
        'invoice_billing_start_date' => $invoice_billing_start_date,
        'invoice_billing_end_date' => $invoice_billing_end_date
      ];

    } catch (\Throwable $th) {
      throw $th;
    }
  }

  public function generate_invoicepayment($setDate = NULL, $setContractId = NULL, $invoicedate_check_skip = false) {
    $y = date('Y');
    $m = date('m');
    $d = date('d');
    if ($setDate) {
      $y = date('Y', strtotime($setDate));
      $m = date('m', strtotime($setDate));
      $d = date('d', strtotime($setDate));
    }
    $current_date = $y.'-'.$m.'-'.$d;
    Log::instance()->add(Log::INFO, 'Start generate invoice payment: ' . $current_date);
    $invoice_masters = $this->get_all_invoices();
    $generating_invoices = [];
    $errors = [];
    $items = $this->get_all_lang_items_dict();
    // すでに請求開始とまだ請求終了していない契約を取得
    $sql = "SELECT tc.*, mc.country
    FROM t_contract tc 
    LEFT JOIN m_client mc ON tc.client_code = mc.client_code AND mc.delete_flg = 0 AND mc.invalid_flg = 0
    WHERE tc.invalid_flg=0 AND tc.delete_flg=0 AND tc.billing_start_date IS NOT NULL AND tc.invoice_code IS NOT NULL ";
    if ($setContractId !== NULL) {
      $sql .= " AND tc.contract_id = :contract_id ";
    }
    $sql .= "ORDER BY tc.contract_id, tc.seq ASC";
    $query = DB::query(Database::SELECT, $sql);
    $query->parameters(array(
      ':start_date' => $current_date,
      ':end_date' => $current_date,
      ':contract_id' => $setContractId
    ));
    $result = $query->execute('contract')->as_array();
    foreach ($result as $row) {
      try {
        $contract_id = $row['contract_id'];
        if (is_null($row['country'])) {
          Log::instance()->add(Log::INFO, 'Skip generate invoice payment: contract_id:' . $contract_id . ' - country is null');
          continue;
        }
        $lang_cd = 'ja';
        if ($row['country'] != 'ja') {
          $lang_cd = 'en';
        }
        // 契約のチェック状況を確認
        if ($this->is_in_check($contract_id, 'contract')) {
          Log::instance()->add(Log::INFO, 'Skip generate invoice payment: contract_id:' . $contract_id . ' - contract is in check');
          continue;
        }
        $client_code = $row['client_code'];
        if ($this->is_in_check($client_code, 'client')) {
          Log::instance()->add(Log::INFO, 'Skip generate invoice payment: contract_id:' . $contract_id . ' - client is in check');
          continue;
        }
        if (!isset($invoice_masters[$row['invoice_code']]) || count($invoice_masters[$row['invoice_code']]) == 0) {
          Log::instance()->add(Log::INFO, 'Skip generate invoice payment: contract_id:' . $contract_id . ' - no invoice master found');
          throw new ErrorException('No invoice master found: ' . $row['invoice_code']);
        }
        $invoice_master = $invoice_masters[$row['invoice_code']];
        $checked_invoices = [];
        // 該当世代請求先のチェック状況を確認
        foreach ($invoice_master as $invoice) {
          if (!$this->is_in_check($row['invoice_code'], 'invoice', $invoice['seq'])) {
            $checked_invoices[] = $invoice;
          }
        }
        unset($invoice);
        if (count($checked_invoices) == 0) {
          Log::instance()->add(Log::INFO, 'Skip generate invoice payment: contract_id:' . $contract_id . ' - invoice is in check');
          continue;
        }

        $send_timing = NULL;
        $span = NULL;
        $payment_timing = NULL;

        $first_billing_start_date = $row['billing_start_date'];
        $cost_type = $row['cost_type'];
        $invoice_date = '';
        $invoice_billing_start_date = '';
        $invoice_billing_end_date = '';
        $new_flg = $row['new_flg'] == 1; // 新しい仕様適用かどうかのフラグ

        foreach($checked_invoices as $invoice) {
          $send_timing = $invoice['invoice_send_timing'];
          $span = intval($invoice['invoice_span']);
          $payment_timing = $invoice['payment_timing'];

          $dates = $this->generate_payment_invoice_dates($send_timing, $span, $cost_type, $row['billing_start_date'], $row['billing_end_date'], $current_date, $new_flg);
          $invoice_date = $dates['invoice_date'];
          $invoice_billing_start_date = $dates['invoice_billing_start_date'];
          $invoice_billing_end_date = $dates['invoice_billing_end_date'];
          if (
              ($invoice['ver_start_date'] == NULL || strtotime($invoice['ver_start_date']) <= strtotime($invoice_date)) && 
              ($invoice['ver_end_date'] == NULL || strtotime($invoice['ver_end_date']) >= strtotime($invoice_date))
          ) {
            break;
          } else {
            $send_timing = NULL;
            $span = NULL;
            $payment_timing = NULL;
          }
        }

        if (is_null($send_timing) || is_null($span) || is_null($payment_timing)) {
          // 請求先が見つかったが、請求期間外のため、請求書を作成しません。
          // ＊エラーではないので、ログのみ出力
          Log::instance()->add(Log::INFO, 'Skip generate invoice payment: contract_id:' . $contract_id . ' - out of billing period');
          continue;
        }
      
        Log::instance()->add(Log::INFO, 'Generate invoice payment: contract_id:' . $contract_id . ' - item:' . $row['item'] . ' - invoice_date:' . $invoice_date . ' - start_date:' . $invoice_billing_start_date . ' - end_date:' . $invoice_billing_end_date);
        $need_divide = $new_flg && $cost_type == '2' && intval($span) === 1 && date('d', strtotime($first_billing_start_date)) != '01';
        // check if this invoice should be generate
        // check .1: 作成のタイミングをチェック - 請求日が過ぎているか、5日以上経過しているため、請求書を作成しません。 
        if ($invoicedate_check_skip === false) {
          $date1 = new DateTime($invoice_date);
          $date2 = new DateTime($current_date);
          $interval = $date1->diff($date2, false);
          if ($date2->getTimestamp() > $date1->getTimestamp() || $interval->days > 5) {
            Log::instance()->add(Log::INFO, 'Skip generate invoice payment in check-1: ' . $contract_id);
            continue;
          }
        }
      
        // check .2: check if this invoice should be generated by span: spanは日割り、１ヶ月以外の場合、請求開始日と初回請求開始日の差がspanの倍数でない場合、請求書を作成しません。
        if ($span > 1) {
          $date1 = new DateTime($first_billing_start_date);
          $date2 = new DateTime($invoice_billing_start_date);
          $interval = $date1->diff($date2);
          $months = $interval->y * 12 + $interval->m;
          if ($date1->getTimestamp() > $date2->getTimestamp() || $months % $span != 0) {
            Log::instance()->add(Log::INFO, 'Skip generate invoice payment in check-2: ' . $contract_id);
            continue;
          }
        }

        // check .3: check if this invoice period is out of contract period: 請求期間が契約期間を超えている場合、請求書を作成しません。
        if (strtotime($invoice_billing_start_date) < strtotime($row['billing_start_date'])) {
          Log::instance()->add(Log::INFO, 'Skip generate invoice payment in check-3.1: ' . $contract_id);
          continue;
        }
        if ($row['billing_end_date'] != NULL && strtotime($invoice_billing_start_date) > strtotime($row['billing_end_date'])) {
          Log::instance()->add(Log::INFO, 'Skip generate invoice payment in check-3.2: ' . $contract_id);
          continue;
        }
        if ($row['billing_end_date'] != NULL && strtotime($invoice_billing_end_date) > strtotime($row['billing_end_date'])) {
          Log::instance()->add(Log::INFO, 'Skip generate invoice payment in check-3.3: ' . $contract_id);
          continue;
        }

        // check .4: check if this invoice has been generated: 請求書がすでに作成されている場合、請求書を作成しません。
        if ($this->check_invoice_payment_existed($contract_id, $row['seq'], $row['item'], $row['cost_type'], $invoice_billing_start_date, $invoice_billing_end_date)) {
          Log::instance()->add(Log::INFO, 'Skip generate invoice payment in check-4: ' . $contract_id);
          continue;
        }

        // generate invoice payment_due_date
        if ($payment_timing == '1') {
          // 翌月末日
          $payment_due_date = date('Y-m-t', strtotime(date('Y-m-01', strtotime($invoice_date)) . ' +1 month'));
        } else if ($payment_timing == '2') {
          // 当月末日
          $payment_due_date = date('Y-m-t', strtotime($invoice_date));
        } else if ($payment_timing == '3') {
          // 翌々月5日
          $payment_due_date = date('Y-m-05', strtotime(date('Y-m-01', strtotime($invoice_date)) . ' +2 month'));
        } else if ($payment_timing == '4') {
          // 翌々月末日
          $payment_due_date = date('Y-m-t', strtotime(date('Y-m-01', strtotime($invoice_date)) . ' +2 month'));
        } else if ($payment_timing == '5') {
          // 翌々月10日
          $payment_due_date = date('Y-m-10', strtotime(date('Y-m-01', strtotime($invoice_date)) . ' +2 month'));
        } else if ($payment_timing == '6') {
          // 翌月10日
          $payment_due_date = date('Y-m-10', strtotime(date('Y-m-01', strtotime($invoice_date)) . ' +1 month'));
        } else {
          throw new ErrorException('Invalid payment_timing: ' . $payment_timing);
        }

        if (!isset($generating_invoices[$contract_id])) {
          $generating_invoices[$contract_id] = [
            'version' => 1,
            'invoice_number' => 'INV-' . $row['client_code'] . '-' . date('Ym', strtotime($invoice_date)),
            'contract_id' => $contract_id,
            'status' => '0',
            'invoice_code' => $row['invoice_code'],
            'invoice_date' => $invoice_date,
            'payment_due_date' => $payment_due_date,
            'invoice_details' => [],
            'invoice_span' => $span
          ];
        }

        $billing_account = $row['billing_account'] == "" ? [] : explode(',', $row['billing_account']);
        $billing_accounts = $this->get_bots_name($billing_account);
      
        $number = $span;
        if ($span == 0 || $cost_type == '1' || $cost_type == '3' || $cost_type == '5') {
          $number = 1;
        }

        $amount = $row['cost'];
        if ($need_divide) {
          // 日割り計算 新しい仕様の契約のみ
          $month_days = date('t', strtotime($invoice_billing_start_date));
          $date1 = new DateTime($invoice_billing_start_date);
          $date2 = new DateTime($invoice_billing_end_date);
          $interval = $date1->diff($date2);
          $diff_days = $interval->days + 1;
          if (intval($diff_days) < intval($month_days)) {
            $amount = intval(floor(floatval($amount) * floatval($diff_days) / floatval($month_days)));
          }
        }

        $generating_invoices[$contract_id]['invoice_details'][] = [
          'seq' => $row['seq'],
          'item' => $row['item'],
          'item_name' => $items[$row['item']][$lang_cd] ?? $items[$row['item']]['ja'],
          'cost_type' => $row['cost_type'],
          'number' => $number,
          'amount' => $amount,
          'tax' => $row['tax_rate'],
          'billing_start_date' => $invoice_billing_start_date,
          'billing_end_date' => $invoice_billing_end_date,
          'billing_accounts' => $billing_accounts,
          'item_remarks' => $row['item_remarks']
        ];
      } catch (\ErrorException $th) {
        $msg = 'Error on generate_invoicepayment: contract_id: ' . $contract_id . ' - Error: ' . $th->getMessage();
        Log::instance()->add(Log::ERROR, $msg);
        $errors[] = $msg;
      } catch (\Throwable $th) {
        $msg = 'Error on generate_invoicepayment: contract_id: ' . $contract_id . ' - Error: ' . $th->getMessage();
        Log::instance()->add(Log::INFO, $msg);
      } 
    }
    return [
      'invoices' => $generating_invoices,
      'errors' => $errors
    ];
  }

  public function create_or_update_invoicepayment($contract_data) {
    try {
      $new_invoice_id = $this->get_new_invoice_id();
      Database::instance('contract')->begin();
      // insert
      $insert_sql = "INSERT INTO t_invoice (
        invoice_id,
        version, 
        invoice_number, 
        contract_id, 
        status, 
        invoice_code, 
        invoice_date, 
        payment_due_date,
        invoice_details
      )
      VALUES (
        :invoice_id,
        :version, 
        :invoice_number, 
        :contract_id, 
        :status, 
        :invoice_code, 
        :invoice_date, 
        :payment_due_date,
        :invoice_details
      )";
      $insert_query = DB::query(Database::INSERT, $insert_sql);
      $insert_query->parameters(array(
        ':invoice_id' => $new_invoice_id,
        ':version' => $contract_data['version'],
        ':invoice_number' => $contract_data['invoice_number'] . '-' . $new_invoice_id,
        ':contract_id' => $contract_data['contract_id'],
        ':status' => $contract_data['status'],
        ':invoice_code' => $contract_data['invoice_code'],
        ':invoice_date' => $contract_data['invoice_date'],
        ':payment_due_date' => $contract_data['payment_due_date'],
        ':invoice_details' => json_encode($contract_data['invoice_details'], JSON_UNESCAPED_UNICODE)
      ));
      $insert_id = $insert_query->execute('contract');
      Database::instance('contract')->commit();
      return $insert_id[0];
    } catch (\Throwable $th) {
      Database::instance('contract')->rollback();
      throw $th;
    }
  }

  private function get_new_invoice_id() {
    $sql = "SELECT MAX(invoice_id) as max_id FROM t_invoice";
    $query = DB::query(Database::SELECT, $sql);
    $result = $query->execute('contract')->as_array();
    if (count($result) > 0) {
      return $result[0]['max_id'] + 1;
    } else {
      return 1;
    }
  }

  public function sendpaymentmail($invoice_id) {
    try {
      $sql = "SELECT DISTINCT ti.invoice_number, ti.invoice_date, ti.invalid_flg, ti.invoice_code, 
      tc.contract_id,
      mc.department_name as client_department_name, mc.client_code
      FROM t_invoice ti
      INNER JOIN t_contract tc ON ti.contract_id = tc.contract_id
      INNER JOIN m_client mc ON mc.client_code = tc.client_code AND mc.delete_flg = 0 AND mc.invalid_flg = 0
      WHERE ti.invoice_id = :invoice_id";
      $query = DB::query(Database::SELECT, $sql);
      $query->parameters(array(':invoice_id' => $invoice_id));
      $result = $query->execute('contract')->as_array();
      if (count($result) == 0) {
        throw new ErrorException('no_data');
      }
      $data = $result[0];
      $invoice = $this->get_valid_invoice($data['invoice_code'], $data['invoice_date']);
      if ($invoice == null) {
        throw new ErrorException('can not find invoice');
      }
      // 紐づけてる取引先、請求先、契約のチェック状況を確認
      if ($this->is_in_check($data['contract_id'], 'contract')) {
        throw new ErrorException('contract is in check');
      }
      if ($this->is_in_check($invoice['invoice_code'], 'invoice', $invoice['seq'])) {
        throw new ErrorException('invoice is in check');
      }
      if ($this->is_in_check($data['client_code'], 'client')) {
        throw new ErrorException('client is in check');
      }
      $data['invoice_name'] = $invoice['invoice_name'];
      $data['client_department_name'] = $invoice['department_name'];
      $data['receiver'] = $invoice['invoice_recipient_to'];
      $data['receiver_cc'] = $invoice['invoice_recipient_cc'];
      $data['send_method'] = $invoice['send_method'];
      if ($data['send_method'] != 'mail') {
        throw new Exception('send_method is not mail');
      }
      if ($data['invalid_flg'] == '1') {
        throw new Exception('invoice is invalid');
      }
      // メールのパラメータ
      $param = [];
      $param['title'] = "【" . $data['client_department_name'] . "様分】talkappi請求書のご案内（株式会社アクティバリューズ)";
      $param['bot_id'] = '800';
      $param['sender'] = '<EMAIL>';
      $param['replyto'] = '<EMAIL>';
      $param['lang_cd'] = 'ja';
      if ($data['receiver'] == [] || $data['receiver'] == '') {
        throw new ErrorException('no_receiver');
      }
      $receivers = json_decode($data['receiver'], true);
      $to_data = [];
      foreach($receivers as $receiver) {
        if ($receiver != '') {
          $to_data[] = $receiver;
        }
      }
      $param['receiver'] = implode(',', $to_data);
      if ($data['receiver_cc'] != '') {
        $cc_receivers = json_decode($data['receiver_cc'], true);
        $cc_data = [];
        foreach($cc_receivers as $cc_receiver) {
          if ($cc_receiver != '') {
            $cc_data[] = $cc_receiver;
          }
        }
        $cc_data[] = '<EMAIL>';
        $cc_data[] = '<EMAIL>';
        $param['receiver_cc'] = implode(',', $cc_data);
      }
      $param['message_cd'] = 'invoice_payment_notification';
      $param['params'] = [
        'invoice_name'=> $data['invoice_name'],
        'client_department_name'=> $data['client_department_name'],
        'invoice_date'=> date('Y年m月', strtotime($data['invoice_date'])),
      ];
      $pdf_content = $this->generate_invoicepayment_pdf($invoice_id, false);
      if ($pdf_content == '' || $pdf_content == null) {
        throw new ErrorException('no_pdf');
      }
      $pdf_base64 = base64_encode($pdf_content);
      Log::instance()->add(Log::DEBUG, "PDF base64 (first 100 chars): " . substr($pdf_base64, 0, 100));
      $filename = $data['invoice_number'] . '.pdf';
      $param['attachments'] = [
        [
          'name' => $filename,
          'data' => $pdf_base64
        ]
      ];
      $engine_response = $this->post_enginehook('service', 'sendmail', '', $param);
      Log::instance()->add(Log::DEBUG, "Mail response: " . json_encode($engine_response));
      // check 送信結果
      if (isset($engine_response['success']) && $engine_response['success'] != 'true') {
        throw new ErrorException('API send_failed');
      }
      // update 送信結果
      $update_sql = "UPDATE t_invoice SET status = :send_status WHERE invoice_id = :invoice_id";
      $update_query = DB::query(Database::UPDATE, $update_sql);
      $update_query->parameters(array(':send_status' => 1, ':invoice_id' => $invoice_id));
      $update_query->execute('contract');
    } catch (\ErrorException $error) {
      $set_fail_sql = "UPDATE t_invoice SET status = :send_status WHERE invoice_id = :invoice_id";
      $update_query = DB::query(Database::UPDATE, $set_fail_sql);
      $update_query->parameters(array(':send_status' => 2, ':invoice_id' => $invoice_id));
      $update_query->execute('contract');
      throw $error;
    } catch (\Throwable $th) {
      throw $th;
    }
  }

  public function senderror($title, $errors) {
    try {
      $date = date('Y-m-d H:i:s');
      $param = [];
      $param['title'] = "[" . $title . "]" . " - " . $date;
      $param['bot_id'] = '800';
      $param['sender'] = '<EMAIL>';
      $param['receiver'] = '<EMAIL>, <EMAIL>';
      $param['lang_cd'] = 'ja';
      $param['body'] = implode("\n", $errors);
      $this->post_enginehook('service', 'sendmail', '', $param);
    } catch (\Throwable $th) {
      throw $th;
    }
  }

  public function generate_invoicepayment_pdf($invoice_id, $download = true) {
    try {
      $pdf_data = $this->get_invoice_payment_for_pdf($invoice_id);
      $lang_cd = $this->get_lang_cd_from_invoice($invoice_id);
      if ($pdf_data == null) {
        throw new Exception('no data found');
      }
      $filename = $pdf_data['invoice_code'] . '.pdf';
      $defaultConfig = (new Mpdf\Config\ConfigVariables())->getDefaults();
      $fontDirs = $defaultConfig['fontDir'];
      $defaultFontConfig = (new Mpdf\Config\FontVariables())->getDefaults();
      $fontData = $defaultFontConfig['fontdata'];
      $mpdf = new \Mpdf\Mpdf([
        'mode' => 'utf-8',
        'format' => 'A4',
        'orientation' => 'P',
        'tempDir' => __DIR__ . '/../../../../files',
        'fontDir' => array_merge($fontDirs, [__DIR__ . '/../../../assets/common/font']),
        'fontdata' => $fontData + 
          [
            'notojp' => [
              'R' => 'NotoSansJP-Regular.ttf',
            ]
          ],
        'default_font' => 'notojp'
      ]);
      $mpdf->allow_charset_conversion=true;
      $mpdf->autoLangToFont=true;
      $mpdf->charset_in='UTF-8';
      $mpdf->showImageErrors = true;
      // get template
      $payment_template = $this->get_bot_tpl_message(800, 'contract_invoice_template', $lang_cd);
      $payment_labels = $this->get_bot_tpl_message(800, 'contract_invoice_label', $lang_cd);
      if (!$payment_labels) {
        throw new Exception('no labels found');
      }
      $payment_labels = json_decode($payment_labels, true);

      foreach ($pdf_data as $key => $data) {
        $field = '{' . $key . '}';
        if ($key === 'remark') {
          if ($data == '') {
            $replace_value = '';
          } else {
            $replace_value = "<div class='remark'><div>" . $payment_labels['REMARK'] . "</div><div class='remark-detail'>" . $data . "</div></div>";
          }
          $payment_template = str_replace($field, $replace_value, $payment_template);
        } else if ($key === 'invoice_details') {
          $invoice_details = $data;
          $total_amount = 0;
          $invoice_details_html = '';
          $tax_group = [];
          foreach ($invoice_details as $detail) {
            $amount = intval($detail['amount']);
            $number = intval($detail['number']);
            $tax = intval($detail['tax']);
            if (!isset($tax_group[$tax])) {
              $tax_group[$tax] = 0;
            }
            $tax_group[$tax] += $amount * $number;
            $total_amount += $amount * $number * (100 + $tax) / 100;
            $cost_type = '';
            if (!str_starts_with($detail['item'], '11')) {
              if ($detail['cost_type'] == '1') {
                $cost_type = $payment_labels['INITIAL_COST'];
              } else if ($detail['cost_type'] == '2') {
                $cost_type = $payment_labels['MONTHLY_COST'];
              }
            }
            $invoice_details_html .= '<tr><td style="width:11%">' . $pdf_data['invoice_date'] . '</td>';
            $invoice_details_html .= '<td style="width:58%"><div>' . $detail['item_name'] . $cost_type .  '</div>';
            if ($detail['item_remarks'] != '') {
              $invoice_details_html .= '<div class="memo">' . $detail['item_remarks'] . '</div>';
            } else {
              if ($pdf_data['department'] != '') {
                $invoice_details_html .= '<div class="memo">' . $pdf_data['department'] . $payment_labels['FOR_CUSTOMERS'] .'</div>';
              }
            }
            if (isset($detail['billing_start_date']) && isset($detail['billing_end_date'])) {
              $from_date = date($payment_labels['DATE_FORMAT'], strtotime($detail['billing_start_date']));
              $to_date = date($payment_labels['DATE_FORMAT'], strtotime($detail['billing_end_date']));
              if ($detail['billing_start_date'] != $detail['billing_end_date']) {
                $date_period = str_replace(['{from_date}', '{to_date}'], [$from_date, $to_date], $payment_labels['PERIOD_DATE']);
                $invoice_details_html .= '<div class="memo">' . $date_period . '</div>';
              } else {
                $invoice_details_html .= '<div class="memo">' . $from_date . '</div>';
              }
            }
            $invoice_details_html .= '</td>';
            $invoice_details_html .= '<td style="width:11%">¥' . number_format($amount) . '</td>';
            $invoice_details_html .= '<td style="width:10%">' . $number . '</td>';
            $invoice_details_html .= '<td style="width:10%">¥' . number_format($amount * $number) . '</td>';
            $invoice_details_html .= '</tr>';
          }
          $tax_group_html = '';
          foreach ($tax_group as $tax => $amount) {
            $tax_amount = $amount * $tax / 100;
            $tax_group_html .= '<tr><td class="left-align">' . $payment_labels['CONSUME_TAX'] . $tax . '%' . $payment_labels['SUBTOTAL_EXCLUDE_TAX'] . '</td>';
            $tax_group_html .= '<td class="right-align">¥' . number_format($amount) . '</td></tr>';
            $tax_group_html .= '<tr><td class="left-align border-bottom">' . $payment_labels['TAX_AMOUNT'] . '</td>';
            $tax_group_html .= '<td class="right-align border-bottom">¥' . number_format($tax_amount) . '</td></tr>';
          }
          $tax_group_html .= '<tr><td class="left-align border-bottom">' . $payment_labels['TOTAL_AMOUNT'] . '</td><td class="right-align border-bottom">¥'.number_format($total_amount).'</td></tr>';
          $payment_template = str_replace('{invoice_details}', $invoice_details_html, $payment_template);
          $payment_template = str_replace('{tax_group}', $tax_group_html, $payment_template);
          $payment_template = str_replace('{total_amount}', number_format($total_amount), $payment_template);
        } else if ($key === 'invoice_date' || $key === 'payment_due_date') {
          $payment_template = str_replace($field, date($payment_labels['DATE_FORMAT'], strtotime($data)), $payment_template);
        } else {
          $payment_template = str_replace($field, $data, $payment_template);
        }
      }
      $mpdf->WriteHTML($payment_template);
      if ($download) {
        $mpdf->Output($filename, 'D');
      } else {
        return $mpdf->Output($filename, 'S');
      }
    } catch (\Throwable $th) {
      $exception = 'PDF generate fail: ' . $th->getMessage();
      throw new ErrorException($exception);
    }
  }

  public function set_invoicepayment_status($invoice_id, $status, $value) {
    try {
      if (!in_array($status, ['status', 'receipt_flg', 'invalid_flg'])) {
        throw new Exception('Invalid status: ' . $status);
      }
      if ($status === 'status' && !in_array($value, ['0', '1', '2'])) {
        throw new Exception('Invalid value: ' . $value);
      }
      if (($status === 'receipt_flg' || $status === 'invalid') && !in_array($value, [0, 1])) {
        throw new Exception('Invalid value: ' . $value);
      }
      $sql = "UPDATE t_invoice SET $status = :status WHERE invoice_id = :invoice_id";
      $query = DB::query(Database::UPDATE, $sql);
      $query->parameters(array(
        ':status' => $value,
        ':invoice_id' => $invoice_id
      ));
      $query->execute('contract');
    } catch (\Throwable $th) {
      throw $th;
    }
  }

  public function get_diffs($code, $type, $seq = NULL) {
    $diffs = [];
    $sql = "SELECT * FROM t_diff WHERE code = :code AND type = :type";
    if ($seq && $type == 'invoice') {
      $sql .= " AND seq = :seq";
    }
    $sql .= " AND delete_flg = 0 
    ORDER BY no DESC";
    $query = DB::query(Database::SELECT, $sql);
    $query->parameters(array(
      ':code' => $code,
      ':type' => $type
    ));
    if ($seq && $type == 'invoice') {
      $query->parameters(array(':seq' => $seq));
    }
    $results = $query->execute('contract')->as_array();
    $admin_model = new Model_Adminmodel();
    foreach ($results as $result) {
      $user_name = $admin_model->get_user_names_by_ids([$result['upd_user']]);
      $diffs[] = [
        'id' => $result['id'],
        'no' => $result['no'],
        'upd_user' => $user_name[$result['upd_user']],
        'upd_time' => $result['upd_time'],
        'diff' => json_decode($result['upd_diff'], true)
      ];
    }
    return $diffs;
  }

  public function record_diff($code, $type, $diff, $user_id, $seq = NULL) {
    $max_no_sql = "SELECT IFNULL(MAX(no), 0) as max_no FROM t_diff WHERE code = :code AND type = :type";
    if ($seq && $type == 'invoice') {
      $max_no_sql .= " AND seq = :seq";
    }
    $max_no_query = DB::query(Database::SELECT, $max_no_sql);
    $max_no_query->parameters(array(
      ':code' => $code,
      ':type' => $type,
      ':seq' => $seq
    ));
    $max_no_result = $max_no_query->execute('contract')->as_array();
    $max_no = $max_no_result[0]['max_no'] + 1;
    try {
      $items = ['code', 'no', 'type', 'upd_diff', 'upd_time', 'upd_user'];
      if ($seq && $type == 'invoice') {
        $items[] = 'seq';
      }
      $insert_sql = "INSERT INTO t_diff (" . implode(',', $items) . ") values (:code, :no, :type, :upd_diff, :upd_time, :upd_user";
      if ($seq && $type == 'invoice') {
        $insert_sql .= ", :seq";
      }
      $insert_sql .= ")";
      $insert_query = DB::query(Database::INSERT, $insert_sql);
      $insert_query->parameters(array(
        ':code' => $code,
        ':no' => $max_no,
        ':type' => $type,
        ':upd_diff' => json_encode($diff, JSON_UNESCAPED_UNICODE),
        ':upd_time' => date('Y-m-d H:i:s'),
        ':upd_user' => $user_id,
        ':seq' => $seq
      ));
      $insert_query->execute('contract');
    } catch (\Throwable $th) {
      throw $th;
    }
  }

  public function delete_diff_by_id($diff_id, $user_id) {
    try {
      $sql = "UPDATE t_diff SET delete_flg = 1, delete_user = :delete_user, delete_time = :delete_time WHERE id = :id";
      $query = DB::query(Database::UPDATE, $sql);
      $query->parameters(array(
        ':id' => $diff_id,
        ':delete_user' => $user_id,
        ':delete_time' => date('Y-m-d H:i:s')
      ));
      $query->execute('contract');
    } catch (\Throwable $th) {
      throw $th;
    }
  }

  public function add_checkflg($code, $type, $seq = NULL, $mode = 'all') {
    try {
      $items = ['code', 'type', 'create_time'];
      if ($type == 'invoice' && $seq) {
        $items[] = 'seq';
      }
      if ($mode == 'all') {
        $items[] = 'cs_checkflg';
        $items[] = 'sales_checkflg';
        $items[] = 'accountant_checkflg';
        $items[] = 'admin_checkflg';
      } else if ($mode == 'admin') {
        $items[] = 'accountant_checkflg';
        $items[] = 'admin_checkflg';
      } else {
        throw new ErrorException('Invalid mode: ' . $mode);
      }
      $insert_sql = "INSERT INTO t_check (" . implode(',', $items) . ") values (:code, :type, :create_time";
      if ($type == 'invoice' && $seq) {
        $insert_sql .= ", :seq";
      }
      if ($mode == 'admin') {
        $insert_sql .= ", 0, 0";
      } else if ($mode == 'all') {
        $insert_sql .= ", 0, 0, 0, 0";
      }
      $insert_sql .= ")";
      $insert_query = DB::query(Database::INSERT, $insert_sql);
      $insert_query->parameters(array(
        ':code' => $code,
        ':type' => $type,
        ':create_time' => date('Y-m-d H:i:s'),
        ':seq' => $seq
      ));
      $insert_query->execute('contract');
    } catch (\Throwable $th) {
      throw $th;
    }
  }

  public function is_in_check($code, $type, $seq = NULL) {
    $sql = "SELECT * FROM t_check WHERE code = :code AND type = :type AND complete_time IS NULL";
    if ($type == 'invoice' && $seq) {
      $sql .= " AND seq = :seq";
    }
    $sql .= " AND (cs_checkflg = 0 OR sales_checkflg = 0 OR accountant_checkflg = 0 OR admin_checkflg = 0)";
    $query = DB::query(Database::SELECT, $sql);
    $query->parameters(array(
      ':code' => $code,
      ':type' => $type,
      ':seq' => $seq
    ));
    $result = $query->execute('contract')->as_array();
    return count($result) > 0;
  }

  public function reset_checkstatus($code, $type, $seq = NULL, $mode = 'all') {
    try {
      $update_sql = "UPDATE t_check SET 
        cs_checkflg = :cs_checkflg, 
        sales_checkflg = :sales_checkflg, 
        accountant_checkflg = :accountant_checkflg, 
        admin_checkflg = :admin_checkflg 
      WHERE code = :code AND type = :type AND complete_time IS NULL";
      if ($type == 'invoice' && $seq) {
        $update_sql .= " AND seq = :seq";
      }
      $update_query = DB::query(Database::UPDATE, $update_sql);
      $update_query->parameters(array(
        ':code' => $code,
        ':type' => $type,
        ':seq' => $seq,
        ':cs_checkflg' => $mode == 'all' ? 0 : NULL,
        ':sales_checkflg' => $mode == 'all' ? 0 : NULL,
        ':accountant_checkflg' => 0,
        ':admin_checkflg' => 0
      ));
      $update_query->execute('contract');
    } catch (\Throwable $th) {
      throw $th;
    }
  }

  public function before_check($check_id, $check_type, $user) {
    try {
      // check user privilege
      if ($check_type === 'accountant_checkflg' && (!in_array($user->user_id, [6648, 2199]) && $user->role_cd != '08')) {
        throw new Exception('Not accountant');
      }
      if ($check_type === 'admin_checkflg' && !in_array($user->user_id, [1519, 6648, 4501, 1216])) {
        throw new Exception('Not admin');
      }
      $check_data = $this->get_checkstatus_by_id($check_id);
      if ($check_type === 'accountant_checkflg' || $check_type === 'admin_checkflg') {
        if ((!is_null($check_data['cs_checkflg']) && $check_data['cs_checkflg'] == 0) || (!is_null($check_data['sales_checkflg']) && $check_data['sales_checkflg'] == 0)) {
          throw new Exception('CS or Sales check not completed');
        }
        if ($check_type === 'admin_checkflg' && !is_null($check_data['accountant_checkflg']) && $check_data['accountant_checkflg'] == 0) {
          throw new Exception('Accountant check not completed');
        }
      } else {
        // cs or sales check
        $sql = "SELECT contract_id FROM t_contract WHERE";
        $code = $check_data['code'];
        if ($check_data['type'] == 'client') {
          $sql .= " client_code = :code ";
        } else if ($check_data['type'] == 'contract') {
          $sql .= " contract_id = :code";
        } else if ($check_data['type'] == 'invoice') {
          $sql .= " invoice_code = :code";
        }
        $sql .= " AND (cs_representative IS NOT NULL OR sales_representative IS NOT NULL)";
        $query = DB::query(Database::SELECT, $sql);
        $query->parameters(array(':code' => $code));
        $results = $query->execute('contract')->as_array();
        if (count($results) == 0) {
          throw new Exception('CS or Sales representative not set');
        }
      }
    } catch (\Throwable $th) {
      throw $th;
    }
  }

  public function set_check($id, $check_type, $user_id, $warnings = NULL) {
    try {
      $check_data = $this->get_checkstatus_by_id($id);
      if ($check_data === NULL) {
        throw new ErrorException('No check found: ' . $id);
      }
      if ($check_type != 'admin_checkflg') {
        if ($check_type == 'cs_checkflg') {
          $user_type = 'cs_checkuser';
        } else if ($check_type == 'sales_checkflg') {
          $user_type = 'sales_checkuser';
        } else {
          $user_type = 'accountant_checkuser';
        }
        $update_sql = "UPDATE t_check SET $check_type = 1, $user_type = :user_id, warnings = :warnings WHERE id = :id";
        $update_query = DB::query(Database::UPDATE, $update_sql);
        $update_query->parameters(array(
          ':id' => $id,
          ':user_id' => $user_id,
          ':warnings' => $warnings
        ));
        $update_query->execute('contract');
      } else {
        $update_sql = "UPDATE t_check SET admin_checkflg = 1, admin_checkuser = :user_id, complete_time = :complete_time, warnings = :warnings WHERE id = :id";
        $update_query = DB::query(Database::UPDATE, $update_sql);
        $update_query->parameters(array(
          ':id' => $id,
          ':user_id' => $user_id,
          ':complete_time' => date('Y-m-d H:i:s'),
          ':warnings' => $warnings
        ));
        $update_query->execute('contract');
      }
    } catch (\Throwable $th) {
      throw $th;
    }
  }

  public function get_checkstatus($code, $type, $seq = NULL) {
    $sql = "SELECT * FROM t_check WHERE code = :code AND type = :type";
    if ($type == 'invoice' && $seq) {
      $sql .= " AND seq = :seq";
    }
    $sql .= " ORDER BY id DESC";
    $query = DB::query(Database::SELECT, $sql);
    $query->parameters(array(
      ':code' => $code,
      ':type' => $type,
      ':seq' => $seq
    ));
    $results = $query->execute('contract')->as_array();
    if (count($results) == 0) {
      return NULL;
    }
    $result = $results[0];
    $users = [];
    if ($result['cs_checkuser'] !== NULL) {
      $users[] = $result['cs_checkuser'];
    }
    if ($result['sales_checkuser'] !== NULL) {
      $users[] = $result['sales_checkuser'];
    }
    if ($result['accountant_checkuser'] !== NULL) {
      $users[] = $result['accountant_checkuser'];
    }
    if ($result['admin_checkuser'] !== NULL) {
      $users[] = $result['admin_checkuser'];
    }
    if (count($users) > 0) {
      $admin_model = new Model_Adminmodel();
      $user_names = $admin_model->get_user_names_by_ids($users);
      foreach ($user_names as $user_id => $row) {
        if ($result['cs_checkuser'] == $user_id) {
          $result['cs_checkuser'] = $row;
        }
        if ($result['sales_checkuser'] == $user_id) {
          $result['sales_checkuser'] = $row;
        }
        if ($result['accountant_checkuser'] == $user_id) {
          $result['accountant_checkuser'] = $row;
        }
        if ($result['admin_checkuser'] == $user_id) {
          $result['admin_checkuser'] = $row;
        }
      }
    }
    if (count($results) > 1) {
      $result['last_complete_time'] = $results[1]['complete_time'];
    }
    return $result;
  }

  public function get_checkstatus_by_id($id) {
    $sql = "SELECT * FROM t_check WHERE id = :id";
    $query = DB::query(Database::SELECT, $sql);
    $query->parameters(array(':id' => $id));
    $result = $query->execute('contract')->as_array();
    if (count($result) == 0) {
      return NULL;
    }
    return $result[0];
  }

  public function get_all_checks($type) {
    $sql = "SELECT * FROM t_check WHERE type = :type";
    $query = DB::query(Database::SELECT, $sql);
    $query->parameters(array(
      ':type' => $type
    ));
    $result = $query->execute('contract')->as_array();
    $map = [];
    foreach ($result as $row) {
      if ((isset($map[$row['code']]) && $row['id'] > $map[$row['code']]['id']) || !isset($map[$row['code']])) {
        $map[$row['code']] = $row;
      }
    }
    return $map;
  }

  public function delete_warning($check_id, $seq) {
    try {
      if ($seq == -1) {
        $update_sql = "UPDATE t_check SET warnings = NULL WHERE id = :check_id";
        $query = DB::query(Database::UPDATE, $update_sql);
        $query->parameters(array(':check_id' => $check_id));
        $query->execute('contract');
      } else {
        $existed_check = $this->get_checkstatus_by_id($check_id);
        if ($existed_check === NULL) {
          throw new ErrorException('No check found: ' . $check_id);
        }
        if ($existed_check['warnings'] === NULL) {
          throw new ErrorException('No warnings found');
        }
        $warnings = json_decode($existed_check['warnings'], true);
        unset($warnings[$seq]);
        $new_warnings = array_merge($warnings, []);
        $update_sql = "UPDATE t_check SET warnings = :warnings WHERE id = :check_id";
        $query = DB::query(Database::UPDATE, $update_sql);
        $query->parameters(array(
          ':check_id' => $check_id,
          ':warnings' => json_encode($new_warnings, JSON_UNESCAPED_UNICODE)
        ));
        $query->execute('contract');
      }
    } catch (\Throwable $th) {
      throw $th;
    }
  }

  public function get_all_linked_contracts($code, $type) {
    $contract_ids = [];
    if ($type == 'client') {
      $sql = "SELECT contract_id FROM t_contract WHERE client_code = :code";
    } else if ($type == 'contract') {
      $sql = "SELECT contract_id FROM t_contract WHERE contract_id = :code";
    } else if ($type == 'invoice') {
      $sql = "SELECT contract_id FROM t_contract WHERE invoice_code = :code";
    }
    $sql .= " AND invalid_flg = 0 AND delete_flg = 0";
    $query = DB::query(Database::SELECT, $sql);
    $query->parameters(array(':code' => $code));
    $result = $query->execute('contract')->as_array();
    foreach ($result as $row) {
      if (!in_array($row['contract_id'], $contract_ids)) {
        $contract_ids[] = $row['contract_id'];
      }
    }
    return $contract_ids;
  }

  private function check_invoice_payment_existed($contract_id, $seq, $item, $cost_type, $billing_start_date, $billing_end_date) {
    $match = [
      'seq' => $seq, 
      'item' => $item, 
      'billing_start_date' => $billing_start_date
    ];
    if ($cost_type != '1') {
      $match['billing_end_date'] = $billing_end_date;
    }
    $sql = "SELECT invoice_id
    FROM t_invoice
    WHERE contract_id = :contract_id AND JSON_CONTAINS(invoice_details, :item_json) AND invalid_flg = 0
    ORDER BY invoice_id DESC";
    $query = DB::query(Database::SELECT, $sql);
    $query->parameters([
      ':contract_id' => $contract_id,
      ':item_json' => json_encode($match),
    ]);
    $result = $query->execute('contract')->as_array();
    return count($result) > 0;
  }

  private function get_max_contract_id() {
    $sql = "SELECT MAX(contract_id) as max_contract_id FROM t_contract";
    $query = DB::query(Database::SELECT, $sql);
    $result = $query->execute('contract')->as_array();
    if (count($result) > 0) {
      return $result[0]['max_contract_id'];
    }
    return 0;
  }

  private function get_bots_name($bots) {
    if (count($bots) == 0) {
      return [];
    }
    $sql = "SELECT bot_name FROM t_bot WHERE bot_id in :bot_id";
    $query = DB::query(Database::SELECT, $sql);
    $query->parameters(array(':bot_id' => $bots));
    $result = $query->execute()->as_array();
    $bot_names = [];
    foreach ($result as $row) {
      $bot_names[] = $row['bot_name'];
    }
    return $bot_names;
  }

  private function get_lang_cd_from_invoice($invoice_id) {
    $sql = "SELECT DISTINCT c.country FROM t_invoice a
    INNER JOIN t_contract b ON a.contract_id = b.contract_id
    INNER JOIN m_client c ON b.client_code = c.client_code
    WHERE a.invoice_id = :invoice_id";
    $query = DB::query(Database::SELECT, $sql);
    $query->parameters(array(':invoice_id' => $invoice_id));
    $result = $query->execute('contract')->as_array();
    if (count($result) > 0) {
      $country = $result[0]['country'];
      if ($country == 'ja') {
        return 'ja';
      } else {
        return 'en';
      }
    } else {
      return 'ja';
    }
  }

  public function get_admin_users() {
    $admin_model = new Model_Adminmodel();
    $users = $admin_model->get_users_by_roles(array(99));
    $admin_users = [];
    foreach ($users as $user) {
      $admin_users[$user['user_id']] = $user['name'];
    }
    return $admin_users;
  }

  public function get_sales($start_date, $end_date, $type) {
    $total_header = [];
    if ($type == "segment") {
      $total_header = [
        '月度',
        '取引先コード',
        '取引先名',
        'セグメント',
        '国',
        '金額(税込)',
        '金額(税抜)'
      ];
    } else if ($type == 'all') {
      $total_header = [
        '月度',
        '取引先コード',
        '取引先名',
        'セグメント',
        '国',
        '請求先コード',
        'SEQ',
        '品目コード',
        '品目',
        '種類',
        '請求開始日',
        '請求終了日',
        '金額(税込)',
        '金額(税抜)',
        '品目請求開始日',
        '品目請求終了日'
      ];
    } else {
      $total_header = [
        '月度',
        'セグメント',
        '品目',
        '金額(税込)',
        '金額(税抜)'
      ];
    } 
    $total_results = [];
    $initial_total_results = [];
    $monthly_total_results = [];
    $other_total_results = [];
    $item_total_results = [];
    $all_total_results = [];
    $from_date = date('Y-m-01', strtotime($start_date));
    $to_date = date('Y-m-01', strtotime($end_date));
    $months = [];
    while ($from_date <= $to_date) {
      $months[] = $from_date;
      $from_date = date('Y-m-01', strtotime($from_date . ' +1 month'));
    }
    try {
      $segments = $this->get_master_code_options(888811);
      $items_options = $this->get_master_code_options(888812);
      $service_option_map = $this->get_service_option_map();
      $sql = "SELECT tc.*, mc.client_name, mc.country, mc.segment
      FROM t_contract tc
      INNER JOIN m_client mc ON tc.client_code = mc.client_code AND mc.delete_flg = 0 AND mc.invalid_flg = 0
      WHERE tc.billing_start_date IS NOT NULL AND tc.billing_start_date <= :start_date AND tc.invalid_flg=0 AND tc.delete_flg=0";
      $query = DB::query(Database::SELECT, $sql);
      $query->parameters(array(':start_date' => date('Y-m-t', strtotime($start_date))));
      $result = $query->execute('contract')->as_array();
      $invoice_masters = $this->get_all_invoices();
      foreach ($months as $month) {
        $subtotal = [];
        $inital_subtotal = [];
        $monthly_subtotal = [];
        $other_subtotal = [];
        $item_subtotal = [];
        $month_first_date = date('Y-m-01', strtotime($month));
        $month_last_date = date('Y-m-t', strtotime($month));
        foreach ($result as $row) {
          $item = $row['item'];
          if ($item == '1110') {
            // 売上から除外の品目
            continue;
          }
          $invoice_code = $row['invoice_code'];
          $client_code = $row['client_code'];
          $client_name = $row['client_name'];
          $seq = $row['seq'];
          $segment = $row['segment'];
          $country = $row['country'];
          $billing_start_date = $row['billing_start_date'];
          $billing_end_date = $row['billing_end_date'];
          if (!isset($invoice_masters[$invoice_code])) {
            continue;
          }
          
          if ($type === "segment") {
            if (!isset($subtotal[$client_code])) {
              $subtotal[$client_code] = [
                date('Y-m', strtotime($month)),
                $client_code,
                $client_name,
                $segments[$segment],
                $country,
                0,
                0
              ];
              $inital_subtotal[$client_code] = [
                date('Y-m', strtotime($month)),
                $client_code,
                $client_name,
                $segments[$segment],
                $country,
                0,
                0
              ];
              $monthly_subtotal[$client_code] = [
                date('Y-m', strtotime($month)),
                $client_code,
                $client_name,
                $segments[$segment],
                $country,
                0,
                0
              ];
              $other_subtotal[$client_code] = [
                date('Y-m', strtotime($month)),
                $client_code,
                $client_name,
                $segments[$segment],
                $country,
                0,
                0
              ];
            }
          } else if ($type === "item") {
            if (!isset($item_subtotal[strval($segment)])) {
              $item_subtotal[strval($segment)] = [];
            }
            if (!isset($item_subtotal[strval($segment)][$service_option_map[$item]])) {
              $item_subtotal[strval($segment)][$service_option_map[$item]] = [
                date('Y-m', strtotime($month)),
                $segments[$segment],
                $items_options[$service_option_map[$item]],
                0,
                0
              ];
            }
          }
          
          $invoice_master = $invoice_masters[$invoice_code][0];
          $span = intval($invoice_master['invoice_span']);
          $tax_rate = floatval($row['tax_rate']);
          $amount = floatval($row['cost']);
          $cost_type = $row['cost_type'];
          $cost_type_name = match ($cost_type) {
            '1' => '初期費用',
            '2' => '月額費用',
            '3' => 'その他（単発)',
            '4' => 'その他（継続発生）',
            '5' => 'その他（周期発生）',
          };
          if ($span == 0) {
            if ($billing_end_date === NULL) {
              // 日割りの場合、請求終了日はNULLはずではなく、不正なデータの可能性があるので、スキップ
              continue;
            }
            // 日割り:
            // unit: 1日あたりの料金
            if (strtotime($billing_end_date) < strtotime($month_first_date) || strtotime($billing_start_date) > strtotime($month_last_date)) {
              continue;
            }
            $total_days = (strtotime($billing_end_date) - strtotime($billing_start_date)) / (60 * 60 * 24) + 1;
            $unit = $amount / floatval($total_days);
            if (strtotime($billing_start_date) >= strtotime($month_first_date)) {
              if (strtotime($billing_end_date) <= strtotime($month_last_date)) {
                $amount_include_tax = $amount * (100 + $tax_rate) / 100;
                $amount_exclude_tax = $amount;
                $subtotal[$client_code][5] += $amount_include_tax;
                $subtotal[$client_code][6] += $amount_exclude_tax;
                $all_total_results[] = [
                  date('Y-m', strtotime($month)),
                  $client_code,
                  $client_name,
                  $segments[$segment],
                  $country,
                  $invoice_code,
                  $seq,
                  $item,
                  $items_options[$item],
                  $cost_type_name,
                  $billing_start_date,
                  $billing_end_date,
                  $amount_include_tax,
                  $amount_exclude_tax,
                  $billing_start_date,
                  $billing_end_date
                ];
                if ($cost_type == '1') {
                  $inital_subtotal[$client_code][5] += $amount_include_tax;
                  $inital_subtotal[$client_code][6] += $amount_exclude_tax;
                } else if ($cost_type == '2') {
                  $monthly_subtotal[$client_code][5] += $amount_include_tax;
                  $monthly_subtotal[$client_code][6] += $amount_exclude_tax;
                  $item_subtotal[strval($segment)][$service_option_map[$item]][3] += $amount_include_tax;
                  $item_subtotal[strval($segment)][$service_option_map[$item]][4] += $amount_exclude_tax;
                } else {
                  $other_subtotal[$client_code][5] += $amount_include_tax;
                  $other_subtotal[$client_code][6] += $amount_exclude_tax;
                }
              } else {
                $days_of_month = (strtotime($month_last_date) - strtotime($billing_start_date)) / (60 * 60 * 24) + 1;
                $amount_of_month = $unit * $days_of_month;
                $amount_include_tax = $amount_of_month * (100 + $tax_rate) / 100;
                $amount_exclude_tax = $amount_of_month;
                $all_total_results[] = [
                  date('Y-m', strtotime($month)),
                  $client_code,
                  $client_name,
                  $segments[$segment],
                  $country,
                  $invoice_code,
                  $seq,
                  $item,
                  $items_options[$item],
                  $cost_type_name,
                  $billing_start_date,
                  $month_last_date,
                  $amount_include_tax,
                  $amount_exclude_tax,
                  $billing_start_date,
                  $billing_end_date
                ];
                $subtotal[$client_code][5] += $amount_include_tax;
                $subtotal[$client_code][6] += $amount_exclude_tax;
                if ($cost_type == '1') {
                  $inital_subtotal[$client_code][5] += $amount_include_tax;
                  $inital_subtotal[$client_code][6] += $amount_exclude_tax;
                } else if ($cost_type == '2') {
                  $monthly_subtotal[$client_code][5] += $amount_include_tax;
                  $monthly_subtotal[$client_code][6] += $amount_exclude_tax;
                  $item_subtotal[strval($segment)][$service_option_map[$item]][3] += $amount_include_tax;
                  $item_subtotal[strval($segment)][$service_option_map[$item]][4] += $amount_exclude_tax;
                } else {
                  $other_subtotal[$client_code][5] += $amount_include_tax;
                  $other_subtotal[$client_code][6] += $amount_exclude_tax;
                }
              }
            } else {
              $days_of_month = (strtotime($billing_end_date) - strtotime($month_first_date)) / (60 * 60 * 24) + 1;
              $amount_of_month = $unit * $days_of_month;
              $amount_include_tax = $amount_of_month * (100 + $tax_rate) / 100;
              $amount_exclude_tax = $amount_of_month;
              $all_total_results[] = [
                date('Y-m', strtotime($month)),
                $client_code,
                $client_name,
                $segments[$segment],
                $country,
                $invoice_code,
                $seq,
                $item,
                $items_options[$item],
                $cost_type_name,
                $month_first_date,
                $billing_end_date,
                $amount_include_tax,
                $amount_exclude_tax,
                $billing_start_date,
                $billing_end_date
              ];
              $subtotal[$client_code][5] += $amount_include_tax;
              $subtotal[$client_code][6] += $amount_exclude_tax;
              if ($cost_type == '1') {
                $inital_subtotal[$client_code][5] += $amount_include_tax;
                $inital_subtotal[$client_code][6] += $amount_exclude_tax;
              } else if ($cost_type == '2') {
                $monthly_subtotal[$client_code][5] += $amount_include_tax;
                $monthly_subtotal[$client_code][6] += $amount_exclude_tax;
                $item_subtotal[strval($segment)][$service_option_map[$item]][3] += $amount_include_tax;
                $item_subtotal[strval($segment)][$service_option_map[$item]][4] += $amount_exclude_tax;
              } else {
                $other_subtotal[$client_code][5] += $amount_include_tax;
                $other_subtotal[$client_code][6] += $amount_exclude_tax;
              }
            }
          } else {
            // 日割り以外
            $amount_include_tax = $amount * (100 + $tax_rate) / 100;
            $amount_exclude_tax = $amount;
            if ($cost_type == '1' || $cost_type == '3') {
              // 初期費用、その他（単発)の場合は、請求開始日と請求終了日が同じ
              if ($billing_end_date === NULL) {
                // 初期費用、その他（単発)の場合、請求終了日はNULLはずではなく、不正なデータの可能性があるので、スキップ
                continue;
              }
              if (strtotime($billing_start_date) >= strtotime($month_first_date) && strtotime($billing_start_date) <= strtotime($month_last_date)) {
                $subtotal[$client_code][5] += $amount_include_tax;
                $subtotal[$client_code][6] += $amount_exclude_tax;
                $all_total_results[] = [
                  date('Y-m', strtotime($month)),
                  $client_code,
                  $client_name,
                  $segments[$segment],
                  $country,
                  $invoice_code,
                  $seq,
                  $item,
                  $items_options[$item],
                  $cost_type_name,
                  $billing_start_date,
                  $billing_end_date,
                  $amount_include_tax,
                  $amount_exclude_tax,
                  $billing_start_date,
                  $billing_end_date
                ];
                if ($cost_type == '1') {
                  $inital_subtotal[$client_code][5] += $amount_include_tax;
                  $inital_subtotal[$client_code][6] += $amount_exclude_tax;
                } else {
                  $other_subtotal[$client_code][5] += $amount_include_tax;
                  $other_subtotal[$client_code][6] += $amount_exclude_tax;
                }
              } 
            } else {
              // 月額費用、その他（継続発生）、その他（周期発生）の場合
              if ($cost_type == '5') {
                $amount_include_tax = $amount / $span * (100 + $tax_rate) / 100;
                $amount_exclude_tax = $amount / $span;
              }
              if (($billing_end_date === NULL) || 
                ($billing_end_date !== NULL && strtotime($billing_end_date) >= strtotime($month_first_date))
              ) {
                // billing_end_date is NULL: still invoicing
                // billing_end_date is not NULL and it is after the first of the month: still invoicing
                $subtotal[$client_code][5] += $amount_include_tax;
                $subtotal[$client_code][6] += $amount_exclude_tax;
                // 請求期間を仮に生成
                $day_of_billing_start_date = date('d', strtotime($billing_start_date));
                $invoice_billing_start_date = date('Y-m', strtotime($month)) . '-' . $day_of_billing_start_date;
                $invoice_billine_end_date = date('Y-m-d', strtotime($invoice_billing_start_date . ' +1 month -1 day'));
                if (strtotime($invoice_billing_start_date) >= strtotime($month_first_date) && strtotime($invoice_billing_start_date) <= strtotime($month_last_date)) {
                  if ($billing_end_date === NULL || strtotime($invoice_billing_start_date) <= strtotime($billing_end_date)) {
                    $all_total_results[] = [
                      date('Y-m', strtotime($month)),
                      $client_code,
                      $client_name,
                      $segments[$segment],
                      $country,
                      $invoice_code,
                      $seq,
                      $item,
                      $items_options[$item],
                      $cost_type_name,
                      $invoice_billing_start_date,
                      $invoice_billine_end_date,
                      $amount_include_tax,
                      $amount_exclude_tax,
                      $billing_start_date,
                      $billing_end_date
                    ];
                    if ($cost_type == '2') {
                      $monthly_subtotal[$client_code][5] += $amount_include_tax;
                      $monthly_subtotal[$client_code][6] += $amount_exclude_tax;
                      $item_subtotal[strval($segment)][$service_option_map[$item]][3] += $amount_include_tax;
                      $item_subtotal[strval($segment)][$service_option_map[$item]][4] += $amount_exclude_tax;
                    } else {
                      $other_subtotal[$client_code][5] += $amount_include_tax;
                      $other_subtotal[$client_code][6] += $amount_exclude_tax;
                    }
                  }
                }
              } else {
                // billing_end_date is not NULL and it is before the end of the month: invoice end
                continue;
              }
            }
          }
        }
        if ($type == "segment") {
          if (count($subtotal) > 0) {
            foreach ($subtotal as &$data) {
              $data[5] = intval($data[5]);
              $data[6] = intval($data[6]);
              $total_results[] = $data;
            }
            unset($data);
          }
          if (count($inital_subtotal) > 0) {
            foreach ($inital_subtotal as &$data) {
              $data[5] = intval($data[5]);
              $data[6] = intval($data[6]);
              $initial_total_results[] = $data;
            }
            unset($data);
          }
          if (count($monthly_subtotal) > 0) {
            foreach ($monthly_subtotal as &$data) {
              $data[5] = intval($data[5]);
              $data[6] = intval($data[6]);
              $monthly_total_results[] = $data;
            }
            unset($data);
          }
          if (count($other_subtotal) > 0) {
            foreach ($other_subtotal as &$data) {
              $data[5] = intval($data[5]);
              $data[6] = intval($data[6]);
              $other_total_results[] = $data;
            }
            unset($data);
          }
        } else {
          foreach ($item_subtotal as $items) {
            foreach ($items as &$data) {
              $data[3] = intval($data[3]);
              $data[4] = intval($data[4]);
              $item_total_results[] = $data;
            }
            unset($data);
          }
        }
      }
      if ($type == "segment") {
        return [
          'headers' => $total_header,
          'totalDatas' => $total_results,
          'initialDatas' => $initial_total_results,
          'monthlyDatas' => $monthly_total_results,
          'otherDatas' => $other_total_results
        ];
      } else if ($type == 'all') {
        return [
          'headers' => $total_header,
          'totalDatas' => $all_total_results
        ];
      } else {
        return [
          'headers' => $total_header, 
          'totalDatas' => $item_total_results
        ];
      }
    } catch (\Throwable $th) {
      throw $th;
    }
  }

  public function check_if_can_delete($type, $code) {
    switch ($type) {
      case 'contract':
        $sql = "SELECT invoice_id FROM t_invoice WHERE contract_id = :code AND status <> 0";
        break;
      case 'invoice':
        $sql = "SELECT invoice_id FROM t_invoice WHERE invoice_code = :code AND status <> 0";
        break;
      case 'client':
        $sql = "SELECT contract_id FROM t_contract WHERE client_code = :code";
        $query = DB::query(Database::SELECT, $sql);
        $query->parameters(array(':code' => $code));
        $result = $query->execute('contract')->as_array();
        if (count($result) == 0) {
          return true;
        }
        $contract_ids = [];
        foreach ($result as $row) {
          $contract_ids[] = $row['contract_id'];
        }
        $sql = "SELECT invoice_id FROM t_invoice WHERE contract_id IN :contract_ids AND status <> 0";
        $query = DB::query(Database::SELECT, $sql);
        $query->parameters(array(':contract_ids' => $contract_ids));
        $result = $query->execute('contract')->as_array();
        return count($result) == 0;
      default:
        throw new Exception('Invalid type: ' . $type);
    }
    $query = DB::query(Database::SELECT, $sql);
    $query->parameters(array(':code' => $code));
    $result = $query->execute('contract')->as_array();
    return count($result) == 0;
  }

  public function get_contract_attachment_data($client_code) {
    $monthly_cost_attachment_data = [];
    $initial_cost_attachment_data = [];
    $diff_data = [];
    try {
      $contract_sql = "SELECT tc.contract_id, tc.item, tc.billing_account, tc.cost_type, tc.cost, tc.cost_detail, tc.billing_start_date, tc.billing_end_date 
      FROM t_contract tc
      WHERE tc.client_code = :client_code AND tc.delete_flg = 0 AND tc.invalid_flg = 0 AND tc.cost_type IN (1,2)";
      $contract_query = DB::query(Database::SELECT, $contract_sql);
      $contract_query->parameters(array(':client_code' => $client_code));
      $contract_results = $contract_query->execute('contract')->as_array();
      $contract_ids = [];
      foreach ($contract_results as $contract) {
        if (!in_array($contract['contract_id'], $contract_ids)) {
          $contract_ids[] = $contract['contract_id'];
        }
        $item_code = $contract['item'];
        $cost_type = $contract['cost_type'];
        $item = match ($item_code) {
          '01' => 'chatbot',
          '02' => 'faq',
          '03' => 'survey',
          '04' => 'inquiry',
          '05' => 'very',
          '06' => 'page',
          '07' => 'workbot',
          '08' => 'ticket',
          '09' => 'newsletter',
          '12' => 'member',
          '13' => 'order',
          '1113' => '1113',
          '1114' => '1114',
          default => '',
        };
        if ($item == '') {
          continue;
        }
        $cost = intval($contract['cost']);
        $cost_detail = [];
        if ($contract['cost_detail'] !== NULL) {
          $cost_detail = json_decode($contract['cost_detail'], true);
        }
        $billing_start_date = $contract['billing_start_date'];
        $billing_end_date = $contract['billing_end_date'];
        $billing_account = explode(',', $contract['billing_account']);
        foreach ($billing_account as $bot_id) {
          if (isset($cost_detail[$bot_id])) {
            $cost = $cost_detail[$bot_id];
          }
          if ($cost_type == 1) {
            if (!isset($initial_cost_attachment_data[$bot_id])) {
              $initial_cost_attachment_data[$bot_id] = [
                'chatbot' => [],
                'faq' => [],
                'survey' => [],
                'inquiry' => [],
                'very' => [],
                'page' => [],
                'newsletter' => [],
                'member' => [],
                '1113' => []
              ];
            }
          } else {
            if (!isset($monthly_cost_attachment_data[$bot_id])) {
              $monthly_cost_attachment_data[$bot_id] = [
                'chatbot' => [],
                'faq' => [],
                'survey' => [],
                'inquiry' => [],
                'very' => [],
                'page' => [],
                'newsletter' => [],
                'member' => [],
                '1114' => []
              ];
            }
          }
          if ($item_code === '07') {
            if ($cost_type == 1) {
              $initial_cost_attachment_data[$bot_id]['workbot'] = [];
            } else {
              $monthly_cost_attachment_data[$bot_id]['workbot'] = [];
            }
          }
          if ($cost_type == 1) {
            if (!isset($initial_cost_attachment_data[$bot_id][$item][$cost])) {
              $initial_cost_attachment_data[$bot_id][$item][$cost] = [
                "start_date" => $billing_start_date,
                "end_date" => $billing_end_date,
                "cost" => $cost
              ];
            } else {
              if (
                strtotime($initial_cost_attachment_data[$bot_id][$item][$cost]['start_date']) !== NULL && 
                $billing_start_date !== NULL && 
                strtotime($initial_cost_attachment_data[$bot_id][$item][$cost]['start_date']) > strtotime($billing_start_date)
              ) {
                $initial_cost_attachment_data[$bot_id][$item][$cost]['start_date'] = $billing_start_date;
              }
              if (
                ($billing_end_date !== NULL && $initial_cost_attachment_data[$bot_id][$item][$cost]['end_date'] == NULL) || 
                ($billing_end_date !== NULL && $initial_cost_attachment_data[$bot_id][$item][$cost]['end_date'] !== NULL && strtotime($initial_cost_attachment_data[$bot_id][$item][$cost]['end_date']) < strtotime($billing_end_date))
              ) {
                $initial_cost_attachment_data[$bot_id][$item][$cost]['end_date'] = $billing_end_date;
              }
            }
            if (empty($initial_cost_attachment_data[$bot_id][$item])) {
              $initial_cost_attachment_data[$bot_id][$item][$cost] = [
                "start_date" => $billing_start_date,
                "end_date" => $billing_end_date,
                "cost" => $cost
              ];
            }
          } else {
            if (!isset($monthly_cost_attachment_data[$bot_id][$item][$cost])) {
              $monthly_cost_attachment_data[$bot_id][$item][$cost] = [
                "start_date" => $billing_start_date,
                "end_date" => $billing_end_date,
                "cost" => $cost
              ];
            } else {
              if (
                strtotime($monthly_cost_attachment_data[$bot_id][$item][$cost]['start_date']) !== NULL && 
                $billing_start_date !== NULL && 
                strtotime($monthly_cost_attachment_data[$bot_id][$item][$cost]['start_date']) > strtotime($billing_start_date)
              ) {
                $monthly_cost_attachment_data[$bot_id][$item][$cost]['start_date'] = $billing_start_date;
              }
              if (
                ($billing_end_date !== NULL && $monthly_cost_attachment_data[$bot_id][$item][$cost]['end_date'] == NULL) || 
                ($billing_end_date !== NULL && $monthly_cost_attachment_data[$bot_id][$item][$cost]['end_date'] !== NULL && strtotime($monthly_cost_attachment_data[$bot_id][$item][$cost]['end_date']) < strtotime($billing_end_date))
              ) {
                $monthly_cost_attachment_data[$bot_id][$item][$cost]['end_date'] = $billing_end_date;
              }
            }
            if (empty($monthly_cost_attachment_data[$bot_id][$item])) {
              $monthly_cost_attachment_data[$bot_id][$item][$cost] = [
                "start_date" => $billing_start_date,
                "end_date" => $billing_end_date,
                "cost" => $cost
              ];
            }
          }
        }
      }
      $monthly_cost_attachment_data = $this->process_attatch_data($monthly_cost_attachment_data);
      $initial_cost_attachment_data = $this->process_attatch_data($initial_cost_attachment_data);
      $diff_data = $this->get_contract_attachment_diffs($contract_ids);
      return [
        'monthly_attachment_data' => $monthly_cost_attachment_data,
        'initial_attachment_data' => $initial_cost_attachment_data,
        'diff_data' => $diff_data
      ];
    } catch (\Throwable $th) {
      throw $th;
    }
  }

  private function add_costs(&$total_cost, $entries) 
  {
    foreach ($entries as $entry) {
      $total_cost += $entry['cost'];
    }
  }

  private function is_in_range($interval_start, $interval_end, $entry_start, $entry_end) 
  {
    $start_ts = strtotime($interval_start);
    $end_ts = $interval_end === null ? null : strtotime($interval_end);
    $entry_start_ts = $entry_start === "" ? null : strtotime($entry_start);
    $entry_end_ts = $entry_end === "" ? null : strtotime($entry_end);

    return ($entry_start_ts === null || $end_ts === null || $entry_start_ts < $end_ts) &&
           ($entry_end_ts === null || $entry_end_ts > $start_ts);
  }

  private function build_total_range($unformat_total, &$has_no_end) 
  {
    $range = [];
    foreach ($unformat_total as $period_key => $entries) {
      [$start_date, $end_date] = explode("#", $period_key);
      if ($start_date !== "") $range[] = $start_date;
      if ($end_date === "") {
        $has_no_end = true;
      } else {
        $range[] = $end_date;
      }
    }
    $range = array_unique($range);
    usort($range, function($a, $b) {
      return strtotime($a) - strtotime($b);
    });
    if ($has_no_end) {
      $range[] = "#";
    }
    return $range;
  }

  private function calculate_interval_cost($interval_start, $interval_end, $unformat_total, $no_period_total)
  {
    $has_item = false;
    $cost = 0;
    foreach ($unformat_total as $period_key => $entries) {
      [$start_date, $end_date] = explode("#", $period_key);
      if ($this->is_in_range($interval_start, $interval_end, $start_date, $end_date)) {
        $has_item = true;
        $this->add_costs($cost, $entries);
      }
    }
    if ($no_period_total !== null) {
      $has_item = true;
      $this->add_costs($cost, $no_period_total);
    }
    if ($has_item) return $cost;
    return NULL;
  }

  private function process_attatch_data($attachment_data) {
    foreach ($attachment_data as $bot_id => &$bot_data) {
      $total = [];
      $unformat_total = [];
      foreach ($bot_data as $item => &$item_data) {
        $item_data = array_values($item_data);
        uasort($item_data, function($a, $b) {
          return strtotime($a['start_date']) - strtotime($b['start_date']);
        });
        foreach ($item_data as $index => &$data) {
          $date_index = "";
          $date_index = $data['start_date'] . "#" . $data['end_date'];
          if ($data['start_dete'] !== NULL) {
            $data['range'] = " (" . date('Y年m月d日', strtotime($data['start_date'])) . "より適用)";
          }
          if ($data['end_date'] !== NULL) {
            $data['range'] = " (" . date('Y年m月d日', strtotime($data['end_date'])) . "まで)";
          }
          if (!isset($unformat_total[$date_index])) {
            $unformat_total[$date_index] = [];
          }
          $unformat_total[$date_index][$item] = [
            'cost' => $data['cost'],
            'start_date' => $data['start_date'],
            'end_date' => $data['end_date'],
            'range' => $data['range'] ?? NULL
          ];
        }
      }
      $total = [];
      if (count($unformat_total) > 0) {
        $total_range = [];
        $has_no_end = false;
        $no_period_total = $unformat_total["#"] ?? NULL;
        unset($unformat_total["#"]);
        $total_range = $this->build_total_range($unformat_total, $has_no_end);
        $last_cost = null;
        if (count($total_range) == 0 && $no_period_total != NULL) {
          $cost = 0;
          $this->add_costs($cost, $no_period_total);
          $total[] = [
            'cost' => $cost,
            'start_date' => NULL,
            'end_date' => NULL,
          ];
        } else {
          for ($i=0; $i < count($total_range) - 1; $i++) {
            $start_date = $total_range[$i];
            $end_date = $total_range[$i + 1] === "#" ? NULL : $total_range[$i + 1];
            $cost = $this->calculate_interval_cost($start_date, $end_date, $unformat_total, $no_period_total);
            if ($cost === NULL) {
              continue;
            }
            if ($cost === 0 && $start_date == "" && $end_date == "") {
              continue;
            }
            if ($last_cost === null || $last_cost != $cost) {
              $total[] = [
                'cost' => $cost,
                'start_date' => $start_date === "" ? NULL : $start_date, 
                'end_date' => $end_date === "#" ? NULL : $end_date,
              ];
              $last_cost = $cost;
            }
          }
        }
      }
      $need_flag = count($total) > 1;
      if ($need_flag) {
        foreach ($total as $index => &$sub_total) {
          $next = $total[$index + 1] ?? NULL;
          if ($next !== NULL) {
            $sub_total['end_date'] = date('Y-m-d', strtotime($next['start_date'] . ' -1 day'));
            $sub_total['range'] = " (" . date('Y年m月d日', strtotime($sub_total['end_date'])) . "まで)";
          } else {
            $sub_total['range'] = " (" . date('Y年m月d日', strtotime($sub_total['start_date'])) . "より適用)";
          }
        }
      }
      $attachment_data[$bot_id]['total'] = $total;
    }
    return $attachment_data;
  }

  private function get_contract_attachment_diffs($contracts) {
    if (empty($contracts)) {
      return [];
    }
    $sql = "SELECT * FROM t_diff WHERE code IN :contract_ids AND type = 'contract' ORDER BY upd_time ASC";
    $query = DB::query(Database::SELECT, $sql);
    $query->parameters(array(':contract_ids' => $contracts));
    $result = $query->execute('contract')->as_array();
    // format data
    $diff_datas = [];
    $billing_date_diffs = [];
    foreach ($result as $row) {
      $upd_diff = json_decode($row['upd_diff'], true);
      $upd_date = date('Y-m-d', strtotime($row['upd_time']));
      if (!isset($upd_diff['items'])) {
        continue;
      }
      $items = $upd_diff['items'];
      foreach ($items as $item) {
        $temp_diff = [];
        $temp_billing_date_diff = [];
        if (isset($item['billing_account'])) {
          if (isset($item['billing_account']['new'])) {
            $new = explode(',', $item['billing_account']['new']);
            foreach ($new as $bot_id) {
              $temp_diff[] = [
                'billing_account' => $bot_id
              ];
            }
          } else if (isset($item['billing_account']['from']) && isset($item['billing_account']['to'])) {
            $billing_account_from = explode(',', $item['billing_account']['from']);
            $billing_account_to = explode(',', $item['billing_account']['to']);
            $billing_account_diff = array_merge(array_diff($billing_account_to, $billing_account_from), []);
            if (!empty($billing_account_diff)) {
              foreach ($billing_account_diff as $bot_id) {
                $temp_diff[] = [
                  'billing_account' => $bot_id
                ];
              }
            }
          }
        }
        $is_cost_diff = false;
        if ((isset($item['cost']) && isset($item['cost']['from'])) || 
        (isset($item['cost_detail']) && isset($item['cost_detail']['from']))) {
          $is_cost_diff = true;
        }
        if ($is_cost_diff) {
          $temp_diff[] = [
            'cost_diff' => true
          ];
        }
        $item_code = '';
        if (isset($item['item'])) {
          if (isset($item['item']['new'])) {
            $item_code = $item['item']['new'];
          }
          if (isset($item['item']['fixed'])) {
            $item_code = $item['item']['fixed'];
          }
          if (strpos($item_code, '11') === 0 || $item_code === '08' || $item_code === '10') {
            $item_code = '';
          }
        }
        if (isset($item['billing_start_date']) && array_key_exists('from', $item['billing_start_date']) && array_key_exists('to', $item['billing_start_date'])) {
          $temp_billing_date_diff = [
            'billing_start_date_diff' => true,
            'item' => $item_code,
            'date' => $upd_date,
          ];
        }
        foreach ($temp_diff as &$diff) {
          $diff['item'] = $item_code;
          $diff['date'] = $upd_date;
        }
        unset($diff);
        if (!empty($temp_diff)) {
          foreach ($temp_diff as $diff) {
            if (isset($diff['cost_diff'])) {
              $exists = array_filter($diff_datas, function($diff_data) use ($diff) {
                return $diff_data['item'] == $diff['item'] && $diff_data['cost_diff'] === true && strtotime($diff_data['date']) == strtotime($diff['date']);
              });
              if (empty($exists)) {
                $diff_datas[] = $diff;
                continue;
              }
            }
            $billingAccounts = array_column($diff_datas, 'billing_account');
            $items = array_column($diff_datas, 'item');
            $index = array_search($diff['billing_account'], $billingAccounts);
            $itemExists = $index !== false && $diff_datas[$index]['item'] == $diff['item'];
            if (!$itemExists) {
              $diff_datas[] = $diff;
            }
          }
        }
        if (!empty($temp_billing_date_diff)) {
          $billing_date_diffs[] = $temp_billing_date_diff;
        }
      }
    }
    $diff_datas = array_merge($diff_datas, $billing_date_diffs);
    uasort($diff_datas, function($a, $b) {
      return strtotime($a['date']) - strtotime($b['date']);
    });
    return $diff_datas;
  }

  public function generate_contractattachment_pdf($client_code, $version, $download = true) {
    try {
      $attachment_data = $this->get_created_contractattachment_by_code($client_code, $version);
      if ($attachment_data === NULL) {
        throw new ErrorException('No attachment data found');
      }
      $type = $attachment_data['type'];
      $filename = '別紙「契約管理」' . date('Ymd', strtotime($attachment_data['attach_date'])) . '.pdf';
      $contract_datas = json_decode($attachment_data['contract_data'], true);
      $diff_datas = json_decode($attachment_data['diff_data'], true);
      $remarks = null;
      if ($attachment_data['remark']) {
        $remarks = json_decode($attachment_data['remark'], true);
      }

      $PAGINATE_ROW_COUNT = 15;

      $attach_date = date('Y-m-d', strtotime($attachment_data['attach_date']));

      $defaultConfig = (new Mpdf\Config\ConfigVariables())->getDefaults();
      $fontDirs = $defaultConfig['fontDir'];
      $defaultFontConfig = (new Mpdf\Config\FontVariables())->getDefaults();
      $fontData = $defaultFontConfig['fontdata'];
      $mpdf = new \Mpdf\Mpdf([
        'mode' => 'utf-8',
        'format' => 'A3',
        'orientation' => 'P',
        'tempDir' => __DIR__ . '/../../../../files',
        'fontDir' => array_merge($fontDirs, [__DIR__ . '/../../../assets/common/font']),
        'fontdata' => $fontData + 
          [
            'notojp' => [
              'R' => 'NotoSansJP-Regular.ttf',
            ]
          ],
        'default_font' => 'notojp'
      ]);
      $mpdf->allow_charset_conversion=true;
      $mpdf->autoLangToFont=true;
      $mpdf->charset_in='UTF-8';
      $mpdf->showImageErrors = true;
      // get template
      $template = $this->get_bot_tpl_message(800, 'contract_attachment_template_type' . $type, 'ja');
      if ($template === '') {
        throw new ErrorException('No template found');
      }

      // diff_data
      $diff_messages = '';
      foreach ($diff_datas as $diff) {
        if(strtotime($attach_date) >= strtotime($diff['date'])) {
          continue;
        }
        $after_message = str_replace('FAQ', 'よくある質問自動生成（旧talkappi FAQ）', $diff['message']);
        $diff_messages .= '<p>' . date('Y年m月d日', strtotime($diff['date'])) . ' ' . $after_message . '</p>';
      }
      $template = str_replace('{diff_data}', $diff_messages, $template);
      $template = str_replace('{attach_date}', date('Y年m月d日', strtotime($attach_date)), $template);
      $template = str_replace('{create_date}', date('Y年m月d日', strtotime($attach_date)), $template);

      $table = $this->generate_attachment_table(
        $contract_datas['monthly_attachment_data'], 
        $contract_datas['initial_attachment_data'], 
        $remarks['remark'] ?? null, 
        $remarks['very_remark'] ?? null, 
        $attachment_data['attach_date']
      );

      if ($table['very_items_table'] && count($table['very_items_table']) > 0) {
        $template = str_replace('{pagination}', '(1/2)', $template);
      } else {
        $template = str_replace('{pagination}', '', $template);
      }
      $mpdf->WriteHTML($template);

      $talkappi_table_template = $this->get_bot_tpl_message(800, "contract_attachment_table_template", 'ja');
      if ($talkappi_table_template === '') {
        throw new ErrorException('No table template found');
      }

      $count = 0;
      foreach ($table['monthly_items_table'] as $segment => $monthly_items_table) {
        $sub_table_html = $talkappi_table_template;
        $monthly_items_header = '';
        $row_count = count(explode("<tr>", $monthly_items_table['monthly_items_body'])) - 1;
        foreach ($monthly_items_table['monthly_items_header'] as $header) {
          $monthly_items_header .= "<th style='width:9%'>$header</th>";
        }
        $sub_table_html = str_replace('{monthly_items_unit_num}', count($monthly_items_table['monthly_items_header']) + 1,  $sub_table_html);
        $sub_table_html = str_replace('{monthly_items_header_num}', count($monthly_items_table['monthly_items_header']),  $sub_table_html);
        $sub_table_html = str_replace('{monthly_items_header}', $monthly_items_header,  $sub_table_html);
        $sub_table_html = str_replace('{monthly_contract_data}', $monthly_items_table['monthly_items_body'],  $sub_table_html);
        if ($count == 0 || $row_count < $PAGINATE_ROW_COUNT) {
          $mpdf->WriteHTML($sub_table_html);
        } else {
          $mpdf->AddPage();
          $mpdf->WriteHTML($sub_table_html);
        }
        $count++;
      }

      if ($table['talkappi_remark']) {
        $mpdf->WriteHTML($table['talkappi_remark']);
      }
      
      if ($table['initial_items_body'] !== '') {
        $initial_template = $this->get_bot_tpl_message(800, 'contract_attachment_initial_cost_template', 'ja');
        if ($initial_template === '') {
          throw new ErrorException('No initial template found');
        }
        $initial_items_header = '';
        foreach ($table['initial_items_header'] as $header) {
          $initial_items_header .= "<th style='width:7%'>$header</th>";
        }
        $initial_template = str_replace('{initial_items_unit_num}', count($table['initial_items_header']), $initial_template);
        $initial_template = str_replace('{initial_items_header}', $initial_items_header, $initial_template);
        $initial_template = str_replace('{initial_contract_data}', $table['initial_items_body'], $initial_template);
        $mpdf->AddPage();
        $mpdf->WriteHTML($initial_template);
      }

      $sub_table_template = null;
      if (($table['chatbot_items_table'] && count($table['chatbot_items_table']) > 0) || ($table['very_items_table'] && count($table['very_items_table']) > 0)) {
        $sub_table_template = $this->get_bot_tpl_message(800, 'contract_attachment_very_template_type' . $type, 'ja');
        if ($sub_table_template === '') {
          throw new ErrorException('No very template found');
        }
        $sub_table_template = str_replace('{attach_date}', date('Y年m月d日', strtotime($attach_date)), $sub_table_template);
        $mpdf->AddPage();
        $mpdf->WriteHTML($sub_table_template);
        $sub_table_html_template = $this->get_bot_tpl_message(800, 'contract_attachment_sub_table_template', 'ja');
        if ($table['chatbot_items_table'] && count($table['chatbot_items_table']) > 0) {
          $chatbot_count = 0;
          foreach ($table['chatbot_items_table'] as $segment => $chatbot_items_table) {
            $sub_table_html = $this->generate_sub_table_html($sub_table_html_template, $chatbot_items_table);
            $sub_table_html = str_replace('{sub_title}', 'CHATBOT', $sub_table_html);
            $sub_chatbot_table_html = $sub_table_html['sub_table_html'];
            $row_count = $sub_table_html['row_count'];
            if ($chatbot_count == 0 || $row_count < $PAGINATE_ROW_COUNT) {
              $mpdf->WriteHTML($sub_chatbot_table_html);
            } else {
              $mpdf->AddPage();
              $mpdf->WriteHTML($sub_chatbot_table_html);
            }
            $chatbot_count++;
          }
        }
        if ($table['very_items_table'] && count($table['very_items_table']) > 0) {
          $very_count = 0;
          foreach ($table['very_items_table'] as $segment => $very_items_table) {
            $sub_table_html = $this->generate_sub_table_html($sub_table_html_template, $very_items_table);
            $sub_table_html = str_replace('{sub_title}', 'VERY', $sub_table_html);
            $sub_very_table_html = $sub_table_html['sub_table_html'];
            $row_count = $sub_table_html['row_count'];
            if ($very_count == 0 || $row_count < $PAGINATE_ROW_COUNT) {
              $mpdf->WriteHTML($sub_very_table_html);
            } else {
              $mpdf->AddPage();
              $mpdf->WriteHTML($sub_very_table_html);
            }
            $very_count++;
          }  
        }
      }

      if ($table['very_remark']) {
        $mpdf->WriteHTML($table['very_remark']);
      }

      if ($download) {
        return $mpdf->Output($filename, 'D');
      } else {
        return $mpdf->Output($filename, 'S');
      }
    } catch (\Throwable $th) {
      $exception = 'PDF generate fail: ' . $th->getMessage();
      throw new ErrorException($exception);
    }
  }

  private function generate_sub_table_html($sub_table_html_template, $items_table) {
    $sub_table_html = $sub_table_html_template;
    $items_header_html = '';
    $row_count = count(explode('<tr>', $items_table['items_body'])) - 1;
    foreach ($items_table['items_header'] as $header) {
      $items_header_html .= "<th>$header</th>";
    }
    $sub_table_html = str_replace('{sub_items_unit_num}', count($items_table['items_header']) + 1, $sub_table_html);
    $sub_table_html = str_replace('{sub_items_header_num}', count($items_table['items_header']), $sub_table_html);
    $sub_table_html = str_replace('{sub_items_header}', $items_header_html, $sub_table_html);
    $sub_table_html = str_replace('{sub_contract_data}', $items_table['items_body'], $sub_table_html);
    return [
      'sub_table_html' => $sub_table_html,
      'row_count' => $row_count
    ];
  }

  private function convertItemToHeader($item) {
    return match ($item) {
      'chatbot' => 'CHATBOT',
      'very' => 'VERY',
      'survey' => 'SURVEY',
      'inquiry' => 'INQUIRY',
      'page' => 'PAGE',
      'newsletter' => 'NEWSLETTER',
      'member' => 'MEMBER',
      'workbot' => 'WORKBOT',
      '1113' => '洗濯機連携',
      'faq' => 'よくある質問自動生成',
      '0102' => 'ネーティブ翻訳',
      '1114' => '洗濯機連携',
      '1103' => 'LINE公式アカウント利用料',
      'ticket' => 'TICKET',
      default => ''
    };
  }

  private function generateOrderAndHeader($standard_order, $datas, $is_sub_table = false) {
    $all_empty_item = [];
    foreach ($datas as $bot_id => $items) {
      if ($bot_id === 'total') continue;
      $item_keys = array_keys($items);
      $diff_keys = array_diff($standard_order, $item_keys);
      if (empty($all_empty_item)) $all_empty_item = array_merge([], $diff_keys);
      $all_empty_item = array_intersect($all_empty_item, $diff_keys);
    }
    $segment_order = [];
    $segment_header = [];
    foreach ($standard_order as $name) {
      if (!in_array($name, $all_empty_item)) {
        $segment_order[] = $name;
        $header = $this->convertItemToHeader($name);
        if ($is_sub_table) {
          if ($name === 'chatbot' || $name === 'very') {
            $header = "基本利用料";
          }
        }
        if ($header !== '') $segment_header[] = $header;
      }
    }
    return [
      $segment_order,
      $segment_header
    ];
  }

  public function generate_attachment_table($monthly_contract_datas, $initial_contract_datas = [], $talkappi_remark = NULL, $very_remark = null, $date = NULL) {
    $bots = $this->get_bots();
    // header
    $initial_item_header = [
      'CHATBOT',
      'VERY',
      'SURVEY',
      'INQUIRY',
      'PAGE',
      'NEWSLETTER',
      'MEMBER',
      '洗濯機連携'
    ];
    // order
    $order = [
      'total',
      'chatbot',
      'very',
      'survey',
      'inquiry',
      'page',
      'newsletter',
      'member',
      'ticket',
      'workbot',
      '1103'
    ];
    $initial_order = [
      'chatbot',
      // 'faq',
      'very',
      'survey',
      'inquiry',
      'page',
      'newsletter',
      'member',
      'ticket',
      '1113'
    ];
    // total
    $initial_all_total = [
      'chatbot' => 0,
      // 'faq' => 0,
      'very' => 0,
      'survey' => 0,
      'inquiry' => 0,
      'page' => 0,
      'newsletter' => 0,
      'member' => 0,
      '1113' => 0
    ];

    $none_empty_initial_keys = [];
    foreach ($initial_contract_datas as $items) {
      foreach ($items as $key => $item) {
        if ($key !== 'total' && !empty($item) && !in_array($key, $none_empty_initial_keys)) {
          $none_empty_initial_keys[] = $key;
        }
      }
    }
    if (!empty($none_empty_initial_keys)) {
      $initial_item_header = array_merge(array_map(function($value) {
        return $this->convert_item_name($value);
      }, $none_empty_initial_keys, []), []);
      $initial_order = $none_empty_initial_keys;
      $initial_all_total = array_merge(array_fill_keys($none_empty_initial_keys, 0), []);
    }
    $seperate_table_datas = [
      'chatbot' => [],
      'very' => [],
    ];
    $transform_monthly_datas = $this->transformCostDataByDateRange($monthly_contract_datas);
    $monthly_items_table = [];
    foreach ($transform_monthly_datas as $segment => $datas) {
      [$segment_order, $segment_header] = $this->generateOrderAndHeader($order, $datas);

      $monthly_tbody = '';
      [$segStart, $segEnd] = explode('~', $segment);
      if ($segStart || $segEnd) {
        if ($segEnd) {
          $dateTime = new DateTime($segEnd);
          $isLastDay = $dateTime->format('d') === $dateTime->format('t');
          if ($isLastDay) {
            $monthly_tbody .= '<tr><td>' . date('Y年m月請求分まで', strtotime($segEnd)) . '</td>';
          } else {
            $dateTime->modify('first day of this month'); // move to first day of current month
            $dateTime->modify('-1 month'); // then go to previous month
            $monthly_tbody .= '<tr><td>' . $dateTime->format('Y年m月請求分まで') . '</td>';
          }
        } else {
          $monthly_tbody .= '<tr><td>' . date('Y年m月請求分より', strtotime($segStart)) . '</td>';
        }
        for ($i = 0; $i < (count($segment_header) + 2); $i++) {
          $monthly_tbody .= '<td></td>';
        }
        $monthly_tbody .= '</tr>';
      }
      $total = $datas['total'] ?? [];
      unset($datas['total']);
      if (isset($total['total'])) {
        $monthly_tbody .= '<tr><td><b>合計費用</b></td>';
        foreach ($segment_order as $name) {
          $cost = 0;
          if ($name === 'very' || $name === 'chatbot') {
            $total_items = $name === 'chatbot' ? ['chatbot', 'faq', '0102'] : ['very', '1114'];
            foreach ($total_items as $item) {
              $cost += $total[$item] ?? 0;
            }
          } else {
            $cost = $total[$name] ?? 0;
          }
          $monthly_tbody .= '<td><b>' . number_format($cost) . '</b></td>';
        }
      }
      $monthly_tbody .= '<td></td></tr>';
      foreach ($datas as $bot_id => $items) {
        $monthly_tbody .= '<tr><td>' . $bots[$bot_id] . '</td>';
        $detail = '';
        foreach ($segment_order as $name) {
          if ($name === 'total') {
            $cost = $items['total'] ?? 0;
            $monthly_tbody .= '<td>' . number_format($cost) . '</td>';
          } else {
            $width = 60 / count($segment_order);
            $monthly_tbody .= '<td style="width:' . $width . '%">';
            if ($name === 'chatbot' || $name === 'very') {
              $item_keys = $name === 'chatbot' ? ['chatbot', 'faq', '0102'] : ['very', '1114'];
              $all_empty = true;
              foreach ($item_keys as $item_key) {
                if (!empty($items[$item_key])) {
                  $all_empty = false;
                  break;
                }
              }
              if ($all_empty) {
                $monthly_tbody .= '未導入';
              } else {
                $filtered_items = array_filter($items, function($item, $key) use ($item_keys) {
                  return in_array($key, $item_keys) && !empty($item);
                }, ARRAY_FILTER_USE_BOTH);
                $has_items = false;
                foreach ($item_keys as $key) {
                  if (!empty($filtered_items[$key])) {
                    $has_items = true;
                    break;
                  }
                }
                if ($has_items) {
                  $seperate_table_datas[$name][$segment][$bot_id] = $filtered_items;
                }
                $total = 0;
                $temp_date = null;
                foreach ($filtered_items as $item) {
                  if ($item['start_date'] !== NULL) {
                    if ($temp_date === NULL || strtotime($item['start_date']) < strtotime($temp_date)) {
                      $temp_date = $item['start_date'];
                    }
                  }
                  $total += $item['cost'];
                }
                $seperate_table_datas[$name][$segment][$bot_id]['total'] = $total;
                if ($temp_date === NULL) {
                  $year = date('Y');
                  $detail .= '<p>' . $this->convert_item_name($name) . ': ' . $year . '年●月●日(確定後更新)</p>';
                } else {
                  $detail .= '<p>' . $this->convert_item_name($name) . ': ' . date('Y年m月d日', strtotime($temp_date)) . '</p>';
                }
                $monthly_tbody .= '<p>' . number_format($total) . '</p>';
              }
            }
            else {
              if (empty($items[$name])) {
                $monthly_tbody .= '未導入';
              } else {
                if ($items[$name]['start_date'] === NULL) {
                  $year = date('Y');
                  $detail .= '<p>' . $this->convert_item_name($name) . ': ' . $year . '年●月●日(確定後更新)</p>';
                } else {
                  $detail .= '<p>' . $this->convert_item_name($name) . ': ' . date('Y年m月d日', strtotime($items[$name]['start_date'])) . '</p>';
                }
                $monthly_tbody .= '<p>' . number_format($items[$name]['cost']) . '</p>';
              }
              $monthly_tbody .= '</td>';
            }
          }
        }
        $monthly_tbody .= '<td>' . $detail . '</td>';
        $monthly_tbody .= '</tr>';
      }
      $monthly_items_table[$segment] = [
        'monthly_items_header' => $segment_header,
        'monthly_items_body' => $monthly_tbody
      ];
    }

    // initial_cost
    $initial_tbody = '';
    if (!empty($initial_contract_datas)) {
      foreach ($initial_contract_datas as $bot_id => $items) {
        $initial_tbody .= '<tr><td>' . $bots[$bot_id] . '</td>';
        $detail = '';
        foreach ($initial_order as $name) {
          $initial_tbody .= '<td style="width:7%">';
          if (empty($items[$name])) {
            $initial_tbody .= '-';
          } else {
            if ($items[$name][0]['start_date'] === NULL) {
              $year = date('Y');
              $detail .= '<p>' . $this->convert_item_name($name) . ': ' . $year . '年●月●日(確定後更新)</p>';
            } else {
              $detail .= '<p>' . $this->convert_item_name($name) . ': ' . date('Y年m月', strtotime($items[$name][0]['start_date'])) . '</p>';
            }
            $temp_total = 0;
            $len = count($items[$name]);
            if ($len >= 1) {
              $last = $items[$name][$len - 1];
              $temp_total = $last['cost'];
              $initial_tbody .= '<p>' . number_format($temp_total) . '</p>';
              $initial_all_total[$name] += $temp_total;
            }
          }
          $initial_tbody .= '</td>';
        }
        $initial_tbody .= "<td>$detail</td>";
        $initial_tbody .= '</tr>';
      }
      $initial_tbody .= '<tr style="font-weight:bold;"><td>合計費用</td>';
      foreach ($initial_order as $name) {
        $initial_tbody .= '<td>' . number_format($initial_all_total[$name]) . '</td>';
      }
      $initial_tbody .= '<td></td></tr>';
    }

    // seperate tables: chatbot, very
    $seperate_tables = [];
    foreach ($seperate_table_datas as $name => $seperate_table_data) {
      $seperate_tables[$name] = [];
      foreach ($seperate_table_data as $segment => $datas) {
        $seperate_all_total = $name === 'chatbot' ? [
          'total' => 0,
          'chatbot' => 0,
          'faq' => 0,
          '0102' => 0,
        ] : [
          'total' => 0,
          'very' => 0,
          '1114' => 0
        ];
        $orders = $name === 'chatbot' ? [
          'total',
          'chatbot',
          'faq',
          '0102'
        ] : [
          'total',
          'very',
          '1114'
        ];
        [$segment_order, $segment_header] = $this->generateOrderAndHeader($orders, $datas, true);
        [$segStart, $segEnd] = explode('~', $segment);
        $segment_tbody = '';
        if ($segStart || $segEnd) {
          if ($segEnd) {
            $dateTime = new DateTime($segEnd);
            $isLastDay = $dateTime->format('d') === $dateTime->format('t');
            if ($isLastDay) {
              $segment_tbody .= '<tr><td>' . date('Y年m月請求分まで', strtotime($segEnd)) . '</td>';
            } else {
              $dateTime->modify('first day of this month'); // move to first day of current month
              $dateTime->modify('-1 month'); // then go to previous month
              $segment_tbody .= '<tr><td>' . $dateTime->format('Y年m月請求分まで') . '</td>';
            }
          } else {
            $segment_tbody .= '<tr><td>' . date('Y年m月請求分より', strtotime($segStart)) . '</td>';
          }
          for ($i = 0; $i < (count($segment_header) + 2); $i++) {
            $segment_tbody .= '<td></td>';
          }
          $segment_tbody .= '</tr>';
        }
        $item_tbody = '';
        foreach ($datas as $bot_id => $items) {
          $item_tbody .= '<tr><td>' . $bots[$bot_id] . '</td>';
          $detail = '';
          $width = 75 / count($segment_order);
          foreach ($segment_order as $order) {
            if ($order === 'total') {
              $cost = $items['total'] ?? 0;
              $item_tbody .= '<td style="width:' . $width . '%">' . number_format($cost) . '</td>';
              $seperate_all_total['total'] += $cost;
            } else {
              $item_tbody .= '<td style="width:' . $width . '%">';
              if (empty($items[$order])) {
                $item_tbody .= '未導入';
              } else {
                if ($items[$order]['start_date'] === NULL) {
                  $year = date('Y');
                  $detail .= '<p>' . $this->convert_item_name($order) . ': ' . $year . '年●月●日(確定後更新)</p>';
                } else {
                  $detail .= '<p>' . $this->convert_item_name($order) . ': ' . date('Y年m月d日', strtotime($items[$order]['start_date'])) . '</p>';
                }
                $item_tbody .= '<p>' . number_format($items[$order]['cost']) . '</p>';
                $seperate_all_total[$order] += $items[$order]['cost'];
              }
              $item_tbody .= '</td>';
            }
          }
          $item_tbody .= "<td>$detail</td>";
          $item_tbody .= '</tr>';
        }

        $total_tbody = '<tr><td><b>合計費用</b></td>';
        foreach ($segment_order as $order) {
          $total_tbody .= '<td><b>' . number_format($seperate_all_total[$order]) . '</b></td>';
        }
        $total_tbody .= '<td></td></tr>';
        $item_tbody = $segment_tbody . $total_tbody . $item_tbody;
        $seperate_tables[$name][$segment] = [
          'items_header' => $segment_header,
          'items_body' => $item_tbody
        ];
      }
    }

    $results = [
      'monthly_items_table' => $monthly_items_table,
      'initial_items_header' => $initial_item_header,
      'initial_items_body' => $initial_tbody
    ];
    if ($talkappi_remark !== NULL) {
      $remark_body = '';
      $remark_body = '<table class="notasansjp">';
      $remark_body .= '<tr><td style="width:200px">【備考】</td><td style="text-align:left"><div>' . str_replace(["\r", "\n"], '<br />', $talkappi_remark) . '</div></td></tr>';
      $remark_body .= '</table>';
      $results['talkappi_remark'] = $remark_body;
    }
    if (!empty($seperate_tables['chatbot'])) {
      $results['chatbot_items_table'] = $seperate_tables['chatbot'];
    }
    if (!empty($seperate_tables['very'])) {
      $results['very_items_table'] = $seperate_tables['very'];
      if ($very_remark !== NULL) {
        $very_remark_body = '<table class="notasansjp">';
        $very_remark_body .= '<tr><td style="width:200px">【備考】</td><td style="text-align:left"><div>' . str_replace(["\r", "\n"], '<br />', $very_remark) . '</div></td></tr>';
        $very_remark_body .= '</table>';
        $results['very_remark'] = $very_remark_body;
      }
    }
    return $results;
  }

  private function transformCostDataByDateRange($data) 
  {
    $allDates = [];
    // 全ての日付を取得
    foreach ($data as $bot_id => $items) {
      foreach ($items as $item => $entries) {
        if ($item === 'total') continue;
        foreach ($entries as $entry) {
          if (!isset($entry['start_date']) || $entry['start_date'] === NULL) continue;
          $start = $entry['start_date'];
          $end = isset($entry['end_date']) && $entry['end_date'] !== NULL ? $entry['end_date'] : null;
          $allDates[$start] = true;
          if ($end) {
            $endDate = date('Y-m-d', strtotime($end . ' +1 day'));
            $allDates[$endDate] = true;
          }
        }
      }
    }
    
    // 日付をキーにグループ化
    $datePoints = array_keys($allDates);
    sort($datePoints);
    $segments = [];
    $lastIndex = count($datePoints) - 1;
    for ($i = 0; $i < $lastIndex; $i++) {
      $start = $datePoints[$i];
      $end = date('Y-m-d', strtotime($datePoints[$i + 1] . ' -1 day'));
      $key = "$start~$end";
      $segments[$key] = [];
    }
    $finalKey = "$datePoints[$lastIndex]~";
    $segments[$finalKey] = [];

    // データを日付範囲にグループ化
    foreach ($data as $bot_id => $items) {
      foreach ($items as $item => $entries) {
        if ($item === 'total') continue;
        foreach ($entries as $entry) {
          $start = $entry['start_date'];
          $end = isset($entry['end_date']) && $entry['end_date'] !== NULL ? $entry['end_date'] : null;
          $startTime = strtotime($start);
          $endTime = $end ? strtotime($end) : PHP_INT_MAX;

          foreach ($segments as $segment => $arr) {
            [$segStart, $segEnd] = explode('~', $segment);
            $segStartTime = strtotime($segStart);
            $segEndTime = $segEnd ? strtotime($segEnd) : PHP_INT_MAX;
            if ($startTime <= $segEndTime && $endTime >= $segStartTime) {
              if (!isset($segments[$segment][$bot_id])) {
                $segments[$segment][$bot_id] = [];
              }
              $segments[$segment][$bot_id][$item] = $entry;
            }
          }
        }
      }
    }

    // total計算
    foreach ($segments as $segment => $entries) {
      $sub_total = [];
      $total = 0;
      foreach ($entries as $bot_id => $items) {
        $bot_total = 0;
        foreach ($items as $item => $entry) {
          if (!isset($sub_total[$item])) {
            $sub_total[$item] = 0;
          }
          $sub_total[$item] += $entry['cost'];
          $bot_total += $entry['cost'];
        }
        $total += $bot_total;
        $segments[$segment][$bot_id]['total'] = $bot_total;
      }
      $sub_total['total'] = $total;
      $segments[$segment]['total'] = $sub_total;
    }
    return $segments;
  }

  private function convert_item_name($name) {
    switch ($name) {
      case '1113':
      case '1114':
        return '洗濯機連携';
      case 'faq':
        return 'よくある質問自動生成';
      default:
        return strtoupper($name);
    }
  }

  public function get_bots() {
    $sql = "SELECT bot_id, bot_name FROM t_bot WHERE delete_flg = 0";
    $query = DB::query(Database::SELECT, $sql);
    $result = $query->execute()->as_array();
    $bots = [];
    foreach ($result as $row) {
      $bots[$row['bot_id']] = $row['bot_name'];
    }
    return $bots;
  }

  public function create_contractattachment($client_code, $post) {
    try {
      if (!isset($post['attach_date'])) {
        throw new Exception('Invalid attach date');
      }
      if (!isset($post['create_date'])) {
        throw new Exception('Invalid create date');
      }
      $attachment_type = $post['attachment_type'] ?? 1;
      $new_version = $this->get_maximum_version_attachment($client_code) + 1;
      $results = $this->get_contract_attachment_data($client_code);
      $contract_data = [
        'initial_attachment_data' => $results['initial_attachment_data'],
        'monthly_attachment_data' => $results['monthly_attachment_data']
      ];
      $diff_data = $results['diff_data'];
      $diff_messages = $this->format_contractattachment_diff($diff_data);
      $insert_sql = "INSERT INTO t_contract_attachment (client_code, version, type, attach_date, create_date, contract_data, diff_data) 
      VALUES (:client_code, :version, :type, :attach_date, :create_date, :contract_data, :diff_data)";
      $query = DB::query(Database::INSERT, $insert_sql);
      $query->parameters(array(
        ':client_code' => $client_code,
        ':type' => $attachment_type,
        ':version' => $new_version,
        ':attach_date' => $post['attach_date'],
        ':create_date' => $post['attach_date'],
        ':contract_data' => json_encode($contract_data, JSON_UNESCAPED_UNICODE),
        ':diff_data' => json_encode($diff_messages, JSON_UNESCAPED_UNICODE)
      ));
      $query->execute('contract');
      return [
        'version' => $new_version,
        'client_code' => $client_code
      ];
    } catch (\Throwable $th) {
      throw $th;
    }
  }

  public function format_contractattachment_diff($diff_datas) {
    $diff_messages = [];
    $bots = $this->get_bots();
    foreach ($diff_datas as $diff) {
      $diff_message = '';
      $bot_name = isset($diff['billing_account']) ? ($bots[$diff['billing_account']] ?? '') : '';
      $cost_diff = $diff['cost_diff'] ?? NULL;
      $billing_start_date_diff = $diff['billing_start_date_diff'] ?? NULL;
      $item = $diff['item'];
      $item_name = match ($item) {
        '01' => 'CHATBOT',
        '02' => 'よくある質問自動生成（旧talkappi FAQ）',
        '03' => 'SUEVEY',
        '04' => 'INQUIRY',
        '05' => 'VERY',
        '06' => 'PAGE',
        '07' => 'WORKBOT',
        '09' => 'NEWSLETTER',
        '12' => 'MEMBER',
        '13' => 'ORDER',
        default => '',
      };
      if ($bot_name) {
        if ($item_name) {
          $diff_message = "$bot_name" . "の" . "$item_name" . "を追加";
        }
      } else if ($cost_diff && $item_name) {
        $diff_message = $item_name . "の月額利用料を更新";
      }
      if ($billing_start_date_diff) {
        if ($item_name) {
          $diff_message = $item_name . 'の「システム提供日」を更新';
        }
      }
      if (!$diff_message) continue;
      $diff_messages[] = [
        'date' => $diff['date'],
        'message' => $diff_message . '。'
      ];
    }
    return $diff_messages;
  }

  private function get_maximum_version_attachment($client_code) {
    try {
      $sql = "SELECT MAX(version) as version FROM t_contract_attachment WHERE client_code = :client_code";
      $query = DB::query(Database::SELECT, $sql);
      $query->parameters(array(':client_code' => $client_code));
      $result = $query->execute('contract')->as_array();
      return $result[0]['version'] ?? 0;
    } catch (\Throwable $th) {
      throw $th;
    }
  }

  public function get_created_contractattachments($client_code, $last_version = false) {
    try {
      $sql = "SELECT client_code, version, attach_date, create_date FROM t_contract_attachment WHERE client_code = :client_code ORDER BY version DESC";
      $query = DB::query(Database::SELECT, $sql);
      $query->parameters(array(':client_code' => $client_code));
      $result = $query->execute('contract')->as_array();
      if ($last_version) {
        if (empty($result)) {
          return null;
        } else {
          return $result[0];
        }
      }
      return $result;
    } catch (\Throwable $th) {
      return [];
    }
  }

  public function get_created_contractattachment_by_code($client_code, $version) {
    try {
      $sql = "SELECT * FROM t_contract_attachment WHERE client_code = :client_code AND version = :version";
      $query = DB::query(Database::SELECT, $sql);
      $query->parameters(array(':client_code' => $client_code, ':version' => $version));
      $result = $query->execute('contract')->as_array();
      return $result[0] ?? NULL;
    } catch (\Throwable $th) {
      throw $th;
    }
  }

  public function update_contractattachment($client_code, $version, $post) {
    try {
      $update_sql = "UPDATE t_contract_attachment 
      SET remark = :remark, diff_data = :diff_data 
      WHERE client_code = :client_code AND version = :version";
      $query = DB::query(Database::UPDATE, $update_sql);
      $remark = [];
      if ($post['remark']) {
        $remark['remark'] = $post['remark'];
      }
      if ($post['very_remark']) {
        $remark['very_remark'] = $post['very_remark'];
      }
      $query->parameters(array(
        ':remark' => empty($remark) ? NULL : json_encode($remark, JSON_UNESCAPED_UNICODE),
        ':diff_data' => $post['diff_data'],
        ':client_code' => $client_code,
        ':version' => $version
      ));
      $query->execute('contract');
    } catch (\Throwable $th) {
      throw $th;
    }
  }

  public function delete_contractattachment($client_code, $version) {
    try {
      $delete_sql = "DELETE FROM t_contract_attachment WHERE client_code = :client_code AND version = :version";
      $query = DB::query(Database::DELETE, $delete_sql);
      $query->parameters(array(':client_code' => $client_code, ':version' => $version));
      $query->execute('contract');
    } catch (\Throwable $th) {
      throw $th;
    }
  }

  public function get_checklist($representatives = NULL, $exclude_admin = false, $exclude_other = false) {
    $clients = $this->get_clients_option();
    $invoices = $this->get_invoices_option();
    $bots = $this->get_bots();
    $items = $this->get_master_code_options(888812);
    $sql = "SELECT * FROM t_check WHERE complete_time IS NULL ";
    $exclude_sql = "";
    if ($exclude_admin) {
      $exclude_sql = "AND (cs_checkflg = 1 OR cs_checkflg IS NULL) 
      AND (sales_checkflg = 1 OR sales_checkflg IS NULL) 
      AND (accountant_checkflg = 1 OR accountant_checkflg IS NULL) 
      AND (admin_checkflg = 0)";
    }
    if ($exclude_other) {
      $exclude_sql = "AND (cs_checkflg = 1 OR cs_checkflg IS NULL) 
      AND (sales_checkflg = 1 OR sales_checkflg IS NULL) 
      AND (accountant_checkflg = 0) 
      AND (admin_checkflg = 0)";
    }
    $sql .= $exclude_sql;
    $query = DB::query(Database::SELECT, $sql);
    $result = $query->execute('contract')->as_array();
    $contracts_status = [];
    $uncheck_invoices = [];
    $uncheck_clients = [];
    $uncheck_contracts = [];
    $uncheck_contract_ids = [];
    foreach ($result as $item) {
      if ($item['type'] == 'contract') {
        $contracts_status[$item['code']] = $item;
        $uncheck_contract_ids[] = $item['code'];
      } else if ($item['type'] == 'invoice' && $representatives === NULL) {
        $invoice = array_merge(array_filter($invoices, function($in) use ($item) {
          return $in['code'] == $item['code'] && $in['seq'] == $item['seq'];
        }), []);
        if (!empty($invoice)) {
          $uncheck_invoices[] = [
            'type' => 'invoice',
            'code' => $invoice[0]['code'],
            'seq' => $invoice[0]['seq'],
            'invoice_name' => $invoice[0]['text'],
            'depart' => $invoice[0]['depart'],
            'check_status' => $item
          ];
        }
      } else if ($item['type'] == 'client' && $representatives === NULL) {
        $client = array_merge(array_filter($clients, function($cl) use ($item) {
          return $cl['code'] == $item['code'];
        }), []);
        if (!empty($client)) {
          $uncheck_clients[] = [
            'type' => 'client',
            'code' => $client[0]['code'],
            'client_name' => $client[0]['text'],
            'check_status' => $item
          ];
        }
      }
    }
    if (empty($uncheck_contract_ids)) {
      return array_merge($uncheck_contracts, $uncheck_invoices, $uncheck_clients, []);
    }
    $contract_sql = "SELECT tc.*
    FROM t_contract tc 
    INNER JOIN m_client mc ON tc.client_code = mc.client_code AND mc.delete_flg = 0 AND mc.invalid_flg = 0
    WHERE tc.contract_id IN :contracts AND tc.delete_flg = 0 AND tc.invalid_flg = 0";
    if ($representatives !== NULL) {
      $contract_sql .= " AND (tc.sales_representative = :representatives OR tc.cs_representative = :representatives)";
    }
    $contract_query = DB::query(Database::SELECT, $contract_sql);
    $contract_query->parameters(array(
      ':contracts' => $uncheck_contract_ids, 
      ':representatives' => $representatives
    ));
    $results = $contract_query->execute('contract')->as_array();
    foreach ($results as $row) {
      $temp = [];
      $temp['type'] = 'contract';
      $temp['code'] = $row['contract_id'];
      $client = array_filter($clients, function($client) use ($row) {
        return $client['code'] == $row['client_code'];
      });
      $client = array_merge($client, []);
      $temp['client_name'] = count($client) > 0 ? $client[0]['text'] : '';
      $invoice = array_filter($invoices, function($invoice) use ($row) {
        return $invoice['code'] == $row['invoice_code'];
      });
      $invoice = array_merge($invoice, []);
      $temp['invoice_name'] = count($invoice) > 0 ? $invoice[0]['text'] . ($invoice[0]['seq'] >= 2 ? ' - ' . '第'  . $invoice[0]['seq'] . '世代':'') : '';
      $temp['depart'] = count($invoice) > 0 ? $invoice[0]['depart'] : '';
      $temp['check_status'] = $contracts_status[$row['contract_id']];
      $temp['item_name'] = $items[$row['item']] ?? '';
      $billing_account = explode(',', $row['billing_account']);
      if (count($billing_account) > 0) {
        $billing_account = array_map(function($account) use ($bots) {
          return $bots[$account];
        }, $billing_account);
        $temp['billing_account'] = $billing_account;
      } else {
        $temp['billing_account'] = [];
      }
      $uncheck_contracts[] = $temp;
    }
    return array_merge($uncheck_contracts, $uncheck_invoices, $uncheck_clients, []);
  }

  public function get_servicein_datas($bot_ids) {
    if (empty($bot_ids)) {
      return [];
    }
    $sql = "SELECT bot_id, seq, option_cd, start_date, end_date 
    FROM t_facility_options 
    WHERE bot_id IN :bot_ids AND contract_type = '01' 
    ORDER BY bot_id ASC, seq ASC";
    $query = DB::query(Database::SELECT, $sql);
    $query->parameters(array(':bot_ids' => $bot_ids));
    $result = $query->execute()->as_array();
    $data_by_bot = [];
    foreach ($result as $row) {
      $bot_id = $row['bot_id'];
      if (!isset($data_by_bot[$bot_id])) {
        $data_by_bot[$bot_id] = [];
      }
      $map_code = $this->facility_options_map($row['option_cd']);
      if ($map_code === NULL) continue;
      if (!isset($data_by_bot[$bot_id][$map_code])) {
        $data_by_bot[$bot_id][$map_code] = [];
      }
      $data_by_bot[$bot_id][$map_code][] = [
        'seq' => $row['seq'],
        'start_date' => Date('Y-m-d', strtotime($row['start_date'])),
        'end_date' => $row['end_date'] === NULL ? NULL : Date('Y-m-d', strtotime($row['end_date']))
      ];
    }
    return $data_by_bot;
  }

  private function facility_options_map($option_cd) {
    $code = match ($option_cd) {
      'bot' => '01',
      'faq' => '02',
      'survey' => '03',
      'inquiry' => '04',
      'very' => '05',
      'page' => '06',
      'workbot' => '07',
      'coupon' => '08',
      'ticket' => '08',
      'newsletter' => '09',
      'marketing' => '10',
      default => NULL
    };
    if ($code === NULL) {
      return NULL;
    }
    return strval($code);
  }

  private function get_service_option_map() {
    $map = [];
    $items = $this->get_master_code_options(888812, true);
    foreach ($items as $item) {
      if ($item['code'] != '11') {
        $map[$item['code']] = $item['code'];
      }
      if(isset($item['items'])) {
        foreach ($item['items'] as $option) {
          $map[$option['code']] = $item['code'] != '11' ? $item['code'] : $option['code'];
        }
      }
    }
    return $map;
  }

  public function get_invoicepayments_csvdatas($start_date, $end_date) {
    $headers = [
      '請求ID',
      'バージョン',
      '請求書番号',
      '契約ID',
      '送付状態',
      '入金状態',
      '請求先コード',
      '請求先名',
      '請求先部門名',
      '請求先世代',
      '送付方法',
      '請求日',
      '請求期限日',
      '品目',
      '請求開始日',
      '請求終了日',
      '金額'
    ];
    try {
      $params = [];
      $sql = "SELECT DISTINCT a.invoice_id, a.version, a.invoice_number, a.contract_id, a.status, a.receipt_flg, a.invoice_code, a.invoice_date, a.payment_due_date, a.invoice_details, 
      b.invoice_name, b.department_name, b.send_method, b.seq
      FROM `t_invoice` a
      LEFT join m_invoice b ON b.invoice_code=a.invoice_code
      WHERE a.invalid_flg=0
      AND ((a.invoice_date >= b.ver_start_date && a.invoice_date <= b.ver_end_date) OR (b.ver_start_date IS NULL AND a.invoice_date <= b.ver_end_date) OR (a.invoice_date >= b.ver_start_date AND b.ver_end_date IS NULL) OR (b.ver_start_date IS NULL AND b.ver_end_date IS NULL))";
      if ($start_date != "") {
        $sql .= " AND a.invoice_date >= :start_date";
        $params[':start_date'] = $start_date;
      }
      if ($end_date != "") {
        $sql .= " AND a.invoice_date <= :end_date";
        $params[':end_date'] = $end_date;
      }
      $sql .= "ORDER BY a.invoice_date DESC";
      $query = DB::query(Database::SELECT, $sql);
      $query->parameters($params);
      $results = $query->execute('contract')->as_array();
      $datas = [];
      foreach ($results as $result) {
        $invoice_details = json_decode($result['invoice_details'], true);
        foreach ($invoice_details as $invoice_detail) {
          $number = intval($invoice_detail['number']);
          $amount = intval($invoice_detail['amount']);
          $tax = intval($invoice_detail['tax']);
          $total = $number * $amount * (100 + $tax) / 100;
          $send_status = match ($result['status']) {
            '0' => '未送付',
            '1' => '送付済',
            default => '送付失敗'
          };
          $send_method = match ($result['send_method']) {
            'postal' => '郵送',
            'mail' => 'メール',
            'informart' => 'インフォマート',
            default => ''
          };
          $receipt_status = match ($result['receipt_flg']) {
            '0' => '未入金',
            '1' => '入金済み',
            default => ''
          };
          $datas[] = [
            $result['invoice_id'],
            $result['version'],
            $result['invoice_number'],
            $result['contract_id'],
            $send_status,
            $receipt_status,
            $result['invoice_code'],
            $result['invoice_name'],
            $result['department_name'],
            $result['seq'],
            $send_method,
            $result['invoice_date'],
            $result['payment_due_date'],
            $invoice_detail['item_name'],
            $invoice_detail['billing_start_date'],
            $invoice_detail['billing_end_date'],
            $total
          ];
        }
      }
      return [
        'headers' => $headers,
        'datas' => $datas
      ];
    } catch (\Throwable $th) {
      throw $th;
    }
  }
}
