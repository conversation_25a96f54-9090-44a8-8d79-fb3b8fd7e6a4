<?php defined('SYSPATH') OR die('No direct script access.');

class Model_Basemodel extends Model
{
	public $_bot;
	public $_log_ext = '';
	public $_admin_lang_cd = 'ja';

	function __construct($admin_lang_cd = 'ja')
	{
		$this->_admin_lang_cd = $admin_lang_cd;
	}

	function init($bot_id) 
	{
		$this->_log_ext = $this->get_log_table($bot_id);	
		$this->_bot = ORM::factory('bot', $bot_id);
	}
	
	function get_log_table($bot_id)
	{
		// not use t_bot_log_xxxx 
		//return '';
		if ($bot_id == 401000 || $bot_id == 401002 || $bot_id == 401003) {
			return '_401001';
		}
		$grp_bot_id = $this->get_grp_bot_id($bot_id);
		if ($grp_bot_id > 0) {
			return '_' . $grp_bot_id;
		}
		else {
			return '_' . $bot_id;
		}
	}
	
	function get_log_table_fix($bot_id)
	{
		if ($bot_id == 401000 || $bot_id == 401002 || $bot_id == 401003) {
			return '_401001';
		}
		$grp_bot_id = $this->get_grp_bot_id($bot_id);
		if ($grp_bot_id > 0) {
			return '_' . $grp_bot_id;
		}
		else {
			return '_' . $bot_id;
		}
	}
	
	function get_log($bot_id, $log_id) 
	{
		$tbl = 't_bot_log' . $this->get_log_table($bot_id);
		$sql = "SELECT * FROM $tbl WHERE log_id=:log_id";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':log_id' => $log_id,
		));
		return $query->execute()->as_array()[0];
	}
	
	function get_setting($setting_cd)
	{
		$config = (array)Kohana::$config->load('settings');
		if (isset($config[$setting_cd])) return $config[$setting_cd];
		return null;
	}

	function get_env($setting_cd)
	{
		$config = (array)Kohana::$config->load('env');
		if (isset($config[$setting_cd])) return $config[$setting_cd];
		return null;
	}
	
	function get_code($code_div, $lang_cd='ja')
	{
		$config = $this->get_config('code', $lang_cd);
		return $config[$code_div];
	}
	
	function get_system_config($config_name)
	{
		return (Array)Kohana::$config->load($config_name);
	}

	function get_config($config_name, $lang_cd='ja')
	{
		return (Array)Kohana::$config->load($config_name. '.' . $lang_cd);
	}
	
	function get_code_kv($table, $key_column, $val_column, $condition = '', $sort='')
	{
		$sql = "SELECT $key_column, $val_column FROM $table ";
		if ($condition != '') $sql = $sql . " WHERE $condition ";
		if ($sort != '') $sql = $sql . " ORDER BY $sort ";
		$query = DB::query(Database::SELECT, $sql);
		$results = $query->execute()->as_array($key_column, $val_column);
		return $results;
	}
	
	function get_sns_setting($bot_id, $sns_type_cd, $lang_cd) {
		$env = NULL;
		$envs = $this->get_bot_setting($bot_id, $sns_type_cd);
		$envs = json_decode($envs, true);
		if (count($envs) == count($envs, 1)) {
			$env = $envs;
		} else {
			foreach($envs as $e) {
				if ($e['lang_cd'] == $lang_cd) {
					$env = $e;
					break;
				}
			}
			if ($env == NULL) $env = $envs[0];
		}
		return $env;
	}
	
	function get_sns_setting_by_sns_id($bot_id, $sns_type_cd, $sns_id) {
		$env = NULL;
		$envs = $this->get_bot_setting($bot_id, $sns_type_cd);
		$envs = json_decode($envs, true);
		if (count($envs) == count($envs, 1)) {
			$env = $envs;
		} else {
			foreach($envs as $e) {
				if ($e['sns_id'] == $sns_id) {
					$env = $e;
					break;
				}
			}
			if ($env == NULL) $env = $envs[0];
		}
		return $env;
	}
	
	function get_bot_by_scene($scene_cd)
	{
		$sql = 'SELECT a.facility_cd, a.bot_id, a.lang_cd, a.bot_status_cd, a.start_date, a.end_date, a.delete_flg, s.label, s.scene_name, s.label
				FROM t_bot a
				INNER JOIN t_bot_scene s
				ON s.bot_id = a.bot_id
				WHERE s.scene_name = :scene_cd';
		/*
		 $sql = 'SELECT a.facility_cd, a.bot_id, a.lang_cd, s.label, s.scene_name
		 FROM t_bot a
		 INNER JOIN t_bot_scene s
		 ON s.facility_cd = a.facility_cd
		 WHERE s.scene_name = :scene_cd';
		 */
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':scene_cd' => $scene_cd,
		));
		$results = $query->execute()->as_array();
		if (count($results) == 0) return null;
		if ($results[0]['delete_flg'] == 1) return null;
		if ($results[0]['bot_status_cd'] > '04') return null;
		if ($results[0]['end_date'] != null && $results[0]['end_date'] < date("Y-m-d")) return null;
		return $results[0];
	}
	
	function get_bot_class_types_kv($bot, $lang_cd, $self=true)
	{
		$bot_class_cd = $bot->bot_class_cd;
		$div_faq_choice = $this->get_bot_setting($bot->bot_id, 'div_item_class_0');
		if ($div_faq_choice == '') {
			$sql = "SELECT class_cd, name FROM m_class_code WHERE code_div = 9001" . $bot_class_cd . " AND lang_cd=:lang_cd ORDER BY sort";
		}
		else {
			$sql = "SELECT class_cd, name FROM m_class_code WHERE code_div = $div_faq_choice AND parent_cd='' AND lang_cd=:lang_cd ORDER BY sort";
		}
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':lang_cd' => $lang_cd,
		));
		$results = $query->execute()->as_array('class_cd', 'name');
		if ($self == true) {
			$class_types = explode(',', $bot->class_type_cd);
			$ret = [];
			foreach($results as $k=>$v) {
				if (!in_array($k, $class_types)) unset($results[$k]);
			}
		}
		return $results;
	}
	
	function get_bot_class_types($bot, $lang_cd)
	{
		$bot_class_cd = $bot->bot_class_cd;
		$div_faq_choice = $this->get_bot_setting($bot->bot_id, 'div_item_class_0');
		if ($div_faq_choice == '') {
			$sql = "SELECT class_cd, name, grid_pic_url, word
					FROM m_class_code WHERE code_div = 9001" . $bot_class_cd . " AND lang_cd=:lang_cd ORDER BY sort";
		}
		else {
			$sql = "SELECT class_cd, name, grid_pic_url, word
			FROM m_class_code WHERE code_div = $div_faq_choice AND parent_cd='' AND lang_cd=:lang_cd ORDER BY sort";
		}
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':lang_cd' => $lang_cd,
		));
		$results = $query->execute()->as_array();
		return $results;
	}
	
	function get_code_div($code_div, $lang_cd)
	{
		$sql = "SELECT * FROM m_class_code WHERE code_div = :code_div AND lang_cd=:lang_cd ORDER BY sort";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':code_div' => $code_div,
			':lang_cd' => $lang_cd,
		));
		$results = $query->execute()->as_array();
		return $results;
	}
	
	function get_code_div_kv($code_div, $lang_cd='ja')
	{
		$sql = "SELECT class_cd, name FROM m_class_code WHERE code_div = :code_div AND lang_cd=:lang_cd ORDER BY sort";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':code_div' => $code_div,
				':lang_cd' => $lang_cd,
		));
		$results = $query->execute()->as_array("class_cd", "name");
		return $results;
	}
	function get_code_div_kv_with_parent_cd($code_div, $lang_cd='ja')
	{
		$sql = "SELECT parent_cd, class_cd, name FROM m_class_code WHERE code_div = :code_div AND lang_cd=:lang_cd ORDER BY parent_cd, class_cd";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':code_div' => $code_div,
				':lang_cd' => $lang_cd,
		));
		$results = $query->execute()->as_array();
		return $results;
	}

	function get_bot_support_lang($bot)
	{
		$results = array();
		$lang_array = explode(',', $bot->support_lang);
		$code =  $this->get_code('02', Session::instance()->get('lang_cd_admin', NULL));
		foreach($lang_array as $it){
			if ($it != '') $results[$it] = $code[$it];
		}
		return $results;
	}
	function get_bot_local_lang($bot)
	{
		return $bot->local_lang;
	}
	
	function get_bot_lang($bot)
	{
		$results = array();
		$lang_array = explode(',', $bot->lang_cd);
		$code = $this->get_config('code');
		foreach($lang_array as $it) {
			if ($it != '') $results[$it] = $code['02'][$it];
		}
		return $results;
	}

	function get_bot_lang_cd_admin($bot)
	{
		$results = array();
		$lang_array = explode(',', $bot->lang_cd);
		$code =  $this->get_code('02', Session::instance()->get('lang_cd_admin', NULL));
		foreach($lang_array as $it){
			if ($it != '') $results[$it] = $code[$it];
		}
		return $results;
	}

	function get_admin_lang() {
		if (isset($this->_bot->lang_cd)) {
			$lang_array = explode(',', $this->_bot->lang_cd);
			if (!in_array('ja', $lang_array)) return $lang_array[0];;
		}
		return 'ja';
	}
	
	function get_bot_logo($bot_id)
	{
		$scene_cd = $this->get_bot_setting($bot_id, 'default_scene_cd');
		return $this->get_env('base_url') . "assets/talkappi/f/" . $scene_cd .  "/logo.png";
	}
	
	function get_bot_array($bot_id, $desc='DESC') {
		$bot_arr = [$bot_id];
		$template_bot = $this->get_bot_setting_self($bot_id, 'template_bot');
		if ($template_bot != '') $bot_arr[] = $template_bot;
		$grp_bot_id = $this->get_grp_bot_id($bot_id);
		if ($grp_bot_id > 0) {
			$bot_arr[] = $grp_bot_id;
		}
		$bot_arr[] = 0;
		if ($desc == 'DESC') {
			return $bot_arr;
		}
		else {
			return array_reverse($bot_arr);
		}
	}

	function get_faq_bot_array($bot_id, $desc='DESC') {
		$bot_arr = [$bot_id];
		$template_bot = $this->get_template_bot_id($bot_id);
		if ($template_bot != '') $bot_arr[] = $template_bot;
		$grp_bot_id = $this->get_grp_bot_id($bot_id);
		if ($grp_bot_id > 0) {
			$bot_arr[] = $grp_bot_id;
		}
		$bot_arr[] = 0;
		if ($desc == 'DESC') {
			return $bot_arr;
		}
		else {
			return array_reverse($bot_arr);
		}
	}
	
	function get_bot_setting_dict($bot_id) {
		$bot_arr = $this->get_bot_array($bot_id);
		$result = [];
		foreach($bot_arr as $b) {
			$sql = "SELECT setting_cd, setting_value FROM t_bot_setting WHERE delete_flg = 0 AND bot_id=:bot_id";
			$query = DB::query(Database::SELECT, $sql);
			$query->parameters(array(
					':bot_id' => $b,
			));
			$bot_setting = $query->execute()->as_array("setting_cd", "setting_value");
			foreach($bot_setting as $k=>$v) {
				if ($k == 'flg_very' && $b != $bot_id && $b != 0) continue;
				if (!array_key_exists($k, $result)) {
					$result[$k] = $v;
				}
			}
		}

		/*
		$default = $this->get_config('botsetting');
		foreach($default as $k=>$v) {
			if (!array_key_exists($k, $result)) {
				$result[$k] = $v[1];
			}
		}
		*/
		return $result;
	}

	function get_bot_setting($bot_id, $setting_cd, $parse_json = false)
	{
		$bot_arr = $this->get_bot_array($bot_id);
		foreach($bot_arr as $bot_id) {
			$sql = "SELECT setting_cd, setting_value FROM t_bot_setting WHERE bot_id = :bot_id AND setting_cd = :setting_cd";
			$query = DB::query(Database::SELECT, $sql);
			$query->parameters(array(
				':bot_id' => $bot_id,
				':setting_cd' => $setting_cd,
			));
			$results = $query->execute()->as_array();
			if (count($results) > 0) {
				if ($parse_json) {
					return json_decode($results[0]['setting_value'], true);
				}
				else {
					return $results[0]['setting_value'];
				}
			}
			else {
				if ($setting_cd == 'template_bot') return '';
			}
		}
		return '';
	}

	function get_contract_services($bot_id) {

		$sql = "SELECT DISTINCT m.name,c.item as option_cd,o.start_date,o.start_time,o.end_date,o.end_time FROM contract.t_contract c
		LEFT JOIN talkbot.t_facility_options o ON o.option_id = c.item AND o.bot_id=:bot_id AND o.contract_type='01' AND o.option_id = c.item 
                INNER JOIN talkbot.m_class_code m ON c.item = m.class_cd AND m.code_div=888812 AND m.lang_cd='ja'
        		WHERE FIND_IN_SET(:bot_id, c.billing_account);";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':bot_id' => $bot_id
		));
		$results = $query->execute()->as_array();
		return $results;
	}
	
	function update_bot_setting($bot_id, $setting_cd, $value, $upd_user) {
		$setting = ORM::factory('botsetting')->where('bot_id', '=', $bot_id)->where('setting_cd', '=', $setting_cd)->find();
		if (isset($setting->bot_id)) {
			DB::update('t_bot_setting')->set(array('setting_value'=>$value,'upd_user'=>$upd_user,'upd_time'=>date('Y-m-d H:i:s')))->
			where('bot_id', '=', $bot_id)->where('setting_cd', '=', $setting_cd)->execute();
		}
		else {
			$setting = ORM::factory('botsetting');
			$setting->bot_id = $bot_id;
			$setting->setting_cd = $setting_cd;
			$setting->setting_value = $value;
			$setting->upd_user = $upd_user;
			$setting->upd_time = date('Y-m-d H:i:s');
			$setting->save();
		}
	}
	
	function get_bot_setting_self($bot_id, $setting_cd, $parse_json = false)
	{
		$sql = "SELECT setting_cd, setting_value
				 FROM t_bot_setting WHERE bot_id = :bot_id
		 			AND setting_cd = :setting_cd";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
				':setting_cd' => $setting_cd,
		));
		$results = $query->execute()->as_array();
		if (count($results) > 0) {
			if ($parse_json) {
				return json_decode($results[0]['setting_value'], true);
			}
			else {
				return $results[0]['setting_value'];
			}
		}
		return '';
	}
	
	function get_reserve_setting($bot_id, $link_key, $key) 
	{
		$json_reserve_settings = json_decode($this->get_bot_setting($bot_id, 'json_reserve_settings'), true);
		if (array_key_exists('type', $json_reserve_settings)) {
			if (array_key_exists($key, $json_reserve_settings)) {
				return $json_reserve_settings[$key];
			}
		}
		else {
			foreach($json_reserve_settings as $setting) {
				if (array_key_exists('key', $setting)) {
					if ($link_key == $setting['key']) {
						if (array_key_exists($key, $setting)) return $setting[$key];
					}
				}
				else {
					if ($link_key == '') {
						if (array_key_exists($key, $setting)) return $setting[$key];
					}
				}
			}
		}
		return [];
	}
	
	function get_bot_intent_skill($bot_id, $intent_cd) {
		$orm = ORM::factory('intentskill')->where('bot_id', '=', $bot_id)->where('intent_cd', '=', $intent_cd)->find();
		if (isset($orm->skill)) {
			$skill = json_decode($orm->skill, true);
			if (count($skill) > 0) {
				$ret = $skill[0]['skill'];
				if (array_key_exists('params', $skill[0])) {
					$ret = $ret . ' -- ' . json_encode($skill[0]['params']);
				}
				return $ret;
			}
			return '';
		}
		else {
			return '';
		}
	}
	
	function get_bot_intent_skills($bot_id, $intent_cd) {
		$orm = ORM::factory('intentskill')->where('bot_id', '=', $bot_id)->where('intent_cd', '=', $intent_cd)->find();
		if (isset($orm->skill)) {
			if ($orm->skill == '') return [];
			return json_decode($orm->skill, true);
		}
		else {
			return NULL;
		}
	}
	
	function get_message_tbl($msg_type_cd, $orm = true)
	{
		if ($msg_type_cd == 'mnu' || $msg_type_cd == 'btn' || $msg_type_cd == 'lst' || $msg_type_cd == 'rcm') return "botmsgdesclst";
		if ($msg_type_cd == 'mal' || $msg_type_cd == 'tpl' || $msg_type_cd == 'bkg') return "botmsgdesctpl";
		if ($msg_type_cd == 'img' || $msg_type_cd == 'mov') return "botmsgdescimg";
		if ($msg_type_cd == 'txt') return "botmsgdesctxt";
		if ($msg_type_cd == 'car') return "botmsgdesccar";
	}
	
	function get_message_self($bot_id, $msg_id)
	{
		$msg = ORM::factory('botmsg', $msg_id);
		return $this->get_bot_message_orm($bot_id, $msg->msg_cd);
	}
	
	function get_bot_message_orm($bot_id, $msg_cd)
	{
		$bot_message = ['bot_name', 'welcome_image', 'bot_description', 'bot_contact'];
		
		$msg = ORM::factory("botmsg")->where("bot_id", "=", $bot_id)->where("msg_cd", "=", $msg_cd)->where('delete_flg', '=', 0)->find();
		if (isset($msg->msg_id)) return $msg;
		if (!in_array($msg_cd, $bot_message)) {
			$template_bot = $this->get_bot_setting($bot_id, 'template_bot');
			if ($template_bot != '') {
				$msg = ORM::factory("botmsg")->where("bot_id", "=", $template_bot)->where("msg_cd", "=", $msg_cd)->where('delete_flg', '=', 0)->find();
				if (isset($msg->msg_id)) return $msg;
			}
			$grp_bot_id = $this->get_grp_bot_id($bot_id);
			if ($grp_bot_id > 0) {
				$msg = ORM::factory("botmsg")->where("bot_id", "=", $grp_bot_id)->where("msg_cd", "=", $msg_cd)->where('delete_flg', '=', 0)->find();
				if (isset($msg->msg_id)) return $msg;
			}
		}
		return ORM::factory("botmsg")->where("bot_id", "=", 0)->where("msg_cd", "=", $msg_cd)->where('delete_flg', '=', 0)->find();
	}
	
	function get_bot_common_message($bot_id, $msg_cd, $lang_cd, $self = false)
	{
		if (trim($msg_cd) == '') return null;
		if ($self) {
			$bot_arr = [$bot_id];
		}
		else {
			$template_bot = $this->get_bot_setting($bot_id, 'template_bot');
			$grp_bot_id = $this->get_grp_bot_id($bot_id);
			$bot_arr = [$bot_id];
			if ($template_bot != '') $bot_arr[] = $template_bot;
			if ($grp_bot_id > 0)  $bot_arr[] = $grp_bot_id;
			$bot_arr[] = 0;
		}
		foreach($bot_arr as $bot_id) {
			$msgs = ORM::factory('botmsg')->where('bot_id', '=', $bot_id)->where('msg_cd', '=', $msg_cd)->where('delete_flg', '=', 0)->find_all();
			if (count($msgs) > 0) break;
		}
		if (count($msgs) == 0) {
			return null;
		}
		$msg_type_cd = $msgs[0]->msg_type_cd;
		if ($msg_type_cd == 'mnu' || $msg_type_cd == 'btn' || $msg_type_cd == 'rcm') $msg_type_cd = 'lst';
		if ($msg_type_cd == 'mal') $msg_type_cd = 'tpl';
		if ($msg_type_cd == 'mov') $msg_type_cd = 'img';
		$sql = "SELECT a.msg_cd, a.msg_type_cd, a.msg_data, a.start_date, a.end_date, b.*
		FROM t_bot_msg a LEFT JOIN t_bot_msg_desc_$msg_type_cd b
		ON a.msg_id = b.msg_id WHERE a.msg_id = :msg_id";
		if ($lang_cd != NULL) $sql = $sql . " AND b.lang_cd = :lang_cd";
		$sql = $sql . " ORDER BY b.no";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':msg_id' => $msgs[0]->msg_id,
				':lang_cd' => $lang_cd,
		));
		$results = $query->execute()->as_array();
		return $results;
	}
	
	function get_bot_message($bot_id, $msg_cd, $lang_cd, $msg_type_cd, $extend=1)
	{
		$msgs = ORM::factory('botmsg')->where('bot_id', '=', $bot_id)->where('msg_cd', '=', $msg_cd)->where('delete_flg', '=', 0)->find_all();
		if (count($msgs) == 0) {
			if ($extend > 0) {
				$grp_bot_id = $this->get_grp_bot_id($bot_id);
				if ($grp_bot_id > 0) {
					if ($msg_type_cd == 'lst') {
						$grp_bot_id = $this->get_bot_setting($bot_id, 'template_bot');
					}
					$msgs = ORM::factory('botmsg')->where('bot_id', '=', $grp_bot_id)->where('msg_cd', '=', $msg_cd)->where('delete_flg', '=', 0)->find_all();
					if (count($msgs) == 0) {
						$bot_id = 0;
						$msgs = ORM::factory('botmsg')->where('bot_id', '=', 0)->where('msg_cd', '=', $msg_cd)->where('delete_flg', '=', 0)->find_all();
					}
					else {
						$bot_id = $grp_bot_id;
					}
				}
				else {
					$bot_id = 0;
					$msgs = ORM::factory('botmsg')->where('bot_id', '=', 0)->where('msg_cd', '=', $msg_cd)->where('delete_flg', '=', 0)->find_all();
				}
			}
			if($extend == 0) {
				$bot_id = 0;
				$msgs = ORM::factory('botmsg')->where('bot_id', '=', 0)->where('msg_cd', '=', $msg_cd)->where('delete_flg', '=', 0)->find_all();				
			}
		}
		if (count($msgs) == 0) {
			if ($msg_type_cd == 'mnu' || $msg_type_cd == 'btn' || $msg_type_cd == 'rcm') {
				return null;
			}
			else {
				return [];
			}
		}
		$msg_type_cd = $msgs[0]->msg_type_cd;
		if ($msg_type_cd == 'mnu' || $msg_type_cd == 'btn' || $msg_type_cd == 'rcm') $msg_type_cd = 'lst';
		if ($msg_type_cd == 'mal') $msg_type_cd = 'tpl';
		if ($msg_type_cd == 'mov') $msg_type_cd = 'img';
		$sql = "SELECT a.msg_cd, a.msg_type_cd, a.msg_data, b.*
		FROM t_bot_msg a LEFT JOIN t_bot_msg_desc_$msg_type_cd b
		ON a.msg_id = b.msg_id
		WHERE a.bot_id = :bot_id AND a.msg_cd = :msg_cd ";
		if ($lang_cd != NULL) $sql = $sql . " AND b.lang_cd = :lang_cd";
		$sql = $sql . " ORDER BY b.no";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
				':msg_cd' => $msg_cd,
				':lang_cd' => $lang_cd,
		));
		$results = $query->execute()->as_array();
		return $results;
	}
	
	function get_bot_txt_message_all_lang($bot_id, $msg_cd)
	{
		$msg_array = $this->get_bot_message($bot_id, $msg_cd, null, 'txt');
		$msgs = array();
		foreach($msg_array as $msg) {
			$msgs[$msg['lang_cd']] = $msg['content'];
		}
		return $msgs;
	}
	
	function get_bot_txt_message($bot_id, $msg_cd, $lang_cd)
	{
		$msg = $this->get_bot_message($bot_id, $msg_cd, $lang_cd, 'txt');
		if (count($msg) == 0) return '';
		return $msg[0]['content'];
	}
	
	function get_bot_txt_messages($bot_id, $msg_cd, $lang_cd)
	{
		$msg = $this->get_bot_message($bot_id, $msg_cd, $lang_cd, 'txt');
		if (count($msg) == 0) return [];
		$ret = [];
		foreach($msg as $it) {
			$ret[] = $it['content'];
		}
		return $ret;
	}
	
	function get_bot_tpl_message($bot_id, $msg_cd, $lang_cd, $parse_json = false)
	{
		$msg = $this->get_bot_common_message($bot_id, $msg_cd, $lang_cd);
		//$msg = $this->get_bot_message($bot_id, $msg_cd, $lang_cd, 'tpl');
		if ($msg == NULL || count($msg) == 0) {
			if ($parse_json) {
				return [];
			}
			else {
				return '';
			}
		}
		if ($parse_json) {
			return json_decode($msg[0]['content'], true);
		}
		else {
			return $msg[0]['content'];
		}
	}

	function get_bot_tpl_message_all_lang($bot_id, $msg_cd, $parse_json = true)
	{
		$msg_array = $this->get_bot_message($bot_id, $msg_cd, null, 'tpl');
		$msgs = array();
		foreach($msg_array as $msg) {
			if ($parse_json) {
				$msgs[$msg['lang_cd']] = json_decode($msg['content'], true);
			}
			else {
				$msgs[$msg['lang_cd']] = $msg['content'];
			}
		}
		return $msgs;
	}

	function get_bot_lst_message($bot_id, $msg_cd, $lang_cd)
	{
		return $this->get_bot_message($bot_id, $msg_cd, $lang_cd, 'lst', 1);
	}
	function get_bot_mal_message($bot_id, $msg_cd, $lang_cd)
	{
		$msg = $this->get_bot_message($bot_id, $msg_cd, $lang_cd, 'tpl');
		if (count($msg) == 0) return null;
		return array("subject"=>$msg[0]["title"], "body"=>$msg[0]["content"]);
	}
	function get_bot_img_message($bot_id, $msg_cd, $lang_cd)
	{
		$msg = $this->get_bot_message($bot_id, $msg_cd, $lang_cd, 'img');
		if (count($msg) == 0) return '';
		return $msg[0]['msg_image'];
	}
	function get_bot_img_message_list($bot_id, $msg_cd, $lang_cd)
	{
		$msg = $this->get_bot_message($bot_id, $msg_cd, $lang_cd, 'img');
		if (count($msg) == 1) {
			if ($msg[0]['msg_image'] == '') return [];
		}
		return $msg;
	}
	
	function get_item_lst_message($bot_id, $item_div, $class_cd, $message_cd, $lang_cd) {
		$class_cd = explode(' ', $class_cd)[0];
		for(;;) {
			if ($class_cd == '') {
				$msg = $this->get_bot_lst_message($bot_id, $message_cd . '_' . $item_div, $lang_cd);
				return $msg;
			}
			else {
				$msg = $this->get_bot_lst_message($bot_id, $message_cd . '_' . $item_div . '_' . $class_cd, $lang_cd);
				if (count($msg) > 0) return $msg;
			}
			$class_cd = substr($class_cd, 0, strlen($class_cd) - 2);
		}
	}
	
	function get_item_tpl_message($bot_id, $item_div, $class_cd, $message_cd, $lang_cd) {
		$class_cd = explode(' ', $class_cd)[0];
		for(;;) {
			if ($class_cd == '') {
				$msg = $this->get_bot_tpl_message($bot_id, $message_cd . '_' . $item_div, $lang_cd);
				if ($msg == '') {
					return [];
				}
				else {
					return json_decode($msg, true);
				}
			}
			else {
				$msg = $this->get_bot_tpl_message($bot_id, $message_cd . '_' . $item_div . '_' . $class_cd, $lang_cd);
				if ($msg != '') return json_decode($msg, true);
			}
			$class_cd = substr($class_cd, 0, strlen($class_cd) - 2);
		}
	}
	
	function get_message_preview_data($orm, $lang_cd, $button_config) {
		$msg_type_cd = $orm->msg_type_cd;
		$data = ['msg_type_cd'=>$msg_type_cd];
		$data['upd_time'] = str_replace(['-', ' ', ':'], '', $orm->upd_time);
		if ($msg_type_cd == 'btn' || $msg_type_cd == 'lst' || $msg_type_cd == 'mnu') {
			$desc = ORM::factory('botmsgdesclst')->where('msg_id', '=', $orm->msg_id)->where('lang_cd', '=', $lang_cd)->order_by('no')->find_all();
			$data['buttons'] = [];
			$data['title'] = '';
			foreach($desc as $d) {
				if ($d->title != '') $data['title'] = $d->title;
				if (strpos($d->content, "BTN_") === 0) {
					$data['buttons'][] = ['title'=>$button_config[$d->content], 'url'=>$d->url, 'style'=>$d->style, 'sns_cd'=>$d->sns_cd];
				}
				else {
					$data['buttons'][] = ['title'=>$d->content, 'url'=>$d->url, 'style'=>$d->style, 'sns_cd'=>$d->sns_cd];
				}
			}
		}
		else if ($msg_type_cd == 'txt') {
			$desc = ORM::factory('botmsgdesctxt')->where('msg_id', '=', $orm->msg_id)->where('lang_cd', '=', $lang_cd)->order_by('no')->find_all();
			$data['contents'] = [];
			foreach($desc as $d) {
				$data['contents'][] = $d->content;
			}
		}
		else if ($msg_type_cd == 'mal' || $msg_type_cd == 'tpl') {
			$desc = ORM::factory('botmsgdesctpl')->where('msg_id', '=', $orm->msg_id)->where('lang_cd', '=', $lang_cd)->order_by('no')->find_all();
			$data['contents'] = [];
			foreach($desc as $d) {
				$data['contents'][] = $d->content;
			}
		}
		else if ($msg_type_cd == 'img' || $msg_type_cd == 'mov') {
			$desc = ORM::factory('botmsgdescimg')->where('msg_id', '=', $orm->msg_id)->where('lang_cd', '=', $lang_cd)->order_by('no')->find_all();
			$data['images'] = [];
			foreach($desc as $d) {
				$data['images'][] = ['image'=>$d->msg_image, 'title'=>$d->title, 'url'=>$d->url];
			}
		}
		else if ($msg_type_cd == 'car') {
			$desc = ORM::factory('botmsgdesccar')->where('msg_id', '=', $orm->msg_id)->where('lang_cd', '=', $lang_cd)->order_by('no')->find_all();
			$data['contents'] = [];
			foreach($desc as $d) {
				$item = [];
				$item['title'] = $d->title;
				$item['description'] = $d->content;
				$item['image'] = $d->msg_image;
				$item['buttons'] = [];
				if ($d->btn1_name != '') {
					if (strpos($d->btn1_name, "BTN_") === 0) {
						$item['buttons'][] = ['title'=>$button_config[$d->btn1_name]];
					}
					else {
						$item['buttons'][] = ['title'=>$d->btn1_name];
					}
				}
				if ($d->btn2_name != '') {
					if (strpos($d->btn2_name, "BTN_") === 0) {
						$item['buttons'][] = ['title'=>$button_config[$d->btn2_name]];
					}
					else {
						$item['buttons'][] = ['title'=>$d->btn2_name];
					}
				}
				if ($d->btn3_name != '') {
					if (strpos($d->btn3_name, "BTN_") === 0) {
						$item['buttons'][] = ['title'=>$button_config[$d->btn3_name]];
					}
					else {
						$item['buttons'][] = ['title'=>$d->btn3_name];
					}
				}
				$data['contents'][] = $item;
			}
		}
		return $data;
	}

	function get_item_preview_data($id, $item_div, $lang_cd, $config_button, $params, $target = NULL) {
		$data = ['msg_type_cd'=>'car'];
		if ($item_div == 5) {
			$orm = ORM::factory('product', $id);
			$bot_id = $orm->bot_id;
			$bot = ORM::factory('bot', $bot_id);
			$def_buttons = $this->get_bot_lst_message($bot_id, 'product_def_button_{' . $bot->bot_class_cd . '}_' . substr($orm->class_cd, 0, 2), $lang_cd);
			if (count($def_buttons) == 0) $this->get_bot_lst_message($bot_id, 'product_def_button_{' . $bot->bot_class_cd . '}', $lang_cd);
			$sql = 'SELECT product_name AS title, sell_point AS description, product_image AS image, btn1_name, btn1_url, btn1_url_lang_cd, btn2_name, btn2_url, btn2_url_lang_cd, btn3_name, btn3_url, btn3_url_lang_cd FROM t_product_description WHERE product_id=:id AND lang_cd=:lang_cd';
		}
		else if($item_div == 6) {
			$orm = ORM::factory('botmsg', $id);
			$bot_id = $orm->bot_id;
			$bot = ORM::factory('bot', $bot_id);
			$def_buttons = $this->get_bot_lst_message($bot_id, 'item_def_button_{' . $bot->bot_class_cd . '}', $lang_cd);
			$sql = 'SELECT title AS title,
				content	AS description,
				msg_image AS image,
				url, btn1_name, btn1_url, btn1_url_lang_cd, btn2_name, btn2_url, btn2_url_lang_cd, btn3_name, btn3_url, btn3_url_lang_cd 
				FROM t_bot_msg_desc_car 
				WHERE msg_id=:id AND lang_cd=:lang_cd';
			if($target === NULL) $sql = $sql . ' AND no=' . $params['no'];
		}
		else {
			$orm = ORM::factory('item', $id);
			$bot_id = $orm->bot_id;
			$bot = ORM::factory('bot', $bot_id);
			$def_buttons = $this->get_bot_lst_message($bot_id, 'item_def_button_{' . $bot->bot_class_cd . '}', $lang_cd);
			$sql = 'SELECT item_name AS title, sell_point AS description, item_image AS image, btn1_name, btn1_url, btn1_url_lang_cd, btn2_name, btn2_url, btn2_url_lang_cd, btn3_name, btn3_url, btn3_url_lang_cd FROM t_item_description WHERE item_id=:id AND lang_cd=:lang_cd';
		}
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':id' => $id,
				':lang_cd' => $lang_cd,
		));
		$desc = $query->execute()->as_array();
		if (count($desc) == 0) {
			$data['contents']=[['title'=>'', 'description'=>'', 'image'=>'https://cdn.talkappi.com/0/common/no_pic.jpg', 'buttons'=>[]]];
			return $data;
		}
		$data['upd_time'] = str_replace(['-', ' ', ':'], '', $orm->upd_time);
		$data['contents'] = [];
		for($j = 0; $j < count($desc); $j++){
			$item = $desc[$j];
			$buttons = [];
			for($i=1; $i<=3; $i++) {
				if ($item['btn' . $i . '_name'] != '') {
					$url = $item['btn' . $i . '_url'];
					// if (strpos($url, 'http') === FALSE) {
					// 	$url = "javascript:void(0);";
					// }
					// else {
					// 	foreach($params as $k=>$v) {
					// 		$url = str_replace('{' . $k . '}', $v, $url);
					// 	}
					// }
					$title = $item['btn' . $i . '_name'];
					if (strpos($item['btn' . $i . '_name'], "BTN_") === 0) {
						if (array_key_exists($item['btn' . $i . '_name'], $config_button)) {
							$title = $config_button[$item['btn' . $i . '_name']];
						}
					}
					$buttons[] = ['title'=>$title, 'title_lang_cd'=>$item['btn' . $i . '_url_lang_cd'], 'url'=>$url];
				}
			}
			$buttons_def = [];
			foreach($def_buttons as $def) {
				if (strpos($def['content'], "BTN_") === 0) {
					$def['content'] = $config_button[$def['content']];
				}
				if (strpos($def['url'], 'http') === FALSE) {
					$def['url'] = "javascript:void(0);";
				}
				else {
					foreach($params as $k=>$v) {
						$def['url'] = str_replace('{' . $k . '}', $v, $def['url']);
					}
				}
				$buttons_def[] = ['title'=>$def['content'], 'title_lang_cd'=>'', 'url'=>$def['url']];
			}
			if (array_key_exists('t_item.location_lat', $params) && $params['t_item.location_lat'] != 0 && $item_div < 3) {
				$buttons_def[] = ["title"=>$config_button['BTN_ROUTE'], 'title_lang_cd'=>'', "url"=>''];
			}
			$item['buttons'] = $buttons;
			$item['buttons_def'] = $buttons_def;
			$data['contents'][] = $item;
		}
		return $data;
	}

	public function get_customize_list($list) {
		// "db":{"m_city.province":"m_city.province_name"}, "list":[{"":"限定なし"},{"Aichi":"愛知県"}], [{"1":"1人"},{"2":"２人"}]
		if (!is_array($list)) {
			$list = json_decode($list, true);
		}

		if (array_key_exists('db', $list)) {
			$db_info = $list['db'];
			$sql = "SELECT DISTINCT " . $db_info['code'] . ' AS code,' . $db_info['name'] . ' AS name FROM ' . $db_info['table'];
			if (array_key_exists('lang_cd', $db_info)) {
				$sql = $sql . " WHERE lang_cd='" . $db_info['language'] . "'";
			}
			if (array_key_exists('sort', $db_info)) {
				$sql = $sql . ' ORDER BY ' . $db_info['sort'];
			}
			$query = DB::query(Database::SELECT, $sql);
			$results = $query->execute()->as_array('code', 'name');
			if (array_key_exists('blank', $db_info) && $db_info['blank'] == 1) {
				$results = [''=>'指定なし'] + $results;
			}
			return $results;
		}
		else if (array_key_exists('list', $list)) {
			$results = [];
			foreach($list['list'] as $v) {
				$results[key($v)] = $v[key($v)];
			}
			return $results;		
		}
		else {
			return [];
		}
	}
	
	private function format_skill_params($params) {
		$params = json_decode(json_encode($params, JSON_UNESCAPED_UNICODE), true);
		$result = [];
		if (is_array($params)) {
			foreach($params as $k=>$v) {
				if ($v != '') {
					$result[$k] = $v;
				}
			}
			return json_encode($result);
		}
		else {
			return $params;
		}
	}
	function format_skill($skill) {
		if ($skill == '[]' || $skill == '""') {
			return '';
		}
		$skills = json_decode($skill, true);
		$new_skills = [];
		foreach($skills as $skill) {
			$params = [];
			if (isset($skill['params'])) {
				foreach($skill['params'] as $k=>$v) {
					if ($v != '') {
						$params[$k] = $v;
					}
				}
			}
			if (count($params) == 0) {
				$new_skills[] = ["skill"=>$skill['skill']];
			}
			else {
				$new_skills[] = ["skill"=>$skill['skill'], "params"=>$params];
			}
		}
		return json_encode($new_skills);
	}
	function is_skill($skill, $skill_name) {
		return (strpos($skill, $skill_name) !== false);
	}

	function parse_skill($skill) {
		$data = json_decode($skill, true);
		if (is_array($data)) {
			if (count($data) > 0) {
				if (isset($data['skill'])) return $data;
				return $data[0];
			}
			else {
				return null;
			}
		}
		else {
			$parts = explode('--', $skill);
			if (count($parts) > 1) {
				$params = json_decode(trim($parts[1]), true);
				if (is_array($params)) {
					return ["skill"=>trim($parts[0]), "params"=>$params];
				}
				else {
					$param_arr = [];
					for($i=1; $i<count($parts); $i++) {
						$param_arr[] = trim($parts[$i]);
					}
					return ["skill"=>trim($parts[0]), "params"=>implode('|', $param_arr)];
				}
			}
			else {
				return ["skill"=>trim($parts[0])];
			}
		}
		return null;
	}
	
	function parse_skill_array($skill) {
		if ($skill == null || trim($skill) == '') return [];
		$data = json_decode($skill, true);
		if (is_array($data)) {
			return $data;
		}
		else {
			$parts = explode('--', $skill);
			if (count($parts) > 1) {
				$params = json_decode(trim($parts[1]), true);
				if (is_array($params)) {
					// SKILL_NAME -- {}
					if ($this->_is_support_skill(trim($parts[0]))) {
						return [["skill"=>trim($parts[0]), "params"=>$params]];
					}
				}
			}
			else {
				if (strpos($parts[0], 'http') === false) {
					if ($this->_is_support_skill(trim($parts[0]))) {
						return [["skill"=>trim($parts[0])]];
					}
				}
			}
		}
		return [["skill"=>"UNSUPPORT", "params"=>["cmd"=>$skill]]];
	}
	function format_skill_array($skill_obj) {
		$action_skills = [];
		foreach($skill_obj as $k) {
			if (array_key_exists('params', $k)) {
				$action_skills[] = $k['skill'] . ' -- ' . $this->format_skill_params($k['params']);;
			}
			else {
				$action_skills[] = $k['skill'] ;
			}
		}
		return $action_skills;
	}
	
	function get_bot_report($bot_id, $start_date, $report_type_cd)
	{
		$orm = ORM::factory('botreport')->where('report_date', '=', $start_date)->where('bot_id', '=', $bot_id)->where('report_type_cd', '=', $report_type_cd)->find();
		if (isset($orm->bot_id)) {
			return json_decode($orm->content, true);
		}
		else {
			return NULL;
		}
	}
	
	function get_bot_report_range($bot_id, $start_date, $end_date, $report_type_cd)
	{
		$data = ORM::factory('botreport')->where('report_date', '>=', $start_date)->where('report_date', '<=', $end_date)->
		where('bot_id', '=', $bot_id)->where('report_type_cd', '=', $report_type_cd)->find_all();
		$range_data = [];
		foreach($data as $orm) {
			$report = json_decode($orm->content, true);
			if (array_key_exists('repeater', $range_data)) {
				$range_data['repeater'] = $range_data['repeater'] + $report['day']['repeater'];
			}
			else {
				$range_data['repeater'] = $report['day']['repeater'];
			}
			if (array_key_exists('member', $range_data)) {
				$range_data['member'] = $range_data['member'] + $report['day']['member'];
			}
			else {
				$range_data['member'] = $report['day']['member'];
			}
			if (array_key_exists('log', $range_data)) {
				$range_data['log'] = $range_data['log'] + $report['day']['log'];
			}
			else {
				$range_data['log'] = $report['day']['log'];
			}
			if (array_key_exists('log_answer', $range_data)) {
				$range_data['log_answer'] = $range_data['log_answer'] + $report['day']['log_answer'];
			}
			else {
				$range_data['log_answer'] = $report['day']['log_answer'];
			}
			if (array_key_exists('log_correct', $range_data)) {
				$range_data['log_correct'] = $range_data['log_correct'] + $report['day']['log_correct'];
			}
			else {
				$range_data['log_correct'] = $report['day']['log_correct'];
			}
		}
		return $range_data;
	}
	
	function get_sendmail_users($bot_id, $intent_cd) 
	{
		$sql = "SELECT m.email, m.name, t.intent_div, t.send_type FROM t_user m INNER JOIN t_user_notify t ON t.user_id=m.user_id AND t.intent_cd=:intent_cd 
				WHERE m.email_flg = 1 AND m.bot_id=:bot_id";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
				':intent_cd' => $intent_cd,
		));
		$results = $query->execute()->as_array();
		return $results;
	}
	
	function notify_link_system($bot_id, $system, $intent_cd, $message_cd, $message_params, $lang_cd, $config_key = '') {
		$param = ['bot_id'=>$bot_id, 'lang_cd'=>$lang_cd, 'intent_cd'=>$intent_cd, 'message_cd'=>$message_cd, 'params'=>$message_params];
		if ($config_key != '') $param['configKey'] = $config_key;
		if (in_array('lineworks', $system)) {
			$param['strip_tags'] = 1;
			$json_lineworks_setting = $this->get_bot_setting($bot_id, 'json_lineworks_setting');
			if ($json_lineworks_setting != '') {
				$data = $this->post_enginehook('service', 'lineworks_notify','', $param);
				if ($data['success'] == 'False') {
					Log::instance()->add(Log::DEBUG, 'lineworks_notify failure=' . json_encode($data));
				}
			}
		}
		if (in_array('slack', $system)) {
			$param['strip_tags'] = 1;
			$json_slack_setting = $this->get_bot_setting($bot_id, 'json_slack_setting');
			if ($json_slack_setting != '') {
				$data = $this->post_enginehook('service', 'slack_notify','', $param);
				if ($data['success'] == 'False') {
					Log::instance()->add(Log::DEBUG, 'slack_notify failure=' . json_encode($data));
				}
			}
		}
	}

	function send_notice_users($bot_id, $send, $intent_cd, $lang_cd, $message_cd, $message_params, $config_key = '', $mail_sender = '') {

		$param = ['bot_id'=>$bot_id, 'lang_cd'=>$lang_cd, 'message_cd'=>$message_cd, 'params'=>$message_params, 'intent_cd'=>$intent_cd];
		if ($config_key != '') $param['configKey'] = $config_key;

		if (in_array('lineworks', $send)) {
			$param['strip_tags'] = 1;
			$json_lineworks_setting = $this->get_bot_setting($bot_id, 'json_lineworks_setting');
			if ($json_lineworks_setting != '') {
				$data = $this->post_enginehook('service', 'lineworks_notify','', $param);
				if ($data['success'] == 'False') {
					Log::instance()->add(Log::DEBUG, 'lineworks_notify (' . $message_cd . ') failure=' . json_encode($data));
				}
			}
		}

		if (in_array('slack', $send)) {
			$param['strip_tags'] = 1;
			$json_slack_setting = $this->get_bot_setting($bot_id, 'json_slack_setting');
			if ($json_slack_setting != '') {
				$data = $this->post_enginehook('service', 'slack_notify','', $param);
				if ($data['success'] == 'False') {
					Log::instance()->add(Log::DEBUG, 'slack_notify failure=' . json_encode($data));
				}
			}
		}
  
		if (in_array('mail', $send)) {
			$users = $this->get_sendmail_users($bot_id, $intent_cd);
			$to = []; $cc=[]; $bcc=[];
			foreach($users as $user) {
				if ($user['send_type'] == 'to') {
					$to[] = $user['email'];
				}
				else if ($user['send_type'] == 'cc') {
					$cc[] = $user['email'];
				}
				else if ($user['send_type'] == 'bc') {
					$bcc[] = $user['email'];
				}
			}
			if (count($to) > 0) {
				$param['receiver'] = implode(',', $to);
				if (count($cc) > 0) $param['receiver_cc'] = implode(',', $cc);
				if (count($bcc) > 0) $param['receiver_bcc'] = implode(',', $bcc);
				if ($mail_sender != '') $param['sender'] = $mail_sender;
				$param['type'] = '08';
				$data = $this->post_enginehook('service', 'sendmail','', $param);
				if ($data['success'] == 'False') {
					Log::instance()->add(Log::DEBUG, 'sendusermail (' . $message_cd . ') failure=' . json_encode($data));
				}
			}
		}
	}

	function delete_bot_report($bot_id, $start_date, $report_type_cd='')
	{
		if ($report_type_cd == '') {
			DB::delete('t_bot_report')->where('report_date', '=', $start_date)->where('bot_id', '=', $bot_id)->execute();
		}
		else {
			DB::delete('t_bot_report')->where('report_date', '=', $start_date)->where('bot_id', '=', $bot_id)->where('report_type_cd', '=', $report_type_cd)->execute();
		}
	}
	
	function log_error($function, $message, $bot_id=null) {
		$this->_log_call($function, $message, 'E', $bot_id);
	}
	function log_warning($function, $message, $bot_id=null) {
		$this->_log_call($function, $message, 'W', $bot_id);
	}
	function log_info($function, $message, $bot_id=null) {
		$this->_log_call($function, $message, 'I', $bot_id);
	}
	function log_debug($function, $message, $bot_id=null) {
		$this->_log_call($function, $message, 'D', $bot_id);
	}
	
	private function _log_call($function, $message, $level, $bot_id) {
		$orm = ORM::factory('errorlog');
		if ($bot_id === null) {
			if (php_sapi_name() == 'cli') {
				$bot_id = 0;
				$user_id = 0;
			}
			else {
				$bot_id = Session::instance()->get('bot_id', NULL);
				if ($bot_id == null) $bot_id = 0;
				$user_id = Session::instance()->get('user_id', NULL);
				if ($user_id == null) $user_id = 0;
			}
		}
		else {
			$user_id = 0;
		}
		$orm->bot_id = $bot_id;
		$orm->user_id = $user_id;
		$orm->function = $function;
		$orm->message = $message;
		//$orm->message = substr($message, 0, 2000);
		$orm->level = $level;
		$orm->save();
	}
	
	function is_ctl_intent($intent_cd)
	{
		if ($intent_cd == 'change_chatuser' || $intent_cd == 'change_chatmode.0' ||
				$intent_cd == 'change_chatmode.1' || $intent_cd == 'change_chatmode.2' ||
				$intent_cd == 'clear_memberlog.all' || $intent_cd == 'clear_memberlog.1'||
				$intent_cd == 'update_membertag' || $intent_cd == 'mail_chatuser' ||
				$intent_cd == 'member_start_chat' || $intent_cd == 'member_end_chat' ||
				$intent_cd == 'input.chat.change_chatmode.0.user') {
					return true;
				}
				else {
					return false;
				}
	}
	function _create_bot_cond($bot_id, $col_name='bot_id')
	{
		if (strpos($bot_id, ',') !== false) {
			return " " . $col_name . " IN ($bot_id) ";
		}
		else {
			if ($bot_id == '') {
				$bot_id = Session::instance()->get('bot_id', NULL);
				$max_bot_id = intval($bot_id) + 1000;
				return ' ' . $col_name . '>=' . $bot_id . ' AND ' . $col_name . '<' . $max_bot_id;
			}
			else {
				return " " . $col_name . "= :bot_id ";
			}
		}
	}
	function _create_bot_cond_grp($bot_id, $col_name='bot_id')
	{
		if (strpos($bot_id, ',') !== false) {
			return " " . $col_name . " IN ($bot_id) ";
		}
		else {
			$bot_grp_id = $this->get_grp_bot_id($bot_id);
			if ($bot_grp_id == 0) {
				$max_bot_id = intval($bot_id) + 1000;
				return ' ' . $col_name . '>=' . $bot_id . ' AND ' . $col_name . '<' . $max_bot_id;
			}
			else if ($bot_grp_id > 0) {
				$max_bot_id = $bot_grp_id + 1000;
				return ' ' . $col_name . '>=' . $bot_grp_id . ' AND ' . $col_name . '<' . $max_bot_id;
			}
			else {
				return " $col_name = :bot_id ";
			}
		}
	}
	function _create_bot_cond_grp_only($bot_id, $col_name='bot_id')
	{
		if (strpos($bot_id, ',') !== false) {
			return " " . $col_name . " IN ($bot_id) ";
		}
		else {
			$bot_grp_id = $this->get_grp_bot_id($bot_id);
			if ($bot_grp_id == 0) {
				$max_bot_id = intval($bot_id) + 1000;
				return ' ' . $col_name . '>=' . $bot_id . ' AND ' . $col_name . '<' . $max_bot_id;
			}
			else {
				return " $col_name = :bot_id ";
			}
		}
	}

	// 親botで「すべての施設」選択の場合グループの全てのbotを対象、それ以外は対象botのみを検索
	function _create_bot_cond_grp_one($bot_id, $col_name='bot_id', $all = false)
	{
		if ($all === true) {
			$max_bot_id = intval($bot_id) + 1000;
			return ' ' . $col_name . '>=' . $bot_id . ' AND ' . $col_name . '<' . $max_bot_id;
		} else {
			return " $col_name = :bot_id ";
		}
	}
	
	function _create_bot_cond_grp_push($bot_id, $col_name='bot_id')
	{
		if (strpos($bot_id, ',') !== false) {
			return " " . $col_name . " IN ($bot_id) ";
		}
		else {
			return " $col_name = :bot_id ";
		}
	}
	
	function _create_bot_cond_webchat($bot_id, $col_name='bot_id')
	{
		if (strpos($bot_id, ',') !== false) {
			return " " . $col_name . " IN ($bot_id) ";
		}
		else {
			$bot_grp_id = $this->get_grp_bot_id($bot_id);
			if ($bot_grp_id == 0) {
				$max_bot_id = intval($bot_id) + 1000;
				return ' ' . $col_name . '>=' . $bot_id . ' AND ' . $col_name . '<' . $max_bot_id;
			}
			else if ($bot_grp_id > 0) {
				$max_bot_id = $bot_grp_id + 1000;
				return ' ' . $col_name . '>=' . $bot_grp_id . ' AND ' . $col_name . '<' . $max_bot_id;
			}
			else {
				return " $col_name = :bot_id ";
			}
		}
	}
	function _create_bot_cond_chat($bot_id, $col_name='bot_id')
	{
		if (strpos($bot_id, ',') !== false) {
			return " " . $col_name . " IN ($bot_id) ";
		}
		else {
			$bot_grp_id = $this->get_grp_bot_id($bot_id);
			if ($bot_grp_id == 0) {
				$max_bot_id = intval($bot_id) + 1000;
				return ' ' . $col_name . '>=' . $bot_id . ' AND ' . $col_name . '<' . $max_bot_id;
			}
			else if ($bot_grp_id > 0) {
				//return " ($col_name = $bot_grp_id OR $col_name = :bot_id) ";
				return " ($col_name = :bot_id) ";
			}
			else {
				return " $col_name = :bot_id ";
			}
		}
	}
	function _create_bot_cond_access($bot_id, $col_name='bot_id')
	{
		if (strpos($bot_id, ',') !== false) {
			return " " . $col_name . " IN ($bot_id) ";
		}
		else {
			$bot_grp_id = $this->get_grp_bot_id($bot_id);
			if ($bot_grp_id == 0) {
				$max_bot_id = intval($bot_id) + 1000;
				return ' ' . $col_name . '>=' . $bot_id . ' AND ' . $col_name . '<' . $max_bot_id;
			}
			else if ($bot_grp_id > 0) {
				$max_bot_id = $bot_grp_id+ 1000;
				return ' ' . $col_name . '>=' . $bot_grp_id . ' AND ' . $col_name . '<' . $max_bot_id;
			}
			else {
				return " $col_name = :bot_id ";
			}
		}
	}

	function get_grp_bot_id($bot_id) {
		if ($bot_id >= 201000 && $bot_id < 299999) {
			if ($bot_id % 1000 == 0) {
				return 0;
			}
			else {
				return intval(intval($bot_id) / 1000) * 1000;
			}
		} else if ($bot_id >= 2001000 && $bot_id < 2999999) {
			if ($bot_id % 1000 == 0) {
				return 0;
			}
			else {
				return intval(intval($bot_id) / 1000) * 1000;
			}
		}
		else {
			return -1;
		}
	}
	function get_template_bot_id($bot_id) {
		$template_bot = $this->get_bot_setting($bot_id, 'template_bot');
		if ($template_bot == '') return '';
		$flg_refer_template_faq = $this->get_bot_setting($bot_id, 'flg_refer_template_faq');
		if ($flg_refer_template_faq == 1) return $template_bot;
		return '';
	}
	function is_func_scene($bot_id, $scene_cd, $func) {
		$sql = "SELECT use_faq_flg, use_bot_flg, use_survey_flg, use_inquiry_flg FROM t_bot_scene WHERE bot_id=:bot_id AND scene_name:scene_cd";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
				':scene_cd' => $scene_cd,
		));
		$results = $query->execute()->as_array();
		if (count($results) == 0) return false;
		if ($func == 'faq') {
			return ($results[0]['use_faq_flg'] == 1);
		} else if ($func == 'survey') {
			return ($results[0]['use_survey_flg'] == 1);
		} else if ($func == 'bot') {
			return ($results[0]['use_bot_flg'] == 1);
		} else if ($func == 'inquiry') {
			return ($results[0]['use_inquiry_flg'] == 1);
		}
	}
	
	function get_func_scene_tag($bot_id, $scene_cd, $func) {
		$sql = "SELECT use_faq_flg, use_bot_flg, use_survey_flg, use_inquiry_flg, tag FROM t_bot_scene WHERE bot_id=:bot_id AND scene_name=:scene_cd";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
				':scene_cd' => $scene_cd,
		));
		$results = $query->execute()->as_array();
		if (count($results) == 0) return null;
		if ($func == 'faq') {
			if ($results[0]['use_faq_flg'] == 1) return $results[0]['tag'];
		} else if ($func == 'survey') {
			if ($results[0]['use_survey_flg'] == 1) return $results[0]['tag'];
		} else if ($func == 'bot') {
			if ($results[0]['use_bot_flg'] == 1) return $results[0]['tag'];
		} else if ($func == 'inquiry') {
			if ($results[0]['use_inquiry_flg'] == 1) return $results[0]['tag'];
		}
		return '';
	}
	
	function get_parent_func_scene_by_tag($bot_id, $func, $tag) {
		$sql = "SELECT scene_name FROM t_bot_scene WHERE bot_id=:bot_id AND tag=:tag";
		if ($func == 'faq') {
			$sql =  $sql . ' AND use_faq_flg = 1';
		} else if ($func == 'bot') {
			$sql =  $sql . ' AND use_bot_flg = 1';
		} else if ($func == 'survey') {
			$sql =  $sql . ' AND use_survey_flg = 1';
		}
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
				':tag' => $tag,
		));
		$results = $query->execute()->as_array();
		if (count($results) == 0) return '';
		return $results[0]['scene_name'];
	}
	
	function get_default_scene_path($func_type='', $bot_theme_cd='', $template_cd='') {
		if ($func_type === 'icon' || $func_type === 'bubbles' || $func_type === 'otapricecompare') {
			$func_type = 'bot';
		}
		$scene_cd = 'apps';
		if ($func_type == '') {
			return APPPATH . "../assets/" . $scene_cd . '/';
		}
		if ($bot_theme_cd == '') {
			if ($template_cd == '') {
				return APPPATH . "../assets/" . $scene_cd . '/' . $func_type . '/';
			}
			else {
				return APPPATH . "../assets/" . $scene_cd . '/' . $func_type . '/template/' . $template_cd . '/';
			}
		}
		else {
			if ($template_cd == '') {
				return APPPATH . "../assets/" . $scene_cd . '/' . $func_type . '/' . $bot_theme_cd. '/';
			}
			else {
				return APPPATH . "../assets/" . $scene_cd . '/' . $func_type . '/' . $bot_theme_cd. '/template/' . $template_cd . '/';
			}
		}
	}

	function get_scene_path($scene_cd='', $func_type='', $bot_theme_cd='', $template_cd='') {
		if ($func_type === 'icon' || $func_type === 'bubbles' || $func_type === 'otapricecompare') {
			$func_type = 'bot';
		}
		if ($scene_cd == 'default') return $this->get_default_scene_path($func_type, $bot_theme_cd, $template_cd);
		if ($scene_cd == '') {
			return APPPATH . "../assets/f/";
		}
		if ($func_type == '') {
			return APPPATH . "../assets/f/" . $scene_cd . '/';
		}
		if ($bot_theme_cd == '') {
			if ($template_cd == '') {
				return APPPATH . "../assets/f/" . $scene_cd . '/' . $func_type . '/';
			}
			else {
				return APPPATH . "../assets/f/" . $scene_cd . '/' . $func_type . '/template/' . $template_cd . '/';
			}
		}
		else {
			if ($template_cd == '') {
				return APPPATH . "../assets/f/" . $scene_cd . '/' . $func_type . '/' . $bot_theme_cd. '/';
			}
			else {
				return APPPATH . "../assets/f/" . $scene_cd . '/' . $func_type . '/' . $bot_theme_cd. '/template/' . $template_cd . '/';
			}
		}
	}
	
	function get_scene_url_path($scene_cd='', $func_type='', $bot_theme_cd='', $template_cd='') {
		if ($func_type === 'icon' || $func_type === 'bubbles' || $func_type === 'otapricecompare') {
			$func_type = 'bot';
		}
		if ($scene_cd == '') {
			return "/assets/f/";
		}
		if ($func_type == '') {
			return "/assets/f/$scene_cd/";
		}
		if ($bot_theme_cd== '') {
			if ($template_cd == '') {
				return "/assets/f/$scene_cd/$func_type/";
			}
			else {
				return "/assets/f/$scene_cd/$func_type/template/$template_cd/";
			}
		}
		else {
			if ($template_cd == '') {
				return "/assets/f/$scene_cd/$func_type/$bot_theme_cd/";
			}
			else {
				return "/assets/f/$scene_cd/$func_type/$bot_theme_cd/template/$template_cd/";
			}
		}
	}
	
	function get_scene_ref($scene_cd, $func_type = '', $bot_theme_cd = '', $template_cd = '') {
		if ($func_type === 'icon' || $func_type === 'bubbles' || $func_type == 'otapricecompare') {
			$func_type = 'bot';
		}
		$inherit = false;
		$orm = ORM::factory('botscene')->where('scene_name', '=', $scene_cd)->find();
		if ($orm->ref_scene_cd != NULL) {
			$ref = ORM::factory('botscene')->where('scene_name', '=', $orm->ref_scene_cd)->find()->as_array();
			$f = $func_type;
			if ($f == 'site') $f = 'page';
			if ($f == 'webchat') $f = 'bot';
			if ($ref['use_' . $f . '_flg'] == 1) {
				$inherit = true;
			}
		}
		if ($inherit == false) {
			$path = $this->get_scene_path($scene_cd, $func_type, $bot_theme_cd, $template_cd);
			if(file_exists($path . 'config.json')) {
				return $scene_cd;
			}
			else {
				$base_scene = $this->get_bot_setting($orm->bot_id, 'default_scene_cd');
				$path = $this->get_scene_path($base_scene, $func_type, $bot_theme_cd, $template_cd);
				if(!file_exists($path . 'config.json')) {
					$grp_bot_id = $this->get_grp_bot_id($orm->bot_id);
					if ($grp_bot_id > 0) {
						$faq_scene_tag = $this->get_func_scene_tag($orm->bot_id, $scene_cd, 'faq');
						if ($faq_scene_tag != '') {
							$base_scene = $this->get_parent_func_scene_by_tag($grp_bot_id, 'faq', $faq_scene_tag);
							$path = $this->get_scene_path($base_scene, $func_type, $bot_theme_cd, $template_cd);
							if(!file_exists($path . 'config.json')) {
								$base_scene = $this->get_bot_setting($grp_bot_id, 'default_scene_cd');
							}
						}
						else {
							$base_scene = $this->get_bot_setting($grp_bot_id, 'default_scene_cd');
							$path = $this->get_scene_path($base_scene, $func_type, $bot_theme_cd, $template_cd);
							if(!file_exists($path . 'config.json')) {
								return 'default';
							}
							else {
								return $base_scene;
							}
						}
					}
					else {
						$base_scene = 'default';
					}
					return $base_scene;
				}
				else {
					return $base_scene;
				}
				/*
				$grp_bot_id = $this->get_grp_bot_id($orm->bot_id);
				if ($grp_bot_id > 0) {
					if ($this->is_faq_scene($orm->bot_id, $scene_cd)) {
						$base_scene = $this->get_parent_faq_scene($orm->bot_id, $scene_cd);
						$path = $this->get_scene_path($base_scene, $func_type, $bot_theme_cd);
						if(!file_exists($path . 'config.json')) {
							$base_scene = $this->get_bot_setting($grp_bot_id, 'default_scene_cd');
						}
					}
					else {
						$base_scene = $this->get_bot_setting($grp_bot_id, 'default_scene_cd');
					}
				}
				else {
					$base_scene = $this->get_bot_setting($orm->bot_id, 'default_scene_cd');
				}
				$path = $this->get_scene_path($base_scene, $func_type, $bot_theme_cd);
				if(file_exists($path . 'config.json')) {
					return $base_scene;
				}
				else {
					return 'default';
				}
				*/
			}
		}
		else {
			return $this->get_scene_ref($orm->ref_scene_cd, $func_type, $bot_theme_cd, $template_cd);
		}
	}
	
	function get_scene_bot_theme_cd($scene_cd) {
		$bot_theme_cd = 'A01';
		$orm = ORM::factory('botscene')->where('scene_name', '=', $scene_cd)->find();
		if (isset($orm->scene_name)) {
			$scene_data = json_decode($orm->scene_data, true);
			if (array_key_exists('bot_theme_cd', $scene_data)) $bot_theme_cd = $scene_data['bot_theme_cd'];
		}
		return $bot_theme_cd;
	}
	
	function get_scene_image_url($scene_cd, $func_type, $bot_theme_cd, $template_cd, $filename) {
		$scene_path = $this->get_scene_path($scene_cd, $func_type, $bot_theme_cd, $template_cd);
		$scene_url_path = $this->get_scene_url_path($scene_cd, $func_type, $bot_theme_cd, $template_cd);
		$ext_array = $this->get_setting('support_image_type');
		foreach($ext_array as $ext) {
			if (file_exists($scene_path . $filename . '.' . $ext) == true) return $scene_url_path . $filename . '.' . $ext;
		}
		return '';
	}
	
	function get_scene_image_name($scene_cd, $func_type, $bot_theme_cd, $template_cd, $filename) {
		$scene_path = $this->get_scene_path($scene_cd, $func_type, $bot_theme_cd, $template_cd);
		$ext_array = $this->get_setting('support_image_type');
		foreach($ext_array as $ext) {
			if (file_exists($scene_path . $filename . '.' . $ext) == true) return $filename . '.' . $ext;
		}
		return '';
	}
	
	function delete_scene_image_file($scene_path, $filename, $file_ext) {
		$ext_array = $this->get_setting('support_image_type');
		foreach($ext_array as $ext) {
			if ($file_ext != $ext && file_exists($scene_path . $filename. '.' . $ext) == true) unlink($scene_path . $filename . '.' . $ext);
		}
	}
	
	function get_scene_template($bot_id, $func_type, $theme_cd, $scene_cd='') {
		$sql = "SELECT template_cd, template_name FROM t_bot_scene_template
			WHERE bot_id=:bot_id AND func_type_cd=:func_type_cd AND theme_cd=:theme_cd";
		if ($scene_cd != '') $sql = $sql . ' AND scene_cd=:scene_cd';
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
				':scene_cd' => $scene_cd,
				':func_type_cd' => $func_type,
				':theme_cd' => $theme_cd,
		));
		$results = $query->execute()->as_array('template_cd', 'template_name');
		return $results;
	}
	
	function get_member_name($member)
	{
		if ($member->name != NULL) return $member->name;
		if ($member->first_name != NULL && $member->first_name != '') return $member->last_name . ' ' . $member->first_name;
		return '';
	}
	
	function get_member_name2($member)
	{
		if ($member['name'] != NULL) return $member['name'];
		if ($member['first_name'] != NULL && $member['first_name'] != '') return $member['last_name'] . ' ' . $member['first_name'];
		return '';
	}
	
	function get_max_member_no($bot_id) {
		$sql = "SELECT max(member_no) AS member_no FROM t_bot_member WHERE bot_id=:bot_id";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
		));
		$results = $query->execute()->as_array();
		if ($results[0]['member_no'] == null) {
			return 1;
		}
		else {
			return $results[0]['member_no'] + 1;
		}
	}
	
	function get_mstime() {
		$m = explode(" ", microtime(true))[0];
		$arr = explode(".", $m);
		if (count($arr) > 1) {
			return date("Y-m-d H:i:s") . "." . substr($arr[1], 0, 3);
		}
		else {
			return date("Y-m-d H:i:s") . "." . "000";
		}
	}
	
	function get_week($date, $lang_cd = 'ja') {
		$week = [
			'日', //0
			'月', //1
			'火', //2
			'水', //3
			'木', //4
			'金', //5
			'土', //6
		  ];
		$objDate = new DateTime($date);
		$w = $objDate->format('w');
		return $week[$w];
	}

	function create_ua_info()
	{
		//return null;
		try
		{
			$ua_info = array();
			$geo_model = new Model_Geoip;
			//$ip_address = '***********';
			$ip_address = $this->getRemoteIPAddress();

			$geo = $geo_model->getCity($ip_address);
			$ua = get_browser();
			
			$ua_info['ip_address'] = $ip_address;
			$ua_info['country'] = $geo->country->isoCode;
			$ua_info['city'] = $geo->city->name;
			$ua_info['timezone'] = DateTimeZone::listIdentifiers(DateTimeZone::PER_COUNTRY, $ua_info['country'])[0];
			$time = new DateTime('now', new DateTimeZone($ua_info['timezone']));
			$ua_info['timezone_offset'] = $time->format('P');
			$default_timezone = date_default_timezone_get();
			date_default_timezone_set($ua_info['timezone']);
			$ua_info['local_time'] = date('Y-m-d H:i:s');
			date_default_timezone_set($default_timezone);
			$ua_info['browser'] = $ua->browser;
			if ($ua->ismobiledevice == '1') {
				$ua_info['mobile'] = 1;
			}
			else {
				$ua_info['mobile'] = 0;
			}
			if (array_key_exists('HTTP_ACCEPT_LANGUAGE', $_SERVER)) {
				$ua_info['language'] = $_SERVER['HTTP_ACCEPT_LANGUAGE'];
				$ua_info['lang_cd'] = $this->get_userlang();
			}
			else {
				$ua_info['language'] = '';
				$ua_info['lang_cd'] = '';
			}
			return $ua_info;
		}
		catch (Exception $e)
		{
			return null;
		}
	}
	
	function get_refer() {
		$refer = '';
		if (array_key_exists('HTTP_REFERER', $_SERVER)) {
			$refer = $_SERVER['HTTP_REFERER'];
			if (strpos($refer, $this->get_env('admin_url')) === 0) {
				$refer = 'talkappi-admin';
			}
			else {
				$pos = strpos($refer, '/', 8);
				if ($pos > 0) $refer = substr($refer, 0, $pos);
			}
		}
		return $refer;
	}

	function curl($url, $header, $data, $raw=false)
	{
		Log::instance()->add(Log::DEBUG, "CURL DATA:" . json_encode($data, JSON_UNESCAPED_UNICODE));
		$curl = curl_init();
		curl_setopt($curl, CURLOPT_URL, $url);
		curl_setopt($curl, CURLOPT_CUSTOMREQUEST, 'POST');
		if ($raw) {
			curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
		}
		else {
			curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($data, JSON_UNESCAPED_UNICODE));
		}
		curl_setopt($curl, CURLOPT_HTTPHEADER, $header);
		curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
		curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($curl, CURLOPT_HEADER, true);
		$response = curl_exec($curl);
		$header_size = curl_getinfo($curl, CURLINFO_HEADER_SIZE);
		$body = substr($response, $header_size);
		curl_close($curl);
		Log::instance()->add(Log::DEBUG, "CURL RESULT:" . $body);
		return json_decode($body, true);
	}
	
	function curl_post($url, $header, $data, $raw=false)
	{
		Log::instance()->add(Log::DEBUG, "CURL DATA:" . json_encode($data, JSON_UNESCAPED_UNICODE));
		$curl = curl_init();
		curl_setopt($curl, CURLOPT_URL, $url);
		curl_setopt($curl, CURLOPT_CUSTOMREQUEST, 'POST');
		if ($raw) {
			curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
		}
		else {
			curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($data, JSON_UNESCAPED_UNICODE));
		}
		curl_setopt($curl, CURLOPT_HTTPHEADER, $header);
		curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
		curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($curl, CURLOPT_HEADER, true);
		$response = curl_exec($curl);
		$header_size = curl_getinfo($curl, CURLINFO_HEADER_SIZE);
		$body = substr($response, $header_size);
		curl_close($curl);
		Log::instance()->add(Log::DEBUG, "CURL RESULT:" . $body);
		return $body;
	}

	function curl_put($url, $header, $data, $raw=false)
	{
		Log::instance()->add(Log::DEBUG, "CURL DATA:" . json_encode($data, JSON_UNESCAPED_UNICODE));
		$curl = curl_init();
		curl_setopt($curl, CURLOPT_URL, $url);
		curl_setopt($curl, CURLOPT_CUSTOMREQUEST, 'PUT');
		if ($raw) {
			curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
		}
		else {
			curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($data, JSON_UNESCAPED_UNICODE));
		}
		curl_setopt($curl, CURLOPT_HTTPHEADER, $header);
		curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
		curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
		$response = curl_exec($curl);
		if (curl_errno($curl)) {
			Log::instance()->add(Log::ERROR, "CURL ERROR: " . curl_error($curl));
			throw new Exception(curl_error($curl));
		}
		$http_code = curl_getinfo($curl, CURLINFO_HTTP_CODE);
		$header_size = curl_getinfo($curl, CURLINFO_HEADER_SIZE);
		$body = substr($response, $header_size);
		curl_close($curl);
		Log::instance()->add(Log::DEBUG, "CURL RESULT:" . $body);
		if ($http_code >= 400) {
			throw new Exception("HTTP error code: " . $http_code . ". Response: " . $body);
		}
		if ($http_code === 204) {
			return '';
		}
		return $body;
	}
	
	function curl_get($url, $header=NULL)
	{
		$curl = curl_init();
		curl_setopt($curl, CURLOPT_URL, $url);
		if ($header == NULL) {
			curl_setopt($curl, CURLOPT_HEADER, 0);
		}
		else {
			curl_setopt($curl, CURLOPT_HTTPHEADER, $header);
		}
		curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
		$response = curl_exec($curl);
		curl_close($curl);
		Log::instance()->add(Log::DEBUG, "CURL_GET RESULT:" . $response);
		return $response;
	}
	
	function curl_delete($url, $header=NULL)
	{
		$curl = curl_init();
		curl_setopt($curl, CURLOPT_URL, $url);
		curl_setopt($curl, CURLOPT_CUSTOMREQUEST, 'DELETE');
		if ($header == NULL) {
			curl_setopt($curl, CURLOPT_HEADER, 0);
		}
		else {
			curl_setopt($curl, CURLOPT_HTTPHEADER, $header);
		}
		curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
		$response = curl_exec($curl);
		curl_close($curl);
		Log::instance()->add(Log::DEBUG, "CURL_DELETE RESULT:" . $response);
		return $response;
	}

	function call_api_model($api_name, $params) {
		$base_url= $this->get_env('api_url') . $api_name;
		$param_str = '';
		foreach($params as $k=>$v) {
			$param_str = $param_str . $k . '=' . $v . '&';
		}
		$response = $this->curl_get($base_url . '?' . $param_str);
		return json_decode($response, true);
	}
	
	function call_admin_api($api_name, $action, $method, $params) 
	{
		$admin_service_url = $this->get_env('admin_service_url');
		$api_url = $admin_service_url . "$api_name/$action";
		$method = strtoupper($method);
		$params['admin_lang_cd'] = $this->_admin_lang_cd;
		if ($method == 'GET') {
			array_walk($params, function(&$value, $key){
				if (is_array($value)) $value = json_encode($value, JSON_UNESCAPED_UNICODE);
				$value = $key . '=' . $value;
			});
			$api_url .= '?' . implode('&', $params);
			Log::instance()->add(Log::DEBUG, "GET URL:" . $api_url);
			$response = file_get_contents($api_url);
			Log::instance()->add(Log::DEBUG, "GET DATA:" . $response);
			$result = json_decode($response, true);
		}
		else if ($method == 'POST') {
			$header = [
				'Content-Type: application/json; charset=UTF-8',
			];
			Log::instance()->add(Log::DEBUG, "POST URL:" . $api_url);
			Log::instance()->add(Log::DEBUG, "POST DATA:" . json_encode($params, JSON_UNESCAPED_UNICODE));
			Log::instance()->add(Log::DEBUG, "POST HEADER:" . json_encode($header, JSON_UNESCAPED_UNICODE));
			$result = $this->curl($api_url, $header, $params);

		}
		else {
			throw new Exception('call_admin_api_no_method');
		}
		if (!is_array($result)) throw new Exception('call_admin_api_exception');
		if ($result['result'] != 'success') throw new Exception($result['message']);
		return $result['data'];
	}

	function parse_enginehook_result($data, &$message)
	{
		if ($data['success'] == 'True') {
			return true;
		}
		else {
			if ($data['ErrorInfo']['error_type'] == 'tllincoln') {
				$messages = json_decode($data['ErrorInfo']['error_message'], true);
				$message = 'E|' . $messages[0]['$']['ShortText'];
			}
			else {
				$message = 'E|' . $data['ErrorInfo']['error_message'];
			}
			return false;
		}
	}
	
	function post_enginehook($api_name, $action, $token, $data)
	{
		$base_url= $this->get_env('engine_url') . "components/" . $api_name;
		$data = [
				'action' => $action,
				'timestamp' => time(),
				'token' => $token,
				'data' => $data,
				];
		$header = [
				'Content-Type: application/json; charset=UTF-8',
		];
		Log::instance()->add(Log::DEBUG, "CURL DATA:" . json_encode($data, JSON_UNESCAPED_UNICODE));
		$curl = curl_init();
		curl_setopt($curl, CURLOPT_URL, $base_url);
		curl_setopt($curl, CURLOPT_CUSTOMREQUEST, 'POST'); // post
		curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($data));
		curl_setopt($curl, CURLOPT_HTTPHEADER, $header); // リクエストにヘッダーを含める
		curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
		curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($curl, CURLOPT_HEADER, true);
		$response = curl_exec($curl);
		$header_size = curl_getinfo($curl, CURLINFO_HEADER_SIZE);
		$body = substr($response, $header_size);
		Log::instance()->add(Log::DEBUG, "CURL RESULT:" . $body);
		return json_decode($body, true);
	}
	
	function post_shorten_url($url_long)
	{
		$base_url= $this->get_env('shorten_api_url');
		$data = [
				'url_long' => $url_long,
				'cdn_prefix' => 's.talkappi.com',
				];
		Log::instance()->add(Log::DEBUG, "CURL DATA:" . json_encode($data, JSON_UNESCAPED_UNICODE));
		$header = [
				'Content-Type: application/json; charset=UTF-8',
		];
		$curl = curl_init();
		curl_setopt($curl, CURLOPT_URL, $base_url);
		curl_setopt($curl, CURLOPT_CUSTOMREQUEST, 'POST');
		curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($data));
		curl_setopt($curl, CURLOPT_HTTPHEADER, $header);
		curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
		curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($curl, CURLOPT_HEADER, true);
		$response = curl_exec($curl);
		$header_size = curl_getinfo($curl, CURLINFO_HEADER_SIZE);
		$body = substr($response, $header_size);
		Log::instance()->add(Log::DEBUG, "CURL RESULT:" . $body);
		return json_decode($body, true)["url_short"];
	}
	
	function post_redmine($bot_id, $link_type_cd, $link_id, $params, $project = '', $external = false)
	{
		// redmine url apikey..
		$redmine_setting = $this->get_env('redmine');
		
		if ($project == '') {
			$config = $this->get_env('redmine');
		}
		else {
			$config = $this->get_setting('redmine_' . $project);
		}
		
		if ($external == true) {
			$bot_config = json_decode($this->get_bot_setting($bot_id, 'json_redmine'), true);
			if ($bot_config != null) {
				if (array_key_exists('project_id', $bot_config)) {
					$config['project_id'] = $bot_config['project_id'];
				}
				if (array_key_exists($project . 'assigned_to_id', $bot_config)) {
					$config['assigned_to_id'] = $bot_config['assigned_to_id'];
				}
				if (array_key_exists($project . '_assigned_to_id', $bot_config)) {
					$config['assigned_to_id'] = $bot_config[$project . '_assigned_to_id'];
				}
			}
			else {
				return;
			}
		}
		else {
			$bot_config = json_decode($this->get_bot_setting($bot_id, 'json_redmine'), true);
			if ($bot_config != null) {
				if (array_key_exists($project . 'assigned_to_id', $bot_config)) {
					$config['assigned_to_id'] = $bot_config['assigned_to_id'];
				}
				if (array_key_exists($project . '_assigned_to_id', $bot_config)) {
					$config['assigned_to_id'] = $bot_config[$project . '_assigned_to_id'];
				}
			}
		}
		
		if ($config['project_id'] == '') return;
		$config['subject'] = $params['subject'];
		$config['description'] = $params['description'];
		$config['status'] = 1;     // #ステータス
		//$config['tracker_id'] = 8;  // #トラッカー
		if (isset($params['priority_id'])) $config['priority_id'] = intval($params['priority_id']);  // #優先度
		//$config['assigned_to_id'] = 1;  // #担当者のID
		//$config['watcher_user_ids'] = [1];  // #ウォッチするユーザのID
		//$config['parent_issue_id'] = 1;  // #親チケットのID
		//$config['start_date'] = '';  //#開始日
		//$config['due_date'] = '';  //#期日
		//$config['estimated_hours'] = 1;  //# 予想工数
		//$config['done_ratio'] = 1;  //
		//$config['custom_fields'] = [{'id': 1, 'value': 'foo'}];  //
		//$config['uploads'] = [{'path': 'C:\\dev\\python3\\redmine\\test.txt'}];  //
		$data = ['key'=>$redmine_setting['api_key'], 'issue'=>$config];
		$header = [
				'Content-Type: application/json; charset=UTF-8',
		];
		$result = $this->curl($redmine_setting['api_url'], $header, $data);
		if ($result == null || !array_key_exists('issue', $result)) {
			$this->log_error(__FUNCTION__, json_encode($result, JSON_UNESCAPED_UNICODE), $bot_id);
			return null;
		}
		$orm = ORM::factory('redmine');
		$orm->bot_id = $bot_id;
		$orm->ticket_id = $result['issue']['id'];
		$orm->link_class_cd = '01';
		$orm->link_type_cd = $link_type_cd;
		$orm->link_id = $link_id;
		$orm->title = $params['subject'];
		$orm->description = mb_substr($params['description'], 0, 1000);
		$orm->req_user = 0;
		$orm->upd_user = $this->get_user_id();
		$orm->upd_time = date('Y-m-d H:i:s');
		$orm->save();
		return $result['issue']['id'];
	}
	
	function post_kintone($bot_id, $link_type_cd, $link_id, $record)
	{
		$kintone = $this->get_bot_setting($bot_id, 'json_kintone');
		$kintone = json_decode($kintone, true);
		$header = [
				'X-Cybozu-API-Token:' . $kintone['api_token'],
				'Content-Type: application/json',
		];
		$data = [
				'app'=>$kintone['app_id'],
				'record'=>$record
		];
		$result = $this->curl('https://' . $kintone['domain'] . '/k/v1/record.json', $header, $data);
		if ($result == null || !array_key_exists('id', $result)) {
			$this->log_error(__FUNCTION__, json_encode($result, JSON_UNESCAPED_UNICODE), $bot_id);
			return null;
		}
		$orm = ORM::factory('redmine');
		$orm->bot_id = $bot_id;
		$orm->ticket_id = $result['id'];
		$orm->link_class_cd = '03';
		$orm->link_type_cd = $link_type_cd;
		$orm->link_id = $link_id;
		$orm->title = $record['subject']['value'];
		$orm->description = substr($record['text']['value'], 0, 1000);
		$orm->upd_user = $this->get_user_id();
		$orm->upd_time = date('Y-m-d H:i:s');
		$orm->save();
		return $result['id'];
	}
	
	function get_user_id() {
		if (php_sapi_name() == 'cli') {
			return 0;
		}
		else {
			$user = Session::instance()->get('user', NULL);
			return $user->user_id;
		}
	}
	
	function obj2array($obj) {
		return json_decode(json_encode($obj), true);
	}
	
	function result2array($result) {
		if ($result == null) return [];
		$result = $result->as_array();
 		$arr = [];
		foreach($result as $r) {
			$arr[] = $r->as_array();
		}
		return $arr;
	}
	
	function diff_orm($src, $obj) {
		return $this->diff_array($src->as_array(), $obj->as_array());
	}
	
	function diff_array($src, $obj) {
		$ret = [];
		foreach($obj as $k=>$v) {
			if ($v != $src[$k]) {
				$ret[] = [$k=>"変更前：" . $src[$k] . ' 変更後：' . $v];
			}
		}
		return $ret;
	}
	
	function unicode_decode($name){
		$json = '{"str":"'.$name.'"}';
		$arr = json_decode($json,true);
		if(empty($arr)) return '';
		return $arr['str'];
	}
	
	function format_url($message) {
		/*
		 $message = str_replace("\n","<br />", $message);
		 $reg = '/\b(?:(?:https?|ftp):\/\/|www\.)[-a-z0-9+&@#\/%?=~_|!:,.;]*[-a-z0-9+&@#\/%=~_|]/i';
		 $reg = '/(https?|ftp)(:\/\/[-_.!~*\'()a-zA-Z0-9;\/?:\@&=+\$,%#]+)/';
		 //$reg = '@(http(s)?://)?(([a-zA-Z])([-\w]+\.)+([^\s\.]+[^\s]*)+[^,.\s])@';
		 $offset = 0;
		 $matchs = null;
		 while(true) {
		 $matchs = null;
		 if (preg_match($reg, $message, $matchs, PREG_OFFSET_CAPTURE, $offset) === false) break;
		 $match = $matchs[0][0];
		 $message = substr($message, $offset, $match[0][1]) . '<a style="word-break:break-all;color:slategray;" href="' . $match . '" rel="noopener noreferrer" target="_blank">' . $match . '</a>' . substr($message, strlen($match) + $offset);
		 $offset = $offset + strlen('<a style="word-break:break-all;color:slategray;" href="' . $match . '" rel="noopener noreferrer" target="_blank">' . $match . '</a>');
		 }
		 /*
		 preg_match_all($reg, $message, $matchs, PREG_OFFSET_CAPTURE);
		 foreach($matchs[0] as $m) {
		 $match = $m[0];
		 $from = '/'.preg_quote($match, '/').'/';
		 $message = preg_replace($from, '<a style="word-break:break-all;color:slategray;" href="' . $match. '" rel="noopener noreferrer" target="_blank">' . $match. '</a>', $message, 1);
		 }
		 */
		/*
		 $message = nl2br($message);
		 echo($message);
		 */
		/*
		$message = str_replace("\n","<br />", $message);
		$message = nl2br($message);
		$arr = explode('<br />', $message);
		*/
		$arr = explode(PHP_EOL, $message);
		$result = '';
		foreach($arr as $message) {
			$matches = [];
			//$message = preg_replace("/(<a.*>.*<\/a>)/Ue", 'substr("$1", 0, strlen("$1") - 4) . "<img class=\"external-link\" src=\"/assets/common/images/external-link.svg\"/>" . substr("$1", strlen("$1") - 4)', $message);
			$message = preg_replace_callback("/(<a.*>.*<\/a>)/U",
					function($m) {return substr($m[0], 0, strlen($m[0]) - 4) . "<img class=\"external-link\" src=\"/assets/common/images/external-link.svg\"/>" . substr($m[0], strlen($m[0]) - 4);},
					$message);
			preg_match_all("/<a.*>(.*)<\/a>/U", $message, $matches);
			$message_arr = [];
			$start_pos = 0;
			foreach($matches[0] as $m) {
				$pos = strpos($message, $m);
				if ($pos>=0) {
					$message_arr[] = [substr($message, $start_pos, $pos - $start_pos), 0];
					$start_pos = $pos + strlen($m);
					$message_arr[] = [$m, 1];
				}
			}
			$message_arr[] = [substr($message, $start_pos), 0];
			$message = '';
			foreach($message_arr as $me) {
				if ($me[1] == 0) {
					$message = $message . preg_replace('"\b(https?://\S+)"', '<a style="word-break:break-all;color:#045ab1;" href="$1" rel="noopener noreferrer" target="_blank">$1</a>', $me[0]);
				}
				else {
					$message = $message . $me[0];
				}
			}
			$result = $result . $message . '<br />';
		}
		return $result;
	}
	
	function check_url_validate($url){
		try {
			$ip_long = array(
					array('607649792', '608174079'), //*********-*************
					array('1038614528', '1039007743'), //**********-**************
					array('1783627776', '1784676351'), //**********-**************
					array('2035023872', '2035154943'), //**********-**************
					array('2078801920', '2079064063'), //***********-***************
					array('-1950089216', '-1948778497'), //***********-***************
					array('-1425539072', '-1425014785'), //*********-**************
					array('-1236271104', '-1235419137'), //**********-**************
					array('-770113536', '-768606209'), //**********-**************
					array('-569376768', '-564133889'), //**********-**************
			);
			$rand_key = mt_rand(0, 9);
			$ip= long2ip(mt_rand($ip_long[$rand_key][0], $ip_long[$rand_key][1]));
			$header = array("Connection: Keep-Alive","Accept: text/html, application/xhtml+xml, */*", "Pragma: no-cache", "Accept-Language: zh-Hans-CN,zh-Hans;q=0.8,en-US;q=0.5,en;q=0.3","User-Agent: Mozilla/5.0 (compatible; MSIE 10.0; Windows NT 6.2; WOW64; Trident/6.0)",'CLIENT-IP:'.$ip,'X-FORWARDED-FOR:'.$ip);
			$handle = curl_init($url);
			curl_setopt($handle, CURLOPT_RETURNTRANSFER, TRUE);
			curl_setopt($handle, CURLOPT_CONNECTTIMEOUT, 10);
			curl_setopt($handle, CURLOPT_HTTPHEADER, $header);
			curl_exec($handle);
			$httpCode = curl_getinfo($handle, CURLINFO_HTTP_CODE);
			curl_close($handle);
			unset($handle);
			$httpCode = floor($httpCode / 100);
			if($httpCode == 0 || $httpCode == 4 || $httpCode == 5) {
				return false;
			}else{
				return true;
			}
		}catch (Exception $e) {
			return false;
		}
	}
	
	function check_url_validate1($url) {
		try {
			$array = get_headers($url,1);
			if(preg_match('/200/',$array[0])){
				return true;
			}else{
				return false;
			}
		}
		catch(Exception $e) {
			return false;
		}
	}
	
	function mask_privacy_info($message) {
		$message = preg_replace("/[a-zA-Z0-9.!\#$%&'*+\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*/", '********', $message);
		$message = preg_replace('/[0-9]{2,4}-[0-9]{2,4}-[0-9]{3,4}/', '********', $message);
		$message = preg_replace('/[0-9]{10,}/', '********', $message);
		return $message;
	}
	
	function set_token_data($member_id, $token_data, $valid) {
		$orm = ORM::factory('tokenlog');
		$orm->token = md5(uniqid());
		$orm->member_id = $member_id;
		$orm->data = json_encode($token_data, JSON_UNESCAPED_UNICODE);
		$orm->valid_time = date('Y-m-d H:i:s',strtotime('+' . $valid));
		$orm->save();
		return $orm->token;
	}
	
	function get_token_data($token, $valid = true) {
		$link = ORM::factory('tokenlog')->where('token', '=', $token)->find();
		if (!isset($link->id)) {
			return 'NOT_EXIST';
		}
		if ($link->valid_flg == 0) {
			return 'INVALIDATE';
		}
		if ($link->valid_time != null && $link->valid_time < date('Y-m-d H:i:s')) {
			return 'EXPIRED';
		}

		$orm = ORM::factory('tokenlog', $link->id);
		if ($valid == false) $link->valid_flg = 0;
		$orm->access_time = date('Y-m-d H:i:s');
		$orm->save();

		$token_data = json_decode($link->data, true);
		$token_data['token'] = $token;
		return $token_data;
	}
	
	function db_token_data($token) {
		$link = ORM::factory('tokenlog')->where('token', '=', $token)->find();
		if (!isset($link->id)) {
			return 'NOT_EXIST';
		}
		return json_decode($link->data, true);
	}
	
	function db_upd_token_data($token, $token_data) {
		$link = ORM::factory('tokenlog')->where('token', '=', $token)->find();
		if (!isset($link->id)) {
			return false;
		}
		else {
			$orm = ORM::factory('tokenlog', $link->id);
			$orm->data = json_encode($token_data, JSON_UNESCAPED_UNICODE);
			$orm->save();
			return true;
		}
	}
	
	function get_link_param($link_id, $vaild = false) {
		/*
		$link =  ORM::factory('link')
			->where('link_id', '=', $link_id)
			->where('valid_flg', '=', 0)
			->where('valid_date_time', '>', date('Y-m-d H:i:s'))
			->find();
		if (!isset($link->link_id)) {
			return null;
		}
		*/
		$link = ORM::factory('link', $link_id);
		if (!isset($link->link_id)) {
			return 'NOT_EXIST';
		}
		if ($link->valid_flg == 1) {
			return 'INVALIDATE';
		}
		if ($link->valid_date_time != null && $link->valid_date_time < date('Y-m-d H:i:s')) {
			return 'EXPIRED';
		}
		if ($vaild == false) {
			//$link = ORM::factory('link', $link_id);
			$link->valid_flg = 1;
			$link->access_time = date('Y-m-d H:i:s');
			$link->save();
		}
		return json_decode($link->param1, true);
	}
	
	function invalid_link($link_id) {
		$link = ORM::factory('link', $link_id);
		$link->valid_flg = 1;
		$link->access_time = date('Y-m-d H:i:s');
		$link->save();
	}
	
	function set_link_param($type, $param, $valid_time = null)
	{
		if ($valid_time == null) {
			$valid_time = $this->get_setting('link_valid_' . $type);
		}
		$link = ORM::factory('link');
		$link->link_id = md5(time() . mt_rand(0, 1000));
		$link->link_type_cd = $type;
		$link->param1 = json_encode($param);
		if ($valid_time != null) $link->valid_date_time = date('Y-m-d H:i:s', strtotime('+' . $valid_time));
		$link->valid_flg = 0;
		$link->save();
		return $link->link_id;
	}
	
	function distance($lat1, $lon1, $lat2, $lon2, $radius = 6378.137)
	{
		$rad = floatval(M_PI / 180.0);
		
		$lat1 = floatval($lat1) * $rad;
		$lon1 = floatval($lon1) * $rad;
		$lat2 = floatval($lat2) * $rad;
		$lon2 = floatval($lon2) * $rad;
		
		$theta = $lon2 - $lon1;
		
		$dist = acos(sin($lat1) * sin($lat2) + cos($lat1) * cos($lat2) * cos($theta));
		
		if ($dist < 0 ) {
			$dist += M_PI;
		}
		return $dist = $dist * $radius;
	}
	
	public function get_userlang()
	{
		$lang_cd = 'ja';
		if (!array_key_exists('HTTP_ACCEPT_LANGUAGE', $_SERVER)) {
			return $lang_cd;
		}
		$lang = substr($_SERVER['HTTP_ACCEPT_LANGUAGE'], 0, 4);
		if (preg_match("/zh-c/i", $lang)) {
			$lang_cd= 'cn';
		}
		else if (preg_match("/zh/i", $lang)) {
			$lang_cd= 'tw';
		}
		else if (preg_match("/ja/i", $lang)) {
			$lang_cd= 'ja';
		}
		else if (preg_match("/ko/i", $lang)) {
			$lang_cd= 'kr';
		}
		else if (preg_match("/th/i", $lang)) {
			$lang_cd= 'th';
		}
		else if (preg_match("/ne/i", $lang)) {
			$lang_cd= 'ne';
		}
		else if (preg_match("/vi/i", $lang)) {
			$lang_cd= 'vi';
		}
		else if (preg_match("/it/i", $lang)) {
			$lang_cd= 'it';
		}
		else if (preg_match("/es/i", $lang)) {
			$lang_cd= 'es';
		}
		else if (preg_match("/de/i", $lang)) {
			$lang_cd= 'de';
		}
		else if (preg_match("/fr/i", $lang)) {
			$lang_cd= 'fr';
		}
		else if (preg_match("/pt/i", $lang)) {
			$lang_cd= 'pt';
		}
		else if (preg_match("/ru/i", $lang)) {
			$lang_cd= 'ru';
		}
		else if (preg_match("/id/i", $lang)) {
			$lang_cd= 'id';
		}
		else {
			$lang_cd= 'en';
		}
		return $lang_cd;
	}
	
	public function get_translate_url($url, $sl, $tl) 
	{
		if ($tl == 'cn') {
			$url= "https://translate.google.co.jp/translate?sl=$sl&tl=zh-CN&u=" . urlencode($url);
		}
		else if ($tl == 'tw') {
			$url= "https://translate.google.co.jp/translate?sl=$sl&tl=zh-TW&u=" . urlencode($url);
		}
		else if ($tl == 'en') {
			$url= "https://translate.google.co.jp/translate?sl=$sl&tl=en-US&u=" . urlencode($url);
		}
		else if ($tl == 'kr') {
			$url= "https://translate.google.co.jp/translate?sl=$sl&tl=ko-KR&u=" . urlencode($url);
		}
		return $url;
	}
	
	public function get_button_name($button_name, $button_config) {
		if (strpos($button_name, "BTN_") === 0 && array_key_exists($button_name, $button_config)) {
			return $button_config[$button_name];
		}
		else {
			return $button_name;
		}
	}
	
	public function trans_skill($skill, $lang_cd, $bot_id, $member_id) {
		if (strpos($skill, "http") === 0) {
			return ["type"=>"uri", "uri"=>$skill];
		}
		else {
			$skill_obj = json_decode($skill, true);
			if ($skill_obj != null && is_array($skill_obj)) {
				foreach($skill_obj as $k) {
					if ($k['skill'] == 'URL_TRANSITION') {
						$params = $k['params'];
						$url = $params['url'];
						if (array_key_exists('translate', $params) && $params['translate'] == 1) {
							$url = $this->get_translate_url($url, $params['lang'], $lang_cd);
						}
						if (array_key_exists('ref_div', $params) && array_key_exists('ref', $params) || array_key_exists('skill', $params)) {
							$base_url = $this->get_env('base_url');
							$url = $base_url . 'bot/ref?sns=wb&botid=' . $bot_id . '&lang=' . $lang_cd . '&member_id=' . $member_id . '&url=' . urlencode($url);
							if (array_key_exists('ref_div', $params) && array_key_exists('ref', $params)) {
								$url = $url . '&ref=' . urlencode($params['ref']) . '&div=' . $params['ref_div'];
							}
							if (array_key_exists('skill', $params)) {
								if (is_array($params['skill'])) {
									$url = $url . '&skill=' . json_encode($params['skill']);
								}
								else {
									$url = $url . '&skill=' . $params['skill'];
								}
							}
						}
						return ["type"=>"uri", "uri"=>$url];
					}
				}
			}
			return ["type"=>"postback", "uri"=>$skill];
		}
	}
	
	function create_follow($post, $sns_id) {
		if (isset($post['refer']) && $post['refer'] == 'talkappi-admin') return;
		try {
			$follow = ORM::factory('botfollow');
			$follow->follow_time = date('Y-m-d H:i:s');
			$follow->sns_type_cd = $post['channel'];
			$follow->sns_id = $sns_id;
			$follow->member_id = $post['id'];
			$follow->bot_id = $post['bot_id'];
			$follow->scene = $post['facility_cd'];
			$follow->lang_cd = $post['lang_cd'];

			$ua_info = $this->create_ua_info();
			if ($ua_info != null) { 
				$follow->ip_address = $ua_info['ip_address'];
				$follow->country = $ua_info['country'];
				$follow->timezone = $ua_info['timezone'];
				$follow->local_time = $ua_info['local_time'];
				$follow->browser = $ua_info['browser'];
				$follow->mobile = $ua_info['mobile'];
				$follow->language = $ua_info['language'];
			}
			if (isset($post["extra_info"])) {
				// line
				$follow->extra_info = $post["extra_info"];
			}
			if (isset($post["refer"])) {
				$follow->refer = $post["refer"];
			}
			$follow->save();
		}
		catch(Exception $e) {

		}
	}

	function get_tl_setting($bot_id, $link_key='') {
		$tl = $this->get_bot_setting($bot_id, "json_reserve_settings");
		$tl_data = json_decode($tl);
		if (is_array($tl_data)) {
			foreach($tl_data as $tl) {
				if (isset($tl->key)) {
					if ($tl->key == $link_key) return $this->obj2array($tl);
				}
				else {
					if ($link_key == '') return $this->obj2array($tl);
				}
			}
		}
		else {
			return $this->obj2array($tl_data);
		}
		return null;
	}

	function get_redirect_url($url){
		$header = get_headers($url, 1);
		if (strpos($header[0], '301') !== false || strpos($header[0], '302') !== false) {
			if(is_array($header['Location'])) {
				return $header['Location'][count($header['Location']) - 1];
			} else {
				return $header['Location'];
			}
		} 
		else {
			return $url;
		}
	}
	
	function check_authority($uri_action) {
		$user = Session::instance()->get('user', NULL);
		if ($user->role_cd == '99') return true;
		$user_function = Session::instance()->get('user_function', NULL);
		if ($user_function == NULL) return false;
		$has_authority = false;
		foreach($user_function as $f) {
			if (substr($f, -1, 1) == "*") {
				if (strpos($uri_action, substr($f, 0, strlen($f) - 1)) === 0) {
					$has_authority = true;
					break;
				}
			}
			else {
				if ($uri_action == $f) {
					$has_authority = true;
					break;
				}
			}
		}
		return $has_authority;
	}

	function tab_menu_authority($path, $action, $param, $menu_path, $menu_action, $title) {
		$full_action = $menu_path . '/' . $menu_action;
		if ($this->check_authority($full_action)) {
			if ($full_action == $path . '/' . $action) {
				$active = 'active';
			}
			else {
				$active = '';
			}
			return '<li class="' . $active . '"><a href="/' . $full_action . '?' . $param . '">' . $title . '</a></li>';
		}
		else {
			return '';
		}
	}
	
	public function verify_password($password, $encrypt_password) {
		//if ($u->password == md5($password) || $u->password == substr(hash('sha3-512', $password), 0, 32)) $user = $u;
		$encrypt_key = $this->get_env('encrypt_key');
		if ($encrypt_key != null) {
			if (substr(crypt($password, '$1$' . $encrypt_key),0,32) == $encrypt_password) return true;
		}
		if ($encrypt_password == md5($password)) return true;
		return false;
	}
	
	public function encrypt_password($password) {
		//if ($post['password'] != '') $orm->password = substr(hash('sha3-512', $post['password']), 0, 32);
		$encrypt_key = $this->get_env('encrypt_key');
		if ($encrypt_key == null) {
			return md5($password);
		}
		else {
			return substr(crypt($password, '$1$' . $encrypt_key), 0, 32);
		}
	}
	
	public function uid() {
		$chars = md5(uniqid(mt_rand(), true));
		$uuid  = substr($chars,0,8) . '-';
		$uuid .= substr($chars,8,4) . '-';
		$uuid .= substr($chars,12,4) . '-';
		$uuid .= substr($chars,16,4) . '-';
		$uuid .= substr($chars,20,12);
		return $uuid;
	}
	
	public function getRemoteIPAddress() {
		if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
			return $_SERVER['HTTP_CLIENT_IP'];
		} 
		else if (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
			$ips = explode(',', $_SERVER['HTTP_X_FORWARDED_FOR']);
			$regex = '/^(([1-9]?[0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5]).){3}([1-9]?[0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$/';
			foreach($ips as $ip) {
				if (preg_match($regex, trim($ip))) {
					return trim($ip);
				}
			}
		}
		return $_SERVER['REMOTE_ADDR'];
	}
	
	public function create_url_params($post) {
		$param = '';
		foreach($post as $k=>$v) {
			$param = $param . $k . '=' . $v . '&';
		}
		$param = substr($param, 0, strlen($param) - 1);
		return $param;
	}

	public function post_size_check() 
	{
		$post_max_size = ini_get('post_max_size');
		$unit = substr($post_max_size,-1);
		$post_max_size = intval(str_replace($unit, '', $post_max_size));
		switch ($unit)
		{
			case 'G':
				$post_max_size= $post_max_size * 1024 * 1024 * 1024;
			case 'M':
				$post_max_size= $post_max_size * 1024 * 1024;
			case 'K':
				$post_max_size= $post_max_size * 1024;
		}
		return ($_SERVER['CONTENT_LENGTH'] >= $post_max_size);
	}
	
	public function get_file_ext($file_url)
	{
		$arr = explode('.', $file_url);
		return end($arr);
	}
	
	public function delete_files($path) {
		if(is_dir($path)){
			$p = scandir($path);
			foreach($p as $val){
				if($val !="." && $val !=".."){
					if(is_dir($path.$val)){
						continue;
					}else{
						unlink($path.$val);
					}
				}
			}
		}
	}
	
	public function delete_dir_files($path) {
		if(is_dir($path)){
			$p = scandir($path);
			foreach($p as $val){
				if($val !="." && $val !=".."){
					if(is_dir($path.$val)){
						$this->delete_dir_files($path.$val.'/');
						@rmdir($path.$val.'/');
					}else{
						unlink($path.$val);
					}
				}
			}
		}
	}

	public function format_csv_field_view($field) {
		return '="' . $field . '"';
	}
	
	public function format_csv_field($field) {
		if ($field === NULL || $field == '') return '';
		if (is_numeric($field) && substr($field, 0, 1) == '0') {
			return '="' . $field . '"';
		}
		if (preg_match('/^\d{1,9}-\d{1,9}-\d{1,9}-\d{1,9}$/', $field)) {
			return '="' . $field . '"';
		}
		if (preg_match('/^\d{1,9}-\d{1,9}-\d{1,9}$/', $field)) {
			return '="' . $field . '"';
		}
		if (preg_match('/^\d{1,9}-\d{1,9}$/', $field)) {
			return '="' . $field . '"';
		}
		return $field;
	}

	public function translate($content, $lang_cd, $lang_cd_src = null, &$cache = null) {
		if ($content == '') return '';

		// If cache is provided, check for cached translation
		if ($cache !== null) {
			$cache_key = md5($content . '|' . $lang_cd . '|' . $lang_cd_src);
			if (isset($cache[$cache_key])) {
				return $cache[$cache_key];
			}
		}

		$params = ['sentence' => $content, 'lang_cd_target' => $lang_cd];
		if ($lang_cd_src) {
			$params['lang_cd_src'] = $lang_cd_src;
		}
		$data = $this->post_enginehook('service', 'translate','', $params);
		if ($data == null || $data['success'] == 'False') return False;

		$translation = $data['sentence_translated'];
    
    	// Store in cache if cache is provided
		if ($cache !== null) {
			$cache_key = md5($content . '|' . $lang_cd . '|' . $lang_cd_src);
			$cache[$cache_key] = $translation;
		}
		
		return $translation;
	}

	public function get_holidays($lang_cd = 'ja') {
		$lines = file('https://holidays-jp.github.io/api/v1/date.json');
		$holiday = json_decode(implode('', $lines), true);
		return $holiday;
	}

	public function weekOfMonth($date) {
		//Get the first day of the month.
		$date = strtotime($date);
		$firstOfMonth = strtotime(date("Y-m-01", $date));
		//Apply above formula.
		return $this->weekOfYear($date) - $this->weekOfYear($firstOfMonth) + 1;
	}
	
	public function weekOfYear($date) {
		$weekOfYear = intval(date("W", $date));
		if (date('n', $date) == "1" && $weekOfYear > 51) {
			// It's the last week of the previos year.
			return 0;
		}
		else if (date('n', $date) == "12" && $weekOfYear == 1) {
			// It's the first week of the next year.
			return 53;
		}
		else {
			// It's a "normal" week.
			return $weekOfYear;
		}
	}

	public function encode_json_js($arr, $option = JSON_UNESCAPED_UNICODE) {
		return json_encode($arr, $option);
	}
	public function encode_json($arr, $option = JSON_UNESCAPED_UNICODE) {
		if (count($arr) == 0) return '';
		return json_encode($arr, $option);
	}
	public function decode_json($str) {
		return json_decode($str, true);
	}

	public function send_watch_mail($title, $body) {
		$param = ['bot_id'=>17, 'receiver'=>'<EMAIL>', 'title'=>$title, 'body'=>$body, 'lang_cd'=>'ja'];
		$data = $this->post_enginehook('service', 'sendmail','', $param);
		if ($data['success'] == 'False') {
			Log::instance()->add(Log::DEBUG, 'sendusermail failure=' . json_encode($data));
		}
	}

	private function _is_support_skill($skill_name) {
		$sql = "SELECT a.params FROM m_skill a WHERE skill=:skill_name AND lang_cd='ja'";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':skill_name' => $skill_name,
		));
		$skill = $query->execute()->as_array();
		return count($skill) > 0;
	}

	function create_receipt_link($bot_id, $service_type, $result_id) {
		$receipt_setting = $this->get_bot_setting_self($bot_id, 'json_receipt_setting', true);
		if ($receipt_setting == '') return '';
		$receipt_setting['service_type'] = $service_type;
		$receipt_setting['result_id'] = $result_id;
		if (!isset($receipt_setting['period'])) $receipt_setting['period'] = null;
		return $this->get_env('service_url') . 'report/receipt?id=' . $this->set_link_param('', $receipt_setting, $receipt_setting['period']);
	}

	function get_standard_lang_cd($lang_cd) {
		$code = $this->get_setting('language_iso_code');
		if (isset($code[$lang_cd])) return $code[$lang_cd];
		return $lang_cd;
	}

	function google_translate_url($url, $target_lang_cd, $source_lang_cd = 'auto') {
		$target_lang_cd = $this->get_standard_lang_cd($target_lang_cd);
		$source_lang_cd = $this->get_standard_lang_cd($source_lang_cd);
		return "https://translate.google.com/translate?sl=$source_lang_cd&tl=$target_lang_cd&u=$url";
	}

	public function decode_param($data, $keys)
	{
		foreach ($keys as $k) {
			if (!isset($data[$k])) return null;
			$data = $data[$k];
		}
		return $data;
	}

	public function create_base_url($service_type)
	{
		$base_url = $this->get_env($service_type . '_url');
		if ($base_url == '/' . $service_type) {
			return 'http://localhost/';
		}
		return $base_url;
	}
	public function create_service_url($service_type)
	{
		return $this->create_base_url($service_type) . $service_type . '/';
	}

	function get_option_service_start_date($bot_id, $option_cd)
	{
		// 利用開始日の取得
		$sql = "SELECT start_date 
				FROM t_facility_options 
				WHERE option_cd = :option_cd 
				AND contract_type = '01' 
				AND bot_id = :bot_id";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(':bot_id' => $bot_id, ':option_cd' => $option_cd));
		$results = $query->execute()->as_array();

		if (!empty($results)) {
			return $results[0]['start_date'];
		}
		return null;
	}
	function create_whitelist($url, &$domains, $div, $item_id, $user_id = 0) {
        if ($url == null) return;
		try {
			$domain = [];
			$skill = $this->parse_skill($url);
			if (is_array($skill) && isset($skill['skill']) && $skill['skill'] == 'URL_TRANSITION') {
				if (isset($skill['params']['url']) && $skill['params']['url'] != '') {
					try {
						$parsedUrl = parse_url(str_replace("\/", "/", trim($skill['params']['url'])));
						if (isset($parsedUrl['host'])) {
							$domain[] = $parsedUrl['host'];
						}
					}catch(Exception $e) {
					}catch(Throwable $th) {
					}
				}
				if (isset($skill['params']['url_sp']) && $skill['params']['url_sp'] != '') {
					try {
						$parsedUrl = parse_url(str_replace("\/", "/", trim($skill['params']['url_sp'])));
						if (isset($parsedUrl['host'])) {
							$domain[] = $parsedUrl['host'];
						}
					}catch(Exception $e) {
					}catch(Throwable $th) {
					}
				}
			}
			else {
				$url = trim($url);
				if (strpos($url, 'https:') === 0 || strpos($url, 'http:') === 0) { 
					$parsedUrl = parse_url(str_replace("\/", "/", $url));
					if (isset($parsedUrl['host'])) {
						$domain[] = $parsedUrl['host'];
					}
				}
			}
			if (count($domain) == 0) return;
			foreach($domain as $d) {
				if (!in_array($d, $domains)) {
					if ($user_id != 0) {
						$orm = ORM::factory('urlwhitelist')->where('url', '=', $d)->find_all();
						if (count($orm) > 0) {
							$domains[] = $d;
							return;
						}
					}
					$orm = ORM::factory('urlwhitelist');
					$orm->url = $d;
					$orm->upd_user = $user_id;
					$orm->create_from = json_encode(['div'=>$div, 'id'=>$item_id]);
					$orm->save();
					$domains[] = $d;
				}
			}
		}catch(Exception $e) {
		}catch(Throwable $th) {
		}
    }

	function check_table_exist($table_name) {
		$sql = "SHOW TABLES LIKE '" . $table_name . "'";
		$query = DB::query(Database::SELECT, $sql);
		$results = $query->execute()->as_array();
		return count($results) > 0;
	}
}

