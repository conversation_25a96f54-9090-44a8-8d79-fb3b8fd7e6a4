<?php defined('SYSPATH') OR die('No direct script access.');

class Model_Reservemodel extends Model_Surveymodel
{
	function get_max_corp_member_id($bot_id, $corp_id)
	{
		$sql = "SELECT MAX(corp_member_id) AS max_id FROM t_bot_corp_member WHERE bot_id=:bot_id AND corp_id=:corp_id";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
				':corp_id' => $corp_id,
		));
		$results = $query->execute()->as_array();
		if ($results[0]['max_id'] == NULL) {
			return sprintf("%08d", 1);
		}
		else {
			return sprintf("%08d", intval($results[0]['max_id']) + 1);
		}
	}

	// 取得法人情报
	function get_bot_corp($bot_id, $corp_id)
	{
		$sql = "SELECT *
		FROM t_bot_corp 
		WHERE 
		bot_id = :bot_id
		AND corp_id = :corp_id
		AND is_available = 1
		AND (contract_date_start IS NULL OR contract_date_start <= :cur_date)
		AND (contract_date_end IS NULL OR contract_date_end >= :cur_date)
		";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
				':corp_id' => $corp_id,
				':cur_date' => date('Y-m-d'),
			));

		$results = $query->execute();
		return $results->as_array();
	}

	// 法人ログイン認証
	function auth_bot_corp($bot_id, $corp_id, $corp_password)
	{
		$sql = "SELECT *
		FROM t_bot_corp 
		WHERE 
		bot_id = :bot_id
		AND corp_password = :corp_password
		AND corp_id = :corp_id
		AND is_available = 1
		AND (contract_date_start IS NULL OR contract_date_start <= :cur_date)
		AND (contract_date_end IS NULL OR contract_date_end >= :cur_date)
		";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
				':corp_id' => $corp_id,
				':corp_password' => $corp_password,
				':cur_date' => date('Y-m-d'),
			));

		$results = $query->execute();
		return $results->as_array();
	}

}

?>
