<?php defined('SYSPATH') OR die('No direct script access.');

class Model_Botmodel extends Model_Basemodel
{	
	function get_webchats_old($bot_id, $context_id, $member_id, $log_time, $limit=0, $member_msg_only=true)
	{
		$sql = "SELECT log.member_id, log.sns_type_cd, log.log_time, log.lang_cd, log.bot_id, log.scene_cd,
		log.member_msg, log.bot_msg, log.bot_msg_t, log.score, log.intent_cd, log.log_id, des.content AS bot_name
		FROM t_bot_log$this->_log_ext log LEFT JOIN t_bot_msg msg ON log.bot_id = msg.bot_id AND msg.msg_cd = 'bot_name'
		LEFT JOIN t_bot_msg_desc_txt des ON msg.msg_id = des.msg_id AND des.lang_cd = log.lang_cd AND des.no=1
		WHERE log.member_id = :member_id AND log.sns_type_cd='wb' AND (log.user_id IS NULL OR log.user_id > 0) 
		AND log.log_time > :log_time ";
		if ($context_id != '') $sql = $sql . " AND log.context_id = :context_id";
		$sql = $sql . " AND " . $this->_create_bot_cond_webchat($bot_id, 'log.bot_id');
		if ($member_msg_only) $sql = $sql . " AND log.member_msg = ''";
		$sql = $sql . " ORDER BY log.log_time ";
		if ($limit > 0) $sql = $sql . " LIMIT $limit";
		
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
				':member_id' => $member_id,
				':log_time' => $log_time,
				':context_id' => $context_id,
		));
		$results = $query->execute()->as_array();
		return $results;
	}
	
	function get_bot_member($bot_id, $member_id)
	{
		if (strlen($member_id) != 36) return NULL;
		$sql = "SELECT last_talk_date FROM t_bot_member WHERE member_id = :member_id ";
		$sql = $sql . " AND " . $this->_create_bot_cond_webchat($bot_id, 'bot_id');
		$sql = $sql . " ORDER BY last_talk_date DESC";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
				':member_id' => $member_id,
		));
		$results = $query->execute()->as_array();
		return $results;
	}
	
	function get_item_bot($item_cd)
	{
		$sql = 'SELECT a.item_id, a.item_cd, a.item_class_cd, a.bot_id,
				b.*, s.setting_value AS rel_bot
				FROM t_item a
				INNER JOIN t_bot b ON a.bot_id = b.bot_id
				LEFT JOIN t_bot_setting s ON a.bot_id = s.bot_id AND s.setting_cd=\'A01_relation_bots\'
				WHERE a.item_cd = :item_cd';
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':item_cd' => $item_cd,
		));
		$results = $query->execute()->as_array();
		if (count($results) == 0) return null;
		return $results[0];
	}
	
	function get_webchats($bot_id, $context_id, $member_id, $log_time, $member_msg_only=true, $start_time='', $limit=0)
	{
		if ($limit == 0) {
			$table = 't_bot_log_chat';
			$table = "t_bot_log$this->_log_ext";
		}
		else {
			$table = "t_bot_log$this->_log_ext";
		}
		$sql = "SELECT log.member_id, log.sns_type_cd, log.log_time, log.lang_cd, log.scene_cd, log.bot_id,
		log.member_msg, log.bot_msg, log.bot_msg_t, log.score, log.intent_cd, log.log_id, des.content AS bot_name
		FROM $table log LEFT JOIN t_bot_msg msg ON log.bot_id = msg.bot_id AND msg.msg_cd = 'bot_name'
		LEFT JOIN t_bot_msg_desc_txt des ON msg.msg_id = des.msg_id AND des.lang_cd = log.lang_cd AND des.no=1
		WHERE log.member_id = :member_id AND log.sns_type_cd='wb' AND (log.user_id IS NULL OR log.user_id > 0) ";
		$sql = $sql . " AND " . $this->_create_bot_cond_webchat($bot_id, 'log.bot_id');
		if ($member_msg_only) $sql = $sql . " AND log.member_msg = ''";
		if ($context_id != '') $sql = $sql . " AND log.context_id = :context_id";
		if ($start_time == '') {
			// refresh case
			if ($limit == 0) {
				$sql = $sql . " AND log.log_time > :log_time ORDER BY log.log_time";
			}
			// logs case
			else {
				if ($log_time == '') {
					$sql = $sql . " ORDER BY log.log_time DESC LIMIT 0, " . $limit;
				}
				else {
					$sql = $sql . " AND log.log_time >= :log_time ORDER BY log.log_time DESC";
				}
			}
		}
		// more logs case
		else {
			$sql = $sql . " AND log.log_time < :start_time ORDER BY log.log_time DESC LIMIT 0, " . $limit;
		}

		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
				':member_id' => $member_id,
				':log_time' => $log_time,
				':start_time' => $start_time,
				':context_id' => $context_id,
		));
		$results = $query->execute()->as_array();
		if ($limit > 0) {
			$results = array_reverse($results);
		}
		return $results;
	}

	function get_token($bot_id)
	{
		$bot_grp_id = $this->get_grp_bot_id($bot_id);
		if ($bot_grp_id > 0) $bot_id = $bot_grp_id;
		if ($bot_id >= 401000 && $bot_id < 402000) $bot_id = 401000;
		return $bot_id . "|" . $this->get_mstime();
	}
	
	function get_notice_msglist($bot_id, $notice_type, $last_access_time)
	{
		//Log::instance()->add(Log::DEBUG, "last_access_time:" . $last_access_time);
		//$last_access_time = substr($last_access_time, 0, 10);
		$today = date('Y-m-d');
		$sql = "SELECT msg_cd, msg_type_cd FROM t_bot_msg WHERE bot_id=:bot_id AND msg_class_cd =:msg_class_cd AND start_date IS NOT NULL AND end_date IS NOT NULL 
				AND start_date<=:today AND end_date>=:today ";
		if ($last_access_time != NULL) $sql = $sql . " AND DATE_FORMAT(start_date, '%Y-%m-%d %H:%i:%s')>=:last_access_time";
		$sql = $sql . " ORDER BY start_date";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
				':today' => $today,
				':msg_class_cd' => $notice_type,
				':last_access_time' => $last_access_time,
		));		
		return $query->execute()->as_array();
	}
	
	function get_bot_greetings($bot_id, $lang_cd, $scene_cd='')
	{
		if ($scene_cd == '') {
			$greeting = "'talkappi\_greeting\__'";
		}
		else {
			$greeting = "'talkappi\_greeting\_" . $scene_cd . "\__'";
		}
		$today = date('Y-m-d');
		$sql = "SELECT t.msg_cd, t.msg_data, d.* FROM t_bot_msg t LEFT JOIN t_bot_msg_desc_lst d ON t.msg_id=d.msg_id AND d.lang_cd=:lang_cd 
				 WHERE t.bot_id=:bot_id AND t.msg_cd LIKE $greeting AND (t.start_date IS NULL OR t.start_date<=:today) 
				  AND (t.end_date IS NULL OR t.end_date>=:today) 
				ORDER BY t.msg_cd, d.no";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
				':lang_cd' => $lang_cd,
				':today' => $today,
		));
		return $query->execute()->as_array();
	}
	
	function post($facility_cd, $member_id, $lang_cd, $message, $ua_info)
	{
		$base_url= $this->get_env('engine_url') . "webhook/web/";
		$data = ['events'=>[[
				'replyToken'=>'nHuyWiB7yP5Zw52FIkcQobQuGDXCTA',
				'type'=>'message',
				'timestamp'=> time(),
				'source' => [
						'type'=>'user',
						'userId'=>$member_id,
						'facility_cd'=>$facility_cd,
						'from_facility_cd'=>$facility_cd,
				],
				'message' => [
						'id'=>$lang_cd,
						'type'=>'text',
						'text'=>$message
						],
				'ua_info' => $ua_info
				]]];

		$header = [
				'Content-Type: application/json; charset=UTF-8',
		];
		$curl = curl_init();
		curl_setopt($curl, CURLOPT_URL, $base_url);
		curl_setopt($curl, CURLOPT_CUSTOMREQUEST, 'POST'); // post
		curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($data));
		curl_setopt($curl, CURLOPT_HTTPHEADER, $header); // リクエストにヘッダーを含める
		curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
		curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($curl, CURLOPT_HEADER, true);
		$response = curl_exec($curl);
		//Log::instance()->add(Log::DEBUG, "WEBCHAT POST:" . $response);
	}

	function get_menu($bot_id, $menu_group_id, $lang_cd=NULL)
	{
		$sql = "SELECT menu_group_id, lang_cd, sortno, menu_sub_group_text, menu_sub_group_cmd, menu_title, display_mode
                FROM t_bot_menu_group t
				WHERE t.bot_id = :bot_id ";
		$sql = $sql . " AND t.menu_group_id = :menu_group_id ";
		if ($lang_cd !== NULL) $sql = $sql . " AND t.lang_cd = :lang_cd ";

		$sql = $sql . " ORDER BY menu_group_id, lang_cd, sortno";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
				':menu_group_id' => $menu_group_id,
				':lang_cd' => $lang_cd,
		));
		$results = $query->execute()->as_array();
		$menu_groups = array();
		$title_array = array(
			'ja' => 'メニュー',
			'cn' => '主菜单',
			'tw' => '主選單',
			'en' => 'MENU',
			'kr' => '메인 메뉴',
		);
		foreach($results as $m) {
			$action = 'postback';
			if (strpos($m["menu_sub_group_cmd"], "http")===0) {
				$action = 'url';
			}
			if (array_key_exists($m['lang_cd'], $menu_groups)) {
				$item = ['menu_text'=>$m['menu_sub_group_text'], 'menu_type'=>$action, 'menu_action'=>$m['menu_sub_group_cmd']];
				$menu_groups[$m['lang_cd']]['menu_list'][] = $item;
			}
			else {
				$items = [
					//'menu_title'=>$m['menu_title'],
					'menu_title'=>$title_array[$m['lang_cd']],
					'menu_list'=>[
							['menu_text'=>$m['menu_sub_group_text'], 'menu_type'=>$action, 'menu_action'=>$m['menu_sub_group_cmd']]
					]
				];
				$menu_groups[$m['lang_cd']] = $items;
			}
		}
		return $menu_groups;
	}
	
	function get_last_follow($bot_id, $member_id) {
		$sql = 'SELECT extra_info FROM t_bot_follow WHERE bot_id=:bot_id AND member_id=:member_id ORDER BY follow_time DESC LIMIT 1';
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
				':member_id' => $member_id,
		));
		$results = $query->execute()->as_array();
		if (count($results) == 0) return null;
		return $results[0];
	}
}

?>
