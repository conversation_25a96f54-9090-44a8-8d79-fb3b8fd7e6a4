<?php defined('SYSPATH') OR die('No direct script access.');
# Includes the autoloader for libraries installed with composer
require DOCROOT. '/vendor/autoload.php';

# Imports the Google Cloud client library
use Aws\S3\S3Client;
use Aws\Common\Enum\Region;
use Aws\CloudFront\CloudFrontClient;
use Aws\Exception\AwsException;

class Model_Aws extends Model_Basemodel
{
	private $s3client = NULL;
	private $odn_url = "https://d246nfzk8tz6kg.cloudfront.net/";
	private $bucket = 'contents.talkappi.com';
	
	public function __construct()
	{
		$aws_s3_config = $this->get_env('aws_s3_config');
		$this->s3client = new S3Client($aws_s3_config); 
		$this->bucket = $this->get_env('aws_s3_bucket');
		$this->odn_url = $this->get_env('cdn_url');
	}
	
	function refresh_url($url) {
		if ($url == null) return;
		$path = '/' . str_replace($this->odn_url, "", $url);
		$this->_refresh([$path]);
	}
	private function _refresh($paths) {
		$client = new Aws\CloudFront\CloudFrontClient($this->get_env('aws_s3_config'));
		$result = null;
		$id = $this->get_env('cdn_id');
		$callerReference = time();
		try {
			$result = $client->createInvalidation([
					'DistributionId' => $id,
					'InvalidationBatch' => [
							'CallerReference' => $callerReference,
							'Paths' => [
									'Items' => $paths,
									'Quantity' => 1,
							],
					]
			]);
			//var_dump($result);
		} catch (AwsException $e) {
			// output error message if fails
			//echo $e->getMessage();
			//echo "\n";
			Log::instance()->add(Log::DEBUG, "AWS-S3-REFRESH:($this->odn_url>$paths[0])" . $e->getMessage());
		}
		return $result;
	}
	
	function exist($keyname) {
		return $this->s3client->doesObjectExist($this->bucket, $keyname);
	}
	
	function delete($keyname) {
		$result = $this->s3client->deleteObject([
			'Bucket' => $this->bucket,
			'Key'    => $keyname
		]);
		return $result;
	}
	
	function put_faq($bot_id, $intent_cd, $sub_intent_cd, $area_cd, $no, $lang_cd="", $key="image")
	{
		$file_name = $bot_id . "/faq/" . $intent_cd;
		if ($sub_intent_cd != "") $file_name = $file_name . "_" . $sub_intent_cd;
		if ($area_cd != "") $file_name = $file_name . "_" . $area_cd;
		$file_name = $file_name . "_" . $no;
		if ($lang_cd != "") $file_name = $file_name . "_" . $lang_cd;
		return $this->_put_file($file_name, $key);
	}
	function put_message($bot_id, $msg_cd, $no, $lang_cd="", $key="image")
	{
		$file_name = $bot_id . "/message/" . $msg_cd . "_" . $no;
		if ($lang_cd != "") $file_name = $file_name . "_" . $lang_cd;
		return $this->_put_file($file_name, $key);
	}
	function put_product($bot_id, $product_id, $lang_cd="", $key="image")
	{
		$file_name = $bot_id . "/product/" . $product_id;
		if ($lang_cd != "") $file_name = $file_name . "_" . $lang_cd;
		return $this->_put_file($file_name, $key);
	}
	function put_product_ext($bot_id, $product_id, $lang_cd="", $key="image")
	{
		$file_name = $bot_id . "/product/" . $product_id;
		$file_name = $file_name . "_" . time();
		if ($lang_cd != "") $file_name = $file_name . "_" . $lang_cd;
		return $this->_put_file($file_name, $key);
	}
	function put_item($bot_id, $item_id, $lang_cd="", $key="image")
	{
		$file_name = $bot_id . "/item/" . $item_id;
		if ($lang_cd != "") $file_name = $file_name . "_" . $lang_cd;
		return $this->_put_file($file_name, $key);
	}
	function put_survey($bot_id, $item_id, $lang_cd="", $key="image")
	{
		$file_name = $bot_id . "/survey/" . $item_id;
		if ($lang_cd != "") $file_name = $file_name . "_" . $lang_cd;
		return $this->_put_file($file_name, $key);
	}
	
	function put_survey_entry($bot_id, $item_id, $no, $key="image")
	{
		$file_name = $bot_id . "/survey/" . $item_id . '/entry/' . $no . '_' . time();
		return $this->_put_file($file_name, $key);
	}
	
	function put_survey_result($bot_id, $item_id, $no, $uid, $key="image")
	{
		$file_name = $bot_id . "/survey/" . $item_id . '/result/surveyID_' . $item_id . '_question_' . $no . '_uid_' . $uid;
		return $this->_put_file($file_name, $key);
	}
	
	function put_inquiry($bot_id, $item_id, $lang_cd="", $key="image")
	{
		$file_name = $bot_id . "/inquiry/" . $item_id;
		if ($lang_cd != "") $file_name = $file_name . "_" . $lang_cd;
		return $this->_put_file($file_name, $key);
	}
	
	function put_inquiry_entry($bot_id, $item_id, $no, $key="image")
	{
		$file_name = $bot_id . "/inquiry/" . $item_id . '/entry/' . $no . '_' . time();
		return $this->_put_file($file_name, $key);
	}
	
	function put_inquiry_result($bot_id, $item_id, $no, $uid, $key="image")
	{
		$file_name = $bot_id . "/inquiry/" . $item_id . '/result/inquiryID_' . $item_id . '_question_' . $no . '_uid_' . $uid;
		return $this->_put_file($file_name, $key);
	}
	
	function put_file($bot_id, $type, $name, $key="image")
	{
		$file_name = $bot_id . "/$type/" . $name;
		return $this->_put_file($file_name, $key);
	}
	
	function put_standard_car_image($bot_id, $item_div, $item_id, $image_url)
	{
		if ($image_url == '') return '';
		$base_image = $this->_imagecreatefrom($image_url);
		$width = imagesx($base_image);
		$height = imagesy($base_image);

		//$blank_image = $this->_imagecreatefrom("https://admin.talkappi.com/assets/common/images/merge-item.png");
		$blank_image = $this->_imagecreatefrom(APPPATH . '../assets/common/images/merge-item.png');
		$content_type = 'image/png';
		$width_blank = imagesx($blank_image);
		$height_blank = imagesy($blank_image);
		
		$start_left = 0;
		$start_top = 0;
		$width_target = $width_blank;
		$height_target = $height_blank;
		if ($width / $height > 1.5) {
			$height_target = floor($width_blank * ($height / $width));
			$start_top = floor(($height_blank - $height_target)/2);
		}
		else if ($width / $height < 1.5){
			$width_target = floor($height_blank * ($width / $height));
			$start_left = floor(($width_blank - $width_target)/2);
		}
		imagealphablending($blank_image, false);
		imagesavealpha($blank_image, true);
		imagecopyresampled($blank_image, $base_image, $start_left, $start_top, 0, 0, $width_target, $height_target, $width, $height);
		$stream = $this->_image($content_type, $blank_image);
		
		if ($item_div <= 4) {
			$filename = $bot_id . "/item/" . $item_id;
		}
		else if ($item_div == 5 || $item_div == 7) {
			$filename = $bot_id . "/product/" . $item_id;
		}
		else if ($item_div == 6) {
			$filename = $bot_id . "/message/" . $item_id;
		}
		else if ($item_div == 8) {
			$filename = $bot_id . "/survey/" . $item_id;
		}
		$filename = $filename . '.' . 'png';
		$result = $this->s3client->putObject([
				'ACL' => 'public-read',
				'Bucket' => $this->bucket,
				'Key' => $filename,
				'Body' => $stream,
				'ContentType' => $content_type,
		]);
		$this->log_info(__FUNCTION__, json_encode($result, JSON_UNESCAPED_UNICODE), $bot_id);
		imageDestroy($blank_image);
		imageDestroy($base_image);
		return "$this->odn_url$filename";
	}
	
	function delete_object($bot_id, $type, $keyname) {
		$file_name = $bot_id . "/$type/" . $keyname;
		$result = $this->s3client->deleteObject([
				'Bucket' => $this->bucket,
				'Key'    => $file_name
		]);
		return $result;
	}
	
	function delete_object_full_path($keyname) {
		$result = $this->s3client->deleteObject([
				'Bucket' => $this->bucket,
				'Key'    => $keyname
		]);
		return $result;
	}
	
	private function _put_file($file_name, $file_key)
	{
		$ext = substr($_FILES[$file_key]['name'], strrpos($_FILES[$file_key]['name'], '.') + 1);
		$image = fopen($_FILES[$file_key]['tmp_name'],'rb');
		$file_name = $file_name . "." . $ext;
		$result = $this->s3client->putObject([
				'ACL' => 'public-read',
				'Bucket' => $this->bucket,
				'Key' => $file_name,
				'Body' => $image,
				'ContentType' => mime_content_type($_FILES[$file_key]['tmp_name']),
		]);
		//ob_end_flush();
		//Log::instance()->add(Log::DEBUG, "AWS-S3-PUT-RESULT:" . $result);
		return "$this->odn_url$file_name";
	}

	private function _copy_image($src, $obj)
	{
		$result = $this->s3client->copyObject([
				'Bucket'     => $this->bucket,
				'Key'        => $obj,
				'CopySource' => $this->bucket . '/' . $src,
				'ACL'        => 'public-read'
				]);
		
		Log::instance()->add(Log::DEBUG, "src :" . $this->bucket . '/' . $src);
		Log::instance()->add(Log::DEBUG, "obj :" . $obj);
		Log::instance()->add(Log::DEBUG, "copy RESULT:" . json_encode($result));
		return $this->odn_url . $obj;
		/*
		$deleteRes = $this->s3client->deleteObject([
				'Bucket' => $this->bucket,
				'Key'    => $src,
		]);
		*/
	}
	
	private function _copy_image2($src, $obj)
	{
		$content_type = $this->_get_content_type($src);
		$src_url = $this->odn_url . $src;

		$base_image = $this->_imagecreatefrom($src_url);
		
		$stream = $this->_image($content_type, $base_image);
		$result = $this->s3client->putObject([
				'ACL' => 'public-read',
				'Bucket' => $this->bucket,
				'Key' => $obj,
				'Body' => $stream,
				'ContentType' => $content_type,
		]);
		imageDestroy($base_image);
		return $this->odn_url . $obj;
	}

	function all_lang_image($item_image_url)
	{
		if (strpos($item_image_url, $this->odn_url) === 0) {
			$item_image_url = str_replace($this->odn_url, '', $item_image_url);
			$arr = explode('/', $item_image_url);
			$bot_id = $arr[0];
			$path = $arr[1];
			$file_name = $arr[2];
			$pos = strrpos($file_name, '_');
			if ($pos !== false) {
				$lang_cd = substr($file_name, $pos+1, 2);
				$langs = $this->get_code('02');
				if (!array_key_exists($lang_cd, $langs)) return '';
				$old_file_name = $bot_id . '/' . $path . '/' . $file_name;
				$new_filename = $bot_id . '/' . $path . '/' . str_replace("_" . $lang_cd, "", $file_name);
				$this->_copy_image($old_file_name, $new_filename);
				return $this->odn_url . $new_filename;
			}
		}
		return '';
	}

	function base_item_image($item_image_url, $bot_id, $item_id, $lang_cd)
	{
		return $this->_base_image('item', $item_image_url, $bot_id, $item_id, $lang_cd);
	}
	function base_product_image($item_image_url, $bot_id, $product_id, $lang_cd)
	{
		return $this->_base_image('product', $item_image_url, $bot_id, $product_id, $lang_cd);
	}
	
	private function _base_image($type, $item_image_url, $bot_id, $item_id, $lang_cd)
	{
		if (strpos($item_image_url, $this->odn_url) === 0) {
			$has_temp = strpos($item_image_url, '_temp') !== false;
			$item_image_url = str_replace('_xx', '', $item_image_url);
			$ext_pos = strrpos($item_image_url, '.');
			$ext = substr($item_image_url, $ext_pos + 1);
			$base_image_url = substr($item_image_url, 0, $ext_pos) . "_base." . $ext;
			if ($has_temp) {
				$base_image_url = substr($item_image_url, 0, $ext_pos) . "_temp_base." . $ext;
			} 
			$url = $this->get_file_url($base_image_url);
			if ($url != '') {
				return $url;
			}
			else {
				$src = str_replace($this->odn_url, '', $item_image_url);
				$file_name = $bot_id . "/$type/" . $item_id;
				if ($lang_cd != "") $file_name = $file_name . "_" . $lang_cd;
				if ($has_temp) {
					$file_name =  $file_name . "_temp_base." . $ext;
				} else {
					$file_name =  $file_name . "_base." . $ext;
				}
				return $this->copy_object($src, $file_name);
			}
		}
		$has_temp = strpos($item_image_url, '_temp') !== false;
		$ext_pos = strrpos($item_image_url, '.');
		$ext = substr($item_image_url, $ext_pos + 1);
		$file_name = $bot_id . "/$type/" . $item_id;
		if ($lang_cd != "") $file_name = $file_name . "_" . $lang_cd;
		if ($has_temp) {
			$file_name =  $file_name . "_temp_base." . $ext;
		} else {
			$file_name =  $file_name . "_base." . $ext;
		}
		$content_type = $this->_get_content_type($item_image_url);
		$base_image = $this->_imagecreatefrom($item_image_url);
		$stream = $this->_image($content_type, $base_image);
		$result = $this->s3client->putObject([
				'ACL' => 'public-read',
				'Bucket' => $this->bucket,
				'Key' => $file_name,
				'Body' => $stream,
				'ContentType' => $content_type,
		]);
		imageDestroy($base_image);
		return "$this->odn_url$file_name";
	}
	
	function item_tag($bot_id, $item_div, $item_id, $lang_cd, $tags, $image_url, $use_base=true) {
		if ($image_url == null) return null;
		$content_type = $this->_get_content_type($image_url);
		if ($content_type == 'image/svg' || $content_type == 'image/gif' ) return $image_url;
		//if ($lang_cd == '') $lang_cd = 'ja';
		$base_lang_cd = 'ja';
		if ($lang_cd != '') $base_lang_cd = $lang_cd;
		if ($image_url == '') {
			if ($item_div == 5) {
				$desc = ORM::factory('productdescription')->where('product_id', '=', $item_id)->where('lang_cd', '=', $base_lang_cd)->find();
				if (isset($desc->product_image) && $desc->product_image != '') {
					$image_url= $desc->product_image;
				}
			}
			else {
				$desc = ORM::factory('itemdescription')->where('item_id', '=', $item_id)->where('lang_cd', '=', $base_lang_cd)->find();
				if (isset($desc->item_image) && $desc->item_image != '') {
					$image_url = $desc->item_image;
				}
			}
		}
		if ($use_base) {
			if ($item_div == 5) {
				$base_image_url = $this->base_product_image($image_url, $bot_id, $item_id, $lang_cd);
			}
			else {
				$base_image_url = $this->base_item_image($image_url, $bot_id, $item_id, $lang_cd);
			}
		}
		else {
			$base_image_url = $image_url;
		}
		$icons = $this->get_bot_message($bot_id, 'item_div_' . $item_div . '_tag', $base_lang_cd, 'img');
		$icon_dict = [];
		foreach($icons as $icon) {
			$icon_dict[$icon['title']] = $icon['msg_image'];
		}
		$prints = [];
		foreach($tags as $tag) {
			if (array_key_exists($tag, $icon_dict)) {
				$prints[] = ["position_x"=>0, "position_y"=>0, "image_url"=>$icon_dict[$tag]];
			}
		}
		if (count($prints) == 0) {
			//return $image_url;
		}
		
		$icon_height = $this->get_image_height($icons[0]['msg_image']);

		$file_name = str_replace($this->odn_url, "", $base_image_url);
		$file_name = str_replace("_base", "", $file_name);
		$facebook_image_tag = $this->get_bot_setting($bot_id, 'facebook_image_tag');
		if ($facebook_image_tag != '') {
			$ext_pos = strrpos($file_name, '.');
			$file_name_fb = substr($file_name, 0, $ext_pos) . "_". $facebook_image_tag . substr($file_name, $ext_pos);
			$ratio = 1.91;
			$new_url = $this->merge_image($bot_id, $base_image_url, $file_name_fb, $prints, $icon_height, $ratio);
			$file_name = substr($file_name, 0, $ext_pos) . "_". "xx" . substr($file_name, $ext_pos);
		}
		$ratio = 1.51;
		$new_url = $this->merge_image($bot_id, $base_image_url, $file_name, $prints, $icon_height, $ratio);
		return $new_url;
	}
	
	function item_banner($bot_id, $item_div, $item_id, $lang_cd, $item_data, $image_url, $use_base, $temp_file=false)
	{
		if ($lang_cd == '') $lang_cd = 'ja';
		$tag_images = $this->get_bot_img_message_list($bot_id, 'item_div_' . $item_div . '_banner', $lang_cd);
		$tag_class_cd_array = explode(' ', $item_data['banners']);
		$tag_url = '';
		foreach($tag_class_cd_array as $tag_class_cd) {
			foreach($tag_images as $tag_image) {
				if ($tag_image['title'] == $tag_class_cd) {
					$tag_url = $tag_image['msg_image'];
					break;
				}
			}
			if ($tag_url != '') break;
		}
		if ($tag_url == '') {
			foreach($tag_images as $tag_image) {
				if (trim($tag_image['title']) == '') {
					$tag_url = $tag_image['msg_image'];
					break;
				}
			}
		}
		if ($image_url == '') {
			if ($item_div == 5) {
				$desc = ORM::factory('productdescription')->where('product_id', '=', $item_id)->where('lang_cd', '=', $lang_cd)->find();
				if (isset($desc->product_image) && $desc->product_image != '') {
					$image_url= $desc->product_image;
				}
			}
			else {
				$desc = ORM::factory('itemdescription')->where('item_id', '=', $item_id)->where('lang_cd', '=', $lang_cd)->find();
				if (isset($desc->item_image) && $desc->item_image != '') {
					$image_url = $desc->item_image;
				}
			}
		}
		if ($use_base) {
			if ($item_div == 5) {
				$base_image_url = $this->base_product_image($image_url, $bot_id, $item_id, $lang_cd);
			}
			else {
				$base_image_url = $this->base_item_image($image_url, $bot_id, $item_id, $lang_cd);
			}
		}
		else {
			$base_image_url = $image_url;
		}
		$filename = str_replace($this->odn_url, "", $base_image_url);
		$filename = str_replace("_base", "", $filename);
		if ($temp_file) {
			$arr = explode('.', $temp_file);
			$filename = $arr[0] . '_' . $item_data['price'] . '.' . $arr[1];
		}
		$base_image_url = $this->_get_original_aws_url($base_image_url);
		$content_type = $this->_get_content_type($base_image_url);
		$base_image = $this->_imagecreatefrom($base_image_url);

		$width = imagesx($base_image);
		$height = imagesy($base_image);
		
		$start_left = 0;
		$start_top = 0;
		$tag_image = $this->_imagecreatefrom($tag_url);
		$tag_width = imagesx($tag_image);
		$tag_height = imagesy($tag_image);
		$start_left = $width - $tag_width;
		imagecopymerge(
				// destination
				$base_image,
				// source
				$tag_image,
				// destination x and y
				$start_left, $start_top,
				// source x and y
				0, 0,
				// width and height of the area of the source to copy
				$tag_width, $tag_height,
				85);
		imageDestroy($tag_image);
		
		$start_top = 90 - 10;
		if (array_key_exists('room_type', $item_data)) {
			$text = $item_data["room_type"];
		}
		else {
			$text = '';
		}
		$font_path = APPPATH . "../assets/common/font";
		$front_brush = imagecolorallocate($base_image, 255, 255, 255);
		$font = $font_path . "/arialbd.ttf";
		$fontSize = 70;
		$fontBox = imagettfbbox($fontSize, 0, $font, $text);
		$diff = ($tag_width - $fontBox[2]) / 2;
		imagettftext($base_image, $fontSize, 0, $start_left + $diff, $start_top, $front_brush, $font, $text);
		
		$start_top = $tag_height - 19;
		
		//$text = $item_data["price"];
		if (array_key_exists('price', $item_data) && $item_data["price"] > 0) {
			$text = number_format(intval(str_replace(',', '', $item_data["price"])));
			$front_brush = imagecolorallocate($base_image, 0, 0, 0);
			$dummy_brush = imagecolorallocate($base_image,255,255,255);
			$font = $font_path . "/msgothic.ttc";
			$fontSize = 52;
			imagettftext($base_image, $fontSize, 0, $start_left, $start_top, $dummy_brush, $font, "￥");
			imagettftext($base_image, $fontSize, 0, $start_left, $start_top, $front_brush, $font, "￥");
			imagettftext($base_image, $fontSize, 0, $width - $fontSize - 10, $start_top, $front_brush, $font, "～");
			
			$text = str_replace('￥', '', $text);
			$text = str_replace('～', '', $text);
			$font = $font_path . "/arialbd.ttf";
			$fontBox = imagettfbbox($fontSize, 0, $font, $text);
			$diff = ($tag_width - $fontBox[2]) / 2;
			imagettftext($base_image, $fontSize, 0, $start_left + $diff, $start_top, $front_brush, $font, $text);
		}
		
		$stream = $this->_image($content_type, $base_image);
		$result = $this->s3client->putObject([
				'ACL' => 'public-read',
				'Bucket' => $this->bucket,
				'Key' => $filename,
				'Body' => $stream,
				'ContentType' => $content_type,
		]);
		imageDestroy($base_image);
		return "$this->odn_url$filename";
	}

	function item_floor($item_id, $bot)
	{
		// map image
		$bot_id = $bot->bot_id;
		$item = ORM::factory('item', $item_id);
		$item_data = json_decode($item->item_data, true);
		$map_msg_cd = 'floor_map_{' . $bot->bot_class_cd . '}_' . $item->position_z;
		$base_image_url = $this->get_bot_img_message($bot->bot_id, $map_msg_cd, 'ja');
		if ($base_image_url == '') return;
		$lang_array = explode(',', $bot->lang_cd);
		$mark_url = $this->get_bot_img_message($bot_id, 'map_mark_{' . $bot->bot_class_cd . '}_spot', 'ja');
		$location_url = $this->get_bot_img_message($bot_id, 'map_mark_{' . $bot->bot_class_cd . '}_location', 'ja');
		if (is_array($item_data) && array_key_exists('scene_cd', $item_data) && $item_data['scene_cd']!='') {
			// scene item
			$bot_scenes = ORM::factory('botscene')->where('bot_id', '=', $bot_id)->where('scene_name', '=', $item_data['scene_cd'])->find_all();
			foreach($bot_scenes as $scene) {
				DB::update('t_bot_scene')->set(array('scene_data' => json_encode(['item_id'=>$item->item_id]),
						'upd_user'=>1,'upd_time'=>date('Y-m-d H:i:s')))->
						where('bot_id', '=', $bot_id)->where('scene_name', '=', $scene->scene_name)->execute();
						foreach($lang_array as $lang_cd) {
							$base_image_url = $this->get_bot_img_message($bot_id, $map_msg_cd, $lang_cd);
							if ($base_image_url == '') continue;
							$loc_print = ["position_x"=>$item->position_x, "position_y"=>$item->position_y, "image_url"=>$location_url];
							//$filename = $scene->scene_name . '_' . $item->item_id . '_' . $lang_cd . '.png';
							$filename = 'map/' . $scene->scene_name . '_' . $lang_cd . '.' . $this->get_file_ext($base_image_url);
							$this->add_waterprint($bot_id, $base_image_url, $filename, [$loc_print]);
							$base_image_url = $this->get_bot_img_message($bot_id, $map_msg_cd . '.' . '_facility', $lang_cd);
							if ($base_image_url == '') continue;
							$loc_print = ["position_x"=>$item->position_x, "position_y"=>$item->position_y, "image_url"=>$location_url];
							$filename = 'map/' . $scene->scene_name . '_f_' . $lang_cd . '.' . $this->get_file_ext($base_image_url);
							$this->add_waterprint($bot_id, $base_image_url, $filename, [$loc_print]);
						}
						// elavator
			}
		}
		else {
			$bot_scenes = ORM::factory('botscene')->where('bot_id', '=', $bot_id)->find_all();
			// with person at scene
			foreach($bot_scenes as $scene) {
				if ($scene->scene_data == '') continue;
				$scene_data = json_decode($scene->scene_data, true);
				if (!array_key_exists('item_id', $scene_data)) continue;
				$scene_item_id = $scene_data['item_id'];
				$scene_item = ORM::factory('item', $scene_item_id);
				if (strtoupper($scene_item->position_z) == strtoupper($item->position_z)) {
					foreach($lang_array as $lang_cd) {
						$base_image_url = $this->get_bot_img_message($bot_id, $map_msg_cd, $lang_cd);
						if ($base_image_url == '') continue;
						$print = ["position_x"=>$item->position_x, "position_y"=>$item->position_y, "image_url"=>$mark_url];
						$loc_print = ["position_x"=>$scene_item->position_x, "position_y"=>$scene_item->position_y, "image_url"=>$location_url];
						$filename = 'map/' . $item->item_id . '_' . $scene->scene_name . '_' . $lang_cd . '.' . $this->get_file_ext($base_image_url);
						$this->add_waterprint($bot_id, $base_image_url, $filename, [$print, $loc_print]);
					}
				}
			}
			// item only
			foreach($lang_array as $lang_cd) {
				$base_image_url = $this->get_bot_img_message($bot_id, $map_msg_cd, $lang_cd);
				if ($base_image_url == '') continue;
				$print = ["position_x"=>$item->position_x, "position_y"=>$item->position_y, "image_url"=>$mark_url];
				$filename = 'map/' . $item->item_id . '_' . $lang_cd . '.' . $this->get_file_ext($base_image_url);
				$this->add_waterprint($bot_id, $base_image_url, $filename, [$print]);
			}
		}
	}
	
	function add_waterprint($bot_id, $base_image_url, $filename, $waterprint) {
		$base_image_url = $this->_get_original_aws_url($base_image_url);
		$base_image = $this->_imagecreatefrom($base_image_url);
		$content_type = $this->_get_content_type($base_image_url);

		foreach($waterprint as $print) {
			$print_image = $this->_imagecreatefrom($print['image_url']);
			$print_width = imagesx($print_image);
			$print_height = imagesy($print_image);
			imagealphablending($print_image, true);
			imagesavealpha($print_image, true);
			imagecopy(
					// destination
					$base_image,
					// source
					$print_image,
					// destination x and y
					floor($print['position_x'] - $print_width/2), $print['position_y'] - $print_height,
					// source x and y
					0, 0,
					// width and height of the area of the source to copy
					$print_width, $print_height);
			imageDestroy($print_image);
		}

		$stream = $this->_image($content_type, $base_image);
		$result = $this->s3client->putObject([
				'ACL' => 'public-read',
				'Bucket' => $this->bucket,
				'Key' => $bot_id . '/' . $filename,
				'Body' => $stream,
				'ContentType' => $content_type,
		]);
		imageDestroy($base_image);
		return $this->odn_url . $filename;
	}

	private function _get_content_type($url) {
		$arr = explode('.', $url);
		$ext = strtolower(end($arr));
		if ($ext == "jpg") {
			return 'image/jpeg';
		}
		else if ($ext == 'mpg' || $ext == 'mpeg') {
			return 'video/mpg';
		}
		else if ($ext == 'mp4') {
			return 'video/mpeg4';
		}
		else if ($ext == 'mov') {
			return 'video/quicktime';
		}
		else {
			return 'image/' . $ext;
		}
	}

	private function _imagecreatefrom($url) {
		return imagecreatefromstring(file_get_contents($url));
		$arr = explode('.', $url);
		$ext = strtolower(end($arr));
		if ($ext =='png') {
			$base_image = imagecreatefrompng($url);
		}
		else if ($ext =='webp') {
			$base_image = imagecreatefromwebp($url);
		}
		else if ($ext =='gif') {
			$base_image = imagecreatefromgif($url);
		}
		else {
			$base_image = imagecreatefromjpeg($url);
		}
		return $base_image;
	}
	
	private function _image($content_type, $base_image) {
		ob_start();
		if ($content_type == 'image/png') {
			imagepng($base_image);
		}
		else if ($content_type == 'image/webp') {
			imagewebp($base_image);
		}
		else if ($content_type == 'image/gif') {
			imagegif($base_image);
		}
		else {
			imagejpeg($base_image);
		}
		$stream = ob_get_clean();
		return $stream;
	}

	function put($file_key)
	{		
		$filename = $_FILES[$file_key]['name'];
		$ext = substr($filename, strrpos($filename, '.') + 1);
		$new_filename = $file_key.'-'.time().'.'.$ext;
		$image = fopen($_FILES[$file_key]['tmp_name'],'rb');
		
		$result = $this->s3client->putObject([
				'ACL' => 'public-read',
				'Bucket' => $this->bucket,
				'Key' => $new_filename,
				'Body' => $image,
				'ContentType' => mime_content_type($_FILES[$file_key]['tmp_name']),
		]);
		//ob_end_flush();
		//Log::instance()->add(Log::DEBUG, "AWS-S3-PUT-RESULT:" . $result);
		return "$this->odn_url$new_filename";
	}

	function get_image_height($image_url) {
		$print_image = $this->_imagecreatefrom($image_url);;
		$icon_height = imagesy($print_image);
		imageDestroy($print_image);
		return $icon_height;
	}
	
	function get_image_info($image_url) {
		$image_url = $this->_get_original_aws_url($image_url);
		$print_image = $this->_imagecreatefrom($image_url);
		$height = imagesy($print_image);
		$width = imagesx($print_image);
		imageDestroy($print_image);
		return ['width'=>$width, 'height'=>$height];
	}
	
	function _get_original_aws_url($odn_url) {
		$odn_url = str_replace("d246nfzk8tz6kg.cloudfront.net", "s3-ap-northeast-1.amazonaws.com/contents.talkappi.com", $odn_url);
		$odn_url = str_replace("cdn.talkappi.com", "s3-ap-northeast-1.amazonaws.com/contents.talkappi.com", $odn_url);
		$odn_url = str_replace("dxxxxlf5ovtpt.cloudfront.net", "s3-ap-northeast-1.amazonaws.com/contentsdev.talkappi.com", $odn_url);
		return $odn_url;
	}
	
	function merge_image($bot_id, $base_image_url, $filename, $prints, $icon_height, $image_ratio, $banner=false) {
		$base_image_url = $this->_get_original_aws_url($base_image_url);
		$content_type = $this->_get_content_type($base_image_url);
		$base_image = $this->_imagecreatefrom($base_image_url);
		$base_width = imagesx($base_image);
		$base_height = imagesy($base_image);
		$ratio = $base_width/$base_height;
		$diff_x = 0;
		$diff_y = 0;
		if ($ratio > $image_ratio) {
			$diff_x = ($base_width - $base_height * $image_ratio) / 2;
			$base_width = $base_height * $image_ratio;
		}
		else {
			$diff_y = ($base_height - $base_width / $image_ratio) / 2;
			$base_height = $base_width / $image_ratio;
		}
		$rate = ($icon_height*6)/$base_height;
		$width = $base_width*$rate;
		$height = $base_height*$rate;
		$image_target = imagecreatetruecolor($base_width*$rate, $base_height*$rate);
		imagealphablending($image_target, false);
		imagesavealpha($image_target, true);
		imagecopyresampled($image_target, $base_image, 0, 0, $diff_x, $diff_y, $width, $height, $base_width, $base_height);
		
		//$background = imagecolorallocate($image_target, 0, 0, 0);
        //imagecolortransparent($image_target, $background);

		if ($banner) {
			$banner_image = $this->_imagecreatefrom(APPPATH . '../assets/common/images/merge-item-banner.png');
			$this->imagecopymerge_alpha(
					// destination
					$image_target,
					// source
					$banner_image,
					// destination x and y
					0, $height - 1.2*$icon_height,
					// source x and y
					0, 0,
					// width and height of the area of the source to copy
					$width, 1.2*$icon_height,
					75);
			imageDestroy($banner_image);
		}
		$i = 0;
		foreach($prints as $print) {
			$print_image = $this->_imagecreatefrom($print['image_url']);
			$print_width = imagesx($print_image);
			$print_height = imagesy($print_image);
			$this->imagecopymerge_alpha(
					// destination
					$image_target,
					// source
					$print_image,
					// destination x and y
					//$print['position_x'], $print['position_y'] - $print_height,
					$i*$print_width + ($i+1)*$print_width/5, $height - 1.1*$print_height,
					// source x and y
					0, 0,
					// width and height of the area of the source to copy
					$print_width, $print_height,
					100);
			imageDestroy($print_image);
			$i++;
		}

		$stream = $this->_image($content_type, $image_target);
		$result = $this->s3client->putObject([
				'ACL' => 'public-read',
				'Bucket' => $this->bucket,
				'Key' => $filename,
				'Body' => $stream,
				'ContentType' => $content_type,
		]);
		imageDestroy($image_target);
		return "$this->odn_url$filename";
	}
	
	function imagecopymerge_alpha($dst_im, $src_im, $dst_x, $dst_y, $src_x, $src_y, $src_w, $src_h, $opacity){  
        // getting the watermark width  
        $w = imagesx($src_im);  
        // getting the watermark height  
        $h = imagesy($src_im);  
        // creating a cut resource  
        $cut = imagecreatetruecolor($src_w, $src_h);  
        // copying that section of the background to the cut  
        imagecopy($cut, $dst_im, 0, 0, $dst_x, $dst_y, $src_w, $src_h);  
        // placing the watermark now  
        imagecopy($cut, $src_im, 0, 0, $src_x, $src_y, $src_w, $src_h);  
        imagecopymerge($dst_im, $cut, $dst_x, $dst_y, $src_x, $src_y, $src_w, $src_h, $opacity);  
    } 

	function put_item_image($file_key, $bot_id, $item_id, $waterprint=[])
	{	
		$filename = $_FILES[$file_key]['name'];
		$ext = strtolower(substr($filename, strrpos($filename, '.') + 1));
		$new_filename = $bot_id . '/' . $item_id . '_' . date('YmdHis') . '.' . $ext;
		if ($bot_id == 17) $new_filename = $bot_id . '/item/' . $item_id . '.' . $ext;
		if (count($waterprint) == 0) {
			$base_image = fopen($_FILES[$file_key]['tmp_name'],'rb');
			$result = $this->s3client->putObject([
					'ACL' => 'public-read',
					'Bucket' => $this->bucket,
					'Key' => $new_filename,
					'Body' => $base_image,
					'ContentType' => mime_content_type($_FILES[$file_key]['tmp_name']),
			]);
		}
		else {
			if ($ext == 'png') {
				$base_image = $this->_imagecreatefrom($_FILES[$file_key]['tmp_name']);
				$content_type = 'image/png';
			}
			else if ($ext == 'gif') {
				$base_image = $this->_imagecreatefrom($_FILES[$file_key]['tmp_name']);
				$content_type = 'image/gif';
			}
			else {
				$base_image = $this->_imagecreatefrom($_FILES[$file_key]['tmp_name']);
				$content_type = 'image/jpeg';
			}
			$banner_image = $this->_imagecreatefrom("https://cdn.talkappi.com/0/common/item_banner.png");
			$base_width = imagesx($base_image);
			$base_height = imagesy($base_image);
			imagecopymerge(
					// destination
					$base_image,
					// source
					$banner_image,
					// destination x and y
					0, $base_height - 32,
					// source x and y
					0, 0,
					// width and height of the area of the source to copy
					$base_width, 32,
					75);
			imageDestroy($banner_image);
			foreach($waterprint as $print) {
				$print_image = $this->_imagecreatefrom($print['image_url']);
				$print_width = imagesx($print_image);
				$print_height = imagesy($print_image);
				imagealphablending($print_image, true);
				imagesavealpha($print_image, true);
				imagecopymerge(
						// destination
						$base_image,
						// source
						$print_image,
						// destination x and y
						//$print['position_x'], $print['position_y'] - $print_height,
						$print['position_x'], $base_height - 30,
						// source x and y
						0, 0,
						// width and height of the area of the source to copy
						$print_width, $print_height,
						100);
				imageDestroy($print_image);
			}

			$stream = $this->_image($content_type, $base_image);
			$result = $this->s3client->putObject([
					'ACL' => 'public-read',
					'Bucket' => $this->bucket,
					'Key' => $new_filename,
					'Body' => $stream,
					'ContentType' => $content_type,
			]);
		}
		return "$this->odn_url$new_filename";
	}

	function put_image($file_key, $bot_id)
	{	
		$filename = $_FILES[$file_key]['name'];
		$ext = substr($filename, strrpos($filename, '.') + 1);
		$new_filename = $bot_id . '/' . uniqid("", true) . '.' . $ext;
		$image = fopen($_FILES[$file_key]['tmp_name'],'rb');
		$result = $this->s3client->putObject([
				'ACL' => 'public-read',
				'Bucket' => $this->bucket,
				'Key' => $new_filename,
				'Body' => $image,
				'ContentType' => mime_content_type($_FILES[$file_key]['tmp_name']),
		]);
		return "$this->odn_url$new_filename";
	}

	function put_data($file, $filename, $bot_id, $path='')
	{
		$content_type = $this->_get_content_type($filename);
		if ($path == '') {
			$file_path = $bot_id . '/' . $filename;
		}
		else {
			$file_path = $bot_id . '/' . $path . '/' . $filename;
		}
		$result = $this->s3client->putObject([
				'ACL' => 'public-read',
				'Bucket' => $this->bucket,
				'Key' => $file_path,
				'Body' => $file,
				'ContentType' => $content_type,
		]);
		return "$this->odn_url$file_path";
	}
	
	function put_base64_file($bot_id, $data, $filename, $path, $ext_def = '') {
		$data_sample = substr($data, 0, 200);
		$result = preg_match('/^data:(.*)\/(.*);base64,/', $data_sample, $type_arr);
		if ($result) {
			$data = substr($data, strpos($data, ',') + 1);
			$data = base64_decode($data);
			if ($data === false) {
				return null;
			}
		} else {
			return null;
		}
		$mime_type = $this->get_setting('support_mime_type');
		$mine_type = array_flip($mime_type);
		$content_type = $type_arr[1] . '/' . $type_arr[2];
		if (isset($mine_type[$content_type])) {
			$ext = $mine_type[$content_type];
		}
		else {
			if ($content_type == 'application/octet-stream') {
				$ext = $ext_def;
			} else {
				$ext = $type_arr[2];
			}
		}
		if ($path == '') {
			$filename = $bot_id . '/' . $filename;
		}
		else {
			$filename = $bot_id . '/' . $path . '/' . $filename;
		}
		if (pathinfo($filename, PATHINFO_EXTENSION) == '') {
			$filename = $filename . '.' . $ext;
		}

		$config = [
				'ACL' => 'public-read',
				'Bucket' => $this->bucket,
				'Key' => $filename,
				'Body' => $data,
				'ContentType' => $content_type,
		];
		if (strpos($path, 'upload/') === 0) $config['StorageClass'] = 'INTELLIGENT_TIERING';
		$result = $this->s3client->putObject($config);
		return $this->odn_url . $filename;
	}
	
	function put_image_waterprint($bot_id, $base_image_url, $filename, $waterprint) {
		$base_image_url = $this->_get_original_aws_url($base_image_url);
		$base_image = $this->_imagecreatefrom($base_image_url);
		$content_type = $this->_get_content_type($base_image_url);
		
		foreach($waterprint as $print) {
			$print_image = $this->_imagecreatefrom($print['image_url']);
			$print_width = imagesx($print_image);
			$print_height = imagesy($print_image);
			imagealphablending($print_image, true);
			imagesavealpha($print_image, true);
			imagecopy(
					// destination
					$base_image,
					// source
					$print_image,
					// destination x and y
					$print['position_x'] - $print_width/2, $print['position_y'] - $print_height,
					// source x and y
					0, 0,
					// width and height of the area of the source to copy
					$print_width, $print_height);
			imageDestroy($print_image);
		}

		$stream = $this->_image($content_type, $base_image);
		$result = $this->s3client->putObject([
				'ACL' => 'public-read',
				'Bucket' => $this->bucket,
				'Key' => $bot_id . '/' . $filename,
				'Body' => $stream,
				'ContentType' => $content_type,
		]);
		imageDestroy($base_image);
		return "$this->odn_url$bot_id/$filename";
	}
	
	function get_file_url($image_url) 
	{
		try {
			$image = $this->_imagecreatefrom($image_url);
			if ($image == null) {
				return '';
			}
			imageDestroy($image);
		}
		catch(Exception $e) {
			return '';
		}
		return $image_url;
	}
	
	function get_object_key($url) {
		return str_replace($this->odn_url, '', $url);
	}
	
	function copy_object($src, $obj)
	{
		$result = $this->s3client->copyObject([
				'Bucket'     => $this->bucket,
				'Key'        => $obj,
				'CopySource' => $this->bucket . '/' . $src,
				'ACL'        => 'public-read'
		]);
		return $this->odn_url . $obj;
	}
	
	// filename without ext
	function copy_image($bot_id, $base_image_url, $filename, $target_type = '')
	{
		$arr = explode('.', $base_image_url);
		$ext = end($arr);
		$content_type = $this->_get_content_type($base_image_url);
		$base_image = $this->_imagecreatefrom($base_image_url);
		if ($target_type == '') {
			$target_type = $content_type;
		}
		else {
			$ext = $target_type;
			$target_type = 'image/' . $target_type;
		}
		$stream = $this->_image($target_type, $base_image);
		$result = $this->s3client->putObject([
				'ACL' => 'public-read',
				'Bucket' => $this->bucket,
				'Key' => $bot_id . '/' . $filename . '.' . $ext,
				'Body' => $stream,
				'ContentType' => $target_type,
		]);
		imageDestroy($base_image);
		return "$this->odn_url$bot_id/$filename.$ext";
	}
	
	function put_url_file($bot_id, $url, $filename) {
		$arr = explode('.', $url);
		$ext = end($arr);
		$content_type = $this->_get_content_type($url);
		ob_start();
		readfile($url);
		$content = ob_get_contents();
		ob_end_clean();
		$result = $this->s3client->putObject([
				'ACL' => 'public-read',
				'Bucket' => $this->bucket,
				'Key' => $bot_id . '/' . $filename . '.' . $ext,
				'Body' => $content,
				'ContentType' => $content_type,
		]);
		return "$this->odn_url$bot_id/$filename.$ext";
	}

	function white_backcolor($url) {
		$content_type = $this->_get_content_type($url);
		$image = $this->_imagecreatefrom($url);
		$cdn_url = $this->get_env('cdn_url');
		$filename = str_replace($cdn_url, "", $url);
		$width = imagesx($image);
		$height = imagesy($image);
		$background = imagecreatetruecolor($width, $height);
		$white = imagecolorallocate($background, 255, 255, 255);
		imagefill($background, 0, 0, $white);
		imagealphablending($image, true);
		imagesavealpha($image, true);
		imagecopy($background, $image, 0, 0, 0, 0, $width, $height);
		//imagecolortransparent($background, imagecolorallocatealpha($background, 0, 0, 0, 127));
		//imagealphablending($background, false);
		//imagesavealpha($background, true);
		$stream = $this->_image($content_type, $background);
		$result = $this->s3client->putObject([
			'ACL' => 'public-read',
			'Bucket' => $this->bucket,
			'Key' => $filename,
			'Body' => $stream,
			'ContentType' => $content_type,
		]);
		imagedestroy($image);
		imagedestroy($background);
	}

	private function _resize_image($image_filename, $image_url, $target_width, $target_height)
	{
		$content_type = $this->_get_content_type($image_filename);
		if ($content_type == 'image/svg' || $content_type == 'image/gif') return;
		$base_image = $this->_imagecreatefrom($image_url);
		$base_width = imagesx($base_image);
		$base_height = imagesy($base_image);
		$ratio = $base_width/$base_height;
		if ($target_height == 0 || $target_width == 0) {
			if ($target_height == 0) {
				$target_height = $target_width / $ratio;
			}
			else {
				$target_width = $target_height * $ratio;
			}
			$image_target = imagecreatetruecolor($target_width, $target_height);
			imagealphablending($image_target, false);
			imagesavealpha($image_target, true);
			imagecopyresampled($image_target, $base_image, 0, 0, 0, 0, $target_width, $target_height, $base_width, $base_height);
		}
		else {
			$image_ratio = 1.5;
			$image_ratio = $target_width / $target_height;
			$diff_x = 0;
			$diff_y = 0;
			if ($ratio > $image_ratio) {
				$diff_x = ($base_width - $base_height * $image_ratio) / 2;
				$base_width = $base_height * $image_ratio;
			}
			else {
				$diff_y = ($base_height - $base_width / $image_ratio) / 2;
				$base_height = $base_width / $image_ratio;
			}
			$image_target = imagecreatetruecolor($target_width, $target_height);
			imagealphablending($image_target, false);
			imagesavealpha($image_target, true);
			imagecopyresampled($image_target, $base_image, 0, 0, $diff_x, $diff_y, $target_width, $target_height, $base_width, $base_height);
		}
		$stream = $this->_image($content_type, $image_target);
		return $stream;
	}
	
	function resize_image_url($image_url, $target_width, $target_height)
	{
		if ($image_url == null) return;
		$content_type = $this->_get_content_type($image_url);
		if ($content_type == 'image/svg' || $content_type == 'image/gif') return;
		$cdn_url = $this->get_env('cdn_url');
		$filename = str_replace($cdn_url, "", $image_url);
		
		$image_url = $this->_get_original_aws_url($image_url);
		
		$stream = $this->_resize_image($image_url, $image_url, $target_width, $target_height);

		$result = $this->s3client->putObject([
				'ACL' => 'public-read',
				'Bucket' => $this->bucket,
				'Key' => $filename,
				'Body' => $stream,
				'ContentType' => $content_type,
		]);
	}
	
	public function resize_image($filename, $target_width, $target_height, $expand=false)
	{
		$content_type = $this->_get_content_type($filename);
		if ($content_type == 'image/svg' || $content_type == 'image/gif') return;
		$base_image = $this->_imagecreatefrom($filename);
		$base_width = imagesx($base_image);
		$base_height = imagesy($base_image);
		$ratio = $base_width/$base_height;
		if ($target_height == 0 || $target_width == 0) {
			if ($target_height == 0) {
				if (!$expand && $base_width <= $target_width) return;
				$target_height = $target_width / $ratio;
			}
			else {
				if (!$expand && $base_height <= $target_height) return;
				$target_width = $target_height * $ratio;
			}
			$image_target = imagecreatetruecolor($target_width, $target_height);
			imagealphablending($image_target, false);
			imagesavealpha($image_target, true);
			imagecopyresampled($image_target, $base_image, 0, 0, 0, 0, $target_width, $target_height, $base_width, $base_height);
		}
		else {
			if (!$expand && $base_width <= $target_width && $base_height <= $target_height) return;
			$image_target = imagecreatetruecolor($target_width, $target_height);
			imagealphablending($image_target, false);
			imagesavealpha($image_target, true);
			imagecopyresampled($image_target, $base_image, 0, 0, 0, 0, $target_width, $target_height, $base_width, $base_height);
		}
		
		if ($content_type == 'image/png') {
			imagepng($image_target, $filename);
		}
		else if ($content_type == 'image/webp') {
			imagewebp($image_target, $filename);
		}
		else if ($content_type == 'image/gif') {
			imagegif($image_target, $filename);
		}
		else {
			imagejpeg($image_target, $filename);
		}
	}
	
	function attin_tag($bot_id, $base_image_url, $tag_image_url, $filename, $item_data)
	{
		$base_image_url = $this->_get_original_aws_url($base_image_url);
		$content_type = $this->_get_content_type($base_image_url);

		$base_image = $this->_imagecreatefrom($base_image_url);
		$width = imagesx($base_image);
		$height = imagesy($base_image);
		
		$start_left = 0;
		$start_top = 0;
		$tag_image = $this->_imagecreatefrom($tag_image_url);
		$tag_width = imagesx($tag_image);
		$tag_height = imagesy($tag_image);
		$start_left = $width - $tag_width;
		imagecopymerge(
				// destination
				$base_image,
				// source
				$tag_image,
				// destination x and y
				$start_left, $start_top,
				// source x and y
				0, 0,
				// width and height of the area of the source to copy
				$tag_width, $tag_height,
				85);
		imageDestroy($tag_image);
		
		$start_top = 90 - 10;
		$text = $item_data["room_type"];
		$font_path = APPPATH . "../assets/common/font";
		$front_brush = imagecolorallocate($base_image, 255, 255, 255);
		$font = $font_path . "/arialbd.ttf";
		$fontSize = 70;
		$fontBox = imagettfbbox($fontSize, 0, $font, $text);
		$diff = ($tag_width - $fontBox[2]) / 2;
		imagettftext($base_image, $fontSize, 0, $start_left + $diff, $start_top, $front_brush, $font, $text);
		
		$start_top = $tag_height - 19;
		
		//$text = $item_data["price"];
		$text = number_format(intval(str_replace(',', '', $item_data["price"])));
		$front_brush = imagecolorallocate($base_image, 0, 0, 0);
		$dummy_brush = imagecolorallocate($base_image,255,255,255);
		$font = $font_path . "/msgothic.ttc";
		$fontSize = 52;
		imagettftext($base_image, $fontSize, 0, $start_left, $start_top, $dummy_brush, $font, "￥");
		imagettftext($base_image, $fontSize, 0, $start_left, $start_top, $front_brush, $font, "￥");
		imagettftext($base_image, $fontSize, 0, $width - $fontSize - 10, $start_top, $front_brush, $font, "～");
		
		$text = str_replace('￥', '', $text);
		$text = str_replace('～', '', $text);
		$font = $font_path . "/arialbd.ttf";
		$fontBox = imagettfbbox($fontSize, 0, $font, $text);
		$diff = ($tag_width - $fontBox[2]) / 2;
		imagettftext($base_image, $fontSize, 0, $start_left + $diff, $start_top, $front_brush, $font, $text);
		
		/*
		$text = $item_data["room_type"];
		$back_brush = imagecolorallocatealpha($base_image,243,146,49,25);
		imagefilledrectangle($base_image, $width*3/5, 0, $width, $height*1/4,$back_brush);
		$front_brush = imagecolorallocate($base_image, 255, 255, 255);
		$font = $font_path . "/arialbd.ttf";
		$fontSize = $height*1/6;
		$fontBox = imagettfbbox($fontSize, 0, $font, $text);
		$diff = ($width*2/5 - $fontBox[2]) / 2;
		imagettftext($base_image, $fontSize, 0, $width*3/5+$diff, $height*1/5, $front_brush, $font, $text);
		
		$text = $item_data["price"];
		$back_brush = imagecolorallocatealpha($base_image,255,255,255,25);
		imagefilledrectangle($base_image, $width*3/5, $height*1/4, $width, $height*1/4+$height*1/5,$back_brush);
		$front_brush = imagecolorallocate($base_image, 0, 0, 0);
		$dummy_brush = imagecolorallocate($base_image,255,255,255);		
		$font= $font_path . "/msgothic.ttc";
		$fontSize = $height*1/11;
		imagettftext($base_image, $fontSize, 0, $width*3/5, $height*1/4+$height*1/7, $dummy_brush, $font, "￥");
		imagettftext($base_image, $fontSize, 0, $width*3/5, $height*1/4+$height*1/7, $front_brush, $font, "￥");
		imagettftext($base_image, $fontSize, 0, $width - $fontSize - 10, $height*1/4+$height*1/7, $front_brush, $font, "～");
		
		$text = str_replace('￥', '', $text);
		$text = str_replace('～', '', $text);
		$font = $font_path . "/arialbd.ttf";
		$fontBox = imagettfbbox($fontSize, 0, $font, $text);
		$diff = ($width*2/5 - $fontBox[2]) / 2;
		imagettftext($base_image, $fontSize, 0, $width*3/5+$diff, $height*1/4+$height*1/7, $front_brush, $font, $text);
		*/
		
		$stream = $this->_image($content_type, $base_image);

		$result = $this->s3client->putObject([
				'ACL' => 'public-read',
				'Bucket' => $this->bucket,
				'Key' => $bot_id . '/' . $filename,
				'Body' => $stream,
				'ContentType' => $content_type,
		]);
		imageDestroy($base_image);
		return "$this->odn_url$bot_id/$filename";
	}
}

?>
