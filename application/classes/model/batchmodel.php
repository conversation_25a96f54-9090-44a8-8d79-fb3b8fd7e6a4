<?php defined('SYSPATH') OR die('No direct script access.');

class Model_Batchmodel extends Model_Basemodel
{
	function osaka_info($item_id, $lang_cd, $line) {
		$pos = strpos($line, '<div class="page-title">');
		$pos_last = strpos($line, '<div class="page-details">');
		if ($pos === false) return 0;
		$pos_h1 = strpos($line, '<h1>', $pos);
		if ($pos_h1 === false) return 0;
		$pos_h1_end = strpos($line, '</h1>', $pos_h1);
		if ($pos_h1_end === false) return 0;
		$title = substr($line, $pos_h1 + 4, $pos_h1_end - $pos_h1 - 4);
		$pos_a = strpos($title, '<a ');
		if ($pos_a !== false) {
			$title = substr($title, 0, $pos_a);
		}
		$content = '';
		$pos_h2 = strpos($line, '<h2>', $pos);
		if ($pos_h2 !== false && $pos_h2 < $pos_last) {
			$pos_h2_end = strpos($line, '</h2>', $pos_h2);
			if ($pos_h2_end === false) return 0;
			$content = substr($line, $pos_h2 + 4, $pos_h2_end - $pos_h2 - 4);
			$title = str_replace('<br />', PHP_EOL, $title);
			$title = strip_tags($title);
			$content = str_replace('<br />', PHP_EOL, $content);
			$content= strip_tags($content);
		}
		$arr = [];
		$desc = ORM::factory("itemdescription")->where('item_id', '=', $item_id)->where('lang_cd', '=', $lang_cd)->find();
		if ($desc->btn1_url_lang_cd == '' || $desc->btn1_url_lang_cd == $lang_cd) {
			if (trim($desc->item_name) != trim($title) || trim($desc->sell_point) != trim($content)) {
				$this->write_log("-----osakainfo update item info item_id=" . $item_id . " lang_cd=" . $lang_cd );
				if (trim($title) != '') $arr['item_name'] = $title;
				if (trim($content) != '') $arr['sell_point'] = $content;
				DB::update('t_item_description')->set($arr)->where('item_id', '=', $item_id)->where('lang_cd', '=', $lang_cd)->execute();
				$orm = ORM::factory("item", $item_id);
				$orm->end_date = NULL;
				$orm->upd_time = date('Y-m-d H:i:s');
				$orm->upd_user = 0;
				$orm->save();
				return 1;
			}
		}
		return 0;
	}

	function get_valid_item($bot_id, $end_date)
	{
		$sql = "SELECT link_id, item_id FROM t_item WHERE bot_id=:bot_id AND link_id !='' AND (end_date is NULL OR end_date > :end_date)";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':bot_id' => $bot_id,
			':end_date' => $end_date,
		));
		$results = $query->execute()->as_array();
		return $results;
	}

	function get_max_item_id($bot)
	{
		//$sql = "SELECT MAX(item_cd) AS max_item_cd FROM t_item WHERE bot_id is NULL AND item_cd LIKE '$bot_id%'";
		$bot_id = $bot->bot_id;
		
		if ($bot->start_date >= '2020-04-01') {
			$sql = sprintf("SELECT MAX(item_id) AS max_id FROM t_item WHERE item_id LIKE '%u____'", $bot_id);
			$query = DB::query(Database::SELECT, $sql);
			$results = $query->execute()->as_array();
			if ($results[0]['max_id'] == NULL) {
				return number_format(intval($bot_id) * 10000 + 1, 0, '.' ,'');
			}
			else {
				return number_format(floatval($results[0]['max_id']) + 1, 0, '.' ,'');
			}
		}
		else {
			$sql = sprintf("SELECT MAX(item_id) AS max_id FROM t_item WHERE item_id LIKE '%u___'", $bot_id);
			$query = DB::query(Database::SELECT, $sql);
			$results = $query->execute()->as_array();
			if ($results[0]['max_id'] == NULL) {
				return intval($bot_id) * 1000 + 1;
			}
			else {
				return intval($results[0]['max_id']) + 1;
			}
		}
		/*
		else {
			$sql = "SELECT MAX(item_id) AS max_id FROM t_item WHERE bot_id = " . $bot_id;
			$query = DB::query(Database::SELECT, $sql);
			$results = $query->execute()->as_array();
			if ($results[0]['max_id'] == NULL) {
				return 1;
			}
			else {
				return intval($results[0]['max_id']) + 1;
			}
		}
		*/
	}
	function get_member_logs_csv($bot_id, $member_id, $start_date=NULL, $end_date=NULL, $table='t_bot_log_chat', $page_size=NULL)
	{
		if ($end_date != NULL) $end_date = date("Y-m-d",strtotime("+1 day",strtotime($end_date)));
		
		$sql = "SELECT CONCAT(b.last_name, b.first_name) AS member_name, c.name, b.is_tester,
				a.member_id, a.sns_type_cd, a.log_time, a.lang_cd, a.member_msg, a.member_msg_t, a.bot_msg, a.bot_msg_t, a.bot_id, a.intent_cd 
                 FROM $table a LEFT JOIN t_bot_member b ON
					a.member_id = b.member_id AND
					a.bot_id = b.bot_id
					LEFT JOIN t_user c ON c.user_id = a.user_id
				WHERE 1 = 1 ";
		if ($bot_id != NULL) $sql = $sql . " AND a.bot_id = :bot_id ";
		if ($member_id != NULL) $sql = $sql . " AND a.member_id = :member_id ";
		if ($start_date!= NULL) {
			if (strlen($start_date) == 10)
				$sql = $sql . " AND a.log_time >= :start_date ";
			else 
				$sql = $sql . " AND a.log_time > :start_date ";
		}
		if ($end_date!= NULL) $sql = $sql . " AND a.log_time < :end_date ";
		$sql = $sql . " ORDER BY a.log_time";
		if ($page_size!==NULL) {
			$sql = $sql . " LIMIT 0," . $page_size;
		}
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
				':member_id' => $member_id,
				':start_date' => $start_date,
				':end_date' => $end_date,
		));
		$results = $query->execute('slave');
		//Log::instance()->add(Log::DEBUG, "execute end:" . date('H:i:s'));
		$result_array = $results->as_array();
		//Log::instance()->add(Log::DEBUG, "array end:" . date('H:i:s'));
		return $result_array;
	}
	
	function get_member_logs_count($bot_id, $member_id, $start_date=NULL, $end_date=NULL, $table='t_bot_log_chat')
	{
		if ($end_date != NULL) $end_date = date("Y-m-d",strtotime("+1 day",strtotime($end_date)));
		
		$sql = "SELECT COUNT(1) AS log_count FROM $table a WHERE 1=1 ";
		if ($bot_id != NULL) $sql = $sql . " AND a.bot_id = :bot_id ";
		if ($member_id != NULL) $sql = $sql . " AND a.member_id = :member_id ";
		if ($start_date!= NULL) $sql = $sql . " AND a.log_time >= :start_date ";
		if ($end_date!= NULL) $sql = $sql . " AND a.log_time < :end_date ";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
				':member_id' => $member_id,
				':start_date' => $start_date,
				':end_date' => $end_date,
		));
		$results = $query->execute('slave')->as_array();
		return $results[0]['log_count'];
	}
	
	/*
	function init_daily_report($bot_id = 0, $end_date = '2020-02-15')
	{
		$start_date = '2017-01-01';
		$last_data_array = array();
		$statistics_array = $this->get_config('statistics');
		
		if ($bot_id == 0) {
			DB::delete('t_bot_statistics')->execute('slave');
		}
		else {
			DB::delete('t_bot_statistics')->where('bot_id', '=', $bot_id)->execute('slave');
		}
		
		if ($bot_id == 0) {
			// 全BOT
			$sql = "SELECT bot_id, start_date FROM t_bot WHERE delete_flg=0 AND bot_id<>0 ORDER BY bot_id";
		}
		else {
			$sql = "SELECT bot_id, start_date FROM t_bot WHERE delete_flg=0 AND bot_id=" . $bot_id;
		}
		$query = DB::query(Database::SELECT, $sql);
		//$query->parameters($params);
		$result = $query->execute('slave')->as_array();
		foreach($result as $data) {
			$bot_id = $data['bot_id'];
			$start_date = $data['start_date'];
			$this->write_log("------- bot start ------ " . $start_date . '-' . $bot_id);
			$last_data_array = array();
			while($start_date <= $end_date) {
				$this->write_log($start_date . '-' . $bot_id);
				$params = array(
						':start_date' => $start_date,
						':end_date' => date("Y-m-d",strtotime("+1 day",strtotime($start_date))),
						':intent_cd' => 'input.unknown',
						':score_min' => 0,
						':score_max' => 0.85,
						':bot_id' => $bot_id,
				);
				$data_array= array();
				// お客様人数
				//$total_members = DB::select(array(DB::expr('COUNT(`member_id`)'), 'total_members'))->from('t_bot_member')->where('bot_id', '=', $bot_id)->where('regist_date', '>=', date('Y-m-d'))->where('regist_date', '>=', $start_date)->where('is_tester', '=', 0)->execute('slave')->get('total_members', 0);
				$sql = "SELECT bot_id, COUNT(member_id) AS member_count FROM t_bot_member WHERE regist_date >= :start_date AND regist_date < :end_date AND bot_id=:bot_id";
				$query = DB::query(Database::SELECT, $sql);
				$query->parameters($params);
				$result = $query->execute('slave')->as_array();
				$data_array['01'] = [strval($bot_id)=>$result[0]['member_count']];
				if ($result[0]['member_count'] > 0) {
					$new='true';
				}
				//$data_array['01'] = result;
				// お客様人数REPEAT
				$sql = "SELECT t.bot_id, COUNT(DISTINCT t.member_id) AS member_count FROM t_bot_log$this->_log_ext  t INNER JOIN t_bot_member m ON t.bot_id = m.bot_id AND t.member_id = m.member_id AND m.regist_date < :start_date WHERE t.bot_id=:bot_id AND t.member_msg <> '' AND log_time >= :start_date AND log_time < :end_date";
				$query = DB::query(Database::SELECT, $sql);
				$query->parameters($params);
				$result = $query->execute('slave')->as_array();
				$data_array['02'] = [strval($bot_id)=>$result[0]['member_count']];
				//　全件数
				$sql = "SELECT bot_id, COUNT(log_id) AS log_count FROM t_bot_log$this->_log_ext  log WHERE log.member_msg <> '' AND log.log_time >= :start_date AND log.log_time < :end_date AND bot_id=:bot_id";
				$query = DB::query(Database::SELECT, $sql);
				$query->parameters($params);
				$result = $query->execute('slave')->as_array();
				$data_array['11'] = [strval($bot_id)=>$result[0]['log_count']];
				// 返答件数
				$sql = "SELECT bot_id, COUNT(log_id) AS log_count FROM t_bot_log$this->_log_ext  log WHERE log.member_msg <> '' AND intent_cd<>:intent_cd AND log.log_time >= :start_date AND log.log_time < :end_date AND bot_id=:bot_id";
				$query = DB::query(Database::SELECT, $sql);
				$query->parameters($params);
				$result = $query->execute('slave')->as_array();
				$data_array['12'] = [strval($bot_id)=>$result[0]['log_count']];
				// 正解回答
				$sql = "SELECT bot_id, COUNT(log_id) AS log_count FROM t_bot_log$this->_log_ext  log WHERE log.member_msg <> '' AND (score>=:score_max OR score=0 AND intent_cd<>'input.unknown') AND log.log_time >= :start_date AND log.log_time < :end_date AND bot_id=:bot_id";
				$query = DB::query(Database::SELECT, $sql);
				$query->parameters($params);
				$result = $query->execute('slave')->as_array();
				$data_array['13'] = [strval($bot_id)=>$result[0]['log_count']];
	
				$week = date('w', strtotime($start_date));
				$day = date('d', strtotime($start_date));
				
				foreach($statistics_array as $data_type_cd=>$value) {
					$orm = ORM::factory('botstatistics');
					$orm->bot_id = $bot_id;
					$orm->data_type_cd = $data_type_cd;
					$orm->start_date = $start_date;
					$key_type = substr($data_type_cd, 0, 1);
					$key_value = substr($data_type_cd, 1);
					if ($key_type == "A") {
						$orm->value = $this->_get_init_step_value($bot_id, $data_type_cd, $data_array, $last_data_array);
					}
					else if ($key_type == "M") {
						if ($day == '01') {
							$orm->value = $this->_get_value($bot_id, $data_type_cd, $data_array);
						}
						else {
							$orm->value = $this->_get_init_step_value($bot_id, $data_type_cd, $data_array, $last_data_array);
						}
					}
					else if ($key_type == "W") {
						if ($week == 1) {
							$orm->value = $this->_get_value($bot_id, $data_type_cd, $data_array);
						}
						else {
							$orm->value = $this->_get_init_step_value($bot_id, $data_type_cd, $data_array, $last_data_array);
						}
					}
					else {
						$orm->value = $this->_get_value($bot_id, $data_type_cd, $data_array);
					}
					$last_data_array[$data_type_cd] = $orm->value;
					$orm->save();					
				}
				$start_date = date("Y-m-d",strtotime("+1 day",strtotime($start_date)));
			}
			$this->write_log("------- bot end ------ " . $end_date . '-' . $bot_id);
		}
	}
	*/
	
	function create_report_orm($bot_id, $report_date, $report_type_cd, $content) {
		$orm = ORM::factory('botreport');
		$orm->bot_id = $bot_id;
		$orm->report_type_cd = $report_type_cd;
		$orm->report_date = $report_date;
		$orm->content = $content;
		$orm->save();
	}
	
	function create_bot_report_ranking($bot_id, $start_date, $end_date, $context_id=NULL, $lang_cd="", $count=10)
	{
		/*
		$sql="SELECT COUNT(*) AS c, a.intent_cd, a.sub_intent_cd, '' AS s, '' AS no_c, b.question, b.lang_cd FROM t_bot_log$this->_log_ext a
				INNER JOIN m_bot_intent b ON a.intent_cd = b.intent_cd AND a.sub_intent_cd = b.sub_intent_cd
				WHERE " . $this->_create_bot_cond($bot_id) . " AND a.intent_cd LIKE 'inquiry.%' AND a.member_msg<>'' AND a.score > 0 AND score <=1 ";
		if ($start_date != NULL) $sql = $sql . " AND a.log_time>=:start_date ";
		if ($end_date != NULL) $sql = $sql . " AND a.log_time<:end_date ";
		if ($lang_cd != '') $sql = $sql . " AND a.lang_cd=:lang_cd ";
		$sql = $sql . " GROUP BY a.intent_cd, a.sub_intent_cd, b.question, b.lang_cd ORDER BY c DESC, a.intent_cd, a.sub_intent_cd, b.lang_cd LIMIT 0, 48";
*/
		$sql="SELECT COUNT(*) AS c, intent_cd, sub_intent_cd FROM t_bot_log$this->_log_ext 
		WHERE " . $this->_create_bot_cond($bot_id) . " AND intent_cd LIKE 'inquiry.%' AND member_msg<>'' AND score > 0 AND score <=1 AND answer_type > 0 ";
		if ($start_date != NULL) $sql = $sql . " AND log_time>=:start_date ";
		if ($end_date != NULL) $sql = $sql . " AND log_time<:end_date ";
		if ($context_id != NULL) $sql = $sql . " AND context_id=:context_id ";
		if ($lang_cd != '') $sql = $sql . " AND lang_cd=:lang_cd ";

		if ($count == -1) {
			$sql = $sql . " GROUP BY intent_cd, sub_intent_cd ORDER BY c DESC, intent_cd, sub_intent_cd ";
		}
		else {
			$sql = $sql . " GROUP BY intent_cd, sub_intent_cd ORDER BY c DESC, intent_cd, sub_intent_cd LIMIT 0, " . $count;
		}
		$sql = "SELECT a.c, a.intent_cd, a.sub_intent_cd, '' AS s, '' AS no_c, b.question, t.facility_question_title, b.lang_cd FROM ($sql) a 
		INNER JOIN m_bot_intent b ON a.intent_cd = b.intent_cd AND a.sub_intent_cd = b.sub_intent_cd ";
		if ($lang_cd != '') $sql = $sql . " AND b.lang_cd=:lang_cd ";
		// 日本学生支援機構
		if ($bot_id == 701001) {
			$sql = $sql . " AND b.intent_type_cd NOT LIKE '09%' ";
		}
		$sql = $sql . " LEFT JOIN t_bot_intent t ON a.intent_cd = t.intent_cd AND a.sub_intent_cd = t.sub_intent_cd AND t.lang_cd=b.lang_cd AND t.bot_id=:bot_id
		ORDER BY a.c DESC, a.intent_cd, a.sub_intent_cd, b.lang_cd";
		
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
				':lang_cd' => $lang_cd,
				':start_date' => $start_date,
				':end_date' => $end_date,
				':context_id' => $context_id,
		));
		$logs = $query->execute()->as_array();
		$ret = [];
		$bot_arr = [];
		$grp_bot_id = $this->get_grp_bot_id($bot_id);
		if ($grp_bot_id > 0) {
			$bot_arr[] = $bot_id;
			$template_bot = $this->get_bot_setting_self($bot_id, 'template_bot');
			if ($template_bot != '') $bot_arr[] = $template_bot;
			$bot_arr[] = $grp_bot_id;
		}
		foreach($logs as $log) {
			$record = [];
			$record['intent_cd'] = $log['intent_cd'];
			$record['sub_intent_cd'] = $log['sub_intent_cd'];
			$record['lang_cd'] = $log['lang_cd'];
			//$record['context_id'] = $log['context_id'];
			if ($grp_bot_id > 0) {
				$record['question'] = $this->get_faq_question_title($bot_arr, $log['intent_cd'], $log['sub_intent_cd'], $log['lang_cd']);
				if ($record['question'] == '') $record['question'] = $log['question'];
			}
			else {
				if ($log['facility_question_title'] == '') {
					$record['question'] = $log['question'];
				}
				else {
					$record['question'] = $log['facility_question_title'];
				}
			}
			$ret[] = $record;
		}
		return $ret;
	}
	
	function get_faq_question_title($bot_arr, $intent_cd, $sub_intent_cd, $lang_cd) 
	{
		$first = true;
		foreach($bot_arr as $bot_id) {
			$sql = "SELECT facility_question_title, inherit FROM t_bot_intent t WHERE bot_id=:bot_id AND intent_cd=:intent_cd AND sub_intent_cd=:sub_intent_cd AND lang_cd=:lang_cd";
			$query = DB::query(Database::SELECT, $sql);
			$query->parameters(array(
					':bot_id' => $bot_id,
					':intent_cd' => $intent_cd,
					':sub_intent_cd' => $sub_intent_cd,
					':lang_cd' => $lang_cd,
			));
			$logs = $query->execute()->as_array();
			if (count($logs) > 0) {
				$log = $logs[0];
				if ($first) {
					if ($log['inherit'] == 0) return $log['facility_question_title'];
				}
				else {
					if ($log['facility_question_title'] != '') return $log['facility_question_title'];
				}
			}
		}
		return '';
	}

	function create_bot_report_freq($bot_id, $start_date, $end_date, $lang_cd='')
	{
		$sql="SELECT COUNT(*) AS c, intent_cd, sub_intent_cd FROM t_bot_log$this->_log_ext
		WHERE " . $this->_create_bot_cond($bot_id) . " AND intent_cd LIKE 'inquiry.%' AND member_msg<>'' AND score > 0 AND score <=1 AND answer_type > 0 ";
		if ($start_date != NULL) $sql = $sql . " AND log_time>=:start_date ";
		if ($end_date != NULL) $sql = $sql . " AND log_time<:end_date ";
		if ($lang_cd != '') $sql = $sql . " AND lang_cd=:lang_cd ";
		$sql = $sql . " GROUP BY intent_cd, sub_intent_cd ORDER BY c DESC, intent_cd, sub_intent_cd";

		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
				':lang_cd' => $lang_cd,
				':start_date' => $start_date,
				':end_date' => $end_date,
		));
		$log = $query->execute()->as_array();
		return $log;
	}
	
	function create_bot_report_repeat($bot_id, $start_date, $end_date, $lang_cd='')
	{
		$end_date = date("Y-m-d",strtotime("+1 day",strtotime($end_date)));
		$sql="SELECT COUNT(DISTINCT member_id) AS repeater FROM t_bot_log$this->_log_ext
		WHERE " . $this->_create_bot_cond($bot_id);
		if ($start_date != NULL) $sql = $sql . " AND log_time>=:start_date ";
		if ($end_date != NULL) $sql = $sql . " AND log_time<:end_date ";
		if ($lang_cd != '') $sql = $sql . " AND lang_cd=:lang_cd ";
		//$sql = $sql . " GROUP BY intent_cd, sub_intent_cd ORDER BY c DESC, intent_cd, sub_intent_cd LIMIT 0, 8";
		
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
				':lang_cd' => $lang_cd,
				':start_date' => $start_date,
				':end_date' => $end_date,
		));
		$log = $query->execute()->as_array();
		return $log;
	}
	
	function create_bot_report_daily($bot_id, $start_date)
	{
		$last_date = date("Y-m-d",strtotime("-1 day",strtotime($start_date)));
		$total_data_array = $this->get_bot_report($bot_id, $last_date, '02');
		if ($total_data_array == NULL) {
			$total_data_array = [
					'all'=>[
							'member' =>0,
							'repeater' =>0,
							'log' =>0,
							'log_answer' =>0,
							'log_correct' =>0],
					'month'=>[
							'member' =>0,
							'repeater' =>0,
							'log' =>0,
							'log_answer' =>0,
							'log_correct' =>0],
					'week'=>[
							'member' =>0,
							'repeater' =>0,
							'log' =>0,
							'log_answer' =>0,
							'log_correct' =>0],
					'day'=>[
							'member' =>0,
							'repeater' =>0,
							'log' =>0,
							'log_answer' =>0,
							'log_correct' =>0],
			];
		}
		$params = array(
				':start_date' => $start_date,
				':end_date' => date("Y-m-d",strtotime("+1 day",strtotime($start_date))),
				':intent_cd' => 'input.unknown',
				':score_min' => 0,
				':score_max' => 0.85,
				':bot_id' => $bot_id,
		);
		
		$bot_id_tbl = $this->get_log_table($bot_id);
		
		$data_array= array();
		// お客様人数
		//$total_members = DB::select(array(DB::expr('COUNT(`member_id`)'), 'total_members'))->from('t_bot_member')->where('bot_id', '=', $bot_id)->where('regist_date', '>=', date('Y-m-d'))->where('regist_date', '>=', $start_date)->where('is_tester', '=', 0)->execute('slave')->get('total_members', 0);
		$sql = "SELECT bot_id, COUNT(member_id) AS member_count FROM t_bot_member WHERE is_tester=0 AND regist_date >= :start_date AND regist_date < :end_date AND bot_id=:bot_id";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters($params);
		$result = $query->execute('slave')->as_array();
		$data_array['member'] = $result[0]['member_count'];
		//$data_array['01'] = result;
		// お客様人数REPEAT
		$sql = "SELECT t.bot_id, COUNT(DISTINCT t.member_id) AS member_count FROM t_bot_log$bot_id_tbl t INNER JOIN t_bot_member m ON t.bot_id = m.bot_id AND t.member_id = m.member_id AND m.is_tester=0 AND m.regist_date < :start_date WHERE t.bot_id=:bot_id AND t.member_msg <> '' AND log_time >= :start_date AND log_time < :end_date";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters($params);
		$result = $query->execute('slave')->as_array();
		$data_array['repeater'] = $result[0]['member_count'];
		
		//　全件数
		$sql = "SELECT bot_id, COUNT(log_id) AS log_count FROM t_bot_log$bot_id_tbl log WHERE log.is_tester=0 AND log.member_msg <> '' AND log.log_time >= :start_date AND log.log_time < :end_date AND bot_id=:bot_id";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters($params);
		$result = $query->execute('slave')->as_array();
		$data_array['log'] = $result[0]['log_count'];
		
		/*
		//　有効全件数
		$sql = "SELECT bot_id, COUNT(log_id) AS log_count FROM t_bot_log$bot_id_tbl log WHERE log.is_tester=0 AND log.member_msg <> '' AND intent_cd<>'welcome' AND intent_cd<>'member_start_chat' AND intent_cd<>'member_end_chat' AND log.log_time >= :start_date AND log.log_time < :end_date AND bot_id=:bot_id";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters($params);
		$result = $query->execute('slave')->as_array();
		$data_array['reallog'] = $result[0]['log_count'];
		
		// 返答件数
		$sql = "SELECT bot_id, COUNT(log_id) AS log_count FROM t_bot_log$bot_id_tbl log WHERE log.is_tester=0 AND log.member_msg <> '' AND intent_cd=:intent_cd AND log.log_time >= :start_date AND log.log_time < :end_date AND bot_id=:bot_id";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters($params);
		$result = $query->execute('slave')->as_array();
		$data_array['log_answer'] = $data_array['reallog'] - $result[0]['log_count'];
		*/
		
		// 返答件数
		$sql = "SELECT bot_id, COUNT(log_id) AS log_count FROM t_bot_log$bot_id_tbl log WHERE log.is_tester=0 AND log.member_msg <> '' AND intent_cd<>:intent_cd AND log.log_time >= :start_date AND log.log_time < :end_date AND bot_id=:bot_id";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters($params);
		$result = $query->execute('slave')->as_array();
		$data_array['log_answer'] = $result[0]['log_count'];
		
		// 正解回答
		$sql = "SELECT bot_id, COUNT(log_id) AS log_count FROM t_bot_log$bot_id_tbl log WHERE log.is_tester=0 AND log.member_msg <> '' AND (score>=:score_max OR score=0 AND intent_cd<>'input.unknown') AND log.log_time >= :start_date AND log.log_time < :end_date AND bot_id=:bot_id";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters($params);
		$result = $query->execute('slave')->as_array();
		$data_array['log_correct'] = $result[0]['log_count'];
		//リクエスト件数
		
		//予約件数
		
		$week = date('w', strtotime($start_date));
		$day = date('d', strtotime($start_date));
		
		$total_data_array['all']['member'] = $total_data_array['all']['member'] + $data_array['member'];
		$total_data_array['all']['repeater'] = $total_data_array['all']['repeater'] + $data_array['repeater'];
		$total_data_array['all']['log'] = $total_data_array['all']['log'] + $data_array['log'];
		$total_data_array['all']['log_answer'] = $total_data_array['all']['log_answer'] + $data_array['log_answer'];
		$total_data_array['all']['log_correct'] = $total_data_array['all']['log_correct'] + $data_array['log_correct'];
		if ($day == '01') {
			$total_data_array['month'] = $data_array;
		}
		else {
			$total_data_array['month']['member'] = $total_data_array['month']['member'] + $data_array['member'];
			$total_data_array['month']['repeater'] = $total_data_array['month']['repeater'] + $data_array['repeater'];
			$total_data_array['month']['log'] = $total_data_array['month']['log'] + $data_array['log'];
			$total_data_array['month']['log_answer'] = $total_data_array['month']['log_answer'] + $data_array['log_answer'];
			$total_data_array['month']['log_correct'] = $total_data_array['month']['log_correct'] + $data_array['log_correct'];
		}
		if ($week == 1) {
			$total_data_array['week'] = $data_array;
		}
		else {
			$total_data_array['week']['member'] = $total_data_array['week']['member'] + $data_array['member'];
			$total_data_array['week']['repeater'] = $total_data_array['week']['repeater'] + $data_array['repeater'];
			$total_data_array['week']['log'] = $total_data_array['week']['log'] + $data_array['log'];
			$total_data_array['week']['log_answer'] = $total_data_array['week']['log_answer'] + $data_array['log_answer'];
			$total_data_array['week']['log_correct'] = $total_data_array['week']['log_correct'] + $data_array['log_correct'];
		}
		$total_data_array['day'] = $data_array;
		$total_data_array['upd_time'] = date('Y-m-d H:i:s');
		return $total_data_array;
	}
	
	function init_bot_report($bot_id = 0, $end_date = '')
	{
		$start_date = '2017-01-01';
		$last_data_array = array();
		$statistics_array = $this->get_config('statistics');
		
		if ($bot_id == 0) {
			// 全BOT
			$sql = "SELECT bot_id, start_date FROM t_bot WHERE delete_flg=0 AND bot_id>0 ORDER BY bot_id";
		}
		else {
			$sql = "SELECT bot_id, start_date FROM t_bot WHERE delete_flg=0 AND bot_id=" . $bot_id;
		}
		$query = DB::query(Database::SELECT, $sql);
		//$query->parameters($params);
		$result = $query->execute('slave')->as_array();
		foreach($result as $data) {
			$bot_id = $data['bot_id'];
			$start_date = $data['start_date'];
			$this->write_log("------- bot start ------ " . $start_date . '-' . $bot_id);
			$total_data_array = [
					'all'=>[
							'member' =>0,
							'repeater' =>0,
							'log' =>0,
							'log_answer' =>0,
							'log_correct' =>0],
					'month'=>[
							'member' =>0,
							'repeater' =>0,
							'log' =>0,
							'log_answer' =>0,
							'log_correct' =>0],
					'week'=>[
							'member' =>0,
							'repeater' =>0,
							'log' =>0,
							'log_answer' =>0,
							'log_correct' =>0],
					'day'=>[
							'member' =>0,
							'repeater' =>0,
							'log' =>0,
							'log_answer' =>0,
							'log_correct' =>0],
			];
			while($start_date <= $end_date) {
				$this->write_log($start_date . '-' . $bot_id);
				$params = array(
						':start_date' => $start_date,
						':end_date' => date("Y-m-d",strtotime("+1 day",strtotime($start_date))),
						':intent_cd' => 'input.unknown',
						':score_min' => 0,
						':score_max' => 0.85,
						':bot_id' => $bot_id,
				);
				
				$bot_id_tbl = $this->get_log_table($bot_id);
				$data_array= array();
				// お客様人数
				//$total_members = DB::select(array(DB::expr('COUNT(`member_id`)'), 'total_members'))->from('t_bot_member')->where('bot_id', '=', $bot_id)->where('regist_date', '>=', date('Y-m-d'))->where('regist_date', '>=', $start_date)->where('is_tester', '=', 0)->execute('slave')->get('total_members', 0);
				$sql = "SELECT bot_id, COUNT(member_id) AS member_count FROM t_bot_member WHERE is_tester=0 AND regist_date >= :start_date AND regist_date < :end_date AND bot_id=:bot_id";
				$query = DB::query(Database::SELECT, $sql);
				$query->parameters($params);
				$result = $query->execute('slave')->as_array();
				$data_array['member'] = $result[0]['member_count'];
				//$data_array['01'] = result;
				// お客様人数REPEAT
				$sql = "SELECT t.bot_id, COUNT(DISTINCT t.member_id) AS member_count FROM t_bot_log$bot_id_tbl t INNER JOIN t_bot_member m ON t.bot_id = m.bot_id AND t.member_id = m.member_id AND m.regist_date < :start_date WHERE t.bot_id=:bot_id AND t.is_tester=0 AND t.member_msg <> '' AND log_time >= :start_date AND log_time < :end_date";
				$query = DB::query(Database::SELECT, $sql);
				$query->parameters($params);
				$result = $query->execute('slave')->as_array();
				$data_array['repeater'] = $result[0]['member_count'];
				//　全件数
				$sql = "SELECT bot_id, COUNT(log_id) AS log_count FROM t_bot_log$bot_id_tbl log WHERE log.is_tester=0 AND log.member_msg <> '' AND log.log_time >= :start_date AND log.log_time < :end_date AND bot_id=:bot_id";
				$query = DB::query(Database::SELECT, $sql);
				$query->parameters($params);
				$result = $query->execute('slave')->as_array();
				$data_array['log'] = $result[0]['log_count'];
				// 返答件数
				$sql = "SELECT bot_id, COUNT(log_id) AS log_count FROM t_bot_log$bot_id_tbl log WHERE log.is_tester=0 AND log.member_msg <> '' AND intent_cd<>:intent_cd AND log.log_time >= :start_date AND log.log_time < :end_date AND bot_id=:bot_id";
				$query = DB::query(Database::SELECT, $sql);
				$query->parameters($params);
				$result = $query->execute('slave')->as_array();
				$data_array['log_answer'] = $result[0]['log_count'];
				// 正解回答
				$sql = "SELECT bot_id, COUNT(log_id) AS log_count FROM t_bot_log$bot_id_tbl log WHERE log.is_tester=0 AND log.member_msg <> '' AND (score>=:score_max OR score=0 AND intent_cd<>'input.unknown') AND log.log_time >= :start_date AND log.log_time < :end_date AND bot_id=:bot_id";
				$query = DB::query(Database::SELECT, $sql);
				$query->parameters($params);
				$result = $query->execute('slave')->as_array();
				$data_array['log_correct'] = $result[0]['log_count'];
				
				$week = date('w', strtotime($start_date));
				$day = date('d', strtotime($start_date));
				
				$orm = ORM::factory('botreport');
				$orm->bot_id = $bot_id;
				$orm->report_type_cd = '02';
				$orm->report_date = $start_date;
				
				$total_data_array['all']['member'] = $total_data_array['all']['member'] + $data_array['member'];
				$total_data_array['all']['repeater'] = $total_data_array['all']['repeater'] + $data_array['repeater'];
				$total_data_array['all']['log'] = $total_data_array['all']['log'] + $data_array['log'];
				$total_data_array['all']['log_answer'] = $total_data_array['all']['log_answer'] + $data_array['log_answer'];
				$total_data_array['all']['log_correct'] = $total_data_array['all']['log_correct'] + $data_array['log_correct'];
				if ($day == '01') {
					$total_data_array['month'] = $data_array;
				}
				else {
					$total_data_array['month']['member'] = $total_data_array['month']['member'] + $data_array['member'];
					$total_data_array['month']['repeater'] = $total_data_array['month']['repeater'] + $data_array['repeater'];
					$total_data_array['month']['log'] = $total_data_array['month']['log'] + $data_array['log'];
					$total_data_array['month']['log_answer'] = $total_data_array['month']['log_answer'] + $data_array['log_answer'];
					$total_data_array['month']['log_correct'] = $total_data_array['month']['log_correct'] + $data_array['log_correct'];
				}
				if ($week == 1) {
					$total_data_array['week'] = $data_array;
				}
				else {
					$total_data_array['week']['member'] = $total_data_array['week']['member'] + $data_array['member'];
					$total_data_array['week']['repeater'] = $total_data_array['week']['repeater'] + $data_array['repeater'];
					$total_data_array['week']['log'] = $total_data_array['week']['log'] + $data_array['log'];
					$total_data_array['week']['log_answer'] = $total_data_array['week']['log_answer'] + $data_array['log_answer'];
					$total_data_array['week']['log_correct'] = $total_data_array['week']['log_correct'] + $data_array['log_correct'];
				}
				$total_data_array['day'] = $data_array;
				$orm->content = json_encode($total_data_array);
				$orm->save();
				$start_date = date("Y-m-d",strtotime("+1 day",strtotime($start_date)));
			}
			$this->write_log("------- bot end ------ " . $end_date . '-' . $bot_id);
		}
	}
	
	/*
	function create_daily_report($start_date, $bot_id=0, $table='t_bot_log_chat')
	{
		$end_date = $start_date;
		$end_date = date("Y-m-d",strtotime("+1 day",strtotime($end_date)));
		$last_date = date("Y-m-d",strtotime("-1 day",strtotime($start_date)));
		$data_array= array();
		$params = array(
				':start_date' => $start_date,
				':end_date' => $end_date,
				':intent_cd' => 'input.unknown',
				':score_min' => 0,
				':score_max' => 0.85,
				':bot_id' => $bot_id,
		);
		
		if ($bot_id ==0) {
			$groupby = ' GROUP BY bot_id';
			$groupby2 = ' GROUP BY t.bot_id';
		}
		else {
			$groupby = ' AND bot_id=:bot_id';
			$groupby2 = ' AND t.bot_id=:bot_id';
		}
		// お客様人数
		$sql = "SELECT bot_id, COUNT(member_id) AS member_count FROM t_bot_member WHERE regist_date >= :start_date AND regist_date < :end_date $groupby";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters($params);
		$result = $query->execute('slave')->as_array('bot_id', 'member_count');
		$data_array['01'] = $result;
		// お客様人数REPEAT
		$sql = "SELECT t.bot_id, COUNT(DISTINCT t.member_id) AS member_count FROM $table t INNER JOIN t_bot_member m ON t.bot_id = m.bot_id AND t.member_id = m.member_id AND m.regist_date < :start_date WHERE t.member_msg <> '' AND log_time >= :start_date AND log_time < :end_date $groupby2";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters($params);
		$result = $query->execute('slave')->as_array('bot_id', 'member_count');
		$data_array['02'] = $result;
		//　全件数
		$sql = "SELECT bot_id, COUNT(log_id) AS log_count FROM $table log WHERE log.member_msg <> '' AND log.log_time >= :start_date AND log.log_time < :end_date $groupby";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters($params);
		$result = $query->execute('slave')->as_array('bot_id', 'log_count');
		$data_array['11'] = $result;
		// 返答件数
		$sql = "SELECT bot_id, COUNT(log_id) AS log_count FROM $table log WHERE log.member_msg <> '' AND intent_cd<>:intent_cd AND log.log_time >= :start_date AND log.log_time < :end_date $groupby";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters($params);
		$result = $query->execute('slave')->as_array('bot_id', 'log_count');
		$data_array['12'] = $result;
		// 正解回答
		$sql = "SELECT bot_id, COUNT(log_id) AS log_count FROM $table log WHERE log.member_msg <> '' AND (score>=:score_max OR score=0 AND intent_cd<>'input.unknown') AND log.log_time >= :start_date AND log.log_time < :end_date $groupby";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters($params);
		$result = $query->execute('slave')->as_array('bot_id', 'log_count');
		$data_array['13'] = $result;
		
		// 前回データを取得
		if ($bot_id ==0) {
			$sql = "SELECT * FROM t_bot_statistics WHERE start_date = :start_date ORDER BY bot_id, data_type_cd";
		}
		else {
			$sql = "SELECT * FROM t_bot_statistics WHERE start_date = :start_date AND bot_id = $bot_id ORDER BY data_type_cd";
		}
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(':start_date'=>$last_date));
		$result = $query->execute('slave')->as_array();
		$last_data_array = array();
		foreach($result as $data) {
			$last_data_array[$data['bot_id'] . '-' . $data['data_type_cd']] = $data['value'];
		}
		
		// 全BOT
		if ($bot_id == 0) {
			$sql = "SELECT bot_id, start_date FROM t_bot WHERE start_date <= :start_date AND delete_flg=0 AND bot_id<>0 ORDER BY bot_id";
		}
		else {
			$sql = "SELECT bot_id, start_date FROM t_bot WHERE start_date <= :start_date AND bot_id=$bot_id";
		}
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters($params);
		$result = $query->execute('slave')->as_array();
		$statistics_array = $this->get_config('statistics');
		$week = date('w', strtotime($start_date));
		$day = date('d', strtotime($start_date));
		foreach($result as $data) {
			$this->write_log("DEBUG Create " . $start_date . '-' . $data['bot_id']);
			foreach($statistics_array as $data_type_cd=>$value) {
				$orm = ORM::factory('botstatistics');
				$orm->bot_id = $data['bot_id'];
				$orm->data_type_cd = $data_type_cd;
				$orm->start_date = $start_date;
				$key_type = substr($data_type_cd, 0, 1);
				$key_value = substr($data_type_cd, 1);
				if ($key_type == "A") {
					$orm->value = $this->_get_step_value($data['bot_id'], $data_type_cd, $data_array, $last_data_array);
				}
				else if ($key_type == "M") {
					if ($day == '01') {
						$orm->value = $this->_get_value($data['bot_id'], $data_type_cd, $data_array);
					}
					else {
						$orm->value = $this->_get_step_value($data['bot_id'], $data_type_cd, $data_array, $last_data_array);
					}
				}
				else if ($key_type == "W") {
					if ($week == 1) {
						$orm->value = $this->_get_value($data['bot_id'], $data_type_cd, $data_array);
					}
					else {
						$orm->value = $this->_get_step_value($data['bot_id'], $data_type_cd, $data_array, $last_data_array);
					}
				}
				else {
					$orm->value = $this->_get_value($data['bot_id'], $data_type_cd, $data_array);
				}
				$orm->save();
			}
		}
	}
	*/
	
	function get_class_item($bot_id, $class_cd, $area_cd) 
	{
		$sub = '';
		if ($area_cd != '') $sub = ' AND area_cd = :area_cd ';
		$sql = "SELECT item_id, class_cd, area_cd FROM t_item WHERE bot_id = :bot_id AND class_cd LIKE :class_cd $sub ORDER BY class_cd, area_cd, item_id ";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
				':class_cd' => $class_cd . '%',
				':area_cd' => $area_cd,
		));
		$results = $query->execute()->as_array();
		return $results;
	}
	
	function get_item_url_test($bot_id, $item_div, $class_cd, $end_date)
	{
		$sql = "SELECT m.item_id, m.item_div, d.url, d.lang_cd FROM t_item m LEFT JOIN t_item_description d ON m.item_id=d.item_id
			WHERE m.bot_id = :bot_id AND m.item_div = :item_div AND m.class_cd LIKE :class_cd AND (m.end_date is NULL OR m.end_date > :end_date) AND m.item_id=10000130984
			ORDER BY m.item_id ";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
				':item_div' => $item_div,
				':class_cd' => $class_cd . '%',
				':end_date' => $end_date,
		));
		$results = $query->execute()->as_array();
		return $results;
	}
	
	function get_item_url($bot_id, $item_div, $class_cd, $end_date) 
	{
		$sql = "SELECT m.item_id, m.item_div, d.url, d.lang_cd, d.item_name FROM t_item m LEFT JOIN t_item_description d ON m.item_id=d.item_id 
			WHERE m.bot_id = :bot_id AND m.item_div = :item_div AND m.class_cd LIKE :class_cd AND (m.end_date is NULL OR m.end_date > :end_date) AND m.item_id<>10000130007
			ORDER BY m.item_id ";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
				':item_div' => $item_div,
				':class_cd' => $class_cd . '%',
				':end_date' => $end_date,
		));
		$results = $query->execute()->as_array();
		return $results;
	}
	
	private function _get_tl_info($new) {
		$product_name = '';
		$description = '';
		$lastModifyDateTime = '';
		if (array_key_exists('RoomDescription', $new)) {
			$product_name = $new['RoomDescription']['$']['Name'];
			if (array_key_exists('Text', $new['RoomDescription'])) $description =  $new['RoomDescription']['Text'];
			if (array_key_exists('URL', $new['RoomDescription'])) {
				if (is_array($new['RoomDescription']['URL'])) {
					$images = $new['RoomDescription']['URL'];
				}
				else {
					$images = [$new['RoomDescription']['URL']];
				}
			}
			$lastModifyDateTime = $new['RoomDescription']['$']['LastModifyDateTime'];
		}
		if (array_key_exists('RatePlanDescription', $new)) {
			$product_name = $new['$']['RatePlanName'];
			if (array_key_exists('Text', $new['RatePlanDescription'])) $description = $new['RatePlanDescription']['Text'];
			if (array_key_exists('URL', $new['RatePlanDescription'])) {
				if (is_array($new['RatePlanDescription']['URL'])) {
					$images = $new['RatePlanDescription']['URL'];
				}
				else {
					$images = [$new['RatePlanDescription']['URL']];
				}
			}
			$lastModifyDateTime = $new['RatePlanDescription']['$']['LastModifyDateTime'];
		}
		$correct_images = [];
		foreach($images as $image) {
			if ($image != '') $correct_images[] = $image;
		}
		return [$product_name, $description, $correct_images, $lastModifyDateTime];
	}

	function update_product_tl($bot_id, $product_cd, $class_cd, $product_data, $link_key, $link_type_cd='tl')
	{
		$aws_model = new Model_Aws();
		$admin_model = new Model_Adminmodel();
		
		$bot = ORM::factory('bot', $bot_id);
		$bot_lang_arr = explode(',', $bot->lang_cd);

		$flg_product_auto_update = $this->get_bot_setting($bot_id, 'flg_product_auto_update');

		$new = json_decode($product_data, true);
		list($product_name, $description, $images, $lastModifyDateTime) = $this->_get_tl_info($new);
		
		$orm = ORM::factory('product')->where('bot_id', '=', $bot_id)->where('product_cd', '=', $product_cd)->where('link_key', '=', $link_key)->where('class_cd', 'LIKE', $class_cd . '%')->where('delete_flg', '=', 0)->find();
		if (isset($orm->product_id)) {
			$desc = ORM::factory('productdescription')->where('product_id', '=', $orm->product_id)->where('lang_cd', '=', 'ja')->find();
			// modify check
			$modified = [];
			$modifyDateTimeChange = false;
			if ($orm->link_data == '') {
				$modifyDateTimeChange = true;
			}
			else {
				$old = json_decode($orm->link_data, true);
				list($old_product_name, $old_description, $old_images, $old_lastModifyDateTime) = $this->_get_tl_info($old);
				// 最終更新時間
				if ($lastModifyDateTime != $old_lastModifyDateTime) {
					$modifyDateTimeChange = true;
				}
				// タイトル
				if ($old_product_name != $product_name) {
					$modified[] = 'タイトル';
				}
				// 詳細
				if ($old_description != $description) {
					$modified[] = '詳細';
				}
				// 写真変更判断
				if (count(array_diff($images, $old_images)) > 0) {
					$modified[] = '写真';
				}
			}
			$product = ORM::factory('product', $orm->product_id);
			$product->link_id = $product_cd;
			$product->link_data = $product_data;
			if ($modifyDateTimeChange || $product->end_date != NULL) {
				// コンテンツ復活
				if ($product->end_date != NULL) $product->end_date = NULL;
				$product->upd_user = 0;
				$product->upd_time = date('Y-m-d H:i:s');
			}
			// images
			if (count($images) > 0) {
				$exist_product_data = json_decode($product->product_data, true);
				if (is_array($exist_product_data)) {
					if (!array_key_exists('images', $exist_product_data)) {
						$exist_product_data['images'] = [];
					}
				}
				else {
					$exist_product_data = ['images'=>[]];
				}
				$new_image_arr = [];
				foreach($exist_product_data['images'] as $it) {
					if (strpos($it['url'], 'https://www.tl-lincoln.net/') === FALSE || in_array($it['url'], $images)) {
						$new_image_arr[] = $it;
					}
				}
				$exist_product_data['images'] = $new_image_arr;
				foreach($images as $image) {
					$existed = false;
					foreach($exist_product_data['images'] as $exist_image) {
						if ($exist_image['url'] == $image) {
							$existed = true;
							break;
						}
					}
					if (!$existed) $exist_product_data['images'][] = ['title'=>'', 'url'=>$image, 'show'=>1];
				}
				$product->product_data = json_encode($exist_product_data);
			}
			$product->save();

			if ($modifyDateTimeChange) {
				if ($flg_product_auto_update == 1) {
					$url = $desc->product_image;
					if (count($images) > 0) {
						$filename = "product/" . $orm->product_id;
						$url = $aws_model->copy_image($bot_id, $images[0], $filename);
						$aws_model->resize_image_url($url, 640, 424);
					}
					$sell_point = $description;
					if (mb_strlen($sell_point) > 80) $sell_point = mb_substr($sell_point, 0, 77) . '...';
					$user_edit = ['product_name'=>$product_name, 'description'=>$description, 'sell_point' =>$sell_point, 'notes'=>$desc->notes, 'product_image'=>$url];
					DB::update('t_product_description')->set(['product_name'=>$product_name, 'sell_point'=>$sell_point, 'description'=>$description, 'product_image'=>$url, 'user_edit'=>json_encode($user_edit, JSON_UNESCAPED_UNICODE), 'upd_user'=>0, 'upd_time'=>date('Y-m-d H:i:s')])->
					where('product_id', '=', $orm->product_id)->where('lang_cd', '=', 'ja')->execute();
					// 多言語翻訳
					DB::delete('t_product_description')->where('product_id', '=', $orm->product_id)->where('lang_cd', '<>', 'ja')->execute();
					$translate_err = '';
					foreach($bot_lang_arr as $key) {
						//foreach(['ja'=>'日本語'] as $key=>$value) {
						$product_description = ORM::factory('productdescription');
						$product_description->product_id = $orm->product_id;
						$product_description->lang_cd = $key;
						if ($key != 'ja') {
							$ret = $this->translate($product_name, $key);
							if ($ret !== False) {
								$product_description->product_name = $ret;
							}
							else {
								$translate_err = '-翻訳エラーあり';
							}
							$ret = $this->translate($description, $key);
							if ($ret !== False) {
								$product_description->description = $ret;
							}
							else {
								$translate_err = '-翻訳エラーあり';
							}
							$sell_point = $product_description->description;
							if (mb_strlen($sell_point) > 80) $sell_point = mb_substr($sell_point, 0, 77) . '...';
							$product_description->sell_point = $sell_point;
							$product_description->notes = '';
							$user_edit = ['product_name'=>$product_description->product_name, 'description'=>$product_description->description, 'sell_point' =>$product_description->sell_point, 'notes'=>''];
							$product_description->user_edit = json_encode($user_edit, JSON_UNESCAPED_UNICODE);
							$product_description->url = '';
							$product_description->product_image = $url;
							$product_description->save();
						}
					}
					// #53938
					$public_flg = $flg_product_auto_update;
					if ($product_name == '' || $description == '' || count($images) == 0) $public_flg = 0;
					DB::update('t_item_display')->set(['public_flg'=>$public_flg])->where('item_id', '=', $product->product_id)->where('item_div', '=', 5)->execute();
					return ['', $orm->product_id];
				}
				else {
					DB::update('t_product_description')->set(['description'=>$description, 'upd_user'=>0, 'upd_time'=>date('Y-m-d H:i:s')])->
					where('product_id', '=', $orm->product_id)->where('lang_cd', '=', 'ja')->where('description', '=', '')->execute();
					if (count($modified) > 0) {
						return [$product_name . '(変更箇所：' . implode('・', $modified) . ') id = ' . $product_cd, $orm->product_id];
					}
					else {
						return ['', $orm->product_id];
					}
				}
			}
			else {
				return ['', $orm->product_id];
			}
		}
		else {
			$product = ORM::factory('product');
			$product->product_id = $admin_model->get_max_product_id($bot_id);
			$product->product_cd = $product_cd;
			$product->bot_id = $bot_id;
			$product->class_cd = $class_cd;
			$product->product_status_cd = '01';
			$product->country_cd = 'JP';
			$product->start_date = NULL;
			$product->end_date = NULL;
			$product->regular_start = NULL;
			$product->regular_end = NULL;
			$product->link_key = $link_key;
			$product->link_type_cd = $link_type_cd;
			$product->link_id = $product_cd;
			$product->link_data = $product_data;
			if (count($images) > 0) {
				$product_images = [];
				foreach($images as $image) {
					$product_images[] = ['title'=>'', 'url'=>$image, 'show'=>1];
				}
				$product->product_data = json_encode(['images'=>$product_images]);
			}
			$product->upd_user = 0;
			$product->upd_time = date('Y-m-d H:i:s');
			$product->save();
			
			if (count($images) > 0) {
				$filename = "product/" . $product->product_id;
				$url = $aws_model->copy_image($bot_id, $images[0], $filename);
				$aws_model->resize_image_url($url, 640, 424);
			}
			else {
				$url = '';
			}
			
			$translate_err = '';
			foreach($bot_lang_arr as $key) {
				//foreach(['ja'=>'日本語'] as $key=>$value) {
				$product_description = ORM::factory('productdescription');
				$product_description->product_id = $product->product_id;
				$product_description->lang_cd = $key;
				if ($key == 'ja') {
					$sell_point = $description;
					if ($flg_product_auto_update == 1) {
						if (mb_strlen($sell_point) > 80) $sell_point = mb_substr($sell_point, 0, 77) . '...';
					}
					$product_description->product_name = $product_name;
					$product_description->sell_point = $sell_point;
					$product_description->description = $description;
					$product_description->notes = '';
					$user_edit = ['product_name'=>$product_name, 'description'=>$description, 'sell_point' =>$sell_point, 'notes'=>''];
					$product_description->user_edit = json_encode($user_edit, JSON_UNESCAPED_UNICODE);
				}
				else {
					$ret = $this->translate($product_name, $key);
					if ($ret !== False) {
						$product_description->product_name = $ret;
					}
					else {
						$translate_err = '-翻訳エラーあり';
					}
					$ret = $this->translate($description, $key);
					if ($ret !== False) {
						$product_description->description = $ret;
					}
					else {
						$translate_err = '-翻訳エラーあり';
					}
					$sell_point = $product_description->description;
					if ($flg_product_auto_update == 1) {
						if (mb_strlen($sell_point) > 80) $sell_point = mb_substr($sell_point, 0, 77) . '...';
					}
					$product_description->sell_point = $sell_point;
					$product_description->notes = '';
					$user_edit = ['product_name'=>$product_description->product_name, 'description'=>$product_description->description, 'sell_point' =>$product_description->sell_point, 'notes'=>''];
					$product_description->user_edit = json_encode($user_edit, JSON_UNESCAPED_UNICODE);
				}
				$product_description->url = '';
				$product_description->product_image = $url;
				$product_description->save();
			}
			// #53938
			$public_flg = $flg_product_auto_update;
			if ($product_name == '' || $description == '' || count($images) == 0) $public_flg = 0;
			DB::delete('t_item_display')->where('item_id', '=', $product->product_id)->where('item_div', '=', 5)->execute();
			$item = ORM::factory('itemdisplay');
			$item->bot_id = $bot_id;
			$item->item_id = $product->product_id;
			$item->item_div = 5;
			$item->sort_no1 = $product->product_id % 1000;
			$item->sort_no2 = 0;
			$item->sort_no3 = 0;
			$item->sort_no4 = 0;
			$item->sort_no5 = 0;
			$item->recommend = 0;
			$item->public_flg = $public_flg;
			$item->lang_display = $bot->lang_cd;
			$item->save();

			if ($flg_product_auto_update == 1) {
				return ['', $product->product_id];
			}
			else {
				return [$product_name . '(追加' . $translate_err .') id = ' . $product_cd, $product->product_id];
			}
		}
	}
	
	private function _get_value($bot_id, $data_type_cd, $data_array)
	{
		$type_data_array = $data_array[substr($data_type_cd, 1)];
		if (array_key_exists($bot_id, $type_data_array)) {
			return $type_data_array[$bot_id];
		}
		else {
			return 0;
		}
	}
	
	private function _get_step_value($bot_id, $data_type_cd, $data_array, $last_data_array) 
	{
		if (array_key_exists($bot_id . '-' . $data_type_cd, $last_data_array)) {
			return $last_data_array[$bot_id . '-' . $data_type_cd] + $this->_get_value($bot_id, $data_type_cd, $data_array);
		}
		else {
			return $this->_get_value($bot_id, $data_type_cd, $data_array);
		}
	}
	
	private function _get_init_step_value($bot_id, $data_type_cd, $data_array, $last_data_array)
	{
		if (array_key_exists($data_type_cd, $last_data_array)) {
			return $last_data_array[$data_type_cd] + $this->_get_value($bot_id, $data_type_cd, $data_array);
		}
		else {
			return $this->_get_value($bot_id, $data_type_cd, $data_array);
		}
	}
	
	private function write_log($log)
	{
		$file =  APPPATH . "../../files/log/batch_" . date('Y-m-d') . ".log";
		$f = fopen($file, 'a');
		$log = date('Y-m-d H:i:s') . ' ' . $log . PHP_EOL;
		fwrite($f, $log);
		fclose($f);
		print($log);
	}
}

?>
