<?php defined('SYSPATH') OR die('No direct script access.');

class Model_Bookingmodel extends Model_Basemodel
{
	public function init_bot($bot)
	{
		$this->_bot = $bot;
	}

	public function get_room_list()
	{
		$param = ['facility_cd'=>$this->_bot->facility_cd];
		$data = $this->post_enginehook('booking', 'admin_room_list','', $param);
		if ($data['success'] == 'True') {
			$this->log_info(__FUNCTION__, '取得件数:' . count($data['response']['result_list']));
			return $data['response']['result_list'];
		}
		else {
			$this->log_error(__FUNCTION__, json_encode($data, JSON_UNESCAPED_UNICODE));
			return null;
		}
	}
	
	public function get_plan_list()
	{
		$param = ['facility_cd'=>$this->_bot->facility_cd];
		$data = $this->post_enginehook('booking', 'admin_plan_list','', $param);
		if ($data['success'] == 'True') {
			$this->log_info(__FUNCTION__, '取得件数:' . count($data['response']['result_list']));
			return $data['response']['result_list'];
		}
		else {
			$this->log_error(__FUNCTION__, json_encode($data, JSON_UNESCAPED_UNICODE));
			return null;
		}
	}
	
	public function confirm_reservation()
	{
		
	}
	
	public function cancel_reservation()
	{
		
	}
	
}

?>




