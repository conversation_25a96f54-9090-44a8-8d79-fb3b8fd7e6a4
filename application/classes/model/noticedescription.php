<?php defined('SYSPATH') OR die('No direct script access.');

class Model_Noticedescription extends ORM
{
    protected $_table_name = 't_notice_description';
    protected $_primary_key = 'notice_id,lang_cd';

    public function find_noticedescription($notice_id, $lang_cd) {
		$sql = 'SELECT n.notice_id, n.display_start_date, d.title, d.content, d.buttons, d.display_timing_type
        FROM t_notice n
        LEFT JOIN t_notice_description d ON n.notice_id = d.notice_id
        WHERE d.notice_id = :notice_id
        AND d.lang_cd = :lang_cd ;'
        ;
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(':notice_id' => $notice_id, ':lang_cd' => $lang_cd));
		return $query->execute()->current();
    }


    public function find_all_noticedescription($notice_id) {
        $sql = '
            SELECT 
                d.lang_cd, 
                d.title, 
                d.content, 
                d.buttons, 
                d.display_timing_type
            FROM 
                t_notice_description d
            WHERE 
                d.notice_id = :notice_id
        ';
    
        $query = DB::query(Database::SELECT, $sql)
            ->parameters([':notice_id' => $notice_id]);
    
        return $query->execute()->as_array();
    }
    

    public function new_noticedescription($post, $notice_id, $lang_cd){
        $this->notice_id = $notice_id;
		$this->lang_cd = $lang_cd;
		$this->title = $post['title'];
		$this->content = $post['content'];
        if ($post['buttons'] !== "") {
            $this->buttons = $post['buttons'];
        }
        if ($post['display_timing_type'] !== "") {
            $this->display_timing_type = $post['display_timing_type'];
        }
		return $this->save();
	}
    public function update_noticedescription($post, $notice_id, $lang_cd) {
        $data = [
            'title' => $post['title'],
            'content' => $post['content'],
        ];
        if (isset($post['buttons']) && $post['buttons'] !== "") {
            $data['buttons'] = $post['buttons'];
        } else {
            $data['buttons'] = null;
        }
        if (isset($post['display_timing_type']) && $post['display_timing_type'] !== "") {
            $data['display_timing_type'] = $post['display_timing_type'];
        } else {
            $data['display_timing_type'] = null;
        }
        DB::update('t_notice_description')
            ->set($data)
            ->where('notice_id', '=', $notice_id)
            ->where('lang_cd', '=', $lang_cd)
            ->execute();
    }

    /**
    * Duplicate every notice_description row of one notice to a new notice_id.
    *
    * @param int $sourceNoticeId  ID of the notice you are cloning FROM
    * @param int $newNoticeId     ID of the notice you are cloning TO
    */
    public function cloneNoticeDescriptions(int $sourceNoticeId, int $newNoticeId): void
   {
       // fetch every language row for the source notice
       $srcRows = ORM::factory('noticedescription')
           ->where('notice_id', '=', $sourceNoticeId)
           ->find_all();
   
       // columns that should be copied verbatim
       $columnsToCopy = [
           'title',
           'image',
           'content',
           'buttons',
           'display_timing_type',
       ];
   
       foreach ($srcRows as $src) {
           $dst = ORM::factory('noticedescription');
   
           // ----- set composite primary-key fields -----
           $dst->notice_id = $newNoticeId;    // new parent notice
           $dst->lang_cd   = $src->lang_cd;   // keep the same language
   
           // ----- copy the remaining fields -----
           foreach ($columnsToCopy as $col) {
               $dst->$col = $src->$col;
           }
   
           $dst->save();                      // INSERT
       }
   }
    
}