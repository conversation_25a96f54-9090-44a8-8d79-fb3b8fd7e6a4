<?php defined('SYSPATH') OR die('No direct script access.');

class Model_Chatmodel extends Model_Basemodel
{
	function get_chatlist($bot_id, &$log_time, $user_lang, $user_sns, &$new_unanswer, &$chatlist, 
			&$new_lang, &$new_tag_members, $filter_scene)
	{
		$sql = "SELECT count(1) AS c FROM t_bot_log_chat log ";
		$sql = $sql . " WHERE " . $this->_create_bot_cond_chat($bot_id, 'log.bot_id');
		if ($filter_scene != '') $sql = $sql . ' AND log.scene_cd LIKE \'' . $filter_scene . '\'';
		$sql = $sql . " AND log.lang_cd in $user_lang AND log.sns_type_cd in $user_sns AND log.log_time > :log_time";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
				':log_time' => $log_time,
		));
		$results = $query->execute()->as_array();
		if ($results[0]['c'] > $this->get_setting('chat_max_record')) return null;

		$sql = "SELECT log.member_id, log.sns_type_cd, log.log_time, log.lang_cd, log.log_id,log.bot_id,
			log.member_msg, log.bot_msg, log.score, log.intent_cd, log.log_flg,log.mobile,log.request_flg,
			log.member_name, log.chat_mode, log.is_tester, log.user_id, user.name, log.member_tag, '' as remark, log.country_cd, log.timezone, member.member_no, member.avatar, member.chat_online_status
			FROM t_bot_log_chat log
			LEFT JOIN t_bot_member member ON log.bot_id = member.bot_id AND log.member_id = member.member_id
			LEFT JOIN t_user user ON log.user_id = user.user_id ";
		$sql = $sql . " WHERE " . $this->_create_bot_cond_chat($bot_id, 'log.bot_id');
		if ($filter_scene != '') $sql = $sql . ' AND log.scene_cd LIKE \'' . $filter_scene . '\'';
		$sql = $sql . " AND log.lang_cd in $user_lang AND log.sns_type_cd in $user_sns AND log.log_time > :log_time ORDER BY log.log_time, log.member_id";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
				':log_time' => $log_time,
		));
		$results = $query->execute()->as_array();
		
		$new_unanswer = 0;
		$member_in_lang = [];   // member last lang_cd
		
		foreach ($results as $log) {
			$member_id = 'm-' . strval($log['member_id']);
			$member_in_lang[$member_id] = $log['lang_cd'];
			if ($log['intent_cd'] == 'welcome' && $log['member_msg'] <> "") continue;
			if ($log['intent_cd'] == 'clear_memberlog.all' || $log['intent_cd'] == 'clear_memberlog.1') {
				foreach ($chatlist as $chat_lang=>&$chat_members) {
					if (array_key_exists($member_id, $chat_members)) {
						if ($log['intent_cd'] == 'clear_memberlog.all') {
							$chat_members[$member_id]['unanswer'] = 0;
						}
						else {
							$chat_members[$member_id]['unanswer'] = $chat_members[$member_id]['unanswer'] - 1;
						}
						$chat_members[$member_id]['help'] = 0;
						break;
					}
				}
				$log_time = $log['log_time'];
				continue;
			}

			$log_time = $log['log_time'];
			
			if (array_key_exists($log['lang_cd'], $chatlist)) {
				if (array_key_exists($member_id, $chatlist[$log['lang_cd']])) {
					if ($log['log_flg'] == 1 && $log['member_msg'] != '' ) {
						$chatlist[$log['lang_cd']][$member_id]['unanswer'] = $chatlist[$log['lang_cd']][$member_id]['unanswer'] + 1;
						if ($new_unanswer == 0) $new_unanswer = 1;
					}
					if ($log['intent_cd'] == 'request_operator_yes') {
						$new_unanswer = 2;
					}
					$chatlist[$log['lang_cd']][$member_id]['log_time'] = $log_time;
					
					if ($log['member_name'] != '') {
						$chatlist[$log['lang_cd']][$member_id]['name'] = $log['member_name'];
					}
					else {
						if ($log['sns_type_cd'] == 'wb') $chatlist[$log['lang_cd']][$member_id]['name'] = __('admin.chatlist.web_user');
					}
					if ($log['member_tag'] != '' && $chatlist[$log['lang_cd']][$member_id]['member_tag'] != $log['member_tag']) {
						if (!array_key_exists($member_id, $new_tag_members)) {
							$member_name = $log['member_name'];
							if ($log['member_name'] == '' && $log['sns_type_cd'] == 'wb') $member_name = __('admin.chatlist.web_user');
							$new_tag_members[$member_id] = ["member_id"=>$log['member_id'], "member_name"=>$member_name, "lang_cd"=>$log['lang_cd'], "sns_type_cd"=>$log['sns_type_cd'],"log_time"=>$log['log_time'], "tag"=>$log['member_tag'], 'member_no'=>$log['member_no']];
						}
					}
					$chatlist[$log['lang_cd']][$member_id]['member_tag'] = $log['member_tag'];
					$chat_mode = $this->_get_chat_mode($log);
					if ($chat_mode['chat_mode']>=0) $chatlist[$log['lang_cd']][$member_id]['chat_mode'] = $chat_mode['chat_mode'];
					if ($chat_mode['help']>=0) $chatlist[$log['lang_cd']][$member_id]['help'] = $chat_mode['help'];
					$chatlist[$log['lang_cd']][$member_id]['chat_online_status'] = $log['chat_online_status'];
					$chatlist[$log['lang_cd']][$member_id]['is_tester'] = $log['is_tester'];
					$chatlist[$log['lang_cd']][$member_id]['user_id'] = $log['user_id'];
					$chatlist[$log['lang_cd']][$member_id]['user_name'] = $log['name'];
					$chatlist[$log['lang_cd']][$member_id]['avatar'] = $log['avatar'];
					$chatlist[$log['lang_cd']][$member_id]['country_cd'] = $log['country_cd'];
					$chatlist[$log['lang_cd']][$member_id]['bot_id'] = $log['bot_id'];
					$chatlist[$log['lang_cd']][$member_id]['mobile'] = $log['mobile'];
				}
				else {
					// copy member data from old language
					$exist_member = $this->_get_member_from_chatlist($member_id, $chatlist);
					if ($exist_member == null) {
						$unanswer = 0;
						if ($log['log_flg'] == 1 && $log['member_msg'] != '') {
							$unanswer = 1;
							if ($new_unanswer == 0) $new_unanswer = 1;
						}
						if ($log['intent_cd'] == 'request_operator_yes') {
							$new_unanswer = 2;
						}
						$chatlist[$log['lang_cd']][$member_id] = array('member_id'=>$log['member_id'],'chat_mode'=>$log['chat_mode'], 'name'=>$log['member_name'],'country_cd'=>$log['country_cd'],
								'lang_cd'=>$log['lang_cd'], 'sns_type_cd'=>$log['sns_type_cd'],'is_tester'=>$log['is_tester'],'bot_id'=>$log['bot_id'],'mobile'=>$log['mobile'],
								'unanswer'=>$unanswer, 'wait_time'=>'', 'log_time'=>$log_time, 'member_tag'=>$log['member_tag'], 'member_no'=>$log['member_no']);
						if ($log['member_tag'] != '') {
							if (!array_key_exists($member_id, $new_tag_members)) {
								$member_name = $log['member_name'];
								if ($log['member_name'] == '' && $log['sns_type_cd'] == 'wb') $member_name = __('admin.chatlist.web_user');
								$new_tag_members[$member_id] = ["member_id"=>$log['member_id'], "member_name"=>$member_name, "lang_cd"=>$log['lang_cd'], "sns_type_cd"=>$log['sns_type_cd'], "log_time"=>$log['log_time'], "tag"=>$log['member_tag'], 'member_no'=>$log['member_no']];
							}
						}
						$chat_mode = $this->_get_chat_mode($log, true);
						$chatlist[$log['lang_cd']][$member_id]['chat_mode'] = $chat_mode['chat_mode'];
						$chatlist[$log['lang_cd']][$member_id]['chat_online_status'] = $log['chat_online_status'];
						$chatlist[$log['lang_cd']][$member_id]['help'] = $chat_mode['help'];
						$chatlist[$log['lang_cd']][$member_id]['user_id'] = $log['user_id'];
						$chatlist[$log['lang_cd']][$member_id]['user_name'] = $log['name'];
						$chatlist[$log['lang_cd']][$member_id]['avatar'] = $log['avatar'];
					}
					else {
						$chatlist[$log['lang_cd']][$member_id] = $exist_member;
						$chat_mode = $this->_get_chat_mode($log, true);
						$chatlist[$log['lang_cd']][$member_id]['lang_cd'] = $log['lang_cd'];
						$chatlist[$log['lang_cd']][$member_id]['chat_mode'] = $chat_mode['chat_mode'];
						$chatlist[$log['lang_cd']][$member_id]['chat_online_status'] = $log['chat_online_status'];
						$chatlist[$log['lang_cd']][$member_id]['help'] = $chat_mode['help'];
						$chatlist[$log['lang_cd']][$member_id]['user_id'] = $log['user_id'];
						$chatlist[$log['lang_cd']][$member_id]['user_name'] = $log['name'];
						$chatlist[$log['lang_cd']][$member_id]['avatar'] = $log['avatar'];
					}
					if (!array_key_exists($log['lang_cd'], $new_lang)) {
						$new_lang[] = $log['lang_cd'];
					}
				}
			}
			else {
				$exist_member = $this->_get_member_from_chatlist($member_id, $chatlist);
				if ($exist_member == null) {
					$unanswer = 0;
					if ($log['log_flg'] == 1 && $log['member_msg'] != '' ) {
						$unanswer = 1;
						if ($new_unanswer == 0) $new_unanswer = 1;
					}
					if ($log['intent_cd'] == 'request_operator_yes') {
						$new_unanswer = 2;
					}
					$chat = array();
					$chat[$member_id] = array('member_id'=>$log['member_id'], 'chat_mode'=>$log['chat_mode'], 'name'=>$log['member_name'],'country_cd'=>$log['country_cd'],
							'lang_cd'=>$log['lang_cd'], 'sns_type_cd'=>$log['sns_type_cd'],'is_tester'=>$log['is_tester'], 'bot_id'=>$log['bot_id'],'mobile'=>$log['mobile'],
							'unanswer'=>$unanswer, 'wait_time'=>'', 'log_time'=>$log_time, 'member_tag'=>$log['member_tag'], 'member_no'=>$log['member_no']);
					if ($log['member_tag'] != '') {
						if (!array_key_exists($member_id, $new_tag_members)) {
							$member_name = $log['member_name'];
							if ($log['member_name'] == '' && $log['sns_type_cd'] == 'wb') $member_name = __('admin.chatlist.web_user');
							$new_tag_members[$member_id] = ["member_id"=>$log['member_id'], "member_name"=>$member_name, "lang_cd"=>$log['lang_cd'], "sns_type_cd"=>$log['sns_type_cd'], "log_time"=>$log['log_time'], "tag"=>$log['member_tag'], 'member_no'=>$log['member_no']];
						}
					}
					$member = array();
					$chatlist[$log['lang_cd']] = $chat;
					$chat_mode = $this->_get_chat_mode($log, true);
					$chatlist[$log['lang_cd']][$member_id]['chat_mode'] = $chat_mode['chat_mode'];
					$chatlist[$log['lang_cd']][$member_id]['chat_online_status'] = $log['chat_online_status'];
					$chatlist[$log['lang_cd']][$member_id]['help'] = $chat_mode['help'];
					$chatlist[$log['lang_cd']][$member_id]['user_id'] = $log['user_id'];
					$chatlist[$log['lang_cd']][$member_id]['user_name'] = $log['name'];
					$chatlist[$log['lang_cd']][$member_id]['avatar'] = $log['avatar'];
				}
				else {
					$chatlist[$log['lang_cd']][$member_id] = $exist_member;
					$chat_mode = $this->_get_chat_mode($log, true);
					$chatlist[$log['lang_cd']][$member_id]['lang_cd'] = $log['lang_cd'];
					$chatlist[$log['lang_cd']][$member_id]['chat_mode'] = $chat_mode['chat_mode'];
					$chatlist[$log['lang_cd']][$member_id]['chat_online_status'] = $log['chat_online_status'];
					$chatlist[$log['lang_cd']][$member_id]['help'] = $chat_mode['help'];
					$chatlist[$log['lang_cd']][$member_id]['user_id'] = $log['user_id'];
					$chatlist[$log['lang_cd']][$member_id]['user_name'] = $log['name'];
					$chatlist[$log['lang_cd']][$member_id]['avatar'] = $log['avatar'];
				}
				$new_lang[] = $log['lang_cd'];
			}
		}
		
		// filter member name
		$filter_username = $this->_get_session('setting_filter_username');
		// filter member no
		$filter_member_no = $this->_get_session('setting_filter_member_no');
		if ($filter_member_no == null) $filter_member_no = '';
		// filter chat user
		$select_users = $this->_get_session('setting_users');
		// filter tag
		$select_tags = $this->_get_session('setting_tags');
		// filter cond
		$chat_only = $this->_get_session('setting_chat_only');
		$sort_log_time = $this->_get_session('setting_sort_log_time');
		
		if ($chat_only == NULL) {
			$chat_only = 0;
		}
		$chat_req_flg= $this->_get_session('setting_chat_req_flg');
		if ($chat_req_flg == NULL) {
			$chat_req_flg = 0;
		}
		foreach ($chatlist as $chat_lang=>&$chat_members) {
			foreach ($chat_members as $key => $value) {
				//member name filter
				if ($filter_username != "") {
					if (strpos($key, $filter_username) === FALSE && strpos($value['name'], $filter_username) === FALSE) {
						unset($chat_members[$key]);
						continue;
					}
				}
				//member no filter
				$filter_member_no = strtoupper(trim($filter_member_no));
				if ($filter_member_no != "") {
					if ($filter_member_no != $value['member_no'] && 
						strpos(strtoupper($key), $filter_member_no) === FALSE && 
						strpos(strtoupper($value['name']), $filter_member_no) === FALSE && 
						(isset($value['member_remark']) && strpos(strtoupper($value['member_remark']), $filter_member_no) === FALSE)) {
						unset($chat_members[$key]);
						continue;
					}
				}
				//remove member from old lang_cd of chatlist when lang_cd changed
				if (array_key_exists($key, $member_in_lang) && $member_in_lang[$key] != $chat_lang) {
					unset($chat_members[$key]);
					continue;
				}
				else  {
					if ($chat_members[$key]['chat_mode'] == 0) {
						// must deal
						if ($chat_req_flg == 1 && $chat_members[$key]['unanswer'] == 0 && $chat_members[$key]['help']==0) {
							unset($chat_members[$key]);
							continue;
						}
						if ($chat_req_flg == 2 && $chat_members[$key]['help'] == 0) {
							unset($chat_members[$key]);
							continue;
						}
					}
					if ($select_users != NULL && !in_array($chat_members[$key]['user_id'], $select_users)) {
						// user filter
						unset($chat_members[$key]);
						continue;
					}
					if ($select_tags != NULL && !in_array($chat_members[$key]['member_tag'], $select_tags)) {
						// tag filter
						unset($chat_members[$key]);
						continue;
					}
				}

				$code = $this->get_code('42', Session::instance()->get('lang_cd_admin', NULL));
				
				// wait time refresh
				$wait_time = '';
				$diff_min = floor((strtotime(date("Y-m-d H:i:s")) - strtotime($chat_members[$key]['log_time'])) / 60);
				if ($diff_min > 0) $wait_time = $diff_min . $code['min'];
				$diff_hour = floor((strtotime(date("Y-m-d H:i:s")) - strtotime($chat_members[$key]['log_time'])) / 3600);
				if ($diff_hour> 0) $wait_time = $diff_hour. $code['hour'];
				$diff_day = floor((strtotime(date("Y-m-d H:i:s")) - strtotime($chat_members[$key]['log_time'])) / 86400);
				if ($diff_day> 0) $wait_time = $diff_day. $code['day'];
				$chat_members[$key]['wait_time'] = $wait_time;
			}
			if (count($chat_members) == 0) unset($chatlist[$chat_lang]);
			
			/*
			$sort_help = array_column($chat_members,'help');
			$sort_chat_mode = array_column($chat_members,'chat_mode');
			$sort_unanswer = array_column($chat_members,'unanswer');
			$sort_log_time = array_column($chat_members,'log_time');
			array_multisort($sort_help, SORT_DESC, $sort_chat_mode, SORT_DESC, $sort_unanswer, SORT_DESC, $sort_log_time, SORT_DESC, $chat_members);
			*/
			if ($sort_log_time == 0) {
				array_multisort(array_column($chat_members,'log_time'), SORT_ASC, $chat_members);
			}
			else {
				array_multisort(array_column($chat_members,'log_time'), SORT_DESC, $chat_members);
			}
		}
		return $chatlist;
	}
	
	function get_chatmembers_diff2($bot_id, &$log_time, &$member_time, $user_lang, $user_sns, &$new_unanswer, &$chatlist, &$new_lang, $first, &$new_tag_members, $filter_scene)
	{
		//Log::instance()->add(Log::DEBUG, "logtime=" . $log_time);
		/*
		$sql = 'SELECT max(log_time) AS max_log_time FROM t_bot_log_chat ';
		$sql = $sql . " WHERE " . $this->_create_bot_cond_chat($bot_id);
		$sql = $sql . " AND lang_cd IN $user_lang AND sns_type_cd IN $user_sns AND log_time > :log_time";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
				':log_time' => $log_time,
		));
		$results = $query->execute()->as_array();
		if ($results[0]['max_log_time'] == null) return $chatlist;
		
		$max_log_time = $results[0]['max_log_time'];
		
		$sql = 'SELECT member_id, count(log_flg) AS log_count FROM t_bot_log_chat ';
		$sql = $sql . " WHERE " . $this->_create_bot_cond_chat($bot_id);
		$sql = $sql . " AND lang_cd IN $user_lang AND sns_type_cd IN $user_sns AND log_time > :log_time AND log_time <= :max_log_time AND log_flg=1";
		$sql = $sql . " GROUP BY bot_id, member_id";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
				':log_time' => $log_time,
				':max_log_time' => $max_log_time,
		));
		$unanswers = $query->execute()->as_array('member_id', 'log_count');
		*/
		if (!$first) {
			$sql = "SELECT log.member_id, log.sns_type_cd, log.log_time, log.lang_cd, log.log_id,log.bot_id,
				log.member_msg, log.bot_msg, log.score, log.intent_cd, log.log_flg,log.chat_online_status,log.mobile,log.request_flg,
				log.member_name, log.chat_mode, log.is_tester, log.user_id, user.name, log.member_tag, '' as remark, log.country_cd, log.timezone,  member.member_no, member.avatar
				FROM t_bot_log_chat log
				LEFT JOIN t_bot_member member ON log.bot_id = member.bot_id AND log.member_id = member.member_id
				LEFT JOIN t_user user ON log.user_id = user.user_id ";
			$sql = $sql . " WHERE " . $this->_create_bot_cond_chat($bot_id, 'log.bot_id');
			if ($filter_scene != '') $sql = $sql . ' AND log.scene_cd LIKE \'%' . $filter_scene . '%\'';
			$sql = $sql . " AND log.lang_cd in $user_lang AND log.sns_type_cd in $user_sns AND log.log_time > :log_time ORDER BY log.log_time, log.member_id";
			$query = DB::query(Database::SELECT, $sql);
			$query->parameters(array(
					':bot_id' => $bot_id,
					':log_time' => $log_time,
			));
			$results = $query->execute()->as_array();

			$new_unanswer = 0;
			$member_in_lang = array();   // member last lang_cd
			
			foreach ($results as $log) {
				$member_id = 'm-' . strval($log['member_id']);
				$member_in_lang[$member_id] = $log['lang_cd'];
				if ($log['intent_cd'] == 'welcome' && $log['member_msg'] <> "") continue;
				if ($log['intent_cd'] == 'clear_memberlog.all' || $log['intent_cd'] == 'clear_memberlog.1') {
					foreach ($chatlist as $chat_lang=>&$chat_members) {
						if (array_key_exists($member_id, $chat_members)) {
							if ($log['intent_cd'] == 'clear_memberlog.all') {
								$chat_members[$member_id]['unanswer'] = 0;
							}
							else {
								$chat_members[$member_id]['unanswer'] = $chat_members[$member_id]['unanswer'] - 1;
							}
							//$chat_members[$member_id]['new'] = 0;
							$chat_members[$member_id]['help'] = 0;
							break;
						}
					}
					$log_time = $log['log_time'];
					continue;
				}
				$chat_members[$member_id]['help'] = $log['request_flg'];
				$log_time = $log['log_time'];
				if (array_key_exists($log['lang_cd'], $chatlist)) {
					if (array_key_exists($member_id, $chatlist[$log['lang_cd']])) {
						if ($log['log_flg'] == 1 && $log['member_msg'] != '' ) {
							$chatlist[$log['lang_cd']][$member_id]['unanswer'] = $chatlist[$log['lang_cd']][$member_id]['unanswer'] + 1;
							//$chatlist[$log['lang_cd']][$member_id]['new'] = 1;
							if ($new_unanswer == 0) $new_unanswer = 1;
						}
						if ($log['intent_cd'] == 'request_operator_yes') {
							$new_unanswer = 2;
						}
						$chatlist[$log['lang_cd']][$member_id]['log_time'] = $log_time;
						
						if ($log['member_name'] != '') {
							$chatlist[$log['lang_cd']][$member_id]['name'] = $log['member_name'];
						}
						else {
							if ($log['sns_type_cd'] == 'wb') $chatlist[$log['lang_cd']][$member_id]['name'] = __('admin.chatlist.web_user');
						}
						if ($log['member_tag'] != '' && $chatlist[$log['lang_cd']][$member_id]['member_tag'] != $log['member_tag']) {
							if (!array_key_exists($member_id, $new_tag_members)) {
								$member_name = $log['member_name'];
								if ($log['member_name'] == '' && $log['sns_type_cd'] == 'wb') $member_name = __('admin.chatlist.web_user');
								$new_tag_members[$member_id] = ["member_id"=>$log['member_id'], "member_name"=>$member_name, "lang_cd"=>$log['lang_cd'], "sns_type_cd"=>$log['sns_type_cd'],"log_time"=>$log['log_time'], "tag"=>$log['member_tag']];
							}
						}
						$chatlist[$log['lang_cd']][$member_id]['member_tag'] = $log['member_tag'];
						$chat_mode = $this->_get_chat_mode($log);
						if ($chat_mode['chat_mode']>=0) $chatlist[$log['lang_cd']][$member_id]['chat_mode'] = $chat_mode['chat_mode'];
						if ($chat_mode['help']>=0) $chatlist[$log['lang_cd']][$member_id]['help'] = $chat_mode['help'];
						$chatlist[$log['lang_cd']][$member_id]['chat_online_status'] = $log['chat_online_status'];
						$chatlist[$log['lang_cd']][$member_id]['is_tester'] = $log['is_tester'];
						$chatlist[$log['lang_cd']][$member_id]['user_id'] = $log['user_id'];
						$chatlist[$log['lang_cd']][$member_id]['user_name'] = $log['name'];
						$chatlist[$log['lang_cd']][$member_id]['avatar'] = $log['avatar'];
						$chatlist[$log['lang_cd']][$member_id]['country_cd'] = $log['country_cd'];
						$chatlist[$log['lang_cd']][$member_id]['bot_id'] = $log['bot_id'];
						$chatlist[$log['lang_cd']][$member_id]['mobile'] = $log['mobile'];
					}
					else {
						// copy member data from old language
						$exist_member = $this->_get_member_from_chatlist($member_id, $chatlist);
						if ($exist_member == null) {
							$unanswer = 0;
							if ($log['log_flg'] == 1 && $log['member_msg'] != '') {
								$unanswer = 1;
								if ($new_unanswer == 0) $new_unanswer = 1;
							}
							if ($log['intent_cd'] == 'request_operator_yes') {
								$new_unanswer = 2;
							}
							$chatlist[$log['lang_cd']][$member_id] = array('member_id'=>$log['member_id'],'chat_mode'=>$log['chat_mode'], 'name'=>$log['member_name'],'country_cd'=>$log['country_cd'],
									'lang_cd'=>$log['lang_cd'], 'sns_type_cd'=>$log['sns_type_cd'],'is_tester'=>$log['is_tester'],'bot_id'=>$log['bot_id'],'mobile'=>$log['mobile'],
									'unanswer'=>$unanswer, 'wait_time'=>'', 'log_time'=>$log_time, 'member_tag'=>$log['member_tag']);
							if ($log['member_tag'] != '') {
								if (!array_key_exists($member_id, $new_tag_members)) {
									$member_name = $log['member_name'];
									if ($log['member_name'] == '' && $log['sns_type_cd'] == 'wb') $member_name = __('admin.chatlist.web_user');
									$new_tag_members[$member_id] = ["member_id"=>$log['member_id'], "member_name"=>$member_name, "lang_cd"=>$log['lang_cd'], "sns_type_cd"=>$log['sns_type_cd'], "log_time"=>$log['log_time'], "tag"=>$log['member_tag']];
								}
							}
							$chat_mode = $this->_get_chat_mode($log, true);
							$chatlist[$log['lang_cd']][$member_id]['chat_mode'] = $chat_mode['chat_mode'];
							$chatlist[$log['lang_cd']][$member_id]['chat_online_status'] = $log['chat_online_status'];
							$chatlist[$log['lang_cd']][$member_id]['help'] = $chat_mode['help'];
							$chatlist[$log['lang_cd']][$member_id]['user_id'] = $log['user_id'];
							$chatlist[$log['lang_cd']][$member_id]['user_name'] = $log['name'];
							$chatlist[$log['lang_cd']][$member_id]['avatar'] = $log['avatar'];
						}
						else {
							$chatlist[$log['lang_cd']][$member_id] = $exist_member;
						}
						if (!array_key_exists($log['lang_cd'], $new_lang)) {
							$new_lang[] = $log['lang_cd'];
						}
					}
				}
				else {
					$exist_member = $this->_get_member_from_chatlist($member_id, $chatlist);
					if ($exist_member == null) {
						$unanswer = 0;
						if ($log['log_flg'] == 1 && $log['member_msg'] != '' ) {
							$unanswer = 1;
							if ($new_unanswer == 0) $new_unanswer = 1;
						}
						if ($log['intent_cd'] == 'request_operator_yes') {
							$new_unanswer = 2;
						}
						$chat = array();
						$chat[$member_id] = array('member_id'=>$log['member_id'], 'chat_mode'=>$log['chat_mode'], 'name'=>$log['member_name'],'country_cd'=>$log['country_cd'],
								'lang_cd'=>$log['lang_cd'], 'sns_type_cd'=>$log['sns_type_cd'],'is_tester'=>$log['is_tester'], 'bot_id'=>$log['bot_id'],'mobile'=>$log['mobile'],
								'unanswer'=>$unanswer, 'wait_time'=>'', 'log_time'=>$log_time, 'member_tag'=>$log['member_tag']);
						if ($log['member_tag'] != '') {
							if (!array_key_exists($member_id, $new_tag_members)) {
								$member_name = $log['member_name'];
								if ($log['member_name'] == '' && $log['sns_type_cd'] == 'wb') $member_name = __('admin.chatlist.web_user');
								$new_tag_members[$member_id] = ["member_id"=>$log['member_id'], "member_name"=>$member_name, "lang_cd"=>$log['lang_cd'], "sns_type_cd"=>$log['sns_type_cd'], "log_time"=>$log['log_time'], "tag"=>$log['member_tag']];
							}
						}
						$member = array();
						$chatlist[$log['lang_cd']] = $chat;
						$chat_mode = $this->_get_chat_mode($log, true);
						$chatlist[$log['lang_cd']][$member_id]['chat_mode'] = $chat_mode['chat_mode'];
						$chatlist[$log['lang_cd']][$member_id]['chat_online_status'] = $log['chat_online_status'];
						$chatlist[$log['lang_cd']][$member_id]['help'] = $chat_mode['help'];
						$chatlist[$log['lang_cd']][$member_id]['user_id'] = $log['user_id'];
						$chatlist[$log['lang_cd']][$member_id]['user_name'] = $log['name'];
						$chatlist[$log['lang_cd']][$member_id]['avatar'] = $log['avatar'];
					}
					else {
						$chatlist[$log['lang_cd']][$member_id] = $exist_member;
					}
					$new_lang[] = $log['lang_cd'];
				}
			}
			
			/*  ❷　count calc only
			 $sql = "SELECT log.member_id, log.log_time, log.log_id,log.bot_id, log.score, log.intent_cd, log.log_flg FROM t_bot_log_chat log ";
			 $sql = $sql . " WHERE " . $this->_create_bot_cond_chat($bot_id, 'log.bot_id');
			 $sql = $sql ." AND log.lang_cd in $user_lang AND log.sns_type_cd in $user_sns AND log.log_time > :log_time ORDER BY log.log_time";
			$query->parameters(array(
					':bot_id' => $bot_id,
					':log_time' => $log_time,
			));
			$logs = $query->execute()->as_array();			
			$unanswers = array();
			foreach($logs as $log) {
				$member_id = strval($log['member_id']);
				if (!array_key_exists($member_id, $unanswers)) {
					$unanswers[$member_id] = 0;
				}
				if ($log['intent_cd'] == 'clear_memberlog.1') {
					$unanswers[$member_id] = $unanswers[$member_id] - 1;
				} 
				else if ($log['intent_cd'] == 'clear_memberlog.all') {
					$unanswers[$member_id] = null;
				}
				else {
					if ($log['log_flg'] == 1) {
						if ($unanswers[$member_id] == null) {
							$unanswers[$member_id] = 1;
						}
						else {
							$unanswers[$member_id] = $unanswers[$member_id] + 1;
						}
					}
				}
				$log_time　= $log['log_time'];
			}
			*/
		}
		else {
			$sql = 'SELECT max(log_time) AS max_log_time FROM t_bot_log_chat ';
			$sql = $sql . " WHERE " . $this->_create_bot_cond_chat($bot_id);
			$sql = $sql . " AND lang_cd IN $user_lang AND sns_type_cd IN $user_sns AND log_time > :log_time";
			$query = DB::query(Database::SELECT, $sql);
			$query->parameters(array(
					':bot_id' => $bot_id,
					':log_time' => $log_time,
			));
			$results = $query->execute()->as_array();
			
			$sql = 'SELECT member_id, count(log_flg) AS log_count FROM t_bot_log_chat ';
			$sql = $sql . " WHERE " . $this->_create_bot_cond_chat($bot_id);
			$sql = $sql . " AND lang_cd IN $user_lang AND sns_type_cd IN $user_sns AND log_time > :log_time AND log_flg=1";
			$sql = $sql . " GROUP BY bot_id, member_id";
			$query = DB::query(Database::SELECT, $sql);
			$query->parameters(array(
					':bot_id' => $bot_id,
					':log_time' => $log_time,
			));
			$unanswers = $query->execute()->as_array('member_id', 'log_count');
			
			$log_time = $results[0]['max_log_time'];
			
			$sql = "SELECT member.member_id, member.bot_id, member.last_talk_date, member.sns_type_cd, member.lang_cd,
				member.chat_mode, member.request_flg, member.chat_online_status, member.is_tester, member.chat_user_id, user.name,
				member.name AS member_name, member.first_name, member.last_name, member.tags, member.remark, member.country_cd, member.timezone, member.mobile, member.member_no
				FROM t_bot_member member
				LEFT JOIN t_user user ON member.chat_user_id = user.user_id ";
			$sql = $sql . " WHERE " . $this->_create_bot_cond_chat($bot_id, 'member.bot_id');
			$sql = $sql . " AND member.lang_cd IN $user_lang AND member.sns_type_cd IN $user_sns AND member.last_talk_date > :last_member_time ";
			$sql = $sql . " ORDER BY member.last_talk_date, member.member_id";
			$query = DB::query(Database::SELECT, $sql);
			$query->parameters(array(
					':bot_id' => $bot_id,
					':last_member_time' => $member_time,
			));
			$results = $query->execute()->as_array();
			
			
			$member_in_lang = array();   // member last lang_cd
			foreach ($results as $log) {
				if ($log['request_flg'] == 1) $new_unanswer = 2;
				$member_id = 'm-' . strval($log['member_id']);
				$lang_cd = $log['lang_cd'];
				$member_in_lang[$member_id] = $lang_cd;
				if (array_key_exists($lang_cd, $chatlist)) {
					if (array_key_exists($member_id, $chatlist[$lang_cd])) {
						$chatlist[$lang_cd][$member_id] = $this->_get_member_info($log);
					}
					else {
						$chatlist[$lang_cd][$member_id] = $this->_get_member_info($log);
					}
				}
				else {
					$chat = array();
					$chat[$member_id] = $this->_get_member_info($log);
					$chatlist[$lang_cd] = $chat;
					//$new_lang[] = $lang_cd;
				}
				$member_time = $log['last_talk_date'];
			}
		}
		
		//$log_time = $max_log_time;
		$filter_username = $this->_get_session('setting_filter_username');
		$filter_member_no = $this->_get_session('setting_filter_member_no');
		// filter user
		$select_users = $this->_get_session('setting_users');
		// filter tag
		$select_tags = $this->_get_session('setting_tags');
		// filter cond
		$chat_only = $this->_get_session('setting_chat_only');
		if ($chat_only == NULL) {
			$chat_only = 0;
		}
		$chat_req_flg= $this->_get_session('setting_chat_req_flg');
		if ($chat_req_flg == NULL) {
			$chat_req_flg = 0;
		}
		foreach ($chatlist as $chat_lang=>&$chat_members) {
			foreach ($chat_members as $key => $value) {
				// unanswer count
				
				/*
				$member_id = $chat_members[$key]['member_id'];
				if ($first) {
					if (array_key_exists($member_id, $unanswers)) {
						if ($unanswers[$member_id] > $chat_members[$key]['unanswer'] && $new_unanswer == 0) $new_unanswer = 1;
						$chat_members[$key]['unanswer'] = $unanswers[$member_id];
					}
				}
				else {
					 //if ($unanswers[$member_id] > 0) $new_unanswer = 1;
					 //if ($unanswers[$member_id] == null) $chat_members[$key]['unanswer'] = 0;
					 //$chat_members[$key]['unanswer'] = $chat_members[$key]['unanswer'] + $unanswers[$member_id];
				}
				*/
				
				//$chat_members[$key]['unanswer'] = $chat_members[$key]['unanswer'] + $unanswers[$member_id];
				
				/*
				 $bot_member_id = $chat_members[$key]['bot_id'] . '#' .  $chat_members[$key]['member_id'];
				 if (array_key_exists($bot_member_id, $unanswers)) {
				 $chat_members[$key]['unanswer'] = $chat_members[$key]['unanswer'] + $unanswers[$bot_member_id];
				 if ($new_unanswer == 0) $new_unanswer = 1;
				 }
				 */
				//member name filter
				if ($filter_username != "") {
					if (strpos($key, $filter_username) === FALSE && strpos($value['name'], $filter_username) === FALSE) {
						unset($chat_members[$key]);
						continue;
					}
				}
				if ($filter_member_no != "") {
					if (array_key_exists('member_no', $value) && $filter_member_no != $value['member_no']) {
						if (strpos($key, $filter_member_no) === FALSE && strpos($value['name'], $filter_member_no) === FALSE &&
								strpos($value['member_remark'], $filter_member_no) === FALSE) {
							unset($chat_members[$key]);
							continue;
						}
					}
				}
				//remove member from old lang_cd of chatlist when lang_cd changed
				if (array_key_exists($key, $member_in_lang) && $member_in_lang[$key] != $chat_lang) {
					unset($chat_members[$key]);
					continue;
				}
				else  {
					if ($chat_members[$key]['chat_mode'] == 0) {
						// must deal
						if ($chat_req_flg == 1 && $chat_members[$key]['unanswer'] == 0 && $chat_members[$key]['help']==0) {
							unset($chat_members[$key]);
							continue;
						}
						if ($chat_req_flg == 2 && $chat_members[$key]['help'] == 0) {
							unset($chat_members[$key]);
							continue;
						}
					}
					if ($select_users != NULL && !in_array($chat_members[$key]['user_id'], $select_users)) {
						// user filter
						unset($chat_members[$key]);
						continue;
					}
					if ($select_tags != NULL && !in_array($chat_members[$key]['member_tag'], $select_tags)) {
						// user filter
						unset($chat_members[$key]);
						continue;
					}
				}

				$code = $this->get_code('42', Session::instance()->get('lang_cd_admin', NULL));
				
				// wait time refresh
				$wait_time = '';
				$diff_min = floor((strtotime(date("Y-m-d H:i:s")) - strtotime($chat_members[$key]['log_time'])) / 60);
				if ($diff_min > 0) $wait_time = $diff_min . $code['min'];
				$diff_hour = floor((strtotime(date("Y-m-d H:i:s")) - strtotime($chat_members[$key]['log_time'])) / 3600);
				if ($diff_hour> 0) $wait_time = $diff_hour. $code['hour'];
				$diff_day = floor((strtotime(date("Y-m-d H:i:s")) - strtotime($chat_members[$key]['log_time'])) / 86400);
				if ($diff_day> 0) $wait_time = $diff_day. $code['day'];
				$chat_members[$key]['wait_time'] = $wait_time;
			}
			if (count($chat_members) == 0) unset($chatlist[$chat_lang]);
		}
		return $chatlist;
	}
	
	function _get_member_info($log) {
		if ($log['member_name'] == NULL) {
			if ($log['first_name'] != NULL && $log['first_name'] != '') $log['member_name'] = $log['last_name'] . ' ' . $log['first_name'];
		}		
		$r = ['chat_mode'=>$log['chat_mode'], 'help'=>$log['request_flg'], 'chat_online_status' => $log['chat_online_status'],
				'name'=>$log['member_name'],'country_cd'=>$log['country_cd'], 'mobile'=>$log['mobile'], 'member_tag'=>$log['tags'],'member_remark'=>$log['remark'],
			'lang_cd'=>$log['lang_cd'], 'sns_type_cd'=>$log['sns_type_cd'],'is_tester'=>$log['is_tester'],'unanswer'=>0,
			'bot_id'=>$log['bot_id'], 'member_id'=>$log['member_id'], 'log_time'=>$log['last_talk_date'], 'user_id' => $log['chat_user_id'], 'user_name' => $log['name']];
		if (array_key_exists('member_no', $log)) {
			$r['member_no'] = $log['member_no'];
		}
		return $r;
	}
	
	function _get_member_from_chatlist($member_id, $chatlist) {
		foreach($chatlist as $lang_array) {
			if (array_key_exists($member_id, $lang_array)) {
				return $lang_array[$member_id];
			}
		}
		return null;
	}
	
	private function _get_chat_mode($log, $first = false)
	{
		if ($first) {
			$help = 0;
			$mode = 0;
		}
		else {
			$help = -1;
			$mode = -1;
		}
		$mode = $log['chat_mode'];

		if ($log['intent_cd'] == 'request_operator_yes' && $log['log_flg'] == 1) {
			$help = 1;
		}
		if ($log['intent_cd'] == 'user_request_operator_cancel') {
			$help = 0;
		}
		
		$help = $log['request_flg'];
		/*
		if ($log['intent_cd'] == 'change_chatmode.0') {
			$mode = 0;
		}
		else if ($log['intent_cd'] == 'change_chatmode.1') {
			$mode = 1;
		}
		else if ($log['intent_cd'] == 'change_chatmode.2') {
			$mode = 2;
		}
		if ($log['intent_cd'] != 'human mode' && $log['member_msg'] !='') {
			$mode = 0;
		}

		if ($log['intent_cd'] == 'human mode') {
			$mode = $log['chat_mode'];
		}
		*/
		//Log::instance()->add(Log::DEBUG, "LOG:" . $log['member_name'] . '-' . $log['log_time'] . '-' . $log['intent_cd'] . '-' . $fff. 'mode=' . $mode . 'help' . $help);
		return array('chat_mode'=>$mode, 'help'=>$help);
	}

	function get_memberchats_diff($bot_id, $member_id, $log_time, $start_time, $lang_cd, $service_type, $service_id)
	{
		if ($log_time == null) $log_time = '';
		if ($start_time != NULL) {
			$log_table = "t_bot_log$this->_log_ext";
			$add_link_log_id = "";
		}
		else {
			$log_table = "t_bot_log_chat";
			$add_link_log_id = "log.link_log_id, ";
		}
		$bot_class_cd = $this->_bot->bot_class_cd;
		$sql = "SELECT log.member_id, log.sns_type_cd, log.log_time, log.lang_cd,
				 log.member_msg, log.bot_msg, log.member_msg_t, log.bot_msg_t,
				 log.score, log.intent_cd, log.log_id, log.log_flg, log.user_id, log.bot_id, log.scene_cd,
				 log.link_type, log.link_id, " . $add_link_log_id .
				 "member.first_name, member.last_name, member.avatar, member.chat_mode, member.last_access_time, member.name, user.name as username,
				 svc.service_status_cd, svc.member_msg as request_member_msg, svc.intent_cd as request_intent_cd, min.intent_name, COALESCE(mail.mail, member.email) as email" .
                " FROM $log_table log
				LEFT JOIN t_bot_member member
					ON member.bot_id = log.bot_id
					AND member.member_id = log.member_id
				LEFT JOIN t_user user ON log.user_id = user.user_id
				LEFT JOIN t_bot_service svc ON svc.service_id = log.link_id
				LEFT JOIN m_intent min ON min.intent_cd = svc.intent_cd AND min.lang_cd = :lang_cd AND (min.intent_class_cd = :bot_class_cd OR min.intent_class_cd = '00')
				LEFT JOIN t_bot_member_mail mail ON mail.member_id = :member_id AND mail.bot_id = :bot_id AND mail.service_type = :service_type AND mail.service_id = :service_id";
		$sql = $sql . " WHERE " . $this->_create_bot_cond_chat($bot_id, 'log.bot_id');
		$sql = $sql . 
                   " AND log.member_id = :member_id ";
		if ($start_time != NULL) {
			$sql = $sql . " AND log.log_time < :start ORDER BY log.log_time DESC LIMIT 0, 50";
		}
		else {
			$sql = $sql . " AND log.log_time > :log_time ORDER BY log.log_time";
		}
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
				':member_id' => $member_id,
				':log_time' => $log_time,
				':start' => $start_time,
				':lang_cd' => $lang_cd,
				':service_type' => $service_type,
				':service_id' => $service_id,
				':bot_class_cd' => $bot_class_cd,
		));
		$results = $query->execute()->as_array();
		if ($start_time != NULL) {
			return array_reverse($results);
		}
		return $results;
	}

	function get_member_mail_log($type, $member_id) 
	{
		$sql = "SELECT log.link_id
		FROM x_mail_log log
		WHERE type = :type AND member_id = :member_id
		";

		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			":type" => $type,
			":member_id" => $member_id
		));
		$results = $query->execute()->as_array();
		return $results;
	}

	function get_chatmessage($bot_id, $member_id, $intent_cd)
	{
		$sql = "SELECT chat.log_id
		FROM t_bot_log_chat chat
		WHERE chat.bot_id = :bot_id AND chat.member_id = :member_id AND chat.intent_cd  = :intent_cd
		ORDER BY chat.log_time DESC
		LIMIT 1";

		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			":bot_id" => $bot_id,
			":member_id" => $member_id,
			":intent_cd" => $intent_cd
		));
		$results = $query->execute()->as_array();
		return $results;
	}

	function get_member_last_access_time($bot_id, $member_id)
	{
		$sql = "SELECT max(member.last_access_time) AS last_access_time
		FROM t_bot_member member ";
		$sql = $sql . " WHERE " . $this->_create_bot_cond_access($bot_id, 'member.bot_id');
		$sql = $sql . " AND member.member_id = :member_id ";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':member_id' => $member_id,
				':bot_id' => $bot_id,
		));
		$results = $query->execute()->as_array();
		return $results[0]['last_access_time'];
	}
	
	function get_member_latest_info($bot_id, $member_id)
	{
		$sql = "SELECT last_access_time, request_flg, chat_mode, chat_online_status, chat_user_id, 0 AS mobile, tags,
				lang_cd, sns_type_cd, is_tester, bot_id, country_cd, first_name, last_name, name
				 FROM t_bot_member member ";
		$sql = $sql . " WHERE " . $this->_create_bot_cond_access($bot_id, 'member.bot_id');
		$sql = $sql . " AND member.member_id = :member_id ORDER BY last_access_time DESC";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':member_id' => $member_id,
				':bot_id' => $bot_id,
		));
		$results = $query->execute()->as_array();
		if (count($results) == 0) {
			return NULL;
		}
		return $results[0];
	}
	
	function get_business_status($bot_id, $datetime)
	{
		$date = substr($datetime, 0, 8);
		$time = substr($datetime, 8, 4);
		$busi_times = ORM::factory('botbusitime')->where('bot_id', '=', $bot_id)->where('use_flg', '=', 1)->order_by('busi_type_cd', 'DESC')->find_all();

		//祝日マスタ定義を取得
		$holiday_array = $this->get_code_div("888801", "ja");

		foreach($busi_times as $item) {
			if ($item->busi_type_cd == '99') {
				return $item;
			}
			//呼び出しされていないため、念の為、実装しておく（テスト未実施）
			else if ($item->busi_type_cd == '08') {
				foreach($holiday_array as $h) {
					if ($h['class_cd'] == $date) {
						if ( $item->start_time == '0000' && $item->end_time == '0000') {
							// 该日期不营业
							return null;
						} else if ($time >= $item->start_time && $time <= $item->end_time) {
							// 该日期的营业时间内
							return $item;
						}
					}
				}
			}
			else if ($item->busi_type_cd == '10') {
				if ($datetime >= $item->start_date . $item->start_time &&
						$datetime <= $item->end_date . $item->end_time)
					return $item;
			}
			else if ($item->busi_type_cd >='01' && $item->busi_type_cd <='07') {
				$week = date('w', strtotime($datetime));
				if ($week == 0) {
					$week = 7;
				}
				if ($item->busi_type_cd == '0'.$week) {
					if ($time >= $item->start_time && $time <= $item->end_time) {
						return $item;
					}
				}
			}
			else if ($item->busi_type_cd =='00') {
				if ($time >= $item->start_time && $time <= $item->end_time) {
					return $item;
				}
				else {
					if ($item->mode_flg == 1) {
						$item->mode_flg = 2;
						$item->busi_name = $item->busi_name . '(外)';
					}
					return $item;
				}
			}
		}
		return null;
	}

	function get_online_user($bot_id)
	{
		$grp_bot_id = $this->get_grp_bot_id($bot_id); // 親botかどうか判定
		$result = [];
		$user_arr = [];
		$users = ORM::factory("user")->where("bot_id", "=", $bot_id)->find_all();
		foreach($users as $user) {
			if ($user->chat_status_cd == '01' || $user->chat_status_cd == '02') {
				if (date("Y-m-d H:i:s", strtotime("+10 second",strtotime($user->chat_last_time))) < date('Y-m-d H:i:s')) continue;
				if ($grp_bot_id == 0) {
					// 親ボットの場合
					$chat_bot = $user->chat_bot;
					$bot = ORM::factory("bot")->where("bot_id", "=", $chat_bot)->find();
					$user_arr[] = array(
						'user_name' => $user->name,
						'bot_name' => $bot->bot_name
					);
				} else {
					$user_arr[] = $user->name;
				}
				$lang_arr = explode(',', $user->chat_available_lang);
				foreach($lang_arr as $lang_cd) {
					if (array_key_exists($lang_cd, $result)) {
						$result[$lang_cd] = $result[$lang_cd] + 1;
					}
					else {
						$result[$lang_cd] = 1;
					}
				}
			}
		}
		return [$result, $user_arr];
	}

	
	function get_service_information($service_id, $bot_id, $lang_cd)
	{
		$sql = "SELECT t.service_id, t.log_time, t.member_id, m.intent_name, t.member_msg, t.service_status_cd
                FROM t_bot_service t 
				LEFT JOIN m_intent m ON t.intent_cd = m.intent_cd AND m.lang_cd
				WHERE t.bot_id = :bot_id AND t.service_id = :service_id";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
				':service_id' => $service_id,
				':lang_cd' => $lang_cd,
		));
		$results = $query->execute()->as_array();
		return $results;
	}
	
	function update_business_status($bot_id, $no)
	{
		DB::update('t_bot_busitime')->set(array('use_flg'=>0))
		->where('bot_id', '=', $bot_id)
		->where('busi_type_cd', '=', '99')
		->execute();
		if ($no != '')
		DB::update('t_bot_busitime')->set(array('use_flg'=>1))
		->where('bot_id', '=', $bot_id)
		->where('no', '=', $no)
		->execute();
	}
	
	function update_member_chat_mode($bot_id, $member_id, $user_id, $mode=NULL)
	{
		$members = DB::select()->from('t_bot_member')
			->where('bot_id', '=', $bot_id)
			->where('member_id', '=', $member_id)
			->execute()->as_array();
		if (count($members) == 0) return NULL;
		$member = $members[0];
		
		if ($mode === NULL) {
			if ($member['chat_mode'] == 0) {
				$mode = 1;
			}
			else if ($member['chat_mode'] == 1) {
				$mode = 2;
			}
			else {
				$mode = 0;
			}
		}
		$fields = array();
		$fields['chat_mode'] = $mode;
		if ($mode == 0) {
			$fields['chat_user_id'] = null;
			$fields['request_flg'] = 0;
		}
		else {
			$fields['chat_user_id'] = $user_id;
		}

		DB::update('t_bot_member')->set($fields)
			->where('bot_id', '=', $bot_id)
			->where('member_id', '=', $member_id)
			->execute();
		$member['chat_mode'] = $mode;
		return $member;
	}
	
	function update_log($log_id)
	{
		$fields = array();
		$fields['log_flg'] = 0;
		$ret = DB::update('t_bot_log_chat')->set($fields)
		->where('log_id', '=', $log_id)
		->where('log_flg', '=', 1)
		->execute();
		DB::update("t_bot_log$this->_log_ext")->set($fields)
		->where('log_id', '=', $log_id)
		->execute();
		return $ret;
	}

	function update_trans_log($log_id)
	{
		$ret = DB::select('member_msg', 'bot_msg')->from("t_bot_log$this->_log_ext")->where('log_id', '=', $log_id)->execute()->as_array();
		if ($ret[0]['member_msg'] == '') {
			$message = $ret[0]['bot_msg'];
			$type = 'bot';
		}
		else {
			$message = $ret[0]['member_msg'];
			$type = 'member';
		}

		$google_model = Model::factory('google');
		$message_t = nl2br($message);
		//Log::instance()->add(Log::DEBUG, "BF:" . $message_t);
		$message_t = $google_model->translate($message_t, "ja");
		//Log::instance()->add(Log::DEBUG, "AT:" . $message_t);
		$message_t = str_replace("<br />", "\r\n", $message_t);
		//Log::instance()->add(Log::DEBUG, "BR:" . str_replace("<br />", "\r\n", $message_t));
		$fields = array();
		$fields[$type . '_msg_t'] = $message_t;
		DB::update("t_bot_log$this->_log_ext")->set($fields)
			->where('log_id', '=', $log_id)
			->execute();
		DB::update('t_bot_log_chat')->set($fields)
			->where('log_id', '=', $log_id)
			->execute();
		return $message_t;
	}

	function update_member_log($bot_id, $member_id)
	{
		$sql = "UPDATE t_bot_log_chat SET log_flg=0 WHERE member_id=:member_id AND " . $this->_create_bot_cond_chat($bot_id, 'bot_id');
		$query = DB::query(Database::UPDATE, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
				':member_id' => $member_id,
		));
		$query->execute();
		
		$sql = "UPDATE t_bot_log$this->_log_ext SET log_flg=0 WHERE member_id=:member_id AND " . $this->_create_bot_cond_chat($bot_id, 'bot_id');
		$query = DB::query(Database::UPDATE, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
				':member_id' => $member_id,
		));
		$query->execute();
	}

	function update_member_tags($bot_id, $member_id, $tags, $remark, $member_name)
	{
		$fields = array();
		$fields['tags'] = $tags;
		$fields['remark'] = $remark;
		$fields['name'] = $member_name;
		DB::update('t_bot_member')->set($fields)
		->where('bot_id', '=', $bot_id)
		->where('member_id', '=', $member_id)
		->execute();
	}
	
	function update_member_time($bot_id, $member_id)
	{
		//$access_time = $this->get_mstime();
		//DB::update('t_bot_member')->set(['last_talk_date'=>$access_time])->where('bot_id', '=', $bot_id)->where('member_id', '=', $member_id)->execute();
	}
	
	function get_message_menu($bot_id, $lang_cd) {
		$sql = "SELECT m.msg_id, m.msg_cd, m.msg_type_cd, m.msg_class_cd, m.msg_name, d.content, '' as msg_image 
                 FROM t_bot_msg m LEFT JOIN t_bot_msg_desc_txt d
				   ON m.msg_id = d.msg_id
				WHERE m.bot_id = :bot_id AND m.msg_type_cd = 'txt'
					AND d.lang_cd = :lang_cd
				ORDER BY m.msg_class_cd, m.msg_cd";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
				':lang_cd' => $lang_cd,
		));
		$txt = $query->execute()->as_array();
		$sql = "SELECT m.msg_id, m.msg_cd, m.msg_type_cd, m.msg_class_cd, m.msg_name, '' as content, d.msg_image
                 FROM t_bot_msg m LEFT JOIN t_bot_msg_desc_img d
				   ON m.msg_id = d.msg_id
				WHERE m.bot_id = :bot_id AND m.msg_type_cd = 'txt'
					AND d.lang_cd = :lang_cd
				ORDER BY m.msg_class_cd, m.msg_cd";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
				':lang_cd' => $lang_cd,
		));
		$img = $query->execute()->as_array();
		$sql = "SELECT m.msg_id, m.msg_cd, m.msg_type_cd, m.msg_class_cd, m.msg_name, d.content, d.msg_image
                 FROM t_bot_msg m LEFT JOIN t_bot_msg_desc_car d
				   ON m.msg_id = d.msg_id
				WHERE m.bot_id = :bot_id AND m.msg_type_cd = 'txt'
					AND d.lang_cd = :lang_cd
				ORDER BY m.msg_class_cd, m.msg_cd";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
				':lang_cd' => $lang_cd,
		));
		$car = $query->execute()->as_array();
		return $txt+$img+$car;
	}
	
	//有人対応の定型文を取得する（定義した言語のみ出力する）
	function get_message_fix_phrase($bot_id) {
		$sql =
		"SELECT t_bot_msg.msg_cd, t_bot_msg.msg_name, t_bot_msg_desc_txt.lang_cd FROM `t_bot_msg` INNER JOIN t_bot_msg_desc_txt ON t_bot_msg.msg_id = t_bot_msg_desc_txt.msg_id AND t_bot_msg.bot_id = :bot_id AND t_bot_msg_desc_txt.content <> ''  WHERE t_bot_msg.msg_class_cd = 41
	  UNION
	 SELECT t_bot_msg.msg_cd, t_bot_msg.msg_name, t_bot_msg_desc_car.lang_cd  FROM `t_bot_msg` INNER JOIN t_bot_msg_desc_car ON t_bot_msg.msg_id = t_bot_msg_desc_car.msg_id AND t_bot_msg.bot_id = :bot_id AND t_bot_msg_desc_car.title <> ''  WHERE t_bot_msg.msg_class_cd = 41
	  UNION
	 SELECT t_bot_msg.msg_cd, t_bot_msg.msg_name, t_bot_msg_desc_img.lang_cd  FROM `t_bot_msg` INNER JOIN t_bot_msg_desc_img ON t_bot_msg.msg_id = t_bot_msg_desc_img.msg_id AND t_bot_msg.bot_id = :bot_id AND t_bot_msg_desc_img.msg_image <> '' WHERE t_bot_msg.msg_class_cd = 41
	  UNION
	 SELECT t_bot_msg.msg_cd, t_bot_msg.msg_name, t_bot_msg_desc_tpl.lang_cd  FROM `t_bot_msg` INNER JOIN t_bot_msg_desc_tpl ON t_bot_msg.msg_id = t_bot_msg_desc_tpl.msg_id AND t_bot_msg.bot_id = :bot_id AND t_bot_msg_desc_tpl.content <> '' WHERE t_bot_msg.msg_class_cd = 41
	  UNION
	 SELECT t_bot_msg.msg_cd, t_bot_msg.msg_name, t_bot_msg_desc_lst.lang_cd  FROM `t_bot_msg` INNER JOIN t_bot_msg_desc_lst ON t_bot_msg.msg_id = t_bot_msg_desc_lst.msg_id AND t_bot_msg.bot_id = :bot_id AND t_bot_msg_desc_lst.content <> '' WHERE t_bot_msg.msg_class_cd = 41";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
		));
		$results = $query->execute()->as_array();
		return $results;
	}

	function get_message_fix_phrase_new($bot_id) {
		$result = [];
		try {
			$service_url = $this->get_env('service_url');
			$div_item_class_6 = $this->get_bot_setting($bot_id, 'div_item_class_6');
			// カテゴリー分類階層取得
			$ch1 = curl_init();
			curl_setopt($ch1, CURLOPT_URL, $service_url . "content/get_class_code?code_div=$div_item_class_6&lang_cd=ja");
			curl_setopt($ch1, CURLOPT_RETURNTRANSFER, true);
			$categories_response = curl_exec($ch1);
			if (curl_error($ch1)) {
				throw new Exception(curl_error($ch1));
			}
			curl_close($ch1);
			$categories_data = json_decode($categories_response, true);
			if (!$categories_data || $categories_data['result'] != 'success') {
				$result['other'] = [
					'title' => 'その他',
					'items' => []
				];
			} else {
				foreach ($categories_data['data'] as $value) {
					if ($value['class_cd'] == '41') {
						foreach ($value['children'] as $child) {
							$result[$child['class_cd']] = [
								'title' => $child['title'],
								'items' => []
							];
							if (isset($child['children']) && count($child['children']) > 0) {
								$result[$child['class_cd']]['children'] = [];
								foreach ($child['children'] as $sub_child) {
									$result[$child['class_cd']]['children'][$sub_child['class_cd']] = [
										'title' => $sub_child['title'],
										'items' => []
									];
								}
							}
						}
					}
				}
				unset($value);
				$result['other'] = [
					'title' => 'その他',
					'items' => []
				];
			}
			// 有人対応カスタマイズコンテンツを取得
			$ch2 = curl_init();
			curl_setopt($ch2, CURLOPT_URL, $service_url . "content/get_fixed_phrases?bot_id=$bot_id");
			curl_setopt($ch2, CURLOPT_RETURNTRANSFER, true);
			$contents_response = curl_exec($ch2);
			if (curl_errno($ch2)) {
				throw new Exception(curl_error($ch2));
			}
			curl_close($ch2);
			$contents_data = json_decode($contents_response, true);
			if ($contents_data && $contents_data['result'] == 'success') {
				foreach ($contents_data['data'] as $content) {
					$class_cd = $content['cd'] == 41 ? 'other' : $content['cd'];
					if ($class_cd === 'other' || strlen($class_cd) == 4) {
						if (isset($result[$class_cd])) {
							$result[$class_cd]['items'][] = [
								'msg_cd' => $content['id'],
								'msg_name' => $content['title'],
								'lang' => $content['lang']
							];
						}
					} else if (strlen($class_cd) === 6) {
						// 3階層までのカテゴリーに対応
						// uxが悪いので、3階層までのカテゴリーに対応
						$parent_cd = substr($class_cd, 0, 4);
						if (isset($result[$parent_cd]) && isset($result[$parent_cd]['children'][$class_cd])) {
							$result[$parent_cd]['children'][$class_cd]['items'][] = [
								'msg_cd' => $content['id'],
								'msg_name' => $content['title'],
								'lang' => $content['lang']
							];
						} else {
							// カスタマイズコンテンツは3階層に設定されているが、3階層のカテゴリーが存在しない場合、親のカテゴリーに追加
							$result[$parent_cd]['items'][] = [
								'msg_cd' => $content['id'],
								'msg_name' => $content['title'],
								'lang' => $content['lang']
							];
						}
					}
				}
			}
			return $result;
		} catch (\Throwable $th) {
			$result['other'] = [
				'title' => 'その他',
				'items' => []
			];
			return $result;
		}
	}
	
	public $_session_pre = '';
	private function _get_session($key, $pre = '') {
		if ($pre == '') {
			$key = $this->_session_pre . $key;
		}
		else {
			$key = $pre . $this->_session_pre . $key;
		}
		return Session::instance()->get($key, NULL);
	}
}

?>




