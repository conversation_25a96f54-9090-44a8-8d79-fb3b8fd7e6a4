<?php defined('SYSPATH') OR die('No direct script access.');

class Model_Adminverymodel extends Model_Basemodel
{	
    function get_veryuser($bot_id){
		$sql = "SELECT count(distinct(t.member_id)) AS users
                FROM t_bot_follow t
				WHERE t.sns_id = 'very01' AND";
		$grp_bot_id = $this->get_grp_bot_id($bot_id);
		if ($grp_bot_id == 0) {
			$sql = $sql . ' t.bot_id >= :bot_id AND t.bot_id<:bot_id + 1000 ';
		}
		else {
			$sql = $sql . ' t.bot_id = :bot_id ';
		}
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(':bot_id' => $bot_id));
		$result = $query->execute()->as_array()[0]['users'];
		return $result;
	}

    function get_verymember($bot_id){
        $sql = "SELECT count(DISTINCT(m.talkappi_id)) AS member
        FROM t_bot_follow f
        LEFT JOIN t_bot_member m
        ON f.member_id = m.member_id
        WHERE f.sns_id = 'very01' AND m.talkappi_id IS NOT NULL AND";
        $grp_bot_id = $this->get_grp_bot_id($bot_id);
        if ($grp_bot_id == 0) {
            $sql = $sql . ' f.bot_id >= :bot_id AND f.bot_id<:bot_id + 1000 ';
        }
        else {
            $sql = $sql . ' f.bot_id = :bot_id ';
        }
        $query = DB::query(Database::SELECT, $sql);
        $query->parameters(array(':bot_id' => $bot_id));
        $result = $query->execute()->as_array()[0]['member'];
        return $result;
    }

    function read_verystaycontrol($bot_id){
        $record = ORM::factory('verystaycontrol')->where('bot_id', '=',  $bot_id)->find();
        if ($record->seq) {
            return 1;
        } else {
            return 0;
        }
    }

    function read_verystaycontrol_params($bot_id){
        $record = ORM::factory('verystaycontrol')->where('bot_id', '=',  $bot_id)->find();
        if ($record->seq) {
            return $record->params;
        }
    }

    function write_or_delete_verystaycontrol($bot_id,$travel_check_flg, $params = NULL){
        $record_exisit = $this->read_verystaycontrol($bot_id);
        if ($record_exisit == 0 && $travel_check_flg == 1){
            $orm = ORM::factory('verystaycontrol');
            $orm->bot_id = $bot_id;
            if($params){
                $orm->params = $params;
            }
            $orm->save();
            return;
        }

        if ($record_exisit == 1){
            DB::delete('t_very_stay_control')->where('bot_id', '=', $bot_id)->execute();
            if($travel_check_flg == 1){
                $orm = ORM::factory('verystaycontrol');
                $orm->bot_id = $bot_id;
                if($params){
                    $orm->params = $params;
                }
                $orm->save();
            }
            return;
        }
    }

    function create_or_update_very_call_setting($bot_id, $post, $scene_cd, $user_id) {
        $web_call_flg = (string) (isset($post['web_call_flg']) ? $post['web_call_flg']: "0");
        if (!in_array($web_call_flg, ["0", "1"])) {
            return ;
        }
        $web_call_room = (string) (isset($post['web_call_room']) ? $post['web_call_room']: "");
        $params = json_encode(array(
            "call" => $web_call_flg,
            "rooms" => $web_call_room
        ));

        $existingRecords = $this->read_very_call_setting($bot_id, $scene_cd);
        if ($existingRecords === false && $web_call_flg === "1") {
            $insert_data = array(
                'bot_id' => $bot_id,
                'page' => 'top',
                'setting' => 'call',
                'value' => $params,
                'scene_cd' => $scene_cd,
                'upd_user'=> $user_id,
                'upd_time'=>date('Y-m-d H:i:s',time())
            );
            DB::insert('t_very', array_keys($insert_data))
              ->values(array_values($insert_data))
              ->execute();
        } elseif ($existingRecords !== false) {
            if ($web_call_flg === "1") {
                DB::update('t_very')
                  ->set(array(
                      'value' => $params,
                      'upd_user'=> $user_id,
                      'upd_time'=>date('Y-m-d H:i:s',time())
                  ))
                  ->where('bot_id', '=', $bot_id)
                  ->where('page', '=', 'top')
                  ->where('setting', '=', 'call')
                  ->where('scene_cd', '=', $scene_cd)
                  ->execute();
            } else {
                DB::update('t_very')
                  ->set(array(
                      'value' => json_encode(array(
                        "call" => "0",
                        "rooms" => ""
                      )),
                      'upd_user'=> $user_id,
                      'upd_time'=>date('Y-m-d H:i:s',time())
                  ))
                  ->where('bot_id', '=', $bot_id)
                  ->where('page', '=', 'top')
                  ->where('setting', '=', 'call')
                  ->where('scene_cd', '=', $scene_cd)
                  ->execute();
            }
        }
    }

    function read_very_call_setting($bot_id, $scene_cd){
        $result = DB::select('value')
                    ->from('t_very')
                    ->where('bot_id', '=', $bot_id)
                    ->and_where('page', '=', 'top')
                    ->and_where('setting', '=', 'call')
                    ->and_where('scene_cd', '=', $scene_cd)
                    ->execute()
                    ->as_array();
        if (!empty($result)) {
            $decoded_value = json_decode($result[0]['value'], true);
            return $decoded_value;
        }
        return false;
    }

    function get_veryguest($bot_id){
        $user = $this->get_veryuser($bot_id);
        $member = $this->get_verymember($bot_id);
        $result = $user - $member;
        return $result;
    }

    function get_top_clicks($bot_id) {
        $end_date = date("Y-m-d", strtotime("+1 day"));
        $start_date = date("Y-m-d", strtotime("-1 months +1 day"));
        $sql = "SELECT operate_detail, COUNT(x.operate_detail) AS amount
        FROM x_very_log x
        WHERE operate_div = 'categories' AND x.ip_address IS NOT NULL AND operate_time between :start_date AND :end_date AND x.bot_id = :bot_id 
        GROUP BY operate_detail ORDER BY COUNT(operate_detail) DESC";
        $query = DB::query(Database::SELECT, $sql);
        $query->parameters(array(':bot_id' => $bot_id, ':start_date' => $start_date, ':end_date' => $end_date));
        $result = $query->execute()->as_array();
        return $result;
    }

    function get_main_clicks($bot_id, $start_date, $end_date) {
        $sql = "SELECT operate_time as date, operate_detail as title
        FROM x_very_log x
        WHERE operate_div = 'functions' AND x.ip_address IS NOT NULL AND operate_time between :start_date AND :end_date AND x.bot_id = :bot_id 
        ORDER BY operate_detail";
        $query = DB::query(Database::SELECT, $sql);
        $query->parameters(array(':bot_id' => $bot_id, ':start_date' => $start_date, ':end_date' => $end_date));
        $result = $query->execute()->as_array();
        return $result;
    }

    function get_print_media_settings($type){
        $sql = "SELECT m.description,m.settings
        FROM  m_print_media m
        WHERE type =:type";
        $query = DB::query(Database::SELECT, $sql);
        $query->parameters(array(':type' => $type));
        $result = $query->execute()->as_array();
        return $result;
    }
    
    function get_very_setting_bot_list($lang_cd) {
        $sql ="SELECT DISTINCT t_bot.bot_id, t_bot.bot_name 
        FROM t_very_settings 
        INNER JOIN t_bot ON t_bot.bot_id = t_very_settings.bot_id AND t_bot.delete_flg = 0 
        WHERE t_very_settings.lang_cd=:lang_cd 
        ORDER BY t_bot.bot_id";

        $query = DB::query(Database::SELECT, $sql);
        $query->parameters(array(':lang_cd' => $lang_cd));
        $result = $query->execute()->as_array();
        return $result;
    }

    function get_reception_list_information($bot_id, $lang_cd, $reception_id) {
        $sql = "SELECT r.reception_id, r.reception_data, r.pause_flg, r.item_id, r.printer_settings, d.title
        FROM t_reception r
        LEFT JOIN t_reception_description d
        ON r.reception_id = d.reception_id AND d.lang_cd = :lang_cd
        WHERE r.bot_id = :bot_id AND r.reception_id = :reception_id";
        
        $query = DB::query(Database::SELECT, $sql);
        $query->parameters(array(':bot_id' => $bot_id, ':lang_cd' => $lang_cd, ':reception_id' => $reception_id));
        $result = $query->execute()->as_array();
        return $result;
    }
    
    function get_reception_list_items($bot_id, $reception_id) {
        $sql = "SELECT  l.reception_no, l.reception_time, l.upd_time, l.status_cd, l.seq
        FROM t_reception r
        LEFT JOIN t_reception_list l
        ON r.reception_id = l.reception_id
        WHERE r.bot_id = :bot_id AND r.reception_id = :reception_id AND l.reception_time >= CURDATE()
        ORDER BY l.status_cd DESC, l.reception_time ASC";
        
        $query = DB::query(Database::SELECT, $sql);
        $query->parameters(array(':bot_id' => $bot_id, ':reception_id' => $reception_id));
        $result = $query->execute()->as_array();
        return $result;
    }

    /**
     * 指定された受付IDと言語コードに基づいて受付エントリを取得します。
     *
     * @param int $reception_id 受付ID
     * @param mixed $lang_cd 言語コード（単一の言語コードまたは言語コードの配列）
     * @return array 受付エントリの配列
     */
    function get_reception_entry($reception_id, $lang_cd) {
        // ベースのSQL（共通部分）
        $sql = "SELECT no, title, entry_type_cd, required, input_rules, entry_data, lang_cd
            FROM t_reception_entry
            WHERE reception_id = :reception_id
            AND delete_flg=0";

        $query_params = [':reception_id' => $reception_id];

        // $lang_cd が配列の場合、IN 句を使用
        if (is_array($lang_cd)) {
            // IN句のプレースホルダーを生成
            $placeholders = [];
            foreach ($lang_cd as $index => $value) {
                $param_name = ":lang_cd" . $index;
                $placeholders[] = $param_name;
                $query_params[$param_name] = $value;
            }

            // IN句を適用
            $sql .= " AND lang_cd IN (" . implode(',', $placeholders) . ")";
        } else {
            $sql .= " AND lang_cd = :lang_cd";
            $query_params[':lang_cd'] = $lang_cd;
        }

        $sql .= " ORDER BY no ASC";

        // クエリ実行
        $query = DB::query(Database::SELECT, $sql);
        $query->parameters($query_params);

        return $query->execute()->as_array();
    }

    function get_reception_items($bot_id, $lang_cd, $pause=null, $not_public=null) {
        $sql = "SELECT r.reception_id, r.reception_data,r.item_id, r.upd_time, r.upd_user, r.display_flg, r.pause_flg, d.title, id.item_name, c.name AS upd_user_name,
        (
            SELECT
              tbs.setting_value
            FROM t_bot_setting tbs
            WHERE
              (tbs.bot_id = :bot_id OR tbs.bot_id = 0)
              AND (tbs.setting_cd = concat('div_item_class_',i.item_div) OR tbs.setting_cd = concat('div_item_class_',r.item_div))
            ORDER BY
              tbs.bot_id DESC
            LIMIT 1
          ) as class_code_div,
          (
            SELECT
                GROUP_CONCAT(ic.name)
            FROM m_class_code ic
            WHERE
                ic.code_div = class_code_div
                AND ic.lang_cd = :lang_cd
                AND (find_in_set(ic.class_cd,replace(i.class_cd,' ',',')) OR find_in_set(ic.class_cd,replace(r.class_cd,' ',',')))
            GROUP BY
                ic.code_div
            ) AS tags,
            (
                SELECT  count(l.reception_no)
                FROM t_reception_list l
                WHERE l.reception_id = r.reception_id AND l.reception_time >= CURDATE() AND l.status_cd IN (1,2)
            ) AS waiting_count
        FROM t_reception r 
		LEFT JOIN t_user c ON r.upd_user = c.user_id
        LEFT JOIN t_reception_description d ON r.reception_id = d.reception_id AND d.lang_cd = :lang_cd 
        LEFT JOIN t_item i ON i.bot_id = :bot_id AND r.item_id = i.item_id
        LEFT JOIN t_item_description id ON i.item_id = id.item_id AND id.lang_cd = :lang_cd 
        WHERE r.bot_id = :bot_id AND ((d.title IS NOT NULL) OR (id.item_name IS NOT NULL)) AND r.delete_flg <> 1";

        if ($pause == '1') { // 一時停止中
            $sql .= " AND (r.pause_flg = 1 OR r.pause_flg = 2) AND r.display_flg = 1 ";
        } else if ($not_public == '1') { // 非公開
            $sql .= " AND (r.pause_flg = 0 OR r.pause_flg = 1 OR r.pause_flg = 2) AND r.display_flg = 0 ";
        } else { // 受付中
            $sql .= " AND r.pause_flg = 0 AND r.display_flg = 1 ";
        }
        $sql .= " ORDER BY r.upd_time DESC";
        $query = DB::query(Database::SELECT, $sql);
        $query->parameters(array(':bot_id' => $bot_id, ':lang_cd' => $lang_cd));
        $result = $query->execute()->as_array();
        return $result;
    }

    function get_all_receptions_by_pause_status() {
        $sql = "SELECT r.reception_id, r.reception_data,r.pause_flg,
            (
                SELECT  count(l.reception_no)
                FROM t_reception_list l
                WHERE l.reception_id = r.reception_id AND l.reception_time >= CURDATE() AND l.status_cd IN (1,2)
            ) AS waiting_count,
            (
                SELECT  count(l.reception_no)
                FROM t_reception_list l
                WHERE l.reception_id = r.reception_id AND l.reception_time >= CURDATE()
            ) AS reception_list_count
        FROM t_reception r 
        WHERE r.delete_flg <> 1 
        AND r.pause_flg <> 1
        AND JSON_EXTRACT(r.reception_data, '$.auto_pause') = '1'
        AND JSON_EXTRACT(r.reception_data, '$.waiting') IS NOT NULL
        AND JSON_EXTRACT(r.reception_data, '$.waiting.time') != ''
        AND JSON_EXTRACT(r.reception_data, '$.waiting.party') != ''
        HAVING reception_list_count > 0
        ";

        $query = DB::query(Database::SELECT, $sql);
        $query->parameters(array());
        $result = $query->execute()->as_array();
        return $result;
    }

    function reception_display_setting($reception_id, $bot_id, $lang_cd) {
        $sql = "SELECT 
        JSON_EXTRACT(r.reception_data, '$.display') AS display_item,
        d.display_text,
        JSON_EXTRACT(r.reception_data, '$.display_style') AS display_style
        FROM 
            t_reception r
        LEFT JOIN 
            (SELECT reception_id, JSON_EXTRACT(settings, '$.display_text') AS display_text
            FROM t_reception_description
            WHERE lang_cd = :lang_cd) d ON r.reception_id = d.reception_id
        WHERE 
        r.reception_id = :reception_id AND r.bot_id = :bot_id;";
        $query = DB::query(Database::SELECT, $sql);
        $query->parameters(array(':bot_id' => $bot_id, ':reception_id' => $reception_id, ':lang_cd' => $lang_cd));
        $result = $query->execute()->as_array();
        if (count($result) > 0) {
            return $result[0];
        } else {
            return false;
        }
    }

    function update_reception_pause_flg($reception_id, $pause_flg) {
		DB::update('t_reception')->set([
            'pause_flg'=>$pause_flg,
            'upd_time'=>date('Y-m-d H:i:s'),
            ])->where('reception_id', '=', $reception_id)->execute();
    }

    function count_waiting_time($time, $party, $count) {
        if ($count == 0) return 0;
        if (!$time || !$party) return 0;

        return round($time * $count / $party);
    }

    function send_line_notify($user_code, $user_token, $content)
    {
        $url = "https://api.line.me/v2/bot/message/push";
        $headers = array(
            "Content-Type: application/json",
            "Authorization: Bearer " . $user_token
        );

        $data = array(
            "to" => $user_code,
            "messages" => array(
                array(
                    "type" => "text",
                    "text" => $content
                )
            )
        );
        $json_data = json_encode($data);

        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $json_data);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_VERBOSE, true);

        $response = curl_exec($ch);
        $error = curl_error($ch);

        curl_close($ch);

        if ($error) {
            return $error;
        } else {
            return $response;
        }
    }

    function get_very_user_statics($start_date, $end_date) {
		$formatted_start_date = date('Y-m-d');
		$formatted_end_date = date('Y-m-d');

        $sql = "SELECT b.bot_name AS botName, a.bot_id AS botID, COUNT(c.id) AS operateCount
        FROM t_bot_setting a
        LEFT JOIN t_bot b ON b.bot_id = a.bot_id
        LEFT JOIN x_very_log c ON c.bot_id=a.bot_id
        WHERE a.setting_cd='flg_very' AND a.setting_value='1'";

        $unique_user_sql = "SELECT a.bot_id, COUNT(DISTINCT(a.member_id)) AS userCount FROM t_bot_follow a WHERE a.sns_id='very01' AND a.bot_id IN :ids";

		if ($start_date != '') {
			$formatted_start_date = date("Y-m-d", strtotime($start_date));
            $sql .= ' AND date_format(c.operate_time, "%Y-%m-%d") >= :formatted_start_date';
            $unique_user_sql .= ' AND date_format(a.follow_time, "%Y-%m-%d") >= :formatted_start_date';
		}
		if ($end_date != '') {
			$formatted_end_date = date("Y-m-d", strtotime("+1 day", strtotime($end_date)));
            $sql .= ' AND date_format(c.operate_time, "%Y-%m-%d") < :formatted_end_date';
            $unique_user_sql .= ' AND date_format(a.follow_time, "%Y-%m-%d") < :formatted_end_date';
		}
		$sql .= " GROUP BY botName ORDER BY a.bot_id ASC";
        $unique_user_sql .= " GROUP BY a.bot_id ORDER BY a.bot_id ASC";


		$query1 = DB::query(Database::SELECT, $sql);
		$query1->parameters(array(
			':formatted_start_date' => $formatted_start_date,
			':formatted_end_date' => $formatted_end_date,
		));
		$veryResult = $query1->execute()->as_array();
        $ids = [];
        foreach ($veryResult as $k => $result) {
            $ids[] = $result['botID'];
            $veryResult[$k]['userCount'] = 0;
        }
        
        if (count($ids) > 0) {
            $query2 = DB::query(Database::SELECT, $unique_user_sql);
            $query2->parameters(array(
                ':ids' => $ids,
                ':formatted_start_date' => $formatted_start_date,
                ':formatted_end_date' => $formatted_end_date,
            ));

            $unique_user_result = $query2->execute()->as_array();

            foreach ($unique_user_result as $key => $value) {
                foreach ($veryResult as $key => $result) {
                    if ($result['botID'] == $value['bot_id']) {
                        $veryResult[$key]['userCount'] = $value['userCount'];
                        break;
                    }
                }
                $veryResult[$key]['userCount'] = $value['userCount'];
            }
        }
		return $veryResult;
	}
    
    function get_reception_no($reception_id) {
        if (!$reception_id) return;
      
        $receptionLastNo = $this->getLastReceptionNo($reception_id);
        $receptionNoPrefix =  $this->getReceptionNoPrefix($reception_id);
      
        if ($receptionLastNo) {
          // 現在の最新番号はT0001で、管理画面上でprefixをKNNNNに変えた場合は新たに発行するため対象外
          if (
            !$receptionNoPrefix ||
            (substr($receptionLastNo, 0, 1) === substr($receptionNoPrefix, 0, 1) &&
              strlen($receptionLastNo) === strlen($receptionNoPrefix))
          )
            return $this->getNewRecptionNo($receptionLastNo);
        }
      
        if ($receptionNoPrefix) {
          return  $this->createReceptionNo($receptionNoPrefix);
        } else {
          return '0001';
        }
    }

    function createReceptionNo($data) {
        $prefix = substr($data, 0, 1);
        $suffix = substr($data, 1);
      
        return $prefix . str_repeat('0', strlen($suffix) - 1) . '1';
    }
    
    function getNewRecptionNo($data) {
        $prefix = substr($data, 0, 1);
        $suffix = substr($data, 1);
        $newNum = intval($suffix) + 1;
        $formattedNum = str_pad($newNum, strlen($suffix), '0', STR_PAD_LEFT);
      
        return $prefix . $formattedNum;
    }
    
    function getLastReceptionNo($reception_id) {
        $result = DB::select('*')
            ->from('t_reception_list')
            ->where('reception_id', '=', $reception_id)
            ->and_where(DB::expr("DATE_FORMAT(reception_time, '%Y-%m-%d')"), '=', DB::expr("DATE_FORMAT(NOW(), '%Y-%m-%d')"))
            ->order_by('seq', 'DESC')
            ->limit(1)
            ->execute()
            ->as_array();
    
        if (count($result) > 0) return $result[0]['reception_no'];
        return null;
    }
    
    function getReceptionNoPrefix($reception_id) {
        $result = DB::select('reception_data')
            ->from('t_reception')
            ->where('reception_id', '=', $reception_id)
            ->execute()
            ->as_array();
    
        if (count($result) > 0) {
            $receptionData = json_decode($result[0]['reception_data'], true);
            return isset($receptionData['reception_no_prefix']) ? $receptionData['reception_no_prefix'] : null;
        }
    }
    
    function get_very_report_user($bot_id, $scene_cd, $lang_cd, $group_by_type, $start_time = null, $end_time = null)
    {
        // 日付範囲の指定がない場合は、今月までの1年分のデータを取得する
        if ($start_time === null || $end_time === null) {
            $start_time = date('Y-m-01', strtotime('-11 months'));
            $end_time = date('Y-m-t 23:59:59', strtotime(date('Y-m-01')));
        }
        // contract_start_date（契約開始日）が start_time より後の場合、start_time の値を契約開始日に変更する
        $contract_start_date = $this->get_very_contract_date($bot_id);
        if ($contract_start_date) {
            if (strtotime($contract_start_date) > strtotime($start_time)) {
                $start_time = $contract_start_date;
            }
        }
        // グループ条件
        if ($group_by_type === 'lang') {
            $group_by = "lang_cd";
        }
        if ($group_by_type === 'month') {
            $group_by = "DATE_FORMAT(follow_time, '%Y-%m')";
        }

        $sql = "SELECT $group_by AS label, count(distinct(member_id)) AS `usage`
                FROM t_bot_follow 
                WHERE sns_id = 'very01' 
                AND bot_id = :bot_id 
                AND follow_time BETWEEN :start_time AND :end_time";

        if ($scene_cd != '') {
            $sql .= " AND scene = :scene_cd";
        }

        $sql .= " GROUP BY $group_by";

        $query = DB::query(Database::SELECT, $sql);
        $query->parameters(array(
            ':bot_id' => $bot_id,
            ':start_time' => $start_time,
            ':end_time' => $end_time,
            ':scene_cd' => $scene_cd
        ));

        $result = $query->execute()->as_array();

        $formatted_result = array();
        // 言語別データ
        if ($group_by_type === 'lang') {
            $code = $this->get_code('02', $lang_cd);
            foreach ($result as $row) {
                $label = isset($code[$row['label']]) ? $code[$row['label']] : $row['label'];
                $formatted_result[] = ['label' => $label, 'usage' => (int)$row['usage']];
            }
        }
        // 月別データ
        if ($group_by_type === 'month') {
            $all_months = $this->generate_month_array($start_time, $end_time);
            $monthly_usage = [];
            foreach ($all_months as $month) {
                $monthly_usage[$month] = ['label' => $month, 'usage' => 0];
            }
            foreach ($result as $row) {
                $label = isset($code[$row['label']]) ? $code[$row['label']] : $row['label'];
                if (isset($monthly_usage[$label])) {
                    $monthly_usage[$label]['usage'] = (int)$row['usage'];
                }
            }
            $formatted_result = array_values($monthly_usage);
        }
        return $formatted_result;
    }

    private function fetch_scene_codes_with_datas($bot_id)
    {
        $sql = "SELECT DISTINCT v.scene_cd, b.label
            FROM t_very_settings v
            LEFT JOIN t_bot_scene b ON b.scene_name = v.scene_cd
            WHERE v.bot_id = :bot_id
            AND v.scene_cd IS NOT NULL
            AND v.scene_cd != '' 
            AND b.label IS NOT NULL
            ORDER BY b.sort_no DESC";

            return DB::query(Database::SELECT, $sql)
            ->param(':bot_id', $bot_id)
            ->execute()
            ->as_array();
    }

    function get_scene_cds_has_data($bot_id, $all=true)
    {
        $scene_cds = $this->fetch_scene_codes_with_datas($bot_id);
        $formatted_data = $all ? ['' => __('admin.common.label.all_user_flows')] : [];
        foreach ($scene_cds as $row) {
            $formatted_data[$row['scene_cd']] = $row['label'];
        }

        return json_encode($formatted_data);
    }

    function get_scene_cds_has_data_for_qr($bot_id)
    {
        $scene_cds = $this->fetch_scene_codes_with_datas($bot_id);
        $formatted_data = ['' => '-'];
        foreach ($scene_cds as $row) {
            $formatted_data[$row['scene_cd']] = $row['label'];
        }

        return json_encode($formatted_data);
    }

    function get_very_report_clicks($bot_id, $scene_cd, $start_time = null, $end_time = null)
    {
        // 日付範囲の指定がない場合は、今月までの1年分のデータを取得する
        if ($start_time === null || $end_time === null) {
            $start_time = date('Y-m-01', strtotime('-11 months'));
            $end_time = date('Y-m-t 23:59:59', strtotime(date('Y-m-01')));
        }
        // contract_start_date（契約開始日）が start_time より後の場合、start_time の値を契約開始日に変更する
        $contract_start_date = $this->get_very_contract_date($bot_id);
        if ($contract_start_date) {
            if (strtotime($contract_start_date) > strtotime($start_time)) {
                $start_time = $contract_start_date;
            }
        }

        $snsName = [
            "instagram" => "Instagram", 
            "facebook" => "Facebook", 
            "line" => "LINE", 
            "tiktok" => "TikTok",
            "youtube" => "YouTube", 
            "twitter" => "X (Twitter)"
        ];

        $sql = "SELECT lang_cd, operate_div, operate_detail, n.notice_name, DATE_FORMAT(operate_time, '%Y-%m') AS label, COUNT(*) AS `usage`
                FROM x_very_log x
                LEFT JOIN t_notice n ON n.notice_id=(convert(json_extract(x.operate_detail, '$.notice_id'), UNSIGNED))
                WHERE x.ip_address IS NOT NULL 
                AND operate_time BETWEEN :start_time AND :end_time 
                AND x.bot_id = :bot_id
                AND x.operate_div != 'restaurant'";

        if ($scene_cd != '') {
            $sql .= " AND x.scene = :scene_cd";
        }

        $sql .= " GROUP BY lang_cd, operate_div, operate_detail, label, n.notice_name";

        $query = DB::query(Database::SELECT, $sql);
        $query->parameters(array(':bot_id' => $bot_id, ':start_time' => $start_time, ':end_time' => $end_time, ':scene_cd' => $scene_cd));
        $result = $query->execute()->as_array();

        $all_months = $this->generate_month_array($start_time, $end_time);
        $click_counts = array();
        foreach ($result as $row) {
            $lang_cd = $row['lang_cd'];
            $operate_div = $row['operate_div'];
            $detail = json_decode($row['operate_detail'], true);
            if ($operate_div === 'notice') {
                $value = '(' . $detail['notice_id'] . ')' . $row['notice_name'];
            } else if ($operate_div === 'sns') {
                $value = isset($snsName[$detail['title']]) ? $snsName[$detail['title']] : $detail['title'];

            } else {
                $value = $detail['title'];
            }
            $label = $row['label'];
            if ($operate_div === 'functions') {
                $type = 'new';
                $old_functions = ['chatbot', 'reserve', 'faq', 'request', 'congestion', 'checkout', 'userguide', 'customize_type_url', 'customize_type_congestion', 'customize_type_customize_content', 'customize_type_market', 'customize_type_page', 'customize_type_survey'];
                if (in_array($value, $old_functions)) {
                    $type = 'old';
                }
                if (!isset($click_counts[$lang_cd][$operate_div][$type][$value])) {
                    foreach ($all_months as $month) {
                        $click_counts[$lang_cd][$operate_div][$type][$value][] = ['label' => $month, 'usage' => 0];
                    }
                }
                $index = array_search($label, array_column($click_counts[$lang_cd][$operate_div][$type][$value], 'label'));
                if ($index !== false) {
                    $click_counts[$lang_cd][$operate_div][$type][$value][$index]['usage'] = (int)$row['usage'];
                }
            } else {
                if (!isset($click_counts[$lang_cd][$operate_div][$value])) {
                    foreach ($all_months as $month) {
                        $click_counts[$lang_cd][$operate_div][$value][] = ['label' => $month, 'usage' => 0];
                    }
                }
                $index = array_search($label, array_column($click_counts[$lang_cd][$operate_div][$value], 'label'));
                if ($index !== false) {
                    $click_counts[$lang_cd][$operate_div][$value][$index]['usage'] = (int)$row['usage'];
                }
            }
        }
        return $click_counts;
    }

    function get_very_report_clicks_for_table($bot_id, $scene_cd, $start_time = null, $end_time = null) {
        $click_counts = $this->get_very_report_clicks($bot_id, $scene_cd, $start_time, $end_time);
        // データ整形
        $very_report_clicks_data_table = array();
        $all_months = array();
        foreach ($click_counts as $lang_cd => $operate_divs) {
            foreach ($operate_divs as $operate_div => $values) {
                if ($operate_div === 'functions') {
                    foreach ($values as $type => $records) {
                        foreach ($records as $value => $records2) {
                            $row = array('lang_cd' => $lang_cd, 'category' => $operate_div, 'type' => $type, 'title' => $value);
                            foreach ($records2 as $record) {
                                $row[$record['label']] = $record['usage'];
                                $all_months[$record['label']] = true;
                            }
                            $very_report_clicks_data_table[] = $row;
                        }
                    }
                } else {
                    foreach ($values as $value => $records) {
                        $row = array('lang_cd' => $lang_cd, 'category' => $operate_div, 'title' => $value);
                        foreach ($records as $record) {
                            $row[$record['label']] = $record['usage'];
                            $all_months[$record['label']] = true;
                        }
                        $very_report_clicks_data_table[] = $row;
                    }
                }
            }
        }
        // 合計列
        foreach ($very_report_clicks_data_table as &$row) {
            $row_total = 0;
            foreach ($row as $key => $value) {
                if (!in_array($key, ['lang_cd', 'category', 'title', 'type'])) {
                    $row_total += $value;
                }
            }
            $row['Total'] = $row_total;
        }
        unset($row);
        // 合計行（仕様検討中）
        // $total_rows = [];
        // foreach ($click_counts as $lang_cd => $operate_divs) {
        //     foreach ($operate_divs as $operate_div => $values) {
        //         $total_row = array('lang_cd' => $lang_cd, 'category' => $operate_div, 'title' => 'Total', 'isTotalRow' => true);
        //         foreach (array_keys($all_months) as $month) {
        //             $total_row[$month] = array_sum(array_column(array_filter($very_report_clicks_data_table, function ($row) use ($lang_cd, $operate_div) {
        //                 return $row['lang_cd'] === $lang_cd && $row['category'] === $operate_div;
        //             }), $month));
        //         }
        //         $total_row['Total'] = array_sum(array_column(array_filter($very_report_clicks_data_table, function ($row) use ($lang_cd, $operate_div) {
        //             return $row['lang_cd'] === $lang_cd && $row['category'] === $operate_div;
        //         }), 'Total'));
        //         $total_rows[] = $total_row;
        //     }
        // }
        // 合計行をデータに追加
        // foreach ($total_rows as $total_row) {
        //     $very_report_clicks_data_table[] = $total_row;
        // }
    
        $sorted_months = array_keys($all_months);
        sort($sorted_months);
        // 合計列をカラムに追加
        $columns = array(
            array('Header' => __('admin.veryreport.item_name'), 'accessor' => 'title')
        );
        foreach ($sorted_months as $month) {
            $columns[] = array('Header' => $month, 'accessor' => $month);
        }
        $columns[] = array('Header' => __('admin.veryreport.total'), 'accessor' => 'Total', 'isTotalColumn' => true);
    
        return array('data' => $very_report_clicks_data_table, 'columns' => $columns);
    }

    function get_very_report_users_for_table($bot_id, $scene_cd, $lang_cd, $start_time = null, $end_time = null) {
       // 日付範囲の指定がない場合は、今月までの1年分のデータを取得する
       if ($start_time === null || $end_time === null) {
            $start_time = date('Y-m-01', strtotime('-11 months'));
            $end_time = date('Y-m-t 23:59:59', strtotime(date('Y-m-01')));
        }
        // contract_start_date（契約開始日）が start_time より後の場合、start_time の値を契約開始日に変更する
        $contract_start_date = $this->get_very_contract_date($bot_id);
        if ($contract_start_date) {
            if (strtotime($contract_start_date) > strtotime($start_time)) {
                $start_time = $contract_start_date;
            }
        }

        $sql = "SELECT COUNT(DISTINCT(member_id)) as `usage`, lang_cd, DATE_FORMAT(follow_time, '%Y-%m') AS label
            FROM t_bot_follow 
            WHERE sns_id = 'very01' 
            AND bot_id = :bot_id 
            AND follow_time BETWEEN :start_time AND :end_time";

        if ($scene_cd != '') {
            $sql .= " AND scene = :scene_cd";
        }

        $sql .= " GROUP BY lang_cd, DATE_FORMAT(follow_time, '%Y-%m')";

        $query = DB::query(Database::SELECT, $sql);
        $query->parameters(array(
            ':bot_id' => $bot_id,
            ':start_time' => $start_time,
            ':end_time' => $end_time,
            ':scene_cd' => $scene_cd
        ));
        $results = $query->execute()->as_array();

        $very_report_users_data_table = array();
        
        $start_month_first_day = (new DateTime($start_time))->modify('first day of this month')->format('Y-m-d');
        $start = new DateTime($start_month_first_day);
        $end = new DateTime($end_time);
        $all_months = array();
        while ($start <= $end) {
            $all_months[] = $start->format('Y-m');
            $start->modify('+1 month');
        }

        foreach($results as $result) {
            $very_report_users_data_table[$result['lang_cd']][$result['label']] = (int)$result['usage'];
            $very_report_users_data_table[$result['lang_cd']]['title'] = $result['lang_cd'];
            $very_report_users_data_table[$result['lang_cd']]['Total'] = isset($very_report_users_data_table[$result['lang_cd']]['Total']) ? $very_report_users_data_table[$result['lang_cd']]['Total'] + (int)$result['usage'] : (int)$result['usage'];
        }

        foreach ($very_report_users_data_table as &$languageData) {
            $languageData = array_merge(array_fill_keys($all_months, 0), $languageData);
        }

        sort($all_months);
        $columns = array(
            array('Header' => __('admin.veryreport.item_name'), 'accessor' => 'title')
        );
        foreach ($all_months as $month) {
            $columns[] = array('Header' => $month, 'accessor' => $month);
        }
        $columns[] = array('Header' => __('admin.veryreport.total'), 'accessor' => 'Total', 'isTotalColumn' => true);
    
        return array('data' => $very_report_users_data_table, 'columns' => $columns);
    }
    
    function get_very_contract_date($bot_id)
    {
        // 利用開始日の取得
        $sql = "SELECT start_date 
                FROM t_facility_options 
                WHERE option_cd = 'very' 
                AND contract_type = '01' 
                AND bot_id = :bot_id";
        $query = DB::query(Database::SELECT, $sql);
        $query->parameters(array(':bot_id' => $bot_id));
        $results = $query->execute()->as_array();

        if (!empty($results)) {
            return $results[0]['start_date'];
        }
        return null;
    }

    function get_very_report_user_for_export_csv($bot_id, $scene_cd, $admin_lang_cd, $format, $start_time, $end_time)
    {
        // 日付範囲の指定がない場合は、今月までの1年分のデータを取得する
        if (empty($start_time) || empty($end_time)) {
            $start_time = date('Y-m-01', strtotime('-11 months'));
            $end_time = date('Y-m-t 23:59:59', strtotime(date('Y-m-01')));
        }
        // contract_start_date（契約開始日）が start_time より後の場合、start_time の値を契約開始日に変更する
        $contract_start_date = $this->get_very_contract_date($bot_id);
        if ($contract_start_date) {
            if (strtotime($contract_start_date) > strtotime($start_time)) {
                $start_time = $contract_start_date;
            }
        }

        if ($format === 'daily') {
            $date_format = '%Y-%m-%d';
        }
        if ($format === 'monthly') {
            $date_format = '%Y-%m';
        }
        $group_by = "DATE_FORMAT(follow_time, '$date_format')";

        $sql = "SELECT $group_by AS period, lang_cd, count(distinct(member_id)) AS `usage`
                FROM t_bot_follow 
                WHERE sns_id = 'very01'
                AND bot_id = :bot_id
                AND follow_time BETWEEN :start_time AND :end_time";

        if (!empty($scene_cd)) {
            $sql .= " AND scene = :scene_cd";
        }

        $sql .= " GROUP BY $group_by, lang_cd";

        $query = DB::query(Database::SELECT, $sql);
        $query->parameters(array(
            ':bot_id' => $bot_id,
            ':start_time' => $start_time,
            ':end_time' => $end_time,
            ':scene_cd' => $scene_cd
        ));

        $result = $query->execute()->as_array();

        // CSVフォーマットに整形
        $formatted_data = [];
        $headers = [''];
        $language_usage = [];

        $all_periods = ($format === 'daily') ? $this->generate_day_array($start_time, $end_time) : $this->generate_month_array($start_time, $end_time);
        $code = $this->get_code('02', $admin_lang_cd);

        // 言語ごとの利用状況
        foreach ($all_periods as $period) {
            foreach ($language_usage as $lang_cd => $usages) {
                $language_usage[$lang_cd][$period] = 0;
            }
        }
        // データ整形
        foreach ($result as $row) {
            $period = $row['period'];
            $lang_cd = !empty($row['lang_cd']) && isset($code[$row['lang_cd']]) ? $code[$row['lang_cd']] : $row['lang_cd'];
            $usage = $row['usage'];
            if (!isset($language_usage[$lang_cd])) {
                $language_usage[$lang_cd] = array_fill_keys($all_periods, 0);
            }
            $language_usage[$lang_cd][$period] = $usage;
        }
        $headers = array_merge($headers, $all_periods);
        foreach ($language_usage as $lang_cd => $usages) {
            $row = array_merge([$lang_cd], array_values($usages));
            $formatted_data[] = $row;
        }
        return ['headers' => $headers, 'data' => $formatted_data];
    }

    function get_very_report_clicks_for_export_csv($bot_id, $scene_cd, $admin_lang_cd, $format, $start_time, $end_time)
    {
        // 日付範囲の指定がない場合は、今月までの1年分のデータを取得する
        if (empty($start_time) || empty($end_time)) {
            $start_time = date('Y-m-01', strtotime('-11 months'));
            $end_time = date('Y-m-t 23:59:59', strtotime(date('Y-m-01')));
        }
        // contract_start_date（契約開始日）が start_time より後の場合、start_time の値を契約開始日に変更する
        $contract_start_date = $this->get_very_contract_date($bot_id);
        if ($contract_start_date) {
            if (strtotime($contract_start_date) > strtotime($start_time)) {
                $start_time = $contract_start_date;
            }
        }
        if ($format === 'daily') {
            $date_format = '%Y-%m-%d';
        } elseif ($format === 'monthly') {
            $date_format = '%Y-%m';
        }
        $group_by = "DATE_FORMAT(operate_time, '$date_format')";
        $sql = "SELECT $group_by AS period, lang_cd, operate_div, operate_detail, COUNT(*) AS `usage`
                FROM x_very_log
                WHERE bot_id = :bot_id 
                AND ip_address IS NOT NULL 
                AND operate_time BETWEEN :start_time AND :end_time
                AND operate_div != 'restaurant'";

        if ($scene_cd != '') {
            $sql .= "AND scene = :scene_cd ";
        }

        $sql .= "GROUP BY $group_by, lang_cd, operate_div, operate_detail";

        $query = DB::query(Database::SELECT, $sql);
        $query->parameters(array(
            ':bot_id' => $bot_id,
            ':start_time' => $start_time,
            ':end_time' => $end_time,
            ':scene_cd' => $scene_cd
        ));

        $result = $query->execute()->as_array();
        $all_periods = ($format === 'daily') ? $this->generate_day_array($start_time, $end_time) : $this->generate_month_array($start_time, $end_time);

        // データ集計
        $click_counts = [];
        foreach ($all_periods as $period) {
            $click_counts[$period] = [];
        }
        foreach ($result as $row) {
            $period = $row['period'];
            $lang_cd = $row['lang_cd'];
            $operate_div = $row['operate_div'];
            $operate_detail = json_decode($row['operate_detail'], true);
            $key = ($operate_div === 'notice') ? 'notice_id' : 'title';
            $value = $operate_detail[$key];

            if (!isset($click_counts[$period][$lang_cd][$operate_div][$value])) {
                $click_counts[$period][$lang_cd][$operate_div][$value] = 0;
            }
            $click_counts[$period][$lang_cd][$operate_div][$value] += (int)$row['usage'];
        }
        // CSV形式に整形
        $formatted_data = [];
        $headers = ['', '', ''];
        $headers = array_merge($headers, $all_periods);

        // 言語と operation_div ごとにデータを集計
        $languages = [];
        foreach ($click_counts as $period => $langs) {
            foreach ($langs as $lang_cd => $divs) {
                foreach ($divs as $div => $values) {
                    if (!isset($languages[$lang_cd])) {
                        $languages[$lang_cd] = [];
                    }
                    if (!isset($languages[$lang_cd][$div])) {
                        $languages[$lang_cd][$div] = ['values' => [], 'total' => array_fill_keys($all_periods, 0)];
                    }
                    foreach ($values as $value => $periods) {
                        $languages[$lang_cd][$div]['values'][$value] = array_fill_keys($all_periods, 0);
                        foreach ($all_periods as $p) {
                            $usage = $click_counts[$p][$lang_cd][$div][$value] ?? 0;
                            $languages[$lang_cd][$div]['values'][$value][$p] = $usage;
                            $languages[$lang_cd][$div]['total'][$p] += $usage;
                        }
                    }
                }
            }
        }
        $code = $this->get_code('02', $admin_lang_cd);
        // 言語ごとに整形したデータをまとめる
        foreach ($languages as $lang_cd => $divs) {
            $lang_cd = $code[$lang_cd];
            $first_lang = true; // 言語コードの登場が初回の場合 true
            // operation_divの順序でカスタムソート
            uksort($divs, function ($a, $b) {
                // TOPボタン →メインメニュー →お知らせ →SNS の順
                $order = ['functions', 'categories', 'notice', 'sns'];
                $pos_a = array_search($a, $order);
                $pos_b = array_search($b, $order);
                if ($pos_a === false) $pos_a = count($order);
                if ($pos_b === false) $pos_b = count($order);
                return $pos_a - $pos_b;
            });
            foreach ($divs as $div => $data) {
                switch ($div) {
                    case "functions":
                        echo $div = __('admin.very.report.functions');
                        break;
                    case "categories":
                        echo $div = __('admin.very.report.categories');
                        break;
                    case "notice":
                        echo $div = __('admin.very.report.notice');
                        break;
                    case "sns":
                        echo $div = __('admin.very.report.sns');
                        break;
                    default:
                        break;
                }
                $first_div = true; // operation_div の登場が初回の場合 true
                $div_total = array_fill_keys($all_periods, 0);

                foreach ($data['values'] as $value => $periods) {
                    switch ($value) {
                        case "twitter":
                            echo $value = __('admin.common.label.sns.x');
                            break;
                        case "tiktok":
                            echo $value = __('admin.common.label.sns.tiktok');
                            break;
                        case "instagram":
                            echo $value = __('admin.common.label.sns.instagram');
                            break;
                        case "line":
                            echo $value = __('admin.common.label.sns.line');
                            break;
                        case "youtube":
                            echo $value = __('admin.common.label.sns.youtube');
                            break;
                        case "facebook":
                            echo $value = __('admin.common.label.sns.facebook');
                            break;
                        default:
                            break;
                    }
                    $row = [
                        $first_lang ? $lang_cd : '',
                        $first_div ? $div : '',
                        $value
                    ];
                    $first_lang = false;
                    foreach ($all_periods as $p) {
                        $usage = $periods[$p] ?? 0;
                        $row[] = $usage;
                        $div_total[$p] += $usage;
                    }
                    $formatted_data[] = $row;
                    $first_div = false;
                }
                $div_total_row = ['', '', __('admin.common.label.sum')];
                foreach ($all_periods as $p) {
                    $div_total_row[] = $div_total[$p];
                }
                $formatted_data[] = $div_total_row;
            }
        }
        return ['headers' => $headers, 'data' => $formatted_data];
    }

    // 期間内の月（e.g. '2023-01'）の配列を作成する
    function generate_month_array($start_time, $end_time) {
        $all_months = [];
        $start_month_first_day =  (new DateTime($start_time))->modify('first day of this month')->format('Y-m-d');
        $period = new DatePeriod(
            new DateTime($start_month_first_day),
            new DateInterval('P1M'),
            (new DateTime($end_time))   //  ->modify('+1 day')  do not need to modify if last second of last day was set as $end_time
        );
        foreach ($period as $dt) {
            $all_months[] = $dt->format("Y-m");
        }
        return $all_months;
    }

    // 期間内の日（e.g. '2023-01-01'）の配列を作成する
    function generate_day_array($start_time, $end_time) {
        $all_days = [];

        $period = new DatePeriod(
            new DateTime($start_time),
            new DateInterval('P1D'),
            (new DateTime($end_time))   //  ->modify('+1 day')  do not need to modify if last second of last day was set as $end_time
        );
        foreach ($period as $dt) {
            $all_days[] = $dt->format("Y-m-d");
        }
        return $all_days;
    }

    function get_very_receptionlog($cond_bot_id, $bot_ids_str, $grp_bot_cond, $start_date, $end_date)
    {
        $sql =
            "SELECT 
                x.reception_id, 
                DATE_FORMAT(x.notification_time, '%Y/%m') AS month, 
                COUNT(*) as notification_count, 
                t.bot_name as bot_name
            FROM x_reception_notification_log x
            LEFT JOIN t_bot t ON x.bot_id = t.bot_id
            WHERE x.notification_method = 'line'
            AND x.result = 1";

        // 施設が絞り込まれているとき
        if (!empty($grp_bot_cond)) {
            $sql .= " AND " . $grp_bot_cond;
        } else if ($cond_bot_id !== null && $cond_bot_id !== '') {
            $sql .= " AND x.bot_id = :cond_bot_id";
        } else {
            $sql .= " AND x.bot_id IN ($bot_ids_str)";
        }

        // 対象月が絞り込まれているとき
        $sql .= " AND DATE_FORMAT(x.notification_time, '%Y/%m') BETWEEN :start_date AND :end_date";
        $sql .= " GROUP BY x.bot_id, DATE_FORMAT(x.notification_time, '%Y/%m')";

        $query = DB::query(Database::SELECT, $sql);
        $query->parameters([
            ':cond_bot_id' => $cond_bot_id,
            ':start_date' => $start_date,
            ':end_date' => $end_date
        ]);

        return $query->execute()->as_array();
    }

    function get_very_top($bot_id, $scene_cd) {
        $sql = "SELECT a.setting, a.value
        FROM t_very a
        WHERE a.bot_id=:bot_id AND a.scene_cd=:scene_cd AND a.page='top'";
        $query = DB::query(Database::SELECT, $sql);
        $query->parameters([
            ':bot_id' => $bot_id,
            ':scene_cd' => $scene_cd
        ]);
        $results = $query->execute()->as_array();
        return $results;
    }

    function get_very_settings($bot_id, $scene_cd, $page, $lang_cd) {
        $sql = "SELECT setting, value
        FROM t_very_settings
        WHERE bot_id=:bot_id AND scene_cd=:scene_cd AND page=:page AND lang_cd=:lang_cd";
        $query = DB::query(Database::SELECT, $sql);
        $query->parameters([
            ':bot_id' => $bot_id,
            ':scene_cd'=> $scene_cd,
            ':page'=> $page,
            ':lang_cd'=> $lang_cd
        ]);
        $results = $query->execute()->as_array();
        return $results;
    }

    function create_or_get_very_obfuscation_id($bot_id, $scene_cd, $room_number) {
        try {
            $sql = "SELECT token 
            FROM t_very_token 
            WHERE bot_id = :bot_id 
            AND (scene_cd = :scene_cd OR (scene_cd IS NULL AND :scene_cd IS NULL))
            AND (room_number = :room_number OR (room_number IS NULL AND :room_number IS NULL))";
            
            $query = DB::query(Database::SELECT, $sql);
            $query->parameters([
                ':bot_id' => $bot_id,
                ':scene_cd' => $scene_cd,
                ':room_number' => $room_number
            ]);
            
            $results = $query->execute()->as_array();
            
            if (empty($results)) {
                $token = $this->_create_unique_toke($bot_id . ($scene_cd !== null ? "-$scene_cd" : '') . ($room_number !== null ? "-$room_number" : ''), 32);
                Database::instance()->begin();
                $insert_sql = "INSERT INTO t_very_token (bot_id, scene_cd, room_number, token) 
                               VALUES (:bot_id, :scene_cd, :room_number, :token)";
                
                $insert_query = DB::query(Database::INSERT, $insert_sql);
                $insert_query->parameters([
                    ':bot_id' => $bot_id,
                    ':scene_cd' => $scene_cd,
                    ':room_number' => $room_number,
                    ':token' => $token
                ]);
                
                $insert_query->execute();
                
                // Commit the transaction
                Database::instance()->commit();
                
                return $token;
            } else {
                return $results[0]['token'];
            }
        } catch (Database_Exception $e) {
            Database::instance()->rollback();
            return "";
        } catch (Exception $e) {
            return "";
        }
    }

    function get_very_obfuscation_id($bot_id, $scene_cd, $room_number) {
        try {
            $sql = "SELECT token 
            FROM t_very_token 
            WHERE bot_id = :bot_id 
            AND (scene_cd = :scene_cd OR (scene_cd IS NULL AND :scene_cd IS NULL))
            AND (room_number = :room_number OR (room_number IS NULL AND :room_number IS NULL))";
            
            $query = DB::query(Database::SELECT, $sql);
            $query->parameters([
                ':bot_id' => $bot_id,
                ':scene_cd' => $scene_cd,
                ':room_number' => $room_number
            ]);
            
            $results = $query->execute()->as_array();
            
            if (empty($results)) {
                return "";
            } else {
                return $results[0]['token'];
            }
        } catch (Database_Exception $e) {
            return "";
        } catch (Exception $e) {
            return "";
        }
    }

    private function _create_unique_toke($str, $length) {
        return substr(md5($str . "very-token-S40(y-2G:t"), 0, $length);
	}

    function update_laundry_setting_data($value, $user_id, $bot_id, $lang_cd)
    {
        $data = array(
            'value' => json_encode($value, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES),
            'upd_user' => $user_id,
            'upd_time' => date('Y-m-d H:i:s', time())
        );
        // まず、更新を試みる
        $result = DB::update('t_very_settings')
            ->set($data)
            ->where('bot_id', '=', $bot_id)
            ->where('page', '=', "laundry")
            ->where('setting', '=', 'section')
            ->where('lang_cd', '=', $lang_cd)
            ->execute();

        // 更新された行がない場合、新規挿入を行う
        if ($result === 0) {
            $data['bot_id'] = $bot_id;
            $data['page'] = "laundry";
            $data['setting'] = "section";
            $data['lang_cd'] = $lang_cd;
            $result = DB::insert('t_very_settings', array_keys($data))
                ->columns(array_keys($data))
                ->values(array_values($data))
                ->execute();
        }

        return $result;
    }

    function get_laundry_api_access_token($refresh_token)
	{
		try {
			$API_CLIENT_ID = $this->get_env('laundry_setting')['client_id'];
			$API_CLIENT_SECRET = $this->get_env('laundry_setting')['client_secret'];
			$API_SCOPE = 'offline_access https://api.aiot.sharp.co.jp/auth/devices/washing-machine';
            $API_AUTH_HEADER = 'Basic ' . base64_encode($API_CLIENT_ID . ':' . $API_CLIENT_SECRET);
			$url = $this->get_env('laundry_setting')['token_api_url'];

			$headers = [
				'Authorization: ' . $API_AUTH_HEADER,
				'Content-Type: application/x-www-form-urlencoded'
			];

			$body = http_build_query([
				'grant_type' => 'refresh_token',
				'client_id' => $API_CLIENT_ID,
				'client_secret' => $API_CLIENT_SECRET,
				'refresh_token' => $refresh_token,
				'scope' => $API_SCOPE
			]);

			$ch = curl_init($url);
			curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
			curl_setopt($ch, CURLOPT_POST, true);
			curl_setopt($ch, CURLOPT_POSTFIELDS, $body);
			curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

			$response = curl_exec($ch);
			if (curl_errno($ch)) {
				throw new Exception('Curl error: ' . curl_error($ch));
			}
			curl_close($ch);

			$decoded_res = json_decode($response, true);
			if (json_last_error() !== JSON_ERROR_NONE) {
				throw new Exception('JSON decode error: ' . json_last_error_msg());
			}

			if (isset($decoded_res['access_token'])) {
				return $decoded_res['access_token'];
			} else {
				throw new Exception('Access token not found in response');
			}
		} catch (\Throwable $th) {
			error_log('Error: ' . $th->getMessage());
		}
	}

	function get_laundry_device_name_from_api($device_id)
	{
        // 1. refresh tokenの取得
        $laundry_status_refresh_token = $this->get_bot_setting(0, 'laundry_status_refresh_token');
		// 2. refresh tokenを使ってaccess tokenを取得
		$access_token = $this->get_laundry_api_access_token($laundry_status_refresh_token);
        // 3. access tokenを使ってdevice_nameを取得
        $laundry_info_api_url = $this->get_env('laundry_setting')['info_api_url'] ;
		$url = $laundry_info_api_url . urlencode($device_id);

		$headers = [
			'Authorization: Bearer ' . $access_token,
		];
		$ch = curl_init();
		curl_setopt($ch, CURLOPT_URL, $url);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

		$response = curl_exec($ch);

		if (curl_errno($ch)) {
			throw new Exception('Curl error: ' . curl_error($ch));
		}

		curl_close($ch);
		$device_name = json_decode($response, true)['device_name'];
		if (json_last_error() !== JSON_ERROR_NONE
		) {
			throw new Exception('JSON decode error: ' . json_last_error_msg());
		}
		return $device_name;
	}

    function get_reception_display_settings($reception_id)
    {
        $sql = "SELECT reception_data FROM t_reception WHERE reception_id = :reception_id";
        $query = DB::query(Database::SELECT, $sql);
        $query->parameters([
            ':reception_id' => $reception_id
        ]);
        $results = $query->execute()->as_array();
        return $results;
    }

    function get_reception_display_text($reception_id, $lang_cd)
    {
        $sql = "SELECT settings FROM t_reception_description WHERE reception_id = :reception_id AND lang_cd = :lang_cd";
        $query = DB::query(Database::SELECT, $sql);
        $query->parameters([
            ':reception_id' => $reception_id,
            ':lang_cd' => $lang_cd
        ]);
        $results = $query->execute()->as_array();
        return $results;
    }

    function get_reception_display_data($reception_id, $lang_cd)
    {
        $display_text_results = $this->get_reception_display_text($reception_id, $lang_cd);
        $display_text_data = [];
        if (!empty($display_text_results)) {
            $json_display_text_data = json_decode($display_text_results[0]['settings'], true);
            $display_text_data = $json_display_text_data['display_text'] ?? [];
        }
        
        $display_settings_results = $this->get_reception_display_settings($reception_id);
        $display_style_data = [];
        if (!empty($display_settings_results)) {
            $json_display_settings_data = json_decode($display_settings_results[0]['reception_data'], true);
            $display_style_data = $json_display_settings_data['display_style'] ?? [];
        }
        $combined_data = [
            'display_text' => $display_text_data,
            'display_style' => $display_style_data,
        ];
        return json_encode($combined_data, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    }

    function save_display_text_setting($display_text_setting, $reception_id, $lang_cd)
    {
        $json_settings = json_encode(array('display_text' => $display_text_setting), JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    
        $existing_record = DB::select('reception_id')
            ->from('t_reception_description')
            ->where('reception_id', '=', $reception_id)
            ->where('lang_cd', '=', $lang_cd)
            ->execute()
            ->as_array();
    
        if (!empty($existing_record)) {
            $data = array(
                'settings' => $json_settings,
            );
            $result = DB::update('t_reception_description')
                ->set($data)
                ->where('reception_id', '=', $reception_id)
                ->where('lang_cd', '=', $lang_cd)
                ->execute();
        } else {
            $result = DB::insert('t_reception_description', array('reception_id', 'lang_cd', 'settings'))
                ->values(array($reception_id, $lang_cd, $json_settings))
                ->execute();
        }
        return $result;
    }

    function save_display_style($reception_display_style, $bot_id, $reception_id, $user_id)
    {
        $existing_data = DB::select('reception_data')
            ->from('t_reception')
            ->where('reception_id', '=', $reception_id)
            ->where('bot_id', '=', $bot_id)
            ->execute()
            ->current();

        $reception_data_array = [];
        if ($existing_data && isset($existing_data['reception_data'])) {
            $reception_data_array = json_decode($existing_data['reception_data'], true);
            if (!is_array($reception_data_array)) {
                $reception_data_array = [];
            }
        }

        $reception_data_array['display_style'] = $reception_display_style;
        $data = array(
            'reception_data' => json_encode($reception_data_array, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES),
            'upd_time' => date('Y-m-d H:i:s'),
            'upd_user' => $user_id,
        );

        $result = DB::update('t_reception')
            ->set($data)
            ->where('reception_id', '=', $reception_id)
            ->where('bot_id', '=', $bot_id)
            ->execute();

        return $result;
    }

    function get_all_very_settings_history_records($bot_id, $scene_cd, $page, $lang_cd) {
        $sql = "SELECT version, value, upd_user, upd_time
                FROM t_very_settings_history
                WHERE bot_id = :bot_id
                  AND scene_cd = :scene_cd
                  AND page = :page
                  AND lang_cd = :lang_cd
                ORDER BY version DESC";
    
        $query = DB::query(Database::SELECT, $sql);
        $query->parameters(array(
            ':bot_id'   => $bot_id,
            ':scene_cd' => $scene_cd,
            ':page'     => $page,
            ':lang_cd'  => $lang_cd,
        ));
        $results = $query->execute()->as_array();
    
        $history_records = [];
        $user_ids = [];
    
        foreach ($results as $row) {
            $record = [
                'version'  => $row['version'],
                'upd_time' => substr($row['upd_time'], 0, 16),
                'value'    => json_decode($row['value'], true),
                'upd_user' => $row['upd_user'],
            ];
            $user_ids[] = $row['upd_user'];
            $history_records[] = $record;
        }
    
        if (count($user_ids) > 0) {
            $user_names = $this->get_user_names_by_ids($user_ids);
            foreach ($history_records as &$record) {
                $record['upd_user'] = $user_names[$record['upd_user']] ?? '';
            }
        }
    
        return $history_records;
    }

    function get_very_settings_by_version($bot_id, $scene_cd, $page, $lang_cd, $version) {
        $sql = "SELECT version, value, upd_user, upd_time
                FROM t_very_settings_history
                WHERE bot_id = :bot_id
                  AND scene_cd = :scene_cd
                  AND page = :page
                  AND lang_cd = :lang_cd
                  AND version = :version";
    
        $query = DB::query(Database::SELECT, $sql);
        $query->parameters(array(
            ':bot_id'   => $bot_id,
            ':scene_cd' => $scene_cd,
            ':page'     => $page,
            ':lang_cd'  => $lang_cd,
            ':version'  => $version,
        ));
        $result = $query->execute()->as_array();
        
        if (empty($result)) {
            return null;
        }
        
        $row = $result[0];
        
        $record = [
            'version'  => $row['version'],
            'upd_time' => substr($row['upd_time'], 0, 16),
            'value'    => json_decode($row['value'], true),
            'upd_user' => $row['upd_user'],
        ];
        
        $user_names = $this->get_user_names_by_ids([$row['upd_user']]);
        $record['upd_user'] = $user_names[$row['upd_user']] ?? '';
        
        return $record;
    }

    function get_latest_version($bot_id, $scene_cd, $page, $lang_cd) {
        $sql = "SELECT MAX(version) AS version
                FROM t_very_settings_history
                WHERE bot_id = :bot_id
                  AND scene_cd = :scene_cd
                  AND page = :page
                  AND lang_cd = :lang_cd";
    
        $query = DB::query(Database::SELECT, $sql);
        $query->parameters(array(
            ':bot_id'   => $bot_id,
            ':scene_cd' => $scene_cd,
            ':page'     => $page,
            ':lang_cd'  => $lang_cd,
        ));
        $result = $query->execute()->current();
    
        return $result ? $result['version'] : null;
    }

    // TODO: move to common model, cause it is shared by lot of models
    public function get_user_names_by_ids($user_ids) {
        $user_ids_int = array_map('intval', $user_ids);
    
        $query = DB::select('user_id', 'name')
                   ->from('t_user')
                   ->where('user_id', 'IN', $user_ids_int);
    
        $results = $query->execute()->as_array();
        $names = [];
        foreach ($results as $row) {
            $names[$row['user_id']] = $row['name'];
        }
        return $names;
    }

    public function get_very_list($bot_id)
    {
        $sql = "SELECT DISTINCT v.scene_cd, v.setting, v.value, s.label
            FROM t_very v
            INNER JOIN t_bot_scene s
                ON v.scene_cd = s.scene_name AND s.bot_id = :bot_id
            WHERE v.bot_id = :bot_id";

        $query = DB::query(Database::SELECT, $sql);
        $query->parameters([
            ':bot_id' => $bot_id
        ]);

        $results = $query->execute()->as_array();

        $grouped_by_scene = [];
        foreach ($results as $row) {
            $scene_cd = $row['scene_cd'];
            $setting = $row['setting'];
            $value = $row['value'];
            $scene_name = $row['label'];

            if (!isset($grouped_by_scene[$scene_cd])) {
                $grouped_by_scene[$scene_cd] = [
                    'scene_cd' => $scene_cd,
                    'scene_name' => $scene_name
                ];
            }
            
            // settingをキーとしてvalueを格納
            $decoded_value = json_decode($value, true);
            $grouped_by_scene[$scene_cd][$setting] = $decoded_value;
        }
        return array_values($grouped_by_scene);
    }

    public function create_very_from_template($source_bot_id, $source_scene_cd, $target_bot_id, $target_scene_cd, $user_id, $support_lang)
    {
        try {
            // t_veryデータをコピー
            $sql_very = "INSERT INTO t_very (bot_id, scene_cd, setting, value, page, upd_time, upd_user)
             SELECT :target_bot_id, :target_scene_cd, setting, value, 'top', NOW(), :user_id
             FROM t_very
             WHERE bot_id = :source_bot_id AND scene_cd = :source_scene_cd AND page = 'top'";
            DB::query(Database::INSERT, $sql_very)
                ->parameters([
                    ':source_bot_id' => $source_bot_id,
                    ':source_scene_cd' => $source_scene_cd,
                    ':target_bot_id' => $target_bot_id,
                    ':target_scene_cd' => $target_scene_cd,
                    ':user_id' => $user_id
                ])
                ->execute();

            // t_very_settingsデータをコピー
            $support_lang_array = explode(',', $support_lang);
            $placeholders = ':lang' . implode(', :lang', range(0, count($support_lang_array) - 1));

            $sql_settings = "INSERT INTO t_very_settings (bot_id, scene_cd, lang_cd, page, setting, value, upd_time, upd_user)
                 SELECT :target_bot_id, :target_scene_cd, lang_cd, 'top', setting, value, NOW(), :user_id
                 FROM t_very_settings
                 WHERE bot_id = :source_bot_id AND scene_cd = :source_scene_cd AND page = 'top' 
                 AND lang_cd IN ($placeholders)";

            $parameters = [
                ':source_bot_id' => $source_bot_id,
                ':source_scene_cd' => $source_scene_cd,
                ':target_bot_id' => $target_bot_id,
                ':target_scene_cd' => $target_scene_cd,
                ':user_id' => $user_id
            ];
            // このボットのサポート言語のデータのみコピーする
            foreach ($support_lang_array as $index => $lang) {
                $parameters[':lang' . $index] = trim($lang);
            }
            DB::query(Database::INSERT, $sql_settings)
                ->parameters($parameters)
                ->execute();

            return true;
        } catch (Exception $e) {
            throw $e;
        }
    }
}
