<?php defined('SYSPATH') or die('No direct script access.');

class Controller_Sample extends Controller_Template_Adminbase {

	public function __construct(Request $request, Response $response)
	{
		parent::__construct($request, $response);
		$this->_model = Model::factory('adminmodel');
	}

	public function action_components()
	{
        $view = View::factory ('sample/components');
		$this->template->content = $view;
    }


	public function action_reactcomponents() {
		$view = View::factory('sample/' . 'reactcomponents');
		$this->template->content = $view;
	}

	public function action_page() 
	{
		$post = [];
		if ($this->request->post()){
			$post = $this->request->post();
			if ($this->_page_action == 'save') {
				// 画面表示用エラー登録
				$this->_set_message('maximumorders.message.error.cancel_fail');
				$this->redirect('/sample/page');
			}
			else if ($this->_page_action == 'add') {

			}
		}
		$view = View::factory ('sample/page');
		$view->post = $post;
		$this->template->content = $view;
	}
}
