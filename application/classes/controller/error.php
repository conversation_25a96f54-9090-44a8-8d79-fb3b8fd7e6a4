<?php defined('SYSPATH') or die('No direct script access.');

class Controller_Error extends Controller{

	public function action_index()
	{
		// error画面迁移
		$message_id = $this->request->query('message_id', NULL);
		if ($message_id == NULL) $message_id = '';
		Session::instance()->destroy();
		//session_destroy();
		$view = View::factory('error');
		$view->message = $message_id;
		$view->_assets = $this->_model->get_env('assets_url');
		$this->response->body($view);
	}

}
