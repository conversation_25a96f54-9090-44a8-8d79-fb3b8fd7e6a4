<?php defined ( 'SYSPATH' ) or die ( 'No direct script access.' );

class Controller_Apply extends Controller_Template_Templatebase{

	public $template_file = 'apply/template';
	public $auth_required = FALSE;
	public $page_title = '申し込み | talkappi';
	public $_model;
	
	public $_step;
	
	public function __construct(Request $request, Response $response)
	{
		parent::__construct($request, $response);
		$this->_model = new Model_Applymodel();
	}
	public function before()
	{
		ob_end_flush();
		parent::before();
	}
	
	public function after()
	{
		$js = 'step' . $this->_step . '.js?v=' . time();
		View::bind_global('_js', $js);
		View::bind_global('_step', $this->_step);
		parent::after();
	}
	
	public function redirect_error($message_id)
	{
		$this->redirect ('/apply/error');
	}
	
	public function action_index() {
		$errors = NULL;
		$messages = array();
		$this->_step = 1;
		
		if ($this->request->post()){
			$email = $this->request->post()['mail'];
			if(!preg_match("^[_a-z0-9-]+(\.[_a-z0-9-]+)*@[a-z0-9-]+(\.[a-z0-9-]+)*(\.[a-z]{2,3})$^",$email)) {
				$messages['mail'] = '正しいメールを入力してください';
			}
			if (count($messages) == 0) {
				$user = ORM::factory('user')->where('email', '=', $email)->where('delete_flg', '=', 0)->find();
				if (!isset($user->user_id)) {
					$link_id = $this->_model->set_link_param('01', ['email'=>$email]);
					Session::instance()->set('register_email', $email);
					Session::instance()->set('apply_link_id', $link_id);
					// send mail
					$data_url = $this->_model->get_env('base_url') . 'apply/link?id=' . $link_id;
					$mail_result = $this->send_mail_template($email, 'ja', '01', array('${data_url}' => $data_url, '${data_url_limit}' => $valid_days));
					if (!$mail_result) {
						$this->redirect_error('register1.message.mail_send_error');
						return;
					}
					$this->redirect('apply/step1');
				}
				else {
					$messages['mail'] = '既にtalkappi利用中のメールアドレスです';
				}
			}
		}
		$view = View::factory ('apply/step1');
		$view->messages = $messages;
		$this->template->content = $view;
	}

	public function action_link() {
		if ($this->request->query()){
			$link_id = $this->request->query('id', NULL);
			if ($link_id == NULL) {
				$this->redirect_error('apply.error.linkid_notexist');
				return;
			}
			$link = $this->_model->get_link_param($link_id, true);
			if (!is_array($link)) {
				$this->redirect_error('apply.error.mail_link.' . $link);
				return;
			}
			Session::instance()->set('apply_email', $link['email']);
			Session::instance()->set('apply_link_id', $link_id);
			Session::instance()->delete('apply_common_info');
			Session::instance()->delete('apply_bot_info');
			$this->redirect('/apply/step2');
		}
	}
	
	public function action_step1() {
		$this->_step = 0;
		$view = View::factory ('apply/step0');
		$view->mail = Session::instance()->get('register_email');
		$view->token = Session::instance()->get('apply_link_id');
		$this->template->content = $view;
	}

	public function action_step2() {
		
		if (Session::instance()->get('apply_email', NULL) == NULL) {
			$this->redirect_error('');
		}
		
		$messages = array();
		$post = NULL;
		$this->_step = 2;
		
		if ($this->request->post()){
			$post = $this->request->post();
			if(trim($post['company_name']) == '') {
				$messages['company_name'] = '会社名を入力してください';
			}
			if(trim($post['bot_name']) == '') {
				$messages['bot_name'] = '施設名を入力してください';
			}
			if(trim($post['address']) == '') {
				$messages['address'] = '住所を入力してください';
			}
			if(trim($post['tel']) == '') {
				$messages['tel'] = '電話番号を入力してください';
			}
			if(!preg_match("/\A\d{2,4}+-\d{2,4}+-\d{4}\z/",$post['tel'])) {
				$messages['tel'] = '正しい電話番号を入力してください';
			}
			if(trim($post['contact']) == '') {
				$messages['contact'] = '担当者を入力してください';
			}
			if(trim($post['url']) != '') {
				if (!(filter_var($post['url'], FILTER_VALIDATE_URL) && preg_match('@^https?+://@i', $post['url']))) {
					$messages['url'] = '正しいURLを入力してください';
				}
			}
			if (count($messages) == 0) {
				Session::instance()->set('apply_common_info', $post);
				$this->redirect('/apply/step3');
			}
 		}
 		else {
 			$post = Session::instance()->get('apply_common_info', NULL);
 		}
 		if ($post == NULL) {
 			$post['company_name'] = '';
 			$post['bot_name'] = '';
 			$post['address'] = '';
 			$post['contact'] = '';
 			$post['tel'] = '';
 			$post['url'] = '';
 		}
 		$view = View::factory ('apply/step2');
 		$view->messages = $messages;
 		$view->post = $post;
 		$this->template->content = $view;
	}
	
	public function action_step3() {
		if (Session::instance()->get('apply_email', NULL) == NULL) {
			$this->redirect_error('');
		}
		
		$messages = array();
		$post = NULL;
		$this->_step = 3;
		$lang = array (
				'ja' => '日本語',
				'en' => '英語',
				'cn' => '中国語(簡)',
				'tw' => '中国語(繁)',
				'kr' => '韓国語',
		);
		$sns = array (
				'fb' => 'Facebook Messenger',
				'ln' => 'Line',
				'wc' => 'Wechat',
				'wb' => 'Web',
		);
		if ($this->request->post()){
			$post = $this->request->post();
			/*
			if (!array_key_exists('room_count', $post)) {
				$messages['room_count'] = '部屋数を選択してください';
			}
			*/
			if (!array_key_exists('lang_cd', $post)) {
				$messages['lang_cd'] = '言語を選択してください';
				$post['lang_cd'] = array();
			}
			if (!array_key_exists('sns_type_cd', $post)) {
				$messages['sns_type_cd'] = 'SNSを選択してください';
				$post['sns_type_cd'] = array();
			}
			if (count($messages) == 0) {
				$post['lang'] = '';
				$post['sns'] = '';
				foreach($post['lang_cd'] as $k) {
					$post['lang'] = $post['lang'] . $lang[$k] .  ' ';
				}
				foreach($post['sns_type_cd'] as $k) {
					$post['sns'] = $post['sns'] . $sns[$k] .  ' ';
				}
				Session::instance()->set('apply_bot_info', $post);
				$this->redirect('/apply/step4');
			}
		}
		else {
			$post = Session::instance()->get('apply_bot_info', NULL);
		}
		if ($post == NULL) {
			//$post['bot_name'] = '';
			$post['room_count'] = '';
			$post['lang_cd'] = array();
			$post['sns_type_cd'] = array();
		}
		$view = View::factory ('apply/step3');
		$view->messages = $messages;
		$view->lang = $lang;
		$view->sns = $sns;
		$view->post = $post;
		$this->template->content = $view;
	}

	public function action_step4() {
		if (Session::instance()->get('apply_email', NULL) == NULL) {
			$this->redirect_error('');
		}
		
		$this->_step = 4;
		
		$info = Session::instance()->get('apply_common_info', NULL);
		$bot_info = Session::instance()->get('apply_bot_info', NULL);
		$post = $info + $bot_info;
		if ($this->request->post()){
			$orm = ORM::factory('applybot');
			$orm->mail = Session::instance()->get('apply_email', NULL);
			$orm->apply_time = date('Y-m-d H:i:s',time());
			$orm->apply_status_cd = '01';
			$orm->bot_name = $post['bot_name'];
			$orm->address = $post['address'];
			$orm->company_name = $post['company_name'];
			$orm->url = $post['url'];
			$orm->tel = $post['tel'];
			$orm->contact = $post['contact'];
			//$orm->room_count = $post['room_count'];
			//$orm->icon = $post['icon'];
			//$orm->image = $post['image'];
			$orm->sns_type_cd = join(',', $post['sns_type_cd']);
			$orm->lang_cd = join(',', $post['lang_cd']);
			$orm->save();
			
			// send mail
			$data_url = $this->_model->get_env('base_url');
			$mail_result = $this->send_mail_template($orm->mail, 'ja', '02', array('${data_url}' => $data_url));
			if (!$mail_result) {
				$this->redirect_error('register1.message.mail_send_error');
				return;
			}
			
			$this->redirect('/apply/step5');
		}

		$view = View::factory ('apply/step4');
		$view->post = $post;
		$this->template->content = $view;
	}

	public function action_step5() {
		if (Session::instance()->get('apply_email', NULL) == NULL) {
			$this->redirect_error('');
		}
		$this->_step = 5;
		
		$this->_model->invalid_link(Session::instance()->get('apply_link_id'));

		$post = Session::instance()->get('apply_common_info', NULL);
		Session::instance()->delete('apply_email');
		Session::instance()->delete('apply_link_id');
		Session::instance()->delete('apply_common_info');
		Session::instance()->delete('apply_bot_info');
		
		$view = View::factory ('apply/step5');
		$view->post = $post;
		$this->template->content = $view;
	}
	
	function send_mail_template($mail, $lang_cd, $template, $params)
	{
		$template_file = APPPATH . 'template/mail/' . $template . '.' . $lang_cd . '.tpl';
		$mail_template = file($template_file);
		$subject = $mail_template[0];
		$body = '';
		for($i = 1; $i < count($mail_template); $i++) {
			$body = $body . $mail_template[$i];
		}
		$sign_file = APPPATH . 'template/mail/' . 'sign' . '.' . $lang_cd . '.tpl';
		$mail_sign = file($sign_file);
		for($i = 0; $i < count($mail_sign); $i++) {
			$body = $body . $mail_sign[$i];
		}
		foreach($params as $find=>$replace) {
			$body = str_replace($find, $replace, $body);
		}
		$mail_model = Model::factory('email');
		$result = $mail_model->send($mail, '', $subject, $body);
		return $result;
	}
}
