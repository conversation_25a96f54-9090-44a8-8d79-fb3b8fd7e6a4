<?php defined('SYSPATH') or die('No direct script access.');

class Controller_Template_Templatebase extends Controller_Template
{
	protected function _redirect($url) {
		$this->redirect($url);
	}
	
	public function action_error() {
		$lang_cd = 'ja';
		$lang = substr($_SERVER['HTTP_ACCEPT_LANGUAGE'], 0, 4);
		if (preg_match("/zh-c/i", $lang)) {
			$lang_cd= 'cn';
		}
		else if (preg_match("/zh/i", $lang)) {
			$lang_cd= 'tw';
		}
		else if (preg_match("/ja/i", $lang)) {
			$lang_cd= 'ja';
		}
		else if (preg_match("/ko/i", $lang)) {
			$lang_cd= 'kr';
		}
		else {
			$lang_cd= 'en';
		}
		I18n::$lang = $lang_cd;
		$this->auto_render = FALSE;
		$view = View::factory('common/front_error');
		$this->response->body($view);
	}
}