<?php defined('SYSPATH') or die('No direct script access.');

class Controller_Template_Adminbase extends Controller_Template
{
	const SESSION_PREFIX = 'bot_';

	public $_transactional = TRUE;
	private $_transaction_finished = false;

	public $template_file = 'template/metronic';
	public $_auth_required;
	public $_lang_cd = 'ja';
	public $_settings;
	public $_codes;
	public $_classes;
	public $_user_id;
	public $_user;
	public $_user_function;
	public $_sys_menu;
	public $_tab_menu;
	public $_active_menu;
	public $_active_menu_name;
	public $_bot_id;
	public $_bot;
	public $_bot_setting;
	public $_bot_lang;
    public $_bot_local_lang;
	public $_item_id;
	public $_item_cd;
	public $_action;
	public $_uri;
	public $_path;
	public $_bef_chgbot_action;
	public $_bef_chgbot_query;
	public $_query;
	public $_js;
	public $_page_action = '';
	public $_model;
	public $_functions;
	public $_disaster;

	public $_can_copied_action = ['/admininquiry/inquiry' => ['text' => '問い合わせ', 'item_div' => 9], '/adminorder/inquiry' => ['text' => '予約・販売', 'item_div' => 19], '/adminsurvey/survey' => ['text' => 'アンケート', 'item_div' => 8], '/admin/maximum' => ['text' => '枠', 'item_div' => 12], '/adminticket/coupon' => ['text' => 'チケット', 'item_div' => 7]];
	public $_can_pasted_action = ['/admininquiry/inquirys' => ['text' => '問い合わせ一覧', 'item_div' => 9], '/adminorder/inquirys' => ['text' => '予約・販売一覧', 'item_div' => 19], '/adminsurvey/surveys' => ['text' => 'アンケート一覧', 'item_div' => 8], '/admin/maximums' => ['text' => '枠一覧', 'item_div' => 12], '/adminticket/coupons' => ['text' => 'チケット一覧', 'item_div' => 7]];
	
	public $_service_useflg = [];

	public function __construct(Request $request, Response $response)
	{
		// 本番場合、外す
		/*
		set_exception_handler(function ($e) {
			if($this->_transactional && !$this->_transaction_finished)
			{
				Database::instance()->rollback();
				$this->_transaction_finished = true;
			}
			//$this->redirect_error('error.exception');
			//throw $e;

		 	//session_destroy();
		 	//echo "<b>User Exception:</b>" . " [" . $e->getCode() . "]" . " message " . $e->getMessage() . " in file " . $e->getFile() . " on line " . $e->getLine() . PHP_EOL;
		 	$html = file_get_contents(APPPATH . '/views/error.php');
		 	echo($html);
		});
*/
		/*
		set_error_handler(function($err_no, $err_msg, $err_file, $err_line) {
			if ($err_no == E_USER_ERROR || $err_no == E_USER_NOTICE) {
				exit("error/fatal error, exit!");
			}
		});
		*/

		parent::__construct($request, $response);
		$this->_model = new Model_Adminmodel();

		$query_string = '';
		foreach ($request->query() as $k => $v) {
			$query_string = $query_string . $k . '=' . $v . '&';
		}
		if ($query_string == '') {
			$query_string = '/' . $request->uri();
		} else {
			$query_string = '/' . $request->uri() . '?' . $query_string;
		}
		// once token judge
		if (isset($_GET['once_token']) && $_GET['once_token'] != '') {
			$orm = ORM::factory('user')->where('once_token', '=',  $_GET['once_token'])->find();
			if (isset($orm->user_id)) {
				$user = ORM::factory('user', $orm->user_id);
				$user->once_token = NULL;
				$user->save();
				Session::instance()->set('user', $user);
				// bot session
				if ($user->latest_bot != '') {
					$latest_bot = explode(',', $user->latest_bot)[0];
					$bot = ORM::factory('bot')->where('bot_id', '=', $latest_bot)->find();
				}
				else {
					$bot = ORM::factory('bot')->where('bot_id', '=', $user->bot_id)->find();
				}
				Session::instance()->set('bot', $bot);
				Session::instance()->set('bot_id', $bot->bot_id);
				Session::instance()->set('bot_setting', $this->_model->get_bot_setting_dict($bot->bot_id));
				Session::instance()->set('service_useflg', $this->_model->get_service_useflg($bot->bot_id));
				$item = ORM::factory('item')->where('bot_id', '=', $bot->bot_id)->find();
				Session::instance()->set('item_id', $item->item_id);
				Session::instance()->set('item_cd', $item->item_cd);
				// user function session
				Session::instance()->set('user_id', $user->user_id);
				Session::instance()->set('user_name', $user->name);
				$role = ORM::factory('role')->where('role_cd', '=', $user->role_cd)->find();
				$role_function = explode(',', $role->functions);
				// omotenash role
				$role_97 = ORM::factory('role')->where('role_cd', '=', '97')->find();
				$role_97_function = explode(',', $role_97->functions);
				$role_function = array_intersect($role_function, $role_97_function);
				$div_function = $this->_model->get_code_div(888803, $this->_lang_cd);
				$sys_function = [];
				foreach($div_function as $f) {
					$sys_function[$f['class_cd']] = $f['word'];
				}
				$user_function = [];
				foreach($role_function as $f) {
					$user_function[] = $sys_function[$f];
				}
				Session::instance()->set('sys_function', array_values($sys_function));
				Session::instance()->set('role_function', $role_function);
				Session::instance()->set('user_function', $user_function);
				$sys_menu = [];
				$tab_menu = [];
				$div_menu = $this->_model->get_code_div(888802, $this->_lang_cd);
				foreach($div_menu as $m) {
					if (trim($m['name']) == '') continue;
					if ($m['parent_cd'] == '') {
						$sys_menu[$m['class_cd']] = ["parent"=>$m, "children"=>[]];
					}
					else {
						if (strlen($m['parent_cd']) == 2) {
							$sys_menu[$m['parent_cd']]["children"][$m['class_cd']] = $m;
							$tab_menu[$m['class_cd']] = ["parent"=>$m, "children"=>[]];
						}
						else {
							$tab_menu[$m['parent_cd']]["children"][$m['class_cd']] = $m;
						}
					}
				}
				Session::instance()->set('sys_menu', $sys_menu);
				Session::instance()->set('tab_menu', $tab_menu);
				$bot_array = [];
				$has_grp_bot = false;
				if ($user->role_cd == '99' || $user->role_cd == '80') {
					$bots = ORM::factory('bot')->where('delete_flg', '=', 0)->order_by('bot_id')->find_all();
					foreach($bots as $b) {
						$grp_bot_id = $this->_model->get_grp_bot_id($b->bot_id);
						if ($grp_bot_id > 0) continue;
						$bot_array[] = [$b->bot_id, $b->bot_name];
					}
				}
				else {
					$user_bots = $this->_model->get_user_bots($user->email);
					$p_bot_arr = [];
					foreach($user_bots as $b) {
						$bots[] = $b;
						$grp_bot_id = $this->_model->get_grp_bot_id($b->bot_id);
						if ($grp_bot_id == 0) {
							$bot_array[] = [$b->bot_id, $b->bot_name];
							$p_bot_arr[] = $b->bot_id;
							$has_grp_bot = true;
						}
						else if ($grp_bot_id < 0) {
							$bot_array[] = [$b->bot_id, $b->bot_name];
						}
						else {
							if (!in_array($grp_bot_id, $p_bot_arr)) {
								$grp_bot = ORM::factory('bot', $grp_bot_id);
								$bot_array[] = [$grp_bot_id, $grp_bot->bot_name];
								$p_bot_arr[] = $grp_bot_id;
								$has_grp_bot = true;
							}
						}
					}
				}
				Session::instance()->set('bot_array', $bot_array);
				Session::instance()->set('has_grp_bot', $has_grp_bot);		
			}
		}
		// login judge
		$this->_user = Session::instance()->get('user', NULL);
		$two_factor_flag_session = Session::instance()->get('two_factor_flag', false);
		if ($this->_auth_required && (!$this->_user || $two_factor_flag_session)) {
			if (
				true || strpos($query_string, '/chat?') === 0 ||
				strpos($query_string, '/admin/services') === 0 ||
				strpos($query_string, '/admin/orders') === 0 ||
				strpos($query_string, '/admin/talks') === 0 ||
				strpos($query_string, '/admin/items') === 0 ||
				strpos($query_string, '/admin/products') === 0
			) {
				$this->redirect('/login?redirect=' . urlencode($query_string));
			} else {
				$this->redirect('/login');
			}
		}
		
		// sessionのdisaster_modeが1ならadmin/disastersettingにリダイレクト（admin/disasterは例外）
		$disaster_mode = Session::instance()->get('disaster_mode', 0);
		$is_fullview = $this->request->query('fullview') === 'true'; // 無限リダイレクトの防止
		$is_disaster_edit_page = strpos($this->request->uri(), 'admin/disaster') !== false; // 編集ページはアクセス可
		$is_logout_page = strpos($this->request->uri(), 'admin/logout') !== false; // logoutは可能にする
		$is_chgbot_page = strpos($this->request->uri(), 'admin/chgbot') !== false; // chgbotは可能にする
		if ($disaster_mode == 1 && !$is_fullview && !$is_disaster_edit_page && !$is_logout_page && !$is_chgbot_page) {
			$this->redirect('admin/disastersetting?fullview=true');
			exit();
		}

		// role authority
		$this->_user_function = Session::instance()->get('user_function', NULL);
		$this->_sys_menu = Session::instance()->get('sys_menu', NULL);
		$this->_tab_menu = Session::instance()->get('tab_menu', NULL);
		$this->_action = $request->action();
		$this->_uri = $_SERVER['REQUEST_URI'];
		$this->_path = explode('/', $this->_uri)[1];

		$this->_active_menu = '';

		foreach ($this->_tab_menu as $menu) {
			foreach ($menu['children'] as $sub_menu) {
				if ($this->_get_action($this->_uri) == $this->_get_action('/' . $sub_menu['word'])) {
					$this->_active_menu = $sub_menu['class_cd'];
					if (array_key_exists('parent', $menu)) {
						$this->_active_menu_name = $menu['parent']['name'];
					}
					break;
				}
			}
			if ($this->_active_menu == '') {
				if (array_key_exists('parent', $menu)) {
					$sub_menu = $menu['parent'];
					if ($this->_get_action($this->_uri) == $this->_get_action('/' . $sub_menu['word'])) {
						$this->_active_menu = $sub_menu['class_cd'];
						$this->_active_menu_name = $sub_menu['name'];

						if($this->_uri !== '/' . $sub_menu['word']) {
							// #34738
							foreach ($this->_tab_menu as $menu) {
								foreach ($menu['children'] as $sub_menu) {
									if ($this->_uri == '/' . $sub_menu['word']) {
										$this->_active_menu = $sub_menu['class_cd'];
										if (array_key_exists('parent', $menu)) {
											$this->_active_menu_name = $menu['parent']['name'];
										}
										break;
									}
								}
								if (array_key_exists('parent', $menu)) {
									$sub_menu = $menu['parent'];
									if ($this->_uri == '/' . $sub_menu['word']) {
										$this->_active_menu = $sub_menu['class_cd'];
										$this->_active_menu_name = $sub_menu['name'];
										break;
									}
								}
							}
						}
						
						break;
					}
				}
			}
		}


		if ($this->_active_menu_name == '' && strpos($query_string, '/admin/top') !== 0) {
			$this->_active_menu_name = Session::instance()->get('active_menu_name', NULL);
		} else {
			Session::instance()->set('active_menu_name', $this->_active_menu_name);
		}

		if ($this->_user->role_cd != '99' && $this->_action != 'logout' && $this->_action != 'chgbot' && $this->_action != 'copy'  && $this->_action != 'paste') {
			// chat only
			if ($this->_user->role_cd == '02') {
				$uri = $request->uri();
				$this->redirect_login();
			}
			$pos = strpos($this->_uri, '?');
			if ($pos === false) {
				$uri_action = substr($this->_uri, 1);
			} else {
				$uri_action = substr($this->_uri, 1, $pos - 1);
			}
			// current action is sys function?
			$sys_function = Session::instance()->get('sys_function', NULL);
			$is_sys_action = false;
			foreach ($sys_function as $f) {
				if (substr($f, -1, 1) == "*") {
					if (strpos($uri_action, substr($f, 0, strlen($f) - 1)) === 0) {
						$is_sys_action = true;
						break;
					}
				} else {
					if ($uri_action == $f) {
						$is_sys_action = true;
						break;
					}
				}
			}
			if ($is_sys_action) {
				$has_authority = false;
				foreach ($this->_user_function as $f) {
					if (substr($f, -1, 1) == "*") {
						if (strpos($uri_action, substr($f, 0, strlen($f) - 1)) === 0) {
							$has_authority = true;
							break;
						}
					} else {
						if ($uri_action == $f) {
							$has_authority = true;
							break;
						}
					}
				}
				if ($has_authority == false && $uri_action != 'admin/null') {
					$this->redirect_login();
				}
			}
		}

		// bot_id token check
		if ($request->post()) {
			$post = $request->post();
			if (array_key_exists('bot_id_token', $post)) {
				if ($post['bot_id_token'] != Session::instance()->get('bot_id', NULL)) {
					$this->redirect_error();
				}
			}
			if (array_key_exists('page_action', $post)) {
				$this->_page_action = $post['page_action'];
			}
		}

		$message = Session::instance()->get('talkappi-admin-message');
		if ($message == null) $message = '';
		View::bind_global('_message', $message);
		Session::instance()->delete('talkappi-admin-message');

		if ($this->_action != 'chgbot' && $this->_action != 'call') {
			$this->_bef_chgbot_action = $this->_action;
			$this->_bef_chgbot_query = $request->query();
			Session::instance()->set('bef_chgbot_action', $this->_action);
			Session::instance()->set('bef_chgbot_query', $request->query());
		} else {
			$this->_bef_chgbot_action = Session::instance()->get('bef_chgbot_action', 'top');
			$this->_bef_chgbot_query = Session::instance()->get('bef_chgbot_query', []);
		}

		$this->_query = $request->query();
		$this->_js = $request->action();
		$this->_user_id = Session::instance()->get('user_id', NULL);
		$this->_user = Session::instance()->get('user', NULL);
		$this->_bot_id = Session::instance()->get('bot_id', NULL);
		$this->_bot = Session::instance()->get('bot', NULL);
		$this->_bot_setting = Session::instance()->get('bot_setting', NULL);
		$this->_service_useflg = Session::instance()->get('service_useflg', []);
		$this->_item_id = Session::instance()->get('item_id', NULL);
		$this->_item_cd = Session::instance()->get('item_cd', NULL);
		$this->_settings = $this->_model->get_system_config('settings');
		// admin support lang
		// $this->_lang_cd = $model->get_userlang();
		$this->_lang_cd = Session::instance()->get('lang_cd_admin', NULL);
		if (!array_key_exists($this->_lang_cd, $this->_settings['admin_support_lang'])) {
			$this->_lang_cd = 'ja';
		}
		I18n::$lang = $this->_lang_cd;

		$this->_codes = $this->_model->get_config('code', $this->_lang_cd);
		//$this->_classes = $this->_model->get_config('class', $this->_lang_cd);
		if ($this->_bot != null) $this->_bot_lang = $this->_model->get_bot_support_lang($this->_bot);
		if ($this->_bot != null) $this->_bot_local_lang = $this->_model->get_bot_local_lang($this->_bot);
		$this->_functions = Session::instance()->get('functions', NULL);
		if ($this->_functions == NULL) {
			$functions = $this->_model->get_code_div(888803, $this->_lang_cd);
			$this->_functions = [];
			foreach ($functions as $f) {
				$this->_functions[$f['class_cd']] = $f['name'];
			}
			Session::instance()->set('functions', $this->_functions);
		}

		$setting = json_decode($this->_model->get_bot_setting($this->_bot_id, 'json_chatbot_mode'), true);
		if($setting){
			// _is_setting_flg: 公開中かどうかを判定
			$this->_disaster = ['_is_setting_flg' => true];
		}
	}

	private function _get_action($uri)
	{
		$pos = strpos($uri, '?');
		if ($pos === false) {
			return $uri;
		} else {
			return substr($uri, 0, $pos);
		}
	}

	public function redirect_error($message_id = '')
	{
		if ($message_id == '') {
			Session::instance()->destroy();
			$this->redirect('/login');
		} else {
			$this->redirect('/error?message_id=' . $message_id);
		}
	}

	public function redirect_login()
	{
		Session::instance()->destroy();
		$this->redirect('/login');
	}

	public function _redirect($url)
	{
		$this->redirect($url);
	}

	public function _set_message($cd)
	{
		Session::instance()->set('talkappi-admin-message', $cd);
	}

	public function _get_session($key, $value)
	{
		$ret = Session::instance()->get($key, NULL);
		if ($ret === NULL) {
			return $value;
		} else {
			return $ret;
		}
	}

	public function _set_session($key, $value)
	{
		Session::instance()->set($key, $value);
	}

	public function _del_session($key)
	{
		Session::instance()->delete($key);
	}

	public function _is_admin()
	{
		if ($this->_user->role_cd == '99') {
			return true;
		} else {
			return false;
		}
	}

	public function _change_bot($bot_id, $redirect_url = '')
	{
		$this->_del_session('report_bot_id');
		$this->_del_session('report1_start_date');
		$this->_del_session('report1_end_date');
		$this->_del_session('report1_scene_cd');
		$this->_del_session('report1_context_id');
		$this->_del_session('report8_scene_cd');
		$this->_del_session('report8_context_id');
		$this->_del_session('task_id');
		$this->_del_session('survey_result_tags');

		Session::instance()->delete('surveys_user_in_charge');
		Session::instance()->delete('items_class_cd');
		Session::instance()->delete('items_area_cd');
		$bot = ORM::factory('bot', $bot_id);
		Session::instance()->set('bot', $bot);
		Session::instance()->set('bot_id', $bot->bot_id);
		Session::instance()->set('bot_setting', $this->_model->get_bot_setting_dict($bot_id));
		Session::instance()->set('service_useflg', $this->_model->get_service_useflg($bot->bot_id));
		$item = ORM::factory('item')->where('bot_id', '=', $bot->bot_id)->find();
		Session::instance()->set('item_id', $item->item_id);
		Session::instance()->set('item_cd', $item->item_cd);
		// user of selected bot
		if ($this->_user->role_cd != '99' && $this->_user->role_cd != '80') {
			$bot_grp_id = $this->_model->get_grp_bot_id($this->_user->bot_id);
			if ($bot_grp_id == 0) {
				$user = ORM::factory('user', $this->_user->user_id);
				$user->last_login_date = date('Y-m-d H:i:s');
				$user->save();
			} else {
				$user = ORM::factory('user')->where('email', "=", $this->_user->email)->where("bot_id", "=", $bot_id)->find();
				// 2021/3/18 null user insert bug
				if (isset($user->user_id)) {
					Session::instance()->set('user', $user);
					// update last_login_date
					$user = ORM::factory('user', $user->user_id);
					$user->last_login_date = date('Y-m-d H:i:s');
					$user->save();
				}
			}
		} else {
			// update last_login_date
			$user = ORM::factory('user', $this->_user->user_id);
			$user->last_login_date = date('Y-m-d H:i:s');
			$user->save();
		}
		// switch bot in disaster mode
		$is_disaster_mode = $this->_get_session('disaster_mode', 0);
		if ($is_disaster_mode == 1) {
			$this->_model->send_disastermode_mail($bot->bot_id, $bot->bot_name, $this->_user->name, 'login');
		}
		// switch bot access for statistics
		$this->_bot_id = $bot_id;
		$this->_action = 'login';
		$this->_write_access_log();
		$this->_del_session('chat_bot_id');
		if ($redirect_url == '') {
			$this->redirect('/admin/top');
		} else {
			$this->redirect($redirect_url);
		}
	}

	public function before()
	{
		$this->_user = Session::instance()->get('user', NULL);
		if ($this->_user) {
			$facility_cd = $this->request->query('facility_cd', NULL);
			if ($facility_cd != null) {
				$query_string = '';
				foreach ($this->request->query() as $k => $v) {
					if ($k != 'facility_cd') $query_string = $query_string . $k . '=' . $v . '&';
				}
				if ($query_string == '') {
					$query_string = '/' . $this->request->uri();
				} else {
					$query_string = '/' . $this->request->uri() . '?' . $query_string;
				}
				$bot_id = $this->_model->get_bot_by_scene($facility_cd)['bot_id'];
				if ($this->_user->role_cd == '99' || $this->_user->role_cd == '80') {
					$this->_change_bot($bot_id, $query_string);
				} else {
					$orms = ORM::factory('user')->where('email', '=', $this->_user->email)->where('delete_flg', '=', 0)->find_all();
					$bots = [];
					foreach ($orms as $orm) {
						$bots[] = $orm->bot_id;
					}
					$bot_grp_id = $this->_model->get_grp_bot_id($bot_id);
					if ($bot_grp_id > 0 && in_array($bot_grp_id, $bots)) $bots[] = $bot_id;
					if (in_array($bot_id, $bots)) {
						$this->_change_bot($bot_id, $query_string);
					}
				}
			}
		}
		/*
		if(property_exists($this, '_transactional'))
		{
			if(is_array($this->_transactional))
			{
				$this->_transactional = in_array($this->request->action(), $controller->_transactional);
			}
		}
		
		// run action inside transaction
		if($this->_transactional)
		{
			Database::instance()->begin();
			
			// commit at end of script, even if someone uses die/exit
			register_shutdown_function(array($this, 'commit'));
		}
		*/

		ob_end_flush();
		$this->_write_access_log();
		parent::before();
		View::bind_global('_lang_cd', $this->_lang_cd);
		View::bind_global('_codes', $this->_codes);
		View::bind_global('_classes', $this->_classes);
		View::bind_global('_bot', $this->_bot);
		if ($this->_user->adminsite_flg == 0) $this->_bot_setting['num_admin_refresh_interval'] = -1;
		View::bind_global('_bot_setting', $this->_bot_setting);
		$admin_members = ORM::factory('botmember')->where('link_id', '=', strval($this->_user->user_id))->find_all();
		if (count($admin_members) > 0) {
			$admin_member_id = $admin_members[0]->member_id;
		}
		else {
			$admin_member_id = '';
		}
		View::bind_global('_admin_member', $admin_member_id);
		View::bind_global('_user', $this->_user);
		View::bind_global('_user_function', $this->_user_function);
		View::bind_global('_sys_menu', $this->_sys_menu);
		View::bind_global('_tab_menu', $this->_tab_menu);
		View::bind_global('_active_menu', $this->_active_menu);
		View::bind_global('_active_menu_name', $this->_active_menu_name);
		$bot_array = Session::instance()->get('bot_array', array());
		View::bind_global('_bot_array', $bot_array);
		View::bind_global('_action', $this->_action);
		View::bind_global('_path', $this->_path);
		View::bind_global('_uri', $this->_uri);
		View::bind_global('_can_copied_action', $this->_can_copied_action);
		View::bind_global('_can_pasted_action', $this->_can_pasted_action);
		View::bind_global('_disaster', $this->_disaster);
		View::bind_global('_query', $this->_query);
		if (strpos($this->_js, 'sysmsg') === 0) {
			$js = str_replace('sysmsg', 'msg', $this->_js) . '.js';
		} else if (strpos($this->_js, 'pushmsgtask') === 0) {
			$js = str_replace('pushmsg', '', $this->_js) . '.js';
		} else if (strpos($this->_js, 'pushmsgmembertask') === 0) {
			$js = str_replace('pushmsgmember', '', $this->_js) . '.js';
		} else if (strpos($this->_js, 'couponresults') === 0) {
			$js = str_replace('couponresults', 'surveyorders', $this->_js) . '.js';
		} else if ($this->_js == 'faqs') {
			$js = 'talks.js';
		} else if ($this->_js == 'faq') {
			$js = 'talk.js';
		} else if ($this->_js == 'faqnew') {
			$js = 'talknew.js';
		}
		/*
		else if ($this->_js == 'coupons') {
			$js = 'products.js';
		}
		else if ($this->_js == 'coupon') {
			$js = 'product.js';
		}
		else if ($this->_js == 'coupondesc') {
			$js = 'productdesc.js';
		}
		*/ else if ($this->_js == 'couponkeyword') {
			$js = 'productkeyword.js';
		} else if ($this->_js == 'coupondisplay') {
			$js = 'productdisplay.js';
		} else {
			$js = $this->_js . '.js';
		}
		View::bind_global('_js', $js);
		$asset_url = $this->_model->get_env('assets_url');
		View::bind_global('_assets', $asset_url);
		$base_url = $this->_model->get_env('base_url');
		View::bind_global('_base_url', $base_url);
		$service_url = $this->_model->get_env('service_url');
		View::bind_global('_service_url', $service_url);
		View::bind_global('_bot_id', $this->_bot_id);
		View::bind_global('_bot_lang', $this->_bot_lang);

		if ($this->_bot != NULL) {
			$ref_scene_cd = $this->_model->get_scene_ref($this->_bot->facility_cd, 'webchat');
			if ($ref_scene_cd == 'default') {
				$logo_url = '/assets/apps/webchat/logo.png';
			}
			else {
				$scene_path = $this->_model->get_scene_path($ref_scene_cd, 'webchat');
				$scene_url_path = $this->_model->get_scene_url_path($ref_scene_cd, 'webchat');
				$ext_array = $this->_model->get_setting('support_image_type');
				$logo_url = '';
				foreach ($ext_array as $ext) {
					if (file_exists($scene_path . 'logo' . '.' . $ext) == true) {
						$logo_url = $scene_url_path . 'logo' . '.' . $ext;
						break;
					}
				}
			}
		}
		View::bind_global('_logo_url', $logo_url);
		if ($this->_user != null && $this->_user->role_cd == '99') {
			$only_99 = '';
		}
		else {
			$only_99 = ' style="display:none;" ';
		}
		View::bind_global('_role_99_only', $only_99);
	}

	/*
	public function execute()
	{
		if(property_exists($this, '_transactional'))
		{
			if(is_array($this->_transactional))
			{
				$this->_transactional = in_array($this->request->action(), $controller->_transactional);
			}
		}
		
		// run action inside transaction
		if($this->_transactional)
		{
			Database::instance()->begin();
			
			// commit at end of script, even if someone uses die/exit
			register_shutdown_function(array($this, 'commit'));
		}
		
		try
		{
			return parent::execute();
		}
		catch (Exception $e)
		{
			if($this->_transactional && !$this->_transaction_finished)
			{
				Database::instance()->rollback();
				$this->_transaction_finished = true;
			}
			//$this->redirect_error('error.exception');
			throw $e;
		}
	}
	
	public function commit()
	{
		if($this->_transactional && !$this->_transaction_finished)
		{
			if (function_exists('http_response_code') && http_response_code() >= 400)
			{
				//Kohana::$log->add(Log::WARNING, 'rollback');
				Database::instance()->rollback();
			}
			else
			{
				//Kohana::$log->add(Log::NOTICE, 'commit');
				Database::instance()->commit();
			}
			$this->_transaction_finished = true;
			//Kohana::$log->write();
		}
	}
	*/

	/*
	public function execute()
	{
		// Execute the "before action" method
		$this->before();

		// Determine the action to use
		$action = 'action_'.$this->request->action();

		// If the action doesn't exist, it's a 404
		if ( ! method_exists($this, $action))
		{
			throw HTTP_Exception::factory(404,
				'The requested URL :uri was not found on this server.',
				[':uri' => $this->request->uri()]
			)->request($this->request);
		}

		try {
		// Execute the action itself
			$this->{$action}();
		} catch (Exception $e) {
			throw HTTP_Exception::factory(500,
			'The requested Error on this server.',
			[':uri' => $this->request->uri()]
			)->request($this->request);
		}
		// Execute the "after action" method
		$this->after();

		// Return the response
		return $this->response;
	}
	*/
	public function after()
	{
		//View::bind_global('_js', $this->_js . '.js?v=' . time());
		// service notification
		$bot_id = $this->_bot_id;
		if ($bot_id != NULL) {
			$grp_bot_id = $this->_model->get_grp_bot_id($bot_id);
			$log_date = date('Y-m-d', strtotime('-' . $this->_bot_setting['num_request_refresh_period'] . ' day'));
			if ($grp_bot_id == 0) {
				$service_count = DB::select(array(DB::expr('COUNT(`service_id`)'), 'service_count'))->from('t_bot_service')
					->where('bot_id', '>=', $bot_id)->where('bot_id', '<', intval($bot_id) + 1000)->where('service_status_cd', '=', '01')
					->where('log_time', '>', $log_date)->where('delete_flg', '=', 0)->execute()->get('service_count', 0);
			} else {
				$service_count = DB::select(array(DB::expr('COUNT(`service_id`)'), 'service_count'))->from('t_bot_service')
					->where('bot_id', '=', $bot_id)->where('service_status_cd', '=', '01')
					->where('log_time', '>', $log_date)->where('delete_flg', '=', 0)->execute()->get('service_count', 0);
			}
			$inquiry_unsupport_count = $this->_model->get_inquiry_unsupport_count($bot_id);
			// faq scene
			$orms = ORM::factory('botscene')->where('bot_id', '=', $bot_id)->where('use_faq_flg', '=', 1)->find_all();
			$scene_list = [];
			foreach ($orms as $orm) {
				$scene_list[$orm->scene_name] = $orm->label;
			}
			View::bind_global('_faq_scenes', $scene_list);
			View::bind_global('_service_count', $service_count);
			View::bind_global('_inquiry_unsupport_count', $inquiry_unsupport_count);
		}
		parent::after();
	}

	public function action_chgbot()
	{
		$bot_id = $this->request->query('id', NULL);
		// check authority
		if ($this->_user->role_cd != '99' && $this->_user->role_cd != '80') {
			$sql = "SELECT user_id, bot_id, role_cd FROM t_user WHERE email = :email AND delete_flg=0";
			$query = DB::query(Database::SELECT, $sql);
			$query->parameters(array(
					':email' => $this->_user->email,
			));
			$result = $query->execute()->as_array();
			$bot_array = array();
			foreach ($result as $bot) {
				$bot_array[$bot['bot_id']] = $bot['user_id'];
			}
			if (!array_key_exists($bot_id, $bot_array)) {
				$bot_grp_id = $this->_model->get_grp_bot_id($bot_id);
				if ($bot_grp_id > 0) {
					if (!array_key_exists($bot_grp_id, $bot_array)) $this->redirect_login();
					if ($this->_user->auth_all_groups_flg == 0) $this->redirect_login();
				}
				else {
					$this->redirect_login();
				}
				$user_id = $bot_array[$bot_grp_id];
			}
			else {
				$user_id = $bot_array[$bot_id];
			}
			$user = ORM::factory('user', $user_id);
			Session::instance()->set('user_id', $user->user_id);
			Session::instance()->set('user_name', $user->name);
			Session::instance()->set('user', $user);
			// #17216 authority refresh
			// get role function menu
			$role = ORM::factory('role')->where('role_cd', '=', $user->role_cd)->find();
			$role_function = explode(',', $role->functions);
			$div_function = $this->_model->get_code_div(888803, $this->_lang_cd);
			$sys_function = [];
			foreach($div_function as $f) {
				$sys_function[$f['class_cd']] = $f['word'];
			}
			$user_function = [];
			foreach($role_function as $f) {
				$user_function[] = $sys_function[$f];
			}
			Session::instance()->set('role_function', $role_function);
			Session::instance()->set('user_function', $user_function);
			$sys_menu = [];
			$tab_menu = [];
			$div_menu = $this->_model->get_code_div(888802, $this->_lang_cd);
			foreach($div_menu as $m) {
				if (trim($m['name']) == '') continue;
				if ($m['parent_cd'] == '') {
					$sys_menu[$m['class_cd']] = ["parent"=>$m, "children"=>[]];
				}
				else {
					if (strlen($m['parent_cd']) == 2) {
						$sys_menu[$m['parent_cd']]["children"][$m['class_cd']] = $m;
						$tab_menu[$m['class_cd']] = ["parent"=>$m, "children"=>[]];
					}
					else {
						$tab_menu[$m['parent_cd']]["children"][$m['class_cd']] = $m;
					}
				}
			}
			Session::instance()->set('sys_menu', $sys_menu);
			Session::instance()->set('tab_menu', $tab_menu);
		}

		if ($bot_id != 0 && ($this->_user->role_cd == '99' || $this->_user->role_cd == '80')) {
			$user = ORM::factory('user', $this->_user_id);
			if ($user->latest_bot == '') {
				$latest_bot = array();
			}
			else {
				$latest_bot = explode(',', $user->latest_bot);
			}
			for($i=0; $i<count($latest_bot); $i++) {
				if ($latest_bot[$i] == $bot_id) {
					unset($latest_bot[$i]);
				}
			}
			if (count($latest_bot) == 5) array_pop($latest_bot);
			array_unshift($latest_bot, $bot_id);
			$sql = "SELECT bot_id, bot_name FROM t_bot WHERE bot_id IN (" . implode(',', $latest_bot) . ")";
			$query = DB::query(Database::SELECT, $sql);
			$bots = $query->execute()->as_array('bot_id', 'bot_name');
			$bots_sort = array();
			foreach($latest_bot as $bot) {
				if (array_key_exists(strval($bot), $bots)) {
					$bots_sort[strval($bot)] = $bots[strval($bot)];
				}
			}
			Session::instance()->set('user_latest_bot', $bots_sort);
			$user->latest_bot = implode(',', $latest_bot);
			$user->save();
		}
		// action mapping
		$action_map = [
				"talk"=>"talks", "talknew"=>"talks", "faq"=>"faqs", "msglist"=>"msglist", "msgnew"=>"msglist", "msg"=>"msglist", "msgdesc"=>"msglist", "sysmsg"=>"sysmsglist", "sysmsgdesc"=>"sysmsglist",
				"botclasscodeedit"=>"botclasscode", "botscene"=>"botscenes","bottheme"=>"botscenes","botthemeZ"=>"botscenes","task"=>"tasks", "tasklog"=>"tasks", "taskresult"=>"tasks",
				"survey"=>"surveys", "surveydesc"=>"surveys", "surveyentry"=>"surveys", "surveyresult"=>"surveys", "surveyreport"=>"surveys", "surveyorders"=>"surveys",
				"inquiry"=>"inquirys", "inquirydesc"=>"inquirys", "inquiryentry"=>"inquirys", "inquiryresult"=>"inquirys",
				"maximumorders"=>"maximums","maximumorder"=>"maximums","maximum"=>"maximums",
				"items"=>"items", "item"=>"items", "itemcode"=>"items", "itemdesc"=>"items", "itemdisplay"=>"items", "itemkeyword"=>"items","botlinemenu"=>"botline",
				"products"=>"products", "product"=>"products", "productdesc"=>"products", "productdisplay"=>"products", "productkeyword"=>"products",
				"coupons"=>"coupons", "coupon"=>"coupons", "coupondesc"=>"coupons", "couponkeyword"=>"coupons", "copy"=>"top",
				"sitepage"=>"sitepages", "sitepagedetail"=>"sitepages",
				"notice"=>"notices", "noticedesc"=>"notices",
				"issue"=>"issues", "issuenew"=>"issues", "getredmineusersapi"=>"issues",
				"visitgroups"=>"visits", "visitgroupsdata"=>"visits", "memberdetail"=>"members",
				"reception"=>"receptions","receptionentry"=>"receptions","receptiondisplay"=>"receptions"
		];
		if ($this->_user->role_cd == '99' || $this->_user->role_cd =='80') {
			// copy 特別処理
			$obj = Session::instance()->get('item_copy');
			if ($obj != NULL) {
				if ($this->_bef_chgbot_action == 'copy' || $this->_bef_chgbot_action == 'paste') {
					if ($obj['item_div'] == 9) {
						$this->_path = 'admininquiry';
						$action_map['copy'] = 'inquirys';
					}
					else if ($obj['item_div'] == 19) {
						$this->_path = 'adminorder';
						$action_map['copy'] = 'inquirys';
					}
					else if ($obj['item_div'] == 12) {
						$action_map['copy'] = 'maximums';
					}
					else if ($obj['item_div'] == 8) {
						$action_map['copy'] = 'surveys';
					}
					else if ($obj['item_div'] == 7) {
						$action_map['copy'] = 'coupons';
					}
				}
			}
			$new_action = $this->_bef_chgbot_action;
			if (isset($action_map[$this->_bef_chgbot_action])) $new_action = $action_map[$this->_bef_chgbot_action];
			if ($new_action == 'items') {
				// item_div保持
				$new_action = $new_action . '?div=' . $this->_bef_chgbot_query['div'];
			}
			$this->_change_bot($bot_id, '/' . $this->_path . '/' . $new_action);
		}
		else {
			// top
			$this->_change_bot($bot_id);
		}
	}

	protected function _can_copied($uri)
	{
		$pos = strpos($uri, '?');
		if ($pos !== false) $uri = substr($uri, 0, $pos);
		if (array_key_exists($uri, $this->_can_copied_action)) return $this->_can_copied_action[$uri]['item_div'];
		return false;
	}
	protected function _can_pasted($uri)
	{
		$pos = strpos($uri, '?');
		if ($pos !== false) $uri = substr($uri, 0, $pos);
		if (array_key_exists($uri, $this->_can_pasted_action)) return $this->_can_pasted_action[$uri]['item_div'];
		return false;
	}
	protected function _copy($bot_id, $item_div, $item_id)
	{
		$bot = ORM::factory('bot', $bot_id);
		$bot_name = $bot->bot_name;
		if ($item_div == 9 || $item_div == 19) {
			$item = ORM::factory('inquiry', $item_id);
			$item_name = $item->inquiry_name;
		} else if ($item_div == 8) {
			$item = ORM::factory('survey', $item_id);
			$item_name = $item->survey_name;
		} else if ($item_div == 12) {
			$item = ORM::factory('botmaximum')->where('id', '=', $item_id)->where('bot_id', '=', $bot_id)->find();
			$item_name = $item->name;
		} else if ($item_div == 7) {
			$item = ORM::factory('coupon', $item_id);
			$item_name = $item->coupon_name;
		}
		$obj = ['bot_id' => $bot_id, 'bot_name' => $bot_name, 'item_div' => $item_div, 'item_id' => $item_id, 'item_name' => $item_name];
		Session::instance()->set('item_copy', $obj);
		return $obj;
	}
	protected function _paste()
	{
		return Session::instance()->get('item_copy', null);
	}
	
	protected function _verytop_copy($bot_id, $scene_cd)
	{
		$obj = ['bot_id' => $bot_id, 'scene_cd' => $scene_cd];
		Session::instance()->set('verytop_copy', $obj);
		return $obj;
	}
	protected function _verytop_paste()
	{
		return Session::instance()->get('verytop_copy', null);
	}
	protected function _page_navi($items)
	{
		if (!is_array($items)) $items = json_decode($items, true);
		$navi_str = '';
		foreach ($items as $item) {
			if (isset($item['url'])) {
				$navi_str = $navi_str . '<a href="' . $item['url'] . '">' . $this->_functions[$item['f']] . '</a><span style="margin: 0 8px;"><img src="/assets/admin/css/img/icon-breadcrumb.svg" /></span>';
			} else {
				$navi_str = $navi_str . '<span>' . $this->_functions[$item['f']] . '</span>';
			}
		}
		$this->template->page_navi = $navi_str;
	}
	protected function _write_access_log($content = '')
	{
		if ($this->_settings["user_access_log"]) {
			//Talkappi Base Bot	ダッシュボード (top) 記録しない
			if ($this->_action == 'top' && $this->_bot_id == 0) return;
			$user_access = ORM::factory("Useraccess");
			$user_access->bot_id = $this->_bot_id;
			$user_access->user_id = $this->_user_id;
			$user_access->ip_address = $this->_model->getRemoteIPAddress();
			$user_access->action = $this->_action;
			if (is_array($content)) {
				$user_access->content = json_encode($content, JSON_UNESCAPED_UNICODE);
			} else {
				$user_access->content = $content;
			}
			$user_access->save();
		}
	}
}
