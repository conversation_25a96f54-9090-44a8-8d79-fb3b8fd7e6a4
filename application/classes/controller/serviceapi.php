<?php defined ( 'SYSPATH' ) or die ( 'No direct script access.' );

class Controller_Serviceapi extends Controller_Template_Adminbase
{
    public $_model;

    public function __construct(Request $request, Response $response) {
        parent::__construct($request, $response);
        $this->_model = new Model_Adminmodel($this->_lang_cd);
        $this->_model->init($this->_bot_id);
    }
    
    public function action_call() 
    {
        $this->auto_render = FALSE;
        $this->response->headers('Access-Control-Allow-Origin', '*');
        $this->response->headers('Content-Type', 'application/json');
        try {
            $this->is_login();
            $post = $this->request->post();
            $api_name = $post['api_name'];
            $api_action = $post['api_action'];
            $api_method = $post['api_method'];
            $api_params = json_decode($post['api_params'], true);
            $response = $this->_model->call_admin_api($api_name, $api_action, $api_method, $api_params);
            $this->response->status(200)->body(json_encode(['result' => 'success', 'data' => $response], JSON_UNESCAPED_UNICODE));
        } catch (\Throwable $th) {
            $response = ['error' => $th->getMessage()];
            $this->response->status(200)->body(json_encode($response, JSON_UNESCAPED_UNICODE));
        }
    }

    private function is_login()
    {
        $login_user = Session::instance()->get('user', NULL);
        if (!$login_user) {
            throw new HTTP_Exception_403('Unauthorized');
        }
    }
}