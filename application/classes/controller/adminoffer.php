<?php defined('SYSPATH') or die('No direct script access.');

class Controller_Adminoffer extends Controller_Template_Adminbase
{
	public $_action_path = '/adminoffer/';
	public $_view_path = 'admin/offer/';

	public function action_offers()
	{
		$view = View::factory($this->_view_path . 'offers');
		$this->template->content = $view;
	}

	public function action_offer()
	{
		$view = View::factory($this->_view_path . 'offer');
		// プレビューモードかどうかを確認
		$is_preview = $this->request->query('mode') ? $this->request->query('mode') === 'preview' : false;
		$view->is_preview = $is_preview;
		$this->template->content = $view;
	}

	public function action_users()
	{
		$view = View::factory($this->_view_path . 'users');
		$this->template->content = $view;
	}
}
