<?php defined('SYSPATH') or die('No direct script access.');

class Controller_Login extends Controller_Template_Normalbase {

	public $_model;

	public function __construct(Request $request, Response $response)
	{
		parent::__construct($request, $response);
		$this->_model = new Model_Adminmodel();
		ini_set('max_execution_time', 300); // 300 seconds = 5 minutes
	}

	private function login($email, $password, $facility_cd, $lang_cd_admin, $update_login_time = TRUE, $is_passkey_auth = false)
	{
		$this->detect_and_set_accept_lang();
		$users = ORM::factory('user')
			->where('email', '=', $email)
			->where('delete_flg', '=', 0)
			->where('role_cd', '>', '00')
			->find_all();
		if (count($users) == 0) {
			return __('admin.login.message.invalid_id_and_mail');
		}	
		
		if ($users[0]->login_error_times >= 5) {
			if (date('Y-m-d H:i:s', strtotime('-30 minute')) > $users[0]->login_error_time) {
				DB::update('t_user')->set(['login_error_times'=>0, 'login_error_time'=>null])->where('email', '=', $users[0]->email)->execute();	
			}
			else {
				return __('admin.login.message.account_locked');
			}
		}
		
		$user = null;
		if (!$is_passkey_auth) {
			foreach($users as $u) {
				if ($this->_model->verify_password($password, $u->password)) $user = $u; 
			}
		}
		else {
			// For passkey authentication, pick the account whose last_login_date is the most recent
			foreach ($users as $u) {
				if ($user === null || strtotime($u->last_login_date) > strtotime($user->last_login_date)) {
					$user = $u;
				}
			}
		}
		
		if ($user == null) {
			$user_access = ORM::factory("Useraccess");
			$user_access->user_id = $users[0]->user_id;
			$user_access->ip_address = $_SERVER['REMOTE_ADDR'];
			$user_access->action = 'login';
			$user_access->content = 'password error';
			$user_access->save();
			DB::update('t_user')->set(['login_error_times'=>$users[0]->login_error_times + 1, 
					'login_error_time'=>date('Y-m-d H:i:s')])->where('email', '=', $users[0]->email)->execute();
			return __('admin.login.message.invalid_id_or_mail_check_again');
		}
		
		$admin_user_psw_change_span = $this->_model->get_bot_setting($user->bot_id, 'admin_user_psw_change_span');
		if ($admin_user_psw_change_span != '') {
			$last_password_modify_date = substr($user->password_modify_time, 0, 10);
			$last_password_modify_date = date("Y-m-d",strtotime("+$admin_user_psw_change_span day",strtotime($last_password_modify_date)));
			if ($last_password_modify_date < date('Y-m-d')) {
				return __('admin.login.message.password_expired');
			}
		}

		$json_mgr_limited_setting = $this->_model->get_bot_setting($user->bot_id, 'json_mgr_limited_setting', true);
		if (isset($json_mgr_limited_setting['white_ip']) && count($json_mgr_limited_setting['white_ip']) > 0) {
			$ip_address = $this->_model->getRemoteIPAddress();
			if (!in_array($ip_address, $json_mgr_limited_setting['white_ip'])) {
				$user_access = ORM::factory("Useraccess");
				$user_access->user_id = $users[0]->user_id;
				$user_access->ip_address = $ip_address;
				$user_access->action = 'login';
				$user_access->content = 'ip restrictions';
				$user_access->save();
				return __('admin.login.message.restricted_ip');
			}
		}
		/*
		if ($user->allow_ip != '') {
			$ip_address = $this->_model->getRemoteIPAddress();
			//Log::instance()->add(Log::DEBUG, 'login ip=' . $ip_address);
			if (strpos($user->allow_ip, $ip_address) === FALSE) {
				$user_access = ORM::factory("Useraccess");
				$user_access->user_id = $users[0]->user_id;
				$user_access->ip_address = $_SERVER['REMOTE_ADDR'];
				$user_access->action = 'login';
				$user_access->content = 'ip restrictions';
				$user_access->save();
				return __('admin.login.message.restricted_ip');
			}
		}
		*/

		//session_destroy();
		
		Session::instance()->set('user_id', $user->user_id);
		Session::instance()->set('user_name', $user->name);
		Session::instance()->set('user', $user);
		if (empty($lang_cd_admin)) {
			$lang_cd_admin = 'ja';
		}
		Session::instance()->set('lang_cd_admin', $lang_cd_admin);

		// get role function menu
		$role = ORM::factory('role')->where('role_cd', '=', $user->role_cd)->find();
		$role_function = explode(',', $role->functions);
		// $div_function = $this->_model->get_code_div(888803, 'ja');
		$div_function = $this->_model->get_code_div(888803, $lang_cd_admin);
		$sys_function = [];
		foreach($div_function as $f) {
			$sys_function[$f['class_cd']] = $f['word'];
		}
		$user_function = [];
		foreach($role_function as $f) {
			$user_function[] = $sys_function[$f];
		}
		Session::instance()->set('sys_function', array_values($sys_function));
		Session::instance()->set('role_function', $role_function);
		Session::instance()->set('user_function', $user_function);
		$sys_menu = [];
		$tab_menu = [];
		// $div_menu = $this->_model->get_code_div(888802, 'ja');
		$div_menu = $this->_model->get_code_div(888802, $lang_cd_admin);
		foreach($div_menu as $m) {
			if (trim($m['name']) == '') continue;
			if ($m['parent_cd'] == '') {
				$sys_menu[$m['class_cd']] = ["parent"=>$m, "children"=>[]];
			}
			else {
				if (strlen($m['parent_cd']) == 2) {
					$sys_menu[$m['parent_cd']]["children"][$m['class_cd']] = $m;
					$tab_menu[$m['class_cd']] = ["parent"=>$m, "children"=>[]];
				}
				else {
					$tab_menu[$m['parent_cd']]["children"][$m['class_cd']] = $m;
				}
			}
		}
		Session::instance()->set('sys_menu', $sys_menu);
		Session::instance()->set('tab_menu', $tab_menu);
		
		$bots = [];
		$bot_array = [];
		$has_grp_bot = false;
		if ($user->role_cd == '99' || $user->role_cd == '80') {
			$bots = ORM::factory('bot')->where('delete_flg', '=', 0)->order_by('bot_id')->find_all();
			foreach($bots as $b) {
				$grp_bot_id = $this->_model->get_grp_bot_id($b->bot_id);
				if ($grp_bot_id > 0) continue;
				$bot_array[] = [$b->bot_id, $b->bot_name];
			}
		}
		else {
			$user_bots = $this->_model->get_user_bots($user->email);
			$p_bot_arr = [];
			foreach($user_bots as $b) {
				$bots[] = $b;
				$grp_bot_id = $this->_model->get_grp_bot_id($b->bot_id);
				if ($grp_bot_id == 0) {
					$bot_array[] = [$b->bot_id, $b->bot_name];
					$p_bot_arr[] = $b->bot_id;
					$has_grp_bot = true;
				}
				else if ($grp_bot_id < 0) {
					$bot_array[] = [$b->bot_id, $b->bot_name];
				}
				else {
					if (!in_array($grp_bot_id, $p_bot_arr)) {
						$grp_bot = ORM::factory('bot', $grp_bot_id);
						$bot_array[] = [$grp_bot_id, $grp_bot->bot_name];
						$p_bot_arr[] = $grp_bot_id;
						$has_grp_bot = true;
					}
				}
			}
		}
		Session::instance()->set('bot_array', $bot_array);
		Session::instance()->set('has_grp_bot', $has_grp_bot);
		
		
		// select facility_cd ->bot
		$bot = NULL;
		if ($facility_cd != NULL) {
			$bot_result = $this->_model->get_bot_by_scene($facility_cd); 
			if ($bot_result != null) {
				$param_bot_id = $bot_result['bot_id'];
				foreach($bots as $b) {
					if ($b->bot_id == $param_bot_id) {
						$bot = $b;
						break;
					}
				}
				if ($bot == NULL) {
					$param_grp_bot_id = $this->_model->get_grp_bot_id($param_bot_id);
					if ($param_grp_bot_id > 0) {
						foreach($bots as $b) {
							if ($b->bot_id == $param_grp_bot_id) {
								$bot = $b;
								break;
							}
						}
					}
				}
			}
		}
		
		// only 1 bot first bot ->bot 
		if ($bot == NULL && count($bots) == 1) {
			$bot = $bots[0];
			$bot_grp_id = $this->_model->get_grp_bot_id($bot->bot_id);
			// group bot user must change bot
			if ($bot_grp_id == 0 && $user->role_cd != '73' && $user->role_cd != '74') $bot = NULL;
		}

		// latest bot
		$latest_bot = explode(',', $user->latest_bot);
		if ($latest_bot[0]!='') {
			$sql = "SELECT bot_id, bot_name FROM t_bot WHERE bot_id IN (" . implode(',', $latest_bot) . ")";
			$query = DB::query(Database::SELECT, $sql);
			$bots = $query->execute()->as_array('bot_id', 'bot_name');
			$bots_sort = [];
			foreach($latest_bot as $b) {
				if (array_key_exists(strval($b), $bots)) {
					$bots_sort[strval($b)] = $bots[strval($b)];
				}
			}
			Session::instance()->set('user_latest_bot', $bots_sort);
			$user_upd = ORM::factory('user', $user->user_id);
			$user_upd->latest_bot = implode(',', $latest_bot);
			$user_upd->save();
			if (count($bots_sort) > 0 && $bot == NULL) $bot = ORM::factory('bot', key($bots_sort));
		}
		
		if ($bot != NULL) {
			Session::instance()->set('bot', $bot);
			Session::instance()->set('bot_id', $bot->bot_id);	
			$item = ORM::factory('item')->where('bot_id', '=', $bot->bot_id)->find();
			Session::instance()->set('item_id', $item->item_id);
			Session::instance()->set('item_cd', $item->item_cd);
			Session::instance()->set('bot_setting', $this->_model->get_bot_setting_dict($bot->bot_id));
			Session::instance()->set('service_useflg', $this->_model->get_service_useflg($bot->bot_id));
			// update last_login_date
			$user = ORM::factory('user', $user->user_id);
			if ($update_login_time) {
				$user->last_login_date = date('Y-m-d H:i:s');
			}
			$user->save();

			// login access
			$user_access = ORM::factory("Useraccess");
			$user_access->bot_id = $bot->bot_id;
			$user_access->user_id = $user->user_id;
			$user_access->ip_address = $this->_model->getRemoteIPAddress();
			$user_access->action = 'login';
			$user_access->content = 'success';
			$user_access->save();
		}
		else {
			Session::instance()->delete('bot_id');	
		}
		// clear login error times
		DB::update('t_user')->set(['login_error_times'=>0])->where('email', '=', $users[0]->email)->execute();
		return TRUE;
	}

	public function action_index()
	{
		//$this->_redirect('https://admin.talkappi.com/');
		$errors = NULL;
		$message = NULL;
		$post = NULL;
		$two_factor_auth_required = FALSE; 
		$is_two_factor_auth_error = FALSE;
		$two_factor_auth_error_type = '';
		$is_passkey_auth = false;
		$passkey_auth_result = false;
		$passkey_auth_error_type = '';
		$passkey_auth_redirect_url = '';

		if ($this->request->post()){
			$post = $this->request->post();
			$is_passkey_auth = isset($post['passkey_credential']) && isset($post['passkey_challengeId']);
			if (isset($post['2fa_code'])) {
				$two_factor_auth_result = $this->is_valid_two_factor_auth_token($post['2fa_code']);
				if ($two_factor_auth_result === TRUE) {
					Session::instance()->delete('two_factor_flag'); 
					Session::instance()->delete('2fa_attempts');
					$email = Session::instance()->get('email');
            		$this->handle_successful_login($email, $post, $redirect_url = NULL);
				} else {
					$two_fa_attempts = Session::instance()->get('2fa_attempts', 0);
					$two_fa_attempts++;
					Session::instance()->set('2fa_attempts', $two_fa_attempts);
					if ($two_fa_attempts >= 5) {
						Session::instance()->delete('2fa_attempts'); 
						Session::instance()->delete('email'); 
						Session::instance()->delete('password'); 
						$this->_redirect('/login');
						return;
					}
					$two_factor_auth_required = TRUE;
					if ($two_factor_auth_result == 'invalid_code') {
						$two_factor_auth_error_type = 'invalid_code';
					} elseif ($two_factor_auth_result == 'expired') {
						$two_factor_auth_error_type = 'expired';
					}					
				}
			} else if ($is_passkey_auth) {
				$redirect_url = isset($post['redirect']) ? $post['redirect'] : NULL;
				$facility_cd = isset($post['facility_cd']) ? $post['facility_cd'] : NULL;
				// Handle passkey authentication
				if (isset($post['passkey_credential']) && isset($post['passkey_challengeId'])) {
					$is_passkey_auth = true;
					$credential = json_decode($post['passkey_credential'], true);
					$challengeId = $post['passkey_challengeId'];
					
					// Call the finish API from backend for security
					$finish_url = 'http://************:9610/passkey/auth/finish';
					$finish_data = json_encode([
						'credential' => $credential,
						'challengeId' => $challengeId
					]);
					
					// Build and forward Origin header (scheme+host[:port])
					$host   = $_SERVER['HTTP_HOST'] ?? '';
					$scheme = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ? 'https' : 'http';
					$origin = $host ? $scheme . '://' . $host : '';
					$headers = ['Content-Type: application/json'];
					if ($origin) {
						$headers[] = 'Origin: ' . $origin;
						$headers[] = 'X-Frontend-Origin: ' . $origin;
					}

					$ch = curl_init();
					curl_setopt($ch, CURLOPT_URL, $finish_url);
					curl_setopt($ch, CURLOPT_POST, 1);
					curl_setopt($ch, CURLOPT_POSTFIELDS, $finish_data);
					curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
					curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
					curl_setopt($ch, CURLOPT_TIMEOUT, 30);
					
					$response = curl_exec($ch);
					$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
					curl_close($ch);
					if ($http_code === 200) {
						$result = json_decode($response, true);
						if ($result && isset($result['user']['email'])) {
							Session::instance()->delete('two_factor_flag');
							Session::instance()->delete('2fa_attempts');
							$email = $result['user']['email'];
							$lang_cd_admin = isset($post['lang_cd_admin']) ? $post['lang_cd_admin'] : 'ja';
							
							// Get user for passkey auth
							$users = ORM::factory('user')
										->where('email', '=', $email)
										->where('delete_flg', '=', 0)
										->where('role_cd', '>', '00')
										->find_all();
								
							if (count($users) > 0) {
								// For passkey auth, bypass password verification in login method
								$login_result = $this->login($email, '', $facility_cd, $lang_cd_admin, true, true);
								
								if ($login_result === TRUE) {
									if (isset($post['disaster']) && $post['disaster'] == 1) {
										// 災害時モードなら災害時モードのページにリダイレクト
										Session::instance()->set('disaster_mode', 1);
										$bot = Session::instance()->get('bot');
										$user = Session::instance()->get('user');
										if ($bot != null && $user != null) {
											$this->_model->send_disastermode_mail($bot->bot_id, $bot->bot_name, $user->name, 'login');
										}
										$passkey_auth_redirect_url = '/admin/disastersetting';
										$this->response->status(200);
										$this->response->headers('Content-Type', 'application/json');
										$this->response->body(json_encode(['ok' => true, 'redirect' => $passkey_auth_redirect_url]));
										return; 
									}
									$this->handle_successful_login($email, $post, $redirect_url, $is_passkey_auth);
									$passkey_auth_result = true;
									$passkey_auth_error_type = '';
									$passkey_auth_redirect_url = $redirect_url;
									//return;
								} else {
									$message = $login_result;
									$passkey_auth_result = false;
									$passkey_auth_error_type = 'login_failed';
								}
							} else {
								$passkey_auth_result = false;
								$passkey_auth_error_type = 'user_not_found';
							}
						} else {
							$passkey_auth_result = false;
							$passkey_auth_error_type = 'passkey_auth_failed';
						}
					} else {
						$passkey_auth_result = false;
						$passkey_auth_error_type = 'passkey_auth_failed';
					}
					if ($is_passkey_auth && $passkey_auth_result == false) {
						// response ok is false
						$this->response->status(400);
						$this->response->headers('Content-Type', 'application/json');
						$this->response->body(json_encode(['ok' => false, 'message' => $passkey_auth_error_type]));
						return;
					}
					if ($is_passkey_auth && $passkey_auth_result == true) {
						$this->response->status(200);
						$this->response->headers('Content-Type', 'application/json');
						$this->response->body(json_encode(['ok' => true, 'redirect' => $passkey_auth_redirect_url]));
						return;
					}
				}
			} else {
				$redirect_url = isset($post['redirect']) ? $post['redirect'] : NULL;
				$facility_cd = isset($post['facility_cd']) ? $post['facility_cd'] : NULL;
				$validation = Validation::factory($this->request->post())
					->rule('email', 'not_empty')
					->rule('email', 'email')
					->rule('password', 'not_empty');
				if ($validation->check()){
					$post = $this->request->post();
					$email = $post['email'];
					if (empty($post['lang_cd_admin'])) {
						$post['lang_cd_admin'] = 'ja';
					}
					if (isset($post['disaster']) && $post['disaster'] == 1) {
						// 災害時モードなら二段階認証をスキップする
						Session::instance()->delete('two_factor_flag');
						Session::instance()->delete('2fa_attempts');
						$result = $this->login($post['email'], $post['password'], $facility_cd, $post['lang_cd_admin'], true); 
						if ($result === TRUE) { 
							Session::instance()->set('disaster_mode', 1);
							$bot = Session::instance()->get('bot');
							$user = Session::instance()->get('user');
							if ($bot != null && $user != null) {
								$this->_model->send_disastermode_mail($bot->bot_id, $bot->bot_name, $user->name, 'login');
							}
							$this->_redirect('/admin/disastersetting');
							return; 
						} else { 
							$message = $result; 
						} 
					} else { 
						Session::instance()->delete('disaster_mode'); // 災害時モードのsessionが残っていたら削除する
						if ($this->is_two_factor_auth_required($post['email']) || Session::instance()->get('two_factor_flag') === true) {
							$result = $this->login($post['email'], $post['password'], $facility_cd, $post['lang_cd_admin'], false);
							if ($result === TRUE) {
								$two_factor_auth_required = true;
								Session::instance()->set('email', $post['email']);
								Session::instance()->set('password', $post['password']);
								Session::instance()->set('two_factor_flag', true);
								$tfa_code = $this->generate_two_factor_auth_token($post['email']);
								$param = [
									'bot_id' => 0,
									'receiver' => $email,
									'title' => __('admin.login.message.2fa_mail_title'),
									'body' => __('admin.login.message.2fa_mail_body_1') . $tfa_code . __('admin.login.message.2fa_mail_body_2'),
								];
								$this->_model->post_enginehook('service', 'sendmail','', $param);
							} else { 
								$message = $result;
							}
						} else { 
							$result = $this->login($post['email'], $post['password'], $facility_cd, $post['lang_cd_admin'], true);
							if ($result === TRUE) {
								$this->handle_successful_login($post['email'], $post, $redirect_url);
							} else {
								$message = $result;
							}
						}
					}
				} else {
					$errors = $validation->errors('login');
				}					
			}
		}

		$facility_cd = $this->request->query('facility_cd', NULL);
		$redirect_url = $this->request->query('redirect', NULL);
		//$redirect_url = urldecode($redirect_url);
		if ($redirect_url != NULL) {
			$startPos = strpos($redirect_url, 'facility_cd=');
			$len = strlen('facility_cd=');
			if ($startPos !== false) {
				$endPos = strpos($redirect_url, '&', $startPos + $len);
				if ($endPos === false) {
					$facility_cd = substr($redirect_url, $startPos + $len);
				}
				else {
					$facility_cd = substr($redirect_url, $startPos + $len, $endPos - $startPos - $len);
				}
			}
		}
		$email = Cookie::get('email', NULL);
		$password = Cookie::get('password', NULL);
		$lang_cd_admin = Cookie::get('lang_cd_admin', NULL);
		if ($email != NULL && $password != NULL) {
			if ($this->login($email, $password, $facility_cd, $lang_cd_admin) == TRUE) {
				Session::instance()->delete('report_bot_id');
				if($redirect_url == NULL) {
					$user = Session::instance()->get('user');
					if ($user != null && $user->role_cd == '02') {
						$this->_redirect('/chat');
					}
					$this->_redirect('/admin/top');
				}
				else {
					$redirect_url = urldecode($redirect_url);
					$this->_redirect($redirect_url);
				}
			}
		}	
		

		$this->detect_and_set_accept_lang();
		$view = View::factory ('admin/login');
		$view->lang_cds = $this->_model->get_system_config('lang');
		if ($errors != NULL) $view->errors = $errors;
		$view->message = $message;
		$view->message = $message;
		$view->admin_support_lang = $this->_model->get_setting('admin_support_lang');
		$view->action = 'login';
		$view->email = $email;
		$view->redirect = $redirect_url;
		$view->facility_cd = $facility_cd;
		$view->_assets = $this->_model->get_env('assets_url');
		$view->two_factor_auth_required = $two_factor_auth_required;
		$view->two_factor_auth_error_type = $two_factor_auth_error_type;
		$view->is_passkey_auth = $is_passkey_auth;
		$this->response->body($view);
	}
	
	public function action_forget()
	{
		$message = '';
		$this->detect_and_set_accept_lang();
		if ($this->request->post()){
			$email = $this->request->post()['email'];
			if(!preg_match("^[_a-z0-9-]+(\.[_a-z0-9-]+)*@[a-z0-9-]+(\.[a-z0-9-]+)*(\.[a-z]{2,3})$^",$email)) {
				$message = __('admin.login.message.input_valid_mail');
			}
			if ($message == '') {
				$users = ORM::factory('user')->where('email', '=', $email)->where('delete_flg', '=', 0)->find_all();
				if (count($users) > 0) {
					$link_id = $this->_model->set_link_param('03', ['email'=>$email]);
					$orm = ORM::factory('link', $link_id);
					$data_url = $this->_model->get_env('admin_url') . 'login/forgetinput?id=' . $link_id;
					$params = ['data_url'=>$data_url, 'data_url_limit'=>$orm->valid_date_time];
					$mail_model = new Model_Email();
					$ret = $mail_model->send_message($email, '', 'mail.link_type_03', 'ja', $params);
					$this->_redirect('/login/forget?email=' . $email);
				}
				else {
					$message = __('admin.login.message.email_not_exist');
				}
			}
		}
		else {
			$email = $this->request->query('email', NULL);
		}
		$view = View::factory ('admin/login');
		$view->admin_support_lang = $this->_model->get_setting('admin_support_lang');
		$view->email = $email;
		$view->message = $message;
		$view->action = 'forget';
		$view->redirect = '';
		$view->facility_cd = '';
		$view->_assets = $this->_model->get_env('assets_url');
		$this->response->body($view);
	}
	
	public function action_forgetinput() {
		$message = '';
		if ($this->request->post()){
			$post = $this->request->post();
			$user_mail = Session::instance()->get('forget_user_mail', NULL);
			Session::instance()->set('forget_finish', 0);
			if ($user_mail != trim($post['email'])) {
				$message = __('admin.login.message.forgot_password_invalid_mail');
				$view = View::factory ('admin/login');
				$view->forget_input = 1;
				$view->message = $message;
				$view->finish = Session::instance()->get('forget_finish', NULL);
				$view->email = $post['email'];
				$view->_assets = $this->_model->get_env('assets_url');
				$this->response->body($view);
				return;
			}
			else {
				DB::update('t_user')->set(['password'=>$this->_model->encrypt_password(trim($post['password1'])), 'login_error_times'=>0])->where('email', '=', $user_mail)->where('delete_flg', '=', 0)->execute();
				Session::instance()->set('forget_finish', 1);
				$this->redirect('/login/forgetinput');
			}
		}
		if (Session::instance()->get('forget_finish', NULL) === 1) {
			$view = View::factory ('admin/login');
			$view->forget_input = 1;
			$view->message = $message;
			$view->finish = Session::instance()->get('forget_finish', NULL);
			$view->email = '';
			$view->_assets = $this->_model->get_env('assets_url');
			$this->response->body($view);
			return;
		}
		Session::instance()->delete('forget_finish');
		$view = View::factory ('admin/login');
		$view->forget_input = 1;
		$link_id = $this->request->query('id', NULL);
		$params = $this->_model->get_link_param($link_id, true);
		if ($params == 'NOT_EXIST') {
			$message = __('admin.login.message.forgot_password_invalid_url');
		}
		else if ($params == 'INVALIDATE') {
			$message = __('admin.login.message.forgot_password_invalid_url');
		}
		else if ($params == 'EXPIRED') {
			$message = __('admin.login.message.forgot_password_overtime_url');
		}
		else {
			Session::instance()->set('forget_finish', 0);
			Session::instance()->set('forget_user_mail', $params['email']);
			$view->message = $message;
		}
		$view->message = $message;
		$view->finish = Session::instance()->get('forget_finish', NULL);
		$view->email = '';
		$view->_assets = $this->_model->get_env('assets_url');
		$this->response->body($view);
	}

	public function action_modify()
	{
		$message = '';
		$this->detect_and_set_accept_lang();
		
		if ($this->request->post()){
			$post = $this->request->post();
			$email = $post['email'];
			$password = $post['old_password'];
			if(!preg_match("^[_a-z0-9-]+(\.[_a-z0-9-]+)*@[a-z0-9-]+(\.[a-z0-9-]+)*(\.[a-z]{2,3})$^",$email)) {
				$message =  __('admin.login.message.input_valid_mail');
			}
			if ($message == '') {
				$users = ORM::factory('user')->where('email', '=', $email)->where('delete_flg', '=', 0)->find_all();
				$user = null;
				foreach($users as $u) {
					if ($this->_model->verify_password($password, $u->password)) $user = $u;
				}
				if ($user == null) {
					$message = __('admin.login.message.invalid_id_and_mail');
				}
				if ($message == '') {
					DB::update('t_user')->set(['password'=>$this->_model->encrypt_password(trim($post['password1'])), 'password_modify_time'=>date('Y-m-d H:i:s'), 'login_error_times'=>0])->where('email', '=', $email)->where('delete_flg', '=', 0)->execute();
					$message = 'success';
				}
			}
		}
		else {
			$email = $this->request->query('email', NULL);
		}
		$view = View::factory ('admin/login');
		$view->admin_support_lang = $this->_model->get_setting('admin_support_lang');
		$view->email = $email;
		$view->message = $message;
		$view->action = 'modify';
		$view->redirect = '';
		$view->facility_cd = '';
		$view->_assets = $this->_model->get_env('assets_url');
		$this->response->body($view);
	}

	private function detect_and_set_accept_lang(){
		$model = new Model_Basemodel();
		$accept_lang = $model->get_userlang();
		$admin_support_lang = $this->_model->get_setting('admin_support_lang');
		if (!array_key_exists($accept_lang, $admin_support_lang)) {
			$accept_lang = 'ja';
		}
		I18n::lang($accept_lang);
	}

	public function action_token() {
		$id = $this->request->query('id', NULL);
		if ($id == NULL) {
			$this->response->body('token login failure');
			return;
		}
		$data = $this->_model->get_link_param($id, false);
		if (!is_array($data)) {
			$this->response->body('token login failure');
			return;
		}
		$orm = ORM::factory('link', $id);
		//$orm->delete();
		$result = $this->login($data['email'], $data['password'], $data['facility_cd'], $data['lang_cd']);
		if ($result === TRUE) {
			$this->response->body('token login success');
		}
		else {
			$this->response->body('token login failure');
		}
	}

	public function is_two_factor_auth_required($email) {
		if ($_SERVER['SERVER_NAME'] == 'localhost') {
			Session::instance()->delete('two_factor_flag');
			return false;
		}
		$users = ORM::factory('user')
			->where('email', '=', $email)
			->where('delete_flg', '=', 0)
			->where('role_cd', '>', '00')
			->order_by('last_login_date', 'DESC') 
			->find_all();
		if (count($users) == 0) {
			return false;
		}
		$user = $users[0];
		if ($user->{'two_factor_auth_flg'} != 1) {
			if (Session::instance()->get('two_factor_flag') !== null) {
				Session::instance()->delete('two_factor_flag');
			}
			return false;
		}
		$current_ip = $this->_model->getRemoteIPAddress();
		$last_login_date = new DateTime($user->last_login_date);
		$three_days_ago = new DateTime('-3 days');
		if (empty($user->tfa_token) 
			|| $user->current_ip_address != $current_ip 
			|| $last_login_date < $three_days_ago) { 
			return true;
		}
		return false;
	}	

	public function generate_two_factor_auth_token($email) {
		$users = ORM::factory('user')
			->where('email', '=', $email)
			->order_by('last_login_date', 'DESC')
			->find_all();
		$user = $users[0];
		$new_tfa_token = str_pad(mt_rand(0, 999999), 6, '0', STR_PAD_LEFT);
		$tfa_token_expiration = date('Y-m-d H:i:s', strtotime('+30 minutes'));
		DB::update('t_user')->set([
			'tfa_token' => $new_tfa_token,
			'tfa_token_expiration' => $tfa_token_expiration,
		])->where('user_id', '=', $user->user_id)->execute();
		return $new_tfa_token;
	}	

	public function is_valid_two_factor_auth_token($two_fa_code) {
		$email = Session::instance()->get('email');
		$users = ORM::factory('user')
			->where('email', '=', $email)
			->find_all();
		// 複数のユーザーレコードがある場合、1つでも2FAコードが一致すれば成功
		foreach ($users as $user) {
			if ($user->tfa_token == $two_fa_code) {
				if (strtotime($user->tfa_token_expiration) < time()) {
					return 'expired';
				}
				$password = Session::instance()->get('password');
				Session::instance()->delete('email');
				Session::instance()->delete('password');
				return TRUE;
			}
		}
		return 'invalid_code';
	}

	public function handle_successful_login($email, $post, $redirect_url, $is_passkey_auth = false) {
		// 2FA不要、または2FAが成功後のログイン処理
		$current_ip = $this->_model->getRemoteIPAddress();
		$user = Session::instance()->get('user');
		if ($user && $user->user_id) {
			DB::update('t_user')->set([
				'last_login_date' => date('Y-m-d H:i:s'),
				'current_ip_address' => $current_ip
			])->where('user_id', '=', $user->user_id)->execute();
		}
		if (array_key_exists('remember', $post)) {
			Cookie::set('email', $email);
			Cookie::set('password', $post['password']);
			Cookie::set('lang_cd_admin', $post['lang_cd_admin']);
		}

		if ($is_passkey_auth) {
			return;
		}

		if($redirect_url == NULL) {
			if ($user != null && $user->role_cd == '02') {
				$this->_redirect('/chat');
			}
			$this->_redirect('/admin/top');
		}
		else {
			$redirect_url = urldecode($redirect_url);
			$this->_redirect($redirect_url);
		}
	}
}