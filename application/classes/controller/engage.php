<?php defined('SYSPATH') or die('No direct script access.');

class Controller_Engage extends Controller_Template_Adminbase {

	public $_auth_required = TRUE;
	public $_transactional = true;
	public $_model;
	public $_member_model;
	public $_view_path = 'admin/engage/';

	public function __construct(Request $request, Response $response)
	{
		parent::__construct($request, $response);
		$this->_model = new Model_Adminmodel();
		$this->_member_model = new Model_Membermodel();
		$this->_model->init($this->_bot_id);
		$this->_member_model->init($this->_bot_id);
	}

	public function action_members()
	{
		$post = NULL;
		if ($this->request->post()) {
			$post = $this->request->post();
		}
		if (!isset($post['bot_id'])) {
			$post['bot_id'] = $this->_bot_id;
		}
		if (!isset($post['start_date'])) {
			$post['start_date'] = date('Y-m-d', strtotime('-12 month'));
		}
		if (!isset($post['end_date'])) {
			$post['end_date'] = date('Y-m-d');
		}
		if (!isset($post['lang_cd'])) {
			$post['lang_cd'] = $this->_lang_cd;
		}
		if (!isset($post['page'])) {
			$page = 1;
		} else {
			$page = $post['page'];
		}

		$filter = [
			"age" => [],
			"area" => [],
			"car" => [],
			"member_type" => [],
			"purpose" => [],
			"reservation_channel" => []
		];
		if (isset($post['page_action']) && $post['page_action'] == 'filter') {
			$filter = (array) json_decode($post['filter_condition']);
		}
		$page = $this->request->query('page') ?: 1;
		$params = [
			'bot_id' => $post['bot_id'],
			'lang_cd' => $post['lang_cd'],
			'page' => $page,
			'start_date' => $post['start_date'],
			'end_date' => $post['end_date'],
			'filter' => $filter
		];
		$response_data = $this->_model->call_admin_api('engage', 'getmemberslist', 'POST', $params);
		$members_list_data = $response_data['member_list_data'];
		$filter_condition = $response_data['filter_condition'];
		$view = View::factory($this->_view_path . 'members');
		$view->filter = $filter;
		$view->post = $post;
		$view->members = $members_list_data['members'];
		$view->filterCondition = $filter_condition;
		$view->page = $members_list_data['page'];
		$view->totalMembers = $members_list_data['total'];
		$view->totalPages = ceil($members_list_data['total'] / $members_list_data['perPage']);
		$this->template->content = $view;    
	}


	public function action_memberdetail()
	{
    $customer_id = $this->request->query('id');
		$params = [
			'bot_id' => $this->_bot_id,
			'customer_id' => $customer_id
		];
		$response_data = $this->_model->call_admin_api('engage', 'getmemberdetail', 'POST', $params);
		$member = $response_data['member'];
		$histories = $response_data['histories'];
		$view = View::factory($this->_view_path . 'memberdetail');
		$view->member = $member;
		$view->histories = $histories;
		$this->template->content = $view;    
		$this->_page_navi('[{"f":"8002","url":"/engage/members"},{"f":"8003"}]');
	}

	public function action_visits()
	{
		$post = NULL;
		if ($this->request->post()) {
			$post = $this->request->post();
		} else {
			$post['start_date'] = Session::instance()->get('member_visits_start_date', NULL);
			$post['end_date'] = Session::instance()->get('member_visits_end_date', NULL);
		}
		if ($post['start_date'] == NULL) {
			$post['start_date'] = date('Y-m-d', strtotime('-1 year'));
		}
		if ($post['end_date'] == NULL) {
			$post['end_date'] = '';
		}
		Session::instance()->set('member_visits_start_date', $post['start_date']);
		Session::instance()->set('member_visits_end_date', $post['end_date']);
		$params = [
			'bot_id' => $this->_bot_id,
			'start_date' => $post['start_date'],
			'end_date' => $post['end_date']
		];
		$visits_groups = $this->_model->call_admin_api('engage', 'getvisitsdata', 'POST', $params);
		$view = View::factory($this->_view_path . 'visits');
		$view->post = $post;
		$view->visitsgroups = $visits_groups;
		$view->admin_service_url = $this->_model->get_env('admin_service_url');
		$this->template->content = $view;
	}

	public function action_visitgroups()
	{
		$date = null;
		if ($this->request->post()) {

		} else {
			$date = $this->request->query("date", null);
		}

		$msg = ORM::factory('botmsg')->where('bot_id', '=', $this->_bot_id)->where('msg_cd', '=', 'engage_room_number_order')->find();
		$msg_desc = ORM::factory('botmsgdesctpl')->where('msg_id', '=', $msg->msg_id)->where('lang_cd', '=', 'ja')->find();

		$view = View::factory($this->_view_path . 'visitgroups');
		$view->date = $date;
		$view->engage_room_number_order = $msg_desc->content ?: '[]';
		$view->admin_service_url = $this->_model->get_env('admin_service_url');
		$this->template->content = $view;
	}

	public function action_visit() {
		$bot_id = $this->_bot_id;
		$visit_id = $this->request->query('id');
		$user_name = $this->_user->name;
		$user_id = $this->_user->user_id;
		$view = View::factory($this->_view_path . 'visit');
		$response_data = $this->_model->call_admin_api('engage', 'getvisit', 'POST', [
			'bot_id' => $bot_id,
			'visit_id' => $visit_id,
			'user_id' => $user_id,
			'user_name' => $user_name
		]);
		$header_info = $response_data['header_info'];
		$important_items = $response_data['important_items'];
		if (empty($important_items)) {
			$view->important_items = null;
		} else {
			$user_ids = array_column($important_items, 'createUserId');
			$user_names = $this->_model->get_user_names_by_ids($user_ids);
			foreach ($important_items as &$item) {
				if (isset($user_names[$item['createUserId']])) {
					$item['createBy'] = $user_names[$item['createUserId']];
				}
			}
			unset($item);
			$view->important_items = $important_items;
		}
		$view->header_info = $header_info;
		$view->admin_service_url = $this->_model->get_env('admin_service_url');
		$view->user_id = $this->_user_id;
		$this->template->content = $view;
	}
}
