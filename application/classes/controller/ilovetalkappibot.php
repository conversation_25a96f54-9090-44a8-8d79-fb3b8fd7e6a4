<?php defined('SYSPATH') or die('No direct script access.');
class Controller_ilovetalkappibot extends Controller_Template_Normalbase{

	public function action_index()
	{
		$user = Session::instance()->get('user', NULL);
		if (! $user) {
			$this->_redirect('/error');
		}
		$model = new Model_Adminmodel();
		$view = View::factory ('admin/botlist');
		$view->bot_class = $model->get_code('10', 'ja');
		$bots = ORM::factory('bot')->where('bot_id', '>', 0)->where('delete_flg', '=', 0)->order_by('bot_class_cd')->order_by('bot_id')->find_all();
		$bot_scenes = ORM::factory('botscene')->order_by('bot_id')->find_all();
		$scenes = array();
		foreach($bot_scenes as $scene) {
			if (array_key_exists(strval($scene->bot_id), $scenes)) {
				$scenes[strval($scene->bot_id)][] = $scene;
			}
			else {
				$scenes[strval($scene->bot_id)] = [$scene];
			}
		}
		$logos = array();
		$grp_array = array();
		foreach($bots as $bot) {
			$bot_grp_id = $model->get_grp_bot_id($bot->bot_id);
			if ($bot_grp_id > 0) {
				if (array_key_exists($bot_grp_id, $grp_array)) {
					$logos[$bot->bot_id] = $grp_array[$bot_grp_id];
				}
				else {
					$logos[$bot->bot_id] = $model->get_bot_setting($bot_grp_id, 'default_scene_cd');
					$grp_array[$bot_grp_id] = $logos[$bot->bot_id];
				}
			}
		}
		$view->bots = $bots;
		$view->base_url = $model->get_env('base_url');
		$view->scenes = $scenes;
		$view->logos = $logos;
		$this->response->body($view);
	}
}
