<?php defined ( 'SYSPATH' ) or die ( 'No direct script access.' );

class Controller_Public extends Controller_Template_Templatebase {
	
	public $template_file = 'public/template';
	public $_model;
	public $_aws_model;
	public $_lang_cd;
	public $_bot_id;
	public $_bot;
	public $_facility_cd;
	public $_ip_address;
	public $_action;

	public function before() {
		ob_end_flush();
		$base_url = $this->_model->get_env('base_url');
		View::bind_global('base_url', $base_url);
		$engine_url = $this->_model->get_env('engine_url');
		View::bind_global('engine_url', $engine_url);
		$asset_url = $this->_model->get_env('assets_url');
		View::bind_global('_assets', $asset_url);
		View::bind_global('facility_cd', $this->_facility_cd);
		View::bind_global('_action', $this->_action);
		parent::before();
	}
	
	public function __construct(Request $request, Response $response)
	{
		parent::__construct($request, $response);
		$this->_model = new Model_Adminmodel();
		$this->_aws_model = new Model_Aws();
		$this->_ip_address = $this->_model->getRemoteIPAddress();
		$this->_lang_cd = $this->_model->get_userlang();
		$this->_action = $request->action();
		$this->_bot_id = $this->_get_session('pubic_bot_id', NULL);
	}

	public function action_apply()
	{
		$lang_cd = 'ja';
		$post = [];

		if ($this->request->post()){
			$post = $this->request->post();
			$post['mail'] = trim($post['mail']);
			if ($post['mail'] == '' || !$this->is_email($post['mail'])) {
				$this->redirect('/public/apply?id=' . $post['id'] . '&err=1');
			}
			else {
				$link_type_cd = $post['link_type_cd'];
				
				$link = ORM::factory('link');
				$link->link_id = md5(time().mt_rand(0, 1000));
				$link->link_type_cd = $link_type_cd;
				$link->valid_date_time = date("Y-m-d",strtotime("+365 day"));
				$ext_data_string = $this->_model->get_bot_tpl_message($this->_bot_id, 'link_type_' . $link_type_cd, 'ja');
				$ext_data = [];
				if ($ext_data_string != '') $ext_data = json_decode($ext_data_string, true);
				$link_data = [];
				foreach($ext_data as $k=>$v) {
					$link_data[$k] = $post[$k];
				}
				$link_data['bot_id'] = $this->_bot_id;
				$link->param1 = json_encode($link_data);
				$link->save();
				
				$mail_message = $this->_model->get_bot_mal_message($this->_bot_id, 'mail.link_type_' . $link_type_cd, 'ja');
				$mail_message['body'] = str_replace("{base_url}", $this->_model->get_env('base_url'), $mail_message['body']);
				$mail_message['body'] = str_replace("{link_id}", $link->link_id, $mail_message['body']);
				$mail = new Model_Email();
				$result = $mail->send($link_data['mail'], '', $mail_message['subject'], $mail_message['body']);
				$this->redirect('/public/applyfinish');
			}
		}
		$scene_cd = $this->request->query('id', NULL);
		if ($scene_cd == NULL) {
			$this->_error();
			return;
		}
		$this->_facility_cd = $scene_cd;
		
		$item = $this->_model->get_bot_by_scene($scene_cd);
		if ($item == null) $this->_error();
		$this->_bot_id = $item['bot_id'];
		$this->_set_session('pubic_bot_id', $this->_bot_id);
		
		$err = $this->request->query('err', NULL);
		if ($err != NULL) {
			$post['err'] = $err;
		}
		$post['id'] = $scene_cd;
		$post['link_type_cd'] = '90';
		$post['item_div'] = 2;
		$post['class_cd'] = '02';
		$view = View::factory ('public/apply');
		$view->post = $post;
		$view->public_apply_contents_description = $this->_model->get_bot_txt_message($this->_bot_id, 'public_apply_contents.description', $lang_cd);
		$this->template->bot_name = $this->_model->get_bot_txt_message($this->_bot_id, 'bot_name', $lang_cd);
		$this->template->content = $view;		
	}
	
	public function action_applyfinish()
	{
		$lang_cd = 'ja';
		$this->_facility_cd = $this->_model->get_bot_setting($this->_bot_id, 'default_scene_cd');
		$view = View::factory('public/applyfinish');
		$this->template->bot_name = $this->_model->get_bot_txt_message($this->_bot_id, 'bot_name', $lang_cd);
		$this->template->content = $view;
		$this->_del_session('public_bot_id');
	}
	
	public function action_item()
	{
		$errors = NULL;
		$message = NULL;
		$post = NULL;
		$item = NULL;
		$lang_cd = 'ja';
		$item_data_def = array();
		$item_div = 2;
		$banners = [];
		if ($this->request->post()){
			$params = $this->_get_session('pubic_params', NULL);
			$this->_bot_id = $params['bot_id'];
			$item_div = $params['item_div'];
			$this->_bot = ORM::factory('bot', $this->_bot_id);
			$post = $this->request->post();
			$act = $post['act'];
			if ($act == 'delete') {
			}
			else {				
				$is_new = false;
				if (!array_key_exists('item_id', $params)) {
					$is_new = true;
					$item = ORM::factory('item');
					$item->item_id = $this->_model->get_max_item_id($this->_bot);
					$item->item_div = $item_div;
					$item->item_cd = (string)$item->item_id;
					$item->bot_id = $this->_bot_id;
				}
				else {
					$item = ORM::factory('item', $params['item_id']);
				}
				$item->class_cd = trim($post['class_cd_hidden']);
				/*
				if(array_key_exists('class_cd', $post) && $post['class_cd'] !='') {
					$item->class_cd = $post['class_cd'];
				}
				else if(array_key_exists('class_cd1', $post) && $post['class_cd1'] !='') {
					$item->class_cd = $post['class_cd1'];
				}
				else if(array_key_exists('class_cd2', $post) && $post['class_cd2'] !='') {
					$item->class_cd = $post['class_cd2'];
				}
				else if(array_key_exists('class_cd3', $post) && $post['class_cd3'] !='') {
					$item->class_cd = $post['class_cd3'];
				}
				else {
					if ($is_new) $item->class_cd = '';
				}
				*/
				if(array_key_exists('area_cd', $post) && $post['area_cd'] !='') {
					$item->area_cd = $post['area_cd'];
				}
				else if(array_key_exists('area_cd1', $post) && $post['area_cd1'] !='') {
					$item->area_cd = $post['area_cd1'];
				}
				else if(array_key_exists('area_cd2', $post) && $post['area_cd2'] !='') {
					$item->area_cd = $post['area_cd2'];
				}
				else if(array_key_exists('area_cd3', $post) && $post['area_cd3'] !='') {
					$item->area_cd = $post['area_cd3'];
				}
				else {
					if ($is_new) $item->area_cd = '';
				}
				$item->item_status_cd = $post['item_status_cd'];
				$item->country_cd = 'JP';
				$item->upd_user = 0;
				$item->upd_time = date('Y-m-d H:i:s');
				$item->save();
				if ($is_new) {
					$orm = ORM::factory('itemdisplay');
					$orm->bot_id = $item->bot_id;
					$orm->item_id = $item->item_id;
					$orm->item_div = $item_div;
					$orm->sort_no1 = $item->item_id % 10000;
					$orm->sort_no2 = 0;
					$orm->sort_no3 = 0;
					$orm->sort_no4 = 0;
					$orm->sort_no5 = 0;
					$orm->lang_display = $this->_bot->lang_cd;
					$orm->public_flg = 0;
					$orm->recommend = 0;
					$orm->save();
				}
				
				$image_url = '';
				/*
				if ($_FILES['image']['size'] > 0) {
					$image_url= $this->_aws_model->put_item($this->_bot_id, $item->item_id);
					$this->_aws_model->resize_image_url($image_url, 800, 0);
				}
				*/
				if (array_key_exists('image_base64', $post) && $post['image_base64'] !='') {
					$image_url = $this->_aws_model->put_base64_file($this->_bot_id, $post['image_base64'], $item->item_id, 'item');
					$this->_aws_model->resize_image_url($image_url, 800, 0);
				}
				if ($is_new) {
					$item_description= ORM::factory('itemdescription');
					$item_description->item_id = $item->item_id;
					$item_description->lang_cd = $lang_cd;
					$item_description->item_name = $post['item_name'];
					$item_description->sell_point = $post['sell_point'];
					$item_description->tel = $post['tel'];
					$item_description->address = $post['address'];
					$item_description->url = $post['url'];
					$item_description->item_image = $image_url;
					$item_description->save();
				}
				else {
					$up_array = [];
					$up_array['item_name'] = $post['item_name'];
					//$up_array['description'] = $post['item_description'];
					$up_array['sell_point'] = $post['sell_point'];
					$up_array['tel'] = $post['tel'];
					$up_array['url'] = htmlspecialchars_decode($post['url']);
					$up_array['address'] = $post['address'];
					//$up_array['map_url'] = htmlspecialchars_decode($post['map_url']);
					$up_array['upd_user'] = 0;
					$up_array['upd_time'] = date('Y-m-d H:i:s');
					DB::update('t_item_description')->
					set($up_array)->where('item_id', '=', $item->item_id)->where('lang_cd', '=', $lang_cd)->execute();			
				}
				
				if ($is_new) {
					$params['item_id'] = $item->item_id;
					$link_params = [];
					foreach($params as $k=>$v) {
						if ($k == 'link_id' || $k == 'modify_link_id') continue;
						$link_params[$k] = $v;
					}
					$link_id = $this->_model->set_link_param('90', $link_params);
					$params['modify_link_id'] = $link_id;
					$mail_message = $this->_model->get_bot_mal_message($this->_bot_id, 'mail.link_type_90_finished', 'ja');
					$mail_message['body'] = str_replace("{base_url}", $this->_model->get_env('base_url'), $mail_message['body']);
					$mail_message['body'] = str_replace("{link_id}", $link_id, $mail_message['body']);
					$mail_message['body']  = str_replace("{item_name}", $post['item_name'], $mail_message['body']);
					$mail = new Model_Email();
					$result = $mail->send($params['mail'], '', $mail_message['subject'], $mail_message['body']);
					$label = '追加';
				}
				else {
					$label = '更新';
				}
				$bot = ORM::factory('bot', $this->_bot_id);
				$message = 'お客様下記コンテンツを' . $label . PHP_EOL . PHP_EOL . $post['item_name'] . PHP_EOL . PHP_EOL . '確認URL: ' . PHP_EOL . $this->_model->get_env('admin_url') . 'admin/item?id=' . $item->item_id;
				$this->_model->post_redmine($bot->bot_id, '03', $bot->bot_id, ['subject'=>'「店舗コンテンツ' . $label . '」(' . $bot->bot_name . ')', 'description'=>$message], 'cs_sale_contents');
				
				$this->_set_session('pubic_params', $params);
				$this->redirect('/public/itemfinish');
			}
		}
		else {
			$link_id = $this->request->query('id', NULL);
			$params = $this->_model->get_link_param($link_id, true);
			
			if (!is_array($params)) $this->_error();
			
			$this->_bot_id = $params['bot_id'];
			if (array_key_exists('item_id', $params)) {
				$item_id = $params['item_id'];
				$params['modify_link_id'] = $link_id;
			}
			else {
				$item_id = NULL;
				$params['link_id'] = $link_id;
			}
			$this->_set_session('pubic_params', $params);
			$item_div = $params['item_div'];
			$post['tags'] = [];
			if ($item_id == NULL) {
				$post['item_kind'] = '01';
				$post['class_cd'] = '';
				$post['def_class_cd'] = $params['class_cd'];
				$post['item_class_cd'] = '01';
				$post['item_status_cd'] = '01';
				$post['item_class_type_cd'] = '';
				$post['item_cd'] = '';
				$post['start_date'] = '';
				$post['end_date'] = '';
				$post['start_mm'] = '0';
				$post['end_mm'] = '0';
				$post['start_dd'] = '';
				$post['end_dd'] = '';
				$post['area_cd'] = '';
				$post['location_lon'] = '';
				$post['location_lat'] = '';
				$post['position_x'] = '';
				$post['position_y'] = '';
				$post['position_z'] = '';
				$post['item_name'] = '';
				$post['sell_point'] = '営業時間：10:00〜22:00(LO 21:30）' . PHP_EOL . '定休日：火、祝日';
				$post['url'] = '';
				$post['tel'] = '';
				$post['address'] = '';
				$post['item_image'] = '';
				foreach($item_data_def as $k=>$v) {
					$post[$k] = '';
				}
				$permission = 1;
			}
			else {
				$item = ORM::factory('item')->where('item_id', '=', $item_id)->find();
				$item_description = ORM::factory('itemdescription')->where('item_id', '=', $item_id)->where('lang_cd', '=', $lang_cd)->find();
				$post['start_mm'] = '0';
				$post['end_mm'] = '0';
				$post['start_dd'] = '';
				$post['end_dd'] = '';
				/*
				 if ($item->regular_range != '') {
				 $regular_range = explode('-', $item->regular_range);
				 $post['start_mmdd'] = substr($regular_range[0], 0, 2) . '-' . substr($regular_range[0], 2, 2);
				 $post['end_mmdd'] = substr($regular_range[1], 0, 2) . '-' . substr($regular_range[1], 2, 2);
				 }
				 */
				if ($item->regular_start != '') {
					$s = explode('-', $item->regular_start);
					$post['start_mm'] = intval($s[0]);
					$post['start_dd'] = intval($s[1]);
				}
				if ($item->regular_end != '') {
					$s = explode('-', $item->regular_end);
					$post['end_mm'] = intval($s[0]);
					$post['end_dd'] = intval($s[1]);
				}
				$post['item_status_cd'] = $item->item_status_cd;
				$post['class_cd'] = trim($item->class_cd);
				$post['def_class_cd'] = $params['class_cd'];
				$post['item_cd'] = $item->item_cd;
				$post['start_date'] = $item->start_date;
				$post['end_date'] = $item->end_date;
				$post['area_cd'] = $item->area_cd;
				$post['location_lon'] = $item->location_lon;
				$post['location_lat'] = $item->location_lat;
				$post['position_x'] = $item->position_x;
				$post['position_y'] = $item->position_y;
				$post['position_z'] = $item->position_z;
				
				$post['item_name'] = $item_description->item_name;
				$post['sell_point'] = $item_description->sell_point;
				$post['url'] = $item_description->url;
				$post['tel'] = $item_description->tel;
				$post['address'] = $item_description->address;
				$post['item_image'] = $item_description->item_image;
				$post['link_id'] = $item->link_id;
				
				if ($item->item_data != '') {
					$item_data = json_decode($item->item_data, true);
					foreach($item_data_def as $k=>$v) {
						$post[$k] = $item_data[$k];
					}
					if (array_key_exists('tags', $item_data)) {
						$post['tags'] = explode(',', $item_data['tags']);
					}
				}
				else {
					foreach($item_data_def as $k=>$v) {
						$post[$k] = '';
					}
				}
			}
		}
		$this->_facility_cd = $this->_model->get_bot_setting($this->_bot_id, 'default_scene_cd');
		$btn_name = $this->_model->get_bot_tpl_message($this->_bot_id, 'btn_name', $lang_cd);
		$config_button = json_decode($btn_name, true);
		$def_buttons = $this->_model->get_bot_lst_message($this->_bot_id, 'item_def_button_' . $item_div, $lang_cd);
		$buttons = [];
		foreach($def_buttons as $def) {
			if (strpos($def['content'], "BTN_") === 0) {
				$def['content']= $config_button[$def['content']];
			}
			$buttons[] = array("title"=>$def['content'], "url"=>'');
		}
		
		$chatpreview = View::factory('admin/chatpreview');
		if ($item_id != NULL) {
			$item_array = array("title"=>$item_description->item_name,
					"content"=>$item_description->sell_point,
					"image"=>$item_description->item_image,
					"url"=>$item_description->url,
					"buttons"=>$buttons
			);
		}
		else {
			$item_array = array("title"=> '',
					"content"=>'',
					"image"=>'',
					"url"=>'',
					"buttons"=>$buttons
			);
		}
		$chatpreview->button_list = $config_button;
		$chatpreview->msg_type_cd = 'car';
		$chatpreview->item = $item_array;
		$chatpreview->public = 1;
		$chatpreview->s3_url = 1;
		$view = View::factory ('public/item');
		
		$view->item = $item;

		$view->icons = [];
		$view->banners = [];
		$div_item_class = $this->_model->get_bot_setting($this->_bot_id, 'div_item_class_' . $item_div);
		$view->div_item_class = $div_item_class;
		$view->div_item_area = $this->_model->get_bot_setting($this->_bot_id, 'div_item_area_' . $item_div);
		if ($post['class_cd'] == '') {
			$post['class_name_array'] = array();
		}
		else {
			$post['class_name_array'] = $this->_model->get_class_name($div_item_class, $post['class_cd']);
		}
		$view->post = $post;
		
		$view->item_data_def = $item_data_def;
		$view->item_bot_id = $this->_bot_id;
		$view->item_div = $item_div;
		$view->lang_cd = $lang_cd;
		$months = [];
		$months[] = '-';
		for($i=1; $i<=12;$i++) {
			$months[strval($i)] = strval($i);
		}
		$view->months = $months;
		$view->status_codes = $this->_model->get_code('11');
		$view->chatpreview = $chatpreview;
		$view->public_apply_contents_abstract = $this->_model->get_bot_txt_message($this->_bot_id, 'public_apply_contents.abstract', $lang_cd);
		$this->template->bot_name = $this->_model->get_bot_txt_message($this->_bot_id, 'bot_name', $lang_cd);
		$this->template->content = $view;
	}
	
	public function action_itemfinish()
	{
		$params = $this->_get_session('pubic_params', null);
		if ($params == null) $this->_error();
		$this->_bot_id = $params['bot_id'];
		$this->_facility_cd = $this->_model->get_bot_setting($this->_bot_id, 'default_scene_cd');
		$view = View::factory ('public/itemfinish');
		/*
		$mail_message = $this->_model->get_bot_mal_message($this->_bot_id, 'mail.link_type_90_finished', 'ja');
		$mail_message['body'] = str_replace("{item_name}", $post['item_name'], $mail_message['body']);
		$params['item_id'] = $item->item_id;
		$link_id = $this->_model->set_link_param('90', $params);
		$data_url = $this->_model->get_env('base_url') . 'public/item?id=' . $link_id;
		*/
		if (array_key_exists('link_id', $params)) {
			$view->add_url = $this->_model->get_env('base_url') . 'public/item?id=' . $params['link_id'];
		}
		else {
			$view->add_url = '';
		}
		$view->modify_url = $this->_model->get_env('base_url') . 'public/item?id=' . $params['modify_link_id'];
		$this->template->bot_name = '';
		$this->template->content = $view;
	}
	
	private function _get_session($key, $value)
	{
		$ret = Session::instance()->get($key, NULL);
		if ($ret === NULL) {
			return $value;
		}
		else {
			return $ret;
		}
	}
	
	private function _set_session($key, $value)
	{
		Session::instance()->set($key, $value);
	}
	
	private function _del_session($key)
	{
		Session::instance()->delete($key);
	}
	
	private function _error($message_id = 'error.exception')
	{
		$this->redirect('/public/error?message=' . $message_id);
	}

	private function is_email($user_email)
	{
		$chars = "/^([a-z0-9+_]|\\-|\\.)+@(([a-z0-9_]|\\-)+\\.)+[a-z]{2,6}\$/i";
		if (strpos($user_email, '@') !== false && strpos($user_email, '.') !== false)
		{
			if (preg_match($chars, $user_email)){
				return true;
			}
			else{
				return false;
			}
		}
		else{
			return false;
		}
	}
	
}
