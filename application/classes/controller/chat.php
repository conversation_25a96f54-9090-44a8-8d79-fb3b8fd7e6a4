<?php defined('SYSPATH') or die('No direct script access.');
class Controller_Cha<PERSON> extends Controller_Template_Normalbase{
	public $_user;
	public $_bot;
	public $_bot_id;
	public $_user_id;
	public $_model;
	public $_sns_model;
	public $_session_pre = '';
	public $_lang_cd = 'ja';

	private function _get_session($key, $pre = '') {
		if ($pre == '') {
			$key = $this->_session_pre . $key;
		}
		else {
			$key = $pre . $this->_session_pre . $key;
		}
		return Session::instance()->get($key, NULL);
	}
	
	private function _set_session($key, $value, $pre = '') {
		if ($pre == '') {
			$key = $this->_session_pre. $key;
		}
		else {
			$key = $pre . $this->_session_pre . $key;
		}
		Session::instance()->set($key, $value);
	}
	
	private function _delete_session($key, $pre = '') {
		if ($pre == '') {
			$key = $this->_session_pre. $key;
		}
		else {
			$key = $pre . $this->_session_pre . $key;
		}
		Session::instance()->delete($key);
	}
	
	public function __construct(Request $request, Response $response)
	{	
		ob_end_flush();
		parent::__construct($request, $response);
		
		View::bind_global('_lang_cd', $this->_lang_cd);

		$query_string = '/' . $request->uri() . '?';
		foreach($request->query() as $k=>$v) {
			$query_string = $query_string . $k . '=' . $v . '&';
		}
		
		$this->_user = Session::instance()->get('user', NULL);
		if (! $this->_user) {
			$this->_redirect('/login?redirect=' . urlencode($query_string));
		}
		
		$this->_bot = Session::instance()->get('bot', NULL);
		if ($this->_bot == NULL) {
			$this->_redirect('/admin/top');
		}
		
		$post = $request->post();
		if ($post == null) {
			$post = $this->request->query();
			if (array_key_exists('token', $post)) {
				$this->_session_pre = $post['token'];
			}
			else {
				$this->_session_pre = time();
			}
		}
		else {
			if (array_key_exists('token', $post)) {
				$this->_session_pre = $post['token'];
			}
			else {
				$this->_session_pre = '';
			}
		}
		
		$this->_bot_id = $this->_get_session('chat_bot_id', NULL);
		if ($this->_bot_id == NULL) $this->_bot_id = $this->_bot->bot_id;
		
		$this->_user_id = $this->_get_session('chat_user_id', NULL);
		if ($this->_user_id == NULL) $this->_user_id = $this->_user->user_id;
		
		$this->_model = new Model_Chatmodel();
		$this->_model->init($this->_bot_id);
		$this->_model->_session_pre = $this->_session_pre;
		$this->_sns_model = new Model_Snsmodel();
		$bot = ORM::factory('bot', $this->_bot_id);
		$this->_sns_model->init_bot($bot);

		$this->_lang_cd = Session::instance()->get('lang_cd_admin', NULL);
		if (!array_key_exists($this->_lang_cd, $this->_model->get_setting('admin_support_lang'))) {
			$this->_lang_cd = 'ja';
		}
		I18n::$lang = $this->_lang_cd;
	}
	
	function shutdown_function()
	{
		$e = error_get_last();
		print_r($e);
		//$this->_redirect('/chat?token=' . $this->_session_pre . '&showsetting=1');
	}
	
	public function action_index()
	{
		$errors = NULL;
		$message = NULL;
		
		//register_shutdown_function('shutdown_function');  
		
		$post = $this->request->query();
		// service_typeとservice_idがあったら、t_bot_member_mailからのemailをとる
		$service_type = isset($_GET['service_type']) ? $_GET['service_type'] : NULL;
		$service_id = isset($_GET['service_id']) ? $_GET['service_id'] : NULL;
		
		$query_string = '';
		foreach($this->request->query() as $k=>$v) {
			$query_string = $query_string . $k . '=' . $v . '&';
		}
		if ($query_string == '') {
			$query_string = '/' . $this->request->uri();
		}
		else {
			$query_string = '/' . $this->request->uri() . '?' . $query_string;
		}
		
		if (key_exists('facility_cd', $post)) {
			if ($post['facility_cd'] != $this->_bot->facility_cd) {
				// 
				$bot_result = $this->_model->get_bot_by_scene($post['facility_cd']);
				if ($bot_result == null) {
					session_destroy();
					$this->_redirect('/login');
					return;
				}
				$param_bot_id = $bot_result['bot_id'];
				$match_users = ORM::factory('user')->where('bot_id', '=', $param_bot_id)->where('email', '=', $this->_user->email)->where('delete_flg', '=', 0)->find_all();
				if (count($match_users) == 0) {
					$param_grp_bot_id = $this->_model->get_grp_bot_id($param_bot_id);
					if ($param_grp_bot_id > 0) {
						$match_users = ORM::factory('user')->where('bot_id', '=', $param_grp_bot_id)->where('email', '=', $this->_user->email)->where('delete_flg', '=', 0)->find_all();
						if (count($match_users) == 0) {
							session_destroy();
							$this->_redirect('/login?redirect=' . urlencode($query_string));
						}
					}
					else {
						session_destroy();
						$this->_redirect('/login?redirect=' . urlencode($query_string));
					}
				}
				$post['bot_id'] = $param_bot_id;
			}
		}
		
		$this->_delete_session('chat_bot_id');
		if (key_exists('bot_id', $post)) {
			$this->_bot_id = $post['bot_id'];
			$this->_set_session('chat_bot_id', $this->_bot_id);
		}
		else {
			$this->_set_session('chat_bot_id', $this->_bot_id);
		}
		
		$this->_write_access_log();

		$sort_log_time = $this->_get_session('setting_sort_log_time');
		if ($sort_log_time === NULL) {
			$this->_set_session('setting_sort_log_time', 1);
		}

		// get user info again
		$user = ORM::factory('user', $this->_user->user_id);
		Session::instance()->set("user", $user);
		$this->_user = $user;
		
		$user_chat_settings = json_decode($user->chat_setting, true);
		if (is_array($user_chat_settings) && array_key_exists(strval($this->_bot_id), $user_chat_settings)) {
			$chat_setting = $user_chat_settings[strval($this->_bot_id)];
		}
		else {
			$chat_setting = ['chat_lang'=>$user->chat_lang, 'chat_sns'=>$user->chat_sns, 'chat_day'=>$user->chat_day, 'chat_req_flg'=>$user->chat_req_flg, 'chat_scene_cd'=>''];
		}
		// user setting days
		$log_time = date('Y-m-d H:i:s', strtotime('-' . $chat_setting['chat_day'] . 'day'));
		$this->_set_session('max_log_time', $log_time);
		$this->_set_session('last_log_time', $log_time);

		// user setting lang
		$langs = explode(',', $chat_setting['chat_lang']);
		$user_chat_langs = array();
		$chatlist = array();
		foreach($langs as $lang) {
			$chatlist[$lang] = array();
			$user_chat_langs[] = $lang;
		}
		$user_lang = "(";
		foreach($langs as $lang){
			$user_lang = $user_lang . "'" . $lang . "'" . ",";
		}
		$user_lang = substr($user_lang, 0, strlen($user_lang)-1) . ")";
		$this->_set_session('chat_lang', $user_lang);
		// user setting sns
		$snses = explode(",", $chat_setting['chat_sns'] );
		$user_sns = "(";
		foreach($snses as $sns){
			$user_sns = $user_sns . "'" . $sns . "'" . ",";
		}
		$user_sns = substr($user_sns, 0, strlen($user_sns)-1) . ")";
		$this->_set_session('chat_sns', $user_sns);		
		// user setting tags
		$select_tags = $this->_get_session('setting_tags', NULL);
		// user setting user list
		$users = ORM::factory("user")->where("bot_id", "=", $this->_bot_id)->order_by('chat_status_cd')->find_all();
		$select_users = $this->_get_session('setting_users', NULL);
		// user setting deal only
		$chat_only = $this->_get_session('setting_chat_only', NULL);
		if ($chat_only == NULL) {
			$this->_set_session('setting_chat_only', $chat_only);
		}
		
		$this->_set_session('setting_chat_req_flg', $chat_setting['chat_req_flg']);
		if (array_key_exists('chat_scene_cd', $chat_setting)) $this->_set_session('setting_filter_scene', $chat_setting['chat_scene_cd']);
		
		// user setting filter member
		$filter_username = $this->_get_session('setting_filter_username', NULL);
		if ($filter_username == NULL) {
			$filter_username = "";
			$this->_set_session('setting_filter_username', $filter_username);
		}
		
		// 陳 2021/11/16 定義されている言語のMSGのみ表示させるように修正
		// $msg_list = ORM::factory('botmsg')->where('bot_id', '=', $this->_bot_id)->where('msg_class_cd', '=', '41')->find_all();
		// $msg_list = $this->_model->get_message_fix_phrase($this->_bot_id);
		// $msg_keys = [];
		// foreach($msg_list as $m) {
		// 	$msg_keys[] = $m['msg_cd'];
		// }
		// $grp_bot_id = $this->_model->get_grp_bot_id($this->_bot_id);
		// if ($grp_bot_id > 0) {
		// 	$msg_grp_list = $this->_model->get_message_fix_phrase($grp_bot_id);
		// 	foreach($msg_grp_list as $m) {
		// 		if (!in_array($m['msg_cd'], $msg_keys)) {
		// 			$msg_list[] = $m;
		// 		}
		// 	}
		// }
		$msg_list = $this->_model->get_message_fix_phrase_new($this->_bot_id);
		// message menu 
		$msg_array = array();
		/*　非表示のため
		$msg_list = $this->_model->get_message_menu($this->_bot_id, "ja");
		foreach($msg_list as $msg) {
			if (array_key_exists($msg["msg_class_cd"], $msg_array)) {
				$msg_array[$msg["msg_class_cd"]][$msg["msg_cd"]] = $msg;
			}
			else {
				$msg_array[$msg["msg_class_cd"]] = array();
				$msg_array[$msg["msg_class_cd"]][$msg["msg_cd"]] = $msg;
			}
		}
		*/
		
		// 関連BOT
		$rel_bot = $this->_model->get_bot_setting($this->_bot_id, 'A01_relation_bots');
		if ($rel_bot == '') {
			$chat_bots = array($this->_bot_id);
			$orm = ORM::factory('bot', $this->_bot_id);
			$user_bots = array($orm);
			$bot_grp_id = $this->_model->get_grp_bot_id($this->_bot_id); 
			
			if ($bot_grp_id >= 0) {
				if ($bot_grp_id == 0) $bot_grp_id = $this->_bot_id;
				$grp_bot = ORM::factory('bot', $bot_grp_id);
				$talkappi_image = $this->_get_bot_image($grp_bot->facility_cd);
				$grp_bots = ORM::factory('bot')->where('bot_id', '>=',  $bot_grp_id)->where('bot_id', '<',  $bot_grp_id+1000)->find_all();
				$chat_bot_dict = array();
				foreach($grp_bots as $orm) {
					$chat_bot_dict[strval($orm->bot_id)] = ['bot_name'=>$orm->bot_name, 'facility_cd'=>$grp_bot->facility_cd, 'talkappi_image'=>$talkappi_image];
				}
			}
			else {
				$talkappi_image = $this->_get_bot_image($orm->facility_cd);
				$chat_bot_dict = array();
				$chat_bot_dict[strval($this->_bot_id)] = ['bot_name'=>$orm->bot_name, 'facility_cd'=>$orm->facility_cd, 'talkappi_image'=>$talkappi_image];
			}
			$this->_set_session('chat_bot_dict', $chat_bot_dict);
		}
		else {
			$rel_bot = $rel_bot . "," . $this->_bot_id;
			$rel_bots = ORM::factory('bot')->where('bot_id', 'IN',  explode(',', $rel_bot))->find_all();
			$chat_bot_dict = array();
			foreach($rel_bots as $orm) {
				$talkappi_image = $this->_get_bot_image($orm->facility_cd);
				$chat_bot_dict[strval($orm->bot_id)] = ['bot_name'=>$orm->bot_name, 'facility_cd'=>$orm->facility_cd, 'talkappi_image'=>$talkappi_image];
			}
			$this->_set_session('chat_bot_dict', $chat_bot_dict);
			// rel bots
			if($this->_user->role_cd == '99' || $this->_user->role_cd == '80') {
				$user_bots = $rel_bots;
			}
			else {
				$user_bots = array();
				$admin_model = new Model_Adminmodel();
				$regist_bots = $admin_model->get_user_bots($this->_user->email);
				foreach($regist_bots as $orm) {
					foreach($rel_bots as $r) {
						if ($orm->bot_id == $r->bot_id) {
							$user_bots[] = $orm;
							break;
						}
					}
				}
			}
			if($this->_user->role_cd == '99' || $this->_user->role_cd == '80') {
				$chat_bots = explode(',', $rel_bot);
			}
			else {
				// chat_bot setting
				if ($this->_user->chat_bot == '') {
					$chat_bots = array($this->_bot_id);
				}
				else {
					$chat_bots = array();
					$user_chat_bots = explode(',', $this->_user->chat_bot);
					foreach($user_bots as $orm) {
						if (in_array($orm->bot_id, $user_chat_bots)) {
							$chat_bots[] = $orm->bot_id;
						}
					}
				}
			}
		}
		if($this->_user->role_cd != '99' && $this->_user->role_cd != '80') {
			// user chat bot update
			$user = ORM::factory('user', $this->_user->user_id);
			$user->chat_bot = implode(',', $chat_bots);
			$user->save();
			$user = ORM::factory('user', $this->_user->user_id);
			Session::instance()->set("user", $user);
			$this->_user = $user;
		}
		
		$this->_set_session('chat_list', []);
		
		$member_id = $this->request->query('id', NULL);

		$showsetting = $this->request->query('showsetting', NULL);
		if ($showsetting == 1) {
			$chatlist_view = NULL;
			$chatwindow_view = NULL;
		}
		else {
			if ($member_id == NULL) {
				$this->_set_session('chat_one', NULL);
				$chatlist_view = $this->_chat_list($this->_bot->lang_cd);
				if ($chatlist_view == null) {
					$this->_redirect('/chat?token=' . $this->_session_pre . '&showsetting=1');
					return;
				}
				if ($chatlist_view->chat_member != NULL) {
					$grp_bot_id = $this->_model->get_grp_bot_id($this->_bot_id);
					if ($grp_bot_id > 0) {
						$member = ORM::factory("botmember")->where("bot_id", ">=", $grp_bot_id)->where("bot_id", "<", $grp_bot_id + 1000)->where("member_id", "=", $chatlist_view->chat_member)->find();
					}
					else if ($grp_bot_id == 0) {
						$member = ORM::factory("botmember")->where("bot_id", ">=", $this->_bot_id)->where("bot_id", "<", $this->_bot_id + 1000)->where("member_id", "=", $chatlist_view->chat_member)->find();
					}
					else {
						$member = ORM::factory("botmember")->where("bot_id", "=", $this->_bot_id)->where("member_id", "=", $chatlist_view->chat_member)->find();
					}
					$chatwindow_view = $this->_chat($chatlist_view->chat_member, $chatlist_view->expand_lang, $log_time, null, 0, $service_type, $service_id);
				}
				else {
					$chatwindow_view = NULL;
					$member = NULL;
				}
			}
			else {
				$this->_set_session('chat_member', $member_id);
				$this->_set_session('chat_one', $member_id);
				$chatlist_view = $this->_chat_one();
				if ($chatlist_view != NULL) {
					$grp_bot_id = $this->_model->get_grp_bot_id($this->_bot_id);
					if ($grp_bot_id > 0) {
						$member = ORM::factory("botmember")->where("bot_id", ">=", $grp_bot_id)->where("bot_id", "<", $grp_bot_id + 1000)->where("member_id", "=", $member_id)->find();
					}
					else if ($grp_bot_id == 0) {
						$member = ORM::factory("botmember")->where("bot_id", ">=", $this->_bot_id)->where("bot_id", "<", $this->_bot_id + 1000)->where("member_id", "=", $member_id)->find();
					}
					else {
						$member = ORM::factory("botmember")->where("bot_id", "=", $this->_bot_id)->where("member_id", "=", $member_id)->find();
					}
					$chatwindow_view = $this->_chat($chatlist_view->chat_member, $chatlist_view->expand_lang, $log_time, null, 0, $service_type, $service_id);
				}
			}
		}
		
		$view = View::factory ('admin/chat');
		if ($chatlist_view === null) {
			//$view->token = '';
			$view->token = $this->_session_pre;
			$view->showsetting = 1;
			$view->log_time = '';
		}
		else {
			$view->token = $this->_session_pre;
			$view->showsetting = 0;
			$view->log_time = $log_time;
		}
		$this->_set_session('chat_token', $view->token);
		$view->bot = $this->_bot;
		$view->codes = $this->_model->get_config('code', $this->_lang_cd);
		$temp_lang = [];
		foreach($user_bots as $it) {
			$temp_lang = array_merge($temp_lang, $this->_model->get_bot_lang_cd_admin($it));
		}
		$view->bot_lang = $temp_lang;
		$view->user_langs = explode(',',$this->_user->chat_available_lang);
		$view->user_chat_langs = $user_chat_langs;
		$view->user_snses = explode(',', $chat_setting['chat_sns']);
		$view->user_days = $chat_setting['chat_day'];
		$view->enter_mode = $this->_user->chat_enter_mode;
		$view->translate = $this->_get_session('translate', '0');
		$view->user = $this->_user;
		$view->logo = $talkappi_image;
		$view->users = $users;
		$view->select_tags = $select_tags;
		$view->select_users = $select_users;
		$view->chat_only = $chat_only;
		$view->filter_username = $filter_username;
		$view->filter_member_no = $this->_get_session('setting_filter_member_no', '');
		$view->filter_scene = $this->_get_session('setting_filter_scene', '');
		$view->user_bots = $user_bots;
		$view->chat_bots = $chat_bots;
		$view->msg_array = $msg_array;
		// todo よく使う回答
		$view->config_phrase = [];
		$chat_tags = json_decode($this->_model->get_bot_tpl_message($this->_bot_id, 'chat_tags', $this->_lang_cd), true);
		$chat_tags_select = [];
		foreach($chat_tags as $k=>$v) {
			$chat_tags_select[$k] = $k;
		}
		$view->tags = array(""=>"ー") + $chat_tags_select;
		$view->tag_color = $chat_tags;
		$view->flg_operator_mode_control = $this->_model->get_bot_setting($this->_bot_id, 'flg_operator_mode_control');
		$view->chatlist_view = $chatlist_view;
		$view->chatwindow_view = $chatwindow_view;
		$view->member = $member;
		$bot = ORM::factory('bot', $this->_bot_id);
		$view->bot_name = $bot->bot_name;
		$view->admin_bot_id = $this->_bot->bot_id;
		$view->chat_req_flg_array = $this->_model->get_code('26', $this->_lang_cd);
		$view->chat_req_flg = $chat_setting['chat_req_flg'];
		$view->msg_list = $msg_list;
		$view->_assets = $this->_model->get_env('assets_url');
		$kintone = $this->_model->get_bot_setting($this->_bot_id, 'json_kintone');
		if ($kintone == '') {
			$view->support = false;
		}
		else {
			$view->support = true;
		}
		$view->modify_mode = $this->_get_session('setting_modify_mode') ? $this->_get_session('setting_modify_mode'): 0;
		$view->mask_privacy = $this->_get_session('setting_mask_privacy') ? $this->_get_session('setting_mask_privacy') : 0;
		$view->sort_log_time = $this->_get_session('setting_sort_log_time');
		$view->chat_one = $this->_get_session('chat_one', NULL);
		$scene_name_pattern = $this->_model->get_bot_tpl_message($this->_bot_id, 'scene_name_pattern', $this->_lang_cd);
		if ($scene_name_pattern == '') {
			$view->scene_pattern = [''=>'指定なし'];
		}
		else {
			$view->scene_pattern = [''=>'指定なし'] + json_decode($scene_name_pattern, true);
		}
		$this->response->body($view);
	}
	
	private function _get_bot_image($scene_cd, $filename = 'logo')
	{
		$ext_array = $this->_model->get_setting('support_image_type');
		$ref_scene_cd = $this->_model->get_scene_ref($scene_cd, 'webchat');
		if ($ref_scene_cd == 'default') {
			return '/assets/apps/webchat/logo.png';
		}
		else {
			$asset_path = $this->_model->get_scene_path($ref_scene_cd, 'webchat');
			foreach($ext_array as $ext) {
				if (file_exists($asset_path . $filename. '.' . $ext) == true) {
					$asset_url_path = $this->_model->get_scene_url_path($ref_scene_cd, 'webchat');
					return $asset_url_path . $filename . "." . $ext;
				}
			}
		}
		return '';
	}
	
	public function action_chatlist()
	{
		$expand_lang = '';
		$post = $this->request->post();
		if ($post) {
			if (key_exists('expand_lang', $post)) {
				$expand_lang= $post['expand_lang'];
			}
			if (key_exists('token', $post)) {
				$token = $post['token'];
				if ($token != $this->_get_session('chat_token')) {
					$this->response->body("token_err");
					return;
				}
			}

			/*
			if (key_exists('admin_bot_id', $post)) {
				$admin_bot_id = $post['admin_bot_id'];
				if ($admin_bot_id != $this->_bot->bot_id) {
					$this->response->body("bot_switch_err");
					return;
				}
			}
			*/
		}
		$chat_one = $this->_get_session('chat_one');
		if ($chat_one == NULL) {
			$chatlist_view = $this->_chat_list($expand_lang);
		}
		else {
			$chatlist_view = $this->_chat_one();
		}
		DB::update('t_user')->set(array('chat_last_time'=>date('Y-m-d H:i:s')))->where('user_id', '=', $this->_user_id)->execute();
		$this->response->body($chatlist_view);
	}
	
	private function _chat_one($expand_lang = '') 
	{
		$chat_member = $this->_get_session('chat_member', NULL);
		$chat_bot = $this->_get_chat_bot(true);
		$member = $this->_model->get_member_latest_info($chat_bot, $chat_member);
		
		if ($member == NULL) return NULL;
		
		$member_name = '';
		if ($member['name'] != NULL) {
			$member_name = $member['name'];
		}
		else if ($member['first_name'] != NULL && $member['first_name'] != '') {
			$member_name =  $member['last_name'] . ' ' . $member['first_name'];
		}
		
		$chatlist = array();
		$chat['m-' . $chat_member] = array('chat_mode'=>$member['chat_mode'], 'name'=>$member_name,'country_cd'=>$member['country_cd'],
				'lang_cd'=>$member['lang_cd'], 'sns_type_cd'=>$member['sns_type_cd'], 'is_tester'=>$member['is_tester'], 'user_id'=>$member['chat_user_id'],
				'chat_online_status'=>$member['chat_online_status'],'bot_id'=>$member['bot_id'],'mobile'=>$member['mobile'], 'member_tag'=>$member['tags'],
				'unanswer'=>0, 'new'=>0, 'help'=>0, 'wait_time'=>'', 'log_time'=>'', 'user_name'=>''
				
		);
		/*
		$chat['m-' . $chat_member] = array('chat_mode'=>$member->chat_mode, 'name'=>$member_name,'country_cd'=>$member->country_cd,
				'lang_cd'=>$member->lang_cd, 'sns_type_cd'=>$member->sns_type_cd, 'is_tester'=>$member->is_tester, 'user_id'=>$member->chat_user_id, 'user_name'=>'',
				'unanswer'=>0, 'new'=>0, 'help'=>0, 'wait_time'=>'', 'log_time'=>'', 'member_tag'=>$member->tags,
				'chat_online_status'=>$member->chat_online_status,'bot_id'=>$this->_bot_id,'mobile'=>0
		);
*/
		$chatlist[$member['lang_cd']] = $chat;
		$expand_lang = $member['lang_cd'];
		
		$chatlist_view = View::factory('admin/chatlist');
		$chatlist_view->new_unanswer = 0;
		$chatlist_view->chatlist = $chatlist;
		$chatlist_view->expand_lang = $expand_lang;
		
		$chatlist_view->codes = $this->_model->get_config('code', $this->_lang_cd);
		$chat_tags = json_decode($this->_model->get_bot_tpl_message($this->_bot_id, 'chat_tags', $this->_lang_cd), true);
		$chatlist_view->tag_color = $chat_tags;
		$chatlist_view->country = $this->_model->get_config('country', $this->_lang_cd);
		$chatlist_view->chat_member = $chat_member;
		$chatlist_view->new_tags = '';
		return $chatlist_view;
	}
	
	private function _chat_list($expand_lang) 
	{
		$log_time = $this->_get_session('last_log_time', NULL);
		$chat_member = $this->_get_session('chat_member', NULL);
		$chatlist = $this->_get_session('chat_list');
		
		if ($chat_member == NULL) $chat_member = '';
		$filter_scene = $this->_get_session('setting_filter_scene');
		$user_lang = $this->_get_session('chat_lang');
		$user_sns = $this->_get_session('chat_sns');
		
		$new_unanswer = 0;
		$new_lang = [];
		$new_tag_members = [];
		//$member_time = $this->_get_session('last_member_time');
		$chat_bot = $this->_get_chat_bot(true);
		$chatlist = $this->_model->get_chatlist($chat_bot, $log_time, $user_lang, $user_sns, $new_unanswer, $chatlist, $new_lang, $new_tag_members, $filter_scene);
		if ($chatlist === null) return null;
		
		$exist_member = false;
		if ($chat_member != '') {
			foreach($chatlist as $c) {
				if (array_key_exists('m-' . $chat_member, $c)) {
					$exist_member = true;
					break;
				}
			}
		}
		if ($exist_member == false) $chat_member = '';
		
		//$this->_set_session('last_member_time', $member_time);
		
		
		foreach($new_lang as $lang) {
			if (strpos($expand_lang, $lang) === false) {
				$expand_lang = $expand_lang . $lang . ',';
			}
		}
		
		if ($chatlist === null) return null;
		
		$this->_set_session('chat_list', $chatlist);
		$this->_set_session('last_log_time', $log_time);
		
		$chatlist_view = View::factory('admin/chatlist');
		$chatlist_view->new_unanswer = $new_unanswer;
		$chatlist_view->chatlist = $chatlist;
		$chatlist_view->expand_lang = $expand_lang;

		$chatlist_view->codes = $this->_model->get_config('code', $this->_lang_cd);
		$chat_tags = json_decode($this->_model->get_bot_tpl_message($this->_bot_id, 'chat_tags', $this->_lang_cd), true);
		$chatlist_view->tag_color = $chat_tags;
		$chatlist_view->country = $this->_model->get_config('country', $this->_lang_cd);
		$chatlist_view->chat_member = $chat_member;
		$chatlist_view->new_tags = json_encode($new_tag_members);
		return $chatlist_view;
	}
	
	public function action_chat()
	{
		$member_id = NULL;
		$lang_cd = NULL;
		$chat_user = NULL;
		$log_time = NULL;
		$start_time = NULL;
		$service_type = NULL;
		$service_id = NULL;
		$auto_first = 0;
		
		$post = $this->request->post();
		if ($post) {
			if (key_exists('bot_id', $post)) {
				$this->_bot_id = $post['bot_id'];
				$this->_set_session('chat_bot_id', $this->_bot_id);
				// user_id
				$orm = ORM::factory('user')->where('bot_id', '=', $this->_bot_id)->where('email', '=', $this->_user->email)->find();
				$this->_user_id = $orm->user_id;
				$this->_set_session('chat_user_id', $this->_user_id);
			}
			if (key_exists('member_id', $post)) {
				$member_id = $post['member_id'];
			}
			if (key_exists('service_type', $post)) {
				$service_type = $post['service_type'];
			}
			if (key_exists('service_id', $post)) {
				$service_id = $post['service_id'];
			}
			if (key_exists('lang_cd', $post)) {
				$lang_cd = $post['lang_cd'];
			}
			if (key_exists('log_time', $post)) {
				$log_time = $post['log_time'];
			}
			if (key_exists('start_time', $post)) {
				$start_time = $post['start_time'];
			}
			if (key_exists('auto_first', $post)) {
				$auto_first = $post['auto_first'];
			}
			if (key_exists('token', $post)) {
				$token = $post['token'];
				if ($token != $this->_get_session('chat_token')) {
					$this->response->body("token_err");
					return;
				}
			}
		}
		if ($member_id == NULL) return;		
		
		$chatwindow_view = $this->_chat($member_id, $lang_cd, $log_time, $start_time, $auto_first, $service_type, $service_id);
		$this->response->body($chatwindow_view);
	}
	
	private function _chat($member_id, $lang_cd, $log_time, $start_time, $auto_first, $service_type, $service_id)
	{	
		$refresh = false;
		$chat_user = NULL;
		$this->_set_session('chat_member', $member_id);
		$this->_set_session('chat_member_lang_cd', $lang_cd);		

		if ($start_time != null) {
			// more log
			$refresh = false;
		}
		else {
			if ($log_time === null) {
				// refresh
				$log_time = $this->_get_session('last_member_log_time', NULL);
				$chat_user = $this->_get_session('chat_user', NULL);
				$refresh = true;
			}
			else {
				// select member 
				if ($log_time === '') {
					$log_time = $this->_get_session('max_log_time', NULL);
					if ($log_time == NULL) $log_time = '';
					$this->_set_session('last_member_log_time', $log_time);
				}
			}
		}
		
		$chat_mode = '';
		$bot_id = $this->_bot_id;
		
		if ($start_time == null || $auto_first == 1) {
			$chat_bot = $this->_get_chat_bot(true);
		}
		else {
			$chat_bot = $this->_get_chat_bot(false);
		}

		$chat = $this->_model->get_memberchats_diff($chat_bot, $member_id, $log_time, $start_time, $this->_lang_cd, $service_type, $service_id);
		if (count($chat) > 0) {
			if ($start_time == null || $auto_first == 1) {
				$this->_set_session('last_member_log_time', $chat[count($chat) - 1]['log_time']);
				$lang_cd = $chat[count($chat) - 1]['lang_cd'];
				$chat_mode = $chat[count($chat) - 1]['chat_mode'];
				// current chat bot set
				$bot_id = $chat[count($chat) - 1]['bot_id'];
				$this->_set_session('chat_bot_id', $bot_id);
			}
		}
		
		if ($refresh == false) {
			if ($start_time !== NULL && count($chat) < 50) {
				$start_time = NULL;
			}
			else {
				if (count($chat) == 0) {
					$start_time = date("Y-m-d");
				}
				else {
					$start_time = $chat[0]['log_time'];
				}
			}
		}
		/*
		// set user
		foreach($chat as $c) {
			if ($c['bot_msg'] != '') {
				if ($c['intent_cd'] == 'change_chatmode.1' ||
						$c['intent_cd'] == 'change_chatmode.2' ||
						strpos($c['intent_cd'], "input.chat")===0 ||
						$c['intent_cd'] == 'change_chatuser' ||
						$c['intent_cd'] == 'update_membertag') {
							$chat_user = $c['user_id'];
						}
						else {
							$chat_user = "";
						}
			}
		}
		*/
		$this->_set_session('chat_user', $chat_user);
		
		if ($chat_user == NULL) $chat_user = '';
		if ($chat_user == $this->_user_id) $chat_user = 'me';

		$mail_logs = $this->_model->get_member_mail_log('06', $member_id);
		
		$chatwindow_view = View::factory('admin/chatwindow');
		$chatwindow_view->settings = $this->_model->get_system_config('settings');
		$chatwindow_view->modify_mode = $this->_get_session('setting_modify_mode') ? $this->_get_session('setting_modify_mode'): 0;
		$chatwindow_view->mask_privacy = $this->_get_session('setting_mask_privacy') ? $this->_get_session('setting_mask_privacy') : 0;
		$chatwindow_view->sort_log_time = $this->_get_session('setting_sort_log_time');
		$chatwindow_view->start_time = $start_time;
		$chatwindow_view->member_id = $member_id;
		$chatwindow_view->member_lang_cd = $lang_cd;
		$chatwindow_view->chat_mode = $chat_mode;
		$chatwindow_view->username = $this->_user->name;
		$member = ORM::factory("botmember")->where("bot_id", "=", $bot_id)->where("member_id", "=", $member_id)->find();
		$chatwindow_view->member_tags = $member->tags;
		$chatwindow_view->member_remark = $member->remark;
		$chatwindow_view->mobile = $member->mobile;
		$chatwindow_view->member_name = $this->_model->get_member_name($member);
		if ($member->sns_type_cd == 'fb') {
			$chatwindow_view->eu_restict = $this->_model->get_bot_setting($this->_bot_id, 'flg_facebook_eu_restict');
		}
		$chatwindow_view->mail_logs = $mail_logs;
		$countries = $this->_model->get_config('country', $this->_lang_cd);
		$timezone = '';
		if ($member->timezone!='' && $member->timezone != NULL) {
			if (is_numeric($member->timezone)) {
				$diff = 9 - $member->timezone;
				$now = time();
				if ($diff >= 0) {
					$timezone= date('Y-m-d H:i', strtotime("-" . $diff . " hours", $now));
				}
				else {
					$timezone= date('Y-m-d H:i', strtotime("+" . abs($diff) . " hours", $now));
				}
			}
			else {
				date_default_timezone_set($member->timezone);
				$timezone = date('Y-m-d H:i');
			}
		}
		$country = '';
		if (array_key_exists($member->country_cd, $countries)) {
			$country= $countries[$member->country_cd];
		}
		$member_name = $this->_model->get_member_name($member);
		if ($member_name == '' && $member->sns_type_cd == 'wb') $member_name = __('admin.chatlist.web_user');
		if ($member->is_tester == 1) $member_name = "※tester";
		$chatwindow_view->member_number =  'No:' .$member->member_no;
		$chatwindow_view->member_label = $member_name;
		$chatwindow_view->member_timezone = '';
		if ($timezone != '') {
			if ($country != '') {
				$chatwindow_view->member_timezone =  "(" . $country . " " . __('admin.chat.window.local_time') . " " . $timezone . ")";
			} else {
				$chatwindow_view->member_timezone = "(" . __('admin.chat.window.local_time') . " " . $timezone . ")";
			}
		}
		$chatwindow_view->chat_bot_dict = $this->_get_session('chat_bot_dict');
		if ($member->chat_user_id == $this->_user_id) {
			$chatwindow_view->chat_user = "me";
		}
		else {
			$chatwindow_view->chat_user = $member->chat_user_id;
		}
		//$chatwindow_view->member_last_access_time = $member->last_access_time;
		$chatwindow_view->member_last_access_time = $this->_model->get_member_last_access_time($chat_bot, $member_id);
		if ($member_id != NULL) {
			$chatwindow_view->chats = $chat;
		}
		else {
			$chatwindow_view->chats = NULL;
		}
		return $chatwindow_view;
	}
	
	public function action_clear()
	{
		$post = $this->request->post();
		$log_id = $post['log_id'];
		
		$member_id = $this->_get_session('chat_member', NULL);
		if ($member_id == NULL) return;

		$chatlist = $this->_get_session('chat_list');
		if ($log_id !='') {
			$upd_count = $this->_model->update_log($log_id);
			if ($upd_count > 0) {
				$msg = $this->_model->get_bot_txt_message($this->_bot_id, 'clear_memberlog.1', $this->_lang_cd);
				$this->_sns_model->writelog($member_id, $msg, $msg, 'clear_memberlog.1');
			}		
		}
		else {
			$this->_model->update_member_log($this->_bot_id, $member_id);
			$member = ORM::factory('botmember')->where('bot_id', '=', $this->_bot_id)->where('member_id', '=', $member_id)->find();
			$chat_mode_bef = $member->chat_mode;
			$msg = $this->_model->get_bot_txt_message($this->_bot_id, 'clear_memberlog.all', $this->_lang_cd);
			$this->_sns_model->writelog($member_id, $msg, $msg, 'clear_memberlog.all');
			if ($chat_mode_bef != 0) {
				$member = $this->_model->update_member_chat_mode($this->_bot_id, $member_id, $this->_user_id, 0);
				$msg = $this->_model->get_bot_txt_message($this->_bot_id, 'change_chatmode.0', $this->_lang_cd);
				$this->_sns_model->writelog($member_id, $msg, $msg, 'change_chatmode.0');
			}
			if ($chat_mode_bef != 0) {
				$this->_chat_end($member_id);
			}
		}
		$this->_model->update_member_time($this->_bot_id, $member_id);
	}
	
	public function action_delete()
	{
		$post = $this->request->post();
		$log_id = $post['log_id'];
		
		$member_id = $this->_get_session('chat_member', NULL);
		if ($member_id == NULL) return;
		
		$chatlist = $this->_get_session('chat_list');
		if ($log_id !='') {
			DB::delete('t_bot_log_chat')->where('log_id', '=', $log_id)->execute();
			$tbl_ext = $this->_model->get_log_table_fix($this->_bot_id);
			DB::delete('t_bot_log' . $tbl_ext)->where('log_id', '=', $log_id)->execute();
		}
	}
	
	private function _chat_start($member_id) 
	{
		$bot = ORM::factory('bot', $this->_bot_id);
		$skills = $this->_model->get_bot_intent_skills($this->_bot_id, 'system.chat_start');
		if ($skills === NULL) $skills = $this->_model->get_bot_intent_skills(0, 'system.chat_start');
		if ($skills === NULL) return;
		foreach($skills as $skill) {
			$skill = json_encode([$skill]);
			$param = ["facility_cd"=>$bot->facility_cd, "id"=>$member_id, "command"=>$skill];
			$data = $this->_model->post_enginehook('engine', 'execute','', $param);
		}
	}
	
	private function _chat_end($member_id) 
	{
		$bot = ORM::factory('bot', $this->_bot_id);
		$skills = $this->_model->get_bot_intent_skills($this->_bot_id, 'system.chat_end');
		if ($skills === NULL) $skills = $this->_model->get_bot_intent_skills(0, 'system.chat_end');
		if ($skills === NULL) return;
		foreach($skills as $skill) {
			if ($skill['skill'] == 'AUTOANSWER') {
				$flg_accept_request = $this->_model->get_bot_setting($this->_bot_id, 'flg_accept_request');
				$skill = 'AUTOANSWER -- {"flow_id":"' . $skill['params']['flow_id'] . '"}';
				if ($flg_accept_request == '1') {
					$param = ["facility_cd"=>$bot->facility_cd, "id"=>$member_id, "command"=>$skill];
					$data = $this->_model->post_enginehook('engine', 'execute','', $param);
				}
			}
			else {
				/*
				$skill = $this->_model->format_skill_array([$skill]);
				$param = ["facility_cd"=>$this->_bot->facility_cd, "id"=>$member_id, "command"=>$skill[0]];
				*/
				$skill = json_encode([$skill]);
				$param = ["facility_cd"=>$bot->facility_cd, "id"=>$member_id, "command"=>$skill];
				$data = $this->_model->post_enginehook('engine', 'execute','', $param);
			}
		}
	}
	
	public function action_translate()
	{
		$post = $this->request->post();
		$log_id = $post['log_id'];
		$message_t = $this->_model->update_trans_log($log_id);
		ob_end_clean();
		$this->response->body($message_t);
	}
	
	public function action_send()
	{
		$post = $this->request->post();
		$lang_cd = strtolower($post['lang_cd']);
		$recipient = $post['recipient'];
		$message = $post['message'];
		$is_translate = FALSE;
		if (array_key_exists('translate', $post)) {
			if ($post['translate'] == '1' && $lang_cd != 'ja') $is_translate = TRUE;
		}
		if (substr($message, 0, 1) == "#"){
			$this->_sns_model->send_message_cd($recipient, substr($message, 1));
		}
		else {
			$this->_sns_model->send_message($recipient, $message, $is_translate);
		}
		$this->response->body(json_encode('OK'));
	}
	
	public function action_skill()
	{
		$post = $this->request->post();
		$message = $post['msg_cd'];
		$member_id = $post['recipient'];
		$sns_type_cd = $post['sns_type_cd'];
		
		if ($message == 'UPLOAD_LOCATION') {
			$skill = 'UPLOAD_LOCATION';
			$param = ["facility_cd"=>$this->_bot->facility_cd, "id"=>$member_id, "command"=>$skill];
			$data = $this->_model->post_enginehook('engine', 'execute','', $param);
		}
		else if ($message == 'UPLOAD_IMAGE') {
			if ($sns_type_cd == 'wb') {
				$this->_sns_model->send_message_cd($member_id, 'req_upload_image_menu');
			}
			else {
				$this->_sns_model->send_message_cd($member_id, 'req_upload_image');
			}
		}
		
		$this->response->body(json_encode('OK'));
	}
	
	public function action_mail()
	{
		$post = $this->request->post();
		$member_id = $post['member_id'];
		$service_type = NULL;
		$service_id = NULL;

		if (key_exists('service_type', $post)) {
			$service_type = $post['service_type'];
		}
		if (key_exists('service_id', $post)) {
			$service_id = $post['service_id'];
		}

		$member = ORM::factory('botmember')->where('bot_id', '=', $this->_bot_id)->where('member_id', '=', $member_id)->find();
		$member_mail = ORM::factory('botmembermail')->where('member_id', '=', $member_id)->where('bot_id', '=', $this->_bot_id)->where('service_type', "=", $service_type)->where('service_id', "=", $service_id)->find();
		$logs = DB::select()->from('t_bot_log' . $this->_model->get_log_table($this->_bot_id))
		->where('bot_id', '=', $this->_bot_id)
		->where('member_id', '=', $member_id)
		->where('log_time', '>', $member->last_access_time)
		->order_by('log_time')
		->execute()->as_array();
		$message = '';
		if ($post["message"]) {
			$message = $post["message"];
		} else {
			foreach($logs as $log) {
				//if ($this->_model->is_ctl_intent($log->intent_cd)) continue;
				if ($log['intent_cd'] != 'input.chat') continue;
				if ($log['bot_msg_t'] == NULL) {
					$message = $message . $log['bot_msg'] . PHP_EOL;
				}
				else {
					$message = $message . $log['bot_msg_t'] . PHP_EOL;
					$message = $message . '※(' . $log['bot_msg'] . ')' . PHP_EOL;
				}
			}
		}
		$msg = $this->_model->get_bot_tpl_message($this->_bot_id, 'mail.chat_web_sendmail', $member->lang_cd);
		$bot_name = $this->_model->get_bot_txt_message($this->_bot_id, "bot_name", $member->lang_cd);
		$msg = str_replace("{bot_name}", $bot_name, $msg);
		$member_name = $this->_model->get_member_name($member);
		$msg = str_replace("{name}", $member_name, $msg);
		$msg = str_replace("{message}", $message, $msg);
		//$base_url = $this->_model->get_env('base_url') . "bot/webchat?id=" . $this->_bot->facility_cd . "&mid=" . $member->member_id;
		$base_url = $this->_model->get_env('base_url') . "bot?id=" . $this->_bot->facility_cd . "&mid=" . $member->member_id;
		$msg = str_replace("{webchat_url}", $base_url, $msg);

		$email_to_send = $member_mail->mail ? $member_mail->mail : $member->email;
		$param = ['bot_id'=>$this->_bot_id];
		$param['type'] = '06';
		$param['link_id'] = $post["log_id"];
		$param['member_id'] = $member_id;
		$param['receiver'] = $email_to_send;
		$param['title'] = $bot_name;
		$param['body'] = $msg;
		$data = $this->_model->post_enginehook('service', 'sendmail','', $param);
		if ($data == null || $data['success'] == 'False') {
			Log::instance()->add(Log::DEBUG, 'chat mail failure=' . json_encode($data));
			$msg = $this->_model->get_bot_txt_message($this->_bot_id, 'mail_chatuser_error', $this->_lang_cd);
			$this->_sns_model->writelog($member->member_id, $msg, $msg, 'mail_chatuser_error');
		}
		else {
			$msg = $this->_model->get_bot_txt_message($this->_bot_id, 'mail_chatuser', $this->_lang_cd);		
			$this->_sns_model->writelog($member->member_id, $msg, $msg, 'mail_chatuser');
		}

		// change mode 0
		// if ($member->chat_mode > 0) {
		// 	$this->_model->update_member_chat_mode($this->_bot_id, $member->member_id, $this->_user_id, 0);
			/*
			$msg = $this->_model->get_bot_txt_message($this->_bot_id, 'change_chatmode.0', 'ja');
			$this->_sns_model->writelog($member->member_id, $msg, $msg, 'change_chatmode.0');
			$flg_operator_disconnect_msg = $this->_model->get_bot_setting($this->_bot_id, 'flg_operator_disconnect_msg');
			if ($flg_operator_disconnect_msg == 1) {
				$this->_sns_model->send_message_cd($member->member_id, 'change_chatmode.0.user');
			}
			*/
			//$this->_chat_end($member_id);
		// }
		
		$this->response->body(json_encode('OK'));
	}

	public function action_chatmessage() {
		$post = $this->request->post();
		$member_id = $post['member_id'];
		$bot_id = $post['bot_id'];

		$latest_log_id = $this->_model->get_chatmessage($bot_id, $member_id, 'input.chat');

		$this->response->body(json_encode($latest_log_id[0]));
	}
	
	public function action_kintone()
	{
		$post = $this->request->post();
		$member_id = $post['member_id'];
		$member = ORM::factory('botmember')->where('bot_id', '=', $this->_bot_id)->where('member_id', '=', $member_id)->find();
		
		$logs = DB::select()->from('t_bot_log' . $this->_model->get_log_table($this->_bot_id))
		->where('bot_id', '=', $this->_bot_id)
		->where('member_id', '=', $member_id)
		->where('member_msg', '<>', '')
		->order_by('log_time', 'DESC')->limit(1)
		->execute()->as_array();

		$codes = $this->_model->get_config("code");
		$chat_url = $this->_model->get_env('admin_url') . 'chat?facility_cd=' . $this->_bot->facility_cd . '&id=' . $member_id;
		
		$msg = $this->_model->get_bot_mal_message($this->_bot_id, 'chat.support_notification', $this->_lang_cd);
		$subject = $msg['subject'];
		$subject = str_replace("{lang}", $codes['02'][$member->lang_cd], $subject);
		$subject = str_replace("{no}", $member->member_no, $subject);
		$subject = str_replace("{remark}", $post['remark'], $subject);
		
		$member_name = $this->_model->get_member_name($member);
		$message = $msg['body'];
		$message = str_replace("{bot_name}", $this->_bot->bot_name, $message);
		$message = str_replace("{send_time}", date('Y-m-d H:i:s'), $message);
		$message = str_replace("{sns_type}", $codes['08'][$member->sns_type_cd], $message);
		$message = str_replace("{member_name}", $member_name, $message);
		$message = str_replace("{lang}", $codes['02'][$member->lang_cd], $message);
		if ($logs[0]['member_msg_t'] == '') {
		$message = str_replace("{log}", $logs[0]['member_msg'], $message);
		}
		else {
			$message = str_replace("{log}", $logs[0]['member_msg_t'] . PHP_EOL . '※原文：' . $logs[0]['member_msg'], $message);
		}
		$message = str_replace("{chat_url}", $chat_url, $message);
		if ($member->lang_cd == 'cn') {
			$lang_name = '簡体字';
		}
		else if ($member->lang_cd == 'tw') {
			$lang_name = '繁体字';
		}
		else {
			$lang_name = $codes['02'][$member->lang_cd];
		}
		$record = 
			[
				'subject'=>['value'=>$subject],
				'text'=>['value'=>$message],
				'language'=>['value'=>$lang_name],
				'channel'=>['value'=>'チャット'],
			];
		$result = $this->_model->post_kintone($this->_bot_id, '04', $member_id, $record);
		$this->response->body(json_encode('OK'));
	}
	
	public function action_switch()
	{
		$post = $this->request->post();
		$send_notice = true;
		if (array_key_exists('input', $post)) {
			$send_notice = false;
		}
		$member_id = $post['member_id'];

		if (array_key_exists('chat_mode', $post)) {
			$member = $this->_model->update_member_chat_mode($this->_bot_id, $member_id, $this->_user_id,$post['chat_mode']);
		}
		else {
			$member = $this->_model->update_member_chat_mode($this->_bot_id, $member_id, $this->_user_id);
		}
		$msg = $this->_model->get_bot_txt_message($this->_bot_id, 'change_chatmode.' . $member['chat_mode'], $this->_lang_cd);
		$this->_sns_model->writelog($member_id, $msg, $msg, 'change_chatmode.' . $member['chat_mode']);
		if ($member['chat_mode'] == '0') {
			$this->_chat_end($member_id);
		}

		if (($member['chat_mode'] == '1' || $member['chat_mode'] == '2') && $send_notice) {
			$this->_chat_start($member_id);
			/*
			$flg_operator_connect_msg = $this->_model->get_bot_setting($this->_bot_id, 'flg_operator_connect_msg');
			if ($flg_operator_connect_msg == 1) $this->_sns_model->send_message_cd($member_id, 'operator_connect_notice');
			*/
		}

		$this->_model->update_member_time($this->_bot_id, $member_id);
		$this->response->body($member['chat_mode']);
	}
	
	public function action_switchuser()
	{
		$post = $this->request->post();
		$member_id = $post['member_id'];

		$msg = $this->_model->get_bot_txt_message($this->_bot_id, 'change_chatuser', $this->_lang_cd);
		$this->_sns_model->writelog($member_id, $msg, $msg, 'change_chatuser');
		$this->_model->update_member_time($this->_bot_id, $member_id);
		$this->response->body("");
	}
	
	public function action_chatbusitime()
	{
		$current_busitime = $this->_model->get_business_status($this->_bot_id, date("YmdHi"));
		
		$busi_times = ORM::factory('botbusitime')->
		where('bot_id', '=', $this->_bot_id)->
		where('busi_type_cd', '=', '99')->order_by('no')->find_all();
		
		$view = View::factory('admin/chatuserstatus');
		$view->item = $current_busitime;
		$view->items = $busi_times;
		$this->response->body($view);
	}
	
	public function action_userstatus()
	{
		$codes = $this->_model->get_config('code', $this->_lang_cd);
		$view = View::factory('admin/chatuserstatus');
		$view->items = $codes["23"];
		$view->lang_arr = $codes["02"];
		$view->online_user = $this->_model->get_online_user($this->_bot_id);
		$this->response->body($view);
	}
	
	public function action_onlineuser()
	{	
		$online_user = $this->_model->get_online_user($this->_bot_id);
		$this->response->body($online_user);
	}
	
	public function action_previewmessage() {
		$service_url = $this->_model->get_env('service_url');
		$post = $this->request->post();
		$msg_cd= $post['msg_cd'];
		$lang_cd = $post['lang_cd'];
		
		$preview = View::factory('admin/msgpreview');
		$items = [];
		try {
			$params = [
				'item_id' => $msg_cd,
				'item_div' => '6',
				'bot_id' => $this->_bot_id
			];
			$json_params = json_encode($params, JSON_UNESCAPED_UNICODE);
			$headers = [
				'Content-Type: application/json',
				'Accept-Charset: UTF-8',
			];
			$ch = curl_init();
			curl_setopt($ch, CURLOPT_URL, $service_url . "content/get_content_description");
			curl_setopt($ch, CURLOPT_POST, TRUE);
  		curl_setopt($ch, CURLOPT_POSTFIELDS, $json_params);
			curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
			curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
			$response = curl_exec($ch);
			if (curl_error($ch)) {
				throw new Exception(curl_error($ch));
			}
			curl_close($ch);
			$response_data = json_decode($response, true);
			if ($response_data && $response_data['result'] == 'success') {
				$messages = $response_data['data'];
				$default_message = [];
				$lang_message = [];
				foreach ($messages as $value) {
					if ($value['lang_cd'] == 'ja') {
						$default_message[] = $value;
					}
					if ($value['lang_cd'] == $lang_cd) {
						$lang_message[] = $value;
					}
				}
				$message = count($lang_message) > 0 ? $lang_message : $default_message;
				if (count($message) > 0) {
					$msg_type_cd = $message[0]['msg_type_cd'];
					if ($msg_type_cd == 'txt' || $msg_type_cd == 'mal' || $msg_type_cd == 'tpl') {
						foreach($message as $row) {
							$items[] = ['msg_type_cd'=>$msg_type_cd, 'content' => $row['content']];
						}
						ob_end_clean();
						$this->response->body(trim($row['content']));
						return;
					}
					else if ($msg_type_cd == 'img') {
						if ($message[0]['title'] != '') $items[] = ['msg_type_cd'=>'txt', 'content' => $message[0]['title']];
						foreach($message as $row) {
							$items[] = ['msg_type_cd'=>$msg_type_cd, 'content' => $row['msg_image']];
						}
					}
					else if ($msg_type_cd == 'btn' || $msg_type_cd == 'lst'  || $msg_type_cd == 'mnu') {
						$items[] = ['msg_type_cd'=>$msg_type_cd, 'content' => json_encode($message)];
					}
					else if ($msg_type_cd == 'car') {
						//$items[] = ['msg_type_cd'=>'txt', 'content' => $message[0]['title']];
						$items[] = ['msg_type_cd'=>$msg_type_cd, 'content' => json_encode($message)];
					}
					$preview->messages = $items;
					$btn_name = $this->_model->get_bot_tpl_message($this->_bot_id, 'btn_name', $lang_cd);
					$button_config = json_decode($btn_name, true);
					$preview->buttons = $button_config;
					ob_end_clean();
				}
			}
			$this->response->body($preview);
		} catch (\Throwable $th) {
			$this->response->body($preview);
		}
	}
	
	public function action_updatebusitime()
	{
		$post = $this->request->post();
		$no = $post['no'];
		
		$this->_model->update_business_status($this->_bot_id, $no);
	}
	
	public function action_updateuserstatus()
	{
		$post = $this->request->post();
		if (count($post) == 0) {
			$post = $this->request->body();
			$post = json_decode($post, true);
		}
		$status_cd = $post['status_cd'];
		$orm = ORM::factory('user', $this->_user->user_id);
		$orm->chat_status_cd = $status_cd;
		$orm->save();
		$this->response->body(json_encode([]));
	}
	
	public function action_updatetag()
	{
		$post = $this->request->post();
		$member_id = $post['member_id'];
		$tags = $post['tags'];
		$remark = $post['remark'];
		$member_name = $post['member_name'];
		
		$this->_model->update_member_tags($this->_bot_id, $member_id, $tags, $remark, $member_name);
		$msg = $this->_model->get_bot_txt_message($this->_bot_id, 'update_membertag', $this->_lang_cd);
		$this->_sns_model->writelog($member_id, $msg, $msg, 'update_membertag');
		$this->_model->update_member_time($this->_bot_id, $member_id);
	}
	
	public function action_setting()
	{
		$errors = NULL;
		
		if ($this->request->post()){
			$post = $this->request->post();
			if (array_key_exists('langs', $post)) {
				$langs = $post['langs'];
			}
			else {
				$langs = array('ja');
			}
			if (array_key_exists('snses', $post)) {
				$snses = $post['snses'];
			}
			else {
				$snses = array('fb');
			}
			if (array_key_exists('bots', $post)) {
				$bots = $post['bots'];
			}
			else {
				$bots = array();
			}
			$days = $post['days'];
			$user = ORM::factory('user', $this->_user->user_id);
			$user->chat_lang = implode(',', $langs);
			$user->chat_sns = implode(',', $snses);
			$user->chat_day = $days;
			$user->chat_enter_mode = $post['enter_mode'];
			$user->chat_req_flg = $post['chat_req_flg'];
			
			$user_chat_setting = json_decode($user->chat_setting, true);
			if ($user_chat_setting == null) {
				$user_chat_setting = [];
			}
			$user_chat_setting[strval($this->_bot_id)] = ['chat_lang'=>$user->chat_lang, 
					'chat_sns'=>$user->chat_sns, 'chat_day'=>$user->chat_day, 'chat_req_flg'=>$user->chat_req_flg, 'chat_scene_cd'=>$post['filter_scene']];
			$user->chat_setting = json_encode($user_chat_setting);
			if($this->_user->role_cd != '99' && $this->_user->role_cd != '80') {
				$user->chat_bot = implode(',', $bots);
			}
			$user->save();
			
			if (array_key_exists('tags', $post)) {
				$this->_set_session('setting_tags', $post['tags']);
			}
			else {
				$this->_set_session('setting_tags', array());
			}

			if (array_key_exists('users', $post)) {
				$this->_set_session('setting_users', $post['users']);
			}
			else {
				$this->_set_session('setting_users', array());
			}
			
			if (array_key_exists('chat_only', $post)) {
				$this->_set_session('setting_chat_only', 1);
			}
			else {
				$this->_set_session('setting_chat_only', 0);
			}
			
			$this->_set_session('setting_chat_req_flg', $post['chat_req_flg']);
			
			$this->_set_session('setting_filter_username', $post['filter_username']);
			
			$this->_set_session('setting_filter_scene', $post['filter_scene']);
			
			$this->_set_session('setting_modify_mode', $post['modify_mode']);
			
			$this->_set_session('setting_mask_privacy', $post['mask_privacy']);
			
			$this->_set_session('setting_sort_log_time', $post['sort_log_time']);
		}
		$this->_redirect('/chat?token=' . $post['token']);
	}
	
	public function action_searchmember()
	{
		if ($this->request->post()){
			$post = $this->request->post();
			if (array_key_exists('keyword', $post)) {
				$this->_set_session('setting_filter_member_no', $post['keyword']);
			}
		}
		$this->_redirect('/chat?token=' . $post['token']);
	}
	
	public function action_logout()
	{
		session_destroy();
		Cookie::delete('email');
		Cookie::delete('password');
		$this->_redirect('/login');
	}
	
	private function _get_chat_bot($all_bot) {
		$rel_bot = $this->_model->get_bot_setting($this->_bot_id, 'A01_relation_bots');
		if($this->_user->role_cd == '99' || $this->_user->role_cd == '80') {
			if ($rel_bot == '') {
				$bot_grp_id = $this->_model->get_grp_bot_id($this->_bot->bot_id);
				if ($bot_grp_id == 0) {
					if ($all_bot) {
						$chat_bot = $this->_bot->bot_id;
						//$chat_bot = $this->_bot_id;
					}
					else {
						$chat_bot = $this->_bot_id;
					}
				}
				else {
					$chat_bot = $this->_bot_id;
				}
			}
			else {
				$chat_bot = $rel_bot . "," . $this->_bot_id;
			}
		}
		else {
			if ($rel_bot == '') {
				if ($all_bot) {
					$chat_bot = $this->_bot->bot_id;
					//$chat_bot = $this->_bot_id;
				}
				else {
					$chat_bot = $this->_bot_id;
				}
			}
			else {
				$chat_bot = $this->_user->chat_bot;
				if ($chat_bot == '') $chat_bot = $this->_bot_id;
			}
		}
		return $chat_bot;
	}
	
	private function _write_access_log($content="")
	{
		$settings = $this->_model->get_system_config('settings');
		if ($settings["user_access_log"]) {
			$user_access = ORM::factory("Useraccess");
			$user_access->bot_id = $this->_bot_id;
			$user_access->user_id = $this->_user->user_id;
			$user_access->ip_address = $this->_model->getRemoteIPAddress();
			$user_access->action = 'chat';
			if (is_array($content)) {
				$user_access->content = json_encode($content);
			}
			else {
				$user_access->content = $content;
			}
			$user_access->save();
		}
	}

	private function _get_mode()
	{
		$business_hours = str_replace(":", "", date("H:i"));
		//if ($this->_bot->chat_mode == 1 && $business_hours >= $this->_bot->business_hours_begin && $business_hours <= $this->_bot->business_hours_end) {
		if ($this->_bot->chat_mode == 1) {
				return 1;
		}
		else {
			return 0;
		}		
	}

	public function action_serviceinformation()
	{
		if ($this->request->post()){
			$post = $this->request->post();
			$service_info = $this->_model->get_service_information($post['service_id'], $this->_bot_id, $this->_lang_cd);

			$this->response->body(json_encode($service_info[0]));
		}
	}

}