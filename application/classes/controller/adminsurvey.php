<?php defined('SYSPATH') or die('No direct script access.');

class Controller_<PERSON><PERSON>ur<PERSON> extends Controller_Template_Adminbase {

	public $_auth_required = TRUE;
	public $_transactional = true;
	public $_model;
	public $_survey_model;
	public $_aws_model;
	public $_view_path = 'admin/survey/';
	public $_action_path = '/adminsurvey/';
	const ITEM_DIV = 8;

	public function __construct(Request $request, Response $response)
	{
		parent::__construct($request, $response);
		$this->_model = new Model_Adminmodel();
		$this->_survey_model = new Model_Adminsurveymodel();
		$this->_aws_model = new Model_Aws();
		$this->_model->init($this->_bot_id);
		ini_set('max_execution_time', 300); // 300 seconds = 5 minutes
	}

	public function action_surveys()
	{
		$errors = NULL;
		$message = NULL;
		$post = NULL;
		$item_div = 8;

		if ($this->_bot_setting['flg_next_survey'] == 0) $this->redirect('/admin/top');

		$this->_del_session('survey_result_tags');

		if ($this->request->post()) {
			$post = $this->request->post();
			if ($post['act'] == 'newcopy') {
				$survey_id = $post['survey_id'];
				$new_id = $this->_copy_survey($survey_id, 'newcopy', $this->_bot_id);
				$this->redirect($this->_action_path . 'survey?id=' . $new_id);
			}
		}
		else {
			$post['start_date'] = Session::instance()->get('surveys_start_date', NULL);
			if ($post['start_date'] === NULL) $post['start_date'] = date('Y-m-d');
			$post['end_date'] = Session::instance()->get('surveys_end_date', NULL);
			if ($post['end_date'] === NULL) $post['end_date'] = '';
			$post['user_in_charge'] = Session::instance()->get('surveys_user_in_charge', NULL);
			if ($post['user_in_charge'] === NULL) $post['user_in_charge'] = 0;
			if ($this->_user->auth_all_contents_flg == 0) {
				$post['user_in_charge'] = strval($this->_user->user_id);
			}
			$post['template_cd'] = Session::instance()->get('surveys_template_cd', NULL);
			if ($post['template_cd'] === NULL) $post['template_cd'] = '';
			$class_cd = $this->request->query('class', NULL);
			if ($class_cd == NULL) {
				$post['class_cd_cond'] = Session::instance()->get('surveys_class_cd', NULL);
			}
			else {
				$post['class_cd_cond'] = $class_cd;
			}
			if ($post['class_cd_cond'] == NULL) $post['class_cd_cond'] = '';
		}
		Session::instance()->set('surveys_start_date', $post['start_date']);
		Session::instance()->set('surveys_end_date', $post['end_date']);
		Session::instance()->set('surveys_user_in_charge', $post['user_in_charge']);
		Session::instance()->set('surveys_template_cd', $post['template_cd']);
		Session::instance()->set('surveys_class_cd', $post['class_cd_cond']);

		$code_div = $this->_model->get_bot_setting($this->_bot_id, 'div_item_class_' . $item_div);
		$code_div_dict = $this->_model->get_code_div_kv($code_div, $this->_lang_cd);

		$json_next_survey_setting = $this->_model->get_bot_setting($this->_bot_id, 'json_next_survey_setting', true);
		if ($json_next_survey_setting == null) $json_next_survey_setting = [];
		if (!array_key_exists('user_in_charge_required', $json_next_survey_setting)) $json_next_survey_setting['user_in_charge_required'] = 0;
		if ($json_next_survey_setting['user_in_charge_required'] == 0) $post['user_in_charge'] = '';

		if ($this->_bot_id > 0) {
			$template_survey_id = $this->_bot_id * 1000;
			$template_survey = ORM::factory('survey')->where('survey_id', '=', $template_survey_id)->find();
			if (!isset($template_survey->survey_id)) {
				$this->_copy_survey(1, 'template', $this->_bot_id);
			}
		}
		
		$ret = $this->_model->get_admin_surveys($this->_bot_id, $post['user_in_charge'], $post['template_cd'], $post['class_cd_cond'], $this->_lang_cd);
		$items = $ret[0];
		$results = $ret[1];

		$items_show = [];
		$items_not_show = [];
		if ($post['start_date'] != NULL) {
			$start_date_cond = $post['start_date'];
		}
		else {
			$start_date_cond = '0000-01-01';
		}
		if ($post['end_date'] != NULL) {
			$end_date_cond = $post['end_date'];
		}
		else {
			$end_date_cond= '9999-12-31';
		}
		for($i=0; $i<count($items); $i++) {
			$item = $items[$i];
			$item['present'] = $this->_survey_model->get_undeleted_coupons($item['present']);
			if ($item['start_date'] != NULL) {
				$start_date = $item['start_date'];
			}
			else {
				$start_date = '0000-01-01';
			}
			if ($item['end_date'] != NULL) {
				$end_date = $item['end_date'];
			}
			else {
				$end_date = '9999-12-31';
			}
			if ($end_date < $start_date_cond || $start_date > $end_date_cond) {
				$items_not_show[] = $item;
			}
			else {
				$items_show[] = $item;
			}
		}
		$view = View::factory($this->_view_path . 'surveys');
		$menu = View::factory($this->_view_path . 'surveymenu');
		$menu->survey_id = NULL;
		$menu->lang_display = [];
		$view->menu = $menu;
		$view->code_div_dict = $code_div_dict;

		if ($json_next_survey_setting['user_in_charge_required'] == 1) {
			if ($this->_user->auth_all_contents_flg == 0 && $this->_user->role_cd != '99' && $this->_user->role_cd != '09') {
				$view->user_list = [strval($this->_user->user_id) =>$this->_user->name];
			}
			else {
				$view->user_list = [''=>__('admin.common.label.person_in_charge.all')] + $this->_model->get_bot_user_dict($this->_bot_id);
			}
		}
		else {
			$view->user_list = [''=>__('admin.common.label.person_in_charge.all')];
		}
		$view->template_list = [''=>__('admin.common.label.all.template')] + $this->_model->get_scene_template($this->_bot_id, 'survey', '');
		$view->item_div = $item_div;
		$view->div_item_class = $code_div;
		$view->post = $post;
		$view->items = $items_show;
		$view->results = $results;
		$view->json_next_survey_setting = $json_next_survey_setting;
		$this->template->content = $view;
	}

	private function _copy_survey($source_id, $mode, $target_bot_id) {
		$item_div = 8;
		$orm = ORM::factory('survey', $source_id);
		$survey = ORM::factory('survey');
		if ($mode == 'template') {
			$survey->survey_id = $this->_bot_id * 1000;
		} else {
			$survey->survey_id = $this->_model->get_max_survey_id($target_bot_id);
		}
		if ($mode == 'version') {
			// todo
		}
		else if ($mode == 'template') {
			$survey->survey_name = $orm->survey_name;
		}
		else {
			$survey->survey_name = substr($orm->survey_name . '-copy', 0, 200);
		}
		$survey->survey_cd = substr((string)$survey->survey_id, strlen((string)$target_bot_id));
		$survey->bot_id = $target_bot_id;
		$survey->class_cd = $orm->class_cd;
		$survey->survey_status_cd = $orm->survey_status_cd;
		$survey->survey_type_cd = $orm->survey_status_cd;
		$survey->start_date = $orm->start_date;
		$survey->end_date = $orm->end_date;
		$survey->duration = $orm->duration;
		$survey->present = $orm->present;
		$survey->survey_data = $orm->survey_data;
		$survey->redirect_url = $orm->redirect_url;
		$survey->user_in_charge = $orm->user_in_charge;
		if ($orm->bot_id == $target_bot_id) {
			/**
			 * only copy mail templates within same bot
			 */
			$survey->member_mail_template = $orm->member_mail_template ;
			$survey->user_mail_template = $orm->user_mail_template;
			$survey->mail_member_flg = $orm->mail_member_flg;
			$survey->mail_user_flg = $orm->mail_user_flg;
			$survey->scene_cd = $orm->scene_cd;
			$survey->template_cd = $orm->template_cd;
		}
		else {
			/**
			 * not to copy mail templates 
			 */
			$survey->member_mail_template = null;
			$survey->user_mail_template = null;
			$survey->mail_member_flg = 0;
			$survey->mail_user_flg = 0;

			$default_scene_cd = $this->_model->get_bot_setting($target_bot_id, 'default_scene_cd');
			$survey->scene_cd = $default_scene_cd;
		}
		$survey->upd_user = $this->_user->user_id;
		$survey->upd_time = date('Y-m-d H:i:s');
		$survey->save();

		$orms = ORM::factory('surveydescription')->where('survey_id', '=', $source_id)->find_all();
		foreach($orms as $orm) {
			$survey_desc = ORM::factory('surveydescription');
			$survey_desc->survey_id = $survey->survey_id;
			$survey_desc->lang_cd = $orm->lang_cd;
			$survey_desc->title = $orm->title;
			$survey_desc->description = $orm->description;
			$survey_desc->description_extra = $orm->description_extra;
			$survey_desc->survey_image = $orm->survey_image;
			$survey_desc->save();
		}
		$orms = ORM::factory('itemdisplay')->where('item_id', '=', $source_id)->where('item_div', '=', $item_div)->find_all();
		foreach($orms as $orm) {
			$survey_display = ORM::factory('itemdisplay');
			$survey_display->item_id = $survey->survey_id;
			$survey_display->item_div = $item_div;
			$survey_display->bot_id = $target_bot_id;
			$survey_display->lang_display = $orm->lang_display;
			$survey_display->public_flg = $orm->public_flg;
			$survey_display->save();
		}
		if ($mode == 'template') return $survey;
		
		$orms = ORM::factory('surveyentry')->where('survey_id', '=', $source_id)->find_all();
		foreach($orms as $orm) {
			$survey_entry = ORM::factory('surveyentry');
			$survey_entry->survey_id = $survey->survey_id;
			$survey_entry->no = $orm->no;
			$survey_entry->lang_cd = $orm->lang_cd;
			$survey_entry->title = $orm->title;
			$survey_entry->entry_type_cd = $orm->entry_type_cd;
			$survey_entry->required = $orm->required;
			$survey_entry->input_rules = $orm->input_rules;
			$survey_entry->entry_data = $orm->entry_data;
			$survey_entry->next_page = $orm->next_page;
			$survey_entry->delete_flg = $orm->delete_flg;
			$survey_entry->save();
		}
		$orms = ORM::factory('surveybranch')->where('survey_id', '=', $source_id)->find_all();
		foreach($orms as $orm) {
			$survey_branch = ORM::factory('surveybranch');
			$survey_branch->survey_id = $survey->survey_id;
			$survey_branch->no = $orm->no;
			$survey_branch->lang_cd = $orm->lang_cd;
			$survey_branch->title = $orm->title;
			$survey_branch->type = $orm->type;
			$survey_branch->dest_no = $orm->dest_no;
			$survey_branch->conditions = $orm->conditions;
			$survey_branch->save();
		}
		$orms = ORM::factory('surveysection')->where('survey_id', '=', $source_id)->find_all();
		foreach($orms as $orm) {
			$survey_section = ORM::factory('surveysection');
			$survey_section->survey_id = $survey->survey_id;
			$survey_section->no = $orm->no;
			$survey_section->lang_cd = $orm->lang_cd;
			$survey_section->title = $orm->title;
			$survey_section->entries = $orm->entries;
			$survey_section->sort_no = $orm->sort_no;
			$survey_section->save();
		}
		return $survey->survey_id;
	}

	public function action_survey()
	{
		$errors = NULL;
		$message = NULL;
		$post = NULL;
		$survey = NULL;
		$item_div = 8;
		$survey_id = NULL;

		$code_div = $this->_model->get_bot_setting($this->_bot_id, 'div_item_class_' . $item_div);
		$item_data_def = $this->_model->get_item_tpl_message($this->_bot_id, $item_div, '', 'item_data', 'ja');

		if ($this->request->post()){
			$is_new = false;
			$post = $this->request->post();
			$survey_id = $post['survey_id'];
			$act = $post['act'];
			if ($act == 'delete') {
				DB::update('t_survey')->set(['delete_flg'=>1])->where('survey_id', '=', $survey_id)->execute();
				$this->redirect($this->_action_path . 'surveys');
			}
			else {
				if ($survey_id == NULL) {
					$survey = ORM::factory('survey');
					$survey->survey_id = $this->_model->get_max_survey_id($this->_bot_id);
					$survey->survey_cd = substr((string)$survey->survey_id, strlen((string)$this->_bot_id));
					$survey->bot_id = $this->_bot_id;
					$survey_data = [];
				}
				else {
					$survey = ORM::factory('survey', $survey_id);
					$survey_data = json_decode($survey->survey_data, true);
					//$item_data = json_decode($survey->survey_data, true);
				}
				$survey->survey_name = trim($post['survey_name']);
				$survey->class_cd = implode(' ', json_decode($post['class_cd'], true));
				$survey->survey_status_cd = '01';
				$survey->survey_type_cd = '000';

				if (strlen($post['start_time']) == 4) $post['start_time'] = "0" . $post['start_time'];
				if (strlen($post['end_time']) == 4) $post['end_time'] = "0" . $post['end_time'];

				if ($post['start_date'] == '') {
					$survey->start_date = NULL;
				}
				else {
					$survey->start_date = $post['start_date'] . ' ' . $post['start_time'];
				}
				if ($post['end_date'] == '') {
					$survey->end_date = NULL;
				}
				else {
					$survey->end_date = $post['end_date'] . ' ' . $post['end_time'];
				}
				$survey->duration = $post['duration'];
				$survey->scene_cd = $post['scene_cd'];
				$survey->template_cd = $post['template_cd'];
				$survey->redirect_url = $post['redirect_url'];
				$survey->member_mail_template = $post['member_mail_template'];
				$survey->user_mail_template = $post['user_mail_template'];
				if ($post['member_mail_template'] == '') {
					$survey->mail_member_flg = 0;
				}
				else {
					$survey->mail_member_flg = 1;
				}
				if ($post['user_mail_template'] == '') {
					$survey->mail_user_flg = 0;
				}
				else {
					$survey->mail_user_flg = 1;
				}
				if ($post['user_in_charge'] != '') $survey->user_in_charge = $post['user_in_charge'];
				$survey->mail_users = $post['mail_users'];
				if ($post['answer_limit'] == 1) {
					$survey_data['answer_limit'] = 1;
				}
				else {
					unset($survey_data['answer_limit']);
				}
				if ($post['survey_answer_limit'] == '') {
					unset($survey_data['survey_answer_limit']);
				}
				else {
					$survey_data['survey_answer_limit'] = $post['survey_answer_limit'];
				}
				if ($post['redirect_url2'] == '') {
					unset($survey_data['redirect_url']);
				}
				else {
					$survey_data['redirect_url'] = $post['redirect_url2'];
				}
				$survey->support_lang_cd = implode(',', json_decode($post['lang_support_cd'], true));
				$survey->survey_data = json_encode($survey_data, JSON_UNESCAPED_UNICODE);
				$survey->upd_user = $this->_user->user_id;
				$survey->upd_time = date('Y-m-d H:i:s');

				$survey->save();
				$this->_update_item_display($this->_bot_id, $survey->survey_id, $item_div, $post);
				$this->redirect($this->_action_path . 'survey?id=' . $survey->survey_id);
			}
		}
		else {
			$json_next_survey_setting = $this->_model->get_bot_setting($this->_bot_id, 'json_next_survey_setting');
			$json_next_survey_setting = json_decode($json_next_survey_setting, true);
			if (!array_key_exists('user_in_charge_required', $json_next_survey_setting)) $json_next_survey_setting['user_in_charge_required'] = 0;
			$user_dict = $this->_model->get_bot_user_dict($this->_bot_id);
			$survey_id = $this->request->query('id', NULL);
			if ($survey_id == NULL) {
				$survey_id = '';
				$this->_del_session('survey_id');
				$post['class_cd'] = '';
				$post['survey_cd'] = '';
				$post['start_date'] = '';
				$post['end_date'] = '';
				$post['survey_name'] = '';
				$post['class_name_array'] = array();
				$post['duration'] = '5';
				$post['recommend'] = 0;
				$post['support_lang_cd'] = explode(',', $this->_bot->lang_cd);
				$post['lang_display'] = explode(',', $this->_bot->lang_cd);
				$post['public_flg'] = 1;
				$post['scene_cd'] = '';
				$post['template_cd'] = '';
				$post['redirect_url'] = $json_next_survey_setting['end_redirect_url'];
				$post['mail_member_flg'] = 0;
				$post['mail_user_flg'] = 0;
				$post['member_mail_template'] = '';
				$post['user_mail_template'] = '';
				$post['user_in_charge'] = 0;
				$post['mail_users'] = '';
				$post['answer_limit'] = '0';
				$post['survey_answer_limit'] = '';
			}
			else {
				$permission = $this->_item_permission_check($survey_id);
				if ($permission < 0) {
					$this->redirect('/adminsurvey/surveys');
				}
				$this->_set_session('survey_id', $survey_id);
				$survey = ORM::factory('survey', $survey_id);
				if ($survey->user_in_charge == null) {
					$user_in_charge_array = [];
				}
				else {
					$user_in_charge_array = explode(',', $survey->user_in_charge);
				}
				$user_is_in_charge = in_array($this->_user->user_id, $user_in_charge_array);
				if (
					$json_next_survey_setting['user_in_charge_required'] == 1 &&
					$this->_user->auth_all_contents_flg == 0 &&
					!$user_is_in_charge
					) {
					$this->redirect($this->_action_path . 'surveys');
				}
				$post['class_cd'] = trim($survey->class_cd);
				$post['survey_cd'] = $survey->survey_cd;
				$post['start_date'] = $survey->start_date;
				$post['end_date'] = $survey->end_date;
				$post['duration'] = $survey->duration;
				$post['survey_name'] = $survey->survey_name;
				$post['scene_cd'] = $survey->scene_cd;
				$post['template_cd'] = $survey->template_cd;
				$post['redirect_url'] = $survey->redirect_url;
				$post['mail_member_flg'] = $survey->mail_member_flg;
				$post['mail_user_flg'] = $survey->mail_user_flg;
				$post['member_mail_template'] = $survey->member_mail_template;
				$post['user_mail_template'] = $survey->user_mail_template;
				$post['user_in_charge'] = $survey->user_in_charge;
				$post['mail_users'] = $survey->mail_users;
				$post['support_lang_cd'] = explode(',', $survey->support_lang_cd);
				
				$display = ORM::factory('itemdisplay')->where('bot_id', '=', $this->_bot_id)->where('item_id', '=', $survey->survey_id)->where('item_div', '=', $item_div)->find();
				if (isset($display->item_id)) {
					$post['recommend'] = $display->recommend;
					$post['lang_display'] = $display->lang_display;
					$post['public_flg'] = $display->public_flg;
				}
				else {
					$post['recommend'] = 0;
					$post['lang_display'] = $this->_bot->lang_cd;
					$post['public_flg'] = 1;
				}
				if ($post['lang_display'] == '') {
					$post['lang_display'] = [];
				}
				else {
					$post['lang_display'] = explode(',', $post['lang_display']);
				}
				$post['answer_limit'] = '0';
				$post['survey_answer_limit'] = '';
				$post['redirect_url2'] = '';
				$survey_data = json_decode($survey->survey_data, true);
				if (is_array($survey_data) && array_key_exists('answer_limit', $survey_data)) $post['answer_limit'] = $survey_data['answer_limit'];
				if (is_array($survey_data) && array_key_exists('survey_answer_limit', $survey_data)) $post['survey_answer_limit'] = $survey_data['survey_answer_limit'];
				if (is_array($survey_data) && array_key_exists('redirect_url', $survey_data)) $post['redirect_url2'] = $survey_data['redirect_url'];
			}
		}

		$view = View::factory ($this->_view_path . 'survey');
		$menu = View::factory($this->_view_path . 'surveymenu');
		$menu->survey_id = $survey_id;
		$menu->admin_path = $this->_model->get_env('admin_url') . "admin";
		$view->menu = $menu;
		$view->scene_cd = $this->_bot->facility_cd;
		$view->survey_id = $survey_id;
		$view->duration = ['1'=>'1','3'=>'3','5'=>'5','10'=>'10','15'=>'15','20'=>'20','30'=>'30'];
		$user_dict = $this->_model->get_bot_user_dict($this->_bot_id);
		// React複数選択コンポーネント用
		$user_list_for_react_select = array_map(function ($key, $value) {
			return array('value' => $key, 'label' => $value);
		}, array_keys($user_dict), $user_dict);
		$view->user_list_for_react_select = json_encode($user_list_for_react_select);
		// 担当者
		$user_in_charge_list_for_react_select = [];
		foreach ($user_in_charge_array as $user_id) {
			$user_id = (int) $user_id;
			if (array_key_exists($user_id, $user_dict)) {
				$user_in_charge_list_for_react_select[] = ['value' => $user_id, 'label' => $user_dict[$user_id]];
			}
		}
		$view->user_in_charge_list_for_react_select = json_encode($user_in_charge_list_for_react_select);

		$scenes = ORM::factory('botscene')->where('bot_id', '=', $this->_bot_id)->where('use_survey_flg', '=', 1)->order_by('sort_no')->find_all();
		$scene_list = ['' => '-'];
		foreach($scenes as $scene) {
			$scene_list[$scene->scene_name] = $scene->label;
		}
		$view->scene_list = $scene_list;
		$view->template_list = [''=>'-デフォルト-'] + $this->_model->get_scene_template($this->_bot_id, 'survey', '', $post['scene_cd']);
		$view->member_mail_template_list = $this->_model->get_bot_message_dict($this->_bot_id, '42', 'mal');
		$view->user_mail_template_list = $this->_model->get_bot_message_dict($this->_bot_id, '43', 'mal');
		//$view->survey_url = 'https://survey.talkappi.com/?f=' . $this->_bot->facility_cd . '&id=' . $survey_id_show . '&uid={member_id}';

		if ($survey == NULL) {
			$verify_url = '';
		}
		else {
			if ($survey->scene_cd != NULL) {
				$verify_url = $this->_model->get_env('survey_url') . '?f=' . $survey->scene_cd . '&id=' . $post['survey_cd'];
			}
			else {
				$verify_url = $this->_model->get_env('survey_url') . '?f=' . $this->_bot->facility_cd . '&id=' . $post['survey_cd'];
			}
		}
		$view->verify_url = $verify_url;
		$view->login_class_cd = '';
		if (strpos($json_next_survey_setting['base_url'], 'https://survey.talkappi.com') === 0) {
			$view->open_url = $verify_url;
		}
		else {
			$view->open_url = $json_next_survey_setting['base_url'] . '?' . $json_next_survey_setting['param_id'] . '=' . $post['survey_cd'];
			if (array_key_exists('login', $json_next_survey_setting)) {
				$view->login_class_cd = json_encode($json_next_survey_setting['login']);
				$survey_class = explode(' ', $post['class_cd']);
				foreach($survey_class as $c) {
					if (in_array($c, $json_next_survey_setting['login'])) {
						$view->open_url = $json_next_survey_setting['base_url'] . '?' . $json_next_survey_setting['param_id'] . '=' . $post['survey_cd'] . '&' . $json_next_survey_setting['param_login'];
					}
				}
			}
		}
		$view->entries = json_encode($this->_model->get_survey_entries_mail($survey_id));
		$view->post = $post;
		$view->survey_id = $survey_id;
		$view->code_div = $code_div;
		$view->json_next_survey_setting = $json_next_survey_setting;
		$view->item_data_def = $item_data_def;
		$this->template->content = $view;
		$this->_page_navi('[{"f":"50","url":"/adminsurvey/surveys"}, {"f":"5001"}]');
	}

	public function action_surveydesc()
	{
		$errors = NULL;
		$message = '';
		$post = NULL;
		$item_description = NULL;
		$item_description_ja = NULL;

		$item_div = 8;

		$lang_cd = NULL;
		$url = NULL;
		$up_image = false;

		if ($this->request->post()) {
			$post = $this->request->post();
			$survey_id = $post['survey_id'];
			if (array_key_exists('lang', $post)) {
				$lang_cd = $post['lang'];
			}
			if ($this->_page_action == 'translate') {
				if ($lang_cd != NULL) {
					$from = ORM::factory('surveydescription')->where('survey_id', '=', $survey_id)->where('lang_cd', '=', $post['translate_from_lang'])->find();
					$to = ORM::factory('surveydescription')->where('survey_id', '=', $survey_id)->where('lang_cd', '=', $lang_cd)->find();
					$description_extra = json_decode($from->description_extra, true);
					for($i=0; $i<count($description_extra); $i++) {
						foreach($description_extra[$i] as $k=>$v) {
							if ($k != 'fold') $description_extra[$i][$k] = $this->_model->translate($v, $lang_cd);
						}
					}
					// DBに保存していない場合は、初期表示内容ベースに翻訳
					$complete_info = json_decode($from->complete_info, true);
					if ($complete_info == null) {
						$complete_info['complete_area'] = json_encode([
							'title' => $this->_model->translate('お忙しい中ご回答ありがとうございました。', $lang_cd),
							'description' => $this->_model->translate('ご回答内容は今後のサービス向上に役立てさせていただきます。', $lang_cd),
						], JSON_UNESCAPED_UNICODE);
					} else {
						foreach($complete_info as $k=>$v) {
							if ($k == 'description') {
								$complete_info[$k] = $this->_model->translate($v, $lang_cd);
							}
							else if ($k == 'description_extra') {
								$extra = json_decode($v, true);
								for($i=0; $i<count($extra); $i++) {
									foreach($extra[$i] as $k2=>$v2) {
										if ($k2 != 'fold') $extra[$i][$k2] = $this->_model->translate($v2, $lang_cd);
									}
								}
								$complete_info[$k] = json_encode($extra, JSON_UNESCAPED_UNICODE);
							}
							else if ($k == 'actions') {
								$extra = json_decode($v, true);
								for($i=0; $i<count($extra); $i++) {
									foreach($extra[$i] as $k2=>$v2) {
										if ($k2 == 'title') $extra[$i][$k2] = $this->_model->translate($v2, $lang_cd);
									}
								}
								$complete_info[$k] = json_encode($extra, JSON_UNESCAPED_UNICODE);
							}
							else if ($k == 'complete_area') {
								$extra = json_decode($v, true);
								foreach($extra as $k2=>$v2) {
									if ($k2 != 'icon') $extra[$k2] = $this->_model->translate($v2, $lang_cd);
								}
								$complete_info[$k] = json_encode($extra, JSON_UNESCAPED_UNICODE);
							}
						}
					}
					
					if (isset($to->survey_id)) {
						$pairs = [];
						$pairs['survey_name'] = $this->_model->translate($from->survey_name, $lang_cd);
						$pairs['title'] = $this->_model->translate($from->title, $lang_cd);
						$pairs['description'] = $this->_model->translate($from->description, $lang_cd);
						$pairs['description_extra'] = json_encode($description_extra, JSON_UNESCAPED_UNICODE);
						$pairs['survey_image'] = $from->survey_image;
						$pairs['complete_info'] = json_encode($complete_info, JSON_UNESCAPED_UNICODE);
						DB::update('t_survey_description')->set($pairs)->where('survey_id', '=', $survey_id)->where('lang_cd', '=', $lang_cd)->execute();
					}
					else {
						$to = ORM::factory('surveydescription');
						$to->survey_id = $survey_id;
						$to->lang_cd = $lang_cd;
						$to->survey_name = $this->_model->translate($from->survey_name, $lang_cd);
						$to->title = $this->_model->translate($from->title, $lang_cd);
						$to->description = $this->_model->translate($from->description, $lang_cd);
						$to->description_extra = json_encode($description_extra, JSON_UNESCAPED_UNICODE);
						$to->complete_info = json_encode($complete_info, JSON_UNESCAPED_UNICODE);
						$to->survey_image = $from->survey_image;
						$to->save();
					}
				}
				$this->redirect($this->_action_path . "surveydesc?id=$survey_id&lang=$lang_cd&page=" . $post['page']);
				return;
			}
			$item = ORM::factory('survey', $survey_id);
			$flg_auto_translate = false;
			$flg_apply_all_lang = false;
			if (array_key_exists('flg_auto_translate', $post)) {
				$flg_auto_translate = true;
			}
			if (array_key_exists('flg_apply_all_lang', $post)) {
				$flg_apply_all_lang = true;
			}
			/*
			if (strlen($post['url']) > 1000) {
				$message = "E|写真アクションは1000以内してください。";
			}
			if (strlen($post['btn1_url']) > 1000 || strlen($post['btn2_url']) > 1000 || strlen($post['btn3_url']) > 1000) {
				$message = "E|ボタンアクションは1000以内してください。";
			}
			if (strlen($post['survey_image']) > 1000) {
				$message = "E|写真URLは1000以内してください。";
			}
			if ($post['survey_image'] != '' && strpos($post['survey_image'], 'https:') !== 0) {
				$message = "E|写真URLはHTTPS必要です。";
			}
			*/

			$google_model = Model::factory('google');

				$survey_description = ORM::factory('surveydescription')->where('survey_id', '=', $survey_id)->where('lang_cd', '=', $lang_cd)->find();
				// if (!isset($survey_description->survey_id)) {
					// $survey_description = ORM::factory('surveydescription');
					// $survey_description->survey_id = $survey_id;
					// $survey_description->lang_cd = $lang_cd;
					// $survey_description->title = $post['title'];
					// $survey_description->description = $post['description'];
					// $survey_description->description_extra = $post['description_extra'];
					// $survey_description->survey_image = $url;
					// $survey_description->save();
					// $message = "S|アンケート情報を登録しました。";
				// }
				// else {
					// $up_array = [];
					// $up_array['title'] = $post['title'];
					// $up_array['description'] = $post['description'];
					// $up_array['description_extra'] = $post['description_extra'];
					// $up_array['survey_image'] = $url;
					// $query = DB::update('t_survey_description')->
					// set($up_array)->where('survey_id', '=', $survey_id)->where('lang_cd', '=', $lang_cd);
					// $result = $query->execute();
					// if ($url === '' && $survey_description->survey_image != NULL) {
					// 	$this->_aws_model->delete_object($this->_bot_id, 'survey', basename($survey_description->survey_image));
					// }
					// $message = "S|アンケート情報を更新しました。";
				// }

				if ($flg_apply_all_lang && !$up_image) {
					$new_image_url = $this->_aws_model->all_lang_image($url);
					if ($new_image_url != '') DB::update('t_survey_description')->set(['survey_image'=>$new_image_url])->where('survey_id', '=', $survey_id)->execute();
				}
				if ($post['page'] == 'input') {
					if ($post['image_base64'] == '') {
						$url = '';
					}
					else {
						if (strpos($post['image_base64'], 'data:') === 0) {
							if ($flg_apply_all_lang) {
								$url = $this->_aws_model->put_base64_file($this->_bot_id, $post['image_base64'], $survey_id, 'survey');
							}
							else {
								$url = $this->_aws_model->put_base64_file($this->_bot_id, $post['image_base64'], $survey_id . '_' . $lang_cd, 'survey');
							}
							$this->_aws_model->resize_image_url($url, 800, 0);
							$up_image = true;
							$this->_aws_model->refresh_url($url);
						}
						else {
							$url = $post['image_base64'];
						}
					}
					if (!isset($survey_description->survey_id)) {
						$survey_description = ORM::factory('surveydescription');
						$survey_description->survey_id = $survey_id;
						$survey_description->lang_cd = $lang_cd;
						$survey_description->title = $post['title'];
						$survey_description->description = $post['description'];
						$survey_description->description_extra = $post['description_extra'];
						$survey_description->survey_image = $url;
						$survey_description->save();
						$message = "S|アンケート情報を登録しました。";
					}
					else {
						$up_array = [];
						$up_array['title'] = $post['title'];
						$up_array['description'] = $post['description'];
						$up_array['description_extra'] = $post['description_extra'];
						if ($url !== NULL) $up_array['survey_image'] = $url;
						$query = DB::update('t_survey_description')->set($up_array)->where('survey_id', '=', $survey_id)->where('lang_cd', '=', $lang_cd);
						$result = $query->execute();
						if ($url === '' && $survey_description->survey_image != NULL) {
							$this->_aws_model->delete_object($this->_bot_id, 'survey', basename($survey_description->survey_image));
						}
						$message = "S|アンケート情報を更新しました。";
					}
					if ($flg_apply_all_lang && !$up_image) {
						$new_image_url = $this->_aws_model->all_lang_image($url);
						if ($new_image_url != '') DB::update('t_survey_description')->set(['survey_image'=>$new_image_url])->where('survey_id', '=', $survey_id)->execute();
					}
				} else if ($post['page'] == 'complete') {
					if ($post['image_base64'] == '') {
						$url = '';
					}
					else {
						if (strpos($post['image_base64'], 'data:') === 0) {
							if ($flg_apply_all_lang) {
								$url = $this->_aws_model->put_base64_file($this->_bot_id, $post['image_base64'], $survey_id . '_icon', 'survey');
							}
							else {
								$url = $this->_aws_model->put_base64_file($this->_bot_id, $post['image_base64'], $survey_id . '_' . $lang_cd . '_icon', 'survey');
							}
							$this->_aws_model->resize_image_url($url, 800, 0);
							$up_image = true;
							$this->_aws_model->refresh_url($url);
						}
						else {
							$url = $post['image_base64'];
						}
					}

					$temp_data = json_decode($post['complete_area'], true);
					$temp_data['icon'] = $url;
					$post['complete_area'] = json_encode($temp_data, JSON_UNESCAPED_UNICODE);
					$temp_arr = ['complete_area'=>$post['complete_area'],
								'actions'=>json_encode([['title'=>$post['complete_action_name'] , 'url'=>$post['complete_action_url']]], JSON_UNESCAPED_UNICODE)];
					// if ($post['title'] != $survey_description->title) $temp_arr['title'] = $post['title'];
					// if ($post['description'] != $survey_description->description) $temp_arr['description'] = $post['description'];
					// if ($post['description_extra'] != $survey_description->description_extra) $temp_arr['description_extra'] = $post['description_extra'];
					$up_array['complete_info'] = json_encode($temp_arr, JSON_UNESCAPED_UNICODE);
					$query = DB::update('t_survey_description')->set($up_array)->where('survey_id', '=', $survey_id)->where('lang_cd', '=', $lang_cd);
					$result = $query->execute();
				}

				$orm = ORM::factory('survey', $survey_id);
				$orm->upd_user = $this->_user->user_id;
				$orm->upd_time = date('Y-m-d H:i:s');
				$orm->save();
				$this->redirect($this->_action_path . "surveydesc?id=$survey_id&lang=$lang_cd&page=" . $post['page']);
				/*
				 $this->_auto_keyword($item_id, $item_div);

				 $tool_url = $this->_model->get_env('tool_url');
				 $tool_url = $tool_url . "table=i&lang=$lang_cd&facilitylabel=". $this->_bot_id . "&itemid=$item_id";
				 $this->_model->curl_get($tool_url);
				 */
		}
		else {
			$lang_cd = $this->request->query('lang', NULL);
			$survey_id = $this->request->query('id', NULL);
			if ($lang_cd == NULL) {
				$support_lang = ORM::factory('survey', $survey_id)->support_lang_cd;
				if (!empty($support_lang)) {
					$langs = explode(',', $support_lang);
	
					if (count($langs) > 0) $lang_cd = $langs[0];
					$this->redirect("/adminsurvey/surveydesc?id=$survey_id&lang=$lang_cd");
				}
				if ($lang_cd == NULL) $this->redirect('/adminsurvey/survey?id=' . $survey_id);
			}
			if ($lang_cd != NULL) {
				$support_lang = ORM::factory('survey', $survey_id)->support_lang_cd;
				if (!empty($support_lang)) {
					$langs = explode(',', $support_lang);
					if (!in_array($lang_cd, $langs)) {
						$this->redirect('/adminsurvey/survey?id=' . $survey_id);
					}
				}
			}

			$permission = $this->_item_permission_check($survey_id);
			if ($permission < 0) {
				$this->redirect('/adminsurvey/surveys');
			}
			$item = ORM::factory('survey', $survey_id);

			$desc_page = $this->request->query('page', NULL);
			if ($desc_page == NULL) $desc_page = 'input';
			$post['new'] = 0;
			$post['page'] = $desc_page;
			$post['survey_name'] = '';
			$post['title'] = '';
			$post['description'] = '';
			$post['description_extra'] = '';
			$post['survey_image'] = '';
			$item_description = ORM::factory('surveydescription')->where('survey_id', '=', $survey_id)->where('lang_cd', '=', $lang_cd)->find();
			if (isset($item_description->survey_id)) {
				$post['title'] = $item_description->title;
				$post['description'] = $item_description->description;
				$post['description_extra'] = json_decode($item_description->description_extra, true);
				$post['survey_image'] = $item_description->survey_image;
				if ($desc_page == 'complete') {
					$complete_info = json_decode($item_description->complete_info, true);
					if (is_array($complete_info)) {
						if (array_key_exists('title', $complete_info)) $post['title'] = $complete_info['title'];
						if (array_key_exists('description', $complete_info)) $post['description'] = $complete_info['description'];
						if (array_key_exists('description_extra', $complete_info)) $post['description_extra'] = json_decode($complete_info['description_extra'], true);
						if (array_key_exists('complete_area', $complete_info)) {
							$post['complete_area'] = json_decode($complete_info['complete_area'], true);
							if (!array_key_exists('icon', $post['complete_area']) || $post['complete_area']['icon'] == '') {
								$post['complete_area']['icon'] = '/assets/common/images/thank_answer.jpeg';
							}
						}
						else {
							$post['complete_area'] = ['title'=>'お忙しい中ご回答ありがとうございました。', 'icon'=>'/assets/common/images/thank_answer.jpeg', 'description'=>'ご回答内容は今後のサービス向上に役立てさせていただきます。'];
						}
						if (array_key_exists('actions', $complete_info)) {
							$post['actions'] = json_decode($complete_info['actions'], true);
						}
						else {
							$post['actions'] = [['title'=>'', 'url'=>'']];
						}
					}
					else {
						$post['complete_area'] = ['title'=>'お忙しい中ご回答ありがとうございました。', 'icon'=>'/assets/common/images/thank_answer.jpeg', 'description'=>'ご回答内容は今後のサービス向上に役立てさせていただきます。'];
						$post['actions'] = [['title'=>'', 'url'=>'']];
					}
				}
			}
			else {
				$post['new'] = 1;
			}
		}

		$view = View::factory ($this->_view_path . 'surveydesc');
		$menu = View::factory($this->_view_path . 'surveymenu');
		// $menu->type = $lang_cd;
		$menu->survey_id = $survey_id;
		$menu->admin_path = $this->_model->get_env('admin_url') . "admin";
		$item_descs = ORM::factory('surveydescription')->where('survey_id', '=', $survey_id)->find_all();
		$translate_from_lang = [];
		foreach($item_descs as $de) {
			if ($de->lang_cd != $lang_cd) {
				$translate_from_lang[$de->lang_cd] = $this->_codes['02'][$de->lang_cd];
			}
		}
		if (count($item_descs) == 0) {
			$view->auto_translate = 1;
			$view->all_language = 1;
		}
		else {
			$view->auto_translate = 0;
			$view->all_language = 0;
		}
		$view->lang_display = $item->support_lang_cd;
		$view->menu = $menu;

		$survey = ORM::factory('survey', $survey_id);
		if ($survey->scene_cd != NULL) {
			$verify_url = $this->_model->get_env('survey_url') . '?f=' . $survey->scene_cd . '&id=' . $survey->survey_cd . '&lang_cd=' . $lang_cd;
			$scene_cd = $survey->scene_cd;
		}
		else {
			$verify_url = $this->_model->get_env('survey_url') . '?f=' . $this->_bot->facility_cd . '&id=' . $survey->survey_cd . '&lang_cd=' . $lang_cd;
			$scene_cd = $this->_bot->facility_cd;
		}

		// 多言語表示OFFでその言語の検証できるようにtokenをつける
		$display = ORM::factory('itemdisplay')->where('item_id', '=', $survey_id)->where('item_div', '=', $item_div)->find();
		$display_lang_arr = explode(',', $display->lang_display);
		if (!in_array($lang_cd, $display_lang_arr)) {
			$token_payload = [];
			$token_payload['data'] = json_encode(["survey_id"=>$survey_id,"lang_cd"=>$lang_cd,"bot_id"=>$this->_bot_id]);
			$verify_url = $verify_url. '&eval=' . $this->_model->get_new_token($token_payload);
		}
		
		$ref_scene_cd = $this->_model->get_scene_ref($scene_cd, 'survey', '', $survey->template_cd, $scene_cd);
		$scene_path = $this->_model->get_scene_path($ref_scene_cd, 'survey', '', $survey->template_cd);

		$filename = $scene_path . "config.json";
		$handle = fopen($filename, "r");
		$content = fread($handle, filesize($filename));
		fclose($handle);
		$config = json_decode($content, true);

		$scene_url_path = $this->_model->get_scene_url_path($ref_scene_cd, 'survey', '', $survey->template_cd);
		$logo_file = '';
		$ext_array = $this->_model->get_setting('support_image_type');
		foreach($ext_array as $ext) {
			if (file_exists($scene_path . 'logo.' . $ext) == true) $logo_file = $scene_url_path . 'logo.' . $ext;
		}
		$view->logo_url = $logo_file;
		$view->survey = $survey;

		$view->verify_url = $verify_url;
		$view->lang_edit = $lang_cd;
		$view->survey_id = $survey_id;
		$view->scene_cd = $scene_cd;
		$view->item_description_ja = $item_description_ja;
		$view->post = $post;
		$view->config = $config;
		$view->message = $message;
		//$view->btn_select = array(''=>"-") + $config_button;
		$view->url_lang_cd = array(''=>"-") + $this->_model->get_code('02');
		$view->skillbox = $this->_skill_box();
		//$view->skills = $skills;
		//$view->permission = $permission;
		$view->item_div = $item_div;
		$view->item = $item;
		$view->translate_from_lang = $translate_from_lang;
		$view->debug = $scene_path;

		// locale
		$view->preview_title = I18n::get('survey.index.label.content', $lang_cd);
		$view->preview_survey_period = I18n::get('survey.index.label.period', $lang_cd);
		$view->preview_input_time_cost_label = I18n::get('survey.index.label.duration', $lang_cd);
		$view->preview_input_time_cost_value = str_replace('{duration}', $survey->duration, I18n::get('survey.index.label.duration.content', $lang_cd));
		// customize preview-notes remark
		$remark_msg_cd = 'survey_remark_text';
		$messages = $this->_model->get_service_msg($this->_bot_id, $remark_msg_cd);
		if ( $messages && isset( $messages['survey_remark_text'] ) && $messages['survey_remark_text'] && 
			isset( $messages['survey_remark_text'][$lang_cd] ) && $messages['survey_remark_text'][$lang_cd] && $messages['survey_remark_text'][$lang_cd]['content'] ) {
			$survey_remark_text_template = $messages['survey_remark_text'][$lang_cd]['content'];
			$view->preview_notes = str_replace("{bot_name}", $this->_bot->bot_name, $survey_remark_text_template);
		} else {
			$view->preview_notes = str_replace("{bot_name}", $this->_bot->bot_name, __('survey.index.label.remark'));
		}

		$this->template->content = $view;
		$this->_page_navi('[{"f":"50","url":"/adminsurvey/surveys"}, {"f":"5002"}]');
	}

	public function action_surveyentry()
	{
		$errors = NULL;
		$message = '';
		$post = NULL;
		$isEdit = false;
		$item_div = 8;

		if ($this->request->post()){
			$post = $this->request->post();
			$survey_id = $post['survey_id'];
			$lang_cd = $post['lang_cd'];
			if ($this->_page_action == 'translate') {
				if ($lang_cd != NULL) {
					$entry = ORM::factory('surveyentry')->where('survey_id', '=', $survey_id)->where('lang_cd', '=', $post['translate_from_lang'])->find_all();
					DB::delete('t_survey_entry')->where('survey_id', '=', $survey_id)->where('lang_cd', '=', $lang_cd)->execute();
					foreach($entry as $f) {
						$orm = ORM::factory('surveyentry');
						$orm->survey_id = $survey_id;
						$orm->lang_cd = $lang_cd;
						$orm->no = $f->no;
						$orm->title = $this->_model->translate($f->title, $lang_cd);
						$orm->entry_type_cd = $f->entry_type_cd;
						$orm->required = $f->required;
						$input_rules = json_decode($f->input_rules, true);
						if (is_array($input_rules)) {
							foreach ($input_rules as $key => $value) {
								if ($key === 'mtx_x' || $key === 'mtx_y') {
									if (is_array($value)) {
										foreach ($value as $i => $v) {
											if (isset($v['label'])) {
												$input_rules[$key][$i]['label'] = $this->_model->translate($v['label'], $lang_cd);
											} else {
												$input_rules[$key][$i] = $this->_model->translate($v, $lang_cd);
											}
										}
									}
								}
							}
							$orm->input_rules = json_encode($input_rules, JSON_UNESCAPED_UNICODE);
						} else {
							$orm->input_rules = $f->input_rules;
						}
						$entry_data = json_decode($f->entry_data, true);
						$other_array = ['ja' => 'その他', 'en' => 'Other', 'cn' => '其他', 'tw' => '其他', 'kr' => '그 외'];
						if (is_array($entry_data)) {
							for($i=0; $i<count($entry_data); $i++) {
								if (is_array($entry_data[$i])) {
									foreach($entry_data[$i] as $k=>$v) {
										if ($k === 'label' || $k === 'title') {
											if (strstr($v, "...") && !strstr($v, "*...")) {
												$entry_data[$i][$k] = $other_array[$lang_cd] . "...";
											} elseif (strstr($v, "*...")) {
												$entry_data[$i][$k] = $other_array[$lang_cd] . "*...";
											} else {
												$entry_data[$i][$k] = $this->_model->translate($v, $lang_cd);
											}
										}
									}
								}
								else {
									if (strstr($entry_data[$i], "...") && !strstr($entry_data[$i], "*...")) {
										$entry_data[$i] = $other_array[$lang_cd] . "...";
									} elseif (strstr($entry_data[$i], "*...")) {
										$entry_data[$i] = $other_array[$lang_cd] . "*...";
									} else {
										$entry_data[$i] = $this->_model->translate($entry_data[$i], $lang_cd);
									}
								}
							}
							$orm->entry_data = json_encode($entry_data, JSON_UNESCAPED_UNICODE);
						}
						else {
							$orm->entry_data = $f->entry_data;
						}
						$orm->next_page = $f->next_page;
						$orm->save();
					}
				}
				$sections = ORM::factory('surveysection')->where('survey_id', '=', $survey_id)->where('lang_cd', '=', $post['translate_from_lang'])->find_all();
				DB::delete('t_survey_section')->where('survey_id', '=', $survey_id)->where('lang_cd', '=', $lang_cd)->execute();
				foreach($sections as $section) {
					$orm = ORM::factory('surveysection');
					$orm->survey_id = $survey_id;
					$orm->no = $section->no;
					$orm->lang_cd = $lang_cd;
					$orm->title = $this->_model->translate($section->title, $lang_cd);
					$orm->entries = $section->entries;
					$orm->sort_no = $section->sort_no;
					$orm->save();
				}
				$branchs = ORM::factory('surveybranch')->where('survey_id', '=', $survey_id)->where('lang_cd', '=', $post['translate_from_lang'])->find_all();
				DB::delete('t_survey_branch')->where('survey_id', '=', $survey_id)->where('lang_cd', '=', $lang_cd)->execute();
				foreach($branchs as $branch) {
					$orm = ORM::factory('surveybranch');
					$orm->survey_id = $survey_id;
					$orm->no = $branch->no;
					$orm->lang_cd = $lang_cd;
					$orm->title = $this->_model->translate($branch->title, $lang_cd);
					$orm->type = $branch->type;
					$orm->dest_no = $branch->dest_no;
					$conditions = json_decode($branch->conditions, true);
					if (is_array($conditions)) {
						for($i=0; $i<count($conditions); $i++) {
							foreach($conditions[$i] as $k=>$v) {
								if ($k == 'value') $conditions[$i][$k] = $this->_model->translate($v, $lang_cd);
							}
						}
						$orm->conditions = json_encode($conditions, JSON_UNESCAPED_UNICODE);
					}
					else {
						$orm->conditions = $branch->conditions;
					}
					$orm->save();
				}
				$orm = ORM::factory('survey', $survey_id);
				$orm->upd_user = $this->_user->user_id;
				$orm->upd_time = date('Y-m-d H:i:s');
				$orm->save();	
				$this->redirect($this->_action_path . 'surveyentry?id=' . $survey_id . '&lang=' . $lang_cd);
				return;
			}
			$entries = json_decode($post['survey_entries'], true);
			$sections = json_decode($post['survey_sections'], true);
			$branchs = json_decode($post['survey_branchs'], true);

			DB::delete('t_survey_entry')->where('survey_id', '=', $survey_id)->where('lang_cd', '=', $lang_cd)->execute();
			foreach($entries as $entry) {
				$orm = ORM::factory('surveyentry');
				$orm->survey_id = $survey_id;
				$orm->no = $entry['no'];
				$orm->lang_cd = $lang_cd;
				$orm->title = $entry['title'];
				$orm->entry_type_cd = $entry['entry_type_cd'];
				$orm->required = $entry['required'];
				$orm->input_rules = $entry['input_rules'];
				if(count($entry['entry_data']) > 0 ) {
					if (is_array($entry['entry_data'])) {
						$rules = json_decode($entry['input_rules'], true);
						if (is_array($rules) && array_key_exists('with_picture', $rules)) {
							for($m=1; $m<=count($entry['entry_data']); $m++) {
								$file_key = 'file_' . $entry['no'] . '_' . $m;
								if (array_key_exists($file_key, $post)) {
									$url = $this->_aws_model->put_base64_file($this->_bot_id, $post[$file_key], $entry['no'] . '_' . $m, 'survey/' . $survey_id . '/entry');
									if ($url != null) {
										$entry['entry_data'][$m - 1]['image'] = $url;
										$this->_aws_model->refresh_url($url);
									}
								}
							}
						}
						$orm->entry_data = json_encode($entry['entry_data'],JSON_UNESCAPED_UNICODE);
					}
					else {
						$orm->entry_data = $entry['entry_data'];
					}
				}
				$orm->next_page = $entry['next_page'];
				$orm->save();
			}
			DB::delete('t_survey_section')->where('survey_id', '=', $survey_id)->where('lang_cd', '=', $lang_cd)->execute();
			foreach($sections as $section) {
				$orm = ORM::factory('surveysection');
				$orm->survey_id = $survey_id;
				$orm->no = $section['no'];
				$orm->lang_cd = $lang_cd;
				$orm->title = $section['title'];
				$orm->entries = $section['entries'];
				$orm->sort_no = $section['sort_no'];
				$orm->save();
			}
			DB::delete('t_survey_branch')->where('survey_id', '=', $survey_id)->where('lang_cd', '=', $lang_cd)->execute();
			foreach($branchs as $branch) {
				$orm = ORM::factory('surveybranch');
				$orm->survey_id = $survey_id;
				$orm->lang_cd = $lang_cd;
				$orm->no = $branch['no'];
				$orm->title = $branch['title'];
				$orm->type = $branch['type'];
				$orm->dest_no = $branch['dest_no'];
				if (is_array($branch['conditions'])){
					$orm->conditions = json_encode($branch['conditions'],JSON_UNESCAPED_UNICODE);
				}
				else {
					$orm->conditions = $branch['conditions'];
				}
				$orm->save();
			}
			
			$orm = ORM::factory('survey', $survey_id);
			$orm->present = $post['coupon_setting'];
			$orm->save();
			
			//$this->_model->update_survey_entry_count($survey_id);
			$this->redirect($this->_action_path . 'surveyentry?id=' . $survey_id . '&lang=' . $lang_cd);
		}
		else {
			$lang_cd = $this->request->query('lang', NULL);
			$survey_id = $this->request->query('id', NULL);
			if ($lang_cd == NULL) {
				$support_lang = ORM::factory('survey', $survey_id)->support_lang_cd;
				if (!empty($support_lang)) {
					$langs = explode(',', $support_lang);
	
					if (count($langs) > 0) $lang_cd = $langs[0];
					$this->redirect("/adminsurvey/surveyentry?id=$survey_id&lang=$lang_cd");
				}
				if ($lang_cd == NULL) $this->redirect('/adminsurvey/survey?id=' . $survey_id);
			}
			if ($lang_cd != NULL) {
				$support_lang = ORM::factory('survey', $survey_id)->support_lang_cd;
				if (!empty($support_lang)) {
					$langs = explode(',', $support_lang);
					if (!in_array($lang_cd, $langs)) {
						$this->redirect('/adminsurvey/survey?id=' . $survey_id);
					}
				}
			}
		}

		$permission = $this->_item_permission_check($survey_id);
		if ($permission < 0) {
			$this->redirect('/adminsurvey/surveys');
		}

		$coupons = [];//''=>'無し'
		$new_coupons = ORM::factory('coupon')->where('bot_id', '=', $this->_bot_id)->where('delete_flg', '=', 0)->find_all();
		foreach($new_coupons as $nc) {
			if ($nc->end_date != '' && $nc->end_date < date('Y-m-d')) continue;
			if ($nc->coupon_name == '') {
				$coupons[$nc->coupon_id] = __('admin.common.label.untitled');
			} else {
				$coupons[$nc->coupon_id] = $nc->coupon_name;
			}
		}

		$view = View::factory ($this->_view_path . 'surveyentry');
		$view->coupons = $coupons;
		
		$menu = View::factory($this->_view_path . 'surveymenu');
		$menu->survey_id = $survey_id;
		$menu->admin_path = $this->_model->get_env('admin_url') . "admin";
		$survey = ORM::factory('survey', $survey_id);
		$item_description = ORM::factory('surveydescription')->where('survey_id', '=', $survey_id)->where('lang_cd', '=', $lang_cd)->find();
		if (isset($item_description->survey_id)) {
			$view->survey_title =  $item_description->title;
			$view->survey_description = $item_description->description;
		}

		$item = ORM::factory('survey', $survey_id);
		$view->user_template_flg = $this->_model->get_survey_mail_template($survey_id);
		$view->lang_display = $item->support_lang_cd;
		$view->menu = $menu;
		$view->survey = $item;
		$view->survey_id = $survey_id;
		$view->scene_cd = $this->_bot->facility_cd;
		$view->post = $post;
		$view->entry_type_list = $this->_codes['10'];
		$view->message = $message;
		$view->entries = json_encode($this->_model->get_survey_entries($survey_id, $lang_cd));
		$view->sections = json_encode($this->_model->get_survey_sections($survey_id, $lang_cd));
		$view->branchs = json_encode($this->_model->get_survey_branchs($survey_id, $lang_cd));
		$view->present = $this->_survey_model->format_coupons($item->present);

		$template_survey_id = $this->_bot_id * 1000;
		$view->template_entries = json_encode(array_merge($this->_model->get_survey_entries(1, $lang_cd), $this->_model->get_survey_entries($template_survey_id, $lang_cd)), JSON_UNESCAPED_UNICODE);
		$view->lang_cd = $lang_cd;
		$survey = ORM::factory('survey', $survey_id);
		if ($survey->scene_cd != NULL) {
			$verify_url = $this->_model->get_env('survey_url') . '?f=' . $survey->scene_cd . '&id=' . $survey->survey_cd . '&lang_cd=' . $lang_cd;
		}
		else {
			$verify_url = $this->_model->get_env('survey_url') . '?f=' . $this->_bot->facility_cd . '&id=' . $survey->survey_cd . '&lang_cd=' . $lang_cd;
		}
		
		// 多言語表示OFFでその言語の検証できるようにtokenをつける
		$display = ORM::factory('itemdisplay')->where('item_id', '=', $survey_id)->where('item_div', '=', $item_div)->find();
		$display_lang_arr = explode(',', $display->lang_display);
		if (!in_array($lang_cd, $display_lang_arr)) {
			$token_payload = [];
			$token_payload['data'] = json_encode(["survey_id"=>$survey_id,"lang_cd"=>$lang_cd,"bot_id"=>$this->_bot_id]);
			$verify_url = $verify_url. '&eval=' . $this->_model->get_new_token($token_payload);
		}
		
		$view->verify_url = $verify_url;
		$lang_entries = ORM::factory('surveyentry')->where('survey_id', '=', $survey_id)->find_all();
		$translate_from_lang = [];
		foreach($lang_entries as $de) {
			if ($de->lang_cd != $lang_cd) {
				if (!array_key_exists($de->lang_cd, $translate_from_lang)) $translate_from_lang[$de->lang_cd] = $this->_codes['02'][$de->lang_cd];
			}
		}
		$view->translate_from_lang = $translate_from_lang;
		
		// 「ファイルアップロード」で利用できる拡張子
		$support_image_type = ['jpg', 'jpeg', 'png', 'svg', 'gif', 'webp'];
		$extensions = $this->_model->formatExtensions($support_image_type);
		$view->extensions = $extensions;
		$view->upload_limit_size = '5';  // 5MBまで

		$this->template->content = $view;
		$this->_page_navi('[{"f":"50","url":"/adminsurvey/surveys"}, {"f":"5003"}]');
	}

	public function action_surveyresult()
	{
		$errors = NULL;
		$message = NULL;
		$post = [];
		$survey_id = NULL;
		$item_div = 8;
		$has_emotion_analytics = false;
		$user_access_limit_survey = [];
		$user_access_limit_entry = [];
		$full_size = '';
		$surveyFilters = [];
		if ($this->_user->role_cd == '73') {
			$orms = ORM::factory('botmsg')->where('bot_id', '=', $this->_bot_id)->where('delete_flg','=',0)->where('msg_cd', 'LIKE', 'survey_user_access_limit_%')->find_all();
			foreach($orms as $orm) {
				$data = $this->_model->get_bot_tpl_message($this->_bot_id, $orm->msg_cd, 'ja', true);
				foreach($data['user_access_result_limit'] as $li) {
					if (in_array($this->_user->user_id, $li['users'])) {
						$survey = ORM::factory('survey', $data['survey_id']);
						$user_access_limit_survey[$data['survey_id']] = $survey->survey_name;
					}
				}
			}
		}

		if ($this->request->post()) {
			$post = $this->request->post();
			$survey_id = $post['survey_id'];
			$survey_result_tages = $this->_get_session('survey_result_tags', []);
			if ($post['act'] == 'delete') {
				if (isset($survey_result_tages[$post['filter_entry_delete']])) {
					$key = array_search($post['filter_entry_answer_delete'], $survey_result_tages[$post['filter_entry_delete']]);
					unset($survey_result_tages[$post['filter_entry_delete']][$key]);
					if (count($survey_result_tages[$post['filter_entry_delete']]) == 0) unset($survey_result_tages[$post['filter_entry_delete']]);
					$this->_set_session('survey_result_tags', $survey_result_tages);
				}
			}
			else if ($post['act'] == 'clear') {
				$survey_result_tages = [];
				$this->_set_session('survey_result_tags', $survey_result_tages);
			}
			else if ($post['act'] == 'result_delete') {
				$this->_delete_surveyresult($post['result_no']);
			}
			else {
				if ($post['survey_entry'] != '') {
					if (isset($survey_result_tages[$post['survey_entry']])) {
						if (!in_array($post['survey_entry_answer'], $survey_result_tages[$post['survey_entry']])) {
							$survey_result_tages[$post['survey_entry']][] = $post['survey_entry_answer'];
						}
					}
					else {
						$survey_result_tages[$post['survey_entry']] = [$post['survey_entry_answer']];
					}
					$this->_set_session('survey_result_tags', $survey_result_tages);
				}

				if ($post['branches'] != '') {
					$surveyFilters = json_decode($post['branches'], JSON_UNESCAPED_UNICODE);
				}
			}
			$post['survey_entry'] = '';
			$post['survey_entry_answer'] = '';

			Session::instance()->set('surveyresult_start_date', $post['start_date']);
			Session::instance()->set('surveyresult_end_date', $post['end_date']);
			Session::instance()->set('surveyresult_lang_cd', $post['lang_cd']);

			/*
			Session::instance()->set('surveyresult_start_date', $post['start_date']);
			Session::instance()->set('surveyresult_end_date', $post['end_date']);
			Session::instance()->set('surveyresult_lang_cd', $post['lang_cd']);
			Session::instance()->set('surveyresult_sns_type_cd', $post['sns_type_cd']);
			Session::instance()->set('surveyresult_member_name', $post['member_name']);
			Session::instance()->set('surveyresult_member_no', $post['member_no']);
			*/

		}
		else {
			$survey_id = $this->request->query('id', NULL);
			if ($this->_user->role_cd == '73') {
				if ($survey_id == NULL) $survey_id = array_key_first($user_access_limit_survey);
			}
			$permission = $this->_item_permission_check($survey_id);
			if ($permission < 0) {
				$this->redirect('/adminsurvey/surveys');
			}
			$post['type'] = $this->request->query('type', NULL);
			if ($post['type'] == NULL) {
				$post['type'] = 'total';
				$survey_result_tages = [];
				$this->_set_session('survey_result_tags', $survey_result_tages);
			}
			else {
				$survey_result_tages = $this->_get_session('survey_result_tags', []);
			}
			$post['lang_cd'] = Session::instance()->get('surveyresult_lang_cd', NULL);
			if ($post['lang_cd'] == NULL) {
				$post['lang_cd'] = '';
			}
			$post['sns_type_cd'] = Session::instance()->get('surveyresult_sns_type_cd', NULL);
			if ($post['sns_type_cd'] == NULL) {
				$post['sns_type_cd'] = '';
			}
			$post['member_name'] = Session::instance()->get('surveyresult_member_name', NULL);
			if ($post['member_name'] == NULL) {
				$post['member_name'] = '';
			}
			$post['member_no'] = Session::instance()->get('surveyresult_member_no', NULL);
			if ($post['member_no'] == NULL) {
				$post['member_no'] = '';
			}
			$survey = ORM::factory('survey', $survey_id);
			$post['start_date'] = Session::instance()->get('surveyresult_start_date', NULL);
			if ($post['start_date'] == NULL) {
				if ($survey->start_date && $survey->end_date) {
					$post['start_date'] = $survey->start_date;
				} else {
					$start_date = new DateTime('first day of this month');
					$post['start_date'] = $start_date->format('Y-m-d');
				}
			}
			$post['end_date'] = Session::instance()->get('surveyresult_end_date', NULL);
			if ($post['end_date'] == NULL) {
				if ($survey->start_date && $survey->end_date) {
					$post['end_date'] = $survey->end_date;
				} else {
					$end_date = new DateTime('last day of this month');
					$post['end_date'] = $end_date->format('Y-m-d');
				}
			}

			$post['survey_entry'] = '';

			$survey_support_languages = [];
			if ($survey->support_lang_cd) {
				$support_langs = explode(',', $survey->support_lang_cd);
				foreach($support_langs as $lang) {
					$survey_support_languages[$lang] = $this->_bot_lang[$lang];
				}
			}
			$post['lang_cd'] = key($survey_support_languages);
		}

		$post['csv_type_cd'] = '1';

		$survey = ORM::factory('survey', $survey_id);

		if (!isset(($survey_support_languages))) {
			$survey_support_languages = [];
			if ($survey->support_lang_cd) {
				$support_langs = explode(',', $survey->support_lang_cd);
				foreach($support_langs as $lang) {
					$survey_support_languages[$lang] = $this->_bot_lang[$lang];
				}
			}
		}

		if ($survey->user_in_charge != NULL) {
			$user_in_charge_array = explode(',', $survey->user_in_charge);
			$user_is_in_charge = in_array($this->_user->user_id, $user_in_charge_array);
		} else {
			$user_is_in_charge = true;
		}
		$mask_privacy = true;
		if ($this->_user->privacy_show_flg == 0) {
			if ($this->_user->privacy_self_show_flg == 1 && $user_is_in_charge) {
				$mask_privacy = false;
			}
		} else {
			$mask_privacy = false;
		}

		$page_size = Session::instance()->get('surveyresult_page_size', NULL);
		if ($page_size == NULL) $page_size = 100;
		isset($post['paging'])? $post['paging'] = json_decode($post['paging'], true) : $post['paging'] = ['count'=>0, 'size'=>$page_size, 'page'=>0, 'all'=>1];
		Session::instance()->set('surveyresult_page_size', $post['paging']['size']);

		if ($post['type'] == 'total') $post['paging'] = ['count'=>0, 'size'=>999999, 'page'=>0, 'all'=>1];
		$post['paging']['count'] = $this->_model->get_survey_result_count($this->_bot_id, $survey_id, $post['start_date'], $post['end_date'], '', '', $post['lang_cd'], '', '');
		// survey result
		$survey_result = $this->_model->get_survey_result_page($this->_bot_id, $survey_id, $post['start_date'], $post['end_date'], '', '', $post['lang_cd'], '', '', $post['paging']['page']*$post['paging']['size'], $post['paging']['size']);
		// survey entry result
		// 20230517 added entry_extra_info in select デン
		if (count($survey_result) == 0) {
			$start = 0;
			$end = 0;
		}
		else {
			$end = $survey_result[0]['id'];
			$start = $survey_result[count($survey_result) - 1]['id'];
		}
		$survey_entry_result = $this->_model->get_survey_entry_result_page($this->_bot_id, $survey_id, $start, $end, $post['lang_cd']);
		// survey entry
		$survey_entry = ORM::factory('surveyentry')->where('survey_id', '=', $survey_id)->where('lang_cd', '=', $post['lang_cd'])->order_by('no')->find_all();

		$survey_entry_dict = [''=>'-'];
		$survey_entry_type = [];
		$survey_entry_data = [];
		foreach($survey_entry as $entry) {
			if ($entry->entry_type_cd == 'opt' || $entry->entry_type_cd == 'sel' || $entry->entry_type_cd == 'chk') {
				$survey_entry_dict[$entry->no] = strip_tags($entry->title);
				$survey_entry_type[$entry->no] = $entry->entry_type_cd;
			}
			if ($entry->entry_data != '') {
				if ($entry->entry_type_cd === 'scr') {
					$input_rules = json_decode($entry->input_rules, true);
					$select = $input_rules['select'];
					$scale = $input_rules['scale'];
					if ($scale !== 'nps' && $select === 'opt') {
						$entry_data = json_decode($entry->entry_data, true);
						usort($entry_data, function($a, $b){ return ($b['score'] - $a['score']);});
						$survey_entry_data[$entry->no] = $entry_data;
					}
				} else {
					$entry_data = json_decode($entry->entry_data, true);
					$survey_entry_data[$entry->no] = $entry_data;
				}
			}
		}

		// filter
		if ($this->_user->role_cd == '73') {
			$user_access_limit_entry = $this->_model->get_user_access_limit($this->_bot_id, $this->_user_id, $survey_id, 'survey');
			if (isset($user_access_limit_entry[$post['lang_cd']])) {
				$user_access_limit_entry = $user_access_limit_entry[$post['lang_cd']];
			}
			else {
				$user_access_limit_entry = $user_access_limit_entry['ja'];
			}
			foreach($user_access_limit_entry as $k=>$v) {
				unset($survey_entry_dict[$k]);
				if (is_array($v)) {
					if (isset($survey_result_tages[$k])) {
						$survey_result_tages[$k] = array_unique(array_merge($v, $survey_result_tages[$k]));
					}
					else {
						$survey_result_tages[$k] = $v;
					}
				}
				else {
					if (isset($survey_result_tages[$k])) {
						$survey_result_tages[$k] = array_unique(array_merge([$v], $survey_result_tages[$k]));
					}
					else {
						$survey_result_tages[$k] = [$v];
					}
				}
			}
		}
		
		$this->_model->filter_survey_result($survey_result, $survey_entry_result, $survey_entry_type, $survey_result_tages);
		$survey_branch_entries = $this->_model->get_survey_filter_entries(survey_id: $survey_id);	//TODO should it be just survey_entries?
		list($survey_result, $survey_entry_result) = $this->_model->modal_filter_survey_result($survey_result, $survey_entry_result, $survey_branch_entries[$post['lang_cd']], $surveyFilters);

		$post['paging']['filter_count'] = count($survey_result);
		
		// result report
		$survey_entries = [];
		foreach($survey_entry as $entry) {
			$entries = [];
			if ($this->_is_choice($entry->entry_type_cd)) {
				$entry_data = json_decode($entry->entry_data, true);
				if (!is_array($entry_data)) {
					$entry_data = $this->_model->get_code_div_kv($entry->entry_data, $post['lang_cd']);
					$i=0;
					foreach($entry_data as $k=>$v) {
						$entries[strval($i+1)] = ['title'=>$v, 'count'=>0];
						$i++;
					}
				}
				else {
					foreach($entry_data as $k=>$v) {
						if (is_array($v)) {
							$entries[strval($k+1)] = ['title'=>$v['title'], 'count'=>0];
						}
						else {
							$entries[strval($k+1)] = ['title'=>$v, 'count'=>0];
						}
					}
				}
			}
			$is_emotion_analytics = $this->_is_emotion_analytics($entry);
			if (!$has_emotion_analytics) {
				$has_emotion_analytics = $is_emotion_analytics;
			}
			$survey_entries[strval($entry->no)] = [
				'title'=>$entry->title, 
				'entry_type_cd'=>$entry->entry_type_cd, 
				'input_rules'=>$entry->input_rules, 
				'required'=>$entry->required, 
				'entry_count'=>$entries, 
				'emotion_analytics'=>$is_emotion_analytics
			];

			if ($entry->entry_type_cd === 'mtx') {
				$input_rules = json_decode($entry->input_rules, true);
				if (array_key_exists('mtx_x', $input_rules) && array_key_exists('mtx_y', $input_rules)) {
					$mtx_x_count = [];
					foreach ($input_rules['mtx_x'] as $k => $v) {
						$mtx_x_count[$k+1] = 0;
					}
					foreach ($input_rules['mtx_y'] as $k => $v) {
						$survey_entries[strval($entry->no)]['mtx_count'][$k+1] = $mtx_x_count;
					}
				}
			}

			if ($entry->entry_type_cd === 'scr') {
				$input_rules = json_decode($entry->input_rules, true);
				$select = $input_rules['select'];
				$scale = $input_rules['scale'];
				if ($select === 'opt') {
					// 単一選択
					$entries = [];
					if ($scale === 'nps') {
						$entries = [
							"10" => ['title' => '10','count' => 0],
							"9" => ['title' => '9','count' => 0],
							"8" => ['title' => '8','count' => 0],
							"7" => ['title' => '7','count' => 0],
							"6" => ['title' => '6','count' => 0],
							"5" => ['title' => '5','count' => 0],
							"4" => ['title' => '4','count' => 0],
							"3" => ['title' => '3','count' => 0],
							"2" => ['title' => '2','count' => 0],
							"1" => ['title' => '1','count' => 0],
							"0" => ['title' => '0','count' => 0],
						];
					} else {
						$entry_data = json_decode($entry->entry_data, true);
						usort($entry_data, function($a, $b){ return ($b['score'] - $a['score']);});
						if (is_array($entry_data)) {
							foreach($entry_data as $k=>$v) {
								$entries[strval($v['score'])] = ['title'=>$v['label'], 'count'=>0, 'score'=>$v['score']];
							}
							$survey_entries[strval($entry->no)]['entry_data'] = $entry_data;
						}
					}
					$survey_entries[strval($entry->no)]['entry_count'] = $entries;
					$survey_entries[strval($entry->no)]['select'] = $select;
					$survey_entries[strval($entry->no)]['scale'] = $scale;
				} else if ($select === 'mtx') {
					//　マトリクス
					if (array_key_exists('mtx_x', $input_rules) && array_key_exists('mtx_y', $input_rules)) {
						$mtx_x_count = [];
						if ($scale === 'nps') {
							$mtx_x_count[10] = 0;
							$mtx_x_count[9] = 0;
							$mtx_x_count[8] = 0;
							$mtx_x_count[7] = 0;
							$mtx_x_count[6] = 0;
							$mtx_x_count[5] = 0;
							$mtx_x_count[4] = 0;
							$mtx_x_count[3] = 0;
							$mtx_x_count[2] = 0;
							$mtx_x_count[1] = 0;
							$mtx_x_count[0] = 0;
						} else {
							$mtx_x = $input_rules['mtx_x'];
							usort($mtx_x, function($a, $b){ return($b['score'] - $a['score']); });
							foreach ($mtx_x as $k => $v) {
								$mtx_x_count[$v['score']] = 0;
							}
						}
						foreach ($input_rules['mtx_y'] as $k => $v) {
							$survey_entries[strval($entry->no)]['mtx_count'][$k+1] = $mtx_x_count;
						}
					}
					$survey_entries[strval($entry->no)]['select'] = $select;
					$survey_entries[strval($entry->no)]['scale'] = $scale;
				}
			}
		}

		// result detail
		$survey_result_entry_dict = [];

		foreach($survey_entry_result as $re) {
			if (!array_key_exists(strval($re['no']), $survey_entries)) continue;

			if ($survey_entries[strval($re['no'])]['entry_type_cd'] === 'mtx') {
				$res = json_decode($re['entry_result'], true);
				foreach($res as $mtx_x => $answers) {
					if (array_key_exists($mtx_x, $survey_entries[$re['no']]['mtx_count'])) {
						foreach ($answers as $k => $v) {
							$survey_entries[$re['no']]['mtx_count'][$mtx_x][$v] = $survey_entries[strval($re['no'])]['mtx_count'][$mtx_x][$v]+1;
						}
					}
				}
			}

			$emotion_analytics = null;
			if ($re['entry_extra_info'] != null) {
				$emotion_analytics = $this->_get_emotion_analytics($re['entry_extra_info']);
			}

			if ($this->_is_choice($survey_entries[strval($re['no'])]['entry_type_cd'])) {
				$res = explode(',', $re['entry_result']);
				foreach($res as $r) {
					if (array_key_exists($r, $survey_entries[$re['no']]['entry_count']))
					$survey_entries[$re['no']]['entry_count'][$r]['count'] = $survey_entries[strval($re['no'])]['entry_count'][$r]['count']+1;
				}
			}
			else if ($survey_entries[strval($re['no'])]['entry_type_cd'] === 'scr') {
				if ($survey_entries[strval($re['no'])]['select'] === 'opt') {
					$res = explode(',', $re['entry_result']);
					foreach($res as $r) {
						if (array_key_exists($r, $survey_entries[$re['no']]['entry_count'])) {
							$survey_entries[$re['no']]['entry_count'][$r]['count'] = $survey_entries[strval($re['no'])]['entry_count'][$r]['count']+1;
						}
					}
				} else if ($survey_entries[strval($re['no'])]['select'] === 'mtx') {
					$res = json_decode($re['entry_result'], true);

					foreach ($res as $mtx_x => $answers) {
						if (array_key_exists($mtx_x, $survey_entries[$re['no']]['mtx_count'])) {
							foreach ($answers as $k => $v) {
								$survey_entries[$re['no']]['mtx_count'][$mtx_x][$v] = $survey_entries[strval($re['no'])]['mtx_count'][$mtx_x][$v]+1;
							}
						}
					}
					// 質問に回答する人数の計算
					if (!isset($survey_entries[$re['no']]['result_count'])) {
						$survey_entries[$re['no']]['result_count'] = 1;
					} else {
						$survey_entries[$re['no']]['result_count'] = intval($survey_entries[$re['no']]['result_count']) + 1;
					}
				}
			}
			else {
				if ($re['link_id'] == null) {
					$member_id = $re['member_id'];
				}
				else {
					$member_id = $re['link_id'];
				}
				if ($mask_privacy) {
					$input_rules = json_decode($survey_entries[strval($re['no'])]['input_rules'], true);
					$input_rules = json_decode($survey_entries[strval($re['no'])]['input_rules'], true);
					if (is_array($input_rules) && array_key_exists('type', $input_rules)) {
						if (
							$input_rules['type'] == 'tel' || $input_rules['type'] == 'mail' || $input_rules['type'] == 'postcode' || $input_rules['type'] == 'name' ||
							($input_rules['type'] == 'text' && $input_rules['privacy_masking'] == '1') || $input_rules['type'] == 'address'
						) {
							$re['entry_result'] = $this->_model->mask_string($re['entry_result'], 5, '*');
							$re['entry_data'] = $this->_model->mask_string($re['entry_data'], 5, '*');
						}
					}
				}
				if ($survey_entries[strval($re['no'])]['entry_type_cd'] == 'mtx') {
					$re['entry_data'] = str_replace('"', '', str_replace('}', '', str_replace('{', '', $re['entry_data'])));
				}
				
				$answers = ['uid'=>$member_id, 'answer'=>$re['entry_data']];
				if ($emotion_analytics != null) {
					$answers['emotion_analytics'] = $emotion_analytics;
				}
				$survey_entries[$re['no']]['entry_count'][] = $answers;
			}
			
			$re['emotion_data'] = $emotion_analytics;
			// detail
			if (array_key_exists($re['result_id'], $survey_result_entry_dict)) {
				$survey_result_entry_dict[$re['result_id']][] = $re;
			}
			else {
				$survey_result_entry_dict[$re['result_id']] = [];
				$survey_result_entry_dict[$re['result_id']][] = $re;
			}
			
		}

		$sections = ORM::factory('surveysection')->where('survey_id', '=', $survey_id)->where('lang_cd', '=', $post['lang_cd'])->where('delete_flg', '=', 0)->order_by('sort_no')->find_all();
		$section = [];
		foreach($sections as $s) {
			$section = array_merge($section, explode(',', $s->entries));
		}

		$js_colors_template = json_decode($this->_model->get_bot_tpl_message($this->_bot_id, 'colors_template_surveyresult', "ja"),true);

		$view = View::factory ($this->_view_path . 'surveyresult');
		$menu = View::factory($this->_view_path . 'surveymenu');
		$menu->survey_id = $survey_id;
		$menu->admin_path = $this->_model->get_env('admin_url') . "admin";
		$menu->coupon_id = $survey->present;
		$view->menu = $menu;
		$view->js_colors_template = $js_colors_template;
		$lang_cd_list = array(''=>"言語") + $this->_bot_lang;
		$view->lang_cd_list = $lang_cd_list;
		$view->survey_support_languages = $survey_support_languages;
		$sns_type_cd_list = array(''=>"SNS") + $this->_codes['16'];
		$view->sns_type_cd_list= $sns_type_cd_list;
		$view->post = $post;
		$view->survey_id = $survey_id;
		$view->survey = $survey;
		$view->survey_result = $survey_result;
		$view->survey_result_entry_dict = $survey_result_entry_dict;

		$view->survey_branch_entries = $survey_branch_entries;
		$view->surveyFilters = count($surveyFilters) > 0 ? json_encode($surveyFilters, JSON_UNESCAPED_UNICODE) : '';

		$view->survey_entry = $survey_entry;
		$view->survey_entry_dict = $survey_entry_dict;
		$view->survey_entry_data = $survey_entry_data;
		$view->survey_result_total = $survey_entries;
		$view->survey_result_tages = $survey_result_tages;
		$view->has_emotion_analytics = $has_emotion_analytics;
		$view->csv_type = ['1'=>'複数回答一括','2'=>'複数回答分割'];
		$view->section = $section;
		$view->user_access_limit_survey = $user_access_limit_survey;
		$view->user_access_limit_entry = $user_access_limit_entry;
		$view->full_size = $full_size;
		$this->template->content = $view;
		$this->_page_navi('[{"f":"50","url":"/adminsurvey/surveys"}, {"f":"5004"}]');
		if ($this->_user->role_cd == '73') {
			$this->_page_navi('[{"f":"50"}, {"f":"5004"}]');
		}
	}

	public function action_surveyreport() {
		$errors = NULL;
		$message = NULL;
		$post = NULL;
		$start_date = NULL;
		$end_date = NULL;
		$survey_id = $this->request->query('id', NULL);
		$type = $this->request->query('type', NULL);

		if ($type == NULL) {
			$type = 'monthly';	
		} 
		if ($survey_id == NULL) {
			$this->redirect('/adminsurvey/surveys');
		}

		if ($this->request->post()) {
			$post = $this->request->post();
			$start_date = $post['start_date'];
			$end_date = $post['end_date'];
			if ($type == 'monthly') {
				$start_date = date('Y-m', strtotime($start_date)) . '-01';
				$days = date('t', strtotime($end_date));
				$end_date = date('Y-m', strtotime($end_date)) . '-' . $days;
			}
		} else {
			$start_date = date('Y-m-d');
			$end_date = date('Y-m-d');
			if ($type == 'monthly') {
				$start_date = date('Y', strtotime($start_date)) . '-01-01';
				$end_date = date('Y', strtotime($end_date)) . '-12-31';
			} else {
				$start_date = date('Y-m', strtotime($start_date)) . '-01';
				$days = date('t', strtotime($end_date));
				$end_date = date('Y-m', strtotime($end_date)) . '-' . $days;
			}
		}

		$survey = ORM::factory('survey', $survey_id);

		// get data
		$survey_model = new Model_Surveymodel();
		$results = $survey_model->get_survey_report($survey_id, $start_date, $end_date, $type);
		// 言語別用
		// $total_result = [];
		// foreach ($results as $result) {
		// 	$period = $type == 'monthly' ? $result['month'] : $result['day'];
		// 	if (!isset($period, $total_result)) {
		// 		$total_result[$period][$result['lang']] = 1;
		// 		$total_result[$period]['total'] = 1;
		// 	} else {
		// 		if (isset($total_result[$period][$result['lang']], $total_result[$period])) {
		// 			$total_result[$period][$result['lang']] += 1;
		// 		} else {
		// 			$total_result[$period][$result['lang']] = 1;
		// 		}
		// 		$total_result[$period]['total'] += 1;
		// 	}
		// }

		$view = View::factory($this->_view_path . 'surveyreport');
		$menu = View::factory($this->_view_path . 'surveymenu');

		$menu->survey_id = $survey_id;
		$view->menu = $menu;
		$view->survey_id = $survey_id;
		$view->type = $type;
		$view->start_date = $start_date;
		$view->end_date = $end_date;
		$view->survey_name = $survey->survey_name;
		$view->results = $results;
		$this->template->content = $view;
		$this->_page_navi('[{"f":"50","url":"/adminsurvey/surveys"}, {"f":"5007"}]');
	}

	public function action_surveyreport73() {
		$post = NULL;
		$start_date = NULL;
		$end_date = NULL;
		$survey_id = $this->request->query('id', NULL);
		$type = $this->request->query('type', NULL);

		if ($type == NULL) {
			$type = 'monthly';	
		} 
		if ($survey_id == NULL) {
			$this->redirect('/login');
		}

		if ($this->request->post()) {
			$post = $this->request->post();
			$start_date = $post['start_date'];
			$end_date = $post['end_date'];
			if ($type == 'monthly') {
				$start_date = date('Y-m', strtotime($start_date)) . '-01';
				$days = date('t', strtotime($end_date));
				$end_date = date('Y-m', strtotime($end_date)) . '-' . $days;
			}
		} else {
			$start_date = date('Y-m-d');
			$end_date = date('Y-m-d');
			if ($type == 'monthly') {
				$start_date = date('Y', strtotime($start_date)) . '-01-01';
				$end_date = date('Y', strtotime($end_date)) . '-12-31';
			} else {
				$start_date = date('Y-m', strtotime($start_date)) . '-01';
				$days = date('t', strtotime($end_date));
				$end_date = date('Y-m', strtotime($end_date)) . '-' . $days;
			}
		}

		$survey = ORM::factory('survey', $survey_id);
		$survey_result = $this->_model->get_survey_result($this->_bot_id, $survey_id, $start_date, $end_date, '', '', '', '', '');
		if (count($survey_result) == 0) {
			$start = 0;
			$end = 0;
		}
		else {
			$start = $survey_result[0]['id'];
			$end = $survey_result[count($survey_result) - 1]['id'];
		}
		$survey_entry_result = $this->_model->get_survey_entry_result_page($this->_bot_id, $survey_id, $start, $end, '');
		// survey entry
		$survey_entry = ORM::factory('surveyentry')->where('survey_id', '=', $survey_id)->where('lang_cd', '=', 'ja')->order_by('no')->find_all();

		$survey_entry_type = [];
		foreach($survey_entry as $entry) {
			if ($entry->entry_type_cd == 'opt' || $entry->entry_type_cd == 'sel' || $entry->entry_type_cd == 'chk') {
				$survey_entry_type[$entry->no] = $entry->entry_type_cd;
			}
		}

		// filter
		$survey_result_tages = [];
		if ($this->_user->role_cd == '73') {
			$user_access_limit_entry = $this->_model->get_user_access_limit($this->_bot_id, $this->_user_id, $survey_id, 'survey');
			$survey_result_tages = $user_access_limit_entry['ja'];
		}
		$this->_model->filter_survey_result($survey_result, $survey_entry_result, $survey_entry_type, $survey_result_tages);

		$results = [];
		foreach($survey_result as $r) {
			if ($type == 'monthly') {
				$key = substr($r['end_time'], 0, 7);
			}
			else {
				$key = substr($r['end_time'], 0, 10);
			}
			if (!isset($results[$key])) $results[$key] = 0;
			$results[$key] = $results[$key] + 1;
		}
		ksort($results);
		$total_result = [];
		foreach($results as $k=>$v) {
			$total_result[] = ['time'=>$k, 'total'=>$v];
		}
		$view = View::factory($this->_view_path . 'surveyreport73');
		$menu = View::factory($this->_view_path . 'surveymenu');
		$menu->survey_id = $survey_id;
		$view->menu = $menu;
		$view->survey_id = $survey_id;
		$view->type = $type;
		$view->start_date = $start_date;
		$view->end_date = $end_date;
		$view->survey_name = $survey->survey_name;
		$view->results = $total_result;
		$this->template->content = $view;
		$this->_page_navi('[{"f":"50"}, {"f":"5004"}]');
	}

	private function _is_choice($entry_type_cd) {
		if ($entry_type_cd == 'opt' || $entry_type_cd== 'chk' || $entry_type_cd== 'cha' || $entry_type_cd== 'sel') {
			return true;
		}
		return false;
	}

	private function _is_emotion_analytics($entry) {
		$entry_type_cd = $entry->entry_type_cd;
		if ($entry_type_cd != 'txt' && $entry_type_cd != 'txa') {
			return false;
		}
		$input_rules = json_decode($entry->input_rules);
		if (!isset($input_rules->emotion_analytics)) return false;
		return $input_rules->emotion_analytics == '1';
	}

	private function _get_emotion_analytics($entry_extra_info) {
		$decode_extra_info = json_decode($entry_extra_info);
		$emotion_analytics = [
			'positive' => [
				'texts' => [],
				'position' => null,
				'score' => 0
			],
			'negative' => [
				'texts' => [],
				'position' => null,
				'score' => 0
			],
			'average' => 0
		];
		if ($decode_extra_info->emotion_analytics != null) {
			$analytics = $decode_extra_info->emotion_analytics;
			if ($analytics->success == true) {
				$emotion_analytics['positive']['texts'] = $analytics->positive->texts;
				if (isset($analytics->positive->position)) {
					$emotion_analytics['positive']['position'] = $analytics->positive->position;
				}
				$emotion_analytics['positive']['score'] = $analytics->positive->score;
				$emotion_analytics['negative']['texts'] = $analytics->negative->texts;
				if (isset($analytics->negative->position)) {
					$emotion_analytics['negative']['position'] = $analytics->negative->position;
				}
				$emotion_analytics['negative']['score'] = $analytics->negative->score;
				$emotion_analytics['average'] = $analytics->average;
			}
		}
		return $emotion_analytics;
	}

	private function _delete_surveyresult($result_no) {
		if (!$result_no) {
			return;
		}
		DB::delete('t_survey_result')->where('id', '=', $result_no)->execute();
		DB::delete('t_survey_result_entry')->where('result_id', '=', $result_no)->execute();
	}

	public function action_surveyorders()
	{
		$errors = NULL;
		$message = NULL;
		$post = NULL;
		$last_service_id = 0;
		$class_cd = '';
		$status_cd = '';
		$survey_id = NULL;
		$item_div = 8;

		if ($this->request->post()){
			$post = $this->request->post();
			$survey_id = $post['survey_id'];
			$status_cd = $post['status_cd'];
			$start_date = $post['start_date'];
			$end_date = $post['end_date'];
		}
		else {
			$survey_id = $this->request->query('id', NULL);
			$start_date = new DateTime('first day of this month');
			$start_date = $start_date->format('Y-m-d');
			$end_date = new DateTime('last day of this month');
			$end_date = $end_date->format('Y-m-d');
		}
		$permission = $this->_item_permission_check($survey_id);
		if ($permission < 0) {
			$this->redirect('/adminsurvey/surveys');
		}
		$bot_setting = "30";
		if (array_key_exists('num_request_refresh_period', $this->_bot_setting)) {
			$bot_setting = $this->_bot_setting['num_request_refresh_period'];
		}
		$log_date = str_replace('-', '', date('Y-m-d', strtotime('-' . $bot_setting . ' day')));
		$action = NULL;
		$services = $this->_model->get_survey_orders($this->_bot_id, $status_cd, $start_date, $end_date);
		if (count($services) > 0) {
			$last_service_id = $services[0]['order_id'];
			$this->_set_session('last_order_id', $last_service_id);
		}

		$view = View::factory ($this->_view_path . 'surveyorders');
		$menu = View::factory($this->_view_path . 'couponmenu');
		$menu->survey_id = $survey_id;
		$view->menu = $menu;
		$talkbox = View::factory ('admin/talkbox');
		$talkbox->translate = Session::instance()->get('translate', '0');
		$talkbox->phrase_types = $this->_model->get_code_div_kv($this->_model->get_bot_setting($this->_bot_id, 'div_item_class_6'), 'ja');
		$talkbox->status = $this->_model->get_code('25');
		$view->talkbox = $talkbox;
		$view->survey_id = $survey_id;
		$view->survey = ORM::factory('survey', $survey_id);
		$view->codes = $this->_codes;
		$view->services = $services;
		$view->service_status_array = $this->_model->get_code('45');
		$view->new_flg = 0;
		$view->class_array = array(""=>'すべて') + $this->_model->get_code('25');
		$view->status_array = array(""=>'すべて') + $this->_model->get_code('25');
		$view->status_cd = $status_cd;
		$view->start_date = $start_date;
		$view->end_date = $end_date;
		$this->template->content = $view;
	}

	public function action_coupons() {
		$errors = NULL;
		$message = NULL;
		$post = NULL;
		$item_div = 7;

		if ($this->_bot_setting['flg_next_survey'] == 0 && $this->_bot_setting['flg_ticket'] == 0) $this->redirect('/admin/top');

		if ($this->request->post()) {
			$post = $this->request->post();
			if ($post['act'] == 'newcopy') {
				$coupon_id = $post['coupon_id'];
				$coupon = $this->_copy_coupon($coupon_id);
				$this->redirect($this->_action_path . 'coupon?id=' . $coupon->coupon_id);
			}
		} else {
			// 検索内容デフォルト
			// 分類選択
			$class_cd = $this->request->query('class', NULL);
			$post['user_in_charge'] = Session::instance()->get('surveys_user_in_charge', NULL);
			if ($post['user_in_charge'] === NULL) $post['user_in_charge'] = 0;
			if ($this->_user->auth_all_contents_flg == 0) {
				$post['user_in_charge'] = strval($this->_user->user_id);
			}
			if ($class_cd == NULL) {
				$post['class_cd_cond'] = Session::instance()->get('coupons_class_cd', NULL);
			}
			else {
				$post['class_cd_cond'] = $class_cd;
			}
			if ($post['class_cd_cond'] == NULL) $post['class_cd_cond'] = '';
			// 期間選択
			$post['start_date'] = Session::instance()->get('coupons_start_date', NULL);
			if ($post['start_date'] === NULL) $post['start_date'] = date('Y-m-d');
			$post['end_date'] = Session::instance()->get('coupons_end_date', NULL);
			if ($post['end_date'] === NULL) $post['end_date'] = '';
		}
		Session::instance()->set('coupons_class_cd', $post['class_cd_cond']);
		Session::instance()->set('coupons_start_date', $post['start_date']);
		Session::instance()->set('coupons_end_date', $post['end_date']);

		// 分類情報
		$code_div = $this->_model->get_bot_setting($this->_bot_id, 'div_item_class_' . $item_div);
		$code_div_dict = $this->_model->get_code_div_kv($code_div);
    
		// DBよりデータ取得
		$ret = $this->_model->get_coupons($this->_bot_id, $post['class_cd_cond'], $post['user_in_charge']);
		// クーポン一覧
		$items = $ret[0];
		$items_show = [];
		$items_not_show = [];
		// 発行統計回数
		$results = $ret[1];

		$use_results = $ret[2];

		// 検索条件（はじまり）
		$start_date_cond = '0000-01-01';
		if ($post['start_date'] != NULL) {
			$start_date_cond = $post['start_date'];
		}

		// 検索条件（終わり）
		$end_date_cond= '9999-12-31';
		if ($post['end_date'] != NULL) {
			$end_date_cond = $post['end_date'];
		}
		
		// データを配列に挿入
		for($i=0; $i<count($items); $i++) {
			$item = $items[$i];
			// クーポン情報（はじまり）
			$start_date = '0000-01-01';
			if ($item['start_date'] != NULL) {
				$start_date = $item['start_date'];
			}
			// クーポン情報（終わり）
			$end_date = '9999-12-31';
			if ($item['end_date'] != NULL) {
				$end_date = $item['end_date'];
			}

			// クーポン情報（終わり）が検索条件（はじまり）より前 もしくは クーポン情報（はじまり）が検索条件（終わり）より後
			if ($end_date < $start_date_cond || $start_date > $end_date_cond) {
				$items_not_show[] = $item;
			} else {
				$items_show[] = $item;
			}
		}

		$json_next_survey_setting = $this->_model->get_bot_setting($this->_bot_id, 'json_next_survey_setting');
		$json_next_survey_setting = json_decode($json_next_survey_setting, true);
		if (!array_key_exists('user_in_charge_required', $json_next_survey_setting)) $json_next_survey_setting['user_in_charge_required'] = 0;
		if ($json_next_survey_setting['user_in_charge_required'] == 0) $post['user_in_charge'] = '';

		// view設定
		$view = View::factory ($this->_view_path . 'coupons');
		$menu = View::factory($this->_view_path . 'couponmenu');
		$menu->coupon_id = NULL;
		$menu->lang_display = [];
		$view->menu = $menu;
		$view->item_div = $item_div;
		$view->code_div_dict = $code_div_dict;
		$view->post = $post;
		$view->items = $items_show;
		$view->results = $results;
		$view->use_results = $use_results;
		if ($json_next_survey_setting['user_in_charge_required'] == 1) {
			if ($this->_user->auth_all_contents_flg == 0) {
				$view->user_list = [strval($this->_user->user_id) =>$this->_user->name];
			}
			else {
				$view->user_list = [''=>'- 担当者 -'] + $this->_model->get_bot_user_dict($this->_bot_id);
			}
		}
		else {
			$view->user_list = [''=>'- 担当者 -'];
		}
		$view->json_next_survey_setting = $json_next_survey_setting;
		$this->template->content = $view;	
	}

	private function _copy_coupon($source_id, $target_id = null) {
		$item_div = 7;
		// 参照とするクーポン
		$orm = ORM::factory('coupon', $source_id);
		if (!isset($orm->coupon_id)) return null;
		// 新しいクーポン
		$coupon = ORM::factory('coupon');
		if ($target_id == null) {
			$coupon->coupon_id = $this->_model->get_max_coupon_id($this->_bot_id);
			$coupon->coupon_name = substr($orm->coupon_name . '-copy', 0, 200);
		}
		else {
			$coupon->coupon_id = $target_id;
			$coupon->coupon_name = $orm->coupon_name;
		};

		$coupon->bot_id = $this->_bot_id;
		$coupon->class_cd = $orm->class_cd;
		$coupon->tags = $orm->tags;
		$coupon->start_date = $orm->start_date;
		$coupon->end_date = $orm->end_date;
		$coupon->coupon_data = $orm->coupon_data;
		$coupon->upd_user = $this->_user->user_id;
		$coupon->upd_time = date('Y-m-d H:i:s');
		$coupon->save();
		
		$orms = ORM::factory('coupondescription')->where('coupon_id', '=', $source_id)->find_all();
		foreach($orms as $orm) {
			$coupon_desc = ORM::factory('coupondescription');
			$coupon_desc->coupon_id = $coupon->coupon_id;
			$coupon_desc->lang_cd = $orm->lang_cd;
			$coupon_desc->title = $orm->title;
			$coupon_desc->sub_title = $orm->sub_title;
			$coupon_desc->style = $orm->style;
			$coupon_desc->section = $orm->section;
			$coupon_desc->button_pc = $orm->button_pc;
			$coupon_desc->button_sp = $orm->button_sp;
			$coupon_desc->image = $orm->image;
			$coupon_desc->save();
		}
		$orms = ORM::factory('itemdisplay')->where('item_id', '=', $source_id)->where('item_div', '=', $item_div)->find_all();
		foreach($orms as $orm) {
			$coupon_display = ORM::factory('itemdisplay');
			$coupon_display->item_id = $coupon->coupon_id;
			$coupon_display->item_div = $item_div;
			$coupon_display->bot_id = $this->_bot_id;
			$coupon_display->lang_display = $orm->lang_display;
			$coupon_display->public_flg = $orm->public_flg;
			$coupon_display->save();
		}
		return $coupon;
	}

	public function action_coupon() {
		$errors = NULL;
		$message = NULL;
		$post = NULL;
		$coupon = NULL;
		$item_div = 7;
		$coupon_id = NULL;

		$code_div = $this->_model->get_bot_setting($this->_bot_id, 'div_item_class_' . $item_div);
		// $coupon_class = $this->_model->get_code_div_kv($code_div, 'ja');
    
		// データ更新
		if ($this->request->post()) {
			$post = $this->request->post();
			$coupon_id = $post['coupon_id'];
			$act = $post['act'];
			// 削除ボタンを押した時
			if ($act == 'delete') {
				DB::update('t_coupon')->set(['delete_flg'=>1])->where('coupon_id', '=', $coupon_id)->execute();
				$this->redirect($this->_action_path . 'coupon');
			}
			else { //
				// 保存ボタンを押した時
				
				// if coupon name is "", do nothing and redirect to edit page
				if (trim($post['coupon_name']) == "") {
					if ($coupon_id != NULL) {
						$this->redirect($this->_action_path . 'coupon?id=' . $coupon_id);
					} else {
						$this->redirect($this->_action_path . 'coupon');
					}
				}
				if ($coupon_id == NULL) {
          			$coupon = ORM::factory('coupon');
					$coupon->coupon_id = $this->_model->get_max_coupon_id($this->_bot_id);
					$coupon->bot_id = $this->_bot_id;
				} else { // 編集
 			         $coupon = ORM::factory('coupon', $coupon_id);
			    }
				$coupon->coupon_name = trim($post['coupon_name']);
				$coupon->class_cd = implode(' ', json_decode($post['class_cd'], true));
				if ($post['start_date'] == '') {
					$coupon->start_date = NULL;
				} else {
					$coupon->start_date = $post['start_date'];
				}
				if ($post['end_date'] == '') {
					$coupon->end_date = NULL;
				} else {
					$coupon->end_date = $post['end_date'];
				}
				$coupon->coupon_data = $post['coupon_data'];
				if ($post['user_in_charge'] != '') $coupon->user_in_charge = $post['user_in_charge'];
				if (array_key_exists('input_cd', $post)) $coupon->input_code = $post['input_cd'];
				$coupon->upd_user = $this->_user->user_id;
				$coupon->upd_time = date('Y-m-d H:i:s');
				$coupon->save();
				$this->_update_item_display($this->_bot_id, $coupon->coupon_id, $item_div, $post);
        		$this->redirect($this->_action_path . 'coupon?id=' . $coupon->coupon_id);
			}
		} else { //表示
			$coupon_id = $this->request->query('id', NULL);
			// 新規
			if ($coupon_id == NULL) {
				$coupon_id = '';
				$this->_del_session('coupon_id');
				$post['coupon_name'] = '';
				$post['class_cd'] = '';
				$post['start_date'] = '';
				$post['end_date'] = '';
				$post['end_date'] = '';
				$post['coupon_data'] = '';
				$post['user_in_charge'] = 0;
				$post['lang_display'] = explode(',', $this->_bot->lang_cd);
				// coupon (user side) only support ja, en, cn, tw, kr
				$post['lang_display'] = array_merge(array_intersect($post['lang_display'], $this->_model->get_setting('native_support_lang')));
				$post['input_cd'] = '';
				$data = [];
			} else { // 編集
				$this->_set_session('coupon_id', $coupon_id);
				$permission = $this->_coupon_permission_check($coupon_id);
				if ($permission < 0) {
					$this->redirect($this->_action_path . 'coupons');
				}
				$coupon = ORM::factory('coupon', $coupon_id);
				$post['coupon_name'] = $coupon->coupon_name;
				$post['class_cd'] = trim($coupon->class_cd);
				$post['class_name_array'] = $this->_model->get_class_full_name($code_div, $post['class_cd']);
				$post['start_date'] = $coupon->start_date;
				$post['end_date'] = $coupon->end_date;
				$post['user_in_charge'] = $coupon->user_in_charge;
				$post['input_cd'] = $coupon->input_code;
				$post['coupon_data'] = json_decode($coupon->coupon_data, true);
				$display = ORM::factory('itemdisplay')->where('bot_id', '=', $this->_bot_id)->where('item_id', '=', $coupon->coupon_id)->where('item_div', '=', $item_div)->find();
				if (isset($display->item_id)) {
					$post['lang_display'] = explode(',', $display->lang_display);
				}
				else {
					$post['lang_display'] = explode(',', $this->_bot->lang_cd);
				}
				// coupon (user side) only support ja, en, cn, tw, kr
				$post['lang_display'] = array_merge(array_intersect($post['lang_display'], $this->_model->get_setting('native_support_lang')));
				// prevent 500 Internal Server Error if database table t_coupon "coupon_data" is null
				if ($post['coupon_data'] == NULL) {
					$data = [];
				} else {
					$data = $post['coupon_data'];
				}
			}
		}
		if (!array_key_exists('lottery_probability', $data)) $data['lottery_probability'] = '';
		if (!array_key_exists('lottery_type', $data)) $data['lottery_type'] = '';
		if (!array_key_exists('lottery_maximum', $data)) $data['lottery_maximum'] = '';
		if (!array_key_exists('use_facilitys', $data)) $data['use_facilitys'] = '';
		if (!array_key_exists('use_amount_type', $data)) $data['use_amount_type'] = '';
		if (!array_key_exists('use_amount', $data)) $data['use_amount'] = '';
		if (!array_key_exists('discount_type', $data)) $data['discount_type'] = '';
		if (!array_key_exists('discount_div', $data)) $data['discount_div'] = '';
		if (!array_key_exists('discount_amount', $data)) $data['discount_amount'] = '';
		if (!array_key_exists('stock_type', $data)) $data['stock_type'] = '';
		if (!array_key_exists('stock', $data)) $data['stock'] = '';
		if (!array_key_exists('stock_member', $data)) $data['stock_member'] = '';
		if (!array_key_exists('stock_type_member', $data)) $data['stock_type_member'] = '';
		if (!array_key_exists('expiration_date', $data)) $data['expiration_date'] = '';

		$view = View::factory ($this->_view_path . 'coupon');
		$menu = View::factory($this->_view_path . 'couponmenu');
		$view->item_def = $this->_model->get_item_tpl_message($this->_bot_id, $item_div, '01', 'item_data', $this->_lang_cd); //TODO: 多言語
		$menu->coupon_id = $coupon_id;

		if ($this->_model->get_env('survey_url') === "https://survey.talkappi.com/") {
			if ($this->_action_path == '/adminticket/') {
				$view->open_url = $this->_model->get_env('ticket_url') . '?f=' . $this->_bot->facility_cd . '&id=' . $coupon_id;
			} else {
				$view->open_url = $this->_model->get_env('survey_url') . 'coupon?f=' . $this->_bot->facility_cd . '&id=' . $coupon_id;
			}
		}
		else {
			$view->open_url = $this->_model->get_env('base_url') . 'coupon?f=' . $this->_bot->facility_cd . '&id=' . $coupon_id;
		}

		$json_next_survey_setting = $this->_model->get_bot_setting($this->_bot_id, 'json_next_survey_setting');
		$json_next_survey_setting = json_decode($json_next_survey_setting, true);
		if (!array_key_exists('user_in_charge_required', $json_next_survey_setting)) $json_next_survey_setting['user_in_charge_required'] = 0;
		if ($json_next_survey_setting['user_in_charge_required'] == 1) {
			if ($this->_user->auth_all_contents_flg == 0) {
				$view->user_list = [strval($this->_user->user_id) =>$this->_user->name];
			}
			else {
				$view->user_list = [''=>'-'] + $this->_model->get_bot_user_dict($this->_bot_id);
			}
		}
		else {
			$view->user_list = [''=>'-'];
		}
		$view->json_next_survey_setting = $json_next_survey_setting;

		$view->menu = $menu;
		$view->post = $post;
		$view->coupon_id = $coupon_id;
		$view->code_div = $code_div;
		$view->lottery_probability = $this->_create_percentage_array();
		$view->use_facilitys = $this->_model->get_bot_message_dict($this->_bot_id, '52', 'tpl');
		$view->data = $data;
		$view->native_support_lang = $this->_model->get_setting('native_support_lang');
		$this->template->content = $view;
		$this->_page_navi('[{"f":"5100","url":"coupons"}, {"f":"5101"}]');
	}

	public function action_coupondesc() {
		$errors = NULL;
		$message = '';
		$post = NULL;
		$item_description = NULL;
		$item_description_ja = NULL;

		$item_div = 7;

		$lang_cd = NULL;
		$url = NULL;
		$up_image = false;

		if ($this->request->post()) { // アクション
			$post = $this->request->post();

			$coupon_id = $post['coupon_id'];

			$item = ORM::factory('coupon', $coupon_id);
			if (array_key_exists('lang', $post)) $lang_cd = $post['lang'];
			$flg_auto_translate = false;
			$flg_apply_all_lang = false;

			if (array_key_exists('image_base64', $post) && $post['image_base64'] !='') {
				if (strpos($post['image_base64'], 'data:') === 0) {
					if ($flg_apply_all_lang) {
						$url = $this->_aws_model->put_base64_file($this->_bot_id, $post['image_base64'], $coupon_id, 'coupon');
						$this->_aws_model->delete_object($this->_bot_id, 'coupon', $coupon_id. '_base.jpg');
						$this->_aws_model->delete_object($this->_bot_id, 'coupon', $coupon_id. '_base.png');
					}
					else {
						$url = $this->_aws_model->put_base64_file($this->_bot_id, $post['image_base64'], $coupon_id. '_' . $lang_cd, 'coupon');
						$this->_aws_model->delete_object($this->_bot_id, 'coupon', $coupon_id. '_' . $lang_cd . '_base.jpg');
						$this->_aws_model->delete_object($this->_bot_id, 'coupon', $coupon_id. '_' . $lang_cd . '_base.png');
					}
					$this->_aws_model->resize_image_url($url, 800, 0);
					//tag
					// if ($this->_has_tag($item_div)) {
					// 	$item_data = json_decode($item->item_data, true);
					// 	$tags = [];
					// 	if (array_key_exists('tags', $item_data) && $item_data['tags'] != '') $tags = explode(',', $item_data['tags']);
					// 	$url = $this->_aws_model->item_tag($this->_bot_id, $item_div, $coupon_id, '', $tags, $url);
					// }
					$this->_aws_model->refresh_url($url);
					$up_image = true;
				}
			}

			if ($message == "") { // 保存OK
				$coupon_description = ORM::factory('coupondescription')->where('coupon_id', '=', $coupon_id)->where('lang_cd', '=', $lang_cd)->find();

				if (!isset($coupon_description->coupon_id)) { // 新しく追加
					$coupon_description = ORM::factory('coupondescription');
					$coupon_description->coupon_id = $coupon_id;
					$coupon_description->lang_cd = $lang_cd;
					$coupon_description->title = $post['title'];
					$coupon_description->sub_title = $post['sub_title'];
					if ($url != NULL) $coupon_description->image = $url;
					$coupon_description->style = json_encode(["theme_color"=>$post['theme-bk-color'], "title_color"=>$post['theme-color']]);
					$coupon_description->button_pc = $post['button_pc'];
					$coupon_description->button_sp = $post['button_sp'];
					$coupon_description->section = $post['section'];
					$coupon_description->save();
					$message = 'クーポン情報を登録しました';
				} else { //既にある情報変更
					$up_array = [];
					$up_array['title'] = $post['title'];
					$up_array['sub_title'] = $post['sub_title'];
					if (strpos($post['image_base64'], 'data:') === 0) {
						$up_array['image'] = $url;
					}
					$up_array['style'] = json_encode(["theme_color"=>$post['theme-bk-color'], "title_color"=>$post['theme-color']]);
					$up_array['button_sp'] = $post['button_sp'];
					$up_array['button_pc'] = $post['button_pc'];
					$up_array['section'] = $post['section'];

					$query = DB::update('t_coupon_description')->
					set($up_array)->where('coupon_id', '=', $coupon_id)->where('lang_cd', '=', $lang_cd);
					$result = $query->execute();
					$message = 'クーポン情報を更新しました';
				}

				if ($flg_apply_all_lang && !$up_image) {
					$new_image_url = $this->_aws_model->all_lang_image($url);
					if ($new_image_url != '') DB::update('t_coupon_description')->set(['image'=>$new_image_url])->where('coupon_id', '=', $coupon_id)->execute();
				}
				$orm = ORM::factory('coupon', $coupon_id);
				$orm->upd_user = $this->_user->user_id;
				$orm->upd_time = date('Y-m-d H:i:s');
				$orm->save();
			}

			if (isset($post['translate']) && $post['translate'] == 'translate_auto' && $lang_cd !== null) {
				foreach ($post['translate_lang'] as $translate_target) {
					$coupon_from = $this->_survey_model->find_coupondesc($coupon_id, $lang_cd);
					if ($translate_target && $coupon_from['coupon_id']) {
						$post['title'] = $this->_model->translate($coupon_from['title'], $translate_target, $lang_cd);
						$post['sub_title'] = $this->_model->translate($coupon_from['sub_title'], $translate_target, $lang_cd);
						$post['image'] = $coupon_from['image'];
						$post['style'] = $coupon_from['style'];
						$post['section'] = $this->_survey_model->auto_translate($translate_target, $coupon_from['section'], $lang_cd);
						$post['button_pc'] = $this->_survey_model->auto_translate($translate_target, $coupon_from['button_pc'], $lang_cd);
						$post['button_sp'] = $this->_survey_model->auto_translate($translate_target, $coupon_from['button_sp'], $lang_cd);
	
						if ($this->_survey_model->find_coupondesc($coupon_id, $translate_target)){
							$this->_survey_model->update_coupondesc($coupon_id, $translate_target, $post);
						} else {
							$this->_survey_model->save_coupondesc($coupon_id, $translate_target, $post);
						}	
					}
				}
				$this->redirect($this->_action_path . 'coupondesc?id=' .$coupon_id. '&lang=' .$lang_cd);
				return;
			}
			$this->redirect($this->_action_path . 'coupondesc?id=' .$coupon_id. '&lang=' .$lang_cd);
		} else { // 表示
			$lang_cd = $this->request->query('lang', NULL);
			if ($lang_cd == NULL) {
				$lang_cd = key($this->_bot_lang);
			}
			$coupon_id = $this->request->query('id', NULL);
			$permission = $this->_coupon_permission_check($coupon_id);
			if ($permission < 0) {
				$this->redirect($this->_action_path . 'coupons');
			}
			$item = ORM::factory('coupon', $coupon_id);
			$today = new DateTime('today');
			$not_started = ($item->start_date && date('Y-m-d') < $item->start_date) ? true : false;
			$expired = $item->end_date && date('Y-m-d') <= $item->end_date ? false : true;
			$start_date = $not_started ? $this->_format_calendar($item->start_date, $lang_cd) : '';
			if ($item->end_date) {
				$end_date = $this->_format_calendar($item->end_date, $lang_cd);
			} else {
				$end_date = '';
			}
			if (!$expired) {
				$end = new DateTime($item->end_date);
				$days = $end->diff($today)->days + 1;
				if ($not_started) {
					$days = '';
				}
			}
			else {
				$days = '';
			}

			$coupon_data = json_decode($item->coupon_data, true);

			$post['title'] = ''; 
			$post['sub_title'] = '';
			$post['image'] = '';
			$post['style'] = ["theme_color"=>"#3d3f45", "title_color"=>"#FFFFFF"];
			$post['button_pc'] = [["action"=>"クーポンを使う", "text"=>"クーポンを使う"]];
			$post['button_sp'] = [];
			$post['section'] = [];
			$item_description = ORM::factory('coupondescription')->where('coupon_id', '=', $coupon_id)->where('lang_cd', '=', $lang_cd)->find();
			// 情報がない
			if (!isset($item_description->coupon_id)) {
				// 日本語以外の場合
				if ($lang_cd != 'ja') {
					$item_description_ja = ORM::factory('coupondescription')->where('coupon_id', '=', $coupon_id)->where('lang_cd', '=', 'ja')->find();
					if (isset($item_description_ja->coupon_id)) {
						$post['title'] = $item_description_ja->title;
						$post['sub_title'] = $item_description_ja->sub_title;
						$post['image'] = $item_description_ja->image;
						$post['style'] = json_decode($item_description_ja->style, true);
						$post['button_pc'] = json_decode($item_description_ja->button_pc, true);
						$post['button_sp'] = json_decode($item_description_ja->button_sp, true);
						$post['section'] = json_decode($item_description_ja->section, true);
						$item_description = $item_description_ja;
					}
				}
			}
			else {
				$post['title'] = $item_description->title;
				$post['sub_title'] = $item_description->sub_title;
				$post['image'] = $item_description->image;
				$post['style'] = json_decode($item_description->style, true);
				$post['button_sp'] = json_decode($item_description->button_sp, true);
				$post['button_pc'] = json_decode($item_description->button_pc, true);
				$post['section'] = json_decode($item_description->section, true);
			}
		}
		
		$view = View::factory ($this->_view_path . 'coupondesc');
		$menu = View::factory($this->_view_path . 'couponmenu');
		$menu->coupon_id = $coupon_id;
		$item_descs = ORM::factory('coupondescription')->where('coupon_id', '=', $coupon_id)->find_all();
		if (count($item_descs) == 0) {
			$view->auto_translate = 1;
			$view->all_language = 1;
		}
		else {
			$view->auto_translate = 0;
			$view->all_language = 0;
		}
		$display = ORM::factory('itemdisplay')->where('item_id', '=', $coupon_id)->where('item_div', '=', $item_div)->find();
		$view->lang_display = $display->lang_display;
		$lang_setting = explode(",", $display->lang_display);
		$lang_cd_array = array_intersect_key($this->_model->get_code('02', $this->_lang_cd), array_flip($lang_setting));
		$view->supprt_lang_cd = $lang_cd_array;
		$view->menu = $menu;
		$view->coupon_id = $coupon_id;
		$view->item_description_ja = $item_description_ja;
		$view->post = $post;
		$view->message = $message;
		$view->url_lang_cd = array(''=>"-") + $this->_model->get_code('02');
		$view->item_div = $item_div;
		$view->item = $item;
		$view->lang_edit = $lang_cd;
		$view->days = $days;
		$view->end_date = $end_date;
		$view->start_date = $start_date;
		$view->coupon_data = $coupon_data;
		$this->template->content = $view;
		$this->_page_navi('[{"f":"5100","url":"coupons"}, {"f":"5102"}]');
	}

	public function action_couponresult() {
		$errors = NULL;
		$message = NULL;
		$post = [];
		$coupon_id = NULL;

		if ($this->request->post()) { // 検索
			$post = $this->request->post();
			if ($post['act'] === 'result_delete') {
				$this->_delete_couponresult($post['result_no'], $post['seq']);
			}
			$coupon_id = $post['coupon_id'];
			Session::instance()->set('couponresult_get_start_date', $post['get_start_date']);
			Session::instance()->set('couponresult_get_end_date', $post['get_end_date']);
			Session::instance()->set('couponresult_use_start_date', $post['use_start_date']);
			Session::instance()->set('couponresult_use_end_date', $post['use_end_date']);
			Session::instance()->set('couponresult_lang_cd', $post['lang_cd']);
			Session::instance()->set('couponresult_sns_type_cd', $post['sns_type_cd']);
			Session::instance()->set('couponresult_status_cd', $post['status_cd']);
		} else { // 初期表示
			$coupon_id = $this->request->query('id', NULL);
			$permission = $this->_coupon_permission_check($coupon_id);
			if ($permission < 0) {
				$this->redirect($this->_action_path . 'coupons');
			}
			// 取得期間
			$post['get_start_date'] = Session::instance()->get('couponresult_get_start_date', NULL);
			$post['get_end_date'] = Session::instance()->get('couponresult_get_end_date', NULL);
			// 利用期間
			$post['use_start_date'] = Session::instance()->get('couponresult_use_start_date', NULL);
			$post['use_end_date'] = Session::instance()->get('couponresult_use_end_date', NULL);
			// 言語
			$post['lang_cd'] = Session::instance()->get('couponresult_lang_cd', NULL);
			if ($post['lang_cd'] == NULL) {
				$post['lang_cd'] = '';
			}
			// チャンネル
			$post['sns_type_cd'] = Session::instance()->get('couponresult_sns_type_cd', NULL);
			if ($post['sns_type_cd'] == NULL) {
				$post['sns_type_cd'] = '';
			}
			// 利用状態
			$post['status_cd'] = Session::instance()->get('couponresult_status_cd', NULL);
			if ($post['status_cd'] == NULL) {
				$post['status_cd'] = '';
			}
			$post['type'] = 'detail';
		}

		$coupon = ORM::factory('coupon', $coupon_id);
		$coupon_data = json_decode($coupon->coupon_data, true);
		$use_facility_name = $coupon_data['use_facilitys'];
		$use_facilitys = $this->_model->get_bot_tpl_message($this->_bot_id, $use_facility_name, 'ja');
		$coupon_result = $this->_model->get_coupon_result($this->_bot_id, $coupon_id, $post['get_start_date'], $post['get_end_date'], $post['lang_cd'], $post['sns_type_cd']);
		$coupon_use_result = $this->_model->get_coupon_use_result($this->_bot_id, $coupon_id, $post['get_start_date'], $post['get_end_date'], $post['lang_cd']);
		$coupon_use_dict = [];
		foreach($coupon_use_result as $cur) {
			if (isset($cur['result_no'])) {
				$coupon_use_dict[$cur['result_no']][] = $cur;
			}
			else {
				$coupon_use_dict[$cur['result_no']] = [$cur];
			}
		}

		$total_remain = null;
		$hasLimitForAll = $coupon_data['use_amount_type'] == '2';
		$hasLimitForMember = $coupon_data['stock_type_member'] == '2';
		if ($hasLimitForAll) {
			if ($hasLimitForMember) {
				$total_remain = min(intval($coupon_data['use_amount'] ?? 0), intval($coupon_data['stock_member'] ?? 0));
			} else {
				$total_remain = intval($coupon_data['use_amount'] ?? 0);
			}
		} else {
			if ($hasLimitForMember) {
				$total_remain = intval($coupon_data['stock_member'] ?? 0);
			}
		}

		$coupon_result_data = $this->_model->format_coupon_result($coupon_result, $coupon_use_dict, $total_remain, $post['status_cd'], $post['use_start_date'], $post['use_end_date']);
 		$view = View::factory ($this->_view_path . 'couponresult');
		$menu = View::factory($this->_view_path . 'couponmenu');
		$menu->coupon_id = $coupon_id;
		$view->menu = $menu;
		$view->use_facilitys = json_decode($use_facilitys,true);
		$lang_cd_list = array(''=>"すべての言語") + $this->_bot_lang;
		$view->lang_cd_list = $lang_cd_list;
		$sns_type_cd_list = array(''=>"すべてのチャンネル") + $this->_codes['16'];
		$view->sns_type_cd_list= $sns_type_cd_list;
		$view->status_cd = [''=>'すべての利用状態','02'=>'利用済み'];
		$view->post = $post;
		$view->coupon_id = $coupon_id;
		$view->coupon_result = $coupon_result_data;
		$view->coupon = $coupon;
		$this->template->content = $view;
		$this->_page_navi('[{"f":"5100","url":"coupons"}, {"f":"5103"}]');
	}

	private function _delete_couponresult($result_no, $seq) {
		if (!$result_no) {
			return;
		}

		if ($seq === '') {
			DB::delete('t_coupon_result')->where('result_no', '=', $result_no)->execute();
			DB::delete('t_coupon_use_result')->where('result_no', '=', $result_no)->execute();
		} else {
			DB::delete('t_coupon_use_result')->where('result_no', '=', $result_no)->where('seq', '=', $seq)->execute();
			// 同じresult_noで複数回利用されている場合、全ての利用歴が消されたときに、t_coupon_result(発行時データ)も削除
			$item = ORM::factory('couponuseresult')->where('result_no', '=', $result_no)->find_all();
			if (count($item) === 0) {
				DB::delete('t_coupon_result')->where('result_no', '=', $result_no)->execute();
			}
		}
	}

	public function action_couponresults()
	{
		$errors = NULL;
		$message = NULL;
		$post = NULL;
		$status_cd = '';
		$survey_id = '';

		if ($this->request->post()){
			$post = $this->request->post();
			$status_cd = $post['status_cd'];
			$start_date = $post['start_date'];
			$end_date = $post['end_date'];
		}
		else {
			$coupon_id = $this->request->query('id', NULL);
			$start_date = new DateTime('first day of this month');
			$start_date = $start_date->format('Y-m-d');
			$end_date = new DateTime('last day of this month');
			$end_date = $end_date->format('Y-m-d');
		}

		$services = $this->_model->get_survey_orders($this->_bot_id, $status_cd, $start_date, $end_date, $coupon_id);

		$view = View::factory ($this->_view_path . 'surveyorders');
		$menu = View::factory ($this->_view_path . 'couponresultsmenu');
		$view->menu = $menu;
		$menu->survey_id = $survey_id;
		$talkbox = View::factory ('admin/talkbox');
		$talkbox->translate = Session::instance()->get('translate', '0');
		$talkbox->phrase_types = $this->_model->get_code_div_kv($this->_model->get_bot_setting($this->_bot_id, 'div_item_class_6'), 'ja');
		$talkbox->status = $this->_model->get_code('25');
		$view->talkbox = $talkbox;
		$view->survey_id = $survey_id;
		$view->survey = ORM::factory('survey', $survey_id);
		$view->codes = $this->_codes;
		$view->services = $services;
		$view->service_status_array = $this->_model->get_code('45');
		$view->new_flg = 0;
		$view->status_cd = $status_cd;
		$view->start_date = $start_date;
		$view->end_date = $end_date;
		$this->template->content = $view;
	}

	public function action_copy() {
		$this->auto_render = FALSE;
		$post = $this->request->post();
		$item_div = $this->_can_copied($post['uri']);
		if ($item_div === false) {
			$this->response->body(json_encode(['result'=>'fail', 'message'=>'common.message.error.content_copy_error']));
			return;
		}
		if ($item_div == 8) {
			$item_id = $this->_get_session('survey_id', NULL);
		}
		$this->_copy($this->_bot_id, $item_div, $item_id);
		$this->response->body(json_encode(['result'=>'success', 'message'=>'common.message.success.content_copy_success']));
	}

	public function action_paste() {
		$this->auto_render = FALSE;
		$post = $this->request->post();
		$item_div = $this->_can_pasted($post['uri']);
		if ($item_div == false) {
			$this->response->body(json_encode(['result'=>'fail', 'message'=>'common.message.error.content_copy_error']));
			return;
		}
		$obj = $this->_paste();
		if ($obj == null) {
			$this->response->body(json_encode(['result'=>'fail', 'message'=>'common.message.error.content_copy_error']));
			return;
		}
		if ($obj['item_div'] == 8) {
			$new_id = $this->_copy_survey($obj['item_id'], 'newcopy', $this->_bot_id);
			$this->response->body(json_encode(['result'=>'success', 'message'=>'content_copy_success', 'url'=>$this->_action_path . 'survey?id=' . $new_id]));
		}
	}

	private function _item_permission_check($survey_id) {
		$item = ORM::factory('survey', $survey_id);
		if (isset($item->bot_id)) {
			if ($item->bot_id == $this->_bot_id) return 1;
		}
		return -1;
	}

	private function _coupon_permission_check($coupon_id) {
		$coupon = ORM::factory('coupon', $coupon_id);
		if (isset($coupon->bot_id)) {
			if ($coupon->bot_id == $this->_bot_id) return 1;
			if ($coupon->bot_id == 9999999) return 0;
		}
		$coupon_display = ORM::factory('itemdisplay')->where('bot_id', '=', $this->_bot_id)->where('item_div', '=', 7)->where('item_id', '=', $coupon_id)->find();
		if (isset($coupon_display->coupon_id)) {
			return 0;
		}
		return -1;
	}

	private function _get_default_btn($bot_id, $item_div, $class_cd, $lang_cd) {
		return $this->_model->get_item_lst_message($bot_id, $item_div, $class_cd, 'item_def_button', $lang_cd);
	}

	private function _skill_box($all_language = NULL, $default_btn = NULL) {
		$skill = $this->_model->get_skill();
		$skillbox = View::factory('admin/skillbox');
		$kind_list = DB::select('kind')->distinct('kind')->from('m_skill')->execute();
		$kinds = [];
		foreach($kind_list as $kind) {
			$kinds[$kind['kind']] = $kind['kind'];
		}
		$skillbox->skill_kinds = [''=>'-すべて-'] + $kinds;
		$skillbox->all_language = $all_language;
		if ($default_btn != NULL ) $skillbox->default_btn = $default_btn;
		return $skillbox;
	}

	private function _update_item_display($bot_id, $item_id, $item_div, $post) {
		$display_info = [];
		if (array_key_exists('lang_display', $post)) {
			$display_info['lang_display'] = implode(',', json_decode($post['lang_display'], true));
		}
		else {
			$display_info['lang_display'] = '';
		}
		if(!array_key_exists('recommend', $post)) {
			$display_info['recommend'] = 0;
		}
		else {
			$display_info['recommend'] = 1;
		}
		if(!array_key_exists('public_flg', $post)) {
			$display_info['public_flg'] = 0;
		}
		else {
			$display_info['public_flg'] = 1;
		}
		$has_display = false;
		if ($item_id != NULL) {
			$item = ORM::factory('itemdisplay')->where('bot_id', '=', $bot_id)->where('item_id', '=', $item_id)->where('item_div', '=', $item_div)->find();
			if (isset($item->item_id)) {
				$has_display = true;
			}
		}
		if ($has_display == false) {
			$item = ORM::factory('itemdisplay');
			$item->bot_id = $bot_id;
			$item->item_id = $item_id;
			$item->item_div = $item_div;
			$item->sort_no1 = $item_id % 1000;
			$item->sort_no2 = 0;
			$item->sort_no3 = 0;
			$item->sort_no4 = 0;
			$item->sort_no5 = 0;
			$item->recommend = $display_info['recommend'];
			$item->lang_display = $display_info['lang_display'];
			$item->public_flg = $display_info['public_flg'];
			$item->save();
		}
		else {
			$grp_bot_id = $this->_model->get_grp_bot_id($bot_id);
			if ($grp_bot_id == 0) {
				DB::update('t_item_display')->set($display_info)->where('bot_id', '>=', $bot_id)->where('bot_id', '<', intval($bot_id) + 1000)->where('item_id', '=', $item_id)->where('item_div', '=', $item_div)->execute();
			}
			else {
				DB::update('t_item_display')->set($display_info)->where('bot_id', '=', $bot_id)->where('item_id', '=', $item_id)->where('item_div', '=', $item_div)->execute();
			}
		}
	}

	private function _has_tag($item_div) {
		$icons = $this->_model->get_bot_message($this->_bot_id, 'item_div_' . $item_div . '_tag', 'ja', 'img');
		if (count($icons) == 0) return false;
		return true;
	}

	private function _format_calendar($d, $lang_cd) {
		$arr = explode('-', $d);
		if ($lang_cd == 'ja' || $lang_cd == 'cn' || $lang_cd == 'tw') {
			return intval($arr[0]) . '年' . intval($arr[1]) . '月' . intval($arr[2]) . '日';
		}
		else {
			return intval($arr[0]) . '/' . intval($arr[1]) . '/' . intval($arr[2]);
		}
	}
	
	private function _create_percentage_array() {
		$percentage_array = array();
		for ($i = 1; $i <= 99; $i++) {
			$percentage_array["$i"] = "$i%";
		}
		return $percentage_array;
	}

}
