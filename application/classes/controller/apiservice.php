<?php defined('SYSPATH') or die('No direct script access.');
class Controller_Apiservice extends Controller
{
    public $_log_id = "";

    public function action_wordcount_demo()
    {
        $view = View::factory('service/workcount_demo');
        $view->cur_date = date('Y-m-d');
        $this->response->body($view);
    }

    public function action_wordcount()
    {
        $debug_flg = false;

        $request_time = $_SERVER["REQUEST_TIME"];
        $this->_log_id = "action_wordcount $request_time";
        $log_id = $this->_log_id;
        // 本番環境のwordcount url:  https://admin.talkappi.com/apiservice/wordcount?bot_id=17&lang_cd=ja&log_time_begin=2021-12-01&log_time_end=2021-12-01&intent_type_cd=11,03
        // 開発環境のwordcount url:  https://dev1-api.talkappi.com/apiservice/wordcount?bot_id=17&lang_cd=ja&log_time_begin=2021-12-01&log_time_end=2021-12-01&intent_type_cd=11,03
        if ($debug_flg) {
            error_log("$log_id Coming action_wordcount()");
        }
        try {
            if ($_SERVER["SERVER_ADDR"] == "172.31.24.135" // 本番サーバである
            ||  $_SERVER["SERVER_ADDR"] == "172.31.37.123" // STサーバ
            ||  $_SERVER["SERVER_ADDR"] == "172.31.41.201" // ST7サーバ
            ||  $_SERVER["SERVER_ADDR"] == "172.31.21.245" // admin8サーバ
                ) {
                
                Database::$default = 'slave';

                $connectURL = "http://172.31.29.46:9001/service/";              // 本番の単語分析サービスURLで、社内インフラから使用する場合
            } elseif ($_SERVER["SERVER_ADDR"] == "172.31.17.237") {
                // dev1サーバである
                //Database::$default = 'honban_slave';
                Database::$default = 'default';

                // 本番の単語分析サービスURL
                //$connectURL = "http://172.31.29.46:9001/service/";              // 本番の単語分析サービスURLで、社内インフラから使用する場合
                $connectURL = "http://127.0.0.1:9001/service/";                     // dev1環境の単語分析サービスURLで、dev1から使用する場合
            } else {
                // そのたサーバである
                Database::$default = 'default';

                $connectURL = "http://127.0.0.1:9001/service/";                     // dev1環境の単語分析サービスURLで、dev1から使用する場合
                // $connectURL = "http://172.31.17.237:9001/service/";              // dev1環境の単語分析サービスURLで、dev1以外の社内インフラから使用する場合
                // $connectURL = "http://dev1-engine.talkappi.com:9101/service/";   // dev1環境の単語分析サービスURLで、internetから使用する場合
            }

            $query = $this->request->query();

            if ($debug_flg) {
                error_log("$log_id params is:");
                foreach ($query as $name => $value) {
                    error_log("$log_id   $name = $value");
                }
            }

            $lang_cd = "ja";
            if (array_key_exists('lang_cd', $query)) {
                $lang_cd = $query["lang_cd"];
            }
            $bot_id = 17;
            if (array_key_exists('bot_id', $query)) {
                $bot_id = $query["bot_id"];
            }
            $type_cd = "";
            if (array_key_exists('intent_type_cd', $query)) {
                $type_cd = $query["intent_type_cd"];
            }
            $sql_innerjoin_m_bot_intent = "";
            $sql_type_cd = "1 = 1";
            if ($type_cd != "") {
                $sql_innerjoin_m_bot_intent = "
                    INNER JOIN m_bot_intent b
                    ON a.intent_cd = b.intent_cd
                    AND a.sub_intent_cd = b.sub_intent_cd
                    AND a.lang_cd = b.lang_cd
                ";
                $array_type_cd = explode(",", $type_cd);
                for ($i=0; $i<count($array_type_cd); $i++) {
                    $cur_type_cd = $array_type_cd[$i];
                    $cur_sql =  "concat(',',b.intent_type_cd) like concat('%,','$cur_type_cd', '%')";
                    if ($i == 0) {
                        $sql_type_cd = $cur_sql;
                    } else {
                        $sql_type_cd = "$sql_type_cd 
                        OR $cur_sql";
                    }
                }
            }

            $log_time_begin = "";
            $log_time_end = "";
            if (array_key_exists('log_time_begin', $query)) {
                $log_time_begin = $query["log_time_begin"];
            }
            if (array_key_exists('log_time_end', $query)) {
                $log_time_end = $query["log_time_end"];
            }
            $sql_time = "1 = 1";
            if ($log_time_begin != "") {
                $log_time_begin = str_replace("-", "", $log_time_begin);
                $cur_sql = "a.vir_int_log_time >= $log_time_begin";
                $sql_time = "$sql_time 
                AND $cur_sql";
            }
            if ($log_time_end != "") {
                $log_time_end = str_replace("-", "", $log_time_end);
                $cur_sql = "a.vir_int_log_time <= $log_time_end";
                $sql_time = "$sql_time 
                AND $cur_sql";
            }

            $admin_model = new Model_Adminmodel();
            $parent_bot_id = $admin_model->get_grp_bot_id($bot_id);


            // 2022.10.03 begin　#30545
            $sql_bot_id = "a.bot_id= :bot_id";
            if ($parent_bot_id == 0) {
                $sql_bot_id = $this->wuzhao_create_group_bot_cond($bot_id, "a.bot_id");
            }
            // 2022.10.03 end

            if ($parent_bot_id == -1) {
                $parent_bot_id = $bot_id;
            }

            $log_table_id = $bot_id;
            if ($log_table_id == 401002 || $log_table_id == 401003) {
                $log_table_id = 401001;
            } else {
                $log_table_id = $parent_bot_id;
            }
            $table_name = "t_bot_log_$log_table_id";

            $MAX_SENTENCE_NUM = 1500; // 最多分析语句数目
            $sql = "SELECT 
                a.member_msg,
                count(1) as number
            FROM $table_name a
            $sql_innerjoin_m_bot_intent
            WHERE 
                $sql_bot_id
                AND vir_is_intent_inquiry = 1
                AND vir_is_member_msg =1
                AND vir_is_score_between01 = 1
                AND $sql_time
                AND (a.answer_type = 0 or a.answer_type = 1 or a.answer_type = 2)
                AND a.lang_cd = :lang_cd
                AND ($sql_type_cd)
            GROUP BY
                a.member_msg
            ORDER BY number DESC
            LIMIT $MAX_SENTENCE_NUM
            ";
            // AND a.member_msg NOT LIKE '👉%'  // 用answer_type =0 or 2代替
            //            AND find_in_set(b.intent_type_cd,'03') > 0
            $query = DB::query(Database::SELECT, $sql);
            $query->parameters(array(
                ':bot_id' => $bot_id,
                ':lang_cd' => $lang_cd,
            ));
            $results = $query->execute()->as_array();
            $count = count($results);
            if ($debug_flg) {
                error_log("$log_id sql = $sql");
                error_log("$log_id record_count = $count");
            }

            // 分词 & 统计结果
            $flg_sentence_merge = "true";
            // 2022.03.27 统一api接口，交由server端来计算merge结果
            /*
            // 日语不能merge，merge了就会导致sudachi崩溃
            $lang_merge = ["en"];
            $flg_sentence_merge = "false";
            if (in_array($lang_cd, $lang_merge)) {
                $flg_sentence_merge = "true";
            }
            */

            $word_results = [
                "words" => [],          // 存放"a"=>100
                "max_number" => 0,
            ];        // 存放统计结果

            $word_segment_sentence_num = $count;
            if ($word_segment_sentence_num > $MAX_SENTENCE_NUM) {
                $word_segment_sentence_num = $MAX_SENTENCE_NUM;
                if ($debug_flg) {
                    error_log("$log_id 调整分析句子数 word_segment_sentence_num = $word_segment_sentence_num");
                }
            }
            $is_word_segment_success = true;
            {
                $MAX_ONE_TIME_POST_SIZE = 10000; // 一次最多送字节给分词
                $sentence_mul = [];
                $sentence_duplicate_time = [];
                $cur_post_size = 0;
                $call_curl_count = 1;
                for ($i=0; $i<$word_segment_sentence_num;$i++) {
                    $cur_record_msg = $results[$i]["member_msg"];
                    $cur_msg_length = strlen($cur_record_msg);
                    if (($cur_post_size + $cur_msg_length) >= $MAX_ONE_TIME_POST_SIZE) {
                        if ($debug_flg) {
                            error_log("$log_id $call_curl_count cur_post_size = $cur_post_size");
                            ++$call_curl_count;
                        }
                        $cur_wordsegment_results =  $this->get_word_segment_result($bot_id, $sentence_mul, $sentence_duplicate_time, $lang_cd, $connectURL, $flg_sentence_merge);
                        if ($cur_wordsegment_results === false) {
                            $is_word_segment_success = false;
                            break;
                        } else {
                            // 合并
                            $word_results = $this->merge_word_segment_result_to_word($lang_cd, $word_results, $cur_wordsegment_results, $sentence_duplicate_time, $flg_sentence_merge);
                            // 初始化
                            $sentence_mul = [];
                            $sentence_duplicate_time = [];
                            $cur_post_size = 0;
                            // 缓存本次
                            $sentence_mul[] = $cur_record_msg;
                            $sentence_duplicate_time[] = $results[$i]["number"];
                            $cur_post_size = $cur_post_size + $cur_msg_length;
                        }
                    } else {
                        // 缓存
                        $sentence_mul[] = $cur_record_msg;
                        $sentence_duplicate_time[] = $results[$i]["number"];
                        $cur_post_size = $cur_post_size + $cur_msg_length;
                    }

                    if (count($word_results["words"]) > 200) {
                        if ($debug_flg) {
                            $temp_words_num = count($word_results["words"]);
                            error_log("$log_id got words >= 200 = $temp_words_num ,exit");
                        }
                        break;
                    }
                }
                // 最后一次
                if ($is_word_segment_success == true) {
                    if ($debug_flg) {
                        error_log("$log_id $call_curl_count lasttime cur_post_size = $cur_post_size");
                        ++$call_curl_count;
                    }
                    $cur_wordsegment_results =  $this->get_word_segment_result($bot_id, $sentence_mul, $sentence_duplicate_time, $lang_cd, $connectURL, $flg_sentence_merge);
                    if ($cur_wordsegment_results === false) {
                        $is_word_segment_success = false;
                    } else {
                        // 合并
                        $word_results = $this->merge_word_segment_result_to_word($lang_cd, $word_results, $cur_wordsegment_results, $sentence_duplicate_time, $flg_sentence_merge);
                    }
                }
            }

            // 排序
            arsort($word_results["words"]);

            // 保留50个
            $final_word_number = count($word_results["words"]);
            array_splice($word_results["words"], 50);

            // 调整ratio
            $MAX_RATIO = 200;
            if ($is_word_segment_success == true) {
                if ($word_results["max_number"] > 0) {
                    $ratio = $MAX_RATIO / $word_results["max_number"];
                    foreach ($word_results["words"] as $name => $value) {
                        $word_results["words"][$name] = $value * $ratio;
                    }
                }
            }

            $final_ret = [];
            $final_ret["record_count"] = $count;
            $final_ret["word_number"] = $final_word_number;
            $final_ret["word_count"] = $word_results["words"];
            if ($is_word_segment_success != true) {
                $final_ret["err"] = "curl_exec error";
            }
            $this->response->body(json_encode($final_ret));
            if ($debug_flg) {
                error_log("$log_id Exit action_wordcount()");
            }
        } catch (Exception $e) {
            $final_ret = [];
            $final_ret["record_count"] = 0;
            $final_ret["word_count"] = [];
            $err = $e->getMessage();
            $final_ret["err"] = $err;
            $this->response->body(json_encode($final_ret));
            if ($debug_flg) {
                error_log("$log_id Exit action_wordcount() with err： $err");
            }
        }
    }

    public function get_word_segment_result($bot_id, $sentence_mul, $sentence_duplicate_time, $lang_cd, $connectURL, $flg_sentence_merge)
    {
        $debug_flg = false;

        if ($debug_flg) {
            $sentence_number = count($sentence_mul);
            error_log("sentence_number =  $sentence_number");
        }
        $wordsegment_results = false;
        $curl = curl_init();
        $timeout = 5;
        $data = [
                "name"=>"keyword_analyzer",
                "bot_ids"=>"$bot_id",
                "sentence_mul"=> $sentence_mul,
                "sentence_duplicate_time"=> $sentence_duplicate_time,
                "flg_sentence_merge"=> $flg_sentence_merge,
                "lang_cd"=>"$lang_cd",
                "use_japanese_reading"=>false,
                "module"=>"sudachi",
            ];
        $header = [
                'Content-Type: application/json; charset=UTF-8',
            ];
        $condition = json_encode($data);

        curl_setopt($curl, CURLOPT_URL, $connectURL);
        curl_setopt($curl, CURLOPT_CUSTOMREQUEST, 'POST');
        curl_setopt($curl, CURLOPT_POSTFIELDS, $condition);
        curl_setopt($curl, CURLOPT_HTTPHEADER, $header);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_HEADER, true);
        //curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, $timeout);
        $response= curl_exec($curl);
        $http_code = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        if ($debug_flg) {
            error_log("curl error_code = $http_code");
        }
        if ($http_code == 200) {
            if ($debug_flg) {
                $temp_len = strlen($response);
                error_log("response len = $temp_len");
            }
            $header_size = curl_getinfo($curl, CURLINFO_HEADER_SIZE);
            $body = substr($response, $header_size);
            $wordsegment_results = json_decode($body, false);
        }
        curl_close($curl);

        return $wordsegment_results;
    }

    public function merge_word_segment_result_to_word($lang_cd, $word_results, $wordsegment_results, $sentence_duplicate_time, $flg_sentence_merge)
    {
        $log_id = $this->_log_id;
        //$lang_havebeen_merge = ["en", "ja", "cn", "tw", "kr","th"];
        //if (in_array($lang_cd,$lang_havebeen_merge)) {
        if ($flg_sentence_merge == "true") {
            // 已经在分词服务那边计算过duplicate_time了
            // $wordsegment_results = {
            //  keyword_array: "a,b",
            //  keyword_frequency_array: "10,2",
            // }
            // 2023.03.28 begin
            if ($wordsegment_results->keyword_array == "" || $wordsegment_results->keyword_frequency_array == "") {
                //error_log("$log_id keyword_array or keyword_frequency_array kara string");
                return $word_results;
            }
            // 2023.03.28 end

            $array_keyword = explode(",", $wordsegment_results->keyword_array);
            $array_keyword_frequence = explode(",", $wordsegment_results->keyword_frequency_array);
            $keyword_number = count($array_keyword);
            $frequency_number = count($array_keyword_frequence);

            //error_log("$log_id keyword_number = $keyword_number   and frequency_number = $frequency_number");
            for ($i=0; $i<$keyword_number; $i++) {
                $cur_keyword = $array_keyword[$i];
                $cur_keyword_frequence = $array_keyword_frequence[$i] - 0;
                if (array_key_exists($cur_keyword, $word_results["words"])) {
                    $word_results["words"][$cur_keyword] += $cur_keyword_frequence;
                } else {
                    $word_results["words"][$cur_keyword] = $cur_keyword_frequence;
                }
                if ($word_results["words"][$cur_keyword] > $word_results["max_number"]) {
                    $word_results["max_number"] = $word_results["words"][$cur_keyword];
                }
            }
            return $word_results;
        } else {
            $wordsegment_results_number = count($wordsegment_results);
            for ($record_index=0; $record_index<$wordsegment_results_number;$record_index++) {
                $cur_number = $sentence_duplicate_time[$record_index];

                if ($wordsegment_results[$record_index] == "") {
                    continue;
                }

                //$array_keyword = $wordsegment_results[$record_index]->keyword_array;
                $array_keyword = explode(",", $wordsegment_results[$record_index]);

                for ($i=0; $i<count($array_keyword); $i++) {
                    $cur_keyword = $array_keyword[$i];
                    if (array_key_exists($cur_keyword, $word_results["words"])) {
                        $word_results["words"][$cur_keyword] += $cur_number;
                    } else {
                        $word_results["words"][$cur_keyword] = $cur_number;
                    }
                    if ($word_results["words"][$cur_keyword] > $word_results["max_number"]) {
                        $word_results["max_number"] = $word_results["words"][$cur_keyword];
                    }
                }
            }
            return $word_results;
        }
    }

    public function wuzhao_create_group_bot_cond($bot_id, $col_name='bot_id')
    {
        $max_bot_id = intval($bot_id) - 0 + 1000;
        $sql = "SELECT bot_id FROM t_bot WHERE bot_id>=$bot_id AND bot_id<$max_bot_id";
        $query = DB::query(Database::SELECT, $sql);
        $results = $query->execute();
        $condition = "";
        foreach ($results as $it) {
            $cur_bot_id = $it['bot_id'];
            if ($condition == "") {
                $condition = "$col_name=$cur_bot_id";
            } else {
                $condition = "$condition OR $col_name=$cur_bot_id";
            }
        }
        $condition = " ($condition) ";

        return $condition;
    }
}
