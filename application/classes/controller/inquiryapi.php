<?php defined ( 'SYSPATH' ) or die ( 'No direct script access.' );

class Controller_Inquiryapi extends Controller_Template_Adminbase
{
    public $_model;

    public function __construct(Request $request, Response $response) {
      parent::__construct($request, $response);
      $this->_model = new Model_Adminmodel($this->_lang_cd);
      $this->_model->init($this->_bot_id);
    }

    public function action_call() 
    {
      $this->auto_render = FALSE;
      $this->response->headers('Access-Control-Allow-Origin', '*');
      $this->response->headers('Content-Type', 'application/json');
      $post = $this->request->post();
      try {
        $this->is_login();
        $action = $post['action'];
        if ($action == 'oauth_token') {
          $response = $this->get_oauth_token();
          $this->response->body(json_encode($response, JSON_UNESCAPED_UNICODE));
        }
      } catch (\Throwable $th) {
        $this->response->body(json_encode([
          'success' => 'false',
          'error' => $th->getMessage(),
        ]));
      }
    }

    private function get_oauth_token()
    {    
      try {
        $oauth_url = $this->_model->get_env('oauth_url');
        if (!$oauth_url) {
          throw new ErrorException('oauth_url not found');
        }
        $url = $oauth_url . "admin/oauth2/v2.0/token";
        $params = [
          'grant_type' => 'password',
          'user_id' => $this->_user_id,
          'password' => substr(md5($this->_user->password . 'nM9:C$HyGbLJ*f'), 0, 16)
        ];
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_HEADER, [
          'Content-Type: application/x-www-form-urlencoded',
        ]);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($params));
        $response = curl_exec($ch);
        if (curl_errno($ch)) {
          throw new ErrorException(curl_error($ch));
        }
        curl_close($ch);
        $header_size = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
        $body = substr($response, $header_size);
        return json_decode($body, true);
      } catch (\Throwable $th) {
        throw $th;
      }
    }

    private function is_login()
    {
      $login_user = Session::instance()->get('user', NULL);
      if (!$login_user) {
        throw new HTTP_Exception_403('Unauthorized');
      }
    }
}