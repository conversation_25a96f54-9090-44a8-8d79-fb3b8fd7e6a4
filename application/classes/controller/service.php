<?php defined('SYSPATH') or die('No direct script access.');
class Controller_Service extends Controller_Template_Normalbase{
	public $_bot_id;
	public $_bot;
	public $_model;

	public function __construct(Request $request, Response $response)
	{
		parent::__construct($request, $response);
		$this->_model = new Model_Adminmodel();
		ob_end_flush();
	}

	public function action_inquiry() {
		$post = $this->request->query();
		$sql = "SELECT * FROM t_inquiry WHERE inquiry_id=:id";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':id' => $post['id'],
		));
		$base = $query->execute()->as_array()[0];
		$base['inquiry_data'] = json_decode($base['inquiry_data'], true);

		$sql = "SELECT title, description, inquiry_image AS image, description_extra, confirm_info, complete_info FROM t_inquiry_description WHERE inquiry_id=:id AND lang_cd=:lang_cd";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':id' => $post['id'],
			':lang_cd' => $post['lang_cd'],
		));
		$description = $query->execute()->as_array()[0];
		if (!is_array($description['image'])) $description['image'] = json_decode($description['image'], true);
		if ($description['description_extra'] == '') {
			$description_extra = [];
		}
		else {
			$description_extra = json_decode($description['description_extra'], true);
		}
		if (trim($description['description']) != '') {
			array_unshift($description_extra, ['title'=>'', 'description'=>$description['description'], 'fold'=>'show']);
		}
		$description['input_info'] = $description_extra;
		if (!is_array($description['confirm_info'])) $description['confirm_info'] = json_decode($description['confirm_info'], true);
		if (!is_array($description['complete_info'])) $description['complete_info'] = json_decode($description['complete_info'], true);
		unset($description['description']);
		unset($description['description_extra']);

		$sql = "SELECT * FROM t_inquiry_entry WHERE inquiry_id=:id AND lang_cd=:lang_cd AND delete_flg=0";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':id' => $post['id'],
			':lang_cd' => $post['lang_cd'],
		));
		$entries = $query->execute()->as_array();
		$entrie_dict = [];
		foreach($entries as $entry) {
			$entrie_dict[$entry['no']] = $entry;
		}
		$sql = "SELECT no, entries FROM t_inquiry_section WHERE inquiry_id=:id AND lang_cd=:lang_cd AND delete_flg=0 ORDER BY sort_no";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':id' => $post['id'],
			':lang_cd' => $post['lang_cd'],
		));
		$sections = $query->execute()->as_array();
		$entries = [];
		foreach($sections as $section) {
			$section_entries = explode(',', $section['entries']);
			foreach($section_entries as $section_entry) {
				$entry = $entrie_dict[$section_entry];
				$entry['section_no'] = $section['no'];
				unset($entry['inquiry_id']);
				unset($entry['delete_flg']);
				if (!is_array($entry['input_rules'])) $entry['input_rules'] = json_decode($entry['input_rules'], true);
				if (!is_array($entry['entry_data'])) $entry['entry_data'] = json_decode($entry['entry_data'], true);
				$entries[] = $entry;
			}
		}

		$sql = "SELECT no, type, dest_no, conditions FROM t_inquiry_branch WHERE inquiry_id=:id AND lang_cd=:lang_cd";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':id' => $post['id'],
			':lang_cd' => $post['lang_cd'],
		));
		$branchs = $query->execute()->as_array();
		$result['$'] = $base;
		$result['description'] = $description;
		$result['entry'] = $entries;
		$result['branch'] = $branchs;
		ob_end_clean();
		$this->response->headers('Access-Control-Allow-Origin', '*');
		$this->response->body(json_encode($result, JSON_UNESCAPED_UNICODE));
	}

	public function action_survey() {

	}

	public function action_classcode()
	{
		$post = $this->request->post();
		$code_div = $post['div'];
		$parent_cd = $post['class_cd'];
		$item_class = $this->_model->get_class_code($code_div, $parent_cd);
		if (array_key_exists('blank', $post)) {
			$new_item_class = [];
			$new_item_class[] = ["class_cd"=>"", "name"=>"-", "sort"=>0];
			foreach($item_class as $k) {
				$new_item_class[] = $k;
			}
			$item_class = $new_item_class;
		}
		ob_end_clean();
		$this->response->body(json_encode($item_class));
	}
	
	public function action_chatpreview()
	{
		if ($this->request->post()){
			$post = $this->request->post();
			$this->_bot_id = $post['bot_id'];
			$chatpreview = View::factory('admin/chatpreview');
			$msg_type_cd = $post['type'];
			$chatpreview->msg_type_cd = $msg_type_cd;
			$chatpreview->preview = 1;
			if ($msg_type_cd == 'faq') {
				$chatpreview->faq = $post;
				$chatpreview->max_no = $post['max_no'];
				$chatpreview->intent_cd = '';
				$chatpreview->sub_intent_cd = '';
				$chatpreview->area_cd = '';
			}
			else if ($msg_type_cd == 'car') {
				$btn_name = $this->_model->get_bot_tpl_message($this->_bot_id, 'btn_name', $post['lang_cd']);
				$config_button = json_decode($btn_name, true);
				$chatpreview->button_list = $config_button;
				$buttons = [];
				if ($post['item_div'] < 6) {
					$def_buttons = $this->_model->get_bot_lst_message($this->_bot_id, 'item_def_button_' . $post['item_div'] . '_' . substr($post['class_cd'], 0, 2), $post['lang_cd']);
					if (count($def_buttons) == 0) $def_buttons = $this->_model->get_bot_lst_message($this->_bot_id, 'item_def_button_' . $post['item_div'], $post['lang_cd']);
					foreach($def_buttons as $def) {
						if (strpos($def['content'], "BTN_") === 0) {
							$def['content'] = $config_button[$def['content']];
						}
						$buttons[] = ["title"=>$def['content'], "url"=>""];
					}
				}
				if (array_key_exists('buttons', $post)) {
					$btn_name = [];
					foreach($post['buttons'] as $btn) {
						$btn_name[] = $btn['title'];
					}
					foreach($buttons as $btn) {
						if (!in_array($btn['title'], $btn_name)) $post['buttons'][] = $btn;
					}
				}
				else {
					$post['buttons'] = $buttons;
				}
				$chatpreview->item = $post;
			}
			else if ($msg_type_cd == 'btn' || $msg_type_cd == 'lst'  || $msg_type_cd == 'mnu') {
				$msg_orm_name = $this->_model->get_message_tbl($msg_type_cd);
				$btn_name = $this->_model->get_bot_tpl_message($this->_bot_id, 'btn_name', $post['lang_cd']);
				$config_button = json_decode($btn_name, true);
				$chatpreview->button_list = $config_button;
				$msg_desc_list = $this->_model->get_message_self_desc_list('', $msg_orm_name, $post['id'], $post['lang_cd']);
				$chatpreview->button_list = $config_button;
				$msg_desc_list = $this->_model->result2array($msg_desc_list);
				$exist = false;
				if (array_key_exists('menu_button', $post)) {
					$post['content'] = $post['menu_button'];
				}
				for($i=0; $i<count($msg_desc_list); $i++) {
					if ($msg_desc_list[$i]['no'] == $post['no']) {
						$msg_desc_list[$i]['content'] = $post['content'];
						$exist = true;
					}
					if ($post['no'] == 1) $msg_desc_list[0]['title'] = $post['title'];
				}
				if ($exist == false) {
					$msg_desc_list[] = ['title'=>$post['title'], 'content'=>$post['content'], 'no'=>count($msg_desc_list) + 1, 'lang_cd'=>$post['lang_cd']];
				}
				$chatpreview->msg_desc_list= $msg_desc_list;
			}
			else if ($msg_type_cd == 'txt') {
				$msg_orm_name = $this->_model->get_message_tbl($msg_type_cd);
				$msg_desc_list = $this->_model->get_message_self_desc_list('', $msg_orm_name, $post['id'], $post['lang_cd']);
				$msg_desc_list = $this->_model->result2array($msg_desc_list);
				$exist = false;
				for($i=0; $i<count($msg_desc_list); $i++) {
					if ($msg_desc_list[$i]['no'] == $post['no']) {
						$msg_desc_list[$i]['content'] = $post['content'];
						$exist = true;
					}
					if ($post['no'] == 1) $msg_desc_list[0]['title'] = $post['title'];
				}
				if ($exist == false) {
					$msg_desc_list[] = ['title'=>$post['title'], 'content'=>$post['content']];
				}
				$chatpreview->msg_desc_list= $msg_desc_list;
			}
			else if ($msg_type_cd == 'mal' || $msg_type_cd == 'tpl') {
				$msg_desc_list = [];
				$msg_desc_list[] = ['title'=>$post['title'], 'content'=>$post['content']];
				$chatpreview->msg_desc_list= $msg_desc_list;
			}
			else if ($msg_type_cd == 'img') {
				$msg_orm_name = $this->_model->get_message_tbl($msg_type_cd);
				$msg_desc_list = $this->_model->get_message_self_desc_list('', $msg_orm_name, $post['id'], $post['lang_cd']);
				$msg_desc_list = $this->_model->result2array($msg_desc_list);
				$exist = false;
				for($i=0; $i<count($msg_desc_list); $i++) {
					if ($msg_desc_list[$i]['no'] == $post['no']) {
						$msg_desc_list[$i]['title'] = $post['title'];
						$exist = true;
					}
				}
				if ($exist == false) {
					$msg_desc_list[] = ['title'=>$post['title'], 'url'=>'', 'no'=>count($msg_desc_list) + 1];
				}
				$chatpreview->msg_desc_list= $msg_desc_list;
			}
			else {
				$this->response->body('');
				return;
			}
			$chatpreview->public = 1;
			$chatpreview->s3_url = 1;
			$this->response->body($chatpreview);
		}
	}
	
	public function action_invoicebillingdl()
	{
		$post = $this->request->query();
		$url = $post['url'];
		
		$invoice_model = new Model_Invoicemodel();
		$access_token = $invoice_model->get_access_token();
		
		$header = ['Authorization:BEARER ' . $access_token, 'Content-Type:application/octet-stream'];
		$pdf_steam = $this->_model->curl_get($url, $header);
		
		DB::update('t_invoice_billing')->set(['bot_download_flg'=>1])->where('pdf_url', '=', $url)->execute();
		
		header("Content-Type: application/octet-stream");
		header("Content-Disposition: attachment; filename=billing.pdf");
		
		$f = fopen('php://output', 'w');
		ob_end_clean();
		fwrite($f, $pdf_steam);
		fclose($f);
	}
	
	public function action_faqkeywords() {
		$post = $this->request->query();
		if (!array_key_exists('facility_cd', $post)) {
			$this->response->body($this->_resultfail('パラメータエラー'));
		}
		$faq_model = new Model_Faqmodel();
		$bot_id = $this->_model->get_bot_by_scene($post['facility_cd'])['bot_id'];
		if (!array_key_exists('context', $post)) {
			$post['context'] = NULL;
		}
		$logs = $faq_model->get_faq_log_count($bot_id, $post['facility_cd'], $post['context'],$post['lang_cd'], 1);
		$keywords = [];
		$i=0;
		foreach($logs as $log) {
			$i++;
			if ($i > 5) break;
			$keywords[] = $log['content'];
		}
		$this->response->body($this->_resultsuccess($keywords));
	}
	
	private function _resultsuccess($data) {
		return json_encode(['result'=>'success', 'data'=>$data]);
	}
	private function _resultfail($data) {
		return json_encode(['result'=>'fail', 'message'=>$data]);
	}
	private function _jsoncallback($func_name, $data) {
		return $func_name . "(" . json_encode($data) . ")";
	}
}