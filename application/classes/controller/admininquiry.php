<?php defined('SYSPATH') or die('No direct script access.');

class Controller_Admininquiry extends Controller_Template_Adminbase {

	public $_auth_required = TRUE;
	public $_transactional = true;
	public $_model;
	public $_aws_model;
	public $_view_path = 'admin/inquiry/';
	public $_action_path = '/admininquiry/';
	const ITEM_DIV = 9;

	protected $_item_div = 9;
	protected $_inquiry_order_start = false;

	protected $navi_maps_inquiry = [
		'inquirys' => "6005",
		'inquiry' => "6001",
		'inquirydesc' => "6002",
		'inquiryentry' => "6003",
		'inquiryresult' => "6004",
		'inquiryrefer' => "6011",
		'labels' => "6017",
		'label' => "6018",
	];

	protected $navi_maps_order = [
		'calendars' => "6019",
		'calendar' => "6020",
		'inquirys' => "6026",
		'inquiry' => "6022",
		'inquirydesc' => "6023",
		'inquiryentry' => "6024",
		'inquiryresult' => "6025",
		'inquiryrefer' => "6027",
		'labels' => "6028",
		'label' => "6029",
	];

	public function __construct(Request $request, Response $response)
	{
		parent::__construct($request, $response);
		$this->_model = new Model_Adminmodel();
		$this->_aws_model = new Model_Aws();
		$this->_model->init($this->_bot_id);
		if ($this->_bot_id == 17) $this->_inquiry_order_start = true;
		ini_set('max_execution_time', 300); // 300 seconds = 5 minutes
	}

	public function action_newinquirys()
	{
		$errors = NULL;
		$message = NULL;
		$post = NULL;

		if ($this->_user->role_cd != '99') $this->redirect('/admin/top');
		
		if ($this->request->post()) {
			$post = $this->request->post();
			$orm = ORM::factory('inquiry', $post['inquiry_id']);
			$bot_id = $orm->bot_id;
			$this->_del_session('report_bot_id');
			$this->_del_session('report1_start_date');
			$this->_del_session('report1_end_date');
			$this->_del_session('report1_scene_cd');
			$this->_del_session('report1_context_id');
			$this->_del_session('report8_scene_cd');
			$this->_del_session('report8_context_id');
			$this->_del_session('task_id');
			$this->_del_session('survey_result_tags');

			Session::instance()->delete('surveys_user_in_charge');
			Session::instance()->delete('items_class_cd');
			Session::instance()->delete('items_area_cd');
			$bot = ORM::factory('bot', $bot_id);
			Session::instance()->set('bot', $bot);
			Session::instance()->set('bot_id', $bot->bot_id);
			Session::instance()->set('bot_setting', $this->_model->get_bot_setting_dict($bot_id));
			Session::instance()->set('service_useflg', $this->_model->get_service_useflg($bot->bot_id));
			$item = ORM::factory('item')->where('bot_id', '=', $bot->bot_id)->find();
			Session::instance()->set('item_id', $item->item_id);
			Session::instance()->set('item_cd', $item->item_cd);
			// user of selected bot

			// update last_login_date
			$user = ORM::factory('user', $this->_user->user_id);
			$user->last_login_date = date('Y-m-d H:i:s');
			$user->save();

			// switch bot access for statistics
			$this->_bot_id = $bot_id;
			$this->_action = 'login';
			$this->_write_access_log();
			$this->_del_session('chat_bot_id');
			$this->redirect('/admininquiry/inquiryresult?type=detail&id=' . $post['inquiry_id']);
		}

		list($items, $results, $entries) = $this->_model->get_admin_newinquirys();
		
		$view = View::factory($this->_view_path . 'newinquirys');

		$view->post = $post;
		$view->items = $items;
		$view->results = $results;
		$view->entries = $entries;
		$view->verify_url = $this->_model->get_env('inquiry_url');
		$this->template->content = $view;
	}

	public function action_inquirys()
	{
		$errors = NULL;
		$message = NULL;
		$post = NULL;
		$item_div = self::ITEM_DIV;

		if ($this->_inquiry_order_start) {
			if ($this->_item_div == 9 && $this->_service_useflg['use_inquiry_flg'] !== true) $this->redirect('/admin/top');
			if ($this->_item_div == 19 && $this->_service_useflg['use_order_flg'] !== true) $this->redirect('/admin/top');
		} else {
			if ($this->_bot_setting['flg_next_inquiry'] == 0) $this->redirect('/admin/top');
		}

		if ($this->request->post()) {
			$post = $this->request->post();
			if ($post['act'] == 'newcopy' || $post['act'] == 'version') {
				$new_id = $this->_copy_inquiry($post['inquiry_id'], $post['act'], $this->_bot_id);
				if ($new_id != 0) {
					$this->redirect($this->_action_path . 'inquiry?id=' . $new_id);
				}
				else {
					$message = __("common.error.front.title");
				}
			}
			else if ($post['act'] == 'copy_template') {
				$copy_inquiry_id = explode(',', $post['from_inquiry_id']);
				foreach($copy_inquiry_id as $cid) {
					$this->_copy_inquiry($cid, 'template', $this->_bot_id);
				}
				$this->redirect($this->_action_path . 'inquirys');
			}
			else if ($post['act'] == 'newinquiry') {
				$orm = ORM::factory('inquiry', $post['inquiry_id']);
				$orm->renew_time = date('Y-m-d H:i:s');
				$orm->upd_user = $this->_user->user_id;
				$orm->upd_time = date('Y-m-d H:i:s');
				$orm->save();
				$this->_model->send_watch_mail('【' . $this->_bot->bot_name . '】INQUIRYを新版に切り替えました', $orm->inquiry_id . ' ' . $orm->inquiry_name);
				$this->redirect($this->_action_path . 'inquirys');
			}
			else if ($post['act'] == 'oldinquiry') {
				$orm = ORM::factory('inquiry', $post['inquiry_id']);
				$orm->renew_time = null;
				$orm->upd_user = $this->_user->user_id;
				$orm->upd_time = date('Y-m-d H:i:s');
				$orm->save();
				$this->_model->send_watch_mail('【' . $this->_bot->bot_name . '】INQUIRYを旧版に切り替えました', $orm->inquiry_id . ' ' . $orm->inquiry_name);
				$this->redirect($this->_action_path . 'inquirys');
			}
		}
		else {
			$post['start_date'] = Session::instance()->get('inquirys_start_date', NULL);
			if ($post['start_date'] === NULL) $post['start_date'] = date('Y-m-d');
			$post['end_date'] = Session::instance()->get('inquirys_end_date', NULL);
			if ($post['end_date'] === NULL) $post['end_date'] = '';
			$post['user_in_charge'] = Session::instance()->get('inquirys_user_in_charge', NULL);
			if ($post['user_in_charge'] === NULL) $post['user_in_charge'] = 0;
			if ($this->_user->auth_all_contents_flg == 0) {
				$post['user_in_charge'] = strval($this->_user->user_id);
			}
			$post['template_cd'] = Session::instance()->get('inquirys_template_cd', NULL);
			if ($post['template_cd'] === NULL) $post['template_cd'] = '';
			$class_cd = $this->request->query('class', NULL);
			if ($class_cd == NULL) {
				$post['class_cd_cond'] = Session::instance()->get('inquirys_class_cd', NULL);
			}
			else {
				$post['class_cd_cond'] = $class_cd;
			}
			if ($post['class_cd_cond'] == NULL) $post['class_cd_cond'] = '';
		}
		Session::instance()->set('inquirys_start_date', $post['start_date']);
		Session::instance()->set('inquirys_end_date', $post['end_date']);
		Session::instance()->set('inquirys_user_in_charge', $post['user_in_charge']);
		Session::instance()->set('inquirys_template_cd', $post['template_cd']);
		Session::instance()->set('inquirys_class_cd', $post['class_cd_cond']);

		$code_div = $this->_model->get_bot_setting($this->_bot_id, 'div_item_class_' . $item_div);
		$code_div_dict = $this->_model->get_code_div_kv($code_div);
		
		$json_next_inquiry_setting = $this->_model->get_bot_setting($this->_bot_id, 'json_next_inquiry_setting', true);
		if (!array_key_exists('user_in_charge_required', $json_next_inquiry_setting)) $json_next_inquiry_setting['user_in_charge_required'] = 0;
		if ($json_next_inquiry_setting['user_in_charge_required'] == 0) $post['user_in_charge'] = '';
		
		list($items, $results, $unsupports, $access_count, $entries, $maximums) = $this->_model->get_admin_inquirys($this->_bot_id, $post['user_in_charge'], $post['template_cd'], $post['class_cd_cond'], $this->_lang_cd, $this->_inquiry_order_start ? $this->_item_div : null);
		$items_show = [];
		$items_not_show = [];
		if ($post['start_date'] != NULL) {
			$start_date_cond = $post['start_date'];
		}
		else {
			$start_date_cond = '0000-01-01';
		}
		if ($post['end_date'] != NULL) {
			$end_date_cond = $post['end_date'];
		}
		else {
			$end_date_cond= '9999-12-31';
		}
		for($i=0; $i<count($items); $i++) {
			$item = $items[$i];
			if ($item['start_date'] != NULL) {
				$start_date = $item['start_date'];
			}
			else {
				$start_date = '0000-01-01';
			}
			if ($item['end_date'] != NULL) {
				$end_date = $item['end_date'];
			}
			else {
				$end_date = '9999-12-31';
			}
			if ($end_date < $start_date_cond || $start_date > $end_date_cond) {
				$items_not_show[] = $item;
			}
			else {
				$items_show[] = $item;
			}
		}
		$view = View::factory($this->_view_path . 'inquirys');
		$menu = View::factory($this->_view_path . 'inquirymenu');
		$menu->inquiry_id = NULL;
		$menu->lang_display = [];
		$view->menu = $menu;
		$view->code_div_dict = $code_div_dict;

		if ($json_next_inquiry_setting['user_in_charge_required'] == 1) {
			if ($this->_user->auth_all_contents_flg == 0 && $this->_user->role_cd != '99' && $this->_user->role_cd != '09') {
				$view->user_list = [strval($this->_user->user_id) =>$this->_user->name];
			}
			else {
				$view->user_list = [''=>__('admin.common.label.person_in_charge.all')] + $this->_model->get_bot_user_dict($this->_bot_id);
			}
		}
		else {
			$view->user_list = [''=>__('admin.common.label.person_in_charge.all')];
		}
		if (isset($json_next_inquiry_setting['template_bot_id'])) {
			$view->template_bot_id = $json_next_inquiry_setting['template_bot_id'];
		}
		else {
			$base_settings = $this->_model->get_setting('inquiry_settings');
			$view->template_bot_id = $base_settings['template_bot_id'];
		}
		$view->template_list = [''=>__('admin.common.label.all.template')] + $this->_model->get_scene_template($this->_bot_id, 'inquiry', '');
		$view->item_div = $item_div;
		$view->inquiry_div = $this->_inquiry_order_start ? $this->_item_div : self::ITEM_DIV;
		$view->div_item_class = $code_div;
		$view->post = $post;
		$view->items = $items_show;
		$view->results = $results;
		$view->unsupports = $unsupports;
		$view->access_count = $access_count;
		$view->entries = $entries;
		$view->maximums = $maximums;
		$view->json_next_inquiry_setting = $json_next_inquiry_setting;
		$view->verify_url = $this->_model->get_env('inquiry_url');
		$this->template->content = $view;
	}

	private function _copy_inquiry($inquiry_id, $mode, $target_bot_id) {
		Database::instance()->begin();
		try {
			$item_div = $this->_inquiry_order_start ? $this->_item_div : 9;
			$orm = ORM::factory('inquiry', $inquiry_id);
			$from_bot_id = $orm->bot_id;
			$from_label_id = $orm->label_id;
			$inquiry = ORM::factory('inquiry');
			$inquiry->inquiry_id = $this->_model->get_max_inquiry_id($target_bot_id);
			$support_lang_array = array_keys($this->_bot_lang);
			if ($mode == 'version') {
				if ($orm->base_ver_id == null) {
					$inquiry->base_ver_id = $inquiry_id;
				}
				else {
					$inquiry->base_ver_id = $orm->base_ver_id;
				}
				$inquiry->inquiry_name = substr($orm->inquiry_name . '-改版', 0, 200);
				$inquiry->start_date = null;
				$inquiry->end_date = null;
				$inquiry->secret_mode = 1;
				$inquiry->password = 'pass' . $inquiry_id;
			}
			else {
				$inquiry->inquiry_name = substr($orm->inquiry_name . '-コピー', 0, 200);
				$inquiry->start_date = $orm->start_date;
				$inquiry->end_date = $orm->end_date;
				$inquiry->secret_mode = $orm->secret_mode;
				$inquiry->password = $orm->password;
			}
			$inquiry->inquiry_cd = substr(md5(uniqid(mt_rand(), true)), 0, 16);
			$inquiry->bot_id = $target_bot_id;
			$inquiry->item_div = $orm->item_div;
			$inquiry->class_cd = $orm->class_cd;
			$inquiry->member_mail_from = $orm->member_mail_from;
			$inquiry->member_mail_replyto = $orm->member_mail_replyto;
			$inquiry->inquiry_status_cd = $orm->inquiry_status_cd;
			$inquiry->support_lang_cd = $this->_extractSupportedLang($orm->support_lang_cd, $support_lang_array);
			$inquiry->inquiry_type_cd = $orm->inquiry_status_cd;
			$inquiry->support_flg = $orm->support_flg;
			$inquiry->duration = $orm->duration;
			$inquiry->present = $orm->present;
			$inquiry->redirect_url = $orm->redirect_url;
	
			if ($orm->bot_id == $target_bot_id) {
				$inquiry->user_in_charge = $orm->user_in_charge;
				$inquiry->member_mail_template = $orm->member_mail_template;
				$inquiry->user_mail_template = $orm->user_mail_template;
				$inquiry->mail_member_flg = $orm->mail_member_flg;
				$inquiry->mail_user_flg = $orm->mail_user_flg;
				$inquiry->mail_users = $orm->mail_users;
				$inquiry->scene_cd = $orm->scene_cd;
				$inquiry->faq_scene = $orm->faq_scene;
				$inquiry->template_cd = $orm->template_cd;
			}
			else {
				if ($mode == 'template') {
					$inquiry->member_mail_template = $orm->member_mail_template;
					$inquiry->user_mail_template = $orm->user_mail_template;
					$inquiry->mail_member_flg = $orm->mail_member_flg;
					$inquiry->mail_user_flg = $orm->mail_user_flg;
				}
				$inquiry->scene_cd = $this->_model->get_bot_setting($target_bot_id, 'default_scene_cd');
			}
			$inquiry->inquiry_data = $orm->inquiry_data;
			$inquiry->label_id = $orm->label_id;
			$inquiry->upd_user = $this->_user->user_id;
			$inquiry->upd_time = date('Y-m-d H:i:s');
			$inquiry->renew_time = date('Y-m-d H:i:s');
			$inquiry->save();
	
			$inquiry_data = json_decode($orm->inquiry_data, true);
			if ($inquiry_data['receiption_id_prefix'] != '' && $inquiry_data['receiption_id_prefix'] != '?') {
				$seq = ORM::factory('sequence');
				$seq->app_div = 1;
				$seq->id = $inquiry->inquiry_id;
				$seq->seq = 0;
				$seq->save();
			}

			$orms = ORM::factory('inquirydescription')->where('inquiry_id', '=', $inquiry_id)->find_all();
			foreach($orms as $orm) {
				if (!in_array($orm->lang_cd, $support_lang_array)) continue;
				$inquiry_desc = ORM::factory('inquirydescription');
				$inquiry_desc->inquiry_id = $inquiry->inquiry_id;
				$inquiry_desc->lang_cd = $orm->lang_cd;
				$inquiry_desc->title = $orm->title;
				$inquiry_desc->description = $orm->description;
				$inquiry_desc->description_extra = $orm->description_extra;
				$inquiry_desc->confirm_info = $orm->confirm_info;
				$inquiry_desc->complete_info = $orm->complete_info;
				$inquiry_desc->inquiry_image = $orm->inquiry_image;
				$inquiry_desc->delete_flg = $orm->delete_flg;
				$inquiry_desc->save();
			}
			$orms = ORM::factory('itemdisplay')->where('item_id', '=', $inquiry_id)->where('item_div', '=', $item_div)->find_all();
			foreach($orms as $orm) {
				$inquiry_display = ORM::factory('itemdisplay');
				$inquiry_display->item_id = $inquiry->inquiry_id;
				$inquiry_display->item_div = $item_div;
				$inquiry_display->bot_id = $target_bot_id;
				$inquiry_display->lang_display = $this->_extractSupportedLang($orm->lang_display, $support_lang_array);
				$inquiry_display->public_flg = $orm->public_flg;
				$inquiry_display->save();
			}
			// 改版時、Label自動作成 不要
			/*
			if ($mode == 'version' && $inquiry->label_id == NULL) {
				$orms = ORM::factory('inquiryentry')->where('inquiry_id', '=', $inquiry_id)->where('lang_cd', '=', 'ja')->order_by('no')->find_all();
				$labels = [];
				$ptr = 1;
				foreach($orms as $orm) {
					$labels[strval($ptr)] = strip_tags($orm->title);
					DB::update('t_inquiry_entry')->set(['label_no'=>$ptr])->where('inquiry_id', '=', $inquiry_id)->where('no', '=', $orm->no)->where('lang_cd', '=', $orm->lang_cd)->execute();
					$ptr++;
				}
				$entrylabel = ORM::factory('entrylabel');
				$entrylabel->bot_id = $target_bot_id;
				$entrylabel->label_div = 'inquiry';
				$entrylabel->label_name = $src_inquiry_name;
				$entrylabel->labels = json_encode($labels, JSON_UNESCAPED_UNICODE);
				$entrylabel->upd_user = $this->_user->user_id;
				$entrylabel->save();
				DB::update('t_inquiry')->set(['label_id'=>$entrylabel->label_id])->where('inquiry_id', '=', $inquiry_id)->execute();
				DB::update('t_inquiry')->set(['label_id'=>$entrylabel->label_id])->where('inquiry_id', '=', $inquiry->inquiry_id)->execute();
			}
			*/
			$orms = ORM::factory('inquiryentry')->where('inquiry_id', '=', $inquiry_id)->find_all();
			$maximums_created = [];
			foreach($orms as $orm) {
				if (!in_array($orm->lang_cd, $support_lang_array)) continue;
				$entry_data = $orm->entry_data;
				if ($mode != 'version' && $from_bot_id != $target_bot_id) {
					// 枠コピー
					$entry_data = json_decode($orm->entry_data, true);
					foreach($entry_data as &$e) {
						if (!is_array($e)) break;
						if (!isset($e['maximum'])) continue;
						if (!isset($maximums_created[$e['maximum']])) {
							$maximum = $this->_copy_maximum($e['maximum'], $target_bot_id);
							$maximums_created[$e['maximum']] = $maximum->id;
						}
						$e['maximum'] = $maximums_created[$e['maximum']];
					}
					$entry_data = json_encode($entry_data, JSON_UNESCAPED_UNICODE);
				}
				$inquiry_entry = ORM::factory('inquiryentry');
				$inquiry_entry->inquiry_id = $inquiry->inquiry_id;
				$inquiry_entry->no = $orm->no;
				$inquiry_entry->lang_cd = $orm->lang_cd;
				$inquiry_entry->title = $orm->title;
				$inquiry_entry->title_description = $orm->title_description;
				$inquiry_entry->placeholder = $orm->placeholder;
				$inquiry_entry->description = $orm->description;
				$inquiry_entry->entry_type_cd = $orm->entry_type_cd;
				$inquiry_entry->required = $orm->required;
				$inquiry_entry->input_rules = $orm->input_rules;
				$inquiry_entry->entry_data = $entry_data;
				$inquiry_entry->next_page = $orm->next_page;
				$inquiry_entry->actions = $orm->actions;
				$inquiry_entry->delete_flg = $orm->delete_flg;
				$inquiry_entry->label_no = $orm->label_no;
				$inquiry_entry->labels = $orm->labels;
				$inquiry_entry->save();
			}
			$orms = ORM::factory('inquirybranch')->where('inquiry_id', '=', $inquiry_id)->find_all();
			foreach($orms as $orm) {
				if (!in_array($orm->lang_cd, $support_lang_array)) continue;
				$inquiry_branch = ORM::factory('inquirybranch');
				$inquiry_branch->inquiry_id = $inquiry->inquiry_id;
				$inquiry_branch->no = $orm->no;
				$inquiry_branch->lang_cd = $orm->lang_cd;
				$inquiry_branch->title = $orm->title;
				$inquiry_branch->type = $orm->type;
				$inquiry_branch->dest_no = $orm->dest_no;
				$inquiry_branch->conditions = $orm->conditions;
				$inquiry_branch->save();
			}
			$orms = ORM::factory('inquiryentryaction')->where('inquiry_id', '=', $inquiry_id)->find_all();
			foreach($orms as $orm) {
				if (!in_array($orm->lang_cd, $support_lang_array)) continue;
				$inquiry_action = ORM::factory('inquiryentryaction');
				$inquiry_action->inquiry_id = $inquiry->inquiry_id;
				$inquiry_action->no = $orm->no;
				$inquiry_action->lang_cd = $orm->lang_cd;
				$inquiry_action->actions = $orm->actions;
				$inquiry_action->save();
			}
			$orms = ORM::factory('inquirysection')->where('inquiry_id', '=', $inquiry_id)->find_all();
			foreach($orms as $orm) {
				if (!in_array($orm->lang_cd, $support_lang_array)) continue;
				$inquiry_section = ORM::factory('inquirysection');
				$inquiry_section->inquiry_id = $inquiry->inquiry_id;
				$inquiry_section->no = $orm->no;
				$inquiry_section->lang_cd = $orm->lang_cd;
				$inquiry_section->title = $orm->title;
				$inquiry_section->entries = $orm->entries;
				$inquiry_section->sort_no = $orm->sort_no;
				$inquiry_section->delete_flg = $orm->delete_flg;
				$inquiry_section->save();
			}
			if ($mode != 'version' && $from_bot_id != $target_bot_id && $from_label_id != NULL) {
				// Label作成
				$label = ORM::factory('entrylabel', $from_label_id);
				$new_label = ORM::factory('entrylabel');
				$new_label->bot_id = $target_bot_id;
				$new_label->label_div = $label->label_div;
				$new_label->label_name = $label->label_name;
				$new_label->labels = $label->labels;
				$new_label->upd_user = $this->_user->user_id;
				$new_label->save();
				$inquiry->label_id = $new_label->label_id;
				$inquiry->save();
			}
			Database::instance()->commit();
			return $inquiry->inquiry_id;
		}
		catch(Exception $e) {
			Database::instance()->rollback();
			return 0;
		}

	}

	private function _copy_maximum($maximum_id, $target_bot_id) {
		$orm = ORM::factory('botmaximum')->where('id', '=', $maximum_id)->find();
		$maximum = ORM::factory('botmaximum');
		$new_id = $this->_model->get_max_maximum_id($target_bot_id);
		$maximum->bot_id = $target_bot_id;
		$maximum->id = $new_id;
		$maximum->name = $orm->name;
		$maximum->span = $orm->span;
		$maximum->maximum_data = $orm->maximum_data;
		$maximum->extra_data = $orm->extra_data;
		$maximum->upd_user = $this->_user->user_id;
		$maximum->upd_time = date('Y-m-d H:i:s');
		$maximum->save();
		return $maximum;
	}
	/**
	 * Extracts supported languages from a given list of languages.
	 * @param string $lang The comma-separated list of languages. // ja,en,ko
	 * @param array $support_lang An array containing supported languages. // ['ja','en']
	 * @return string|null Returns a comma-separated string of supported languages
	 *                     found in the input $lang, or null if no common values exist. // ja,en || null
	 */
	private function _extractSupportedLang($lang, $support_lang) {
		$lang_string = explode(',', $lang);
		$extracted_lang = array_intersect($lang_string, $support_lang);
		if (!empty($extracted_lang)) {
			return implode(',', $extracted_lang);
		} else {
			return null;
		}
	}

	public function action_inquiry()
	{
		$errors = NULL;
		$message = NULL;
		$post = NULL;
		$inquiry = NULL;
		$inquiry_div = NULL;
		$inquiry_id = NULL;
		$code_div = $this->_model->get_bot_setting($this->_bot_id, 'div_item_class_' . self::ITEM_DIV);
		$item_data_def = $this->_model->get_item_tpl_message($this->_bot_id, self::ITEM_DIV, '', 'item_data', 'ja');

		if ($this->request->post()){
			$post = $this->request->post();
			$inquiry_id = $post['inquiry_id'];
			if ($this->_page_action == 'delete') {
				$inquiry = ORM::factory('inquiry', $inquiry_id);
				$inquiry_div = $this->_inquiry_order_start ? $inquiry->item_div : NULL;
				DB::update('t_inquiry')->set(['delete_flg'=>1])->where('inquiry_id', '=', $inquiry_id)->execute();
				$orms = ORM::factory('inquiry')->where('member_mail_from', '=', $inquiry->member_mail_from)->where('delete_flg', '=', 0)->find_all();
				if (count($orms) == 0) {
					$data = $this->_model->post_enginehook('service', 'delete_mail_identity','', ['mail'=>$inquiry->member_mail_from]);
				}
				$url = ($inquiry_div == NULL || $inquiry_div == 9) ? '/admininquiry/inquirys' : '/adminorder/inquirys';
				$this->redirect($url);
			}
			else {
				if ($inquiry_id == NULL) {
					$inquiry = ORM::factory('inquiry');
					$inquiry->inquiry_id = $this->_model->get_max_inquiry_id($this->_bot_id);
					$inquiry->inquiry_cd = substr(md5(uniqid(mt_rand(), true)), 0, 16);
					$inquiry->bot_id = $this->_bot_id;
					$inquiry->renew_time = date('Y-m-d H:i:s');
					$old_mail = '';
					$inquiry_data = [];
					$inquiry->item_div = $this->_inquiry_order_start ? $this->_item_div : NULL;
				}
				else {
					$inquiry = ORM::factory('inquiry', $inquiry_id);
					$inquiry_data = json_decode($inquiry->inquiry_data, true);
					//$item_data = json_decode($inquiry->inquiry_data, true);
					$old_mail = $inquiry->member_mail_from;
				}
				$inquiry->inquiry_name = trim($post['inquiry_name']);
				$inquiry->member_mail_from = trim($post['member_mail_from']);
				$inquiry->member_mail_replyto = trim($post['member_mail_replyto']);
				$inquiry->class_cd = implode(' ', json_decode($post['class_cd'], true));
				$inquiry->inquiry_status_cd = '01';
				$inquiry->inquiry_type_cd = '000';
				if (strlen($post['start_time']) == 4) $post['start_time'] = "0" . $post['start_time'];
				if (strlen($post['end_time']) == 4) $post['end_time'] = "0" . $post['end_time'];
				if ($post['start_date'] == '') {
					$inquiry->start_date = NULL;
				}
				else {
					$inquiry->start_date = $post['start_date'] . ' ' . $post['start_time'];
				}
				if ($post['end_date'] == '') {
					$inquiry->end_date = NULL;
				}
				else {
					$inquiry->end_date = $post['end_date'] . ' ' . $post['end_time'];
				}
				$inquiry->duration = $post['duration'];
				if ($post['present'] == '') {
					$inquiry->present = NULL;
				}
				else {
					$inquiry->present = $post['present'];
				}
				$inquiry->support_flg = $post['support_flg'];
				$inquiry->scene_cd = $post['scene_cd'];
				$inquiry->template_cd = $post['template_cd'];
				//$inquiry->redirect_url = $post['redirect_url'];
				$inquiry->member_mail_template = $post['member_mail_template'];
				$inquiry->user_mail_template = $post['user_mail_template'];
				if ($post['member_mail_template'] == '') {
					$inquiry->mail_member_flg = 0;
				}
				else {
					$inquiry->mail_member_flg = 1;
				}
				if ($post['user_mail_template'] == '') {
					$inquiry->mail_user_flg = 0;
				}
				else {
					$inquiry->mail_user_flg = 1;
				}
				
				$input_policy_opening = [];
				if(array_key_exists('input_policy_opening', $post)){
					$decoded = json_decode($post['input_policy_opening']);
					foreach($decoded as $item){
						$input_policy_opening[] = trim($item);
					}
				}
				if (!isset($post['receiption_id_prefix'])) $post['receiption_id_prefix'] = '?';
				$inquiry_data = 
					[
						'cancelable'=>$post['cancelable'],
						'cancel_policy'=>json_decode($post['cancel_policy'], true),
						'receiption_id_prefix'=>$post['receiption_id_prefix'],
						'gtm_tag_id'=>$post['gtm_tag_id'],
						'payment' => [],
						'mail_sender'=>$post['mail_sender'],
					];
				if ($post['inquiry_mail_signature']) {
					$inquiry_data['mail_signature'] = $post['inquiry_mail_signature'];
				} else {
					unset($inquiry_data['mail_signature']);
				}
				if ($post['use_cart'] == 1) {
					$inquiry_data['use_cart'] = 1;
				}
				if ($post['2_step'] == 1) {
					$inquiry_data['payment'] = ['2_step'=>1, 'pay_in_hours'=>$post['pay_in_hours']];
				}
				if ($post['sandbox'] == 1) {
					$inquiry_data['payment']['sandbox'] = 1;
				}
				if (!empty($input_policy_opening)) {
					$inquiry_data['input_policy'] = ['opening_time'=>$input_policy_opening];
				}
				if (isset($post['modifiable'])) {
					$inquiry_data['modifiable'] = $post['modifiable'];
					if ($inquiry_data['modifiable'] == '1') $inquiry_data['modify_policy'] = json_decode($post['modify_policy'], true);
				}
				if (isset($post['remind_flg'])) {
					$inquiry_data['remind_flg'] = $post['remind_flg'];
					if ($inquiry_data['remind_flg'] == '1') $inquiry_data['remind'] = json_decode($post['remind'], true);
				}
				if (array_key_exists('secret_mode', $post) && $post['secret_mode'] == '1') {
					$inquiry->secret_mode = 1;
					$inquiry->password = $post["password"];
				}
				else {
					$inquiry->secret_mode = 0;
					$inquiry->password = '';
				}
				if ($post['assist'] == 1) {
					$inquiry_data['pms'] = [
						'kind' => 'assist',
						'version' => ''
					];
				}
				if ($post['is_mail_logged'] == 1) {
					$inquiry_data['is_mail_logged'] = 1;
				}

				$receipt_setting = array();
				if(array_key_exists('receipt_setting', $post) && $post['receipt_setting'] == '1') {
					if (isset($post['tax_rate']) && is_numeric($post['tax_rate']) !== FALSE) {
						$receipt_setting = array_merge($receipt_setting, array('tax_rate' => intval($post['tax_rate'])));
						$inquiry_data['receipt_setting'] = $receipt_setting;
					}
				}

				if ($post['user_in_charge'] != '') $inquiry->user_in_charge = $post['user_in_charge'];
				$inquiry->mail_users = $post['mail_users'];
				if ($post['answer_limit'] == 1) {
					$inquiry_data = array_merge($inquiry_data, array('answer_limit'=>1));
				}
				else {
					unset($inquiry_data['answer_limit']);
				}
				if ($post['inquiry_answer_limit'] == '') {
					unset($inquiry_data['inquiry_answer_limit']);
				}
				else {
					$inquiry_data = array_merge($inquiry_data, array('inquiry_answer_limit'=>$post['inquiry_answer_limit']));
				}
				if ($post['redirect_url'] == '') {
					unset($inquiry_data['redirect_url']);
				}
				else {
					$inquiry_data = array_merge($inquiry_data, array('redirect_url'=>$post['redirect_url']));
				}
				if (isset($post['notification_message']) && !empty($post['notification_message'])) {
					$inquiry_data['message'] = array(
						'short' => $post['notification_message']
					);
				}
				if ($post['member_mail_template_thanks'] != '') {
					$inquiry_data['thanks'] = ["template"=>$post['member_mail_template_thanks'], "day"=>$post['thanks_mail_day'], "time"=>$post['thanks_mail_time']];
				}
				$inquiry->inquiry_data = json_encode($inquiry_data, JSON_UNESCAPED_UNICODE);
				$inquiry->secret_mode = $post['secret_mode'];
				$inquiry->faq_scene = $post['faq_scene'];
				$inquiry->support_lang_cd = implode(',', json_decode($post['support_lang_cd'], true));
				if (trim($post["base_ver_id"]) == '') {
					$inquiry->base_ver_id = null;
				}
				else {
					$inquiry->base_ver_id = trim($post["base_ver_id"]);
				}
				if ($post['label_id'] == '') {
					$inquiry->label_id = NULL;
				}
				else {
					$inquiry->label_id = $post['label_id'];
				}
				$inquiry->upd_user = $this->_user->user_id;
				$inquiry->upd_time = date('Y-m-d H:i:s');
				$inquiry->save();

				$item_div = $inquiry->item_div ?? self::ITEM_DIV;
				// 新しい言語ON
				if ($inquiry_id != NULL) {
					$display = ORM::factory('itemdisplay')->where('bot_id', '=', $this->_bot_id)->where('item_id', '=', $inquiry->inquiry_id)->where('item_div', '=', $item_div)->find();
					if (isset($display->lang_display)) {
						$old_lang_display = explode(',', $display->lang_display);
					}
					else {
						$old_lang_display = [];
					}
					$new_lang_display = json_decode($post['lang_display'], true);
					$copy_from_lang_cd = '';
					foreach($old_lang_display as $o) {
						$orms = ORM::factory('inquiryentry')->where('inquiry_id', '=', $inquiry_id)->where('lang_cd', '=', $o)->find_all();
						if (count($orms) > 0) {
							$copy_from_lang_cd = $o;
						}
					}
					foreach($new_lang_display as $n) {
						if (!in_array($n, $old_lang_display)) {
							$orms = ORM::factory('inquirydescription')->where('inquiry_id', '=', $inquiry_id)->where('lang_cd', '=', $n)->find_all();
							if (count($orms) == 0) {
								$orms = ORM::factory('inquirydescription')->where('inquiry_id', '=', $inquiry_id)->where('lang_cd', '=', $copy_from_lang_cd)->find_all();
								foreach($orms as $orm) {
									$inquiry_desc = ORM::factory('inquirydescription');
									$inquiry_desc->inquiry_id = $inquiry->inquiry_id;
									$inquiry_desc->lang_cd = $n;
									$inquiry_desc->title = $orm->title;
									$inquiry_desc->description = $orm->description;
									$inquiry_desc->description_extra = $orm->description_extra;
									$inquiry_desc->confirm_info = $orm->confirm_info;
									$inquiry_desc->complete_info = $orm->complete_info;
									$inquiry_desc->inquiry_image = $orm->inquiry_image;
									$inquiry_desc->delete_flg = $orm->delete_flg;
									$inquiry_desc->save();
								}
							}
							$orms = ORM::factory('inquiryentry')->where('inquiry_id', '=', $inquiry_id)->where('lang_cd', '=', $n)->find_all();
							if (count($orms) == 0) {
								$orms = ORM::factory('inquiryentry')->where('inquiry_id', '=', $inquiry_id)->where('lang_cd', '=', $copy_from_lang_cd)->find_all();
								foreach($orms as $orm) {
									$inquiry_entry = ORM::factory('inquiryentry');
									$inquiry_entry->inquiry_id = $inquiry->inquiry_id;
									$inquiry_entry->no = $orm->no;
									$inquiry_entry->lang_cd = $n;
									$inquiry_entry->title = $orm->title;
									$inquiry_entry->title_description = $orm->title_description;
									$inquiry_entry->placeholder = $orm->placeholder;
									$inquiry_entry->description = $orm->description;
									$inquiry_entry->entry_type_cd = $orm->entry_type_cd;
									$inquiry_entry->required = $orm->required;
									$inquiry_entry->input_rules = $orm->input_rules;
									$inquiry_entry->entry_data = $orm->entry_data;
									$inquiry_entry->next_page = $orm->next_page;
									$inquiry_entry->actions = $orm->actions;
									$inquiry_entry->delete_flg = $orm->delete_flg;
									$inquiry_entry->save();
								}
							}
							$orms = ORM::factory('inquirybranch')->where('inquiry_id', '=', $inquiry_id)->where('lang_cd', '=', $n)->find_all();
							if (count($orms) == 0) {
								$orms = ORM::factory('inquirybranch')->where('inquiry_id', '=', $inquiry_id)->where('lang_cd', '=', $copy_from_lang_cd)->find_all();
								foreach($orms as $orm) {
									$inquiry_branch = ORM::factory('inquirybranch');
									$inquiry_branch->inquiry_id = $inquiry->inquiry_id;
									$inquiry_branch->no = $orm->no;
									$inquiry_branch->lang_cd = $n;
									$inquiry_branch->title = $orm->title;
									$inquiry_branch->type = $orm->type;
									$inquiry_branch->dest_no = $orm->dest_no;
									$inquiry_branch->conditions = $orm->conditions;
									$inquiry_branch->save();
								}
							}
							$orms = ORM::factory('inquirysection')->where('inquiry_id', '=', $inquiry_id)->where('lang_cd', '=', $n)->find_all();
							if (count($orms) == 0) {
								$orms = ORM::factory('inquirysection')->where('inquiry_id', '=', $inquiry_id)->where('lang_cd', '=', $copy_from_lang_cd)->find_all();
								foreach($orms as $orm) {
									$inquiry_section = ORM::factory('inquirysection');
									$inquiry_section->inquiry_id = $inquiry->inquiry_id;
									$inquiry_section->no = $orm->no;
									$inquiry_section->lang_cd = $n;
									$inquiry_section->title = $orm->title;
									$inquiry_section->entries = $orm->entries;
									$inquiry_section->sort_no = $orm->sort_no;
									$inquiry_section->delete_flg = $orm->delete_flg;
									$inquiry_section->save();
								}			
							}	
						}
					}
				}
				$this->_update_item_display($this->_bot_id, $inquiry->inquiry_id, $item_div, $post);
				if ($inquiry->member_mail_from !='') {
					if (strpos($inquiry->member_mail_from, '@talkappi.com') === false) {
						$data = $this->_model->post_enginehook('service', 'check_mail_identity','', ['mail'=>$inquiry->member_mail_from]);
						if ($data == null || $data['success'] == 'false' || $data['exist'] == 'false' || $data['verified'] == 'false') {
							$data = $this->_model->post_enginehook('service', 'create_mail_identity','', ['mail'=>$inquiry->member_mail_from]);
							$this->_set_message('inquiry.message.popup.aws_mail_confirm');
						}
					}
				}
				if ($old_mail != '' && strpos($old_mail, '@talkappi.com') === false) {
					$orms = ORM::factory('inquiry')->where('member_mail_from', '=', $old_mail)->where('delete_flg', '=', 0)->find_all();
					if (count($orms) == 0) {
						$data = $this->_model->post_enginehook('service', 'delete_mail_identity','', ['mail'=>$old_mail]);
					}
				}
				if ($inquiry_id == NULL && $post['receiption_id_prefix'] != '' && $post['receiption_id_prefix'] != '?') {
					$seq = ORM::factory('sequence');
					$seq->app_div = 1;
					$seq->id = $inquiry->inquiry_id;
					$seq->seq = 0;
					$seq->save();
				}
				$this->redirect($this->_action_path . 'inquiry?id=' . $inquiry->inquiry_id);
			}
		}
		else {
			$json_next_inquiry_setting = $this->_model->get_bot_setting($this->_bot_id, 'json_next_inquiry_setting');
			$json_next_inquiry_setting = json_decode($json_next_inquiry_setting, true);
			if (!array_key_exists('user_in_charge_required', $json_next_inquiry_setting)) $json_next_inquiry_setting['user_in_charge_required'] = 0;
			$user_dict = $this->_model->get_bot_user_dict($this->_bot_id);
			$inquiry_id = $this->request->query('id', NULL);
			$base_inquiry_data = ["cancelable"=>0, "cancel_policy"=>[], "payment"=>[['2_step'=>0, 'pay_in_hours'=>'', 'sandbox'=>0]], "receiption_id_prefix"=>"", "gtm_tag_id"=>"", "mail_sender"=>0, "thanks"=>["member_mail_template_thanks"=>'', "day"=>"", "time"=>""]];
			$post['only_talkappipay'] = 1;
			$inquiry_div = $this->_inquiry_order_start ? $this->_item_div : NULL;
			if ($inquiry_id == NULL) {
				$inquiry_id = '';
				$this->_del_session('inquiry_id');
				$post['class_cd'] = '';
				$post['inquiry_cd'] = '';
				$post['scene_cd'] = '';
				$post['start_date'] = '';
				$post['end_date'] = '';
				$post['inquiry_name'] = '';
				$post['member_mail_from'] = '';
				$post['member_mail_replyto'] = '';
				$post['support_flg'] = 0;
				$post['use_cart'] = 0;
				$post['is_mail_logged'] = 0;
				$post['present'] = '';
				$post['duration'] = '5';
				$post['recommend'] = 0;
				$post['support_lang_cd'] = [$this->_model->get_admin_lang()];
				$post['lang_display'] = [$this->_model->get_admin_lang()];
				$post['public_flg'] = 1;
				$post['template_cd'] = '';
				$post['redirect_url'] = $json_next_inquiry_setting['end_redirect_url'];
				$post['mail_member_flg'] = 0;
				$post['mail_user_flg'] = 1;
				$post['member_mail_template'] = '';
				$post['user_mail_template'] = 'inquiry.complete_admin';
				$post['user_in_charge'] = 0;
				$post['mail_users'] = '';
				$post['faq_scene'] = '';
				$post['secret_mode'] = 0;
				$post['password'] = '';
				$post['inquiry_data'] = $base_inquiry_data;
				$post['show_receipt_setting'] = $this->_model->get_bot_setting($this->_bot_id, 'json_receipt_setting', true) !== ''? 1 : 0;
				$post['default_tax_rate'] = $this->_get_default_tax_rate();
				$post['answer_limit'] = '0';
				$post['inquiry_answer_limit'] = '';
				$post['redirect_url'] = '';
				$post['base_ver_id'] = '';
				$post['label_id'] = '';
				$user_in_charge_array = [];
				$inquiry_data = $base_inquiry_data;
				$post['assist'] = 0;
			}
			else {
				$permission = $this->_item_permission_check($inquiry_id);
				if ($permission < 0) {
					$url = ($inquiry_div == NULL || $inquiry_div == 9) ? '/admininquiry/inquirys' : '/adminorder/inquirys';
					$this->redirect($url);
				}
				$inquiry = ORM::factory('inquiry', $inquiry_id);
				if ($this->_inquiry_order_start && $inquiry->item_div) {
					$match_action = $inquiry->item_div == 9 ? 'admininquiry' : 'adminorder';
					if ($this->_path != $match_action) {
						$this->redirect('/' . $match_action . '/inquiry?id=' . $inquiry_id);
					}
				}
				$inquiry_div = $this->_inquiry_order_start ? $inquiry->item_div : NULL;
				$this->_set_session('inquiry_id', $inquiry_id);
				if ($inquiry->user_in_charge == null) {
					$user_in_charge_array = [];
				}
				else {
					$user_in_charge_array = explode(',', $inquiry->user_in_charge);
				}
				$user_is_in_charge = in_array($this->_user->user_id, $user_in_charge_array);
				if (
					$json_next_inquiry_setting['user_in_charge_required'] == 1 &&
					$this->_user->auth_all_contents_flg == 0 &&
					!$user_is_in_charge
				) {
					$this->redirect($this->_action_path . 'inquirys');
				}
				$post['class_cd'] = trim($inquiry->class_cd);
				$post['inquiry_cd'] = $inquiry->inquiry_cd;
				$post['scene_cd'] = $inquiry->scene_cd;
				$post['start_date'] = $inquiry->start_date;
				$post['end_date'] = $inquiry->end_date;
				$post['duration'] = $inquiry->duration;
				$post['support_flg'] = $inquiry->support_flg;
				$post['inquiry_name'] = $inquiry->inquiry_name;
				$post['member_mail_from'] = $inquiry->member_mail_from;
				$post['member_mail_replyto'] = $inquiry->member_mail_replyto;
				$post['present'] = $inquiry->present;
				$post['template_cd'] = $inquiry->template_cd;
				$post['redirect_url'] = $inquiry->redirect_url;
				$post['mail_member_flg'] = $inquiry->mail_member_flg;
				$post['mail_user_flg'] = $inquiry->mail_user_flg;
				$post['member_mail_template'] = $inquiry->member_mail_template;
				$post['user_mail_template'] = $inquiry->user_mail_template;
				$post['user_in_charge'] = $inquiry->user_in_charge;
				$post['faq_scene'] = $inquiry->faq_scene;
				$post['secret_mode'] = $inquiry->secret_mode;
				$post['show_receipt_setting'] = $this->_model->get_bot_setting($this->_bot_id, 'json_receipt_setting', true) !== ''? 1 : 0;
				$post['default_tax_rate'] = $this->_get_default_tax_rate();
				$post['password'] = $inquiry->password;
				$post['mail_users'] = $inquiry->mail_users;
				$post['base_ver_id'] = $inquiry->base_ver_id;
				$post['label_id'] = $inquiry->label_id;
				$inquiry_data = json_decode($inquiry->inquiry_data, true);
				if ($inquiry_data == null) {
					$post['inquiry_data'] = $base_inquiry_data;
				}
				else {
					$post['inquiry_data'] = $inquiry_data;
					if(!isset($post['inquiry_data']['payment'])) {
						$post['inquiry_data']['payment'] = ['2_step'=>0, 'pay_in_hours'=>'', 'sandbox'=>0];
					}
					if(!isset($post['inquiry_data']['payment']['2_step'])) {
						$post['inquiry_data']['payment']['2_step'] = 0;
						$post['inquiry_data']['payment']['pay_in_hours'] = '';
					}
					if(!isset($post['inquiry_data']['payment']['sandbox'])) {
						$post['inquiry_data']['payment']['sandbox'] = 0;
					}
					if(!isset($post['inquiry_data']['thanks'])) {
						$post['inquiry_data']['thanks'] = ["member_mail_template_thanks"=>'', "day"=>"", "time"=>""];
					}
				}
				if(isset($post['inquiry_data']['receipt_setting']) && $post['inquiry_data']['receipt_setting'] !== '') {
					$post['receipt_setting'] = 1;
				} else {
					$post['receipt_setting'] = 0;
				}
				$item_div = $inquiry->item_div ?? self::ITEM_DIV;
				$display = ORM::factory('itemdisplay')->where('bot_id', '=', $this->_bot_id)->where('item_id', '=', $inquiry->inquiry_id)->where('item_div', '=', $item_div)->find();
				if (isset($display->item_id)) {
					$post['recommend'] = $display->recommend;
					if ($display->lang_display == '') {
						$post['lang_display'] = [];
					}
					else {
						$post['lang_display'] = explode(',', $display->lang_display);
					}
					$post['public_flg'] = $display->public_flg;
				}
				else {
					$post['recommend'] = 0;
					$post['lang_display'] = [];
					$post['public_flg'] = 1;
				}
				$post['support_lang_cd'] = explode(',', $inquiry->support_lang_cd);
				$post['answer_limit'] = '0';
				$post['inquiry_answer_limit'] = '';
				$inquiry_data = json_decode($inquiry->inquiry_data, true);
				if (is_array($inquiry_data) && array_key_exists('answer_limit', $inquiry_data)) $post['answer_limit'] = $inquiry_data['answer_limit'];
				if (is_array($inquiry_data) && array_key_exists('inquiry_answer_limit', $inquiry_data)) $post['inquiry_answer_limit'] = $inquiry_data['inquiry_answer_limit'];
				if (is_array($inquiry_data) && array_key_exists('redirect_url', $inquiry_data)) $post['redirect_url'] = $inquiry_data['redirect_url'];
				if (is_array($inquiry_data) && isset($inquiry_data['use_cart'])) {
					$post['use_cart'] = $inquiry_data['use_cart'];
				}
				else {
					$post['use_cart'] = 0;
				}
				if (is_array($inquiry_data) && isset($inquiry_data['is_mail_logged'])) {
					$post['is_mail_logged'] = $inquiry_data['is_mail_logged'];
				}
				else {
					$post['is_mail_logged'] = 0;
				}
				if (is_array($inquiry_data) && array_key_exists('pms', $inquiry_data)) {
					if (is_array($inquiry_data['pms']) && array_key_exists('kind', $inquiry_data['pms']) && $inquiry_data['pms']['kind'] === 'assist') {
						$post['assist'] = 1;
					}
				}
				else {
					$post['assist'] = 0;
				}
				// 支払い項目確認
				$entries = ORM::factory('inquiryentry')->where('inquiry_id', '=', $inquiry_id)->where('entry_type_cd', '=', 'opt')->find_all();
				foreach($entries as $entry) {
					$input_rules = json_decode($entry->input_rules, true);
					if ($input_rules == null) continue;
					// {"type":"payment","options":[{"type":"1"},{"type":"4"},{"type":"2","vender":"talkappi"}]}
					if (isset($input_rules['type']) && $input_rules['type'] == 'payment') {
						$options = $input_rules['options'];
						if (count($options) > 1 || $options[0]['type'] != '2' || $options[0]['vender'] != 'talkappi') {
							$post['only_talkappipay'] = 0;
							break;
						}
					}
				}
				$post['mail_signature'] = $inquiry_data['mail_signature'] ?? '0';
			}
		}

		$products = $this->_model->get_products($this->_bot_id, 7, '', 'ja');
		$coupons = [''=>'無し'];
		foreach($products as $product) {
			if ($product['end_date'] != '' && $product['end_date'] < date('Y-m-d')) continue;
			$coupons[$product['product_id']] = $product['product_name'] . '　(' . $product['start_date'] . '～' . $product['end_date'] . ')';
		}
		$view = View::factory ($this->_view_path . 'inquiry');
		$menu = View::factory($this->_view_path . 'inquirymenu');
		$menu->inquiry_id = $inquiry_id;
		$menu->inquiry_div = $inquiry_div;
		$view->menu = $menu;
		$view->inquiry_div = $inquiry_div;
		$view->scene_cd = $this->_bot->facility_cd;
		$view->inquiry_id = $inquiry_id;
		$view->duration = ['1'=>'1','3'=>'3','5'=>'5','10'=>'10','15'=>'15','20'=>'20','30'=>'30'];
		// React複数選択コンポーネント用
		$user_list_for_react_select = array_map(function ($key, $value) {
			return array('value' => $key, 'label' => $value);
		}, array_keys($user_dict), $user_dict);
		$view->user_list_for_react_select = json_encode($user_list_for_react_select);
		// 担当者
		$user_in_charge_list_for_react_select = [];
		foreach ($user_in_charge_array as $user_id) {
			$user_id = (int) $user_id;
			if (array_key_exists($user_id, $user_dict)) {
				$user_in_charge_list_for_react_select[] = ['value' => $user_id, 'label' => $user_dict[$user_id]];
			}
		}
		$view->user_in_charge_list_for_react_select = json_encode($user_in_charge_list_for_react_select);
		
		$all_users = [];
		foreach($user_dict as $k=>$v) {
			$all_users[] = ['userId'=>$k, 'userName'=>$v];
		}
		$post['all_users'] = json_encode($all_users, JSON_UNESCAPED_UNICODE);
		$view->template_list = [''=>'-デフォルト-'] + $this->_model->get_scene_template($this->_bot_id, 'inquiry', '', $post['scene_cd']);
		$view->member_mail_template_list = $this->_model->get_bot_message_dict($this->_bot_id, '44', 'mal');
		$view->user_mail_template_list = $this->_model->get_bot_message_dict($this->_bot_id, '45', 'mal');
		$view->coupons = $coupons;
		$scenes = ORM::factory('botscene')->where('bot_id', '=', $this->_bot_id)->where('use_inquiry_flg', '=', 1)->order_by('sort_no')->find_all();
		$scene_list = ['' => '-'];
		foreach($scenes as $scene) {
			$scene_list[$scene->scene_name] = $scene->label;
		}
		$view->scene_list = $scene_list;
		if ($inquiry == NULL) {
			$view->verify_url = '';
		}
		else {
			$view->verify_url = $this->_verify_url($inquiry_id);
		}

		// カスタマイズコンテンツ管理に切り替える際、/inquiryreferの方も修正してください
		$view->verify_url_inflow_type = [
			'mail' => __('admin.inquiry.label.url.mail'),
			'hp'   =>  __('admin.inquiry.label.url.hp'),
			'line' => 'LINE'
		];

		$view->login_class_cd = '';

		// commented by Shane for not be used
		$payment_methods = [];
		// if (array_key_exists('payment', $json_next_inquiry_setting)) {
		// 	$json_template = $this->_model->get_bot_tpl_message(0, 'payment_display', 'ja');
		// 	$pay_template = json_decode($json_template, true);
		// 	$inquiry_payments = $json_next_inquiry_setting['payment'];
		// 	foreach ($inquiry_payments as $method) {
		// 		$type = $method["type"];
		// 		$name = '';
		// 		foreach ($pay_template as $temp) {
		// 			if ($temp["type"] == $type) {
		// 				$name = $temp["title"]["ja"]; //TODO: 多言語対応
		// 			}
		// 		}
		// 		$payment_methods[$type] = $name;
		// 	}

		// }
		$mail_signatures = $this->_model->get_signatures($this->_bot_id);
		$mail_signature_options['0'] = __('admin.send.label.signature_select');
		foreach ($mail_signatures as $mail_signature) {
			$mail_signature_options[$mail_signature['id']] = $mail_signature['sign_title'];
		}
		$self_assigned_signature = $this->_model->get_self_assigned_signature($this->_bot_id, $this->_user_id);
		$view->mail_signature_options = json_encode($mail_signature_options, JSON_UNESCAPED_UNICODE);
		$view->self_assigned_signature = $self_assigned_signature;

		$view->post = $post;
		$view->inquiry_id = $inquiry_id;
		$view->entries = json_encode($this->_model->get_inquiry_entries_mail($inquiry_id));
		$view->inquiry_data = $inquiry_data;
		$view->code_div = $code_div;
		$view->json_next_inquiry_setting = $json_next_inquiry_setting;
		$view->inquiry_class = $this->_model->get_code_div_kv($code_div, 'ja');
		$view->div_item_class = $code_div;
		$view->item_data_def = $item_data_def;
		$view->faq_scenes = $this->_model->get_bot_func_scene($this->_bot_id, 'faq', false);
		$view->labels = $this->_model->get_bot_labels($this->_bot_id);
		$view->payment_methods = $payment_methods;
		$view->has_pms_linked_inquiryentry = $this->_model->has_pms_linked_inquiryentry($inquiry_id);
		$this->template->content = $view;
		$url = ($inquiry_div == NULL || $inquiry_div == 9) ? '/admininquiry/inquirys' : '/adminorder/inquirys';
		$navi_next = ($inquiry_div == NULL || $inquiry_div == 9) ? $this->navi_maps_inquiry['inquirys'] : $this->navi_maps_order['inquirys'];
		$navi_current = ($inquiry_div == NULL || $inquiry_div == 9) ? $this->navi_maps_inquiry['inquiry'] : $this->navi_maps_order['inquiry'];
		$this->_page_navi('[{"f":"'. $navi_next .'","url":"' . $url . '"}, {"f":"'. $navi_current .'"}]');
	}

	private function _get_default_tax_rate() {
		$receipt_setting = $this->_model->get_bot_setting($this->_bot_id, 'json_receipt_setting', true);
		if ($receipt_setting !== '' && array_key_exists('tax_rate', $receipt_setting)) {
			return $receipt_setting['tax_rate'];
		}
		return '';
	}

	public function action_inquirydesc()
	{
		$errors = NULL;
		$message = '';
		$post = NULL;
		$item_description = NULL;
		$item_description_ja = NULL;
		$inquiry_div = NULL;

		$item_div = $this->_inquiry_order_start ? $this->_item_div : self::ITEM_DIV;

		$lang_cd = NULL;
		$url = NULL;
		$up_image = false;

		$displayAction = false;

		if ($this->request->post()) {
			$post = $this->request->post();
			if (!isset($post['description'])) $post['description'] = '';
			$inquiry_id = $post['inquiry_id'];
			if (array_key_exists('lang', $post)) $lang_cd = $post['lang'];
			if ($this->_page_action == 'translate') {
			}
			$item = ORM::factory('inquiry', $inquiry_id);
			$flg_apply_all_lang = false;
			if (array_key_exists('flg_apply_all_lang', $post)) {
				$flg_apply_all_lang = true;
			}
			if (isset($_FILES['image']) && $_FILES['image']['error'] == 1) {
				$message = "E|ファイルサイズが5MB以内の写真をアップロードしてください。";
			}
			else {
				$inquiry_description = ORM::factory('inquirydescription')->where('inquiry_id', '=', $inquiry_id)->where('lang_cd', '=', $lang_cd)->find();
				if ($post['page'] == 'input') {
					if (!isset($inquiry_description->inquiry_id)) {
						$inquiry_description = ORM::factory('inquirydescription');
						$inquiry_description->inquiry_id = $inquiry_id;
						$inquiry_description->lang_cd = $lang_cd;
						$inquiry_description->title = $post['title'];
						$inquiry_description->description = $post['description'];
						if ($post['use_brief'] == 1) {
							$inquiry_description->sell_point = $post['sell_point'];
						}
						else {
							$inquiry_description->sell_point = null;
						}
						$inquiry_description->description_extra = $post['description_extra'];
						$inquiry_description->inquiry_image = $post['inquiry_image'];
						$inquiry_description->save();
						$message = "S|アンケート情報を登録しました。";
					}
					else {
						$up_array = [];
						$up_array['title'] = $post['title'];
						$up_array['description'] = $post['description'];
						if ($post['use_brief'] == 1) {
							$up_array['sell_point'] = $post['sell_point'];
						}
						else {
							$up_array['sell_point'] = null;
						}
						$up_array['description_extra'] = $post['description_extra'];
						$up_array['inquiry_image'] = $post['inquiry_image'];
						$query = DB::update('t_inquiry_description')->set($up_array)->where('inquiry_id', '=', $inquiry_id)->where('lang_cd', '=', $lang_cd);
						$result = $query->execute();
						$message = "S|アンケート情報を更新しました。";
					}
				}
				else if ($post['page'] == 'confirm') {
					$temp_arr = [];
					if ($post['title'] != $inquiry_description->title) $temp_arr['title'] = $post['title'];
					if ($post['description'] != $inquiry_description->description) $temp_arr['description'] = $post['description'];
					if ($post['description_extra'] != $inquiry_description->description_extra) $temp_arr['description_extra'] = $post['description_extra'];
					$up_array['confirm_info'] = json_encode($temp_arr, JSON_UNESCAPED_UNICODE);
					$query = DB::update('t_inquiry_description')->set($up_array)->where('inquiry_id', '=', $inquiry_id)->where('lang_cd', '=', $lang_cd);
					$result = $query->execute();
				}
				else if ($post['page'] == 'complete') {
					if ($post['image_base64'] == '') {
						$url = '';
					}
					else {
						if (strpos($post['image_base64'], 'data:') === 0) {
							if ($flg_apply_all_lang) {
								$url = $this->_aws_model->put_base64_file($this->_bot_id, $post['image_base64'], $inquiry_id . '_icon', 'inquiry');
							}
							else {
								$url = $this->_aws_model->put_base64_file($this->_bot_id, $post['image_base64'], $inquiry_id . '_' . $lang_cd . '_icon', 'inquiry');
							}
							$this->_aws_model->resize_image_url($url, 800, 0);
							$up_image = true;
							$this->_aws_model->refresh_url($url);
						}
						else {
							$url = $post['image_base64'];
						}
					}

					$temp_data = json_decode($post['complete_area'], true);
					$temp_data['icon'] = $url;
					$post['complete_area'] = json_encode($temp_data, JSON_UNESCAPED_UNICODE);

					$temp_arr = ['complete_area'=>$post['complete_area']];
					if(isset(json_decode($post['actions'])[0])) $temp_arr['actions'] = $post['actions'];
					if ($post['title'] != $inquiry_description->title) $temp_arr['title'] = $post['title'];
					if ($post['description'] != $inquiry_description->description) $temp_arr['description'] = $post['description'];
					if ($post['description_extra'] != $inquiry_description->description_extra) $temp_arr['description_extra'] = $post['description_extra'];
					$up_array['complete_info'] = json_encode($temp_arr, JSON_UNESCAPED_UNICODE);
					$query = DB::update('t_inquiry_description')->set($up_array)->where('inquiry_id', '=', $inquiry_id)->where('lang_cd', '=', $lang_cd);
					$result = $query->execute();
				}
				$orm = ORM::factory('inquiry', $inquiry_id);
				$orm->upd_user = $this->_user->user_id;
				$orm->upd_time = date('Y-m-d H:i:s');
				$orm->save();

				// 自動翻訳処理
				if (isset($post['translate']) && $post['translate'] === 'translate_auto') {
					$translate_langs = $post['translate_lang'];
					foreach($translate_langs as $translate_lang) {
						if ($translate_lang === $lang_cd) continue;
						$from = ORM::factory('inquirydescription')->where('inquiry_id', '=', $inquiry_id)->where('lang_cd', '=', $lang_cd)->find();
						$to = ORM::factory('inquirydescription')->where('inquiry_id', '=', $inquiry_id)->where('lang_cd', '=', $translate_lang)->find();
						$description_extra = json_decode($from->description_extra, true);
						for($i=0; $i<count($description_extra); $i++) {
							foreach($description_extra[$i] as $k=>$v) {
								if ($k != 'fold') $description_extra[$i][$k] = $this->_model->translate($v, $translate_lang, $lang_cd);
							}
						}
						$confirm_info = json_decode($from->confirm_info, true);
						foreach($confirm_info as $k=>$v) {
							if ($k == 'description') {
								$confirm_info[$k] = $this->_model->translate($v,  $translate_lang, $lang_cd);
							}
							else if ($k == 'description_extra') {
								$extra = json_decode($v, true);
								for($i=0; $i<count($extra); $i++) {
									foreach($extra[$i] as $k2=>$v2) {
										if ($k2 != 'fold') $extra[$i][$k2] = $this->_model->translate($v2,  $translate_lang, $lang_cd);
									}
								}
								$confirm_info[$k] = json_encode($extra, JSON_UNESCAPED_UNICODE);
							}
						}
						// DBに保存していない場合は、初期表示内容ベースに翻訳します。
						$complete_info = json_decode($from->complete_info, true);
						if ($complete_info == null) {
							$complete_info['complete_area'] = json_encode([
								'title' => $this->_model->translate('受付完了しました',  $translate_lang, $lang_cd),
								'description' => $this->_model->translate('お問い合わせありがとうございます。通常3営業日以内に返信いたします。',  $translate_lang, $lang_cd),
							], JSON_UNESCAPED_UNICODE);
						} else {
							foreach($complete_info as $k=>$v) {
								if ($k == 'description') {
									$complete_info[$k] = $this->_model->translate($v, $translate_lang, $lang_cd);
								}
								else if ($k == 'description_extra') {
									$extra = json_decode($v, true);
									for($i=0; $i<count($extra); $i++) {
										foreach($extra[$i] as $k2=>$v2) {
											if ($k2 != 'fold') $extra[$i][$k2] = $this->_model->translate($v2, $translate_lang, $lang_cd);
										}
									}
									$complete_info[$k] = json_encode($extra, JSON_UNESCAPED_UNICODE);
								}
								else if ($k == 'actions') {
									$extra = json_decode($v, true);
									for($i=0; $i<count($extra); $i++) {
										foreach($extra[$i] as $k2=>$v2) {
											if ($k2 == 'title') $extra[$i][$k2] = $this->_model->translate($v2, $translate_lang, $lang_cd);
										}
									}
									$complete_info[$k] = json_encode($extra, JSON_UNESCAPED_UNICODE);
								}
								else if ($k == 'complete_area') {
									$extra = json_decode($v, true);
									foreach($extra as $k2=>$v2) {
										if ($k2 != 'icon') $extra[$k2] = $this->_model->translate($v2, $translate_lang, $lang_cd);
									}
									$complete_info[$k] = json_encode($extra, JSON_UNESCAPED_UNICODE);
								}
							}
						}
						if (isset($to->inquiry_id)) {
							$pairs = [];
							$pairs['inquiry_name'] = $this->_model->translate($from->inquiry_name, $translate_lang, $lang_cd);
							$pairs['title'] = $this->_model->translate($from->title, $translate_lang, $lang_cd);
							if ($from->sell_point != null) $pairs['sell_point'] = $this->_model->translate($from->sell_point, $translate_lang, $lang_cd);
							$pairs['description'] = $this->_model->translate($from->description, $translate_lang, $lang_cd);
							$pairs['description_extra'] = json_encode($description_extra, JSON_UNESCAPED_UNICODE);
							$pairs['confirm_info'] = json_encode($confirm_info, JSON_UNESCAPED_UNICODE);
							$pairs['complete_info'] = json_encode($complete_info, JSON_UNESCAPED_UNICODE);
							$pairs['inquiry_image'] = $from->inquiry_image;
							DB::update('t_inquiry_description')->set($pairs)->where('inquiry_id', '=', $inquiry_id)->where('lang_cd', '=', $translate_lang)->execute();
						}
						else {
							$to = ORM::factory('inquirydescription');
							$to->inquiry_id = $inquiry_id;
							$to->lang_cd = $translate_lang;
							$to->inquiry_name = $this->_model->translate($from->inquiry_name, $translate_lang, $lang_cd);
							$to->title = $this->_model->translate($from->title, $translate_lang, $lang_cd);
							if ($from->sell_point != null) $to->sell_point = $this->_model->translate($from->sell_point, $translate_lang, $lang_cd);
							$to->description = $this->_model->translate($from->description, $translate_lang, $lang_cd);
							$to->description_extra = json_encode($description_extra, JSON_UNESCAPED_UNICODE);
							$to->confirm_info = json_encode($confirm_info, JSON_UNESCAPED_UNICODE);
							$to->complete_info = json_encode($complete_info, JSON_UNESCAPED_UNICODE);
							$to->inquiry_image = $from->inquiry_image;
							$to->save();
						}
					}	
				}
				$this->redirect($this->_action_path . "inquirydesc?id=$inquiry_id&lang=$lang_cd&page=" . $post['page']);
			}
		}
		else {
			$inquiry_id = $this->request->query('id', NULL); 
			$lang_cd = $this->request->query('lang', NULL);
			if ($lang_cd == NULL) {
				$support_lang = ORM::factory('inquiry', $inquiry_id)->support_lang_cd;
				if (!empty($support_lang)) {
					$langs = explode(',',$support_lang);
					if (count($langs) > 0) $lang_cd = $langs[0];
					$this->redirect("/admininquiry/inquirydesc?id=$inquiry_id&lang=$lang_cd");
				}
			}
			if ($lang_cd == NULL) $this->redirect('/admininquiry/inquiry?id=' . $inquiry_id);
			$permission = $this->_item_permission_check($inquiry_id);
			if ($permission < 0) {
				$this->redirect('/admininquiry/inquirys');
			}
			$item = ORM::factory('inquiry', $inquiry_id);
			
			$desc_page = $this->request->query('page', NULL);
			if ($desc_page == NULL) $desc_page = 'input';
			$post['new'] = 0;
			$post['page'] = $desc_page;
			$post['inquiry_name'] = '';
			$post['title'] = '';
			$post['description'] = '';
			$post['sell_point'] = '';
			$post['use_brief'] = 0;
			$post['description_extra'] = '';
			$post['inquiry_image'] = '';
			$item_description = ORM::factory('inquirydescription')->where('inquiry_id', '=', $inquiry_id)->where('lang_cd', '=', $lang_cd)->find();
			if (isset($item_description->inquiry_id)) {
				$post['title'] = $item_description->title;
				$post['description'] = $item_description->description;
				if ($item_description->sell_point != null) {
					$post['use_brief'] = 1;
					$post['sell_point'] = $item_description->sell_point;
				}
				$post['description_extra'] = json_decode($item_description->description_extra, true);
				$post['inquiry_image'] = $item_description->inquiry_image;
				if ($desc_page == 'confirm') {
					$confirm_info = json_decode($item_description->confirm_info, true);
					if (is_array($confirm_info)) {
						if (array_key_exists('title', $confirm_info)) $post['title'] = $confirm_info['title'];
						if (array_key_exists('description', $confirm_info)) $post['description'] = $confirm_info['description'];
						if (array_key_exists('description_extra', $confirm_info)) $post['description_extra'] = json_decode($confirm_info['description_extra'], true);
					}
				}
				else if ($desc_page == 'complete') {
					$complete_info = json_decode($item_description->complete_info, true);
					if (is_array($complete_info)) {
						if (array_key_exists('title', $complete_info)) $post['title'] = $complete_info['title'];
						if (array_key_exists('description', $complete_info)) $post['description'] = $complete_info['description'];
						if (array_key_exists('description_extra', $complete_info)) $post['description_extra'] = json_decode($complete_info['description_extra'], true);
						if (array_key_exists('complete_area', $complete_info)) {
							$post['complete_area'] = json_decode($complete_info['complete_area'], true);
							if (!array_key_exists('icon', $post['complete_area']) || $post['complete_area']['icon'] == '') {
								$post['complete_area']['icon'] = 'https://admin.talkappi.com/assets/admin/css/img/form-finished.png';
							}
						}
						else {
							$post['complete_area'] = [
								'title' => I18n::get('inquiry.complete.common.title', $lang_cd),
								'icon'=>'https://admin.talkappi.com/assets/admin/css/img/form-finished.png', 
								'description' => I18n::get('inquiry.complete.common.description', $lang_cd)
							];
						}
						if (array_key_exists('actions', $complete_info)) {
							$post['actions'] = json_decode($complete_info['actions'], true);
						}
					}
					else {
						$post['complete_area'] = [
							'title' => I18n::get('inquiry.complete.common.title', $lang_cd),
							'icon'=>'https://admin.talkappi.com/assets/admin/css/img/form-finished.png', 
							'description' => I18n::get('inquiry.complete.common.description', $lang_cd)
						];
						// $post['actions'] = [];
					}
				}
			}
			else {
				$post['new'] = 1;
			}
			if(
					isset($post['actions'][0]) && 
					(
						(array_key_exists('title', $post['actions'][0]) && $post['actions'][0]['title'] !== "")
						|| (array_key_exists('url', $post['actions'][0]) && $post['actions'][0]['url'] !== "")
					)
				){
				$displayAction = true; // show
			}
			if ($this->_inquiry_order_start && $item->item_div) {
				$match_action = $item->item_div == 9 ? 'admininquiry' : 'adminorder';
				if ($this->_path != $match_action) {
					$this->redirect('/' . $match_action . '/inquirydesc?id=' . $inquiry_id . '&lang=' . $lang_cd);
				}
			}
			$inquiry_div = $this->_inquiry_order_start ? $item->item_div : NULL;
		}

		$view = View::factory ($this->_view_path . 'inquirydesc');
		$menu = View::factory($this->_view_path . 'inquirymenu');
		$menu->type = $lang_cd;
		$menu->inquiry_id = $inquiry_id;
		$menu->inquiry_div = $inquiry_div;
		$view->inquiry_div = $inquiry_div;
		$item_descs = ORM::factory('inquirydescription')->where('inquiry_id', '=', $inquiry_id)->find_all();
		$translate_from_lang = [];
		foreach($item_descs as $de) {
			if ($de->lang_cd != $lang_cd) {
				$translate_from_lang[$de->lang_cd] = $this->_codes['02'][$de->lang_cd];
			}
		}
		if (count($item_descs) == 0) {
			$view->auto_translate = 1;
			$view->all_language = 1;
		}
		else {
			$view->auto_translate = 0;
			$view->all_language = 0;
		}

		$view->menu = $menu;

		$inquiry = ORM::factory('inquiry', $inquiry_id);
		$verify_url = $this->_verify_url($inquiry_id, $lang_cd);
		if ($inquiry->scene_cd != NULL) {
			$scene_cd = $inquiry->scene_cd;
		}
		else {
			$scene_cd = $this->_bot->facility_cd;
		}
		$lang_setting = explode(",", $inquiry->support_lang_cd);
		$lang_cd_array = array_intersect_key($this->_model->get_code('02', $this->_lang_cd), array_flip($lang_setting));
		$item_div = $inquiry->item_div ?? self::ITEM_DIV;
		$display = ORM::factory('itemdisplay')->where('item_id', '=', $inquiry_id)->where('item_div', '=', $item_div)->find();
		$view->lang_display = array_values(array_intersect(explode(',', $display->lang_display), array_keys($lang_cd_array)));
		$view->support_lang_cd = $lang_cd_array;
		// 表示言語の中から、サポート言語の中にあるものだけを取得
		$display_lang_cd = array_values(array_intersect(explode(',', $display->lang_display), array_keys($lang_cd_array)));
		$lang_cd = $this->request->query('lang');
		if (!$lang_cd) {
			// デフォルト表示を表示言語の配列の一番目にする
			$lang_cd = $display_lang_cd[0];
		}
		$view->lang_cd = $lang_cd;
		// 多言語表示OFFでその言語の検証できるようにtokenをつける
		$display_lang_arr = explode(',', $display->lang_display);
		if (!in_array($lang_cd, $display_lang_arr)) {
			$token_payload = [];
			$token_payload['data'] = json_encode(["inquiry_id"=>$inquiry_id,"lang_cd"=>$lang_cd,"bot_id"=>$this->_bot_id]);
			$verify_url = $verify_url. '&eval=' . $this->_model->get_new_token($token_payload);
		}

		$ref_scene_cd = $this->_model->get_scene_ref($scene_cd, 'inquiry', '', $inquiry->template_cd);
		$scene_path = $this->_model->get_scene_path($ref_scene_cd, 'inquiry', '', $inquiry->template_cd);
		$filename = $scene_path . "config.json";
		
		$config = ['theme-bk-color'=>'#FFF'];
		if (file_exists($filename) == true) {
			$handle = fopen($filename, "r");
			$content = fread($handle, filesize($filename));
			fclose($handle);
			$config = json_decode($content, true);
		}

		$scene_url_path = $this->_model->get_scene_url_path($ref_scene_cd, 'inquiry', '', $inquiry->template_cd);
		$logo_file = '';
		$ext_array = $this->_model->get_setting('support_image_type');
		foreach($ext_array as $ext) {
			if (file_exists($scene_path . 'logo.' . $ext) == true) $logo_file = $scene_url_path . 'logo.' . $ext;
		}
		$view->logo_url = $logo_file;
		$view->inquiry = $inquiry;

		// i18n::$lang = $lang_cd;

		$view->verify_url = $verify_url;
		$view->lang_edit = $lang_cd;
		$view->inquiry_id = $inquiry_id;
		$view->scene_cd = $scene_cd;
		$view->post = $post;
		$view->config = $config;
		$view->message = $message;
		$view->displayAction = $displayAction;
		//$view->btn_select = array(''=>"-") + $config_button;
		$view->url_lang_cd = array(''=>"-") + $this->_model->get_code('02');
		$view->skillbox = $this->_skill_box();
		//$view->skills = $skills;
		//$view->permission = $permission;
		$view->item_div = $item_div;
		$view->item = $item;
		$view->translate_from_lang = $translate_from_lang;
		$view->debug = $scene_path;
		$this->template->content = $view;
		$url = ($inquiry_div == NULL || $inquiry_div == 9) ? '/admininquiry/inquirys' : '/adminorder/inquirys';
		$navi_next = ($inquiry_div == NULL || $inquiry_div == 9) ? $this->navi_maps_inquiry['inquirys'] : $this->navi_maps_order['inquirys'];
		$navi_current = ($inquiry_div == NULL || $inquiry_div == 9) ? $this->navi_maps_inquiry['inquirydesc'] : $this->navi_maps_order['inquirydesc'];
		$this->_page_navi('[{"f":"'. $navi_next .'","url":"' . $url . '"}, {"f":"'. $navi_current .'"}]');
	}

	public function action_inquiryentry()
	{
		$errors = NULL;
		$message = '';
		$post = NULL;
		$isEdit = false;
		$_lang_cd = I18n::$lang = $this->_lang_cd;
		$inquiry_div = NULL;

		if ($this->request->post()){
			$post = $this->request->post();
			$inquiry_id = $post['inquiry_id'];
			$lang_cd = $post['lang_cd'];
			if ($this->_page_action == 'label') {
				$this->redirect($this->_action_path . 'inquiryentry?id=' . $inquiry_id . '&lang=' . $lang_cd);
				return;
			}
			$entries = json_decode($post['inquiry_entries'], true);
			$sections = json_decode($post['inquiry_sections'], true);
			$branchs = json_decode($post['inquiry_branchs'], true);
			$inquiry = ORM::factory('inquiry', $inquiry_id);
			$label_no_dict = [];
			$label_no_text_dict = [];
			if ($inquiry->label_id != NULL) {
				// 旧Entryデータのlabel_noを取得
				$old_entries = ORM::factory('inquiryentry')->where('inquiry_id', '=', $inquiry_id)->where('lang_cd', '=', $lang_cd)->find_all();
				foreach($old_entries as $old) {
					$label_no_dict[$old->no] = $old->label_no;
				}
			}
			DB::delete('t_inquiry_entry')->where('inquiry_id', '=', $inquiry_id)->where('lang_cd', '=', $lang_cd)->execute();
			DB::delete('t_inquiry_entry_action')->where('inquiry_id', '=', $inquiry_id)->where('lang_cd', '=', $lang_cd)->execute();
			foreach($entries as $entry) {
				$orm = ORM::factory('inquiryentry');
				$orm->inquiry_id = $inquiry_id;
				$orm->no = $entry['no'];
				$orm->lang_cd = $lang_cd;
				$orm->title = $entry['title'];
				if (isset($label_no_dict[$orm->no])) {
					$orm->label_no = $label_no_dict[$orm->no];
					$label_no_text_dict[$orm->label_no] = strip_tags($orm->title);
				}
				$orm->title_description = $entry['title_desc'];
				$orm->entry_type_cd = $entry['entry_type_cd'];
				$orm->required = $entry['required'];
				$orm->label_no = $entry['label_no'];
				$orm->labels = $entry['labels'];
				$orm->placeholder = $entry['placeholder'];
				$orm->description = $entry['description'];
				$inputRules = json_decode($entry['input_rules'], true);
				if (is_array($inputRules)) {			
					if(array_key_exists('discount_policy', $inputRules)) {
						if (!is_array($inputRules['discount_policy'])) {
							$inputRules['discount_policy'] = json_decode($inputRules['discount_policy'], true);
						}
					}
					if(array_key_exists('extra_settings', $inputRules)) {
						if (!is_array($inputRules['extra_settings'])) {
							$inputRules['extra_settings'] = json_decode($inputRules['extra_settings'], true);
						}
					}
					if(array_key_exists('range_setting', $inputRules)) {
						if (!is_array($inputRules['range_setting'])) {
							$inputRules['range_setting'] = json_decode($inputRules['range_setting'], true);
						}
					}
					$orm->input_rules = json_encode($inputRules, JSON_UNESCAPED_UNICODE);
				}
				else {
					$orm->input_rules = $entry['input_rules'];
				}
				// t_inquiry_entry_actionのテーブルの保存
				// https://support.activalues.com/issues/50886
				// if (is_array($entry['actions'])) {
				// 	$orm->actions = json_encode($entry['actions'],JSON_UNESCAPED_UNICODE);
				// } else {
				// 	$orm->actions = $entry['actions'];
				// }
				if ($entry['actions'] != NULL) {
					$action_orm = ORM::factory('inquiryentryaction');
					$action_orm->inquiry_id = $inquiry_id;
					$action_orm->no = $entry['no'];
					$action_orm->lang_cd = $lang_cd;
					if (is_array($entry['actions'])) {
						$action_orm->actions = json_encode($entry['actions'],JSON_UNESCAPED_UNICODE);
					} else {
						$action_orm->actions = $entry['actions'];
					}
					$action_orm->save();
				}
				if (is_array($entry['entry_data'])) {
					$rules = json_decode($entry['input_rules'], true);
					if (is_array($rules) && array_key_exists('with_picture', $rules)) {
						for($m=1; $m<=count($entry['entry_data']); $m++) {
							$file_key = 'file_' . $entry['no'] . '_' . $m;
							if (array_key_exists($file_key, $post)) {
								$new_filename = $entry['no'] . '_' . time();
								$url = $this->_aws_model->put_base64_file($this->_bot_id, $post[$file_key], $new_filename, 'inquiry/' . $inquiry_id . '/entry');
								if ($url != null) {
									$entry['entry_data'][$m - 1]['image'] = $url;
									$this->_aws_model->refresh_url($url);
								}
							}
							else {
								// #42487
								$url = $entry['entry_data'][$m - 1]['image'];
								$pos = strpos($url, '?');
								if ($pos > 0) $entry['entry_data'][$m - 1]['image'] = substr($url, 0, $pos);
							}
						}
					}
					$orm->entry_data = json_encode($entry['entry_data'],JSON_UNESCAPED_UNICODE);
				}
				else {
					$orm->entry_data = $entry['entry_data'];
				}
				/*
				if(count($entry['entry_data']) > 0 ) {
					if (is_array($entry['entry_data'])){
						$orm->entry_data = json_encode($entry['entry_data'],JSON_UNESCAPED_UNICODE);
					}
					else {
						$orm->entry_data = $entry['entry_data'];
					}
				}
				*/
				$orm->next_page = $entry['next_page'];
				$orm->save();
			}
			// 日本語Entry編集の場合、Label自動タイトルから更新
			if ($inquiry->label_id != NULL && $lang_cd == 'ja') {
				$label = ORM::factory('entrylabel', $inquiry->label_id);
				$labels = json_decode($label->labels, true);
				foreach($labels as $k=>&$v) {
					if (isset($label_no_text_dict[$k])) $v = $label_no_text_dict[$k];
				}
				$label->labels = json_encode($labels, JSON_UNESCAPED_UNICODE);
				$label->save();
			}

			DB::delete('t_inquiry_section')->where('inquiry_id', '=', $inquiry_id)->where('lang_cd', '=', $lang_cd)->execute();
			foreach($sections as $section) {
				$orm = ORM::factory('inquirysection');
				$orm->inquiry_id = $inquiry_id;
				$orm->no = $section['no'];
				$orm->lang_cd = $lang_cd;
				$orm->title = $section['title'];
				$orm->entries = $section['entries'];
				$orm->sort_no = $section['sort_no'];
				$orm->save();
			}
			DB::delete('t_inquiry_branch')->where('inquiry_id', '=', $inquiry_id)->where('lang_cd', '=', $lang_cd)->execute();
			foreach($branchs as $branch) {
				$orm = ORM::factory('inquirybranch');
				$orm->inquiry_id = $inquiry_id;
				$orm->no = $branch['no'];
				$orm->lang_cd = $lang_cd;
				$orm->title = $branch['title'];
				$orm->type = $branch['type'];
				$orm->dest_no = $branch['dest_no'];
				if (is_array($branch['conditions'])){
					$orm->conditions = json_encode($branch['conditions'],JSON_UNESCAPED_UNICODE);
				}
				else {
					$orm->conditions = $branch['conditions'];
				}
				$orm->save();
			}
			$up_array = [];
			$up_array['labels'] = json_decode($post['ex_labels'], true);
			$up_array['upd_user'] = $this->_user->user_id;
			$up_array['upd_time'] = date('Y-m-d H:i:s');
			DB::update('t_entry_label')->set($up_array)->where('label_id', '=', ORM::factory('inquiry', $inquiry_id)->label_id)->where('bot_id', '=', $this->_bot_id)->where('label_div', '=', 'inquiry')->execute();
			
			$orm = ORM::factory('inquiry', $inquiry_id);
			$orm->entry_count = count($entries);
			$orm->upd_user = $this->_user->user_id;
			$orm->upd_time = date('Y-m-d H:i:s');
			$orm->save();

			// 自動翻訳処理
			if (isset($post['translate']) && $post['translate'] === 'translate_auto') {
				// translate cache
				// ticket: https://activalues.atlassian.net/browse/DXDEV-1555
				$translate_cache = [];

				$translate_langs = $post['translate_lang'];
				foreach($translate_langs as $translate_lang) {
					if ($translate_lang === $lang_cd) continue;
					// inquiry_entry
					$entry = ORM::factory('inquiryentry')->where('inquiry_id', '=', $inquiry_id)->where('lang_cd', '=', $lang_cd)->find_all();
					DB::delete('t_inquiry_entry')->where('inquiry_id', '=', $inquiry_id)->where('lang_cd', '=', $translate_lang)->execute();
					foreach($entry as $f) {
						$orm = ORM::factory('inquiryentry');
						$orm->inquiry_id = $inquiry_id;
						$orm->lang_cd = $translate_lang;
						$orm->no = $f->no;
						$orm->label_no = $f->label_no;
						$orm->labels = $f->labels;
						$orm->title = $this->_model->translate($f->title, $translate_lang, $lang_cd, $translate_cache);
						$orm->title_description = $this->_model->translate($f->title_description, $translate_lang, $lang_cd, $translate_cache);
						$orm->entry_type_cd = $f->entry_type_cd;
						$orm->required = $f->required;
						$orm->placeholder = $this->_model->translate($f->placeholder, $translate_lang, $lang_cd, $translate_cache);
						$orm->description = $this->_model->translate($f->description, $translate_lang, $lang_cd, $translate_cache);
						$input_rules = json_decode($f->input_rules, true);
						$entry_data = json_decode($f->entry_data, true);
						$other_array = ['ja' => 'その他', 'en' => 'Other', 'cn' => '其他', 'tw' => '其他', 'kr' => '그 외'];
						if(array_key_exists('type', $input_rules)) {
							foreach ($input_rules as $key => $object){
								if (in_array($key, ['full_name', 'last_name', 'first_name', 'country', 'postcode', 'address1', 'address2', 'address3', 'address4'])) 
								{
									foreach ($object as $k => $v){
										$input_rules[$key][$k] = $this->_model->translate($v, $translate_lang, $lang_cd, $translate_cache);
									}
								} else if (in_array($key, ['furigana', 'first_name_kana', 'last_name_kana'])) 
								{
									unset($input_rules[$key]);
								}
							}
							if($input_rules['type'] == 'name_full') {
								if ($translate_lang === 'ja') {
									// フリガナ：日本語のみ
									// フルネームのフリガナを格納
									$input_rules['furigana']['label'] = 'フリガナ';
									$input_rules['furigana']['placeholder'] = 'ヤマダ　タロウ';
								} else {
									unset($input_rules['furigana']);
									if ($translate_lang === 'en') {
										$input_rules['full_name']['label'] = 'Full Name';
										$input_rules['full_name']['placeholder'] = 'John Smith';
									} else if ($translate_lang === 'cn') {
										$input_rules['full_name']['label'] = '姓名（全名）';
										$input_rules['full_name']['placeholder'] = '张三';
									} else if ($translate_lang === 'tw') {
										$input_rules['full_name']['label'] = '姓名（全名）';
										$input_rules['full_name']['placeholder'] = '王小明';
									} else if ($translate_lang === 'kr') {
										$input_rules['full_name']['label'] = '성함';
										$input_rules['full_name']['placeholder'] = '김철수';
									} 
								}
							} 
							if($input_rules['type'] == 'name_separate') {
								if ($translate_lang === 'ja') {
									// フリガナ：日本語のみ
									// 姓名別のフリガナを格納
									$input_rules['first_name_kana']['label'] = 'セイ';
									$input_rules['first_name_kana']['placeholder'] = 'ヤマダ';
									$input_rules['last_name_kana']['label'] = 'メイ';
									$input_rules['last_name_kana']['placeholder'] = 'タロウ';
								} else {
									unset($input_rules['first_name_kana']);
									unset($input_rules['last_name_kana']);
									if ($translate_lang === 'en') {
										$input_rules['first_name']['label'] = 'First name';
										$input_rules['first_name']['placeholder'] = 'John';
										$input_rules['last_name']['label'] = 'Last name';
										$input_rules['last_name']['placeholder'] = 'Smith';
									} else if ($translate_lang === 'cn') {
										$input_rules['first_name']['label'] = '名';
										$input_rules['first_name']['placeholder'] = '张';
										$input_rules['last_name']['label'] = '姓';
										$input_rules['last_name']['placeholder'] = '三';
									} else if ($translate_lang === 'tw') {
										$input_rules['first_name']['label'] = '名';
										$input_rules['first_name']['placeholder'] = '王';
										$input_rules['last_name']['label'] = '姓';
										$input_rules['last_name']['placeholder'] = '小明';
									} else if ($translate_lang === 'kr') {
										$input_rules['first_name']['label'] = '이름';
										$input_rules['first_name']['placeholder'] = '김';
										$input_rules['last_name']['label'] = '성';
										$input_rules['last_name']['placeholder'] = '철수';
									}
								}
							} 
							if($input_rules['type'] == 'address' && array_key_exists('country', $input_rules) && $input_rules['type'] == 'address') {
								// 国・地域：日本語以外のみ
								if($translate_lang !== 'ja') {
									// 国・地域を格納
									$input_rules['country']['label'] = $this->_model->translate('国・地域', $translate_lang, $lang_cd, $translate_cache);
									$input_rules['country']['placeholder'] = '';
								} else {
									// 国・地域を削除
									unset($input_rules['country']);
								}
							}
							$orm->input_rules = json_encode($input_rules, JSON_UNESCAPED_UNICODE);
						} else {
							$orm->input_rules = $f->input_rules;
						}
						if (is_array($entry_data)) {
							for($i=0; $i<count($entry_data); $i++) {
								if (is_array($entry_data[$i])) {
									foreach($entry_data[$i] as $k=>$v) {
										if ($k == 'title' || $k == 'description') {
											if (strstr($v, "...") && !strstr($v, "*...")) {
												$entry_data[$i][$k] = $other_array[$translate_lang] . "...";
											} elseif (strstr($v, "*...")) {
												$entry_data[$i][$k] = $other_array[$translate_lang] . "*...";
											} else {
												$entry_data[$i][$k] = $this->_model->translate($v, $translate_lang, $lang_cd, $translate_cache);
											}
										}
									}
								}
								else {
									if (strstr($entry_data[$i], "...") && !strstr($entry_data[$i], "*...")) {
										$entry_data[$i] = $other_array[$translate_lang] . "...";
									} elseif (strstr($entry_data[$i], "*...")) {
										$entry_data[$i] = $other_array[$translate_lang] . "*...";
									} else {
										$entry_data[$i] = $this->_model->translate($entry_data[$i], $translate_lang, $lang_cd, $translate_cache);
									}
								}
							}
							$orm->entry_data = json_encode($entry_data, JSON_UNESCAPED_UNICODE);
						}
						else {
							$orm->entry_data = $f->entry_data;
						}
						$orm->next_page = $f->next_page;
						$orm->save();
					}
					// iqnuiry_entry_action
					$entry_action = ORM::factory('inquiryentryaction')->where('inquiry_id', '=', $inquiry_id)->where('lang_cd', '=', $lang_cd)->find_all();
					DB::delete('t_inquiry_entry_action')->where('inquiry_id', '=', $inquiry_id)->where('lang_cd', '=', $translate_lang)->execute();
					foreach($entry_action as $ea) {
						$action_orm = ORM::factory('inquiryentryaction');
						$action_orm->inquiry_id = $inquiry_id;
						$action_orm->no = $ea->no;
						$action_orm->lang_cd = $translate_lang;
						$actions = json_decode($ea->actions, true);
						if (is_array($actions)) {
							for($i=0; $i<count($actions); $i++) {
								if (is_array($actions[$i])) {
									foreach($actions[$i] as $k=>$v) {
										if ($k == 'title') {
											$actions[$i][$k] = $this->_model->translate($v, $translate_lang, $lang_cd, $translate_cache);
										}
										else if ($k == 'option') {
											for($j=0; $j<count($actions[$i]['option']); $j++) {
												foreach($actions[$i]['option'][$j] as $k2=>$v2) {
													if ($k2 == 'value') {
														$actions[$i]['option'][$j][$k2] = $this->_model->translate($v2, $translate_lang, $lang_cd, $translate_cache);
													}
												}
											}
										}
									}
								}
								else {
									$entry_data[$i] = $this->_model->translate($actions[$i], $translate_lang, $lang_cd, $translate_cache);
								}
							}
							$action_orm->actions = json_encode($actions,JSON_UNESCAPED_UNICODE);
						}
						else {
							$action_orm->actions = $actions;
						}
						$action_orm->save();
					}
					// inquiry_section
					$sections = ORM::factory('inquirysection')->where('inquiry_id', '=', $inquiry_id)->where('lang_cd', '=', $lang_cd)->find_all();
					DB::delete('t_inquiry_section')->where('inquiry_id', '=', $inquiry_id)->where('lang_cd', '=', $translate_lang)->execute();
					foreach($sections as $section) {
						$orm = ORM::factory('inquirysection');
						$orm->inquiry_id = $inquiry_id;
						$orm->no = $section->no;
						$orm->lang_cd = $translate_lang;
						$orm->title = $this->_model->translate($section->title, $translate_lang, $lang_cd, $translate_cache);
						$orm->entries = $section->entries;
						$orm->sort_no = $section->sort_no;
						$orm->save();
					}
					// inquiry_branch
					$branchs = ORM::factory('inquirybranch')->where('inquiry_id', '=', $inquiry_id)->where('lang_cd', '=', $lang_cd)->find_all();
					DB::delete('t_inquiry_branch')->where('inquiry_id', '=', $inquiry_id)->where('lang_cd', '=', $translate_lang)->execute();
					foreach($branchs as $branch) {
						$orm = ORM::factory('inquirybranch');
						$orm->inquiry_id = $inquiry_id;
						$orm->no = $branch->no;
						$orm->lang_cd = $translate_lang;
						$orm->title = $this->_model->translate($branch->title, $translate_lang, $lang_cd, $translate_cache);
						$orm->type = $branch->type;
						$orm->dest_no = $branch->dest_no;
						$conditions = json_decode($branch->conditions, true);
						if (is_array($conditions)) {
							for($i=0; $i<count($conditions); $i++) {
								foreach($conditions[$i] as $k=>$v) {
									if ($k == 'value') $conditions[$i][$k] = $this->_model->translate($v, $translate_lang, $lang_cd, $translate_cache);
								}
							}
							$orm->conditions = json_encode($conditions, JSON_UNESCAPED_UNICODE);
						}
						else {
							$orm->conditions = $branch->conditions;
						}
						$orm->save();
					}
				}
			}

			$this->redirect($this->_action_path . 'inquiryentry?id=' . $inquiry_id . '&lang=' . $lang_cd);
		}
		else {
			$inquiry_id = $this->request->query('id', NULL);
			$user_dict = $this->_model->get_bot_user_dict($this->_bot_id);
			$permission = $this->_item_permission_check($inquiry_id);
			if ($permission < 0) {
				$this->redirect('/admininquiry/inquirys');
			}
			$lang_cd = $this->request->query('lang', NULL);
			if ($lang_cd == NULL) {
				$support_lang = ORM::factory('inquiry', $inquiry_id)->support_lang_cd;
				if (!empty($support_lang)) {
					$langs = explode(',',$support_lang);
					if (count($langs) > 0) $lang_cd = $langs[0];
					$this->redirect("/admininquiry/inquiryentry?id=$inquiry_id&lang=$lang_cd");
				}
			}
			if ($lang_cd == NULL) $this->redirect('/admininquiry/inquiry?id=' . $inquiry_id);
			$item = ORM::factory('inquiry', $inquiry_id);
			if ($this->_inquiry_order_start && $item->item_div) {
				$match_action = $item->item_div == 9 ? 'admininquiry' : 'adminorder';
				if ($this->_path != $match_action) {
					$this->redirect('/' . $match_action . '/inquiryentry?id=' . $inquiry_id . '&lang=' . $lang_cd);
				}
			}
			$inquiry_div = $this->_inquiry_order_start ? $item->item_div : NULL;
		}

		$view = View::factory ($this->_view_path . 'inquiryentry');
		$menu = View::factory($this->_view_path . 'inquirymenu');
		$menu->inquiry_id = $inquiry_id;
		$menu->inquiry_div = $inquiry_div;
		$view->inquiry_div = $inquiry_div;
		$label_id = ORM::factory('inquiry', $inquiry_id)->label_id;
		if($label_id == null){ $label_id = 0; }
		$view->label_id = $label_id;
		$view->labels = '{}';
		if($label_id){
			$label = ORM::factory('entrylabel')
			->where('label_id', '=', $label_id)
			->where('bot_id', '=', $this->_bot_id)
			->where('label_div', '=', 'inquiry')->find();
			if (isset($label->labels) && $label->labels != '') $view->labels = $label->labels;
		}

		$inquiey_res = ORM::factory('entrylabel')->where('bot_id', '=', '0')->where('label_div', '=', 'inquiry_reserve')->find();
		if($inquiey_res){
			$view->inquiry_res = $inquiey_res;
		} else {
			$view->inquiry_res = json_encode([]);
		}

		$num_label = $this->_model->get_bot_tpl_message($this->_bot_id, 'maximum_num_label', $_lang_cd);
		if ($num_label == '') $num_label = $this->_model->get_bot_tpl_message($this->_bot_id, 'maximum_num_label', 'ja');
		$num_label_config = json_decode($num_label, true);

		$use_number = $this->_model->get_bot_tpl_message($this->_bot_id, 'use_number_label', $_lang_cd);
		if ($use_number == '') $use_number = $this->_model->get_bot_tpl_message($this->_bot_id, 'use_number_label', 'ja');
		$use_number_config = json_decode($use_number, true);

		$item_description = ORM::factory('inquirydescription')->where('inquiry_id', '=', $inquiry_id)->where('lang_cd', '=', $lang_cd)->find();
		if (isset($item_description->inquiry_id)) {
			$view->inquiry_title =  $item_description->title;
			$view->inquiry_description = $item_description->description;
		}

		$all_users = [];
		foreach($user_dict as $k=>$v) {
			$all_users[] = ['userId'=>$k, 'userName'=>$v];
		}
		$post['all_users'] = json_encode($all_users, JSON_UNESCAPED_UNICODE);
		$inquiry = ORM::factory('inquiry', $inquiry_id);
		$lang_setting = explode(",", $inquiry->support_lang_cd);
		$lang_cd_array = array_intersect_key($this->_model->get_code('02', $this->_lang_cd), array_flip($lang_setting));
		$item_div = $inquiry->item_div ?? self::ITEM_DIV;
		$display = ORM::factory('itemdisplay')->where('item_id', '=', $inquiry_id)->where('item_div', '=', $item_div)->find();
		$view->lang_display = array_values(array_intersect(explode(',', $display->lang_display), array_keys($lang_cd_array)));
		$view->support_lang_cd = $lang_cd_array;
		// 表示言語の中から、サポート言語の中にあるものだけを取得
		$display_lang_cd = array_values(array_intersect(explode(',', $display->lang_display), array_keys($lang_cd_array)));
		$lang_cd = $this->request->query('lang');
		if (!$lang_cd) {
			// デフォルト表示を表示言語の配列の一番目にする
			$lang_cd = $display_lang_cd[0];
		}
		$view->lang_cd = $lang_cd;
		$view->menu = $menu;

		$view->inquiry = $inquiry;
		$view->inquiry_id = $inquiry_id;
		$view->scene_cd = $this->_bot->facility_cd;
		$view->post = $post;
		$view->entry_type_list = $this->_codes['10'];
		$view->num_label_config = $num_label_config;
		$view->use_number_config = $use_number_config;
		$view->user_template_flg = $this->_model->get_inquiry_mail_template($inquiry_id);
		$view->message = $message;
		$view->coupons = $this->_model->get_coupons($this->_bot_id,"04","");
		$view->entries = json_encode($this->_model->get_inquiry_entries($inquiry_id, $lang_cd));
		$entry_actions = ORM::factory('inquiryentryaction')->where('inquiry_id', '=', $inquiry_id)->where('lang_cd', '=', $lang_cd)->find_all();
		$entry_action = [];
		foreach($entry_actions as $ea) {
			$entry_action[] = ['no'=>$ea->no, 'action'=>$ea->actions];
		}
		$view->entry_action = $entry_action;

		$view->sections = json_encode($this->_model->get_inquiry_sections($inquiry_id, $lang_cd));
		$view->branchs = json_encode($this->_model->get_inquiry_branchs($inquiry_id, $lang_cd));
		$view->lang_cd = $lang_cd;
		$maximum_model = new Model_Maximummodel();
		$view->maximum_all = $maximum_model->get_maximum_all($this->_bot_id);
		$view->maximum_all[''] = __('inquiry.common.maximum.remove');
		$view->maximum_1d1h = $maximum_model->get_maximum_1d1h($this->_bot_id);
		$view->maximum_1d = array_merge($maximum_model->get_maximum_1d($this->_bot_id),array('0'=>array('id'=>'', 'name'=>__('inquiry.common.maximum.remove'))));
		$view->maximum_1h = array_merge($maximum_model->get_maximum_1h($this->_bot_id),array('0'=>array('id'=>'', 'name'=>__('inquiry.common.maximum.remove'))));
		$payment_display = $this->_model->get_bot_tpl_message($this->_bot_id, 'payment_display', 'ja');
		$inquiry_data = json_decode($view->inquiry->inquiry_data, true);
		$payment_display_array = json_decode($payment_display, true) ?: [];
		if (!isset($inquiry_data['pms'])) {
			// PMS連携していないとき、支払い方法からPMS連携の選択肢を除外
			$payment_display_array = array_filter($payment_display_array, function($item) {
				return $item['type'] != '8';
			});
			$payment_display = json_encode(array_values($payment_display_array));
		}
		$view->payment_display = $payment_display;
		$view->json_payment_service = $this->_model->get_bot_setting($this->_bot_id, 'json_payment_service');

		$verify_url = $this->_verify_url($inquiry_id, $lang_cd);
		
		// 多言語表示OFFでその言語の検証できるようにtokenをつける
		$display_lang_arr = explode(',', $display->lang_display);
		if (!in_array($lang_cd, $display_lang_arr)) {
			$token_payload = [];
			$token_payload['data'] = json_encode(["inquiry_id"=>$inquiry_id,"lang_cd"=>$lang_cd,"bot_id"=>$this->_bot_id]);
			$verify_url = $verify_url. '&eval=' . $this->_model->get_new_token($token_payload);
		}

		$view->verify_url = $verify_url;
		$maximum_category_price = [];
		$maximums = ORM::factory('botmaximum')->where('bot_id', '=', $this->_bot_id)->find_all();
		foreach($maximums as $maximum) {
			$category_labels = [];
			if ($maximum->extra_data != null) {
				$extra_data = json_decode($maximum->extra_data, true);
				if (isset($extra_data['category'])) {
					foreach($extra_data['category'] as $c) {
						if (isset($c['title'][$lang_cd])){
							$category_labels[] = $c['title'][$lang_cd];
						}
					}
				}
			}
			/*
			$category_labels = [];
			$maximum_data = json_decode($maximum->maximum_data, true);
			if ($maximum->span == 'all') {
				if ($maximum_data && array_key_exists('category_price', $maximum_data)) {
					foreach($maximum_data['category_price'] as $c) {
						if (!in_array($c['title'][$lang_cd], $category_labels)) {
							$category_labels[] = $c['title'][$lang_cd];
						}
					}
				}
			}
			else if ($maximum->span == '1d') {
				foreach($maximum_data as $d) {
					if (array_key_exists('category_price', $d)) {
						foreach($d['category_price'] as $c) {
							if (!in_array($c['title'][$lang_cd], $category_labels)) {
								$category_labels[] = $c['title'][$lang_cd];
							}
						}
					}
				}
			}
			else if ($maximum->span == '1h') {
				foreach($maximum_data as $p) {
					if(array_key_exists('times', $p)){
						foreach($p['times'] as $d) {
							if (array_key_exists('category_price', $d)) {
								foreach($d['category_price'] as $c) {
									if (!in_array($c['title'][$lang_cd], $category_labels)) {
										$category_labels[] = $c['title'][$lang_cd];
									}
								}
							}
						}
					}
				}
			}
			*/
			if (count($category_labels) > 0) $maximum_category_price[$maximum->id] = $category_labels;
		}
		$view->maximum_category_price = $maximum_category_price;
		$lang_entries = ORM::factory('inquiryentry')->where('inquiry_id', '=', $inquiry_id)->find_all();
		$translate_from_lang = [];
		foreach($lang_entries as $de) {
			if ($de->lang_cd != $lang_cd) {
				if (!array_key_exists($de->lang_cd, $translate_from_lang)) $translate_from_lang[$de->lang_cd] = $this->_codes['02'][$de->lang_cd];
			}
		}
		$view->user_mail_template_list = $this->_model->get_bot_message_dict($this->_bot_id, '44', 'mal');
		$view->translate_from_lang = $translate_from_lang;
		
		// 「ファイルアップロード」で利用できる拡張子
		$support_file_type = ['jpg', 'jpeg', 'png', 'svg', 'gif', 'webp', 'pdf','xls','xlsx'];
		$extensions = $this->_model->formatExtensions($support_file_type);
		$view->extensions = $extensions;
		$view->upload_limit_size = '5';  // 5MBまで
		
		$this->template->content = $view;
		$url = ($inquiry_div == NULL || $inquiry_div == 9) ? '/admininquiry/inquirys' : '/adminorder/inquirys';
		$navi_next = ($inquiry_div == NULL || $inquiry_div == 9) ? $this->navi_maps_inquiry['inquirys'] : $this->navi_maps_order['inquirys'];
		$navi_current = ($inquiry_div == NULL || $inquiry_div == 9) ? $this->navi_maps_inquiry['inquiryentry'] : $this->navi_maps_order['inquiryentry'];
		$this->_page_navi('[{"f":"'. $navi_next .'","url":"' . $url . '"}, {"f":"'. $navi_current .'"}]');
	}

	public function action_inquiryresult() {
		$errors = NULL;
		$message = NULL;
		$post = [];
		$inquiry_id = NULL;
		$maximum_model = new Model_Maximummodel();
		$inquiryFilters = [];
		$show_allversion = true;
		$current_version = NULL;
		$inquiry_div = NULL;

		$user_access_limit_inquiry = [];
		$user_access_limit_entry = [];
		$full_size = '';
		if ($this->_user->role_cd == '74') {
			$orms = ORM::factory('botmsg')->where('bot_id', '=', $this->_bot_id)->where('delete_flg','=',0)->where('msg_cd', 'LIKE', 'inquiry_user_access_limit_%')->find_all();
			foreach($orms as $orm) {
				$data = $this->_model->get_bot_tpl_message($this->_bot_id, $orm->msg_cd, 'ja', true);
				foreach($data['user_access_result_limit'] as $li) {
					if (in_array($this->_user->user_id, $li['users'])) {
						$inquiry = ORM::factory('inquiry', $data['inquiry_id']);
						$user_access_limit_inquiry[$data['inquiry_id']] = $inquiry->inquiry_name;
					}
				}
			}
			$full_size = '&fullview=true';
		}

		if ($this->request->post()) {
			$post = $this->request->post();
			$inquiry_id = $post['inquiry_id'];
			if (isset($post['current_version'])) $current_version = $post['current_version'];
			if (isset($post['show_allversion'])) $show_allversion = $post['show_allversion'] == 1;
			// $inquiry_result_tages = $this->_get_session('inquiry_result_tags', []);
			$config = [
				'2260040003'=>['member_id'=>'923144d4-8c34-f676-ec13-2a3aa10601a3','maximum_id'=>226004000001,'scene_cd'=>'hanaori-ashinoko-hp'],
				'2260060004'=>['member_id'=>'a0ab4bb2-8134-ffef-5fb8-27c0a36b9c16','maximum_id'=>226006000001,'scene_cd'=>'onyado-toho-hp'],
			];
			if ($this->_page_action == 'import') {
				$inquiry_id = $post['inquiry_id'];
				if (!isset($config[$inquiry_id])) {
					$this->redirect('/admininquiry/inquiryresult?id=' . $post['inquiry_id'] . '&type=detail');
					return;
				}
				$file_date = '20' . substr(explode('.', $post['csv_file_name'])[0], -6);
				$file_date = substr($file_date, 0, 4) . '-' . substr($file_date, 4, 2) . '-' . substr($file_date, 6, 2);
				$csv_data = substr($post['csv_file'], strpos($post['csv_file'], ',') + 1);
				$csv_data = base64_decode($csv_data);
				$csv_data = mb_convert_encoding($csv_data, "UTF-8", "SJIS");
				$lines = explode(PHP_EOL, $csv_data);
				array_shift($lines);
				$member_id = $config[$inquiry_id]['member_id'];
				foreach($lines as $line) {
					$f = explode(",", $line);
					if ($f[2] == '') continue;
					$order_info = [];
					$result = ORM::factory('inquiryresult');
					$result->inquiry_id = $inquiry_id;
					$result->bot_id = $this->_bot_id;
					$result->scene_cd = $config[$inquiry_id]['scene_cd'];
					$result->member_id = $member_id;
					$result->lang_cd = 'ja';
					$result->start_time = $file_date;
					$result->end_time = $result->start_time;
					$result->status_cd = '01';
					$result->save();
					// お名前
					$entry = ORM::factory('inquiryresultentry');
					$entry->result_id = $result->id;
					$entry->no = 3;
					$entry->entry_result = json_encode(['full_name'=> $f[2], 'furigana'=> '']);
					$entry->entry_data = $f[2];
					$entry->save();
					$order_info[] = ['no'=>3, 'value'=>$entry->entry_data];
					// 枠
					$entry = ORM::factory('inquiryresultentry');
					$entry->result_id = $result->id;
					$entry->no = 4;
					if ($f[6] == '17:00') {
						$time = '17:00-18:30';
					}
					else if ($f[6] == '17:15') {
						$time = '17:15-18:45';
					}
					else if ($f[6] == '17:30') {
						$time = '17:30-19:00';
					}
					else if ($f[6] == '18:00') {
						$time = '18:00-19:30';
					}
					else if ($f[6] == '19:30') {
						$time = '19:30-21:00';
					}
					else if ($f[6] == '19:45') {
						$time = '19:45-21:15';
					}
					else if ($f[6] == '20:00') {
						$time = '20:00-21:30';
					}
					else if ($f[6] == '17:45') {
						$time = '17:45-19:15';
					}
					else {
						$time = '';
					}
					$entry->entry_result = json_encode(['maximum_id'=>$config[$inquiry_id]['maximum_id'], 'date'=> $file_date, 'time'=> $time, 'price'=> '0', 'num'=>$f[4], 'unit'=>'LABEL_PEOPLE']);
					$entry->entry_data = $file_date . ' ' . $time . ' 人数 × ' . $f[4];
					$entry->save();
					$order_info[] = ['no'=>4, 'value'=>$entry->entry_data];
					// 部屋番号
					$entry = ORM::factory('inquiryresultentry');
					$entry->result_id = $result->id;
					$entry->no = 7;
					$entry->entry_result = $f[10];
					$entry->entry_data = $f[10];
					$entry->save();
					$order_info[] = ['no'=>7, 'value'=>$entry->entry_data];
					// メモ
					$entry = ORM::factory('inquiryresultentry');
					$entry->result_id = $result->id;
					$entry->no = 8;
					$entry->entry_result = $f[12] . PHP_EOL . (count($f) > 13 ? $f[13] : '') . (count($f) > 14 ? $f[14] : '');
					$entry->entry_data = $entry->entry_result;
					$entry->save();
					$order_info[] = ['no'=>8, 'value'=>$entry->entry_data];

					if ($time != '') {
						$maximum_model = new Model_Maximummodel();
						$e = ['maximum_id'=>$config[$inquiry_id]['maximum_id'], 'day'=>$file_date, 'time'=>$time, 'num'=>$f[4], 'order_info'=>$order_info];
						$ret = $maximum_model->execute_maximum($this->_bot_id, $e, $member_id, $result->id, '01');
					}
				}
				$this->redirect('/admininquiry/inquiryresult?id=' . $post['inquiry_id'] . '&type=detail');
				return;
			}
			else if ($this->_page_action == 'delete-import') {
				$inquiry_id = $post['inquiry_id'];
				if (!isset($config[$inquiry_id])) {
					$this->redirect('/admininquiry/inquiryresult?id=' . $post['inquiry_id'] . '&type=detail');
					return;
				}
				$end_time = date('Y-m-d') . ' 00:00:00';
				$sql = 'SELECT id FROM t_inquiry_result WHERE inquiry_id=:inquiry_id AND end_time=:end_time';
				$query = DB::query(Database::SELECT, $sql);
				$query->parameters(array(
					':inquiry_id' => $inquiry_id,
					':end_time' => $end_time,
				));
				$result = $query->execute()->as_array();
				Database::instance()->begin();
				try {
					foreach ($result as $r) {
						$maximum = ORM::factory('maximumorder')->where('link_id', '=', $r['id'])->find();
						if (isset($maximum->maximum_id)) {
							$m = ['maximum_id' => $maximum->maximum_id, 'day' => $maximum->day, 'time' => $maximum->time, 'num' => $maximum->num];
							$maximum_model->restore_maximum_only($this->_bot_id, $m);
							DB::delete('t_maximum_order')->where('link_id', '=', $r['id'])->execute();
						}
						DB::delete('t_inquiry_result')->where('id', '=', $r['id'])->execute();
						DB::delete('t_inquiry_result_entry')->where('result_id', '=', $r['id'])->execute();
					}
					Database::instance()->commit();
				}
				catch(Exception $e) {
					Database::instance()->rollback();
				}
				$this->redirect('/admininquiry/inquiryresult?id=' . $post['inquiry_id'] . '&type=detail');
			}
			else if ($this->_page_action == 'delete') {
				// $new_tages = [];
				// foreach($inquiry_result_tages as $k=>$v) {
				// 	if ($k == $post['filter_entry_delete'] && ($v == $post['filter_entry_answer_delete'] || $post['filter_entry_answer_delete'] == '')) continue;
				// 	$new_tages[$k] = $v;
				// }
				// $inquiry_result_tages = $new_tages;
				// $this->_set_session('inquiry_result_tags', $inquiry_result_tages);
			}
			else if ($this->_page_action == 'cancel') {
				Database::instance()->begin();
				try {
					$this->_model->call_admin_api('inquiry', 'inquirycancel', 'post', ['bot_id'=>$this->_bot_id, 'user_id'=>$this->_user_id, 'result_id'=>$post['result_id']]);
					$orm = ORM::factory('inquiryresultsupport');
					$orm->result_id = $post['result_id'];
					$orm->no = $this->_model->get_inquiry_result_support_no($post['result_id']);
					$orm->support_type_cd = '04';
					$orm->memo = '';
					$orm->upd_user = $this->_user_id;
					$orm->save();
					Database::instance()->commit();
				}
				catch(Exception $e) {
					Database::instance()->rollback();
				}
				$this->redirect('/admininquiry/inquiryresult?id=' . $post['inquiry_id'] . '&type=detail');
			}
			else if ($this->_page_action == 'cancel-payment') {
				try {
					if ($this->_user_id == 1 || $this->_user_id == 1216) {
						$maximum_model->cancel_payment($this->_bot_id, $post['result_id'], $this->_user_id);
					}
				}
				catch(Exception $e) {
				}
				$this->redirect('/admininquiry/inquiryresult?id=' . $post['inquiry_id'] . '&type=detail');
			}
			else if ($this->_page_action == 'clear') {
				// $inquiry_result_tages = [];
				// $this->_set_session('inquiry_result_tags', $inquiry_result_tages);
				$inquiryFilters = [];
			}
			else {
				// if ($post['inquiry_entry'] != '') {
				// 	$existed = false;
				// 	foreach($inquiry_result_tages as $k=>$v) {
				// 		if ($k == $post['inquiry_entry'] && $v == $post['inquiry_entry_answer']) {
				// 			$existed = true;
				// 			break;
				// 		}
				// 	}
				// 	if (!$existed) {
				// 		$inquiry_result_tages[strval($post['inquiry_entry'])] = $post['inquiry_entry_answer'];
				// 	}
				// 	$this->_set_session('inquiry_result_tags', $inquiry_result_tages);
				// }
				if ($post['branches'] != '') {
					$inquiryFilters = json_decode($post['branches'], JSON_UNESCAPED_UNICODE);
				}
			}
			$post['inquiry_entry'] = '';
			$post['inquiry_entry_answer'] = '';

			Session::instance()->set('inquiryresult_start_date', $post['start_date']);
			Session::instance()->set('inquiryresult_end_date', $post['end_date']);
			Session::instance()->set('inquiryresult_lang_cd', $post['lang_cd']);
			Session::instance()->set('inquiryresult_is_blocked', $post['is_blocked']);
			Session::instance()->set('inquiryresult_is_deleted', $post['is_deleted']);
			Session::instance()->set('inquiryresult_status_cd', $post['status_cd']);
			Session::instance()->set('inquiryresult_support_type_cd', $post['support_type_cd']);
			$result_id = NULL;
			$post['result_id'] = NULL;
		} else {
			$post['inquiry_entry'] = '';
			$post['inquiry_entry_answer'] = '';

			$inquiry_id = $this->request->query('id', NULL);
			$result_id = $this->request->query('result_id', NULL);
			$item = ORM::factory('inquiry', $inquiry_id);
			if ($this->_inquiry_order_start && $item->item_div) {
				$match_action = $item->item_div == 9 ? 'admininquiry' : 'adminorder';
				if ($this->_path != $match_action) {
					$this->redirect('/' . $match_action . '/inquiryresult?id=' . $inquiry_id . '&type=detail');
				}
			}
			$inquiry_div = $this->_inquiry_order_start ? $item->item_div : NULL;
			if ($result_id != NULL && $inquiry_id == NULL) {
				$result_orm = ORM::factory('inquiryresult', $result_id);
				$inquiry_id = $result_orm->inquiry_id;
			}
			if ($result_id != NULL) $post['result_id'] = $result_id;
			if ($this->_user->role_cd == '74') {
				if ($inquiry_id == NULL) $inquiry_id = array_key_first($user_access_limit_inquiry);
			}
			$permission = $this->_item_permission_check($inquiry_id);
			if ($permission < 0) {
				$url = ($inquiry_div == NULL || $inquiry_div == 9) ? '/admininquiry/inquirys' : '/adminorder/inquirys';
				$this->redirect($url);
			}
			$post['type'] = $this->request->query('type', NULL);
			if ($post['type'] == NULL) {
				$post['type'] = 'total';
			}
			$post['lang_cd'] = Session::instance()->get('inquiryresult_lang_cd', NULL);
			if ($post['lang_cd'] === NULL) {
				if ($post['type'] == 'total') {
					//questions are different between lang, but use '' may better. fixed for #53783
					$post['lang_cd'] = '';
				} else {
					$post['lang_cd'] = '';
				}
			}
			$post['sns_type_cd'] = Session::instance()->get('inquiryresult_sns_type_cd', NULL);
			if ($post['sns_type_cd'] === NULL) {
				$post['sns_type_cd'] = '';
			}
			$post['member_name'] = Session::instance()->get('inquiryresult_member_name', NULL);
			if ($post['member_name'] === NULL) {
				$post['member_name'] = '';
			}
			$post['member_no'] = Session::instance()->get('inquiryresult_member_no', NULL);
			if ($post['member_no'] === NULL) {
				$post['member_no'] = '';
			}
			$post['start_date'] = Session::instance()->get('inquiryresult_start_date', NULL);
			if ($post['start_date'] === NULL) {
				$post['start_date'] = date('Y-m-d', strtotime('-1 month'));
			}
			$post['end_date'] = Session::instance()->get('inquiryresult_end_date', NULL);
			if ($post['end_date'] === NULL) {
				$post['end_date'] = date('Y-m-d');
			}
			$post['is_blocked'] = Session::instance()->get('inquiryresult_is_blocked', NULL);
			if ($post['is_blocked'] === NULL) {
				$post['is_blocked'] = '0';
			}
			$post['is_deleted'] = Session::instance()->get('inquiryresult_is_deleted', NULL);
			if ($post['is_deleted'] === NULL) {
				$post['is_deleted'] = '0';
			}
			$post['status_cd'] = Session::instance()->get('inquiryresult_status_cd', NULL);
			if ($post['status_cd'] === NULL) {
				$post['status_cd'] = '';
			}
			$post['support_type_cd'] = Session::instance()->get('inquiryresult_support_type_cd', NULL);
			if ($post['support_type_cd'] === NULL) {
				$post['support_type_cd'] = '';
			}
			$post['current_version'] = NULL;
			$post['show_allversion'] = 1;
		}
		$inquiry_orm = ORM::factory('inquiry', $inquiry_id);
		if ($this->_inquiry_order_start && $inquiry_orm->item_div) {
			$match_action = $inquiry_orm->item_div == 9 ? 'admininquiry' : 'adminorder';
			if ($this->_path != $match_action) {
				$this->redirect('/' . $match_action . '/inquiryresult?id=' . $inquiry_id);
			}
		}
		$inquiry_div = $this->_inquiry_order_start ? $inquiry_orm->item_div : NULL;

		Session::instance()->set('inquiry_result_refresh_time', date('Y-m-d H:i:s'));
		$post['csv_type_cd'] = '1';

		// get current inquiry
		$inquiry = ORM::factory('inquiry', $inquiry_id);
		$inquiry_data = json_decode($inquiry->inquiry_data, true);
		// get temp lang cd
		$temp_lang_cd = $post['lang_cd'];
		if ($temp_lang_cd == '') {
			$temp_lang_cd = 'ja';
		}

		// get support languages
		//フォーム統計タブの場合、「すべて言語」は機能しないため、一時的に非表示。#fix/53783
		if ($post['type'] == 'detail') {
			$support_lang_cd_array = [''=>__('admin.inquiryresult.label.language_default')];
		}
		foreach(explode(',', $inquiry->support_lang_cd) as $lang) {
			$support_lang_cd_array[$lang] = $this->_codes['02'][$lang];
		}

		// get inquiry status list
		$inquiry_status_list = $this->_model->get_bot_tpl_message($this->_bot_id, 'inquiry.support_status_' . $inquiry_id, $this->_lang_cd, true);
		if (count($inquiry_status_list) == 0) {
			$inquiry_status_list = $this->_model->get_bot_tpl_message($this->_bot_id, 'inquiry.support_status', $this->_lang_cd, true);
		}
		$inquiry_status_list_fixed = $this->_model->get_bot_tpl_message($this->_bot_id, 'inquiry.support_status_fixed', $this->_lang_cd, true);
		
		// get inquiry entries
		if ($current_version != NULL) {
			// 他のバージョンを選択された場合
			$inquiry_entry = ORM::factory('inquiryentry')->where('inquiry_id', '=', $current_version)->where('lang_cd', '=', $temp_lang_cd)->order_by('no')->find_all();
		} else {
			$inquiry_entry = ORM::factory('inquiryentry')->where('inquiry_id', '=', $inquiry_id)->where('lang_cd', '=', $temp_lang_cd)->order_by('no')->find_all();
		}

		// $inquiry_entry_type = [];
		$maximums = [];

		foreach($inquiry_entry as $entry) {
			if ($entry->entry_data != '' && $entry->entry_data != '[]') {
				$entry_data = json_decode($entry->entry_data, true);
				if (is_array($entry_data)) {
					if (array_key_exists('maximum', $entry_data)) {
						if (!array_key_exists($entry_data['maximum'], $maximums)) {
							$maximums[$entry_data['maximum']] = $entry_data['title'];
						}
					}
					else {
						foreach($entry_data as $edata) {
							if (is_array($edata) && array_key_exists('maximum', $edata)) {
								if (!array_key_exists($edata['maximum'], $maximums)) {
									$maximums[$edata['maximum']] = $edata['title'];
								}
							}
						}
					}
				}
			}
		}

		$maximum_category_price = [];
		$botMaximums = [];
		$maximum_orms = ORM::factory('botmaximum')->where('bot_id', '=', $this->_bot_id)->find_all();
		foreach($maximum_orms as $maximum) {
			$botMaximums[$maximum->id] = $maximum->name;
			$category_labels = [];
			if ($maximum->extra_data != null) {
				$extra_data = json_decode($maximum->extra_data, true);
				if (isset($extra_data['category'])) {
					foreach($extra_data['category'] as $c) {
						if (isset($c['title'][$temp_lang_cd]) && isset($c['title']['ja'])) {
							if ($c['title'][$temp_lang_cd] == '') {
								$category_labels[] = $c['title']['ja'];
							} else {
								$category_labels[] = $c['title'][$temp_lang_cd];
							}
						}
					}
				}
			}
			if (count($category_labels) > 0) $maximum_category_price[$maximum->id] = $category_labels;
		}

		// バージョン処理
		$all_version_options = [];
		$all_version_options[$inquiry_id] = $inquiry_id . ' ' . $inquiry->inquiry_name;
		$all_version = [$inquiry_id];
		if ($inquiry->base_ver_id != NULL) {
			$all_version[] = $inquiry->base_ver_id;
			$versions = ORM::factory('inquiry')->where('base_ver_id', '=', $inquiry->base_ver_id)->find_all();
			foreach($versions as $v) {
				if (!in_array($v->inquiry_id, $all_version)) {
					$all_version[] = $v->inquiry_id;
					$all_version_options[$v->inquiry_id] = $v->inquiry_id . ' ' . $v->inquiry_name;
				}
			}
		}
		$versions = ORM::factory('inquiry')->where('base_ver_id', '=', $inquiry_id)->find_all();
		foreach ($versions as $v) {
			if (!in_array($v->inquiry_id, $all_version)) {
				$all_version[] = $v->inquiry_id;
				$all_version_options[$v->inquiry_id] = $v->inquiry_id . ' ' . $v->inquiry_name;
			}
		}
		
		if ($inquiry_div == 9) {
			unset($this->_codes['25']['00']);
		}
		
		$inquiry_setting = $this->_model->get_bot_setting($this->_bot_id, 'json_next_inquiry_setting', true);
		// Viewの共通関数 (part 1)
		$view = View::factory($this->_view_path . 'inquiryresult');
		$menu = View::factory($this->_view_path . 'inquirymenu');
		$menu->inquiry_id = $inquiry_id;
		$menu->inquiry_div = $inquiry_div;
		$view->inquiry_div = $inquiry_div;
		$view->menu = $menu;
		$view->memo_box = View::factory('admin/memobox');
		$view->lang_cd_list = $support_lang_cd_array;
		$view->status = $this->_codes['25'];
		$view->buttons = $inquiry_status_list;
		$view->buttons_fixed = $inquiry_status_list_fixed;
		$view->button_color = $this->_model->get_config('buttoncolor');
		$view->inquiry_id = $inquiry_id;
		$view->inquiry_setting = $inquiry_setting ?: [];
		$view->maximums = $maximums;
		$view->maximum_category_price = $maximum_category_price;
		$view->inquiry = $inquiry;
		$view->csv_type = ['1'=>'複数回答一括','2'=>'複数回答分割'];
		$view->labelcolor = ['01'=>'#d84a38', '02'=>'#FFB848', '03'=>'#1BBC9B', '04'=>'#95A5A6', '05'=>'#CC00B2', '06'=>'#798a57'];
		$view->verify_url = $this->_verify_url($inquiry_id);
		$mail_sender_alias = '';
		if (isset($inquiry_data['mail_sender']) && $inquiry_data['mail_sender'] == '1') {
			$mail_sender_alias = "=?utf-8?B?". base64_encode($this->_bot->bot_name) . "?=";
		}
		$view->mail_sender_alias = $mail_sender_alias;
		$view->clink_url = $this->_model->get_env('clink_url');
		
		$all_language_same = 1;
		$self_assigned_signature = $this->_model->get_self_assigned_signature($this->_bot_id, $this->_user_id);
		if ($self_assigned_signature) {
			$mail_signature_id = $self_assigned_signature['id'];
		} else {
			$mail_signature_id = $inquiry_data['mail_signature'] ?? ($inquiry_setting['mail_signature'] ?? '');
		}
		$mail_signature = [];
		if ($mail_signature_id) {
			$signatures = $this->_model->get_signature_all_language($mail_signature_id, $this->_bot_id);
			foreach ($signatures as $lang_cd => $signature_data) {
				$mail_signature[$lang_cd] = $signature_data['sign_detail'] ?? '';
			}
		}
		$view->mail_signature = $mail_signature;
		
		$mail_template = $this->_model->get_bot_tpl_message($this->_bot_id, 'inquiry.reply_inquiry_mail_template', $this->_lang_cd, false);
		$mail_template = str_replace(
			['{hotel_name}', '{question_prompt}', '{answer_point}', '{signature}', '{to_stuff_prompt}', '{ユーザー名前}'],
			[$this->_bot->hotel_name, '', '', '', '', '[お名前]'],
			$mail_template
		);
		$view->mail_template = $mail_template;

		// 絞り込み共通部分
		if ($current_version != NULL) {
			$inquiry_branch_entries = $this->_model->get_inquiry_filter_entries($current_version);
		} else {
			$inquiry_branch_entries = $this->_model->get_inquiry_filter_entries($inquiry_id);
		}
		$inquiry_entry_result_group_by_id = [];

		$maximum_amount_label = $this->_model->get_bot_tpl_message($this->_bot_id, 'maximum_amount_label', $this->_lang_cd, true);

		Session::instance()->set('inquiry_result_show_ids', NULL);

		// get form entries in format
		$inquiry_entries = [];
		foreach($inquiry_entry as $entry) {
			$entries = [];
			if ($this->_is_choice($entry->entry_type_cd)) {
				$entry_data = json_decode($entry->entry_data, true);
				if (!is_array($entry_data)) {
					$entry_data = $this->_model->get_code_div_kv($entry->entry_data, $post['lang_cd']);
					$i=0;
					foreach($entry_data as $k=>$v) {
						$entries[strval($i+1)] = ['title'=>$v, 'count'=>0];
						$i++;
					}
				}
				else {
					foreach($entry_data as $k=>$v) {
						if (is_array($v)) {
							if (array_key_exists('title', $v)) {
								$entries[strval($k+1)] = ['title'=>$v['title'], 'count'=>0];
							}
							else if (array_key_exists('image', $v)) {
								$entries[strval($k+1)] = ['title'=>$v['image'], 'count'=>0];
							}
						}
						else {
							$entries[strval($k+1)] = ['title'=>$v, 'count'=>0];
						}
					}
				}
			}
			$inquiry_entries[strval($entry->no)] = ['title'=>$entry->title, 'entry_type_cd'=>$entry->entry_type_cd, 'input_rules'=>$entry->input_rules, 'required'=>$entry->required, 'entry_count'=>$entries];
		}
		
		if ($inquiry->user_in_charge != NULL) {
			$user_in_charge_array = explode(',', $inquiry->user_in_charge);
			$user_is_in_charge = in_array($this->_user->user_id, $user_in_charge_array);
		} else {
			$user_is_in_charge = true;
		}
		$mask_privacy = true;
		if ($this->_user->privacy_show_flg == 0) {
			if ($this->_user->privacy_self_show_flg == 1 && $user_is_in_charge) {
				$mask_privacy = false;
			}
		} else {
			$mask_privacy = false;
		}

		if ($post['type'] == 'detail' && $result_id == NULL) {
			$_all_versions = [];
			if ($show_allversion == true) {
				$_all_versions = $all_version;
			} else {
				if ($current_version != NULL) {
					$_all_versions = [$current_version];
				} else {
					$_all_versions = [$inquiry_id];
				}
			}

			$total = 0;
			$inquiry_name = [];
			$_inquiry_entry_result = [];
			$inquiry_result = [];
			$inquiry_entry_result = [];
			$inquiry_result_support = [];
			$_inquiry_entry = [];
			$inquiry_section = [];
			$inquiry_not_show_frs_no_arr = [];
			$entry_type_arr = [];
			$inquiry_entry_dict = [];

			$page_size = Session::instance()->get('inquiryresult_page_size', NULL);
			if ($page_size == NULL) $page_size = 100;
			isset($post['paging'])? $post['paging'] = json_decode($post['paging'], true) : $post['paging'] = ['count'=>0, 'size'=>$page_size, 'page'=>0, 'all'=>1];
			Session::instance()->set('inquiryresult_page_size', $post['paging']['size']);

			$post['paging']['count'] = $this->_model->get_inquiry_result_count($this->_bot_id, $_all_versions, $post['start_date'], $post['end_date'], intval($post['is_blocked']), intval($post['is_deleted']), '', '', $post['lang_cd'], '', '');
			$inquiry_result = $this->_model->get_inquiry_result_page($this->_bot_id, $_all_versions, $post['start_date'], $post['end_date'], intval($post['is_blocked']), intval($post['is_deleted']), '', '', $post['lang_cd'], '', '', $post['paging']['page']*$post['paging']['size'], $post['paging']['size']);
			if (count($inquiry_result) > 0) {
				$end = $inquiry_result[0]['id'];
				$start = $inquiry_result[count($inquiry_result) - 1]['id'];
				$inquiry_entry_result_arr = $this->_model->get_inquiry_entry_result_page($this->_bot_id, $_all_versions, $start, $end, intval($post['is_blocked']), $post['lang_cd']);
				$inquiry_entry_result = $inquiry_entry_result_arr;
				foreach($inquiry_entry_result_arr as $r) {
					if (!array_key_exists($r['result_id'], $_inquiry_entry_result)) {
						$_inquiry_entry_result[$r['result_id']] = [];
					}
					$_inquiry_entry_result[$r['result_id']][$r['no']] = $r;
				}
				$inquiry_result_support_arr = $this->_model->get_inquiry_result_support_page($this->_bot_id, $_all_versions, $start, $end, $post['lang_cd']);
				foreach($inquiry_result_support_arr as $r) {
					if (!array_key_exists($r['result_id'], $inquiry_result_support)) {
						$inquiry_result_support[$r['result_id']] = [];
					}
					$inquiry_result_support[$r['result_id']][] = $r;
				}
				if (isset($inquiry_data['is_mail_logged']) && $inquiry_data['is_mail_logged'] == '1') {
					$result_ids = array_keys($_inquiry_entry_result);
					Session::instance()->set('inquiry_result_show_ids', $result_ids);
					$inquiry_result_mails = $this->_model->call_admin_api('inquiry', 'inquiryresultmails', 'post', ['result_ids'=>$result_ids]);
					foreach($inquiry_result_mails as $rid => $mails) {
						$temp_support = array_merge($inquiry_result_support[$rid] ?? [], $mails, []);
						usort($temp_support, function($a, $b) {
							return strtotime($b['upd_time']) - strtotime($a['upd_time']);
						});
						$inquiry_result_support[$rid] = $temp_support;
					}
				}
			}
			foreach ($_all_versions as $id) {
				$inquiry_orm = ORM::factory('inquiry', $id);
				$inquiry_name[$id] = $inquiry_orm->inquiry_name;
				/*
				$inquiry_result = array_merge($inquiry_result, $this->_model->get_inquiry_result($this->_bot_id, $id, $post['start_date'], $post['end_date'], intval($post['is_blocked']), '', '', $post['lang_cd'], '', ''));
				$inquiry_entry_result_arr = $this->_model->get_inquiry_entry_result($this->_bot_id, $id, $post['start_date'], $post['end_date'], intval($post['is_blocked']), $post['lang_cd']);
				
				// 絞り込み用 inquiry_entry_result
				if ($id == $inquiry_id) {
					$inquiry_entry_result = $inquiry_entry_result_arr;
				}

				foreach($inquiry_entry_result_arr as $r) {
					if (!array_key_exists($r['result_id'], $_inquiry_entry_result)) {
						$_inquiry_entry_result[$r['result_id']] = [];
					}
					$_inquiry_entry_result[$r['result_id']][$r['no']] = $r;
				}
				$inquiry_result_support_arr = $this->_model->get_inquiry_result_support($this->_bot_id, $id, $post['start_date'], $post['end_date'], $post['lang_cd']);
				foreach($inquiry_result_support_arr as $r) {
					if (!array_key_exists($r['result_id'], $inquiry_result_support)) {
						$inquiry_result_support[$r['result_id']] = [];
					}
					$inquiry_result_support[$r['result_id']][] = $r;
				}
				*/
				$entry_arr = ORM::factory('inquiryentry')->where('inquiry_id', '=', $id)->order_by('no')->find_all();
				$_inquiry_entry[$id] = [];
				$inquiry_not_show_frs_no_arr[$id] = [];
				$entry_type_arr[$id] = [];
				$inquiry_entry_dict[$id] = [];
				foreach($entry_arr as $e) {
					$entry_type_arr[$id][$e->no] = $e->entry_type_cd;
					$_inquiry_entry[$id][$e->lang_cd][$e->no] = strip_tags($e->title);
					$e->entry_data = json_decode($e->entry_data, true);
					$inquiry_entry_dict[$id][$e->lang_cd][$e->no] = $e;
					$inquiry_not_show_frs_no_arr[$id][$e->lang_cd] = [];
					if ($e->entry_type_cd == 'frs') {
						if ($_inquiry_entry[$id][$e->lang_cd][$e->no] == preg_replace('/\/\?.*\?\//', '', $_inquiry_entry[$id][$e->lang_cd][$e->no])) {
							$_inquiry_entry[$id][$e->lang_cd][$e->no] = '';
							$inquiry_not_show_frs_no_arr[$id][$e->lang_cd][] = $e->no;
						}
						else {
							$_inquiry_entry[$id][$e->lang_cd][$e->no] = str_replace('¥', '', preg_replace('/\/\?.*\?\//', '', $_inquiry_entry[$id][$e->lang_cd][$e->no]));
						}
					}
				}
				$sections = ORM::factory('inquirysection')->where('inquiry_id', '=', $id)->where('delete_flg', '=', 0)->order_by('lang_cd')->order_by('sort_no')->find_all();
				if ($sections->count() == 0) continue;
				$section = [];
				$section_lang_cd = [];
				$inquiry_section[$id] = [];
				foreach($sections as $s) {
					if ($s->lang_cd != $section_lang_cd && count($section) > 0) {
						$inquiry_section[$id][$section_lang_cd] = $section;
						$section = [];
					}
					$section = array_merge($section, explode(',', $s->entries));
					$section_lang_cd = $s->lang_cd;
				}
				$inquiry_section[$id][$section_lang_cd] = $section;
			}
			$section_str_arr = [];
			foreach($inquiry_section[$inquiry_id] as $v) {
				$section_str_arr[] = implode(',', $v);
			}
			for($i = 0; $i < count($section_str_arr) - 1; $i++) {
				if ($section_str_arr[$i] != $section_str_arr[$i + 1]) {
					$all_language_same = 0;
					break;
				}
			}
			foreach($inquiry_result as &$result) {
				$payment_amount = 0;
				$charges = 0;
				$result_data = json_decode($result['result_data'], true);
				if (is_array($result_data)) {
					if (array_key_exists('amount', $result_data)) {
						$payment_amount = $result_data['amount'];
					}
					else if (array_key_exists('payment', $result_data) && array_key_exists('amount', $result_data['payment'])) {
						$payment_amount = $result_data['payment']['amount'];
						if (isset($result_data['payment']['charges'])) $charges = intval($result_data['payment']['charges']);
					}
				}
				if (!isset($_inquiry_entry_result[$result['id']])) continue;
				$entry_result = $_inquiry_entry_result[$result['id']];
				$i = 1;
				$html = [];
				$result['answer_html'] = implode(PHP_EOL, $html);
				$entry_total = '';
				$entry_total_price = NULL;
				$tax_label = '';
				if (!isset($inquiry_section[$result['inquiry_id']][$result['lang_cd']])) {
					continue;
				}
				foreach ($inquiry_section[$result['inquiry_id']][$result['lang_cd']] as $no) {
					if (in_array($no, $inquiry_not_show_frs_no_arr[$result['inquiry_id']][$result['lang_cd']])) {
						continue;
					}
					if (isset($entry_result[$no])) {
						if ($entry_type_arr[$result['inquiry_id']][$no] == 'frs') {
							$format_answer = str_replace('.', ',', $entry_result[$no]['entry_data']);
						}
						else if ($entry_type_arr[$result['inquiry_id']][$no] == 'fup') {
							$format_answer = '';
							if ($entry_result[$no]['entry_data'] != '') {
								$files = explode(',', $entry_result[$no]['entry_data']);
								$ptr = 1;
								foreach($files as $file) {
									$ext = end(explode('.', $file));
									$format_answer .= '<a style="margin-right:10px;" href="' . $file . '" target="_blank">' . $ext . $ptr++ .'</a>';
								}
							}
						}
						else {
							$format_answer = $entry_result[$no]['entry_data'];
							if ($mask_privacy) {
								$input_rules = json_decode($inquiry_entries[strval($no)]['input_rules'], true);
								if (is_array($input_rules) && array_key_exists('type', $input_rules)) {
									if ($input_rules['type'] == 'tel' || $input_rules['type'] == 'mail' || $input_rules['type'] == 'postcode' || $input_rules['type'] == 'name' || $input_rules['type'] == 'name_full' || $input_rules['type'] == 'name_separate' || $input_rules['type'] == 'address') {
										$format_answer = $this->_model->mask_string($entry_result[$no]['entry_data'], 5, '*');
									}
								}
							}
						}
						if ($entry_result[$no]['entry_extra_info'] != '') {
							$format_answer = str_replace(['"','[', ']'], '', $format_answer);
							$entry_extra_info = json_decode($entry_result[$no]['entry_extra_info'], true);
							if (is_array($entry_extra_info)) {
								if (array_key_exists('detail', $entry_extra_info)) {
									$detail = trim(strip_tags(str_replace('<br>', PHP_EOL, $entry_extra_info['detail'])));
									$pos2 = strpos($detail, '小計:');
									if ($pos2 > 0) {
										$detail = substr($detail, 0, $pos2 - 1); // -1 delete last PHP_EOL
									}
									else {
										$detail = '';
									}
									$detail = trim($detail);
									if ($detail != '' && strpos($format_answer, $detail) === false) $format_answer = $format_answer . PHP_EOL . trim($detail);
								}
								if (array_key_exists('coupon', $entry_extra_info) && array_key_exists('discount_result', $entry_extra_info['coupon'])) {
									$format_answer = $format_answer . PHP_EOL . '-￥' . number_format($entry_extra_info['coupon']['discount_result']);
								}
								if (array_key_exists('total', $entry_extra_info)) {
									$entry_total = $entry_extra_info['total'];
								}
							}
						} else {
							$entry_result_data = json_decode($entry_result[$no]['entry_result'], true);
							// 新版inquiry向けの処理
							// 内訳はentry_extra_infoに保存ではなく、entry_resultに保存しています。
							if ($entry_result_data != NULL) {
								if (isset($entry_result_data['items']) && is_array($entry_result_data['items'])) {
									if (is_null($entry_total_price)) { 
										$entry_total_price = 0;
									}
									$items = $entry_result_data['items'];
									$tax_sum = '';
									$tax_array = ['' => 0, 'tax-none' => 1, 'tax' => 2, 'tax-service' => 3];
									foreach ($items as $item) {
										$entry_option = $inquiry_entry_dict[$result['inquiry_id']][$result['lang_cd']][$no]->entry_data[$item['value'] - 1];
										if (isset($entry_option['price_type'])) {
											$tax = $entry_option['price_type'];
											if ($tax_array[$tax] > $tax_array[$tax_sum]) {
												$tax_sum = $entry_option['price_type'];
											}
										}
										if (isset($item['price'])) {
											$price = intval($item['price']);
											$discount = 0;
											$spans = NULL;
											$item_total = 0;
											if (isset($item['discount_price'])) {
												$discount = intval($item['discount_price']);
											}
											if (isset($item['spans'])) {
												$spans = intval($item['spans']);
											}
											$item_total = ($price - $discount) * intval($item['num']);
											if ($spans != NULL) {
												$item_total = ($price - $discount) * intval($item['num']) * $spans;
											}
											if (isset($item['discount'])) {
												$item_total = $item_total - intval($item['discount']);
											}
											$entry_total_price += $item_total;
										}
									}
									$tax_label = ($tax_sum === '') ? '' : $maximum_amount_label['LABEL_' . strtoupper(str_replace('-', '_', $tax_sum))];
								}
								else if (isset($entry_result_data['price'])) {
									$num = isset($entry_result_data['num'])?intval($entry_result_data['num']):1;
									$entry_total_price += intval($entry_result_data['price']) * $num;
									if (isset($entry_result_data['discount'])) {
										$entry_total_price = $entry_total_price - intval($entry_result_data['discount']);
									}
								}
							}
						}
						$html[] = $i . '.' . $_inquiry_entry[$result['inquiry_id']][$result['lang_cd']][$no] . PHP_EOL . '<span style="color:royalblue;">' . $format_answer . '</span>';
						$i++;
					}
				}
				if ($payment_amount > 0) {
					$html[] = PHP_EOL . '<span style="color:royalblue;">' . $maximum_amount_label['LABEL_SUM_TOTAL'] . ':' . str_replace('{total}', number_format($payment_amount), str_replace('{tax}', $tax_label, $maximum_amount_label['LABEL_SUM_TOTAL_UNIT'])) . '</span>';
					if ($result['status_cd'] == '01' || $result['status_cd'] == '02') {
						$total += intval($payment_amount);
					}
					else if ($result['status_cd'] == '03') {
						if ($charges > 0) {
							$total += $charges;
							$html[] = PHP_EOL . '<span style="color:#CC0000;">' . str_replace('{charges}', number_format($charges), $maximum_amount_label['LABEL_CHARGES']) . '</span>';
						}
					}
				}
				else if ($entry_total != '') {
					// 旧版合計
					$html[] = PHP_EOL . '<span style="color:royalblue;">' . $entry_total . '</span>';
				} 
				else if ($entry_total_price != NULL) {
					// 新版inquiry向けの処理
					// 内訳はentry_extra_infoに保存ではなく、entry_resultに保存しています。
					if ($result['status_cd'] == '01' || $result['status_cd'] == '02') {
						$total += intval($entry_total_price);
					}
					$html[] = PHP_EOL . '<span style="color:royalblue;">' . $maximum_amount_label['LABEL_SUM_TOTAL'] . ':' . str_replace('{total}', number_format($entry_total_price), str_replace('{tax}', $tax_label, $maximum_amount_label['LABEL_SUM_TOTAL_UNIT'])) . '</span>';
				}
				if (count($_all_versions) > 1) {
					$result['answer_html'] = $inquiry_name[$result['inquiry_id']] . ' - ' . $result['inquiry_id'] . PHP_EOL . implode(PHP_EOL, $html);
				} else {
					$result['answer_html'] = implode(PHP_EOL, $html);
				}
			}
			unset($result);

			$sns_type_cd_list = array('' => "SNS") + $this->_codes['16'];

			// access limit -> inquiryFilters
			if ($this->_user->role_cd == '74') {
				$user_access_limit_entry = $this->_model->get_user_access_limit($this->_bot_id, $this->_user_id, $inquiry_id, 'inquiry');
				if (isset($user_access_limit_entry[$post['lang_cd']])) {
					$user_access_limit_entry = $user_access_limit_entry[$post['lang_cd']];
				}
				else {
					$user_access_limit_entry = $user_access_limit_entry['ja'];
				}
				$result_tag = [];
				foreach($user_access_limit_entry as $k=>$v) {
					//unset($inquiry_entry_dict[$k]);
					$k = strval($k);
					if (is_array($v)) {
						foreach($v as $vv) {
							$result_tag[] = [['entry_no'=>$k, 'value'=>$vv, 'condition'=>'equal']];
						}
					}
					else {
						$result_tag[] = [['entry_no'=>$k, 'value'=>$v, 'condition'=>'equal']];
					}
				}
				$inquiryFilters = $result_tag;
			}

			// 絞り込み　detail部分
			foreach ($inquiry_entry_result as $en) {
				if (!array_key_exists($en['result_id'], $inquiry_entry_result_group_by_id)) {
					$inquiry_entry_result_group_by_id[$en['result_id']] = [];
				}
				$inquiry_entry_result_group_by_id[$en['result_id']][] = $en;
			}
			$filted_result_id_arr = [];
			$filter_inquiry_result = [];
			$version_flag = false;
			if (count($all_version) > 1) {
				if (!$show_allversion) $version_flag = true;
			} else {
				$version_flag = true;
			}
			if ($version_flag && count($inquiryFilters) > 0) {
				$filter_inquiry_entry_result = [];
				foreach ($inquiry_entry_result_group_by_id as $result_id => $entry_results) {
					$lang_inquiry_branch_entries = $inquiry_branch_entries[$temp_lang_cd];
					if ($this->_model->filter_results($inquiryFilters, $entry_results, $lang_inquiry_branch_entries)) {
						$filted_result_id_arr[] = $result_id;
					}
				}
				foreach ($inquiry_result as $re) {
					if (in_array($re['id'], $filted_result_id_arr)) {
						$filter_inquiry_result[] = $re;
					}
				}
			} else {
				$filter_inquiry_result = $inquiry_result;
			}
			
			$post['paging']['filter_count'] = count($filter_inquiry_result);

			// detail画面の関数
			$view->total = $total;
			$view->inquiry_result_support_dict = $inquiry_result_support;
			$view->inquiry_result = $filter_inquiry_result;
			$view->sns_type_cd_list = $sns_type_cd_list;
		} else if ($post['type'] == 'detail' && $result_id != NULL) {
			$total = 0;
			$result = ORM::factory('inquiryresult', $result_id);
			$id = $result->inquiry_id;
			$entry_arr = ORM::factory('inquiryentry')->where('inquiry_id', '=', $id)->order_by('no')->find_all();
			$_inquiry_entry[$id] = [];
			$inquiry_not_show_frs_no_arr[$id] = [];
			$entry_type_arr[$id] = [];
			$inquiry_entry_dict = [];
			foreach($entry_arr as $e) {
				$entry_type_arr[$id][$e->no] = $e->entry_type_cd;
				$_inquiry_entry[$id][$e->lang_cd][$e->no] = strip_tags($e->title);
				$e->entry_data = json_decode($e->entry_data, true);
				$inquiry_entry_dict[$id][$e->lang_cd][$e->no] = $e;
				$inquiry_not_show_frs_no_arr[$id][$e->lang_cd] = [];
				if ($e->entry_type_cd == 'frs') {
					if ($_inquiry_entry[$id][$e->lang_cd][$e->no] == preg_replace('/\/\?.*\?\//', '', $_inquiry_entry[$id][$e->lang_cd][$e->no])) {
						$_inquiry_entry[$id][$e->lang_cd][$e->no] = '';
						$inquiry_not_show_frs_no_arr[$id][$e->lang_cd][] = $e->no;
					}
					else {
						$_inquiry_entry[$id][$e->lang_cd][$e->no] = str_replace('¥', '', preg_replace('/\/\?.*\?\//', '', $_inquiry_entry[$id][$e->lang_cd][$e->no]));
					}
				}
			}
			$sections = ORM::factory('inquirysection')->where('inquiry_id', '=', $id)->where('delete_flg', '=', 0)->order_by('lang_cd')->order_by('sort_no')->find_all();
			$section = [];
			$section_lang_cd = [];
			$inquiry_section[$id] = [];
			foreach($sections as $s) {
				if ($s->lang_cd != $section_lang_cd && count($section) > 0) {
					$inquiry_section[$id][$section_lang_cd] = $section;
					$section = [];
				}
				$section = array_merge($section, explode(',', $s->entries));
				$section_lang_cd = $s->lang_cd;
			}
			$inquiry_section[$id][$section_lang_cd] = $section;
			$_inquiry_entry_result = [];
			$inquiry_entry_result_arr = $this->_model->get_inquiry_entry_result_one($result_id, $post['lang_cd']);
			foreach($inquiry_entry_result_arr as $r) {
				if (!array_key_exists($r['result_id'], $_inquiry_entry_result)) {
					$_inquiry_entry_result[$r['result_id']] = [];
				}
				$_inquiry_entry_result[$r['result_id']][$r['no']] = $r;
			}
			$inquiry_result = $this->_model->get_inquiry_result_one($this->_bot_id, $result_id, $post['lang_cd']);
			foreach($inquiry_result as &$result) {
				$tax_label = '';
				$payment_amount = 0;
				$result_data = json_decode($result['result_data'], true);
				if (is_array($result_data)) {
					if (array_key_exists('amount', $result_data)) {
						$payment_amount = $result_data['amount'];
					}
					else if (array_key_exists('payment', $result_data) && array_key_exists('amount', $result_data['payment'])) {
						$payment_amount = $result_data['payment']['amount'];
					}
				}
				if (!isset($_inquiry_entry_result[$result['id']])) continue;
				$entry_result = $_inquiry_entry_result[$result['id']];
				$i = 1;
				$html = [];
				$result['answer_html'] = implode(PHP_EOL, $html);
				$entry_total = '';
				$entry_total_price = NULL;
				if (!isset($inquiry_section[$result['inquiry_id']][$result['lang_cd']])) {
					continue;
				}
				foreach ($inquiry_section[$result['inquiry_id']][$result['lang_cd']] as $no) {
					if (in_array($no, $inquiry_not_show_frs_no_arr[$result['inquiry_id']][$result['lang_cd']])) {
						continue;
					}
					if (isset($entry_result[$no])) {
						if ($entry_type_arr[$result['inquiry_id']][$no] == 'frs') {
							$format_answer = str_replace('.', ',', $entry_result[$no]['entry_data']);
						}
						else if ($entry_type_arr[$result['inquiry_id']][$no] == 'fup') {
							$format_answer = '';
							if ($entry_result[$no]['entry_data'] != '') {
								$files = explode(',', $entry_result[$no]['entry_data']);
								$ptr = 1;
								foreach($files as $file) {
									$ext = end(explode('.', $file));
									$format_answer .= '<a style="margin-right:10px;" href="' . $file . '" target="_blank">' . $ext . $ptr++ .'</a>';
								}
							}
						}
						else {
							$format_answer = $entry_result[$no]['entry_data'];
							if ($mask_privacy) {
								$input_rules = json_decode($inquiry_entries[strval($no)]['input_rules'], true);
								if (is_array($input_rules) && array_key_exists('type', $input_rules)) {
									if ($input_rules['type'] == 'tel' || $input_rules['type'] == 'mail' || $input_rules['type'] == 'postcode' || $input_rules['type'] == 'name' || $input_rules['type'] == 'name_full' || $input_rules['type'] == 'name_separate' || $input_rules['type'] == 'address') {
										$format_answer = $this->_model->mask_string($entry_result[$no]['entry_data'], 5, '*');
									}
								}
							}
						}
						if ($entry_result[$no]['entry_extra_info'] != '') {
							$format_answer = str_replace(['"','[', ']'], '', $format_answer);
							$entry_extra_info = json_decode($entry_result[$no]['entry_extra_info'], true);
							if (is_array($entry_extra_info)) {
								if (array_key_exists('detail', $entry_extra_info)) {
									$detail = trim(strip_tags(str_replace('<br>', PHP_EOL, $entry_extra_info['detail'])));
									$pos2 = strpos($detail, '小計:');
									if ($pos2 > 0) {
										$detail = substr($detail, 0, $pos2 - 1); // -1 delete last PHP_EOL
									}
									else {
										$detail = '';
									}
									$detail = trim($detail);
									if ($detail != '' && strpos($format_answer, $detail) === false) $format_answer = $format_answer . PHP_EOL . trim($detail);
								}
								if (array_key_exists('coupon', $entry_extra_info) && array_key_exists('discount_result', $entry_extra_info['coupon'])) {
									$format_answer = $format_answer . PHP_EOL . '-￥' . number_format($entry_extra_info['coupon']['discount_result']);
								}
								if (array_key_exists('total', $entry_extra_info)) {
									$entry_total = $entry_extra_info['total'];
								}
							}
						} else {
							$entry_result_data = json_decode($entry_result[$no]['entry_result'], true);
							// 新版inquiry向けの処理
							// 内訳はentry_extra_infoに保存ではなく、entry_resultに保存しています。
							if ($entry_result_data != NULL) {
								if (isset($entry_result_data['items']) && is_array($entry_result_data['items'])) {
									if (is_null($entry_total_price)) { 
										$entry_total_price = 0;
									}
									$items = $entry_result_data['items'];
									$tax_sum = '';
									$tax_array = ['' => 0, 'tax-none' => 1, 'tax' => 2, 'tax-service' => 3];
									foreach ($items as $item) {
										$entry_option = $inquiry_entry_dict[$result['inquiry_id']][$result['lang_cd']][$no]->entry_data[$item['value'] - 1];
										if (isset($entry_option['price_type'])) {
											$tax = $entry_option['price_type'];
											if ($tax_array[$tax] > $tax_array[$tax_sum]) {
												$tax_sum = $entry_option['price_type'];
											}
										}
										if (isset($item['price'])) {
											$price = intval($item['price']);
											$discount = 0;
											$spans = NULL;
											$item_total = 0;
											if (isset($item['discount_price'])) {
												$discount = intval($item['discount_price']);
											}
											if (isset($item['spans'])) {
												$spans = intval($item['spans']);
											}
											$item_total = ($price - $discount) * intval($item['num']);
											if ($spans != NULL) {
												$item_total = ($price - $discount) * intval($item['num']) * $spans;
											}
											if (isset($item['discount'])) {
												$item_total = $item_total - intval($item['discount']);
											}
											$entry_total_price += $item_total;
										}
									}
									$tax_label = ($tax_sum === '') ? '' : $maximum_amount_label['LABEL_' . strtoupper(str_replace('-', '_', $tax_sum))];
								}
								else if (isset($entry_result_data['price'])) {
									$num = isset($entry_result_data['num'])?intval($entry_result_data['num']):1;
									$entry_total_price += intval($entry_result_data['price']) * $num;
									if (isset($entry_result_data['discount'])) {
										$entry_total_price = $entry_total_price - intval($entry_result_data['discount']);
									}
								}
							}
						}
						$html[] = $i . '.' . $_inquiry_entry[$result['inquiry_id']][$result['lang_cd']][$no] . PHP_EOL . '<span style="color:royalblue;">' . $format_answer . '</span>';
						$i++;
					}
				}
				if ($payment_amount > 0) {
					$html[] = PHP_EOL . '<span style="color:royalblue;">' . $maximum_amount_label['LABEL_SUM_TOTAL'] . ':' . str_replace('{total}', number_format($payment_amount), str_replace('{tax}', $tax_label, $maximum_amount_label['LABEL_SUM_TOTAL_UNIT'])) . '</span>';

					if ($result['status_cd'] == '01' || $result['status_cd'] == '02') {
						$total += intval($payment_amount);
					}
				}
				else if ($entry_total != '') {
					$html[] = PHP_EOL . '<span style="color:royalblue;">' . $entry_total . '</span>';
				} 
				else if ($entry_total_price != NULL) {
					// 新版inquiry向けの処理
					// 内訳はentry_extra_infoに保存ではなく、entry_resultに保存しています。
					if ($result['status_cd'] == '01' || $result['status_cd'] == '02') {
						$total += intval($entry_total_price);
					}
					$html[] = PHP_EOL . '<span style="color:royalblue;">' . $maximum_amount_label['LABEL_SUM_TOTAL'] . ':' . str_replace('{total}', number_format($entry_total_price), str_replace('{tax}', $tax_label, $maximum_amount_label['LABEL_SUM_TOTAL_UNIT'])) . '</span>';

				}
				$result['answer_html'] = implode(PHP_EOL, $html);
			}
			$inquiry_result_support = [];
			$inquiry_result_support_arr = $this->_model->get_inquiry_result_support_one($result_id, $post['lang_cd']);
			foreach($inquiry_result_support_arr as $r) {
				if (!array_key_exists($r['result_id'], $inquiry_result_support)) {
					$inquiry_result_support[$r['result_id']] = [];
				}
				$inquiry_result_support[$r['result_id']][] = $r;
			}
			if (isset($inquiry_data['is_mail_logged']) && $inquiry_data['is_mail_logged'] == '1') {
				$result_ids = [$result_id];
				Session::instance()->set('inquiry_result_show_ids', $result_ids);
				$inquiry_result_mails = $this->_model->call_admin_api('inquiry', 'inquiryresultmails', 'post', ['result_ids'=>$result_ids]);
				foreach($inquiry_result_mails as $rid => $mails) {
					$temp_support = array_merge($inquiry_result_support[$rid] ?? [], $mails, []);
					usort($temp_support, function($a, $b) {
						return strtotime($b['upd_time']) - strtotime($a['upd_time']);
					});
					$inquiry_result_support[$rid] = $temp_support;
				}
			}
			$view->total = 0;
			$view->inquiry_result_support_dict = $inquiry_result_support;
			$view->inquiry_result = $inquiry_result;
			$near = $this->_model->get_inquiry_result_nearby($inquiry_id, $result_id, 'next');
			if (count($near) > 0) $view->next_result_id = $near[0]['id'];
			$near = $this->_model->get_inquiry_result_nearby($inquiry_id, $result_id, 'prev');
			if (count($near) > 0) $view->prev_result_id = $near[0]['id'];
		} else {
			$post['show_allversion'] = false;
			// 統計画面は他のバージョンのデータがいらない
			if ($current_version != NULL) {
				$inquiry_entry_result = $this->_model->get_inquiry_entry_result($this->_bot_id, $current_version, $post['start_date'], $post['end_date'], intval($post['is_blocked']), $post['lang_cd'], $post['status_cd']);
			} else {
				$inquiry_entry_result = $this->_model->get_inquiry_entry_result($this->_bot_id, $inquiry_id, $post['start_date'], $post['end_date'], intval($post['is_blocked']), $post['lang_cd'], $post['status_cd']);
			}
			//　絞り込み total部分
			foreach ($inquiry_entry_result as $en) {
				if (!array_key_exists($en['result_id'], $inquiry_entry_result_group_by_id)) {
					$inquiry_entry_result_group_by_id[$en['result_id']] = [];
				}
				$inquiry_entry_result_group_by_id[$en['result_id']][] = $en;
			}
			$filted_result_id_arr = [];
			$filter_inquiry_result = [];
			if (count($inquiryFilters) > 0) {
				$filter_inquiry_entry_result = [];
				foreach ($inquiry_entry_result_group_by_id as $result_id => $entry_results) {
					$lang_inquiry_branch_entries = $inquiry_branch_entries[$temp_lang_cd];
					if ($this->_model->filter_results($inquiryFilters, $entry_results, $lang_inquiry_branch_entries)) {
						$filted_result_id_arr[] = $result_id;
					}
				}
				foreach ($inquiry_entry_result as $en) {
					if (in_array($en['result_id'], $filted_result_id_arr)) {
						$filter_inquiry_entry_result[] = $en;
					}
				}
				$inquiry_entry_result = $filter_inquiry_entry_result;
			}

			// get inquiry form results in format
			$inquiry_result_entry_dict = [];

			foreach($inquiry_entry_result as $re) {
				if (!array_key_exists(strval($re['no']), $inquiry_entries)) continue;
				
				if ($this->_is_choice($inquiry_entries[strval($re['no'])]['entry_type_cd'])) {
					$choice_arr = json_decode($re['entry_result'], true);
					if (is_array($choice_arr)) {
						foreach($choice_arr['items'] as $r) {
							if (array_key_exists($r['value'], $inquiry_entries[$re['no']]['entry_count']))
							$inquiry_entries[strval($re['no'])]['entry_count'][$r['value']]['count']++;
						}
					}
					else {
						$res = explode(',', $re['entry_result']);
						foreach($res as $r) {
							if (array_key_exists($r, $inquiry_entries[$re['no']]['entry_count']))
							$inquiry_entries[strval($re['no'])]['entry_count'][$r]['count']++;
						}
					}
				}
				else {
					if ($re['link_id'] == null) {
						$member_id = $re['member_id'];
					}
					else {
						$member_id = $re['link_id'];
					}
					if ($inquiry->user_in_charge != NULL) {
						if ($mask_privacy) {
							$input_rules = json_decode($inquiry_entries[strval($re['no'])]['input_rules'], true);
							if (is_array($input_rules) && array_key_exists('type', $input_rules)) {
								if ($input_rules['type'] == 'tel' || $input_rules['type'] == 'mail' || $input_rules['type'] == 'postcode' || 
								$input_rules['type'] == 'name' || $input_rules['type'] == 'name_full' || $input_rules['type'] == 'name_separate' || $input_rules['type'] == 'address') {
									$re['entry_result'] = $this->_model->mask_string($re['entry_result'], 5, '*');
									$re['entry_data'] = $this->_model->mask_string($re['entry_data'], 5, '*');
								}
							}
						}
					}
					if ($inquiry_entries[strval($re['no'])]['entry_type_cd'] == 'spl') {
						$answers = ['uid'=>$member_id, 'answer'=>$re['entry_data']];
					}
					else {
						$answers = ['uid'=>$member_id, 'answer'=>$re['entry_result']];
					}
					$inquiry_entries[$re['no']]['entry_count'][] = $answers;
				}
				
				if (!array_key_exists($re['result_id'], $inquiry_result_entry_dict)) {
					$inquiry_result_entry_dict[$re['result_id']] = [];
				}
				$inquiry_result_entry_dict[$re['result_id']][] = $re;
			}

			// get sections
			if ($current_version != NULL) {
				$sections = ORM::factory('inquirysection')->where('inquiry_id', '=', $current_version)->where('lang_cd', '=', $temp_lang_cd)->where('delete_flg', '=', 0)->order_by('sort_no')->find_all();
			} else {
				$sections = ORM::factory('inquirysection')->where('inquiry_id', '=', $inquiry_id)->where('lang_cd', '=', $temp_lang_cd)->where('delete_flg', '=', 0)->order_by('sort_no')->find_all();
			}
			$section = [];
			foreach($sections as $s) {
				$section = array_merge($section, explode(',', $s->entries));
			}
			// total画面の関数
			$view->section = $section;
			$view->inquiry_result_total = $inquiry_entries;
			$view->inquiry_result_entry_dict = $inquiry_result_entry_dict;
		}
		// 保存した絞り込み条件を取得する
		$inquiry_result_condtions = $this->_model->get_inquiry_result_condition($inquiry_id);

		// Viewの共通関数　（part ２）
		$view->post = $post;
		$view->versions_count = count($all_version);
		$view->all_version_options = $all_version_options;
		$view->inquiry_branch_entries = $inquiry_branch_entries;
		$view->inquiryFilters = count($inquiryFilters) > 0 ? json_encode($inquiryFilters, JSON_UNESCAPED_UNICODE) : '';
		$view->inquiry_entry = $inquiry_entry;
		$view->all_language_same = $all_language_same;
		$view->inquiry_result_condtions = $inquiry_result_condtions;
		$view->inquiry_data = $inquiry_data;
		$this->template->content = $view;
		$url = ($inquiry_div == NULL || $inquiry_div == 9) ? '/admininquiry/inquirys' : '/adminorder/inquirys';
		$navi_next = ($inquiry_div == NULL || $inquiry_div == 9) ? $this->navi_maps_inquiry['inquirys'] : $this->navi_maps_order['inquirys'];
		$navi_current = ($inquiry_div == NULL || $inquiry_div == 9) ? $this->navi_maps_inquiry['inquiryresult'] : $this->navi_maps_order['inquiryresult'];
		$this->_page_navi('[{"f":"'. $navi_next .'","url":"' . $url . '"}, {"f":"'. $navi_current .'"}]');
	}
	
	public function action_inquiryresultrow() {
		$this->auto_render = FALSE;
		$post = $this->request->post();
		$inquiry_id = $post['inquiry_id'];
		$inquiry = ORM::factory('inquiry', $inquiry_id);
		$inquiry_data = json_decode($inquiry->inquiry_data, true);
		$params = [
			'bot_id' => $this->_bot_id,
			'user_id' => $this->_user->user_id,
			'inquiry_id' => $post['inquiry_id'],
			'start_time' => $post['last_time'],
			'lang_cd' => $post['lang_cd'],
			'with_detail' => 1,
		];
		if ($post['support_type_cd'] == '') {
			$support_type_list = null;
		}
		else {
			$support_type_list = explode(',', $post['support_type_cd']);
		}
		if ($post['status_cd'] == '') {
			$status_list = null;
		}
		else {
			$status_list = explode(',', $post['status_cd']);
		}
		$inquiry_status_list = $this->_model->get_bot_tpl_message($this->_bot_id, 'inquiry.support_status_' . $inquiry_id, $this->_lang_cd, true);
		if (count($inquiry_status_list) == 0) {
			$inquiry_status_list = $this->_model->get_bot_tpl_message($this->_bot_id, 'inquiry.support_status', $this->_lang_cd, true);
		}
		$inquiry_status_list_fixed = $this->_model->get_bot_tpl_message($this->_bot_id, 'inquiry.support_status_fixed', $this->_lang_cd, true);
		$buttons = $inquiry_status_list + $inquiry_status_list_fixed;
		$labelcolor = ['01'=>'#d84a38', '02'=>'#FFB848', '03'=>'#1BBC9B', '04'=>'#95A5A6', '05'=>'#CC00B2', '06'=>'#798a57'];
		$params['start_time'] = Session::instance()->get('inquiry_result_refresh_time', date('Y-m-d H:i:s'));
		$inquiry_result = $this->_model->call_admin_api('inquiry', 'inquiryresults', 'POST', $params);
		Session::instance()->set('inquiry_result_refresh_time', date('Y-m-d H:i:s'));
		$text = '';
		$inquiry_result_mails = [];
		$has_new_mail_received = false;
		if (isset($inquiry_data['is_mail_logged']) && $inquiry_data['is_mail_logged'] == '1') {
			$inquiry_result_show_ids = Session::instance()->get('inquiry_result_show_ids', NULL);
			foreach ($inquiry_result as $result) {
				if ($inquiry_result_show_ids !== NULL && !in_array($result['id'], $inquiry_result_show_ids)) {
					$inquiry_result_show_ids[] = $result['id'];
				}
			}
			Session::instance()->set('inquiry_result_show_ids', $inquiry_result_show_ids);
			if ($inquiry_result_show_ids != NULL && count($inquiry_result_show_ids) > 0) {
				$inquiry_result_mails = $this->_model->call_admin_api('inquiry', 'inquiryresultmails', 'post', ['result_ids'=>$inquiry_result_show_ids, 'start_date' => $params['start_time']]);
				foreach ($inquiry_result_mails as $mails) {
					foreach ($mails as $mail) {
						if ($mail['support_type_cd'] == 'received_mail') {
							$has_new_mail_received = true;
							break;
						}
					}
					if ($has_new_mail_received) break;
				}
			}
		}
		foreach ($inquiry_result as $result) {
			// if ($result['status_cd'] == '00') continue;
			if ($result['status_cd'] == '03' && $result['upd_user'] === '0') continue;
			if ($status_list) {
				if (!in_array($result['status_cd'], $status_list)) continue;
			}
			$support_type_cd = '';
			if (isset($result['support'])) {
				foreach($result['support'] as $support) {
					if ($support['support_type_cd'] != '') $support_type_cd = $support['support_type_cd'];
				}
			}
			if ($support_type_list) {
				if (!in_array($support_type_cd, $support_type_list)) continue;
			}
			$result_data = is_array($result['result_data']) ? $result['result_data'] : json_decode($result['result_data'], true);
			if (isset($result_data['error_code'])) continue;
			$row = View::factory('admin/inquiry/inquiryresultrow');
			$row->result = $result;
			$row->inquiry = $inquiry;
			$row->post = $post;
			if (isset($result['support'])) {
				$row->inquiry_result_support = $result['support'];
			}
			else {
				$row->inquiry_result_support = [];
			}
			if (isset($inquiry_result_mails[$result['id']])) {
				$temp_support = array_merge($row->inquiry_result_support, $inquiry_result_mails[$result['id']], []);
				usort($temp_support, function($a, $b) {
					return strtotime($b['upd_time']) - strtotime($a['upd_time']);
				});
				$row->inquiry_result_support = $temp_support;
				unset($inquiry_result_mails[$result['id']]);
			}
			$row->buttons = $buttons;
			$row->labelcolor = $labelcolor;
			$row->inquiry_data = $inquiry_data;
			$row->result_base_id = [];
			$text .=  $row;
		}
		$has_new_result = $text != '';
		$inquiry_result_mails_htmls = [];
		$inquiry_model = new Model_Inquirymodel();
		foreach ($inquiry_result_mails as $result_id => $mails) {
			foreach($mails as $mail) {
				$inquiry_result_mails_htmls[$result_id][$mail['id']] = $inquiry_model->create_supportmemoview([$mail], $buttons);
			}
		}
		$response = [
			'newRow' => $text,
			'newResultMails' => $inquiry_result_mails_htmls,
			'has_new_result' => $has_new_result,
			'has_new_mail_received' => $has_new_mail_received,
		];
		$this->response->body(json_encode($response, JSON_UNESCAPED_UNICODE));
	}

	public function action_inquiryresultedit()
	{
		$post = [];
		if ($this->request->post()){
			$post = $this->request->post();
			$result_id = $post["result_id"];
			$inquiry_result = ORM::factory('inquiryresult', $result_id);
			if ($this->_page_action == 'modify') {
				$entry_results = json_decode($post['order_info'], true);
				$order_kv = [];
				foreach($entry_results as $r) {
					$v = $r['data'];
					if (count($v) == 1) {
						$v = array_shift($v);
						$d = $v;
					}
					else {
						if (isset($v['last_name_kana'])) {
							$d = $v['last_name'] . ' ' . $v['first_name'] . PHP_EOL . $v['last_name_kana'] . ' ' . $v['first_name_kana'];
						}
						else if (isset($v['furigana'])) {
							$d = $v['full_name'] . PHP_EOL . $v['furigana'];
						}
						else {
							$d = implode(' ', $v);
						}
						$v = json_encode($v, JSON_UNESCAPED_UNICODE);
					}
					DB::update('t_inquiry_result_entry')->set(['entry_result'=>$v, 'entry_data'=>$d])->where('result_id', '=', $result_id)->where('no', '=', $r['no'])->execute();
					$order_kv[$r['no']] = $d;
				}
				$orders = ORM::factory('maximumorder')->where('link_id', '=', $result_id)->find_all();
				foreach($orders as $order) {
					$order_info = json_decode($order->order_info, true);
					foreach($order_info as &$v) {
						if (isset($order_kv[$v['no']])) $v['value'] = $order_kv[$v['no']];
					}
					$orm = ORM::factory('maximumorder', $order->order_id);
					$orm->order_info = json_encode($order_info, JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES);
					$orm->save();
				}
				$orm = ORM::factory('inquiryresultsupport');
				$orm->result_id = $result_id;
				$orm->no = $this->_model->get_inquiry_result_support_no($result_id);
				$orm->support_type_cd = '';
				$orm->memo = '申込変更';
				$orm->upd_user = $this->_user_id;
				$orm->save();
			}
			else if ($this->_page_action == 'back') {
			}
			$this->redirect('/admininquiry/inquiryresult?type=detail&id=' . $inquiry_result->inquiry_id);
		}

		$result_id = $this->request->query('id', NULL);
		$inquiry_result = ORM::factory('inquiryresult', $result_id);
		$inquiry_orm = ORM::factory('inquiry', $inquiry_result->inquiry_id);
		$inquiry_div = $this->_inquiry_order_start ? $inquiry_orm->item_div : NULL;
		$inquiry_result = ORM::factory('inquiryresult', $result_id);
		$permission = $this->_item_permission_check($inquiry_result->inquiry_id);
		if ($permission < 0) {
			$this->redirect('/admininquiry/inquirys');
		}
		
		$inquiry_result_entry = ORM::factory('inquiryresultentry')->where('result_id', '=', $result_id)->find_all();
		$inquiry_result_kv = [];
		foreach($inquiry_result_entry as $r) {
			$inquiry_result_kv[$r->no] = $r;
		}
		$inquiry_entry = ORM::factory('inquiryentry')->where('inquiry_id', '=', $inquiry_result->inquiry_id)->where('lang_cd', '=', $inquiry_result->lang_cd)->order_by('no')->find_all();
		$view = View::factory ($this->_view_path . 'inquiryresultedit');
		$menu = View::factory($this->_view_path . 'inquirymenu');
		$menu->inquiry_id = $inquiry_result->inquiry_id;
		$view->menu = $menu;
		$view->result_id = $result_id;
		$view->inquiry_entry = $inquiry_entry;
		$view->inquiry_result_kv = $inquiry_result_kv;
		$view->lang_cd = $inquiry_result->lang_cd;
		$view->codes = $this->_codes;
		$this->template->content = $view;
		$url = ($inquiry_div == NULL || $inquiry_div == 9) ? '/admininquiry/inquirys' : '/adminorder/inquirys';
		$navi_next = ($inquiry_div == NULL || $inquiry_div == 9) ? $this->navi_maps_inquiry['inquirys'] : $this->navi_maps_order['inquirys'];
		$navi_current = ($inquiry_div == NULL || $inquiry_div == 9) ? $this->navi_maps_inquiry['inquiryresult'] : $this->navi_maps_order['inquiryresult'];
		$this->_page_navi('[{"f":"'. $navi_next .'","url":"' . $url . '"}, {"f":"'. $navi_current .'"}]');
	}

	public function action_inquiryresultdetail() {
		$result_id = $this->request->query('id', NULL);
		$inquiry_result = ORM::factory('inquiryresult', $result_id);
		$inquiry_id = $inquiry_result->inquiry_id;
		$permission = $this->_item_permission_check($inquiry_id);
		$inquiry = ORM::factory('inquiry', $inquiry_id);
		if ($this->_inquiry_order_start && $inquiry->item_div) {
			$match_action = $inquiry->item_div == 9 ? 'admininquiry' : 'adminorder';
			if ($this->_path != $match_action) {
				$this->redirect('/' . $match_action . '/inquiryresultdetail?id=' . $inquiry_id);
			}
		}
		$inquiry_div = $this->_inquiry_order_start ? $inquiry->item_div : NULL;
		if ($permission < 0) {
			$url = ($inquiry_div == NULL || $inquiry_div == 9) ? '/admininquiry/inquirys' : '/adminorder/inquirys';
			$this->redirect($url);
		}
		$this->redirect('/admininquiry/inquiryresult?id=' . $inquiry_id . '&type=detail&result_id=' . $result_id);
	}

	public function action_inquiryrefer()
	{

		$inquiry_div = NULL;

		$post = [];
		if ($this->request->post()) {
			$post = $this->request->post();
			$inquiry_id = $post['inquiry_id'];
			Session::instance()->set('inquiryrefer_start_date', $post['start_date']);
			Session::instance()->set('inquiryrefer_end_date', $post['end_date']);
			Session::instance()->set('inquiryresult_lang_cd', $post['lang_cd']);
		}
		else {
			$inquiry_id = $this->request->query('id', NULL);
			$permission = $this->_item_permission_check($inquiry_id);

			$item = ORM::factory('inquiry', $inquiry_id);
			if ($this->_inquiry_order_start && $item->item_div) {
				$match_action = $item->item_div == 9 ? 'admininquiry' : 'adminorder';
				if ($this->_path != $match_action) {
					$this->redirect('/' . $match_action . '/inquiryrefer?id=' . $inquiry_id);
				}
			}
			$inquiry_div = $this->_inquiry_order_start ? $item->item_div : NULL;

			if ($permission < 0) {
				$url = ($inquiry_div == NULL || $inquiry_div == 9) ? '/admininquiry/inquirys' : '/adminorder/inquirys';
				$this->redirect($url);
			}
			$post['lang_cd'] = Session::instance()->get('inquiryresult_lang_cd', NULL);
			if ($post['lang_cd'] == NULL) {
				$post['lang_cd'] = '';
			}
			$post['start_date'] = Session::instance()->get('inquiryrefer_start_date', NULL);
			if ($post['start_date'] == NULL) {
				$post['start_date'] = '';
			}
			$post['end_date'] = Session::instance()->get('inquiryrefer_end_date', NULL);
			if ($post['end_date'] == NULL) {
				$post['end_date'] = '';
			}
		}

		$refer_data = $this->_model->get_inquiry_refer($this->_bot_id, $inquiry_id, $post['start_date'], $post['end_date'], $post['lang_cd']);
		$refers = [];
		$domain_refers = [];
		foreach($refer_data as $rd) {
			if ($rd['action'] != 'input' && $rd['action'] != 'complete') continue;
			$key = $rd['refer'];
			if (strpos($key, 'https://inquiry.talkappi.com/inquiry/input') === 0) $key = 'https://inquiry.talkappi.com/inquiry/input';
			if (strpos($key, 'https://inquiry.talkappi.com/inquiry/complete') === 0) $key = 'https://inquiry.talkappi.com/inquiry/complete';
			//再度の/をとって、URLを纏める
			if (substr($key, -1) === '/') $key = substr($key, 0, -1);
			if ($key == NULL) {
				$key = '';
				$domain = '';
			}
			else {
				if (strpos($key, 'https://') === 0 || strpos($key, 'http://') === 0 ) {
					$pos = strpos($key, '/', 8);
					if ($pos > 0) {
						$domain = substr($key, 0, $pos);
					}
					else {
						$domain = $key;
					}
				} 
				else {
					$domain = $key;
				}
			}
			if (!array_key_exists($key, $refers)) {
				$refers[$key] = ['input'=>0, 'complete'=>0];
			}
			$refers[$key][$rd['action']] = $refers[$key][$rd['action']] + $rd['s'];

			if (!array_key_exists($domain, $domain_refers)) {
				$domain_refers[$domain] = ['input'=>0, 'complete'=>0];
			}
			$domain_refers[$domain][$rd['action']] = $domain_refers[$domain][$rd['action']] + $rd['s'];
		}
		$refer_data = $this->_model->get_inquiry_refer_city($this->_bot_id, $inquiry_id, $post['start_date'], $post['end_date'], $post['lang_cd']);
		$citys = [];
		foreach($refer_data as $rd) {
			if ($rd['action'] != 'input' && $rd['action'] != 'complete') continue;
			$key = $rd['city'];
			if ($key == 'null') $key = '';
			$key = str_replace('"', '', $key);
			if (!array_key_exists($key, $citys)) {
				$citys[$key] = ['input'=>0, 'complete'=>0];
			}
			$citys[$key][$rd['action']] = $citys[$key][$rd['action']] + $rd['s'];
		}
		// カスタマイズコンテンツ管理に切り替える際、/inquiryの方も修正してください
		$refer_labels = [
			'mail' => __('admin.inquiry.label.url.mail'),
			'hp'   => __('admin.inquiry.label.url.hp'),
			'line' => 'LINE',
		];
		$updated_refers = [];
		foreach ($refers as $k => $v) {
			$display_key = $refer_labels[$k] ?? $k;
			$updated_refers[$display_key] = $v;
		}
		$updated_domain_refers = [];
		foreach ($domain_refers as $k => $v) {
			$display_key = $refer_labels[$k] ?? $k;
			$updated_domain_refers[$display_key] = $v;
		}

		$view = View::factory($this->_view_path . 'inquiryrefer');
		$menu = View::factory($this->_view_path . 'inquirymenu');
		$menu->inquiry_id = $inquiry_id;
		$menu->inquiry_div = $inquiry_div;
		$view->menu = $menu;
		$view->lang_cd_list = array(''=>__('admin.inquiryresult.label.language_default')) + $this->_bot_lang;;
		$view->post = $post;
		$view->refers = $updated_refers;
		$view->domain_refers = $updated_domain_refers;
		$view->citys = $citys;
		$view->inquiry_id = $inquiry_id;
		$this->template->content = $view;
		$url = ($inquiry_div == NULL || $inquiry_div == 9) ? '/admininquiry/inquirys' : '/adminorder/inquirys';
		$navi_next = ($inquiry_div == NULL || $inquiry_div == 9) ? $this->navi_maps_inquiry['inquirys'] : $this->navi_maps_order['inquirys'];
		$navi_current = ($inquiry_div == NULL || $inquiry_div == 9) ? $this->navi_maps_inquiry['inquiryrefer'] : $this->navi_maps_order['inquiryrefer'];
		$this->_page_navi('[{"f":"'. $navi_next .'","url":"' . $url . '"}, {"f":"'. $navi_current .'"}]');
	}
	
	//action calendars
	public function action_calendars() {
		if ($this->_inquiry_order_start && $this->_service_useflg['use_order_flg'] !== true) {
			$this->redirect('/admin/top');
		}
		if ($this->_inquiry_order_start && $this->_path == 'admininquiry') {
			$this->redirect('/adminorder/calendars');
		}

		$bot_id = $this->_bot_id;

		$calendars = DB::select('id', 'title', 't_inquiry_calendar.upd_time', ['name','upd_user_name'])->from('t_inquiry_calendar')
			->join('t_user', 'LEFT')->on('t_inquiry_calendar.upd_user', '=', 't_user.user_id')
			->where('t_inquiry_calendar.bot_id', '=', $bot_id)
			->execute()
			->as_array()
			;

		// get all maximum options: inquiry_id => maximum_options
		$inquiries_and_maximums = $this->_model->get_inquiries_with_maximums($bot_id);
		$inquiry_maximum_mapping = $inquiries_and_maximums['mapping'];
		$all_inquiry_maximums = $inquiries_and_maximums['maximums'];
		$inquiry_array = $inquiries_and_maximums['inquiries'];

		$view = View::factory($this->_view_path . 'calendars');
		$view->_lang_cd = $this->_lang_cd;
		$view->bot_lang = $this->_model->get_bot_support_lang(ORM::factory('bot', $bot_id));

		// TODO get public_url_prefix from config (env settings)
		$view->public_url_prefix = "https://apps.talkappi.com/calendar?id=";
		$view->calendars = $calendars;
		$view->inquiry_options = json_encode($inquiry_array, JSON_UNESCAPED_UNICODE);
		$view->inquiry_maximum_mapping = json_encode($inquiry_maximum_mapping, JSON_UNESCAPED_UNICODE);
		$view->all_maximum_options = json_encode($all_inquiry_maximums, JSON_UNESCAPED_UNICODE);
		$this->template->content = $view;
		// page navi -> 問合せ・予約カレンダー if needed
		$this->_page_navi('[{"f":"'. $this->navi_maps_order['calendars'] .'"}]');
	}

	public function action_calendar() {

		if ($this->_inquiry_order_start && $this->_service_useflg['use_order_flg'] !== true) {
			$this->redirect('/admin/top');
		}

		$inquiry_calendar_id = $this->request->query('id');

		if ($this->_inquiry_order_start && $this->_path == 'admininquiry') {
			$this->redirect('/adminorder/calendar?id=' . $inquiry_calendar_id);
		}

		$bot_id = $this->_bot_id;
		$calendar = ORM::factory('inquirycalendar')->where('bot_id', '=', $bot_id)->where('id', '=', $inquiry_calendar_id)->find();
		if ( !$calendar ) {
			//TODO validate inquiry_calendar_id
		}

		// get all maximum options: inquiry_id => maximum_options
		$inquiries_and_maximums = $this->_model->get_inquiries_with_maximums($bot_id);
		$inquiry_maximum_mapping = $inquiries_and_maximums['mapping'];
		$all_inquiry_maximums = $inquiries_and_maximums['maximums'];
		$inquiry_array = $inquiries_and_maximums['inquiries'];
		$inquiry_name_mapping = [];
		foreach ($inquiry_array as $inquiry) {
			$inquiry_name_mapping[$inquiry['inquiry_id']] = $inquiry['inquiry_name'];
		}
		$view = View::factory($this->_view_path . 'calendar');
		$view->calendar = $calendar;
		$json_title = $calendar->title;
		$obj_title = json_decode($json_title, true);
		$title = $obj_title[$this->_lang_cd];
		if (!$title) {
			foreach ($obj_title as $value) {
				if ($value) {
					$title = $value;
					break;
				}
			}
		}
		$view->calendar_title = $title;
		$remarks = $calendar->remark;
		$calendar_remark = NULL;
		if (!is_null($remarks)) {
			$remarks = json_decode($remarks, true);
			foreach ($remarks as $remark) {
				if ($remark) {
					$calendar_remark = $remark;
					break;
				}
			}
		}
		$view->remarks = $remarks;
		$view->calendar_remark = $calendar_remark;
		$view->calendar_data = $calendar->calendar_data;
		$reservations = [];
		$calendar_data = json_decode($calendar->calendar_data);
		if ($calendar_data) {
			// $inquiry_name_mapping = $inquiries_and_maximums['inquiries']->as_array('inquiry_id', 'inquiry_name');
			$view->reservations_text = '';
			foreach ($calendar_data as $index=>$reservation) {
				$reservation->maximum_name = $all_inquiry_maximums[$reservation->maximum_id];
				$reservation->inquiry_name = $inquiry_name_mapping[$reservation->inquiry_id];
				$reservation->text = $reservation->inquiry_name . ' | ' . $reservation->maximum_name;
				if ($index < count($calendar_data) - 1) {
					$reservation->text .= ' ;'.PHP_EOL;
				}
				$view->reservations_text .= $reservation->text;
				$reservations[] = $reservation;
			}
		}
		$view->reservations = $reservations;
		$public_url_prefix = "https://apps.talkappi.com/calendar?id=";
		// TODO get public_url_prefix from config (env settings)
		$view->public_url = $public_url_prefix.$inquiry_calendar_id;

		//TODO get embed_code and embed_url from  (env settings)?
		$view->embed_code = '<iframe width="100%" height="100%" src="'.$view->public_url.'"></iframe>';
		$view->_lang_cd = $this->_lang_cd;
		$view->bot_lang = $this->_model->get_bot_support_lang(ORM::factory('bot', $bot_id));

		$view->inquiry_options = json_encode($inquiry_array, JSON_UNESCAPED_UNICODE);
		$view->inquiry_maximum_mapping = json_encode($inquiry_maximum_mapping, JSON_UNESCAPED_UNICODE);
		$view->all_maximum_options = json_encode($all_inquiry_maximums, JSON_UNESCAPED_UNICODE);

		$view->navi_parent_url = $this->_action_path . 'calendars';
		$this->template->content = $view;

		$page_navi_array = [
			['f'=>$this->navi_maps_order['calendars'],'url'=>$view->navi_parent_url],
			['f'=>$this->navi_maps_order['calendar']],
		];
		$this->_page_navi(json_encode($page_navi_array));
	}

	public function action_inquirysetting() {
		$bot_id = $this->_bot_id;
		// 保存が必要なsetting_cdを追記
		$array_settings = ['json_next_inquiry_setting'];
		$inquiry_div = $this->_inquiry_order_start ? $this->_item_div : NULL;
		if ($inquiry_div != 9) {
			$array_settings[] = 'json_receipt_setting';
		}		

		function getBotSetting($model, $bot_id, $setting_cd) {
			$json_setting = $model->get_bot_setting($bot_id, $setting_cd);
			if (is_null($json_setting) || $json_setting === '') {
				return ['data' => [], 'user_in_charge_required' => null];
			} else {
				$array_setting = json_decode($json_setting, true);
				if (json_last_error() !== JSON_ERROR_NONE) {
					echo "エラー: JSONのデコードに失敗しました。エラー: " . json_last_error_msg();
					return ['data' => [], 'user_in_charge_required' => null];
				}
				return ['data' => $array_setting];
			}
		}

		// json_next_inquiry_setting
		$next_inquiry_setting = getBotSetting($this->_model, $bot_id, 'json_next_inquiry_setting');
		$array_next_inquiry_setting = $next_inquiry_setting['data'];

		// json_receipt_setting
		$next_receipt_setting = getBotSetting($this->_model, $bot_id, 'json_receipt_setting');
		$array_receipt_setting = $next_receipt_setting['data'];


		if ($this->request->post()){
			$post = $this->request->post();
			foreach ($array_settings as $setting_cd) {
				$array_setting = ($setting_cd === 'json_receipt_setting') ? $array_receipt_setting : $array_next_inquiry_setting;
				if($setting_cd == 'json_next_inquiry_setting'){
					$array_setting['user_in_charge_required'] = $post['user_in_charge_required'];
					if ($post['mail_signature'] != '0') {
						$array_setting['mail_signature'] = $post['mail_signature'];
					} else {
						unset($array_setting['mail_signature']);
					}
				} else if($setting_cd == 'json_receipt_setting'){
					$array_setting['corporation'] = $post['corporation'];
					$array_setting['address'] = $post['address'];
					$array_setting['tel'] = $post['tel'];
					$array_setting['regist_no'] = $post['regist_no'];
					$array_setting['template'] = 'receipt.inquiry.pdf1'; // 現状固定
					$array_setting['tax_rate'] = $post['tax_rate'];
				}
				
				$updated_json_string = json_encode($array_setting, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES); // formatted

				DB::delete('t_bot_setting')
					->where('bot_id', '=', $this->_bot_id)
					->where('setting_cd', '=', $setting_cd)
					->execute();
				
				$setting = ORM::factory('botsetting');
				$setting->bot_id = $bot_id;
				$setting->setting_cd = $setting_cd; 
				$setting->setting_value = $updated_json_string;
				$setting->upd_user = $this->_user->user_id;
				$setting->upd_time = date('Y-m-d H:i:s');
				$setting->save(); 
			}

			$this->redirect($this->_action_path . 'inquirysetting');
		} else {
		}
		
		$mail_signatures = $this->_model->get_signatures($this->_bot_id);
		$mail_signature_options['0'] = __('admin.send.label.signature_select');
		foreach ($mail_signatures as $mail_signature) {
			$mail_signature_options[$mail_signature['id']] = $mail_signature['sign_title'];
		}

		$post = [];
		$post['domain'] = NULL;
		$view = View::factory($this->_view_path . 'inquirysetting');
		$view->bot_id = $bot_id;
		$view->inquiry_div = $inquiry_div;
		$view->post = $post;
		$view->inquiry_setting = $array_next_inquiry_setting;
		$view->receipt_setting = $array_receipt_setting;
		$view->mail_signature_options = json_encode($mail_signature_options, JSON_UNESCAPED_UNICODE);
		$this->template->content = $view;
	}

	public function action_inquirymodify() {
		$this->auto_render = FALSE;
		$post = $this->request->post();
		$inquiry_result = ORM::factory('inquiryresult', $post['result_id']);
		$token_data = [];
		$token_data['facility_cd'] = $inquiry_result->scene_cd;
		$token_data['id'] = $inquiry_result->inquiry_id;
		$token_data['bot_id'] = $inquiry_result->bot_id;
		$token_data['member_id'] = $inquiry_result->member_id;
		$token_data['channel'] = 'wb';
		$token_data['line_id'] = '';
		$token_data['lang_cd'] = $inquiry_result->lang_cd;
		$token_data['result_id'] = $inquiry_result->id;
		$token_data['result_cd'] = $inquiry_result->result_cd;
		//$token_data['mail'] = $post['mail'];
		$token_data['user_id'] = $this->_user->user_id;
		$inquiry_url = $this->_model->create_service_url('inquiry');
		$url = $inquiry_url . 'input?id=' . $this->_model->set_token_data($inquiry_result->member_id, $token_data, '1 day');
		$this->response->body(json_encode(['url'=>$url]));
	}

	public function action_copy() {
		$this->auto_render = FALSE;
		$post = $this->request->post();
		$item_div = $this->_can_copied($post['uri']);
		if ($item_div === false) {
			$this->response->body(json_encode(['result'=>'fail', 'message'=>'common.message.error.content_copy_error']));
			return;
		}
		if ($item_div == 9 || $item_div == 19) {
			$item_id = $this->_get_session('inquiry_id', NULL);
		}
		$this->_copy($this->_bot_id, $item_div, $item_id);
		$this->response->body(json_encode(['result'=>'success', 'message'=>'common.message.success.content_copy_success']));
	}

	public function action_labels() {
		$errors = NULL;
		$message = NULL;
		$post = NULL;

		if ($this->request->post()) {
			$post = $this->request->post();
			if ($this->_page_action == 'delete') {
				$orm = ORM::factory('entrylabel', $post['label_id']);
				$orm->delete();
				$this->redirect($this->_action_path . 'labels');
			}
		}

		$items = $this->_model->get_admin_labels($this->_bot_id);
		$view = View::factory($this->_view_path . 'labels');
		$view->items = $items;
		$view->results =  $this->_model->get_admin_inquiry_labels($this->_bot_id);
		$this->template->content = $view;
	}

	public function action_label() {
		$errors = NULL;
		$message = NULL;
		$post = NULL;

		if ($this->request->post()) {
			$post = $this->request->post();
			$label_data = json_decode($post['label_data'], true);
			$label_id = (isset($label_data['label_id']) && $label_data['label_id']) ? $label_data['label_id'] : null;
			if ($this->_page_action == 'delete') { // TODO: delete
				if (!$label_id) return;
			} else {
				if ($label_id) { // update
					DB::update('t_entry_label')
					->set([
						'label_name' => $label_data['label_name'],
						'labels' => json_encode($label_data['labels'], JSON_UNESCAPED_UNICODE),
						'upd_user' => $this->_user_id,
						'upd_time' => date('Y-m-d H:i:s'),
					])
					->where('bot_id', '=', $this->_bot_id)
					->where('label_id', '=', $label_id)
					->execute();
				} else { // new
					$newLabel = DB::insert('t_entry_label', array(
						'bot_id','label_div','label_name','labels','upd_user','upd_time'
						))->values(array(
						$this->_bot_id,'inquiry',$label_data['label_name'],json_encode($label_data['labels']),$this->_user_id,date('Y-m-d H:i:s')))->execute();
					$label_id = $newLabel[0];
				}

				$this->redirect('/admininquiry/label?id=' . $label_id);

			}
		} else {
			$label_id = $this->request->query('id', NULL);
		}
		
		$item = null;
		$inquiries = [];
		if ($label_id) {
			$item = $this->_model->get_admin_labels($this->_bot_id, $label_id);
			$inquiries =  $this->_model->get_admin_inquiry_by_label_id($this->_bot_id, $label_id);
		}
		$labelData = array(
			'name' => $item ? $item[0]['label_name'] : '',
			'id' => $item ? $item[0]['label_id'] : null,
			'labels' => $item ? json_decode($item[0]['labels']): array()
		);

		$view = View::factory($this->_view_path . 'label');
		$view->labelData = $labelData;
		$view->inquiries =  $inquiries;
		$this->template->content = $view;

		$action_path = explode('/', trim($this->_action_path, '/'))[0];
		$navi_next = ($action_path === 'admininquiry') ? $this->navi_maps_inquiry['labels'] : $this->navi_maps_order['labels'];
        $navi_current = ($action_path === 'admininquiry') ? $this->navi_maps_inquiry['label'] : $this->navi_maps_order['label'];
        $this->_page_navi('[{"f":"'. $navi_next .'","url":"' . $this->_action_path . 'labels' . '"}, {"f":"'. $navi_current .'"}]');
	}

	public function action_paste() {
		$this->auto_render = FALSE;
		$post = $this->request->post();
		$item_div = $this->_can_pasted($post['uri']);
		if ($item_div == false) {
			$this->response->body(json_encode(['result'=>'fail', 'message'=>'common.message.error.content_copy_error']));
			return;
		}
		$obj = $this->_paste();
		if ($obj == null) {
			$this->response->body(json_encode(['result'=>'fail', 'message'=>'common.message.error.content_copy_error']));
			return;
		}
		if ($obj['item_div'] == 9 || $obj['item_div'] == 19) {
			$new_id = $this->_copy_inquiry($obj['item_id'], 'newcopy', $this->_bot_id);
			if ($new_id != 0) {
				$this->response->body(json_encode(['result'=>'success', 'message'=>'content_copy_success', 'url'=>$this->_action_path . 'inquiry?id=' . $new_id]));
			}
			else {
				$this->response->body(json_encode(['result'=>'success', 'message'=>'common.error.front.title', 'url'=>$this->_action_path . 'inquiry?id=' . $obj['item_id']]));
			}
			
		}
	}

	public function action_save_calendarremark() {
		$this->auto_render = FALSE;
    $this->response->headers('Access-Control-Allow-Origin', '*');
    $this->response->headers('Content-Type', 'application/json');
		try {
			if ($this->request->post()) {
				$post = $this->request->post();
				if (!isset($post['calendar_id'])) {
					throw new Exception('no_calendar_id');
				}
				$decoded_data = json_decode($post['remark'], true);
				$data = ['remark' => $decoded_data];
				$inquirymodel = new Model_InquiryModel();
				$inquirymodel->save_inquirycalendar($this->_bot_id, $post['calendar_id'], $data, $this->_user_id);
				$this->response->status(200)->body(json_encode(['result'=>'success']));
			} 
		} catch (Exception $e) {
			$this->response->status(500)->body(json_encode(['result'=>'fail', 'message'=>'common.message.error.content_save_error']));
		}
	}

	private function _item_permission_check($inquiry_id) {
		$item = ORM::factory('inquiry', $inquiry_id);
		if (isset($item->bot_id)) {
			if ($item->bot_id == $this->_bot_id) return 1;
		}
		return -1;
	}
	
	private function _is_choice($entry_type_cd) {
		if ($entry_type_cd == 'opt' || $entry_type_cd == 'chk' || $entry_type_cd == 'cha' || $entry_type_cd == 'sel') {
			return true;
		}
		return false;
	}
	
	private function _get_default_btn($bot_id, $item_div, $class_cd, $lang_cd) {
		return $this->_model->get_item_lst_message($bot_id, $item_div, $class_cd, 'item_def_button', $lang_cd);
	}

	private function _skill_box($all_language = NULL, $default_btn = NULL) {
		$skill = $this->_model->get_skill();
		$skillbox = View::factory('admin/skillbox');
		$kind_list = DB::select('kind')->distinct('kind')->from('m_skill')->execute();
		$kinds = [];
		foreach($kind_list as $kind) {
			$kinds[$kind['kind']] = $kind['kind'];
		}
		$skillbox->skill_kinds = [''=>'-すべて-'] + $kinds;
		$skillbox->all_language = $all_language;
		if ($default_btn != NULL ) $skillbox->default_btn = $default_btn;
		return $skillbox;
	}

	private function _update_item_display($bot_id, $item_id, $item_div, $post) {
		$display_info = [];
		if (array_key_exists('lang_display', $post)) {
			$display_info['lang_display'] = implode(',', json_decode($post['lang_display'], true));
		}
		else {
			$display_info['lang_display'] = '';
		}
		if(!array_key_exists('recommend', $post)) {
			$display_info['recommend'] = 0;
		}
		else {
			$display_info['recommend'] = 1;
		}
		if(!array_key_exists('public_flg', $post)) {
			$display_info['public_flg'] = 0;
		}
		else {
			$display_info['public_flg'] = 1;
		}
		$has_display = false;
		if ($item_id != NULL) {
			$item = ORM::factory('itemdisplay')->where('bot_id', '=', $bot_id)->where('item_id', '=', $item_id)->where('item_div', '=', $item_div)->find();
			if (isset($item->item_id)) {
				$has_display = true;
			}
		}
		if ($has_display == false) {
			$item = ORM::factory('itemdisplay');
			$item->bot_id = $bot_id;
			$item->item_id = $item_id;
			$item->item_div = $item_div;
			$item->sort_no1 = $item_id % 1000;
			$item->sort_no2 = 0;
			$item->sort_no3 = 0;
			$item->sort_no4 = 0;
			$item->sort_no5 = 0;
			$item->recommend = $display_info['recommend'];
			$item->lang_display = $display_info['lang_display'];
			$item->public_flg = $display_info['public_flg'];
			$item->save();
		}
		else {
			$grp_bot_id = $this->_model->get_grp_bot_id($bot_id);
			if ($grp_bot_id == 0) {
				DB::update('t_item_display')->set($display_info)->where('bot_id', '>=', $bot_id)->where('bot_id', '<', intval($bot_id) + 1000)->where('item_id', '=', $item_id)->where('item_div', '=', $item_div)->execute();
			}
			else {
				DB::update('t_item_display')->set($display_info)->where('bot_id', '=', $bot_id)->where('item_id', '=', $item_id)->where('item_div', '=', $item_div)->execute();
			}
		}
	}

	private function _has_tag($item_div) {
		$icons = $this->_model->get_bot_message($this->_bot_id, 'item_div_' . $item_div . '_tag', 'ja', 'img');
		if (count($icons) == 0) return false;
		return true;
	}

	private function _verify_url($inquiry_id, $lang_cd='') {
		$inquiry = ORM::factory('inquiry', $inquiry_id);
		if ($inquiry->scene_cd != NULL) {
			$scene_cd = $inquiry->scene_cd;
		}
		else {
			$scene_cd = $this->_bot->facility_cd;
		}
		if ($inquiry->renew_time == null) {
			$verify_url = $this->_model->get_env('inquiry_url') . '?f=' . $scene_cd . '&id=' . $inquiry->inquiry_id;
		}
		else {
			$verify_url = $this->_model->get_env('inquiry_url') . '?f=' . $scene_cd . '&id=' . $inquiry->inquiry_cd;
			//$verify_url = $this->_model->get_env('inquiry_url') . '?id=' . $inquiry->inquiry_cd;
		}
		if ($lang_cd != '') {
			$verify_url = $verify_url . '&lang_cd=' . $lang_cd;
		}
		return $verify_url;
	}
}
