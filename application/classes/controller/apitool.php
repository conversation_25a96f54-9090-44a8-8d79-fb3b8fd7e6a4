<?php

defined('SYSPATH') or die('No direct script access.');
class Controller_Apitool extends Controller
{
    const API_RESULT_OK = '0';
    const API_RESULT_ERR = '1';
    const API_RESULT_WARN = '2';

    private function check_accesstoken($post, $nid)
    {
        $login_type = "";
        if (isset($post->login_type)) {
            $login_type = $post->login_type;
        }

        if ($login_type == "admin") {
            // 使用session
            $bot_id = Session::instance()->get("bot_id");
            if ($bot_id == null) {
                error_log("not login!");
                // test begin
                //return 201003;
                // test edn
                return -1; // 失败
            }
            return $bot_id;
        }

        $token = $post->accesstoken;
        if ($token == "e68b1d8366fa") {
            if ($nid == "honban") {
                Database::$default = 'honban';
            } elseif ($nid == "dev") {
                Database::$default = 'default';
            }
            return -2;  // 成功
        }
        return -1;  // 失败
    }
    private function check_modify_accesstoken($post, $nid)
    {
        $login_type = "";
        if (isset($post->login_type)) {
            $login_type = $post->login_type;
        }
        if ($login_type == "admin") {
            // 使用session
            $bot_id = Session::instance()->get("bot_id");
            if ($bot_id == null) {
                error_log("not login!");
                return -1; // 失败
            }
            return $bot_id;
        }

        if (isset($post->accesstokensave)) {
            $token = $post->accesstokensave;
        } else {
            $token = $post->accesstoken;
        }
        $cur_ip = $this->getlocalip();
        if ($cur_ip == "*************") {
            // admin.talkappi.com机器上，必须用此token
            if ($token == "activalues anhui") {
                return -2; // 成功
            } else {
                return -1;  // 失败
            }
        }

        if ($nid == "honban") {
            if ($token == "activalues anhui") {
                Database::$default = 'honban';
                return -2;  // 成功
            } else {
                return -1;  // 失败
            }
        } elseif ($nid == "dev") {
            if ($token == "activalues guilin") {
                Database::$default = 'default';
                return -2; // 成功
            } else {
                return -1;  // 失败
            }
        } elseif ($nid == "honbandefault") {
            // 只允许admin.talkappi.com机器使用这个nid
            if ($cur_ip != "*************") {
                return -1;  // 失败
            }
            if ($token == "activalues anhui") {
                return -2; // 成功
            } else {
                return -1;  // 失败
            }
        } elseif ($nid == "devdefault") {
            // 只允许dev1-api.talkappi.com机器使用这个nid
            if ($cur_ip != "*************") {
                return -1;  // 失败
            }
            if ($token == "activalues guilin") {
                return -2; // 成功
            } else {
                return -1;  // 失败
            }
        }
        return -1;  // 失败
    }
    private function jresult($result_cd, $reason_cd = null, $data = null)
    {
        $result_array = array('result'=>$result_cd, 'reason'=>$reason_cd, 'data'=>$data);
        return json_encode($result_array);

        // 2021.07.31 改为压缩
        /* 改为在php.ini里设置
        $result_array = array('result'=>$result_cd, 'reason'=>$reason_cd, 'data'=>$data);
        $result_json_string =  json_encode($result_array);

        $retzip = gzencode($result_json_string);
        $this->response->headers('Content-Encoding', 'gzip');
        return $retzip;
        */
    }

    private function getBotInfo($item_cd)
    {
        if ($item_cd == "99999999") {
            return array('bot_id'=>'999');
        }
        $record = DB::select('bot_id')->from('t_bot')
            ->where('facility_cd', '=', $item_cd)
            ->execute()->as_array();
        if (count($record) == 0) {
            return array('bot_id'=>'');
        } else {
            return array('bot_id'=>$record[0]['bot_id']);
        }
    }

    public function action_get_bots()
    {
        $this->response->headers('Access-Control-Allow-Origin', '*');
        $condition = $this->request->post();
        if ($condition) {
            $post = json_decode($condition["condition"]);

            if ($this->check_accesstoken($post, $post->nid) == -1) {
                return $this->response->body($this->jresult("01"));
            }

            $sql_bot_id = "";
            $bot_id = "";
            if (isset($post->login_type) && $post->login_type == "admin") {
                $bot_id = $this->check_accesstoken($post, $post->nid);
                $sql_bot_id = "AND a.bot_id = :bot_id";
            }

            $sql = "SELECT a.bot_id,a.bot_name, a.facility_cd as facility_cd, -1 as item_id,a.support_lang
				FROM t_bot a
                WHERE 1=1
                $sql_bot_id
				ORDER BY a.bot_id
				";
            /*
            $sql = "SELECT a.bot_id,a.bot_name, b.item_cd as facility_cd,b.item_id
                FROM t_bot a
                INNER JOIN t_item b
                ON a.facility_cd = b.item_cd
                AND b.item_div = 1
                ORDER BY a.bot_id
                ";
            */
            /*
            $sql = "SELECT a.bot_id,a.bot_name,b.item_cd as facility_cd, -1 as item_id
            FROM t_bot a
            ORDER BY a.bot_id
            ";
            */
            $query = DB::query(Database::SELECT, $sql);
            $query->parameters(array(
                ':bot_id' => $bot_id,
            ));

            $results = $query->execute();
            $ret = array();
            $ret["bot_data"] = $results->as_array();


            $sql = "SELECT a.*,
				CASE WHEN a.scene_name = b.facility_cd THEN 1
				ELSE 0
				END sort_facility
				FROM t_bot_scene a
				INNER JOIN t_bot b
				ON a.bot_id = b.bot_id
				ORDER BY b.bot_id,sort_facility DESC
				";
            $query = DB::query(Database::SELECT, $sql);
            $results = $query->execute();
            $ret["bot_scene_data"] = $results->as_array();

            $this->response->body($this->jresult("00", null, $ret));
            return ;
        } else {
            $this->response->body($this->jresult("01"));
        }
        return;
    }

    public function action_get_bot_inf()
    {
        $this->response->headers('Access-Control-Allow-Origin', '*');
        $condition = $this->request->post();
        if ($condition) {
            $post = json_decode($condition["condition"]);
            if ($this->check_accesstoken($post, $post->nid) == -1) {
                return $this->response->body($this->jresult("01"));
            }
            $ret = array();

            if (isset($post->login_type) && $post->login_type == "admin") {
                // admin模式不返回任何信息
                return $this->response->body($this->jresult("01"));
            }

            // 读取t_bot
            $sql = "SELECT *
				FROM t_bot a
				where a.bot_id = :bot_id
				";
            $query = DB::query(Database::SELECT, $sql);
            $query->parameters(array(
                ':bot_id' => $post->bot_id,
            ));
            $results = $query->execute();
            $ret["bot_data"] = $results->as_array();

            // 读取t_bot_setting
            $sql = "SELECT *
				FROM t_bot_setting a
				where a.bot_id = :bot_id
				";
            $query = DB::query(Database::SELECT, $sql);
            $query->parameters(array(
                ':bot_id' => $post->bot_id,
            ));
            $results = $query->execute()->as_array();
            $this->doMask($results, $post);
            $ret["bot_setting_data"] = $results;

            // 读取t_bot_busitime
            $sql = "SELECT *
				FROM t_bot_busitime a
				where a.bot_id = :bot_id
				order by
					busi_type_cd DESC,
					start_date ASC,
					start_time ASC
				";
            $query = DB::query(Database::SELECT, $sql);
            $query->parameters(array(
                ':bot_id' => $post->bot_id,
            ));
            $results = $query->execute();
            $ret["bot_busitime_data"] = $results->as_array();

            // 读取t_bot_stock
            $sql = "SELECT *
				FROM t_bot_stock a
				where a.bot_id = :bot_id
				order by
					stock_cd
				";
            $query = DB::query(Database::SELECT, $sql);
            $query->parameters(array(
                ':bot_id' => $post->bot_id,
            ));
            $results = $query->execute();
            $ret["bot_stock_data"] = $results->as_array();

            $this->response->body($this->jresult("00", null, $ret));
            return ;
        } else {
            $this->response->body($this->jresult("01"));
        }
        return;
    }

    public function action_get_all_bot_setting()
    {
        $this->response->headers('Access-Control-Allow-Origin', '*');
        $condition = $this->request->post();
        if ($condition) {
            $post = json_decode($condition["condition"]);
            if ($this->check_accesstoken($post, $post->nid) == -1) {
                return $this->response->body($this->jresult("01"));
            }

            if (isset($post->login_type) && $post->login_type == "admin") {
                // admin模式不返回任何信息
                return $this->response->body($this->jresult("01"));
            }

            if ($post->nid == "honban") {
                $curl = curl_init();
                $timeout = 5;

                $connectURL = "http://172.31.32.237:9090/api/api_get?read_table";
                $data = ['api_get_source' => 'read_table',
                    'tablename' => 't_bot_setting'
                ];

                $header = [
//					'Content-Type: application/json',
                    'Content-Type: application/x-www-form-urlencoded',
                ];

                curl_setopt($curl, CURLOPT_URL, $connectURL);
                curl_setopt($curl, CURLOPT_CUSTOMREQUEST, 'POST');
                //				curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($data));
                curl_setopt($curl, CURLOPT_POSTFIELDS, http_build_query($data));
                curl_setopt($curl, CURLOPT_HTTPHEADER, $header);
                curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
                curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($curl, CURLOPT_HEADER, false);
                curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, $timeout);
                $response= curl_exec($curl);
                //				$header_size = curl_getinfo($curl, CURLINFO_HEADER_SIZE);
                curl_close($curl);
                //				$body = substr($response, $header_size);
                //error_log($header_size);
                //error_log($response);
                //				$results = json_decode($body, true);

                $results = json_decode($response, false);
                $results = $results->data;
                $results = json_encode($results);
                $results = json_decode($results, true);
                $this->doMask($results, $post);
                $ret["bot_setting_data"] = $results;
                $this->response->body($this->jresult("00", null, $ret));
            } else {
                $model = Model::factory('apimodel');

                $results = $model->read_t_bot_setting();

                $this->doMask($results, $post);

                $ret["bot_setting_data"] = $results;
                $this->response->body($this->jresult("00", null, $ret));
            }
            return ;
        } else {
            $this->response->body($this->jresult("01"));
        }
        return;
    }

    private function doMask(&$results, $post)
    {
        for ($i=0; $i<count($results); $i++) {
            $cur_result = $results[$i];
            if ($cur_result["setting_value"] == "") {
                continue;
            }
            if ($cur_result["setting_cd"] == "json_moneyforward") {
                try {
                    $temp_value = json_decode($results[$i]["setting_value"]);
                    $temp_value->partner_id = $this->doMaskValue($temp_value->partner_id, 4);
                    $temp_value->department_id = $this->doMaskValue($temp_value->department_id, 4);
                    $results[$i]["setting_value"] = json_encode($temp_value, JSON_UNESCAPED_UNICODE);
                } catch (Exception $e) {
                    $results[$i]["setting_value"] = "Invalid value!!!";
                }
            }
            if ($cur_result["setting_cd"] == "awl_lite") {
                try {
                    $temp_value = json_decode($results[$i]["setting_value"]);
                    $temp_value->api_key = $this->doMaskValue($temp_value->api_key, 4);
                    $results[$i]["setting_value"] = json_encode($temp_value, JSON_UNESCAPED_UNICODE);
                } catch (Exception $e) {
                    $results[$i]["setting_value"] = "Invalid value!!!";
                }
            }
            if ($cur_result["setting_cd"] == "json_payment_setting") {
                try {
                    $obj = json_decode($results[$i]["setting_value"]);

                    foreach ($obj as $key=>$value) {
                        if (is_array($value)) {
                            for ($num=0; $num<count($value);$num++) {
                                if (property_exists($value[$num], 'settings')) {
                                    if (property_exists($value[$num]->settings, 'ip_pass')) {
                                        $value[$num]->settings->ip_pass = $this->doMaskValue($value[$num]->settings->ip_pass, 1);
                                    }
                                }
                            }
                        } else {
                            if (property_exists($value, 'ip_pass')) {
                                $value->ip_pass = $this->doMaskValue($value->ip_pass, 1);
                            }
                        }
                    }
                    $results[$i]["setting_value"] = json_encode($obj, JSON_UNESCAPED_UNICODE);
                } catch (Exception $e) {
                    $results[$i]["setting_value"] = "Invalid value!!!";
                }
            }
            if ($cur_result["setting_cd"] == "json_kintone") {
                try {
                    $temp_value = json_decode($results[$i]["setting_value"]);
                    $temp_value->api_token = $this->doMaskValue($temp_value->api_token, 4);
                    $results[$i]["setting_value"] = json_encode($temp_value, JSON_UNESCAPED_UNICODE);
                } catch (Exception $e) {
                    $results[$i]["setting_value"] = "Invalid value!!!";
                }
            }
            if ($cur_result["setting_cd"] == "json_reserve_settings") {
                try {
                    $temp_value = json_decode($results[$i]["setting_value"]);
                    if (is_array($temp_value)) {
                        $nums = count($temp_value);
                        for ($j=0; $j<$nums; $j++) {
                            $cur_obj = $temp_value[$j];
                            $cur_obj->config->password = $this->doMaskValue($cur_obj->config->password, 2);
                            if (isset($cur_obj->payment_card_setting) && isset($cur_obj->payment_card_setting->jtb)&& isset($cur_obj->payment_card_setting->jtb->LoginPassword)) {
                                $cur_obj->payment_card_setting->jtb->LoginPassword = $this->doMaskValue($cur_obj->payment_card_setting->jtb->LoginPassword, 2);
                            }
                        }
                    } else {
                        $temp_value->config->password = $this->doMaskValue($temp_value->config->password, 2);

                        if (isset($temp_value->payment_card_setting) && isset($temp_value->payment_card_setting->jtb)&& isset($temp_value->payment_card_setting->jtb->LoginPassword)) {
                            $temp_value->payment_card_setting->jtb->LoginPassword = $this->doMaskValue($temp_value->payment_card_setting->jtb->LoginPassword, 2);
                        }
                    }

                    $results[$i]["setting_value"] = json_encode($temp_value, JSON_UNESCAPED_UNICODE);
                } catch (Exception $e) {
                    $results[$i]["setting_value"] = "Invalid value!!!";
                }
            }

            if ($cur_result["setting_cd"] == "json_sns_link_facebook") {
                try {
                    $temp_value = json_decode($results[$i]["setting_value"]);
                    $temp_value->verify_token = $this->doMaskValue($temp_value->verify_token, 4);
                    $temp_value->app_secret = $this->doMaskValue($temp_value->app_secret, 4);
                    $temp_value->page_access_token = $this->doMaskValue($temp_value->page_access_token, 4);
                    $results[$i]["setting_value"] = json_encode($temp_value, JSON_UNESCAPED_UNICODE);
                } catch (Exception $e) {
                    $results[$i]["setting_value"] = "Invalid value!!!";
                }
            }
            if ($cur_result["setting_cd"] == "json_sns_link_line") {
                try {
                    $temp_value = json_decode($results[$i]["setting_value"]);
                    $temp_value->channel_secret = $this->doMaskValue($temp_value->channel_secret, 4);
                    $temp_value->channel_access_token = $this->doMaskValue($temp_value->channel_access_token, 4);
                    if (property_exists($temp_value, 'weblogin_channel_id')) {
                        $temp_value->weblogin_channel_id = $this->doMaskValue($temp_value->weblogin_channel_id, 4);
                    }
                    if (property_exists($temp_value, 'weblogin_channel_secret')) {
                        $temp_value->weblogin_channel_secret = $this->doMaskValue($temp_value->weblogin_channel_secret, 4);
                    }
                    $results[$i]["setting_value"] = json_encode($temp_value, JSON_UNESCAPED_UNICODE);
                } catch (Exception $e) {
                    $results[$i]["setting_value"] = "Invalid value!!!";
                }
            }
            if ($cur_result["setting_cd"] == "json_sns_link_wechat") {
                try {
                    $temp_value = json_decode($results[$i]["setting_value"]);
                    $temp_value->verify_token = $this->doMaskValue($temp_value->verify_token, 4);
                    $temp_value->app_secret = $this->doMaskValue($temp_value->app_secret, 4);
                    $temp_value->aes_key = $this->doMaskValue($temp_value->aes_key, 4);
                    $results[$i]["setting_value"] = json_encode($temp_value, JSON_UNESCAPED_UNICODE);
                } catch (Exception $e) {
                    $results[$i]["setting_value"] = "Invalid value!!!";
                }
            }
            if ($cur_result["setting_cd"] == "txt_link_tl_info") {
                try {
                    $temp_value = json_decode($results[$i]["setting_value"]);
                    $temp_value->password = $this->doMaskValue($temp_value->password, 2);
                    $results[$i]["setting_value"] = json_encode($temp_value, JSON_UNESCAPED_UNICODE);
                } catch (Exception $e) {
                    $results[$i]["setting_value"] = "Invalid value!!!";
                }
            }
            if ($cur_result["setting_cd"] == "txt_slack_channel_url") {
                try {
                    $results[$i]["setting_value"] = $this->doMaskValue($cur_result["setting_value"], 4);
                } catch (Exception $e) {
                    $results[$i]["setting_value"] = "Invalid value!!!";
                }
            }
            if ($cur_result["setting_cd"] == "txt_facebook_post_token") {
                try {
                    $results[$i]["setting_value"] = $this->doMaskValue($cur_result["setting_value"], 4);
                } catch (Exception $e) {
                    $results[$i]["setting_value"] = "Invalid value!!!";
                }
            }
            if ($cur_result["setting_cd"] == "json_lineworks_setting") {
                try {
                    if ($results[$i]["setting_value"] != "") {
                        $temp_value = json_decode($results[$i]["setting_value"]);
                        $temp_value->app_config->client_secret = $this->doMaskValue($temp_value->app_config->client_secret, 4);
                        $temp_value->app_config->private_key = $this->doMaskValue($temp_value->app_config->private_key, 4);
                        $results[$i]["setting_value"] = json_encode($temp_value, JSON_UNESCAPED_UNICODE);
                    }
                } catch (Exception $e) {
                    $results[$i]["setting_value"] = "Invalid value!!!";
                }
            }
            if ($cur_result["setting_cd"] == "json_mail_setting") {
                try {
                    $obj = json_decode($results[$i]["setting_value"]);

                    foreach ($obj as $key=>$value) {
                        if (property_exists($value, 'config')) {
                            $value2 = $value->config;
                            if ($value2 != null) {
                                if (property_exists($value2, 'credentials')) {
                                    $value2->credentials = $this->doMaskValue($value2->credentials, 4);
                                }
                                if (property_exists($value2, 'auth')) {
                                    $value2->auth = $this->doMaskValue($value2->auth, 4);
                                }
                            }
                        }
                    }
                    $results[$i]["setting_value"] = json_encode($obj, JSON_UNESCAPED_UNICODE);
                } catch (Exception $e) {
                    $results[$i]["setting_value"] = "Invalid value!!!";
                }
            }
        }
    }

    private function doMaskValue($value, $size)
    {
        if ($value == "") {
            return $value;
        }
        if (strlen($value) < ($size * 2)) {
            $size = 2;
        }
        $ret =  substr($value, 0, $size) . "******" . substr($value, strlen($value) -$size);
        return $ret;
    }

    public function action_get_inquiry_inf()
    {
        $this->response->headers('Access-Control-Allow-Origin', '*');
        $condition = $this->request->post();
        if ($condition) {
            $post = json_decode($condition["condition"]);
            if ($this->check_accesstoken($post, $post->nid) == -1) {
                return $this->response->body($this->jresult("01"));
            }

            if (isset($post->login_type) && $post->login_type == "admin") {
                // admin模式不返回任何信息
                return $this->response->body($this->jresult("01"));
            }

            $ret = array();

            // 读取inquiry
            /*
            $sql = 'SELECT
                    a.faq_id,
                    a.intent_class_cd,a.intent_type_cd,a.sub_intent_cd,a.intent_cd,a.lang_cd,a.level,a.item_ids,a.question,
                    a.ft_keyword1,a.ft_keyword1_weight,a.ft_keyword2,a.ft_keyword2_weight,a.ft_keyword3,a.ft_keyword3_weight,
                    b.facility_question_title,b.area_cd,b.default_sub_intent,b.show_in_relation_question,
                    b.answer_summary_title,
                    b.answer1_summary,b.answer1_type_cd,b.answer1,
                    b.answer2_summary,b.answer2_type_cd,b.answer2,
                    b.answer3_summary,b.answer3_type_cd,b.answer3,
                    b.answer4_summary,b.answer4_type_cd,b.answer4,
                    b.answer5_summary,b.answer5_type_cd,b.answer5,
                    b.answer6_summary,b.answer6_type_cd,b.answer6,
                    b.answer7_summary,b.answer7_type_cd,b.answer7,
                    b.answer8_summary,b.answer8_type_cd,b.answer8,
                    b.answer9_summary,b.answer9_type_cd,b.answer9,
                    a.question_ft, b.answer_ft, b.question_answer_ft
                FROM m_bot_intent a
                INNER JOIN t_bot_intent b
                    ON a.intent_cd = b.intent_cd
                    AND a.lang_cd = b.lang_cd
                    AND a.sub_intent_cd = b.sub_intent_cd
                WHERE
                    b.bot_id = :bot_id
                    AND (
                        :lang_cd = ""
                        OR a.lang_cd = :lang_cd
                    )
                ORDER BY
                    a.lang_cd,a.intent_type_cd,a.intent_cd,a.sub_intent_cd,b.area_cd
                ';
                */
            $parent_bot_id = $this->get_grp_parent_bot_id($post->bot_id);
            $template_bot_id = $this->get_template_bot_id($post->bot_id);

            $sql = 'SELECT 
                    a.faq_id,
                    a.intent_class_cd,
                    a.intent_type_cd as intent_type_cd_of_m,
                    a.intent_cd as intent_cd_of_m,
                    a.sub_intent_cd as sub_intent_cd_of_m,
                    a.lang_cd as lang_cd_of_m,
                    a.level,
                    a.item_ids,
                    a.question,
                    a.answer1 as answer1_of_m,
                    a.answer2 as answer2_of_m,
                    a.answer3 as answer3_of_m,
                    a.answer4 as answer4_of_m,
                    a.answer5 as answer5_of_m,
                    a.answer6 as answer6_of_m,
                    a.answer7 as answer7_of_m,
                    a.answer8 as answer8_of_m,
                    a.answer9 as answer9_of_m,
                    a.question_ft,
                    a.ft_keyword1,
                    a.ft_keyword1_weight,
                    a.ft_keyword2,
                    a.ft_keyword2_weight,
                    a.ft_keyword3,
                    a.ft_keyword3_weight,
                    a.default_action_skill,
                    a.attr
                    ,"---" as split1
                    , b.*
                    ,"---" as split2
                    ,d.intent_cd as intent_cd_of_skill
                    ,d.sub_intent_cd as sub_intent_cd_of_skill
                    ,d.skill
				FROM m_bot_intent a
				LEFT JOIN t_bot_intent b
					ON a.intent_cd = b.intent_cd
					AND a.lang_cd = b.lang_cd
					AND a.sub_intent_cd = b.sub_intent_cd
                    AND b.bot_id = :bot_id
                LEFT JOIN (
                        SELECT c.intent_cd, c.sub_intent_cd, GROUP_CONCAT(concat("\n\nbot_id=",c.bot_id," lang_cd=",c.lang_cd, ">  ",c.skill)) AS skill
                        FROM t_intent_skill c
                        WHERE 
                            (c.bot_id = 0 OR c.bot_id = :bot_id OR c.bot_id = :parent_bot_id OR c.bot_id = :template_bot_id)
                        GROUP BY
                            c.intent_cd,c.sub_intent_cd
                    ) d
                    ON a.intent_cd = d.intent_cd
				    AND a.sub_intent_cd = d.sub_intent_cd
                INNER JOIN t_bot e
                    ON a.intent_class_cd = e.bot_class_cd
				WHERE
                    1 = 1
                    AND e.bot_id = :bot_id
					AND (
						:lang_cd = ""
						OR a.lang_cd = :lang_cd
					)
                    AND (b.intent_cd IS NOT NULL OR d.intent_cd IS NOT NULL)
				ORDER BY
                    b.intent_cd DESC,b.sub_intent_cd DESC,a.lang_cd,a.intent_type_cd,b.area_cd
				';
            $query = DB::query(Database::SELECT, $sql);
            $query->parameters(array(
                ':bot_id' => $post->bot_id,
                ':lang_cd' => $post->lang_cd,
                ':parent_bot_id' => $parent_bot_id,
                ':template_bot_id' => $template_bot_id,
            ));
            $results = $query->execute();
            $ret["bot_items_data"] = $results->as_array();


            $this->response->body($this->jresult("00", null, $ret));
            return ;
        } else {
            $this->response->body($this->jresult("01"));
        }
        return;
    }

    public function action_get_items_inf()
    {
        $this->response->headers('Access-Control-Allow-Origin', '*');
        $condition = $this->request->post();
        if ($condition) {
            $post = json_decode($condition["condition"]);
            if ($this->check_accesstoken($post, $post->nid) == -1) {
                return $this->response->body($this->jresult("01"));
            }

            if (isset($post->login_type) && $post->login_type == "admin") {
                // admin模式不返回任何信息
                return $this->response->body($this->jresult("01"));
            }

            $ret = array();

            //			INNER JOIN t_keyword k
            //			ON a.item_id = k.find_id

            // 读取items
            /*
            $sql = 'SELECT DISTINCT a.bot_id,a.item_id, a.class_cd, a.item_class_cd,a.item_class_type_cd,a.item_class_type_sub_cd,a.features_cd,a.country_cd, a.item_status_cd,
                        a.item_cd,a.item_div,a.area_cd,a.location_lat,a.location_lon,
                        a.position_x,a.position_y,a.position_z,a.start_date,a.end_date,a.regular_start,a.regular_end,a.item_data,
                        b.lang_cd, b.item_name item_name, b.sell_point, b.sell_point_line, b.tel, b.description,
                        b.item_image, b.item_image_thumb, b.reserve1,
                        b.btn1_name, b.btn1_url, b.btn2_name, b.btn2_url, b.btn3_name, b.btn3_url,
                        b.url,b.show_image, r.sort_no1,r.public_flg,r.lang_display
                    FROM t_item a
                    INNER JOIN t_item_description b
                        ON a.item_id = b.item_id
                    INNER JOIN t_item_display r
                        ON a.bot_id = r.bot_id
                        AND a.item_id = r.item_id
                        AND a.item_div = r.item_div
                    WHERE
                    r.bot_id = :bot_id
                    AND a.delete_flg = 0
                    AND (
                        :lang_cd = ""
                        OR b.lang_cd = :lang_cd
                    )
                    ORDER BY b.lang_cd,a.item_class_cd,a.item_class_type_cd,a.item_class_type_sub_cd,a.features_cd,r.sort_no1
                ';
                */
            $sql = 'SELECT DISTINCT 
                        a.item_id,
                        a.bot_id,
                        a.item_div,
                        a.item_cd,
                        a.class_cd,
                        a.item_class_cd,
                        a.item_class_type_cd,
                        a.item_class_type_sub_cd,
                        a.features_cd,
                        a.item_status_cd,
                        a.country_cd,
                        a.area_cd,
                        a.location_lat,
                        a.location_lon,
                        a.position_x,
                        a.position_y,
                        a.position_z,
                        a.item_barcode,
                        a.start_date,
                        a.end_date,
                        a.regular_start,
                        a.regular_end,
                        a.regular_range,
                        a.link_id,
                        a.item_data as item_data_t_item,
                        a.link_data,
                        a.reserve1 as reserve1_t_item,
                        a.reserve2 as reserve2_t_item,
                        a.delete_flg as delete_flg_t_item,
                        a.upd_user as upd_user_t_item,
                        a.upd_time as upd_time_t_item,

                        (CASE WHEN (a.start_date IS NULL OR a.start_date = "") AND (a.end_date IS NULL OR a.end_date = "") THEN 0
                                WHEN (a.start_date IS NULL OR a.start_date = "") AND (now() <= a.end_date) THEN 0
                                WHEN (a.start_date <= now()) AND (a.end_date IS NULL OR a.end_date = "") THEN 0
                                WHEN !(a.start_date IS NULL OR a.start_date = "") AND !(a.end_date IS NULL OR a.end_date = "") AND (a.start_date <= now()) AND (now() <= a.end_date) THEN 0
                                ELSE 1
                                END
                        ) AS _flg_not_use,
                        "---" as split1,
                        b.*,
                        "---" as split2,
                        r.*,
                        "---" as split3,
                        d.itemname_sellpoint_ft as itemname_sellpoint_ft_new,
                        d.additional_info_for_search as additional_info_for_search_new
					FROM t_item a
					INNER JOIN t_item_description b
						ON a.item_id = b.item_id
					INNER JOIN t_item_display r
						ON a.bot_id = r.bot_id
						AND a.item_id = r.item_id
						AND a.item_div = r.item_div
					LEFT JOIN t_item_description_fulltext d
						ON b.item_id = d.item_id
						AND b.lang_cd = d.lang_cd
					WHERE
					r.bot_id = :bot_id
					AND a.delete_flg = 0
					AND (
						:lang_cd = ""
						OR b.lang_cd = :lang_cd
					)
					ORDER BY b.lang_cd,a.item_class_cd,a.item_class_type_cd,a.item_class_type_sub_cd,a.features_cd,r.sort_no1 
				';
            $query = DB::query(Database::SELECT, $sql);
            $query->parameters(array(
                ':bot_id' => $post->bot_id,
                ':lang_cd' => $post->lang_cd,
            ));
            $results = $query->execute();
            $ret["bot_items_data"] = $results->as_array();


            $this->response->body($this->jresult("00", null, $ret));
            return ;
        } else {
            $this->response->body($this->jresult("01"));
        }
        return;
    }

    public function action_get_menugroup_inf()
    {
        $this->response->headers('Access-Control-Allow-Origin', '*');
        $condition = $this->request->post();
        if ($condition) {
            $post = json_decode($condition["condition"]);
            if ($this->check_accesstoken($post, $post->nid) == -1) {
                return $this->response->body($this->jresult("01"));
            }

            if (isset($post->login_type) && $post->login_type == "admin") {
                // admin模式不返回任何信息
                return $this->response->body($this->jresult("01"));
            }

            $ret = array();

            // 读取menugroup
            $sql = 'SELECT *
				FROM t_bot_menu_group a
				WHERE
					a.bot_id = :bot_id
					AND (
						:lang_cd = ""
						OR a.lang_cd = :lang_cd
					)
				ORDER BY
					a.menu_group_id,
					a.sortno
				';
            $query = DB::query(Database::SELECT, $sql);
            $query->parameters(array(
                ':bot_id' => $post->bot_id,
                ':lang_cd' => $post->lang_cd,
            ));
            $results = $query->execute();
            $ret["bot_menugroup_data"] = $results->as_array();


            $this->response->body($this->jresult("00", null, $ret));
            return ;
        } else {
            $this->response->body($this->jresult("01"));
        }
        return;
    }

    public function action_get_msg_inf()
    {
        $this->response->headers('Access-Control-Allow-Origin', '*');
        $condition = $this->request->post();
        if ($condition) {
            $post = json_decode($condition["condition"]);
            if ($this->check_accesstoken($post, $post->nid) == -1) {
                return $this->response->body($this->jresult("01"));
            }

            if (isset($post->login_type) && $post->login_type == "admin") {
                // admin模式不返回任何信息
                return $this->response->body($this->jresult("01"));
            }

            $ret = array();

            // 读取 t_bot_msg_desc_txt
            $sql = 'SELECT a.*,
                            "---" as split1
                            ,b.lang_cd
                            ,b.no
                            ,b.content
                            ,b.delete_flg as delete_flg_txt
				FROM t_bot_msg a 
				INNER JOIN t_bot_msg_desc_txt b
					ON a.msg_id = b.msg_id
				WHERE
					(a.bot_id = :bot_id)
					AND (
						:lang_cd = ""
						OR b.lang_cd = :lang_cd
					)
				ORDER BY 
					bot_id DESC,msg_cd';
            $query = DB::query(Database::SELECT, $sql);
            $query->parameters(array(
                ':bot_id' => $post->bot_id,
                ':lang_cd' => $post->lang_cd,
            ));
            $results = $query->execute();
            $ret["t_bot_msg_desc_txt"] = $results->as_array();


            // 读取 t_bot_msg_desc_img
            $sql = 'SELECT a.*,
                            "---" as split1
                            ,b.lang_cd
                            ,b.no
                            ,b.title
                            ,b.msg_image
                            ,b.url
                            ,b.delete_flg as delete_flg_img
				FROM t_bot_msg a 
				INNER JOIN t_bot_msg_desc_img b
					ON a.msg_id = b.msg_id
				WHERE
					(a.bot_id = :bot_id)
					AND (
						:lang_cd = ""
						OR b.lang_cd = :lang_cd
					)
				ORDER BY 
					bot_id DESC,msg_cd';
            $query = DB::query(Database::SELECT, $sql);
            $query->parameters(array(
                ':bot_id' => $post->bot_id,
                ':lang_cd' => $post->lang_cd,
            ));
            $results = $query->execute();
            $ret["t_bot_msg_desc_img"] = $results->as_array();

            // 读取 t_bot_msg_desc_car
            $sql = 'SELECT a.*,
                            "---" as split1
                            ,b.lang_cd
                            ,b.no
                            ,b.msg_name
                            ,b.title
                            ,b.content
                            ,b.msg_image
                            ,b.url
                            ,b.btn1_name
                            ,b.btn1_url
                            ,b.btn1_url_sp
                            ,b.btn1_url_lang_cd
                            ,b.btn2_name
                            ,b.btn2_url
                            ,b.btn2_url_sp
                            ,b.btn2_url_lang_cd
                            ,b.btn3_name
                            ,b.btn3_url
                            ,b.btn3_url_sp
                            ,b.btn3_url_lang_cd
                            ,b.delete_flg as delete_flg_car
				FROM t_bot_msg a 
				INNER JOIN t_bot_msg_desc_car b
					ON a.msg_id = b.msg_id
				WHERE
					(a.bot_id = :bot_id)
					AND (
						:lang_cd = ""
						OR b.lang_cd = :lang_cd
					)
				ORDER BY 
					bot_id DESC,msg_cd,b.no';
            $query = DB::query(Database::SELECT, $sql);
            $query->parameters(array(
                ':bot_id' => $post->bot_id,
                ':lang_cd' => $post->lang_cd,
            ));
            $results = $query->execute();
            $ret["t_bot_msg_desc_car"] = $results->as_array();

            // 读取 t_bot_msg_desc_lst
            $sql = 'SELECT a.*,
                            "---" as split1
                            ,b.lang_cd
                            ,b.no
                            ,b.title
                            ,b.content
                            ,b.msg_image
                            ,b.url
                            ,b.style
                            ,b.sns_cd
                            ,b.delete_flg as delete_flg_lst
				FROM t_bot_msg a 
				INNER JOIN t_bot_msg_desc_lst b
					ON a.msg_id = b.msg_id
				WHERE
					(a.bot_id = :bot_id)
					AND (
						:lang_cd = ""
						OR b.lang_cd = :lang_cd
					)
				ORDER BY 
					bot_id DESC,	msg_cd, no';
            $query = DB::query(Database::SELECT, $sql);
            $query->parameters(array(
                ':bot_id' => $post->bot_id,
                ':lang_cd' => $post->lang_cd,
            ));
            $results = $query->execute();
            $ret["t_bot_msg_desc_lst"] = $results->as_array();

            // 读取 t_bot_msg_desc_tpl
            $sql = 'SELECT a.*,
                            "---" as split1
                            ,b.lang_cd
                            ,b.no
                            ,b.title
                            ,b.content
                            ,b.delete_flg as delete_flg_tpl
				FROM t_bot_msg a 
				INNER JOIN t_bot_msg_desc_tpl b
					ON a.msg_id = b.msg_id
				WHERE
					(a.bot_id = :bot_id)
					AND (
						:lang_cd = ""
						OR b.lang_cd = :lang_cd
					)
				ORDER BY 
					bot_id DESC,msg_cd';
            $query = DB::query(Database::SELECT, $sql);
            $query->parameters(array(
                ':bot_id' => $post->bot_id,
                ':lang_cd' => $post->lang_cd,
            ));
            $results = $query->execute();
            $ret["t_bot_msg_desc_tpl"] = $results->as_array();

            $this->response->body($this->jresult("00", null, $ret));
            return ;
        } else {
            $this->response->body($this->jresult("01"));
        }
        return;
    }

    public function action_get_msg_inf_old_not_use()
    {
        $this->response->headers('Access-Control-Allow-Origin', '*');
        $condition = $this->request->post();
        if ($condition) {
            $post = json_decode($condition["condition"]);
            if ($this->check_accesstoken($post, $post->nid) == -1) {
                return $this->response->body($this->jresult("01"));
            }

            if (isset($post->login_type) && $post->login_type == "admin") {
                // admin模式不返回任何信息
                return $this->response->body($this->jresult("01"));
            }

            $ret = array();

            // 读取 t_bot_msg_desc_txt
            $sql = 'SELECT a.bot_id,a.msg_cd, a.msg_type_cd, a.msg_id,a.msg_class_cd,a.msg_data,a.start_date,a.end_date,
							b.*
				FROM t_bot_msg a 
				INNER JOIN t_bot_msg_desc_txt b
					ON a.msg_id = b.msg_id
				WHERE
					(a.bot_id = :bot_id)
					AND (
						:lang_cd = ""
						OR b.lang_cd = :lang_cd
					)
				ORDER BY 
					bot_id DESC,msg_cd';
            $query = DB::query(Database::SELECT, $sql);
            $query->parameters(array(
                ':bot_id' => $post->bot_id,
                ':lang_cd' => $post->lang_cd,
            ));
            $results = $query->execute();
            $ret["t_bot_msg_desc_txt"] = $results->as_array();


            // 读取 t_bot_msg_desc_img
            $sql = 'SELECT a.bot_id,a.msg_cd, a.msg_type_cd, a.msg_id,a.msg_class_cd,
							b.*
				FROM t_bot_msg a 
				INNER JOIN t_bot_msg_desc_img b
					ON a.msg_id = b.msg_id
				WHERE
					(a.bot_id = :bot_id)
					AND (
						:lang_cd = ""
						OR b.lang_cd = :lang_cd
					)
				ORDER BY 
					bot_id DESC,msg_cd';
            $query = DB::query(Database::SELECT, $sql);
            $query->parameters(array(
                ':bot_id' => $post->bot_id,
                ':lang_cd' => $post->lang_cd,
            ));
            $results = $query->execute();
            $ret["t_bot_msg_desc_img"] = $results->as_array();

            // 读取 t_bot_msg_desc_car
            $sql = 'SELECT a.bot_id,a.msg_cd, a.msg_type_cd, a.msg_id,a.msg_class_cd,
							b.*
				FROM t_bot_msg a 
				INNER JOIN t_bot_msg_desc_car b
					ON a.msg_id = b.msg_id
				WHERE
					(a.bot_id = :bot_id)
					AND (
						:lang_cd = ""
						OR b.lang_cd = :lang_cd
					)
				ORDER BY 
					bot_id DESC,msg_cd,b.no';
            $query = DB::query(Database::SELECT, $sql);
            $query->parameters(array(
                ':bot_id' => $post->bot_id,
                ':lang_cd' => $post->lang_cd,
            ));
            $results = $query->execute();
            $ret["t_bot_msg_desc_car"] = $results->as_array();

            // 读取 t_bot_msg_desc_lst
            $sql = 'SELECT a.bot_id,a.msg_cd, a.msg_type_cd, a.msg_id,a.msg_class_cd,
							b.*
				FROM t_bot_msg a 
				INNER JOIN t_bot_msg_desc_lst b
					ON a.msg_id = b.msg_id
				WHERE
					(a.bot_id = :bot_id)
					AND (
						:lang_cd = ""
						OR b.lang_cd = :lang_cd
					)
				ORDER BY 
					bot_id DESC,	msg_cd, no';
            $query = DB::query(Database::SELECT, $sql);
            $query->parameters(array(
                ':bot_id' => $post->bot_id,
                ':lang_cd' => $post->lang_cd,
            ));
            $results = $query->execute();
            $ret["t_bot_msg_desc_lst"] = $results->as_array();

            // 读取 t_bot_msg_desc_tpl
            $sql = 'SELECT a.bot_id,a.msg_cd, a.msg_type_cd, a.msg_id,a.msg_class_cd,
							b.*
				FROM t_bot_msg a 
				INNER JOIN t_bot_msg_desc_tpl b
					ON a.msg_id = b.msg_id
				WHERE
					(a.bot_id = :bot_id)
					AND (
						:lang_cd = ""
						OR b.lang_cd = :lang_cd
					)
				ORDER BY 
					bot_id DESC,msg_cd';
            $query = DB::query(Database::SELECT, $sql);
            $query->parameters(array(
                ':bot_id' => $post->bot_id,
                ':lang_cd' => $post->lang_cd,
            ));
            $results = $query->execute();
            $ret["t_bot_msg_desc_tpl"] = $results->as_array();

            $this->response->body($this->jresult("00", null, $ret));
            return ;
        } else {
            $this->response->body($this->jresult("01"));
        }
        return;
    }

    // not used
    public function action_get_product_inf()
    {
        $this->response->headers('Access-Control-Allow-Origin', '*');
        $condition = $this->request->post();
        if ($condition) {
            $post = json_decode($condition["condition"]);
            if ($this->check_accesstoken($post, $post->nid) == -1) {
                return $this->response->body($this->jresult("01"));
            }

            if (isset($post->login_type) && $post->login_type == "admin") {
                // admin模式不返回任何信息
                return $this->response->body($this->jresult("01"));
            }

            $ret = array();

            // 读取
            $sql = 'SELECT a.*,"---" as split1,b.*,"---" as split2, c.*
				FROM t_product a
				LEFT JOIN t_product_description b
				ON a.product_id = b.product_id
				LEFT JOIN t_item_display c
				ON a.bot_id = c.bot_id
				AND a.product_id = c.item_id
				AND c.item_div = 5
				WHERE
					(a.bot_id = :bot_id)
					AND (
						:lang_cd = ""
						OR b.lang_cd = :lang_cd
					)
				ORDER BY
					a.class_cd,
					a.product_cd,
					c.sort_no1,
					c.sort_no2,
					c.sort_no3,
					c.sort_no4,
					c.sort_no5,
					b.product_id
				';
            /*
            $sql = 'SELECT a.product_cd,a.class_cd,a.link_key,a.delete_flg as delete_flg1,b.*,c.*
                FROM t_product a
                LEFT JOIN t_product_description b
                ON a.product_id = b.product_id
                LEFT JOIN t_item_display c
                ON a.bot_id = c.bot_id
                AND a.product_id = c.item_id
                AND c.item_div = 5
                WHERE
                    (a.bot_id = :bot_id)
                    AND (
                        :lang_cd = ""
                        OR b.lang_cd = :lang_cd
                    )
                ORDER BY
                    a.class_cd,
                    a.product_cd,
                    c.sort_no1,
                    c.sort_no2,
                    c.sort_no3,
                    c.sort_no4,
                    c.sort_no5,
                    b.product_id
                ';
            */
            $query = DB::query(Database::SELECT, $sql);
            $query->parameters(array(
                ':bot_id' => $post->bot_id,
                ':lang_cd' => $post->lang_cd,
            ));
            $results = $query->execute();
            $ret["t_bot_product"] = $results->as_array();

            $this->response->body($this->jresult("00", null, $ret));
            return ;
        } else {
            $this->response->body($this->jresult("01"));
        }
        return;
    }

    public function action_get_product_inf_by_type()
    {
        $this->response->headers('Access-Control-Allow-Origin', '*');
        $condition = $this->request->post();
        if ($condition) {
            $post = json_decode($condition["condition"]);
            if ($this->check_accesstoken($post, $post->nid) == -1) {
                return $this->response->body($this->jresult("01"));
            }

            if (isset($post->login_type) && $post->login_type == "admin") {
                // admin模式不返回任何信息
                return $this->response->body($this->jresult("01"));
            }

            $ret = array();
            $sql = 'SELECT 
                    a.link_key
                    ,a.product_id
                    ,a.bot_id
                    ,a.item_div
                    ,a.stock_id
                    ,a.product_cd
                    ,a.class_cd
                    ,a.tags
                    ,a.product_status_cd
                    ,a.country_cd
                    ,a.product_barcode
                    ,a.start_date
                    ,a.end_date
                    ,a.regular_start
                    ,a.regular_end
                    ,a.adult_num
                    ,a.child_num
                    ,a.infant_num
                    ,a.cap1_num
                    ,a.cap2_num
                    ,a.cap3_num
                    ,a.product_data
                    ,a.link_id
                    ,a.link_type_cd
                    ,a.link_data
                    ,a.discount_type
                    ,a.discount_value
                    ,a.delete_flg as delete_flg1
                    ,a.upd_user
                    ,a.upd_time
                    ,"---" as split1
                    ,b.lang_cd
                    ,b.product_name
                    ,b.description
                    ,b.notes
                    ,b.sell_point
                    ,b.product_image
                    ,b.url
                    ,b.btn1_name
                    ,b.btn1_url
                    ,b.btn1_url_lang_cd
                    ,b.btn2_name
                    ,b.btn2_url
                    ,b.btn2_url_lang_cd
                    ,b.btn3_name
                    ,b.btn3_url
                    ,b.btn3_url_lang_cd
                    ,b.last_data
                    ,b.user_edit
                    ,b.upd_user as upd_user_description
                    ,b.upd_time as upd_time_description
                    ,b.delete_flg  as delete_flg_description
                    ,"---" as split2
                    , c.*
                    FROM t_product a
				LEFT JOIN t_product_description b
				ON a.product_id = b.product_id
				LEFT JOIN t_item_display c
				ON a.bot_id = c.bot_id
				AND a.product_id = c.item_id
				AND c.item_div = a.item_div
				WHERE
					(a.bot_id = :bot_id)
					AND (
						:lang_cd = ""
						OR b.lang_cd = :lang_cd
					)
					AND a.item_div = :item_div
					AND (:class_cd = "" OR class_cd like :class_cd)
				ORDER BY
					a.link_key,
					a.product_cd,
					a.delete_flg,
					c.sort_no1,
					c.sort_no2,
					c.sort_no3,
					c.sort_no4,
					c.sort_no5,
					b.product_id
            ';

            // 读取room
            $query = DB::query(Database::SELECT, $sql);
            $query->parameters(array(
                ':bot_id' => $post->bot_id,
                ':lang_cd' => $post->lang_cd,
                ':item_div' => 5,
                ':class_cd' => '01%',
            ));
            $results = $query->execute();
            $ret["t_bot_product_room"] = $results->as_array();

            // 读取plan
            $query = DB::query(Database::SELECT, $sql);
            $query->parameters(array(
                ':bot_id' => $post->bot_id,
                ':lang_cd' => $post->lang_cd,
                ':item_div' => 5,
                ':class_cd' => '02%',
            ));
            $results = $query->execute();
            $ret["t_bot_product_plan"] = $results->as_array();

            // 读取coupon
            $query = DB::query(Database::SELECT, $sql);
            $query->parameters(array(
                ':bot_id' => $post->bot_id,
                ':lang_cd' => $post->lang_cd,
                ':item_div' => 7,
                ':class_cd' => '',
            ));
            $results = $query->execute();
            $ret["t_bot_product_coupon"] = $results->as_array();

            $this->response->body($this->jresult("00", null, $ret));
            return ;
        } else {
            $this->response->body($this->jresult("01"));
        }
        return;
    }

    public function action_get_bot_flows()
    {
        $this->response->headers('Access-Control-Allow-Origin', '*');
        $condition = $this->request->post();
        if ($condition) {
            $post = json_decode($condition["condition"]);
            if ($this->check_accesstoken($post, $post->nid) == -1) {
                return $this->response->body($this->jresult("01"));
            }

            $item_info = $this->getBotInfo($post->facility_cd);

            if (isset($post->login_type) && $post->login_type == "admin") {
                $bot_id = $this->check_accesstoken($post, $post->nid);
                if ($item_info['bot_id'] != $bot_id) {
                    // 试图读取别人的bot的内容
                    $this->response->body($this->jresult("01"));
                    return;
                }
            }

            $flow_data = $this->search_flows($item_info['bot_id'], -1, $post);
            $ret = array();
            $ret["flow_data"] = $flow_data;

            $this->response->body($this->jresult("00", null, $ret));
        } else {
            $this->response->body($this->jresult("01"));
        }
        return;
    }

    public function action_get_autoanswer_flow()
    {
        $this->response->headers('Access-Control-Allow-Origin', '*');
        $condition = $this->request->post();
        if ($condition) {
            $post = json_decode($condition["condition"]);
            if ($this->check_accesstoken($post, $post->nid) == -1) {
                return $this->response->body($this->jresult("01"));
            }
            //error_log($json_string);
            //error_log($post["lang_cd"]);
            //$json_obj = json_decode($post["lang_cd"]);
            //error_log($post->lang_cd->cd2->wuwuwu);
            //error_log($post->facility_cd);

            $item_info = $this->getBotInfo($post->facility_cd);
            $bot_id = $item_info["bot_id"];

            if (isset($post->login_type) && $post->login_type == "admin") {
                $bot_id = $this->check_accesstoken($post, $post->nid);
                if ($item_info['bot_id'] != $bot_id) {
                    // 试图读取别人的bot的内容
                    $this->response->body($this->jresult("01"));
                    return;
                }
            }

            // 2021.10.23 begin
            $base_bot_id = 0;
            $parent_bot_id = $this->get_grp_parent_bot_id($bot_id);

            $next_bot_id = $post->next_bot_id;
            if (isset($post->login_type) && $post->login_type == "admin") {
                if ($next_bot_id != $bot_id && $next_bot_id != $base_bot_id && $next_bot_id != $parent_bot_id) {
                    // 试图读取别人的bot的内容
                    $this->response->body($this->jresult("01"));
                    return;
                }
            }

            $flow_data = $this->search_flow($next_bot_id, -1, $post);
            $node_data = $this->search_node($next_bot_id, -1, $post);
            $action_data = $this->search_action($next_bot_id, -1, $post);
            $text_data = $this->search_text($next_bot_id, -1, $post);
            $ret = array();
            $ret["flow_data"] = $flow_data;
            $ret["node_data"] = $node_data;
            $ret["action_data"] = $action_data;
            $ret["text_data"] = $text_data;

            $this->response->body($this->jresult("00", null, $ret));
        // 2021.10.23 end

            /*
            $flow_data = $this->search_flow($item_info['bot_id'], -1, $post);
            $node_data = $this->search_node($item_info['bot_id'], -1, $post);
            $action_data = $this->search_action($item_info['bot_id'], -1, $post);
            $text_data = $this->search_text($item_info['bot_id'], -1, $post);
            $ret = array();
            $ret["flow_data"] = $flow_data;
            $ret["node_data"] = $node_data;
            $ret["action_data"] = $action_data;
            $ret["text_data"] = $text_data;

            $this->response->body($this->jresult("00", null, $ret));
            */
        } else {
            $this->response->body($this->jresult("01"));
        }
        return;
    }

    public function action_save_autoanswer_flow()
    {
        $this->response->headers('Access-Control-Allow-Origin', '*');
        $condition = $this->request->post();
        if ($condition) {
            $post = json_decode($condition["condition"]);
            $data = $post->data;
            //error_log($post);
            if ($this->check_modify_accesstoken($post, $post->nid) == -1) {
                return $this->response->body($this->jresult("06"));
            }

            // 2021.10.23 begin
            {
                $first_bot_id = $data->flow_data[0]->bot_id;
                $length = count($data->node_data);
                for ($x=0; $x<$length; $x++) {
                    $cur_data = $data->node_data[$x];
                    if ($cur_data->bot_id != $first_bot_id) {
                        // 数据不一致
                        $this->response->body($this->jresult("08"));
                        return;
                    }
                }

                $length = count($data->action_data);
                for ($x=0; $x<$length; $x++) {
                    $cur_data = $data->action_data[$x];
                    if ($cur_data->bot_id != $first_bot_id) {
                        // 数据不一致
                        $this->response->body($this->jresult("08"));
                        return;
                    }
                }

                $length = count($data->text_data);
                for ($x=0; $x<$length; $x++) {
                    $cur_data = $data->text_data[$x];
                    if ($cur_data->bot_id != $first_bot_id) {
                        // 数据不一致
                        $this->response->body($this->jresult("08"));
                        return;
                    }
                }
            }
            // 2021.10.23 end

            if (isset($post->login_type) && $post->login_type == "admin") {
                $bot_id = $this->check_accesstoken($post, $post->nid);
                if ($data->flow_data[0]->bot_id != $bot_id) {
                    // 试图写别人的bot的内容
                    $this->response->body($this->jresult("01"));
                    return;
                }

                $length = count($data->node_data);
                for ($x=0; $x<$length; $x++) {
                    $cur_data = $data->node_data[$x];
                    if ($cur_data->bot_id != $bot_id) {
                        // 试图写别人的bot的内容
                        $this->response->body($this->jresult("01"));
                        return;
                    }
                }

                $length = count($data->action_data);
                for ($x=0; $x<$length; $x++) {
                    $cur_data = $data->action_data[$x];
                    if ($cur_data->bot_id != $bot_id) {
                        // 试图写别人的bot的内容
                        $this->response->body($this->jresult("01"));
                        return;
                    }
                }

                $length = count($data->text_data);
                for ($x=0; $x<$length; $x++) {
                    $cur_data = $data->text_data[$x];
                    if ($cur_data->bot_id != $bot_id) {
                        // 试图写别人的bot的内容
                        $this->response->body($this->jresult("01"));
                        return;
                    }
                }
            }


            try {
                //error_log("will start transaction");
                //DB::query(NULL,'START TRANSACTION');
                Database::instance()->begin();
                $data = $post->data;
                //$ret = $this->delete_flow($data->flow_data[0]->bot_id, $data->flow_data[0]->item_id, $post->old_flow_id, $data->flow_data[0]->update_time);
                $ret = $this->delete_flow($data->flow_data[0]->bot_id, -1, $post->old_flow_id, $data->flow_data[0]->update_time);
                //$ret = $this->delete_flow($data->flow_data[0]->bot_id, $data->flow_data[0]->item_id, $data->flow_data[0]->flow_id);
                if ($ret != "00") {
                    //error_log("will rollback");
                    Database::instance()->rollback();
                    //DB::query(NULL,'ROLLBACK');
                    return $this->response->body($this->jresult("07"));
                }

                $ret = $this->save_flow($data->flow_data[0]);
                if ($ret == "01") {
                    //error_log("will rollback");
                    Database::instance()->rollback();
                    //DB::query(NULL,'ROLLBACK');
                    return $this->response->body($this->jresult("02"));
                }
                $new_update_time = $ret;
                $ret = $this->save_node($data->node_data);
                if ($ret != "00") {
                    //error_log("will rollback");
                    Database::instance()->rollback();
                    //DB::query(NULL,'ROLLBACK');
                    return $this->response->body($this->jresult("03"));
                }
                $ret = $this->save_action($data->action_data);
                if ($ret != "00") {
                    //error_log("will rollback");
                    //DB::query(NULL,'ROLLBACK');
                    Database::instance()->rollback();
                    return $this->response->body($this->jresult("04"));
                }
                $ret = $this->save_text($data->text_data);
                if ($ret != "00") {
                    //error_log("will rollback");
                    //DB::query(NULL,'ROLLBACK');
                    Database::instance()->rollback();
                    return $this->response->body($this->jresult("05"));
                }

                $this->response->body($this->jresult("00", null, $new_update_time));
                //error_log("will commit");
                //DB::query(NULL,'COMMIT');
                Database::instance()->commit();
            } catch (Exception $e) {
                //error_log("will rollback");
                //DB::query(NULL,'ROLLBACK');
                Database::instance()->rollback();
                error_log($e->getMessage());
                $this->response->body($this->jresult("01", null));
            }
        } else {
            $this->response->body($this->jresult("01"));
        }
        return;
    }

    public function action_delete_autoanswer_flow()
    {
        $this->response->headers('Access-Control-Allow-Origin', '*');
        $condition = $this->request->post();
        if ($condition) {
            try {
                //DB::query(Database::UPDATE,'START TRANSACTION');
                Database::instance()->begin();
                $post = json_decode($condition["condition"]);
                if ($this->check_modify_accesstoken($post, $post->nid) == -1) {
                    return $this->response->body($this->jresult("01"));
                }

                if (isset($post->login_type) && $post->login_type == "admin") {
                    $bot_id = $this->check_accesstoken($post, $post->nid);
                    if ($post->bot_id != $bot_id) {
                        // 试图写别人的bot的内容
                        $this->response->body($this->jresult("01"));
                        return;
                    }
                }

                //$ret = $this->delete_flow($post->bot_id,$post->item_id,$post->flow_id, $post->update_time);
                $ret = $this->delete_flow($post->bot_id, -1, $post->flow_id, $post->update_time);

                if ($ret != "00") {
                    //error_log("will rollback");
                    Database::instance()->rollback();
                    //DB::query(NULL,'ROLLBACK');
                    return $this->response->body($this->jresult("07"));
                }

                //DB::query(Database::UPDATE,'COMMIT');
                Database::instance()->commit();
                $this->response->body($this->jresult("00", null));
            } catch (Exception $e) {
                //DB::query(Database::UPDATE,'ROLLBACK');
                Database::instance()->rollback();
                error_log($e->getMessage());
                $this->response->body($this->jresult("01"));
            }
        } else {
            $this->response->body($this->jresult("01"));
        }
        return;
    }

    private function delete_flow($bot_id, $item_id, $flow_id, $update_time)
    {
        if ($update_time == null) {
            // 新规flow,不需要删除
            return "00";
        }
        //					AND item_id = :item_id
        $sql = "DELETE FROM t_autoanswer_flow
					WHERE
						bot_id = :bot_id
					AND flow_id = :flow_id
					AND update_time = :update_time
					";
        $query = DB::query(Database::UPDATE, $sql);
        $query->parameters(array(
                ':bot_id' => $bot_id,
//				':item_id' => $item_id,
                ':flow_id' => $flow_id,
                ':update_time'=> $update_time,
            ));
        $ret = $query->execute();
        if ($ret != 1) {
            // 要删除的不存在
            return "01";
        }

        //			AND item_id = :item_id
        $sql = "DELETE FROM t_autoanswer_node
					WHERE
						bot_id = :bot_id
					AND flow_id = :flow_id
					";
        $query = DB::query(Database::UPDATE, $sql);
        $query->parameters(array(
                ':bot_id' => $bot_id,
//				':item_id' => $item_id,
                ':flow_id' => $flow_id,
            ));
        $ret = $query->execute();

        //			AND item_id = :item_id
        $sql = "DELETE FROM t_autoanswer_action
					WHERE
						bot_id = :bot_id
					AND flow_id = :flow_id
					";
        $query = DB::query(Database::UPDATE, $sql);
        $query->parameters(array(
                ':bot_id' => $bot_id,
//				':item_id' => $item_id,
                ':flow_id' => $flow_id,
            ));
        $ret = $query->execute();

        //			AND item_id = :item_id
        $sql = "DELETE FROM t_autoanswer_text
					WHERE
						bot_id = :bot_id
					AND flow_id = :flow_id
					";
        $query = DB::query(Database::UPDATE, $sql);
        $query->parameters(array(
                ':bot_id' => $bot_id,
//				':item_id' => $item_id,
                ':flow_id' => $flow_id,
            ));
        $ret = $query->execute();

        return "00";
    }

    private function save_flow($data)
    {
        try {
            if (isset($data->mail_title)) {
                // 2023.04.25 #43479
                $sql = "INSERT INTO t_autoanswer_flow
                        (bot_id, flow_id, start_node_id, intent_cd, `description`, interruptible_flg, flow_text, mail_title, flow_status_flg)
                        VALUES
                        (:bot_id, :flow_id, :start_node_id, :intent_cd, :param_description, :interruptible_flg, :flow_text, :mail_title, :flow_status_flg) 
                        ";
                $query = DB::query(Database::UPDATE, $sql);
                $query->parameters(array(
                    ':bot_id' => $data->bot_id,
    //				':item_id' => $data->item_id,
                    ':flow_id' => $data->flow_id,
                    ':start_node_id' => $data->start_node_id,
                    ':intent_cd' => $data->intent_cd,
                    ':param_description' => $data->description,
                    ':interruptible_flg' => $data->interruptible_flg,
                    ':flow_text' => $data->flow_text,
                    ':mail_title' => $data->mail_title,
                    ':flow_status_flg' => $data->flow_status_flg,
                ));
            } else {
                $sql = "INSERT INTO t_autoanswer_flow
                        (bot_id, flow_id, start_node_id, intent_cd, `description`, interruptible_flg, flow_text, flow_status_flg)
                        VALUES
                        (:bot_id, :flow_id, :start_node_id, :intent_cd, :param_description, :interruptible_flg, :flow_text, :flow_status_flg) 
                        ";
                $query = DB::query(Database::UPDATE, $sql);
                $query->parameters(array(
                    ':bot_id' => $data->bot_id,
    //				':item_id' => $data->item_id,
                    ':flow_id' => $data->flow_id,
                    ':start_node_id' => $data->start_node_id,
                    ':intent_cd' => $data->intent_cd,
                    ':param_description' => $data->description,
                    ':interruptible_flg' => $data->interruptible_flg,
                    ':flow_text' => $data->flow_text,
                    ':flow_status_flg' => $data->flow_status_flg,
                ));
            }
            

            $ret = $query->execute();

            // 取出time stamp
            //			AND item_id = :item_id
            $sql = "SELECT update_time FROM t_autoanswer_flow
					WHERE
						bot_id = :bot_id
						AND flow_id = :flow_id
					";
            $query = DB::query(Database::SELECT, $sql);
            $query->parameters(array(
                ':bot_id' => $data->bot_id,
//				':item_id' => $data->item_id,
                ':flow_id' => $data->flow_id,
            ));
            $ret = $query->execute()->as_array();

            if (count($ret) > 0) {
                $ret = $ret[0]["update_time"];
            } else {
                $ret = "01";
            }

            return $ret;
        } catch (Exception $e) {
            error_log($e->getMessage());
            return "01";
        }
    }
    private function save_node($datas)
    {
        try {
            $length = count($datas);
            //error_log("node data size: $length");
            for ($x=0; $x<$length; $x++) {
                $data = $datas[$x];
                //error_log("node id is " . $data->node_id);
                $node_interruptible_flg = $data->node_interruptible_flg;
                if ($node_interruptible_flg == "") {
                    $node_interruptible_flg = null;
                }
                // 2023.01.26 begin
                if (isset($data->node_register_log_id_flg)) {
                    $node_register_log_id_flg = $data->node_register_log_id_flg;
                } else {
                    $node_register_log_id_flg = 0;
                }
                // 2023.01.26 end

                $sql = "INSERT INTO t_autoanswer_node
						(bot_id,  flow_id, node_id, node_text, node_post_text, node_memo, node_interruptible_flg, node_enter_status, node_register_log_id_flg)
						VALUES
						(:bot_id, :flow_id, :node_id, :node_text, :node_post_text, :node_memo, :node_interruptible_flg, :node_enter_status, :node_register_log_id_flg) 
						";
                $query = DB::query(Database::UPDATE, $sql);
                $query->parameters(array(
                    ':bot_id' => $data->bot_id,
//					':item_id' => $data->item_id,
                    ':flow_id' => $data->flow_id,
                    ':node_id' => $data->node_id,
                    ':node_text' => $data->node_text,
                    ':node_post_text' => $data->node_post_text,
                    ':node_memo' => $data->node_memo,
                    ':node_interruptible_flg' => $node_interruptible_flg,
                    ':node_enter_status' =>  $data->node_enter_status,
                    ':node_register_log_id_flg' => $node_register_log_id_flg,
                ));

                $ret = $query->execute();
            }
            //error_log("node data finish");
            return "00";
        } catch (Exception $e) {
            //error_log("node data error");
            error_log($e->getMessage());
            return "01";
        }
    }
    private function save_action($datas)
    {
        try {
            $length = count($datas);
            //error_log("action data size: $length");
            for ($x=0; $x<$length; $x++) {
                $data = $datas[$x];
                //error_log("node id is " . $data->node_id);
                //error_log("action id is " . $data->action_id);

                $sql = "INSERT INTO t_autoanswer_action
						(bot_id, flow_id, node_id, action_id, action_type,action_parameters,
						action_image,action_text,action_input_parameter_name,action_next_node_id,
						action_next_skill,action_memo,action_input_parameter_value,
						action_input_check_expression,action_next_node_id_when_cancel,action_input_parameter_type,
						action_next_skill_when_cancel,action_show_cancel_flg, action_can_click_forever, action_execute_status)
						VALUES
						(:bot_id, :flow_id, :node_id, :action_id, :action_type,:action_parameters,
						:action_image,:action_text,:action_input_parameter_name,:action_next_node_id,
						:action_next_skill,:action_memo,:action_input_parameter_value,
						:action_input_check_expression,:action_next_node_id_when_cancel,:action_input_parameter_type,
						:action_next_skill_when_cancel,:action_show_cancel_flg, :action_can_click_forever, :action_execute_status
						) 
						";
                $query = DB::query(Database::UPDATE, $sql);
                if ($data->action_next_node_id == "") {
                    $data->action_next_node_id = null;
                }
                if ($data->action_next_node_id_when_cancel == "") {
                    $data->action_next_node_id_when_cancel = null;
                }
                if ($data->action_show_cancel_flg == "") {
                    $data->action_show_cancel_flg = null;
                }
                if ($data->action_can_click_forever == "") {
                    $data->action_can_click_forever = 1;
                }
                $query->parameters(array(
                    ':bot_id' => $data->bot_id,
//					':item_id' => $data->item_id,
                    ':flow_id' => $data->flow_id,
                    ':node_id' => $data->node_id,
                    ':action_id' => $data->action_id,
                    ':action_type' => $data->action_type,
                    ':action_parameters' => $data->action_parameters,
                    ':action_image' => $data->action_image,
                    ':action_text' => $data->action_text,
                    ':action_input_parameter_name' => $data->action_input_parameter_name,
                    ':action_next_node_id' => $data->action_next_node_id,
                    ':action_next_skill' => $data->action_next_skill,
                    ':action_memo' => $data->action_memo,
                    ':action_input_parameter_value' => $data->action_input_parameter_value,
                    ':action_input_check_expression' => $data->action_input_check_expression,
                    ':action_next_node_id_when_cancel' => $data->action_next_node_id_when_cancel,
                    ':action_input_parameter_type' => $data->action_input_parameter_type,
                    ':action_next_skill_when_cancel' => $data->action_next_skill_when_cancel,
                    ':action_show_cancel_flg' => $data->action_show_cancel_flg,
                    ':action_can_click_forever' => $data->action_can_click_forever,
                    ':action_execute_status' => $data->action_execute_status,
                ));

                $ret = $query->execute();
            }
            return "00";
        } catch (Exception $e) {
            error_log($e->getMessage());
            return "01";
        }
    }
    private function save_text($datas)
    {
        try {
            $length = count($datas);
            //error_log("text data size: $length");
            for ($x=0; $x<$length; $x++) {
                $data = $datas[$x];
                //error_log("text id is " . $data->text_id);
                // 2021.04.06 chenshi要求增加 默认text_id, 所以为空的数据也一起保存到DB
                if ($data->content == null) {
                    $data->content = "";
                }

                $sql = "INSERT INTO t_autoanswer_text
						(bot_id, flow_id, text_id, lang_cd, content)
						VALUES
						(:bot_id, :flow_id, :text_id, :lang_cd, :content) 
						";
                $query = DB::query(Database::UPDATE, $sql);
                $query->parameters(array(
                    ':bot_id' => $data->bot_id,
//					':item_id' => $data->item_id,
                    ':flow_id' => $data->flow_id,
                    ':text_id' => $data->text_id,
                    ':lang_cd' => $data->lang_cd,
                    ':content' => $data->content,
                ));

                $ret = $query->execute();
            }
            return "00";
        } catch (Exception $e) {
            error_log($e->getMessage());
            return "01";
        }
    }

    // 检索某bot的所有flow
    public function search_flows($bot_id, $item_id, $post)
    {
        /*
        //		AND a.item_id = :item_id
        $sql = "SELECT a.*
                    FROM t_autoanswer_flow a
                    WHERE
                        a.bot_id = :bot_id
                    ORDER BY
                        a.flow_id
                    ";

        $query = DB::query(Database::SELECT, $sql);
        $query->parameters(array(
                ':bot_id' => $bot_id,
//				':item_id' => $item_id,
            ));

        $results = $query->execute();
        return $results->as_array();
        */
        // 2021.10.13 改为连同base，父的一起读
        if (isset($post->login_type) && $post->login_type == "admin") {
            // 管理模式需要join t_bot_def_intent
            $base_bot_id = 0;
            $parent_bot_id = $this->get_grp_parent_bot_id($bot_id);
            $sql = "SELECT b.*, bot_all_flow.bot_ids_have_flow, :base_bot_id as base_bot_id, :parent_bot_id as parent_bot_id FROM
                (SELECT a.flow_id, MAX(a.bot_id) AS bot_id_max, group_concat(a.bot_id) as bot_ids_have_flow
				FROM t_autoanswer_flow a
                LEFT JOIN t_bot_def_intent b
                    ON b.bot_id = :bot_id
    				AND b.intent_cd = a.intent_cd
                LEFT JOIN t_bot_def_intent c
                    ON c.bot_id = :parent_bot_id
    				AND c.intent_cd = a.intent_cd
				WHERE
					a.bot_id = :bot_id
                    OR (a.bot_id = :base_bot_id and b.intent_cd = a.intent_cd)
                    OR (a.bot_id = :base_bot_id and c.intent_cd = a.intent_cd)
                    OR a.bot_id = :parent_bot_id
                GROUP BY
                    a.flow_id) bot_all_flow
                INNER JOIN t_autoanswer_flow b
                    ON bot_all_flow.bot_id_max = b.bot_id
                    AND bot_all_flow.flow_id = b.flow_id
				ORDER BY
					b.flow_id
				";

            $query = DB::query(Database::SELECT, $sql);
            $query->parameters(array(
                ':bot_id' => $bot_id,
                ':base_bot_id' => $base_bot_id,
                ':parent_bot_id' => $parent_bot_id,
            ));

            $results = $query->execute();
            return $results->as_array();
        } else {
            $base_bot_id = 0;
            $parent_bot_id = $this->get_grp_parent_bot_id($bot_id);
            $sql = "SELECT b.*, bot_all_flow.bot_ids_have_flow, :base_bot_id as base_bot_id, :parent_bot_id as parent_bot_id FROM
                (SELECT a.flow_id, MAX(a.bot_id) AS bot_id_max, group_concat(a.bot_id) as bot_ids_have_flow
				FROM t_autoanswer_flow a
				WHERE
					a.bot_id = :bot_id
                    OR a.bot_id = :base_bot_id
                    OR a.bot_id = :parent_bot_id
                GROUP BY
                    a.flow_id) bot_all_flow
                INNER JOIN t_autoanswer_flow b
                    ON bot_all_flow.bot_id_max = b.bot_id
                    AND bot_all_flow.flow_id = b.flow_id
				ORDER BY
					b.flow_id
				";

            $query = DB::query(Database::SELECT, $sql);
            $query->parameters(array(
                ':bot_id' => $bot_id,
                ':base_bot_id' => $base_bot_id,
                ':parent_bot_id' => $parent_bot_id,
            ));

            $results = $query->execute();
            return $results->as_array();
        }
    }

    // 检索某bot的指定flow_id的单个flow
    public function search_flow($bot_id, $item_id, $post)
    {
        //		AND a.item_id = :item_id
        $sql = "SELECT a.*
					FROM t_autoanswer_flow a
					WHERE
						a.bot_id = :bot_id
						AND a.flow_id = :flow_id
					";

        $query = DB::query(Database::SELECT, $sql);
        $query->parameters(array(
                ':bot_id' => $bot_id,
//				':item_id' => $item_id,
                ':flow_id' => $post->flow_id,
            ));

        $results = $query->execute();
        return $results->as_array();
    }

    public function search_node($bot_id, $item_id, $post)
    {
        //		AND a.item_id = :item_id
        $sql = "SELECT a.*
					FROM t_autoanswer_node a
					WHERE
						a.bot_id = :bot_id
						AND a.flow_id = :flow_id
					";

        $sql = "$sql ORDER BY a.node_id ";

        $query = DB::query(Database::SELECT, $sql);
        $query->parameters(array(
                ':bot_id' => $bot_id,
//				':item_id' => $item_id,
                ':flow_id' => $post->flow_id,
            ));

        $results = $query->execute();
        return $results->as_array();
    }

    public function search_action($bot_id, $item_id, $post)
    {
        //						AND a.item_id = :item_id
        $sql = "SELECT a.*
					FROM  t_autoanswer_action a
					WHERE
						a.bot_id = :bot_id
						AND a.flow_id = :flow_id
					ORDER BY
						a.node_id,a.action_id
					";

        $query = DB::query(Database::SELECT, $sql);
        $query->parameters(array(
                ':bot_id' => $bot_id,
//				':item_id' => $item_id,
                ':flow_id' => $post->flow_id,
            ));

        $results = $query->execute();
        return $results->as_array();
    }

    public function search_text($bot_id, $item_id, $post)
    {
        //						AND d.item_id = :item_id
        $sql = "SELECT d.*
					FROM  t_autoanswer_text d
					WHERE
						d.bot_id = :bot_id
						AND d.flow_id = :flow_id
					";

        $query = DB::query(Database::SELECT, $sql);
        $query->parameters(array(
                ':bot_id' => $bot_id,
//				':item_id' => $item_id,
                ':flow_id' => $post->flow_id,
            ));

        $results = $query->execute();
        return $results->as_array();
    }

    // 此函数现在不用
    public function get_autoanswer_flow($bot_id, $item_id, $post)
    {
        $node_id = null;
        if (array_key_exists("node_id", $post)) {
            $node_id = $post["node_id"];
        }

        //						AND a.item_id = b.item_id
        //						AND a.item_id = c.item_id
        //						AND a.item_id = :item_id
        $sql = "SELECT a.bot_id, a.flow_id, a.start_node_id, a.intent_cd,
							b.node_id, b.node_text, b.node_post_test,
							c.action_id, c.action_type, c.action_parameters, c.action_image, c.action_text, c.action_next_node_id, c.action_next_skill, c.action_input_parameter_name, c.action_input_parameter_value,
							c.action_input_check_expression,c.action_input_parameter_type,c.action_next_node_id_when_cancel,c.action_next_skill_when_cancel,c.action_show_cancel_flg,c.action_can_click_forever
					FROM t_autoanswer_flow a
					INNER JOIN t_autoanswer_node b
						ON a.bot_id = b.bot_id
						AND a.flow_id = b.flow_id
					LEFT JOIN t_autoanswer_action c
						ON a.bot_id = c.bot_id
						AND a.flow_id = c.flow_id
						AND b.node_id = c.node_id
					WHERE
						a.bot_id = :bot_id
						AND a.flow_id = :flow_id
					";
        if (array_key_exists("node_id", $post)) {
            $sql = "$sql AND b.node_id = :node_id ";
        } else {
            $sql = "$sql AND b.node_id = a.start_node_id ";
        }

        $sql = "$sql ORDER BY c.action_id ";

        $query = DB::query(Database::SELECT, $sql);
        $query->parameters(array(
                ':bot_id' => $bot_id,
//				':item_id' => $item_id,
                ':flow_id' => $post['flow_id'],
                ':node_id' => $node_id,
            ));

        $results = $query->execute();
        return $results->as_array();
    }


    public function action_get_survey_data()
    {
        $this->response->headers('Access-Control-Allow-Origin', '*');
        $condition = $this->request->post();
        if ($condition) {
            $post = json_decode($condition["condition"]);
            if ($this->check_accesstoken($post, $post->nid) == -1) {
                return $this->response->body($this->jresult("01"));
            }

            if (isset($post->login_type) && $post->login_type == "admin") {
                // admin模式不返回任何信息
                return $this->response->body($this->jresult("01"));
            }

            $ret = array();

            // 读取t_survey
            $sql = 'SELECT a.survey_id,a.survey_name
                    , (CASE WHEN (start_date IS NULL OR start_date = "") AND (end_date IS NULL OR end_date = "") THEN ""
                            WHEN (start_date IS NULL OR start_date = "") AND (now() <= end_date) THEN ""
                            WHEN (start_date <= now()) AND (end_date IS NULL OR end_date = "") THEN ""
                            WHEN !(start_date IS NULL OR start_date = "") AND !(end_date IS NULL OR end_date = "") AND (start_date <= now()) AND (now() <= end_date) THEN ""
                            ELSE "(out of date)"
                            END
                    ) AS survey_status
				FROM t_survey a
				WHERE
					(a.bot_id = :bot_id)
				ORDER BY
					a.survey_id
				';
            $query = DB::query(Database::SELECT, $sql);
            $query->parameters(array(
                ':bot_id' => $post->bot_id,
                ':lang_cd' => $post->lang_cd,
            ));
            $results = $query->execute();
            $ret["survey_id_list"] = $results->as_array();

            // 读取t_survey
            $sql = 'SELECT *
                    , (CASE WHEN (start_date IS NULL OR start_date = "") AND (end_date IS NULL OR end_date = "") THEN 0
                            WHEN (start_date IS NULL OR start_date = "") AND (now() <= end_date) THEN 0
                            WHEN (start_date <= now()) AND (end_date IS NULL OR end_date = "") THEN 0
                            WHEN !(start_date IS NULL OR start_date = "") AND !(end_date IS NULL OR end_date = "") AND (start_date <= now()) AND (now() <= end_date) THEN 0
                            ELSE 1
                            END
                    ) AS _flg_not_use
				FROM t_survey a
				WHERE
					(a.bot_id = :bot_id)
				ORDER BY
					a.survey_id
				';
            $query = DB::query(Database::SELECT, $sql);
            $query->parameters(array(
                ':bot_id' => $post->bot_id,
                ':lang_cd' => $post->lang_cd,
            ));
            $results = $query->execute();
            $ret["t_survey"] = $results->as_array();

            // 读取t_survey_branch
            $sql = 'SELECT b.*
				FROM t_survey_branch b
				INNER JOIN t_survey a
				ON a.survey_id = b.survey_id
				WHERE
					(a.bot_id = :bot_id)
				ORDER BY
					a.survey_id,
					b.no
				';
            $query = DB::query(Database::SELECT, $sql);
            $query->parameters(array(
                ':bot_id' => $post->bot_id,
                ':lang_cd' => $post->lang_cd,
            ));
            $results = $query->execute();
            $ret["t_survey_branch"] = $results->as_array();

            // 读取t_survey_description
            $sql = 'SELECT b.*
				FROM t_survey_description b
				INNER JOIN t_survey a
				ON a.survey_id = b.survey_id
				WHERE
					(a.bot_id = :bot_id)
					AND (
						:lang_cd = ""
						OR b.lang_cd = :lang_cd
					)
				ORDER BY
					a.survey_id
				';
            $query = DB::query(Database::SELECT, $sql);
            $query->parameters(array(
                ':bot_id' => $post->bot_id,
                ':lang_cd' => $post->lang_cd,
            ));
            $results = $query->execute();
            $ret["t_survey_description"] = $results->as_array();

            // 读取t_survey_entry
            $sql = 'SELECT b.*
				FROM t_survey_entry b
				INNER JOIN t_survey a
				ON a.survey_id = b.survey_id
				WHERE
					(a.bot_id = :bot_id)
					AND (
						:lang_cd = ""
						OR b.lang_cd = :lang_cd
					)
				ORDER BY
					a.survey_id,
					b.no
				';
            $query = DB::query(Database::SELECT, $sql);
            $query->parameters(array(
                ':bot_id' => $post->bot_id,
                ':lang_cd' => $post->lang_cd,
            ));
            $results = $query->execute();
            $ret["t_survey_entry"] = $results->as_array();

            // 读取t_survey_section
            $sql = 'SELECT b.*
				FROM t_survey_section b
				INNER JOIN t_survey a
				ON a.survey_id = b.survey_id
				WHERE
					(a.bot_id = :bot_id)
				ORDER BY
					a.survey_id,
					b.no
				';
            $query = DB::query(Database::SELECT, $sql);
            $query->parameters(array(
                ':bot_id' => $post->bot_id,
                ':lang_cd' => $post->lang_cd,
            ));
            $results = $query->execute();
            $ret["t_survey_section"] = $results->as_array();

            $this->response->body($this->jresult("00", null, $ret));
            return ;
        } else {
            $this->response->body($this->jresult("01"));
        }
        return;
    }

    public function action_get_inquiry_data()
    {
        $this->response->headers('Access-Control-Allow-Origin', '*');
        $condition = $this->request->post();
        if ($condition) {
            $post = json_decode($condition["condition"]);
            if ($this->check_accesstoken($post, $post->nid) == -1) {
                return $this->response->body($this->jresult("01"));
            }

            if (isset($post->login_type) && $post->login_type == "admin") {
                // admin模式不返回任何信息
                return $this->response->body($this->jresult("01"));
            }

            $ret = array();

            // 读取t_survey
            $sql = 'SELECT a.inquiry_id,a.inquiry_name
                    , (CASE WHEN (start_date IS NULL OR start_date = "") AND (end_date IS NULL OR end_date = "") THEN ""
                            WHEN (start_date IS NULL OR start_date = "") AND (now() <= end_date) THEN ""
                            WHEN (start_date <= now()) AND (end_date IS NULL OR end_date = "") THEN ""
                            WHEN !(start_date IS NULL OR start_date = "") AND !(end_date IS NULL OR end_date = "") AND (start_date <= now()) AND (now() <= end_date) THEN ""
                            ELSE "(out of date)"
                            END
                    ) AS inquiry_status
				FROM t_inquiry a
				WHERE
					(a.bot_id = :bot_id)
				ORDER BY
					a.inquiry_id
				';
            $query = DB::query(Database::SELECT, $sql);
            $query->parameters(array(
                ':bot_id' => $post->bot_id,
                ':lang_cd' => $post->lang_cd,
            ));
            $results = $query->execute();
            $ret["inquiry_id_list"] = $results->as_array();

            // 读取t_inquiry
            $sql = 'SELECT *
                    , (CASE WHEN (start_date IS NULL OR start_date = "") AND (end_date IS NULL OR end_date = "") THEN 0
                            WHEN (start_date IS NULL OR start_date = "") AND (now() <= end_date) THEN 0
                            WHEN (start_date <= now()) AND (end_date IS NULL OR end_date = "") THEN 0
                            WHEN !(start_date IS NULL OR start_date = "") AND !(end_date IS NULL OR end_date = "") AND (start_date <= now()) AND (now() <= end_date) THEN 0
                            ELSE 1
                            END
                    ) AS _flg_not_use
				FROM t_inquiry a
				WHERE
					(a.bot_id = :bot_id)
				ORDER BY
					a.inquiry_id
				';
            $query = DB::query(Database::SELECT, $sql);
            $query->parameters(array(
                ':bot_id' => $post->bot_id,
                ':lang_cd' => $post->lang_cd,
            ));
            $results = $query->execute();
            $ret["t_inquiry"] = $results->as_array();

            // 读取t_inquiry_branch
            $sql = 'SELECT b.*
				FROM t_inquiry_branch b
				INNER JOIN t_inquiry a
				ON a.inquiry_id = b.inquiry_id
				WHERE
					(a.bot_id = :bot_id)
				ORDER BY
					a.inquiry_id,
					b.no
				';
            $query = DB::query(Database::SELECT, $sql);
            $query->parameters(array(
                ':bot_id' => $post->bot_id,
                ':lang_cd' => $post->lang_cd,
            ));
            $results = $query->execute();
            $ret["t_inquiry_branch"] = $results->as_array();

            // 读取t_inquiry_description
            $sql = 'SELECT b.*
				FROM t_inquiry_description b
				INNER JOIN t_inquiry a
				ON a.inquiry_id = b.inquiry_id
				WHERE
					(a.bot_id = :bot_id)
					AND (
						:lang_cd = ""
						OR b.lang_cd = :lang_cd
					)
				ORDER BY
					a.inquiry_id
				';
            $query = DB::query(Database::SELECT, $sql);
            $query->parameters(array(
                ':bot_id' => $post->bot_id,
                ':lang_cd' => $post->lang_cd,
            ));
            $results = $query->execute();
            $ret["t_inquiry_description"] = $results->as_array();

            // 读取t_inquiry_entry
            $sql = 'SELECT b.*
				FROM t_inquiry_entry b
				INNER JOIN t_inquiry a
				ON a.inquiry_id = b.inquiry_id
				WHERE
					(a.bot_id = :bot_id)
					AND (
						:lang_cd = ""
						OR b.lang_cd = :lang_cd
					)
				ORDER BY
					a.inquiry_id,
					b.no
				';
            $query = DB::query(Database::SELECT, $sql);
            $query->parameters(array(
                ':bot_id' => $post->bot_id,
                ':lang_cd' => $post->lang_cd,
            ));
            $results = $query->execute();
            $ret["t_inquiry_entry"] = $results->as_array();

            // 读取t_inquiry_section
            $sql = 'SELECT b.*
				FROM t_inquiry_section b
				INNER JOIN t_inquiry a
				ON a.inquiry_id = b.inquiry_id
				WHERE
					(a.bot_id = :bot_id)
				ORDER BY
					a.inquiry_id,
					b.no
				';
            $query = DB::query(Database::SELECT, $sql);
            $query->parameters(array(
                ':bot_id' => $post->bot_id,
                ':lang_cd' => $post->lang_cd,
            ));
            $results = $query->execute();
            $ret["t_inquiry_section"] = $results->as_array();

            $this->response->body($this->jresult("00", null, $ret));
            return ;
        } else {
            $this->response->body($this->jresult("01"));
        }
        return;
    }

    public function action_get_m_class_code_data()
    {
        $this->response->headers('Access-Control-Allow-Origin', '*');
        $condition = $this->request->post();
        if ($condition) {
            $post = json_decode($condition["condition"]);
            if ($this->check_accesstoken($post, $post->nid) == -1) {
                return $this->response->body($this->jresult("01"));
            }

            if (isset($post->login_type) && $post->login_type == "admin") {
                // admin模式不返回任何信息
                return $this->response->body($this->jresult("01"));
            }

            $ret = array();

            // m_class_code
            $sql = 'SELECT DISTINCT code_div
				FROM m_class_code a
				WHERE
                    (
						:lang_cd = ""
						OR a.lang_cd = :lang_cd
					)
				ORDER BY
					a.code_div
				';
            $query = DB::query(Database::SELECT, $sql);
            $query->parameters(array(
                ':lang_cd' => $post->lang_cd,
            ));
            $results = $query->execute();
            $ret["bot_classcode_codediv_data"] = $results->as_array();

            // m_class_code
            $sql = 'SELECT *
				FROM m_class_code a
				WHERE
                    (
						:lang_cd = ""
						OR a.lang_cd = :lang_cd
					)
				ORDER BY
					a.code_div,
                    a.class_cd
				';
            $query = DB::query(Database::SELECT, $sql);
            $query->parameters(array(
                ':lang_cd' => $post->lang_cd,
            ));
            $results = $query->execute();
            $ret["bot_classcode_data"] = $results->as_array();

            $this->response->body($this->jresult("00", null, $ret));
            return ;
        } else {
            $this->response->body($this->jresult("01"));
        }
        return;
    }

    public function action_get_sudachi_user_dict_csv()
    {
        $this->response->headers('Access-Control-Allow-Origin', '*');
        $condition = $this->request->post();

        if ($condition) {
            $post = json_decode($condition["condition"]);
            if ($this->check_accesstoken($post, $post->nid) == -1) {
                return $this->response->body($this->jresult("01"));
            }

            if (isset($post->login_type) && $post->login_type == "admin") {
                // admin模式不返回任何信息
                return $this->response->body($this->jresult("01"));
            }

            $sudachi_url = "";
            if ($post->nid == "honban") {
                $sudachi_url = "http://172.31.32.237:9002/userdictget";
            } elseif ($post->nid == "dev") {
                $sudachi_url = "http://*************:9002/userdictget";
            }

            $curl = curl_init();
            $timeout = 5;

            $header = [
                'Content-Type: application/x-www-form-urlencoded',
            ];
            // 假的data
            $data = ['api_get_source' => '1',
                'tablename' => '2'
            ];

            curl_setopt($curl, CURLOPT_URL, $sudachi_url);
            curl_setopt($curl, CURLOPT_CUSTOMREQUEST, 'POST');
            curl_setopt($curl, CURLOPT_POSTFIELDS, http_build_query($data));
            curl_setopt($curl, CURLOPT_HTTPHEADER, $header);
            curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($curl, CURLOPT_HEADER, false);
            curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, $timeout);
            $response= curl_exec($curl);
            curl_close($curl);

            $ret["csv"] = $response;
            $this->response->body($this->jresult("00", null, $ret));
            return ;
        } else {
            $this->response->body($this->jresult("01"));
        }
        return;
    }

    public function action_sudachi_user_dict_build()
    {
        $this->response->headers('Access-Control-Allow-Origin', '*');
        $condition = $this->request->post();
        if ($condition) {
            $post = json_decode($condition["condition"]);
            if ($this->check_modify_accesstoken($post, $post->nid) == -1) {
                return $this->response->body($this->jresult("01"));
            }

            if (isset($post->login_type) && $post->login_type == "admin") {
                // admin模式不返回任何信息
                return $this->response->body($this->jresult("01"));
            }

            $sudachi_url = "";
            if ($post->nid == "honban") {
                $sudachi_url = "http://172.31.32.237:9002/userdictbuild";
            } elseif ($post->nid == "dev") {
                $sudachi_url = "http://*************:9002/userdictbuild";
            }

            $curl = curl_init();
            $timeout = 5;

            $header = [
                'Content-Type: application/json',
            ];

            $data = ['sentence' => $post->csv,
                'tablename' => '2'
            ];

            curl_setopt($curl, CURLOPT_URL, $sudachi_url);
            curl_setopt($curl, CURLOPT_CUSTOMREQUEST, 'POST');
            curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($data));
            //			curl_setopt($curl, CURLOPT_POSTFIELDS, http_build_query($data));
            curl_setopt($curl, CURLOPT_HTTPHEADER, $header);
            curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($curl, CURLOPT_HEADER, false);
            curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, $timeout);
            $response= curl_exec($curl);
            curl_close($curl);

            $ret["result"] = $response;
            $this->response->body($this->jresult("00", null, $ret));
            return ;
        } else {
            $this->response->body($this->jresult("01"));
        }
        return;
    }

    public function action_get_enginestatus()
    {
        //error_log("action_get_enginestatus() begin");
        $this->response->headers('Access-Control-Allow-Origin', '*');
        $condition = $this->request->post();
        if ($condition) {
            $post = json_decode($condition["condition"]);
            if ($this->check_accesstoken($post, $post->nid) == -1) {
                return $this->response->body($this->jresult("01"));
            }

            if (isset($post->login_type) && $post->login_type == "admin") {
                // admin模式不返回任何信息
                return $this->response->body($this->jresult("01"));
            }

            $flg_all_engine = $post->flg_all_engine;

            // 固定logic：设定参数
            $engine_global_keep_alive_url = "";
            $engine_list = array();
            if ($post->nid == "honban") {
                $engine_global_keep_alive_url = "https://i.talkappi.com/components/keepalive";
                $engine_list[0] = array();
                $engine_list[0]["status_url"] = "http://172.31.29.46:9122/tools/status";
                $engine_list[1] = array();
                $engine_list[1]["status_url"] = "http://172.31.29.46:9222/tools/status";
            } elseif ($post->nid == "dev" && $post->nid_engine == "1") {
                $engine_global_keep_alive_url = "https://dev1-engine.talkappi.com/components/keepalive";
                $engine_list[0] = array();
                $engine_list[0]["status_url"] = "http://*************:9022/tools/status";
            } elseif ($post->nid == "dev" && $post->nid_engine == "2") {
                $engine_global_keep_alive_url = "https://dev2-engine.talkappi.com/components/keepalive";
                $engine_list[0] = array();
                $engine_list[0]["status_url"] = "http://*************:9042/tools/status";
            }
            // curl参数
            $timeout = 5;
            $header = [
                'Content-Type: application/json',
            ];

            // 首先用global_keep_alive_url获得当前正处于active状态的engine的port号
            $active_port = "cannot get active port";
            {
                $curl = curl_init();
                curl_setopt($curl, CURLOPT_URL, $engine_global_keep_alive_url);
                curl_setopt($curl, CURLOPT_CUSTOMREQUEST, 'GET');
                curl_setopt($curl, CURLOPT_HTTPHEADER, $header);
                curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
                curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($curl, CURLOPT_HEADER, false);
                curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, $timeout);
                $response= curl_exec($curl);
                curl_close($curl);
                $ret = json_decode($response);
                $active_port = $ret->port;
            }

            $engine_status_list = array();
            {
                $engine_counts = count($engine_list);
                for ($i=0; $i<$engine_counts; $i++) {
                    $curl = curl_init();
                    curl_setopt($curl, CURLOPT_URL, $engine_list[$i]["status_url"]);
                    curl_setopt($curl, CURLOPT_CUSTOMREQUEST, 'GET');
                    curl_setopt($curl, CURLOPT_HTTPHEADER, $header);
                    curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
                    curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
                    curl_setopt($curl, CURLOPT_HEADER, false);
                    curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, $timeout);
                    $response= curl_exec($curl);
                    curl_close($curl);
                    $engine_status_list[$i] = $response;
                }
            }

            //error_log("action_get_enginestatus() begin create result");
            $engine_counts = count($engine_list);
            $engine_counts_html = "<div>engineは　" . $engine_counts . "　個があります。</div>";
            $active_engine = "<div>使用中のengine番号は判定失敗！</div>";
            $engine_status_html = "";
            {
                $engine_counts = count($engine_status_list);
                for ($i=0; $i<$engine_counts; $i++) {
                    $cur_engine_status_html = "";
                    $cur_engine_color = "color:red";
                    $engine_no = $i + 1;
                    //error_log("action_get_enginestatus() begin create result for engine no $engine_no");
                    $ret = json_decode($engine_status_list[$i]);

                    if ($ret->_summary->server_port == $active_port) {
                        $cur_engine_color = "color:green";
                        $active_engine = "<div>現在使用中のengine番号は<span style='color:green'>　$engine_no" . "番　</span>です</div>";
                    }

                    if ($ret->_summary->server_port != $active_port && $flg_all_engine == "false") {
                        // 非active的不处理详细,只显示server启动时间
                        // part 0
                        $server_start_time_txt = $ret->_summary->server_start_time_txt;
                        $status_get_time_txt = $ret->_summary->status_get_time_txt;
                        $seconds = $ret->_summary->server_elapse_time % 60;
                        $minitus = floor(($ret->_summary->server_elapse_time) / 60) % 60;
                        $hours =  floor((($ret->_summary->server_elapse_time) / 60) / 60) % 24;
                        $days =  floor(((($ret->_summary->server_elapse_time) / 60) / 60) / 24);
                        $event_num = $ret->_summary->event_number;
                        $average_event_num_per_hour = 0;
                        if ($ret->_summary->server_elapse_time != 0) {
                            $temp_hour= ((($ret->_summary->server_elapse_time) / 60) / 60);
                            if ($temp_hour == 0) {
                                $temp_hour = 1;
                            }
                            if ($temp_hour != 0) {
                                $average_event_num_per_hour = floor($event_num / $temp_hour);
                            }
                        }
                        $cur_part_status_html = "
							<div>
							    engine(<span style='" . $cur_engine_color . "'>$engine_no"."番</span>) のステータス詳細:
								<div style='margin: 0 0 0 10px;'>
									<div>
                                    ステータス取得日時：$status_get_time_txt </br>
									serverスタート日時：$server_start_time_txt
									</div>
								</div>
						";
                        $cur_part_status_html .= "
							</div>
						";
                        $cur_engine_status_html = $cur_part_status_html;
                        $engine_status_html .= "<br>" . $cur_engine_status_html;
                        continue;
                    } else {
                        $part_head =  "
                                <div>
                                    engine(<span style='" . $cur_engine_color . "'>$engine_no"."番</span>) のステータス詳細:
                                    <div style='margin: 0 0 0 10px;'>
                        ";

                        //error_log("action_get_enginestatus() begin create result part 0");
                        // part 0
                        $part0_status_html = "";
                        {
                            $server_start_time_txt = $ret->_summary->server_start_time_txt;
                            $status_get_time_txt = $ret->_summary->status_get_time_txt;
                            $seconds = $ret->_summary->server_elapse_time % 60;
                            $minitus = floor(($ret->_summary->server_elapse_time) / 60) % 60;
                            $hours =  floor((($ret->_summary->server_elapse_time) / 60) / 60) % 24;
                            $days =  floor(((($ret->_summary->server_elapse_time) / 60) / 60) / 24);
                            $event_num = $ret->_summary->event_number;
                            $average_event_num_per_hour = 0;
                            if ($ret->_summary->server_elapse_time != 0) {
                                $temp_hour= ((($ret->_summary->server_elapse_time) / 60) / 60);
                                if ($temp_hour == 0) {
                                    $temp_hour = 1;
                                }
                                if ($temp_hour != 0) {
                                    $average_event_num_per_hour = floor($event_num / $temp_hour);
                                }
                            }
                            $cur_part_status_html = "
                                        <div>
                                        ステータス取得日時：$status_get_time_txt </br>
                                        serverスタート日時：$server_start_time_txt </br>
                                        server稼働時間：$days 日 $hours 時間 $minitus 分 $seconds 秒</br>
                                        処理event数：$event_num" . "個</br>
                                        算出平均event数/時間：$average_event_num_per_hour" . "個
                                        </div>
                            ";
                            $part0_status_html = $cur_part_status_html;
                        }

                        // part 1
                        //error_log("action_get_enginestatus() begin create result part 1");
                        $part1_status_html = "";
                        {
                            $cur_part_status_html = "<div>1.　処理中event数：" . $ret->_summary->event_processing_number . "</div>";
                            if ($ret->_summary->event_processing_number != 0) {
                                $cur_part_status_html .= "
                                    <div style='max-height:400px;overflow:auto;border-color:#00F;border-style:solid;'><table style='margin:0;border-collapse:collapse;border-spacing:0;'>
                                        <tr>
                                            <td style='background-color: rgb(151, 193, 240);border:1px solid;padding:0;'>no</td>
                                            <td style='background-color: rgb(151, 193, 240);border:1px solid;padding:0;'>bot_id</td>
                                            <td style='background-color: rgb(151, 193, 240);border:1px solid;padding:0;'>処理経過秒数</td>
                                            <td style='background-color: rgb(151, 193, 240);border:1px solid;padding:0;'>log_id</td>
                                            <td style='background-color: rgb(151, 193, 240);border:1px solid;padding:0;'>sns_type</td>
                                            <td style='background-color: rgb(151, 193, 240);border:1px solid;padding:0;'>begin_time</td>
                                            <td style='background-color: rgb(151, 193, 240);border:1px solid;padding:0;'>処理概要</td>
                                            <td style='background-color: rgb(151, 193, 240);border:1px solid;padding:0;'>end_time</td>
                                            <td style='background-color: rgb(151, 193, 240);border:1px solid;padding:0;'>event</td>
                                        </tr>
                                ";
                                $_event_processing_array = json_decode(json_encode($ret->_event_processing), true);
                                $count = 1;
                                foreach ($_event_processing_array as $record) {
                                    //$record_event = json_encode($record["event"], JSON_PRETTY_PRINT);
                                    $record_event = $record["event"];
                                    $process_text = str_replace("\\n", "<br>", $record['process_text']);
                                    $cur_part_status_html .= "
                                        <tr>
                                            <td style='border:1px solid;padding:0;'><div style='height:100px;overflow:auto'> $count </div></td>
                                            <td style='border:1px solid;padding:0;'><div style='height:100px;overflow:auto'>" . $record['bot_id'] . "</div></td>
                                            <td style='border:1px solid;padding:0;'><div style='height:100px;overflow:auto'>" .  $record['elapse_time'] . " </div></td>
                                            <td style='border:1px solid;padding:0;'><div style='max-width:150px;height:100px;overflow:auto;word-wrap:break-word;word-break:break-all'>" .  $record['log_id'] . " </div></td>
                                            <td style='border:1px solid;padding:0;'><div style='height:100px;overflow:auto'>" .  $record['sns_type'] . " </div></td>
                                            <td style='border:1px solid;padding:0;'><div style='height:100px;overflow:auto'>" .  $record['begin_time_txt'] . " </div></td>
                                            <td style='border:1px solid;padding:0;'><div style='white-space:nowrap;max-width:300px;height:100px;overflow:auto'>" .  $process_text . " </div></td>
                                            <td style='border:1px solid;padding:0;'><div style='height:100px;overflow:auto'></div></td>
                                            <td style='border:1px solid;padding:0;'><div style='white-space:nowrap;max-width:400px;height:100px;overflow:auto'> <pre>$record_event </pre></div></td>
                                        </tr>
                                    ";
                                    $count += 1;
                                }
                                $cur_part_status_html .= "
                                    </table></div>
                                ";
                            }
                            $part1_status_html = $cur_part_status_html;
                        }


                        // part 2
                        //error_log("action_get_enginestatus() begin create result part 2");
                        $part2_status_html = "";
                        {
                            $cur_part_status_html = "<div>2.　最後に処理成功の" . count($ret->_event_processed_ten) ."個event：</div>";
                            $cur_part_status_html .= "
                                <div style='overflow:auto;border-color:#00F;border-style:solid;'><table style='margin:0;border-collapse:collapse;border-spacing:0;'>
                                    <tr>
                                        <td style='background-color: rgb(151, 193, 240);border:1px solid;padding:0;'>no</td>
                                        <td style='background-color: rgb(151, 193, 240);border:1px solid;padding:0;'>bot_id</td>
                                        <td style='background-color: rgb(151, 193, 240);border:1px solid;padding:0;'>処理経過秒数</td>
                                        <td style='background-color: rgb(151, 193, 240);border:1px solid;padding:0;'>log_id</td>
                                        <td style='background-color: rgb(151, 193, 240);border:1px solid;padding:0;'>sns_type</td>
                                        <td style='background-color: rgb(151, 193, 240);border:1px solid;padding:0;'>begin_time</td>
                                        <td style='background-color: rgb(151, 193, 240);border:1px solid;padding:0;'>処理概要</td>
                                        <td style='background-color: rgb(151, 193, 240);border:1px solid;padding:0;'>end_time</td>
                                        <td style='background-color: rgb(151, 193, 240);border:1px solid;padding:0;'>event</td>
                                    </tr>
                            ";
                            for ($count=1; $count<=count($ret->_event_processed_ten); $count++) {
                                $record = $ret->_event_processed_ten[$count - 1];
                                //$record_event = json_encode($record->event, JSON_PRETTY_PRINT);
                                $record_event = $record->event;
                                $process_text = str_replace("\\n", "<br>", $record->process_text);
                                $cur_part_status_html .= "
                                    <tr>
                                        <td style='border:1px solid;padding:0;'><div style='height:100px;overflow:auto'> $count </div></td>
                                        <td style='border:1px solid;padding:0;'><div style='height:100px;overflow:auto'> $record->bot_id </div></td>
                                        <td style='border:1px solid;padding:0;'><div style='height:100px;overflow:auto'> $record->elapse_time </div></td>
                                        <td style='border:1px solid;padding:0;'><div style='max-width:150px;height:100px;overflow:auto;word-wrap:break-word;word-break:break-all'> $record->log_id </div></td>
                                        <td style='border:1px solid;padding:0;'><div style='height:100px;overflow:auto'> $record->sns_type </div></td>
                                        <td style='border:1px solid;padding:0;'><div style='height:100px;overflow:auto'> $record->begin_time_txt </div></td>
                                        <td style='border:1px solid;padding:0;'><div style='white-space:nowrap;max-width:300px;height:100px;overflow:auto'> $process_text </div></td>
                                        <td style='border:1px solid;padding:0;'><div style='height:100px;overflow:auto'> $record->end_time_txt </div></td>
                                        <td style='border:1px solid;padding:0;'><div style='white-space:nowrap;max-width:400px;height:100px;overflow:auto'> <pre>$record_event </pre></div></td>
                                    </tr>
                                ";
                            }
                            $cur_part_status_html .= "
                                </table></div>
                            ";

                            // 合并
                            $part2_status_html = $cur_part_status_html;
                        }

                        $num_one_page = 20;
                        //$div_base_style_invisible = 'max-height:400px;overflow:auto;display:none';
                        //$div_base_style_visible = 'max-height:400px;overflow:auto;';
                        $div_base_style_invisible = 'overflow:auto;display:none;border:2px;border-color:#00F;border-style:solid;';
                        $div_base_style_visible = 'overflow:auto;border:2px;border-color:#00F;border-style:solid;';
                        $btn_style_selected = 'background-color:green;outline：none;box-shadow:none;border-color: transparent;';
                        $btn_style_unselected = 'background-color:rgb(239, 239, 239);outline：none;box-shadow:none;border-color: transparent;';

                        // part 3
                        //error_log("action_get_enginestatus() begin create result part 3");
                        $part3_status_html = "";
                        {
                            $pages_event_error = floor(($ret->_summary->event_error_number - 0 +  $num_one_page - 1) / $num_one_page);
                            $cur_title_html = "<div>3.　例外が発生したevent数： " . $ret->_summary->event_error_number;
                            $cur_div_html = "";
                            for ($pg = 0 ;$pg <$pages_event_error; ++$pg) {
                                $cur_btn_style = $btn_style_unselected;
                                $pg_no = $pg + 1;
                                if ($pg_no == 1) {
                                    $cur_btn_style = $btn_style_selected;
                                    $cur_title_html .= "<br>ページ";
                                }
                                $cur_title_html .= "<input type='button' id='error_btn_". "$engine_no" . "_$pg_no' style='$cur_btn_style' onclick='click_error_pg( $engine_no, $pages_event_error, $pg_no)' value='$pg_no'/>";
                                $beginIndex = $pg * $num_one_page;
                                $endIndex = $beginIndex + $num_one_page - 1;
                                if ($endIndex >= ($ret->_summary->event_error_number - 1)) {
                                    $endIndex = $ret->_summary->event_error_number - 1;
                                }
                                // 初期只让第一页可见
                                if ($pg == 0) {
                                    $div_style =  $div_base_style_visible;
                                } else {
                                    $div_style =  $div_base_style_invisible;
                                }
                                $cur_div_html .= "
                                    <div style=" . $div_style . " id='error_". "$engine_no" . "_$pg_no'>
                                        <table style='margin:0;border-collapse:collapse;border-spacing:0;'>
                                            <tr>
                                                <td style='background-color: rgb(151, 193, 240);border:1px solid;padding:0;'>no</td>
                                                <td style='background-color: rgb(151, 193, 240);border:1px solid;padding:0;'>bot_id</td>
                                                <td style='background-color: rgb(151, 193, 240);border:1px solid;padding:0;'>処理経過秒数</td>
                                                <td style='background-color: rgb(151, 193, 240);border:1px solid;padding:0;'>log_id</td>
                                                <td style='background-color: rgb(151, 193, 240);border:1px solid;padding:0;'>sns_type</td>
                                                <td style='background-color: rgb(151, 193, 240);border:1px solid;padding:0;'>begin_time</td>
                                                <td style='background-color: rgb(151, 193, 240);border:1px solid;padding:0;'>処理概要</td>
                                                <td style='background-color: rgb(151, 193, 240);border:1px solid;padding:0;'>end_time</td>
                                                <td style='background-color: rgb(151, 193, 240);border:1px solid;padding:0;'>event</td>
                                                <td style='background-color: rgb(151, 193, 240);border:1px solid;padding:0;'>exception</td>
                                            </tr>
                                ";
                                for ($count=$beginIndex; $count<=$endIndex; $count++) {
                                    $record = $ret->_event_error[$count];
                                    $displayno = $count + 1;
                                    //$record_event = json_encode($record->event, JSON_PRETTY_PRINT);
                                    $record_event = $record->event;
                                    $process_text = str_replace("\\n", "<br>", $record->process_text);
                                    $cur_div_html .= "
                                            <tr>
                                                <td style='border:1px solid;padding:0;'><div style='height:100px;overflow:auto'> $displayno </div></td>
                                                <td style='border:1px solid;padding:0;'><div style='height:100px;overflow:auto'> $record->bot_id </div></td>
                                                <td style='border:1px solid;padding:0;'><div style='height:100px;overflow:auto'> $record->elapse_time </div></td>
                                                <td style='border:1px solid;padding:0;'><div style='max-width:150px;height:100px;overflow:auto;word-wrap:break-word;word-break:break-all'> $record->log_id </div></td>
                                                <td style='border:1px solid;padding:0;'><div style='height:100px;overflow:auto'> $record->sns_type </div></td>
                                                <td style='border:1px solid;padding:0;'><div style='height:100px;overflow:auto'> $record->begin_time_txt </div></td>
                                                <td style='border:1px solid;padding:0;'><div style='white-space:nowrap;max-width:300px;height:100px;overflow:auto'> $process_text </div></td>
                                                <td style='border:1px solid;padding:0;'><div style='height:100px;overflow:auto'> $record->end_time_txt </div></td>
                                                <td style='border:1px solid;padding:0;'><div style='white-space:nowrap;max-width:400px;height:100px;overflow:auto'> <pre>$record_event </pre></div></td>
                                                <td style='border:1px solid;padding:0;'><div style='white-space:nowrap;max-width:400px;height:100px;overflow:auto'> <pre>$record->error_msg </pre></div></td>
                                            </tr>
                                    ";
                                }
                                $cur_div_html .= "
                                        </table>
                                    </div>
                                ";
                            }
                            $cur_title_html .= "</div>";
                            $part3_status_html .= $cur_title_html . $cur_div_html;
                        }


                        // part 4
                        //error_log("action_get_enginestatus() begin create result part 4");
                        $part4_status_html = "";
                        {
                            $_event_warning_array = json_decode(json_encode($ret->_event_warning), true);
                            $count_event_warning_array = count($_event_warning_array);
                            $cur_title_html = "<div>4.　処理時間>5秒のevent数：" . $count_event_warning_array;

                            if ($count_event_warning_array > 100) {
                                array_splice($_event_warning_array, 0, $count_event_warning_array - 100);
                                $count_event_warning_array = count($_event_warning_array);
                            }

                            $cur_div_html = "";
                            $pages_warning_error = floor(($count_event_warning_array - 0 +  $num_one_page - 1) / $num_one_page);

                            $count = 0;
                            $pg_no = 1;
                            foreach ($_event_warning_array as $record) {
                                $displayno = $count + 1;
                                // 每20行分一个div出来
                                if (($count % $num_one_page) == 0) {
                                    $cur_btn_style = $btn_style_unselected;
                                    if ($pg_no == 1) {
                                        $cur_btn_style = $btn_style_selected;
                                        $cur_title_html .= "<br>最後５ページ";
                                    }
                                    $cur_title_html .= "<input type='button'  id='warning_btn_". "$engine_no" . "_$pg_no' style='$cur_btn_style' onclick='click_warning_pg( $engine_no, $pages_warning_error, $pg_no)' value='$pg_no'/>";

                                    // 初期只让第一页可见
                                    if ($count == 0) {
                                        $div_style =  $div_base_style_visible;
                                    } else {
                                        $div_style =  $div_base_style_invisible;
                                    }

                                    $cur_div_html .= "
                                        <div style=" . $div_style . " id='warning_" . "$engine_no" . "_$pg_no'>
                                            <table style='margin:0;border-collapse:collapse;border-spacing:0;'>
                                                <tr>
                                                    <td style='background-color: rgb(151, 193, 240);border:1px solid;padding:0;'>no</td>
                                                    <td style='background-color: rgb(151, 193, 240);border:1px solid;padding:0;'>bot_id</td>
                                                    <td style='background-color: rgb(151, 193, 240);border:1px solid;padding:0;'>処理経過秒数</td>
                                                    <td style='background-color: rgb(151, 193, 240);border:1px solid;padding:0;'>log_id</td>
                                                    <td style='background-color: rgb(151, 193, 240);border:1px solid;padding:0;'>sns_type</td>
                                                    <td style='background-color: rgb(151, 193, 240);border:1px solid;padding:0;'>begin_time</td>
                                                    <td style='background-color: rgb(151, 193, 240);border:1px solid;padding:0;'>処理概要</td>
                                                    <td style='background-color: rgb(151, 193, 240);border:1px solid;padding:0;'>end_time</td>
                                                    <td style='background-color: rgb(151, 193, 240);border:1px solid;padding:0;'>event</td>
                                                </tr>
                                    ";
                                    $pg_no = $pg_no + 1;
                                }

                                // 当前行数据
                                //$record_event = json_encode($record["event"], JSON_PRETTY_PRINT);
                                $record_event = $record["event"];
                                $process_text = str_replace("\\n", "<br>", $record['process_text']);
                                $cur_div_html .= "
                                                <tr>
                                                    <td style='border:1px solid;padding:0;'><div style='height:100px;overflow:auto'> $displayno </div></td>
                                                    <td style='border:1px solid;padding:0;'><div style='height:100px;overflow:auto'>" . $record['bot_id'] . "</div></td>
                                                    <td style='border:1px solid;padding:0;'><div style='height:100px;overflow:auto'>" .  $record['elapse_time'] . " </div></td>
                                                    <td style='border:1px solid;padding:0;'><div style='max-width:150px;height:100px;overflow:auto;word-wrap:break-word;word-break:break-all'>" .  $record['log_id'] . " </div></td>
                                                    <td style='border:1px solid;padding:0;'><div style='height:100px;overflow:auto'>" .  $record['sns_type'] . " </div></td>
                                                    <td style='border:1px solid;padding:0;'><div style='height:100px;overflow:auto'>" .  $record['begin_time_txt'] . " </div></td>
                                                    <td style='border:1px solid;padding:0;'><div style='white-space:nowrap;max-width:300px;height:100px;overflow:auto'>" .  $process_text . " </div></td>
                                                    <td style='border:1px solid;padding:0;'><div style='height:100px;overflow:auto'>" .  $record['end_time_txt'] . " </div></td>
                                                    <td style='border:1px solid;padding:0;'><div style='white-space:nowrap;max-width:400px;height:100px;overflow:auto'> <pre>$record_event </pre></div></td>
                                                </tr>
                                ";

                                // 每20行分一个div出来
                                if ((($count % $num_one_page) == 19) || ($count == $count_event_warning_array - 1)) {
                                    $cur_div_html .= "
                                            </table>
                                        </div>
                                    ";
                                }

                                // 行 + 1
                                $count += 1;
                            }

                            $cur_title_html .= "</div>";
                            $part4_status_html .= $cur_title_html . $cur_div_html;
                        }


                        // part 0 end
                        $part_head_footer = "
                                </div>
                            </div>
                            <br>
                        ";
                        //error_log("action_get_enginestatus() begin merge cur engine result part");
                        $cur_engine_status_html = $part_head
                            . $part0_status_html
                            . "<br>"
                            . $part1_status_html
                            . "<br>"
                            . $part2_status_html
                            . "<br>"
                            . $part3_status_html
                            . "<br>"
                            . $part4_status_html
                            . $part_head_footer;
                        //error_log("action_get_enginestatus() begin merge cur engine result part to final engine result");
                        $engine_status_html .= "<br>" . $cur_engine_status_html;
                    }
                }
            }
            //error_log("action_get_enginestatus() begin create final result");
            $all_engine_status_html = "<br>" . $engine_counts_html . $active_engine . $engine_status_html;

            $ret = array();
            $ret["current_engine_no"] = "";
            $ret["engine_status_html"] = $all_engine_status_html;
            $ret["myscript"] = "
                function click_error_pg(engine_no, pages_event_error, pgno) {
                    for (i = 1; i <= pages_event_error; i++) { 
                        let temp_id = 'error_' + engine_no + '_' + i;
                        document.getElementById(temp_id).style.display = 'none';
                        temp_id = 'error_btn_' + engine_no + '_' + i;
                        document.getElementById(temp_id).style['background-color'] = 'rgb(239,239,239)';
                    }                    
                    let temp_id = 'error_' + engine_no + '_' + pgno;
                    document.getElementById(temp_id).style.display = 'block';
                    temp_id = 'error_btn_' + engine_no + '_' + pgno;
                    document.getElementById(temp_id).style['background-color'] = 'green';
                }
                function click_warning_pg(engine_no, pages_warning_error, pgno) {
                    for (i = 1; i <= pages_warning_error; i++) { 
                        let temp_id = 'warning_' + engine_no + '_' + i;
                        document.getElementById(temp_id).style.display = 'none';
                        temp_id = 'warning_btn_' + engine_no + '_' + i;
                        document.getElementById(temp_id).style['background-color'] = 'rgb(239,239,239)';
                    }                    
                    let temp_id = 'warning_' + engine_no + '_' + pgno;
                    document.getElementById(temp_id).style.display = 'block';
                    temp_id = 'warning_btn_' + engine_no + '_' + pgno;
                    document.getElementById(temp_id).style['background-color'] = 'green';
                }
            ";


            //error_log("action_get_enginestatus() end normal");
            $this->response->body($this->jresult("00", null, $ret));
            return ;
        } else {
            //error_log("action_get_enginestatus() end error");
            $this->response->body($this->jresult("01"));
        }
        return;
    }


    /*
    private function get_status_html($post)
    {
        $this->response->headers('Access-Control-Allow-Origin', '*');
        $this->response->headers('Content-Encoding', 'gzip');
        $condition = $this->request->post();
        if ($condition) {
            $post = json_decode($condition["condition"]);
            if ($this->check_accesstoken($post, $post->nid) == -1) {
                return $this->response->body($this->jresult("01"));
            }


            $ret = array();
            $ret["current_engine_no"] = "";
            $html = "abcdefghgi";
            $ret["engine_status_html"] =  $html;
            $retzip = gzencode($this->jresult("00", null, $ret));

            $this->response->body($retzip);
            return ;
        } else {
            $this->response->body($this->jresult("01"));
        }
        return;
    }
    */
    private function getlocalip()
    {
        //$reIP=$_SERVER["REMOTE_ADDR"];
        //echo $reIP;

        $preg = "/\A((([0-9]?[0-9])|(1[0-9]{2})|(2[0-4][0-9])|(25[0-5]))\.){3}(([0-9]?[0-9])|(1[0-9]{2})|(2[0-4][0-9])|(25[0-5]))\Z/";
        exec("/sbin/ifconfig", $out, $stats);
        if (!empty($out)) {
            if (isset($out[1]) && strstr($out[1], 'addr:')) {
                $tmpArray = explode(":", $out[1]);
                $tmpIp = explode(" ", $tmpArray[1]);
                if (preg_match($preg, trim($tmpIp[0]))) {
                    return trim($tmpIp[0]);
                }
            }
        }
        return '127.0.0.1';
    }

    private function get_grp_parent_bot_id($bot_id)
    {
        if ($bot_id >= 201000 && $bot_id < 299999) {
            if ($bot_id % 1000 == 0) {
                return $bot_id;
            } else {
                return intval($bot_id / 1000) * 1000;
            }
        } else if ($bot_id >= 2001000 && $bot_id < 2999999) {
            if ($bot_id % 1000 == 0) {
                return $bot_id;
            } else {
                return intval($bot_id / 1000) * 1000;
            }
        } else {
            return -1;
        }
    }

    private function get_template_bot_id($bot_id)
    {
        $results = $this->get_bot_setting($bot_id, "template_bot");
        if (count($results) > 0) {
            $base_bot_id = $results[0]["setting_value"];
            return $base_bot_id;
        } else {
            return -1;
        }
    }

    private function get_bot_setting($bot_id, $setting_cd)
    {
        $sql = "SELECT a.*
			FROM t_bot_setting a
			WHERE
				a.bot_id = :bot_id
				AND a.setting_cd = :setting_cd
			";
        $query = DB::query(Database::SELECT, $sql);
        $query->parameters(array(
            ':bot_id' => $bot_id,
            ':setting_cd' => $setting_cd,
        ));
        $results = $query->execute();
        return $results->as_array();
    }

    public function action_jump_to_sns()
    {
        $this->response->headers('Access-Control-Allow-Origin', '*');
        $condition = $this->request->query();
        if ($condition) {
            $post = json_decode($condition["condition"]);
            /*
            if ($this->check_accesstoken($post, $post->nid) == -1) {
                echo "access token is not valid";
                return ;
            }
            */
            if (isset($post->login_type) && $post->login_type == "admin") {
                // admin模式不返回任何信息
                echo "not support action in admin mode";
                return ;
            }

            if (isset($post->sns) == false || ($post->sns == "facebook" && $post->sns == "line")) {
                echo "not support sns_type";
                return ;
            }
            if ($post->sns == "facebook") {
                $setting_cd = "json_sns_link_facebook";
                $sns_cd = "fb";
            } elseif ($post->sns == "line") {
                $setting_cd = "json_sns_link_line";
                $sns_cd = "ln";
            } elseif ($post->sns == "web") {
                $setting_cd = "no_setting";
                $sns_cd = "wb";
            }


            // 检查是否支持facebook,line
            $sql = "SELECT *
				FROM t_bot a
				where a.bot_id = :bot_id
                and delete_flg = 0
				";
            $query = DB::query(Database::SELECT, $sql);
            $query->parameters(array(
                ':bot_id' => $post->bot_id,
            ));
            $results = $query->execute()->as_array();
            if (count($results) != 1) {
                echo "bot not support " . $post->sns;
                return ;
            }
            $sns_cd_db = $results[0]["sns_cd"];
            $array_sns_cd_db = explode(",", $sns_cd_db);
            $flg_have = 0;
            for ($i=0; $i<count($array_sns_cd_db); $i++) {
                if ($array_sns_cd_db[$i] == $sns_cd) {
                    $flg_have = 1;
                    break;
                }
            }
            if ($flg_have == 0) {
                echo "bot not support " . $post->sns;
                return ;
            }

            // 读取t_bot_setting
            if ($post->sns == "facebook" || $post->sns == "line") {
                $sql = "SELECT *
                    FROM t_bot_setting a
                    where a.bot_id = :bot_id
                    and setting_cd = :setting_cd
                    ";
                $query = DB::query(Database::SELECT, $sql);
                $query->parameters(array(
                    ':bot_id' => $post->bot_id,
                    ':setting_cd' => $setting_cd,
                ));
                $results = $query->execute()->as_array();
                if (count($results) != 1) {
                    $parent_bot_id = $this->get_grp_parent_bot_id($post->bot_id);
                    if ($parent_bot_id != -1 && $parent_bot_id != $post->bot_id) {
                        // 从父bot读
                        $sql = "SELECT *
                            FROM t_bot_setting a
                            where a.bot_id = :bot_id
                            and setting_cd = :setting_cd
                            ";
                        $query = DB::query(Database::SELECT, $sql);
                        $query->parameters(array(
                            ':bot_id' => $parent_bot_id,
                            ':setting_cd' => $setting_cd,
                        ));
                        $results = $query->execute()->as_array();
                    }
                }
                if (count($results) != 1) {
                    // 从 bot_id=0读
                    $sql = "SELECT *
                        FROM t_bot_setting a
                        where a.bot_id = :bot_id
                        and setting_cd = :setting_cd
                        ";
                    $query = DB::query(Database::SELECT, $sql);
                    $query->parameters(array(
                        ':bot_id' => 0,
                        ':setting_cd' => $setting_cd,
                    ));
                    $results = $query->execute()->as_array();
                }
                if (count($results) != 1) {
                    echo "$setting_cd is not set";
                    return ;
                }
            }

            if ($post->sns == "web") {
                if ($post->nid == "honban") {
                    $server_url = "bot.talkappi.com";
                } else {
                    $server_url = "dev1-api.talkappi.com";
                }
                $scene_cd =  $post->scene_cd;
                $url = "https://$server_url/bot/webchat?id=$scene_cd&lang=ja";
                echo "<head> <meta http-equiv='refresh' content='0;url=$url'> </head>";
            } elseif ($post->sns == "line") {
                $json_sns_link_line = json_decode($results[0]["setting_value"]);
                $channel_id = $json_sns_link_line->weblogin_channel_id;
                $sns_id = $json_sns_link_line->sns_id;
                $scene_cd =  $post->scene_cd;
                if ($post->nid == "honban") {
                    $server_url = "i.talkappi.com";
                } else {
                    $server_url = "dev1-engine.talkappi.com";
                }

                echo "<head> <meta http-equiv='refresh' content='0;url=https://access.line.me/oauth2/v2.1/authorize?response_type=code&client_id=$channel_id&redirect_uri=https%3A%2F%2F$server_url%2Fline&state=$scene_cd&scope=openid%20profile'> </head>";
            } elseif ($post->sns == "facebook") {
                $json_sns_link_facebook = json_decode($results[0]["setting_value"]);
                $ref = $json_sns_link_facebook->ref;
                $scene_cd =  $post->scene_cd;
                $url = "https://m.me/$ref?ref=$scene_cd";
                echo "<head> <meta http-equiv='refresh' content='0;url=$url'> </head>";
            }
            return ;
        } else {
            echo "miss parameters";
            return ;
        }
        return;
    }

}
