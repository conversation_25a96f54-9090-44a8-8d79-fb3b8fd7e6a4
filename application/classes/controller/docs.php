<?php defined('SYSPATH') or die('No direct script access.');
class Controller_Docs extends Controller_Template_Normalbase{
	public function __construct(Request $request, Response $response)
	{
		parent::__construct($request, $response);
		if (Session::instance()->get('user_id', NULL) == NULL) {
			$this->_redirect("/");
		}
	}
	public function action_index()
	{
		$view = View::factory ('docs/index');
		$this->response->body($view);
	}
}