<?php defined('SYSPATH') or die('No direct script access.');
class Controller_Api extends Controller {

	const API_RESULT_OK = '0';
	const API_RESULT_ERR = '1';
	const API_RESULT_WARN = '2';

	public function action_connect() {
		$this->response->body('api connected ok.');
	}
	
	// 
	public function action_link()
	{
		$post = $this->request->query();
		if ($post){
			$link = ORM::factory('link');
			$link->link_id = $this->create_unique_toke(32);
			$link->link_type_cd = $post['type'];
			$query_string = '';
			foreach($post as $k=>$v) {
				$query_string = $query_string . $k . '=' . $v . '&';
			}
			$link->param1 = $query_string;
			$link->valid_date_time = date('Y-m-d H:i:s', strtotime('+' . Kohana::$config->load('settings.link_valid_' . $post['type'])));
			$link->valid_flg = 0;
			$link->save();
			$this->response->body($this->jresult("00", NULL, $link->link_id));
			return;
		}
		else {
			$this->response->body($this->jresult("01"));
		}
	}
	
	// 体験情報取得API
	public function action_experiences()
	{
		$post = $this->request->query();
		$model = Model::factory('apimodel');
		if ($post){
			$items = $model->get_experiences($post['lang'], $post["keyword"]);
			$this->response->body($this->jresult("00", NULL, $items));
			return;
		}
		else {
			$this->response->body($this->jresult("01"));
		}
	}

	public function action_experience()
	{
		$post = $this->request->query();
		$model = Model::factory('apimodel');
		if ($post){
			$items = $model->get_experience($post['lang'], $post['experience_cd']);
			foreach($items as $item) {
				$this->response->body($this->jresult("00", NULL, $item));
				return;
			}
		}
		else {
			$this->response->body($this->jresult("01"));
		}
	}

	public function action_experience_stock()
	{
		$post = $this->request->query();
		$model = Model::factory('apimodel');
		if ($post){
			$items = $model->get_experience_stock($post['experience_cd']);
			$this->response->body($this->jresult("00", NULL, $items));
			return;
		}
		else {
			$this->response->body($this->jresult("01"));
		}
	}
	// 体験受付API
	public function action_reserve_activity()
	{
		//$post = $this->request->query();
		$post = $this->request->query();
		if ($post){
			$data = json_decode($post['data']);
			$info = $this->getExperienceId($data->experience_cd);
			$orm = ORM::factory('botorder');
			$orm->member_id = $data->id;
			$orm->reserve_name = $data->name;
			$orm->order_status_cd= '00';
			$orm->bot_id = $info['bot_id'];
			$orm->experience_id = $info['experience_id'];
			$orm->experience_date = $data->experience_date;
			$orm->adult_num = $data->adult_num;
			$orm->child_num = $data->child_num;
			$orm->infant_num = $data->infant_num;
			$orm->order_date = date('Y-m-d H:i:s');
			$orm->lang_cd = $data->lang;
			$orm->reserve1= $data->msg;

			$max_order = ORM::factory('botorder')
				->where('order_no', 'LIKE', date('Ymd') . '%')
				->order_by('order_no', 'DESC')
				->find();
			if (!isset($max_order->order_no)) {
				$order_no =  date('Ymd') . '0001';
			}
			else {
				$order_no = substr($max_order->order_no, 0, 8) .
				sprintf('%04d', intval(substr($max_order->order_no, 8, 4)) + 1);
			}
			$orm->order_no = $order_no;

			$orm->save();
			$this->response->body($this->jresult("00", NULL, NULL));
			return;
		}
		else {
			$this->response->body($this->jresult("01"));
		}
	}

	/*wuzhao delete 2019.08.06
	public function action_bot_items()
	{
		$post = $this->request->query();
		$model = Model::factory('apimodel');
		if ($post){
			$items = $model->get_bot_items($post['bot_id'], $post["lang"]);
			$this->response->body($this->jresult("00", NULL, $items));
			return;
		}
		else {
			$this->response->body($this->jresult("01"));
		}
	}
	*/

	// 施設情報取得API
	public function action_facilities()
	{
		$post = $this->request->query();
		$model = Model::factory('apimodel');
		if ($post){
			$items = $model->get_items($post['lang'], $post["facility_keyword"]);
			$this->response->body($this->jresult("00", NULL, $items));
			return;
		}
		else {
			$this->response->body($this->jresult("01"));
		}
	}

	/*wuzhao delete 2019.08.06
	public function action_facility()
	{
		$post = $this->request->query();
		$model = Model::factory('apimodel');
		if ($post){
			$items = $model->get_item($post['lang'], $post['facility_cd']);
			foreach($items as $item) {
				$this->response->body($this->jresult("00", NULL, $item));
				return;
			}
		}
		else {
			$this->response->body($this->jresult("01"));
		}
	}
	*/

	// FAQ情報取得API
	public function action_faq()
	{
		$post = $this->request->query();
		if ($post){
			$bot_info = $this->getBotIdByFacilityCd($post['facility_cd'], "action_faq");

			// wuzhao add begin #530 2018.09.30
			$model = Model::factory('apimodel');
			$faqs = $model->get_faq($post, $bot_info);
			// wuzhao add end #530

			/* 2018.09.30 delete
			$faqs = DB::select()->from('t_bot_intent')
				->where('bot_id', '=', $item_info['bot_id'])
				->where('item_id', '=', $item_info['item_id'])
				->where('intent_cd', '=', $post['intent_cd'])
				->where('lang_cd', '=', $post['lang'])
				->execute()->as_array();
			*/
			/*
			if (count($faqs) == 0) {
				$faqs = DB::select()->from('t_bot_intent')
					->where('bot_id', '=', $item_info['bot_id'])
					->where('item_id', '=', 0)
					->where('intent_cd', '=', $post['intent_cd'])
					->where('lang_cd', '=', $post['lang'])
					->execute()->as_array();
			}
			*/
			$this->response->body($this->jresult("00", NULL, $faqs));
			return;
		}
		else {
			$this->response->body($this->jresult("01"));
		}
	}

	// ユーザ情報登録API
	public function action_regist()
	{
		//$post = $this->request->query();
		$post = $this->request->post();
		if ($post){
			$member_info = json_decode($post['data']);
			$bot_info = $this->getBotIdByFacilityCd($member_info->facility_cd, "action_regist");
			$bot_id = $bot_info['bot_id'];

			$member = DB::select()->from('t_bot_member')
			->where('bot_id', '=', $bot_id)
			->where('member_id', '=', $member_info->id)
			->execute()->as_array();
			if (count($member) > 0) {
				$fields = array();
				$fields['country_cd'] = $member_info->country;
				$fields['first_name'] = $member_info->first_name;
				$fields['last_name'] = $member_info->last_name;
				$fields['avatar']= $member_info->avatar;
				$fields['sex'] = $member_info->gender;
				$fields['timezone'] = $member_info->timezone;
				$fields['lang_cd'] = $member_info->lang;
				// $fields['is_tester'] = $member_info->is_tester;  // 2020.01.31 更新的时候不更新这个测试者标记
				$fields['mobile'] = $member_info->mobile;
				if (isset($member_info->sns_id)) {
					// 2022.12.23 #35624 begin
					// $fields['sns_id'] = $member_info->sns_id;
					if ($member_info->sns_id == "web01") {
						if ($member[0]["sns_type_cd"] == "wb") {
							$fields['sns_id'] = $member_info->sns_id;
						} else {
							// some error occur
							error_log("action_regist() discard setting sns_type_cd to web01 for bot_id= $bot_id member_id=" . $member_info->id);
						}
					} else {
						// line ,facebook, wechat
						$fields['sns_id'] = $member_info->sns_id;
					}
					// 2022.12.23 #35624 end
				} else {
				}

				// 2020.01.05 #7304 begin
				if (isset($member_info->secret_mode)) {
					$fields['secret_mode'] = $member_info->secret_mode;
				}
				// 2020.01.05 #7304 end

				// 2020.08.10 #4982 begin
				if (isset($member_info->src_member_id) && $member_info->src_member_id != "") {
					$fields['induced_web_member_id'] = $member_info->src_member_id;
				}
				// 2020.08.10 #4982 end
				// 2021.03.24 #9478 begin
				if (isset($member_info->city)) {
					$fields['city'] = $member_info->city;
				} else {
					$fields['city'] = "";
				}
				// 2021.03.24 #9478 end

				// 2021.09.20 改善 #16532  add ga_client_id begin
				if (isset($member_info->ga_client_id)) {
					$fields['ga_client_id'] = $member_info->ga_client_id;
				}
				// 2021.09.20 改善 #16532  add ga_client_id end
				DB::update('t_bot_member')->set($fields)
				->where('bot_id', '=', $bot_id)
				->where('member_id', '=', $member_info->id)
				->execute();
			}
			else {
				// 2020.05.07 add #3096 begin
				$sql = "SELECT count(1)as max_count, max(member_no) as max_member_no
				FROM t_bot_member a
				WHERE
					bot_id = $bot_id
				";

				$query = DB::query(Database::SELECT, $sql);
				$results = $query->execute();
				$max_count = $results[0]["max_count"];
				$max_member_no = $results[0]["max_member_no"];
				if ($max_count == null) {
					$max_count = 0;
				}
				if ($max_member_no == null) {
					$max_member_no = 0;
				}
				$max_count += 1;
				$max_member_no += 1;
				$cur_member_no = $max_count;
				if ($max_member_no > $cur_member_no) {
					$cur_member_no = $max_member_no;
				}
				// 2020.05.07 add #3096 end

				$member = ORM::factory('botmember');
				$member->member_no = $cur_member_no;
				$member->bot_id = $bot_id;
				$member->member_id = $member_info->id;
				$member->sns_type_cd = $member_info->sns_type;
				$member->lang_cd = $member_info->lang;
				$member->country_cd = $member_info->country;
				$member->first_name = $member_info->first_name;
				$member->last_name = $member_info->last_name;
				$member->avatar= $member_info->avatar;
				$member->sex = $member_info->gender;
				$member->timezone = $member_info->timezone;
				$member->regist_date = date('Y-m-d H:i:s');
				$member->is_tester = $member_info->is_tester;
				$member->scene_cd = $member_info->scene_cd;
				$member->mobile = $member_info->mobile;
				if (isset($member_info->sns_id)) {
					$member->sns_id = $member_info->sns_id;
				} else {
				}

				// 2020.01.05 #7304 begin
				if (isset($member_info->secret_mode)) {
					$member->secret_mode = $member_info->secret_mode;
				}
				// 2020.01.05 #7304 end

				// 2020.08.10 #4982 begin
				if (isset($member_info->src_member_id) && $member_info->src_member_id != "") {
					$member->induced_web_member_id = $member_info->src_member_id;
				}
				// 2020.08.10 #4982 end
				// 2021.03.24 #9478 begin
				if (isset($member_info->city)) {
					$member->city = $member_info->city;
				} else {
					$member->city = "";
				}
				// 2021.03.24 #9478 end

				// 2021.09.20 改善 #16532  add ga_client_id begin
				if (isset($member_info->ga_client_id)) {
					$member->ga_client_id = $member_info->ga_client_id;
				}
				// 2021.09.20 改善 #16532  add ga_client_id end

				$member->save();
			}
			$this->response->body($this->jresult("00", NULL, NULL));
			return;
		}
		else {
			$this->response->body($this->jresult("01"));
		}
	}

	// ユーザ情報登録API
	public function action_member_update()
	{
		//$post = $this->request->query();
		$post = $this->request->query();
		if ($post){
			$member_info = json_decode($post['data']);
			$bot_info = $this->getBotIdByFacilityCd($member_info->facility_cd, "action_member_update");
			$bot_id = $bot_info['bot_id'];

			$member = DB::select()->from('t_bot_member')
			->where('bot_id', '=', $bot_id)
			->where('member_id', '=', $member_info->id)
			->execute()->as_array();
			if (count($member) > 0) {
				$fields = array();
				$fields['country_cd'] = $member_info->country;
				$fields['timezone'] = $member_info->timezone;
				// 2021.03.24 #9478 增加city
				if (isset($member_info->city)) {
					$fields['city'] = $member_info->city;
				} else {
					$fields['city'] = '';
				}
				// 2021.03.24 #9478 end
				// 2021.09.20 改善 #16532 begin add ga_client_id
				if (isset($member_info->ga_client_id)) {
					$fields['ga_client_id'] = $member_info->ga_client_id;
				}
				// 2021.09.20 改善 #16532 end add ga_client_id
				DB::update('t_bot_member')->set($fields)
				->where('bot_id', '=', $bot_id)
				->where('member_id', '=', $member_info->id)
				->execute();
			}
			$this->response->body($this->jresult("00", NULL, NULL));
			return;
		}
		else {
			$this->response->body($this->jresult("01"));
		}
	}

	// ユーザ情報取得API
	public function action_member()
	{
		$post = $this->request->query();
		$bot_info = null;

		if ($post){
			$ret = array();
			// if param contains facility_cd
			if(array_key_exists('facility_cd', $post)) {
				$bot_info = $this->getBotIdByFacilityCd($post['facility_cd'], "action_member1");
				$ret['facility_cd'] = $post['facility_cd'];
			}
			// last talk facility
			else {
				if(array_key_exists('bot_id_list', $post)) {
					$model = Model::factory('apimodel');
					$logs = $model->search_member_last_facility($post['id'], $post['bot_id_list']);
				} else {
					$logs = DB::select('bot_id')->from('t_bot_member')
					->where('member_id', '=', $post['id'])
					->order_by('last_talk_date', 'DESC')
					->limit(1)
					->execute()->as_array();
				}
				if (count($logs) > 0) {
					if ($logs[0]['bot_id'] == "999") {
						$bot_info = array();
						$bot_info['bot_id'] = "999";
						$ret['facility_cd'] = "99999999";
					} else {
						$bot = ORM::factory('bot', $logs[0]['bot_id']);
						$ret['facility_cd'] = $bot->facility_cd;
						$bot_info = $this->getBotIdByFacilityCd($ret['facility_cd'], "action_member2");
						if ($logs[0]['bot_id'] == "999") {
							$bot_info = array();
							$bot_info['bot_id'] = "999";
							$ret['facility_cd'] = "99999999";
						}
					}
				}
			}
			if ($bot_info != null) {
				$member = ORM::factory('botmember')
					->where('member_id', '=', $post['id'])
					->where('bot_id', '=', $bot_info['bot_id'])
					->find();
			}
			else {
				$ret['facility_cd'] = null;
				$member = ORM::factory('botmember')
					->where('member_id', '=', $post['id'])
					->find();
			}

			// 2023.05.20 #38860 chatgpt begin
			$ai = "";
			if ($bot_info != null && $member->member_id != null) {
				$t_member_extent_record = DB::select()->from('t_member_extent')
					->where('bot_id', '=', $bot_info['bot_id'])
					->where('member_id', '=', $member->member_id)
					->execute()->as_array();
				if (count($t_member_extent_record) > 0) {
					$ai = $t_member_extent_record[0]["ai"];
				}
			}
			$ret['ai']= $ai;
			// 2023.05.20 #38860 chatgpt end

			$ret['member_id']= $member->member_id;
			$ret['lang_cd']= $member->lang_cd;
			$ret['country_cd']= $member->country_cd;
			$ret['first_name'] = $member->first_name;
			$ret['last_name'] = $member->last_name;
			$ret['avatar'] = $member->avatar;
			$ret['gender'] = $member->sex;
			$ret['timezone'] = $member->timezone;
			$ret['chat_mode'] = $member->chat_mode;
			$ret['request_flg'] = $member->request_flg;
			$ret['last_talk_date'] = $member->last_talk_date;
			$ret['sns_type_cd'] = $member->sns_type_cd;
			$ret['phone'] = $member->phone;
			$ret['email'] = $member->email;
			$ret['is_tester'] = $member->is_tester;
			$ret['sns_id'] = $member->sns_id;
			// 2020.08.10 #4982 begin
			$ret['chat_msg_count'] = $member->chat_msg_count;
			$ret['line_induce_flg'] = $member->line_induce_flg;
			$ret['induced_web_member_id'] = $member->induced_web_member_id;
			// 2020.08.10 #4982 begin
			// 2020.09.20 #5926 begin
			$ret['mobile'] = $member->mobile;
			// 2020.09.20 #5926 end
			// 2020.11.26 #6844 begin
			$ret['order_name'] = $member->order_name;
			// 2020.11.26 #6844 end
			// 2021.10.14 #17343 begin
			$ret['order_name_kanji'] = $member->order_name_kanji;
			// 2021.10.14 #17343 end
			// 2021.03.24 #9478 begin 增加city
			$ret['city'] = $member->city;
			// 2021.03.24 #9478 end
			// 2022.03.11 #9391 begin
			$ret['is_blocked'] = $member->is_blocked;
			// 2022.03.11 #9391 end
			// 2021.09.20 改善 #16532  add ga_client_id begin
			$ret['ga_client_id'] = $member->ga_client_id;
			// 2021.09.20 改善 #16532  add ga_client_id end

			$this->response->body($this->jresult("00", NULL, $ret));
		}
		else {
			$this->response->body($this->jresult("01"));
		}
	}
	// ユーザ情報取得API
	public function action_chatmode()
	{
		$post = $this->request->query();
		if ($post){
			$bot_info = $this->getBotIdByFacilityCd($post['facility_cd'], "action_chatmode");
            $bot_id = $bot_info['bot_id'];
			
			$fields = array();
			$fields['chat_mode'] = $post['mode'];
			$fields['request_flg'] = $post['request_flg'];
			if ($post['mode'] == 0) {
				$fields['chat_user_id'] = null;
				$fields['request_flg'] = 0;
			}
			DB::update('t_bot_member')->set($fields)
				->where('bot_id', '=', $bot_id)
				->where('member_id', '=', $post['id'])
				->execute();
			$this->response->body($this->jresult("00", NULL, NULL));
		}
		else {
			$this->response->body($this->jresult("01"));
		}
	}

	// ユーザFollow登録API
	public function action_follow()
	{
		//$post = $this->request->query();
		$post = $this->request->query();
		if ($post){
			$member = ORM::factory('botmember', $post['id']);
			$member->follow_flg = 1;
			$member->save();
			$this->response->body($this->jresult("00", NULL, NULL));
		}
		else {
			$this->response->body($this->jresult("01"));
		}
	}

	// ユーザUnFollow登録API
	public function action_unfollow()
	{
		//$post = $this->request->query();
		$post = $this->request->query();
		if ($post){
			$member = ORM::factory('botmember', $post['id']);
			$member->follow_flg = 0;
			$member->save();
			$this->response->body($this->jresult("00", NULL, NULL));
		}
		else {
			$this->response->body($this->jresult("01"));
		}
	}

	private function get_member_name($member)
	{
		if ($member->name != NULL) return $member->name;
		if ($member->first_name != NULL && $member->first_name != '') return $member->last_name . ' ' . $member->first_name;
		return '';
	}
	
	// MSG履歴登録API
	public function action_log()
	{
        $post = $this->request->post();
		$log_info = [];
        if ($post) {
            $log_info = json_decode($post['data']);
			$this->log_sub($log_info);
        } else {
			$this->response->body($this->jresult("01"));
		}
    }

	public function log_sub($log_info)
	{
		$ret = array();
		{
			if ($log_info->id != NULL && $log_info->id != '') {
				$bot_info = $this->getBotIdByFacilityCd($log_info->facility_cd, "action_log");
				$bot_id = $bot_info['bot_id'];
				// ユーザ情報取得
				$member_name = "";
				$member_request_flg = 0;	// 2020.05.06 戚广智要求增加
				$is_tester = 0;
				$chat_mode = 0;
				$chat_mode_fix = 0;
				$lang_cd = $log_info->lang;
				
				$member = ORM::factory('botmember')->where('bot_id', '=', $bot_info['bot_id'])->where('member_id', '=', $log_info->id)->find();
				if (isset($member->member_id) && $log_info->intent_cd != 'member_start_chat' && $log_info->intent_cd != 'member_end_chat' && $log_info->intent_cd != 'push') {
					$member_name = $this->get_member_name($member);
					$chat_mode = $member->chat_mode;
					$is_tester = $member->is_tester;
					$member_request_flg = $member->request_flg;	// 2020.05.06 戚广智要求增加
					$fields = array();
					$fields['last_talk_date'] = $log_info->send_time;
					$fields['lang_cd'] = $log_info->lang;
					// 有人対応固定の場合
					if (isset($log_info->chat_mode) && $log_info->chat_mode != NULL && $log_info->chat_mode == 1) {
						$chat_mode_fix = 1;
						if ($chat_mode == 0) {
							$chat_mode = 1;
							$fields['chat_mode'] = $chat_mode;
						}
					}
					// 2020.08.10 #4982 begin
					if ($log_info->member_msg != "") {
						$fields['chat_msg_count'] = $member->chat_msg_count + 1;
					}
					// 2020.08.10 #4982 end
					// 2020.01.05 #7304 begin
					if (isset($log_info->secret_mode)) {
						$fields['secret_mode'] = $log_info->secret_mode;
					}
					// 2020.01.05 #7304 end
					DB::update('t_bot_member')->set($fields)
					->where('bot_id', '=', $bot_info['bot_id'])
					->where('member_id', '=', $log_info->id)
					->execute();
				}
				else if (isset($member->member_id)){
					$member_name = $this->get_member_name($member);
					$is_tester = $member->is_tester;
					$chat_mode = $member->chat_mode;
					$member_request_flg = $member->request_flg;	// 2020.05.06 戚广智要求增加
				}
				
				$message = $log_info->member_msg;
				$message_t = "";
				
				// intent question translate
				/*
				 if ($log_info->intent_cd != 'input.unknown' && $log_info->lang != 'ja') {
				 $model = Model::factory('apimodel');
				 $message_t = $model->get_intent_ques_by_cd($bot_info['bot_id'], $log_info->intent_cd, "ja");
				 }
				 */
				
				// google translate
				if (($chat_mode == 2 && $log_info->intent_cd == 'member_start_chat' && $log_info->intent_cd == 'member_end_chat') ||
					(($log_info->intent_cd == 'input.unknown' || $log_info->intent_cd == 'recommend_intents_aimai' || $log_info->intent_cd == 'human mode')
								&& $log_info->lang != 'ja')) {
					//$org = '原文:';
					$google = Model::factory('google');
					$message_t = $google->translate($log_info->member_msg, "ja");
				}								

				// 有人対応履歴記入
				$log_chat = ORM::factory('botlogchat');
				$log_chat->member_id = $log_info->id;
				$log_chat->sns_type_cd = $log_info->sns_type;
				$log_chat->intent_cd = $log_info->intent_cd;
				$log_chat->sub_intent_cd = $log_info->sub_intent_cd;
				$log_chat->area_cd = $log_info->area_cd;
				$log_chat->bot_id = $bot_info['bot_id'];
				//wuzhao delete 2019.08.24 $log_chat->item_id = $item_info['item_id'];
				$log_chat->log_time = $log_info->send_time;
				$log_chat->member_msg = $message;
				$log_chat->member_msg_t = $message_t;
				if(is_object($log_info->bot_msg)) {
					$log_chat->bot_msg = json_encode($log_info->bot_msg, JSON_UNESCAPED_UNICODE);
				}
				else {
					$log_chat->bot_msg = $log_info->bot_msg;
				}

				// 2023.02.04 #37133 begin
				if (isset($log_info->_talkappi_inf)) {
					if ($log_info->_talkappi_inf->type == "autoanswer_complete") {
						$log_chat->bot_msg = "";
					}
				}
				// 2023.02.04 #37133 end

				$log_chat->score = $log_info->score;
				$log_chat->lang_cd = $log_info->lang;
				if ($log_chat->intent_cd == 'input.unknown' && $log_chat->member_msg != '' || $log_chat->intent_cd == 'request_operator_yes') {
					$log_chat->log_flg = 1;
				}
				if ($chat_mode_fix == 1) $log_chat->log_flg = 1;
				$log_chat->member_name = $member_name;
				$log_chat->request_flg = $member_request_flg;	// 2020.05.06 戚广智要求增加
				$log_chat->chat_mode = $chat_mode;
				if (isset($log_info->is_change_intent) && $log_info->is_change_intent != NULL && $log_info->is_change_intent == "true") {
					$log_chat->is_change_intent = 1;
				} else {
					$log_chat->is_change_intent = 0;
				}
				$log_chat->is_tester = $is_tester;
				$log_chat->scene_cd = $log_info->latest_scene_cd;
				if (isset($member->member_id)) {
					$log_chat->country_cd = $member->country_cd;
					$log_chat->chat_online_status = $member->chat_online_status;
					$log_chat->member_tag = $member->tags;
					if ($member->chat_mode != 0) $log_chat->user_id = $member->chat_user_id;
				}
				if (isset($log_info->mobile)) {
					$log_chat->mobile = $log_info->mobile;
				}
				$log_chat->answer_type = $log_info->answer_type;
				// 2023.01.26 #36870 begin
				// 2023.02.04 #37133 begin
				$flg_not_save_t_bot_log = 0;
				if (isset($log_info->_talkappi_inf)) {
					if ($log_info->_talkappi_inf->type == "autoanswer_complete") {
						// 特殊处理，不保存t_bot_log
						$flg_not_save_t_bot_log = 1;

						// 特殊处理，写link_type,link_id,link_log_id
						$flowdata_orm = ORM::factory('flowdata')
							->where('bot_id', '=', $log_info->_talkappi_inf->bot_id)
							->where('member_id', '=', $log_info->_talkappi_inf->member_id)
							->where('lf', '=', $log_info->_talkappi_inf->_lf)
							->find();

						$extra_data_obj = json_decode($flowdata_orm->extra_data);
						if (isset($extra_data_obj->log_id_t) && isset($extra_data_obj->log_table_m)) {
							$log_chat->link_log_id = $extra_data_obj->log_id_t;
							$log_chat->link_type = $log_info->_talkappi_inf->link_type;
							$log_chat->link_id = $log_info->_talkappi_inf->link_id;
						}
					}
				}
				// 2023.02.04 #37133 end
				// 2023.01.26 #36870 end

				$log_chat->save();

				if ($flg_not_save_t_bot_log == 0) {
					// 履歴記入
					// 2020.04.03 $log = ORM::factory('botlog');	// TODO 等将来t_bot_log废弃时，注释掉此行，换成$log = (object)[];;
					// 2020.04.03 $log->log_id = $log_chat->log_id; // TODO 等将来t_bot_log废弃时，注释掉此行
					$log = (object)[]; // 2020.04.03 add
					$log->member_id = $log_info->id;
					$log->sns_type_cd = $log_info->sns_type;
					$log->intent_cd = $log_info->intent_cd;
					$log->sub_intent_cd = $log_info->sub_intent_cd;
					$log->area_cd = $log_info->area_cd;
					$log->bot_id = $bot_info['bot_id'];
					//wuzhao delete 2019.08.24 $log->item_id = $item_info['item_id'];	
					$log->log_time = $log_info->send_time;
					$log->member_msg = $message;
					$log->member_msg_t = $message_t;
					if(is_object($log_info->bot_msg)) {
						$log->bot_msg = json_encode($log_info->bot_msg, JSON_UNESCAPED_UNICODE);
					}
					else {
						$log->bot_msg = $log_info->bot_msg;
					}
					$log->score = $log_info->score;
					$log->lang_cd = $log_info->lang;
					if ($log->intent_cd == 'input.unknown' && $log->member_msg != '' || $log->intent_cd == 'request_operator_yes') {
						$log->log_flg = 1;
					}
					if ($chat_mode_fix == 1) $log->log_flg = 1;
					if (isset($log_info->is_change_intent) && $log_info->is_change_intent != NULL && $log_info->is_change_intent == "true") {
						$log->is_change_intent = 1;
					} else {
						$log->is_change_intent = 0;
					}
					$log->is_tester = $is_tester;
					if (isset($member->member_id)) {
						if ($member->chat_mode != 0) $log->user_id = $member->chat_user_id;
					}
					$log->scene_cd = $log_info->latest_scene_cd;
					if ($log_info->context_id !="") {
						$log->context_id = $log_info->context_id;
					}
					$log->answer_type = $log_info->answer_type;
					// 2020.04.03 $log->save();	// TODO 等将来t_bot_log废弃时，注释掉此行

					// 2020.02.22 t_bot_log分割
					{
						$log_table_id = $bot_info['bot_id'];
						if ($log_table_id == 401002 || $log_table_id == 401003) {
							$log_table_id = 401001;
						} else {
							$model = Model::factory('apimodel');
							$parent_bot_id = $model->get_grp_parent_bot_id($log_table_id);
							if ($parent_bot_id != -1) {
								$log_table_id = $parent_bot_id;
							}
						}
						$table_name = "t_bot_log_$log_table_id";

						// 2020.05.12 add begin for 后续update　answer_type
						$ret["log_id"] =  $log_chat->log_id;
						$ret["table"] =  $table_name;
						// 2020.05.12 add end

						$fields = "(log_id, member_id, sns_type_cd, intent_cd, bot_id, log_time, member_msg, member_msg_t,bot_msg,score,lang_cd,is_change_intent,is_tester,scene_cd";
						$values = "(:log_id, :member_id, :sns_type_cd, :intent_cd, :bot_id, :log_time, :member_msg, :member_msg_t,:bot_msg,:score,:lang_cd,:is_change_intent,:is_tester,:scene_cd";
						if ($log->sub_intent_cd !== NULL) {
							$fields = "$fields, sub_intent_cd";
							$values = "$values, :sub_intent_cd";
						}
						if ($log->area_cd !== NULL) {
							$fields = "$fields, area_cd";
							$values = "$values, :area_cd";
						}
						if (isset($log->log_flg) && $log->log_flg !== NULL) {	 // 2020.04.03 add
							$fields = "$fields, log_flg";
							$values = "$values, :log_flg";
						}
						if (isset($log->user_id) && $log->user_id !== NULL) {	 // 2020.04.03 add
							$fields = "$fields, user_id";
							$values = "$values, :user_id";
						}
						if (isset($log->context_id) && $log->context_id !== NULL) {	 // 2020.04.03 add
							$fields = "$fields, context_id";
							$values = "$values, :context_id";
						}
						if ($log->answer_type !== NULL) {
							$fields = "$fields, answer_type";
							$values = "$values, :answer_type";
						}
						$fields = "$fields)";
						$values = "$values)";
						$sql = "INSERT INTO $table_name
								$fields
								VALUES
								$values
								";
						$query = DB::query(Database::UPDATE, $sql);
						$parameters = array(
							':log_id' => $log_chat->log_id,
							':member_id' => $log->member_id,
							':sns_type_cd' => $log->sns_type_cd,
							':intent_cd' => $log->intent_cd,
							':sub_intent_cd' => $log->sub_intent_cd,
							':area_cd' => $log->area_cd,
							':bot_id' => $log->bot_id,
							':log_time' => $log->log_time,
							':member_msg' => $log->member_msg,
							':member_msg_t' => $log->member_msg_t,
							':bot_msg' => $log->bot_msg,
							':score' => $log->score,
							':lang_cd' => $log->lang_cd,
							':is_change_intent' => $log->is_change_intent,
							':is_tester' => $log->is_tester,
							':scene_cd' => $log->scene_cd,
							':answer_type' => $log->answer_type,
						);
						if (isset($log->log_flg) && $log->log_flg !== NULL) {	 // 2020.04.03 add
							$parameters[':log_flg'] = $log->log_flg;
						}
						if (isset($log->user_id) && $log->user_id !== NULL) {	 // 2020.04.03 add
							$parameters[':user_id'] = $log->user_id;
						}
						if (isset($log->context_id) && $log->context_id !== NULL) {	 // 2020.04.03 add
							$parameters[':context_id'] = $log->context_id;
						}
						$query->parameters($parameters);

						$query->execute();

						// 2022.01.13 #16281 ユーザー属性自動付け機能 begin
						if ($log->member_msg != "" && $log->member_msg != NULL) {
							$model = Model::factory('apimodel');
							$model->create_update_member_attr("chatbot", $table_name, $log_chat->log_id);
						}
						// 2022.01.13 #16281 ユーザー属性自動付け機能 end
					}
				}	
				
				// 2023.01.26 #36870 begin
				if (isset($log_info->_talkappi_inf)) {
					if ($log_info->_talkappi_inf->type == "flow_log_id_register") {
						$temp_data = $log_info->_talkappi_inf->data;
						$temp_data->log_id_t = $log_chat->log_id;

						$model = Model::factory('apimodel');
						$model->set_flow_data_extra_data($log_info->_talkappi_inf->bot_id, $log_info->_talkappi_inf->member_id, $log_info->_talkappi_inf->_lf, $temp_data);
					} else if ($log_info->_talkappi_inf->type == "ai") {
						// 2023.05.23 #38860
						$extent_inf = (object)[];
						$extent_inf->ai = $log_info->_talkappi_inf->ai_type;
						/* 2023.06.21 
						if ($log_info->_talkappi_inf->model == "gpt-3.5-turbo") {
							$extent_inf->m = "g35t";
						} else if ($log_info->_talkappi_inf->model == "Davinci") {
							$extent_inf->m = "davi";
						} else {
							$extent_inf->m = $log_info->_talkappi_inf->model;
						}
						*/
						$extent_inf->m = $log_info->_talkappi_inf->db_model;
						$extent_inf->p = intval($log_info->_talkappi_inf->parent_log_id);
						$extent_inf->t = substr($log_info->_talkappi_inf->parent_log_table, strlen("t_bot_log_"));

						$extent = ORM::factory('botlogextent');
						$extent->log_id = $ret["log_id"];
						$extent->extent_inf = json_encode($extent_inf, JSON_UNESCAPED_UNICODE);
						$extent->save();
					}
				}
				// 2023.01.26 #36870 end
			}
			else {
				$this->response->body($this->jresult("01"));
				return;
			}
			$this->response->body($this->jresult("00", NULL, $ret));
			return;
		}
	}

	//
	public function action_class()
	{
		$post = $this->request->query();
		$model = Model::factory('apimodel');
		if ($post){
			$kind = NULL;
			if (array_key_exists('kind', $post)) {
				$kind = $post['kind'];
			}
			$items = $model->get_item_class($post['lang'], $post['facility_cd'], $kind);
			$this->response->body($this->jresult("00", NULL, $items));
			return;
		}
		else {
			$this->response->body($this->jresult("01"));
		}
	}
	public function action_classtype()
	{
		$post = $this->request->query();
		$model = Model::factory('apimodel');
		if ($post){
			$items = $model->get_item_classtype($post['lang'], $post['facility_cd'], $post['class_cd']);
			$this->response->body($this->jresult("00", NULL, $items));
			return;
		}
		else {
			$this->response->body($this->jresult("01"));
		}
	}

	// 施設のお勧めスポットリスト取得
	public function action_spots()
	{
		// 此函数已经不使用
		$post = $this->request->query();
		$model = Model::factory('apimodel');
		$bot_info = $this->getBotInfo($post['facility_cd']);
		if ($post){
			$items = $model->get_item_relation($post['lang'], $bot_info, $post['class_cd'], $post['class_type_cd']);
			$this->response->body($this->jresult("00", NULL, $items));
			return;
		}
		else {
			$this->response->body($this->jresult("01"));
		}
	}

	// 施設のお勧めスポットリスト取得
	public function action_spots_new()
	{
		$post = $this->request->query();
		$model = Model::factory('apimodel');
		$bot_info = $this->getBotInfo($post['facility_cd']);
		if ($post){
			$item_id_history = $post['item_id_history'];
			if (substr($post['item_id_history'], 0, 2) == "ID") {
				$user_item_id_history = DB::select('item_id_history')->from('t_bot_user_item_history')
					->where('id', '=', $post['item_id_history'])
					->execute()->as_array();
				$item_id_history = $user_item_id_history[0]['item_id_history'];
			};
			$items = $model->get_item_relation_new($post['lang'], $bot_info, $post['class_cd'], $post['class_type_cd'], $item_id_history, NULL);
			$this->response->body($this->jresult("00", NULL, $items));
			return;
		}
		else {
			$this->response->body($this->jresult("01"));
		}
	}

	// wuzhao add begin #183
	// 施設のお勧めスポットリスト取得
	public function action_spots_class_type_sub_cd()
	{
		// 此函数已经不使用
		$post = $this->request->query();
		$model = Model::factory('apimodel');
		$bot_info = $this->getBotInfo($post['facility_cd']);
		if ($post){
			$items = $model->get_item_relation($post['lang'], $bot_info, $post['class_cd'], $post['class_type_cd'], $post['class_type_sub_cd']);
			$this->response->body($this->jresult("00", NULL, $items));
			return;
		}
		else {
			$this->response->body($this->jresult("01"));
		}
	}
	// wuzhao add end #183
	public function action_spots_class_type_sub_cd_new()
	{
		$post = $this->request->query();
		$model = Model::factory('apimodel');
		$bot_info = $this->getBotInfo($post['facility_cd']);
		if ($post){
			$item_id_history = $post['item_id_history'];
			if (substr($post['item_id_history'], 0, 2) == "ID") {
				$user_item_id_history = DB::select('item_id_history')->from('t_bot_user_item_history')
					->where('id', '=', $post['item_id_history'])
					->execute()->as_array();
				$item_id_history = $user_item_id_history[0]['item_id_history'];
			};
			if (isset($post['class_type_sub_cd'])) {
				$class_type_sub_cd = $post['class_type_sub_cd'];
			} else {
				$class_type_sub_cd = NULL;
			}
			$items = $model->get_item_relation_new($post['lang'], $bot_info, $post['class_cd'], $post['class_type_cd'], $item_id_history, $class_type_sub_cd);
			$this->response->body($this->jresult("00", NULL, $items));
			return;
		}
		else {
			$this->response->body($this->jresult("01"));
		}
	}

	public function action_get_items_by_condition()
	{
		$post = $this->request->query();
		$model = Model::factory('apimodel');
		$bot_info = $this->getBotInfo($post['facility_cd']);
		if ($post){
			$item_id_history = $post['item_id_history'];
			if (substr($post['item_id_history'], 0, 2) == "ID") {
				$user_item_id_history = DB::select('item_id_history')->from('t_bot_user_item_history')
					->where('id', '=', $post['item_id_history'])
					->execute()->as_array();
				$item_id_history = $user_item_id_history[0]['item_id_history'];
			};

			if (isset($post['class_cd'])) {
				$class_cd = $post['class_cd'];
			} else {
				$class_cd = NULL;
			}
			if (isset($post['class_type_cd'])) {
				$class_type_cd = $post['class_type_cd'];
			} else {
				$class_type_cd = NULL;
			}
			if (isset($post['class_type_sub_cd'])) {
				$class_type_sub_cd = $post['class_type_sub_cd'];
			} else {
				$class_type_sub_cd = NULL;
			}
			if (isset($post['features'])) {
				$features = $post['features'];
			} else {
				$features = NULL;
			}

			$items = $model->get_items_by_condition($post, $post['lang'], $bot_info, $class_cd, $class_type_cd, $class_type_sub_cd, $features, $item_id_history);
			$this->response->body($this->jresult("00", NULL, $items));
			return;
		}
		else {
			$this->response->body($this->jresult("01"));
		}
	}

	// 施設のお勧めスポットリスト取得
	public function action_spotshot()
	{
		$post = $this->request->query();
		$model = Model::factory('apimodel');
		$bot_info = $this->getBotInfo($post['facility_cd']);
		if ($post){
			$items = $model->get_hot_item_relation($post['lang'], $bot_info);
			$this->response->body($this->jresult("00", NULL, $items));
			return;
		}
		else {
			$this->response->body($this->jresult("01"));
		}
	}

	// 受付登録API
	public function action_service()
	{
		//$post = $this->request->query();
		$post = $this->request->query();
		if ($post){
			$log_info = json_decode($post['data']);
			$bot_info = $this->getBotIdByFacilityCd($log_info->facility_cd, "action_service");
            $bot_id = $bot_info['bot_id'];
			$log = ORM::factory('botservice');
			$log->member_id = $log_info->id;
			//$log->sns_type_cd = $log_info->sns_type;
			$log->intent_cd = $log_info->intent_cd;
			$log->bot_id = $bot_id;
			$log->log_time = $log_info->send_time;
			$log->member_msg = $log_info->member_msg;
			//$log->bot_msg= $log_info->bot_msg;
			$log->lang_cd = $log_info->lang;
			$log->reserve1= $log_info->room;
			$log->reserve2= $log_info->name;
			$log->service_status_cd = '01';
			$log->save();
			$this->response->body($this->jresult("00", NULL, NULL));
			return;
		}
		else {
			$this->response->body($this->jresult("01"));
		}
	}

	// 受付登録API
	// 2021.06.03 #12976 增加db_sns_type
	public function action_service2()
	{
		$post = $this->request->post();
		if ($post){
			$log_info = json_decode($post["condition"]);
			$bot_info = $this->getBotIdByFacilityCd($log_info->facility_cd, "action_service2");
            $bot_id = $bot_info['bot_id'];
			// 2022.12.12 #35218 begin
			if (isset($log_info->dest_bot_id) && $log_info->dest_bot_id != "") {
				$bot_id = $log_info->dest_bot_id;
			}
			// 2022.12.12 #35218 end
			$log = ORM::factory('botservice');
			$log->member_id = $log_info->id;
			//$log->sns_type_cd = $log_info->sns_type;
			$log->intent_cd = $log_info->intent_cd;
			$log->bot_id = $bot_id;
			$log->log_time = $log_info->send_time;
			$log->service_msg = $log_info->service_msg;
			$log->member_msg = $log_info->member_msg;
			//$log->bot_msg= $log_info->bot_msg;
			$log->lang_cd = $log_info->lang;
			$log->reserve1= $log_info->room;
			$log->reserve2= $log_info->name;
			$log->service_status_cd = '01';
			$log->reserve_date= $log_info->reserve_date;
			$log->reserve_time= $log_info->reserve_time;
			// 2021.06.03 #12976 增加db_sns_type
			$log->sns_type_cd= $log_info->sns_type_cd;

			$service_data = json_decode($log_info->service_data,true);
			foreach($service_data as $k=>$v) {
				$pos = strpos($k, ".translate");
				if ($pos!==false) {
					$key_only = substr($k, 0, $pos);
					$google_model = Model::factory('google');
					$service_data[$key_only . '.ja'] = $google_model->translate($service_data[$k], 'ja');
					$service_data[$key_only . '.en'] = $google_model->translate($service_data[$k], 'en');
					$service_data[$key_only . '.cn'] = $google_model->translate($service_data[$k], 'zh-CN');
					$service_data[$key_only . '.tw'] = $google_model->translate($service_data[$k], 'zh-TW');
					$service_data[$key_only . '.kr'] = $google_model->translate($service_data[$k], 'ko');
				}
			}
			$log->service_data=json_encode($service_data, JSON_UNESCAPED_UNICODE);
			//$log->service_data= $log_info->service_data;
			
			$log->save();
			$service_id = $log->service_id;
			$ret = array();
			$ret["service_id"] = $service_id;
			
			$this->response->body($this->jresult("00", NULL, $ret));
			return;
		}
		else {
			$this->response->body($this->jresult("01"));
		}
	}
	
	// wuzhao add begin #85
	/*
	// bot言語一覧取得API
	public function action_languagelist()
	{
		$post = $this->request->query();
		if ($post){
			$parameters = json_decode($post['data']);
			$item = ORM::factory('item')
				->where('item_cd', '=', $parameters->facility_cd)
				->where('delete_flg', '=', 0)
				->find();
			$bot = ORM::factory('bot')
				->where('bot_id', '=', $item->bot_id)
				->where('delete_flg', '=', 0)
				->find();
			/_*
			$ret = array();
			$ret['lang_cd']= $bot->lang_cd;
			$ret['tel']= $bot->tel;
			*_/
			$ret = $bot->as_array();
			$this->response->body($this->jresult("00", NULL, $ret));
		}
		else {
			$this->response->body($this->jresult("01"));
		}
		return;
	}
	*/
	// wuzhao add end #85

	// wuzhao add begin #63
	// 管理員情報取得API
	public function action_adminusers()
	{
		$post = $this->request->query();
		$model = Model::factory('apimodel');
		if ($post){
			$parameters = json_decode($post['data']);
			$bot_info = $this->getBotIdByFacilityCd($parameters->facility_cd, "action_adminusers");
			$users = $model->get_users($bot_info['bot_id']);
			$this->response->body($this->jresult("00", NULL, $users));
			return;
		}
		else {
			$this->response->body($this->jresult("01"));
		}
	}
	// wuzhao add end #63

	// wuzhao add begin #111
	// bot question&answer あいまい検索API
	public function action_bot_question_answer_aimai()
	{
		$post = $this->request->query();
		$model = Model::factory('apimodel');
		if ($post){
			$bot_info = $this->getBotIdByFacilityCd($post['facility_cd'], "action_bot_question_answer_aimai");

			if (isset($post['original_inquiry'])) {
				$original_intent_cd = $post['original_inquiry'];
			} else {
				$original_intent_cd = "";
			}

			$type_cd = "";
			if (isset($post["type_cd"])) {
				$type_cd = $post["type_cd"];
			}
		
			// 2022.01.21 #20895 增加limit参数
			$limit = 11;
			if (array_key_exists("limit", $post)) {
				$limit = $post['limit'];
			}
			$intents = $model->get_intent_list_aimai($post['lang'], $bot_info['bot_id'], $post['keywords'], $original_intent_cd, $type_cd, $limit);
			$this->response->body($this->jresult("00", NULL, $intents));
		}
		else {
			$this->response->body($this->jresult("01"));
		}
		return;
	}

	// bot itemname&sell_pointあいまい検索API
	public function action_item_description_aimai()
	{
		$post = $this->request->query();
		$model = Model::factory('apimodel');
		if ($post){
			// 2021.12.19 增加limit参数，作为api提供给外部使用
			$limit = 6;
			if (array_key_exists("limit", $post)) {
				$limit = $post['limit'];
			}
			$bot_id = "";
			if (array_key_exists("bot_id", $post)) {
				$bot_id = $post['bot_id'];
			}
			if ($bot_id == "") {
				$bot_info = $this->getBotInfo($post['facility_cd']);
			} else {
				$bot_info = [
					"bot_id" => $bot_id,
				];
			}
			$items = $model->get_item_description_aimai($post['lang'], $bot_info, $post['keywords'], $limit);
			$this->response->body($this->jresult("00", NULL, $items));
		}
		else {
			$this->response->body($this->jresult("01"));
		}
		return;
	}

	public function action_item_description()
	{
		$post = $this->request->query();
		$model = Model::factory('apimodel');
		if ($post){
			$items = $model->get_item_description($post);
			$this->response->body($this->jresult("00", NULL, $items));
		}
		else {
			$this->response->body($this->jresult("01"));
		}
		return;
	}
	// wuzhao add end #111

	public function action_get_message()
	{
		$post = $this->request->query();
		$model = Model::factory('apimodel');
		if ($post){
			$bot_info = $this->getBotIdByFacilityCd($post['facility_cd'], "action_get_message");
            $bot_id = $bot_info['bot_id'];
			
			$items = $model->get_bot_message($bot_id, $post['id'], $post['lang']);
			$this->response->body($this->jresult("00", NULL, $items));
		}
		else {
			$this->response->body($this->jresult("01"));
		}
		return;
	}

	private function getExperienceId($cd)
	{
		$item = DB::select('bot_id', 'experience_id')->from('t_experience')
			->where('experience_cd', '=', $cd)
			->execute()->as_array();
		if (count($item) == 0) {
			return array('experience_id'=>'', 'bot_id'=>'');
		}
		else {
			return array('experience_id'=>$item[0]['experience_id'], 'bot_id'=>$item[0]['bot_id']);
		}
	}

	private function getBotIdByFacilityCd($facility_cd, $src)
	{
		if ($facility_cd == "99999999") {
			return array('bot_id'=>'999');
		}
		$record = DB::select('bot_id')->from('t_bot')
			->where('facility_cd', '=', $facility_cd)
			->execute()->as_array();
		if (count($record) == 0) {
			error_log("getBotIdByFacilityCd()1 cannot get facility_cd for $facility_cd from $src");
			return array('bot_id'=>'');
		}
		else {
			return array('bot_id'=>$record[0]['bot_id']);
		}
	}
	
	private function getBotInfo($item_cd)
	{
		if ($item_cd == "99999999") {
			return array('bot_id'=>'999');
		}
		$record = DB::select('bot_id')->from('t_bot')
			->where('facility_cd', '=', $item_cd)
			->execute()->as_array();
		if (count($record) == 0) {
			return array('bot_id'=>'');
		}
		else {
			return array('bot_id'=>$record[0]['bot_id']);
		}


		/* 2019.10.23 去除t_item_relation以及各bot在t_item表的定义数据
		$item = DB::select('bot_id', 'item_id')->from('t_item')
			->where('item_cd', '=', $item_cd)
			->where('item_div', '=', 1)
			->execute()->as_array();
		if (count($item) == 0) {
			return array('item_id'=>'', 'bot_id'=>'');
		}
		else {
			return array('item_id'=>$item[0]['item_id'], 'bot_id'=>$item[0]['bot_id']);
		}
		*/

	}

	/* 2019.10.23 去除t_item_relation以及各bot在t_item表的定义数据
	private function getItemCd($item_id)
	{
		$item = DB::select('item_cd')->from('t_item')
			->where('item_id', '=', $item_id)
			->execute()->as_array();
		if (count($item) == 0) {
			return '';
		}
		else {
			return $item[0]['item_cd'];
		}
	}
	*/

	private function jresult($result_cd, $reason_cd = NULL, $data = NULL)
	{
		$result_array = array('result'=>$result_cd, 'reason'=>$reason_cd, 'data'=>$data);
		return json_encode($result_array);
	}

	// wuzhao add begin #165
	public function action_scan_scene()
	{
		$post = $this->request->post();
		if ($post){
			$follow = ORM::factory('botfollow');
			$follow->follow_time = date('Y-m-d H:i:s');
			$follow->sns_type_cd = $post['sns_type_cd'];
			$follow->sns_id = $post['sns_id'];
			$follow->member_id = $post['member_id'];
			$follow->bot_id = $post['bot_id'];
			$follow->scene = $post['scene'];
			if (isset($post["lang_cd"])) {
				$follow->lang_cd = $post['lang_cd'];
			}
			if (isset($post["country_cd"])) {
				$follow->country = $post["country_cd"];
			}
			if (isset($post["timezone"])) {
				$follow->timezone = $post["timezone"];
			}
			if (isset($post["ua_info"])) {
				// web chat
				$ua_info = json_decode($post['ua_info']);
				$follow->ip_address = $ua_info->ip_address;
				$follow->country = $ua_info->country;
				$follow->timezone = $ua_info->timezone;
				$follow->local_time = $ua_info->local_time;
				$follow->browser = $ua_info->browser;
				$follow->mobile = $ua_info->mobile;
				$follow->language = $ua_info->language;
			}
			// 2020.10.23
			if (isset($post["extra_info"])) {
				// line
				$follow->extra_info = $post["extra_info"];
			}
            // 2022.02.03 #20626 begin
			if (isset($post["refer"])) {
				$follow->refer = $post["refer"];
			}
            // 2022.02.03 #20626 end

			$follow->save();
			$this->response->body($this->jresult("00", NULL, NULL));
			return;
		}
		else {
			$this->response->body($this->jresult("01"));
		}
	}
	public function action_get_last_scene()
	{
		$post = $this->request->post();
		if ($post){
			$ret = array();
			if ($post['time_limit'] == 0) {
				$bot_info = $this->getBotIdByFacilityCd($post['facility_cd'], "action_get_last_scene");
				$bot_id = $bot_info['bot_id'];
				$bot_id_list = "";
				if (array_key_exists("bot_id_list", $post)) {
					$bot_id_list = $post['bot_id_list'];
				}

				// 2022.06.02 begin
				$range = "";
				if (array_key_exists("range", $post)) {
					$range = $post['range'];
				}
				if ($range == "all") {
					$follow_time = date('Y-m-d H:i:s');
					if (array_key_exists("follow_time", $post)) {
						$follow_time = $post['follow_time'];
					}
					$follows = DB::select('bot_id', 'scene', 'lang_cd', 'follow_time', 'bot_id', 'sns_id', 'extra_info', 'process_time')->from('t_bot_follow')
					->where('sns_type_cd', '=', $post['sns_type_cd'])
					->where('sns_id', '=', $post['sns_id'])
					->where('member_id', '=', $post['member_id'])
					->where('follow_time', '<', $post['follow_time'])
					->order_by('follow_time', 'DESC')
					->limit(1)
					->execute()->as_array();
				} else
				// 2022.06.02 end

				if ($bot_id_list == "") {
					$follows = DB::select('bot_id', 'scene', 'lang_cd', 'follow_time', 'bot_id', 'sns_id', 'extra_info', 'process_time')->from('t_bot_follow')
					->where('sns_type_cd', '=', $post['sns_type_cd'])
					->where('sns_id', '=', $post['sns_id'])
					->where('member_id', '=', $post['member_id'])
					->where('bot_id', '=', $bot_id)
					->order_by('follow_time', 'DESC')
					->limit(1)
					->execute()->as_array();
				} else {
					$sql = "SELECT * FROM t_bot_follow
					WHERE
						sns_type_cd = :sns_type_cd
						AND sns_id = :sns_id
						AND member_id = :member_id
						AND bot_id in ($bot_id_list)
					ORDER BY
						follow_time DESC
					LIMIT 1
					";
					$query = DB::query(Database::SELECT, $sql);
					$query->parameters(array(
							':sns_type_cd' => $post['sns_type_cd'],
							':sns_id' => $post['sns_id'],
							':member_id' => $post['member_id'],
					));
					$follows = $query->execute()->as_array();
				}
			} else {
				$follows = DB::select('bot_id', 'scene', 'lang_cd', 'follow_time', 'bot_id', 'sns_id', 'extra_info', 'process_time')->from('t_bot_follow')
				->where('sns_type_cd', '=', $post['sns_type_cd'])
				->where('sns_id', '=', $post['sns_id'])
				->where('member_id', '=', $post['member_id'])
				->where('follow_time', '>=', date('Y-m-d H:i:s', strtotime("-1 hours")))
				->order_by('follow_time', 'DESC')
				->limit(1)
				->execute()->as_array();

				// 2022.04.17 add begin
                if (count($follows) > 0) {
					$fields = array();
					$fields['process_time'] = date('Y-m-d H:i:s');
					DB::update('t_bot_follow')->set($fields)
						->where('follow_time', '=', $follows[0]['follow_time'])
						->where('sns_type_cd', '=', $post['sns_type_cd'])
						->where('sns_id', '=', $follows[0]['sns_id'])
						->where('member_id', '=', $post['member_id'])
						->execute();
                }
				// 2022.04.17 add end
			}

			if (count($follows) > 0) {
				$bot = ORM::factory('bot', $follows[0]['bot_id']);
				$ret['facility_cd'] = $bot->facility_cd;
				$ret['scene'] = $follows[0]['scene'];
				$ret['lang_cd'] = $follows[0]['lang_cd'];
				$ret['follow_time'] = $follows[0]['follow_time'];
				$ret['bot_id'] = $follows[0]['bot_id'];
				$ret['sns_id'] = $follows[0]['sns_id'];
				$ret['extra_info'] = $follows[0]['extra_info'];
				$ret['process_time'] = $follows[0]['process_time'];
				$ret['cur_time'] = date('Y-m-d H:i:s');
			}

			$this->response->body($this->jresult("00", NULL, $ret));
			return;
		}
		else {
			$this->response->body($this->jresult("01"));
		}
	}
	// wuzhao add end #165

	// wuzhao add end #169
	public function action_survey()
	{
		$post = $this->request->post();

		$sub_intent_cd = $post['sub_intent_cd'];
		if ($sub_intent_cd === "") {
			$sub_intent_cd = NULL;
		} else if ($sub_intent_cd === "NONE_SUB_INTENT_CD") {
			$sub_intent_cd = "";
		}
		$area_cd = $post['area_cd'];
		if ($area_cd === "") {
			$area_cd = ""; // 不允许为NULL
		} else if ($area_cd === "NONE_AREA_CD") {
			$area_cd = "";
		}
		// 2021.08.11 t_bot_survey增加context_id字段 begin
		$context_id = "";
        if (isset($post["context_id"])) {
			$context_id = $post["context_id"];
		}
		// 2021.08.11 t_bot_survey增加context_id字段 end
		// 2021.08.19 t_bot_survey增加scene_cd字段 #15371 begin
		$scene_cd = "";
        if (isset($post["scene_cd"])) {
			$scene_cd = $post["scene_cd"];
		}
		// 2021.08.19 #15371 end
		if ($post){
			// 2023.05.27 #21060 begin
			$intval_answer = intval($post['answer']);
			if ($intval_answer == 1 || $intval_answer == 2) {
				$check_time = date('Y-m-d H:i:s', strtotime("-1 days"));
				$record = ORM::factory('botsurvey')
					->where('sns_type_cd', '=', $post['sns_type_cd'])
					->where('sns_id', '=', $post['sns_id'])
					->where('member_id', '=', $post['member_id'])
					->where('bot_id', '=', $post['bot_id'])
					->where('intent_cd', '=', $post['intent_cd'])
					->where('lang_cd', '=', $post['lang_cd'])
					->where('survey_time', '>=', $check_time)
					->find();
				if (isset($record->survey_time)) {
					// 最近24小时做过十分/不十分
					$ret = array();
					$ret['already_done'] = 1;
					$ret['survey_time'] = $record->survey_time;
					$ret['answer'] = $record->answer;
					$this->response->body($this->jresult("00", NULL, $ret));
					return;
				}
			}
			// 2023.05.27 #21060 end
			$follow = ORM::factory('botsurvey');
			$follow->survey_time = date('Y-m-d H:i:s');
			$follow->sns_type_cd = $post['sns_type_cd'];
			$follow->sns_id = $post['sns_id'];
			$follow->member_id = $post['member_id'];
			$follow->bot_id = $post['bot_id'];
			$follow->intent_cd = $post['intent_cd'];
			$follow->sub_intent_cd = $sub_intent_cd;
			$follow->area_cd = $area_cd;
			$follow->lang_cd = $post['lang_cd'];
			$follow->answer = $post['answer'];
			// 2021.08.11 t_bot_survey增加context_id字段 begin
			if ($context_id != "") {
				$follow->context_id = $context_id;
			}
			// 2021.08.11 t_bot_survey增加context_id字段 end
			// 2021.08.19 t_bot_survey增加scene_cd字段 #15371 begin
			if ($scene_cd != "") {
				$follow->scene_cd = $scene_cd;
			}
			// 2021.08.19 #15371 end
			
			$follow->save();

			$ret = array();
			$ret['survey_time'] = $follow->survey_time;
			$this->response->body($this->jresult("00", NULL, $ret));
			return;
		}
		else {
			$this->response->body($this->jresult("01"));
		}
	}

	public function action_get_survey_flag()
	{
		// survey_flag
		// 2		 : survey allow, but survey for this intent_cd is done
		$post = $this->request->post();
		if ($post){
			$bot_id = $post['bot_id'];

			$sub_intent_cd = $post['sub_intent_cd'];
			if ($sub_intent_cd === "") {
				$sub_intent_cd = NULL;
			} else if ($sub_intent_cd === "NONE_SUB_INTENT_CD") {
				$sub_intent_cd = "";
			}
			$area_cd = $post['area_cd'];
			if ($area_cd === "") {
				$area_cd = "";
			} else if ($area_cd === "NONE_AREA_CD") {
				$area_cd = "";
			}

			$ret = array();
			$ret['survey_flg'] = "0"; // 还没做过survey

			// check if the user allow survey
			if ($ret['survey_flg'] == "0") {
				$members = DB::select('survey_flg')->from('t_bot_member')
				->where('bot_id', '=', $bot_id)
				->where('member_id', '=', $post['member_id'])
				->execute()->as_array();
				if (count($members) > 0) {
					$ret['survey_flg'] = $members[0]['survey_flg'];
					if ($ret['survey_flg'] == null) {
						$ret['survey_flg'] = "0";
					}
				}
			}

			// check is user already done survey for the special inquiry
			if ($ret['survey_flg'] == "0") {
				$sql = "SELECT answer
						FROM t_bot_survey a
						WHERE
							a.bot_id = :bot_id
							AND intent_cd = :intent_cd
							AND lang_cd = :lang_cd
							AND member_id = :member_id
						";
				if ($sub_intent_cd === NULL) {
					$sql = " $sql AND sub_intent_cd is NULL ";
				} else {
					$sql = " $sql AND sub_intent_cd = :sub_intent_cd ";
				}
				$sql = " $sql AND area_cd = :area_cd ";
				$query = DB::query(Database::SELECT, $sql);
				$query->parameters(array(
						':bot_id' => $bot_id,
						':lang_cd' => $post['lang_cd'],
						':intent_cd' => $post['intent_cd'],
						':member_id' => $post['member_id'],
						':sub_intent_cd' => $sub_intent_cd,
						':area_cd' => $area_cd,
				));
		
				$survey = $query->execute()->as_array();

				if (count($survey) > 0) {
					$ret['survey_flg'] = "2";
				}
			}

			$this->response->body($this->jresult("00", NULL, $ret));
			return;
		}
		else {
			$this->response->body($this->jresult("01"));
		}
	}

	public function action_set_survey_flag()
	{
		$post = $this->request->post();
		if ($post){
			$bot_info = $this->getBotIdByFacilityCd($post['facility_cd'], "action_set_survey_flag");
            $bot_id = $bot_info['bot_id'];

			$fields = array();
			$fields['survey_flg'] = $post['survey_flg'];
			DB::update('t_bot_member')->set($fields)
			->where('bot_id', '=', $bot_id)
			->where('member_id', '=', $post['member_id'])
			->execute();

			$this->response->body($this->jresult("00", NULL, NULL));
			return;
		}
		else {
			$this->response->body($this->jresult("01"));
		}
	}

	public function action_set_survey_reason()
	{
		$post = $this->request->post();
		if ($post){
			$fields = array();
			$fields['reason'] = $post['survey_reason'];
			DB::update('t_bot_survey')->set($fields)
			->where('survey_time', '=', $post['survey_time'])
			->where('member_id', '=', $post['member_id'])
			->execute();

			$this->response->body($this->jresult("00", NULL, NULL));
			return;
		}
		else {
			$this->response->body($this->jresult("01"));
		}
	}
	// wuzhao add end #169

	// wuzhao add begin #201
	public function action_get_intent_relation()
	{
		$post = $this->request->post();
		$model = Model::factory('apimodel');
		if ($post){
			$bot_info = $this->getBotIdByFacilityCd($post['facility_cd'], "action_get_intent_relation");

			$type_cd = "";
			if (isset($post["type_cd"])) {
				$type_cd = $post["type_cd"];
			}
		
			$intents = $model->get_intent_relation($bot_info['bot_id'], $post['intent_cd'], $post['lang_cd'], $type_cd);
			$this->response->body($this->jresult("00", NULL, $intents));
		}
		else {
			$this->response->body($this->jresult("01"));
		}
		return;
	}
	// wuzhao add end #201

	// wuzhao add begin #182
	public function action_get_unknown_stop_word_list()
	{
		/*
		$ret = array();
		$stopword = DB::select('*')->from('t_dict_stopword')
		->execute()->as_array();
		$ret['stop_words'] = $stopword;

		$unknownword = DB::select('*')->from('t_dict_unknown_word')
		->execute()->as_array();
		$ret['unknown_words'] = $unknownword;

		$this->response->body($this->jresult("00", NULL, $ret));
		*/
		$ret = array();

		$sql = "SELECT bot_id, lang_cd, trim(stop_word) as stop_word
				FROM t_dict_stopword
				";
		$query = DB::query(Database::SELECT, $sql);
		$stopword = $query->execute()->as_array();
		$ret['stop_words'] = $stopword;

		$sql = "SELECT lang_cd, trim(unknown_word) as unknown_word
				FROM t_dict_unknown_word
				";
		$query = DB::query(Database::SELECT, $sql);
		$unknownword = $query->execute()->as_array();
		$ret['unknown_words'] = $unknownword;

		$this->response->body($this->jresult("00", NULL, $ret));
	}
	// wuzhao add end #182

	// wuzhao add begin #232
	public function action_get_access_method()
	{
		$post = $this->request->post();
		$model = Model::factory('apimodel');
		if ($post){
			$bot_info = $this->getBotIdByFacilityCd($post['facility_cd'], "action_get_access_method");
			$access_from = null;
			$access_to = null;
			$sightseeing_spot_access_to = null;

			if (array_key_exists("access_from", $post)) {
				$access_from = $post['access_from'];
			}
			if (array_key_exists("access_to", $post)) {
				$access_to = $post['access_to'];
			}
			if (array_key_exists("sightseeing_spot_access_to", $post)) {
				$sightseeing_spot_access_to = $post['sightseeing_spot_access_to'];
			}

			$accessmethod = $model->get_access_method($bot_info['bot_id'], $post['lang_cd'], $access_from, $access_to, $sightseeing_spot_access_to);
			$this->response->body($this->jresult("00", NULL, $accessmethod));
		}
		else {
			$this->response->body($this->jresult("01"));
		}
		return;
	}
	// wuzhao add end #232

	// wuzhao add begin for menu_group
	public function action_get_menu_group()
	{
		$post = $this->request->post();
		$model = Model::factory('apimodel');
		if ($post){
			$bot_info = $this->getBotIdByFacilityCd($post['facility_cd'], "action_get_menu_group");

			// 2021.10.18 改善 #17388 begin
			$sns_cd = "";
            if (array_key_exists('sns_cd', $post)) {
				$sns_cd = $post['sns_cd'];
            }
			// 2021.10.18 改善 #17388 end
			$menugroup = $model->get_menu_group($bot_info['bot_id'], $post['menu_group_id'], $post['lang_cd'], $sns_cd);
			$this->response->body($this->jresult("00", NULL, $menugroup));
		}
		else {
			$this->response->body($this->jresult("01"));
		}
		return;
	}
	// wuzhao add end for menu_group

	// wuzhao add begin for wbf
	public function action_get_holiday()
	{
		$post = $this->request->post();
		if ($post){
			$bot_info = $this->getBotIdByFacilityCd($post['facility_cd'], "action_get_holiday");
            $bot_id = $bot_info['bot_id'];
			$holiday = DB::select('holiday')->from('t_bot_holiday')
			->where('bot_id', '=', $bot_id)
			->where('holiday', '=', $post['date'])
			->execute()->as_array();
			if (count($holiday) > 0) {
				$ret['holiday_flg'] = "1";
			} else {
				$ret['holiday_flg'] = "0";
			}
			$this->response->body($this->jresult("00", NULL, $ret));
		}
		else {
			$this->response->body($this->jresult("01"));
		}
		return;
	}
	// wuzhao add end for wbf

	// wuzhao add begin for tl-lincoln reserve
	public function action_save_tl_reserve_data()
	{
		$post = $this->request->post();
		if ($post){
			$reservedata = ORM::factory('tlreserve');
			$reservedata->sns_type_cd = $post['sns_type_cd'];
			$reservedata->sns_id = $post['sns_id'];
			$reservedata->member_id = $post['member_id'];

			$bot_info = $this->getBotIdByFacilityCd($post['facility_cd'], "action_save_tl_reserve_data");
            $bot_id = $bot_info['bot_id'];
			$reservedata->bot_id = $bot_id;

			$reservedata->lang_cd = $post['lang_cd'];

			$reservedata->hotel_code = $post['hotel_code'];
			$reservedata->reserve_time = date('Y-m-d H:i:s');

			$reservedata->reserve_type = $post['reserve_type'];
			$reservedata->tl_agent_notification_number = $post['tl_agent_notification_number'];
			$reservedata->tl_reserve_id = $post['tl_reserve_id'];
			$reservedata->agent_reserve_id = $post['agent_reserve_id'];
			$reservedata->tl_cancel_reserve_id = $post['tl_cancel_reserve_id'];
			$reservedata->save();

			$ret = array();
			$ret['reserve_time'] = $reservedata->reserve_time;
			$this->response->body($this->jresult("00", NULL, $ret));
			return;
		}
		else {
			$this->response->body($this->jresult("01"));
		}
	}

	public function action_get_tl_reserve_data()
	{
		$post = $this->request->post();
		$model = Model::factory('apimodel');
		if ($post){
			$bot_info = $this->getBotIdByFacilityCd($post['facility_cd'], "action_get_tl_reserve_data");

			$reserve_data = $model->get_tl_reserve_data($bot_info['bot_id'], $post);
			$this->response->body($this->jresult("00", NULL, $reserve_data));
		}
		else {
			$this->response->body($this->jresult("01"));
		}
		return;
	}
	// wuzhao add end for tl-lincoln reserve

	// wuzhao add begin for wbf customized message
	public function action_get_bot_busitime_message_info()
	{
		$post = $this->request->post();
		if ($post){
			$bot_info = $this->getBotIdByFacilityCd($post['facility_cd'], "action_get_bot_busitime_message_info");
            $bot_id = $bot_info['bot_id'];
			$datetime = $post['datetime'];
			$busi_times = ORM::factory('botbusitime')
				->where('bot_id', '=', $bot_id)
				->where('use_flg', '=', 1)
				->order_by('busi_type_cd', 'DESC')
				->order_by('start_date', 'ASC')
				->order_by('start_time', 'ASC')
				->find_all();

			$week = '0'.date('w', strtotime($datetime));
			if ($week == '00') {
				$week = '07';
			};
			$date = substr($datetime, 0, 8);
			$time = substr($datetime, 8, 4);

			$ret = array();
			$ret["service_flag"] = "out";
			$ret["service_in_message"] = "";
			$ret["service_out_message_id"] = "";

			$special_date_defined = false;
			$week_defined = false;

			$count = 0;

			//祝日マスタ定義を取得
			$holiday_array = ORM::factory('classcode')
            ->where('code_div', '=', "888801")
			->where('class_cd', '=', "$date")
            ->where('lang_cd', '=', "ja")
            ->find_all();

			foreach($busi_times as $item) {
				$count = $count + 1;
				if ($special_date_defined == true) {
					// type_cd=10的记录已经全部处理完了。
					// 如果该日期已经有定义过了，就不能继续处理
					break;
				}
				// 営業日、かつ不在などの場合
				if ($item->busi_type_cd == '99') {
					// 用户要求显示当前消息
					$ret["service_in_message"] = $item->message;
				}
				//特例日
				else if ($item->busi_type_cd == '10') {
					if ($date >= $item->start_date && $date <= $item->end_date) {
						// 该日期有定义
						$special_date_defined = true;
						$ret["service_out_message_id"] = $item->message;
						if ( $item->start_time == '0000' && $item->end_time == '0000') {
							// 该日期不营业
							$ret["service_flag"] = "out";
							$ret["service_out_message_id"] = $item->message;
							break;
						} else if ($time >= $item->start_time && $time <= $item->end_time) {
							// 该日期的营业时间内
							$ret["service_flag"] = "in";
							break;
						}
					}
				}
				//祝日
				else if ($item->busi_type_cd == '08') {
					foreach($holiday_array as $h) {
						if ($h->class_cd == $date) { 
							// 该日期有定义
							$special_date_defined = true;
							$ret["service_out_message_id"] = $item->message;
							if ( $item->start_time == '0000' && $item->end_time == '0000') {
								// 该日期不营业
								$ret["service_flag"] = "out";
							} else if ($time >= $item->start_time && $time <= $item->end_time) {
								// 该日期的营业时间内
								$ret["service_flag"] = "in";
							}
							break;
						}
					}
				}
				else if ($item->busi_type_cd >='01' && $item->busi_type_cd <='07') {
					if ($item->busi_type_cd == $week) {
						// 该星期几有定义
						$week_defined = true;
						$ret["service_out_message_id"] = $item->message;
						if ( $item->start_time == '0000' && $item->end_time == '0000') {
							// 不营业
							$ret["service_flag"] = "out";
							$ret["service_out_message_id"] = $item->message;
							break;
						} else if ($time >= $item->start_time && $time <= $item->end_time) {
							// 营业时间内
							$ret["service_flag"] = "in";
							break;
						}
					}
				}
				else if ($week_defined == true) {
					// type_cd=01-07的记录已经全部处理完了。
					// 如果星期几已经有定义过了，就不能继续处理
					break;
				}
				else if ($item->busi_type_cd =='00') {
					// 该日期没有定义过，改星期几也没有定义过的情况下，才能走入这个分支
					$ret["service_out_message_id"] = $item->message;
					if ($time >= $item->start_time && $time <= $item->end_time) {
						// 营业时间内
						$ret["service_flag"] = "in";
						break;
					}
				}
			}

			// 2020.07.25 改为没有记录就表示in
			if ($count == 0) {
				$ret["service_flag"] = "in";
			}

			$this->response->body($this->jresult("00", NULL, $ret));
		}
		else {
			$this->response->body($this->jresult("01"));
		}
		return;
	}
	// wuzhao add end for wbf customized message

	public function action_save_item_history()
	{
		$post = $this->request->post();
		if ($post){
			$historydata = ORM::factory('botuseritemhistory');
			$historydata->member_id = $post['member_id'];
			$historydata->id = $post['id'];
			$historydata->item_id_history = $post['item_id_history'];

			$historydata->save();

			$this->response->body($this->jresult("00"));
			return;
		}
		else {
			$this->response->body($this->jresult("01"));
		}
	}

	public function action_get_item_history()
	{
		$post = $this->request->post();
		if ($post){
			$user_item_id_history = DB::select('member_id','item_id_history')->from('t_bot_user_item_history')
			->where('id', '=', $post['item_id_history'])
			->execute()->as_array();

			$this->response->body($this->jresult("00", NULL, $user_item_id_history));
			return;
		}
		else {
			$this->response->body($this->jresult("01"));
		}
	}

	public function action_save_wechat_msgmenu()
	{
		$post = $this->request->post();
		if ($post){
			$historydata = ORM::factory('botuserwechatmsgmenu');
			$historydata->log_time = date('Y-m-d H:i:s');
			$historydata->member_id = $post['member_id'];
			$historydata->id = $post['id'];
			$historydata->cmd = $post['cmd'];
			$historydata->module_type = $post['module_type'];

			$historydata->save();

			$this->response->body($this->jresult("00"));
			return;
		}
		else {
			$this->response->body($this->jresult("01"));
		}
	}

	public function action_get_wechat_msgmenu()
	{
		$post = $this->request->post();
		if ($post){
			$user_item_id_history = DB::select('member_id','cmd')->from('t_bot_wechat_msgmenu')
			->where('id', '=', $post['id'])
			->where('module_type', '=', $post['module_type'])
			->execute()->as_array();

			$this->response->body($this->jresult("00", NULL, $user_item_id_history));
			return;
		}
		else {
			$this->response->body($this->jresult("01"));
		}
	}

	public function action_get_wechat_msgmenu_maxid()
	{
		// 2020/11/15 分module_type
		$post = $this->request->post();
		$ret = array();

		$sql = "SELECT MAX(id) as max_id
				FROM t_bot_wechat_msgmenu
				WHERE module_type = :module_type
				";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':module_type' => $post['module_type']
		));
		$ids = $query->execute()->as_array();
		$this->response->body($this->jresult("00", NULL, $ids));
		return;
	}

	public function action_get_autoanswer_flow()
	{
		$post = $this->request->post();
		$model = Model::factory('apimodel');
		if ($post){
			$bot_info = $this->getBotIdByFacilityCd($post['facility_cd'], "action_get_autoanswer_flow");

			$flow_data = $model->get_autoanswer_flow($bot_info['bot_id'], $post);
			$this->response->body($this->jresult("00", NULL, $flow_data));
		}
		else {
			$this->response->body($this->jresult("01"));
		}
		return;
	}

	public function action_get_keyvalue_map()
	{
		$post = $this->request->post();
		$bot_info = $this->getBotIdByFacilityCd($post['facility_cd'], "action_get_keyvalue_map");
		$model = Model::factory('apimodel');
		if ($post){
			$data = $model->get_keyvalue_map($bot_info['bot_id'], $post);
			$this->response->body($this->jresult("00", NULL, $data));
		}
		else {
			$this->response->body($this->jresult("01"));
		}
		return;
	}

	public function action_tl_reserve_saiban() {
		$post = $this->request->post();
		$date = date('Ymd');
		$saiban = ORM::factory('saiban')
		->where('type', '=', $post['type'])
		->where('date', '=', $date)
		->find();

		$ret = array();
		$ret["date"] = $date;
		$ret["type"] = $post['type'];
		if (!isset($saiban->no)) {
			// 登录新记录
			$ret["no"] = 1;
			$saiban = ORM::factory('saiban');
			$saiban->type = $post['type'];
			$saiban->date = $date;
			$saiban->no = $ret["no"];
			$saiban->save();
		} else {
			// 更新记录
			$ret["no"] = 1 + $saiban->no;
			$fields = array();
			$fields['no'] = $ret["no"];
			DB::update('t_saiban')->set($fields)
			->where('type', '=', $post['type'])
			->where('date', '=', $date)
			->execute();
		}
		$this->response->body($this->jresult("00", NULL, $ret));
	}

	public function action_search_inquiry_by_type()
	{
		$post = $this->request->post();
		$model = Model::factory('apimodel');
		if ($post){
			$bot_info = $this->getBotIdByFacilityCd($post['facility_cd'], "action_search_inquiry_by_type");
			
			$intents = $model->search_inquiry_by_type($post['lang'], $bot_info['bot_id'], $post['type']);
			$this->response->body($this->jresult("00", NULL, $intents));
		}
		else {
			$this->response->body($this->jresult("01"));
		}
		return;
	}

	public function get_link_id($condition)
	{
			$link = ORM::factory('link');
			$link->link_id = $this->create_unique_toke(32);
			$link->link_type_cd = "02";
			$link->param1 = $condition["param1"];
			$link->valid_date_time = date('Y-m-d H:i:s', strtotime('+' . Kohana::$config->load('settings.link_valid_' . "02")));
			$link->valid_flg = 0;
			$link->save();
			return $link->link_id;
	}

	// 2021.07.28 #14773 valid_time初始值设置为create_time+30分钟
	public function get_token_log_token($condition)
	{
		$token = $this->create_unique_toke(32);
		$data = $condition["data"];
		$member_id = $condition["member_id"];

		$sql = "INSERT INTO x_token_log
				(token, data, member_id, create_time, valid_time)
				VALUES
				(:token, :data, :member_id, :create_time, :valid_time) 
				";
		$query = DB::query(Database::UPDATE, $sql);
		$query->parameters(array(
			':token' => $token,
			':data' => $data,
			':member_id' => $member_id,
			':create_time' => date('Y-m-d H:i:s'),
			':valid_time' => date("Y-m-d H:i:s",strtotime(date("Y-m-d H:i:s")." +30 days")),
		));

		$query->execute();
		return $token;
	}

	// 2021.09.29 #15893 改成token版 begin
	public function read_log_token_data($condition)
	{
		$token = $condition["token"];

		$sql = "SELECT * FROM x_token_log
				WHERE token = :token
				";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':token' => $token,
		));

		$results = $query->execute();
		return $results->as_array();
	}

	public function write_log_token_data($condition)
	{
		$token = $condition["token"];
		$data = $condition["data"];

		$sql = "UPDATE x_token_log
				SET
					data = :data
				WHERE token = :token
				";
		$query = DB::query(Database::UPDATE, $sql);
		$query->parameters(array(
			':token' => $token,
			':data' => $data,
		));

		$query->execute();
		return "1";
	}
	// 2021.09.29 #15893 改成token版 end
	
	public function action_api_get()
	{
		$post = $this->request->post();
		$model = Model::factory('apimodel');
		if ($post){
			$api_get_source = $post['api_get_source'];
			$data = array();
			if ($api_get_source == 't_bot_intent_hit_keywords_search') {
				$bot_info = $this->getBotIdByFacilityCd($post['facility_cd'], "t_bot_intent_hit_keywords_search");
				$type_cd = "";
				if (isset($post["type_cd"])) {
					$type_cd = $post["type_cd"];
				}
				$data = $model->get_intents_keyword($bot_info['bot_id'], $post['lang_cd'], $post['sentence'], $type_cd);
			} else if ($api_get_source == 'is_exist_faq_definition') {
				$data = $model->is_exist_faq_definition($post);


			} else if ($api_get_source == 't_bot_car_search') {
				$data = $model->get_car_data($post);
			} else if ($api_get_source == 'search_sub_intent') {
				$bot_info = $this->getBotIdByFacilityCd($post['facility_cd'], "search_sub_intent");
				$data = $model->get_intents_by_intent_cd($bot_info, $post);
			} else if ($api_get_source == 'search_nearby_hotel') {
				$data = $model->get_nearby_hotel($post);
			} else if ($api_get_source == 'search_ots_hotel') {
				$data = $model->get_ots_hotel($post);
			} else if ($api_get_source == 'search_nearby_business_office') {
				$data = $model->get_nearby_business_office($post);
			} else if ($api_get_source == 'search_all_business_office') {
				$data = $model->get_all_business_office($post);
			} else if ($api_get_source == 't_bot_ots_ownedmedia_search') {
				$bot_info = $this->getBotIdByFacilityCd($post['facility_cd'], "t_bot_ots_ownedmedia_search");
				$data = $model->get_ownedmedia_data($post, $bot_info);
			} else if ($api_get_source == 'bot_stock_day_search') {
				$data = $model->search_bot_stock_day($post);
			} else if ($api_get_source == 'bot_stock_time_search') {
				$data = $model->search_bot_stock_time($post);
			} else if ($api_get_source == 'search_items_by_location_areacd') {
				$bot_info = $this->getBotInfo($post['facility_cd']);
				$item_id_history = $post['item_id_history'];
				if (substr($post['item_id_history'], 0, 2) == "ID") {
					$user_item_id_history = DB::select('item_id_history')->from('t_bot_user_item_history')
						->where('id', '=', $post['item_id_history'])
						->execute()->as_array();
					$item_id_history = $user_item_id_history[0]['item_id_history'];
				};
				$data = $model->search_items_by_location_areacd($bot_info, $item_id_history, $post);
			} else if ($api_get_source == 'search_recommend_items_by_item_id') {
				$data = $model->search_recommend_items_by_item_id($post);
			} else if ($api_get_source == 'facebook_feed') {
				$data = $model->facebook_feed($post);
			} else if ($api_get_source == 'set_member_last_access_time') {
				$bot_info = $this->getBotIdByFacilityCd($post['facility_cd'], "set_member_last_access_time");
				$bot_id = $bot_info['bot_id'];
					
				$data = $model->set_member_last_access_time($bot_id, $post);
			} else if ($api_get_source == 'set_member_lang_cd') {
				// 2022.08.11 begin
				$data = $model->set_member_lang_cd($post);
				// 2022.08.11 end
			} else if ($api_get_source == 'set_member_line_induce_flg') {
				$data = $model->set_member_line_induce_flg($post);
			} else if ($api_get_source == 'set_member_follow') {
				$data = $model->set_member_follow($post);

			} else if ($api_get_source == 'set_member_email') {
				$bot_info = $this->getBotIdByFacilityCd($post['facility_cd'], "set_member_email");
				$bot_id = $bot_info['bot_id'];
				$data = $model->set_member_email($bot_id, $post);
			} else if ($api_get_source == 'set_member_tags') {
				$bot_info = $this->getBotIdByFacilityCd($post['facility_cd'], "set_member_tags");
				$bot_id = $bot_info['bot_id'];
				$data = $model->set_member_tags($bot_id, $post);
			} else if ($api_get_source == 'set_member_is_tester') {
				$data = $model->set_member_is_tester($post);

			} else if ($api_get_source == 'set_member_name_by_t_user') {
				$data = $model->set_member_name_by_t_user($post);
				
			} else if ($api_get_source == 'get_member_notify_msg') {
				$data = $model->get_member_notify_msg($post);
			} else if ($api_get_source == 'get_users_by_condition') {
				$data = $model->get_users_by_condition($post);

			} else if ($api_get_source == 'update_log') {
				$data = $model->update_log($post);
			} else if ($api_get_source == 'update_log_intent_cd') {
				$data = $model->update_log_intent_cd($post);
			} else if ($api_get_source == 'insert_t_member_mail_certify') {
				$data = $model->insert_t_member_mail_certify($post);

			} else if ($api_get_source == 't_bot_service_get_mail_send_list') {
				$data = $model->t_bot_service_get_mail_send_list($post);
			} else if ($api_get_source == 't_bot_service_update_mail_sent') {
				$data = $model->t_bot_service_update_mail_sent($post);
			} else if ($api_get_source == 't_bot_service_get_list') {
				$data = $model->t_bot_service_get_list($post);


			} else if ($api_get_source == "get_item_info_by_keyword") {
                // wuzhao add for #1561
				$bot_info = $this->getBotInfo($post['facility_cd']);
				$data = $model->get_item_info_by_keyword($bot_info, $post['facility_cd'], $post['lang_cd']);
			} else if ($api_get_source == "get_item_info_by_keyword_by_class_cd") {
                // wuzhao add for #1561
				$bot_info = $this->getBotInfo($post['facility_cd']);
				$data = $model->get_item_info_by_keyword_by_class_cd($bot_info, $post['facility_cd'], $post['code_div'], $post['lang_cd']);
			} else if ($api_get_source == "get_item_info_by_keyword_for_access") {
                // wuzhao add for #1561
				$bot_info = $this->getBotInfo($post['facility_cd']);
				$data = $model->get_item_info_by_keyword_for_access($bot_info, $post['facility_cd'], $post['lang_cd']);

			} else if ($api_get_source == "get_t_keyword_with_bot_info") {
				$data = $model->get_t_keyword_with_bot_info($post);
			} else if ($api_get_source == "get_items_keyword_aimai") {
				$data = $model->get_items_keyword_aimai($post);

			// 2020.12.27 #7473
			} else if ($api_get_source == "get_all_child_bot_from_t_item") {
				$data = $model->get_all_child_bot_from_t_item($post);

			} else if ($api_get_source == "get_t_faq_replace") {
				$bot_info = $this->getBotIdByFacilityCd($post['facility_cd'], "get_t_faq_replace");
				$bot_id = $bot_info['bot_id'];
				$data = $model->get_t_faq_replace($bot_id, $post['facility_cd'], $post['lang_cd']);
			} else if ($api_get_source == "get_m_bot_engine_redirect") {
				$data = $model->get_m_bot_engine_redirect($post['lang_cd']);
			} else if ($api_get_source == "get_m_engine_action_mapping") {
				$bot_info = $this->getBotIdByFacilityCd($post['facility_cd'], "get_m_engine_action_mapping");
				$bot_id = $bot_info['bot_id'];
				$data = $model->get_m_engine_action_mapping($bot_id, $post['facility_cd'], $post['lang_cd'], $post['intent']);
			} else if ($api_get_source == "get_m_bot_skill_mapping") {
                // wuzhao add for #1562
				$data = $model->get_m_bot_skill_mapping($post['lang_cd']);
			} else if ($api_get_source == "get_default_action_skill") {
                // wuzhao add for #1560
				$bot_info = $this->getBotIdByFacilityCd($post['facility_cd'], "get_default_action_skill");
				$bot_id = $bot_info['bot_id'];
				$data = $model->get_default_action_skill($bot_id,$post);
			} else if ($api_get_source == "get_m_bot_area") {
                // wuzhao add for #1560
				$bot_info = $this->getBotIdByFacilityCd($post['facility_cd'], "get_m_bot_area");
				$bot_id = $bot_info['bot_id'];
				$data = $model->get_m_bot_area($bot_id,$post);
			} else if ($api_get_source == "get_parent_area_name") {
                // wuzhao add for #1560
				$bot_info = $this->getBotIdByFacilityCd($post['facility_cd'], "get_parent_area_name");
				$bot_id = $bot_info['bot_id'];
				$data = $model->get_parent_area_name($bot_id,$post);
			} else if ($api_get_source == "get_child_area_name") {
                // wuzhao add for #1560
				$bot_info = $this->getBotIdByFacilityCd($post['facility_cd'], "get_child_area_name");
				$bot_id = $bot_info['bot_id'];
				$data = $model->get_child_area_name($bot_id,$post);
			} else if ($api_get_source == "get_t_intent_skill") {
				$bot_info = $this->getBotIdByFacilityCd($post['facility_cd'], "get_t_intent_skill");
				$bot_id = $bot_info['bot_id'];
				$data = $model->get_t_intent_skill($bot_id,$post);
			} else if ($api_get_source == "get_all_t_intent_skill") {
				$data = $model->get_all_t_intent_skill($post);
			} else if ($api_get_source == "get_def_intent_skill") {
				$data = $model->get_def_intent($post['bot_id'], $post['lang_cd'], $post['check_def_intent'], array(array("skill" => $post['skill'])));
			} else if ($api_get_source == "regist_ref_info") {
				$data = $model->regist_ref_info($post);
			} else if ($api_get_source == "regist_ref_info_bulk") {
				$data = $model->regist_ref_info_bulk($post);
			} else if ($api_get_source == "get_m_class_code_child") {
				// TODO 等t_item_relation表修改方针确定
				$bot_info = $this->getBotInfo($post['facility_cd']);
				$data = $model->get_m_class_code_child($bot_info, $post);

			} else if ($api_get_source == "get_m_class_code") {
				// 2021.08.05 add
				$data = $model->get_m_class_code($post);

			} else if ($api_get_source == "get_m_class_code_child_product") {
				$data = $model->get_m_class_code_child_product($post);


			} else if ($api_get_source == "get_items_by_class_cd") {
				$bot_info = $this->getBotInfo($post['facility_cd']);
				$item_id_history = $post['item_id_history'];
				if (substr($post['item_id_history'], 0, 2) == "ID") {
					$user_item_id_history = DB::select('item_id_history')->from('t_bot_user_item_history')
						->where('id', '=', $post['item_id_history'])
						->execute()->as_array();
					$item_id_history = $user_item_id_history[0]['item_id_history'];
				};
					
				$data = $model->get_items_by_class_cd($bot_info, $post, $item_id_history);

			// 2021.11.04 多个class_cd时改为暧昧 begin
			} else if ($api_get_source == "get_items_by_class_cd_aimai") {
				$bot_info = $this->getBotInfo($post['facility_cd']);
				$item_id_history = $post['item_id_history'];
				if (substr($post['item_id_history'], 0, 2) == "ID") {
					$user_item_id_history = DB::select('item_id_history')->from('t_bot_user_item_history')
						->where('id', '=', $post['item_id_history'])
						->execute()->as_array();
					$item_id_history = $user_item_id_history[0]['item_id_history'];
				};
					
				$data = $model->get_items_by_class_cd_aimai($bot_info, $post, $item_id_history);
				// 2021.11.04 多个class_cd时改为暧昧 end
			} else if ($api_get_source == "get_child_area_by_class_code") {
				$bot_info = $this->getBotInfo($post['facility_cd']);
				$data = $model->get_child_area_by_class_code($bot_info, $post);

			} else if ($api_get_source == "get_bot_division_data") {
				$data = $model->get_bot_division_data($post);

			} else if ($api_get_source == "get_task") {
				$data = $model->get_task($post);

			} else if ($api_get_source == "get_follow_times") {
				// 取得venusfort qrcode follow次数
				$data = $model->get_follow_times($post);
			} else if ($api_get_source == "get_floor_facility_menu") {
				// 取得venusfort qrcode follow次数
				$data = $model->get_floor_facility_menu($post);

			} else if ($api_get_source == "get_child_faq_by_class_code") {
				// 取得FAQ分类子菜单
				$data = $model->get_child_faq_by_class_code($post);
			} else if ($api_get_source == "get_class_cd_name") {
				// 取得class_cd名
				$data = $model->get_class_cd_name($post);
			} else if ($api_get_source == "get_faq_selection_extend") {
				// 2023.01.11 #36344
				$data = $model->get_faq_selection_extend($post);

			// master
			} else if ($api_get_source == 'read_t_bot') {
				$data = $model->read_t_bot();
			} else if ($api_get_source == 'read_t_bot_line_richmenu_id') {
				$data = $model->read_t_bot_line_richmenu_id();
			} else if ($api_get_source == 'read_t_bot_scene') {
				$data = $model->read_t_bot_scene();
			} else if ($api_get_source == 'read_bot_setting_multilang') {
				$data = $model->read_bot_setting_multilang();
			} else if ($api_get_source == 'read_bot_setting_multilang_by_bot_id') {
				// 2021.01.03 #3541
				$data = $model->read_bot_setting_multilang_by_bot_id($post);
			} else if ($api_get_source == 'read_bot_line_richmenu_id') {
				$data = $model->read_bot_line_richmenu_id();
			} else if ($api_get_source == 'read_multi_lang') {
				$data = $model->read_multi_lang();

			// t_item
			} else if ($api_get_source == 'get_items_by_keyword') {
				$data = $model->get_items_by_keyword($post);
			} else if ($api_get_source == 'get_items_from_grp_child_bot_by_keyword') {
				$data = $model->get_items_from_grp_child_bot_by_keyword($post);
			} else if ($api_get_source == 'get_items_keyword_aimai_for_grp_child_bot') {
				$data = $model->get_items_keyword_aimai_for_grp_child_bot($post);
			} else if ($api_get_source == 'get_item_description_aimai_for_grp_child_bot') {
				$data = $model->get_item_description_aimai_for_grp_child_bot($post);
			} else if ($api_get_source == 'get_class_cd_mapping') {
				// 2021.07.01 #13950 根据t_class_mapping调整class_cd参数
				$data = $model->get_class_cd_mapping($post);

			// t_survey // 2020.10.13 #6006 アンケート機能の新規実装
			} else if ($api_get_source == 'read_t_survey') {
				$data = $model->read_t_survey($post);

			// t_bot_service
			} else if ($api_get_source == 'update_t_bot_service_service_data') {
				$data = $model->update_t_bot_service_service_data($post);
			} else if ($api_get_source == 'delete_t_bot_service_data') {
				$data = $model->delete_t_bot_service_data($post);

			// autoanswer
			} else if ($api_get_source == 'get_autoanswer_text') {
				$data = $model->get_autoanswer_text($post);

			// tl-lincoln相关
			} else if ($api_get_source == 'read_t_product') {
				$data = $model->read_t_product($post);
			} else if ($api_get_source == 'read_t_product_only') {
				$data = $model->read_t_product_only($post);
			} else if ($api_get_source == 'read_t_bot_order_latest') {
				$data = $model->read_t_bot_order_latest($post);
			} else if ($api_get_source == 'save_tl_pre_reserve_data') {
				$data = $model->save_tl_pre_reserve_data($post);
			} else if ($api_get_source == 'save_reserve_data_card_info') {
				// 2021.08.21 #12594 begin
				$data = $model->save_reserve_data_card_info($post);
				// 2021.08.21 #12594 end
			} else if ($api_get_source == 'save_tl_reserve_confirm_data') {
				$data = $model->save_tl_reserve_confirm_data($post);
			} else if ($api_get_source == 'save_tl_reserve_cancel_data') {
				$data = $model->save_tl_reserve_cancel_data($post);
			} else if ($api_get_source == 'save_tl_reserve_change_data') {
				$data = $model->save_tl_reserve_change_data($post);
			} else if ($api_get_source == 'update_t_bot_order_mail_sent') {
				// 2021.07.23 #14647
				$data = $model->update_t_bot_order_mail_sent($post);
			} else if ($api_get_source == 't_bot_order_reserve_get_remind_list') {
				// 2021.07.24 #13647
				$data = $model->t_bot_order_reserve_get_remind_list($post);
			} else if ($api_get_source == 'update_t_bot_order_remind') {
				// 2021.07.24 #13647
				$data = $model->update_t_bot_order_remind($post);
			} else if ($api_get_source == 'get_group_child_bots_have_t_bot_order') {
				$data = $model->get_group_child_bots_have_t_bot_order($post);
			} else if ($api_get_source == 'get_link_id') {
				$data = $this->get_link_id($post);
			} else if ($api_get_source == 'get_token_log_token') {
				$data = $this->get_token_log_token($post);
			// 2021.09.29 #15893 改成token版 begin
			} else if ($api_get_source == 'read_log_token_data') {
				$data = $this->read_log_token_data($post);
			} else if ($api_get_source == 'write_log_token_data') {
				$data = $this->write_log_token_data($post);
			// 2021.09.29 #15893 改成token版 end
			} else if ($api_get_source == 'save_t_bot_action_status') {
				$data = $model->save_t_bot_action_status($post);
			} else if ($api_get_source == 't_bot_retargeting_get_list') {
				$data = $model->t_bot_retargeting_get_list($post);
			} else if ($api_get_source == 't_bot_action_status_update') {
				$data = $model->t_bot_action_status_update($post);

			// 安比巴士预约
			} else if ($api_get_source == 'product_bus_search') {
				$data = $model->product_bus_search($post);

			// task
			} else if ($api_get_source == 'read_t_task') {
				$data = $model->read_t_task($post);
			} else if ($api_get_source == 'update_t_task_process_time') {
				$data = $model->update_t_task_process_time($post);
			} else if ($api_get_source == 'update_t_task_task_status_cd') {
				$data = $model->update_t_task_task_status_cd($post);
			} else if ($api_get_source == 'save_t_task_log') {
				$data = $model->save_t_task_log($post);
			} else if ($api_get_source == 'save_t_task_log_batch') {
				// 2022.06.08 begin
				//$data = $model->save_t_task_log($post);
				$allcondition = $post["allcondition"];
				$allcondition_obj = json_decode($allcondition, true);
				$data = [];
		
				$count = count($allcondition_obj);
				// error_log("Comming save_t_task_log_batch, count=$count");
				for ($i=0; $i<$count; $i++) {
					$cur_condition = $allcondition_obj[$i];
					$cur_ret = $model->save_t_task_log($cur_condition);
					$data[] = $cur_ret;
				}
				// error_log("end save_t_task_log_batch, count=$count");
				// 2022.06.08 end
			} else if ($api_get_source == 'save_t_bot_log_batch') {
				// 2022.06.08 begin
				$allcondition = $post["allcondition"];
				$allcondition_obj = json_decode($allcondition);
				$data = [];
		
				$count = count($allcondition_obj);
				// error_log("Comming save_t_bot_log_batch, count=$count");
				for ($i=0; $i<$count; $i++) {
					$cur_condition = $allcondition_obj[$i];
					$cur_ret = $this->log_sub($cur_condition);
					$data[] = $cur_ret;
				}
				// error_log("end save_t_bot_log_batch, count=$count");
				// 2022.06.08 end
			} else if ($api_get_source == 'save_t_task_push_result') {
				// 2020.12.21增加
				$data = $model->save_t_task_push_result($post);
			} else if ($api_get_source == 'save_t_task_push_result_batch') {
				// 2022.06.08 begin
				//$data = $model->save_t_task_push_result($post);
				$allcondition = $post["allcondition"];
				$allcondition_obj = json_decode($allcondition, true);
				$data = [];
		
				$count = count($allcondition_obj);
				// error_log("Comming save_t_task_push_result_batch, count=$count");
				for ($i=0; $i<$count; $i++) {
					$cur_condition = $allcondition_obj[$i];
					$cur_ret = $model->save_t_task_push_result($cur_condition);
					$data[] = $cur_ret;
				}
				// error_log("end save_t_task_push_result_batch, count=$count");
				// 2022.06.08 end
			} else if ($api_get_source == 'get_t_task_push_result') {
				// 2020.12.21增加
				$data = $model->get_t_task_push_result($post);
			} else if ($api_get_source == 'get_member_list_for_task_by_member_ids') {
				$data = $model->get_member_list_for_task_by_member_ids($post);
			} else if ($api_get_source == 'get_member_list_for_task') {
				$data = $model->get_member_list_for_task($post);

			// 2021.01.22 #7482
			} else if ($api_get_source == 'save_t_bot_memeber_room') {
				$data = $model->save_t_bot_memeber_room($post);
			} else if ($api_get_source == 'read_t_bot_memeber_room') {
				$data = $model->read_t_bot_memeber_room($post);
			// 2021.01.22 #end

			// 2021.01.22 #7876 begin
			} else if ($api_get_source == 'read_coupon_info') {
				$data = $model->read_coupon_info($post);
			} else if ($api_get_source == 'create_user_coupon') {
				$data = $model->create_user_coupon($post);
			} else if ($api_get_source == 'check_coupon_and_lock') {
				$data = $model->check_coupon_and_lock($post);
			} else if ($api_get_source == 'coupon_unlock') {
				$data = $model->coupon_unlock($post);
			// 2021.01.22 #7876 end

			// 2021.05.07 #9478 begin 增加province
			} else if ($api_get_source == 'get_province_by_city') {
				$data = $model->get_province_by_city($post);
			// 2021.05.07 #9478 begin 增加province

			// 2021.09.16 #16233 ユーザー誘導仕組み構築 begin
			} else if ($api_get_source == 'get_bot_induce') {
				$data = $model->get_bot_induce($post);
			} else if ($api_get_source == 'get_t_bot_induce_result') {
				$data = $model->get_t_bot_induce_result($post);
			} else if ($api_get_source == 'save_t_bot_induce_result') {
				$data = $model->save_t_bot_induce_result($post);
			// 2021.09.16 #16233 ユーザー誘導仕組み構築 end

			// 2021.11.01 #17801 法人预约 begin
			} else if ($api_get_source == 'get_corp_info') {
				$data = $model->get_corp_info($post);
			// 2021.11.01 #17801 法人预约 end

			// 2021.12.25 jtb timeout begin
			} else if ($api_get_source == 'get_t_bot_order_jtb_timeout') {
				$data = $model->get_t_bot_order_jtb_timeout($post);
			// 2021.12.25 jtb timeout end

			// 2022.03.22 #22751 begin
			} else if ($api_get_source == 'search_item_crowd_status') {
				$data = $model->search_item_crowd_status($post);
			// 2022.03.22 #22751 end
			// 2023.07.02 #49950 begin
			} else if ($api_get_source == 'search_item_schedule_description') {
				$data = $model->search_item_schedule_description($post);
			// 2022.07.02 #49950 end

			// 2022.05.04 line的rich menu变化后，取出需要修改menu的用户 begin
			} else if ($api_get_source == 'search_bot_active_user_of_line') {
				$data = $model->search_bot_active_user_of_line($post);
			} else if ($api_get_source == 'search_bot_user_of_line') {
				$data = $model->search_bot_user_of_line($post);
			// 2022.05.04 end

			// 2022.09.20 #23807 begin
			} else if ($api_get_source == 'create_task') {
				$data = $model->create_task($post);
			// 2022.09.20 #23807 end

			// 2022.12.06 #32792 begin
			} else if ($api_get_source == 'add_x_mail_log') {
				$data = $model->add_x_mail_log($post);
			// 2022.12.06 #32792 end

			// 2022.12.10 begin flow标记
			} else if ($api_get_source == 'save_flow_data') {
				$data = $model->save_flow_data($post);
			} else if ($api_get_source == 'read_flow_data') {
				$data = $model->read_flow_data($post);
			// 2022.12.10 end flow标记
			// 2023.01.26 #36870 begin
			} else if ($api_get_source == 'update_log_table_for_flow') {
				$data = $model->update_log_table_for_flow($post);
			// 2023.01.26 #36870 end
		
			// 2023.04.23 #43257 begin
			} else if ($api_get_source == 'search_t_pay_client_info') {
				$data = $model->search_t_pay_client_info($post);
			} else if ($api_get_source == 'create_t_pay_transaction') {
				$data = $model->create_t_pay_transaction($post);
			} else if ($api_get_source == 'search_t_pay_transaction') {
				$data = $model->search_t_pay_transaction($post);
			// 2023.04.23 #43257 end

			// 2023.04.26 #43410 begin
			} else if ($api_get_source == 'add_x_engineapi_log') {
				$data = $model->add_x_engineapi_log($post);
			// 2023.04.26 #43410 end

			// 2023.05.10 begin
			} else if ($api_get_source == 'add_amazon_ses_event') {
				$data = $model->add_amazon_ses_event($post);
			// 2023.05.10 end

			// 2023.05.14 #43185 begin
			} else if ($api_get_source == 'search_user_device_token') {
				$data = $model->search_user_device_token($post);
			// 2023.05.14 end

			// 2023.05.20 #38860 chatgpt begin
			} else if ($api_get_source == 'search_user_latest_log') {
				$data = $model->search_user_latest_log($post);
			} else if ($api_get_source == 'change_ai') {
				$data = $model->change_ai($post);
			} else if ($api_get_source == 'add_x_openai_cost') {
				$data = $model->add_x_openai_cost($post);
			} else if ($api_get_source == 'get_x_openai_cost') {
				$data = $model->get_x_openai_cost($post);
			// 2023.05.20 #38860 chatgpt end

			// 2023.06.10 #48647 begin
			} else if ($api_get_source == 'search_puzzle') {
				$data = $model->search_puzzle($post);
			} else if ($api_get_source == 'insert_update_puzzle_answer_result') {
				$data = $model->insert_update_puzzle_answer_result($post);
			} else if ($api_get_source == 'get_member_puzzle_status') {
				$data = $model->get_member_puzzle_status($post);
			// 2023.06.10 #48647 end

			// 2023.06.28 #47533 begin
			} else if ($api_get_source == 'get_rc_area') {
				$data = $model->get_rc_area($post);
			} else if ($api_get_source == 'get_rc_municipality') {
				$data = $model->get_rc_municipality($post);
			} else if ($api_get_source == 'update_t_member_rc') {
				$data = $model->update_t_member_rc($post);
			// 2023.06.28 #47533 end

			// 共通
			} else if ($api_get_source == 'read_table') {
				$data = $model->read_table($post);
			} else if ($api_get_source == 'update_table') {
				$model->update_table($post);
			} else {
				error_log("api.php miss api_get_source..." . $api_get_source);
			}
			$this->response->body($this->jresult("00", NULL, $data));
		}
		else {
			$this->response->body($this->jresult("01"));
		}
		return;
	}

	// 2022.04.01 begin
	public function create_unique_toke($length) {
		return substr(md5(uniqid("api" . rand(), true)), 0, $length);
	}
	// 2022.04.01 end

	public function action_merge_product_image()
	{
		$post = $this->request->post();
		if ($post){
			$aws = Model::factory('aws');
			$bot_id = $post['bot_id'];
			$item_div = $post['item_div'];
			$item_id = $post['item_id'];
			$lang_cd = $post['lang_cd'];
			$item_data = ['price'=>$post['price']];
			$image_url = $post['image_url'];   // ''的场合，会根据item_id和语言去取图片url，多花一点性能
			
			// 判断既存价格的图片是否存在，存在直接利用
			if ($image_url != '') {
				$pos = strrpos($image_url, '.');
				$url = substr($image_url, 0, $pos) . '_' . $post['price'] . substr($image_url, $pos);
				$url = $aws->get_file_url($url);
				if ($url != '') {
					$this->response->body($this->jresult("00", NULL, $url));
					return;
				}
			}
			
			$url = $aws->item_banner($bot_id, $item_div, $item_id, $lang_cd, $item_data, $image_url, false, true);
			$this->response->body($this->jresult("00", NULL, $url));
		}
		else {
			$this->response->body($this->jresult("01"));
		}
	}
}