<?php defined('SYSPATH') or die('No direct script access.');
class Controller_Ajax extends Controller_Template_Normalbase{
	public $_lang_cd = 'ja';
	public $_bot_id;
	public $_bot;
	public $_item_id;
	public $_item_cd;
	public $_user_id;
	public $_user;
	public $_codes;
	public $_model;
	public $_very_model;
	public $_member_model;
	public $_aws_model;
	public function _set_session($key, $value)
	{
		Session::instance()->set($key, $value);
	}
	public function _get_session($key, $value)
	{
		$ret = Session::instance()->get($key, NULL);
		if ($ret == NULL) {
			return $value;
		}
		else {
			return $ret;
		}
	}
	public function _del_session($key)
	{
		Session::instance()->delete($key);
	}
	
	protected function _redirect($path = '/admin/top') {
		parent::_redirect($path);
	}

	public function __construct(Request $request, Response $response)
	{
		parent::__construct($request, $response);
		//if(!$this->request->is_ajax()) throw new HTTP_Exception_403;
		/*
		if (!$this->request->is_ajax()){
			$this->_redirect();
		}
		*/
		$this->_user_id = Session::instance()->get('user_id', NULL);
		if ($this->_user_id == NULL) {
			$this->_redirect();
		}
		$this->_user = Session::instance()->get('user', NULL);
		$this->_bot_id = Session::instance()->get('bot_id', NULL);
		$this->_bot = Session::instance()->get('bot', NULL);
		if ($this->_bot == NULL) {
			// 最初top bot 選択時、該当内容なし、ajax実行不可のバグ対応
		}
		$this->_lang_cd = Session::instance()->get('lang_cd_admin', NULL);
		I18n::$lang = $this->_lang_cd;
		$this->_item_cd = Session::instance()->get('item_cd', NULL);
		$this->_model = new Model_Adminmodel();
		$this->_model->init($this->_bot_id);
		$this->_very_model = new Model_Adminverymodel();
		$this->_member_model = new Model_Membermodel();
		$this->_aws_model = new Model_Aws();
		if (!array_key_exists($this->_lang_cd, $this->_model->get_setting('admin_support_lang'))) {
			$this->_lang_cd = 'ja';
		}
		$this->_codes = $this->_model->get_config('code.'. $this->_lang_cd);
		ob_end_flush();
	}

	public function action_subbot() {
		$post = $this->request->post();
		$bot_id = $post['bot_id'];
		$bots = $this->_model->get_sub_bots($bot_id);
		$this->response->body(json_encode($bots, JSON_UNESCAPED_UNICODE));
	}

	public function action_sublist() {
		$post = $this->request->post();
		$bot_id = $post['bot_id'];
		$bot_grp_id = $this->_model->get_grp_bot_id($bot_id);
		$bots = [];
		$sub_bots = [];
		if ($bot_grp_id == 0) {
			if ($this->_user->role_cd == '99' || $this->_user->role_cd == '80') {
				$bots = $this->_model->get_sub_bots($bot_id);
				$this->response->body(json_encode(['full_sub'=>1, 'bots'=>$bots]));
				return;
			}

			$user_bots = $this->_model->get_user_bots($this->_user->email);
			$has_parent = false;
			foreach($user_bots as $ub) {
				if ($ub->bot_id == $bot_id) {
					$has_parent = true;
					continue;
				}
				if (intval($ub->bot_id / 1000) == intval(intval($bot_id) / 1000)) {
					$sub_bots[] = ['bot_id'=>$ub->bot_id, 'bot_name'=>$ub->bot_name];
				}
			}

			if ($has_parent == false) {
				$this->response->body(json_encode(['full_sub'=>0, 'bots'=>$sub_bots]));
			}
			else {
				if ($this->_user->auth_all_groups_flg == 0) {
					$this->response->body(json_encode(['full_sub'=>1, 'bots'=>$sub_bots]));
				}
				else {
					$bots = $this->_model->get_sub_bots($bot_id);
					$this->response->body(json_encode(['full_sub'=>1, 'bots'=>$bots]));
				}
			}
		}
		else {
			$this->response->body(json_encode(['full_sub'=>-1]));
		}
	}
	
	public function action_botscenelist() {
		$post = $this->request->post();
		$bot_id = $post['bot_id'];
		$scene_use_flg = $post['scene_use_flg'];
		$scenes = $this->_model->get_bot_scene($bot_id, $scene_use_flg);
		$this->response->body(json_encode($scenes));
	}
	
	public function action_botcontextlist() {
		$post = $this->request->post();
		$bot_id = $post['bot_id'];
		$scenes = $this->_model->get_bot_context($bot_id, false);
		$this->response->body(json_encode($scenes));
	}
	
	public function action_botscenepattern() {
		$post = $this->request->post();
		$bot_id = $post['bot_id'];
		if ($bot_id == '') $bot_id = $this->_bot_id;
		$scenes = $this->_model->get_bot_tpl_message($bot_id, 'scene_name_pattern', 'ja');
		//$scenes = [''=>'(すべて)導線'] + json_decode($scenes, true);
		if ($scenes == '') $scenes = '[]';
		$this->response->body($scenes);
	}
	
	public function action_phrase() {
		$post = $this->request->post();
		$messages = $this->_model->get_phrase_messages($this->_bot_id, $post['class_cd'], $post['lang_cd']);
		if (count($messages) == 0) $messages = $this->_model->get_phrase_messages(0, $post['class_cd'], $post['lang_cd']);
		
		$msgs = array();
		for($i=0; $i<count($messages); $i++) {
			preg_match_all('/(?:\{)(.*)(?:\})/i', $messages[$i]['msg_cd'], $matchs);
			if (count($matchs[1]) == 0) {
				$msgs[] = $messages[$i];
			}
			else {
				foreach($matchs[1] as $match) {
					if ($match == "{$this->_bot->bot_class_cd}") {
						$msgs[] = $messages[$i];
						break;
					}
				}
			}
		}
		$this->response->body(json_encode($msgs));
	}
	
	public function action_usermessage() {
		$post = $this->request->post();
		//$messages = $this->_model->get_user_messages($this->_bot_id, $post['class_cd'], $post['lang_cd']);
		$messages = $this->_model->get_user_messages($this->_bot_id, null, $post['lang_cd']);
		if (count($messages) == 0) $messages = $this->_model->get_phrase_messages(0, null, $post['lang_cd']);
		
		$msgs = array();
		for($i=0; $i<count($messages); $i++) {
			preg_match_all('/(?:\{)(.*)(?:\})/i', $messages[$i]['msg_cd'], $matchs);
			if (count($matchs[1]) == 0) {
				$msgs[] = $messages[$i];
			}
			else {
				foreach($matchs[1] as $match) {
					if ($match == "{$this->_bot->bot_class_cd}") {
						$msgs[] = $messages[$i];
						break;
					}
				}
			}
		}
		$this->response->body(json_encode($msgs));
	}
	
	public function action_updatefaqsortno()
	{
		if ($this->request->post()){
			$post = $this->request->post();
			if ($post['intent_cd'] != '') {
				DB::update('t_bot_intent')->set(['sort_no'=>$post['sort_no']])->
				where('bot_id', '=', $this->_bot_id)->where('intent_cd', '=', $post['intent_cd'])->where('sub_intent_cd', '=', $post['sub_intent_cd'])->execute();
				$this->response->body(json_encode(['result'=>'success']));
				return;
			}
		}
		$this->response->body(json_encode(['result'=>'fail']));
	}
	
	public function action_updateitem()
	{
		if ($this->request->post()){
			$post = $this->request->post();
			if ($post['item_id'] != '') {
				$orm = ORM::factory('item', $post['item_id']);
				if (isset($orm->item_id)) {
					$orm->item_status_cd = $post['item_status_cd'];
					$orm->upd_user = $this->_user_id;
					$orm->upd_time = date('Y-m-d H:i:s');
					$orm->save();
					$this->response->body(json_encode(['result'=>'success']));
					return;
				}
			}
		}
		$this->response->body(json_encode(['result'=>'fail']));
	}
	
	public function action_linkitem()
	{
		if ($this->request->post()){
			$post = $this->request->post();
			if ($post['item_id'] != '') {

				//広域コンテンツ連携ボット設定を取得する
				$area_contents_link_bots = $this->_model->get_bot_setting($this->_bot_id, 'area_contents_link_bots');

                //掲載許可された広域DMO
				$dmo_contents_chat_show_allow = $this->_model->get_bot_tpl_message($this->_bot_id, 'dmo_contents_chat_show_allow', 'ja');

				$common_item = DB::select('lang_display')->from('t_item_display')->where('bot_id', 'IN', explode(',', $area_contents_link_bots) )->where('item_id', '=', $post['item_id'])->where('item_div', '=', $post['item_div'])->execute()->as_array();
				if (isset($common_item[0])) {
					$lang_display = $common_item[0];
				} else {
					$lang_array = $this->_model->get_bot_lang($this->_bot);
					$lang_display = implode(',', array_keys($lang_array));
				}

				$dmo_bots = json_decode($dmo_contents_chat_show_allow)->bots;
				$dmo_bots_array = explode(',', $dmo_bots);

				$item_id_substr = substr($post['item_id'],-4);
				$item_id_bot = str_replace($item_id_substr, "", $post['item_id']);
				$bot_grp_id = $this->_model->get_grp_bot_id($this->_bot_id);
				
				$orm = ORM::factory('itemdisplay');
				$orm->bot_id = $this->_bot_id;
				$orm->item_id = $post['item_id'];
				$orm->item_div = $post['item_div'];
				$orm->sort_no1 = 0;
				$orm->sort_no2 = 0;
				$orm->sort_no3 = 0;
				$orm->sort_no4 = 0;
				$orm->sort_no5 = 0;
				$orm->lang_display = $lang_display;
				// $post['item_id']が$bot_grp_idで始まる場合は1を設定
				if (strpos($post['item_id'], $bot_grp_id) === 0) {
					$orm->dmo_chat_show_flg = 1;
				} else {
					// それ以外の場合の条件（dmo_botsに含まれているかどうか）
					$orm->dmo_chat_show_flg = array_search($item_id_bot, $dmo_bots_array) === false ? 0 : 1;
				}
				$orm->save();
			}
		}
	}
	
	public function action_unlinkitem()
	{
		if ($this->request->post()){
			$post = $this->request->post();
			if ($post['item_id'] != '') {
				DB::delete('t_item_display')->where('item_id', '=', $post['item_id'])->where('item_div', '=', $post['item_div'])->where('bot_id', '=', $this->_bot_id)->execute();
			}
		}
	}
	
	public function action_linkmarket()
	{
		if ($this->request->post()){
			$post = $this->request->post();
			if ($post['item_id'] != '') {
				$lang_array = $this->_model->get_bot_lang($this->_bot);

				// auto translate market item description and get images/English restaurant name
				if ($post['item_div'] == '17') {
					$this->_process_tabelog_link($post['item_id'], $lang_array);
				}

				$lang_display = implode(',', array_keys($lang_array));
				$orm = ORM::factory('itemdisplay');
				$orm->bot_id = $this->_bot_id;
				$orm->item_id = $post['item_id'];
				$orm->item_div = $post['item_div'];
				$orm->sort_no1 = 0;
				$orm->sort_no2 = 0;
				$orm->sort_no3 = 0;
				$orm->sort_no4 = 0;
				$orm->sort_no5 = 0;
				$orm->lang_display = $lang_display;
				$orm->save();
			}
		}
	}

	private function _process_tabelog_link($item_id, $lang_array) {
		// TODO get top url for shop
		$market_item_orm = ORM::factory('marketitem', $item_id);
		if (!isset($market_item_orm->item_id)) {
			Log::instance()->add(Log::ERROR, "Market item not found for item_id: $item_id");
			return null;
		}
		$link_data = $market_item_orm->link_data;
		$link_data_json = json_decode($link_data, true);
		if (!$link_data_json || !isset($link_data_json['link'])) {
			// No top_url found, cannot translate
			Log::instance()->add(Log::ERROR, "Market item link_data.link not found for item_id: $item_id");
			return null;
		}
		$top_url = $link_data_json['link'];
		list($images, $restaurant_name_en, $reservation_available) = $this->_fetch_shop_info($top_url);
		Log::instance()->add(Log::DEBUG, "Fetched HTML content from $top_url".PHP_EOL.
			"Images: " . json_encode($images) . PHP_EOL .
			"Restaurant Name EN: $restaurant_name_en" . PHP_EOL .
			"Reservation Available: $reservation_available");
		$link_data_json['reservation_available'] = $reservation_available;
		if ($images) {
			$link_data_json['restaurant_image_url_array'] = $images;
		}
		$market_item_orm->link_data = json_encode($link_data_json, JSON_UNESCAPED_UNICODE);
		$market_item_orm->upd_time = date('Y-m-d H:i:s');
		$market_item_orm->save();

		$item_desc_orm_ja = ORM::factory('marketitemdescription', [
			'item_id' => $item_id,
			'lang_cd' => 'ja'
		]);
		if (!isset($item_desc_orm_ja->item_id)) {
			Log::instance()->add(Log::ERROR, "Market item description not found for item_id: $item_id, lang_cd: ja");
			return null;
		}
		$business_hours_ja = $item_desc_orm_ja->description;

		// translate item name and description
		// echo "Translating business hours for item_id: $item_id to ".json_encode(array_keys($lang_array)).PHP_EOL;
		foreach($lang_array as $lang_cd => $_code) {
			if ($lang_cd == 'ja') continue; // 日本語はスキップ

			if(!in_array($lang_cd, ['en', 'tw', 'kr', 'cn'])) {
				// Only translate to English, Chinese, and Korean
				continue;
			}

			$business_hours_translated = $this->_model->translate($business_hours_ja, $lang_cd, 'ja');
			$market_item_desc_orm = ORM::factory('marketitemdescription', [
				'item_id' => $item_id,
				'lang_cd' => $lang_cd
			]);
			$update = true;

			// Log::instance()->add(Log::ERROR, "Market item description item_id: $item_id, ".$market_item_desc_orm->item_id.", lang_cd:". $market_item_desc_orm->lang_cd);

			if (!isset($market_item_desc_orm->item_id)) {
				$market_item_desc_orm->item_id = $item_id;
				$market_item_desc_orm->lang_cd = $lang_cd;
				$market_item_desc_orm->title = $restaurant_name_en??'';
				$update = false;
			}
			$market_item_desc_orm->description = $business_hours_translated;
			if ($update) {
				$update_array = array(
					'title' => $restaurant_name_en??DB::expr('title'),
					'description' => $business_hours_translated,
					'upd_time' => DB::expr('NOW()')
				);
				DB::update('t_market_item_description')
					->set($update_array)
					->where('item_id', '=', $item_id)
					->where('lang_cd', '=', $lang_cd)
					->execute();
			} else {			
				$market_item_desc_orm->save();
			}
		}
	}

	private function _fetch_shop_info($top_url) {
		$html = @file_get_contents($top_url);
		if ($html === false || empty($html)) {
			// Log an error or handle the failure gracefully
			Log::instance()->add(Log::ERROR, "Failed to fetch HTML content from $top_url");
			return array(null, null);
		}
		$images = null;
		$regex = '~<img[^>]+src="((https://tblg.k-img.com/resize/660x370c/restaurant/images/Rvw/)[0-9]+/[0-9a-z]+\.jpg\?token=[0-9a-z]+&(amp;)?api=v[0-9]+)[^"]*"[^>]*>~i';
		preg_match_all($regex, $html, $matches);
		if ($matches){
			$images = [];
			foreach ($matches[1] as $_index=>$match) {
				if ($match) {
					$image_url = preg_replace('/&amp;/i', "&", $match);
					// echo "Found match image URL $match".PHP_EOL;
					$images[] = $image_url;
					if (count($images) >= 10) {
						break; // limit to 10 images
					}
				}
			}
		}
		if (!$images) {
			// $this->_write_log("No SELECTED images found in $top_url");
			// echo "No SELECTED images found in $top_url, finding PLAIN images...".PHP_EOL;
			//<a href="https://tblg.k-img.com/restaurant/images/Rvw/202340/640x640_rect_251e4031870a608f22e907bc8d92b50a.jpg" data-id="202340558" title="バーやまざき - " class="js-imagebox-trigger">
			// <img width="125" height="125" alt="バーやまざき - " src="https://tblg.k-img.com/restaurant/images/Rvw/202340/150x150_square_251e4031870a608f22e907bc8d92b50a.jpg" class="loading" data-was-processed="true">
			$regex = '~<a[^>]+href="((https://tblg.k-img.com/restaurant/images/Rvw/)[0-9]+/[0-9a-z_]+\.jpg)([^"]*)"[^>]*>~i';
			preg_match_all($regex, $html, $matches);
			if ($matches){
				$images = [];
				foreach ($matches[1] as $_index=>$match) {
					if ($match) {
						$image_url = $match;
						// echo "Found match image URL $match".PHP_EOL;
						$images[] = $image_url;
						if (count($images) >= 10) {
							break; // limit to 10 images
						}
					}
				}
			}
		}

		// <div class="rstdtl-side-yoyaku__booking js-rstdtl-side-yoyaku__reserve">
		// <a class="js-booking-form-open c-btn c-btn--full c-btn--primary" href="/booking/form/new?member=2&amp;rcd=30002359&amp;visit_date=20250619&amp;visit_time=1900&amp;lid=yoyaku_rstdtl_side_calendar"><span class="js-booking-container-button">予約する</span></a>
		$reserve_box_regex = '~<div class="rstdtl-side-yoyaku__booking js-rstdtl-side-yoyaku__reserve">.*<div class="js-side-calendar-wrapper rstdtl-side-yoyaku__calendar">~is';
		preg_match_all($reserve_box_regex, $html, $matches_reserve);
		$reservation_available = 0;
		if ($matches_reserve){
			foreach ($matches_reserve[0] as $_index=>$match) {
				if ($match) {
					$reservation_available = 1; // found reservation link
				}
			}
		}

		//<div class="rdheader-rstname"><a href="https://tabelog.com/en/okinawa/A4702/A470202/47020005/" property=""></a><span class="pillow-word">Nishikiya, where you can enjoy dining on the rooftop terrace or in an old folk house</span><h2 class="display-name"><span>Nishikiya</span></h2><span class="alias">(錦屋)</span></div>
		$en_name_regex = '~<h2 class="display-name">[^<]+<span>([^<]+)</span>[^<]+</h2>~i';
		$en_top_url = str_replace('https://tabelog.com/', 'https://tabelog.com/en/', $top_url);
		$en_html = file_get_contents($en_top_url);
		preg_match_all($en_name_regex, $en_html, $matches_en);
		$restaurant_name_en = null;
		if ($matches_en){
			foreach ($matches_en[1] as $_index=>$match) {
				if ($match) {
					$restaurant_name_en = $match;
				}
			}
		}

		return array(
			$images, 
			$restaurant_name_en,
			$reservation_available
		);
	}
	
	public function action_unlinkmarket()
	{
		if ($this->request->post()){
			$post = $this->request->post();
			if ($post['item_id'] != '') {
				DB::delete('t_item_display')->where('item_id', '=', $post['item_id'])->where('item_div', '=', $post['item_div'])->where('bot_id', '=', $this->_bot_id)->execute();
			}
		}
	}

	public function action_get_market_filter_options()
	{
		$item_div = $this->request->query('item_div');
		$lang_cd = $this->_lang_cd;
		$from_bot_id = $this->_model->get_bot_setting($this->_bot_id, 'area_contents_link_bots');
		$link_bot_id = explode($from_bot_id, ',');

		$filter_options = $this->_model->get_market_filter_options($link_bot_id[0], $item_div, $lang_cd);

		echo json_encode($filter_options);
	}

	public function action_csvfile()
	{
		$post = $this->request->query();
		$csv = $post['id'];
		$log = file_get_contents(APPPATH . "../../files/csv/" . $csv);
		unlink(APPPATH . "../../files/csv/" . $csv);
		$log = str_replace("\n", "\r\n", $log);
		header("Content-Type: application/octet-stream");
		header("Content-Disposition: attachment; filename=csvfile.csv");
		$f = fopen('php://output', 'w');
		ob_end_clean();
		fwrite($f, $log);
		fclose($f);
	}
	
	public function action_filedownload()
	{
		$post = $this->request->query();
		$data = file_get_contents(APPPATH . "../../files/" . $post['path'] . $post['name']);
		header("Content-Type: application/octet-stream");
		header("Content-Disposition: attachment; filename=" . $post['name']);
		$f = fopen('php://output', 'w');
		ob_end_clean();
		fwrite($f, $data);
		fclose($f);
	}

	public function action_download()
	{
		if ($this->_user->role_cd != '90' && $this->_user->role_cd != '99') {
			$this->_redirect();
		}
		
		$post = $this->request->query();
		$filename = $post['id'];
		// filename check
		$file_id = explode('-', $filename)[0];
		if ($file_id != $this->_bot_id) {
			$this->_redirect();
		}
		header("Content-Type: application/octet-stream");
		header("Content-Disposition: attachment; filename=chatlog_$filename");
		
		$log = file_get_contents(APPPATH . "../../files/csv/" . $filename);
		
		$log = str_replace("\n", "\r\n", $log);
		
		$f = fopen('php://output', 'w');
		ob_end_clean();
		fwrite($f, $log);
		fclose($f);
		/*
		$csv = fopen(APPPATH . "file/csv/" . $filename, "r");
		while (($line = fgets($csv)) !== false) {
			fput($f, $line);
		}
		fclose($csv);
		*/
	}
	
	public function action_downloadreport()
	{
		if ($this->_user->role_cd != '90' && $this->_user->role_cd != '99') {
			$this->_redirect();
		}
		$post = $this->request->query();
		$filename = $post['id'];
		$zipname = APPPATH . "../../files/report/" . $post['report']. '/' . $filename;		
		$fp = fopen($zipname,'r');
		$filesize = filesize($zipname);
		header("Content-Type: application/octet-stream");
		header("Accept-Ranges: bytes");
		header("Accept-Length: $filesize");
		header('Content-Disposition: attachment; filename=' . $filename);
		ob_clean();
		flush();
		$buffer=1024;
		$count=0;
		while(!feof($fp)&&($filesize-$count>0)){
			$data=fread($fp,$buffer);
			$count+=$data;
			echo $data;
		}
		fclose($fp);
	}
	
	public function action_uploadnow() {
		$post = $this->request->post();
		$bot_id = $this->_bot_id;
		if (isset($post['bot_id'])) {
			$bot_id = $post['bot_id'];
		}
		if (array_key_exists('image', $post)) {
			if (array_key_exists('path', $post) && str_starts_with($post['path'], 'classcode/')) {
				$url = $this->_aws_model->put_base64_file($bot_id, $post['image'], $post['filename'], $post['path']);
				$this->response->body(json_encode(['url'=>$url]));
				return;
			}
			$path = 'misc';
			if (array_key_exists('path', $post)) $path = $post['path'];
			$url = $this->_aws_model->put_base64_file($bot_id, $post['image'], time(), $path);
			$this->response->body(json_encode(['url'=>$url]));
			return;
		}
		else {
			$this->response->body(json_encode(['url'=>'', 'message'=>'no file']));
			return;
		}
	}

	public function action_uploadtos3() {
		$post = $this->request->post();
		if (array_key_exists('image', $post)) {
			$path = array_key_exists('path', $post) ? $post['path'] : 'misc';
			// ファイル名に拡張子を追加
			$data_sample = substr($post['image'], 0, 200);
			$result = preg_match('/^data:(.*)\/(.*);base64,/', $data_sample, $type_arr);
			$ext = $result ? $type_arr[2] : 'jpg';
			$filename = time() . '.' . $ext;
			$this->_aws_model->put_base64_file($this->_bot_id, $post['image'], $filename, $path);
			$s3_bucket = $this->_model->get_env('aws_s3_bucket');
			$s3_base_url = 'https://s3.ap-northeast-1.amazonaws.com/' . $s3_bucket . '/';
			$s3_url = $s3_base_url . $this->_bot_id . '/' . $path . '/' . $filename;
			$this->response->body(json_encode(['url' => $s3_url]));
		} else {
			$this->response->body(json_encode(['url' => '', 'message' => 'no file']));
		}
	}

	public function action_uploadsummernote() {
		$post = $this->request->post();
		if (array_key_exists('image', $post)) {
			$url = $this->_aws_model->put_base64_file($this->_bot_id, $post['image'], time(), 'summernote');
			$this->response->body(json_encode(['url'=>$url]));
			return;
		}
		else {
			$this->response->body(json_encode(['url'=>'', 'message'=>'no file']));
			return;
		}
		$this->response->body(json_encode(['url'=>'', 'message'=>'no file']));
	}

	public function action_uploadsurveyentry() {
		$post = $this->request->post();
		if ($_FILES['file']['name']) {
			if (!$_FILES['file']['error']) {
				$url = $this->_aws_model->put_survey_entry($this->_bot_id, $post['survey_id'], $post['no'], 'file');
				$this->response->body(json_encode(['url'=>$url]));
				return;
			}
			else
			{
				$this->response->body(json_encode(['url'=>'', 'message'=>$_FILES['file']['error']]));
				return;
			}
		}
		$this->response->body(json_encode(['url'=>'', 'message'=>'no file']));
	}

	public function action_uploadinquiryentry() {
		$post = $this->request->post();
		if ($_FILES['file']['name']) {
			if (!$_FILES['file']['error']) {
				$url = $this->_aws_model->put_inquiry_entry($this->_bot_id, $post['inquiry_id'], $post['no'], 'file');
				$this->response->body(json_encode(['url'=>$url]));
				return;
			}
			else
			{
				$this->response->body(json_encode(['url'=>'', 'message'=>$_FILES['file']['error']]));
				return;
			}
		}
		$this->response->body(json_encode(['url'=>'', 'message'=>'no file']));
	}

	public function action_uploadfaq() {
		$post = $this->request->post();
		if ($_FILES['file']['name']) {
			if (!$_FILES['file']['error']) {
				$url = $this->_aws_model->put_faq($this->_bot_id, $post['intent_cd'], $post['sub_intent_cd'], $post['area_cd'], $post['no'],$post['lang_cd'], 'file');
				$this->response->body(json_encode(['url'=>$url]));
				return;
			}
			else
			{
				$this->response->body(json_encode(['url'=>'', 'message'=>$_FILES['file']['error']]));
				return;
			}
		}
		$this->response->body(json_encode(['url'=>'', 'message'=>'no file']));
	}
	
	public function action_servicecsv()
	{
		$post = $this->request->query();
		$data = $this->_model->get_service($this->_bot_id, $post['status_cd'], $post['intent_cd'], $post['start_date'], $post['end_date']);
		$service_intent = $this->_model->get_service_intent($this->_bot, $this->_lang_cd);
		$service_status = $this->_model->get_code('45', $this->_lang_cd);
		$create_time = date('Y-m-d');
		header("Content-Type: application/octet-stream");
		header("Content-Disposition: attachment; filename=service_$create_time.csv");
		$f = fopen('php://output', 'w');
		ob_end_clean();
		fwrite($f, "\xEF\xBB\xBF");
		$title = ['受付日時','種類','ご用件','対応状態','担当者'];

		fputcsv($f, $title, ",");
		foreach($data as $log) {
			$line = array();
			$line[] = $log['log_time'];
			if(array_key_exists($log['intent_cd'], $service_intent)) {
				$line[] = $service_intent[$log['intent_cd']];
			}
			else {
				$line[] = $log['intent_cd'];
			}
			$line[] = $log['staff_msg'];
			//$line[] = $log['member_msg'];
			$line[] = $service_status[$log['service_status_cd']];
			$line[] = $log['name'];

			fputcsv($f, $line, ",");
		}
		fclose($f);
	}
	
	public function action_report1csv()
	{
		$start_date = $this->_get_session('report1_start_date', NULL);
		$end_date = $this->_get_session('report1_end_date', NULL);
		$lang_cd = $this->_get_session('report1_lang_cd', NULL);
		$bot_id =  $this->_get_session('report_bot_id', NULL);
		$scene_cd = $this->_get_session('report1_scene_cd', NULL);
		$context_id = $this->_get_session('report1_context_id', NULL);
		$intent_cd = $this->request->query('intent_cd', NULL);
		$sub_intent_cd = $this->request->query('sub_intent_cd', NULL);
		$area_cd = $this->request->query('area_cd', NULL);
		
		$data = $this->_model->get_report1_1($bot_id, $intent_cd, $sub_intent_cd, $area_cd, $start_date, $end_date, $lang_cd, $scene_cd, $context_id, $this->_lang_cd);
		
		$create_time = date('Y-m-d');
		$filename = $intent_cd . '_user_says_' . $bot_id . '.csv';
		header("Content-Type: application/octet-stream");
		header("Content-Disposition: attachment; filename=$filename");
		$f = fopen('php://output', 'w');
		ob_end_clean();
		fwrite($f, "\xEF\xBB\xBF");
		$title = ['お名前','チャンネル','言語','ユーザー導線','会話日時','内容','スコア'];

		fputcsv($f, $title, ",");
		foreach($data as $log) {
			$line = array();
			if ($log['sns_type_cd'] == 'wb') {
				$line[] = 'Webユーザ';
			}
			else {
				$line[] = $log['last_name'] . ' ' . $log['first_name'];
			}
			$line[] = $this->_codes['08'][$log['sns_type_cd']];
			$line[] = $this->_codes['02'][$log['lang_cd']];
			$line[] = $log['label'];
			$line[] = $log['log_time'];
			if ($log['lang_cd'] == 'ja' || $log['member_msg_t'] == '') {
				$line[] = $log['member_msg'];
			}
			else {
				$line[] = $log['member_msg_t'] . "<br/>※原文：". $log['member_msg'];
			}
			$line[] = $log['score'];
			fputcsv($f, $line, ",");
		}
		fclose($f);
	}
	
	public function action_intentcsv()
	{
		$post = $this->request->query();
		if ($post['answer_type'] == "01") { // 全て
			$data = $this->_model->csv_bot_intent_all($this->_bot_id);
		}
		else if ($post['answer_type'] == "02") { // 回答済み
			$data = $this->_model->csv_bot_intent_answered($this->_bot_id);
		}
		else { // 未回答
			$data = $this->_model->csv_bot_intent_all($this->_bot_id);
		}
		$faq_div = $this->_model->get_bot_setting($this->_bot_id, 'div_item_class_0');
		if ($faq_div == '') {
			$code_div = '9001' . $this->_bot->bot_class_cd;
		}
		else {
			$code_div = $faq_div;
		}
		$faq_class = $this->_model->get_code_div_kv($code_div, 'ja');
		$create_time = date('Y-m-d');
		header("Content-Type: application/octet-stream");
		header("Content-Disposition: attachment; filename=intent_$create_time.csv");
		$f = fopen('php://output', 'w');
		ob_end_clean();
		fwrite($f, "\xEF\xBB\xBF");
		foreach($data as $log) {
			if($post['answer_type'] == "03" && $log['answer1'] != '') continue;
			for($i=1; $i<=5; $i++) {
				if ($log['answer' . $i . '_type_cd'] == 'car') {
					$log['answer' . $i] = '';
				}
				unset($log['answer' . $i . '_type_cd']);
			}
			if ($code_div == $faq_div) {
				unset($log['intent_type_cd']);
				if (array_key_exists($log['t_intent_type_cd'], $faq_class)) $log['t_intent_type_cd'] = $faq_class[$log['t_intent_type_cd']];
			}
			else {
				unset($log['t_intent_type_cd']);
				if (array_key_exists($log['intent_type_cd'], $faq_class)) $log['intent_type_cd'] = $faq_class[$log['intent_type_cd']];
			}
			fputcsv($f, $log, ",");
		}
		fclose($f);
	}

	public function action_check_faq_update_by_gpt(){
		$post = $this->request->post();
		if (array_key_exists('bot_id', $post)) {
			$bot_id = $post['bot_id'];
			// 陳さんと戚さんを聞いて、t_taskテーブルを利用して、バッチの実行状況を管理と確認した方かいいです。
			$this->_model->create_task($bot_id, 'FAQ自動更新検出(施設全体バッチ)', '07', ['cmd'=>'update_faq_by_gpt-' . $bot_id ], 'admin site');
			$this->response->body(json_encode(array('result' => 'success', 'bot_id' => $bot_id)));
		} else {
			$this->response->body(json_encode(array('result' => 'error', 'message' => 'bot_id is required')));
		}
	}

	public function action_create_faq_by_gpt(){
		$post = $this->request->post();
		if (array_key_exists('bot_id', $post)) {
			$bot_id = $post['bot_id'];
			if ($this->_user->role_cd !== '99') {
				$this->response->body(json_encode(array('result' => 'error', 'message' => 'permission denied')));
				return;
			}
			// 陳さんと戚さんを聞いて、t_taskテーブルを利用して、バッチの実行状況を管理と確認した方かいいです。
			$this->_model->create_task($bot_id, 'FAQ自動作成(施設全体バッチ)', '07', ['cmd'=>'create_faq_by_gpt-' . $bot_id ], 'admin site');
			$this->response->body(json_encode(array('result' => 'success', 'bot_id' => $bot_id)));
		} else {
			$this->response->body(json_encode(array('result' => 'error', 'message' => 'bot_id is required')));
		}
	}

	public function action_check_faq_update_by_gpt_individual(){
		$post = $this->request->post();
		if (array_key_exists('bot_id', $post) && array_key_exists('intent_cd', $post) && array_key_exists('lang_cd', $post)) {
			$bot_id = $post['bot_id'];
			$intent_cd = $post['intent_cd'];
			$sub_intent_cd = $post['sub_intent_cd'];
			$lang_cd = $post['lang_cd'];
			// 陳さんと戚さんを聞いて、t_taskテーブルを利用して、バッチの実行状況を管理と確認した方かいいです。
			$this->_model->create_task($bot_id, 'FAQ自動更新検出(個別バッチ)', '07', ['cmd'=>'update_faq_by_gpt-' . $bot_id . '-' . $intent_cd . '-' . $sub_intent_cd . '-' . $lang_cd], 'admin site');
			$this->response->body(json_encode(array('result' => 'success')));
		} else {
			$this->response->body(json_encode(array('result' => 'error')));
		}
	}

	public function action_create_faq_by_gpt_individual() {
		$post = $this->request->post();
		if (array_key_exists('bot_id', $post) && array_key_exists('intent_cd', $post) && array_key_exists('lang_cd', $post)) {
			$bot_id = $post['bot_id'];
			$intent_cd = $post['intent_cd'];
			$sub_intent_cd = $post['sub_intent_cd'];
			$lang_cd = $post['lang_cd'];
			if ($this->_user->role_cd !== '99') {
				$this->response->body(json_encode(array('result' => 'error', 'message' => 'permission denied')));
				return;
			}
			// 陳さんと戚さんを聞いて、t_taskテーブルを利用して、バッチの実行状況を管理と確認した方かいいです。
			$this->_model->create_task($bot_id, 'FAQ自動作成(個別バッチ)', '07', ['cmd'=>'create_faq_by_gpt_individual-' . $bot_id . '-' . $intent_cd . '-' . $sub_intent_cd . '-' . $lang_cd], 'admin site');
			$this->response->body(json_encode(array('result' => 'success')));
		} else {
			$this->response->body(json_encode(array('result' => 'error')));
		}
	}

	public function action_create_faq_by_gpt_status() {
		$post = $this->request->post();
		if (array_key_exists('bot_id', $post) && array_key_exists('intent_cd', $post) && array_key_exists('lang_cd', $post)) {
			$bot_id = $post['bot_id'];
			$intent_cd = $post['intent_cd'];
			$sub_intent_cd = $post['sub_intent_cd'];
			$lang_cd = $post['lang_cd'];
			if ($this->_user->role_cd !== '99') {
				$this->response->body(json_encode(array('result' => 'error', 'message' => 'permission denied')));
				return;
			}
			$task_param = json_encode(['cmd'=>'create_faq_by_gpt_individual-' . $bot_id . '-' . $intent_cd . '-' . $sub_intent_cd . '-' . $lang_cd]);
			$params = [
				'bot_id' => $bot_id, 
				'task_name' => 'FAQ自動作成(個別バッチ)', 
				'task_data' => $task_param, 
				'task_type_cd' => [
					'07', '04'
				]
			];
			$response_data = $this->_model->call_admin_api('notification', 'fetch_task_status', 'POST', $params);
			if ($response_data !== null) {
				if ($response_data['task_status_cd'] == '03') {
					$data_sql = "SELECT * 
								FROM t_bot_intent_gpt_result
								WHERE bot_id = :bot_id
								AND intent_cd = :intent_cd
								AND sub_intent_cd = :sub_intent_cd
								AND lang_cd = :lang_cd";
					$data = DB::query(Database::SELECT, $data_sql)->parameters([':bot_id' => $bot_id, ':intent_cd' => $intent_cd, ':sub_intent_cd' => $sub_intent_cd, ':lang_cd' => $lang_cd])->execute()->as_array();
					if (count($data) > 0) {
						$this->response->body(json_encode(array('result' => 'success', 'status' => $response_data['task_status_cd'], 'data' => $data[0])));
						return;
					}
					return;
				}
				$this->response->body(json_encode(array('result' => 'success', 'status' => $response_data['task_status_cd'])));
				return;
			} else {
				$this->response->body(json_encode(array('result' => 'success', 'status' => '00')));
				return;
			}
		} else {
			$this->response->body(json_encode(array('result' => 'error')));
		}
	}

	public function action_update_faq_by_gpt_status() {
		$post = $this->request->post();
		if (array_key_exists('bot_id', $post) && array_key_exists('intent_cd', $post) && array_key_exists('lang_cd', $post)) {
			$bot_id = $post['bot_id'];
			$intent_cd = $post['intent_cd'];
			$sub_intent_cd = $post['sub_intent_cd'];
			$lang_cd = $post['lang_cd'];
			if ($this->_user->role_cd !== '99') {
				$this->response->body(json_encode(array('result' => 'error', 'message' => 'permission denied')));
				return;
			}

			$task_param = json_encode(['cmd'=>'update_faq_by_gpt-' . $bot_id . '-' . $intent_cd . '-' . $sub_intent_cd . '-' . $lang_cd]);
			$params = [
				'bot_id' => $bot_id, 
				'task_name' => 'FAQ自動更新検出(個別バッチ)', 
				'task_data' => $task_param, 
				'task_type_cd' => [
					'07', '04'
				]
			];
			$response_data = $this->_model->call_admin_api('notification', 'fetch_task_status', 'POST', $params);

			if ($response_data !== null) {
				if ($response_data['task_status_cd'] == '03') {
					$data_sql = "SELECT * 
								FROM t_bot_intent_gpt_result
								WHERE bot_id = :bot_id
								AND intent_cd = :intent_cd
								AND sub_intent_cd = :sub_intent_cd
								AND lang_cd = :lang_cd";
					$data = DB::query(Database::SELECT, $data_sql)->parameters([':bot_id' => $bot_id, ':intent_cd' => $intent_cd, ':sub_intent_cd' => $sub_intent_cd, ':lang_cd' => $lang_cd])->execute()->as_array();
					if (count($data) > 0) {
						$this->response->body(json_encode(array('result' => 'success', 'status' => $response_data['task_status_cd'], 'data' => $data[0])));
						return;
					}
				}
				$this->response->body(json_encode(array('result' => 'success', 'status' => $response_data['task_status_cd'])));
				return;
			} else {
				$this->response->body(json_encode(array('result' => 'success', 'status' => '00')));
				return;
			}
		} else {
			$this->response->body(json_encode(array('result' => 'error')));
		}
	}

	public function action_update_intent_gpt_settings() {
		$post = $this->request->post();

		if (!array_key_exists('bot_id', $post) || !array_key_exists('intent_cd', $post) || !array_key_exists('type', $post)) {
			$this->response->body(json_encode(['result' => 'error', 'message' => 'bot_id, intent_cd, type is required']));
			return;
		}

		$bot_id = $post['bot_id'];
		$intent_cd = $post['intent_cd'];
		$sub_intent_cd = isset($post['sub_intent_cd']) ? $post['sub_intent_cd'] : '';
		$type = $post['type'];
		$query_result = false;

		if ($type !== 'on' && $type !== 'off') {
			$this->response->body(json_encode(['result' => 'error', 'message' => 'Invalid type']));
			return;
		}

		if ($type == 'on') {
			$query_result = $this->upsert_intent_gpt_settings($bot_id, $intent_cd, $sub_intent_cd);
		} else if ($type == 'off') {
			$lang_cds = $this->get_intent_gpt_lang_cds($bot_id, $intent_cd, $sub_intent_cd);
			$query_result = $this->delete_intent_gpt_settings($bot_id, $intent_cd, $sub_intent_cd, $lang_cds);
		}

		if ($query_result) {
			$this->response->body(json_encode(['result' => 'success']));
			return;
		} else {
			$this->response->body(json_encode(['result' => 'error', 'message' => 'Failed to update settings']));
			return;
		}
	}


	private function delete_intent_gpt_settings($bot_id, $intent_cd, $sub_intent_cd, $lang_cds) {
		try {
			// First, get existing records for this bot_id, intent_cd, and sub_intent_cd combination
			$sql = "SELECT lang_cd FROM t_bot_intent_gpt_settings 
					WHERE bot_id = :bot_id 
					AND intent_cd = :intent_cd 
					AND sub_intent_cd = :sub_intent_cd";
					
			$query = DB::query(Database::SELECT, $sql)
						->parameters([
							':bot_id' => $bot_id,
							':intent_cd' => $intent_cd,
							':sub_intent_cd' => $sub_intent_cd
						])
						->execute();

			// Get existing language codes
			$existing_lang_cds = [];
			foreach ($query as $row) {
				$existing_lang_cds[] = $row['lang_cd'];
			}

			// Insert records for language codes that don't exist
			foreach ($lang_cds as $lang_cd) {
				if (!in_array($lang_cd, $existing_lang_cds)) {
					$insert_sql = "INSERT INTO t_bot_intent_gpt_settings 
								(bot_id, intent_cd, sub_intent_cd, lang_cd, upd_user, upd_time) 
								VALUES (:bot_id, :intent_cd, :sub_intent_cd, :lang_cd, :upd_user, :upd_time)";
								
					DB::query(Database::INSERT, $insert_sql)
						->parameters([
							':bot_id' => $bot_id,
							':intent_cd' => $intent_cd,
							':sub_intent_cd' => $sub_intent_cd,
							':lang_cd' => $lang_cd,
							':upd_user' => $this->_user_id,
							':upd_time' => date('Y-m-d H:i:s')
						])
						->execute();
				}
			}
			return true;
		} catch (\Throwable $th) {
			return false;
		}

	}

	private function upsert_intent_gpt_settings($bot_id, $intent_cd, $sub_intent_cd) {
		try {
			$delete_sql = "DELETE FROM t_bot_intent_gpt_settings 
						   WHERE bot_id = :bot_id 
						   AND intent_cd = :intent_cd 
						   AND sub_intent_cd = :sub_intent_cd";
			DB::query(Database::DELETE, $delete_sql)
				->parameters([
					':bot_id' => $bot_id,
					':intent_cd' => $intent_cd,
					':sub_intent_cd' => $sub_intent_cd
				])
				->execute();
			return true;
		} catch (\Throwable $th) {
			return false;
		}
	}

	private function get_intent_gpt_lang_cds($bot_id, $intent_cd, $sub_intent_cd) {
		$sql = "SELECT lang_cd FROM t_bot_intent_gpt WHERE (bot_id = :bot_id OR bot_id = 0) AND intent_cd = :intent_cd AND sub_intent_cd = :sub_intent_cd";
		$query = DB::query(Database::SELECT, $sql)
					->parameters([
						':bot_id' => $bot_id,
						':intent_cd' => $intent_cd,
						':sub_intent_cd' => $sub_intent_cd
					])
					->execute();
		$lang_cds = [];
		foreach ($query as $row) {
			if (!array_key_exists($row['lang_cd'], $lang_cds)) {
				$lang_cds[] = $row['lang_cd'];
			}
		}
		return $lang_cds;
	}

	public function action_get_url_title() {
        $post = $this->request->post();
        
        if (!array_key_exists('url', $post)) {
            $this->response->body(json_encode([
                'result' => 'error',
                'message' => 'URL parameter missing'
            ]));
            return;
        }

        $url = $post['url'];
        
        try {
            $context = stream_context_create([
                'http' => [
                    'method' => 'GET',
                    'timeout' => 10,
                    'header' => [
                        'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                        'Accept: text/html,application/xhtml+xml,application/xml',
                        'Accept-Language: ja,en-US;q=0.9,en;q=0.8'
                    ]
                ],
                'ssl' => [
                    'verify_peer' => false,
                    'verify_peer_name' => false
                ]
            ]);
            
            $html = @file_get_contents($url, false, $context);
            
            if ($html === false) {
                throw new Exception('Failed to fetch URL');
            }

            // Convert encoding to UTF-8 if needed
            $encoding = mb_detect_encoding($html, ['UTF-8', 'SJIS', 'EUC-JP'], true);
            if ($encoding !== 'UTF-8') {
                $html = mb_convert_encoding($html, 'UTF-8', $encoding);
            }
            
            // Extract title with consideration for various patterns
            if (preg_match('/<title[^>]*>(.*?)<\/title>/is', $html, $matches)) {
                $title = trim($matches[1]);
                // Remove extra whitespace and line breaks
                $title = preg_replace('/\s+/', ' ', $title);
                
                $this->response->body(json_encode([
                    'result' => 'success',
                    'title' => $title
                ]));
            } else {
                throw new Exception('Title not found');
            }
            
        } catch (Exception $e) {
            $this->response->body(json_encode([
                'result' => 'error',
                'message' => $e->getMessage()
            ]));
        }
    }
	
	public function action_logcsv()
	{
		if ($this->_user == NULL) return;
		if ($this->_user->role_cd != '90' && $this->_user->role_cd != '99') return;
		
		//$this->response->headers('Content-Type', 'application/octet-stream');
		//$this->response->headers('Content-Disposition', 'attachment; filename=test.csv');
		$start_date = NULL;
		$end_date = NULL;
		$post = $this->request->query();
		if (array_key_exists('all', $post)) {
			$member_id = NULL;
			if (array_key_exists('start_date', $post)) {
				$start_date = $post['start_date'];
				if (trim($start_date) == '') $start_date = NULL;
			}
			if (array_key_exists('end_date', $post)) {
				$end_date= $post['end_date'];
				if (trim($end_date) == '') $end_date= NULL;
			}
		}
		else {
			$member_id = Session::instance()->get('log_member', NULL);
		}
		$data = $this->_model->get_member_logs_csv($this->_bot_id, $member_id, $start_date, $end_date);
		
		$create_time = date('Y-m-d');
		header("Content-Type: application/octet-stream");
		header("Content-Disposition: attachment; filename=chatlog_$create_time.csv");
		if (count($data) == 0) {
			echo('no data.');
			return;
		}
		
		$f = fopen('php://output', 'w');
		ob_end_clean();
		fwrite($f, "\xEF\xBB\xBF");
		foreach($data as $log) {
			if ($log['sns_type_cd'] == 'wb') $log['member_name'] = 'Webユーザ';
			$msg_div = 'bot_msg';
			$message = $log['bot_msg'];
			if ($message == '') {
				$message = $log['member_msg'];
				$msg_div = 'member_msg';
			}
			$ex = json_decode($message);
			if (!isset($ex->type)) $ex = null;
			if ($ex != null) {
				if ($ex->type == 'card') {
					$message = 'carousel:';
					for($cc=count($ex->items)-1; $cc>=0; $cc--) {
						$item = $ex->items[$cc];
						if ($cc == 0) {
							$message = $message . $item->title;
						}
						else {
							$message = $message . $item->title . ";";
						}
					}
				}
				else if ($ex->type == 'image'){
					$message = 'image:';
				}
				else if ($ex->type == 'button' || $ex->type == 'list'){
					$message = $ex->type;
					foreach ($ex->items as $item) {
						$message = $message . $item->title . ';';
					}
					$message = substr($message, 0, strlen($message) - 1);
				}
			}
			$log[$msg_div] = preg_replace("/[\r\n]+/", " ", $message);
			fputcsv($f, $log, ",");
		}
		fclose($f);
	}
	
	public function action_report6csvua()
	{
		$start_date = NULL;
		$end_date = NULL;
		$post = $this->request->query();
		
		if (array_key_exists('start_date', $post)) {
			$start_date = $post['start_date'];
			if (trim($start_date) == '') $start_date = NULL;
		}
		if (array_key_exists('end_date', $post)) {
			$end_date= $post['end_date'];
			if (trim($end_date) == '') $end_date= NULL;
		}
		
		$data = $this->_model->get_report6_follow_csv($this->_bot_id, $start_date, $end_date);
		
		$create_time = date('Y-m-d');
		header("Content-Type: application/octet-stream");
		header("Content-Disposition: attachment; filename=follow_$create_time.csv");
		if (count($data) == 0) {
			ob_end_clean();
			echo('no data.');
			return;
		}
		$title = array();
		foreach($data[0] as $k=>$v) {
			$title[] = $k;
		}
		
		$f = fopen('php://output', 'w');
		ob_end_clean();
		fwrite($f, "\xEF\xBB\xBF");
		fputcsv($f, $title, ",");
		foreach($data as $log) {
			fputcsv($f, $log, ",");
		}
		fclose($f);
	}
	
	public function action_report6csv()
	{
		$start_date = NULL;
		$end_date = NULL;
		$post = $this->request->query();
		
		if (array_key_exists('start_date', $post)) {
			$start_date = $post['start_date'];
			if (trim($start_date) == '') $start_date = NULL;
		}
		if (array_key_exists('end_date', $post)) {
			$end_date= $post['end_date'];
			if (trim($end_date) == '') $end_date= NULL;
		}
		// all  bot_id = ''
		$datas = $this->_model->get_report6_csv($this->_bot_id, $start_date, $end_date);
		$scene_data = $this->_model->get_bot_scenes($this->_bot_id);
		
		$tmp_id = 0;
		$bot_scene = [];
		foreach($scene_data as $data) {
			if ($data['bot_id'] == $tmp_id) {
				$bot_scene[strval($data['bot_id'])]["scenes"][$data['scene_name']] = $data['label'];
			}
			else {
				$bot_scene[strval($data['bot_id'])] = ["bot_name" => $data['bot_name'], "scenes" =>[$data['scene_name']=>$data['label']]];
			}
			$tmp_id = $data['bot_id'];
		}
		
		$csv_data = [];
		foreach($datas as $data) {
			$scene_cd = $data['scene'];
			if (!array_key_exists($data['scene'], $bot_scene[strval($data['bot_id'])]['scenes'])) {
				$scene_cd = '-';
			}
			if ($data['sns_type_cd'] == 'wb') {
				if ($data['mobile'] == null) $data['mobile'] = 0;
				$key = $data['bot_id'] . '-' . $scene_cd . '-' . $data['sns_type_cd'] . '-' . $data['mobile'] . '-' . $data['follow_date'];
			}
			else {
				$key = $data['bot_id'] . '-' . $scene_cd . '-' . $data['sns_type_cd'] . '-' . $data['follow_date'];
			}
			if (array_key_exists($key, $csv_data)) {
				$csv_data[$key] = $csv_data[$key] + $data['follows'];
			}
			else {
				$csv_data[$key] = $data['follows'];
			}
		}
		
		$create_time = date('Y-m-d');
		header("Content-Type: application/octet-stream");
		header("Content-Disposition: attachment; filename=scene_$create_time.csv");
		if (count($csv_data) == 0) {
			ob_end_clean();
			echo('no data.');
			return;
		}
		
		$title = ['bot', 'scene', 'channel'];
		for($i=0; ;$i++) {
			$cur_day = date("Y-m-d",strtotime("+" . $i ." day",strtotime($start_date)));
			if ($cur_day > $end_date) break;
			$title[] = $cur_day;
		}
		$title[] = 'total';
		
		$f = fopen('php://output', 'w');
		ob_end_clean();
		fwrite($f, "\xEF\xBB\xBF");
		fputcsv($f, $title, ",");
		
		$sns_type_data = explode(',', $this->_bot->sns_cd);
		foreach($bot_scene as $bot_id=>$bot_data) {
			//$bot_data['scenes']['-'] = "親遷移";
			foreach($bot_data['scenes'] as $scene_cd=>$scene_name) {
				foreach($sns_type_data as $sns_type_cd) {
					$log = [$bot_data['bot_name'], $scene_name, $sns_type_cd];
					for($i=0; ;$i++) {
						$cur_day = date("Y-m-d",strtotime("+" . $i ." day",strtotime($start_date)));
						if ($cur_day > $end_date) break;
						if ($sns_type_cd == 'wb') {
							$mobile = 0;
							$key = $bot_id . '-' . $scene_cd . '-' . $sns_type_cd . '-1-' . $cur_day;
							if (array_key_exists($key, $csv_data)) {
								$mobile = $csv_data[$key];
							}
							$pc = 0;
							$key = $bot_id . '-' . $scene_cd . '-' . $sns_type_cd . '-0-' . $cur_day;
							if (array_key_exists($key, $csv_data)) {
								$pc = $csv_data[$key];
							}
							$log[] = ($pc+$mobile) . "(" . $mobile . ")";
						}
						else {
							$key = $bot_id . '-' . $scene_cd . '-' . $sns_type_cd . '-' . $cur_day;
							if (array_key_exists($key, $csv_data)) {
								$log[] = $csv_data[$key];
							}
							else {
								$log[] = '';
							}
						}
					}
					fputcsv($f, $log, ",");
				}
			}
		}
		fclose($f);
	}
	
	public function action_reportcsv2()
	{
		$post = NULL;
		$start_date = NULL;
		$end_date = NULL;
		$lang_cd = '';
		$scene_cd = '';
		$context_id = '';
		
		if ($this->request->query()){
			$post = $this->request->query();
			$start_date = $post['start_date'];
			$end_date = $post['end_date'];
			$bot_id = $post['bot_id'];
			$lang_cd = $post['lang_cd'];
		}
		else {
			$start_date = new DateTime('first day of this month');
			$start_date = $start_date->format('Y-m-d');
			$end_date = new DateTime('last day of this month');
			$end_date = $end_date->format('Y-m-d');
			$bot_id = $this->_get_session('report_bot_id', NULL);
			if ($bot_id === NULL) $bot_id = $this->_bot_id;
		}
		
		$report_data = $this->_model->get_report1_csv2($bot_id, $start_date, $end_date, $lang_cd);
		list($intent_type_dict, $intent_type_level) = $this->_model->get_report1_intent_class($bot_id);
		$csv_data = [];
		$max_col = 0;
		foreach($report_data as $row) {
			$key = $row['intent_cd'] . '#' . $row['sub_intent_cd'] . '#' . $row['area_cd'];
			if (array_key_exists($key, $csv_data)) {
				$csv_data[$key]['count'] = $csv_data[$key]['count'] + 1;
				if ($row['reason']!='') $csv_data[$key]['reason'][] = ['txt'=>$row['reason'], 'context'=>$row['context_id']];
				if (count($csv_data[$key]['reason']) > $max_col) $max_col = count($csv_data[$key]['reason']);
			}
			else {
				if ($row['facility_question_title'] != '') $row['question'] = $row['facility_question_title'];
				if ($intent_type_level > 0) {
					$temp = [];
					foreach($intent_type_dict[$key] as $c) {
						$temp[] = $c;
					}
					for($i=0;$i<$intent_type_level-count($intent_type_dict[$key]);$i++) {
						$temp[] = '';
					}
					$row['intent_type'] = $temp;
				}
				else {
					$row['intent_type'] = [];
				}
				if ($row['question'] == '') $row['question'] = $key;
				$csv_data[$key] = ['question'=>$row['question'], 'intent_type'=> $row['intent_type'], 'count'=>1, 'reason'=>[]];
				if ($row['reason']!='') $csv_data[$key]['reason'][] = ['txt'=>$row['reason'], 'context'=>$row['context_id']];
			}
		}
		
		$create_time = date('Y-m-d');
		header("Content-Type: application/octet-stream");
		header("Content-Disposition: attachment; filename=freq_survey_$create_time.csv");
		$f = fopen('php://output', 'w');
		ob_end_clean();
		fwrite($f, "\xEF\xBB\xBF");
		$title = [];
		for($i=0;$i<$intent_type_level;$i++) {
			$title[] = 'カテゴリー' . ($i+1);
		}
		$title[] = '質問文';
		$title[] = '不十分回数';
		for($i=1; $i<=$max_col; $i++) {
			$title[] = '不十分' . $i . '(コンテキスト)';
			$title[] = '不十分理由' . $i;
		}

		fputcsv($f, $title, ",");
		
		$contexts = ORM::factory('botcontext')->where('bot_id', '=', $this->_bot_id)->find_all();
		$context_kv = [];
		foreach($contexts as $c) {
			$context_kv[strval($c->context_id)] = $c->context_name;
		}
		
		foreach ($csv_data as $row) {
			$cols = [];
			foreach($row['intent_type'] as $c) {
				$cols[] = $c;
			}
			$cols[] = $row['question'];
			$cols[] = $row['count'];
			foreach($row['reason'] as $r) {
				if (array_key_exists($r['context'], $context_kv)) {
					$cols[] = $context_kv[$r['context']];
				}
				else {
					$cols[] = '';
				}
				$cols[] = $r['txt'];
			}
			fputcsv($f, $cols, ",");
		}
		fclose($f);
	}
	
	public function action_report9csv()
	{
		$post = NULL;
		$lang_cd = '';
		
		if ($this->request->query()){
			$post = $this->request->query();
		}
		else {
			$post['start_date'] = new DateTime('first day of this month');
			$post['start_date'] = $post['start_date']->format('Y-m-d');
			$post['end_date']= new DateTime('last day of this month');
			$post['end_date'] = $post['end_date']->format('Y-m-d');
			$bot_id = $this->_get_session('report_bot_id', NULL);
			if ($bot_id === NULL) $bot_id = $this->_bot_id;
		}
		
		$report_types = ['01'=>'サービス', '02'=>'担当者', '03'=>'日付'];
		$bot = ORM::factory('bot', $post['bot_id']);
		$lang_cd = $this->_lang_cd;
		$results = $this->_model->get_report9($post['bot_id'], $bot->bot_class_cd, $post['report_type'], $lang_cd, '', $post['start_date'], $post['end_date']);
		
		$create_time = date('Y-m-d');
		header("Content-Type: application/octet-stream");
		header("Content-Disposition: attachment; filename=req_report_$create_time.csv");
		$f = fopen('php://output', 'w');
		ob_end_clean();
		fwrite($f, "\xEF\xBB\xBF");
		$title = [$report_types[$post['report_type']], '受付', '対応','完了','取消','合計'];

		fputcsv($f, $title, ",");

		foreach($results as $k=>$v) {
			$rows = [];
			$rows[] = $k;
			$rows[] = $v['01'];
			$rows[] = $v['02'];
			$rows[] = $v['03'];
			$rows[] = $v['04'];
			$rows[] = $v['01'] + $v['02'] + $v['03'] + $v['04'];
			fputcsv($f, $rows, ",");
		}
		fclose($f);
	}
	
	public function action_chatpreview()
	{
		if ($this->request->post()){
			$post = $this->request->post();
			$chatpreview = View::factory('admin/chatpreview');
			$msg_type_cd = $post['type'];
			$chatpreview->msg_type_cd = $msg_type_cd;
			$chatpreview->preview = 1;
			if ($msg_type_cd == 'faq') {
				$chatpreview->faq = $post;
				$chatpreview->max_no = $post['max_no'];
				$chatpreview->intent_cd = '';
				$chatpreview->sub_intent_cd = '';
				$chatpreview->area_cd = '';
			}
			else if ($msg_type_cd == 'car') {
				$btn_name = $this->_model->get_bot_tpl_message($this->_bot_id, 'btn_name', $post['lang_cd']);
				$config_button = json_decode($btn_name, true);
				$chatpreview->button_list = $config_button;
				$buttons = [];
				if ($post['item_div'] < 9) {
					$def_buttons = $this->_model->get_bot_lst_message($this->_bot_id, 'item_def_button_' . $post['item_div'] . '_' . substr($post['class_cd'], 0, 2), $post['lang_cd']);
					if (count($def_buttons) == 0) $def_buttons = $this->_model->get_bot_lst_message($this->_bot_id, 'item_def_button_' . $post['item_div'], $post['lang_cd']);
					foreach($def_buttons as $def) {
						if (strpos($def['content'], "BTN_") === 0) {
							$def['content'] = $config_button[$def['content']];
						}
						$buttons[] = ["title"=>$def['content'], "url"=>""];
					}
				}
				if (array_key_exists('buttons', $post)) {
					foreach($post['buttons'] as $btn) {
						$same = false;
						for($i=0; $i<count($buttons); $i++) {
							$def = $buttons[$i];
							if ($btn['title'] == $def['title']) {
								$same = true;
								if ($btn['url'] == '') {
									unset($buttons[$i]);
								}
								break;
							}
						}
						if ($same) continue;
						$buttons[] = $btn;
					}
				}
				$post['buttons'] = $buttons;
				$chatpreview->item = $post;
			}
			else if ($msg_type_cd == 'btn' || $msg_type_cd == 'lst'  || $msg_type_cd == 'mnu') {
				$msg_orm_name = $this->_model->get_message_tbl($msg_type_cd);
				$btn_name = $this->_model->get_bot_tpl_message($this->_bot_id, 'btn_name', $post['lang_cd']);
				$config_button = json_decode($btn_name, true);
				$chatpreview->button_list = $config_button;
				$msg_desc_list = $this->_model->get_message_self_desc_list('', $msg_orm_name, $post['id'], $post['lang_cd']);
				$chatpreview->button_list = $config_button;
				$msg_desc_list = $this->_model->result2array($msg_desc_list);
				$exist = false;
				if (array_key_exists('menu_button', $post)) {
					$post['content'] = $post['menu_button'];
				}
				for($i=0; $i<count($msg_desc_list); $i++) {
					if ($msg_desc_list[$i]['no'] == $post['no']) {
						$msg_desc_list[$i]['content'] = $post['content'];
						$exist = true;
					}
					if ($post['no'] == 1) $msg_desc_list[0]['title'] = $post['title'];
				}
				if ($exist == false) {
					$msg_desc_list[] = ['title'=>$post['title'], 'content'=>$post['content'], 'no'=>count($msg_desc_list) + 1, 'lang_cd'=>$post['lang_cd']];
				}
				$chatpreview->msg_desc_list= $msg_desc_list;
			}
			else if ($msg_type_cd == 'txt') {
				$msg_orm_name = $this->_model->get_message_tbl($msg_type_cd);
				$msg_desc_list = $this->_model->get_message_self_desc_list('', $msg_orm_name, $post['id'], $post['lang_cd']);
				$msg_desc_list = $this->_model->result2array($msg_desc_list);
				$exist = false;
				for($i=0; $i<count($msg_desc_list); $i++) {
					if ($msg_desc_list[$i]['no'] == $post['no']) {
						$msg_desc_list[$i]['content'] = $post['content'];
						$exist = true;
					}
					if ($post['no'] == 1) $msg_desc_list[0]['title'] = $post['title'];
				}
				if ($exist == false) {
					$msg_desc_list[] = ['title'=>$post['title'], 'content'=>$post['content']];
				}
				$chatpreview->msg_desc_list= $msg_desc_list;
			}
			else if ($msg_type_cd == 'mal' || $msg_type_cd == 'tpl') {
				$msg_desc_list = [];
				$msg_desc_list[] = ['title'=>$post['title'], 'content'=>$post['content']];
				$chatpreview->msg_desc_list= $msg_desc_list;
			}
			else if ($msg_type_cd == 'img') {
				$msg_orm_name = $this->_model->get_message_tbl($msg_type_cd);
				$msg_desc_list = $this->_model->get_message_self_desc_list('', $msg_orm_name, $post['id'], $post['lang_cd']);
				$msg_desc_list = $this->_model->result2array($msg_desc_list);
				$exist = false;
				for($i=0; $i<count($msg_desc_list); $i++) {
					if ($msg_desc_list[$i]['no'] == $post['no']) {
						$msg_desc_list[$i]['title'] = $post['title'];
						$exist = true;
					}
				}
				if ($exist == false) {
					$msg_desc_list[] = ['title'=>$post['title'], 'url'=>'', 'no'=>count($msg_desc_list) + 1];
				}
				$chatpreview->msg_desc_list= $msg_desc_list;
			}
			else {
				$this->response->body('');
				return;
			}
			$this->response->body($chatpreview);
		}
	}
	
	public function action_redmine() 
	{
		if ($this->request->post()){
			$post = $this->request->post();
			if ($post['type'] == '01') {
				$log = $this->_model->get_log($this->_bot_id, $post['log_id']);
				$subject1 = '「FAQ追加」' . $post['title'];
				$user_name = $this->_user->name;
				$bot_info = '';
				if (isset($post['bot_id'])) {
					$bot_id = $post['bot_id'];
					$bot = ORM::factory('bot')->where('bot_id', '=', $bot_id)->where('delete_flg', '=', 0)->find();
					$bot_info = '施設名：' . PHP_EOL . $bot->bot_name .'('.$bot_id . ')';
				}
				if($log){
					$chat_url = $this->_model->get_env('admin_url') . 'chat?facility_cd=' . $this->_bot->facility_cd . '&id=' . $log['member_id'];
					$description = "ユーザー[ $user_name ]がFAQ追加を依頼しました。" . PHP_EOL . PHP_EOL . $bot_info . PHP_EOL . PHP_EOL .  '特記事項：' . PHP_EOL . $post['content'] . PHP_EOL . PHP_EOL .  '質問が含まれているチャット履歴：' . PHP_EOL . $chat_url;
				} else {
					$description = "ユーザー[ $user_name ]がFAQ追加を依頼しました。" . PHP_EOL . PHP_EOL . $bot_info . PHP_EOL . PHP_EOL .  '特記事項：' . PHP_EOL . $post['content'] . PHP_EOL . PHP_EOL .  '質問が含まれているチャット履歴：' . PHP_EOL . "なし(新規追加依頼)";
				}
				$ticket_id = $this->_model->post_redmine($this->_bot_id, '01', $post['log_id'], ['subject'=>$subject1, 'description'=>$description], 'ai', true);
				
				// if ($ticket_id != null) {
				// 	$redmine_setting = $this->_model->get_env('redmine');
				// 	$description = $description . PHP_EOL . 'お客様向けチケット:' . PHP_EOL . $redmine_setting['url'] . 'issues/' . $ticket_id;
				// }
				// $this->_model->post_redmine($this->_bot_id, '01', $post['log_id'], ['subject'=>$subject2, 'description'=>$description], 'ai');
				$this->response->body(json_encode(array('ok')));
			}
		}
	}
	
	public function action_sendmessage()
	{
		$mail = new Model_Email();
		
		if ($this->request->post()){
			$post = $this->request->post();
			$service_id = $post['service_id'];
			$member_id = $post['recipient'];
			$sns_type_cd = $post['sns_type_cd'];
			$lang_cd = $post['lang_cd'];
			$member_mail = $post['mail'];
			$action = $post['action'];
			$member_name = '';
			$bot_name = '';
			
			$service = ORM::factory('botservice', $service_id);
			
			// update service when change seat
			if ($action == '02') {
				$service->service_data_bak = $service->service_data;
				$service_data = json_decode($service->service_data, true);
				$service_data['seat_type.cd'] = $post['new_seat'];
				$service->service_data = json_encode($service_data, JSON_UNESCAPED_UNICODE);
				$service->service_status_cd = $action;
				$service->upd_time = date('Y-m-d H:i:s');
				$service->user_id = $this->_user_id;
				$service->upd_div = 2;
				$service->save();
			}
			else {
				$service->service_status_cd = $action;
				$service->upd_time = date('Y-m-d H:i:s');
				$service->user_id = $this->_user_id;
				$service->upd_div = 2;
				$service->save();
			}
			
			// get info to send message
			$service = ORM::factory('botservice', $service_id);
			
			$msg_name = $post['msg_name'];
			$msg = ORM::factory('botmsg')->where('bot_id', '=', 0)->where('msg_name', '=', $msg_name)->find();
			$message_send = $this->_model->get_bot_tpl_message($this->_bot_id, $msg->msg_cd, $lang_cd);
			$message_service = $this->_model->get_bot_tpl_message($this->_bot_id, $service->service_msg, $lang_cd);

			// old seat type
			$old_seat = '';
			if($action == '02' && $service->service_data_bak != NULL) {
				$service_data = json_decode($service->service_data_bak, true);
				$pair = $this->_model->format_service_message($this->_bot_id, $service_data, 'seat_type.cd', $lang_cd, $lang_cd);
				$old_seat = $pair[key($pair)];
			}
			
			$service_data = json_decode($service->service_data, true);
			$type_array = array();
			foreach($service_data as $key=>$value) {
				if ($key == 'name') $member_name = $service_data[$key];
				$pair = $this->_model->format_service_message($this->_bot_id, $service_data, $key, $lang_cd, $lang_cd);
				if ($pair == null) continue;
				$t_key = key($pair);
				$t_value = $pair[$t_key];
				if ($key == "seat_type.cd" && $old_seat!='') {
					$t_value = $old_seat . " → " . $t_value;
				}
				$message_service = str_replace("{" . $t_key . "}", $t_value, $message_service);
				if ($t_key == 'bot') $bot_name = $t_value;
			}
			
			$message_send = str_replace("{" . $service->service_msg . "}", $message_service, $message_send);

			$sns_model = new Model_Snsmodel();
			$sns_model->init_bot($this->_bot);
			$sns_model->send_message($member_id, $message_send, false);
			
			if($action == '02') {
				$sns_model->send_message_cd($member_id, $service->service_msg . "_02_user_mnu", array("service_id"=>$service->service_id));
				if ($sns_type_cd = 'wb') {
					$link_footer = $this->_model->get_bot_tpl_message($this->_bot_id, $service->service_msg . "_02_user_wb", $lang_cd);
					$default_scene_cd = $this->_model->get_bot_setting($this->_bot_id, 'default_scene_cd');
					$base_url = $this->_model->get_env('base_url') . "bot/webchat?id=" . $this->_item_cd . "&mid=" . $member_id;
					$link_footer = str_replace("{webchat_url}", $base_url, $link_footer);
					$result = $mail->send($member_mail, $member_name, $bot_name, $message_send . $link_footer);
				}
				else {
					$result = $mail->send($member_mail, $member_name, $bot_name, $message_send);
				}
			}
			else {
				$result = $mail->send($member_mail, $member_name, $bot_name, $message_send);
			}
			$intent_cd = $post['intent_cd'];
			$status_cd = $post['status_cd'];
			if ($intent_cd == '') $intent_cd = NULL;
			if ($status_cd == '') $status_cd = NULL;
			
			$this->response->body($this->_refresh_service($this->_bot_id, $intent_cd, $status_cd));
		}
	}
	
	public function action_countmember()
	{
		$post = $this->request->post();

		$user_attrs = '';

		// ajaxからempty arrayだと送られてこないのでphp error fixのためにempty arrayをつくる
		if (!isset($post['user_attr_cd'])) {
			$post['user_attr_cd'] = [];
		}

		for($i=0; $i < count($post['user_attr_cd']); $i++) {
			if($i === 0){
				$user_attrs = $post['user_attr_cd'][$i];
			}
			else {
				$user_attrs = $user_attrs . "," . $post['user_attr_cd'][$i];
			}
		}

		// 配信対象施設
		$bot_id = $this->_bot_id;
		if (isset($post['bot_id_list']) && $post['bot_id_list'] !== '') {
			$bot_id = $post['bot_id_list'];
		}
		
		$scene_cd = '';
		if (isset($post['scene_cd'])) {
			$scene_cd = $post['scene_cd'];
		}

		$member_count = $this->_model->get_members_num($bot_id, $post['sns_type_cd'], $post["lang_cd"], $post['regist_date_from'], $post['regist_date_to'], 
		$post['last_talk_time_from'], $post['last_talk_time_to'], $user_attrs, count($post['user_attr_cd']), $post['user_attr_cond'], $scene_cd);

		$this->response->body($member_count);
	}

	public function action_getmemberattrs()
	{
		$post = $this->request->post();

		// 以下get_bot_members_attrsは変更済み（旧画面なので使わないと想定）、再度この画面を使うなら修正必要
		$classes = $this->_model->get_bot_members_attrs($this->_bot_id, $post['sns_type_cd'], $post["lang_cd"], $post['regist_date_from'], $post['regist_date_to'], 
		$post['last_talk_time_from'], $post['last_talk_time_to']);

		$all_user_attrs = [];
		foreach($classes as $class) {
			$all_user_attrs[$class["class_cd"]] = $class["name"] . "　　 → " . $class["attr_num"] . "名";
		}

		$this->response->body(json_encode($all_user_attrs));
	}

	public function action_doservice()
	{
		if ($this->request->post()){
			$post = $this->request->post();
			$has_change = false;
			$orm = ORM::factory('botservice', $post['service_id']);
			if (array_key_exists('action', $post)) {
				if ($post['action'] == '05') {
					$orm->delete_flg = 1;
					$orm->upd_time = date('Y-m-d H:i:s');
					$orm->user_id = $this->_user_id;
					$orm->save();
					$orm = ORM::factory('botservicesupport');
					$orm->service_id = $post['service_id'];
					$orm->no = $this->_model->get_service_support_no($post['service_id']);
					$orm->support_type_cd = $post['action'];
					$orm->upd_user = $this->_user_id;
					$orm->save();
					$this->response->body('');
					return;
				}
				if ($orm->delete_flg == 1) $has_change = true;
				if ($orm->service_status_cd != $post['action']) {
					$orm->service_status_cd = $post['action'];
					if ($orm->service_status_cd == '02') {
						//if ($orm->service_data_bak == NULL) $orm->service_data_bak = $orm->service_data;
					}
					else {
						$orm->service_data_bak = NULL;
					}
					$has_change = true;
				}
			}
			if (array_key_exists('service_data', $post)) {
				if ($orm->service_data != $post['service_data']) {
					if ($orm->service_data_bak == NULL) $orm->service_data_bak = $orm->service_data;
					$orm->service_data = $post['service_data'];
					$has_change = true;
				}
			}
			if ($has_change) {
				$orm->upd_time = date('Y-m-d H:i:s');
				$orm->upd_div = 2;
				$orm->delete_flg = 0;
				$orm->user_id = $this->_user_id;
				$orm->save();
			}
			
			$service_status = $this->_model->get_code('45', $this->_lang_cd);
			//unset($service_status['05']);
			$btn_color = $this->_model->get_config('buttoncolor', $this->_lang_cd);
			$result = $this->get_new_service_buttons($post['action'], $post['service_id'], $service_status, $btn_color);
			$this->response->body($result);
		}
	}

	private function get_new_service_buttons($status_cd, $service_id, $buttons, $button_color) {
		$result = '';
		foreach($buttons as $key=>$value) {
			$disabled = "";
			if ($status_cd == $key) {
				$disabled = "disabled";
				$btncolor = $button_color[$value];		
			}
			else {
				$btncolor = 'gray';		
			}
		$result = $result . '<button type="button"' . $disabled . ' class="btn btn-talkbox ' . $btncolor . ' action" style="margin-bottom: 10px; opacity: 1;" act="' . $key . '" sid="' . $service_id . '">' . $value . '</button>';
		}
		return $result;
	}
	
	public function action_sendmail()
	{
		if ($this->request->post()){
			$post = $this->request->post();
			$mail = new Model_Email();
			$result = $mail->send($post['to'], $post['to_text'], $post['subject'], $post['message']);
			$this->response->body(json_encode(array('ok')));
		}
	}
	
	public function action_notification()
	{
		$now = date('Y-m-d H:i:s');
		$log_time = Session::instance()->get('last_notification_time', NULL);
		if ($log_time == NULL) $log_time = date('Y-m-d H:i:s');

		Session::instance()->set('last_notification_time', $now);

		$notification = [];
		$services = $this->_model->get_notification_request($this->_bot_id, $log_time, $now);
		foreach($services as $r) {
			$notification[] = ['type'=>'request', 'title'=>'リクエスト' . ' (受付ID:' . $r['service_id'] . ')', 
					'message'=>$r['intent_name'] . ' (' . substr($r['log_time'], 5, 11) . ')'];
		}
		$chats = $this->_model->get_notification_chatrequest($this->_bot_id, $log_time, $now);
		$sns = $this->_model->get_code('08');
		foreach($chats as $r) {
			if ($r['sns_type_cd'] == 'wb') {
				$message = 'Webユーザ' . ' (' . substr($r['last_talk_date'], 5, 11) . ')';
			}
			else {
				$member_name = $this->_model->get_member_name2($r);
				$message = $sns[$r['sns_type_cd']] . ':' . $member_name . ' (' . substr($r['last_talk_date'], 5, 11) . ')';
			}
			$notification[] = ['type'=>'chat', 'title'=>'有人対応請求', 'member_id'=>$r['member_id'],
					'message'=>$message];
		}
		$inquirys = $this->_model->get_notification_inquiry($this->_bot_id, $log_time, $now);
		foreach($inquirys as $r) {
			$notification[] = ['type'=>'inquiry', 'title'=>'問い合わせ' . ' (受付ID:' . $r['id'] . ')', 
					'message'=>$r['inquiry_name'] . ' (' . substr($r['end_time'], 5, 11) . ')'];
		}
		$this->response->body(json_encode($notification));
	}
	
	// Ajax
	public function action_service()
	{	
		$intent_cd = '';
		$status_cd = '';
		
		$post = $this->request->post();
		if ($post) {
			$post = $this->request->post();
			$intent_cd = $post['intent_cd'];
			$this->_set_session('service_list_intent_cd', $intent_cd);
			$status_cd = $post['status_cd'];
			$this->_set_session('service_list_status_cd', $status_cd);
			$bot_id = $post['bot_id'];
			if ($bot_id != $this->_bot_id) {
				$this->_redirect();
			}
		}
		
		$intent_cd = $this->_get_session('service_list_intent_cd', '');
		$status_cd = $this->_get_session('service_list_status_cd', '');

		if ($this->_bot == null) return '';
		$bot_setting = "30";
		$bot_settings = Session::instance()->get('bot_setting', NULL);
		if ($bot_settings != NULL && array_key_exists('num_request_refresh_period', $bot_settings)) {
			$bot_setting = $bot_settings['num_request_refresh_period'];
		}
		$log_date = date('Y-m-d', strtotime('-' . $bot_setting. ' day'));
		$end_log_date = date('Y-m-d', strtotime('+1 day'));
		
		$services = $this->_model->get_service($bot_id, $status_cd, $intent_cd, $log_date,"", $this->_lang_cd);

		$related_inquiry_results = $this->_model->get_inquiry_result_from_service($this->_bot_id, $log_date, $end_log_date);
		$result_ids = [];
		foreach ($services as $service) {
			$service_id = $service['service_id'];
			if (isset($related_inquiry_results[$service_id])) {
				$result_ids[] = $related_inquiry_results[$service_id]['result_id'];
			}
		}
		$inquiry_result_mails = $this->_model->call_admin_api('inquiry', 'inquiryresultmails', 'post', ['result_ids'=>$result_ids]);
		$related_mails = [];
		foreach ($related_inquiry_results as $service_id => $data) {
			$result_id = $data['result_id'];
			$mail_sender_alias = '';
			if (isset($data['mail_sender']) && $data['mail_sender'] == '1') {
				$mail_sender_alias = "=?utf-8?B?". base64_encode($this->_bot->bot_name) . "?=";
			}
			$mails = $inquiry_result_mails[$result_id] ?? [];
			foreach ($mails as $key => $mail) {
				$mails[$key]['result_id'] = $result_id;
				$mails[$key]['mail_sender_alias'] = $mail_sender_alias;
			}
			$related_mails[$service_id] = $mails;
		}

		$services_with_support = [];
		foreach($services as $service) {
			if ($service['delete_flg'] == 1 && $post['delete_flg'] == 0) continue;
			$mails = $related_mails[$service['service_id']] ?? [];
			$support = $this->_model->get_service_support($service["service_id"]);
			$temp_support = array_merge($support ?? [], $mails);
			usort($temp_support, function($a, $b) {
				return strtotime($b['upd_time']) - strtotime($a['upd_time']);
			});
			$new_service = $service;
			$new_service["support"] = $temp_support;
			array_push($services_with_support, $new_service);
		}

		$service_view = View::factory('admin/servicetablerows');
		$service_view->_bot_lang = $this->_model->get_bot_lang($this->_bot);
		$new_flg = "0";
		if (count($services) > 0) {
			$last_service_id = $this->_get_session ('last_service_id', 0);
			if ($services [0] ['service_id'] > $last_service_id) {
				$new_flg = "1";
				$this->_set_session ('last_service_id', $services[0]['service_id']);
			}
		}
		$service_status = $this->_model->get_code('45', $this->_lang_cd);

		$service_view->buttons = $service_status;
		$service_view->button_color = $this->_model->get_config('buttoncolor', $this->_lang_cd);
		$service_view->bot_class_cd = $this->_bot->bot_class_cd;
		$service_view->services = $services_with_support;
		$service_view->has_delete = 0;
		$service_view->_codes = $this->_model->get_config('code.'. $this->_lang_cd);
	
		$this->response->body(json_encode(array('service'=>$service_view->render(), 'new_flg'=>$new_flg)));
	}

	public function action_servicesupport()
	{
		if ($this->request->post()){
			$post = $this->request->post();
			$orm = ORM::factory('botservicesupport');
			$orm->service_id = $post['service_id'];
			$orm->no = $this->_model->get_service_support_no($post['service_id']);
			$orm->support_type_cd = $post['support_type_cd'];
			$orm->memo = array_key_exists("memo", $post) ? $post['memo'] : "";
			$orm->upd_user = $this->_user_id;
			$orm->save();
			$this->response->body($this->get_new_service_support($post['service_id']));
		}
	}

	private function get_new_service_support($service_id) {
		$supports = $this->_model->get_service_support($service_id);
		$result = '';
		$service_status = $this->_model->get_code('45', $this->_lang_cd);
		foreach($supports as $support) {
			if ($support['support_type_cd']) {
				$labelcolor = '#d84a38';
				if ($support['support_type_cd'] == '02') {
					$labelcolor = '#FFB848';
				} elseif ($support['support_type_cd'] == '03') {
					$labelcolor = '#1BBC9B';
				} elseif ($support['support_type_cd'] == '04') {
					$labelcolor = '#95A5A6';
				}
				$support_label = '<span class="label" style="background-color: ' . $labelcolor . '; color: #FFF;">' . $service_status[$support['support_type_cd']] . '</span>';
				$comment = '<div style="width: 100%; font-size:11px;">'. substr($support['upd_time'], 0, 16) . ' ' . $support['name'] . ' ' . $support_label .'</div>';
				$result = $result . '<div class="small-table-pannel" style="display: flex;padding: 8px; background-color: #EFEFEF;">' . $comment . '</div>';
			} else {
				$close = '<div class="icon-cancel-small js-memo-delete" no="' . $support['no'] . '" style="margin: 0 0 0 auto; cursor: pointer"></div>';
				$comment = '<div style="width: calc(100% - 18px); font-size:11px;">'. substr($support['upd_time'], 0, 16) . ' ' . $support['name'] . '<br/>' . nl2br($support['memo']) .'</div>';
				$result = $result . '<div class="small-table-pannel" style="display: flex;padding: 8px;">' . $comment . $close . '</div>';
			}
		}
		return $result;
	}

	public function action_deleteservicesupportmemo()
	{
		if ($this->request->post()){
			$post = $this->request->post();
			DB::delete('t_bot_service_support')->where('service_id', '=', $post['service_id'])->where('no', '=', $post['no'])->execute();
			$this->response->body($this->get_new_service_support($post['service_id']));
		}
	}
	
	private function _refresh_service($bot_id, $status_cd=NULL, $intent_cd=NULL)
	{
		if ($this->_bot == null) return '';
		$bot_setting = "30";
		$bot_settings = Session::instance()->get('bot_setting', NULL);
		if ($bot_settings != NULL && array_key_exists('num_request_refresh_period', $bot_settings)) {
			$bot_setting = $bot_settings['num_request_refresh_period'];
		}
		$log_date = date('Y-m-d', strtotime('-' . $bot_setting. ' day'));
		
		$services = $this->_model->get_service($bot_id, $status_cd, $intent_cd, $log_date,"", $this->_lang_cd);

		$services_with_support = [];
		foreach($services as $service) {
			$support = $this->_model->get_service_support($service["service_id"]);
			$new_service = $service;
			$new_service["support"] = $support;
			array_push($services_with_support, $new_service);
		}

		$service_view = View::factory('admin/servicetable');
		$service_view->_bot_lang = $this->_model->get_bot_lang($this->_bot);
		$service_view->new_flg = "0";
		if (count($services) > 0) {
			$last_service_id = $this->_get_session ('last_service_id', 0);
			if ($services [0] ['service_id'] > $last_service_id) {
				$service_view->new_flg = "1";
				$this->_set_session ('last_service_id', $services[0]['service_id']);
			} else if ($services [0] ['service_id'] < $last_service_id) {
				//$service_view->new_flg = "1";
				//$this->_set_session ('last_service_id', $services[0]['service_id']);
			}
		}
		$service_status = $this->_model->get_code('45', $this->_lang_cd);
		unset($service_status['05']);
		$service_view->buttons = $service_status;
		$service_view->button_color = $this->_model->get_config('buttoncolor', $this->_lang_cd);
		$service_view->bot_class_cd = $this->_bot->bot_class_cd;
		$service_view->services = $services_with_support;
		$service_view->has_delete = 0;
		$service_view->_codes = $this->_model->get_config('code.'. $this->_lang_cd);
		return $service_view;
	}
	
	// Ajax
	public function action_order()
	{
		$class_cd = '';
		$status_cd = '';
		
		$bot_setting = "30";
		$bot_settings = Session::instance()->get('bot_setting', NULL);
		if ($bot_settings != NULL && array_key_exists('num_request_refresh_period', $bot_settings)) {
			$bot_setting = $bot_settings['num_request_refresh_period'];
		}
		$log_date = str_replace('-', '', date('Y-m-d', strtotime('-' . $bot_setting. ' day')));
		
		$post = $this->request->post();
		if ($post) {
			$post = $this->request->post();
			$class_cd = $post['class_cd'];
			$status_cd = $post['status_cd'];
		}
		
		if ($class_cd== '') $class_cd = NULL;
		if ($status_cd == '') $status_cd = NULL;
		
		$services = $this->_model->get_orders($this->_bot_id, '', $class_cd, $log_date);		
		
		$service_view = View::factory('admin/ordertable');
		$service_view->codes = $this->_codes;
		$service_view->new_flg = "0";
		if (count ( $services ) > 0) {
			$last_service_id = $this->_get_session ('last_order_id', 0);
			if ($services [0] ['order_id'] > $last_service_id) {
				$service_view->new_flg = "1";
				$this->_set_session ('last_order_id', $services[0]['order_id']);
			} else {
				
			}
		}
		$service_view->services = $services;
		$this->response->body($service_view);
	}
	
	public function action_skillintent() {
		$bot_id = $this->_bot_id;
		$post = $this->request->post();
		if (isset($post['temp_bot_id'])) {
			$bot_id = $post['temp_bot_id'];
		}
		$results = $this->_model->get_bot_faq_answered($bot_id, $this->_lang_cd);
		$this->response->body(json_encode($results));
	}

	public function action_skillparam()
	{
		$type = $this->request->post()['type'];
		if ($type=='item_div') {
			$results = $this->_model->get_code_div_kv(888807, $this->_lang_cd);
		}
		else if ($type=='class_cd') {
			
		}
		else if ($type=='message') {
			
		}
		$this->response->body(json_encode($results));
	}
	
	public function action_itemcodediv()
	{
		$post = $this->request->post();
		$bot_id = $this->_bot_id;
		$item_div = $post['item_div'];
		if (isset($post['bot_id'])) {
			$bot_id = $post['bot_id'];
		}
		$this->response->body($this->_model->get_bot_setting($bot_id, 'div_item_class_' . $item_div));
	}
	
	public function action_kind() 
	{
		$post = $this->request->post();
		$kind_list = DB::select('kind')->distinct('kind')->from('m_skill')->where('lang_cd', '=', $this->_lang_cd)->order_by('sort_no','ASC')->execute();
		$kinds = [];
		foreach($kind_list as $kind) {
			$kinds[$kind['kind']] = $kind['kind'];
		}
		$result = ['kinds'=>$kinds];
		if (array_key_exists('skill', $post)) {
			$orm = ORM::factory('skill')->where('skill', '=', $post['skill'])->where('lang_cd', '=', $this->_lang_cd)->find();
			$result['selected'] = $orm->kind;
		}
		$this->response->body(json_encode($result));
	}

	public function action_skill()
	{
		$skill = $this->request->post()['skill'];
		$orm = ORM::factory('skill')->where('skill', '=', $skill)->where('lang_cd', '=', $this->_lang_cd)->find();
		$this->response->body($orm->params);
	}
	
	public function action_skillkind()
	{
		$kind = $this->request->post()['kind'];
		if ($kind == '') {
			$orms = ORM::factory('skill')->where('lang_cd', '=', $this->_lang_cd)->where('delete_flg', '=', '0')->order_by('sort_no')->find_all();
		}
		else {
			$orms = ORM::factory('skill')->where('kind', '=', $kind)->where('lang_cd', '=', $this->_lang_cd)->where('delete_flg', '=', '0')->order_by('sort_no')->find_all();
		}
		$skills = [];
		foreach($orms as $orm) {
			$skills[$orm->skill] = $orm->skill_name;
		}
		$this->response->body(json_encode($skills));
	}
	
	public function action_kindskill()
	{
		$skill = $this->request->post()['skill'];
		$orm = ORM::factory('skill')->where('skill', '=', $skill)->where('lang_cd', '=', $this->_lang_cd)->find();
		$this->response->body($orm->kind);
	}
	
	public function action_updateskill()
	{
		$post = $this->request->post();
		$intent_cd = $post['intent_cd'];
		$sub_intent_cd = $post['sub_intent_cd'];
		$skill = $post['skill'];
		$lang_cd = '';
		if (array_key_exists('lang_cd', $post)) {
			$lang_cd = $post['lang_cd'];
		}
		$skill = $this->_model->format_skill($skill);
		if ($skill != '') {
			$skills = json_decode($skill, true);
			foreach($skills as $s) {
				if ($s['skill'] == 'DEFAULT') {
					if ($lang_cd == '') {
						DB::delete('t_intent_skill')->where('bot_id', '=', $this->_bot_id)->where('intent_cd', '=', $intent_cd)->where('sub_intent_cd', '=', $sub_intent_cd)->execute();
					}
					else {
						DB::delete('t_intent_skill')->where('bot_id', '=', $this->_bot_id)->where('intent_cd', '=', $intent_cd)->where('sub_intent_cd', '=', $sub_intent_cd)->where('lang_cd', '=', $lang_cd)->execute();
					}
					// 親などSKILLを取得
					$bot_ids = [];
					$bot_template_id = $this->_model->get_template_bot_id($this->_bot_id);
					$flg_refer_template_faq = $this->_model->get_bot_setting($this->_bot_id, 'flg_refer_template_faq');
					if ($flg_refer_template_faq == 1 && $bot_template_id != '') {
						$bot_ids[] = $bot_template_id;
					}
					$bot_grp_id = $this->_model->get_grp_bot_id($this->_bot_id);
					if ($bot_grp_id > 0) {
						$bot_ids[] = $bot_grp_id;
					}
					$bot_ids[] = 0;
					foreach($bot_ids as $b_id) {
						$intent_skill = ORM::factory('intentskill')->where('bot_id', '=', $b_id)->where('intent_cd', '=', $intent_cd)->where('sub_intent_cd', '=', $sub_intent_cd)->where('lang_cd', '=', $lang_cd)->find();
						if (isset($intent_skill->skill)) {
							$skill = $this->_model->format_skill($intent_skill->skill);
							$this->response->body($skill);
							return;
						}
					}
					$this->response->body('');
					return;
				}
			}
		}

		if ($lang_cd == '') {
			$orm = ORM::factory('botintentskill')->where('bot_id', '=', 0)->where('intent_cd', '=', $intent_cd)->where('sub_intent_cd', '=', $sub_intent_cd)->find();
			if($orm->skill == $skill && $this->_bot_id != 0) {
				DB::delete('t_intent_skill')->where('bot_id', '=', $this->_bot_id)->where('intent_cd', '=', $intent_cd)->where('sub_intent_cd', '=', $sub_intent_cd)->execute();
			}
			else {
				DB::delete('t_intent_skill')->where('bot_id', '=', $this->_bot_id)->where('intent_cd', '=', $intent_cd)->where('sub_intent_cd', '=', $sub_intent_cd)->execute();
				$orm = ORM::factory('botintentskill');
				$orm->bot_id = $this->_bot_id;
				$orm->intent_cd = $intent_cd;
				$orm->sub_intent_cd = $sub_intent_cd;
				$orm->skill = $skill;
				$orm->upd_user = $this->_user->user_id;
				$orm->upd_time = date('Y-m-d H:i:s');
				$orm->save();
			}
		}
		else {
			$orm = ORM::factory('botintentskill')->where('bot_id', '=', 0)->where('intent_cd', '=', $intent_cd)->where('sub_intent_cd', '=', $sub_intent_cd)->where('lang_cd', '=', $lang_cd)->find();
			if($orm->skill == $skill && $this->_bot_id != 0) {
				DB::delete('t_intent_skill')->where('bot_id', '=', $this->_bot_id)->where('intent_cd', '=', $intent_cd)->where('sub_intent_cd', '=', $sub_intent_cd)->where('lang_cd', '=', $lang_cd)->execute();
			}
			else {
				DB::delete('t_intent_skill')->where('bot_id', '=', $this->_bot_id)->where('intent_cd', '=', $intent_cd)->where('sub_intent_cd', '=', $sub_intent_cd)->where('lang_cd', '=', $lang_cd)->execute();
				$orm = ORM::factory('botintentskill');
				$orm->bot_id = $this->_bot_id;
				$orm->intent_cd = $intent_cd;
				$orm->sub_intent_cd = $sub_intent_cd;
				$orm->lang_cd = $lang_cd;
				$orm->skill = $skill;
				$orm->upd_user = $this->_user->user_id;
				$orm->upd_time = date('Y-m-d H:i:s');
				$orm->save();
			}
		}

		$domains = [];
		$this->_model->create_whitelist($skill, $domains, 'skill', $intent_cd, $this->_user->user_id);

		$this->response->body($skill);
	}
	
	public function action_delsetting()
	{
		$post = $this->request->post();
		$setting_cd = $post['setting_cd'];
		DB::delete('t_bot_setting')->where('bot_id', '=', $this->_bot_id)->where('setting_cd', '=', $setting_cd)->execute();
		$this->response->body('');
	}
	
	public function action_updatekeywordskill()
	{
		$post = $this->request->post();
		$skill = $post['skill'];
		$keyword = $post['keyword'];
		if (array_key_exists('skill', $post)) {
			$skill = $this->_model->format_skill($skill);
			DB::update('t_bot_keyword')->set(array('skill'=>$skill))->where('bot_id', '=', $this->_bot_id)->where('keyword', '=', $keyword)->execute();
		}
		else {
			if ($post['keyword'] == '') {
				DB::insert('t_bot_keyword', array('bot_id', 'keyword'))->values(array($this->_bot_id, $post['keyword_new']))->execute();
			}
			else {
				if ($post['keyword_new'] == '') {
					DB::delete('t_bot_keyword')->where('bot_id', '=', $this->_bot_id)->where('keyword', '=', $keyword)->execute();
				}
				else {
					DB::update('t_bot_keyword')->set(array('keyword'=>$post['keyword_new']))->where('bot_id', '=', $this->_bot_id)->where('keyword', '=', $keyword)->execute();
				}
			}
		}
		$this->response->body('');
	}
	
	public function action_updatesceneskill()
	{
		$scene_name = $this->request->post()['scene_name'];
		$skill = $this->request->post()['skill'];
		$skill = $this->_model->format_skill($skill);
		DB::update('t_bot_scene')->set(array('welcome_skill'=>$skill,'upd_user'=>$this->_user->user_id,'upd_time'=>date('Y-m-d H:i:s')))->where('bot_id', '=', $this->_bot_id)->where('scene_name', '=', $scene_name)->execute();
		$this->_model->dirty_bot_control('t_bot_scene');
		$this->response->body('');
	}
	
	public function action_updatebottimeskill()
	{
		$busitime_no = $this->request->post()['busitime_no'];
		$skill = $this->request->post()['skill'];
		$skill = $this->_model->format_skill($skill);
		DB::update('t_bot_busitime')->set(array('skill'=>$skill))
		->where('bot_id', '=', $this->_bot_id)
		->where('no', '=', $busitime_no)->execute();
		$this->response->body('');
	}
	
	public function action_updatemsgskill()
	{
		$item_id = $this->request->post()['msg_id'];
		$lang_cd = $this->request->post()['lang_cd'];
		$no = $this->request->post()['no'];
		$skill = $this->request->post()['skill'];
		$skill = $this->_model->format_skill($skill);
		DB::update('t_item_description')->set(array('btn' . $no . '_url' =>$skill))
		->where('item_id', '=', $item_id)
		->where('lang_cd', '=', $lang_cd)->execute();
		$this->response->body('');
	}
	
	public function action_classcode()
	{
		$bot_id = $this->_bot_id;
		$post = $this->request->post();
		$code_div = $post['div'];
		$parent_cd = $post['class_cd'];
		if (isset($post['bot_id'])) {
			$bot_id = $post['bot_id'];
		}
		$item_class = $this->_model->get_class_code($code_div, $parent_cd, $this->_lang_cd, $bot_id);
		
		if (array_key_exists('blank', $post) && $post['blank'] == 1) {
			//$item_class = [''=>'-'] + $item_class;
			//array_unshift($item_class, [''=>'-'][0]);
			$new_item_class = [];
			$new_item_class[] = ["class_cd"=>"", "name"=>__('admin.common.label.all'), "sort"=>0];
			foreach($item_class as $k) {
				$new_item_class[] = $k;
			}
			$item_class = $new_item_class;
		}
		//Log::instance()->add(Log::DEBUG, "class_code :" . json_encode($item_class));
		$this->response->body(json_encode($item_class));
	}
	
	public function action_classcodeiteminfo()
	{
		$post = $this->request->post();
		$item_flg = $this->_model->get_item_class_show_rule($this->_bot_id, $post['div'], $post['class_cd']);
		$this->response->body(json_encode(['item_flg'=>$item_flg]));
	}
	
	public function action_allclasscode()
	{
		$bot_id = $this->_bot_id;
		$post = $this->request->post();
		$code_div = $post['div'];
		if (isset($post['bot_id'])) {
			$bot_id = $post['bot_id'];
		}
		$item_class = $this->_model->get_all_class_code($code_div, $this->_lang_cd, $bot_id);
		$this->response->body(json_encode($item_class));
	}

	public function action_invoicebillingdl()
	{
		$post = $this->request->query();
		$url = $post['url'];

		$invoice_model = new Model_Invoicemodel();
		$access_token = $invoice_model->get_access_token();
		
		$header = ['Authorization:BEARER ' . $access_token, 'Content-Type:application/octet-stream'];
		$pdf_steam = $this->_model->curl_get($url, $header);
		
		DB::update('t_invoice_billing')->set(['bot_download_flg'=>1])->where('pdf_url', '=', $url)->execute();
		
		header("Content-Type: application/octet-stream");
		header("Content-Disposition: attachment; filename=billing.pdf");
		
		$f = fopen('php://output', 'w');
		ob_end_clean();
		fwrite($f, $pdf_steam);
		fclose($f);
	}
	
	public function action_language() {
		$post = $this->request->query();
		if ($post){
			Session::instance()->set('lang_cd', $post['id']);
			$this->response->body(json_encode(array('ok')));
		}
	}
	
	public function action_currency() {
		$post = $this->request->query();
		if ($post){
			Session::instance()->set('currency_cd', $post['id']);
			$this->response->body(json_encode(array('ok')));
		}
	}
	
	public function action_hasauth() {
		$this->response->body('');
	}
	
	public function action_logout() {
		Cookie::delete('email');
		Cookie::delete('password');
		$this->response->body(json_encode(array('ok')));
	}
	
	private function hmstring($id)
	{
		$hour = intval($id / 2);
		$minute = ($id % 2) * 30;
		return sprintf("%02d", $hour) . ':' . sprintf("%02d", $minute);
	}
	
	public function action_stock()
	{
		$post = $this->request->query();
		if ($post){
			$product = ORM::factory('product', $post['id']);
			$stock_date = date('Y-m-d',strtotime($post['date']));
			$orders = ORM::factory('order')
				->where('stock_cd', '=', $product->stock_cd)
				->where('order_status_cd', '<>', '09')
				->where('experience_date', '=', $stock_date)
				->order_by('order_id', 'DESC')
				->find_all();
				
			$stock_date = date('Y-m-d',strtotime($post['date']));
			$stock = ORM::factory('itemstock')
				->where('item_id', '=', $product->item_id)
				->where('stock_cd', '=', $product->stock_cd)
				->where('stock_date', '=', $stock_date)
				->find()
				->as_array();
				
			if ($stock['item_id'] == NULL) {
				$stock = ORM::factory('itemstock')
					->where('item_id', '=', $product->item_id)
					->where('stock_cd', '=', $product->stock_cd)
					->where('stock_date', '=', '2000-01-01')
					->find()
					->as_array();
			}
		
			foreach ($orders as $order) {
				for($i=0; $i<$product->time_span; $i++) {
					$key = sprintf("%02d", $order->time_id);
					$stock["time_id_$key"] --;
				}
			}
		
			$select_str = '';
			$options = array();
			
			for($i=0; $i<48 - $product->time_span; $i++) {
				$free = true;
				for($j=0; $j<$product->time_span; $j++) {
					$key = sprintf("%02d", $i);
					if ($stock["time_id_$key"] - 1 < 0) {
						$free = false;
						break;
					}
				}
				if ($free) {
					$time = $this->hmstring($i) . "-" . $this->hmstring($i+$product->time_span);
					$options[] = $time;
					$select_str = $select_str . "<option value=\"$i\">$time</option>";
				}
			}
		}
		//$this->response->body(json_encode($options));
		$this->response->body(json_encode($select_str));
	}
	
	public function action_intentcontext()
	{
		$intent_cd = $this->request->post()['intent_cd'];
		$context_cd = $this->request->post()['context_cd'];
		if (isset($intent_cd)) {
			DB::update('t_bot_intent')->set(array('context_cd'=>$context_cd))->
			where('bot_id', '=', $this->_bot_id)->where('intent_cd', '=', $intent_cd)->execute();
			$this->response->body(json_encode(array('ok')));
		}
	}
	
	public function action_selectmember()
	{
		$member_id = $this->request->post()['member_id'];
		$member_name = $this->request->post()['member_name'];
		$members = Session::instance()->get('select_members', NULL);
		if ($members == NULL) {
			$members = array($member_id=>$member_name);
		}
		else {
			if (key_exists($member_id, $members)) {
				unset($members[$member_id]);
			}
			else {
				$members[$member_id] = $member_name;
			}
		}
		Session::instance()->set('select_members', $members);
		$this->response->body("(" . count($members) . ")");
	}
	
	public function action_upload()
	{
		/*
		$chat_member_id = Session::instance()->get('chat_member', NULL);
		if ($chat_member_id == NULL) {
			$this->response->body(json_encode(array('code'=>1, 'val'=>'送信先はありません。'), JSON_UNESCAPED_UNICODE));
			return;
		}
		*/
		$post = $this->request->post();
		if ($post){
			$post = $this->request->post();
			$chat_member_id = $post['member'];
			$url = $this->_aws_model->put_base64_file($this->_bot_id, $post['img'], md5(uniqid(rand())), 'upload/operator/' . $this->_user_id . '/img');
			if ($url != '') {
				$sns_model = new Model_Snsmodel();
				$bot = ORM::factory("bot", $this->_bot_id);
				$sns_model->init_bot($bot);
				$sns_model->send_image_message($chat_member_id, $url);
				$this->response->headers('Access-Control-Allow-Origin', '*');
				$this->response->body(json_encode(array('code'=>0, 'val'=>$url)));
				return;
			}
			else {
				$this->response->body(json_encode(array('code'=>1, 'val'=>'無効なファイルタイプです。'), JSON_UNESCAPED_UNICODE));
				return;
			}
		}
	}

	public function action_uploadfile()
	{
		$post = $this->request->post();
		if ($post){
			$post = $this->request->post();
			$chat_member_id = $post['member'];
			$url = $this->_aws_model->put_base64_file($this->_bot_id, $post['file'], $post['name'], 'upload/operator/' . $this->_user_id . '/file/' . md5(uniqid(rand())));
			if ($url != '') {
				$filename = $post['name'];
				$filesize = $post['size'];
				$sns_model = new Model_Snsmodel();
				$bot = ORM::factory("bot", $this->_bot_id);
				$sns_model->init_bot($bot);
				$sns_model->send_file_message($chat_member_id, $url, $filename, $filesize);
				$this->response->headers('Access-Control-Allow-Origin', '*');
				$this->response->body(json_encode(array('code'=>0, 'val'=>$url)));
				return;
			}
			else {
				$this->response->body(json_encode(array('code'=>1, 'val'=>'無効なファイルタイプです。'), JSON_UNESCAPED_UNICODE));
				return;
			}
		}
	}

	public function action_botexport()
	{
		$util_model = new Model_Utilmodel();
		$path = APPPATH . "../../files/bot_data/" . $this->_bot_id;
		if(is_dir($path)) {
			$util_model->clear_dir($path);
		}
		else {
			mkdir($path, 0777, true);
		}
		$tables = $this->_model->get_setting('bot_tables');
		foreach($tables as $table) {
			$util_model->bot2csv($this->_bot_id, $table, $path);
		}
		$zip_file = APPPATH . "../../files/bot_data/" . $this->_bot_id . ".zip";
		$util_model->zip_path($path, $zip_file);
		
		header("Content-Type: application/octet-stream");
		header("Content-Disposition: attachment; filename=$this->_bot_id.zip");
		$log = file_get_contents($zip_file);
		$f = fopen('php://output', 'w');
		ob_end_clean();
		fwrite($f, $log);
		fclose($f);
	}
	
	public function action_reqtask()
	{
		if ($this->request->post()){
			$post = $this->request->post();
			$orm = ORM::factory('redmine');
			$orm->bot_id = $this->_bot_id;
			$orm->link_class_cd= $post['link_class_cd'];  // redmine=01
			$orm->link_type_cd = $post['link_type_cd'];
			$orm->ticket_id = 0;
			$orm->link_id = $post['item_id'];
			if (array_key_exists('req_task_msg_cd', $post)) {
				$req_task_msg = $this->_model->get_bot_tpl_message($this->_bot_id, $post['req_task_msg_cd'], 'ja');
				$req_task_data = json_decode($req_task_msg, true);
				$orm->limited_date = $req_task_data['limited_date'];
				$orm->title = $req_task_data['title'];
				$orm->description = $req_task_data['description'];
				$orm->req_data = json_encode(['msg_cd'=>$post['req_task_msg_cd']]);
			}
			else {
				//$orm->limited_date = $post['limited_date'];
				$orm->title = $post['title'];
				$orm->description = $post['description'];
			}
			$orm->req_user = $this->_user_id;
			$orm->upd_user = $this->_user_id;
			$orm->save();
			$this->response->body($this->_reqview($orm->link_type_cd, $orm->link_id));
		}
	}
	
	public function action_reqtaskupdate()
	{
		if ($this->request->post()){
			$post = $this->request->post();
			$orm = ORM::factory('redmine', $post['id']);
			$orm->link_status_cd = $post['status_cd'];
			if ($post['status_cd'] == '02') {
				$orm->res_user = $this->_user_id;
				$orm->res_time = date('Y-m-d H:i:s');
			}
			$orm->upd_user = $this->_user_id;
			$orm->upd_time = date('Y-m-d H:i:s');
			$orm->save();
			$this->response->body($this->_reqview($orm->link_type_cd, $orm->link_id));
		}
	}

	public function action_reqtaskcancel()
	{
		if ($this->request->post()){
			$post = $this->request->post();
			$orm = ORM::factory('redmine', $post['id']);
			$link_type_cd = $orm->link_type_cd;
			$link_id = $orm->link_id;
			$orm->delete();
			$this->response->body($this->_reqview($link_type_cd, $link_id));
		}
	}
	
	public function action_blockuser()
	{
		$post = $this->request->post();
		$member_id = $post['member_id'];
		if (isset($post['bot_id'])) {
			DB::update('t_bot_member')->set(['is_blocked'=>$post['is_blocked']])->where('member_id', '=', $member_id)->where('bot_id', '=', $post['bot_id'])->execute();
		}
		else {
			DB::update('t_bot_member')->set(['is_blocked'=>$post['is_blocked']])->where('member_id', '=', $member_id)->execute();
		}
		$this->response->body('');
	}

	public function action_inquirysupportmemo()
	{
		if ($this->request->post()){
			$post = $this->request->post();
			$orm = ORM::factory('inquiryresultsupport');
			$orm->result_id = $post['result_id'];
			$orm->no = $this->_model->get_inquiry_result_support_no($post['result_id']);
			$orm->support_type_cd = $post['support_type_cd'];
			$orm->memo = $post['memo'];
			$orm->upd_user = $this->_user_id;
			$orm->save();
			if ($post['support_type_cd'] == '05') {
				$maximum_model = new Model_Maximummodel();
				$maximum_model->cancel_inquiry($this->_bot_id, $post['result_id'], $this->_user_id, true);
			}
			// 2_step 支払い金額設定 (06現在利用しません)
			/*
			else if ($post['support_type_cd'] == '06') {
				$result = ORM::factory('inquiryresult', $post['result_id']);
				$result_data = json_decode($result->result_data, true);
				if ($result_data['payment']['2_step'] == 3) {
					$this->response->body('');
					return;
				}
				$result_data['payment']['2_step'] = 2;
				$result_data['payment']['amount'] = $post['amount'];
				$inquiry = ORM::factory('inquiry', $result->inquiry_id);
				$inquiry_data = json_decode($inquiry->inquiry_data, true);
				$limit = $inquiry_data['payment']['pay_in_hours'];
				$result_data['payment']['limit'] = date('Y-m-d H:i:s', strtotime("+ $limit hour"));
				$result->result_data = json_encode($result_data, JSON_UNESCAPED_UNICODE);
				$result->save();
				$maximum_model = new Model_Maximummodel();
				$maximum_model->send_inquiry_result_mail($post['result_id'], 'pay', '', 1);  // 1 to member
			}
			*/
			$this->response->body($this->_supportmemoview($post['result_id']));
		}
	}
	private function _supportmemoview($result_id) {
		$orm = ORM::factory('inquiryresult', $result_id);
		$inquiry_id = $orm->inquiry_id;
		$supports = $this->_model->get_inquiry_result_support_by_id($result_id);
		$result = '';
		$inquiry_status_list = $this->_model->get_bot_tpl_message($this->_bot_id, 'inquiry.support_status_' . $inquiry_id, 'ja', true);
		if (count($inquiry_status_list) == 0) {
			$inquiry_status_list = $this->_model->get_bot_tpl_message($this->_bot_id, 'inquiry.support_status', 'ja', true);
		}
		$inquiry = ORM::factory('inquiry', $inquiry_id);
		$inquiry_data = json_decode($inquiry->inquiry_data, true);
		if ($this->_model->decode_param($inquiry_data, ['payment', '2_step']) == 1 && false) {
			$inquiry_status_list_fixed = $this->_model->get_bot_tpl_message($this->_bot_id, 'inquiry.support_status_pay', 'ja', true);
		}
		else {
			$inquiry_status_list_fixed = $this->_model->get_bot_tpl_message($this->_bot_id, 'inquiry.support_status_fixed', 'ja', true);
		}
		$inquiry_status_list = $inquiry_status_list + $inquiry_status_list_fixed;
		$labelcolor = ['01'=>'#d84a38', '02'=>'#FFB848', '03'=>'#1BBC9B', '04'=>'#95A5A6', '05'=>'#CC00B2', '06'=>'#798a57'];
		if (isset($inquiry_data['is_mail_logged']) && $inquiry_data['is_mail_logged'] == '1') {
			$result_ids = [$result_id];
			$inquiry_result_mails = $this->_model->call_admin_api('inquiry', 'inquiryresultmails', 'post', ['result_ids'=>$result_ids]);
			$supports = array_merge($supports ?? [], $inquiry_result_mails[$result_id] ?? [], []);
			usort($supports, function($a, $b) {
				return strtotime($b['upd_time']) - strtotime($a['upd_time']);
			});
		}
		foreach($supports as $support) {
			if ($support['support_type_cd'] != '') {
				$support_label = '<span class="label" style="color:#FFF;background-color:' . $labelcolor[$support['support_type_cd']] . '">' . $inquiry_status_list[$support['support_type_cd']] . '</span>';
				$close = '';
			}
			else {
				$support_label = '';
				$close = '<div class="icon-cancel-small js-memo-delete" no="' . $support['no'] . '" style="margin: 0 0 0 auto;"></div>';
			}
			$memo = '';
			if(in_array($support['support_type_cd'], ['send_mail', 'received_mail'], true)){
				$comment = '<div style="width: calc(100% - 20px);font-size:11px;" class="js-memo-mail memo-mail" no="'. $support['id'] .'">'. date('Y/m/d H:i', strtotime($support['upd_time']))  . ' ' . $support['title'] . '<br/>' . $memo .'</div>';
				if($support['support_type_cd'] == 'send_mail'){
					$close = '<div class="icon-preview-mail-sent js-memo-mail" no="' . $support['id'] . '" style="margin: 0 0 0 auto;"></div>';
				} else if($support['support_type_cd'] == 'received_mail') {
					$close = '<div class="icon-preview-mail js-memo-mail" no="' . $support['id'] . '" style="margin: 0 0 0 auto;"></div>';
				}
			} else {
				if ($support['memo'] != null) $memo = nl2br($support['memo']);
				$comment = '<div style="width: calc(100% - 20px);font-size:11px;">'. substr($support['upd_time'], 0, 16) . ' ' . $support['name'] . $support_label . '<br/>' . nl2br($support['memo']) .'</div>';
			}
			$result = $result . '<div class="small-table-pannel" style="display: flex;padding: 8px;">' . $comment . $close . '</div>';
		}
		return $result;
	}
	public function action_deleteinquirysupportmemo()
	{
		if ($this->request->post()){
			$post = $this->request->post();
			DB::delete('t_inquiry_result_support')->where('result_id', '=', $post['result_id'])->where('no', '=', $post['no'])->execute();
			$this->response->body($this->_supportmemoview($post['result_id']));
		}
	}
	public function action_refetchinquirysupportmemo()
	{
		if ($this->request->post()){
			$post = $this->request->post();
			$this->response->body($this->_supportmemoview($post['result_id']));
		}
	}
	
	public function action_surveyentrydata() 
	{
		if ($this->request->post()){
			$post = $this->request->post();
			$orm = ORM::factory('surveyentry')->where('survey_id', '=', $post['survey_id'])->where('no', '=', $post['no'])->where('lang_cd', '=', 'ja')->find();
			if (isset($orm->entry_data) && $orm->entry_data != '') {
				$entry_data = json_decode($orm->entry_data, true);
				if (is_array($entry_data) && is_array($entry_data[0])) {
					$result = [];
					foreach($entry_data as $entry) {
						$result[] = $entry['title'];
					}
					$this->response->body(json_encode($result, JSON_UNESCAPED_UNICODE));
					return;
				}
				$this->response->body($orm->entry_data);
				return;
			}
			else {
				$this->response->body("[]");
			}
		}
	}
	
	public function action_surveyresult() {
		$post = $this->request->query();
		$survey_id = $post['survey_id'];
		$start_date = $post['start_date'];
		$end_date = $post['end_date'];
		$lang_cd = $post['lang_cd'];
		$csv_type_cd = $post['type'];
		$excel_view = false;
		if ($post['csv_format'] == "preview") {
			$excel_view = true;
		}
		$encode = $post['encode'];
		$survey = ORM::factory('survey', $survey_id);
		// survey result
		$survey_result = $this->_model->get_survey_result($this->_bot_id, $survey_id, $start_date, $end_date, '', '', $lang_cd, '');
		// survey entry result
		$survey_entry_result = $this->_model->get_survey_entry_result($this->_bot_id, $survey_id, $start_date, $end_date, $lang_cd);
		// survey entry
		$survey_entry = ORM::factory('surveyentry')->where('survey_id', '=', $survey_id)->where('lang_cd', '=', $lang_cd)->order_by('no')->find_all();

		if ($this->_user->role_cd == '73') {
			$survey_entry_type = [];
			foreach($survey_entry as $entry) {
				if ($entry->entry_type_cd == 'opt' || $entry->entry_type_cd == 'sel' || $entry->entry_type_cd == 'chk') {
					$survey_entry_type[$entry->no] = $entry->entry_type_cd;
				}
			}
			$user_access_limit_entry = $this->_model->get_user_access_limit($this->_bot_id, $this->_user_id, $survey_id, 'survey');
			if (isset($user_access_limit_entry[$lang_cd])) {
				$survey_result_tages = $user_access_limit_entry[$lang_cd];
			}
			else {
				$survey_result_tages = $user_access_limit_entry['ja'];
			}
			$this->_model->filter_survey_result($survey_result, $survey_entry_result, $survey_entry_type, $survey_result_tages);
		}
		// result detail
		$survey_result_entry_dict = [];
		foreach($survey_entry_result as $en) {
			if (array_key_exists($en['result_id'], $survey_result_entry_dict)) {
				$survey_result_entry_dict[$en['result_id']][$en['no']] = $en;
			}
			else {
				$survey_result_entry_dict[$en['result_id']] = [];
				$survey_result_entry_dict[$en['result_id']][$en['no']] = $en;
			}
		}
		
		$header = ['状況','ユーザID','回答日','回答時','回答時間','IPアドレス','端末情報(回答時)'];
		
		$survey_entry_data = [];

		$sections = ORM::factory('surveysection')->where('survey_id', '=', $survey_id)->where('lang_cd', '=', $lang_cd)->where('delete_flg', '=', 0)->order_by('sort_no')->find_all();
		$section = [];
		foreach($sections as $s) {
			$section = array_merge($section, explode(',', $s->entries));
		}
		
		$survey_entry_arr = [];
		foreach($survey_entry as $ie) {
			$survey_entry_arr[$ie->no] = $ie;
		}
		$entry_arr = [];
		if (count($section) > 0) {
			foreach($section as $s) {
				$entry_arr[] = $survey_entry_arr[$s];
			}
		}
		else {
			foreach($survey_entry as $ie) {
				$entry_arr[] = $ie;
			}
		}

		$pos = 0;
		foreach($entry_arr as $entry) {
			$pos++;
			$entry_data = [];
			if ($entry->entry_data != '') {
				$entry_data = json_decode($entry->entry_data, true);
				$survey_entry_data[$entry->no] = $entry_data;
			}
			if (count($entry_data) > 0) {
				if ($entry->entry_type_cd == 'chk' && $csv_type_cd == 2) {
					foreach($entry_data as $e) {
						if (is_array($e)) {
							$t = $e['title'];
						}
						else {
							$t = $e;
						}
						$header[] = $pos . '.' . strip_tags($entry->title) . '(' . $t . ')';
					}
				}
				else {
					$ts = [];
					foreach($entry_data as $e) {
						if (is_array($e)) {
							$ts[] = $e['title'];
						}
						else {
							$ts[] = $e;
						}
					}
					$header[] = $pos . '.' . strip_tags($entry->title) . PHP_EOL . PHP_EOL . implode(PHP_EOL, $ts);
				}
			}
			else {
				if ($entry->entry_type_cd == 'spl') {
					$input_rules = json_decode($entry->input_rules, true);
					if ($input_rules['type'] == 'address') {
						$header[] = $pos . '.' . '郵便番号';
						$header[] = '都道府県';
						$header[] = '市区町村';
					}
					else {
						$header[] = $pos . '.' . strip_tags($entry->title);
					}
				}
				else if ($entry->entry_type_cd == 'mtx') {
					if ($csv_type_cd == 1) {
						$header[] = $pos . '.' . strip_tags($entry->title);
					}
					else {
						$input_rules = json_decode($entry->input_rules, true);
						foreach($input_rules['mtx_y'] as $c) {
							$header[] = strip_tags($entry->title) . '[' . $c . ']';
						}
					}
				}
				else {
					$header[] = $pos . '.' . strip_tags($entry->title);
				}
			}
		}
		
		if ($csv_type_cd == 1) {
			$csv_type_name = '一括';
		}
		else {
			$csv_type_name = '分割';
		}
		$create_time = date('Y-m-d');
		header("Content-Type: application/octet-stream");
		header("Content-Disposition: attachment; filename=複数回答" . $csv_type_name . "_$survey_id" . "_$create_time.csv");
		$f = fopen('php://output', 'w');
		ob_end_clean();
		if ($encode == "sjis") {
			mb_convert_variables('SJIS-win', 'UTF-8', $header);
		}
		else {
			fwrite($f, "\xEF\xBB\xBF");
		}
		fputcsv($f, $header, ",");
		
		foreach($survey_result as $r) {
			if ($r['link_id'] == null) {
				$uid = $r['member_id'];
			}
			else {
				$uid = $r['link_id'];
			}
			if (!array_key_exists($r['id'], $survey_result_entry_dict)) continue;
			$diff = round((strtotime($r['end_time']) - strtotime($r['start_time'])) / 60, 1);
			$data = ['回答完了', $uid, substr($r['start_time'], 0, 10), substr($r['start_time'], 11), $diff, $r['ip_address'], $r['ua_info']];
			$multi_data_list = [];
			$is_masking = false;
			$survey_user_in_charge = '';
			if ($survey->user_in_charge != NULL) {
				$survey_user_in_charge = $survey->user_in_charge;
			}
			$user_in_charge_array = explode(',', $survey_user_in_charge);
			$user_is_in_charge = in_array($this->_user->user_id, $user_in_charge_array);
			if(($this->_user->privacy_self_show_flg == 0 && $user_is_in_charge) || 
			($this->_user->privacy_show_flg == 0 && !$user_is_in_charge)){
				$is_masking = true;
			}
			foreach($entry_arr as $entry) {
				// 回答あり
				if (array_key_exists($entry->no, $survey_result_entry_dict[$r['id']])) {
					// 複数選択場合、分割判断
					if ($entry->entry_type_cd == 'chk') {
						if ($csv_type_cd == 2) {
							$rows = explode(',', $survey_result_entry_dict[$r['id']][$entry->no]['entry_result']);
							// 選択肢の数をカウント
							$totalChoices = count($survey_entry_data[$entry->no]); 
							$currentChoice = 0;							
							foreach($survey_entry_data[$entry->no] as $k=>$v) {
								$currentChoice++;
								if (in_array($k+1, $rows)) {
									$dataValue = 1;
									// その他を選択した場合は回答内容も出力
									if ($currentChoice == $totalChoices) {
										// カンマ区切りで格納された回答データの最後の要素を取得
										$allChoices = explode(',', $survey_result_entry_dict[$r['id']][$entry->no]['entry_data']);
										$lastChoice = end($allChoices);
										// "...({文字列})" の形式を確認
										if (preg_match('/\.\.\.\(([^)]+)\)$/', $lastChoice, $matches)) {
											$dataValue = "1...(" . $matches[1] . ")";
										}
										// "({文字列})" の形式を確認
										elseif (preg_match('/\(([^)]+)\)$/', $lastChoice, $matches)) {
											$dataValue = "1(" . $matches[1] . ")";
										}
									}
									$data[] = $dataValue;
								}
								else {
									$data[] = 0;
								}
							}
						}
						else if ($csv_type_cd == 3) {
							$rows = explode(',', $survey_result_entry_dict[$r['id']][$entry->no]['entry_result']);
							$checkbox_data = [];
							if (count($rows) > 1) {
								foreach($rows as $row) {
									$checkbox_data[] = $survey_entry_data[$entry->no][$row - 1];
								}
								$multi_data_list[$entry->no] = $checkbox_data;
								$data[] = '';
							}
							else {
								$data[] = $survey_result_entry_dict[$r['id']][$entry->no]['entry_data'];
							}
						}
						else {
							$data[] = $survey_result_entry_dict[$r['id']][$entry->no]['entry_data'];
						}
					}
					else {
						$temp = $survey_result_entry_dict[$r['id']][$entry->no]['entry_data'];
						if ($is_masking) {
							$input_rules = json_decode($entry->input_rules, true);
							if (is_array($input_rules) && array_key_exists('type', $input_rules)) {
								if (
									$input_rules['type'] == 'tel' || $input_rules['type'] == 'mail' || $input_rules['type'] == 'postcode' || $input_rules['type'] == 'name'	||	
									($input_rules['type'] == 'text' && $input_rules['privacy_masking'] == '1')
								) {	
									$temp = $this->_model->mask_string($temp, 5, '*');
								}
							}
						}

						if ($entry->entry_type_cd == 'mtx') {
							if ($csv_type_cd == 1) {
								$temp = str_replace('"', '', str_replace('}', '', str_replace('{', '', $temp)));
								$data[] = $temp;
							}
							else {
								$matrix = json_decode($survey_result_entry_dict[$r['id']][$entry->no]['entry_result'], true);
								$input_rules = json_decode($entry->input_rules, true);
								$yy = 1;
								foreach($input_rules['mtx_y'] as $y) {
									if (isset($matrix[$yy])) {
										$xx = 1;
										$text = [];
										foreach($input_rules['mtx_x'] as $x) {
											if (in_array($xx, $matrix[$yy])) {
												$text[] = $x;
											}
											$xx++;
										}
										$data[] = implode(',', $text);
									}
									else {
										$data[] = '';
									}
									$yy++;
								}
							}
						}
						else if ($entry->entry_type_cd == 'spl') {
							$input_rules = json_decode($entry->input_rules, true);
							if ($input_rules['type'] == 'address') {
								$address = json_decode($survey_result_entry_dict[$r['id']][$entry->no]['entry_result'], true);
								if ($excel_view) {
									if ($is_masking) {
											$data[] = $this->_model->format_csv_field_view($this->_model->mask_string($address['postcode'], 5, '*'));
											$data[] = $this->_model->format_csv_field_view($this->_model->mask_string($address['address1'], 5, '*'));
											$data[] = $this->_model->format_csv_field_view($this->_model->mask_string($address['address2'], 5, '*'));
									} else {
										$data[] = $this->_model->format_csv_field_view($address['postcode']);
										$data[] = $this->_model->format_csv_field_view($address['address1']);
										$data[] = $this->_model->format_csv_field_view($address['address2']);
									}
								}
								else {
									if ($is_masking) {
										$data[] = $this->_model->mask_string($address['postcode'], 5, '*');
										$data[] = $this->_model->mask_string($address['address1'], 5, '*');
										$data[] = $this->_model->mask_string($address['address2'], 5, '*');
									} else {
										$data[] = $address['postcode'];
										$data[] = $address['address1'];
										$data[] = $address['address2'];
									}
								}
							}
							else {
								if ($excel_view) {
									$data[] = $this->_model->format_csv_field_view($temp);
								}
								else {
									$data[] = $temp;
								}
							}
						}
						else {
							if ($excel_view) {
								$data[] = $this->_model->format_csv_field_view($temp);
							}
							else {
								$data[] = $temp;
							}
						}
					}
				}
				// 回答なし場合
				else {
					// 複数選択場合、分割判断
					if ($entry->entry_type_cd == 'chk' && $csv_type_cd == 2) {
						foreach($survey_entry_data[$entry->no] as $k=>$v) {
							$data[] = 0;
						}
					}
					else {
						$data[] = '';
					}
				}
			}
			if (count($multi_data_list) > 0) {
				foreach($multi_data_list as $k=>$v) {
					foreach($v as $m) {
						$data[7 + intval($k) - 1] = $m;
						if ($encode == "sjis") {
							mb_convert_variables('SJIS-win', 'UTF-8', $data);
						}
						fputcsv($f, $data, ",");
					}
				}
			}
			else {
				if ($encode == "sjis") {
					mb_convert_variables('SJIS-win', 'UTF-8', $data);
				}
				fputcsv($f, $data, ",");
			}
		}
		fclose($f);
	}

	public function action_surveyreport() {
		$post = $this->request->query();
		$survey_id = $post['survey_id'];
		$start_date = $post['start_date'];
		$end_date = $post['end_date'];
		$type = $post['type'];
		if ($type == 'monthly') {
			$start_date = date('Y-m', strtotime($start_date)) . '-01';
			$days = date('t', strtotime($end_date));
			$end_date = date('Y-m', strtotime($end_date)) . '-' . $days;
		}

		$survey = ORM::factory('survey', $survey_id);
		$survey_model = new Model_Surveymodel();
		$reports = $survey_model->get_survey_report($survey_id, $start_date, $end_date, $type);
		$filename = $type == 'monthly' ? __('admin.surveyreport.label.csvexport.monthly_filename') : __('admin.surveyreport.label.csvexport.daily_filename');
 		$header_time = $type == 'monthly' ? __('admin.surveyreport.label.csvexport.header_time_monthly') : __('admin.surveyreport.label.csvexport.header_time_daily');
		$header = [$header_time, __('admin.surveyreport.label.csvexport.header_count')];
		$create_time = date('Y-m-d');
		header("Content-Type: application/octet-stream");
		header("Content-Disposition: attachment; filename=" . $filename . "_" . $survey->survey_name . "_$survey_id" . "_$create_time.csv");
		$f = fopen('php://output', 'w');
		ob_end_clean();
		fwrite($f, "\xEF\xBB\xBF");
		fputcsv($f, $header, ",");
		foreach ($reports as $report) {
			$data = [$report['time'], $report['total']];
			fputcsv($f, $data, ",");
		}
		fclose($f);
	}
	
	public function action_surveyreport73() {
		$post = $this->request->query();
		$survey_id = $post['survey_id'];
		$start_date = $post['start_date'];
		$end_date = $post['end_date'];
		$type = $post['type'];
		if ($type == 'monthly') {
			$start_date = date('Y-m', strtotime($start_date)) . '-01';
			$days = date('t', strtotime($end_date));
			$end_date = date('Y-m', strtotime($end_date)) . '-' . $days;
		}

		$survey = ORM::factory('survey', $survey_id);
		$survey_result = $this->_model->get_survey_result($this->_bot_id, $survey_id, $start_date, $end_date, '', '', '', '', '');
		if (count($survey_result) == 0) {
			$start = 0;
			$end = 0;
		}
		else {
			$start = $survey_result[0]['id'];
			$end = $survey_result[count($survey_result) - 1]['id'];
		}
		$survey_entry_result = $this->_model->get_survey_entry_result_page($this->_bot_id, $survey_id, $start, $end, '');
		// survey entry
		$survey_entry = ORM::factory('surveyentry')->where('survey_id', '=', $survey_id)->where('lang_cd', '=', 'ja')->order_by('no')->find_all();

		$survey_entry_type = [];
		foreach($survey_entry as $entry) {
			if ($entry->entry_type_cd == 'opt' || $entry->entry_type_cd == 'sel' || $entry->entry_type_cd == 'chk') {
				$survey_entry_type[$entry->no] = $entry->entry_type_cd;
			}
		}

		// filter
		$survey_result_tages = [];
		if ($this->_user->role_cd == '73') {
			$user_access_limit_entry = $this->_model->get_user_access_limit($this->_bot_id, $this->_user_id, $survey_id, 'survey');
			$survey_result_tages = $user_access_limit_entry['ja'];
		}
		$this->_model->filter_survey_result($survey_result, $survey_entry_result, $survey_entry_type, $survey_result_tages);

		$results = [];
		foreach($survey_result as $r) {
			if ($type == 'monthly') {
				$key = substr($r['end_time'], 0, 7);
			}
			else {
				$key = substr($r['end_time'], 0, 10);
			}
			if (!isset($results[$key])) $results[$key] = 0;
			$results[$key] = $results[$key] + 1;
		}
		ksort($results);
		$total_result = [];
		foreach($results as $k=>$v) {
			$total_result[] = ['time'=>$k, 'total'=>$v];
		}

		$filename = $type == 'monthly' ? __('admin.surveyreport.label.csvexport.monthly_filename') : __('admin.surveyreport.label.csvexport.daily_filename');
 		$header_time = $type == 'monthly' ? __('admin.surveyreport.label.csvexport.header_time_monthly') : __('admin.surveyreport.label.csvexport.header_time_daily');
		$header = [$header_time, __('admin.surveyreport.label.csvexport.header_count')];
		$create_time = date('Y-m-d');
		header("Content-Type: application/octet-stream");
		header("Content-Disposition: attachment; filename=" . $filename . "_" . $survey->survey_name . "_$survey_id" . "_$create_time.csv");
		$f = fopen('php://output', 'w');
		ob_end_clean();
		fwrite($f, "\xEF\xBB\xBF");
		fputcsv($f, $header, ",");
		foreach ($total_result as $report) {
			$data = [$report['time'], $report['total']];
			fputcsv($f, $data, ",");
		}
		fclose($f);
	}

	public function action_surveyresultdownload() {
		$post = $this->request->query();
		$results = $this->_model->get_survey_entry_result_by_no($post['survey_id'], $post['start_date'], $post['end_date'], $post['lang_cd'], $post['no']);
		$zipname = APPPATH . '../../files/' . time() . '.zip';
		$zip = new ZipArchive;
		$zip->open($zipname, ZipArchive::CREATE);
		foreach ($results as $result) {
			$files = json_decode($result['entry_result'], true);
			foreach($files as $f) {
				$fileContent = file_get_contents($f['url']);
				$zip->addFromString(basename($f['url']), $fileContent);
			}
		}
		$zip->close();
		
		$fp = fopen($zipname,'r');
		$filesize = filesize($zipname);
		//header('Content-Type: application/zip');
		header("Content-Type: application/octet-stream");
		header("Accept-Ranges: bytes");
		header("Accept-Length: $filesize");
		header('Content-Disposition: attachment; filename=files_' . time() . '.zip');
		
		ob_clean();
		flush();
		$buffer=1024;
		$count=0;
		while(!feof($fp)&&($filesize-$count>0)){
			$data=fread($fp,$buffer);
			$count+=$data;
			echo $data;
		}
		fclose($fp);
		unlink($zipname);
	}
	
	public function action_inquiryresult() {
		$post = $this->request->post();
		$inquiry_id = $post['inquiry_id'];
		$start_date = $post['start_date'];
		$end_date = $post['end_date'];
		$is_blocked = intval($post['is_blocked']);
		$lang_cd = $post['lang_cd'];
		$csv_type_cd = $post['type'];
		$filters = $post['filters'];
		$encode = $post['encode'];
		if ($lang_cd == '') {
			$base_lang_cd = 'ja';
		}
		else {
			$base_lang_cd = $lang_cd;
		}
		$inquiry = ORM::factory('inquiry', $inquiry_id);
		$bot_id = $inquiry->bot_id;

		// inquiry result
		$inquiry_result = $this->_model->get_inquiry_result($this->_bot_id, $inquiry_id, $start_date, $end_date, $is_blocked, '', '', $lang_cd, '', '');
		// inquiry entry result
		$inquiry_entry_result = $this->_model->get_inquiry_entry_result($this->_bot_id, $inquiry_id, $start_date, $end_date, $is_blocked, $lang_cd);

		if ($this->_user->role_cd == '74') {
			$inquiry_entry = ORM::factory('inquiryentry')->where('inquiry_id', '=', $inquiry_id)->where('lang_cd', '=', $lang_cd)->order_by('no')->find_all();
			$entry_type = [];
			foreach($inquiry_entry as $entry) {
				if ($entry->entry_type_cd == 'opt' || $entry->entry_type_cd == 'sel' || $entry->entry_type_cd == 'chk') {
					$entry_type[$entry->no] = $entry->entry_type_cd;
				}
			}
			$user_access_limit_entry = $this->_model->get_user_access_limit($this->_bot_id, $this->_user_id, $inquiry_id, 'inquiry');
			if (isset($user_access_limit_entry[$lang_cd])) {
				$result_tages = $user_access_limit_entry[$lang_cd];
			}
			else {
				$result_tages = $user_access_limit_entry['ja'];
			}
			$this->_model->filter_inquiry_result($inquiry_result, $inquiry_entry_result, $entry_type, $result_tages);
		}

		// inquiry result support
		$inquiry_result_support_arr = $this->_model->get_inquiry_result_support($this->_bot_id, $inquiry_id, $start_date, $end_date, $lang_cd);
		$inquiry_result_support = [];
		foreach($inquiry_result_support_arr as $r) {
			if (array_key_exists($r['result_id'], $inquiry_result_support)) {
				$inquiry_result_support[$r['result_id']][] = $r;
			}
			else {
				$inquiry_result_support[$r['result_id']] = [];
				$inquiry_result_support[$r['result_id']][] = $r;
			}
		}
		// result detail
		$result_details = [];
		foreach($inquiry_entry_result as $en) {
			if (array_key_exists($en['result_id'], $result_details)) {
				$result_details[$en['result_id']][$en['no']] = $en;
			}
			else {
				$result_details[$en['result_id']] = [];
				$result_details[$en['result_id']][$en['no']] = $en;
			}
		}
		
		$header = ['状況','受付ID','ユーザID','回答日','回答時','回答時間','IPアドレス','端末情報(回答時)','対応状態','対応履歴'];
		
		$inquiry_entry = ORM::factory('inquiryentry')->where('inquiry_id', '=', $inquiry_id)->where('lang_cd', '=', $base_lang_cd)->order_by('no')->find_all();
		$inquiry_entry_data = [];

		$inquiry_result_entry_dict = [];
		if ($filters != '') {
			$inquiryFilters = json_decode($filters, JSON_UNESCAPED_UNICODE);
			$filter_entry = $this->_model->get_inquiry_filter_entries_by_lang($inquiry_id, $base_lang_cd);
			foreach ($result_details as $result_id => $entry_results) {
				if ($this->_model->filter_results($inquiryFilters, $entry_results, $filter_entry)) {
					$inquiry_result_entry_dict[$result_id] = $entry_results;
				}
			}
		} else {
			$inquiry_result_entry_dict = $result_details;
		}

		$sections = ORM::factory('inquirysection')->where('inquiry_id', '=', $inquiry_id)->where('delete_flg', '=', 0)->where('lang_cd', '=', $base_lang_cd)->order_by('sort_no')->find_all();
		$section = [];
		foreach($sections as $s) {
			$section = array_merge($section, explode(',', $s->entries));
		}
		
		$inquiry_entry_arr = [];
		foreach($inquiry_entry as $ie) {
			$inquiry_entry_arr[$ie->no] = $ie;
		}
		$entry_arr = [];
		if (count($section) > 0) {
			foreach($section as $s) {
				if ($inquiry_entry_arr[$s]->entry_type_cd == 'frs' && $inquiry_entry_arr[$s]->required == 0) continue;
				$entry_arr[] = $inquiry_entry_arr[$s];
			}
		}
		else {
			foreach($inquiry_entry as $ie) {
				if ($ie->entry_type_cd == 'frs' && $ie->required == 0) continue;
				$entry_arr[] = $ie;
			}
		}
		
		$entry_input_rules_type = [];
		$pos = 0;
		$add = 0;
		$not_show_frs_no_arr = [];

		foreach($entry_arr as $entry) {
			$pos++;
			$entry_data = [];
			if ($entry->entry_data != '' && $entry->entry_data != '[]') {
				$entry_data = json_decode($entry->entry_data, true);
				// maximum情報をentry_dataに反映
				$is_entry_data = true;
				foreach($entry_data as &$d) {
					if (!is_array($d)) {
						if ($entry->entry_type_cd == 'chk') {
							$d = ['title'=>strip_tags($entry->title) . '('. $d . ')'];
						}
						else {
							$is_entry_data = false;
						}
					}
					if (isset($d['maximum'])) {
						$d['maximum_id'] = $d['maximum'];
						unset($d['maximum']);
						$orm = ORM::factory('botmaximum')->where('id', '=', $d['maximum_id'])->where('bot_id', '=', $bot_id)->find();
						$extra_data = json_decode($orm->extra_data, true);
						if ($extra_data == null) continue;
						$d['span'] = $orm->span;
						if (isset($extra_data['category']) && count($extra_data['category']) > 0) {
							$d['category'] = $extra_data['category'];
						}
					}
				}
				if ($is_entry_data) $inquiry_entry_data[$entry->no] = $entry_data;
			}
			// entry_data構造の分割対応
			if (isset($inquiry_entry_data[$entry->no])) {
				$has_datetime = false;
				foreach($inquiry_entry_data[$entry->no] as $e) {
					if (isset($e['maximum_id']) && $e['span'] != 'all') {
						$has_datetime = true;
					}
				}
				$text_title = strip_tags($entry->title);
				if ($has_datetime) $header[] = $pos . '.' . $text_title . ' 日時';
				if ($csv_type_cd == 2) {
					foreach($inquiry_entry_data[$entry->no] as $e) {
						if (isset($e['category'])) {
							foreach($e['category'] as $c) {
								$header[] = $pos . '.' . $text_title . ' ' . $c['title'][$base_lang_cd] . ' 数量';
								$header[] = $pos . '.' . $text_title . ' ' . $c['title'][$base_lang_cd] . ' 料金';
							}
						}
						else if (isset($e['price'])) {
							$header[] = $pos . '.' . $text_title . ' ' . $e['title'] . ' 数量';
							$header[] = $pos . '.' . $text_title . ' ' . $e['title'] . ' 料金';
						}
						else {
							$header[] = $pos . '.' . $text_title . ' 数量';
						}
					}
				}
				else {
					$header[] = $pos . '.' . strip_tags($entry->title);
				}
			}
			else {
				$input_rules = json_decode($entry->input_rules, true);
				if (is_array($input_rules) && array_key_exists('type', $input_rules)) {
					if ($input_rules['type'] == 'name_separate') {
						$entry_input_rules_type[$entry->no] = 'name_separate';
						$header[] = $pos . '.' . strip_tags($entry->title) . '-' . $input_rules['first_name']['label'];
						$header[] = $pos . '.' . strip_tags($entry->title) . '-' . $input_rules['last_name']['label'];
						$header[] = $pos . '.' . strip_tags($entry->title) . '-' . $input_rules['first_name_kana']['label'];
						$header[] = $pos . '.' . strip_tags($entry->title) . '-' . $input_rules['last_name_kana']['label'];
					}
					else if ($input_rules['type'] == 'name_full') {
						$entry_input_rules_type[$entry->no] = 'name_full';
						$header[] = $pos . '.' . strip_tags($entry->title) . '-' . $input_rules['full_name']['label'];
						$header[] = $pos . '.' . strip_tags($entry->title) . '-' . $input_rules['furigana']['label'];
					}
					else if ($input_rules['type'] == 'address') {
						$entry_input_rules_type[$entry->no] = 'address';
						$header[] = $pos . '.' . strip_tags($entry->title) . '-' . $input_rules['postcode']['label'];
						$header[] = $pos . '.' . strip_tags($entry->title) . '-' . $input_rules['address1']['label'];
						$header[] = $pos . '.' . strip_tags($entry->title) . '-' . $input_rules['address2']['label'];
						$header[] = $pos . '.' . strip_tags($entry->title) . '-' . $input_rules['address3']['label'];
						$header[] = $pos . '.' . strip_tags($entry->title) . '-' . $input_rules['address4']['label'];
					}
					else {
						$header[] = $pos . '.' . strip_tags($entry->title);
					}
				}
				else {
					if ($entry->entry_type_cd == 'frs') {
						if ($entry->title == preg_replace('/\/\?.*\?\//', '', $entry->title)) {
							$not_show_frs_no_arr[] = $entry->no;
						}
						else {
							$header[] =  $pos . '.' . str_replace('¥', '', preg_replace('/\/\?.*\?\//', '', strip_tags($entry->title)));
						}
					}
					else {
						$header[] = $pos . '.' . strip_tags($entry->title);
					}
				}
			}
		}
		
		if (count($inquiry_result) > 0) {
			$result_data = json_decode($inquiry_result[0]['result_data'], true);
			if (is_array($result_data) && (isset($result_data['amount']) || (isset($result_data['payment']) && isset($result_data['payment']['amount'])))) $header[] = '合計金額';
		}

		if ($csv_type_cd == 1) {
			$csv_type_name = '一括';
		}
		else {
			$csv_type_name = '分割';
		}
		$create_time = date('Y-m-d');
		header("Content-Type: application/octet-stream");
		header("Content-Disposition: attachment; filename=複数回答" . $csv_type_name . "_$inquiry_id" . "_$create_time.csv");
		$f = fopen('php://output', 'w');
		ob_end_clean();
		if ($encode == "sjis") {
			mb_convert_variables('SJIS-win', 'UTF-8', $header);
		}
		else {
			fwrite($f, "\xEF\xBB\xBF");
		}
		fputcsv($f, $header, ",");

		$status_dict = $this->_model->get_code('25', 'ja');

		$service_status = $this->_model->get_code('45');

		foreach($inquiry_result as $r) {
			// if ($r['status_cd'] == '00') continue;
			if ($r['status_cd'] == '03' && $r['upd_user'] === '0') continue;
			if ($r['link_id'] == null) {
				$uid = $r['member_id'];
			}
			else {
				$uid = $r['link_id'];
			}
			if (!array_key_exists($r['id'], $inquiry_result_entry_dict)) continue;
			$diff = round((strtotime($r['end_time']) - strtotime($r['start_time'])) / 60, 1);
			if ($r['result_cd'] == null) {
				$result_cd = $r['id'];
			}
			else {
				$result_cd = $r['result_cd'];
			}
			$data = [$status_dict[$r['status_cd']], $result_cd, $uid, substr($r['start_time'], 0, 10), substr($r['start_time'], 11), $diff, $r['ip_address'], $r['ua_info']];
			if (array_key_exists($r['id'], $inquiry_result_support)) {
				$support_line = [];
				$last_support_type_name = '';
				foreach($inquiry_result_support[$r['id']] as $rs) {
					if (array_key_exists($rs['support_type_cd'], $service_status)) {
						$support_type_name = $service_status[$rs['support_type_cd']];
					}
					else {
						$support_type_name = '';
					}
					$support_line[] = $rs['upd_time'] . ' ' . $rs['name'] . ' ' . $support_type_name . ' ' . $rs['memo'];
					if ($support_type_name != '') $last_support_type_name = $support_type_name;
				}
				$data[] = $last_support_type_name;
				$data[] = implode(PHP_EOL, $support_line);
			}
			else {
				$data[] = '';
				$data[] = '';
			}
			$multi_data_list = [];
			$ptr = $add + 10;
			foreach($entry_arr as $entry) {
				if (in_array($entry->no, $not_show_frs_no_arr)) continue;
				$ptr++;
				if (array_key_exists($entry->no, $inquiry_result_entry_dict[$r['id']])) {
					// entry_data構造の回答結果分割対応
					if (isset($inquiry_entry_data[$entry->no])) {
						if ($inquiry_result_entry_dict[$r['id']][$entry->no]['entry_extra_info'] == null) {
							$entry_extra_info = [];
						}
						else {
							$entry_extra_info = json_decode($inquiry_result_entry_dict[$r['id']][$entry->no]['entry_extra_info'], true);
						}
						// new inquiry
						$entry_result_info = json_decode($inquiry_result_entry_dict[$r['id']][$entry->no]['entry_result'], true);
						if ($entry_result_info == null) $entry_result_info = [];

						$has_datetime = false;
						foreach($inquiry_entry_data[$entry->no] as $e) {
							if (isset($e['maximum_id']) && $e['span'] != 'all') {
								$has_datetime = true;
							}
						}

						$daytime = '';
						if (isset($entry_extra_info['day'])) {
							$daytime = $daytime . $entry_extra_info['day'];
						}
						else if (isset($entry_result_info['date'])) {
							$daytime = $daytime . $entry_result_info['date'];
						}
						if (isset($entry_extra_info['time'])) {
							$daytime = $daytime . ' ' . $entry_extra_info['time'];
						}
						else if (isset($entry_result_info['time']) && $entry_result_info['time'] != '') {
							$daytime = $daytime . ' ' . $entry_result_info['time'];
						}

						if ($has_datetime) $data[] = $daytime;
						if ($csv_type_cd == 2) {
							$category_result = [];
							if (isset($entry_extra_info['category'])) {
								foreach($entry_extra_info['category'] as $c) {
									$category_result[$entry_extra_info['maximum_id'] . '-' . $c['price_id']] = ['num'=>$c['num'], 'price'=>$c['price']];
								}
							}
							else if (isset($entry_extra_info['select'])) {
								foreach($entry_extra_info['select'] as $c) {
									if (isset($c['maximum_id'])) {
										$category_result[$c['maximum_id']] = ['num'=>$c['num'], 'price'=>$c['price']];
									}
									else {
										$category_result[''] = ['num'=>$c['num'], 'price'=>$c['price']];
									}
								}
							}
							else if (isset($entry_result_info['items'])) {
								foreach($entry_result_info['items'] as $c) {
									if (isset($c['maximum_id'])) {
										$category_result[$c['maximum_id']] = ['num'=>$c['num'], 'price'=>$c['price']];
									}
									else {
										if (isset($entry_result_info['maximum_id'])) {
											if (isset($c['discount_price'])) $c['price'] = $c['price'] - $c['discount_price'];
											$category_result[$entry_result_info['maximum_id'] . '-' . $c['value']] = ['num'=>$c['num'], 'price'=>$c['price']];
										}
										else {
											$category_result[$c['value']] = ['value'=>$c['value'], 'num'=>$c['num'], 'price'=>$c['price']];
										}
									}
								}
							}
							$no = 1;
							foreach($inquiry_entry_data[$entry->no] as $e) {
								if (isset($e['category'])) {
									foreach($e['category'] as $c) {
										if (isset($category_result[$e['maximum_id'] . '-' . $c['id']])) {
											$data[] = $category_result[$e['maximum_id'] . '-' . $c['id']]['num'];
											$data[] = $category_result[$e['maximum_id'] . '-' . $c['id']]['price'] * $category_result[$e['maximum_id'] . '-' . $c['id']]['num'];
										}
										else if (isset($category_result[$e['maximum_id']])) {
											$data[] = $category_result[$e['maximum_id']]['num'];
											$data[] = $category_result[$e['maximum_id']]['price'];
										}
										else {
											$data[] = '';
											$data[] = '';
										}
									}
								}
								else if (isset($e['price'])) {
									if (isset($e['maximum_id']) && isset($category_result[$e['maximum_id'] . '-' . $no])) {
										$data[] = $category_result[$e['maximum_id']]['num'];
										$data[] = $category_result[$e['maximum_id']]['price'];
									}
									else if (isset($e['maximum_id']) && isset($category_result[$e['maximum_id']])) {
										$data[] = $category_result[$e['maximum_id']]['num'];
										$data[] = $category_result[$e['maximum_id']]['price'];
									}
									else if (isset($category_result[$no])) {
										$data[] = $category_result[$no]['num'];
										$data[] = $category_result[$no]['price'];
									}
									else {
										$data[] = '';
										$data[] = '';
									}
								}
								else if (is_array($entry_result_info) && count($entry_result_info) > 0) {
									$data[] = $entry_result_info['num'];
									if (!isset($inquiry_entry_data[$entry->no]['category']) && isset($inquiry_entry_data[$entry->no]['price'])) {
										$data[] = $entry_result_info['price'];
									}
								}
								else {
									if (in_array($no, explode(',', $inquiry_result_entry_dict[$r['id']][$entry->no]['entry_result']))) {
										$data[] = '1';
									}
									else {
										$data[] = '0';
									}
								}
								$no++;
							}
						}
						else {
							$data[] = $this->_model->format_csv_field(strip_tags($inquiry_result_entry_dict[$r['id']][$entry->no]['entry_data']));
						}
					}
					else {
						if (array_key_exists($entry->no, $entry_input_rules_type)) {
							$entry_result = json_decode($inquiry_result_entry_dict[$r['id']][$entry->no]['entry_result'], true);
							if ($entry_input_rules_type[$entry->no] == 'name_separate') {
								$data[] = $entry_result['last_name'];
								$data[] = $entry_result['first_name'];
								$data[] = isset($entry_result['last_name_kana'])?$entry_result['last_name_kana']:'';
								$data[] = isset($entry_result['first_name_kana'])?$entry_result['first_name_kana']:'';
							}
							else if ($entry_input_rules_type[$entry->no] == 'name_full') {
								$data[] = $entry_result['full_name'];
								$data[] = $entry_result['furigana'];
							}
							else if ($entry_input_rules_type[$entry->no] == 'address') {
								$data[] = $entry_result['postcode'];
								$data[] = $entry_result['address1'];
								$data[] = $entry_result['address2'];
								$data[] = $this->_model->format_csv_field($entry_result['address3']);
								$data[] = $this->_model->format_csv_field($entry_result['address4']);
							}
						}
						else {
							$temp = $inquiry_result_entry_dict[$r['id']][$entry->no]['entry_data'];
							if ($entry->entry_type_cd == 'frs') {
								$temp = str_replace('.', ',', $temp);
							}
							else if ($entry->entry_type_cd == 'txt') {
								$input_rules = json_decode($entry->input_rules, true);
								if ($input_rules['type'] == 'date' || $input_rules['type'] == 'date-time') {
									$df = json_decode($inquiry_result_entry_dict[$r['id']][$entry->no]['entry_result'], true);
									if (is_array($df)) {
										$temp = $df['date'];
										if (isset($df['time'])) $temp = $temp . ' ' . $df['time'];
									}
								}
							}
							if ($inquiry->user_in_charge != NULL) {
								$user_in_charge_array = explode(',', $inquiry->user_in_charge);
								$user_is_in_charge = in_array($this->_user->user_id, $user_in_charge_array);
								if (($this->_user->privacy_self_show_flg == 0 && $user_is_in_charge) || 
										($this->_user->privacy_show_flg == 0 && !$user_is_in_charge)) {
											$input_rules = json_decode($entry->input_rules, true);
											if (is_array($input_rules) && array_key_exists('type', $input_rules)) {
												if ($input_rules['type'] == 'tel' || $input_rules['type'] == 'mail' || $input_rules['type'] == 'postcode' || $input_rules['type'] == 'name' || $input_rules['type'] == 'name_full' || $input_rules['type'] == 'name_separate' || $input_rules['type'] == 'address') {
													$temp = $this->_model->mask_string($temp, 5, '*');
												}
											}
										}
							}
							$data[] = $this->_model->format_csv_field($temp);
						}
					}
				}
				else {
					// 回答なし場合
					if (isset($inquiry_entry_data[$entry->no])) {
						$has_datetime = false;
						foreach($inquiry_entry_data[$entry->no] as $e) {
							if (isset($e['maximum_id']) && $e['span'] != 'all') {
								$has_datetime = true;
							}
						}
						if ($has_datetime) $data[] = '';
						if ($csv_type_cd == 2) {
							foreach($inquiry_entry_data[$entry->no] as $e) {
								if (isset($e['category'])) {
									foreach($e['category'] as $c) {
										$data[] = '';
										$data[] = '';
									}
								}
								else if (isset($e['price'])) {
									$data[] = '';
									$data[] = '';								
								}
								else {
									$data[] = '';
								}
							}
						}
						else {
							$data[] = '';
						}
					}
					else {
						if (array_key_exists($entry->no, $entry_input_rules_type)) {
							if ($entry_input_rules_type[$entry->no] == 'name_separate') {
								$data[] = '';
								$data[] = '';
								$data[] = '';
								$data[] = '';
							}
							else if ($entry_input_rules_type[$entry->no] == 'name_full') {
								$data[] = '';
								$data[] = '';
							}
							else if ($entry_input_rules_type[$entry->no] == 'address') {
								$data[] = '';
								$data[] = '';
								$data[] = '';
								$data[] = '';
								$data[] = '';
							}
						}
						else {
							$data[] = '';
						}
					}
				}
			}
			$result_data = json_decode($r['result_data'], true);
			if (is_array($result_data) && isset($result_data['amount'])) {
				$data[] = $result_data['amount'];
			}
			else if (is_array($result_data) && isset($result_data['payment']) && isset($result_data['payment']['amount'])) {
				$data[] = $result_data['payment']['amount'];
			}
			if (count($multi_data_list) > 0) {
				foreach($multi_data_list as $k=>$v) {
					foreach($v as $m) {
						$data[7 + intval($k) - 1] = $m;
						if ($encode == "sjis") {
							mb_convert_variables('SJIS-win', 'UTF-8', $data);
						}
						fputcsv($f, $data, ",");
					}
				}
			}
			else {
				if ($encode == "sjis") {
					mb_convert_variables('SJIS-win', 'UTF-8', $data);
				}
				fputcsv($f, $data, ",");
			}
		}
		fclose($f);
	}
	
	public function action_inquiryresultdownload() {
		$post = $this->request->query();
		$results = $this->_model->get_inquiry_entry_result_by_no($post['inquiry_id'], $post['start_date'], $post['end_date'], $post['lang_cd'], $post['no']);
		$zipname = APPPATH . '../../files/' . time() . '.zip';
		$zip = new ZipArchive;
		$zip->open($zipname, ZipArchive::CREATE);
		foreach ($results as $result) {
			$files = json_decode($result['entry_result'], true);
			foreach($files as $f) {
				$fileContent = file_get_contents($f['url']);
				$zip->addFromString(basename($f['url']), $fileContent);
			}
		}
		$zip->close();
		
		$fp = fopen($zipname,'r');
		$filesize = filesize($zipname);
		//header('Content-Type: application/zip');
		header("Content-Type: application/octet-stream");
		header("Accept-Ranges: bytes");
		header("Accept-Length: $filesize");
		header('Content-Disposition: attachment; filename=files_' . time() . '.zip');
		
		ob_clean();
		flush();
		$buffer=1024;
		$count=0;
		while(!feof($fp)&&($filesize-$count>0)){
			$data=fread($fp,$buffer);
			if($data){
				$count+= strlen($data);
			}
			echo $data;
		}
		fclose($fp);
		unlink($zipname);
	}
	
	public function action_inquiryentrydata() 
	{
		if ($this->request->post()){
			$post = $this->request->post();
			$orm = ORM::factory('inquiryentry')->where('inquiry_id', '=', $post['inquiry_id'])->where('no', '=', $post['no'])->where('lang_cd', '=', 'ja')->find();
			if ($orm->entry_type_cd == 'opt' || $orm->entry_type_cd == 'sel' || $orm->entry_type_cd == 'chk') {
				$entry_data = json_decode($orm->entry_data, true);
				if (is_array($entry_data) && is_array($entry_data[0])) {
					$result = [];
					foreach($entry_data as $entry) {
						$result[] = $entry['title'];
					}
					$this->response->body(json_encode($result, JSON_UNESCAPED_UNICODE));
					return;
				}
				$this->response->body($orm->entry_data);
				return;
			}
			else if ($orm->entry_type_cd == 'txt') {
				$input_rules = json_decode($orm->input_rules, true);
				if (isset($input_rules['type']) && $input_rules['type'] == 'date') {
					$this->response->body(json_encode(['type'=>'date']));
					return;
				}
			}
			$this->response->body(json_encode(['type'=>'']));
		}
	}
	
	public function action_inquirymail() 
	{
		if ($this->request->post()){
			$post = $this->request->post();
			ob_end_clean();
			$maximum_model = new Model_Maximummodel();
			$ret = $maximum_model->send_inquiry_result_mail($post['result_id'], $post['type'], $post['title'], $post['div']);
			if ($ret) {
				$orm = ORM::factory('inquiryresultsupport');
				$orm->result_id = $post['result_id'];
				$orm->no = $this->_model->get_inquiry_result_support_no($post['result_id']);
				$orm->support_type_cd = '';
				$orm->memo = $post['memo'];
				$orm->upd_user = $this->_user_id;
				$orm->save();
				$this->response->body($this->_supportmemoview($post['result_id']));
			}
			else {
				$this->response->body('error');
			}
		}
	}

	public function action_inquiry2stepconfirm()
	{
		if ($this->request->post()){
			$post = $this->request->post();
			ob_end_clean();
			// 2_step 支払い金額設定
			$result = ORM::factory('inquiryresult', $post['result_id']);
			$result_data = json_decode($result->result_data, true);
			if ($result_data['payment']['2_step'] == 3) {
				$this->response->body('error');
				return;
			}
			$result_data['payment']['2_step'] = 2;
			$result_data['payment']['amount'] = $post['amount'];
			$inquiry = ORM::factory('inquiry', $result->inquiry_id);
			$inquiry_data = json_decode($inquiry->inquiry_data, true);
			$limit = $inquiry_data['payment']['pay_in_hours'];
			$result_data['payment']['limit'] = date('Y-m-d H:i:s', strtotime("+ $limit hour"));
			$result->result_data = json_encode($result_data, JSON_UNESCAPED_UNICODE);
			$result->save();
			$maximum_model = new Model_Maximummodel();
			$ret = $maximum_model->send_inquiry_result_mail($post['result_id'], 'pay', '', 1);  // 1 to member
			if ($ret) {
				$orm = ORM::factory('inquiryresultsupport');
				$orm->result_id = $post['result_id'];
				$orm->no = $this->_model->get_inquiry_result_support_no($post['result_id']);
				$orm->support_type_cd = '';
				$orm->memo = str_replace(['{amount}','{limit}'], [number_format($result_data['payment']['amount']), $result_data['payment']['limit']], $post['memo']);
				$orm->upd_user = $this->_user_id;
				$orm->save();
				$this->response->body($this->_supportmemoview($post['result_id']));
			}
			else {
				$this->response->body('error');
			}
		}
	}

	public function action_inquiry2steprefuse()
	{
		if ($this->request->post()){
			$post = $this->request->post();
			ob_end_clean();
			// 2_step 支払い金額設定
			$result = ORM::factory('inquiryresult', $post['result_id']);
			$result_data = json_decode($result->result_data, true);
			if ($result_data['payment']['2_step'] == 3) {
				$this->response->body('error');
				return;
			}
			$maximum_model = new Model_Maximummodel();
			$maximum_model->cancel_inquiry($this->_bot_id, $post['result_id'], $this->_user_id);
			$ret = $maximum_model->send_inquiry_result_mail($post['result_id'], 'cancelpay', '', 1);  // 1 to member
			if ($ret) {
				$orm = ORM::factory('inquiryresultsupport');
				$orm->result_id = $post['result_id'];
				$orm->no = $this->_model->get_inquiry_result_support_no($post['result_id']);
				$orm->support_type_cd = '';
				$orm->memo = $post['memo'];
				$orm->upd_user = $this->_user_id;
				$orm->save();
				$this->response->body($this->_supportmemoview($post['result_id']));
			}
			else {
				$this->response->body('error');
			}
		}
	}

	public function action_classbot()
	{
		$kind = $this->request->post()['kind'];
		if ($kind == '') {
			$orms = ORM::factory('bot')->where('delete_flg', '=', '0')->order_by('bot_id')->find_all();
		}
		else {
			$orms = ORM::factory('bot')->where('bot_class_cd', '=', $kind)->where('delete_flg', '=', '0')->order_by('bot_id')->find_all();
		}
		$bots = [];
		foreach($orms as $orm) {
			$bots[] = ['bot_id'=>$orm->bot_id, 'bot_name'=>$orm->bot_name];
		}
		$this->response->body(json_encode($bots));
	}
	
	public function action_scenetemplate()
	{
		$post = $this->request->post();
		$templates = $this->_model->get_scene_template($this->_bot_id, $post['func_type_cd'], '', $post['scene_cd']);
		$this->response->body(json_encode($templates));
	}

	public function action_maximumlist()
	{
		$post = $this->request->post();
		$list = ORM::factory('botmaximum')->where('bot_id', '=', $this->_bot_id)->where('span', '=', $post['span'])->find_all();
		$dict = [];
		foreach($list as $orm) {
			$dict[$orm->id] = $orm->name;
		}
		$this->response->body(json_encode($dict));
	}

	public function action_maximumlist_for_inquirycalendar()
	{		
		$inquiry_id = $this->request->query('inquiry');
		$input_rules = json_encode(['type'=>'date'], JSON_UNESCAPED_UNICODE);
		$inquiry_entries_with_maximum_select = "SELECT lang_cd, `no`, entry_data FROM t_inquiry_entry WHERE inquiry_id=$inquiry_id AND JSON_CONTAINS(input_rules, '$input_rules') AND entry_data != '' AND JSON_CONTAINS_PATH(entry_data, 'one', '$[0].maximum');";
		$entries_query = DB::query(Database::SELECT, $inquiry_entries_with_maximum_select);
		$entries_data = $entries_query->execute()->as_array();
		$maximum_list = [];
		$maximum_mapping = [];
		foreach($entries_data as $entry) {
			$entry_data = json_decode($entry['entry_data'], true);
			$lang_cd = $entry['lang_cd'];
			if ($entry_data) {
				foreach($entry_data as $data) {
					if (isset($data['maximum']) && !in_array($data['maximum'], $maximum_mapping)) {
						$maximum_list[] = ['id'=>$data['maximum'], 'name'=>$data['title'], 'lang_cd'=>$lang_cd];
						$maximum_mapping[] = $data['maximum'];
					}
				}
			}
		}
		$this->response->body(json_encode($maximum_list, JSON_UNESCAPED_UNICODE));
	}

	public function action_formlist_for_calendars()
	{		
		$bot_id = $this->_bot_id;
		$input_rules = json_encode(['type'=>'date'], JSON_UNESCAPED_UNICODE);
		$inquiries_with_maximum_select = "SELECT inquiry_id, inquiry_name FROM t_inquiry WHERE bot_id = $bot_id AND inquiry_id IN (SELECT inquiry_id FROM t_inquiry_entry WHERE JSON_CONTAINS(input_rules, '$input_rules') AND entry_data != '' AND JSON_CONTAINS_PATH(entry_data, 'one', '$[0].maximum') GROUP BY inquiry_id);";
		$inquiries_query = DB::query(Database::SELECT, $inquiries_with_maximum_select);
		$inquiries_results = $inquiries_query->execute()->as_array();

		$this->response->body(json_encode($inquiries_results));
	}
	
	public function action_maximumlists_for_calendars()
	{
		$post = $this->request->query();
		if ($post['span'] == 'null'){
			$lists = ORM::factory('botmaximum')->where('bot_id', '=', $this->_bot_id)->find_all();
		} else {
			$lists = ORM::factory('botmaximum')->where('bot_id', '=', $this->_bot_id)->where('span', '=', $post['span'])->find_all();
		}
		$results = [];
		foreach($lists as $list) {
			$results[] = ['id'=>$list->id, 'name'=>$list->name, 'span'=>$list->span];
		}
		$this->response->body(json_encode($results));
	}

	public function action_save_inquirycalendar()
	{
		$post = $this->request->post();
		
		if ($post['id'] == null){
			$result = DB::insert('t_inquiry_calendar', array('bot_id', 'title', 'upd_user', 'calendar_data'))->values(array($this->_bot_id, $post['title'], $this->_user_id, json_encode($post['calendar_data'], JSON_UNESCAPED_UNICODE)))->execute();
			if ($result[1] == true){
				$result = ['id'=> $result[0]];
			}
		} else {
			$calendar_data = json_encode($post['calendar_data'], JSON_UNESCAPED_UNICODE);
			$result = DB::update('t_inquiry_calendar')->set(array('title'=>$post['title'], 'upd_time'=> date('Y-m-d H:i:s', time()), 'upd_user'=> $this->_user_id, 'calendar_data'=> $calendar_data))
						->where('bot_id', '=', $this->_bot_id)->where('id', '=', intval($post['id']))->execute();
			if ($result == true){
				$result = ['id'=> intval($post['id'])];
			}
		}
		$this->response->body(json_encode($result));
	}

	public function action_inquirycalendar()
	{
		$post = $this->request->query();
		$list = ORM::factory('inquirycalendar')->where('bot_id', '=', $this->_bot_id)->where('id', '=', $post['id'])->find();
		$this->response->body(json_encode(['id'=>$list->id, 'title'=> $list->title, 'calendar_data'=>$list->calendar_data]));
	}

	public function action_checkFormAndInventory()
	{
		$inquiry = ORM::factory('inquiry')->where('bot_id', '=', $this->_bot_id)->where('delete_flg', '=', 0)->find_all();
		$maximum = ORM::factory('botmaximum')->where('bot_id', '=', (int)$this->_bot_id)->find_all();
		$can_create_calendar = false;
		if(count($inquiry) > 0 && count($maximum) > 0){
			$can_create_calendar = true;
		}
		$this->response->body(json_encode(['can_create_calendar'=>$can_create_calendar]));
	}

	public function action_maximumcalendar()
	{
		$post = $this->request->query();
		$list = ORM::factory('maximumcalendar')->where('bot_id', '=', $this->_bot_id)->where('id', '=', $post['id'])->find();
		// $this->response->body(json_encode(array("ok")));
		$this->response->body(json_encode(['id'=>$list->id, 'title'=> $list->title, 'span'=> $list->span, 'maximum_data'=>$list->maximum_data]));
	}

	public function action_inquiry_result_for_calendar()
	{
		$post = $this->request->query();
		$inquiry_dict = $this->_model->get_maximum_orders_inquiry($this->_bot_id, '', intval($post['maximum_id']), '', '', NULL, NULL);
		$inquiry_results = [];
		$inquiry_result_support = [];
		foreach ($inquiry_dict as $key=>$value) {
			$inquiry_result = $this->_model->get_inquiry_result_page_for_calendar($this->_bot_id, $key, $post['start_date'], '','', $post['maximum_id']);
			if (count($inquiry_result) > 0) {
				$result_id = $inquiry_result[0]['id'];
				foreach($inquiry_result as $key=>$value){
					$_inquiry_entry_result = [];
					$inquiry_entry_result_arr = $this->_model->get_inquiry_entry_result_page_for_calendar($value['result_id']);
					foreach($inquiry_entry_result_arr as $r) {
						if(strpos($r["entry_result"], 'maximum_id') !== false){
							if(strpos($r["entry_result"], $post['maximum_id']) === false){
								continue;	
							}
						}
						if (!array_key_exists($r['result_id'], $_inquiry_entry_result)) {
							$_inquiry_entry_result[$r['result_id']] = [];
						}
						$_inquiry_entry_result[$r['result_id']][] = $r;
					}
					array_push($inquiry_results, $_inquiry_entry_result);
					$inquiry_result_support_arr = $this->_model->get_inquiry_result_support_page_for_calendar($value['result_id']);
					foreach($inquiry_result_support_arr as $r) {
						if (!array_key_exists($r['result_id'], $inquiry_result_support)) {
							$inquiry_result_support[$r['result_id']] = [];
						}
						$inquiry_result_support[$r['result_id']][] = $r;
					}
				}
			}

			// get inquiry status list
			$inquiry_status_list = $this->_model->get_bot_tpl_message($this->_bot_id, 'inquiry.support_status_' . $key, $this->_lang_cd, true);
			if (count($inquiry_status_list) == 0) {
				$inquiry_status_list = $this->_model->get_bot_tpl_message($this->_bot_id, 'inquiry.support_status', $this->_lang_cd, true);
			}
		}
		$this->response->body(json_encode(['inquiry_dict'=>$inquiry_dict, 'inquiry_results'=> $inquiry_results, 'buttons'=> $inquiry_status_list, 'inquiry_result_support_dict'=>$inquiry_result_support]));
	}

	public function action_save_maximumcalendar()
	{
		$post = $this->request->post();
		if ($post['id'] == null){
			$result = DB::insert('t_maximum_calendar', array('bot_id', 'title', 'span','upd_user','maximum_data'))->values(array($this->_bot_id, $post['title'], $post['span'], $this->_user_id, json_encode($post['maximum_data'], JSON_UNESCAPED_UNICODE)))->execute();
			if ($result[1] == true){
				$result = ['id'=> $result[0]];
			}
		} else {
			$result = DB::update('t_maximum_calendar')->set(array('bot_id'=> $this->_bot_id,'title'=>$post['title'],'span'=>$post['span'], 'upd_time'=> date('Y-m-d H:i:s',time()), 'upd_user'=> $this->_user_id, 'maximum_data'=> json_encode($post['maximum_data'], JSON_UNESCAPED_UNICODE)))
						->where('bot_id', '=', $this->_bot_id)->where('id', '=', intval($post['id']))->execute();
			if ($result == true){
				$result = ['id'=> intval($post['id'])];
			}
		}
		$this->response->body(json_encode($result));
	}

	public function action_maximumremains()
	{
		$post = $this->request->query();
		$maximum = ORM::factory('botmaximum', $post['maximum_id']);
		$list = ORM::factory('botmaximumremains')->where('bot_id', '=', $this->_bot_id)->where('id', '=', $post['maximum_id'])->order_by('day')->order_by('time')->find_all();
		$create_time = date('Y-m-d');
		header("Content-Type: application/octet-stream");
		header("Content-Disposition: attachment; filename=" . $maximum->name . "_$create_time.csv");
		$f = fopen('php://output', 'w');
		ob_end_clean();
		fwrite($f, "\xEF\xBB\xBF");
		if ($maximum->span == '1d') {
			$title = ['日付', '残数'];
		}
		else {
			$title = ['日付', '時間帯', '残数'];
		}
		fputcsv($f, $title, ",");
		if ($maximum->span == '1d') {
			foreach($list as $orm) {
				$data = [$orm->day, $orm->maximum];
				fputcsv($f, $data, ",");
			}
		}
		else {
			foreach($list as $orm) {
				$data = [$orm->day, $orm->time, $orm->maximum];
				fputcsv($f, $data, ",");
			}
		}
		fclose($f);
	}

	public function action_checkunknown()
	{
		$log_id = $this->request->post()['log_id'];
		$title = $this->request->post()['title'];
		$lang_cd = NULL;
		$param = NULL;
		if($log_id){
			$table = "t_bot_log" . $this->_model->_log_ext;
			$sql = "SELECT log.member_msg, log.scene_cd, log.context_id, log.lang_cd
			FROM $table log
			WHERE log.log_id = :log_id";
			$query = DB::query(Database::SELECT, $sql);
			$query->parameters(array(
				':log_id' => $log_id,
			));
			$log = $query->execute()->as_array()[0];
			$lang_cd = $log['lang_cd'] ?: "ja";
			$param = ['sentence'=>$title, 'bot_id'=>$this->_bot_id, 'lang_cd'=>$lang_cd, 'scene_cd'=>$log['scene_cd'], 'context_id'=>$log['context_id']];
		} else {
			$lang_cd = $this->request->post()['lang_cd'] ?: "ja";
			$param = ['sentence'=>$title, 'bot_id'=>$this->_bot_id, 'lang_cd'=>$lang_cd, 'scene_cd'=>NULL, 'context_id'=>NULL];
		}
		$data = $this->_model->post_enginehook('service', 'identify_intent_by_ai','', $param);

		// 必要なデータに整形
		$intent_cd = $data['intent']['inquiry_inf']['db_data']['intent_cd'] ?: $data['intent']['parameters']['kind'] ?: "";
		if($data['intent']['name'] !== "input.unknown" && ($data['success'] == 'False' || $intent_cd === "")){
			Log::instance()->add(Log::DEBUG, 'identify_intent_by_ai failure=' . json_encode($data));
			$this->response->body(json_encode(['intent'=>'','user_input'=>$title,'name'=>$data['intent']['name']]));
			return;
		}

		$sub_intent_cd = $data['intent']['inquiry_inf']['db_data']['sub_intent_cd'] ?: $data['intent']['parameters']['sub_intent_cd'] ?: '';
		// sub_intent_cdに日本語が渡ることがある：日本語を含む文字列かどうかを判別
		if (preg_match("/[ぁ-ん]+|[ァ-ヴー]+|[一-龠]/u", $sub_intent_cd)) {
			$sub_intent_cd = ""; // 日本語文字列が含まれている
		}
		$question = "";
		if($intent_cd && strpos($intent_cd, "inquiry") !== 0){
			// intent_cd に inquiry. がない場合 => 付与
			$intent_cd = "inquiry.".$intent_cd;
		}
		if($intent_cd){
			// FAQ質問を検索
			$sql = "SELECT mb.question
			FROM m_bot_intent mb
			WHERE mb.intent_cd = :intent_cd
			AND mb.sub_intent_cd = :sub_intent_cd
			AND mb.lang_cd = :lang_cd
			";
			$query = DB::query(Database::SELECT, $sql);
			$query->parameters(array(
				':intent_cd' => $intent_cd,
				':sub_intent_cd' => $sub_intent_cd,
				':lang_cd' => $lang_cd,
			));
			$question = $query->execute()->as_array()[0]['question'];
		}

		$formatted_data = array(
			'name'=>$data['intent']['name'],
			'title'=>$question,
			'intent_cd' => $intent_cd,
			'sub_intent_cd' => $sub_intent_cd,
			'lang_cd' => $lang_cd,
		);

		error_log("ajax.php action_checkunknown() faq_title=$question intent_cd=$intent_cd sub_intent_cd=$sub_intent_cd lang_cd=$lang_cd");
		$this->response->body(json_encode(['data'=>$data,'intent'=>$formatted_data,'user_input'=>$title]));
	}

	public function action_report5()
	{
		$post = $this->request->query();
		$bot_id = $post['bot_id'];
		$start_date = $post['start_date'];
		$end_date = $post['end_date'];
		$scene_cd= '';
		//$context_id = '';
		//$repeat_flg = 1;
		//$zero = '-';

		// https://support.activalues.com/issues/30545　の対応のため
		$grp_bot_id = $this->_model->get_grp_bot_id($bot_id);
		if ($grp_bot_id == 0) {
			$bot_id = '';
		}

		$result = $this->_model->get_report5($bot_id, $start_date, $end_date, $scene_cd);
		$day_result = [];
		$format_result = [];
		$total = 0;
		foreach($result as $row) {
			$total = $total + $row['mnt'];
			$day_result[$row['regist_date1'] . $row['sns_type_cd'] . $row['lang_cd']] = $row['mnt'];
			if (array_key_exists($row['sns_type_cd'] . $row['lang_cd'], $format_result)) {
				$format_result[$row['sns_type_cd'] . $row['lang_cd']] = $format_result[$row['sns_type_cd'] . $row['lang_cd']] + intval($row['mnt']);
			}
			else {
				$format_result[$row['sns_type_cd'] . $row['lang_cd']] = intval($row['mnt']);
			}
		}
		if ( count($format_result) ==! 0 && array_key_exists('wb0', $format_result)){ //"wb0"を"wbja"に変換（一時対応）
			$format_result['wbja'] += $format_result['wb0'];
			unset($format_result['wb0']);
		}
		$this->response->body(json_encode($format_result));
	}

	public function action_tickets()
	{
		$post = $this->request->query();
		$bot_id = $post['bot_id'];
		$ticket_tag_cd = $post['ticket_tag_cd'];
		$display_flg = 0;
		if (isset($post['display_flg'])) {
			$display_flg = $post['display_flg'];
		}
		$total = 0;

		$result = $this->_model->get_ticket_list($bot_id, $ticket_tag_cd, '', '', $display_flg);
		$total = count($result);

		for($i = 0; $i < $total ; $i++) {
			if (!in_array($this->_user_id, explode(",", $result[$i]["content"]))){
				$result[$i]["unread"] = true;
			} else {
				$result[$i]["unread"] = false;
			}
		}
		$this->response->body(json_encode($result));
	}

	public function action_updateticket() 
	{
		if ($this->request->post()) {
			$post = $this->request->post();
			if ($post['ticket_id'] != '') {
				$orm = ORM::factory('ticket', $post['ticket_id']);
				try {
					if (isset($orm->ticket_id)) {
						$orm->display_flg = $post['display_flg'];
						$orm->update_time = date('Y-m-d H:i:s');
						$orm->save();
						$this->response->body(json_encode(['result'=>'success']));
					} else {
						$this->response->body(json_encode(['error'=>'ticket_id not found']));
					}
				} catch (Exception $e) {
					$this->response->body(json_encode(['error'=>$e->getMessage()]));
				}
			}
		}
	}

	public function action_dailyreport()
	{
		$post = $this->request->query();
		$bot_id = $post['bot_id'];
		$start_date = $post['start_date'];
		$end_date = $post['end_date'];
		$result = $this->_model->get_daily_report_for_chart($bot_id, $start_date, $end_date);
		$this->response->body(json_encode(['result'=>$result]));
	}

	public function action_congestion()
	{
		$post = $this->request->query();
		$bot_id = $post['bot_id'];
		$lang = $post['lang'];
		$results = $this->_model->get_items_congestion($bot_id,$lang);
		$this->response->body(json_encode(['result'=>$results]));;
	}

	public function action_congestion_numbers()
	{
		$post = $this->request->query();
		$results = $this->_model->get_items_congestion($this->_bot_id);
		$ja_promotion_images = $this->_model->get_bot_img_message_list($this->_bot_id, 'congestion_user_page_advertisement_img', 'ja');
		$en_promotion_images = $this->_model->get_bot_img_message_list($this->_bot_id, 'congestion_user_page_advertisement_img', 'en');
		$congestion_promotion_images = array_merge($ja_promotion_images, $en_promotion_images);
		$promotion_image_string = "";
		foreach($congestion_promotion_images as $promo) {
			$promotion_image_string = $promotion_image_string . $promo['msg_image'];
		}

		$congestions = [];
		foreach($results as $r) {
			if ($r['status_cd'] == null) continue;
			if (array_key_exists($r['item_id'], $congestions)) continue;
			$item_data = json_decode($r['item_data'], true);
			if (array_key_exists('business_hours', $item_data)) {
				$congestions[$r['item_id']] = $r;
			}
		}
        // https://support.activalues.com/issues/35710の対応
        //class_cd && item_idの場合は想定していない
        if (array_key_exists('item_id', $post) || array_key_exists('class_cd', $post)){
            $fillter_congestions = [];
            if (array_key_exists('item_id', $post)) {
                $item_ids = explode(",", $post['item_id']);
                foreach ($congestions as $k => $v){
                    foreach($item_ids as $item_id ){
                        if ($k == $item_id){
                            $fillter_congestions[$k] = $v;
                        }
                    }
                }
            }
            if (array_key_exists('class_cd', $post)) {
                foreach($congestions as $k => $v){
                    $class_cds = explode(" ", $v['class_cd']);
                    foreach($class_cds as $class_cd){
                    if( preg_match( '/^' . $post['class_cd'] . '/', $class_cd) ) {
                        if (array_key_exists($k, $fillter_congestions)) continue;
                            $fillter_congestions[$k] = $v;
                        }
                    }
                }
            }
            $congestions = $fillter_congestions;
        }
		$this->response->body(json_encode(['result'=> count($congestions), 'images'=> $promotion_image_string]));
	}

	public function action_get_maximum_screen_data() {
		$maximum_id = $this->request->query('id');
		$target_date = date('Y-m-d'); 
		$num_in_page = $this->request->query('num_in_page');
		$is_busy_period = $num_in_page == 1 || $num_in_page != 2;
		$days = $is_busy_period ? ['today', 'tomorrow'] : ['today', 'tomorrow', 'day_after_tomorrow', 'two_days_after_tomorrow'];
		$results = [];
		foreach ($days as $i => $day) {
			$date = date('Y-m-d', strtotime("{$i} days", strtotime($target_date)));
			$day_results = $this->_model->get_maximum_screen($maximum_id, $date);
			$current_date_time = new DateTime();
			$current_date = $current_date_time->format('Y-m-d');
			$current_time = $current_date_time->format('H:i');
			foreach ($day_results as &$res_list) {
				foreach ($res_list as &$res) {
					$res['is_past'] = ($res['day'] == $current_date) && ($res['time'] < $current_time);
					$res['is_low_stock'] = (int)$res['maximum'] <= (int)$res['threshold'];
					$res['is_no_stock'] = (int)$res['maximum'] == 0;
					$res = array_intersect_key($res, array_flip(['day', 'time', 'is_past', 'is_low_stock', 'is_no_stock']));
				}
			}
			$results[$day] = $day_results;
		}
		$this->response->headers('Content-Type', 'application/json');
		$this->response->body(json_encode($results));
	}
	
	public function action_botclasscodesort()
	{
		$post = $this->request->post();
		foreach($this->_model->get_bot_lang($this->_bot) as $k=>$v) {
			$result = ORM::factory('classcode')->where('code_div', '=', $post['code_div'])->where('class_cd', '=', $post['class_cd'])->where('lang_cd', '=', $k)->find_all();
			if (count($result)>0) {
				DB::update('m_class_code')->set(['sort'=>$post['sort']])->where('code_div', '=', $post['code_div'])->where('class_cd', '=', $post['class_cd'])->where('lang_cd', '=', $k)->execute();
			}
		}
		$this->response->body(json_encode(array('ok')));
	}
	
	public function action_logsearchcsv()
	{
		$post = $this->request->query();
		$data = $this->_model->get_keywordlog($post['cond_bot_id'], $post['cond_scene_cd'], 
			$post['start_date'], $post['end_date'], $post['keyword'], $post['member_only'], 0, 100000);
		
		$create_time = date('Y-m-d');
		$filename = 'logsearch_' . $create_time . '.csv';
		header("Content-Type: application/octet-stream");
		header("Content-Disposition: attachment; filename=$filename");
		$f = fopen('php://output', 'w');
		ob_end_clean();
		fwrite($f, "\xEF\xBB\xBF");
		$title = ['お名前','チャンネル','言語','会話日時','ユーザ導線','お客様発言','チャット回答','回答者'];

		fputcsv($f, $title, ",");
		foreach($data as $log) {
			$line = array();
			if ($log['sns_type_cd'] == 'wb') {
				$line[] = 'Webユーザ No:' . $log['member_no'];
			}
			else {
				$line[] = $log['last_name'] . ' ' . $log['first_name'];
			}
			$line[] = $this->_codes['08'][$log['sns_type_cd']];
			$line[] = $this->_codes['02'][$log['lang_cd']];
			$line[] = $log['log_time'];
			$line[] = $log['label'];
			if ($log['lang_cd'] == 'ja' || $log['member_msg_t'] == '') {
				$line[] = $log['member_msg'];
			}
			else {
				$line[] = $log['member_msg_t'] . "<br/>※原文：". $log['member_msg'];
			}
			$line[] = str_replace(',', '，',$log['bot_msg']);
			if($log['bot_msg'] != '') {
				if($log['intent_cd'] == 'input.chat') {
					$line[] = $log['name'];
				}
				else {
					$line[] = 'CHATBOT';
				}
			}
			else {
				$line[] = '';
			}

			fputcsv($f, $line, ",");
		}
		fclose($f);
	}

	public function action_couponresultcsv()
	{
		$post = NULL;

		$post = $this->request->query();
		$coupon_id = $post['coupon_id'];
		$lang_cd = $post['lang_cd'];
		$coupon_result = $this->_model->get_coupon_result($this->_bot_id, $coupon_id, $post['get_start_date'], $post['get_end_date'], $lang_cd, $post['sns_type_cd']);
		$coupon_use_result = $this->_model->get_coupon_use_result($this->_bot_id, $coupon_id, $post['get_start_date'], $post['get_end_date'], $post['lang_cd']);
		$coupon_use_dict = [];
		foreach($coupon_use_result as $cur) {
			if (isset($cur['result_no'])) {
				$coupon_use_dict[$cur['result_no']][] = $cur;
			}
			else {
				$coupon_use_dict[$cur['result_no']] = [$cur];
			}
		}
		// facility name get
		$coupon = ORM::factory('coupon', $coupon_id);
		$coupon_data = json_decode($coupon->coupon_data, true);
		$use_facility = $coupon_data['use_facilitys'];
		$use_facilitys = $this->_model->get_bot_tpl_message($this->_bot_id, $use_facility, 'ja');
		$use_facilitys_json = json_decode($use_facilitys,true);

		// handle data
		$total_remain = null;
		$hasLimitForAll = $coupon_data['use_amount_type'] == '2';
		$hasLimitForMember = $coupon_data['stock_type_member'] == '2';
		if ($hasLimitForAll) {
			if ($hasLimitForMember) {
				$total_remain = min(intval($coupon_data['use_amount'] ?? 0), intval($coupon_data['stock_member'] ?? 0));
			} else {
				$total_remain = intval($coupon_data['use_amount'] ?? 0);
			}
		} else {
			if ($hasLimitForMember) {
				$total_remain = intval($coupon_data['stock_member'] ?? 0);
			}
		}

		$report_data = $this->_model->format_coupon_result($coupon_result, $coupon_use_dict, $total_remain, $post['status_cd'], $post['use_start_date'], $post['use_end_date']);
		
		$create_time = date('Y-m-d');
		header("Content-Type: application/octet-stream");
		header("Content-Disposition: attachment; filename=coupon_report_$create_time.csv");
		$f = fopen('php://output', 'w');
		ob_end_clean();
		fwrite($f, "\xEF\xBB\xBF");
		$title = ['利用者', '言語','取得日時','取得方法', '入力コード', '利用日時', '利用履歴'];
		fputcsv($f, $title, ",");
		
		foreach ($report_data as $rank) {
			$rows = [];
			
			if ($rank['sns_type_cd'] == 'wb') {
				$rows[] = 'Webユーザ';
			} else {			
				$rows[] = $rank['last_name'] . ' ' . $rank['first_name'];
			}

			$rows[] = $this->_model->get_code('02', 'ja')[$rank['lang_cd']];
			$rows[] = $rank['get_date'];

			if ($rank['get_route'] == '') {
				$rows[] = '-';
			} else {
				$rows[] = $rank['get_route'] . ' ' . $rank['get_route_detail'];
			}

			if ($rank['apply_code'] == '') {
				$rows[] = '';
			} else {
				$rows[] = $rank['apply_code'];
			}

			//TODO: 予約可能回数の取得（既存ロジックバグっているかも
			// if ($rank['get_route'] == 'inquiry') {
			// 	echo ($rank['num']);
			// }
			// else {
			// 	if (isset($rank['use_result_no']) && count($rank['use_result_no']) == $rank['num']) {
			// 		echo (0);
			// 	}
			// 	else {
			// 		echo ($rank['num']);
			// 	}
			// }

			$rows[] = $rank['last_use_date'] ?? '未使用';
			
			$history_row = [];
			foreach($rank['coupon_use_result'] as $use_result) {
				$use_facility = json_decode($use_result['use_facility'], true);
				$facility = '';
				if (is_array($use_facility)) {
					$facility = $use_facility['type'];
				} else {
					$facility = $use_facilitys_json[$use_result['use_facility']] ?? '';
				}
				$temp = '利用日時:' . $use_result['use_date'] . ", 利用先施設(予約):$facility";
				$history_row[] = $temp;
			}
			$rows[] = count($history_row) == 0 ? '' : join(PHP_EOL, $history_row);

			fputcsv($f, $rows, ",");
		}
		fclose($f);
	}

	public function action_botusers() {
		$post = $this->request->post();
		$bot_id = $post['bot_id'];
		$users = ORM::factory('user')->where('bot_id', '=', $bot_id)->order_by('name')->find_all();
		$user_arr = [];
		foreach($users as $user) {
			$user_arr[] = ['code'=>$user->user_id, 'name'=>$user->name];
		}
		$this->response->body(json_encode($user_arr));
	}

	public function action_botparentusers() {
		$post = $this->request->post();
		$bot_id = $post['bot_id'];
		$users = ORM::factory('user')->where('bot_id', '=', $bot_id)->order_by('name')->find_all();
		$user_arr = [];
		foreach($users as $user) {
			$user_arr[] = ['code'=>$user->user_id, 'name'=>$user->name];
		}
		$this->response->body(json_encode($user_arr));
	}

	public function action_getparentbotname() {
		$post = $this->request->post();
		$bot_id = $post['parentBotId'];
		$parent = ORM::factory('bot')->where('bot_id', '=', $bot_id)->find();
		$this->response->body(json_encode(['bot_name'=>$parent->bot_name]));
	}

	public function action_getserverbot()
	{
		$this->response->body(json_encode(["bot_id"=>$this->_bot_id, "bot_name"=>$this->_bot->bot_name], JSON_UNESCAPED_UNICODE));
	}

	private function _reqview($link_type_cd, $link_id) {
		$req_task_msgs = $this->_model->get_req_task_msgs($this->_bot->bot_class_cd);
		$req_task_data = $this->_model->get_bot_req_task($this->_bot_id, $link_type_cd, $link_id);
		$view_text = '';
		$reqing_msgs = [];
		foreach($req_task_data as $req) {
			$req_task_view = View::factory('admin/reqtaskview');
			$req_task_view->req = $req;
			$view_text =  $view_text . $req_task_view;
			$req_data = json_decode($req['req_data'], true);
			if (is_array($req_data) && array_key_exists('msg_cd', $req_data)) {
				if (!in_array($req_data['msg_cd'], $reqing_msgs)) {
					$reqing_msgs[] = $req_data['msg_cd'];
				}
			}
		}
		$new_text = '';
		if ($this->_user->role_cd == '80' || $this->_user->role_cd == '99') {
			$new_text = $new_text . '<a href="javascript:void(0);" class="reqinput" item_id="' . $link_id . '"><span class="btn-smaller-round btn-lightblue" style="margin: 4px;"><span class="icon-add"></span>' . __('admin.common.label.request') . '</span></a>';
			foreach($req_task_msgs as $msg) {
				if (!in_array($msg['msg_cd'], $reqing_msgs)) {
					$new_text = $new_text . '<a href="javascript:void(0);" class="reqtask" msg_cd="' . $msg['msg_cd'] . '" item_id="' . $link_id . '"><span class="badge badge-primary" style="margin: 4px;">' . $msg['msg_name'] . '</span></a>';
				}
			}
		}
		return $new_text . $view_text;
	}
	
	private function _jsoncallback($func_name, $data) {
		return $func_name . "(" . json_encode($data) . ")";
	}

	public function action_very_mainclicksreport()
	{
		$post = $this->request->query();
		$bot_id = $post['bot_id'];
		$start_date = $post['start_date'];
		$end_date = $post['end_date'];
		$result = $this->_very_model->get_main_clicks($bot_id, $start_date, $end_date);
		$this->response->body(json_encode(['result'=>$result]));
	}

	public function action_all_item_ids()
	{
		$post = $this->request->post();
		$items = $this->_model->get_itemdiv_by_class_cd($this->_bot_id,$post['item_div'],$post['class_cd']);
		$this->response->body(json_encode($items));
	}

	private function _logEcho($_to_echo, $line=false)
	{
		// if ($line) {
		// 	$_to_echo .= '<br>';
		// }
		Log::instance()->add(Log::DEBUG, $_to_echo);
	}
	
	//TODO test localhost task.php
    public function action_check_registered_business_task_test() {
		$task_type_cd = $this->request->query('type_cd', NULL);
		if ($this->_user->role_cd == '99' && $task_type_cd && $task_type_cd >= '05') {
			$cli_task = new CLI_Task();
			$cli_task->execute($task_type_cd);
		}
    }
	
	// 	public function action_get_task_status() {
	public function action_is_mail_task_editable() {
		$mail_task_id = $this->request->query('mail_task_id', NULL);
		if (!$mail_task_id || !is_numeric($mail_task_id) || $mail_task_id < 0) {
			$this->response->body(json_encode(['success'=>false, 'msg'=>'No valid mail_task_id']));
			return;
		}
		$task = ORM::factory('mailtask', $mail_task_id);
		if ($mail_task_id != $task->mail_task_id) {
			$this->response->body(json_encode(['success'=>false, 'msg'=>'Cannot get correct mail_task of id: '.$mail_task_id]));
			return;
		}
		$mail_task_data = json_decode($task->mail_task_data);
		$editable = $task->status < 2 || ($task->status == 3 && $mail_task_data->test_mode == 1);
		$this->response->body(json_encode(['success'=>true, 'editable'=>$editable]));
	}

	public function action_verify_ses_mail_check_id() {
		$email = $this->request->query('email', NULL);
		$fetch_extra_info = $this->request->query('fetch_extra_info', NULL);
		if ($email == NULL) {
			$this->response->body(json_encode(['success'=>false]));
			return;
		}
		$data = [
			'mail' => $email
		];
		$result = $this->_model->post_enginehook('service', 'check_mail_identity', '', $data);
		if ($result == null || $result['success'] == 'false' || $result['exist'] == 'false' || $result['verified'] == 'false') {
			$response = ['success'=>false];
			if ($fetch_extra_info && $result['success'] == 'true' && $result['exist'] == 'true' && $result['verified'] == 'false' && isset($result['extra_info'])) {
				$response['extra_info'] = $result['extra_info'];
				$response['exist'] = $result['exist'];
				$response['verified'] = $result['verified'];
			} else if ($fetch_extra_info && $result['success'] == 'true' && $result['exist'] == 'false' && isset($result['extra_info'])) {
				$response['extra_info'] = $result['extra_info'];
				$response['exist'] = $result['exist'];
			}
			$this->response->body(json_encode($response));
		} else {
			$response = ['success'=>true];
			if ( $fetch_extra_info && isset($result['extra_info']) ) {
				$response['extra_info'] = $result['extra_info'];
			}
			$this->response->body(json_encode($response));
		}
	}

	public function action_create_ses_mail_identity() {
		$email = $this->request->query('email', NULL);
		if ($email == NULL) {
			$this->response->body(json_encode(['success'=>false, 'msg'=>'No valid email']));
			return;
		}
		$data = [
			'mail' => $email
		];
		$result = $this->_model->post_enginehook('service', 'create_mail_identity', '', $data);
		if ($result == null || $result['success'] == 'false') {
			$this->response->body(json_encode(['success'=>false, 'msg'=>'Failed to create mail identity']));
		} else {
			$this->response->body(json_encode(['success'=>true]));
		}
	}
	
	public function action_verify_ses_mail_check_create_id() {
		$email = $this->request->query('email', NULL);
		if ($email == NULL) {
			$this->response->body(json_encode(['success'=>false]));
			return;
		}
		$data = [
			'mail' => $email
		];
		$result = $this->_model->post_enginehook('service', 'check_mail_identity', '', $data);
		if ($result == null || $result['success'] == 'false' || $result['exist'] == 'false' || $result['verified'] == 'false') {
			//メール存在しなければ登録する
			if ($result != null && $result['success'] == 'true') {
				$this->_model->post_enginehook('service', 'create_mail_identity', '', $data);
			}
			$this->response->body(json_encode(['success'=>false]));
		} else {		
			$this->response->body(json_encode(['success'=>true]));
		}
	}

	public function action_put_mail_identity_mail_from_attributes() {
		$domain = $this->request->query('domain', NULL);
		$mail_from_domain = $this->request->query('mail_from_domain', NULL);
		if ($domain == NULL) {
			$this->response->body(json_encode(['success'=>false, 'msg'=>'No valid domain']));
			return;
		}
		if ($mail_from_domain == NULL) {
			$this->response->body(json_encode(['success'=>false, 'msg'=>'No valid mail_from_domain']));
			return;
		}
		$data = [
			'domain' => $domain,
			'mail_from_domain' => $mail_from_domain
		];
		$result = $this->_model->post_enginehook('service', 'put_mail_identity_mail_from_attributes', '', $data);
		if ($result == null || $result['success'] != 'true' ) {
			$response = ['success'=>false, 'msg'=>'Failed to put mail_from_domain'];
			if ($result != null && isset($result['extra_info'])) {
				$response['extra_info'] = $result['extra_info'];
			}
			$this->response->body(json_encode($response));
		} else {
			$this->response->body(json_encode(['success'=>true, 'exist'=>$result['exist'], 'extra_info'=>$result['extra_info']]));
		}
	}

	// send test newsletter email to testers
	public function action_send_newsletter_test_mail(){
		$post = $this->request->post();

		$bot_id = $this->_bot_id;
		if ( isset($post['bot_id']) && $post['bot_id'] ) {
			$bot_id = $post['bot_id'];
		}
		
		$bot = ORM::factory('bot')->where('bot_id', '=', $bot_id)->where('delete_flg', '=', 0)->find();
		$bot_name =  $bot->bot_name ;
		$test_mail_addresses = $post['testers'];

		$data = [
			'bot_id' => $bot_id,
			'type' => '09',
			'title' => $post['title'],
			'body' => $post['body'],
		];

		if (isset($post['task_id']) && $post['task_id']) {
			$data['link_id'] = $post['task_id'];
		}

		// sender and its display name
		$sender = NULL;
		$sender_name = NULL;
		$reply_to = NULL;
		if ( isset($post['sender_name']) && $post['sender_name'] ) {
			$sender_name = $post['sender_name'];
		}
		if ( isset($post['reply_to']) && $post['reply_to'] ) {
			$reply_to = $post['reply_to'];
		}
		if ( isset($post['sender']) && $post['sender'] ) {
			$sender = $post['sender'];
			if ( $sender_name ){
				$sender = "=?utf-8?B?". base64_encode($sender_name) . "?=" . "<" .$sender . ">";
			}
		} else if ( $sender_name ) {
			// get default sender from bot
			// 'json_mail_setting'=>array('送信メール設定','{"default":{"type":"ses","sender":"talkappi<<EMAIL>>"}}'),
			$json_mail_setting = $this->_model->get_bot_setting($bot_id, 'json_mail_setting');
			if ($json_mail_setting) {
				$mail_setting = json_decode($json_mail_setting);
				if ( isset($mail_setting->default) && isset($mail_setting->default->sender) && $mail_setting->default->sender != NULL && !empty($mail_setting->default->sender) ){
					$default_sender = $mail_setting->default->sender;
					$pattern = '/[a-z0-9_\-\+\.]+@[a-z0-9\-]+\.([a-z]{2,4})(?:\.[a-z]{2})?/i';
					preg_match_all($pattern, $default_sender, $matches);
					if ( $matches && isset($matches[0]) && $matches[0] && count($matches[0]) > 0 ){
						$sender = $matches[0][0];
						$sender = "=?utf-8?B?". base64_encode($sender_name) . "?=" . "<" .$sender . ">";
					} else {
						$this->_logEcho('Sending mail extracting default sender failed '.$pattern.PHP_EOL);
					}
				}
			}
		}
		if ($sender != NULL) {
			$data['sender'] = $sender;
		}
		if ($reply_to != NULL) {
			$data['replyto'] = $reply_to;
		}

		if (isset($post['is_track_event']) && $post['is_track_event'] == "true") {	
			//urlの短縮
			$data['body'] = preg_replace_callback(
				"|(href=\")(https?://[\w!?/+\-_~;.,*&@#$%()'\[\]:;=]+)|",
				function ($m) { return $m[1].($this->_model->post_shorten_url(str_replace('&amp;', '&', $m[2]))); },
				$data['body']);
			$data['is_track_event'] = 1;
		}
		$extend_project_id = 0;
		if ( isset($post['project_id']) && $post['project_id'] ) {
			$extend_project_id = $post['project_id'];
		}
		$extend_template_params = db::select('extend_no', 'template_param')
			->from(DB::expr('t_mail_extend'))
			->where('bot_id', '=', $bot_id)
			->where('project_id', '=', $extend_project_id)
			->where('template_param', 'IS NOT', NULL)
			->execute()->as_array('extend_no', 'template_param')
			;

		//test members of this task
		$newsletter_test_members = DB::select('member.mail_member_id', 'member.bot_id', 'member.email', 'first_name', 'last_name', 'bot.bot_name')
		->from(DB::expr('t_mail_member as member'))
		->join(DB::expr('t_mail_member_bot as mb'), 'INNER')
		->on('member.mail_member_id', '=','mb.mail_member_id')
		->join(DB::expr('t_bot as bot'), 'INNER')
		->on('mb.bot_id', '=','bot.bot_id')
		->where('member.email', 'IN', $test_mail_addresses)
		->where('mb.bot_id', '=', $bot_id)
		->execute()
		->as_array('email');

		$count = count($test_mail_addresses);

		foreach($test_mail_addresses as $index=>$email) {
			$request_data = $data;
			$request_data['receiver'] = $email;
			if (isset($newsletter_test_members[$email])) {
				//handle template parameters linked to each mail member: bot, name, extend attributes, etc.
				$member = $newsletter_test_members[$email];

				$member_extend_array = db::select('extend_no', 'value')
					->from('t_mail_member_extend')
					->where('mail_member_id', '=', $member['mail_member_id'])
					->execute()->as_array('extend_no', 'value')
					;
				$params = [
					'bot' => $member['bot_name'],
					'name' => $member['first_name'].' '.$member['last_name'],
					'username' => $member['first_name'].' '.$member['last_name'],
				];
				foreach($member_extend_array as $extend_no=>$value) {
					$params['extend'.$extend_no] = $value;
					if( isset($extend_template_params[$extend_no]) && $extend_template_params[$extend_no] ) {
						$params[$extend_template_params[$extend_no]] = $value;
					}
				}
				$request_data['member_id'] = 'TesterMember'.$member['mail_member_id'];
				$request_data['params'] = $params;
			} else {
				//send mail to unregistered email
				$mail_tester_id = 'Tester'.($index + 1);
				$request_data['member_id'] = $mail_tester_id;
				// no template params
			}

			if (isset($post['unsubscribe_enable']) && $post['unsubscribe_enable'] == "true") {	
				//url link for unsubscribe
				Log::instance()->add(Log::DEBUG, message: 'Posting service.sendmail unsubscriber request_data=' . json_encode($request_data));
				$newsletter_model = new Model_Newslettermodel();
				$request_data['body'] = $newsletter_model->append_unsubscribe_link($data['body'], $post['task_id'], $request_data['member_id'], $email, $bot_id);
			}

			$engine_response = $this->_model->post_enginehook('service', 'sendmail', '', $request_data);
			if ( isset($engine_response['success']) && $engine_response['success'] == 'False') {
				Log::instance()->add(Log::DEBUG, 'API service.sendmail failure=' . json_encode($engine_response));
				$this->_model->log_error(__FUNCTION__, "API service.sendmail failure=" . json_encode($engine_response, JSON_UNESCAPED_UNICODE));

				if( isset($engine_response['error_message']) ) {
					$engine_error_msg = $engine_response['error_message'];
					if( isset($engine_error_msg['message']) && isset($engine_error_msg['code']) && $engine_error_msg['code'] == 'MessageRejected' ) {
						$engine_error_msg_detail = $engine_error_msg['message'];
						if (str_starts_with($engine_error_msg_detail, 'Email address is not verified. The following identities failed the check in region ')
							&& str_ends_with($engine_error_msg_detail, ': '.$sender)
						) {
							// record error of invalid sender
							Log::instance()->add(Log::DEBUG, 'Posting service.sendmail with un-verified email address: ' . $engine_error_msg_detail);
							$this->response->body(json_encode(['success'=>false, 'id'=>$mail_tester_id, 'email'=>$email, 'error'=>'Invalid sender email address']));
						}
						$this->response->body(json_encode(['success'=>false, 'id'=>$mail_tester_id, 'email'=>$email, 'error'=>$engine_error_msg_detail]));
						return;
					}
				}
				// $test_status = 3;
				$this->response->body(json_encode(['success'=>false, 'id'=>$mail_tester_id, 'email'=>$email, 'error'=>'Unknown error', 'response'=>$engine_response]));
				return;
			}
		}
		$this->_logEcho('Test emails sent...<br/>');
		$this->response->body(json_encode(['success'=>true, 'count'=>$count]));
		return;
	}

	public function action_count_mail_members()
	{
		$bot_id = $this->request->query('bot_id', NULL);
		$project_id = $this->request->query('project_id', NULL);
		$mail_member_tag_nos = $this->request->query('tags', NULL);
		$count = 0;
		if ($bot_id == NULL) {
			$this->response->body(json_encode(['success'=>false]));
		}
		$is_newsletter_project_enabled = $this->_model->is_newsletter_project_enabled($bot_id);
		if ( $mail_member_tag_nos == NULL || ! is_array($mail_member_tag_nos) || count($mail_member_tag_nos) <= 0   ) {
			$count_sql = DB::select(array(DB::expr('COUNT(`email`)'), 'total_members'))
				->from(DB::expr('t_mail_member as member'))
				->join(DB::expr('t_mail_member_bot as mb'), 'INNER')
				->on('member.mail_member_id', '=','mb.mail_member_id')
				->where('mb.bot_id', '=', $bot_id);
					
			if ($is_newsletter_project_enabled) {
				if ( $project_id ) {
					$count_sql->where('member.project_id', '=', $project_id);
				} else {							
					$count_sql->where('member.project_id', 'IS', NULL);
				}
			}

			$count = $count_sql->execute()->get('total_members', 0);
		} else {
			$left = in_array(-1, $mail_member_tag_nos);
			$join = 'INNER';
			if ($left) {
				$join = 'LEFT';
				// error_log($join." JOIN for tag_no -1");
			}
			
			$member_tags_count_sql = DB::select(array(DB::expr('COUNT(DISTINCT `email`)'), 'total_members'))
				->from(DB::expr('t_mail_member as member'))
				->join(DB::expr('t_mail_member_bot as mb'), 'INNER')
				->on('member.mail_member_id', '=','mb.mail_member_id')
				->join(DB::expr('t_mail_member_tag as mt'), $join)
				->on('member.mail_member_id', '=','mt.mail_member_id')
				->where('mb.bot_id', '=', $bot_id);
				
			if ($is_newsletter_project_enabled) {
				if ( $project_id ) {
					$member_tags_count_sql->where('member.project_id', '=', $project_id);
				} else {							
					$member_tags_count_sql->where('member.project_id', 'IS', NULL);
				}
			}

			if ( $left ) {
				// error_log($join." JOIN or_where clause for tag_no -1");
				$member_tags_count_sql = $member_tags_count_sql->where_open()
					->or_where('mt.tag_no', 'IN', $mail_member_tag_nos)->or_where('mt.tag_no', 'IS', NULL)
					->where_close();
			} else {
				$member_tags_count_sql = $member_tags_count_sql->where('mt.tag_no', 'IN', $mail_member_tag_nos);
			}
			$count = $member_tags_count_sql->execute()->get('total_members', 0);
		}
		// $this->response->body(json_encode($items));
		// $this->response->body($member_count);
		$this->response->body(json_encode(['success'=>true, 'count'=>$count]));
	}

	public function action_list_mail_members($extend = false)
	{
		$bot_id = $this->request->query('bot_id', NULL);
		$project_id = $this->request->query('project_id', NULL);
		$extend = $this->request->query('extends', $extend);
		$mail_member_tag_nos = $this->request->query('tags', NULL);
		// $count = 0;
		if ($bot_id == NULL) {
			$this->response->body(json_encode(['success'=>false]));
		}
		$mail_members = $this->_model->list_mail_members($bot_id, $project_id, $mail_member_tag_nos);

		$list_mail_members = [];
		foreach($mail_members as $mail_member) {
			$list_mail_members []= $mail_member;
		}
		$response = ['success'=>true, 'data'=>$list_mail_members];
		if ( $extend && $mail_members ) {
			$member_ids = array_keys($mail_members);
			$members_extends = $this->_model->get_mail_member_extend_values($bot_id, $member_ids);
			$response['extend_values'] = $members_extends;

			$all_extensions = $this->_model->list_mail_member_extend_column_names($bot_id, true);
			$members_extend_columns = $this->_model->filter_mail_members_extend_columns($bot_id, $member_ids);
			$no_alias_columns = array_keys($members_extend_columns, null);
			foreach($no_alias_columns as $extend_no) {
				$members_extend_columns[$extend_no] = $all_extensions[$extend_no];
			}
			$response['extends'] = $members_extend_columns;
		}
		$this->response->body(json_encode($response));
	}

	public function action_sample_csv_download() {
		// $create_time = date('Y-m-d');
		header("Content-Type: application/octet-stream");
		header("Content-Disposition: attachment; filename=newsletter_registing_customers_sample.csv");
		$f = fopen('php://output', 'w');
		ob_end_clean();
		// fwrite($f, "\xEF\xBB\xBF");
		$this->_fwriteBOM($f);

		$extensions = $this->request->query('extensions', NULL);
		$project_id = $this->request->query('project_id', NULL);	
		if ($project_id == NULL) {
			$project_id = 0;
		}
		$all_extend_columns = $this->_model->list_mail_member_extend_column_names($this->_bot_id, $project_id, true);
		$this->_logEcho('---------getting sample of csv data columns of project '.$project_id.': '.implode('||', array_values($all_extend_columns)).' ----------', true);
		$extend_columns = [];
		$head_line = ['メールアドレス', '氏名'];
		$sample_line1 = ['<EMAIL>', 'Mr. Sample'];
		$sample_line2 = ['<EMAIL>', 'サンプルさん'];
		$extend_sample_data = [];
		foreach($all_extend_columns as $extend_no=>$extend_name) {
			if ( $extensions && is_array($extensions) ) {
				if ( ! in_array($extend_no, $extensions) ) {
					continue;
				}
			} 
			$extend_columns []= $extend_name;
			array_push($head_line, $extend_name);
			$extend_sample_data []= $extend_name.'情報';
			// 空欄にします blank grid
			array_push($sample_line1, '');
			array_push($sample_line2, '');
		}

		array_push($head_line, '日付（省略可）');
		array_push($sample_line1, '2023-06-15');
		array_push($sample_line2, '2023-05-30');

		fputcsv($f, $head_line);
		fputcsv($f, $sample_line1);
		fputcsv($f, $sample_line2);

		$duplicates = $this->request->query('duplicates', NULL);		
		$suff = $this->request->query('suffix', NULL);	
		if ( $duplicates && is_numeric($duplicates) ) {
			$index = 0;
			$suffix = '';
			if ($suff) {
				$suffix = '.'.$suff;
			}
			for ( $index; $index<$duplicates; $index++ ) {
				$sample_line = ['sample'.$index.'@activalues.com.test'.$suffix, 'サンプルさん'.$index];
				foreach ($extend_sample_data as $extend_attribute) {
					array_push($sample_line, $extend_attribute.$index);
				}
				array_push($sample_line, '');
				// fputcsv($f, ['sample'.$index.'@activalues.com.test'.$suffix, 'サンプルさん'.$index, '']);
				fputcsv($f, $sample_line);
			}
		}

		fclose($f);
	}

	public function action_get_mail_members_import_task() {

		$import_no = $this->request->query('import_no', NULL);		
		if ($import_no == NULL || is_numeric($import_no) ) {
			$msg = '`import_no` not set or invalid';
			$this->response->body(json_encode(['success'=>false, 'msg'=>$msg]));
		}
		$import = ORM::factory('mailimporthistory', $import_no);
		// $this->_logEcho('---------getting csv data import task '.$import_no.' ----------', true);
		$info = json_decode($import->import_info);
		$this->_logEcho('---------getting progress of csv data import task, info '.json_encode($info).' ----------', true);

		$response['success'] = true;
		$response['import_no'] = $import_no;
		$response['status'] = $import->import_status;
		$response['upd_time'] = $import->upd_time;
		$response['info'] = $info;
		$this->response->body( json_encode( $response ) );
	}

	private function _regist_mail_members_import_task($post) {
		$import = ORM::factory('mailimporthistory');
		$import->upd_user = $this->_user_id;
		$this->_logEcho('---------registing csv data import task----------', true);
		if ( isset($post['import_name']) && !empty($post['import_name']) ) {
			$import->import_name = $post['import_name'];
		}
		if ( isset($post['import_count']) && is_numeric($post['import_count']) && $post['import_count'] > 0 ) {
			$import_info = ['total'=>intval($post['import_count']), 'imported'=>0, 'msg'=>'Starting...', 'registed_time'=>date('Y-m-d H:i:s')];
			if ( isset($post['csv_file_name']) && $post['csv_file_name'] ) {
				$import_info['csv_file_name'] = $post['csv_file_name'];
			}
			if ( isset($post['project_id']) && !empty($post['project_id']) ) {
				$import_info['project_id'] = $post['project_id'];
			}
			$import->import_info = json_encode($import_info);
		}
		return $import->save()->import_no;
	}

	public function action_regist_mail_members_import_task() {
		$post = $this->request->post();
		
		$import_no = $this->_regist_mail_members_import_task($post);

		$post['success'] = true;
		$post['import_no'] = $import_no;
		$this->response->body( json_encode( $post ) );
	}

	private function _update_mail_members_import_task($import_no, $import_status, $imported, $msg=null, $exclude=0, $skip=0) {
		$import = ORM::factory('mailimporthistory', $import_no);
		// $this->_logEcho('---------updating csv data import task '.$import_no.' ----------', true);
		if ( isset($import->import_info) && $import->import_info ) {
			$info = json_decode($import->import_info);
		} 
		if ( !isset($info) ) {
			$info = new stdClass();
		}
		$info->imported = $imported;
		if ( $msg ) {
			$info->msg = $msg;
		}
		if ( $exclude ) {
			$info->exclude = $exclude;
		}
		if ( $skip ) {
			$info->skip = $skip;
		}

		$import->import_status = $import_status;
		if ($import_status > 2) {
			$info->end_time = date('Y-m-d H:i:s');
		}
		$import->import_info = json_encode($info);
		$import->upd_time = date('Y-m-d H:i:s');
		$import->save();
		return $info;
	}

	public function action_import_previewed_csv() {
		$post = $this->request->post();
		if(isset($post['extends']))	$extends = $post['extends'];
		if(isset($post['headers']))	$headers = $post['headers'];
		$data = $post['data'];
		$valid_data = false;
		if ( !isset($data) ) {
			$msg = '`data` not set in post';
		} else if ( !is_array($data) ) {
			$msg = '`data` not valid in post';
			if ( is_string($data) && ! empty($data) ) {
				$data = json_decode($data);
				if ( $data != null && is_array($data) ) {
					$valid_data = true;
				}
			}
		} else {
			$valid_data = true;
		}
		if ( !$valid_data ) {
			$status = 5;
			$this->response->body(json_encode(['success'=>false, 'msg'=>$msg]));
			return;
		} else if ( count($data) <= 0 ) {
			$msg = '`data` empty in post';
			$status = 5;
			$this->response->body(json_encode(['success'=>false, 'msg'=>$msg]));
			return;
		}

		$count = count($data);
		// $status = 0;	// registed

		$detach = false;
		if ( isset($post['import_no']) && is_numeric($post['import_no']) ) {
			$import_no = $post['import_no'];
			$detach = true;
		} else if ( isset($post['import_no']) && !is_numeric($post['import_no']) ){
			$msg = 'invalid import_no '.$post['import_no'];
			$status = 5;
			$this->response->body(json_encode(['success'=>false, 'msg'=>$msg]));
			return;
		} else {
			$post['count'] = $count;
			$import_no = $this->_regist_mail_members_import_task($post);
		}

		// $bot_id =  $post['bot_id'];
		$bot_id = $this->_bot_id;	//	17;
		// error_log('---------importing csv data via preview----------');
		$this->_logEcho('---------importing csv data via preview----------', true);
		// $this->_logEcho(var_export($post['bots']), true);
		if ( isset($post['bots']) && is_array($post['bots']) && isset($post['bots'][0]) ) {
			$bot_id =  $post['bots'][0]['code'];
			// error_log(var_export($post['bots'][0]));
			$this->_logEcho($post['bots'][0]['code'], true);
		} else if( isset($post['bots']) && is_numeric($post['bots']) ) {
			$bot_id =  $post['bots'];
			$this->_logEcho($post['bots'], true);
		}

		//fetch project setting
		$project_id = NULL;
		if ( isset($post['project_id']) && $post['project_id'] ) {
			$project_id = $post['project_id'];
		}
		
		// if ( isset($post['detach']) && $post['detach'] == 'true' )
		if ( $detach ) 
		{
		// 	$this->response->body(json_encode(['success'=>true, 'bot_id'=>$bot_id, 'import_no' => $import_no, 'count' => $count]));
			// Session::instance()->write();
			session_write_close();
		}

		$msg = 'Starting...';
		$status = 1;	// Starting
		$exclude_count = 0;
		$skip = 0;
		$imported = 0;
		$msg = 'Checking...';
		// $index = 2;
		$upd = time();

		$emails = [];
		$duplicates_indices = [];
		
		$email_index = 0;
		$name_index = 1;
		$regist_index = 2;
		if(isset($headers) && $headers) {
			$email_index = $headers['email'];
			$name_index = $headers['name'];
			if ( isset($headers['regist_date']) ) {
				$regist_index = $headers['regist_date'];
			} else {
				$regist_index = -1;
			}
		}

		foreach ($data as $index=>$line) {
			$email = strtolower($line[$email_index]);
			if ( ! in_array($email, $emails) ) {
				$emails []= $email;
				// $this->_logEcho("[import$import_no] newsletter address  unique?: $line[$email_index]", true);
			} else {
				//TODO inform about duplicates in CSV (comparison scope within CSV)
				// $line[5] = 'true';	//	does not work	传值引用 
				// $data[$index][5]
				$duplicates_indices []= $index;
			}	
		}
		$duplicates = [];
		if ($emails) {
			$duplicates_sql = db::select('email')
				->from('t_mail_member')
				->where('email', 'IN', $emails)
				->where('bot_id', '=', $this->_bot_id);
			if ( $project_id && $project_id != 'other' ) {
				$duplicates_sql->where('project_id', '=', $project_id );
			} else {
				$duplicates_sql->where('project_id', 'IS', NULL);
			}
				$duplicates = $duplicates_sql
				->execute()->as_array('email');	
		}

		// $dupli = json_encode(array_keys($duplicates));
		$status = 2;	// Importing
		$msg = 'Importing...';
		$info = $this->_update_mail_members_import_task($import_no, $status, $imported, $msg, $exclude_count, $skip);
		$upd = time();
		// $this->_logEcho("[transaction] newsletter address  excluded?: $dupli", true);

		$email_line_mapping = [];
		//TODO batch insert
		$db = Database::instance();
		$db->begin();
		try {
			// insert mail members
			$members_insert_query = db::insert('t_mail_member', array('bot_id', 'first_name', 'email', 'regist_time', 'import_no', 'project_id'));
			$insert_count = 0;
			// insert mail_member_id entries
			$members_bot_insert_query = db::insert('t_mail_member_bot', array('mail_member_id', 'bot_id'));


			foreach ($data as $index=>$line) {
				$excluded = false;
				if ( isset($line[4]) ) {
					$l4type = gettype($line[4]);
					// $this->_logEcho("newsletter address excluded? $line[4], type $l4type", true);
					if ( $l4type == 'string') {
						$excluded = $line[4] == 'true';
					}
				}

				if ( in_array($index, $duplicates_indices) ) {
					// $this->_logEcho("newsletter address duplicates skip? $line[5]", true);
					$count --;
					$skip ++;
					continue;
				}
				$email = strtolower($line[$email_index]);
				if ( $excluded ) {
					// $this->_logEcho("newsletter address excluded?: $line[4] with name $line[1]", true);
					$count --;
					$exclude_count ++;
				} else if ( $duplicates && count($duplicates) > 0  && isset($duplicates[strtolower($email)]) ) {//skipped?
					$count --;
					$skip ++;
					continue;
				} else {
					//	check if already exists, previously checked in batch		
					
					//	filling values in insert sql of t_mail_member
					$regist_date = date('Y-m-d H:i:s');
					if( $regist_index >= 0 && $line[$regist_index] ) {
						$timeRegist = strtotime($line[$regist_index]);
						if ($timeRegist) {
							$regist_date = date('Y-m-d', $timeRegist);
						}
					}
					// $member_name = strtolower($line[$name_index]);
					$member_name = $line[$name_index];
					$email_line_mapping[$email] = $line;

					$members_insert_query->values(array($this->_bot_id, $member_name, $email, $regist_date, $import_no, $project_id));
					$insert_count ++;
					$imported ++;
					if ($insert_count > 999) {
						$members_inserted_result = $members_insert_query->execute();
						$this->_logEcho("[import$import_no] newsletter address bulk inserted count?: $members_inserted_result[1]", true);
						$status = 2;	// Importing
						$info = $this->_update_mail_members_import_task($import_no, $status, $imported, $msg, $exclude_count, $skip);
						$upd = time();
						$members_insert_query = db::insert('t_mail_member', array('bot_id', 'first_name', 'email', 'regist_time', 'import_no', 'project_id'));
						$insert_count = 0;
						
						if(time() - $upd > 1) {
							$status = 2;	// Importing
							$info = $this->_update_mail_members_import_task($import_no, $status, $imported, $msg, $exclude_count, $skip);
							$upd = time();
						}
					}
				}
				// $index ++;
			}
			if ($insert_count > 0) {
				$members_inserted_result = $members_insert_query->execute();
				$insert_count = 0;
				$this->_logEcho("[import$import_no] newsletter address final bulk inserted count?: $members_inserted_result[1]", true);
			}

			$members_inserted_select_query = db::select('mail_member_id', DB::expr($bot_id))
				->from('t_mail_member')
				->where('import_no', '=', $import_no)
				// ->execute()->as_array()
				;	
			// $inserted_json = json_encode($members_inserted);
			// $this->_logEcho("[transaction]validate newsletter address bulk inserted?: $inserted_json", true);
			$members_bot_inserted_result = $members_bot_insert_query->select($members_inserted_select_query)->execute();
			$this->_logEcho("[import$import_no] newsletter member/bot relation bulk inserted count?: $members_bot_inserted_result[1]", true);

			

			// insert extend attributes
			
			if(isset($extends) && $extends) {
				$this->_logEcho("[import$import_no] newsletter bulk inserted extends?: ".json_encode($extends), true);
				$members_inserted_array = db::select('mail_member_id', 'email')
					->from('t_mail_member')
					->where('import_no', '=', $import_no)
					->execute()->as_array('email', 'mail_member_id')
					;
				// insert t_mail_member_extend
				$members_extend_insert_query = db::insert('t_mail_member_extend', array('mail_member_id', 'extend_no', 'value'));
				foreach($members_inserted_array as $email=>$mail_member_id){
					if ( isset($email_line_mapping[$email]) ) {
						$extends_data = $email_line_mapping[$email];
						foreach($extends as $extend) {
							$extend_data = trim($extends_data[$extend['index']]);
							if ($extend_data != '') {
								// $this->_logEcho("[import$import_no] newsletter bulk inserted extend attr?:" . $mail_member_id . '-' . $extend['extend_no'].'='.$extend_data, true);
								$members_extend_insert_query->values(array($mail_member_id, $extend['extend_no'], $extend_data));
								$insert_count ++;
								// $imported ++;
								if ($insert_count > 999) {
									$extends_inserted_result = $members_extend_insert_query->execute();
									$this->_logEcho("[import$import_no] newsletter $email extend attributes bulk inserted count?: $extends_inserted_result[1]", true);
									$status = 2;	// Importing
									$info = $this->_update_mail_members_import_task($import_no, $status, $imported, $msg, $exclude_count, $skip);
									$upd = time();
									$members_extend_insert_query = db::insert('t_mail_member_extend', array('mail_member_id', 'extend_no', 'value'));
									$insert_count = 0;
	
									if(time() - $upd > 1) {
										$status = 2;	// Importing
										$info = $this->_update_mail_members_import_task($import_no, $status, $imported, $msg, $exclude_count, $skip);
										$upd = time();
									}
								}
							}
						}
					}
					
				}
				
				if ($insert_count > 0) {
					$extends_inserted_result = $members_extend_insert_query->execute();
					$insert_count = 0;
					$this->_logEcho("[import$import_no] newsletter $email extend attributes final bulk inserted count?: $extends_inserted_result[1]", true);
				}
			}
			// $this->_logEcho("[import$import_no] newsletter bulk inserted committing....", true);
			$db->commit();

		} catch(Exception $e){
			$db->rollback();
			$this->_logEcho("[ERR][import$import_no] newsletter bulk inserted rollback...." . ($e->getMessage()), true);
			$this->response->body(json_encode(['success'=>false, 'msg'=>$e->getMessage()]));
			return;
		}

		$msg = 'Completed';
		$status = 3;
		$info = $this->_update_mail_members_import_task($import_no, $status, $imported, $msg, $exclude_count, $skip);
		$msg = 'Imported '.$imported.' rows';
		// $this->_model->log_debug('ajax.newsletter_import', $msg, $bot_id);
		// $this->response->body(json_encode(['success'=>true, 'bot_id'=>$bot_id, 'count'=>$count, 'msg'=>$msg]));
		// $this->_logEcho('---------updating csv data import finished task, info '.json_encode($info).' ----------', true);
		$this->response->body(json_encode(['success'=>true, 'import_no' => $import_no, 'bot_id'=>$bot_id, 'info' => $info, 'status' => $status]));
	}

	public function action_delete_mail_task()
	{
		$mail_task_id = $this->request->query('mail_task_id', NULL);
		
		$task = ORM::factory('mailtask', $mail_task_id);
		$msg = 'Deleting ';
		if ( $task->mail_task_id == $mail_task_id ) {	//&& $task->status ? 
			//deleting relations between task and members for recipient
			$db = Database::instance();
			$db->begin();
			try {
				$regist_task_data = ['mail_task_id'=>(int)$mail_task_id];
				$regist_name = 'メルマガ';
				$regist_type_cd = '05';
				$regist_program = 'newsletter';
				$delete_result = db::delete('t_task')
					->where('task_status_cd', 'IN', ['00', '01'])
					->where('task_type_cd', '=', $regist_type_cd)
					->where('task_data', '=', json_encode($regist_task_data))
					->where('task_name', '=', $regist_name)
					->where('create_program', '=', $regist_program)
					->execute();

				$this->_logEcho('--------- deleted regist task for cron service ----------', true);

				$delete_result = db::delete('t_mail_task_recipient')
							->where('mail_task_id', '=', $mail_task_id)
							->execute();

				
				$this->_logEcho('--------- deleted(clean up) mail task recipient relations ----------', true);
				$this->_logEcho(json_encode($delete_result), true);
				$task->delete();	//	TODO pseudo skipping for now
				
				$db->commit();

				// $next_task_id = DB::select(array(DB::expr('MAX(mail_task_id) + 1'), 'next_id'))->from('t_mail_task')->execute()->get('next_id', 0);
				// if ($next_task_id > 0) {

				// }

			} catch(Exception $e){
				$db->rollback();
				$this->response->body(json_encode(['success'=>false, 'msg'=>$e->getMessage()]));
			}
		} else {
			$msg = 'requested mail_task_id not consistant with loaded orm '.$task->mail_task_id;
			$this->response->body(json_encode(['success'=>false, 'msg'=>$msg]));
		}

		$this->response->body(json_encode(['success'=>true, 'mail_task_id'=>$mail_task_id]));
	}

	private function _is_json($string) {
		json_decode($string);
		return json_last_error() === JSON_ERROR_NONE;
	 }
	 
	public function action_delete_mail_members()
	{
		$post = $this->request->post();
		if ( isset($post) && isset($post['mail_member_ids']) ){
			$member_id_list = $post['mail_member_ids'];
			// $this->_logEcho('---------deleting mail members----------'.implode(",", $member_id_list), true);
			if ( is_array($member_id_list) && count($member_id_list) > 0 ) {
				$count = 0;
				$msg = 'Deleting ';
				foreach ($member_id_list as $member_id) {
					$mail_member = ORM::factory('mailmember', $member_id);
					if ( $mail_member->mail_member_id == $member_id ) {

						//deleting relation between tag and members
						$delete_result = DB::delete('t_mail_member_tag')
							->where('mail_member_id', '=', $member_id)
							->execute();
						$this->_logEcho('--------- deleted(clean up) mail member tag relation(s) ----------', true);
						$this->_logEcho(json_encode($delete_result), true);

						
						//deleting relation between bot_id and members
						$delete_result = DB::delete('t_mail_member_bot')
							->where('mail_member_id', '=', $member_id)
							->execute();
						$this->_logEcho('--------- deleted(clean up) mail member bot relation(s) ----------', true);
						$this->_logEcho(json_encode($delete_result), true);

						//TODO deleting relation between task and members in recipients if task not executed yet
						//OR to mark member delete flag
						$delete_result = DB::delete('t_mail_task_recipient')
						->where('mail_member_id', '=', $member_id)
						->where('send_status', '=', 0)
						->execute();
						$this->_logEcho('--------- deleted(clean up) mail member task relation(s) ----------', true);
						$this->_logEcho(json_encode($delete_result), true);

						$mail_member->delete();	//	TODO pseudo skipping for now
						// $this->_logEcho('---------pseudo deleting mail member----------'.$member_id, true);
						$count ++;
					}
				}
				$this->response->body(json_encode(['success'=>true, 'count'=>$count, 'msg'=>$msg]));
			}  else if ( ! is_array($member_id_list) && is_string($member_id_list)) {
				$member_id_list = json_decode($member_id_list);
				if (json_last_error() === JSON_ERROR_NONE && is_array($member_id_list)  && count($member_id_list) > 0 ) {
					//TODO delete on $member_id_list
					$db = Database::instance();
					$db->begin();
					try {
						$this->_logEcho('Batch deleting mail member(s): '.count($member_id_list), true);
						//deleting relation between task and members in recipients if task not executed yet
						$delete_result = db::delete('t_mail_task_recipient')
									->where('mail_member_id', 'IN', $member_id_list)
									->where('send_status', '=', 0)	// TODO hint in task that members been deleted
									->execute();
						$this->_logEcho('Deleted(clean up) mail member task relation(s): '.$delete_result, true);

						//deleting relation between bot_id and members
						$delete_result = db::delete('t_mail_member_bot')
							->where('mail_member_id', 'IN', $member_id_list)
							->execute();
						$this->_logEcho('Deleted(clean up) mail member bot relation(s): '.$delete_result, true);

						//deleting relation between tag and members
						$delete_result = db::delete('t_mail_member_tag')
							->where('mail_member_id', 'IN', $member_id_list)
							->execute();
						$this->_logEcho('Deleted(clean up) mail member tag relation(s): '.$delete_result, true);

						//deleting relation between extended attributes and members
						$delete_result = db::delete('t_mail_member_extend')
							->where('mail_member_id', 'IN', $member_id_list)
							->execute();
						$this->_logEcho('Deleted(clean up) mail member extended attributes relation(s): '.$delete_result, true);

						//deleting members
						$delete_result = db::delete('t_mail_member')
							->where('mail_member_id', 'IN', $member_id_list)
							->execute();
						$this->_logEcho('Deleted mail member(s): '.$delete_result, true);

						$db->commit();
						$msg = 'Deleted '.$delete_result;
						$this->response->body(json_encode(['success'=>true, 'count'=>$delete_result, 'msg'=>$msg]));
					} catch(Exception $e){
						$db->rollback();
						$this->_logEcho('[ERR]Deleting(clean up) mail members exception: '.$e->getMessage(), true);
						$this->response->body(json_encode(['success'=>false, 'msg'=>$e->getMessage()]));
					}
				}
			} else {
				$msg = 'requested mail_member_ids not an array or empty';
				$this->response->body(json_encode(['success'=>false, 'msg'=>$msg]));
			}
		} else {
			$msg = 'no post requested or no mail_member_ids field';
			$this->response->body(json_encode(['success'=>false, 'msg'=>$msg]));
		}
	}
	
	private function _attach_mail_tag($mail_member_id, $tag_no)
	{
		if ( $mail_member_id != NULL && $tag_no != NULL ) {
			//	insert into member-tag link table
			$mail_member_tag = ORM::factory('mailmembertag');
			$mail_member_tag->mail_member_id = $mail_member_id;
			$mail_member_tag->tag_no = $tag_no;

			$mail_member_tag->save();
			return true;
		} else return false;
	}

	
	public function action_attach_mail_member_tags()
	{
		$post = $this->request->post();
		if ( isset($post) && isset($post['mail_member_tag_nos']) ){
			$tag_no_list = $post['mail_member_tag_nos'];
			if ( isset($post['mail_member_tag_nos']) && $post['mail_member_tag_nos'] != NULL && ! empty($post['mail_member_tag_nos']) ) {
				$tag_no_list = json_decode($post['mail_member_tag_nos']); 
			} else {
				$msg = 'at least 1 member in array mail_member_tag_nos needed ';
				$this->response->body(json_encode(['success'=>false, 'msg'=>$msg]));
				return;
			}
			$mail_member_ids =  $post['mail_member_ids'];
			if ( isset($post['mail_member_ids']) && $post['mail_member_ids'] != NULL && ! empty($post['mail_member_ids']) ) {
				$mail_member_ids = json_decode($post['mail_member_ids']); 
			} else {
				$msg = 'at least 1 member_id in array mail_member_ids needed ';
				$this->response->body(json_encode(['success'=>false, 'msg'=>$msg]));
				return;
			}
			if ( isset($mail_member_ids) && $mail_member_ids != NULL && is_array($mail_member_ids) && count($mail_member_ids) > 0 ) {
				if ( is_array($tag_no_list) && count($tag_no_list) > 0 ) {
					$count = 0;
					$msg = 'inserting ';
					
					if ( isset($post['customized']) && $post['customized'] == 'true' ) {

						foreach ($mail_member_ids as $index=>$mail_member_id) {
							$msg .= ' <'.$mail_member_id.'> ';
							foreach ($tag_no_list[$index] as $tag_no) {
								if ( $this->_attach_mail_tag($mail_member_id, $tag_no) ) {
									// $this->_logEcho('---------customize attaching mail member tag ----------tag='.$tag_no, true);
									$msg .= $tag_no.', ';
									$count ++;
								}
							}
						}					
					} else if ( isset($post['customized']) && $post['customized'] == 'false' ){

						foreach ($mail_member_ids as $mail_member_id) {
							// $this->_logEcho('--------- attaching mail member tag ----------member='.$mail_member_id, true);
							$msg .= ' <'.$mail_member_id.'> ';
							foreach ($tag_no_list as $tag_no) {
								if ( $this->_attach_mail_tag($mail_member_id, $tag_no) ) {
									// $this->_logEcho('---------regular attaching mail member tag ----------tag='.$tag_no, true);
									$msg .= $tag_no.', ';
									$count ++;
								}
							}
						}
					} else {
						$msg = 'requested param customized unexpected value:'.$post['customized'];
						$this->response->body(json_encode(['success'=>false, 'count'=>$count, 'msg'=>$msg]));
					}
					$this->response->body(json_encode(['success'=>true, 'count'=>$count, 'msg'=>$msg]));
				} else {
					$msg = 'requested mail_member_tag_nos not an array or empty';
					$this->response->body(json_encode(['success'=>false, 'msg'=>$msg]));
				}
			}else {
				$msg = 'array mail_member_ids needed ';
				$this->response->body(json_encode(['success'=>false, 'msg'=>$msg]));
			}
		} else if ( isset($post) && isset($post['mail_members']) ){
			//TODO customized
			$count = 0;
			$msg = 'inserting ';
			
			// $members = json_decode($post['mail_members']);
			/*
			foreach ($members as $mail_member_id=>$tag_no_list) {
				// $this->_logEcho('--------- attaching mail member tag ----------member='.$mail_member_id, true);
				$msg .= ']M'.$mail_member_id.'[';
				foreach ($tag_no_list as $tag_no) {
					$msg .= ','.$tag_no;
					// if ( $this->_attach_mail_tag($mail_member_id, $tag_no) ) {
					// 	$this->_logEcho('--------- attaching mail member tag ----------tag='.$tag_no, true);
					// 	$msg .= $tag_no.', ';
					// 	$count ++;
					// }
				}
			}*/
			$this->response->body(json_encode(['success'=>true, 'msg'=>$msg.$post['mail_members']]));
		} else {
			$msg = 'no post requested or no mail_member_tag_nos field';
			$this->response->body(json_encode(['success'=>false, 'msg'=>$msg]));
		}
	}

	public function action_list_mail_tags()
	{
		$input = $this->request->post('input', NULL);
		$members = $this->request->post('members', NULL);
		$excludes = $this->request->post('excludes', NULL);
		$project_id = $this->request->post('project_id', NULL);
		$bot_id = $this->_bot_id;
		$orms = ORM::factory('mailtag')->where('bot_id', '=', $bot_id);
		if ($project_id) {
			if ( $project_id == 'other' ) {
				$orms = $orms->where('project_id', 'IS', NULL);
			} else {
				$orms = $orms->where('project_id', '=', $project_id);
			}
		}
		//TODO filter on input
		if ( isset($members) && $members != NULL && ! empty($members) ) {
			$mail_member_ids = json_decode($members); 
			if ( $mail_member_ids != NULL && is_array($mail_member_ids) && count($mail_member_ids) > 0 ) {
				$query_builder = DB::select('tag.tag_no', 'tag.tag_name')
					->from(DB::expr('t_mail_tag as tag'))
					->join(DB::expr('t_mail_member_tag as mt'), 'INNER')
					->on('tag.tag_no', '=','mt.tag_no')->group_by('mt.tag_no')
					->where('tag.bot_id', '=', $bot_id)
					->where('mt.mail_member_id', 'IN', $mail_member_ids);

				if ( $input != NULL && is_string($input) && ! empty($input) ) {
					$query_builder = $query_builder->where('tag.tag_name', 'LIKE', $input.'%');
				}

				// if ( $excludes != NULL && is_array($excludes) && count($excludes) > 0 ) {
				// 	$query_builder = $query_builder->where('tag.tag_no', 'NOT IN', $excludes);
				// }

				$tag_list = $query_builder	//TODO from selected members 
					->execute()->as_array('tag_no', 'tag_name');
			}
		}
		if ( ! isset($tag_list) ) {
			
			if ( $input != NULL && is_string($input) && ! empty($input) ) {
				$orms = $orms->where('tag_name', 'LIKE', $input.'%');
			}
			
			if ( isset($excludes) && $excludes != NULL && ! empty($excludes) ) {
				$tag_nos = json_decode($excludes); 
				if ( $tag_nos != NULL && is_array($tag_nos) && count($tag_nos) > 0 ) {
					$orms = $orms->where('tag_no', 'NOT IN', $tag_nos);
				}
			}
			$tag_list = $orms->find_all()->as_array('tag_no', 'tag_name');//;
		}
		$msg = 'Listing ';
		$this->_logEcho('---------listing mail member tags----------', true);

		$tags = [];
		foreach ($tag_list as $tag_no=>$tag_name) {
			$msg .= $tag_no.':'.$tag_name.', ';
			$tag = ['tag_no'=>$tag_no, 'tag_name'=>$tag_name];
			$tags []= $tag;
		}
		$this->_logEcho($msg, true);

		$this->response->body(json_encode(['success'=>true, 'count'=>count($tags), 'tags' => $tags]));
	}

		
	public function action_detach_mail_member_tags()
	{
		$post = $this->request->post();
		if ( isset($post) && isset($post['mail_member_tag_nos']) ){
			$tag_no_list = $post['mail_member_tag_nos'];
			if (isset($post['mail_member_tag_nos']) && $post['mail_member_tag_nos'] != NULL && ! empty($post['mail_member_tag_nos'])) {
				$tag_no_list = json_decode($post['mail_member_tag_nos']);
			} else {
				$msg = 'at least 1 member in array mail_member_tag_nos needed ';
				$this->response->body(json_encode(['success'=>false, 'msg'=>$msg]));
				return;
			}
			$mail_member_ids =  $post['mail_member_ids'];
			if (isset($post['mail_member_ids']) && $post['mail_member_ids'] != NULL && !empty($post['mail_member_ids'])) {
				$mail_member_ids = json_decode($post['mail_member_ids']);
			} else {
				$msg = 'at least 1 member_id in array mail_member_ids needed ';
				$this->response->body(json_encode(['success'=>false, 'msg'=>$msg]));
				return;
			}
			if (is_array($mail_member_ids) && count($mail_member_ids) > 0) {
				if (is_array($tag_no_list) && count($tag_no_list) > 0) {
					$msg = 'deleting ';
					$delete_result = DB::delete('t_mail_member_tag')
						->where('mail_member_id', 'IN', $mail_member_ids)
						->where('tag_no', 'IN', $tag_no_list)
						->execute();
					$this->_logEcho('--------- deleted mail member tag relation ----------', true);
					$this->_logEcho(json_encode($delete_result), true);
					$this->response->body(json_encode(['success'=>true, 'count'=>$delete_result, 'msg'=>$msg]));
				} else {
					$msg = 'requested mail_member_tag_nos not an array or empty';
					$this->response->body(json_encode(['success'=>false, 'msg'=>$msg]));
				}
			} else {
				$msg = 'requested mail_member_ids not an array or empty';
				$this->response->body(json_encode(['success'=>false, 'msg'=>$msg]));
			}
		} else {
			$msg = 'no post requested or no mail_member_tag_nos field';
			$this->response->body(json_encode(['success'=>false, 'msg'=>$msg]));
		}
	}
	
	public function action_delete_mail_tag()
	{
		$tag_no = $this->request->query('tag_no', NULL);
		
		$tag = ORM::factory('mailtag', $tag_no);
		$msg = 'Deleting ';
		if ( $tag->tag_no == $tag_no ) {
			//deleting relation between tag and members
			$db = Database::instance();
			$db->begin();
			try {
				$delete_result = db::delete('t_mail_member_tag')
							->where('tag_no', '=', $tag_no)
							->execute();
				$this->_logEcho('--------- deleted(clean up) mail member tag relation ----------', true);
				$this->_logEcho(json_encode($delete_result), true);
				$tag->delete();	// delete tag at last
				$db->commit();
			} catch(Exception $e){
				$db->rollback();
				$this->response->body(json_encode(['success'=>true, 'msg'=>$e->getMessage()]));
			}
		} else {
			$msg = 'requested tag_no not consistant with loaded orm '.$tag->tag_no;
			$this->response->body(json_encode(['success'=>false, 'msg'=>$msg]));
		}

		$this->response->body(json_encode(['success'=>true, 'tag_no'=>$tag_no]));
	}

	public function action_delete_mail_extend_alias()
	{
		$extend_no = $this->request->query('extend_no', NULL);
		$project_id = $this->request->query('project_id', NULL);
		if (!$project_id) {
			$project_id = 0;
		}
		// $extend = ORM::factory('mailextend', $extend_no);
		$extend = ORM::factory('mailextend')->where('extend_no', '=', $extend_no)
					->where('bot_id', '=', $this->_bot_id)
					->where('project_id', '=', $project_id)
					->find();
		$msg = 'Deleting ';
		if (!$extend->loaded()) {
			$msg = 'requested extend_no not found '.$extend_no;
			if ($project_id) {
				$msg .= ' in project '.$project_id;
			}
			$this->response->body(json_encode(['success'=>false, 'msg'=>$msg]));
			return;
		}
		if ( $extend->extend_no == $extend_no ) {
			// $this->_logEcho('--------- deleting mail extend alias----------'.$extend_no, true);
			if ( $extend_no >= 1 && $extend_no <= 10 ) {
				// clear alias
				// $msg = 'requested extend_no not supposed to be deleted '.$extend->extend_no;
				// $this->response->body(json_encode(['success'=>false, 'msg'=>$msg]));
				// return;
			}
			//no need to deleting relation between tag and members, clear alias only
			$db = Database::instance();
			$db->begin();
			try {
				$extend->delete();
				$db->commit();
			} catch(Exception $e){
				$db->rollback();
				$this->response->body(json_encode(['success'=>true, 'msg'=>$e->getMessage()]));
			}
		} else {
			$msg = 'requested extend_no not consistant with loaded orm '.$extend->extend_no;
			$this->response->body(json_encode(['success'=>false, 'msg'=>$msg]));
		}

		$this->response->body(json_encode(['success'=>true, 'extend_no'=>$extend_no]));
	}

	public function action_edit_mail_extend()
	{
		$post = $this->request->post();
		if ( isset($post) && isset($post['extend_no']) ){
			$extend_no = $post['extend_no'];
			$project_id = 0;
			if ( isset($post['project_id']) && ! empty($post['project_id']) ) {
				$project_id = $post['project_id'];
			}
			if ( isset($post['extend_name']) ) {
				$extend_name = $post['extend_name'];
				$msg = 'Updating ';
				$extend = ORM::factory('mailextend')->where('extend_no', '=', $extend_no)
					->where('bot_id', '=', $this->_bot_id)
					->where('project_id', '=', $project_id)
					->find();			
				if ( $extend && $extend->project_id == $project_id && $extend->extend_no == $extend_no && $extend->name != $extend_name) {
					$msg = 'Updating extend no '.$extend_no.' from '.$extend->name.' to '.$extend_name ;
					DB::update('t_mail_extend')
						->set(['name'=>$extend_name, 'upd_time'=>date('Y/m/d H:i:s', time())])
						->where('bot_id', '=', $this->_bot_id)
						->where('project_id', '=', $project_id)
						->where('extend_no', '=', $extend_no)
						->execute();

					$this->response->body(json_encode(['success'=>true, 'msg'=>$msg]));
				} else if ( !$extend || ( ! $extend->extend_no && !$extend->name )) {
					$msg = 'OverWriting default extend no '.$extend_no.' to '.$extend_name ;
					$extend->name = $extend_name;
					$extend->extend_no = $extend_no;
					$extend->upd_time = date('Y-m-d H:i:s');
					$extend->bot_id = $this->_bot_id;
					$extend->project_id = $project_id;
					$extend->save();
					$this->response->body(json_encode(['success'=>true, 'msg'=>$msg]));
				} else {
					$msg = 'Update exception: (proj '.$project_id.')expected extend no '.$extend_no.', got '.$extend->extend_no.';tried to edit extend from '.$extend->name.' to '.$extend_name ;
					$this->response->body(json_encode(['success'=>false, 'msg'=>$msg]));
				}
			} else if ( isset($post['tpl_param_alias']) ) {
				$tpl_param_alias = $post['tpl_param_alias'];
				$msg = 'Updating ';

				if ($tpl_param_alias) {
					$exist_alias_orm = ORM::factory('mailextend')->where('template_param', '=', $tpl_param_alias)->where('bot_id', '=', $this->_bot_id)->where('project_id', '=', $project_id)->find();				
					if ($exist_alias_orm && $exist_alias_orm->template_param == $tpl_param_alias ) {
						$msg = 'duplicated tpl_param_alias posted';
						$this->response->body(json_encode(['success'=>false, 'msg'=>$msg, 'err_cd'=>'duplicated_extend_tpl_alias']));
						return;
					}
				}
				
				$extend = ORM::factory('mailextend')->where('extend_no', '=', $extend_no)
					->where('bot_id', '=', $this->_bot_id)
					->where('project_id', '=', $project_id)
					->find();				

				if ( $extend && $extend->project_id == $project_id && $extend->extend_no == $extend_no && $extend->template_param != $tpl_param_alias) {
					$msg = 'Updating extend no '.$extend_no.' from '.$extend->template_param.' to '.$tpl_param_alias ;
					
					DB::update('t_mail_extend')
						->set(['template_param'=>$tpl_param_alias, 'upd_time'=>date('Y/m/d H:i:s', time())])
						->where('bot_id', '=', $this->_bot_id)
						->where('project_id', '=', $project_id)
						->where('extend_no', '=', $extend_no)
						->execute();

					$this->response->body(json_encode(['success'=>true, 'msg'=>$msg, 'project_id'=>$project_id, 'extend_no'=>$extend_no, 'name'=>$extend->name, 'template_param'=>$tpl_param_alias]));
				} else if ( !$extend || ( ! $extend->extend_no && !$extend->template_param )) {
					$msg = 'OverWriting default extend no '.$extend_no.' to '.$tpl_param_alias ;
					$extend->template_param = $tpl_param_alias;
					$extend->extend_no = $extend_no;
					$extend->upd_time = date('Y-m-d H:i:s');
					$extend->bot_id = $this->_bot_id;
					$extend->project_id = $project_id;
					$extend->save();
					$this->response->body(json_encode(['success'=>true, 'msg'=>$msg]));
				} else {
					$msg = 'Update exception: expected extend no '.$extend_no.', got '.$extend->extend_no.';tried to edit extend from '.$extend->template_param.' to '.$tpl_param_alias ;
					$this->response->body(json_encode(['success'=>false, 'msg'=>$msg]));
				}
			} else {
				$msg = 'no extend_name or tpl_param_alias posted';
				$this->response->body(json_encode(['success'=>false, 'msg'=>$msg]));
			}
		} else {
			$msg = 'no post requested or no extend_no posted';
			$this->response->body(json_encode(['success'=>false, 'msg'=>$msg]));
		}
	}

	public function action_edit_mail_tag()
	{
		// $tag_no = $this->request->query('tag_no', NULL);
		$post = $this->request->post();
		if ( isset($post) && isset($post['tag_no']) ){
			$tag_no = $post['tag_no'];
			if ( isset($post['tag_name']) ) {
				$tag_name = $post['tag_name'];
				$msg = 'Updating ';
				
				$tag = ORM::factory('mailtag', $tag_no);
				if ( $tag->tag_no == $tag_no && $tag->tag_name != $tag_name) {
					$msg = 'Updating tag no '.$tag_no.' from '.$tag->tag_name.' to '.$tag_name ;
					$tag->tag_name = $tag_name;
					$tag->upd_time = date('Y-m-d H:i:s');
					$tag->save();
					$this->response->body(json_encode(['success'=>true, 'msg'=>$msg]));
				} else {
					$msg = 'Update exception: expected tag no '.$tag_no.', got '.$tag->tag_no.';tried to edit tag from '.$tag->tag_name.' to '.$tag_name ;
					$this->response->body(json_encode(['success'=>false, 'msg'=>$msg]));
				}
			} else {
				$msg = 'no tag_name posted';
				$this->response->body(json_encode(['success'=>false, 'msg'=>$msg]));
			}
		} else {
			$msg = 'no post requested or no tag_no posted';
			$this->response->body(json_encode(['success'=>false, 'msg'=>$msg]));
		}
	}

	public function action_merge_mail_tags()
	{
		$post = $this->request->post();
		// $bot_id = $this->_bot_id;

		if ( isset($post) && isset($post['mail_tag_nos']) && isset($post['new_tag_name']) ){
			$mail_tag_no_list = $post['mail_tag_nos'];
			$new_tag_name = $post['new_tag_name'];
			// $this->_logEcho('---------merging mail tags----------'.implode(",", $mail_tag_no_list), true);
			if ( is_array($mail_tag_no_list) && count($mail_tag_no_list) > 1 &&  ! empty($new_tag_name) ) {
				$count = 0;
				// $msg = 'Inserting new tag...';
				$this->_logEcho('Merging mail tags['.implode(",", $mail_tag_no_list).']into '.$new_tag_name, true);
				$db = Database::instance();
				$db->begin();
				try {
					if ( isset($post['new_tag_no']) && $post['new_tag_no'] != null ) {
						$target_tag_no = db::expr($post['new_tag_no']);
						if ( in_array($post['new_tag_no'], $mail_tag_no_list) ) {
							$target_index = array_search($post['new_tag_no'], $mail_tag_no_list);
							unset($mail_tag_no_list[$target_index]);
							$msg = 'Merging to one of source tags, index:'.$target_index.', tag_no:'.$target_tag_no;
							$this->_logEcho($msg, true);
						}
					} else {
						$first_tag_no = array_shift($mail_tag_no_list);
						$target_tag_no = db::expr($first_tag_no);

						$msg = 'Updating first tag...';
						$tag = ORM::factory('mailtag', $first_tag_no);
						if ( $tag->tag_no == $first_tag_no && $tag->tag_name != $new_tag_name) {
							$msg = 'Updating tag no '.$first_tag_no.' from '.$tag->tag_name.' to '.$new_tag_name ;
							$tag->tag_name = $new_tag_name;
							$tag->upd_time = date('Y-m-d H:i:s');
							$tag->save();
							$msg = 'Updated tag no '.$first_tag_no.' from '.$tag->tag_name.' to '.$new_tag_name ;
							$this->_logEcho($msg, true);
						} else if ( $tag->tag_name == $new_tag_name ) {
							$msg = 'Skip Update tag: no '.$first_tag_no.', merge to first tag; tried to update tag from '.$tag->tag_name.' to '.$new_tag_name ;
							$this->_logEcho($msg, true);
						} else {
							$msg = 'Update exception: expected tag no '.$first_tag_no.', got '.$tag->tag_no.';tried to edit tag from '.$tag->tag_name.' to '.$new_tag_name ;
							$this->_logEcho($msg, true);
							$this->response->body(json_encode(['success'=>false, 'msg'=>$msg]));
							return;
						}
					}
					
					
					$msg = 'Merging other tags\' relations to target one, tag_no:'.$target_tag_no.'['.$target_tag_no;
					$this->_logEcho($msg, true);
					$source_tag_no_list = $mail_tag_no_list;
					$sub_sub_query_member_ids = db::select('mail_member_id')->from('t_mail_member_tag')->where('tag_no', '=', $target_tag_no);
					$sub_query_member_ids = db::select('mail_member_id', $target_tag_no)->distinct(true)->from('t_mail_member_tag')
						->where('tag_no', 'IN', $source_tag_no_list)->where('mail_member_id', 'NOT IN', $sub_sub_query_member_ids);
					db::insert('t_mail_member_tag', ['mail_member_id', 'tag_no'])->select($sub_query_member_ids)->execute();
					$msg = 'Copied other tags\' relations target one...';
					$this->_logEcho($msg, true);

					$msg = 'Cleaning other tags\' relations...';
					$this->_logEcho($msg, true);
					$relations_delete_result = db::delete('t_mail_member_tag')
						->where('tag_no', 'IN', $source_tag_no_list)->execute();
					$msg = 'Cleaned other tags\' relations, count '.$relations_delete_result;
					$this->_logEcho($msg, true);

					$msg = 'Deleting other tags...';
					$this->_logEcho($msg, true);
					$delete_result = db::delete('t_mail_tag')
							->where('tag_no', 'IN', $source_tag_no_list)
							->execute();
					$msg = 'Deleted other tags, count '.$delete_result;
					$this->_logEcho($msg, true);
					
					$db->commit();
					$this->response->body(json_encode(['success'=>true, 'count'=>$count, 'msg'=>$msg]));
				} catch(Exception $e){
					// and catch whatever exceptions too
					// or your rollback is blown in the wind
					$db->rollback();
					// throw $e;
					$this->response->body(json_encode(['success'=>false, 'count'=>$count, 'msg'=>$e->getMessage()]));
				}
				
			} else {
				$msg = 'requested `mail_tag_no_list` not an array with two or more tags or `new_tag_name` empty';
				$this->response->body(json_encode(['success'=>false, 'msg'=>$msg]));
			}
		} else {
			$msg = 'no post requested or no mail_member_ids field';
			$this->response->body(json_encode(['success'=>false, 'msg'=>$msg]));
		}
	}

	public function action_create_mail_member_extend()
	{
		$post = $this->request->post();
		$bot_id = $this->_bot_id;
		if ( isset($post) && isset($post['alias']) && isset($post['extend_no']) && $post['extend_no'] ) {
			$this->_logEcho('---------ajax post to create mail member extend requested----------', true);
			$alias = $post['alias'];
			$extend_no = $post['extend_no']; 
			$msg = 'inserting ';
			if ( is_string($alias) && !empty($alias) && $alias != '' ) {
				if ( $extend_no = $this->_create_mail_extend_alias($bot_id, $extend_no, $alias) ) {
					$this->_logEcho('---------inserting mail member extend----------'.$extend_no, true);
					$msg .= $extend_no;
					$this->response->body(json_encode(['success'=>true, 'extend_no'=>$extend_no, 'alias'=>$alias, 'msg'=>$msg]));
				} else {
					$msg = 'failed _create_mail_extend_alias';
					$this->response->body(json_encode(['success'=>false, 'msg'=>$msg]));
				}
			} else {
				$msg = 'requested extend column alias empty '.$alias;
				$this->response->body(json_encode(['success'=>false, 'msg'=>$msg]));
			}
		} else {
			$msg = 'no post requested or no mail_member_tags field';
			$this->response->body(json_encode(['success'=>false, 'msg'=>$msg]));
		}
	}
	
	private function _create_mail_extend_alias($bot_id, $extend_no, $extend_alias)
	{
		if ( ! empty($extend_alias) && $extend_alias != '' && $extend_no > 10 ) {
			//	insert into tag table
			$extend = ORM::factory('mailextend', $extend_no);
			if ( $extend->extend_no == $extend_no ) {
				// extend alias with this $extend_no already exist
				return -2;
			}
			$extend->extend_no = $extend_no;
			$extend->bot_id = $bot_id;
			$extend->name = $extend_alias;
			$extend->upd_user = $this->_user_id;

			return $extend->save()->extend_no;
		} else return -1;
	}

	private function _create_mail_tag($bot_id, $tag_name, $project_id = NULL)
	{
		if ( ! empty($tag_name) && $tag_name != '' ) {
			//	insert into tag table
			$mail_tag = ORM::factory('mailtag');
			$mail_tag->bot_id = $bot_id;
			$mail_tag->tag_name = $tag_name;
			$mail_tag->upd_user = $this->_user_id;

			$mail_tag->project_id = $project_id;

			return $mail_tag->save()->tag_no;
		} else return -1;
	}

	public function action_create_mail_member_tags()
	{
		$post = $this->request->post();
		$bot_id = $this->_bot_id;
		// 	$bot_id = $post['bot_id'];
		$project_id = NULL;
		if ( isset($post['project_id']) && $post['project_id'] ) {
			$project_id = $post['project_id'];
		}

		if ( isset($post) && isset($post['mail_member_tags']) ) {
			$this->_logEcho('---------ajax post to create mail member tags requested----------', true);

			$mail_member_tags = $post['mail_member_tags'];
			$msg = 'inserting ';
			$count = 0;
			$created_tags = [];
			if ( is_string($mail_member_tags) &&  ! empty($mail_member_tags) && $mail_member_tags != '' ) {
				$this->_logEcho('---------exploding mail member tags----------'.$mail_member_tags, true);
				$mail_member_tags =  explode(', ', $mail_member_tags);
				$this->_logEcho('---------exploded mail member tags----------'.implode(",", $mail_member_tags), true);
			}
			if ( is_array($mail_member_tags) && count($mail_member_tags) > 0 ) {
				$this->_logEcho('---------inserting mail member tags----------'.implode(",", $mail_member_tags), true);
				$msg = 'inserted tag_no [';
				$skipped = 0;
				foreach ($mail_member_tags as $tag) {
					if (ORM::factory('mailtag')->where('tag_name', '=', $tag)->where('bot_id', '=', $bot_id)->where('project_id', '=', $project_id)->find()->tag_no) {
						$msg .= 'tag '.$tag.' already exist, ';
						continue;
					}
					if ( $tag_no = $this->_create_mail_tag($bot_id, $tag, $project_id) ) {
						$this->_logEcho('---------pseudo inserted mail member tag----------'.$tag_no, true);
						$msg .= $tag_no.', ';
						$created_tags[] = $tag_no;
						$count ++;
					}
				}
				$msg .= ']';
				if ($count < 1) {
					$this->response->body(json_encode(['success'=>false, 'msg'=>$msg]));
				} else {
					$this->response->body(json_encode(['success'=>true, 'count'=>$count, 'skipped'=> $skipped, 'data'=>$created_tags, 'msg'=>$msg]));
				}
			// } else if ( is_string($mail_member_tags) && ($tag_no = $this->_create_mail_tag($bot_id, $mail_member_tags)) > 0 ) {
			// 	$tag_names =
			// 	$this->response->body(json_encode(['success'=>true, 'count'=>1, 'msg'=>"inserted tag_no $tag_no"]));
			} else {
				$msg = 'requested member_ids not an array or empty '.$mail_member_tags;
				$this->response->body(json_encode(['success'=>false, 'msg'=>$msg]));
				return;
			}
		} else {
			$msg = 'no post requested or no mail_member_tags field';
			$this->response->body(json_encode(['success'=>false, 'msg'=>$msg]));
		}
	}

	public function action_save_signature() {
		$post = $this->request->post();
		$signature_id = $this->request->query('id');
		$is_new = true;
		$signature = null;

		if ($signature_id) {
			$signature = $this->_model->get_signature($signature_id, $this->_bot_id);
			if (!$signature) {
				$msg = "Signature(ID:$signature_id) does not exist.";
				$this->response->body(json_encode(['success'=>false, 'msg'=>$msg]));
			}
			$is_new = false;
		}
		$signature_count = $this->_model->get_signature_count($this->_bot_id);
		$defaultNewSignatureLabel = __('admin.signature.label.signature') . ($is_new ? $signature_count + 1 : $signature_count);
		$signature_data = [
			'bot_id' => $this->_bot_id,
			'lang_cd' => $post['lang_cd'] ?? 'ja',
			'sign_title' => $post['sign_title'] ? $post['sign_title'] : $defaultNewSignatureLabel,
			'sign_detail' => isset($post['sign_detail']) ? $post['sign_detail'] : '',
			'upd_user' => $this->_user_id,
			'upd_time' => DB::expr('NOW()'),
		];

		if ($is_new) {
			// 新規作成
			$max_signature_id = $this->_model->get_max_signature_id() + 1;
			$signature_data['id'] = $max_signature_id;
			$this->_model->create_signature($signature_data);
			$this->response->body(json_encode(['success'=>true, 'data'=>$signature_data]));
		} else {
			// 更新
			$this->_model->update_signature($signature_id, $this->_bot_id, $signature_data);
			$signature_data['id'] = $signature_id;
			$this->response->body(json_encode(['success'=>true, 'data'=>$signature_data]));
		}
	}


	public function action_get_tpl_detail() {
		
		$msg_cd = $this->request->query('msg_cd', NULL);
		// $template_list = $this->_model->get_message_list($this->_bot_id, 'mal', '', '', 'ja', 'sys');
		// $item_data_def = $this->_model->get_item_tpl_message(35, 6, '', 'item_data_'.$msg_cd, $this->_lang_cd);
		//TODO first one?
		$parent_msgs = $this->_model->get_message_info_by_msg_cd($this->_bot_id, $msg_cd);
		$parent_msg = $parent_msgs[0];
		$msg_orm_name = $this->_model->get_message_tbl($parent_msg['msg_type_cd']);
		// $msg_desc = $this->_model->get_message_self_desc('sys', $msg_orm_name, $parent_msg['msg_id'], $this->_lang_cd);
		$msg_desc = ORM::factory($msg_orm_name)->
			where('msg_id', '=', $parent_msg['msg_id'])->
			where('lang_cd', '=', $this->_lang_cd)->find()->as_array();

		$this->response->body(json_encode(['success'=>true, 'msg_cd'=>$msg_cd
			// , 'raw_data'=>$parent_msgs
			// , 'raw_data_json'=>json_encode($parent_msgs)
			// , 'desc'=>($msg_desc)
			, 'title'=>($msg_desc['title'])
			, 'content'=>($msg_desc['content'])
			// , 'msg_orm_name'=>$msg_orm_name
			// , 'msg_type_cd'=>$parent_msg['msg_type_cd']
			// , 'msg_id'=>$parent_msg['msg_id']
		]));
	}

	private function _fwriteBOM($stream) {
		fwrite($stream, "\xEF\xBB\xBF");
	}

	private function _trimBOMLine($line) {
		return trim($line, "\xEF\xBB\xBF");
	}

	private function _checkBOMPrefix($first_line) {
		// $utf8bom = "\\xef\\xbb\\xbf";
		return $first_line[0]==0xEF && $first_line[1]==0xBB && $first_line[0]==0xBF;
	}

	private function _checkCSVExtendHeader($header, $extensions) {
		$extend_no = array_search($header, $extensions);
		if ($extend_no) {
			return $extend_no;
		}
		return $this->_model->check_mail_member_extend_column_name_default($header, $this->_bot_id);
	}

	private function _isCSVRegistDateHeader($header) {
		return str_starts_with($header, '日付') ||
			str_starts_with(strtolower($header), 'regist date');
	}

	private function _isCSVEmailHeader($header) {
		return $header == __('admin.common.label.mail.address') ||
			$header == 'メールアドレス' ||
			strtolower($header) == 'email';
	}

	private function _isCSVHeaderLine($data, $headers) {
		$email_index = 0;
		if($headers)
			$email_index = $headers['email'];
		return $this->_isCSVEmailHeader($data[$email_index]);
	}

	public function action_upload_csv_base64() {
		$post = $this->request->post();
		if (! $post['csv_base64'] ) {
			$this->_logEcho('action_upload_csv_base64 no data in csv_base64 ', true);
			$response = ['success'=>false, 'msg'=>'no base64 data of csv posted'];
		} else 
		{
			// if need to store temp csv file in server
			$temp_csv_enable = false;
			
			$upload_csv_stream = fopen($post['csv_base64'],'r');
			$filename = 'upload.csv';
			if ( isset($post['filename']) && $post['filename'] ) {
				$filename = $post['filename'];
			}
			if ($upload_csv_stream) {
				$temp_file_path = APPPATH."../../files/temp/member/".$filename;
				$this->_logEcho('_action_upload_csv_base64 $temp_file_path '.($temp_file_path).' ----------', true);

				$f_temp_csv = false;
				if ($temp_csv_enable) {
					$f_temp_csv = fopen( $temp_file_path, 'w' );
				}

				if ( $temp_csv_enable && !$f_temp_csv ) {
					$this->_logEcho('_action_upload_csv_base64 open $temp_file_path failed: '.($temp_file_path).' ----------', true);
					$response = ['success'=>false, 'msg'=>'cannot open temp csv file: '.$filename];
					$this->response->body(json_encode($response));
					return;
				}

				$csvdata = '';
				while (($csv_line = fgets($upload_csv_stream)) !== false) {
					if ( $temp_csv_enable ) {
						fputs($f_temp_csv, $csv_line);
					}
					$csvdata .= $csv_line.PHP_EOL;
				}
				fclose($upload_csv_stream);
				
				if ( $f_temp_csv ) {
					fclose($f_temp_csv);
				}

				$header = ['Content-Type: application/json; charset=UTF-8',];
				$crmimport_url = 'http://localhost:9501/service/crmimport';

				$child_bot_id = $this->_bot_id;
				$bot_id = $child_bot_id;
				$grp_bot_id = $this->_model->get_grp_bot_id($bot_id);
				if ( $grp_bot_id > 0 ) {
					$bot_id = $grp_bot_id;
				}
				$data = [
					'transaction_type' => 'allline',
					'bot_id' => $bot_id,
					'child_bot_id' => $child_bot_id,
				];
				if ( $temp_csv_enable && $f_temp_csv ) {
					$data['csvfilepath'] = $temp_file_path;
				} else {
					$data['csvdata'] = $csvdata;
				}
				$body = [
					'action' => 'csvimport',
					'data' => $data
				];

				// ticket https://support.activalues.com/issues/55310
				// special process for csv named basara_yoyakuban_member.csv
				if ($filename == "basara_yoyakuban_member.csv") {
					$data_1 = [
						'transaction_type' => 'oneline',
						'bot_id' => $bot_id,
						'child_bot_id' => $child_bot_id,
					];
					$csv_base64 = $post['csv_base64'];
					
					// Remove the initial data URI part and decode the Base64 string
					$encoded_csv_data = substr($csv_base64, strpos($csv_base64, ",") + 1);
					$decoded_csv_data = base64_decode($encoded_csv_data);

					// Set the detection order, including Shift-JIS
					mb_detect_order(['Shift-JIS', 'UTF-8']);

					$this->_logEcho('_action_upload_csv_base64 basara_yoyakuban_member.csv process encoding : '.(mb_detect_encoding($decoded_csv_data)), true);
					$utf8_csv_data = $decoded_csv_data;
					$this->_logEcho('_action_upload_csv_base64 basara_yoyakuban_member.csv process $decoded_csv_data : '.($decoded_csv_data), true);
					if (mb_detect_encoding($decoded_csv_data) == "SJIS") {
						$utf8_csv_data = mb_convert_encoding($decoded_csv_data, "UTF-8", "Shift-JIS");
					}
					$data_1['csvdata'] = $utf8_csv_data;
					$this->_logEcho('_action_upload_csv_base64 basara_yoyakuban_member.csv process encoding : '.(mb_detect_encoding($utf8_csv_data)), true);

					$body_1 = [
						'action' => 'csvimport_email',
						'data' => $data_1
					];
					$import_response = $this->_model->curl_post($crmimport_url, $header, $body_1);
				} elseif (strpos($filename, "sales.csv") !== false || strpos($filename, "伝票データ.csv") !== false) {
					$data_1 = [
						'transaction_type' => 'oneline',
						'bot_id' => $bot_id,
						'child_bot_id' => $child_bot_id,
					];
					$csv_base64 = $post['csv_base64'];
					// Remove the initial data URI part and decode the Base64 string
					$encoded_csv_data = substr($csv_base64, strpos($csv_base64, ",") + 1);
					$decoded_csv_data = base64_decode($encoded_csv_data);
					// Set the detection order, including Shift-JIS
					mb_detect_order(['Shift-JIS', 'UTF-8']);
					$this->_logEcho('_action_upload_csv_base64 sales.csv process encoding : '.(mb_detect_encoding($decoded_csv_data)), true);
					$utf8_csv_data = $decoded_csv_data;
					$this->_logEcho('_action_upload_csv_base64 sales.csv process $decoded_csv_data : '.($decoded_csv_data), true);
					if (mb_detect_encoding($decoded_csv_data) == "SJIS") {
						$utf8_csv_data = mb_convert_encoding($decoded_csv_data, "UTF-8", "Shift-JIS");
					}
					$data_1['csvdata'] = $utf8_csv_data;
					$this->_logEcho('_action_upload_csv_base64 sales.csv process encoding : '.(mb_detect_encoding($utf8_csv_data)), true);
					$body_1 = [
						'action' => 'csvimport_visit_earnings',
						'data' => $data_1
					];
					$import_response = $this->_model->curl_post($crmimport_url, $header, $body_1);
				} else {
					$import_response = $this->_model->curl_post($crmimport_url, $header, $body);
				}
				
				$this->_logEcho('_action_upload_csv_base64 crmimport response: '.($import_response), true);
				$import_result = json_decode($import_response, true);
				$success = false;
				if ($import_result['result'] == 'success') {
					$success = true;
				}
				$response = ['success'=>$success, 'import_result'=>$import_result];

				if ( $f_temp_csv ) {
					// Use unlink() function to delete a file 
					if (!unlink($temp_file_path)) { 
						$msg = ("$temp_file_path cannot be deleted due to an error"); 
					} 
					else { 
						$msg = ("$temp_file_path has been deleted"); 
					} 
					$this->_logEcho('_action_upload_csv_base64 crmimport delete temp csv file: '.($msg), true);
				}
			} else {
				$response = ['success'=>false, 'msg'=>'cannot open uploaded data stream'];
			}
		}
		$this->response->body(json_encode($response));
	}

	public function action_read_csv_base64() {
		$post = $this->request->post();
		// error_log('---------stream from string----------');
		$project_id = 0;
		if ( isset($post['project_id']) && $post['project_id'] ) {
			$project_id = $post['project_id'];
		}
		$csv_stream = fopen($post['csv_base64'],'r');

		$csv_matrix = [];
		$headers = null;
		if ($csv_stream) {
			//	typical column orders
			// 0.メールアドレス/email	1.氏名/name	2+.拡張項目Ｎ	2+N.日付（省略可）Regist Date
			/*
			$extensions = [
				1 => '拡張項目１',
				2 => '会社名２',
				3 => '拡張項目３別名',
				4 => '部署４',
				5 => '拡張項目５',
				6 => '職業６',
				7 => '拡張項目７',
				8 => '年代８',
				9 => '拡張項目９',
				10 => '性別１０',
			];
			
			$members_extend_columns = db::select('extend_no', 'name')
					->from(DB::expr('t_mail_extend'))
					->execute()->as_array('extend_no', 'name')
					;
			for($extend_no = 1; $extend_no <= 10; $extend_no ++) {
				if ( ! isset($members_extend_columns[$extend_no]) )
					$extensions[$extend_no] = '拡張項目'.mb_convert_kana($extend_no, 'N');
				else
					$extensions[$extend_no] = $members_extend_columns[$extend_no];
			}*/
			$extensions = $this->_model->list_mail_member_extend_column_names($this->_bot_id, $project_id, true);
			$this->_logEcho('CSV import preview extensions of project '.$project_id.'['.implode(',', $extensions).'] ----------', true);

			$extend_headers = [];
			// $member_name_head_index = $headers['name'];
			$checkedBOM = false;
			$isJIS = '';
			while (($csv_line = fgets($csv_stream)) !== false) {
				// process the line read.
				// error_log('csv stream line:'.$csv_line);
				if( !$checkedBOM ) {
					// $this->_logEcho('Is JIS encoding csv header? '.$isJIS, true);
					if ( mb_check_encoding($csv_line, 'SJIS-mac') ) {
						$csv_line = mb_convert_encoding($csv_line, 'UTF-8', 'SJIS-mac');
						$isJIS = 'SJIS-mac';
					} else if ( mb_check_encoding($csv_line, 'SJIS-win') ) {
						$csv_line = mb_convert_encoding($csv_line, 'UTF-8', 'SJIS-win');
						$isJIS = 'SJIS-win';
					} else if ( mb_check_encoding($csv_line, 'SJIS') ) {
						$csv_line = mb_convert_encoding($csv_line, 'UTF-8', 'SJIS');
						$isJIS = 'SJIS';
					}
					$csv_line = $this->_trimBOMLine($csv_line);
					$checkedBOM = true;
					$csv_row = str_getcsv($csv_line);
					if ($csv_line && $this->_isCSVHeaderLine($csv_row, $headers)) {
						$headers = [
							'email' => 0,
							'name' => 1,
						];
						//TODO handle free order head lines
						$undef_headers = [];
						foreach ($csv_row as $index=>$header){
							if ($index == 0) {
								// email
							} else if ($index == 1) {
								// name
							} else if ($index == count( $csv_row ) - 1 && $this->_isCSVRegistDateHeader($header) ) {
								// date
								$headers['regist_date'] = $index;
							}  else {
								$extend_index = $this->_checkCSVExtendHeader($header, $extensions);
								if ( $extend_index ) {
									// extend header
									$extend_headers []= ['index'=>$index, 'extend_no'=>$extend_index, 'header'=>$header];
									// $headers[$extend_index] = $header;
								} else {
									// undefined header
									$undef_headers []= $header;
									$this->_logEcho('Skipping csv undefined header '.$header.'', true);
								}
							}
						}
						// $headers = $csv_row;
						continue;
					}
				} else if ($isJIS) {
					$csv_line = mb_convert_encoding($csv_line, 'UTF-8', $isJIS);
				}

				if ( trim($csv_line) == '') {
					continue;
				}
				$csv_row = str_getcsv(trim($csv_line));
				if ( $csv_row && !$this->_isCSVHeaderLine($csv_row, $headers) ) {
					$csv_matrix []= $csv_row;
				} else  {
					// $this->_logEcho('Skipping csv header or empty email '.json_encode($csv_line).' ----------', true);
				}//	TODO invalid lines inform
			}
			fclose($csv_stream);
		}
		$response = ['success'=>true, 'count'=>count($csv_matrix), 'csv'=>$csv_matrix];
		if ($headers) {
			$response['headers'] = $headers;
			if ($extend_headers) {
				$response['extends'] = $extend_headers;
			}
			if( isset($undef_headers) && $undef_headers ) {
				$response['undef_col'] = $undef_headers;
			}
		}
		$this->response->body(json_encode($response));
	}

	public function action_update_mail_member_project() {
		if ($this->request->post()) {
			$post = $this->request->post();
			$projectId = $post['project_id'];
			$memberIds = json_decode($post['member_ids'], true);
			if (is_array($memberIds) && count($memberIds) > 0) {
				try {
					$skipped = 0;
					$updated = 0;
					$db = Database::instance();
					$db->begin();
					foreach ($memberIds as $memberId) {
						// check if email exists in target project
						$duplicates = DB::select('duplicate.mail_member_id', 'member.email')
							->from(DB::expr('t_mail_member as member'))
							->join(DB::expr('t_mail_member as duplicate'), 'INNER')
							->on('member.email', '=','duplicate.email')
							->where('member.mail_member_id', '=', $memberId)
							->where('duplicate.project_id', '=', $projectId)
							->execute()->as_array();

						if ( $duplicates ) {
							// email already exists in target project
							$this->_logEcho('Detected mail member tag duplicate: '.$duplicates[0]['mail_member_id'], true);
							$skipped ++;
						} else {
							$this->_logEcho('Cleaning mail member: '.($memberId), true);
							//deleting relation between task and members in recipients if task not executed yet
							$delete_result = db::delete('t_mail_task_recipient')
										->where('mail_member_id', '=', $memberId)
										->where('send_status', '=', 0)	// TODO hint in task that members been deleted
										->execute();
							$this->_logEcho('Deleted(clean up) mail member task relation(s): '.$delete_result, true);

							//deleting relation between tag and members
							$delete_result = db::delete('t_mail_member_tag')
								->where('mail_member_id', '=', $memberId)
								->execute();
							$this->_logEcho('Deleted(clean up) mail member tag relation(s): '.$delete_result, true);

							//deleting relation between extended attributes and members
							$delete_result = db::delete('t_mail_member_extend')
								->where('mail_member_id', '=', $memberId)
								->execute();
							$this->_logEcho('Deleted(clean up) mail member extended attributes relation(s): '.$delete_result, true);

							$result = db::update('t_mail_member')
								->set(['project_id' => $projectId])
								->where('mail_member_id', '=', $memberId)
								->execute();

							if ($result) {
								$updated ++;
							} else {
								$db->rollback();
								$this->response->body(json_encode(['success' => false, 'msg' => 'Failed to update project_id of mail member:'.$memberId]));
								return;
							}
						}						
					}
					$db->commit();
					$this->response->body(json_encode(['success' => true, 'updated' => $updated, 'skipped' => $skipped]));
				} catch (Exception $e) {
					$db->rollback();
					$this->response->body(json_encode(['success' => false, 'msg' => $e->getMessage()]));
				}
			} else {
				$this->response->body(json_encode(['success' => false, 'msg' => 'No valid member IDs provided']));
			}
		} else {
			$this->response->body(json_encode(['success' => false, 'msg' => 'Invalid request data']));
		}
	}	

	public function action_update_mail_tag_project() {
		if ($this->request->post()) {
			$post = $this->request->post();
			$projectId = $post['project_id'];
			$tagNos = json_decode($post['tag_nos'], true);
			if (is_array($tagNos) && count($tagNos) > 0) {
				try {
					$skipped = 0;
					$updated = 0;
					$db = Database::instance();
					$db->begin();
					foreach ($tagNos as $tagNo) {
						// check if tag_name exists in target project
						$duplicates = DB::select('duplicate.tag_no', 'tag.tag_name')
							->from(DB::expr('t_mail_tag as tag'))
							->join(DB::expr('t_mail_tag as duplicate'), 'INNER')
							->on('tag.tag_name', '=','duplicate.tag_name')
							->where('tag.tag_no', '=', $tagNo)
							->where('duplicate.project_id', '=', $projectId)
							->execute()->as_array();

						if ( $duplicates ) {
							// tag already exists in target project
							$this->_logEcho('Detected mail member tag duplicate: '.$duplicates[0]['tag_no'], true);
							$skipped ++;
						} else {
							$delete_result = db::delete('t_mail_member_tag')
								->where('tag_no', '=', $tagNo)
								->execute();
							$this->_logEcho('Deleted(clean up) mail member tag relation: '.json_encode($delete_result), true);

							$result = db::update('t_mail_tag')
								->set(['project_id' => $projectId])
								->where('tag_no', '=', $tagNo)
								->execute();
							if ($result) {
								$updated ++;
							} else {
								$db->rollback();
								$this->response->body(json_encode(['success' => false, 'msg' => 'Failed to update project_id of mail tag:'.$tagNo]));
								return;
							}
						}						
					}
					$db->commit();
					$this->response->body(json_encode(['success' => true, 'updated' => $updated, 'skipped' => $skipped]));
				} catch (Exception $e) {
					$db->rollback();
					$this->response->body(json_encode(['success' => false, 'msg' => $e->getMessage()]));
				}
			} else {
				$this->response->body(json_encode(['success' => false, 'msg' => 'No valid tag numbers provided']));
			}
		} else {
			$this->response->body(json_encode(['success' => false, 'msg' => 'Invalid request data']));
		}
	}

	public function action_try_resolve_email_suppression() {
		$query = $this->request->query();
		$member_id = $query['member_id'];
		$email = $query['email'];
		$response = ['success'=>false, 'msg'=>'email: '.$email, 'email'=>$email];
		if ($email == NULL) {
			$response['msg'] = 'No email provided';
			$this->response->body(json_encode($response));
			return;
		}
		$data = [
			'mail' => $email
		];
		$check_result = $this->_model->post_enginehook('service', 'aws_ses_is_identity_in_suppressed_list', '', $data);
		if ($check_result == null || !isset($check_result['success']) || !$check_result['success'] || !isset($check_result['extra_info'])) {
			//メール存在しなければ登録する
			$response['msg'] = 'email suppression check failed: '.$email;
			$response['origin'] = $check_result;
		} else {
			$extra_info = $check_result['extra_info'];
			if ( isset($extra_info['ret']) && $extra_info['ret'] == 'info' && isset($extra_info['info']) ) {
				$ret_info = $extra_info['info'];
				if ( isset($ret_info['SuppressedDestination']) ) {
					$suppressed = $ret_info['SuppressedDestination'];
					if ( isset($suppressed['EmailAddress']) && $suppressed['EmailAddress'] == $email && isset($suppressed['Reason']) && $suppressed['Reason'] == 'BOUNCE' ) {
						$response['msg'] = 'email suppressed: '.$email;
						//メール存在すれば削除する
						$resolve_result = $this->_model->post_enginehook('service', 'aws_ses_delete_identity_from_suppressed_list', '', $data);
						if ($resolve_result == null || $resolve_result['success'] == 'false' || (isset($resolve_result['statusCode']) && $resolve_result['statusCode'] != 200)) {
							//resolve failed
							if (isset($resolve_result['statusCode']) && $resolve_result['statusCode'] == 429) {
								//TODO Too Many Requests, retry later
								sleep(1);
								$resolve_result = $this->_model->post_enginehook('service', 'aws_ses_delete_identity_from_suppressed_list', '', $data);
								if ($resolve_result == null || $resolve_result['success'] == 'false' || (isset($resolve_result['statusCode']) && $resolve_result['statusCode'] != 200)) {
									//resolve failed						
									$response['msg'] = 'Failed to resolve email suppression: '.$email;
								} else {
									$response['success'] = true;
								}
							} else {
								$response['msg'] = 'Failed to resolve email suppression: '.$email;
							}
						} else {
							$response['success'] = true;
						}
					} else {
						$response['msg'] = 'email suppression check failed, SuppressedDestination EmailAddress/Reason mismatch: '.$email;
					}
				} else {
					$response['msg'] = 'email suppression check failed, SuppressedDestination not found: '.$email;
				}
			} else {
				if ( isset($extra_info['ret']) && $extra_info['ret'] == 'err' && isset($extra_info['err']) ) {
					$ret_err = $extra_info['err'];
					if ( isset($ret_err['statusCode']) && $ret_err['statusCode'] == 404 ) {
						$response['msg'] = 'email not in suppressed list: '.$email;
						$response['status_code'] = 404;
					} else {
						$response['msg'] = 'email suppression check failed, statusCode='.$ret_err['statusCode'].': '.$email;
					}
				} else {
					$response['msg'] = 'email suppression check failed, ret not found: '.$email;
				}
			}	
			
		}
		$this->response->body(json_encode(value: $response));
	}

	public function action_get_contents_by_id() {
		$post = $this->request->query();
		$items = $this->_model->get_contents_by_id($post['item_id'],$post['lang'] );
		$this->response->body(json_encode($items));
	}

	public function action_get_contents_by_id_with_schedule_description() {
		$post = $this->request->query();
		$items = $this->_model->get_contents_by_id_with_schedule_description($post['item_id'],$post['lang'], $this->_bot_id);
		$this->response->body(json_encode($items));
	}
	
	public function action_reception_status(){
		if ($this->request->post()) {
			$post = $this->request->post();
			$reception_id = $post['reception_id'];
			$pause_flg = $post['pause_flg'];

			DB::update('t_reception')->set(['pause_flg' => $pause_flg])->
			where('bot_id', '=', $this->_bot_id)->where('reception_id', '=', $reception_id)->execute();

			$this->response->body(json_encode(['result' => 'success']));
		}
	}

	public function action_reception_operation(){
		if ($this->request->post()) {
			$post = $this->request->post();
			$reception_id = $post['reception_id'];
			$reception_no = $post['reception_no'];
			$status_cd = $post['next_status'];
			$seq = $post['seq'];
			$reception_notification = $post['waiting'];
			$action_type = $post['action_type'] ? $post['action_type'] : '';

			DB::update('t_reception_list')->set(['status_cd' => $status_cd,'upd_time'=> date('Y-m-d H:i:s')])->
			where('reception_id', '=', $reception_id)->
			where('seq', '=', $seq)->where('reception_no', '=', $reception_no)->
			execute();

			DB::update('t_reception')->set(['list_upd_user' => $this->_user_id,'list_upd_time'=> date('Y-m-d H:i:s')])->
			where('reception_id', '=', $reception_id)->
			where('bot_id', '=', $this->_bot_id)->
			execute();

			// notification
			if ($status_cd == '3' || $action_type == 'skip' || $status_cd == '6') {
				$reception_list = DB::select('notification_type','upd_time','reception_no','member_id')->from('t_reception_list')->where('reception_id', '=', $reception_id)->where('seq', '=', $seq)->execute()->as_array();

				// メッセージコードの設定
				$msg_codes = [
					'3' => ['email' => 'very.reception_notification_calling', 'line' => 'very.reception_notification_calling_line'],
					'skip' => ['email' => 'very.reception_notification_skip', 'line' => 'very.reception_notification_skip_line'],
					'6' => ['email' => 'very.reception_notification_cancel', 'line' => 'very.reception_notification_cancel_line'],
				];
				$msg_code = $msg_codes[$action_type == 'skip' ? 'skip' : $status_cd];

				// $notification_typeがnullでないかをチェック
				if (!is_null($reception_list[0]['notification_type'])) {
					$notification_type = json_decode($reception_list[0]['notification_type'], true);
					// mail notification
					if (array_key_exists('email', $notification_type)) {
						$param['bot_id'] = '0';
						$param['sender'] = '<EMAIL>';
						// TODO : 多言語対応時に、ユーザーごとの言語のテンプレートを送信するように変更する
						$param['lang_cd'] = 'ja';
						$param['receiver'] = $notification_type['email'];
						$param['message_cd'] = $msg_code['email'];
						$param['params'] = [
							'facility_name'=> trim($post['facility_name']),
							'calling_time'=>$reception_list[0]['upd_time'],
							'reception_no'=>$reception_list[0]['reception_no']
						];
						$data = $this->_model->post_enginehook('service', 'sendmail', '', $param);
						$log_params = [
							'bot_id' => $this->_bot_id,
							'reception_id' => $reception_id,
							'member_id' => is_null($reception_list[0]['member_id']) ? '' : $reception_list[0]['member_id'],
							'lang_cd' => $post['lang_cd'],
							'notification_method' => 'email',
							'notification_type' => 2,
							'notification_time' => date('Y-m-d H:i:s'),
							'result' => 1
						];
						if (!is_null($data) && $data['success'] != "true") {
							Log::instance()->add(Log::DEBUG, 'sendusermail (' . $param['message_cd'] . ') failure=' . json_encode($data));
							$log_params['result'] = 0;
							$log_params['message'] = json_encode($data);
						}
						$reception_notification_log = ORM::factory('xreceptionnotificationlog');
						list($result, $msg) = $reception_notification_log->write_log($log_params);
						if (!$result) {
							Log::instance()->add(Log::DEBUG, 'reception_notification_log (' . $param['message_cd'] . ') failure=' . $msg);
						}
					}
					// LINE notification
					if (array_key_exists('LINE', $notification_type)) {
						$msg_code = $msg_code['line'];
						// TODO : 多言語対応時に、ユーザーごとの言語のテンプレートを送信するように変更する
						$lang_cd ="ja";
						$user_code = $notification_type['LINE']['id'];
						$user_token = $notification_type['LINE']['token'];
						$message = $this->_model->get_service_msg($this->_bot_id, $msg_code);
						$content_tpl = $message[$msg_code][$lang_cd]["content"];

						$replacements = array(
							'{facility_name}'=> trim($post['facility_name']),
							'{calling_time}'=>$reception_list[0]['upd_time'],
							'{reception_no}'=>$reception_list[0]['reception_no']
						);
						$content = str_replace(array_keys($replacements), array_values($replacements), $content_tpl);
						$data = $this->_very_model->send_line_notify($user_code, $user_token, $content);
						$log_params = [
							'bot_id' => $this->_bot_id,
							'reception_id' => $reception_id,
							'member_id' => is_null($reception_list[0]['member_id']) ? '' : $reception_list[0]['member_id'],
							'lang_cd' => $post['lang_cd'],
							'notification_method' => 'line',
							'notification_type' => 2,
							'notification_time' => date('Y-m-d H:i:s'),
							'result' => 1
						];
						if (property_exists(json_decode($data), 'message')) {
							Log::instance()->add(Log::DEBUG, 'senduserline (' . $msg_code . ') failure=' . json_decode($data)->message);
							$log_params['result'] = 0;
							$log_params['message'] = json_decode($data)->message;
						}
						$reception_notification_log = ORM::factory('xreceptionnotificationlog');
						list($result, $msg) = $reception_notification_log->write_log($log_params);
						if (!$result) {
							Log::instance()->add(Log::DEBUG, 'reception_notification_log (' . $param['message_cd'] . ') failure=' . $msg);
						}
					}
				}
			}
			// 「もうすぐ呼出」組数の取得
			$waiting_list = DB::select('reception_no', 'status_cd', 'seq')->
			from('t_reception_list')->
			where('reception_id', '=', $reception_id)->
			where('status_cd', '<=', 2)->
			where('reception_time', '>=', date('Y-m-d'))->
			order_by('reception_time','ASC')->execute()->as_array();

			$waiting = array_values(array_filter($waiting_list, function($item) {
				return $item['status_cd'] == '1';
			}));
			$waiting_soon = array_values(array_filter($waiting_list, function($item) {
				return $item['status_cd'] == '2';
			}));

			if(count($waiting_soon) <= $reception_notification){
				$diff = $reception_notification-count($waiting_soon);
				$count = count($waiting) < $diff  ? count($waiting) : $diff;
				for ($i = 0; $i < $count; $i++){
					// 更新前のデータを取得
					$reception_list = DB::select(
						'notification_type',
						'upd_time',
						'reception_no',
						'member_id',
						'status_cd',
        				DB::expr("
        				    (
        				        SELECT COUNT(seq)
        				        FROM t_reception_list li
        				        WHERE li.status_cd IN (1,2)
        				        AND li.reception_id = :reception_id
        				        AND li.reception_time < l.reception_time
        				        AND DATE_FORMAT(li.reception_time, '%Y-%m-%d') = DATE_FORMAT(now(), '%Y-%m-%d')
        				    ) AS waiting_before
        				")
                    )
						->from(['t_reception_list', 'l'])
						->where('l.reception_id', '=', $reception_id)
						->where('l.seq', '=', $waiting[$i]['seq'])
						->parameters([
							':reception_id' => $reception_id
						])
						->execute()
						->as_array();
					DB::update('t_reception_list')->set(['status_cd' => 2,'upd_time'=> date('Y-m-d H:i:s')])->
					where('reception_id', '=', $reception_id)->
					where('seq', '=', $waiting[$i]['seq'])->where('reception_no', '=', $waiting[$i]['reception_no'])->
					execute();
					// 「あと n 組」を計算
					$waiting_before = $reception_list[0]['waiting_before'];

					if (!is_null($reception_list[0]['notification_type']) && $reception_no !== $reception_list[0]['reception_no']) {
						$notification_type = json_decode($reception_list[0]['notification_type'], true);
						if (array_key_exists('email', $notification_type)) {
							$param['bot_id'] = '0';
							$param['sender'] = '<EMAIL>';
							// TODO : 多言語対応時に、ユーザーごとの言語のテンプレートを送信するように変更する
							$param['lang_cd'] = 'ja';
							$param['receiver'] = $notification_type['email'];
							$param['message_cd'] = 'very.reception_notification_calling_soon';
							$param['params'] = [
								'facility_name' => trim($post['facility_name']),
								'reception_no' => $reception_list[0]['reception_no'],
								'remaining_groups' => $waiting_before
							];
							$data = $this->_model->post_enginehook('service', 'sendmail', '', $param);
							$log_params = [
								'bot_id' => $this->_bot_id,
								'reception_id' => $reception_id,
								'member_id' => is_null($reception_list[0]['member_id']) ? '' : $reception_list[0]['member_id'],
								'lang_cd' => $post['lang_cd'],
								'notification_method' => 'email',
								'notification_type' => 2,
								'notification_time' => date('Y-m-d H:i:s'),
								'result' => 1
							];
							if (!is_null($data) && $data['success'] != "true") {
								Log::instance()->add(Log::DEBUG, 'sendusermail (' . $param['message_cd'] . ') failure=' . json_encode($data));
								$log_params['result'] = 0;
								$log_params['message'] = json_encode($data);
							}
							$reception_notification_log = ORM::factory('xreceptionnotificationlog');
							list($result, $msg) = $reception_notification_log->write_log($log_params);
							if (!$result) {
								Log::instance()->add(Log::DEBUG, 'reception_notification_log (' . $param['message_cd'] . ') failure=' . $msg);
							}
						}
						if (array_key_exists('LINE', $notification_type)) {
							$msg_code = 'very.reception_notification_calling_soon_line';
							// TODO : 多言語対応時に、ユーザーごとの言語のテンプレートを送信するように変更する
							$lang_cd = 'ja';
							$user_code = $notification_type['LINE']['id'];
							$user_token = $notification_type['LINE']['token'];
							$message = $this->_model->get_service_msg($this->_bot_id, $msg_code);
							$content_tpl = $message[$msg_code][$lang_cd]['content'];
						
							$replacements = [
								'{facility_name}' => trim($post['facility_name']),
								'{reception_no}' => $reception_list[0]['reception_no'],
								'{remaining_groups}' => $waiting_before
							];
							$content = str_replace(array_keys($replacements), array_values($replacements), $content_tpl);
							$data = $this->_very_model->send_line_notify($user_code, $user_token, $content);
							$log_params = [
								'bot_id' => $this->_bot_id,
								'reception_id' => $reception_id,
								'member_id' => is_null($reception_list[0]['member_id']) ? '' : $reception_list[0]['member_id'],
								'lang_cd' => $post['lang_cd'],
								'notification_method' => 'line',
								'notification_type' => 2,
								'notification_time' => date('Y-m-d H:i:s'),
								'result' => 1
							];
							if (property_exists(json_decode($data), 'message')) {
								Log::instance()->add(Log::DEBUG, 'senduserline (' . $msg_code . ') failure=' . json_decode($data)->message);
								$log_params['result'] = 0;
								$log_params['message'] = json_decode($data)->message;
							}
							$reception_notification_log = ORM::factory('xreceptionnotificationlog');
							list($result, $msg) = $reception_notification_log->write_log($log_params);
							if (!$result) {
								Log::instance()->add(Log::DEBUG, 'reception_notification_log (' . $param['message_cd'] . ') failure=' . $msg);
							}
						}
					}
				}
			}
			$this->response->body(json_encode(['result' => 'success']));
		}
	}

	public function action_reception_list_items(){
		$post = $this->request->post();

		$bot_id = $post['bot_id'];
		$reception_id = $post['reception_id'];
		
		// 「もうすぐ呼出」組数の取得
		$sql = "SELECT l.reception_no AS reception_no, l.reception_time AS reception_time, l.reception_data AS entry_data, r.reception_data AS reception_data, 
		l.upd_time AS upd_time, l.status_cd AS status_cd, l.seq AS seq,
		JSON_ARRAYAGG(JSON_OBJECT('no', le.no, 'entry_result', le.entry_result, 'entry_data', le.entry_data)) AS entries
		FROM t_reception r
		LEFT JOIN t_reception_list l ON r.reception_id=l.reception_id
		LEFT JOIN t_reception_list_entry le ON l.reception_id=le.reception_id AND l.seq=le.seq
		WHERE r.bot_id=:bot_id AND r.reception_id=:reception_id AND l.reception_time >= CURDATE()
		GROUP BY l.seq
		ORDER BY l.status_cd DESC, l.reception_time ASC
		";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
			':bot_id' => $bot_id,
			':reception_id' => $reception_id
		));
		$results = $query->execute()->as_array();
		$result_item = [];
		foreach($results as $result) {
			$capacity = [];
			$reception_data = json_decode($result['reception_data'], true);
			if (array_key_exists('capacity',$reception_data)) {
				$entry_data = json_decode($result['entry_data'], true);
				if (isset($entry_data['capacity'])) {
					$capacity = $entry_data['capacity'];
				}
			}

			$entry_datas = [];
			$entries = json_decode($result['entries'], true);
			foreach ($entries as $entry) {
				if (!is_null($entry) && isset($entry['no']) && !is_null($entry['no'])) {
					$entry_datas[$entry['no']] = $entry['entry_result'];
				}
			}

			$result_item[] = [
				'reception_no'=>$result['reception_no'],
				'reception_time'=>$result['reception_time'],
				'capacity'=>$capacity,
				'entries'=>$entry_datas,
				'upd_time'=>$result['upd_time'],
				'status_cd'=>$result['status_cd'],
				'seq'=>$result['seq'],
				];
		}
		
		$this->response->body(json_encode($result_item));
	}

	// action_reception_receipt
	public function action_reception_receipt() {
		if ($this->request->post()) {
			$post = $this->request->post();
			$reception_id = $post['reception_id'];
			$reception_data = $post['reception_data'];
			$reception_entries = isset($post['reception_entry']) ? $post['reception_entry'] : []; // `entries` は別で受け取る

			// `t_reception_list` の最大 `seq` を取得し、新しい `seq` を決定
			$max_seq_query = DB::select(DB::expr('MAX(seq) as max_seq'))
				->from('t_reception_list')
				->where('reception_id', '=', $reception_id)
				->execute()->as_array();
			$max_seq = $max_seq_query[0]['max_seq'];
			$new_seq = $max_seq + 1;

			// 受付番号を取得
			$reception_no = $this->_very_model->get_reception_no($reception_id);
			$now = date('Y-m-d H:i:s');

			// `t_reception_list` にデータを挿入
			DB::insert('t_reception_list', array('reception_id', 'seq', 'reception_no', 'reception_data', 'status_cd', 'reception_time', 'upd_time'))
				->values(array($reception_id, $new_seq, $reception_no, json_encode($reception_data), '1', DB::expr('NOW()'), DB::expr('NOW()')))
				->execute();

			// `t_reception_list_entry` に `receptionEntry` のデータを挿入
			foreach ($reception_entries as $no => $value) {
				DB::insert('t_reception_list_entry', array('reception_id', 'seq', 'no', 'entry_result', 'entry_data', 'entry_extra_info'))
					->values(array($reception_id, $new_seq, $no, $value, $value, ""))
					->execute();
			}

			// 受付名を取得
			$result = DB::select('title')
				->from('t_reception_description')
				->where('reception_id', '=', $reception_id)
				->where('lang_cd', '=', 'ja')
				->execute()->as_array();

			// 受付票の URL を作成
			$reception_url = $this->_model->get_env('very_url') . 'ja/receptionlist?reception_id=' . $reception_id . '&seq=' . $new_seq . '&bot_id=' . $this->_bot_id . '&qr=1';
			$data = [
				'result' => 'success',
				'data' => [
					'reception_name' => $result[0]['title'],
					'reception_no' => $reception_no,
					'reception_time' => $now,
					'reception_url' => $reception_url
				]
			];
			$this->response->body(json_encode($data));
		}
	}
	
	public function action_reception_online() {
		if ($this->request->post()) {
			$post = $this->request->post();
			$reception_id = $post['reception_id'];
			$bot_id = $post['bot_id'];

			$result = DB::select('pause_flg')
			->from('t_reception')
			->where('bot_id', '=', $bot_id)
			->where('reception_id', '=', $reception_id)
			->execute()->as_array();

			$this->response->body(json_encode($result[0]));
		}
	}

	public function action_check_limit_reached_reception() {    
		$post = $this->request->post();
		$reception_id = $post['reception_id'];
		$bot_id = $post['bot_id'];
		$response = ['status' => 'success', 'message' => ''];
		$reception_data_result = DB::select('reception_data', 
			[DB::expr('(
				SELECT COUNT(*)
				FROM t_reception_list
				WHERE reception_id = r.reception_id AND reception_time >= CURDATE()
			)'), 'total_reception_count'])
			->from(['t_reception', 'r'])
			->where('bot_id', '=', $bot_id)
			->where('reception_id', '=', $reception_id)
			->execute()
			->as_array();
			if (empty($reception_data_result)) {
				$this->response->body(json_encode(['status' => 'error', 'message' => 'Reception information not found']));
				return;
			}
		$reception_data = json_decode($reception_data_result[0]['reception_data'], true);
		$max_reception_party = (isset($reception_data['max_reception_party']) && $reception_data['max_reception_party'] !== "") ? (int)$reception_data['max_reception_party'] : null;
		$total_reception_count = $reception_data_result[0]['total_reception_count'];
		if ($max_reception_party !== null && $max_reception_party !== "" && $total_reception_count >= $max_reception_party) {
			$this->response->body(json_encode(['status' => 'success', 'isLimitReached' => TRUE]));
		} else {
			$this->response->body(json_encode(['status' => 'success', 'isLimitReached' => FALSE]));
		}
	}
	
	public function action_botlinemenu_copytemplate() {
		$post = $this->request->post();
		$response = [];
		$msg_id = $post['msg_id'];
		$skills = ORM::factory('skill')->find_all();
		$skill_kv = [];
		foreach($skills as $skill) {
			$skill_kv[$skill->skill] = ['name'=>$skill->skill_name, 'kind'=>$skill->kind];
		}
		$msg = ORM::factory('botmsg', $msg_id);
		$extra_info = json_decode($msg->extra_info, true);
		if ($extra_info != null) {
			$response['template_cd'] = isset($extra_info['template_cd.'. $this->_lang_cd]) ? $extra_info['template_cd.'. $this->_lang_cd] : null;
		}
		else {
			$response['template_cd'] = $msg->extra_info;
		}
		$msg_desc = ORM::factory('botmsgdesclst')->where('msg_id', '=', $msg_id)->where('lang_cd', '=', $this->_lang_cd)->order_by('no')->find_all();
		$areas = [];
		foreach($msg_desc as $d) {
			if ($d->msg_image != '') $response['msg_image'] = $d->msg_image;
			$action = json_decode($d->url, true)[0];
			if ($action['skill'] != null)
			{
				if ($action['skill'] == 'RICHMENU_SWITCH') {
					// Dont copy richmenu_alias
					$action['params']['richmenu_alias'] = null;
				}
				$action = $action + ['skill_kind'=>$skill_kv[$action['skill']]['kind']];
			}
			$areas[] = ['bounds'=>json_decode($d->content, true), 'action_text'=>$d->title, 'action'=>$action];
		}
		$response['areas'] = $areas;
		$this->response->body(json_encode($response, JSON_UNESCAPED_UNICODE));
	}

	public function action_lottery()
	{
		$post = $this->request->post();
		$member_id = $post['member_id'];
		$coupon_id = $post['coupon_id'];
		$lottery_status = $post['lottery_status'];

		// すでに抽選済みかどうか
		if($lottery_status === 1) return;
	
		// 抽選情報を取得
		$lottery_infomation = DB::select('coupon_data')
		->from('t_coupon')
		->where('bot_id', '=', $this->_bot_id)
			->where('coupon_id', '=', $coupon_id)
			->execute()
			->as_array();

		$lottery_result = false;
		if (count($lottery_infomation) > 0) {
			$coupon_data = json_decode($lottery_infomation[0]['coupon_data'], true);
			$lottery_probability = $coupon_data['lottery_probability'];
			$lottery_type = $coupon_data['lottery_type'];
			$lottery_maximum = $coupon_data['lottery_maximum'];

			if (!$lottery_probability) return;

			if ($lottery_type == '2') {
				// 最大人数の設定があるとき
				$currentWinners = $this->_model->get_lottery_winners($coupon_id);
				$lottery_result = $this->_model->check_user_win($lottery_probability);
				if ($lottery_maximum && $currentWinners >= $lottery_maximum) {
					$lottery_result = false;
				}
			} else {
				$lottery_result = $this->_model->check_user_win($lottery_probability);
			}
		}

		$result = DB::update('t_coupon_result')
		->set(['lottery_status' => '1',
				'lottery_result' => $lottery_result ? 0 : 1,
				'lottery_date' => DB::expr('NOW()')
			])
		->where('bot_id', '=', $this->_bot_id)
		->where('coupon_id', '=', $coupon_id)
		->where('member_id', '=', $member_id)
		->execute();
		
		$this->response->body(json_encode($lottery_result));
	}

	public function action_check_mail_template()
	{
		$post = $this->request->post();
		$msg_class_cd = $post['msg_class_cd'];
		$msg_cd = $post['msg_cd'];

		if (!$msg_class_cd || !$msg_cd) return;

		$result = [];
		$selectDb = '';
		$selectTemplate = '';
		if ($msg_class_cd == '42') {
			$selectDb = 't_survey';
			$selectTemplate = 'member_mail_template';
		} else if ($msg_class_cd == '43') {
			$selectDb = 't_survey';
			$selectTemplate = 'user_mail_template';
		} else if ($msg_class_cd == '44') {
			$selectDb = 't_inquiry';
			$selectTemplate = 'member_mail_template';
		} else if ($msg_class_cd == '45') {
			$selectDb = 't_inquiry';
			$selectTemplate = 'user_mail_template';
		};

		$bots = [$this->_bot_id];
		$grp_bot_id = $this->_model->get_grp_bot_id($this->_bot_id);
		if ($grp_bot_id > 0) {
			// このbotは親botではない場合
			$bots[] = $grp_bot_id;
		}
		if ($grp_bot_id == 0 && $this->_bot_id != 0) {
			// このbotは親botである場合、子botを取得
			$sub_bots = $this->_model->get_sub_bots($this->_bot_id);
			foreach ($sub_bots as $sub_bot) {
				$bots[] = $sub_bot['bot_id'];
			}
		}
		
		$result = DB::select()
		->from($selectDb)
		->where('bot_id', 'in', $bots)
		->where('delete_flg', '=', 0)
		->where($selectTemplate, '=', $msg_cd)
		->execute()
		->as_array();

		if ($msg_class_cd == '44') {
			// アクションのユーザー送信
			$entry_actions = $this->_model->get_inquiry_entry_action($this->_bot_id);
			// if (count($entry_actions) == 0) return;
			foreach ($entry_actions as $entry_action) {
				$actions = json_decode($entry_action['actions'], true);
				if (isset($actions[0]['actions'][1]['param']['member_mail_template']) &&
				$actions[0]['actions'][1]['param']['member_mail_template'] == $msg_cd) {
					array_push($result, [
						'lang' => $entry_action['lang_cd'],
						'no' => $entry_action['no'],
						'inquiry_name' => $entry_action['inquiry_name'],
						'inquiry_id' => $entry_action['inquiry_id'],
					]);
				}
			}
		}
		
		$this->response->body(json_encode($result));
	}
	
	public function action_visitrgroups_notes() {
		if ($this->request->post()) {
			$post = $this->request->post();
			$columnName = $post['column_name'];
			$value = $post['value'];
			$visitId = $post['visit_id'];
	
			// データベースへの保存処理
			$result = DB::update('t_crm_visit')
					   ->set(array($columnName => $value))
					   ->where('visit_id', '=', $visitId)
					   ->execute('member');
			$this->response->body(json_encode(['result' => 'success']));
		}
	}
	public function action_veryreport_user_export()
	{
		$post = $this->request->query();
		$bot_id = $this->_bot_id;
		$lang_cd = $this->_lang_cd;
		$format = $post['format']; // monthly or daily
		$start_date = $post['start_date'] !== "undefined" ? $post['start_date'] : null;
		$end_date = $post['end_date'] !== "undefined" ? $post['end_date'] : null;
		if ( $end_date ) {
			$end_date = DateTime::createFromFormat('Y-m-d', $end_date)->format('Y-m-d 23:59:59');
		}
		$scene_cd = $post['scene_cd'];

		Log::instance()->add(Log::DEBUG, 'Ajax filtering very user count csv report('.$format.') from '.$start_date.' to '.$end_date);

		$formatted_data = $this->_very_model->get_very_report_user_for_export_csv($bot_id, $scene_cd, $lang_cd, $format, $start_date, $end_date);
		$this->response->body(json_encode(['result' => $formatted_data]));

		// CSV出力
		$create_time = date('Y-m-d');
		ob_end_clean();
		header("Content-Type: text/csv");
		header("Content-Disposition: attachment; filename=very_user_statistics_$create_time.csv");
		$f = fopen('php://output', 'w');
		fwrite($f, "\xEF\xBB\xBF");
		fputcsv($f, $formatted_data['headers']);
		foreach ($formatted_data['data'] as $row) {
			fputcsv($f, $row);
		}
		fclose($f);
		exit;
	}

	public function action_veryreport_clicks_export()
	{
		$post = $this->request->query();
		$bot_id = $this->_bot_id;
		$lang_cd = $this->_lang_cd;
		$format = $post['format']; // monthly or daily
		$start_date = $post['start_date'] !== "undefined" ? $post['start_date'] : null;
		$end_date = $post['end_date'] !== "undefined" ? $post['end_date'] : null;

		if ( $end_date ) {
			$end_date = DateTime::createFromFormat('Y-m-d', $end_date)->format('Y-m-d 23:59:59');
		}
		$scene_cd = $post['scene_cd'];
		Log::instance()->add(Log::DEBUG, 'Ajax filtering very click count csv report('.$format.') from '.$start_date.' to '.$end_date);

		$formatted_data = $this->_very_model->get_very_report_clicks_for_export_csv($bot_id, $scene_cd, $lang_cd,  $format, $start_date, $end_date);
		$this->response->body(json_encode(['result' => $formatted_data]));

		// CSV出力
		$create_time = date('Y-m-d');
		ob_end_clean();
		header("Content-Type: text/csv");
		header("Content-Disposition: attachment; filename=very_clicks_$create_time.csv");
		$f = fopen('php://output', 'w');
		fwrite($f, "\xEF\xBB\xBF");
		fputcsv($f, $formatted_data['headers']);
		foreach ($formatted_data['data'] as $row) {
			fputcsv($f, $row);
		}
		fclose($f);
		exit;
	}
	
	public function action_get_inquiry_entries_for_newsletter() {
		$post = $this->request->post();
		if (isset($post['inquiry_id']) && isset($post['lang_cd'])) {
			$sql = '
				SELECT t_inquiry_entry.no, t_inquiry_entry.title, t_inquiry_entry.input_rules
				FROM t_inquiry_entry
				WHERE inquiry_id=:inquiry_id AND lang_cd=:lang_cd
			';
			$query = DB::query(Database::SELECT, $sql);
			$query->parameters(array(
				':inquiry_id' => $post['inquiry_id'],
				':lang_cd' => $post['lang_cd']
			));
			$results = $query->execute()->as_array();
			$names = [];
			$emails = [];

			$name_types = ['name_full', 'name_separate', 'text'];

			foreach ($results as $result) {
				$input_rules = json_decode($result['input_rules'], true);
				// name_full, name_separate, or text
				if (isset($input_rules['type']) && in_array($input_rules['type'], $name_types)) {
					$names[] = [
						'title' => $result['title'],
						'no' => $result['no']
					];
				}
				// email
				if (isset($input_rules['type']) && $input_rules['type'] == 'mail') {
					$emails[] = [
						'title' => $result['title'],
						'no' => $result['no']
					];
				}
			}
			$responseData = [
				'names' => $names,
				'emails' => $emails
			];
			$this->response->body(json_encode(['result' => $responseData, 'entries' => $this->_model->get_inquiry_filter_entries_by_lang($post['inquiry_id'], $post['lang_cd'])], JSON_UNESCAPED_UNICODE));
		} else {
			$this->response->body(json_encode(['result' => 'fail'], JSON_UNESCAPED_UNICODE));
		}
	}

	public function action_retrive_inquiry_result_name_and_email() {
		$post = $this->request->post();
		$support_lang_cd = $post['lang_cd'];
		$inquiry_id = $post['inquiry_id'];
		$mailNo = (isset($post['mail_no']) && trim($post['mail_no']) != '') ? $post['mail_no'] : NULL;
		$nameNo = (isset($post['name_no']) && trim($post['name_no']) != '') ? $post['name_no'] : NULL;
		$filters = ( isset($post['filters']) && $post['filters'] )? $post['filters'] : NULL;
		$entries = ( isset ($post ['entries']) && $post['entries'] ) ? $post['entries'] : NULL;
		$filtering = $filters && $entries;
		$result_datas = [];
		if ($support_lang_cd != NULL && $inquiry_id != NULL) {
			// 読み込み
			$inquiryModel = new Model_Inquirymodel();

			$nos = [];
			if ($nameNo != NULL) $nos[] = $nameNo;
			if ($mailNo != NULL) $nos[] = $mailNo;

			if ( $filtering ) {	// should retrive results of all entries(nos), for filtering
				foreach ($filters as $subConds) {
					foreach ($subConds as $subCond) {
						if ( ! in_array($subCond['entry_no'], $nos) ) {
							$nos[] = $subCond['entry_no'];
						}
					}
				}
			}
			
			$results = $inquiryModel->get_inquiry_result_name_and_email($inquiry_id, $support_lang_cd, $nos, $filtering);
			foreach ($results as $result) {
				$entry_results = json_decode($result['result'], true);

				// filter inquiry entry results
				if ( $filtering && ! $this->_model->filter_results($filters, $entry_results, $entries) ) {
					continue;
				}

				$temp = [];
				foreach ($entry_results as $entry_result) {
					if ($nameNo != NULL && $entry_result['no'] == $nameNo) {
						// use entry data instead of entry result
						$temp['name'] = $entry_result['entry_data'];
					}
					if ($mailNo != NULL && $entry_result['no'] == $mailNo) {
						// use entry data instead of entry result
						$temp['email'] = $entry_result['entry_data'];
					}
					//TODO future extend fields customization
				}
				if (!isset($temp['name']) || !isset($temp['email']) || $temp['name'] === '' || $temp['email'] === '') {
					continue; // Skip if email or name is empty
				}
				$temp['tags'] = '';
				$result_datas[] = $temp;
			}
		}
		$this->response->body(json_encode(['result' => $result_datas], JSON_UNESCAPED_UNICODE));
	}
	public function action_register_mail_member_from_inquiry() {
		$input_data = file_get_contents('php://input');
		$decoded_data = json_decode($input_data, true);
		$bot_id = (int) $this->_bot_id;
		
		//fetch project setting
		$project_id = $this->request->query('project_id', NULL);		
		if ( $project_id && ($project_id == 'other' || !is_numeric($project_id)) ) {
			$project_id = NULL;
		}

		$registered_count = 0;

		// regist task to t_mail_import_history
		$count = count( $decoded_data );
		$import_task_register_post_body = [
			'import_name' => 'Inquiry-'.(date("Y-m-d H:i:s")),
			'import_count' => $count,
		];
		$import_no = $this->_regist_mail_members_import_task($import_task_register_post_body);
		$status = 2;	// Importing
		$exclude_count = 0;
		$skip = 0;
		$msg = 'Importing...';
		$info = $this->_update_mail_members_import_task($import_no, $status, $registered_count, $msg, $exclude_count, $skip);
		$upd = time();


		// 重複しないメールアドレスの一覧を作成
		$unique_emails = array_unique(array_column($decoded_data, 'email'));
		$existing_records_sql = DB::select('email')
			->from('t_mail_member')
			->where('bot_id', '=', $bot_id)
			->and_where('email', 'IN', $unique_emails);

		// check within project scope 
		if ( $project_id && $project_id != 'other' ) {
			$existing_records_sql->where('project_id', '=', $project_id );
		} else {
			$existing_records_sql->where('project_id', 'IS', NULL);
		}

		$existing_records =	$existing_records_sql->execute()->as_array();
		
		$existing_emails = array_column($existing_records, 'email');
		// インサート用のデータ準備
		$insert_data = [];
		$processed_emails = [];
		foreach ($decoded_data as $data) {
			if (!in_array($data['email'], $existing_emails) && !in_array($data['email'], $processed_emails)) {
				$first_name = $data['name'];
				$last_name = "";
				$processed_emails[] = $data['email'];
				$insert_data[] = [
					$bot_id,
					$first_name,
					$last_name,
					$data['email'],
					date('Y-m-d H:i:s'),
					0
				];
			}
		}
		try {
			// DBの設定でデータは999件ごと登録
			$chunks = array_chunk($insert_data, 999);
			foreach ($chunks as $chunk) {
				Database::instance()->begin();
				foreach ($chunk as $row) {
					$row[] = $import_no;
					$row[] = $project_id;
					DB::insert('t_mail_member', ['bot_id', 'first_name', 'last_name', 'email', 'regist_time', 'delete_flg', 'import_no', 'project_id'])
						->values($row)
						->execute();
				}
				Database::instance()->commit();
				$registered_count += count($chunk);

				if(time() - $upd > 1) {
					$status = 2;	// Importing
					$info = $this->_update_mail_members_import_task($import_no, $status, $registered_count, $msg, $exclude_count, $skip);
					$upd = time();
				}
			}

			$members_inserted_select_query = db::select('mail_member_id', DB::expr($bot_id))
				->from('t_mail_member')
				->where('import_no', '=', $import_no);	
				
			$members_bot_insert_query = db::insert('t_mail_member_bot', array('mail_member_id', 'bot_id'));
			$members_bot_inserted_result = $members_bot_insert_query->select($members_inserted_select_query)->execute();
			$this->_logEcho("[import$import_no] newsletter member/bot relation bulk inserted count?: $members_bot_inserted_result[1]", true);
		} catch (Exception $e) {
			Database::instance()->rollback();
			Kohana::$log->add(Log::ERROR, 'Error: '.$e->getMessage());
			$this->response->body(json_encode(['error' => $e->getMessage()]));
			return;
		}

		$msg = 'Completed';
		$status = 3;
		$skip = $count - $registered_count;
		$info = $this->_update_mail_members_import_task($import_no, $status, $registered_count, $msg, $exclude_count, $skip);
		$msg = 'Imported '.$registered_count.' rows';

		$this->response->body(json_encode(['success'=>true, 'import_no' => $import_no, 'bot_id'=>$bot_id, 'info' => $info, 'status' => $status, 'registered_count' => $registered_count]));
	}

	public function action_get_survey_entries() {
		$post = $this->request->post();
		if (isset($post['survey_id']) && isset($post['lang_cd'])) {
			if (isset($post['for_newsletter']) && $post['for_newsletter'] == true) {
				$sql = '
					SELECT t_survey_entry.no, t_survey_entry.title, t_survey_entry.input_rules
					FROM t_survey_entry
					WHERE survey_id=:survey_id AND lang_cd=:lang_cd
				';
				$query = DB::query(Database::SELECT, $sql);
				$query->parameters(array(
					':survey_id' => $post['survey_id'],
					':lang_cd' => $post['lang_cd']
				));
				$results = $query->execute()->as_array();
				$names = [];
				$emails = [];
				$extends = []; 
				foreach ($results as $result) {
					$input_rules = json_decode($result['input_rules'], true);
					// メールアドレス
					if (isset($input_rules['type']) && $input_rules['type'] == 'mail') {
						$emails[] = [
							'title' => $result['title'],
							'no' => $result['no']
						];
					}
					// 名前
					if (isset($input_rules['type']) && $input_rules['type'] == 'text') {
						$names[] = [
							'title' => $result['title'],
							'no' => $result['no']
						];
					}
					// 拡張項目
					$extends[] = [
						'title' => $result['title'],
						'no' => $result['no'],
					];
				}
				$responseData = [
					'names' => $names,
					'emails' => $emails,
					'extends' => $extends
				];
				$this->response->body(json_encode(['result' => $responseData, 
					'entries' => $this->_model->get_survey_filter_entries_by_lang($post['survey_id'], $post['lang_cd'])], 
				JSON_UNESCAPED_UNICODE));
			} else {
				$entries = $this->_model->get_survey_filter_entries_by_lang($post['survey_id'], $post['lang_cd']);
				$this->response->body(json_encode(['result' => $entries], JSON_UNESCAPED_UNICODE));
			}
		} else {
			$this->response->body(json_encode(['result' => 'fail'], JSON_UNESCAPED_UNICODE));
		}
	}
	

	public function action_retrive_survey_result() {
		$post = $this->request->post();
		$support_lang_cd = $post['lang_cd'];
		$survey_id = $post['survey_id'];
		$mailNo = (isset($post['mail_no']) && trim($post['mail_no']) != '') ? $post['mail_no'] : NULL;
		$nameNo = (isset($post['name_no']) && trim($post['name_no']) != '') ? $post['name_no'] : NULL;
		$extendNos = isset($post['extend_nos_survey']) ? json_decode($post['extend_nos_survey'], true) : array();
		$filters = ( isset($post['filters']) && $post['filters'] ) ? $post['filters'] : NULL;
		$entries = ( isset($post['entries']) && $post['entries'] ) ? $post['entries'] : NULL;
		$filtering = $filters && $entries;
		$result_datas = [];
		if ($support_lang_cd != NULL && $survey_id != NULL) {
			// 読み込み
			$nos = array_merge(array_filter([$nameNo, $mailNo]), $extendNos);
			if ( $filtering ) {	// should retrive results of all entries(nos), for filtering
				foreach ($filters as $subConds) {
					foreach ($subConds as $subCond) {
						if ( ! in_array($subCond['entry_no'], $nos) ) {
							$nos[] = $subCond['entry_no'];
						}
					}
				}
			}
			$surveyModel = new Model_Surveymodel();
			$results = $surveyModel->get_survey_result_name_and_email_and_extends($survey_id, $support_lang_cd, $nos, $filtering);
			foreach ($results as $result) {
				$entry_results = json_decode($result['result'], true);
				if ( $filtering && ! $this->_model->filter_results($filters, $entry_results, $entries, true) ) {
					continue;
				}
				$temp = [];
				foreach ($entry_results as $entry_result) {
					if ($nameNo != NULL && $entry_result['no'] == $nameNo) {
						$temp['name'] = $entry_result['entry_data'];
					}
					if ($mailNo != NULL && $entry_result['no'] == $mailNo) {
						$temp['email'] = $entry_result['entry_data'];
					}
					$index = array_search($entry_result['no'], $extendNos);
					if ($index !== false) {
						$temp['extend_' . $index] = $entry_result['entry_data'];
					}
				}
				// name または email が空欄でないデータのみ読み込み
				if (!empty($temp['name']) && !empty($temp['email'])) {
					$temp['tags'] = '';
					$result_datas[] = $temp;
				}
			}
		}
		$this->response->body(json_encode(['result' => $result_datas], JSON_UNESCAPED_UNICODE));
	}
	
	public function action_register_mail_member_from_survey() {
		$input_data = file_get_contents('php://input');
		$decoded_data = json_decode($input_data, true);
		$members = $decoded_data['members'] ?? []; 
		$extends = $decoded_data['extends'] ?? [];
		$bot_id = (int) $this->_bot_id;

		//fetch project setting
		$project_id = NULL;
		if ( isset($decoded_data['project_id']) && $decoded_data['project_id'] ) {
			$project_id = $decoded_data['project_id'];
		}

		$registered_count = 0;

		// regist task to t_mail_import_history
		$count = count( $members );
		$import_task_register_post_body = [
			'import_name' => 'Survey-'.(date("Y-m-d H:i:s")),
			'import_count' => $count,
		];
		$import_no = $this->_regist_mail_members_import_task($import_task_register_post_body);
		$status = 2;	// Importing
		$exclude_count = 0;
		$skip = 0;
		$msg = 'Importing...';
		$info = $this->_update_mail_members_import_task($import_no, $status, $registered_count, $msg, $exclude_count, $skip);
		$upd = time();

		// 重複しないメールアドレスの一覧を作成
		$unique_emails = array_unique(array_column($members, 'email'));
		$existing_records_sql = DB::select('email')
							  ->from('t_mail_member')
							  ->where('bot_id', '=', $bot_id)
							  ->and_where('email', 'IN', $unique_emails);

		// check within project scope 
		if ( $project_id && $project_id != 'other' ) {
			$existing_records_sql->where('project_id', '=', $project_id );
		} else {
			$existing_records_sql->where('project_id', 'IS', NULL);
		}

		$existing_records =	$existing_records_sql->execute()->as_array();

		$existing_emails = array_column($existing_records, 'email');
		// インサート用のデータ準備
		$insert_data = [];
		$processed_emails = [];
		foreach ($members as $member) {
			if (!in_array($member['email'], $existing_emails) && !in_array($member['email'], $processed_emails)) {
				$first_name = $member['name'];
				$last_name = "";
				$processed_emails[] = $member['email'];
				$insert_data[] = [
					$bot_id,
					$first_name,
					$last_name,
					$member['email'],
					date('Y-m-d H:i:s'),
					0
				];
			}
		}
		try {
			$chunks = array_chunk($insert_data, 999);
			foreach ($chunks as $chunk) {
				Database::instance()->begin();
				foreach ($chunk as $index => $row) {
					$row[] = $import_no;
					$row[] = $project_id;
					list($mail_member_id) = DB::insert('t_mail_member', ['bot_id', 'first_name', 'last_name', 'email', 'regist_time', 'delete_flg', 'import_no', 'project_id'])
											  ->values($row)
											  ->execute();
					$currentMember = $members[$index];
					// 拡張項目
					foreach ($extends as $extend) {
						$extendCode = $extend['code'];
						$key = "extend_" . $extendCode;
						if (isset($currentMember[$key])) { 
							$extendValue = $currentMember[$key]; 
							list($extend_insert_id, $extend_affected_rows) = DB::insert('t_mail_member_extend', ['mail_member_id', 'extend_no', 'value', 'regist_time', 'delete_flg'])
																				->values([$mail_member_id, $extendCode, $extendValue, date('Y-m-d H:i:s'), 0])
																				->execute();
						}
					}
				}
				Database::instance()->commit();
				$registered_count += count($chunk);

				if(time() - $upd > 1) {
					$status = 2;	// Importing
					$info = $this->_update_mail_members_import_task($import_no, $status, $registered_count, $msg, $exclude_count, $skip);
					$upd = time();
				}
			}

			$members_inserted_select_query = db::select('mail_member_id', DB::expr($bot_id))
				->from('t_mail_member')
				->where('import_no', '=', $import_no);	
				
			$members_bot_insert_query = db::insert('t_mail_member_bot', array('mail_member_id', 'bot_id'));
			$members_bot_inserted_result = $members_bot_insert_query->select($members_inserted_select_query)->execute();
			$this->_logEcho("[import$import_no] newsletter member/bot relation bulk inserted count?: $members_bot_inserted_result[1]", true);
		} catch (Exception $e) {
			Database::instance()->rollback();
			Kohana::$log->add(Log::ERROR, 'Error: '.$e->getMessage());
			$this->response->body(json_encode(['error' => $e->getMessage()]));
			return;
		}

		$msg = 'Completed';
		$status = 3;
		$skip = $count - $registered_count;
		$info = $this->_update_mail_members_import_task($import_no, $status, $registered_count, $msg, $exclude_count, $skip);
		$msg = 'Imported '.$registered_count.' rows';

		$this->response->body(json_encode(['success'=>true, 'import_no' => $import_no, 'bot_id'=>$bot_id, 'info' => $info, 'status' => $status, 'registered_count' => $registered_count]));
	}

	public function action_retrive_account_name_and_email() {
		$post = $this->request->post();
		$bot_id = NULL;
		if ( isset($post['bot_id']) && $post['bot_id'] != '' && $post['bot_id'] != '-' ) {
			$bot_id = $post['bot_id'];
		}
		$result_datas = [];
		// 読み込み
		$entries = $this->_model->get_user_email_from_enabled_bot($bot_id);
		$result_datas = $entries;
		$this->response->body(json_encode(['result' => $result_datas], JSON_UNESCAPED_UNICODE));
	}

	public function action_register_mail_member_from_account() {
		$input_data = file_get_contents('php://input');
		$decoded_data = json_decode($input_data, true);
		$members = $decoded_data['members'] ?? []; 
		$bot_id = (int) $this->_bot_id;
		$registered_count = 0;

		$project_id = NULL;
		if ( isset($decoded_data['project_id']) && $decoded_data['project_id'] ) {
			$project_id = $decoded_data['project_id'];
		}

		// regist task to t_mail_import_history
		$count = count( $members );
		$import_task_register_post_body = [
			'import_name' => 'MgtAccount-'.(date("Y-m-d H:i:s")),
			'import_count' => $count,
		];
		$import_no = $this->_regist_mail_members_import_task($import_task_register_post_body);
		$status = 2;	// Importing
		$exclude_count = 0;
		$skip = 0;
		$msg = 'Importing...';
		$info = $this->_update_mail_members_import_task($import_no, $status, $registered_count, $msg, $exclude_count, $skip);
		$upd = time();

		// 重複しないメールアドレスの一覧を作成
		$unique_emails = array_unique(array_column($members, 'email'));
		$existing_records_sql = DB::select('email')
								->from('t_mail_member')
								->where('bot_id', '=', $bot_id)
								->and_where('email', 'IN', $unique_emails);
		// check within project scope 
		if ( $project_id && $project_id != 'other' ) {
			$existing_records_sql->where('project_id', '=', $project_id );
		} else {
			$existing_records_sql->where('project_id', 'IS', NULL);
		}
		$existing_records =	$existing_records_sql->execute()->as_array();
		$existing_emails = array_column($existing_records, 'email');

		// インサート用のデータ準備
		$insert_data = [];
		$processed_emails = [];
		foreach ($members as $member) {
			if (!in_array($member['email'], $existing_emails) && !in_array($member['email'], $processed_emails)) {
				if (isset($member['first_name'])) {
					$first_name = $member['first_name'];
				} else {
					$first_name = $member['name'];
				}
				$last_name = "";
				if (isset($member['last_name'])) {
					$last_name = $member['last_name'];
				}
				$processed_emails[] = $member['email'];
				$insert_data[] = [
					$bot_id,
					$first_name,
					$last_name,
					$member['email'],
					date('Y-m-d H:i:s'),
					0
				];
			}
		}
		try {
			// DBの設定でデータは999件ごと登録
			$chunks = array_chunk($insert_data, 999);
			foreach ($chunks as $chunk) {
				Database::instance()->begin();
				foreach ($chunk as $row) {
					$row[] = $import_no;
					$row[] = $project_id;
					DB::insert('t_mail_member', ['bot_id', 'first_name', 'last_name', 'email', 'regist_time', 'delete_flg', 'import_no', 'project_id'])
						->values($row)
						->execute();
				}
				Database::instance()->commit();
				$registered_count += count($chunk);

				if(time() - $upd > 1) {
					$status = 2;	// Importing
					$info = $this->_update_mail_members_import_task($import_no, $status, $registered_count, $msg, $exclude_count, $skip);
					$upd = time();
				}
			}

			$members_inserted_select_query = db::select('mail_member_id', DB::expr($bot_id))
				->from('t_mail_member')
				->where('import_no', '=', $import_no);	
				
			$members_bot_insert_query = db::insert('t_mail_member_bot', array('mail_member_id', 'bot_id'));
			$members_bot_inserted_result = $members_bot_insert_query->select($members_inserted_select_query)->execute();
			$this->_logEcho("[import$import_no] newsletter member/bot relation bulk inserted count?: $members_bot_inserted_result[1]", true);
		} catch (Exception $e) {
			Database::instance()->rollback();
			Kohana::$log->add(Log::ERROR, 'Error: '.$e->getMessage());
			$this->response->body(json_encode(['error' => $e->getMessage()]));
			return;
		}

		$msg = 'Completed';
		$status = 3;
		$skip = $count - $registered_count;
		$info = $this->_update_mail_members_import_task($import_no, $status, $registered_count, $msg, $exclude_count, $skip);
		$msg = 'Imported '.$registered_count.' rows';

		$this->response->body(json_encode(['success'=>true, 'import_no' => $import_no, 'bot_id'=>$bot_id, 'info' => $info, 'status' => $status, 'registered_count' => $registered_count]));
	}

	public function action_retrieve_memberemails() {
		$params = [
			'bot_id' => $this->_bot_id
		];
		$members = $this->_model->call_admin_api('member', 'memberemails', 'POST', $params);
		$this->response->body(json_encode(['result' => $members], JSON_UNESCAPED_UNICODE));
	}
	

	public function action_save_inquiry_result_condition()
	{
		$post = $this->request->post();
		$inquiryId = $post['inquiry_id'];
		$no = isset($post['no']) ? $post['no'] : null;
		$langCd = $post['lang_cd'];
		$conditions = $post['conditions'];
		$type = 'result';

		if ($no) {
			// noがある場合は既存レコードを更新
			DB::update('t_inquiry_result_selection')
			->set([
				'lang_cd' => $langCd,
				'type' => $type,
				'condition_data' => json_encode($conditions)
			])
				->where('inquiry_id', '=', $inquiryId)
				->where('no', '=', $no)
				->where('lang_cd', '=', $langCd)
				->execute();
		} else {
			$maxNoResult = DB::select([DB::expr('MAX(no)'), 'max_no'])
			->from('t_inquiry_result_selection')
			->where('inquiry_id', '=', $inquiryId)
				->execute()
				->current();

			$maxNo = $maxNoResult ? $maxNoResult['max_no'] : 0;
			$no = $maxNo + 1;
			DB::insert('t_inquiry_result_selection', ['inquiry_id', 'no', 'type', 'lang_cd', 'condition_data', 'delete_flg'])
			->values([$inquiryId, $no, $type, $langCd, json_encode($conditions), 0])
				->execute();
		}
		$this->response->body(json_encode(['result' => $no], JSON_UNESCAPED_UNICODE));
	}

	public function action_delete_inquiry_result_condition()
	{
		$post = $this->request->post();
		$inquiryId = $post['inquiry_id'];
		$no = $post['no'];
		$langCd = $post['lang_cd'];

		$result = DB::update('t_inquiry_result_selection')
		->set(['delete_flg' => 1])
			->where('inquiry_id', '=', $inquiryId)
			->where('no', '=', $no)
			->where('lang_cd', '=', $langCd)
			->execute();

		$this->response->body(json_encode(['result' => $result], JSON_UNESCAPED_UNICODE));
	}

	// INQUIRY回答結果の自動翻訳
	public function action_auto_translate_inquiry_result() {
		$post = $this->request->post();
		$text = $post['text'];
		$from_lang_cd = $post['from_lang_cd'];
		$to_lang_cd = $post['to_lang_cd'];
		$result = $this->_model->translate($text, $to_lang_cd, $from_lang_cd);
		$this->response->body(json_encode(['result' => $result], JSON_UNESCAPED_UNICODE));
	}

	public function action_get_class_code_name_icons()
	{
		$post = $this->request->post();
		$code_div = $post['code_div'];
		$lang_cd = $this->_lang_cd;
		$data = ORM::factory('classcode')->where('code_div', '=', $code_div)->order_by('class_cd')->find_all();
		$result = [];

        foreach ($data as $item) {
            if (!isset($result[$item->class_cd]) && $item->lang_cd == $lang_cd) {
                $result[$item->class_cd] = [
                    "class_cd" => $item->class_cd,
                    "parent_cd" => $item->parent_cd,
                    "sort" => $item->sort,
                    "show_items_flg" => $item->show_items_flg,
                    "bot_id" => $item->bot_id,
                    "grid_pic_url" => $item->grid_pic_url,
					"name" => $item->name,
                ];
            }
        }

		$structuredResult = [];
		foreach ($result as $key => $value) {
			if ($value['parent_cd'] === "") {
				$firstLayerName = $value['name']; // Assuming 'ja' is always set
				foreach ($result as $innerKey => $innerValue) {
					if ($innerValue['parent_cd'] === $key) {
						$secondLayerNameJa = isset($innerValue['name']) ? $innerValue['name'] : '';
						$sort = isset($innerValue['sort']) ? $innerValue['sort'] : '';
						$gridPicUrl = $innerValue['grid_pic_url'];
						$structuredResult[$firstLayerName][] = [
							'name' => $secondLayerNameJa,
							'sort' => $sort,
							'gridPicUrl' => $gridPicUrl,
						];
					}
				}
			}
		}
		$this->response->body(json_encode(['result' => $structuredResult], JSON_UNESCAPED_UNICODE));
	}

	public function action_get_very_settings()
	{
		$admin_model = new Model_Adminmodel();
		$default_scene_cd = $admin_model->get_bot_setting($this->_bot_id, 'default_scene_cd');
		$very_orms = ORM::factory('very')
		->where('bot_id', '=', $this->_bot_id)
		->where('scene_cd', '=', $default_scene_cd)
		->where('setting', '=', 'style')
		->where('page', '=', 'top')
		->find();

		$result = null;
		if (isset($very_orms->value)){
			$result = json_decode($very_orms->value);
		}

		$this->response->body(json_encode(['result' => $result], JSON_UNESCAPED_UNICODE));
	}

	public function action_get_scenes_by_id() {
		try {
			$bot_id = null;
			$post = $this->request->post();
			if (isset($post['bot_id'])) {
				$bot_id = $post['bot_id'];
			}
			if (is_null($bot_id)) {
				throw new Exception('no bot_id');
			}
			$results = DB::select('scene_name', 'label')
			->from('t_bot_scene')
			->where('bot_id', '=', $bot_id)
			->execute()
			->as_array();
			$response = [
				'result' => 'true',
				'data' => $results
			];
			$this->response->body(json_encode($response, JSON_UNESCAPED_UNICODE));
		} catch (\Throwable $th) {
			$response = [
				'result' => 'false',
				'data' => null,
				'error' => $th->getMessage()
			];
			$this->response->body(json_encode($response, JSON_UNESCAPED_UNICODE));
		}
	}

	public function action_get_default_scene_by_id() {
		try {
			$post = $this->request->post();
			if (!isset($post['bot_id'])) {
				throw new Exception('no bot_id');
			}
			$result = DB::select('setting_value')
			->from('t_bot_setting')
			->where('bot_id', '=', $post['bot_id'])
			->where('setting_cd', '=', 'default_scene_cd')
			->where('delete_flg', '=', '0')
			->execute()
			->as_array();
			$response = [
				'result' => 'true',
			];
			if (count($result) > 0) {
				$response['data'] = $result[0]['setting_value'];
			} else {
				$response['data'] = null;
			}
			$this->response->body(json_encode($response, JSON_UNESCAPED_UNICODE));
		} catch (\Throwable $th) {
			$response = [
				'result' => 'false',
				'data' => null,
				'error' => $th->getMessage()
			];
			$this->response->body(json_encode($response, JSON_UNESCAPED_UNICODE));
		}
	}

	public function action_get_very_top_period() {
		try {
			$post = $this->request->post();
			if (!isset($post['bot_id'])) {
				throw new Exception('no bot_id');
			}
			if (!isset($post['scene_cd'])) {
				throw new Exception('no scene_cd');
			}
			$results = DB::select('scene_cd', 'value')
			->from('t_very')
			->where('bot_id', '=', $post['bot_id'])
			->where('scene_cd', '!=', $post['scene_cd'])
			->where('setting', '=', 'period')
			->execute()
			->as_array();
			$response = [
				'result'=> 'true',
				'data' => $results
			];
			$this->response->body(json_encode($response, JSON_UNESCAPED_UNICODE));
		} catch (\Throwable $th) {
			$response = [
				'result' => 'false',
				'error' => $th->getMessage()
			];
			$this->response->body(json_encode($response, JSON_UNESCAPED_UNICODE));
		}
	}

	/*
	 * mail_template に対して、指定された言語のテンプレートが存在するかを確認する
	 * 
	 */
	public function action_get_mail_template_langs() {
		$post = $this->request->post();
		try {
			$bots = [$this->_bot_id, 0];
			$grp_bot_id = $this->_model->get_grp_bot_id($this->_bot_id);
			if ($grp_bot_id > 0) {
				$bots[] = $grp_bot_id;
			}
			$mail_templates = [];
			$ci_mapping = [
				
			];
			if (isset($post['user_mail_template'])) {
				$mail_templates[] = $post['user_mail_template'];
				// user_mail_template to lower case
				$ci_mapping[mb_strtolower($post['user_mail_template'])] = $post['user_mail_template'];
			}
			if (isset($post['member_mail_template'])) {
				$mail_templates[] = $post['member_mail_template'];
				// member_mail_template to lower case
				$ci_mapping[mb_strtolower($post['member_mail_template'])] = $post['member_mail_template'];
			}
			if (count($mail_templates) == 0) {
				$response = [
					'result' => 'true',
					'data' => []
				];
				$this->response->body(json_encode($response, JSON_UNESCAPED_UNICODE));
			} else {
				$sql = "SELECT a.msg_id, a.bot_id, a.msg_cd, GROUP_CONCAT(b.lang_cd) as langs
				FROM `t_bot_msg` a
				LEFT JOIN t_bot_msg_desc_tpl b ON a.msg_id=b.msg_id
				WHERE a.bot_id in :bots AND a.msg_cd in :mail_templates
				GROUP BY a.msg_id, a.bot_id;";
				$query = DB::query(Database::SELECT, $sql);
				$query->parameters(array(
					':bots' => $bots,
					':mail_templates' => $mail_templates
				));
				$results = $query->execute()->as_array();
				$filter_results = [];
				foreach ($results as $result) {
					// $msg_cd = $result['msg_cd'];
					$msg_cd = $ci_mapping[mb_strtolower($result['msg_cd'])];
					$langs = $result['langs'] == NULL ? [] : explode(",", $result['langs']);
					if (!isset($filter_results[$msg_cd])) {
						$filter_results[$msg_cd] = $langs;
					}
					$exited_langs = $filter_results[$msg_cd];
					$combined_langs = array_unique(array_merge($exited_langs, $langs));
					$filter_results[$msg_cd] = array_values($combined_langs);
				}
				$response = [
					'result' => 'true',
					'data' => $filter_results
				];
				$this->response->body(json_encode($response, JSON_UNESCAPED_UNICODE));
			}
		} catch (\Throwable $th) {
			$response = [
				'result' => 'false',
				'error' => $th->getMessage()
			];
			$this->response->body(json_encode($response, JSON_UNESCAPED_UNICODE));
		}
	}

	public function action_save_translate_operation_data()
	{
		$post = $this->request->post();
		$request_id = $this->request->query('request_id');
		$lang_cd = $this->request->query('lang_cd');
		$post['upd_user'] =  $this->_user_id;
		$post['upd_date'] = (new DateTime())->format('Y-m-d H:i:s');
		if (isset($post['release_date']) && $post['release_date'] == '') {
			$post['release_date'] = null;
		}
		if (isset($post['translate_user']) && $post['translate_user'] == '') {
			$post['translate_user'] = null;
		}
		$this->_model->update_translate_operation($request_id, $lang_cd, $post);
		try {
			// 1) 全言語の status の最小値を取得
			$min_status_sql = "SELECT MIN(status) AS min_status FROM t_translate_operation WHERE request_id = :request_id";
			$min_status_row = DB::query(Database::SELECT, $min_status_sql)
			                    ->parameters([':request_id' => $request_id])
			                    ->execute()
			                    ->current();

			// レコードが無い場合はQUIT
			if ($min_status_row === null || !isset($min_status_row['min_status'])) {
				throw new Exception('No records found for this request.');
			}
			$status = $min_status_row['min_status'];

			// 2) 対応する Redmine チケットを取得
			$translate_request_sql = "SELECT redmine_issue_id FROM t_translate_request WHERE request_id = :request_id";
			$translate_request = DB::query(Database::SELECT, $translate_request_sql)
			                        ->parameters([':request_id' => $request_id])
			                        ->execute()
			                        ->as_array();
			if (count($translate_request) > 0) {
				$issue_id = $translate_request[0]['redmine_issue_id'] ?? null;
				if ($issue_id === null) {
					throw new Exception('Redmine issue ID is not set for this request.');
				}
				// redmine status mapping
				$redmine_status = $this->_model->mapping_from_translate_request_status_to_redmine_status($status);
				$this->_model->update_redmine_issue_status($issue_id, $redmine_status);
			}
		} catch (Exception $e) {
			
		}
		$this->response->body(json_encode(['result' => 'success']));
	}

	public function action_update_translation_type() {
		$post = $this->request->post();
		$bot_id = $this->_bot_id;
		$item_id = $post['item_id'];
		$item_div = $post['item_div'];
		$lang_cd = $post['lang_cd'];
		$value = $post['value'];
		if (isset($post['bot_id'])) {
			$bot_id = $post['bot_id'];
		}
		if ($bot_id === null || $bot_id === '' || $item_id == null || $item_id == '' || $item_div == null || $item_div == '') {
			$this->response->status(500)->body(json_encode(['success' => 'false', 'error' => 'parameter error'], JSON_UNESCAPED_UNICODE));
		} else {
			try {
				$auto_translation_lang = $this->_model->get_auto_translate_lang($bot_id, $item_id, $item_div);
				$new_auto_translation_lang = $auto_translation_lang;
				if ($value == '0') {
					$new_auto_translation_lang = array_diff($auto_translation_lang, [$lang_cd]);
				} else {
					if (!in_array($lang_cd, $auto_translation_lang)) {
						$new_auto_translation_lang = array_merge($auto_translation_lang, [$lang_cd]);
					}
				}
				$this->_model->update_auto_translate_lang($bot_id, $item_id, $item_div, $new_auto_translation_lang);
				$this->response->status(200)->body(json_encode(['success' => 'true'], JSON_UNESCAPED_UNICODE));
			} catch (\Throwable $th) {
				$this->response->status(500)->body(json_encode(['success' => 'false', 'error' => $th->getMessage()], JSON_UNESCAPED_UNICODE));
			}
		}
	}

	public function generate_payment_advice_pdf($data) {
		$mpdf = new \Mpdf\Mpdf([
			'mode' => 'utf-8',
			'format' => 'A4',
			'orientation' => 'P',
			'tempDir' => __DIR__ . '/../../../../files'
		]);
		$mpdf->allow_charset_conversion = true;
		$mpdf->autoLangToFont = true;
		$mpdf->charset_in = 'UTF-8';
		// テンプレートの取得
		$payment_template = $this->_model->get_bot_tpl_message($this->_bot_id, 'payment_notification_template', 'ja');
		if ($payment_template == '') {
			return null;
		}
		// データの挿入
		// pdf：「C.差引支払額」（A. 決済金額合計 - B. 控除金額合計）
		$data["net_payment"] = number_format(intval(str_replace(",", "", $data["sales_amount"]) - str_replace(",", "", $data["total_deduction"])));
		foreach ($data as $key => $value) {
			$field = '{' . $key . '}';
			if ($key !== 'services') {
				$payment_template = str_replace($field, $value, $payment_template);
			} else {
				$service_template = "";
				$count = count($value);
				foreach ($value as $service) {
					$tr = "<tr>";
					$tr .= "<td>" . $service["num"] . "</td>";
					$tr .= "<td>" . $service["name"] . "</td>";
					$tr .= "<td class='amount'>" . ($service["amount"] == "" ? "-" : "¥" . $service["amount"]) . "</td>";
					$tr .= "<td class='amount'>" . ($service["comission_fee"] == "" ? "-" : "¥" . $service["comission_fee"]) . "</td>";
					$tr .= "<td class='amount'>" . ($service["notification_costs"] == "" ? "-" : "¥" . $service["notification_costs"]) . "</td>";
					$tr .= "</tr>";
					$service_template .= $tr;
				}
				for ($i = 0; $i < (5 - $count); $i++) {
					$service_template .= "<tr><td>&nbsp;</td><td>&nbsp;</td><td class='amount'>&nbsp;</td><td class='amount'>&nbsp;</td><td class='amount'>&nbsp;</td></tr>";
				}
				$payment_template = str_replace('{services}', $service_template, $payment_template);
			}
		}
		$mpdf->WriteHTML($payment_template);
		return $mpdf->Output('', 'S');
	}

	public function generate_payment_advice_number($month_report_id) {
		// 該当レポートの年月とclient_idを取得
		$report = db::select('due_date_to', 'client_id')
			->from(DB::expr('t_pay_monthly_report'))
			->where('id', '=', $month_report_id)
			->execute()
			->current();
		if (!$report) {
			throw new Exception('Report not found');
		}
		$client_id = $report['client_id'];
		$currentDate = new DateTime();
		$year = $currentDate->format('Y');
		$month = $currentDate->format('m');
		$current_day = (int)$currentDate->format('j');
		// yyyymmと直後の番号の設定
		if ($current_day < 5) {
			$year_month = $year . $month;
			$number_part = '1';
		} elseif ($current_day >= 5 && $current_day <= 19) {
			$year_month = $year . $month;
			$number_part = '2';
		} else {
			$currentDate->modify('+1 month'); // 翌月
			$year_month = $currentDate->format('Ym');
			$number_part = '1';
		}
		// バージョンはv1固定
		$version_part = "v1";
		// 番号を生成
		$payment_number = "PA-{$client_id}-{$year_month}-{$number_part}-{$version_part}";
		return $payment_number;
	}

	public function fetchHolidays() {
		// 祝祭日を取得
		$url = "https://holidays-jp.github.io/api/v1/date.json";
		$json = file_get_contents($url);
		return array_keys(json_decode($json, true));
	}

	public function generate_payment_date() {
		// 振り込み日の算出。1日〜4日であれば同月10日振り込み、5日〜19日であれば同月25日振り込み、20日以降は翌月10日振り込み。ただし土日祝祭日の場合は除く。
		$holidays = $this->fetchHolidays();
		$currentDate = new DateTime();
		$year = $currentDate->format('Y');
		$month = $currentDate->format('m');
		$day = (int)$currentDate->format('d');
		// 振り込み日
		if ($day < 5) {
			$targetDay = 10;
			$targetDate = new DateTime("{$year}-{$month}-{$targetDay}");
		} elseif ($day >= 5 && $day <= 19) {
			$targetDay = 25;
			$targetDate = new DateTime("{$year}-{$month}-{$targetDay}");
		} else {
			$targetDay = 10;
			$targetDate = new DateTime("{$year}-{$month}-{$targetDay}");
			$targetDate->modify('+1 month'); // 翌月
		}
		// 振り込み日が土日祝祭日の場合は次の平日まで繰り延べ
		while (in_array($targetDate->format('Y-m-d'), $holidays) || $targetDate->format('N') >= 6) {
			$targetDate->modify('+1 day');
		}
		return $targetDate->format('Y-m-d');
	}
	public function action_paymentmail() 
	{
		// talkappi PAY支払い通知書のメール送付
		if ($this->request->post()){
			if($this->_bot_id != 800){
				// カスタマイズコンテンツ payment_notification_template を登録すれば800以外でも可能
				$this->response->body(json_encode(['status' => 'invalid_bot', 'message' => 'This operation must be handled manually via bot ID 800.']));
				return;
			}
			$post = $this->request->post();
			Log::instance()->add(Log::DEBUG, "Payment mail request: " . json_encode($post));
			try {
				// レポートID
				if (isset($post['month_report_id']) && $post['month_report_id']) {
					$month_report_id = $post['month_report_id'];
				}
				// クライアントID
				if (isset($post['client_id']) && $post['client_id']) {
					$client_id = $post['client_id'];
				}

				// 支払い通知書の作成
				// 月報の情報取得
				$monthly_report_info = db::select()
					->from(DB::expr('t_pay_monthly_report'))
					->where('id', '=', $month_report_id)
					->execute()
					->current();
				// 日付が無効な場合はエラー
				if (empty($monthly_report_info['due_date_from']) || $monthly_report_info['due_date_from'] === '0000-00-00' ||
					empty($monthly_report_info['due_date_to']) || $monthly_report_info['due_date_to'] === '0000-00-00') {
					$this->response->body(json_encode(['status' => 'invalid_date', 'message' => 'Start date or end date is missing or invalid']));
					return;
				}
				// メール受信者の情報取得
				$receiver_info = db::select()
					->from(DB::expr('t_pay_client'))
					->where('client_id', '=', $client_id)
					->execute()
					->current();
				Log::instance()->add(Log::DEBUG, "Receiver info: " . json_encode($receiver_info));
				// 会社名、担当者名、担当者メールアドレス
				$company_name = $receiver_info['name'];
				$accounting_person = json_decode($receiver_info['accounting_person'], true);
				$accounting_person_name = $accounting_person['name'];
				$accounting_person_email = $accounting_person['email'];
				$bot_values = explode(",", $receiver_info['bot']);
				$bot_info = db::select('bot_name')
					->from('t_bot')
					->where('bot_id', 'IN', $bot_values)
					->execute()
					->as_array();
				Log::instance()->add(Log::DEBUG, "Bot info: " . json_encode($bot_info));
				$facitily_name = implode(', ', array_column($bot_info, 'bot_name'));
				// 空の場合はエラー
				if (empty($company_name) || empty($accounting_person_name) || empty($accounting_person_email)) {
					$this->response->body(json_encode(['status' => 'invalid_receiver', 'message' => 'Company name, accounting person name or email is missing']));
					return;
				}

				// 振込先の取得
				try {
					$account_number = $receiver_info['account_number'];
					// 金融機関コード、支店コード、口座番号
					list($bank_code, $branch_code, $account_num) = explode('-', $account_number);
					// APIで金融機関名と支店名を取得
					$bank_info = file_get_contents("https://bank.teraren.com/banks/{$bank_code}.json");
					if ($bank_info === FALSE) {
						throw new Exception('Invalid bank code');
					}
					$bank_info = json_decode($bank_info, true);
					$branch_info = file_get_contents("https://bank.teraren.com/banks/{$bank_code}/branches/{$branch_code}.json");
					if ($branch_info === FALSE) {
						throw new Exception('Invalid branch code');
					}
					$branch_info = json_decode($branch_info, true);
					$bank_name = $bank_info['normalize']['name'];
					$branch_name = $branch_info['name'];
					$bank_code = $bank_info['code'];
					$branch_code = $branch_info['code'];
					// 口座種別
					$account_type = $receiver_info['account_type'];
					if ($account_type === '01') {
						$account_type_name = '普通';
					} elseif ($account_type === '02') {
						$account_type_name = '当座';
					} else {
						throw new Exception('Invalid account type');
					}
				} catch (Exception $e) {
					$this->response->body(json_encode(['status' => 'invalid_account_number', 'message' => $e->getMessage()]));
					return;
				}

				// 金額の取得
				try {
					// 入金金額（振り込み手数料の考慮前）
					$transfer_money = (int)$monthly_report_info["transfer_money"];
					// 売上金額（pdf：決済合計金額）
					$sales_amount = (int)$monthly_report_info['sales_amount'];
					// talkappi手数料（pdf：talkappiPAY利用料）
					$acti_commision = (int)$monthly_report_info['acti_commision'];
					// トランザクション費用（pdf：通知費用）
					$transaction_fee = (int)$monthly_report_info['transaction_fee'];
					// 振込手数料（pdf：振込手数料）
					$transfer_fee = (int)$monthly_report_info["transfer_money"] < 0 ? 0 : (int)$monthly_report_info['transfer_fee'] / 1.1; // 税抜金額
					// 消費税（pdf：消費税）
					if ((int)$monthly_report_info["transfer_money"] > 0) {
						// 入金金額が計算上0より大きい時は振込手数料の税を加算
						$tax = (int)$monthly_report_info['tax'] + (int)$monthly_report_info['transfer_fee'] * 0.1 / 1.1;  // 管理画面上の消費税 + 振込手数料の税
					} else {
						// 入金金額が計算上0以下の時は振込手数料の税を加算しない（振り込み手数料が0のため）
						$tax = (int)$monthly_report_info['tax'];
					}
					// pdf：控除金額合計
					$total_deduction = $acti_commision + $transaction_fee + $tax + $transfer_fee;
				} catch (Exception $e) {
					$this->response->body(json_encode(['status' => 'invalid_amount', 'message' => $e->getMessage()]));
					return;
				}

				// 書類番号の作成
				$payment_number = $this->generate_payment_advice_number($month_report_id);

				// 振り込み日の算出
				$payment_date = $this->generate_payment_date();

				// pdfの作成
				$data = [
					"document_number" => $payment_number,
					"issue_date" => date('Y年m月d日'),
					"recipient_name" => $company_name,
					"facitily_name" => $facitily_name,
					"staff_name" => $this->_model->get_bot_tpl_message($this->_bot_id, 'payment_notification_staff_name', 'ja'),
					"amount_paid" => $transfer_money,
					"payment_date" => date('Y年m月d日', strtotime($payment_date)),
					"bank_name" => $bank_name,
					"bank_code" => $bank_code,
					"branch_name" => $branch_name,
					"branch_code" => $branch_code,
					"account_type" => $account_type_name,
					"account_number" => $account_num,
					"account_name" => "",
					// 売上金額（pdf：決済合計金額）
					"sales_amount" => number_format($sales_amount),
					// pdf：控除金額合計
					"total_deduction" => number_format($total_deduction),
					// talkappi手数料（pdf：talkappiPAY利用料）
					"acti_commision" => number_format($acti_commision),
					// トランザクション費用（pdf：通知費用）
					"transaction_fee" => number_format($transaction_fee),
					// 振込手数料（pdf：振込手数料）
					"total_amount" => number_format($acti_commision + $transaction_fee + $transfer_fee), // PAY利用料 + 通知費用 + 振込手数料
					// 消費税（pdf：消費税）
					"tax" => number_format($tax),
					// 振込手数料（pdf：振込手数料）
					"transfer_fee" => number_format($transfer_fee),
					"services" => [
						[
							"num" => "1",
							"name" => "取引実績（" . date('Y年m月d日', strtotime($monthly_report_info['due_date_from'])) . "〜" . date('Y年m月d日', strtotime($monthly_report_info['due_date_to'])) . "）",
							"amount" => number_format($sales_amount),
							"comission_fee" => number_format($acti_commision),
							"notification_costs" => "-",
						],
						[
							"num" => "2",
							"name" => "通知費用",
							"amount" => "-",
							"comission_fee" => "-",
							"notification_costs" => number_format($transaction_fee),
						]
					],
				];
				Log::instance()->add(Log::DEBUG, "Payment advice data: " . json_encode($data));
				$pdf_content = $this->generate_payment_advice_pdf($data);
				if ($pdf_content === null) {
					$this->response->body('error');
					return;
				}
				$pdf_base64 = base64_encode($pdf_content);
				Log::instance()->add(Log::DEBUG, "PDF base64 (first 100 chars): " . substr($pdf_base64, 0, 100));

				if (isset($post['action_type']) && $post['action_type'] === 'preview') {
					// PDFプレビュー：PDF情報を渡してajax終了
					$this->response->body(json_encode(['pdf_base64' => $pdf_base64]));
					return false;
				}

				// メールの生成・送付
				$param = [];
				$param['bot_id'] = '800'; // べりー君
				$param['sender'] = '<EMAIL>';
				$param['lang_cd'] = 'ja';
				$param['receiver'] = $accounting_person_email;
				$param['receiver_cc'] = '<EMAIL>';
				$param['message_cd'] = 'payment_notification_mail';
				$param['params'] = [
					'company_name'=> $company_name,
					'accounting_person_name'=> $accounting_person_name,
					'start_date'=> $monthly_report_info['due_date_from'],
					'end_date'=> $monthly_report_info['due_date_to']
				];
				
				// pdfファイル名:$payment_number_会社名_支払い通知書.pdf
				$filename = $payment_number . "_" . $company_name . "_支払い通知書.pdf";
				Log::instance()->add(Log::DEBUG, "Payment advice filename: " . $filename);
				$param['attachments'] = [
					[
						'name' => $filename,
						'data' => $pdf_base64,
					]
				];
				// メール送付
				$data = $this->_model->post_enginehook('service', 'sendmail','', $param);
				Log::instance()->add(Log::DEBUG, "Mail response: " . json_encode($data));
				// メール送付日時、ユーザーを記録
				$upd_data = [
					'send_flg' => 'auto',
					'send_user' => $this->_user_id,
					'send_time' => date('Y-m-d H:i:s')
				];
				$result = DB::update('t_pay_monthly_report')
					->set($upd_data)
					->where('id', '=', $month_report_id)
					->execute();
				$this->response->body(json_encode(['status' => 'success', 'message' => 'Email sent successfully']));
			} catch (Exception $e) {
				Log::instance()->add(Log::DEBUG, "Error in paymentmail: " .  $e->getMessage());
				$this->response->body(json_encode(['status' => 'error', 'message' => $e->getMessage()]));
				return;
			}
		}
	}

	public function action_paymentmail_manual(){
		// 手動で送信済みに変更
		if ($this->request->post()){
			if($this->_bot_id != 800){
				$this->response->body(json_encode(['status' => 'invalid_bot', 'message' => 'This operation must be handled manually via bot ID 800.']));
				return;
			}
			$post = $this->request->post();

			try {
				$upd_data = [
					'send_flg' => 'manual',
					'send_user' => $this->_user_id,
					'send_time' => date('Y-m-d H:i:s')
				];
				
				$result = DB::update('t_pay_monthly_report')
				->set($upd_data)
				->where('id', '=', $post['month_report_id'])
				->execute();

				$this->response->body(json_encode(['status' => 'success', 'message' => 'Manual email send successfully']));
			} catch (Exception $e) {
				Log::instance()->add(Log::DEBUG, "Error in paymentmail_manual: " .  $e->getMessage());
				$this->response->body(json_encode(['status' => 'error', 'message' => $e->getMessage()]));
			}
		}
	}

	public function action_save_laundry_setting_data()
	{
		$post = $this->request->post();
		$user_id = $this->_user_id;
		$bot_id = $this->_bot_id;
		$lang_cd = $this->request->query('lang_cd');

		$isTranslateAuto = $post['isTranslateAuto'];
		$selectedLanguages = $post['selectedLanguages'];
		$laundryData = $post['laundryData'];

		if ($isTranslateAuto && !empty($selectedLanguages)) {
			$translatedValues = [];
			foreach ($selectedLanguages as $lang) {
				$translatedValue = $laundryData;  // 翻訳元のデータをコピー
				$translatedValue['usage_info']['title'] = $this->_model->translate($translatedValue['usage_info']['title'], $lang);
				$translatedValue['usage_info']['description'] = $this->_model->translate($translatedValue['usage_info']['description'], $lang);
				foreach ($translatedValue['laundry_list'] as &$device) {
					$device['name'] = $this->_model->translate($device['name'], $lang);
					$device['description'] = $this->_model->translate($device['description'], $lang);
				}
				$translatedValues[$lang] = $translatedValue;  // 各言語ごとの翻訳結果を保存
			}

			// 各言語の翻訳結果を保存
			foreach ($translatedValues as $lang => $translatedValue) {
				$this->_very_model->update_laundry_setting_data($translatedValue, $user_id, $bot_id, $lang);
			}
			// 翻訳元のデータを保存
			$this->_very_model->update_laundry_setting_data($laundryData, $user_id, $bot_id, $lang_cd);
		} else {
			// 翻訳が不要な場合
			$this->_very_model->update_laundry_setting_data($laundryData, $user_id, $bot_id, $lang_cd);
		}

		$this->response->body(json_encode(['result' => 'success']));
	}

	public function action_get_reception_items() {
		$post = $this->request->post();
		$reception_id = $post['reception_id'];
		$reception_items = $this->_very_model->get_reception_list_items($this->_bot_id, $reception_id);
		$this->response->body(json_encode(['result' => 'success', 'data' => $reception_items]));
	}
	public function action_save_reception_display_data()
	{
		$post = $this->request->post();
		$reception_id = $this->request->query('id');
		$bot_id = $this->_bot_id;
		$lang_cd = $this->request->query('lang_cd');
		$user_id = $this->_user_id;
	
		$isTranslateAuto = $post['isTranslateAuto'];
		$selectedLanguages = $post['selectedLanguages']?? [];

		$display_text_setting = $post['setting']['display_text'] ?? [];
		$reception_display_style = $post['style']['display_style'] ?? [];
	
		if ($isTranslateAuto && !empty($selectedLanguages)) {
			$translatedValues = [];
			foreach ($selectedLanguages as $lang) {
				$translatedValue = $display_text_setting;
				$translatedValue['1'] = $this->_model->translate($translatedValue['1'], $lang); // 未呼出
				$translatedValue['3'] = $this->_model->translate($translatedValue['3'], $lang); // 呼び出し中
				$translatedValue['6'] = $this->_model->translate($translatedValue['6'], $lang); // 自動キャンセル
				$translatedValue['9'] = $this->_model->translate($translatedValue['9'], $lang); // 説明文
				$translatedValues[$lang] = $translatedValue;  // 各言語ごとの翻訳結果を保存
			}
			// 各言語の翻訳結果を保存
			foreach ($translatedValues as $lang => $translatedValue) {
				$this->_very_model->save_display_text_setting($translatedValue, $reception_id, $lang);
			}
			// 翻訳元のデータを保存
			$this->_very_model->save_display_text_setting($display_text_setting, $reception_id, $lang_cd);
		} else {
			// 翻訳が不要な場合
			$this->_very_model->save_display_text_setting($display_text_setting, $reception_id, $lang_cd);
		}
		$this->_very_model->save_display_style($reception_display_style, $bot_id, $reception_id, $user_id);

		$this->response->body(json_encode(['result' => 'success']));
	}

	public function action_getmemberdetail() {
		$post = $this->request->query();
		$params = [
			'member_id' => $post['member_id'],
			'program_id' => $post['program_id'],
			'admin_lang_cd' => $this->_lang_cd
		];
		$member_detail = $this->_model->call_admin_api('member', 'memberdetail', 'POST', $params);
		$this->response->body(json_encode(['result' => 'success', 'data' => $member_detail]));
	}

	public function action_updatememberdetail() {
		$post = $this->request->post();
		$query = $this->request->query();
		$params = [
			'member_id' => $query['member_id'],
			'program_id' => $query['program_id'],
			'admin_lang_cd' => $this->_lang_cd,
			// フォームデータ
			'post_cd' => $post['post_cd'],
			'address' => [
				'prefecture' => $post['address']['prefecture'],
				'city' => $post['address']['city'],
				'street' => $post['address']['street'],
				'building' => $post['address']['building']
			],
			'email' => $post['email'],
			'last_name' => $post['last_name'],
			'first_name' => $post['first_name'],
			'last_name_furigana' => $post['last_name_furigana'],
			'first_name_furigana' => $post['first_name_furigana'],
			'birthday' => $post['birthday'],
			'phone' => $post['phone'],
			'rank' => $post['rank'],
			'dm_allow_flg' => $post['dm_allow_flg'],
			'mail_allow_flg' => $post['mail_allow_flg'],
			'memo_data' => $post['memo_data'],
			'upd_user' => $this->_user_id
		];
		$result = $this->_model->call_admin_api('member', 'updatemember', 'POST', $params);
		$this->response->body(json_encode(['result' => 'success', 'data' => $result]));
	}

	public function action_registermember() {
		$post = $this->request->post();
		$query = $this->request->query();
		$params = [
			'program_id' => $query['program_id'],
			'admin_lang_cd' => $this->_lang_cd,
			'email' => $post['email'],
			'password' => $post['password'],
			'old_member_cd' => $post['old_member_cd'],
			'join_channel' => $post['join_channel'],
			'last_name' => $post['last_name'],
			'first_name' => $post['first_name'],
			'last_name_furigana' => $post['last_name_furigana'],
			'first_name_furigana' => $post['first_name_furigana'],
			'birthday' => $post['birthday'],
			'phone' => $post['phone'],
			'note' => $post['note'],
			'post_cd' => $post['post_cd'],
			'address' => [
				'prefecture' => $post['address']['prefecture'],
				'city' => $post['address']['city'],
				'street' => $post['address']['street'],
				'building' => $post['address']['building']
			],
			'rank' => $post['rank'],
			'dm_allow_flg' => $post['dm_allow_flg'],
			'mail_allow_flg' => $post['mail_allow_flg'],
			'memo_data' => $post['memo_data'],
			'upd_user' => $this->_user_id
		];
		$result = $this->_model->call_admin_api('member', 'register', 'POST', $params);
		$this->response->body(json_encode(['result' => 'success', 'data' => $result]));
	}

	public function action_checkmemberemail() {
		$post = $this->request->post();
		$params = [
			'email' => $post['email'],
			'program_id' => $post['program_id']
		];
		$result = $this->_model->call_admin_api('member', 'checkmemberemail', 'POST', $params);
		$this->response->body(json_encode(['result' => 'success', 'data' => $result]));
	}

	public function action_deletemember() {
		$query = $this->request->query();
		$params = [
			'member_id' => $query['member_id'],
			'program_id' => $query['program_id'],
			'upd_user' => $this->_user_id
		];
		$result = $this->_model->call_admin_api('member', 'delete', 'POST', $params);
		$this->response->body(json_encode(['result' => 'success', 'data' => $result]));
	}

	// 会員退会
	public function action_withdrawal() {
		$query = $this->request->query();
		$params = [
			'member_id' => $query['member_id'],
			'program_id' => $query['program_id'],
			'upd_user' => $this->_user_id
		];
		$result = $this->_model->call_admin_api('member', 'withdrawal', 'POST', $params);
		$this->response->body(json_encode(['result' => 'success', 'data' => $result]));
	}
	public function action_getpointhistory() {
		$query = $this->request->query();
		$params = [
			'member_id' => $query['member_id'],
			'program_id' => $query['program_id']
		];
		$result = $this->_model->call_admin_api('member', 'pointhistory', 'POST', $params);
		$this->response->body(json_encode(['result' => 'success', 'data' => $result]));
	}

	public function action_updatepoint() {
		$query = $this->request->query();
		$post = $this->request->post();
		$params = [
			'member_id' => $query['member_id'],
			'program_id' => $query['program_id'],
			'admin_lang_cd' => $this->_lang_cd,
			'point' => $post['point'],
			'point_data' => $post['transaction_type'] === '07' ? [
                'note' => $post['note'] ?: '',
                'source_transaction_ref' => $post['history_no']
            ] : (is_string($post['point_data']) 
                ? json_decode($post['point_data'], true, 512, JSON_UNESCAPED_UNICODE) 
                : $post['point_data']),
			'transaction_type' => $post['transaction_type'],
			'upd_user' => $this->_user_id
		];
		$result = $this->_model->call_admin_api('member', 'updatepoint', 'POST', $params);
		$this->response->body(json_encode(['result' => 'success', 'data' => $result], JSON_UNESCAPED_UNICODE));
	}

	public function action_mergemember() {
		$post = $this->request->post();
		$params = [
			'program_id' => $post['program_id'],
			'latest_member_id' => $post['latest_member_id'],
			'old_member_id' => $post['old_member_id'],
			'upd_user' => $this->_user_id
		];
		$result = $this->_model->call_admin_api('member', 'merge', 'POST', $params);
		$this->response->body(json_encode([
			'result' => 'success', 
			'data' => $result
		], JSON_UNESCAPED_UNICODE));
	}

	public function action_unmergemember() {
		$post = $this->request->post();
		$params = [
			'program_id' => $post['program_id'],
			'member_id' => $post['member_id'],
			'upd_user' => $this->_user_id
		];
		$result = $this->_model->call_admin_api('member', 'unmerge', 'POST', $params);
		$this->response->body(json_encode([
			'result' => 'success', 
			'data' => $result
		], JSON_UNESCAPED_UNICODE));
	}
	
	public function action_faqcreate() {
		$post = $this->request->post();
		$flg_ai_auto_apply = $this->_model->get_bot_setting($this->_bot_id, 'flg_ai_auto_apply') ?? 0;
		if ($flg_ai_auto_apply == 0) {
			$this->response->body(json_encode([
				'result' => 'false',
				'message' => 'AI自動適用がOFFのため、FAQを追加できません。'
			]));
			return;
		}
		$faq_title = $post['faq_title'];
		$faq_answer = $post['faq_answer'];
		$intent_cd = $this->_model->get_max_inquiry_intent_no($this->_bot_id);
		$faq_id = str_replace('inquiry', 'gpt', str_replace('.', '_', $intent_cd));
		$bot_intent_mst_orm = ORM::factory('botintentmst');
		$bot_intent_mst_orm->faq_id = $faq_id;
		$bot_intent_mst_orm->intent_class_cd = $this->_bot->bot_class_cd;
		$bot_intent_mst_orm->intent_type_cd = "9001" . $this->_bot->bot_class_cd;
		$bot_intent_mst_orm->intent_cd = $intent_cd;
		$bot_intent_mst_orm->sub_intent_cd = "";
		$bot_intent_mst_orm->lang_cd = "ja";
		$bot_intent_mst_orm->level = "3";
		$bot_intent_mst_orm->item_ids = $this->_bot_id;
		$bot_intent_mst_orm->question = trim($faq_title);
		$bot_intent_mst_orm->save();

		$intent_id = $this->_model->get_max_bot_intent_id($this->_bot_id, $intent_cd, "", "");
		$bot_intent_orm = ORM::factory('botintent');
		$bot_intent_orm->intent_id = $intent_id;
		$bot_intent_orm->bot_id = $this->_bot_id;
		$bot_intent_orm->intent_cd = $intent_cd;
		$bot_intent_orm->default_sub_intent = 1;
		$bot_intent_orm->show_in_relation_question = 1;
		$bot_intent_orm->lang_cd = "ja";
		$bot_intent_orm->sort_no = 9999;
		$bot_intent_orm->times = 0;
		$bot_intent_orm->grade = 0;
		$bot_intent_orm->satisfication = 0;
		$bot_intent_orm->show_survey = 1;
		$bot_intent_orm->inherit = 1;
		$bot_intent_orm->answer1_type_cd = "txt";
		$bot_intent_orm->answer1 = trim($faq_answer);
		$bot_intent_orm->save();

		$this->_model->upsert_opensearch_faq_index($intent_cd, "", "", 'ja', $this->_bot_id);
	
		// aimai
		$tool_url = $this->_model->get_env('tool_url');
		$tool_url .= "table=m,t&lang=ja&facilitylabel=$this->_bot_id&intentcd=$intent_cd";
		$this->_model->curl_get($tool_url);

		$this->response->body(json_encode([
			'result' => 'success'
		]));
	}

	public function action_updatemaximum() {
		if ($this->request->post()) {
			$post = $this->request->post();
			$maximum = ORM::factory('botmaximum')->where('id', '=', $post['id'])->find();
			$maximum_data = json_decode($maximum->maximum_data, true);
			$seq_daytime = $this->_model->get_maximum_remain_seq($post['id'], $post['seq']);
			list($remain_def, $orders) = $this->_model->get_maximum_def_remain($post['id'], $maximum_data, $seq_daytime[$post['seq']]['day']);
			if (!isset($post['force'])) {
				if (!$this->_model->check_remain_def($maximum->span, intval($post['maximum']), $remain_def, $orders, $seq_daytime[$post['seq']]['time'])) {
					$this->response->body(json_encode(['result'=>'fail', 'message'=>'maximum_check_error']));
					return;
				}
			}
			// 在庫数更新
			DB::update('t_bot_maximum_remains')->set(['maximum'=>$post['maximum'], 'upd_time'=>date('Y-m-d H:i:s'), 'upd_user'=>$this->_user->user_id, "stop"=>$post['stop']])->where('bot_id', '=', $this->_bot_id)->where('id', '=', $post['id'])->where('seq', '=', $post['seq'])->execute();
			// 在庫更新履歴記録
			DB::insert('t_bot_maximum_remains_history', array('bot_id', 'id', 'seq', 'maximum', 'stop', 'remains_data', 'upd_user', 'upd_time'))
			->values(array($this->_bot_id, $post['id'], $post['seq'], $post['maximum'], $post['stop'], $post['comment'], $this->_user->user_id, date('Y-m-d H:i:s')))->execute();
			$this->response->body(json_encode(['result'=>'success']));
		}
	}

	public function action_create_very_from_template()
	{
		$post = $this->request->post();
		$target_bot_id = $this->_bot_id;
		$user_id = $this->_user_id;
		try {
			$result = $this->_very_model->create_very_from_template(
				$post['source_bot_id'],
				$post['source_scene_cd'],
				$target_bot_id,
				$post['target_scene_cd'],
				$user_id,
				$this->_bot->support_lang
			);
			
			// データ挿入が成功したらt_very_tokenに難読化用のトークンを追加
			$this->_very_model->create_or_get_very_obfuscation_id($target_bot_id, $post['target_scene_cd'], null);

			$this->response->body(json_encode([
				'result' => 'success',
				'data' => $result
			], JSON_UNESCAPED_UNICODE));
		} catch (Exception $e) {
			error_log($e);
			$this->response->body(json_encode([
				'result' => 'error',
				'message' => $e->getMessage()
			], JSON_UNESCAPED_UNICODE));
		}
	}

	public function action_get_very_scenes_by_id()
	{
		$post = $this->request->post();
		$bot_id = $post['bot_id'];
		try {
			$result = $this->_very_model->get_very_list($bot_id);
			$this->response->body(json_encode([
				'result' => 'success',
				'data' => $result
			], JSON_UNESCAPED_UNICODE));
		} catch (Exception $e) {
			error_log($e);
			$this->response->body(json_encode([
				'result' => 'error',
				'message' => $e->getMessage()
			], JSON_UNESCAPED_UNICODE));
		}
	}

	public function action_ota_crawling_status() {
		$post = $this->request->post();
		$msg_id = $post['msg_id'];
		$ota = $post['ota'];
		$keyword = $post['keyword'];
		$url = $post['url'];
		$sql = "SELECT opc.crawling_json, opcl.status FROM t_ota_price_crawling as opc LEFT JOIN t_ota_price_crawling_log as opcl ON opc.id = opcl.link_id WHERE opc.msg_id = :msg_id AND opcl.ota = :ota AND opcl.keyword = :keyword AND opcl.url = :url ORDER BY opcl.update_time DESC LIMIT 1";
		$result = DB::query(Database::SELECT, $sql)
					  ->param(':msg_id', $msg_id)
					  ->param(':ota', $ota)
					  ->param(':keyword', $keyword)
					  ->param(':url', $url)
					  ->execute()
					  ->as_array();
		if (count($result) > 0) {
			if ($result[0]['status'] == '1') {
				$this->response->body(json_encode(['result' => 'success', 'data' => ['status' => 'crawling']]));
			} else {
				$price_data = json_decode($result[0]['crawling_json'], true);
				if (isset($price_data[$ota]) && isset($price_data[$ota]['price']) && $price_data[$ota]['price'] != '' && $price_data[$ota]['price'] != null && (int)$price_data[$ota]['price'] > 0) {
					$this->response->body(json_encode(['result' => 'success', 'data' => ['status' => 'crawled', 'result' => 'found', 'price' => $price_data[$ota]['price']]]));
					return;
				}
				$this->response->body(json_encode(['result' => 'success', 'data' => ['status' => 'crawled', 'result' => 'not_found', 'price' => $price_data[$ota]['price']]]));
				return;
			}
		} else {
			$this->response->body(json_encode(['result' => 'error', 'message' => 'No data found']));
		}
	}
	
	public function action_fetch_exist_passkey() {
		$email = $this->_user->email;
		
		$sql = "SELECT credential_id, name, device_type, created_at, last_used, transports, backed_up FROM t_user_credentials WHERE email = :email ORDER BY created_at DESC";
		$result = DB::query(Database::SELECT, $sql)
					  ->param(':email', $email)
					  ->execute()
					  ->as_array();
		
		if (count($result) > 0) {
			// Format the data for better frontend consumption
			$passkeys = [];
			foreach ($result as $row) {
				$passkeys[] = [
					'credential_id' => $row['credential_id'],
					'name' => $row['name'] ?? 'Unnamed Passkey',
					'device_type' => $row['device_type'],
					'created_at' => $row['created_at'],
					'last_used' => $row['last_used'],
					'transports' => json_decode($row['transports'], true)
				];
			}
			
			$this->response->body(json_encode([
				'result' => 'success', 
				'data' => [
					'passkeys' => $passkeys,
					'count' => count($passkeys)
				]
			]));
		} else {
			$this->response->body(json_encode([
				'result' => 'success', 
				'data' => [
					'passkeys' => [],
					'count' => 0
				]
			]));
		}
	}

	public function action_del_exist_passkey() {
		$post = $this->request->post();
		$credential_id = $post['credential_id'];
		$email = $this->_user->email;

		// Validate input
		if (empty($credential_id)) {
			$this->response->body(json_encode([
				'result' => 'error', 
				'message' => 'Credential ID is required'
			]));
			return;
		}

		try {
			// First check if the credential exists and belongs to the current user
			$check_sql = "SELECT credential_id FROM t_user_credentials WHERE credential_id = :credential_id AND email = :email";
			$check_result = DB::query(Database::SELECT, $check_sql)
							  ->param(':credential_id', $credential_id)
							  ->param(':email', $email)
							  ->execute()
							  ->as_array();

			if (count($check_result) == 0) {
				$this->response->body(json_encode([
					'result' => 'error', 
					'message' => 'Passkey not found or access denied'
				]));
				return;
			}

			// Delete the credential
			$delete_sql = "DELETE FROM t_user_credentials WHERE credential_id = :credential_id AND email = :email";
			$affected_rows = DB::query(Database::DELETE, $delete_sql)
							   ->param(':credential_id', $credential_id)
							   ->param(':email', $email)
							   ->execute();

			if ($affected_rows > 0) {
				$this->response->body(json_encode([
					'result' => 'success', 
					'message' => 'Passkey deleted successfully'
				]));
			} else {
				$this->response->body(json_encode([
					'result' => 'error', 
					'message' => 'Failed to delete passkey'
				]));
			}

		} catch (Exception $e) {
			$this->response->body(json_encode([
				'result' => 'error', 
				'message' => 'Database error: ' . $e->getMessage()
			]));
		}
	}

	public function action_rename_exist_passkey() {
		$post = $this->request->post();
		$credential_id = $post['credential_id'];
		$new_name = $post['new_name'];
		$email = $this->_user->email;

		// Validate input
		if (empty($credential_id)) {
			$this->response->body(json_encode([
				'result' => 'error', 
				'message' => 'Credential ID is required'
			]));
			return;
		}

		if (empty($new_name) || strlen(trim($new_name)) == 0) {
			$this->response->body(json_encode([
				'result' => 'error', 
				'message' => 'New name is required'
			]));
			return;
		}

		// Validate name length (max 100 characters based on database schema)
		$new_name = trim($new_name);
		if (strlen($new_name) > 100) {
			$this->response->body(json_encode([
				'result' => 'error', 
				'message' => 'Name is too long (maximum 100 characters)'
			]));
			return;
		}

		try {
			// First check if the credential exists and belongs to the current user
			$check_sql = "SELECT credential_id FROM t_user_credentials WHERE credential_id = :credential_id AND email = :email";
			$check_result = DB::query(Database::SELECT, $check_sql)
							  ->param(':credential_id', $credential_id)
							  ->param(':email', $email)
							  ->execute()
							  ->as_array();

			if (count($check_result) == 0) {
				$this->response->body(json_encode([
					'result' => 'error', 
					'message' => 'Passkey not found or access denied'
				]));
				return;
			}

			// Update the passkey name
			$update_sql = "UPDATE t_user_credentials SET name = :new_name WHERE credential_id = :credential_id AND email = :email";
			$affected_rows = DB::query(Database::UPDATE, $update_sql)
							   ->param(':new_name', $new_name)
							   ->param(':credential_id', $credential_id)
							   ->param(':email', $email)
							   ->execute();

			if ($affected_rows > 0) {
				$this->response->body(json_encode([
					'result' => 'success', 
					'message' => 'Passkey renamed successfully',
					'data' => [
						'credential_id' => $credential_id,
						'new_name' => $new_name
					]
				]));
			} else {
				$this->response->body(json_encode([
					'result' => 'error', 
					'message' => 'Failed to rename passkey'
				]));
			}

		} catch (Exception $e) {
			$this->response->body(json_encode([
				'result' => 'error', 
				'message' => 'Database error: ' . $e->getMessage()
			]));
		}
	}

	public function action_register_passkey_start() {
		$post = $this->request->post();
		$email = $this->_user->email;
		
		if (empty($email)) {
			$this->response->body(json_encode([
				'result' => 'error',
				'message' => 'User email not found in session'
			]));
			return;
		}
	
		// Call the passkey registration start API
		$api_url = 'http://************:9610/passkey/register/start';
		$api_data = json_encode(['email' => $email]);
		
		// Build Origin header (scheme + host[:port]) to forward to Node service
		$host   = $_SERVER['HTTP_HOST'] ?? '';
		$scheme = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ? 'https' : 'http';
		$origin = $host ? $scheme . '://' . $host : '';
		$headers = ['Content-Type: application/json'];
		if ($origin) {
			$headers[] = 'Origin: ' . $origin;
			$headers[] = 'X-Frontend-Origin: ' . $origin;
		}
		
		$ch = curl_init();
		curl_setopt($ch, CURLOPT_URL, $api_url);
		curl_setopt($ch, CURLOPT_POST, 1);
		curl_setopt($ch, CURLOPT_POSTFIELDS, $api_data);
		curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($ch, CURLOPT_TIMEOUT, 30);
		
		$response = curl_exec($ch);
		$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
		curl_close($ch);
		
		if ($http_code === 200) {
			// Forward the successful response from the API
			$this->response->headers('Content-Type', 'application/json');
			$this->response->body($response);
		} else {
			// Handle error response
			$error_data = json_decode($response, true);
			$this->response->body(json_encode([
				'result' => 'error',
				'message' => $error_data['error'] ?? 'Failed to start passkey registration'
			]));
		}
	}
	
	public function action_register_passkey_finish() {
		$post = $this->request->post();
		$email = $this->_user->email;
		
		if (empty($email)) {
			$this->response->body(json_encode([
				'result' => 'error',
				'message' => 'User email not found in session'
			]));
			return;
		}
	
		// Get credential data from POST
		$input = file_get_contents('php://input');
		$data = json_decode($input, true);
		
		if (!$data || !isset($data['credential'])) {
			$this->response->body(json_encode([
				'result' => 'error',
				'message' => 'Invalid credential data'
			]));
			return;
		}
	
		// Add email to the data
		$data['email'] = $email;
	
		// Call the passkey registration finish API
		$api_url = 'http://************:9610/passkey/register/finish';
		$api_data = json_encode($data);
		
		// Forward Origin header to Node service
		$host   = $_SERVER['HTTP_HOST'] ?? '';
		$scheme = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ? 'https' : 'http';
		$origin = $host ? $scheme . '://' . $host : '';
		$headers = ['Content-Type: application/json'];
		if ($origin) {
			$headers[] = 'Origin: ' . $origin;
			$headers[] = 'X-Frontend-Origin: ' . $origin;
		}
		
		$ch = curl_init();
		curl_setopt($ch, CURLOPT_URL, $api_url);
		curl_setopt($ch, CURLOPT_POST, 1);
		curl_setopt($ch, CURLOPT_POSTFIELDS, $api_data);
		curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($ch, CURLOPT_TIMEOUT, 30);
		
		$response = curl_exec($ch);
		$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
		curl_close($ch);
		
		if ($http_code === 200) {
			// Forward the successful response from the API
			$this->response->headers('Content-Type', 'application/json');
			$this->response->body($response);
		} else {
			// Handle error response
			$error_data = json_decode($response, true);
			$this->response->body(json_encode([
				'result' => 'error',
				'message' => $error_data['error'] ?? 'Failed to complete passkey registration'
			]));
		}
	}
}
