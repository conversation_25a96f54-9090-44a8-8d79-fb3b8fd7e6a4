<?php defined('SYSPATH') or die('No direct script access.');
class Controller_Apireserveadmin extends Controller
{
    const API_RESULT_OK = '0';
    const API_RESULT_ERR = '1';
    const API_RESULT_WARN = '2';

    private function get_bot_info($post)
    {
        $login_type = $post->login_type;
        if ($login_type != "admin") {
            return array('bot_id' => "", 'logo_img' => "", 'bot_name' => "");
        }

        $bot_id = Session::instance()->get("bot_id");
        if ($bot_id == null) {
            return array('bot_id' => "", 'logo_img' => "", 'bot_name' => "");
        }

        $sql = "SELECT *
		    FROM t_bot a
			WHERE
                1 = 1
				AND a.bot_id = :bot_id
		";
        $query = DB::query(Database::SELECT, $sql);
        $query->parameters(array(
            ':bot_id' => $bot_id,
        ));
        $results = $query->execute()->as_array();

        $scene_path = APPPATH . "../assets/f/" . $results[0]["facility_cd"] . '/';
        $logo_url = '/assets/common/images/logo-icon.png';

        $config = Kohana::$config->load('settings');
        $ext_array = $config['support_image_type'];

        foreach ($ext_array as $ext) {
            if (file_exists($scene_path . 'logo' . '.' . $ext) == true) {
                $logo_url = "/assets/f/" . $results[0]["facility_cd"] . '/' . 'logo' . '.' . $ext;
                break;
            }
        }


        return array(
            'bot_id' => $bot_id,
            'logo_img' => $logo_url,
            'bot_name' => $results[0]["bot_name"]
        );
    }

    private function check_accesstoken($post)
    {
        $user_id = $post->user_id;
        $password = $post->password;
        $link_key = $post->link_key;
        $bot_id = $post->bot_id;
        $login_type = $post->login_type;

        if ($login_type == "local") {
            // 用预约系统自己的用户管理账号来登录
            $sql = "SELECT hotel_code
				FROM t_reserve_user a
				WHERE
					a.user_id = :user_id
					AND a.password = :password
					AND type = 'talkappi_admin'
			";
            $query = DB::query(Database::SELECT, $sql);
            $query->parameters(array(
                ':user_id' => $user_id,
                ':password' => $password,
            ));

            $results = $query->execute()->as_array();
            if (count($results) > 0) {
                return $results[0]["hotel_code"];
            }
            return "";
        } elseif ($login_type == "admin") {
            // 使用session
            $bot_id = Session::instance()->get("bot_id");
            if ($bot_id == null) {
                error_log("not login!");
                return "";
            }

            // 查询t_bot_setting.json_reserve_settings,获得预约用hotel_code,预约用用户名/密码
            $json_reserve_settings = ""; {
                $sql = "SELECT setting_value
                    FROM t_bot_setting a
                    WHERE
                        a.bot_id = :bot_id
                        AND setting_cd = 'json_reserve_settings'
                ";
                $query = DB::query(Database::SELECT, $sql);
                $query->parameters(array(
                    ':bot_id' => $bot_id,
                ));

                $results = $query->execute()->as_array();
                if (count($results) > 0) {
                    $json_reserve_settings = $results[0]["setting_value"];
                } else {
                    error_log("t_bot_setting have no json_reserve_settings for bot_id=$bot_id");
                    return "";
                }
            }

            $json_reserve_settings_obj = json_decode($json_reserve_settings);
            $config_of_link_key = "";
            if (is_array($json_reserve_settings_obj)) {
                $nums = count($json_reserve_settings_obj);
                error_log("json_reserve_settings is array nums = $nums for bot_id=$bot_id");
                for ($i = 0; $i < $nums; $i++) {
                    $cur_obj = $json_reserve_settings_obj[$i];
                    if ($cur_obj->type != "tm" ||  $cur_obj->sub_type != "talkappi_reserve") {
                        continue;
                    } else {
                        $config_of_link_key = $json_reserve_settings_obj[$i];
                        break;
                    }
                }
            } else {
                error_log("json_reserve_settings is single for  bot_id=$bot_id");
                $cur_obj = $json_reserve_settings_obj;
                if ($cur_obj->type != "tm" ||  $cur_obj->sub_type != "talkappi_reserve") {
                } else {
                    $config_of_link_key = $json_reserve_settings_obj;
                }
            }

            if ($config_of_link_key == "") {
                error_log("json_reserve_settings of bot_id=$bot_id have no definiton of type=tm sub_type=talkappi_reserve");
                return "";
            }
            if ($config_of_link_key->type != "tm" ||  $config_of_link_key->sub_type != "talkappi_reserve") {
                $type = $config_of_link_key->type;
                $sub_type = $config_of_link_key->sub_type;
                error_log("json_reserve_settings of bot_id=$bot_id :type is $type  sub_type is $sub_type not tm or sub_type is not talkappi_reserve");
                return "";
            }
            $user_id_reserve = $config_of_link_key->config->user;
            $password_reserve = $config_of_link_key->config->password;
            $hotel_code_reserve = $config_of_link_key->config->hotel_code;
            // 根据预约用用户名/密码来检索type=talkappi_reserve的t_reserve_user数据，
            // 验证t_reserve_user表的hotel_code是否等于t_bot_setting.json_reserve_settings里设置的预约用hotel_code
            // 验证通过，return hotel_code
            {
                $sql = "SELECT hotel_code
                    FROM t_reserve_user a
                    WHERE
                        a.user_id = :user_id
                        AND a.password = :password
                        AND type = 'talkappi_reserve'
                        AND hotel_code = :hotel_code
    			";
                $query = DB::query(Database::SELECT, $sql);
                $query->parameters(array(
                    ':user_id' => $user_id_reserve,
                    ':password' => $password_reserve,
                    ':hotel_code' => $hotel_code_reserve,
                ));

                $results = $query->execute()->as_array();
                if (count($results) > 0) {
                    $ret = $results[0]["hotel_code"];
                    error_log("t_reserve_user have this bot reserve_user hotel_code is $ret");
                    return $results[0]["hotel_code"];
                }
                error_log("t_reserve_user have no this reserve_user");
                return "";
            }
        } elseif ($login_type == "bot") {
            error_log("login_type is bot");
            error_log("bot_id is $bot_id");
            error_log("user_id is $user_id");
            error_log("password is $password");
            error_log("link_key is $link_key");
            // 用bot的t_user信息来登录
            // 首先检索t_user表，获得bot_id
            {
                $sql = "SELECT bot_id
                    FROM t_user a
                    WHERE
                        a.email = :user_id
                        AND a.bot_id = :bot_id
                ";
                $query = DB::query(Database::SELECT, $sql);
                $query->parameters(array(
                    ':user_id' => $user_id,
                    ':password' => $password,
                    ':bot_id' => $bot_id,
                ));

                $results = $query->execute()->as_array();
                if (count($results) > 0) {
                } else {
                    error_log("t_user have no this user $user_id");
                    return "";
                }
            }
            // 查询t_bot_setting.json_reserve_settings,获得预约用hotel_code,预约用用户名/密码
            $json_reserve_settings = "";
            {
                $sql = "SELECT setting_value
                    FROM t_bot_setting a
                    WHERE
                        a.bot_id = :bot_id
                        AND setting_cd = 'json_reserve_settings'
                ";
                $query = DB::query(Database::SELECT, $sql);
                $query->parameters(array(
                    ':bot_id' => $bot_id,
                ));

                $results = $query->execute()->as_array();
                if (count($results) > 0) {
                    $json_reserve_settings = $results[0]["setting_value"];
                } else {
                    error_log("t_bot_setting have no json_reserve_settings for $user_id and bot_id=$bot_id");
                    return "";
                }
            }

            $json_reserve_settings_obj = json_decode($json_reserve_settings);
            $config_of_link_key = "";
            if (is_array($json_reserve_settings_obj)) {
                $nums = count($json_reserve_settings_obj);
                error_log("json_reserve_settings is array nums = $nums for $user_id and bot_id=$bot_id");
                for ($i = 0; $i < $nums; $i++) {
                    $link_key_db = "";
                    $cur_obj = $json_reserve_settings_obj[$i];
                    if (isset($cur_obj->link_key)) {
                        $link_key_db = $cur_obj->link_key;
                    }
                    if ($link_key_db == $link_key) {
                        $config_of_link_key = $json_reserve_settings_obj[$i];
                        break;
                    }
                }
            } else {
                error_log("json_reserve_settings is single for $user_id and bot_id=$bot_id");
                $link_key_db = "";
                if (isset($json_reserve_settings_obj->link_key)) {
                    $link_key_db = $json_reserve_settings_obj->link_key;
                }
                if ($link_key_db == $link_key) {
                    $config_of_link_key = $json_reserve_settings_obj;
                }
            }

            if ($config_of_link_key == "") {
                error_log("json_reserve_settings have no link_key=$link_key");
                return "";
            }
            if ($config_of_link_key->type != "tm" ||  $config_of_link_key->sub_type != "talkappi_reserve") {
                $type = $config_of_link_key->type;
                $sub_type = $config_of_link_key->sub_type;
                error_log("json_reserve_settings type is $type  sub_type is $sub_type not tm or sub_type is not talkappi_reserve link_key=$link_key");
                return "";
            }
            $user_id_reserve = $config_of_link_key->config->user;
            $password_reserve = $config_of_link_key->config->password;
            $hotel_code_reserve = $config_of_link_key->config->hotel_code;
            // 根据预约用用户名/密码来检索type=talkappi_reserve的t_reserve_user数据，
            // 验证t_reserve_user表的hotel_code是否等于t_bot_setting.json_reserve_settings里设置的预约用hotel_code
            // 验证通过，return hotel_code
            {
                $sql = "SELECT hotel_code
                    FROM t_reserve_user a
                    WHERE
                        a.user_id = :user_id
                        AND a.password = :password
                        AND type = 'talkappi_reserve'
                        AND hotel_code = :hotel_code
    			";
                $query = DB::query(Database::SELECT, $sql);
                $query->parameters(array(
                    ':user_id' => $user_id_reserve,
                    ':password' => $password_reserve,
                    ':hotel_code' => $hotel_code_reserve,
                ));

                $results = $query->execute()->as_array();
                if (count($results) > 0) {
                    $ret = $results[0]["hotel_code"];
                    error_log("t_reserve_user have this bot reserve_user hotel_code is $ret");
                    return $results[0]["hotel_code"];
                }
                error_log("t_reserve_user have no this reserve_user");
                return "";
            }
        }
        return "";
    }
    private function jresult($result_cd, $reason_cd = null, $data = null)
    {
        $result_array = array('result' => $result_cd, 'reason' => $reason_cd, 'data' => $data);
        return json_encode($result_array);

        // 2021.07.31 改为压缩
        /* 改为在php.ini里设置
        $result_array = array('result'=>$result_cd, 'reason'=>$reason_cd, 'data'=>$data);
        $result_json_string =  json_encode($result_array);

        $retzip = gzencode($result_json_string);
        $this->response->headers('Content-Encoding', 'gzip');
        return $retzip;
        */
    }


    // 以下是talkappi reserve相关的

    public function action_get_hotel_codes()
    {
        $this->response->headers('Access-Control-Allow-Origin', '*');
        $condition = $this->request->post();
        if ($condition) {
            $post = json_decode($condition["condition"]);
            $db_hotel_code = $this->check_accesstoken($post);
            if ($db_hotel_code == "") {
                return $this->response->body($this->jresult("01"));
            }

            $sql = "SELECT b.*
				FROm t_reserve_hotel b
				WHERE
                    b.hotel_code = :hotel_code
				";
            $query = DB::query(Database::SELECT, $sql);
            $query->parameters(array(
                ':hotel_code' => $db_hotel_code,
            ));
            $results = $query->execute()->as_array();

            $bot_info = $this->get_bot_info($post);
            $results[0]["bot_id"] = $bot_info["bot_id"];
            $results[0]["bot_name"] = $bot_info["bot_name"];
            $results[0]["logo_img"] = $bot_info["logo_img"];

            $ret = array();
            $ret["hotels_data"] = $results;


            $this->response->body($this->jresult("00", null, $ret));
            return;
        } else {
            $this->response->body($this->jresult("01"));
        }
        return;
    }

    public function action_get_rooms()
    {
        $this->response->headers('Access-Control-Allow-Origin', '*');
        $condition = $this->request->post();
        if ($condition) {
            $post = json_decode($condition["condition"]);
            $db_hotel_code = $this->check_accesstoken($post);
            if ($db_hotel_code == "") {
                return $this->response->body($this->jresult("01"));
            }

            $sql = "SELECT *
					FROM t_reserve_room a
					where 
						a.hotel_code = :hotel_code
					ORDER BY 
						a.room_type_code
				";
            $query = DB::query(Database::SELECT, $sql);
            $query->parameters(array(
                ':hotel_code' => $post->hotel_code,
            ));
            $results = $query->execute();

            $ret = array();
            $ret["rooms_data"] = $results->as_array();

            $this->response->body($this->jresult("00", null, $ret));
            return;
        } else {
            $this->response->body($this->jresult("01"));
        }
        return;
    }

    public function action_get_room()
    {
        $this->response->headers('Access-Control-Allow-Origin', '*');
        $condition = $this->request->post();
        if ($condition) {
            $post = json_decode($condition["condition"]);
            $db_hotel_code = $this->check_accesstoken($post);
            if ($db_hotel_code == "") {
                return $this->response->body($this->jresult("01"));
            }

            $sql = "SELECT *
					FROM t_reserve_room a
					where 
						a.hotel_code = :hotel_code
						AND room_type_code = :room_type_code
				";
            $query = DB::query(Database::SELECT, $sql);
            $query->parameters(array(
                ':hotel_code' => $post->hotel_code,
                ':room_type_code' => $post->room_type_code,
            ));
            $results = $query->execute();

            $ret = array();
            $ret["room_data"] = $results->as_array();

            $this->response->body($this->jresult("00", null, $ret));
            return;
        } else {
            $this->response->body($this->jresult("01"));
        }
        return;
    }

    public function action_get_room_available()
    {
        $this->response->headers('Access-Control-Allow-Origin', '*');
        $condition = $this->request->post();
        if ($condition) {
            $post = json_decode($condition["condition"]);
            $db_hotel_code = $this->check_accesstoken($post);
            if ($db_hotel_code == "") {
                return $this->response->body($this->jresult("01"));
            }

            $sql = "SELECT *
					FROM t_reserve_room_available a
					where 
						a.hotel_code = :hotel_code
						and a.room_type_code = :room_type_code
					ORDER BY 
						a.ymd
				";
            $query = DB::query(Database::SELECT, $sql);
            $query->parameters(array(
                ':hotel_code' => $post->hotel_code,
                ':room_type_code' => $post->room_type_code,
            ));
            $results = $query->execute();

            $ret = array();
            $ret["room_available"] = $results->as_array();

            $this->response->body($this->jresult("00", null, $ret));
            return;
        } else {
            $this->response->body($this->jresult("01"));
        }
        return;
    }

    public function action_save_room()
    {
        $this->response->headers('Access-Control-Allow-Origin', '*');
        $condition = $this->request->post();
        if ($condition) {
            $post = json_decode($condition["condition"]);
            //error_log($post);
            $db_hotel_code = $this->check_accesstoken($post);
            if ($db_hotel_code == "") {
                return $this->response->body($this->jresult("06"));
            }

            try {
                Database::instance()->begin();
                $data = $post->data;

                if ($data->create_datetime != "") {
                    // 数据更新，要检查update_datetime是否已经被改过了
                    $sql = "SELECT 1 FROM t_reserve_room
                        WHERE
                            hotel_code = :hotel_code
                            AND room_type_code = :room_type_code
                            AND update_datetime = :update_datetime
                    ";
                    $query = DB::query(Database::SELECT, $sql);
                    $query->parameters(array(
                        ':hotel_code' => $data->hotel_code,
                        ':room_type_code' => $data->room_type_code,
                        ':update_datetime' => $data->update_datetime,
                    ));
                    $ret = $query->execute()->as_array();
                    if (count($ret) == 0) {
                        Database::instance()->rollback();
                        $this->response->body($this->jresult("02", null, $ret));
                        return;
                    }
                }

                $sql = "SELECT 1 FROM t_reserve_room
						WHERE
							hotel_code = :hotel_code
							AND room_type_code = :room_type_code
						";
                $query = DB::query(Database::SELECT, $sql);
                $query->parameters(array(
                    ':hotel_code' => $data->hotel_code,
                    ':room_type_code' => $data->room_type_code,
                ));
                $ret = $query->execute()->as_array();

                if (count($ret) > 0) {
                    error_log("update t_reserve_room for room_type_code= $data->room_type_code ");
                    $sql = "UPDATE t_reserve_room
							SET
								room_type_name = :room_type_name,
								status = :status,
								description = :description,
								capacity_from = :capacity_from,
								capacity_to = :capacity_to,
								category = :category,
								feature_nosmoke = :feature_nosmoke,
								feature_smoking = :feature_smoking,
								feature_internet = :feature_internet,
								feature_bath = :feature_bath,
								feature_toilet = :feature_toilet,
								image = :image,
                                update_user = :user_id
							WHERE
								hotel_code = :hotel_code
								AND room_type_code = :room_type_code
							";
                    $query = DB::query(Database::UPDATE, $sql);
                    $query->parameters(array(
                        ':hotel_code' => $data->hotel_code,
                        ':room_type_code' => $data->room_type_code,
                        ':room_type_name' => $data->room_type_name,
                        ':status' => $data->status,
                        ':description' => $data->description,
                        ':capacity_from' => $data->capacity_from,
                        ':capacity_to' => $data->capacity_to,
                        ':category' => $data->category,
                        ':feature_nosmoke' => $data->feature_nosmoke,
                        ':feature_smoking' => $data->feature_smoking,
                        ':feature_internet' => $data->feature_internet,
                        ':feature_bath' => $data->feature_bath,
                        ':feature_toilet' => $data->feature_toilet,
                        ':image' => $data->image,
                        ':user_id' => $post->user_id,
                    ));

                    $ret = $query->execute();
                    $ret = "00";
                } else {
                    error_log("insert t_reserve_room for room_type_code= $data->room_type_code ");
                    $sql = "INSERT INTO t_reserve_room
							(hotel_code, room_type_code, room_type_name, status, `description`, 
                            capacity_from, capacity_to, category, 
                            feature_nosmoke, feature_smoking, feature_internet, feature_bath, feature_toilet, 
                            image, create_user)
							VALUES
							(:hotel_code, :room_type_code, :room_type_name, :status, :description, 
                            :capacity_from, :capacity_to, :category, 
                            :feature_nosmoke, :feature_smoking, :feature_internet, :feature_bath, :feature_toilet, 
                            :image, :user_id)
							";
                    $query = DB::query(Database::UPDATE, $sql);
                    $query->parameters(array(
                        ':hotel_code' => $data->hotel_code,
                        ':room_type_code' => $data->room_type_code,
                        ':room_type_name' => $data->room_type_name,
                        ':status' => $data->status,
                        ':description' => $data->description,
                        ':capacity_from' => $data->capacity_from,
                        ':capacity_to' => $data->capacity_to,
                        ':category' => $data->category,
                        ':feature_nosmoke' => $data->feature_nosmoke,
                        ':feature_smoking' => $data->feature_smoking,
                        ':feature_internet' => $data->feature_internet,
                        ':feature_bath' => $data->feature_bath,
                        ':feature_toilet' => $data->feature_toilet,
                        ':image' => $data->image,
                        ':user_id' => $post->user_id,
                    ));

                    $ret = $query->execute();
                    $ret = "00";
                }

                $sql = "SELECT create_datetime,update_datetime FROM t_reserve_room
						WHERE
							hotel_code = :hotel_code
							AND room_type_code = :room_type_code
						";
                $query = DB::query(Database::SELECT, $sql);
                $query->parameters(array(
                    ':hotel_code' => $data->hotel_code,
                    ':room_type_code' => $data->room_type_code,
                ));
                $ret = $query->execute()->as_array();

                $this->response->body($this->jresult("00", null, $ret[0]));
                //error_log("will commit");
                //DB::query(NULL,'COMMIT');
                Database::instance()->commit();
            } catch (Exception $e) {
                //error_log("will rollback");
                //DB::query(NULL,'ROLLBACK');
                Database::instance()->rollback();
                error_log($e->getMessage());
                $ret = $e->getMessage();
                $this->response->body($this->jresult("01", null, $ret));
            }
        } else {
            $this->response->body($this->jresult("01"));
        }
        return;
    }


    public function action_save_room_available()
    {
        $this->response->headers('Access-Control-Allow-Origin', '*');
        $condition = $this->request->post();
        if ($condition) {
            $post = json_decode($condition["condition"]);
            //error_log($post);
            $db_hotel_code = $this->check_accesstoken($post);
            if ($db_hotel_code == "") {
                return $this->response->body($this->jresult("06"));
            }

            try {
                Database::instance()->begin();
                $save_data = $post->data;

                $length = count($save_data);
                for ($x = 0; $x < $length; $x++) {
                    $data = $save_data[$x];

                    $sql = "SELECT 1 FROM t_reserve_room_available
							WHERE
								hotel_code = :hotel_code
								AND room_type_code = :room_type_code
								AND ymd = :ymd
							";
                    $query = DB::query(Database::SELECT, $sql);
                    $query->parameters(array(
                        ':hotel_code' => $data->hotel_code,
                        ':room_type_code' => $data->room_type_code,
                        ':ymd' => $data->ymd,
                    ));
                    $ret = $query->execute()->as_array();

                    if (count($ret) > 0) {
                        $sql = "UPDATE t_reserve_room_available
								SET
									status = :status,
									available_num = :available_num,
                                    update_user = :user_id
								WHERE
									hotel_code = :hotel_code
									AND room_type_code = :room_type_code
									AND ymd = :ymd
								";
                        $query = DB::query(Database::UPDATE, $sql);
                        $query->parameters(array(
                            ':hotel_code' => $data->hotel_code,
                            ':room_type_code' => $data->room_type_code,
                            ':ymd' => $data->ymd,
                            ':status' => $data->status,
                            ':available_num' => $data->available_num,
                            ':user_id' => $post->user_id,
                        ));

                        $ret = $query->execute();
                        $ret = "00";
                    } else {
                        $sql = "INSERT INTO t_reserve_room_available
								(hotel_code, room_type_code, ymd, status, available_num, reserved_num, create_user)
								VALUES
								(:hotel_code, :room_type_code, :ymd, :status, :available_num, 0, :user_id)
								";
                        $query = DB::query(Database::UPDATE, $sql);
                        $query->parameters(array(
                            ':hotel_code' => $data->hotel_code,
                            ':room_type_code' => $data->room_type_code,
                            ':ymd' => $data->ymd,
                            ':status' => $data->status,
                            ':available_num' => $data->available_num,
                            ':user_id' => $post->user_id,
                        ));

                        $ret = $query->execute();
                        $ret = "00";
                    }
                }

                $this->response->body($this->jresult("00", null, $ret));
                //error_log("will commit");
                //DB::query(NULL,'COMMIT');
                Database::instance()->commit();
            } catch (Exception $e) {
                //error_log("will rollback");
                //DB::query(NULL,'ROLLBACK');
                Database::instance()->rollback();
                error_log($e->getMessage());
                $ret = $e->getMessage();
                $this->response->body($this->jresult("01", null, $ret));
            }
        } else {
            $this->response->body($this->jresult("01"));
        }
        return;
    }
    public function action_get_plans()
    {
        $this->response->headers('Access-Control-Allow-Origin', '*');
        $condition = $this->request->post();
        if ($condition) {
            $post = json_decode($condition["condition"]);
            $db_hotel_code = $this->check_accesstoken($post);
            if ($db_hotel_code == "") {
                return $this->response->body($this->jresult("01"));
            }

            $sql = "SELECT *
					FROM t_reserve_plan a
					where 
						a.hotel_code = :hotel_code
					ORDER BY 
						a.rate_plan_code
				";
            $query = DB::query(Database::SELECT, $sql);
            $query->parameters(array(
                ':hotel_code' => $post->hotel_code,
            ));
            $results = $query->execute();

            $ret = array();
            $ret["plans_data"] = $results->as_array();

            $this->response->body($this->jresult("00", null, $ret));
            return;
        } else {
            $this->response->body($this->jresult("01"));
        }
        return;
    }

    public function action_get_plan()
    {
        $this->response->headers('Access-Control-Allow-Origin', '*');
        $condition = $this->request->post();
        if ($condition) {
            $post = json_decode($condition["condition"]);
            $db_hotel_code = $this->check_accesstoken($post);
            if ($db_hotel_code == "") {
                return $this->response->body($this->jresult("01"));
            }

            $sql = "SELECT *
					FROM t_reserve_plan a
					where 
						a.hotel_code = :hotel_code
						AND rate_plan_code = :rate_plan_code
				";
            $query = DB::query(Database::SELECT, $sql);
            $query->parameters(array(
                ':hotel_code' => $post->hotel_code,
                ':rate_plan_code' => $post->rate_plan_code,
            ));
            $results = $query->execute();

            $ret = array();
            $ret["plan_data"] = $results->as_array();

            $this->response->body($this->jresult("00", null, $ret));
            return;
        } else {
            $this->response->body($this->jresult("01"));
        }
        return;
    }
    public function action_get_room_plan()
    {
        $this->response->headers('Access-Control-Allow-Origin', '*');
        $condition = $this->request->post();
        if ($condition) {
            $post = json_decode($condition["condition"]);
            $db_hotel_code = $this->check_accesstoken($post);
            if ($db_hotel_code == "") {
                return $this->response->body($this->jresult("01"));
            }

            $sql = "SELECT 
						a.*,b.room_type_name
					FROM t_reserve_room_plan a
                    INNER JOIN t_reserve_room b
                        ON a.hotel_code = b.hotel_code
                        AND a.room_type_code = b.room_type_code
					where 
						a.hotel_code = :hotel_code
						and a.rate_plan_code = :rate_plan_code
					ORDER BY 
						a.room_type_code
				";
            $query = DB::query(Database::SELECT, $sql);
            $query->parameters(array(
                ':hotel_code' => $post->hotel_code,
                ':rate_plan_code' => $post->rate_plan_code,
            ));
            $results = $query->execute();

            $ret = array();
            $ret["room_plan"] = $results->as_array();

            $this->response->body($this->jresult("00", null, $ret));
            return;
        } else {
            $this->response->body($this->jresult("01"));
        }
        return;
    }

    public function action_get_plan_rate()
    {
        $this->response->headers('Access-Control-Allow-Origin', '*');
        $condition = $this->request->post();
        if ($condition) {
            $post = json_decode($condition["condition"]);
            $db_hotel_code = $this->check_accesstoken($post);
            if ($db_hotel_code == "") {
                return $this->response->body($this->jresult("01"));
            }

            $sql = "SELECT *
					FROM t_reserve_plan_rate a
					where 
						a.hotel_code = :hotel_code
						and a.rate_plan_code = :rate_plan_code
						and a.room_type_code = :room_type_code
					ORDER BY 
						a.ymd
				";
            $query = DB::query(Database::SELECT, $sql);
            $query->parameters(array(
                ':hotel_code' => $post->hotel_code,
                ':rate_plan_code' => $post->rate_plan_code,
                ':room_type_code' => $post->room_type_code,
            ));
            $results = $query->execute();

            $ret = array();
            $ret["plan_rate"] = $results->as_array();

            $this->response->body($this->jresult("00", null, $ret));
            return;
        } else {
            $this->response->body($this->jresult("01"));
        }
        return;
    }

    public function action_save_plan()
    {
        $this->response->headers('Access-Control-Allow-Origin', '*');
        $condition = $this->request->post();
        if ($condition) {
            $post = json_decode($condition["condition"]);
            //error_log($post);
            $db_hotel_code = $this->check_accesstoken($post);
            if ($db_hotel_code == "") {
                return $this->response->body($this->jresult("06"));
            }

            try {
                Database::instance()->begin();
                $data = $post->data;
                $room_plan_data = $post->room_plan_data;

                if ($data->create_datetime != "") {
                    // 数据更新，要检查update_datetime是否已经被改过了
                    $sql = "SELECT 1 FROM t_reserve_plan
                        WHERE
                            hotel_code = :hotel_code
							AND rate_plan_code = :rate_plan_code
                            AND update_datetime = :update_datetime
                    ";
                    $query = DB::query(Database::SELECT, $sql);
                    $query->parameters(array(
                        ':hotel_code' => $data->hotel_code,
                        ':rate_plan_code' => $data->rate_plan_code,
                        ':update_datetime' => $data->update_datetime,
                    ));
                    $ret = $query->execute()->as_array();
                    if (count($ret) == 0) {
                        Database::instance()->rollback();
                        $this->response->body($this->jresult("02", null, $ret));
                        return;
                    }
                }


                $sql = "SELECT 1 FROM t_reserve_plan
						WHERE
							hotel_code = :hotel_code
							AND rate_plan_code = :rate_plan_code
						";
                $query = DB::query(Database::SELECT, $sql);
                $query->parameters(array(
                    ':hotel_code' => $data->hotel_code,
                    ':rate_plan_code' => $data->rate_plan_code,
                ));
                $ret = $query->execute()->as_array();

                // 2024.05.28 begin
                $fee_lang_cd_mapping = null;
                if (isset($data->fee_lang_cd_mapping)) {
                    $fee_lang_cd_mapping = $data->fee_lang_cd_mapping;
                }
                // 2024.05.28 end
                if (count($ret) > 0) {
                    $sql = "UPDATE t_reserve_plan
							SET
								rate_plan_name = :rate_plan_name,
								offer_from = :offer_from,
								offer_to = :offer_to,
								flg_active = :flg_active,
								fee_system = :fee_system,
								status = :status,
								use_type = :use_type,
								description = :description,
								publish_from = :publish_from,
								publish_to = :publish_to,
								reserve_receipt_before_days = :reserve_receipt_before_days,
								reserve_receipt_before_time = :reserve_receipt_before_time,
								checkin_desc = :checkin_desc,
								checkout_desc = :checkout_desc,
								consecutive_nights_shortest = :consecutive_nights_shortest,
								consecutive_nights_longest = :consecutive_nights_longest,
								meal_type_morning = :meal_type_morning,
								meal_type_daytime = :meal_type_daytime,
								meal_type_night = :meal_type_night,
								sale_max_num = :sale_max_num,
								image = :image,
								cancel_note = :cancel_note,
                                fee_lang_cd_mapping = :fee_lang_cd_mapping,
                                update_user = :user_id
							WHERE
								hotel_code = :hotel_code
								AND rate_plan_code = :rate_plan_code
							";
                    $query = DB::query(Database::UPDATE, $sql);
                    $query->parameters(array(
                        ':hotel_code' => $data->hotel_code,
                        ':rate_plan_code' => $data->rate_plan_code,
                        ':rate_plan_name' => $data->rate_plan_name,
                        ':offer_from' => $data->offer_from,
                        ':offer_to' => $data->offer_to,
                        ':flg_active' => $data->flg_active,
                        ':fee_system' => $data->fee_system,
                        ':status' => $data->status,
                        ':use_type' => $data->use_type,
                        ':description' => $data->description,
                        ':publish_from' => $data->publish_from,
                        ':publish_to' => $data->publish_to,
                        ':reserve_receipt_before_days' => $data->reserve_receipt_before_days,
                        ':reserve_receipt_before_time' => $data->reserve_receipt_before_time,
                        ':checkin_desc' => $data->checkin_desc,
                        ':checkout_desc' => $data->checkout_desc,
                        ':consecutive_nights_shortest' => $data->consecutive_nights_shortest,
                        ':consecutive_nights_longest' => $data->consecutive_nights_longest,
                        ':meal_type_morning' => $data->meal_type_morning,
                        ':meal_type_daytime' => $data->meal_type_daytime,
                        ':meal_type_night' => $data->meal_type_night,
                        ':sale_max_num' => $data->sale_max_num,
                        ':image' => $data->image,
                        ':cancel_note' => $data->cancel_note,
                        ':fee_lang_cd_mapping' => $fee_lang_cd_mapping,
                        ':user_id' => $post->user_id,
                    ));

                    $ret = $query->execute();
                    $ret = "00";
                } else {
                    $sql = "INSERT INTO t_reserve_plan
							(hotel_code, rate_plan_code, rate_plan_name, offer_from, offer_to
							, flg_active, fee_system, status, use_type
							, description, publish_from, publish_to, reserve_receipt_before_days, reserve_receipt_before_time
							, checkin_desc, checkout_desc, consecutive_nights_shortest, consecutive_nights_longest, meal_type_morning, meal_type_daytime, meal_type_night
							, sale_max_num, image, cancel_note, fee_lang_cd_mapping, create_user)
							VALUES
							(:hotel_code, :rate_plan_code, :rate_plan_name, :offer_from, :offer_to
							, :flg_active, :fee_system, :status, :use_type
							, :description, :publish_from, :publish_to, :reserve_receipt_before_days, :reserve_receipt_before_time
							, :checkin_desc, :checkout_desc, :consecutive_nights_shortest, :consecutive_nights_longest, :meal_type_morning, :meal_type_daytime, :meal_type_night
							, :sale_max_num, :image, :cancel_note, :fee_lang_cd_mapping, :user_id)
							";
                    $query = DB::query(Database::UPDATE, $sql);
                    $query->parameters(array(
                        ':hotel_code' => $data->hotel_code,
                        ':rate_plan_code' => $data->rate_plan_code,
                        ':rate_plan_name' => $data->rate_plan_name,
                        ':offer_from' => $data->offer_from,
                        ':offer_to' => $data->offer_to,
                        ':flg_active' => $data->flg_active,
                        ':fee_system' => $data->fee_system,
                        ':status' => $data->status,
                        ':use_type' => $data->use_type,
                        ':description' => $data->description,
                        ':publish_from' => $data->publish_from,
                        ':publish_to' => $data->publish_to,
                        ':reserve_receipt_before_days' => $data->reserve_receipt_before_days,
                        ':reserve_receipt_before_time' => $data->reserve_receipt_before_time,
                        ':checkin_desc' => $data->checkin_desc,
                        ':checkout_desc' => $data->checkout_desc,
                        ':consecutive_nights_shortest' => $data->consecutive_nights_shortest,
                        ':consecutive_nights_longest' => $data->consecutive_nights_longest,
                        ':meal_type_morning' => $data->meal_type_morning,
                        ':meal_type_daytime' => $data->meal_type_daytime,
                        ':meal_type_night' => $data->meal_type_night,
                        ':sale_max_num' => $data->sale_max_num,
                        ':image' => $data->image,
                        ':cancel_note' => $data->cancel_note,
                        ':fee_lang_cd_mapping' => $fee_lang_cd_mapping,
                        ':user_id' => $post->user_id,
                    ));

                    $ret = $query->execute();
                    $ret = "00";
                }

                $length = count($room_plan_data);
                for ($x = 0; $x < $length; $x++) {
                    $data = $room_plan_data[$x];

                    $sql = "SELECT 1 FROM t_reserve_room_plan
							WHERE
								hotel_code = :hotel_code
								AND room_type_code = :room_type_code
								AND rate_plan_code = :rate_plan_code
							";
                    $query = DB::query(Database::SELECT, $sql);
                    $query->parameters(array(
                        ':hotel_code' => $data->hotel_code,
                        ':room_type_code' => $data->room_type_code,
                        ':rate_plan_code' => $data->rate_plan_code,
                    ));
                    $ret = $query->execute()->as_array();

                    if (count($ret) > 0) {
                        if ($data->use_flg == true) {
                            // 继续组合
                            $sql = "UPDATE t_reserve_room_plan
									SET
										child_fee = :child_fee,
										fee_from = :fee_from,
										fee_to = :fee_to,
                                        update_user = :user_id
									WHERE
										hotel_code = :hotel_code
										AND room_type_code = :room_type_code
										AND rate_plan_code = :rate_plan_code
									";
                            $query = DB::query(Database::UPDATE, $sql);
                            $query->parameters(array(
                                ':hotel_code' => $data->hotel_code,
                                ':room_type_code' => $data->room_type_code,
                                ':rate_plan_code' => $data->rate_plan_code,
                                ':child_fee' => $data->child_fee,
                                ':fee_from' => $data->fee_from,
                                ':fee_to' => $data->fee_to,
                                ':user_id' => $post->user_id,
                            ));

                            $ret = $query->execute();
                            $ret = "00";
                        } else {
                            // 不再组合
                            $sql = "DELETE FROM t_reserve_room_plan
									WHERE
										hotel_code = :hotel_code
										AND room_type_code = :room_type_code
										AND rate_plan_code = :rate_plan_code
									";
                            $query = DB::query(Database::UPDATE, $sql);
                            $query->parameters(array(
                                ':hotel_code' => $data->hotel_code,
                                ':room_type_code' => $data->room_type_code,
                                ':rate_plan_code' => $data->rate_plan_code,
                            ));

                            $ret = $query->execute();
                            $ret = "00";

                            // 同时删除所有的plan rate信息
                            $sql = "DELETE FROM t_reserve_plan_rate
									WHERE
										hotel_code = :hotel_code
										AND room_type_code = :room_type_code
										AND rate_plan_code = :rate_plan_code
									";
                            $query = DB::query(Database::UPDATE, $sql);
                            $query->parameters(array(
                                ':hotel_code' => $data->hotel_code,
                                ':room_type_code' => $data->room_type_code,
                                ':rate_plan_code' => $data->rate_plan_code,
                            ));

                            $ret = $query->execute();
                            $ret = "00";
                        }
                    } else {
                        if ($data->use_flg == true) {
                            // 新加组合
                            $sql = "INSERT INTO t_reserve_room_plan
									(hotel_code, room_type_code, rate_plan_code, child_fee, fee_from, fee_to, create_user)
									VALUES
									(:hotel_code, :room_type_code, :rate_plan_code, :child_fee, :fee_from, :fee_to, :user_id)
									";
                            $query = DB::query(Database::UPDATE, $sql);
                            $query->parameters(array(
                                ':hotel_code' => $data->hotel_code,
                                ':room_type_code' => $data->room_type_code,
                                ':rate_plan_code' => $data->rate_plan_code,
                                ':child_fee' => $data->child_fee,
                                ':fee_from' => $data->fee_from,
                                ':fee_to' => $data->fee_to,
                                ':user_id' => $post->user_id,
                            ));

                            $ret = $query->execute();
                            $ret = "00";
                        }
                    }
                }

                $sql = "SELECT create_datetime, update_datetime FROM t_reserve_plan
						WHERE
							hotel_code = :hotel_code
							AND rate_plan_code = :rate_plan_code
						";
                $query = DB::query(Database::SELECT, $sql);
                $query->parameters(array(
                    ':hotel_code' => $data->hotel_code,
                    ':rate_plan_code' => $data->rate_plan_code,
                ));
                $ret = $query->execute()->as_array();

                $this->response->body($this->jresult("00", null, $ret[0]));
                //error_log("will commit");
                //DB::query(NULL,'COMMIT');
                Database::instance()->commit();
            } catch (Exception $e) {
                //error_log("will rollback");
                //DB::query(NULL,'ROLLBACK');
                Database::instance()->rollback();
                error_log($e->getMessage());
                $ret = $e->getMessage();
                $this->response->body($this->jresult("01", null, $ret));
            }
        } else {
            $this->response->body($this->jresult("01"));
        }
        return;
    }

    public function action_save_plan_rate()
    {
        $this->response->headers('Access-Control-Allow-Origin', '*');
        $condition = $this->request->post();
        if ($condition) {
            $post = json_decode($condition["condition"]);
            //error_log($post);
            $db_hotel_code = $this->check_accesstoken($post);
            if ($db_hotel_code == "") {
                return $this->response->body($this->jresult("06"));
            }

            try {
                Database::instance()->begin();
                $save_data = $post->data;

                $length = count($save_data);
                for ($x = 0; $x < $length; $x++) {
                    $data = $save_data[$x];
                    // 2024.05.28 begin
                    $lang_cd = "ja";
                    if (isset($data->lang_cd)) {
                        $lang_cd = $data->lang_cd;
                    }
                    // 2024.05.28 end

                    $sql = "SELECT 1 FROM t_reserve_plan_rate
							WHERE
								hotel_code = :hotel_code
								AND room_type_code = :room_type_code
								AND rate_plan_code = :rate_plan_code
								AND ymd = :ymd
                                AND lang_cd = :lang_cd
							";
                    $query = DB::query(Database::SELECT, $sql);
                    $query->parameters(array(
                        ':hotel_code' => $data->hotel_code,
                        ':room_type_code' => $data->room_type_code,
                        ':rate_plan_code' => $data->rate_plan_code,
                        ':ymd' => $data->ymd,
                        ':lang_cd' => $lang_cd,
                    ));
                    $ret = $query->execute()->as_array();

                    if (count($ret) > 0) {
                        $sql = "UPDATE t_reserve_plan_rate
								SET
									plan_status = :plan_status,
									fee = :fee,
                                    update_user = :user_id
								WHERE
									hotel_code = :hotel_code
									AND room_type_code = :room_type_code
									AND rate_plan_code = :rate_plan_code
									AND ymd = :ymd
									AND lang_cd = :lang_cd
								";
                        $query = DB::query(Database::UPDATE, $sql);
                        $query->parameters(array(
                            ':hotel_code' => $data->hotel_code,
                            ':room_type_code' => $data->room_type_code,
                            ':rate_plan_code' => $data->rate_plan_code,
                            ':ymd' => $data->ymd,
                            ':lang_cd' => $lang_cd,
                            ':plan_status' => $data->plan_status,
                            ':fee' => $data->fee,
                            ':user_id' => $post->user_id,
                        ));

                        $ret = $query->execute();
                        $ret = "00";
                    } else {
                        $sql = "INSERT INTO t_reserve_plan_rate
								(hotel_code, room_type_code, rate_plan_code, ymd, lang_cd, plan_status, fee, create_user)
								VALUES
								(:hotel_code, :room_type_code, :rate_plan_code, :ymd, :lang_cd, :plan_status, :fee, :user_id)
								";
                        $query = DB::query(Database::UPDATE, $sql);
                        $query->parameters(array(
                            ':hotel_code' => $data->hotel_code,
                            ':room_type_code' => $data->room_type_code,
                            ':rate_plan_code' => $data->rate_plan_code,
                            ':ymd' => $data->ymd,
                            ':lang_cd' => $lang_cd,
                            ':plan_status' => $data->plan_status,
                            ':fee' => $data->fee,
                            ':user_id' => $post->user_id,
                        ));

                        $ret = $query->execute();
                        $ret = "00";
                    }
                }

                $this->response->body($this->jresult("00", null, $ret));
                //error_log("will commit");
                //DB::query(NULL,'COMMIT');
                Database::instance()->commit();
            } catch (Exception $e) {
                //error_log("will rollback");
                //DB::query(NULL,'ROLLBACK');
                Database::instance()->rollback();
                error_log($e->getMessage());
                $ret = $e->getMessage();
                $this->response->body($this->jresult("01", null, $ret));
            }
        } else {
            $this->response->body($this->jresult("01"));
        }
        return;
    }


    public function action_get_orders()
    {
        $this->response->headers('Access-Control-Allow-Origin', '*');
        $condition = $this->request->post();
        if ($condition) {
            $post = json_decode($condition["condition"]);
            $db_hotel_code = $this->check_accesstoken($post);
            if ($db_hotel_code == "") {
                return $this->response->body($this->jresult("01"));
            }

            $talkappi_reserve_id = $post->talkappi_reserve_id;
            $sql_talkappi_reserve_id = "";
            if ($talkappi_reserve_id != "") {
                $sql_talkappi_reserve_id = "AND b.talkappi_reserve_id = :talkappi_reserve_id";
            }

            $reserve_date_from = $post->reserve_date_from;
            $sql_reserve_date_from = "";
            if ($reserve_date_from != "") {
                $reserve_date_from_final = substr($reserve_date_from, 0, 4) . "-" . substr($reserve_date_from, 4, 2) . "-" . substr($reserve_date_from, 6, 2) . " 00:00:00";
                $reserve_date_from = $reserve_date_from_final;
                $sql_reserve_date_from = "AND b.create_datetime >= :reserve_date_from";
            }

            $reserve_date_to = $post->reserve_date_to;
            $sql_reserve_date_to = "";
            if ($reserve_date_to != "") {
                $reserve_date_to_final = substr($reserve_date_to, 0, 4) . "-" . substr($reserve_date_to, 4, 2) . "-" . substr($reserve_date_to, 6, 2) . " 23:59:59";
                $reserve_date_to = $reserve_date_to_final;
                $sql_reserve_date_to = "AND b.create_datetime <= :reserve_date_to";
            }

            $checkin_date_from = $post->checkin_date_from;
            $sql_checkin_date_from = "";
            if ($checkin_date_from != "") {
                $sql_checkin_date_from = "AND b.effective_date >= :checkin_date_from";
            }

            $checkin_date_to = $post->checkin_date_to;
            $sql_checkin_date_to = "";
            if ($checkin_date_to != "") {
                $sql_checkin_date_to = "AND b.effective_date <= :checkin_date_to";
            }

            $sql = "SELECT b.*
				FROM t_reserve_order b
				WHERE
                    b.hotel_code = :hotel_code
                    $sql_talkappi_reserve_id
                    $sql_reserve_date_from
                    $sql_reserve_date_to
                    $sql_checkin_date_from
                    $sql_checkin_date_to
                ORDER BY
                    b.id
				";
            $query = DB::query(Database::SELECT, $sql);
            $query->parameters(array(
                ':hotel_code' => $db_hotel_code,
                ':talkappi_reserve_id' => $talkappi_reserve_id,
                ':reserve_date_from' => $reserve_date_from,
                ':reserve_date_to' => $reserve_date_to,
                ':checkin_date_from' => $checkin_date_from,
                ':checkin_date_to' => $checkin_date_to,
            ));
            $results = $query->execute()->as_array();
            $results_length = count($results);

            $ret = array();
            for ($i = 0; $i < $results_length; $i++) {
                $cur_order = $results[$i];
                $cur_order_for_display = $results[$i];
                $cur_ret = array();
                if ($cur_order["type"] == "1") {
                    $cur_ret["状態"] = "予約";
                    $cur_order_user_condition = json_decode($cur_order["user_condition"]);
                } elseif ($cur_order["type"] == "2") {
                    $cur_ret["状態"] = "変更";
                    $cur_order_user_condition = json_decode($cur_order["user_condition"]);
                } elseif ($cur_order["type"] == "3") {
                    $cur_ret["状態"] = "取消";
                    $cur_order_all = $this->get_all_order_by_talkappi_reserve_id($db_hotel_code, $cur_order["talkappi_reserve_id"]);
                    $cur_order_user_condition = json_decode($cur_order_all[1]["user_condition"]);
                    $cur_order_for_display = $cur_order_all[1];
                } else {
                    $cur_ret["状態"] = "不明";
                }

                $cur_ret["予約番号"] = $cur_order["talkappi_reserve_id"];
                $cur_ret["予約日時"] = $cur_order["create_datetime"];
                $cur_ret["チェックイン"] = $cur_order["effective_date"];

                $time_span_duration = $cur_order_user_condition->global_info->time_span_duration;
                $time_span_duration = (int)substr($time_span_duration, 1, strlen($time_span_duration) - 2);
                if ($time_span_duration == 0) {
                    // 日归
                    $cur_ret["チェックアウト"] = $cur_order["expire_date"];
                    $cur_ret["宿泊日数"] = "日帰り";
                } else {
                    $temp_date = date_create_from_format("Ymd", $cur_order["expire_date"]);
                    $cur_ret["チェックアウト"] = date("Ymd", strtotime("+1 days", $temp_date->getTimestamp()));
                    $cur_ret["宿泊日数"] = $time_span_duration;
                    //$cur_ret["有效结束日"] = $cur_order["expire_date"];
                    //$cur_ret["time_span_duration"] = $cur_order_user_condition->global_info->time_span_duration;
                    //$cur_ret["time_span_duration3"] = date("Ymd",$temp_date);
                }

                $name = $cur_order_user_condition->guest_info->given_name;
                if ($name == "" || $name == "　") {
                    $name = $cur_order_user_condition->guest_info->surname;
                } else {
                    if ($cur_order_user_condition->guest_info->surname != "" && $cur_order_user_condition->guest_info->surname != "　") {
                        $name .= " " . $cur_order_user_condition->guest_info->surname;
                    }
                }
                $cur_ret["代表者"] = $name;

                {
                    $cur_room_type_cd = $cur_order_user_condition->room_stay->room_list[0]->room_type_code;
                    $cur_ret["部屋"] = $cur_room_type_cd;
                    $cur_room_found = 0;
                    if ($cur_order_for_display["db_rooms"]) {
                        $db_rooms_obj = json_decode($cur_order_for_display["db_rooms"]);
                        $count_rooms = count($db_rooms_obj);
                        for ($room_i = 0; $room_i < $count_rooms; $room_i++) {
                            if ($db_rooms_obj[$room_i]->room_type_code == $cur_room_type_cd) {
                                $cur_ret["部屋"] = $db_rooms_obj[$room_i]->room_type_name . "($cur_room_type_cd)";
                                $cur_room_found = 1;
                                break;
                            }
                        }
                    }
                }
                {
                    $cur_rate_plan_code = $cur_order_user_condition->room_stay->rate_plan_code;
                    $cur_ret["プラン"] = $cur_rate_plan_code;
                    $cur_plan_found = 0;
                    if ($cur_order_for_display["db_plans"]) {
                        $db_plans_obj = json_decode($cur_order_for_display["db_plans"]);
                        $count_plans = count($db_plans_obj);
                        for ($plan_i = 0; $plan_i < $count_plans; $plan_i++) {
                            if ($db_plans_obj[$plan_i]->rate_plan_code == $cur_rate_plan_code) {
                                $cur_ret["プラン"] = $db_plans_obj[$plan_i]->rate_plan_name . "($cur_rate_plan_code)";
                                $cur_plan_found = 1;
                                break;
                            }
                        }
                    }
                }
                {
                    $cur_room_num = 0;
                    $cur_adult_num = 0;
                    $cur_child_num = 0;
                    $cur_room_list = $cur_order_user_condition->room_stay->room_list;
                    for ($list_i = 0; $list_i < count($cur_room_list); $list_i++) {
                        $cur_room_num += ($cur_room_list[$list_i]->quantity - 0);
                        $cur_guest_count_of_one_room = $cur_room_list[$list_i]->guest_count_of_one_room;
                        for ($room_i = 0; $room_i < count($cur_guest_count_of_one_room); $room_i++) {
                            $cur_age = $cur_guest_count_of_one_room[$room_i]->AgeQualifyingCode;
                            $cur_count = $cur_guest_count_of_one_room[$room_i]->Count;
                            if ($cur_age == "10") {
                                $cur_adult_num += ($cur_count - 0);
                            } elseif ($cur_age == "51") {
                                $cur_adult_num += ($cur_count - 0);
                            } elseif ($cur_age == "52") {
                                $cur_adult_num += ($cur_count - 0);
                            } elseif ($cur_age == "7") {
                                $cur_child_num += ($cur_count - 0);
                            } elseif ($cur_age == "8") {
                                $cur_child_num += ($cur_count - 0);
                            } elseif ($cur_age == "53") {
                                $cur_child_num += ($cur_count - 0);
                            } elseif ($cur_age == "54") {
                                $cur_child_num += ($cur_count - 0);
                            } elseif ($cur_age == "55") {
                                $cur_child_num += ($cur_count - 0);
                            } elseif ($cur_age == "56") {
                                $cur_child_num += ($cur_count - 0);
                            } elseif ($cur_age == "57") {
                                $cur_child_num += ($cur_count - 0);
                            } elseif ($cur_age == "58") {
                                $cur_child_num += ($cur_count - 0);
                            }
                        }
                    }

                    $cur_ret["室数"] = $cur_room_num;
                    $cur_ret["大人数"] = $cur_adult_num;
                    $cur_ret["子供数"] = $cur_child_num;
                }

                $ret[] = $cur_ret;
            }

            $this->response->body($this->jresult("00", null, $ret));
            return;
        } else {
            $this->response->body($this->jresult("01"));
        }
        return;
    }

    public function get_all_order_by_talkappi_reserve_id($db_hotel_code, $talkappi_reserve_id)
    {
        $sql = "SELECT b.*
				FROm t_reserve_order b
				WHERE
                    b.hotel_code = :hotel_code
                    AND b.talkappi_reserve_id = :talkappi_reserve_id
                ORDER BY
                    b.create_datetime DESC
				";
        $query = DB::query(Database::SELECT, $sql);
        $query->parameters(array(
            ':hotel_code' => $db_hotel_code,
            ':talkappi_reserve_id' => $talkappi_reserve_id,
        ));
        $results = $query->execute()->as_array();
        return $results;
    }
}
