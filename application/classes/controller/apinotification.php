<?php

defined('SYSPATH') or die('No direct script access.');
class Controller_Apinotification extends Controller
{
    protected $_model = null;
    public function __construct(Request $request, Response $response)
    {
		ini_set('session.save_path', "/var/lib/php/sessions_omotenashi");
		ini_set('session.gc_maxlifetime', 7776000); // 90日 * 24 * 60 * 60
        $this->_model = new Model_Adminmodel();
        parent::__construct($request, $response);
        return ;
    }

    private function is_login()
    {
        $this->_user = Session::instance()->get('user', null);
        if (!$this->_user) {
            return false;
        }
        return true;
    }

    private function login($email, $password)
    {
        $admin_model = new Model_Adminmodel();
        $users = ORM::factory('user')
            ->where('email', '=', $email)
            ->where('delete_flg', '=', 0)
            ->where('role_cd', '>', '00')
            ->find_all();
        if (count($users) == 0) {
            //error_log("return=false1");
            return false;
        }

        $user = null;
        foreach ($users as $u) {
            if ($admin_model->verify_password($password, $u->password)) {
                $user = $u;
            }
        }

        if ($user == null) {
            //error_log("return=false2");
            return false;
        }

        Session::instance()->set('user_id', $user->user_id);
        Session::instance()->set('user_name', $user->name);
        Session::instance()->set('user', $user);

        $bots = [];
        $bot_array = [];     // not include child bot
        $bot_all_array = []; // all bot include child bot // 2023.03.27 #40529
        $has_grp_bot = false;
        if ($user->role_cd == '99' || $user->role_cd == '80') {
            $bots = ORM::factory('bot')->where('delete_flg', '=', 0)->where('bot_id', '<>', 0)->order_by('bot_id')->find_all();
            foreach ($bots as $b) {
                $bot_all_array[] = [$b->bot_id, $b->bot_name];  // 2023.03.27 #40529
                $grp_bot_id = $admin_model->get_grp_bot_id($b->bot_id);
                if ($grp_bot_id > 0) {
                    continue;
                }
                $bot_array[] = [$b->bot_id, $b->bot_name];
            }
        } else {
            $user_bots = $admin_model->get_user_bots($user->email);
            $p_bot_arr = [];
            $all_bot_arr = []; // 2023.03.27 #40529
            foreach ($user_bots as $b) {
                $bots[] = $b;
                $grp_bot_id = $admin_model->get_grp_bot_id($b->bot_id);
                if ($grp_bot_id == 0) {
                    // $bは親ボット
                    $bot_array[] = [$b->bot_id, $b->bot_name];
                    $p_bot_arr[] = $b->bot_id;
                    $has_grp_bot = true;
                    // 2023.03.27 #40529 begin
                    $child_bots = ORM::factory('bot')->where('delete_flg', '=', 0)
                                ->where('bot_id', '>=', $b->bot_id)
                                ->where('bot_id', '<',  (intval($b->bot_id) + 1000))
                                ->order_by('bot_id')->find_all();
                    foreach ($child_bots as $child_bot) {
                        $bot_all_array[] = [$child_bot->bot_id, $child_bot->bot_name];
                        $all_bot_arr[] = $child_bot->bot_id;
                    }
                    // 2023.03.27 #40529 end
                } elseif ($grp_bot_id < 0) {
                    // $bは通常ボット
                    $bot_array[] = [$b->bot_id, $b->bot_name];
                    // 2023.03.27 #40529 begin
                    $bot_all_array[] = [$b->bot_id, $b->bot_name];;
                    $all_bot_arr[] = $b->bot_id;
                // 2023.03.27 #40529 end
                } else {
                    // $bは子ボット
                    if (!in_array($grp_bot_id, $p_bot_arr)) {
                        $grp_bot = ORM::factory('bot', $grp_bot_id);
                        $bot_array[] = [$grp_bot_id, $grp_bot->bot_name];
                        $p_bot_arr[] = $grp_bot_id;
                        $has_grp_bot = true;
                    }
                    // 2023.03.27 #40529 begin
                    if (!in_array($b->bot_id, $all_bot_arr)) {
                        $bot_all_array[] = [$b->bot_id, $b->bot_name];;
                        $all_bot_arr[] = $b->bot_id;
                    }
                    // 2023.03.27 #40529 end
                }
            }
        }
        Session::instance()->set('bot_array', $bot_array);
        Session::instance()->set('bot_all_array', $bot_all_array);  // 2023.03.27 #40529
        Session::instance()->set('has_grp_bot', $has_grp_bot);

        //error_log("return=true");
        return true;
    }

    public function action_login()
    {
        $this->response->headers('Access-Control-Allow-Origin', '*');
        $this->response->headers('Access-Control-Expose-Headers', "token");
        /*
        $last_session = Session::instance();
        $session_id = session_id();
        */
        $condition = $this->request->query();
        $token = "";
        if (isset($condition["token"])) {
            $token = $condition["token"];
        }
        if ($token != "") {
            //error_log("action_get_notify() token is $token");
            session_id($token);
            $last_session = Session::instance();
            $session_id = session_id();
        } else {
            $last_session = Session::instance();
            $session_id = session_id();
        }

        //error_log("session id is $session_id");
        /*
        */
        /*
        $domian = $_SERVER['HTTP_ORIGIN'];//获取请求的域名
        $this->response->headers('Access-Control-Allow-Origin', "$domian");
        $this->response->headers('Access-Control-Allow-Credentials', "true");
        */
        $this->response->headers('token', $session_id);
        $post = $this->request->post();
        $email = $post['email'];
        //error_log("email=$email");
        $result = $this->login($post['email'], $post['password']);

        $ret["ret"] = $result;
        //error_log("result=$result");
        if ($result == true) {
            // #48137の対応のためusers_all_arrayはここに追加
            $users = ORM::factory('user')
                ->where('email', '=', $email)
                ->where('delete_flg', '=', 0)
                ->where('role_cd', '>', '00')
                ->find_all();
            $user_all_array = [];
            foreach ($users as $u) {
                $user_all_array[] = [$u->bot_id, $u->user_id];
            }

            $ret["user_id"] = Session::instance()->get('user_id');
            $ret["user_id_all"] = $user_all_array;
            $ret["bots"] = Session::instance()->get('bot_array');
            $ret["bots_all"] = Session::instance()->get('bot_all_array');
        }
        $data = json_encode($ret, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
        //error_log("data=$data");
        $this->response->body($data);
    }

    public function action_get_notify()
    {
        $this->response->headers('Access-Control-Allow-Origin', '*');
        $condition = $this->request->query();

        $token = "";
        if (isset($condition["token"])) {
            $token = $condition["token"];
        }
        if ($token != "") {
            //error_log("action_get_notify() token is $token");
            session_id($token);
            $last_session = Session::instance();
            $session_id = session_id();
            //error_log("action_get_notify() session id is $session_id");

            $check_login = $this->is_login();
            if ($check_login == false) {
                $ret["ret"] = "false";
                $ret["reason"] = "not login";

                $data = json_encode($ret, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
                $this->response->body($data);
                return ;
            }

        //error_log("action_get_notify() session user_id is " . Session::instance()->get('user_id', null));
        //error_log("action_get_notify() session user_name is " . Session::instance()->get('user_name', null));
        } else {
        }


        $bot_id = "";
        if (isset($condition["bot_id"])) {
            $bot_id=$condition["bot_id"];
        }
        $log_time = "";
        if (isset($condition["log_time"])) {
            $log_time=$condition["log_time"];
        }

        $ret = [];
        if ($bot_id == "") {
            $ret["ret"] = "false";
            $data = json_encode($ret, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
            $this->response->body($data);
            return ;
        }
        if ($log_time == "") {
            $log_time = date('Y-m-d H:i:s');
        }
        $now = date('Y-m-d H:i:s');

        $services = $this->get_notification_request($bot_id, $log_time, $now);
        $chats = $this->get_notification_chatrequest($bot_id, $log_time, $now);

        $ret["ret"] = "true";
        $ret["now_time"] = $log_time;
        $ret["services"] = $services;
        $ret["chats"] = $chats;

        $config = Kohana::$config->load('botsetting.' . 'ja');

        // 取得该bot的通知类型
        $notification_type = ORM::factory('botsetting')->where('bot_id', '=', $bot_id)->where('setting_cd', '=', 'notification_type')->find();
        if (isset($notification_type->setting_value)) {
            $ret["notification_type"] = $notification_type->setting_value;
        } elseif (isset($config["notification_type"])) {
            $ret["notification_type"] = $config["notification_type"][1];
        } else {
            $ret["notification_type"] = "1"; //1 把主画面弹出  2 弹出banner
        }
        // 取得该bot的通知间隔
        $notification_refresh_interval = ORM::factory('botsetting')->where('bot_id', '=', $bot_id)->where('setting_cd', '=', 'num_admin_refresh_interval')->find();
        if (isset($notification_refresh_interval->setting_value)) {
            $ret["notification_refresh_interval"] = $notification_refresh_interval->setting_value;
        } elseif (isset($config["num_admin_refresh_interval"])) {
            $ret["notification_refresh_interval"] = $config["num_admin_refresh_interval"][1];
        } else {
            $ret["notification_refresh_interval"] = "10";
        }
        //error_log("notification_type = " . $ret["notification_type"]);

        /*
        $ret["chats"]=[
            [
                "member_id"=>"6d94cb78-2d59-1141-e8ad-90def5796825",
                "sns_type_cd"=>"wb",
                "name"=>"wuzhaoname",
                "last_name"=>"zhao",
                "first_name"=>"wu",
                "last_talk_date"=>"2022-09-10 18:30"
            ]
        ];
        */

        $data = json_encode($ret, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
        $this->response->body($data);
    }

    // 2022.12.29 #35728
    public function action_requests() {
        $undefined_txt = [
            "ja"=>"未指定",
            "cn"=>"未指定",
            "en"=>"Unspecified",
            "kr"=>"미지정",
            "th"=>"ไม่ระบุ",
            "tw"=>"未指定"
        ];

		$query = $this->request->query();
		$post = $this->request->post();

        $token = "";
        $bot_id = "";
        $start_date = "";
        $end_date = "";
        $lang_cd = "ja";

        if (isset($query["token"])) {
            $token = $query["token"];
        }

        if ($this->check_token($token) == null) {
            return ;
        }

        if (array_key_exists('bot_id', $post)) {
            $bot_id = $post["bot_id"];
        } else if (array_key_exists('bot_id', $query)) {
            $bot_id = $query["bot_id"];
        }
        if (array_key_exists('start_date', $post)) {
            $start_date = $post["start_date"] . ' 00:00:00.000';
        } else if (array_key_exists('start_date', $query)) {
            $start_date = $query["start_date"] . ' 00:00:00.000';
        }
        if (array_key_exists('end_date', $post)) {
            $end_date = $post["end_date"] . ' 23:59:59.999';
        } else if (array_key_exists('end_date', $query)) {
            $end_date = $query["end_date"] . ' 23:59:59.999';
        }
        if (array_key_exists('lang_cd', $post)) {
            $lang_cd = $post["lang_cd"];
        } else if (array_key_exists('lang_cd', $query)) {
            $lang_cd = $query["lang_cd"];
        }

        if ($bot_id == "") {
            $ret["ret"] = "false";
            $ret["reason"] = "bot_id not exists.";
            $data = json_encode($ret, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
            $this->response->body($data);
            return ;
        }
        if ($start_date == "") {
            $ret["ret"] = "false";
            $ret["reason"] = "start_date not exists.";
            $data = json_encode($ret, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
            $this->response->body($data);
            return ;
        }
        if ($end_date == "") {
            $ret["ret"] = "false";
            $ret["reason"] = "end_date not exists.";
            $data = json_encode($ret, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
            $this->response->body($data);
            return ;
        }

        $sql = "SELECT 
                    a.service_id,
                    a.intent_cd,
                    (
                        SELECT b.intent_name 
                        FROM m_intent b 
                        WHERE 
                            b.intent_cd = a.intent_cd 
                            AND b.bot_id in (0, :bot_id)
                            AND b.lang_cd = :lang_cd
                        ORDER BY 
                            b.bot_id DESC
                        LIMIT 1
                    ) AS intent_name,
                    a.member_msg AS member_msg,
                    a.lang_cd,
                    a.service_status_cd AS status,
                    a.log_time AS time,
                    a.sns_type_cd,
                    a.member_id,
                    a.reserve2 AS name,
                    (
                        SELECT m.avatar
                        FROM t_bot_member m
                        WHERE
                            m.bot_id = :bot_id
                            AND m.member_id = a.member_id
                    ) AS avatar,

                    a.service_data
                FROM
                    t_bot_service a
                WHERE
                    a.bot_id = :bot_id
                    AND a.log_time >= :start_date
                    AND a.log_time <= :end_date

        ";
        $query = DB::query(Database::SELECT, $sql);
        $query->parameters(array(
            ':bot_id' => $bot_id,
            ':start_date' => $start_date,
            ':end_date' => $end_date,
            ':lang_cd' => $lang_cd,
        ));

		$results = $query->execute()->as_array();

        $count = count($results);
        for ($i=0; $i < $count; $i++) {
            $cur_result = $results[$i];
            $description = $cur_result["member_msg"];

            $cur_service_data_obj = json_decode($cur_result["service_data"]);
            if ($cur_service_data_obj != NULL) {
                foreach($cur_service_data_obj as $key => $value) {
                    $description= preg_replace("/\{(" . $key . ")\}/",$value, $description);
                }

                if (isset($cur_service_data_obj->bot_name)) {
                    $description = preg_replace("/\{(botname)\}/",$cur_service_data_obj->bot_name, $description);
                }

                if (array_key_exists($results[$i]["lang_cd"], $undefined_txt)) {
                    $description = preg_replace("/\{[^}]*\}/",$undefined_txt[$results[$i]["lang_cd"]], $description);
                } else {
                    $description = preg_replace("/\{[^}]*\}/",$undefined_txt["ja"], $description);
                }
            }

            $results[$i]["description"] = $description;
        }

        $data = json_encode($results, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
        $this->response->body($data);
    }

    // 2023.9.27 #52137
    public function action_calls() {
        $query = $this->request->query();
		$post = $this->request->post();

        $token = "";
        $bot_id = "";
        $start_date = "";
        $end_date = "";
        $group = '';

        if (isset($query["token"])) {
            $token = $query["token"];
        }

        if ($this->check_token($token) == null) {
            return;
        }

        if (array_key_exists('bot_id', $post)) {
            $bot_id = $post["bot_id"];
        } else if (array_key_exists('bot_id', $query)) {
            $bot_id = $query["bot_id"];
        }
        if (array_key_exists('group', $post)) {
            $group = $post["group"];
        } else if (array_key_exists('group', $query)) {
            $group = $query["group"];
        }
        if (array_key_exists('start_date', $post)) {
            $start_date = $post["start_date"] . ' 00:00:00.000';
        } else if (array_key_exists('start_date', $query)) {
            $start_date = $query["start_date"] . ' 00:00:00.000';
        }
        if (array_key_exists('end_date', $post)) {
            $end_date = $post["end_date"] . ' 23:59:59.999';
        } else if (array_key_exists('end_date', $query)) {
            $end_date = $query["end_date"] . ' 23:59:59.999';
        }

        if ($bot_id == "") {
            $ret["ret"] = "false";
            $ret["reason"] = "bot_id not exists.";
            $data = json_encode($ret, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
            $this->response->body($data);
            return ;
        }
        if ($start_date == "") {
            $ret["ret"] = "false";
            $ret["reason"] = "start_date not exists.";
            $data = json_encode($ret, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
            $this->response->body($data);
            return ;
        }
        if ($end_date == "") {
            $ret["ret"] = "false";
            $ret["reason"] = "end_date not exists.";
            $data = json_encode($ret, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
            $this->response->body($data);
            return ;
        }

        if ($group == '') {
            $group_sql = '';
        } else {
            $group_sql = ' AND FIND_IN_SET(a.group_cd, :group) ';
        }

        $sql = "SELECT 
            a.call_id,
            a.bot_id,
            a.group_cd,
            a.call_type as telephone_type,
            a.user_info_from,
            a.user_info_to,
            a.session_id,
            a.start,
            a.stop_from,
            a.stop_to,
            b.name AS admin_name,
            c.member_create_time,
            x.vir_call_duration_second as duration,
            d.phone,
            d.country_cd,
            d.call_preferred_lang
        FROM t_call_history a
        LEFT JOIN
            t_user b ON (
                b.user_id = COALESCE(JSON_UNQUOTE(JSON_EXTRACT(a.user_info_to, '$.user_id')), JSON_UNQUOTE(JSON_EXTRACT(a.user_info_from, '$.user_id')))
            )
        LEFT JOIN (
            SELECT 
                member_id,
                room, 
                MIN(enter_time) AS member_create_time
            FROM t_bot_member_room
            WHERE bot_id = :bot_id
            AND enter_time BETWEEN :start_date AND :end_date
            GROUP BY member_id, room
        ) c ON (
            (c.member_id = JSON_UNQUOTE(JSON_EXTRACT(a.user_info_to, '$.member_id')) AND JSON_UNQUOTE(JSON_EXTRACT(a.user_info_to, '$.room_no')) = c.room) OR (c.member_id = JSON_UNQUOTE(JSON_EXTRACT(a.user_info_from, '$.member_id')) AND JSON_UNQUOTE(JSON_EXTRACT(a.user_info_from, '$.room_no')) = c.room)
        )
        LEFT JOIN x_telephone_log x ON a.session_id = x.session_id
        LEFT JOIN (
            SELECT 
                member_id,
                phone,
                country_cd,
                call_preferred_lang
                FROM t_bot_member
                WHERE bot_id = :bot_id
                GROUP BY member_id
        ) d ON d.member_id = JSON_UNQUOTE(JSON_EXTRACT(a.user_info_from, '$.member_id'))
        WHERE a.bot_id = :bot_id
        AND a.start BETWEEN :start_date AND :end_date"
        . $group_sql .
        "
        ORDER BY
            a.start DESC
        ";

        $query = DB::query(Database::SELECT, $sql);
        $query->parameters(array(
            ':bot_id' => $bot_id,
            ':start_date' => $start_date,
            ':end_date' => $end_date,
            ':group' => $group,
        ));

        $results = $query->execute()->as_array();
        $count = count($results);

        $data_results = [];

        for ($i=0; $i < $count; $i++) {
            $cur_result = $results[$i];

            $user_info_from = json_decode($cur_result["user_info_from"]);
            $user_info_to = json_decode($cur_result["user_info_to"]);
            $user_info = null;
            if (isset($user_info_from->type) && ($user_info_from->type == "user" || $user_info_from->type == "web")){
                if (isset($user_info_from->src) && $user_info_from->src == "very") {
                    $user_info = $user_info_from;
                }
            }
            if (isset($user_info_to->type) && $user_info_to->type == "user") {
                if (isset($user_info_to->src) && $user_info_to->src == "very") {
                    $user_info = $user_info_to;
                }
            }
            if ($user_info == null) {
                continue;
            }

            $stop = $cur_result["stop_to"] ? $cur_result["stop_to"] : $cur_result["stop_from"];

            $results[$i]["user_info_from"] = $user_info_from->type;
            if ($user_info_to != null) {
                $results[$i]["user_info_to"] = $user_info_to->type;
            }
            else {
                $results[$i]["user_info_to"] = null;
            }
            $results[$i]["user_info"] = $user_info;
            $results[$i]["stop"] = $stop;

            unset($results[$i]["stop_to"]);
            unset($results[$i]["stop_from"]);
            $data_results[] = $results[$i];
        }

        $data = json_encode($data_results, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
        $this->response->body($data);
    }

    public function action_omotenashicalls() {
        $query = $this->request->query();
		$post = $this->request->post();

        $token = "";
        $bot_id = "";
        $start_date = "";
        $end_date = "";
        $user_id = "";
        $lang_cd = "ja";

        if (isset($query["token"])) {
            $token = $query["token"];
        }

        if ($this->check_token($token) == null) {
            return;
        }

        if (array_key_exists('bot_id', $post)) {
            $bot_id = $post["bot_id"];
        } else if (array_key_exists('bot_id', $query)) {
            $bot_id = $query["bot_id"];
        }
        
        if (array_key_exists('start_date', $post)) {
            $start_date = $post["start_date"] . ' 00:00:00.000';
        } else if (array_key_exists('start_date', $query)) {
            $start_date = $query["start_date"] . ' 00:00:00.000';
        }
        if (array_key_exists('end_date', $post)) {
            $end_date = $post["end_date"] . ' 23:59:59.999';
        } else if (array_key_exists('end_date', $query)) {
            $end_date = $query["end_date"] . ' 23:59:59.999';
        }

        if (array_key_exists('user_id', $post)) {
            $user_id = $post["user_id"];
        } else if (array_key_exists('user_id', $query)) {
            $user_id = $query["user_id"];
        }

        if (array_key_exists('lang_cd', $post)) {
            $lang_cd = $post["lang_cd"];
        } else if (array_key_exists('lang_cd', $query)) {
            $lang_cd = $query["lang_cd"];
        }

        if ($bot_id == "") {
            $ret["ret"] = "false";
            $ret["reason"] = "bot_id not exists.";
            $data = json_encode($ret, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
            $this->response->body($data);
            return ;
        }
        if ($start_date == "") {
            $ret["ret"] = "false";
            $ret["reason"] = "start_date not exists.";
            $data = json_encode($ret, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
            $this->response->body($data);
            return ;
        }
        if ($end_date == "") {
            $ret["ret"] = "false";
            $ret["reason"] = "end_date not exists.";
            $data = json_encode($ret, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
            $this->response->body($data);
            return ;
        }
        if ($user_id == "") {
            $ret["ret"] = "false";
            $ret["reason"] = "user_id not exists.";
            $data = json_encode($ret, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
            $this->response->body($data);
            return ;
        }

        $sql = "SELECT 
                    a.call_id,
                    a.bot_id,
                    a.group_cd,
                    a.call_type AS telephone_type,
                    a.user_info_from,
                    a.user_info_to,
                    a.session_id,
                    a.start,
                    a.stop_from,
                    a.stop_to,
                    b.name AS from_name,
                    b.call_group_cd AS from_group_cd,
                    b.user_id AS from_user_id,
                    c.name AS to_name,
                    c.call_group_cd AS to_group_cd,
                    c.user_id AS to_user_id,
                    x.vir_call_duration_second AS duration
                FROM 
                    t_call_history a
                LEFT JOIN 
                    t_user b ON b.user_id = JSON_UNQUOTE(JSON_EXTRACT(a.user_info_from, '$.user_id'))
                LEFT JOIN 
                    t_user c ON c.user_id = JSON_UNQUOTE(JSON_EXTRACT(a.user_info_from, '$.to_user_id'))
                LEFT JOIN 
                    x_telephone_log x ON a.session_id = x.session_id
                WHERE 
                    a.bot_id = :bot_id
                AND a.start BETWEEN :start_date AND :end_date
                AND b.name IS NOT NULL
                AND c.name IS NOT NULL
                AND (b.user_id = :user_id OR c.user_id = :user_id)
                ORDER BY 
                    a.start DESC;";

        $query = DB::query(Database::SELECT, $sql);
        $query->parameters(array(
            ':bot_id' => $bot_id,
            ':start_date' => $start_date,
            ':end_date' => $end_date,
            ':user_id' => $user_id,
        ));
        $results = $query->execute()->as_array();
        $call_group_cds = json_decode($this->get_bot_tpl_message($bot_id, 'call_group_account_select', $lang_cd), true);
        foreach ($results as $key => $result) {
            // Convert from_group_cd to from_group_name
            $from_group_names = array();
            $from_group_cds = explode(',', $result['from_group_cd']);
            foreach ($from_group_cds as $group_cd) {
                $from_group_names[] = isset($call_group_cds[$group_cd]) ? $call_group_cds[$group_cd] : $group_cd;
            }
            $results[$key]['from_group_name'] = implode(',', $from_group_names);
        
            // Convert to_group_cd to to_group_name
            $to_group_names = array();
            $to_group_cds = explode(',', $result['to_group_cd']);
            foreach ($to_group_cds as $group_cd) {
                $to_group_names[] = isset($call_group_cds[$group_cd]) ? $call_group_cds[$group_cd] : $group_cd;
            }
            $results[$key]['to_group_name'] = implode(',', $to_group_names);
        }
        $data = json_encode($results, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
        $this->response->body($data);
    }

    // 2023.01.05 #36045
    public function action_accounts() {
        $id = $this->request->param('id');
        $query = $this->request->query();
		$post = $this->request->post();

        $token = "";

        if (isset($query["token"])) {
            $token = $query["token"];
        }
        if ($this->check_token($token) == null) {
            return ;
        }

        if ($id == "user") {
            $email = "";
            $password = "";
            $lang_cd = "ja";

            if (array_key_exists('lang_cd', $post)) {
                $lang_cd = $post["lang_cd"];
            } else if (array_key_exists('lang_cd', $query)) {
                $lang_cd = $query["lang_cd"];
            }            
            if (array_key_exists('email', $post)) {
                $email = $post["email"];
            } else if (array_key_exists('email', $query)) {
                $email = $query["email"];
            }
            if (array_key_exists('password', $post)) {
                $password = $post["password"];
            } else if (array_key_exists('password', $query)) {
                $password = $query["password"];
            }

			$user = Session::instance()->get('user');
            if ($user == null) {
                $ret["ret"] = "false";
                $ret["reason"] = "failed to get user info from session.";
    
                $data = json_encode($ret, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
                $this->response->body($data);
                return ;
            }

            $ret = array();
            $ret["bot_id"] = $user->bot_id;
            $ret["user_id"] = $user->user_id;
            $ret["name"] = $user->name;
            $ret["lang"] = json_decode("[]");
            if ($user->chat_available_lang != "") {
                $ret["lang"] = explode(",", $user->chat_available_lang);
            }
            $ret["role"] = $user->role_cd;

            $notifications = array();
            $sql = "SELECT 
                        a.*,
                        (
                            SELECT b.intent_name 
                            FROM m_intent b 
                            WHERE 
                                b.intent_cd = a.intent_cd 
                                AND b.bot_id in (0, :bot_id)
                                AND b.lang_cd = :lang_cd
                            ORDER BY 
                                b.bot_id DESC
                            LIMIT 1
                        ) AS intent_name
					FROM 
                        t_user_notify a
					WHERE 
                        a.bot_id = :bot_id
					AND a.user_id = :user_id
            ";
            $query = DB::query(Database::SELECT, $sql);
            $query->parameters(array(
                ':bot_id' => $user->bot_id,
                ':user_id' => $user->user_id,
                ':lang_cd' => $lang_cd,
            ));
            $results = $query->execute()->as_array();
            $count = count($results);
            for ($i=0; $i < $count; $i++) {
                $cur_result = $results[$i];
                $notifications[] = [
                    "intent_cd"=> $cur_result["intent_cd"],
                    "intent_name"=> $cur_result["intent_name"],
                    "intent_notify" => $cur_result["send_type"],
                ];
            }
            $ret["notifications"] = json_decode(json_encode($notifications, JSON_UNESCAPED_UNICODE));

            $data = json_encode($ret, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
            $this->response->body($data);            

        } else {
            $ret["ret"] = "false";
            $ret["reason"] = "action type=$id unsupported.";

            $data = json_encode($ret, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
            $this->response->body($data);
            return null;
        }
    }

    public function action_add_device_token() {
        $query = $this->request->query();
        $device_token = "";
        $user_id = "";

        if (array_key_exists("device_token", $query)) {
            $device_token = $query["device_token"];
        }
        if (array_key_exists("user_id", $query)) {
            $user_id = $query["user_id"];
        }
        if (array_key_exists("voip_token", $query) && 
            is_string($query["voip_token"]) && 
            strlen($query["voip_token"]) > 10 && 
            $query["voip_token"] !== "null"
        ) {
            $voip_token = $query["voip_token"];
        } else {
            $voip_token = null;
        }

        if ($device_token == "") {
            $ret["ret"] = "false";
            $ret["reason"] = "device_token not exists.";
            $data = json_encode($ret, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
            $this->response->body($data);
            return;
        }

        $this->check_device_token($device_token, $user_id, $voip_token);
    }

    public function action_update_device_token() {
        $query = $this->request->query();
        $device_token = "";

        if (array_key_exists("device_token", $query)) {
            $device_token = $query["device_token"];
        }

        // Validate parameter
        if ($device_token === "") {
            $ret = [
                "ret" => "false",
                "reason" => "device_token not exists."
            ];
            $this->response->body(json_encode($ret, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT));
            return;
        }

        // Update update_time if the device_token exists
        $sql = "UPDATE t_user_device
                SET update_time = NOW()
                WHERE device_token = :device_token";
        $query_db = DB::query(Database::UPDATE, $sql);
        $query_db->parameters(array(
            ':device_token' => $device_token,
        ));

        try {
            $affected_rows = $query_db->execute();
            if ($affected_rows == 1) {
                $ret = [
                    "ret" => "true",
                    "data" => [
                        "device_token" => $device_token,
                    ],
                ];
            } else {
                $ret = [
                    "ret" => "false",
                    "reason" => "device_token not found.",
                ];
            }
        } catch (Exception $e) {
            $ret = [
                "ret" => "false",
                "reason" => $e->getMessage(),
            ];
        }

        $this->response->body(json_encode($ret, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT));
    }

    public function get_notification_chatrequest($bot_id, $log_time, $now)
    {
        $sql = "SELECT t.member_id, t.sns_type_cd, t.last_talk_date, t.name, t.last_name, t.first_name
                FROM t_bot_member t
				WHERE " . $this->_create_bot_cond_grp_only($bot_id, "t.bot_id") . " AND t.request_flg = 1 AND last_talk_date >= :log_time AND last_talk_date < :now ";
        $query = DB::query(Database::SELECT, $sql);
        $query->parameters(array(
                ':bot_id' => $bot_id,
                ':log_time' => $log_time,
                ':now' => $now,
        ));
        $results = $query->execute()->as_array();
        return $results;
    }

    public function get_notification_request($bot_id, $log_time, $now)
    {
        $sql = "SELECT t.service_id, t.log_time, t.member_id, m.intent_name
                FROM t_bot_service t 
                INNER JOIN t_bot bot
                    ON t.bot_id = bot.bot_id
                LEFT JOIN m_intent m 
                    ON t.intent_cd = m.intent_cd AND m.bot_id = 0 AND m.intent_class_cd = bot.bot_class_cd
				WHERE "
                . $this->_create_bot_cond_grp_only($bot_id, "t.bot_id")
                . " AND log_time >= :log_time AND log_time < :now"
                //. " AND process_time IS NULL"
                . " AND service_status_cd = '01'"
        ;
        $query = DB::query(Database::SELECT, $sql);
        $query->parameters(array(
                ':bot_id' => $bot_id,
                ':log_time' => $log_time,
                ':now' => $now,
        ));
        $results = $query->execute()->as_array();
        return $results;
    }

    public function _create_bot_cond_grp_only($bot_id, $col_name='bot_id')
    {
        if (strpos($bot_id, ',') !== false) {
            return " " . $col_name . " IN ($bot_id) ";
        } else {
            $bot_grp_id = $this->_model->get_grp_bot_id($bot_id);
            if ($bot_grp_id == 0) {
                $max_bot_id = intval($bot_id) + 1000;
                return ' ' . $col_name . '>=' . $bot_id . ' AND ' . $col_name . '<' . $max_bot_id;
            } else {
                return " $col_name = :bot_id ";
            }
        }
    }

    // record member_device_token: create or update　for VERY Native
    public function action_record_member_device_token() {
        $post = $this->request->post();
        $device_token = '';
        $member_id = '';
        $bot_id = '';
        if (isset($post['device_token'])) {
            $device_token = $post['device_token'];
        }
        if (isset($post['member_id'])) {
            $member_id = $post['member_id'];
        }
        if (isset($post['bot_id'])) {
            $bot_id = $post['bot_id'];
        }
        if ($device_token === '') {
            $response = [
                'ret' => false,
                'reason' => 'no device token'
            ];
            $this->response->body(json_encode($response, JSON_UNESCAPED_UNICODE));
            return;
        }
        if ($member_id === '') {
            $response = [
                'ret' => false,
                'reason' => 'no member id'
            ];
            $this->response->body(json_encode($response, JSON_UNESCAPED_UNICODE));
            return;
        }
        if ($bot_id === '') {
            $response = [
                'ret' => false,
                'reason' => 'no bot id'
            ];
            $this->response->body(json_encode($response, JSON_UNESCAPED_UNICODE));
            return;
        }
        try {
            $now = date('Y-m-d H:i:s',time());
            $memberDeviceOrm = ORM::factory('memberdevice', $device_token);
            if (is_null($memberDeviceOrm->member_id)) { //　新規
                $memberDeviceOrm->device_token = $device_token;
            } else { // 更新
                $memberDeviceOrm->update_time = $now;
            }
            $memberDeviceOrm->member_id = $member_id;
            $memberDeviceOrm->bot_id = $bot_id;
            $memberDeviceOrm->latest_active_time = $now;
            $memberDeviceOrm->save();
            $response = [
                'ret' => true
            ];
            $this->response->body(json_encode($response, JSON_UNESCAPED_UNICODE));
        } catch (\Throwable $th) {
            $response = [
                'ret' => false,
                'reason' => $th->getMessage()
            ];
            $this->response->body(json_encode($response, JSON_UNESCAPED_UNICODE));
        }
    }

    public function action_passkey_auth_start() {
		// Call the passkey authentication start API
		$api_url = 'http://************:9610/passkey/auth/start';
		$api_data = json_encode([]);
        // --- build full origin (scheme + host[:port]) ---
        $host = $_SERVER['HTTP_HOST'] ?? '';
        $scheme = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ? 'https' : 'http';
        $origin = $host ? $scheme . '://' . $host : '';
        $curl_headers = ['Content-Type: application/json'];
        if ($origin) $curl_headers = array_merge($curl_headers, ["Origin: $origin", "X-Frontend-Origin: $origin"]);

		$ch = curl_init();
		curl_setopt($ch, CURLOPT_URL, $api_url);
		curl_setopt($ch, CURLOPT_POST, 1);
		curl_setopt($ch, CURLOPT_POSTFIELDS, $api_data);
		curl_setopt($ch, CURLOPT_HTTPHEADER, $curl_headers);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($ch, CURLOPT_TIMEOUT, 30);

		$response = curl_exec($ch);
		$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
		curl_close($ch);

		if ($http_code === 200) {
			// Forward the successful response from the API
			$this->response->headers('Content-Type', 'application/json');
			$this->response->body($response);
		} else {
			// Handle error response
			$error_data = json_decode($response, true);
			$this->response->body(json_encode([
				'result' => 'error',
				'message' => $error_data['error'] ?? 'Failed to start passkey authentication'
			]));
		}
	}

    private function check_token($token) {
        if ($token != "") {
            session_id($token);
            $last_session = Session::instance();
            $session_id = session_id();

            $check_login = $this->is_login();
            if ($check_login == false) {
                $ret["ret"] = "false";
                $ret["reason"] = "not login";

                $data = json_encode($ret, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
                $this->response->body($data);
                return null;
            }
            return $last_session; 
        } else {
            $ret["ret"] = "false";
            $ret["reason"] = "token not exists.";
            $data = json_encode($ret, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
            $this->response->body($data);
            return null;
        }        
    }

    private function check_device_token($device_token, $user_id, $voip_token) {
        $sql = "SELECT d.device_token, d.user_id, d.delete_flg
                FROM t_user_device d
                WHERE d.device_token = :device_token
        ";
        $query = DB::query(Database::SELECT, $sql);
        $query->parameters(array(
            ':device_token' => $device_token,
        ));
        $select_results = $query->execute()->as_array();
        $count = count($select_results);
        if ($user_id == "undefined") {
            $user_id = NULL;
        }
        if ($count == 0) {
            // Insert when device token does not exist
            $sql = "INSERT INTO t_user_device (device_token, voip_token, user_id, update_time, create_time)
                    VALUES (:device_token, :voip_token, :user_id, now(), now())
                ";
            $query = DB::query(Database::INSERT, $sql);
            $query->parameters(array(
                ':device_token' => $device_token,
                ':user_id' => $user_id,
                ':voip_token' => $voip_token,
            ));
            $results = $query->execute();
            if ($voip_token != null) {
                // when app is uninstall and install again, device_token is changed but voip_token is not changed.
                // if the user uninstall the app and reinstall again, login in another user, the previous user's notification can not send to the user login now.
                $invalid_other_voip_record_sql = "UPDATE t_user_device
                    SET user_id = null
                    WHERE voip_token = :voip_token AND device_token != :device_token";
                $query = DB::query(Database::UPDATE, $invalid_other_voip_record_sql);
                $query->parameters(array(
                    ':device_token' => $device_token,
                    ':voip_token' => $voip_token,
                ));
                $query->execute();
            }
            if ($results != null) { // Insert row success
                $ret["ret"] = "true";
                $ret["data"]["device_token"] = $device_token;
                $ret["data"]["user_id"] = $user_id;
            } else {
                $ret["ret"] = "false";
                $ret["reason"] = "error inserting device_token.";
            }
            
            $data = json_encode($ret, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
            $this->response->body($data);
            return null;
        } else {
            // Update when device token exists
            $sql = "UPDATE t_user_device
                    SET voip_token = :voip_token, user_id = :user_id, update_time = now()
                    WHERE device_token = :device_token
                ";
            $query = DB::query(Database::UPDATE, $sql);
            $query->parameters(array(
                ':device_token' => $device_token,
                ':user_id' => $user_id,
                ':voip_token' => $voip_token,
            ));
            $results = $query->execute();
            if ($voip_token != null) {
                // when app is uninstall and install again, device_token is changed but voip_token is not changed.
                // if the user uninstall the app and reinstall again, login in another user, the previous user's notification can not send to the user login now.
                $invalid_other_voip_record_sql = "UPDATE t_user_device
                SET user_id = null
                WHERE voip_token = :voip_token AND device_token != :device_token";
                $query = DB::query(Database::UPDATE, $invalid_other_voip_record_sql);
                $query->parameters(array(
                    ':device_token' => $device_token,
                    ':voip_token' => $voip_token,
                ));
                $query->execute();
            }
            if ($results == 1) { // Update row success
                $ret["ret"] = "true";
                $ret["data"]["device_token"] = $device_token;
                $ret["data"]["user_id"] = $user_id;
            } else {
                $ret["ret"] = "false";
                $ret["reason"] = "error updating device_token.";
            }

            $data = json_encode($ret, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);

            $this->response->body($data);
            return null;
        }
    }

    private function get_bot_tpl_message($bot_id, $msg_cd, $lang_cd, $parse_json = false)
	{
		$msg = $this->get_bot_common_message($bot_id, $msg_cd, $lang_cd);
		//$msg = $this->get_bot_message($bot_id, $msg_cd, $lang_cd, 'tpl');
		if ($msg == NULL || count($msg) == 0) {
			if ($parse_json) {
				return [];
			}
			else {
				return '';
			}
		}
		if ($parse_json) {
			return json_decode($msg[0]['content'], true);
		}
		else {
			return $msg[0]['content'];
		}
	}

    private function get_bot_common_message($bot_id, $msg_cd, $lang_cd, $self = false)
	{
		if (trim($msg_cd) == '') return null;
		if ($self) {
			$bot_arr = [$bot_id];
		}
		else {
			$template_bot = $this->get_bot_setting($bot_id, 'template_bot');
			$grp_bot_id = $this->_model->get_grp_bot_id($bot_id);
			$bot_arr = [$bot_id];
			if ($template_bot != '') $bot_arr[] = $template_bot;
			if ($grp_bot_id > 0)  $bot_arr[] = $grp_bot_id;
			$bot_arr[] = 0;
		}
		foreach($bot_arr as $bot_id) {
			$msgs = ORM::factory('botmsg')->where('bot_id', '=', $bot_id)->where('msg_cd', '=', $msg_cd)->where('delete_flg', '=', 0)->find_all();
			if (count($msgs) > 0) break;
		}
		if (count($msgs) == 0) {
			return null;
		}
		$msg_type_cd = $msgs[0]->msg_type_cd;
		if ($msg_type_cd == 'mnu' || $msg_type_cd == 'btn' || $msg_type_cd == 'rcm') $msg_type_cd = 'lst';
		if ($msg_type_cd == 'mal') $msg_type_cd = 'tpl';
		if ($msg_type_cd == 'mov') $msg_type_cd = 'img';
		$sql = "SELECT a.msg_cd, a.msg_type_cd, a.msg_data, a.start_date, a.end_date, b.*
		FROM t_bot_msg a LEFT JOIN t_bot_msg_desc_$msg_type_cd b
		ON a.msg_id = b.msg_id WHERE a.msg_id = :msg_id";
		if ($lang_cd != NULL) $sql = $sql . " AND b.lang_cd = :lang_cd";
		$sql = $sql . " ORDER BY b.no";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':msg_id' => $msgs[0]->msg_id,
				':lang_cd' => $lang_cd,
		));
		$results = $query->execute()->as_array();
		return $results;
	}

    private function get_bot_setting($bot_id, $setting_cd, $parse_json = false)
	{
		$bot_arr = $this->get_bot_array($bot_id);
		foreach($bot_arr as $bot_id) {
			$sql = "SELECT setting_cd, setting_value FROM t_bot_setting WHERE bot_id = :bot_id AND setting_cd = :setting_cd";
			$query = DB::query(Database::SELECT, $sql);
			$query->parameters(array(
				':bot_id' => $bot_id,
				':setting_cd' => $setting_cd,
			));
			$results = $query->execute()->as_array();
			if (count($results) > 0) {
				if ($parse_json) {
					return json_decode($results[0]['setting_value'], true);
				}
				else {
					return $results[0]['setting_value'];
				}
			}
			else {
				if ($setting_cd == 'template_bot') return '';
			}
		}
		return '';
	}

    private function get_bot_array($bot_id, $desc='DESC') {
		$bot_arr = [$bot_id];
		$template_bot = $this->get_bot_setting_self($bot_id, 'template_bot');
		if ($template_bot != '') $bot_arr[] = $template_bot;
		$grp_bot_id = $this->_model->get_grp_bot_id($bot_id);
		if ($grp_bot_id > 0) {
			$bot_arr[] = $grp_bot_id;
		}
		$bot_arr[] = 0;
		if ($desc == 'DESC') {
			return $bot_arr;
		}
		else {
			return array_reverse($bot_arr);
		}
	}

    private function get_bot_setting_self($bot_id, $setting_cd, $parse_json = false)
	{
		$sql = "SELECT setting_cd, setting_value
				 FROM t_bot_setting WHERE bot_id = :bot_id
		 			AND setting_cd = :setting_cd";
		$query = DB::query(Database::SELECT, $sql);
		$query->parameters(array(
				':bot_id' => $bot_id,
				':setting_cd' => $setting_cd,
		));
		$results = $query->execute()->as_array();
		if (count($results) > 0) {
			if ($parse_json) {
				return json_decode($results[0]['setting_value'], true);
			}
			else {
				return $results[0]['setting_value'];
			}
		}
		return '';
	}
}
