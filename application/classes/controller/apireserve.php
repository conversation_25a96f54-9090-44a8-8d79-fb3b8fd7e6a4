<?php defined('SYSPATH') or die('No direct script access.');
class Controller_Apireserve extends Controller
{
    const API_RESULT_OK = '0';
    const API_RESULT_ERR = '1';
    const API_RESULT_WARN = '2';

    private function check_accesstoken($token, $nid)
    {
        if (true) {
            if ($nid == "honban") {
                Database::$default = 'honban';
            } else {
                Database::$default = 'default';
            }
            return 1;
        }
        return 0;
    }

    private function jresult($result_cd, $reason_cd = null, $data = null)
    {
        $result_array = array('result'=>$result_cd, 'reason'=>$reason_cd, 'data'=>$data);
        return json_encode($result_array);
    }
    
    public function action_api_reserve()
    {
        $this->response->headers('Access-Control-Allow-Origin', '*');
        $condition = $this->request->post();
        if ($condition) {
            /*
            if ($this->check_accesstoken("***", $condition["nid"]) == 0) {
                return $this->response->body($this->jresult("01"));
            }
            */

            $api_get_source = $condition['api_get_source'];
            $data = array();
            if ($api_get_source == 'get_all_room') {
                $data = $this->get_all_room($condition);
            } elseif ($api_get_source == 'get_all_plan') {
                $data = $this->get_all_plan($condition);
            } elseif ($api_get_source == 'get_all_room_plan') {
                $data = $this->get_all_room_plan($condition);
            } elseif ($api_get_source == 'get_room_plan_available') {
                $data = $this->get_room_plan_available($condition);
            } elseif ($api_get_source == 'create_talkappi_reserve_id') {
                $data = $this->create_talkappi_reserve_id($condition);
            } elseif ($api_get_source == 'unlock_lock_room') {
                $data = $this->unlock_lock_room($condition);
            } elseif ($api_get_source == 'register_reserve_order') {
                $data = $this->register_reserve_order($condition);
            } elseif ($api_get_source == 'update_link_data_for_reserve_order') {
                $data = $this->update_link_data_for_reserve_order($condition);
            } elseif ($api_get_source == 'get_latest_reserve_order') {
                $data = $this->get_latest_reserve_order($condition);


            // 下面是tema相关
            } elseif ($api_get_source == 'get_user') {
                $data = $this->get_user($condition);
            } elseif ($api_get_source == 'get_user_by_hotel_code') {
                $data = $this->get_user_by_hotel_code($condition);
            } elseif ($api_get_source == 'get_room_available') {
                $data = $this->get_room_available($condition);
            } elseif ($api_get_source == 'get_plan_rate') {
                $data = $this->get_plan_rate($condition);
            } elseif ($api_get_source == 'regist_room_available') {
                $data = $this->regist_room_available($condition);
            } elseif ($api_get_source == 'regist_plan_rate') {
                $data = $this->regist_plan_rate($condition);
            } elseif ($api_get_source == 'get_reserve_info') {
                $data = $this->get_reserve_info($condition);
            } else {
                error_log("action_api_reserve.php miss api_get_source..." . $api_get_source);
            }
            $this->response->body($this->jresult("00", null, $data));
        } else {
            $this->response->body($this->jresult("01"));
        }
        return;
    }

    private function get_all_room($condition)
    {
        $hotel_code = $condition["hotel_code"];
        $room_type_code = "";
        $status = "";
        if (array_key_exists("room_type_code", $condition)) {
            $room_type_code = $condition["room_type_code"];
        }
        if ($room_type_code == "") {
            $sql_room_type_code = '';
        } else {
            $sql_room_type_code = $this->create_or_sql($room_type_code, "a.room_type_code");
        }
        if (array_key_exists("status", $condition)) {
            $status = $condition["status"];
        }
        if ($status == "") {
            $sql_status = '';
        } else {
            $sql_status = "
				AND a.status = :status
			";
        }

        $sql = "SELECT 
					a.*
				FROM t_reserve_room a
				WHERE
					1 = 1
                    AND a.hotel_code = :hotel_code
                    $sql_room_type_code
                    $sql_status
			";
        $query = DB::query(Database::SELECT, $sql);
        $query->parameters(array(
            ':hotel_code' => $hotel_code,
            ':status' => $status,
            ':room_type_code' => $room_type_code,
        ));
        $results = $query->execute()->as_array();
                    
        return $results;
    }

    private function get_all_plan($condition)
    {
        $hotel_code = $condition["hotel_code"];
        $rate_plan_code = "";
        if (array_key_exists("rate_plan_code", $condition)) {
            $rate_plan_code = $condition["rate_plan_code"];
        }
        if ($rate_plan_code == "") {
            $sql_rate_plan_code = '';
        } else {
            $sql_rate_plan_code = $this->create_or_sql($rate_plan_code, "a.rate_plan_code");
        }

        $sql = "SELECT 
					a.*
				FROM t_reserve_plan a
				WHERE
					1 = 1
                    AND a.hotel_code = :hotel_code
                    $sql_rate_plan_code
			";
        
        $query = DB::query(Database::SELECT, $sql);
        $query->parameters(array(
            ':hotel_code' => $hotel_code,
            ':rate_plan_code' => $rate_plan_code,
                    ));
        $results = $query->execute()->as_array();
                    
        return $results;
    }

    private function get_all_room_plan($condition)
    {
        $hotel_code = $condition["hotel_code"];
        $search_date_from = $condition["search_date_from"];
        $search_date_to = $condition["search_date_to"];
        $room_type_code = "";
        $room_status = "";
        $plan_status = "";
        if (array_key_exists("room_type_code", $condition)) {
            $room_type_code = $condition["room_type_code"];
        }
        $rate_plan_code = "";
        if (array_key_exists("rate_plan_code", $condition)) {
            $rate_plan_code = $condition["rate_plan_code"];
        }
        if (array_key_exists("room_status", $condition)) {
            $room_status = $condition["room_status"];
        }
        if (array_key_exists("plan_status", $condition)) {
            $plan_status = $condition["plan_status"];
        }

        if ($room_type_code == "") {
            $sql_room_type_code = '';
        } else {
            $sql_room_type_code = $this->create_or_sql($room_type_code, "a.room_type_code");
        }
        if ($rate_plan_code == "") {
            $sql_rate_plan_code = '';
        } else {
            $sql_rate_plan_code = $this->create_or_sql($rate_plan_code, "d.rate_plan_code");
        }
        if ($room_status == "") {
            $sql_room_status = '';
        } else {
            $sql_room_status = "
				AND a.status = :room_status
			";
        }
        if ($plan_status == "") {
            $sql_plan_status = '';
        } else {
            $sql_plan_status = "
                AND d.status = :plan_status
			";
        }

        // 读取贩卖中(房型，plan)
        $sql = "SELECT 
					a.room_type_code,
					a.capacity_from,
					a.capacity_to,

					d.rate_plan_code,

					e.child_fee,
					e.fee_from,
					e.fee_to

				FROM t_reserve_room a
				INNER JOIN t_reserve_room_plan e
					ON a.room_type_code = e.room_type_code
                    AND a.hotel_code = e.hotel_code
				INNER JOIN t_reserve_plan d
					ON e.rate_plan_code = d.rate_plan_code
                    AND a.hotel_code = d.hotel_code
				
				WHERE
					1 = 1
                    AND a.hotel_code = :hotel_code
					$sql_room_status
					$sql_plan_status
					AND (
                            (d.offer_from = '' AND d.offer_to = '')
                          OR 
                            (d.offer_from != '' AND d.offer_to = '' AND :search_date_to >= d.offer_from)
                          OR
                            (d.offer_from = '' AND d.offer_to != '' AND :search_date_from <= d.offer_to)
                          OR 
                            (d.offer_from != '' AND d.offer_to != '' AND
                                (
                                    (:search_date_from >= d.offer_from AND :search_date_from <= d.offer_to) 
                                    OR (:search_date_to >= d.offer_from AND :search_date_to <= d.offer_to)
                                    OR (:search_date_from < d.offer_from AND :search_date_to > d.offer_to)
                                )
                            )
                        )
					$sql_room_type_code
					$sql_rate_plan_code
				ORDER BY
					a.room_type_code,
					d.rate_plan_code
			";
        $query = DB::query(Database::SELECT, $sql);
        $query->parameters(array(
                        ':hotel_code' => $hotel_code,
                        ':search_date_from' => $search_date_from,
                        ':search_date_to' => $search_date_to,
                        ':room_type_code' => $room_type_code,
                        ':rate_plan_code' => $rate_plan_code,
                        ':room_status' => $room_status,
                        ':plan_status' => $plan_status,
                    ));
        $results = $query->execute()->as_array();
                    
        return $results;
    }

    private function get_room_plan_available($condition)
    {
        $hotel_code = $condition["hotel_code"];
        $search_date_from = $condition["search_date_from"];
        $search_date_to = $condition["search_date_to"];
        $room_plan_available_string = $condition["room_plan_available_string"];
        $search_quantity = $condition["search_quantity"];

        // 读取贩卖中(房型，plan) and 有库存(房型)，并定义了价钱的(房型，plan),
        $sql = "SELECT 
					a.room_type_code,
					a.capacity_from,
					a.capacity_to,

					b.ymd,
					b.available_num,
					b.reserved_num,

					d.rate_plan_code,

					e.child_fee,
					e.fee_from,
					e.fee_to,

					c.fee


				FROM t_reserve_room a
				INNER JOIN t_reserve_room_plan e
					ON a.room_type_code = e.room_type_code
                    AND a.hotel_code = e.hotel_code
				INNER JOIN t_reserve_plan d
					ON e.rate_plan_code = d.rate_plan_code
                    AND a.hotel_code = d.hotel_code
				
				INNER JOIN t_reserve_room_available b
					ON a.room_type_code = b.room_type_code
                    AND a.hotel_code = b.hotel_code
                    AND b.status = 0

				INNER JOIN t_reserve_plan_rate c
					ON a.room_type_code = c.room_type_code
					AND d.rate_plan_code = c.rate_plan_code
					AND b.ymd = c.ymd
                    AND a.hotel_code = c.hotel_code
                    AND c.plan_status = 0
				WHERE
					1 = 1
                    AND a.hotel_code = :hotel_code
					AND a.status = 0
					AND d.status = 0
					AND (
                            (d.offer_from = '' AND d.offer_to = '')
                          OR 
                            (d.offer_from != '' AND d.offer_to = '' AND :search_date_to >= d.offer_from)
                          OR
                            (d.offer_from = '' AND d.offer_to != '' AND :search_date_from <= d.offer_to)
                          OR 
                            (d.offer_from != '' AND d.offer_to != '' AND
                                (
                                    (:search_date_from >= d.offer_from AND :search_date_from <= d.offer_to) 
                                    OR (:search_date_to >= d.offer_from AND :search_date_to <= d.offer_to)
                                    OR (:search_date_from < d.offer_from AND :search_date_to > d.offer_to)
                                )
                            )
                        )
					AND b.ymd >= :search_date_from 
					AND b.ymd <= :search_date_to
					AND (
                            (d.offer_from = '' AND d.offer_to = '')
                          OR 
                            (d.offer_from != '' AND d.offer_to = ''  AND b.ymd >= d.offer_from)
                          OR
                            (d.offer_from = '' AND d.offer_to != '' AND b.ymd <= d.offer_to)
                          OR
                            (d.offer_from != '' AND d.offer_to != '' AND b.ymd >= d.offer_from AND b.ymd <= d.offer_to)
                        )

					AND b.available_num >= :search_quantity
					AND concat(a.room_type_code, '_', d.rate_plan_code) in ($room_plan_available_string)

				ORDER BY
					a.room_type_code,
					d.rate_plan_code,
					b.ymd
			";
        $query = DB::query(Database::SELECT, $sql);
        $query->parameters(array(
                        ':hotel_code' => $hotel_code,
                        ':search_date_from' => $search_date_from,
                        ':search_date_to' => $search_date_to,
                        ':room_plan_available_string' => $room_plan_available_string,
                        ':search_quantity' => $search_quantity,
                    ));
        $results = $query->execute()->as_array();
                    
        return $results;
    }

    private function create_talkappi_reserve_id()
    {
        // 生成字母和数字组成的6位字符串
        $str = range('A', 'Z');
        // 去除大写的O，以防止与0混淆 
        unset($str[array_search('O', $str)]);
        $arr = array_merge(range(0, 9), $str);
        shuffle($arr);
        $invitecode = '';
        $arr_len = count($arr);

        for ($i = 0; $i < 10; $i++) {
            $rand = mt_rand(0, $arr_len - 1);
            $invitecode .= $arr[$rand];
        }
        return "TR$invitecode";
    }
    
    private function unlock_lock_room($condition)
    {
        // 开启事务
        try {
            $hotel_code = $condition["hotel_code"];
            Database::instance()->begin();

            $results = array();
            //error_log("lock_data=" . $condition["lock_data"]);
            // 首先unlock
            if (array_key_exists("unlock_data", $condition)) {
                $unlock_data = $condition["unlock_data"];
                $unlock_data_count = count($unlock_data);
                error_log("unlock_data_count = $unlock_data_count");
                for ($i = 0; $i < $unlock_data_count; $i++) {
                    $room_type_code = $unlock_data[$i]["room_type_code"];
                    $ymd = $unlock_data[$i]["ymd"];
                    $quantity = $unlock_data[$i]["quantity"];
                    error_log("unlock_data_$i  room_type_code=$room_type_code ymd=$ymd quantity=$quantity");
                    $this->unlock_room($hotel_code, $room_type_code, $ymd, $quantity);
                }
            }

            // 然后lock
            if (array_key_exists("lock_data", $condition)) {
                $lock_data = $condition["lock_data"];
                $lock_data_count = count($lock_data);
                error_log("lock_data_count = $lock_data_count");
                for ($i = 0; $i < $lock_data_count; $i++) {
                    $room_type_code = $lock_data[$i]["room_type_code"];
                    $ymd = $lock_data[$i]["ymd"];
                    $quantity = $lock_data[$i]["quantity"];
                    error_log("lock_data_$i  room_type_code=$room_type_code ymd=$ymd quantity=$quantity");
                    $this->lock_room($hotel_code, $room_type_code, $ymd, $quantity);
                }

                // 最后检查是否lock的结果是否还有库存(>=0就行，因为之前的lock可能正好让库存为0)
                for ($i = 0; $i < count($lock_data); $i++) {
                    $room_type_code = $lock_data[$i]["room_type_code"];
                    $ymd = $lock_data[$i]["ymd"];
                    $check_ret = $this->check_room($hotel_code, $room_type_code, $ymd);
                    if ($check_ret && count($check_ret) == 1 && $check_ret[0]["available_num"] >= 0) {
                        $available_num = $check_ret[0]["available_num"];
                        $reserved_num = $check_ret[0]["reserved_num"];
                        error_log("room_type_code=$room_type_code ymd=$ymd 还空有$available_num 间房 已经卖了$reserved_num 间");
                    // 有库存
                    } else {
                        // 没库存,就回滚修改
                        error_log("room_type_code=$room_type_code 没有库存了");
                        $results['result'] = 0; // 0表示失败
                        Database::instance()->rollback();
                        return $results;
                    }
                }
            }
            $results['result'] = 1; // 1表示成功
                    
            Database::instance()->commit();
            return $results;
        } catch (Exception $e) {
            Database::instance()->rollback();
            error_log($e->getMessage());
            $results = array();
            $results['result'] = -1;    // -1表示出了例外
            return $results;
        }
    }

    private function lock_room($hotel_code, $room_type_code, $ymd, $quantity)
    {
        $sql = "UPDATE t_reserve_room_available
				SET
                    available_num = available_num - :quantity,
                    reserved_num = reserved_num + :quantity
				WHERE
                    hotel_code = :hotel_code
                    AND room_type_code = :room_type_code
					AND ymd = :ymd
				";
        $query = DB::query(Database::UPDATE, $sql);
        $query->parameters(array(
            ':hotel_code' => $hotel_code,
            ':room_type_code' => $room_type_code,
            ':ymd' => $ymd,
            ':quantity' => $quantity,
        ));

        $results = $query->execute();
        return $results;
    }

    private function unlock_room($hotel_code, $room_type_code, $ymd, $quantity)
    {
        $sql = "UPDATE t_reserve_room_available
				SET
                    available_num = available_num + :quantity,
                    reserved_num = reserved_num - :quantity
				WHERE
                    hotel_code = :hotel_code
                    AND room_type_code = :room_type_code
					AND ymd = :ymd
				";
        $query = DB::query(Database::UPDATE, $sql);
        $query->parameters(array(
            ':hotel_code' => $hotel_code,
            ':room_type_code' => $room_type_code,
            ':ymd' => $ymd,
            ':quantity' => $quantity,
        ));

        $results = $query->execute();
        return $results;
    }

    private function check_room($hotel_code, $room_type_code, $ymd)
    {
        $sql = "SELECT 
                    available_num,
                    reserved_num
                FROM
                    t_reserve_room_available
				WHERE
                    hotel_code = :hotel_code
                    AND room_type_code = :room_type_code
					AND ymd = :ymd
				";
        $query = DB::query(Database::SELECT, $sql);
        $query->parameters(array(
            ':hotel_code' => $hotel_code,
            ':room_type_code' => $room_type_code,
            ':ymd' => $ymd,
        ));

        $results = $query->execute();
        return $results->as_array();
    }

    private function register_reserve_order($condition)
    {
        $hotel_code = $condition["hotel_code"];
        $talkappi_reserve_id = $condition["talkappi_reserve_id"];
        $agent_notification_number = $condition["agent_notification_number"];
        $type = $condition["type"];
        $user_condition = $condition["user_condition"];
        $return_data = $condition["return_data"];
        $db_rooms = $condition["db_rooms"];
        $db_plans = $condition["db_plans"];
        $effective_date = $condition["effective_date"];
        $expire_date = $condition["expire_date"];
        $user_id = $condition["user_id"];

        $sql = "INSERT INTO t_reserve_order
				(hotel_code, talkappi_reserve_id, agent_notification_number, type, user_condition, return_data, db_rooms, db_plans, effective_date, expire_date, create_user)
				VALUES
				(:hotel_code, :talkappi_reserve_id, :agent_notification_number, :type, :user_condition, :return_data, :db_rooms, :db_plans, :effective_date, :expire_date, :user_id)
				";
        $query = DB::query(Database::UPDATE, $sql);
        $query->parameters(array(
            ':hotel_code' => $hotel_code,
            ':talkappi_reserve_id' => $talkappi_reserve_id,
            ':agent_notification_number' => $agent_notification_number,
            ':type' => $type,
            ':user_condition' => $user_condition,
            ':return_data' => $return_data,
            ':db_rooms' => $db_rooms,
            ':db_plans' => $db_plans,
            ':effective_date' => $effective_date,
            ':expire_date' => $expire_date,
            ':user_id' => $user_id,
        ));

        $query->execute();
        $results = 1;
        return $results;
    }

    private function update_link_data_for_reserve_order($condition)
    {
        $hotel_code = $condition["hotel_code"];
        $talkappi_reserve_id = $condition["talkappi_reserve_id"];
        $agent_notification_number = $condition["agent_notification_number"];
        $link_data = $condition["link_data"];
        $user_id = $condition["user_id"];

        $sql = "UPDATE t_reserve_order
				SET
                    link_data = :link_data,
                    update_user = :user_id
				WHERE
                    hotel_code = :hotel_code
                    AND talkappi_reserve_id = :talkappi_reserve_id
					AND agent_notification_number = :agent_notification_number
				";
        $query = DB::query(Database::UPDATE, $sql);
        $query->parameters(array(
            ':hotel_code' => $hotel_code,
            ':talkappi_reserve_id' => $talkappi_reserve_id,
            ':agent_notification_number' => $agent_notification_number,
            ':link_data' => $link_data,
            ':user_id' => $user_id,
        ));

        $results = $query->execute();
        return $results;
    }

    private function get_latest_reserve_order($condition)
    {
        $hotel_code = $condition["hotel_code"];
        $talkappi_reserve_id = $condition["talkappi_reserve_id"];
        $sql = "SELECT 
                    *
                FROM
                    t_reserve_order
				WHERE
                    hotel_code = :hotel_code
                    AND talkappi_reserve_id = :talkappi_reserve_id
                ORDER BY 
                    create_datetime DESC
				";
        $query = DB::query(Database::SELECT, $sql);
        $query->parameters(array(
            ':talkappi_reserve_id' => $talkappi_reserve_id,
            ':hotel_code' => $hotel_code,
        ));

        $results = $query->execute();
        return $results->as_array();
    }


    // 下面是tema相关
    private function get_user($condition)
    {
        $type = $condition["type"];
        $user_id = $condition["user_id"];
        $password = $condition["password"];

        $hotel_code = "";
        if (array_key_exists("hotel_code", $condition)) {
            $hotel_code = $condition["hotel_code"];
        }
        if ($hotel_code == "") {
            $sql_hotel_code_code = '';
        } else {
            $sql_hotel_code_code = "
				AND hotel_code = :hotel_code
			";
        }

        $sql = "SELECT 
                    type,user_id,hotel_code
                FROM
                    t_reserve_user
				WHERE
                    type = :type
                    AND user_id = :user_id
                    AND password = :password
                    $sql_hotel_code_code
				";
        $query = DB::query(Database::SELECT, $sql);
        $query->parameters(array(
            ':type' => $type,
            ':user_id' => $user_id,
            ':password' => $password,
            ':hotel_code' => $hotel_code,
        ));

        $results = $query->execute();
        return $results->as_array();
    }

    private function get_user_by_hotel_code($condition)
    {
        $type = $condition["type"];
        $hotel_code = $condition["hotel_code"];
        $sql = "SELECT 
                    type,user_id,data
                FROM
                    t_reserve_user
				WHERE
                    type = :type
                    AND hotel_code = :hotel_code
				";
        $query = DB::query(Database::SELECT, $sql);
        $query->parameters(array(
            ':type' => $type,
            ':hotel_code' => $hotel_code,
        ));

        $results = $query->execute();
        return $results->as_array();
    }

    private function get_room_available($condition)
    {
        $hotel_code = $condition["hotel_code"];
        $room_type_code = $condition["room_type_code"];
        $ymd_from = $condition["ymd_from"];
        $ymd_to = $condition["ymd_to"];

        $sql = "SELECT
                    ymd,
                    status,
                    available_num,
                    reserved_num
                FROM
                    t_reserve_room_available
				WHERE
                    hotel_code = :hotel_code
                    AND room_type_code = :room_type_code
					AND ymd >= :ymd_from
					AND ymd <= :ymd_to
				";
        $query = DB::query(Database::SELECT, $sql);
        $query->parameters(array(
            ':hotel_code' => $hotel_code,
            ':room_type_code' => $room_type_code,
            ':ymd_from' => $ymd_from,
            ':ymd_to' => $ymd_to,
        ));

        $results = $query->execute();
        return $results->as_array();
    }

    private function create_or_sql($origin_code, $field_name)
    {
        $array_items = explode(",", $origin_code);
        for ($i=0; $i<count($array_items); $i++) {
            $cur_item = $array_items[$i];
            $cur_sql =  "$field_name = '$cur_item'";
            if ($i == 0) {
                $sql_temp = $cur_sql;
            } else {
                $sql_temp = "$sql_temp 
                OR $cur_sql";
            }
        }

        $sql_ret = "
            AND ($sql_temp)
        ";
        return $sql_ret;
    }

    private function get_plan_rate($condition)
    {
        $hotel_code = $condition["hotel_code"];
        $ymd_from = $condition["ymd_from"];
        $ymd_to = $condition["ymd_to"];
        $room_type_code = "";
        if (array_key_exists("room_type_code", $condition)) {
            $room_type_code = $condition["room_type_code"];
        }
        $rate_plan_code = "";
        if (array_key_exists("rate_plan_code", $condition)) {
            $rate_plan_code = $condition["rate_plan_code"];
        }

        if ($room_type_code == "") {
            $sql_room_type_code = '';
        } else {
            $sql_room_type_code = $this->create_or_sql($room_type_code, "a.room_type_code");
        }
        if ($rate_plan_code == "") {
            $sql_rate_plan_code = '';
        } else {
            $sql_rate_plan_code = $this->create_or_sql($rate_plan_code, "a.rate_plan_code");
        }

        $sql = "SELECT
                    ymd,
                    plan_status,
                    fee
                FROM
                    t_reserve_plan_rate a
				WHERE
                    hotel_code = :hotel_code
					AND ymd >= :ymd_from
					AND ymd <= :ymd_to
                    $sql_room_type_code
                    $sql_rate_plan_code
                ORDER BY
                    room_type_code,
                    rate_plan_code
				";
        $query = DB::query(Database::SELECT, $sql);
        $query->parameters(array(
            ':hotel_code' => $hotel_code,
            ':ymd_from' => $ymd_from,
            ':ymd_to' => $ymd_to,
        ));

        $results = $query->execute();
        return $results->as_array();
    }

    private function regist_room_available($condition)
    {
        $hotel_code = $condition["hotel_code"];
        $room_type_code = $condition["room_type_code"];
        $ymd = $condition["ymd"];
        $available_num = $condition["available"];
        $user_id = $condition["user_id"];

        $sql = "SELECT
                    status,
                    available_num,
                    reserved_num
                FROM
                    t_reserve_room_available
				WHERE
                    hotel_code = :hotel_code
                    AND room_type_code = :room_type_code
					AND ymd = :ymd
				";
        $query = DB::query(Database::SELECT, $sql);
        $query->parameters(array(
            ':hotel_code' => $hotel_code,
            ':room_type_code' => $room_type_code,
            ':ymd' => $ymd,
        ));

        $results = $query->execute()->as_array();

        if (count($results) > 0) {
            if ($available_num == "-999") {
                // update
                $sql = "UPDATE 
                            t_reserve_room_available
                        SET
                            status = 1,
                            update_user = :user_id
                        WHERE
                            hotel_code = :hotel_code
                            AND room_type_code = :room_type_code
                            AND ymd = :ymd
                        ";
                $query = DB::query(Database::UPDATE, $sql);
                $query->parameters(array(
                    ':hotel_code' => $hotel_code,
                    ':room_type_code' => $room_type_code,
                    ':ymd' => $ymd,
                    ':user_id' => $user_id,
                ));
                $ret = $query->execute();
            } else {
                // update
                $sql = "UPDATE
                            t_reserve_room_available
                        SET
                            status = 0,
                            available_num = :available_num,
                            update_user = :user_id
                        WHERE
                            hotel_code = :hotel_code
                            AND room_type_code = :room_type_code
                            AND ymd = :ymd
                        ";
                $query = DB::query(Database::UPDATE, $sql);
                $query->parameters(array(
                    ':hotel_code' => $hotel_code,
                    ':room_type_code' => $room_type_code,
                    ':ymd' => $ymd,
                    ':available_num' => $available_num,
                    ':user_id' => $user_id,
                ));
                $ret = $query->execute();
            }
        } else {
            // insert
            if ($available_num == "-999") {
                $sql = "INSERT INTO t_reserve_room_available
                                    (hotel_code, room_type_code, ymd, status, available_num, reserved_num, create_user)
                                    VALUES
                                    (:hotel_code, :room_type_code, :ymd, :status, 0, 0, :user_id)
                                    ";
                $query = DB::query(Database::UPDATE, $sql);
                $query->parameters(array(
                    ':hotel_code' => $hotel_code,
                    ':room_type_code' => $room_type_code,
                    ':ymd' => $ymd,
                    ':status' => "1",
                    ':user_id' => $user_id,
                ));
                $ret = $query->execute();
            } else {
                $sql = "INSERT INTO t_reserve_room_available
                                    (hotel_code, room_type_code, ymd, status, available_num, reserved_num, create_user)
                                    VALUES
                                    (:hotel_code, :room_type_code, :ymd, :status, :available_num, 0, :user_id)
                                    ";
                $query = DB::query(Database::UPDATE, $sql);
                $query->parameters(array(
                    ':hotel_code' => $hotel_code,
                    ':room_type_code' => $room_type_code,
                    ':ymd' => $ymd,
                    ':status' => "0",
                    ':available_num' => $available_num,
                    ':user_id' => $user_id,
                ));
                $ret = $query->execute();
            }
        }
    }

    private function regist_plan_rate($condition)
    {
        $hotel_code = $condition["hotel_code"];
        $room_type_code = $condition["room_type_code"];
        $rate_plan_code = $condition["rate_plan_code"];
        $ymd = $condition["ymd"];
        $fee = $condition["fee"];
        $user_id = $condition["user_id"];

        $sql = "SELECT
                    plan_status
                FROM
                    t_reserve_plan_rate
                WHERE
                    hotel_code = :hotel_code
                    AND room_type_code = :room_type_code
                    AND rate_plan_code = :rate_plan_code
                    AND ymd = :ymd
				";
        $query = DB::query(Database::SELECT, $sql);
        $query->parameters(array(
            ':hotel_code' => $hotel_code,
            ':room_type_code' => $room_type_code,
            ':rate_plan_code' => $rate_plan_code,
            ':ymd' => $ymd,
        ));

        $results = $query->execute()->as_array();

        if (count($results) > 0) {
            $sql = "UPDATE t_reserve_plan_rate
            SET
                fee = :fee,
                update_user = :user_id
            WHERE
                hotel_code = :hotel_code
                AND room_type_code = :room_type_code
                AND rate_plan_code = :rate_plan_code
                AND ymd = :ymd
            ";
            $query = DB::query(Database::UPDATE, $sql);
            $query->parameters(array(
                ':hotel_code' => $hotel_code,
                ':room_type_code' => $room_type_code,
                ':rate_plan_code' => $rate_plan_code,
                ':ymd' => $ymd,
                ':fee' => $fee,
                ':user_id' => $user_id,
            ));
    
            $ret = $query->execute();
        } else {
            $sql = "INSERT INTO t_reserve_plan_rate
            (hotel_code, room_type_code, rate_plan_code, ymd, plan_status, fee, create_user)
            VALUES
            (:hotel_code, :room_type_code, :rate_plan_code, :ymd, :plan_status, :fee, :user_id)
            ";
            $query = DB::query(Database::UPDATE, $sql);
            $query->parameters(array(
                ':hotel_code' => $hotel_code,
                ':room_type_code' => $room_type_code,
                ':rate_plan_code' => $rate_plan_code,
                ':ymd' => $ymd,
                ':fee' => $fee,
                ':plan_status' => "0",
                ':user_id' => $user_id,
            ));

            $ret = $query->execute();
        }
    }

    private function get_reserve_info($condition)
    {
        $hotel_code = $condition["hotel_code"];
        $talkappi_reserve_id = "";
        if (array_key_exists("talkappi_reserve_id", $condition)) {
            $talkappi_reserve_id = $condition["talkappi_reserve_id"];
        }

        if ($talkappi_reserve_id != "") {
            error_log("get_reserve_info() try talkappi_reserve_id");
            $sql = "SELECT
                        *
                    FROM
                        t_reserve_order
                    WHERE
                        hotel_code = :hotel_code
                        AND talkappi_reserve_id = :talkappi_reserve_id
                    ORDER BY
                        create_datetime ASC,
                        talkappi_reserve_id
                    ";
            $query = DB::query(Database::SELECT, $sql);
            $query->parameters(array(
                ':hotel_code' => $hotel_code,
                ':talkappi_reserve_id' => $talkappi_reserve_id,
            ));

            $results = $query->execute();
            return $results->as_array();
        }


        $day_from = "";
        $day_to = "";
        if (array_key_exists("day_from", $condition)) {
            $day_from = $condition["day_from"];
        }
        if (array_key_exists("day_to", $condition)) {
            $day_to = $condition["day_to"];
        }
        if ($day_from != "" || $day_to != "") {
            error_log("get_reserve_info() try day from to");
            $sql = "SELECT
                        *
                    FROM
                        t_reserve_order a
                    WHERE
                        a.talkappi_reserve_id in (
                            SELECT 
                                talkappi_reserve_id 
                            FROM
                                t_reserve_order a
                            WHERE
                                hotel_code = :hotel_code
                                AND (
                                        (:day_from = '' AND :day_to = '')
                                    OR 
                                        (:day_from != '' AND :day_to = '' AND expire_date >= :day_from)
                                    OR
                                        (:day_from = '' AND :day_to != '' AND effective_date <= :day_to)
                                    OR 
                                        (:day_from != '' AND :day_to != '' AND
                                            (
                                                (effective_date >= :day_from AND effective_date <= :day_to) 
                                                OR (expire_date >= :day_from AND expire_date <= :day_to)
                                                OR (effective_date < :day_from AND expire_date > :day_to)
                                            )
                                        )
                                    )
                        )
                    ORDER BY
                        create_datetime ASC,
                        a.talkappi_reserve_id
                    ";
            $query = DB::query(Database::SELECT, $sql);
            $query->parameters(array(
                ':hotel_code' => $hotel_code,
                ':day_from' => $day_from,
                ':day_to' => $day_to,
            ));

            $results = $query->execute();
            return $results->as_array();
        }

        $create_day_from = "";
        $create_day_to = "";
        if (array_key_exists("create_day_from", $condition)) {
            $create_day_from = $condition["create_day_from"];
        }
        if (array_key_exists("create_day_to", $condition)) {
            $create_day_to = $condition["create_day_to"];
        }
        if ($create_day_from != "" || $create_day_to != "") {
            error_log("get_reserve_info() try create_day from to");
            $sql = "SELECT
                        *
                    FROM
                        t_reserve_order a
                    WHERE
                        a.talkappi_reserve_id in (
                            SELECT 
                                talkappi_reserve_id 
                            FROM
                                t_reserve_order a
                            WHERE
                                hotel_code = :hotel_code
                                AND (:create_day_from = '' OR date_format(create_datetime,'%Y/%m/%d') >= :create_day_from)
                                AND (:create_day_to = '' OR date_format(create_datetime,'%Y/%m/%d') <= :create_day_to)
                        )
                    ORDER BY
                        create_datetime ASC,
                        a.talkappi_reserve_id
                    ";
            $query = DB::query(Database::SELECT, $sql);
            $query->parameters(array(
                ':hotel_code' => $hotel_code,
                ':create_day_from' => $create_day_from,
                ':create_day_to' => $create_day_to,
            ));

            $results = $query->execute();
            return $results->as_array();
        }
    }
}
