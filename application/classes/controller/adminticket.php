<?php defined('SYSPATH') or die('No direct script access.');

class Controller_Adminticket extends Controller_Adminsurvey
{
	public $_action_path = '/adminticket/';

	public function action_copy()
	{
		$this->auto_render = FALSE;
		$post = $this->request->post();
		$item_div = $this->_can_copied($post['uri']);
		if ($item_div == false) {
			$this->response->body(json_encode(['result' => 'fail', 'message' => 'common.message.error.content_copy_error']));
			return;
		}
		if ($item_div == 7) {
			$item_id = $this->_get_session('coupon_id', NULL);
		}
		$this->_copy($this->_bot_id, $item_div, $item_id);
		$this->response->body(json_encode(['result' => 'success', 'message' => 'common.message.success.content_copy_success']));
	}

	public function action_paste()
	{
		$this->auto_render = FALSE;
		$post = $this->request->post();
		$item_div = $this->_can_pasted($post['uri']);
		if ($item_div == false) {
			$this->response->body(json_encode(['result' => 'fail', 'message' => 'common.message.error.content_copy_error']));
			return;
		}
		$obj = $this->_paste();
		if ($obj == null) {
			$this->response->body(json_encode(['result' => 'fail', 'message' => 'common.message.error.content_copy_error']));
			return;
		}
		if ($obj['item_div'] == 7) {
			$new_id = $this->_copy_coupon($obj['item_id'], $this->_bot_id);
			$this->response->body(json_encode(['result' => 'success', 'message' => 'content_copy_success', 'url' => $this->_action_path . 'coupon?id=' . $new_id]));
		}
	}

	private function _copy_coupon($source_id, $target_bot_id)
	{
		$item_div = 7;
		$orm = ORM::factory('coupon', $source_id);
		$coupon = ORM::factory('coupon');
		$coupon->coupon_id = $this->_model->get_max_coupon_id($target_bot_id);
		$coupon->coupon_name = substr($orm->coupon_name . '-copy', 0, 200);
		$coupon->bot_id = $target_bot_id;
		$coupon->input_code = $orm->input_code;
		$coupon->class_cd = $orm->class_cd;
		$coupon->start_date = $orm->start_date;
		$coupon->end_date = $orm->end_date;
		$coupon->coupon_data = $orm->coupon_data;
		if ($orm->bot_id == $target_bot_id) {
			$coupon->user_in_charge = $orm->user_in_charge;
		} else {
		}
		$coupon->upd_user = $this->_user->user_id;
		$coupon->upd_time = date('Y-m-d H:i:s');
		$coupon->save();

		$orms = ORM::factory('coupondescription')->where('coupon_id', '=', $source_id)->find_all();
		foreach ($orms as $orm) {
			$coupon_desc = ORM::factory('coupondescription');
			$coupon_desc->coupon_id = $coupon->coupon_id;
			$coupon_desc->lang_cd = $orm->lang_cd;
			$coupon_desc->title = $orm->title;
			$coupon_desc->sub_title = $orm->sub_title;
			$coupon_desc->style = $orm->style;
			$coupon_desc->section = $orm->section;
			$coupon_desc->button_pc = $orm->button_pc;
			$coupon_desc->button_sp = $orm->button_sp;
			$coupon_desc->image = $orm->image;
			$coupon_desc->save();
		}
		$orms = ORM::factory('itemdisplay')->where('item_id', '=', $source_id)->where('item_div', '=', $item_div)->find_all();
		foreach ($orms as $orm) {
			$coupon_display = ORM::factory('itemdisplay');
			$coupon_display->item_id = $coupon->coupon_id;
			$coupon_display->item_div = $item_div;
			$coupon_display->bot_id = $target_bot_id;
			$coupon_display->lang_display = $orm->lang_display;
			$coupon_display->public_flg = $orm->public_flg;
			$coupon_display->save();
		}
		return $coupon->coupon_id;
	}
}
