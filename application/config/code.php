<?php defined('SYSPATH') or die('No direct access allowed.');return array (
		'ja' =>
		array (
				'01' =>
				array (
						'AS' => '亚洲',
						'EU' => '欧洲',
						'NA' => '北美洲',
						'OA' => '大洋洲',
						'AF' => '非洲',
						'SA' => '南美洲',
						'AN' => '南极洲',
				),
				'02' =>
				array (
					'ja' => '日本語',
					'js' => 'やさしい日本語',
					'en' => '英語',
					'cn' => '中国語（簡体）',
					'tw' => '中国語（繁体）',
					'kr' => '韓国語',
					'is' => 'アイスランド語',
					'ga' => 'アイルランド語',
					'az' => 'アゼルバイジャン語',
					'af' => 'アフリカーンス語',
					'am' => 'アムハラ語',
					'ar' => 'アラビア語',
					'sq' => 'アルバニア語',
					'hy' => 'アルメニア語',
					'it' => 'イタリア語',
					'yi' => 'イディッシュ語',
					'ig' => 'イボ語',
					'id' => 'インドネシア語',
					'ug' => 'ウイグル語',
					'cy' => 'ウェールズ語',
					'uk' => 'ウクライナ語',
					'uz' => 'ウズベク語',
					'ur' => 'ウルドゥー語',
					'et' => 'エストニア語',
					'eo' => 'エスペラント語',
					'nl' => 'オランダ語',
					'or' => 'オリヤ語',
					'kk' => 'カザフ語',
					'ca' => 'カタロニア語',
					'gl' => 'ガリシア語',
					'kn' => 'カンナダ語',
					'rw' => 'キニヤルワンダ語',
					'el' => 'ギリシャ語',
					'ky' => 'キルギス語',
					'gu' => 'グジャラート語',
					'km' => 'クメール語',
					'ku' => 'クルド語',
					'hr' => 'クロアチア語',
					'xh' => 'コーサ語',
					'co' => 'コルシカ語',
					'sm' => 'サモア語',
					'jv' => 'ジャワ語',
					'ka' => 'グルジア語',
					'sn' => 'ショナ語',
					'sd' => 'シンド語',
					'si' => 'シンハラ語',
					'sv' => 'スウェーデン語',
					'zu' => 'ズールー語',
					'gd' => 'スコットランド ゲール語',
					'es' => 'スペイン語',
					'sk' => 'スロバキア語',
					'sl' => 'スロベニア語',
					'sw' => 'スワヒリ語',
					'su' => 'スンダ語',
					'cb' => 'セブアノ語',
					'sr' => 'セルビア語',
					'st' => 'ソト語',
					'so' => 'ソマリ語',
					'th' => 'タイ語',
					'tl' => 'タガログ語',
					'tg' => 'タジク語',
					'tt' => 'タタール語',
					'ta' => 'タミル語',
					'cs' => 'チェコ語',
					'ny' => 'ニャンジャ語',
					'te' => 'テルグ語',
					'da' => 'デンマーク語',
					'de' => 'ドイツ語',
					'tk' => 'トルクメン語',
					'tr' => 'トルコ語',
					'ne' => 'ネパール語',
					'no' => 'ノルウェー語',
					'ht' => 'クレオール語（ハイチ）',
					'ha' => 'ハウサ語',
					'ps' => 'パシュト語',
					'eu' => 'バスク語',
					'hw' => 'ハワイ語',
					'hu' => 'ハンガリー語',
					'pa' => 'パンジャブ語',
					'hi' => 'ヒンディー語',
					'fi' => 'フィンランド語',
					'fr' => 'フランス語',
					'fy' => 'フリジア語',
					'bg' => 'ブルガリア語',
					'vi' => 'ベトナム語',
					'he' => 'ヘブライ語',
					'be' => 'ベラルーシ語',
					'fa' => 'ペルシャ語',
					'bn' => 'ベンガル語',
					'pl' => 'ポーランド語',
					'bs' => 'ボスニア語',
					'pt' => 'ポルトガル語',
					'mi' => 'マオリ語',
					'mk' => 'マケドニア語',
					'mr' => 'マラーティー語',
					'mg' => 'マラガシ語',
					'ml' => 'マラヤーラム語',
					'mt' => 'マルタ語',
					'ms' => 'マレー語',
					'my' => 'ミャンマー語',
					'mn' => 'モンゴル語',
					'hn' => 'モン語',
					'yo' => 'ヨルバ語',
					'lo' => 'ラオ語',
					'la' => 'ラテン語',
					'lv' => 'ラトビア語',
					'lt' => 'リトアニア語',
					'ro' => 'ルーマニア語',
					'lb' => 'ルクセンブルク語',
					'ru' => 'ロシア語',
					'as' => 'アッサム語',
					'ay' => 'アイマラ語',
					'bm' => 'バンバラ語',
					'bh' => 'ボージュプリー語',
					'dv' => 'ディベヒ語',
					'do' => 'ドグリ語',
					'ee' => 'エウェ語',
					'gn' => 'グアラニー語',
					'il' => 'イロカノ語',
					'ok' => 'コンカニ語',
					'ki' => 'クリオ語',
					'ck' => 'クルド語',
					'ln' => 'リンガラ語',
					'lg' => 'ルガンダ語',
					'ma' => 'マイティリー語',
					'ni' => 'マニプリ語',
					'lu' => 'ミゾ語',
					'om' => 'オロモ語',
					'qu' => 'ケチュア語',
					'sa' => 'サンスクリット語',
					'ns' => 'セペディ語',
					'ti' => 'ティグリニャ語',
					'ts' => 'ツォンガ語',
					'wi' => 'トウィ語',
				),
				'03' =>
				array (
						'txt' => 'テキスト',
						'img' => 'イメージ',
						'mov' => '動画',
						'car' => 'システム',
						'url' => 'URL',
				),
				'04' =>
				array (
						'01' => '申請中',
						'02' => '承認済み',
						'03' => '準備中',
						'04' => '開通済み',
						'05' => '停止',
				),
				'05' =>
				array (
						'00' => '連携なし',
						'01' => 'Redmine',
						'02' => 'Slack',
						'03' => 'Kintone',
				),
				'06' =>
				array (
						'00' => '一回のみ',
						'01' => '毎日',
				),
				'07' =>
				array (
						'01' => 'FAQ追加依頼',
						'02' => 'FAQ変更',
						'03' => 'コンテンツ',
						'04' => '有人対応',
				),
				'08' =>
				array (
						'fb' => 'Facebook',
						'ln' => 'LINE',
						'wc' => 'WeChat',
						'wb' => 'Web',
				),
				'09' =>
				array (
						'1' => '1日前',
						'3' => '3日前',
						'5' => '5日前',
						'10' => '10日前',
				),
				'10' =>
				array (
						'opt' => '単一選択',
						'chk' => '複数選択',
						'sel' => 'プルダウン',
						'txt' => '短文',
						'txa' => '長文',
						'fup' => 'ファイルアップロード',
						'mtx' => 'マトリクス',
						'spl' => 'グループ',
						'frs' => 'フリースペース',
						'eval_5' => '5段階評価',
						'nps' => 'NPS評価'
				),
				'11' =>
				array (
						'01' => '営業中',
						'02' => '閉店',
						'03' => '一時休業',
						'06' => '夏季休業',
						'07' => '冬季休業',
						'04' => '中止（イベントなど）',
						'05' => '終了（イベントなど）',
						'08' => 'HP準備中',
				),
				'12' =>
				array (
						'00' => '全曜日',
						'01' => '月曜日',
						'02' => '火曜日',
						'03' => '水曜日',
						'04' => '木曜日',
						'05' => '金曜日',
						'06' => '土曜日',
						'07' => '日曜日',
						'08' => '祝日',
						'10' => '特例日',
						'99' => '即時対応',
				),
				'13' =>
				array (
						'txt' => 'テキスト',
						'img' => '画像',
						'mov' => '動画',
						'car' => 'カルーセル',
						'mnu' => 'メニュー',
						'bkg' => '料金比較',
						'mal' => 'メールテンプレート',
						'tpl' => 'JSON定義',
				),
				'14' =>
				array (
						'' => '受信しない',
						'to' => 'TO',
						'cc' => 'CC',
				),
				'15' =>
				array (
						'0' => '成功',
						'1' => '送信成功(ログ出力失敗)',
						'2' => '送信失敗',
						'3' => 'ユーザ存在しない',
						'4' => 'メッセージ作成失敗',
				),
				'16' =>
				array (
						'fb' => 'Facebook',
						'ln' => 'LINE',
						'wc' => 'WeChat',
						'wb' => 'Web',
				),
				'17' =>
				array (
						'0' => 'ボット対応',
						'1' => '有人対応',
				),
				'18' =>
				array (
						'01' => '日枠',
						'02' => '時間帯枠',
				),
				'19' =>
				array (
						'0' => '新規投稿',
						'1' => '投稿済み',
						'2' => '却下済み',
				),
				'20' =>
				array (
						'01' => 'お知らせ',
						'02' => '質問',
						'03' => '作業依頼',
						'04' => '不具合',
						'05' => '改善',
						'09' => 'その他',
				),
				'21' =>
				array (
						'01' => '新規',
						'02' => '対応中',
						'03' => '解決',
						'04' => '完了',
						'05' => '取消',
				),
				// bot apply status
				'22' =>
				array (
						'01' => '新規',
						'02' => '対応中',
						'03' => '承認',
						'04' => '完了',
						'05' => '取消',
				),
				'23' =>
				array (
						'01' => 'オンライン',
						'02' => '一時離席',
						//'03' => '取込中',
						//'04' => '休憩',
						'05' => 'オフライン',
				),
				'24' =>
				array (
						'01' => 'Web',
						'02' => 'POP',
				),
				//inquiry_resultに00〜03の状態しかないので、管理画面からその他の状態を一旦非表示 20240810 by 陳
				'25' =>
				array (
						'00' => '仮受付（未決済）',
						'01' => '新規申込',
						'02' => '変更済み',
						'03' => 'キャンセル済み',
						// '12' => '決済情報登録失敗',
						// '13' => '決済情報登録済(在庫確保中)',
						// '14' => '決済情報登録済(在庫確保失敗)',
						// '15' => '決済準備完了(入力待ち)',
						// '17' => '決済未完了(在庫戻し済)',
						// '18' => '決済未完了(在庫戻し失敗)',
				),
				'26' =>
				array (
						'0' => 'すべて',
						'1' => 'AI認識不可＋有人対応リクエスト',
						'2' => '有人対応リクエストのみ',
				),
				'27' =>
				array (
						'00' => '一時保存',
						'01' => '配信待ち',
						'02' => '配信中',
						'03' => '配信成功',
						'04' => '配信完了（警告あり）',
						'05' => '配信失敗',
				),
				'28' =>
				array (
						'03' => 'CSV出力',
						'04' => 'バッチ実行',
				),
				'281' =>
				array (
						'01' => '全員配信',
						'02' => '個別配信',
				),
				'282' =>
				array (
						'01' => '即時配信',
						'02' => '予約配信',
				),
				// task class cd
				'29' =>
				array (
						'01' => 'すべて',
				),
				'30' =>
				array (
						'01' => '館内施設カテゴリー区分',
						'02' => '館内施設エリア区分',
						'03' => '周辺案内カテゴリー区分',
						'04' => '周辺案内エリア区分',
						'05' => 'ホテル施設カテゴリー区分',
						'06' => 'ホテル施設エリア区分',
						'07' => 'メディアカテゴリー区分',
						'08' => 'メディアエリア区分',
						'09' => '販売商品カテゴリー区分',
						'10' => 'メッセージカテゴリー区分',
						'50' => 'その他',
						'60' => 'MEMBER分類',
						'70' => 'アイコン分類',
						'80' => '選択肢分類',
						'90' => 'FAQ分類',
						'99' => 'システム用',
				),
                '31' =>
                array (
						'0.3' => '<= 300 m',
                        '0.5' => '<= 500 m',
						'1' => '<= 1 km',
						'2' => '<= 2 km',
						'3' => '<= 3 km',
						'5' => '<= 5 km',
                        '10' => '<= 10 km',
                        '20' => '<= 20 km',
                        '50' => '<= 50 km',
                        '100' => '<= 100 km',
                        '200' => '<= 200 km',
                        '' => '全ての距離',
                ),
				'32' =>
				array (
						'1' => '検索窓入力',
						'2' => 'カテゴリー選択',
						'3' => '質問選択',
						'4' => 'キーワード選択',
						'5' => '質問表示',
						'6' => '関連質問表示',
				),
				'33' =>
				array (
						'01' => '依頼中',
						'02' => '対応済み',
						'03' => '確認済み',
						'04' => '完了',
				),
				'34' =>
				array (
						'tl' => 'TLリンカーン',
						'tm' => '手間入らず',
						'np' => 'ねっぱん',
						'rt' => 'らく通with',
						'ta' => 'talkappi',
				),
				'35' =>
				array (
						'bot' => 'CHATBOT',
						'faq' => 'FAQ',
						'survey' => 'SURVEY',
						'inquiry' => 'INQUIRY',
						'site' => 'PAGE',
				),
				'36' =>
				array (
						'all' => '全期間',
						'1d'=>'日付期間', 
						'1h'=>'時間帯'
				),
				'37' =>
				array (
						'01' => 'JTB Book&Pay',
				),
				'38' =>['1'=>'現地決済', '2'=>'クレジットカード決済'],
				'39' =>['1'=>'空いている', '2'=>'やや混雑', '3'=>'混雑', '4'=>'入場制限'],
				'VERY_FUNCTION' =>['chatbot'=>'チャット', 'reserve'=>'予約・申込', 'faq'=>'FAQページ', 'request'=>'リクエスト', 'congestion'=>'混雑状況', 'checkout'=>'チェックアウト','userguide'=>'ご利用案内'],
				'VERY_FUNCTION_MAP' =>['chatbot'=>'flg_ai_bot', 'reserve'=>'flg_next_inquiry', 'faq'=>'flg_faq_site', 'request'=>'flg_ai_bot', 'checkout'=>'flg_ai_bot'], // チェックアウトとリクエスト機能の前提はチャットボットのため、requestとcheckoutの表示flagはflg_ai_botです
				'VERY_FOOTER' =>['chatbot'=>'チャット', 'reserve'=>'予約・申込', 'faq'=>'FAQページ', 'request'=>'リクエスト', 'congestion'=>'混雑状況', 'checkout'=>'チェックアウト','userguide'=>'ご利用案内','coupon'=>'クーポン'],
				'VERY_FOOTER_MAP' =>['chatbot'=>'flg_ai_bot', 'reserve'=>'flg_next_inquiry', 'faq'=>'flg_faq_site', 'request'=>'flg_ai_bot', 'checkout'=>'flg_ai_bot', 'coupon'=>'flg_next_survey'], //現状は基本的にはSURVEY契約時に一緒についてくる機能のため、coupon表示flagはflg_next_surveyです。
				'40' =>
				array (
						'01' => '一般',
						'02' => '新機能',
						'05' => 'セミナー',
						'03' => '改善',
						'04' => '不具合',
				),
				'41' =>
				array (
						'category' => 'カテゴリ選択',
						'keyword'=>'キーワード選択', 
						'search'=>'キーワード検索',
						'intent'=>'質問選択',
						'tag'=>'タグ選択',
						'context'=>'コンテキスト'
				),
				'42' =>
				array (
						'min' => '分前',
						'hour'=>'時間前', 
						'day'=>'日前'
				),
				'45' => array(
					'01'=>'受付',
					'02'=>'対応中',
					'03'=>'完了',
					'04'=>'取消',
					'05'=>'削除',
				),
				'46' => array(
					'1'=>'日',
					'2'=>'月',
					'3'=>'火',
					'4'=>'水',
					'5'=>'木',
					'6'=>'金',
					'7'=>'土',
				),
				'47' =>
				array (
						'00' => '仮登録',
						'01' => '新規',
						'02' => '変更',
						'03' => '取消',
				),
				'48' =>
				array (
						'01' => '完了',
						'02' => '未完了',
				),
				'49' => array(
					'01'=>'受付',
					'02'=>'対応中',
					'03'=>'完了',
					'04'=>'キャンセル',
					'05'=>'削除',
				),
				'50' => array(
					'01'=>'利用期間',
					'02'=>'試用期間',
					'03'=>'申し込み日',
				),
				'51' => array (
					'bot' => 'talkappi CHATBOT',
					'faq' => 'talkappi FAQ',
					'survey' => 'talkappi SURVEY',
					'inquiry' => 'talkappi INQUIRY',
					'very' => 'VERY',
					'page' => 'talkappi PAGE',
					'workbot' => 'talkappi WORKBOT',
					'coupon' => 'talkappi COUPON',
					'newsletter' => 'talkappi NEWSLETTER',
					'marketing' => 'talkappi MARKETING',
					'engage' => 'talkappi ENGAGE',
					'booking' => 'talkappi BOOKING',
				),
				'52' => array (
					'01' => 'カード',
					'02' => 'PayPay',
				),
				'53' => array (
					'inquiry' => 'INQUIRY',
				),
				'54' => array (
					'jcb' => 'JCB',
					'amex' => 'American Express',
					'visa' => 'Visa',
					'diners' => 'Diners Club',
					'master' => 'Mastercard',
				),
				'55' => array(
					'us' => 'アメリカ合衆国',
					'ja' => '日本',
				),
				'56' => array (
					'bot' => '01',
					'faq' => '02',
					'survey' => '03',
					'inquiry' => '04',
					'very' => '05',
					'page' => '06',
					'workbot' => '07',
					'coupon' => '08',
					'newsletter' => '09',
					'marketing' => '10',
					'engage' => '12',
					'booking' => '13'
				),
    ),
        'en' =>
        array (
                '01' =>
                array (
                        'AS' => 'Asia',
                        'EU' => 'Europe',
                        'NA' => 'North America',
                        'OA' => 'Oceania',
                        'AF' => 'Africa',
                        'SA' => 'South America',
                        'AN' => 'Antarctica',
                ),
                '02' =>
                array (
					'ja' => 'Japanese',
					'js' => 'Easy Japanese',
					'en' => 'English',
					'cn' => 'Chinese (Simpl.)',
					'tw' => 'Chinese (Trad.)',
					'kr' => 'Korean',
					'is' => 'Icelandic',
					'ga' => 'Irish',
					'az' => 'Azerbaijani',
					'af' => 'Afrikaans',
					'am' => 'Amharic',
					'ar' => 'Arabic',
					'sq' => 'Albanian',
					'hy' => 'Armenian',
					'it' => 'Italian',
					'yi' => 'Yiddish',
					'ig' => 'Igbo',
					'id' => 'Indonesian',
					'ug' => 'Uyghur',
					'cy' => 'Welsh',
					'uk' => 'Ukrainian',
					'uz' => 'Uzbek',
					'ur' => 'Urdu',
					'et' => 'Estonian',
					'eo' => 'Esperanto',
					'nl' => 'Dutch',
					'or' => 'Odia (Oriya)',
					'kk' => 'Kazakh',
					'ca' => 'Catalan',
					'gl' => 'Galician',
					'kn' => 'Kannada',
					'rw' => 'Kinyarwanda',
					'el' => 'Greek',
					'ky' => 'Kyrgyz',
					'gu' => 'Gujarati',
					'km' => 'Khmer',
					'ku' => 'Kurdish',
					'hr' => 'Croatian',
					'xh' => 'Xhosa',
					'co' => 'Corsican',
					'sm' => 'Samoan',
					'jv' => 'Javanese',
					'ka' => 'Georgian',
					'sn' => 'Shona',
					'sd' => 'Sindhi',
					'si' => 'Sinhala (Sinhalese)',
					'sv' => 'Swedish',
					'zu' => 'Zulu',
					'gd' => 'Scots Gaelic',
					'es' => 'Spanish',
					'sk' => 'Slovak',
					'sl' => 'Slovenian',
					'sw' => 'Swahili',
					'su' => 'Sundanese',
					'cb' => 'Cebuano',
					'sr' => 'Serbian',
					'st' => 'Sesotho',
					'so' => 'Somali',
					'th' => 'Thai',
					'tl' => 'Tagalog (Filipino)',
					'tg' => 'Tajik',
					'tt' => 'Tatar',
					'ta' => 'Tamil',
					'cs' => 'Czech',
					'ny' => 'Nyanja (Chichewa)',
					'te' => 'Telugu',
					'da' => 'Danish',
					'de' => 'German',
					'tk' => 'Turkmen',
					'tr' => 'Turkish',
					'ne' => 'Nepali',
					'no' => 'Norwegian',
					'ht' => 'Haitian Creole',
					'ha' => 'Hausa',
					'ps' => 'Pashto',
					'eu' => 'Basque',
					'hw' => 'Hawaiian',
					'hu' => 'Hungarian',
					'pa' => 'Punjabi',
					'hi' => 'Hindi',
					'fi' => 'Finnish',
					'fr' => 'French',
					'fy' => 'Frisian',
					'bg' => 'Bulgarian',
					'vi' => 'Vietnamese',
					'he' => 'Hebrew',
					'be' => 'Belarusian',
					'fa' => 'Persian',
					'bn' => 'Bengali',
					'pl' => 'Polish',
					'bs' => 'Bosnian',
					'pt' => 'Portuguese',
					'mi' => 'Maori',
					'mk' => 'Macedonian',
					'mr' => 'Marathi',
					'mg' => 'Malagasy',
					'ml' => 'Malayalam',
					'mt' => 'Maltese',
					'ms' => 'Malay',
					'my' => 'Myanmar (Burmese)',
					'mn' => 'Mongolian',
					'hn' => 'Hmong',
					'yo' => 'Yoruba',
					'lo' => 'Lao',
					'la' => 'Latin',
					'lv' => 'Latvian',
					'lt' => 'Lithuanian',
					'ro' => 'Romanian',
					'lb' => 'Luxembourgish',
					'ru' => 'Russian',
					'as' => 'Assamese',
					'ay' => 'Aymara',
					'bm' => 'Bambara',
					'bh' => 'Bhojpuri',
					'dv' => 'Dhivehi',
					'do' => 'Dogri',
					'ee' => 'Ewe',
					'gn' => 'Guarani',
					'il' => 'Ilocano',
					'ok' => 'Konkani',
					'ki' => 'Krio',
					'ck' => 'Kurdish',
					'ln' => 'Lingala',
					'lg' => 'Luganda',
					'ma' => 'Maithili',
					'ni' => 'Meiteilon',
					'lu' => 'Mizo',
					'om' => 'Oromo',
					'qu' => 'Quechua',
					'sa' => 'Sanskrit',
					'ns' => 'Sepedi',
					'ti' => 'Tigrinya',
					'ts' => 'Tsonga',
					'wi' => 'Twi',
                ),
                '03' =>
                array (
                        'txt' => 'Text',
                        'img' => 'Image',
                        'mov' => 'Video',
                        'car' => 'System',
                        'url' => 'URL',
                ),
                '04' =>
                array (
                        '01' => 'In application',
                        '02' => 'Approved',
                        '03' => 'Preparing',
                        '04' => 'Active',
                        '05' => 'Discard',
                ),
                '05' =>
                array (
                        '00' => 'No linkage',
                        '01' => 'Redmine',
                        '02' => 'Slack',
                        '03' => 'Kintone',
                ),
                '06' =>
                array (
                        '00' => 'Only once',
                        '01' => 'Every day',
                ),
                '07' =>
                array (
                        '01' => 'Request addition of FAQ',
                        '02' => 'Change FAQ',
                        '03' => 'Contents',
                        '04' => 'Operator',
                ),
                '08' =>
                array (
                        'fb' => 'Facebook',
                        'ln' => 'Line',
                        'wc' => 'WeChat',
                        'wb' => 'Web',
                ),
                '09' =>
                array (
                        '1' => 'A day before',
                        '3' => '3 days before',
                        '5' => '5 days before',
                        '10' => '10 days before',
                ),
                '10' =>
                array (
                        'opt' => 'Single choice',
                        'chk' => 'Multiple choice',
                        'sel' => 'Drop-down menu',
                        'txt' => 'Short sentence',
                        'txa' => 'Paragraph',
                        'fup' => 'Upload file',
                        'mtx' => 'Matrix',
                        'spl' => 'Group',
                        'frs' => 'Free space',
						'eval_5' => '5-stage evaluation',
						'nps' => 'NPS evaluation'
                ),
                '11' =>
                array (
                        '01' => 'In operation',
                        '02' => 'Closed',
                        '03' => 'Temporarily closed',
                        '06' => 'Summer closure',
                        '07' => 'Winter closure',
                        '04' => 'Canceled (events, etc.)',
                        '05' => 'End (events, etc.)',
						'08' => 'HP is coming soon',
                ),
                '12' =>
                array (
                        '00' => 'All',
                        '01' => 'Monday',
                        '02' => 'Tuesday',
                        '03' => 'Wednesday',
                        '04' => 'Thursday',
                        '05' => 'Friday',
                        '06' => 'Saturday',
                        '07' => 'Sunday',
                        '08' => 'Holiday',
                        '10' => 'Exception',
                        '99' => 'Immediate response',
                ),
                '13' =>
                array (
                        'txt' => 'Text',
                        'img' => 'Image',
                        'mov' => 'Video',
                        'car' => 'Carousel',
                        'mnu' => 'Menu',
						'bkg' => 'Price comparison',
                        'rcm' => 'Rich Menu',
                        'tpl' => 'Fixed text',
                        'mal' => 'Email template',
                ),
                '14' =>
                array (
                        '' => 'Do not receive ',
                        'to' => 'TO',
                        'cc' => 'CC',
                ),
                '15' =>
                array (
                        '0' => 'Success',
                        '1' => 'Successfully sent (failed to export log)',
                        '2' => 'Failed to send',
                        '3' => 'User not found',
                        '4' => 'Message generation failed',
                ),
                '16' =>
                array (
                        'fb' => 'Facebook',
                        'ln' => 'LINE',
                        'wc' => 'WeChat',
                        'wb' => 'Web',
                ),
                '17' =>
                array (
                        '0' => 'Chatbot',
                        '1' => 'Operator',
                ),
                '18' =>
                array (
                        '01' => 'Date',
                        '02' => 'Time',
                ),
                '19' =>
                array (
                        '0' => 'New post',
                        '1' => 'Posted',
                        '2' => 'Declined',
                ),
                '20' =>
                array (
                        '01' => 'Notice',
                        '02' => 'Question',
                        '03' => 'Request',
                        '04' => 'Bug',
                        '05' => 'Bug fixes',
                        '09' => 'Other',
                ),
                '21' =>
                array (
                        '01' => 'New',
                        '02' => 'In progress',
                        '03' => 'Solved',
                        '04' => 'Complete',
                        '05' => 'Cancel',
                ),
                // bot apply status
                '22' =>
                array (
                        '01' => 'New',
                        '02' => 'In progress',
                        '03' => 'Approve',
                        '04' => 'Complete',
                        '05' => 'Cancel',
                ),
                '23' =>
                array (
                        '01' => 'Online',
                        '02' => 'Away',
                        //'03' => 'Busy',
                        //'04' => 'Break',
                        '05' => 'Offline',
                ),
                '24' =>
                array (
                        '01' => 'Web',
                        '02' => 'Print ads',
                ),
                '25' =>
                array (
                        '00' => 'Temporary registration',
                        '01' => 'New',
                        '02' => 'Change',
                        '03' => 'Cancel',
                        // '11' => 'Temporary reservation',
                        // '12' => 'Transaction failed',
                        // '13' => 'Transaction successful',
                        // '14' => 'Transaction confirmation failed',
                        // '15' => 'Transaction confirmation successful',
                        // '16' => 'Confirmation successful, deposit failed',
                        // '17' => 'Deposit failed, cancellation successful',
                ),
                '26' =>
                array (
                        '0' => 'All',
                        '1' => 'No response from the AI＋Operator request',
                        '2' => 'Operator request only',
                ),
                '27' =>
                array (
                        '00' => 'Save temporarily',
                        '01' => 'New',
                        '02' => 'In progress',
                        '03' => 'Delivered successfully',
                        '04' => 'Delivery complete (warning)',
                        '05' => 'Delivery failed',
                ),
                '28' =>
                array (
                        '03' => 'Export CSV',
                        '04' => 'Batch processing',
                ),
                '281' =>
                array (
                        '01' => 'Send to all',
                        '02' => 'Send individually ',
                ),
                '282' =>
                array (
                        '01' => 'Send immediately',
                        '02' => 'Set a time',
                ),
                // task class cd
                '29' =>
                array (
                        '01' => 'All',
                ),
                '30' =>
                array (
                        '01' => 'Building facility category',
                        '02' => 'Building facility area',
                        '03' => 'Area guide category',
                        '04' => 'Area guide area category',
                        '05' => 'Hotel facility category',
                        '06' => 'Hotel facility area category',
                        '07' => 'Media category',
                        '08' => 'Media area',
                        '09' => 'Sales product category',
                        '10' => 'Message category',
                        '50' => 'Other',
						'60' => 'MEMBER category',
						'70' => 'Icon category',
                        '80' => 'Option category',
                        '90' => 'FAQ category',
                        '99' => 'System',
                ),
                '31' =>
                array (
						'0.3' => '<= 300 m',
                        '0.5' => '<= 500 m',
						'1' => '<= 1 km',
						'2' => '<= 2 km',
						'3' => '<= 3 km',
						'5' => '<= 5 km',
                        '10' => '<= 10 km',
                        '20' => '<= 20 km',
                        '50' => '<= 50 km',
                        '100' => '<= 100 km',
                        '200' => '<= 200 km',
                        '' => 'All',
                ),
                '32' =>
                array (
                        '1' => 'Search bar',
                        '2' => 'Categories selected',
                        '3' => 'Questions selected',
                        '4' => 'Keywords selected',
                        '5' => 'Questions displayed',
                        '6' => 'Related questions',
                ),
                '33' =>
                array (
                        '01' => 'Requesting',
                        '02' => 'Solved',
                        '03' => 'Confirmed',
                        '04' => 'Complete',
                ),
                '34' =>
                array (
                        'tl' => 'TL-Lincoln',
                        'tm' => 'Temairazu',
                        'np' => 'Neppan',
                        'rt' => 'Raku-2 with',
                        'ta' => 'talkappi',
                ),
                '35' =>
                array (
                        'bot' => 'AI Bot',
                        'faq' => 'FAQ',
                        'survey' => 'Survey',
                        'inquiry' => 'Inquiry',
                ),
                '36' =>
                array (
                        'all' => 'All',
                        '1d'=>'Date', 
                        '1h'=>'Time'
                ),
                '37' =>
                array (
                        '01' => 'JTB Book&Pay',
                ),
                '38' =>['1'=>'On-site payment', '2'=>'Credit card payment'],
                '39' =>['1'=>'Vacant', '2'=>'Slightly crowded', '3'=>'Crowded', '4'=>'Entry limited'],
                'VERY_FUNCTION' =>['chatbot'=>'Chat', 'reserve'=>'Reserve/Apply', 'faq'=>'FAQ page', 'request'=>'Request', 'congestion'=>'Crowdedness', 'checkout'=>'Check-out', 'userguide'=>'User guide'],
                'VERY_FUNCTION_MAP' =>['chatbot'=>'flg_ai_bot', 'reserve'=>'flg_next_inquiry', 'faq'=>'flg_faq_site', 'request'=>'flg_ai_bot', 'checkout'=>'flg_ai_bot'], // チェックアウトとリクエスト機能の前提はチャットボットのため、requestとcheckoutの表示flagはflg_ai_botです
                'VERY_FOOTER' =>['chatbot'=>'Chat', 'reserve'=>'Reserve/Apply', 'faq'=>'FAQ page', 'request'=>'Request', 'congestion'=>'Crowdedness', 'checkout'=>'Check-out', 'userguide'=>'User guide', 'coupon'=>'Coupon'],
                'VERY_FOOTER_MAP' =>['chatbot'=>'flg_ai_bot', 'reserve'=>'flg_next_inquiry', 'faq'=>'flg_faq_site', 'request'=>'flg_ai_bot', 'checkout'=>'flg_ai_bot', 'coupon'=>'flg_next_survey'],  //現状は基本的にはSURVEY契約時に一緒についてくる機能のため、coupon表示flagはflg_next_surveyです。
                '40' =>
                array (
                        '01' => 'General',
                        '02' => 'New function',
						'05' => 'Seminar',
                        '03' => 'Improvement',
                        '04' => 'Bug fix',
                ),
				'42' =>
				array (
						'min' => ' min ago',
						'hour'=>' hours ago', 
						'day'=>' days ago'
				),
				'45' => array(
					'01'=>'Not handled',
					'02'=>'Handling',
					'03'=>'Completed',
					'04'=>'Cancel',
					'05'=>'Delete',
				),
				'46' => array(
					'1'=>'Sun',
					'2'=>'Mon',
					'3'=>'Tue',
					'4'=>'Wed',
					'5'=>'Thu',
					'6'=>'Fri',
					'7'=>'Sat',
				),
				'47' =>
				array (
						'00' => 'Temporary registration',
						'01' => 'New',
						'02' => 'Modify',
						'03' => 'Cancel',
				),
				'48' =>
				array (
						'01' => 'Complete',
						'02' => 'Incomplete',
				),
				'49' => array(
					'01'=>'Not handled',
					'02'=>'Handling',
					'03'=>'Completed',
					'04'=>'Cancel',
					'05'=>'Delete',
				),
				'50' => array(
					'01'=>'formal',
					'02'=>'trial',
					'03'=>'apply',
				),
				'51' => array (
					'bot' => 'talkappi CHATBOT',
					'faq' => 'talkappi FAQ',
					'survey' => 'talkappi SURVEY',
					'inquiry' => 'talkappi INQUIRY',
					'very' => 'VERY',
					'page' => 'talkappi PAGE',
					'workbot' => 'talkappi WORKBOT',
					'coupon' => 'talkappi COUPON',
					'newsletter' => 'talkappi NEWSLETTER',
					'marketing' => 'talkappi MARKETING',
					'engage' => 'talkappi ENGAGE',
					'booking' => 'talkappi BOOKING',
				),
				'52' => array (
					'01' => 'Card',
					'02' => 'PayPay',
				),
				'53' => array (
					'inquiry' => 'INQUIRY',
				),
				'54' => array (
					'jcb' => 'JCB',
					'amex' => 'American Express',
					'visa' => 'Visa',
					'diners' => 'Diners Club',
					'master' => 'Mastercard',
				),
				'55' => array(
					'us' => 'United States',
					'ja' => 'Japan',
				),
				'56' => array (
					'bot' => '01',
					'faq' => '02',
					'survey' => '03',
					'inquiry' => '04',
					'very' => '05',
					'page' => '06',
					'workbot' => '07',
					'coupon' => '08',
					'newsletter' => '09',
					'marketing' => '10',
					'engage' => '12',
					'booking' => '13'
				),
        ),
);