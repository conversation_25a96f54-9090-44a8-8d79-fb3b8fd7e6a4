<?php defined('SYSPATH') or die('No direct access allowed.');return array (
		'ja' =>
		array (
				/* 全体設定 */
				'group_separator1'=>array('全体設定',''),
				'template_bot'=>array('テンプレートBot','0'),
				'flg_sub_brand_bot'=>array('サブブランドBot','0'),
				'maintenance_msg'=>array('メンテナンスメッセージ',''),
				'A01_relation_bots'=>array('関連ボット',''),
				'area_contents_link_bots'=>array('広域コンテンツ連携ボット','9999999'),
				'default_scene_cd'=>array('デフォルト導線',''),
				'very_scene_cd'=>array('VERY旅中案内導線',''),
				'ai_type'=>array('利用AI種類',''),
				'flg_ai_auto_apply'=>array('生成AIのデフォルト適用','0'),
				'flg_update_faq_by_gpt'=>array('FAQ更新自動検出利用','0'),
				'flg_create_faq_by_gpt'=>array('FAQ自動作成利用','0'),
				'flg_native_translate'=>array('ネイティブ翻訳の提供','1'),
				'apiai_no'=>array('AIエンジン（全体）','1'),
				'scene_ai_engine'=>array('AIエンジン（ユーザー導線）', '{"scene":"", "engines":""}'),
				'flg_use_self_faq_only'=>array('自施設FAQのみ参照（親は参照せず）','0'),
				'txt_inquiry_types'=>array('FAQ分類のグループ定義',''),
				'flg_choice_bot'=>array('AIなし、選択肢Bot','0'),
				'admin_user_psw_change_span'=>array('管理サイトユーザーパスワード変更間隔',''),
				'location_lon'=>array('施設の経度',''),
				'location_lat'=>array('施設の緯度',''),
				'timezone'=>array('タイムゾーン',''),
				'json_chatbot_mode'=>array('災害時モード',''),
				
				/* 提供機能の設定 */
				'group_separator6'=>array('提供機能の設定',''),
				'flg_ai_bot'=>array('CHATBOT利用','1'),
				'flg_ota_be_price_compare'=>array('OTAとBE料金比較設定表示', '0'),
				'flg_faq_site'=>array('FAQ利用','0'),
				'json_faq_setting'=>array('FAQ設定','{"freq_days_1":0, "freq_days_2":14}'),
				'flg_next_survey'=>array('SURVEY利用','0'),
				'json_next_survey_setting'=>array('SURVEY設定','{"base_url":"https://survey.talkappi.com","param_id":"id","param_uid":"","param_login":"","end_redirect_url":"","user_in_charge_required":0,"login":[],"point":[],"coupon":[]}'),
				'flg_next_inquiry'=>array('INQUIRY利用','0'),
				'json_next_inquiry_setting'=>array('INQUIRY設定','{"base_url":"https://inquiry.talkappi.com","param_id":"id","param_login":"","end_redirect_url":""}'),
				'flg_newsletter'=>array('NEWSLETTER利用','0'),
				'json_newsletter_setting'=>array('NEWSLETTER設定','{"project_management": 0}'),
				'flg_next_coupon'=>array('COUPON利用','0'),
				'flg_page'=>array('PAGE利用','0'),
				'flg_very'=>array('VERY利用','0'),
				'flg_book'=>array('BOOK利用','0'),
				'flg_laundry'=>array('洗濯機連携','0'),
				'laundry_device_refresh_token'=>array('洗濯機 登録情報取得トークン',''),
				'laundry_status_refresh_token'=>array('洗濯機 状態取得用トークン',''),
				'json_laundry_setting'=>array('洗濯機情報',''),
				'flg_very_access_by_bot_id'=>array('bot_id を指定してVERYへのアクセスを許可する','0'),
				'flg_engage'=>array('ENGAGE利用','0'),
				'flg_member'=>array('MEMBER利用','0'),
				'flg_booking'=>array('BOOK利用','0'),
				'flg_ticket'=>array('TICKET利用','0'),
				'flg_accept_request'=>array('リクエスト受付','0'),
				'json_request_setting'=>array('リクエスト受付設定','{"refresh_interval":"30","refresh_range":"30","mail_remind_range":"0"}'),
				/* TODO
				'flg_chat'=>array('有人対応利用','0'),
				'json_chat_setting'=>array('有人対応設定','{"allow_change_chatmode":"0","operator_mode_control":"0"}'),
				*/
				'flg_accept_order'=>array('予約販売','0'),
				'json_booking_setting'=>array('予約販売設定','{"send_mail_from":""}'),
				'flg_product_auto_update'=>array('販売コンテンツ自動更新','0'),
				'flg_stock_manage'=>array('在庫枠管理','0'),
				'flg_allow_webchat_upload_file'=>array('Web写真送信','0'),
				'flg_allow_webchat_voice_input'=>array('Web音声入力','0'),
				'json_usage_report'=>array('利用情報レポート送信','{"enable":0, "send":["lineworks","mail"], "frequence":["week","month"], "report":["bot.daily","faq.survey","faq.no_answer","faq.nonrecognition"]}'),
				'flg_dailycsv_append'=>array('日次会話CSVファイル収集','0'),
				'flg_partly_introduce'=>array('グループ施設部分導入','0'),

				/* 表示の設定 */
				'group_separator2'=>array('表示の設定',''),
				'survey_flg'=>array('満足度調査の表示', '1'),
				'show_intent_relation_flg'=>array('関連質問の表示','1'),
				'flg_show_botlist'=>array('SNS公式アカウントの公開','1'),
				'flg_show_inquiry_next_page'=>array('曖昧検索の複数ページ表示','0'),
				'num_inquiry_page'=>array('曖昧検索の表示ページ数','2'),
				'flg_webchat_mlang_menu'=>array('WebChatの「メニュー」表示','0'),
				'flg_show_talkappi_power'=>array('WebChatの「Powered by talkappi」表示','1'),
				'flg_webchat_autocomplete'=>array('WebChat入力内容より質問提示', '0'),
				'show_return_to_select_flg'=>array('「トップに戻るメニュー」表示','0'),

				/* 動作の設定 */
				'group_separator3'=>array('動作の設定',''),
				'flg_inquiry_confirm'=>array('FAQ意図確認','0'),
				'flg_split_sentence'=>array('長文分割認識','1'),
				'skill_need_check_chat_mode'=>array('チャットモード関連スキル',''),
				'skill_need_check_busi_time'=>array('営業時間関連スキル',''),
				'default_skill'=>array('認識不可時のスキル',''),
				'inquiry_no_answer_skill'=>array('質問未回答時のスキル',''),
				'txt_input_unknown_next_skill'=>array('「この中にはない」遷移先スキル',''),
				'flg_sendnotify_for_inputunknown'=>array('認識不可時の送信','0'),
				'flg_sendnotify_for_requestoperator'=>array('有人接続時の送信','0'),
				'dialogflow_try_different_lang_flg'=>array('AI言語自動切替','0'),
				'flg_allow_change_chatmode'=>array('有人モード終止確認','0'),
				'flg_operator_mode_control'=>array('有人対応オンライン・オフライン切替有無','0'),
				'flg_refer_template_faq'=>array('テンプレートボットFAQ回答参照','0'),
				'context_select_flow'=>array('Context選択用フローNo',''),
				'ignore_ip_list'=>array('動作確認用IPアドレス一覧',''),
				'json_external_url_open_modal'=>array('外部URL遷移モーダル設定', '{"chatbot":0,"very":1,"whitelist":[]}'),
				'json_limited_setting'=>array('利用制限','{"black_ip":"","white_ip":"","secret_user":""}'),
				'json_mgr_limited_setting'=>array('管理サイト利用制限','{"black_ip":[],"white_ip":[]}'),
				'account_domain_restriction'=>array('ユーザーアカウントのドメイン制限',''),

				/* SNS関連の設定 */
				'group_separator4'=>array('SNS関連の設定',''),
				'flg_line_follow_select_lang'=>array('LINE開始の言語選択','0'),
				/* 'flg_use_line_flex_message'=>array('LINE FLEX_MSG利用','1'), */
				'msg_rcm_line_richmenu'=>array('LINEリッチメニューMSG',''),

				'flg_facebook_eu_restict'=>array('FACEBOOK EU制限','1'),

				'json_webchat_association_setting'=>array('SNSのWEBへの誘導',''),

				/* 回数・時間の設定 */
				'group_separator5'=>array('回数・時間の設定',''),
				'user_chat_mode_reset_interval'=>array('有人->自動モード切替間隔(分)','4320'),
				'num_user_chat_request_wait'=>array('有人対応混み合いMSG表示間隔(分)','0'),
				'num_duplicate_inquiry_check_interval'=>array('重複意図検出間隔(回)','3'),
				'num_request_refresh_interval'=>array('リクエスト受付画面再描画間隔(秒)','30'),
				'num_request_refresh_period'=>array('リクエスト受付画面表示期間(日)','30'),
				'num_admin_refresh_interval'=>array('管理サイト通知更新間隔(秒)','10'),

				/* カスタマイズ設定 */
				'group_separator7'=>array('カスタマイズ設定',''),
				'div_item_class_0'=>array('FAQ 分類区分',''),
				'div_item_class_1'=>array('施設コンテンツ 分類区分','1'),
				'div_item_area_1'=>array('施設コンテンツ エリア区分',''),
				'div_item_class_2'=>array('広域コンテンツ 分類区分',''),
				'div_item_area_2'=>array('広域コンテンツ エリア区分',''),
				'div_item_class_2_very'=>array('広域コンテンツ(VERY) 分類区分',''),
				'div_item_area_2_very'=>array('広域コンテンツ(VERY) エリア区分',''),
				'div_item_class_3'=>array('グループ施設 分類区分',''),
				'div_item_area_3'=>array('グループ施設 エリア区分',''),
				'div_item_class_4'=>array('メディア 分類区分',''),
				'div_item_area_4'=>array('メディア エリア区分',''),
				'div_item_class_5'=>array('販売コンテンツ 分類区分',''),
				'div_item_area_5'=>array('販売コンテンツ エリア区分',''),
				'div_item_class_6'=>array('メッセージコンテンツ 分類区分','999906'),
				'div_item_class_7'=>array('プレゼント 分類区分','999907'),
				'div_item_class_8'=>array('アンケート 分類区分','999908'),
				'div_item_class_9'=>array('問い合わせ 分類区分','999911'),
				'div_item_class_10'=>array('ページ 分類区分',''),
				'div_item_class_11'=>array('お知らせ 分類区分',''),
				'div_item_class_12'=>array('枠 分類区分',''),
				'div_item_class_13'=>array('混雑状況 分類区分',''),
				'div_item_class_14'=>array('順番待ち 分類区分',''),
				'div_item_class_15'=>array('アクティビティ 分類区分', '999915'),
				'div_item_area_15'=>array('アクティビティ エリア区分','999925'),
				'div_item_class_16'=>array('チケット 分類区分', '999915'),
				'div_item_area_16'=>array('チケット エリア区分','999925'),
				'div_item_class_17'=>array('レストラン 分類区分', '999920'),
				'div_item_area_17'=>array('レストラン エリア区分','999921'),
				'div_engage_class'=>array('MEMBERコード区分', '500000'),

				/* 外部システム連携の設定 */
				'group_separator8'=>array('外部連携設定',''),
				'navigation_url_name'=>array('Google Map連携用 ナビ文字列',''),
				'google_direction_type'=>array('Google Map連携用 デフォルト交通手段','transit'),
				'json_redmine'=>array('Redmine連携',''),
				//'json_moneyforward'=>array('MF請求書連携','{"partner_id":"", "department_id":""}'),
				'lang_cd_faq_modify_guard'=>array('FAQ編集監視言語',''),
				'txt_reserve_type'=>array('予約連携方法','reserve'),
				'json_reserve_settings'=>array('宿泊予約設定',''),
				'json_be_settings'=>array('予約エンジン連携設定',''),
				'reserve_remind'=> array('宿泊予約リマインド設定',''),
				'facebook_image_tag'=>array('Facebookコンテンツ写真ファイル名タグ',''),
				'txt_facebook_post_token'=>array('Facebook投稿用Token',''),
				// 'awl_lite'=>array('awl_lite連携',''),
				'json_payment_setting'=>array('決済方法設定',''),
				'json_payment_service'=>array('施設利用中決済方法',''),
				'json_receipt_setting' => array('領収書設定情報', '{"corporation":"","address":"","tel":"","regist_no":"","template":"","period":""}'),
				'json_pms_assist'=>array('ASSIST連携情報',''),
				'txt_slack_channel_url'=>array('SlackチャンネルURL',''),
				'json_slack_setting'=>array('Slack連携情報', ''),
				'json_kintone'=>array('kintone連携',''),
				'json_recaius_fieldvoice_setting'=>array('リカイアス連携',''),
				'json_lineworks_setting'=>array('LINE WORKS連携',''),
				'json_chatwork_setting'=>array('CHATWORK連携',''),
				'json_teams_setting'=>array('TEAMS連携',''),
				'json_google_chat_setting'=>array('GOOGLE CHAT連携',''),
				'json_contents_link_setting'=>array('CMSコンテンツAPI連携',''),
				'json_disaster_setting'=>array('防災クラウド連携',''),

				'json_mail_setting'=>array('送信メール設定','{"default":{"type":"ses","sender":"talkappi<<EMAIL>>"}}'),
				'google_analytics_tracking_id'=>array('GA トラッキングID',''),
				
				'json_sns_link_facebook'=>array('Facebook連携',
					'{"label":"talkappi","ref":"talkappi","sns_id":"158538017901814","webhook_url":"/","verify_token":"be253564982663e78aed2f33d2a8fcae","app_secret":"be253564982663e78aed2f33d2a8fcae","page_access_token":"EAAQTXRAH7ZBQBAPe7o0s0t95nZBfEEdBRkMatHNQXmp97rCfJZAVmQ7yGYsqaJybPXjxRggzN7ft3GsZB0VxWZAacesTnbFspzr3F1MsP231dQB8UZCOZBGwPId5ZCzwvhTHl2mGUZAod7U0jjKPUDtJOWHGFQcYRfS8XldmwPG2OZBwZDZD"}'),
				'json_sns_link_line'=>array('LINE連携',
					'{"label":"talkappi","sns_id":"talkappi","webhook_url":"/","channel_id":"1555004394","channel_secret":"ff79df3ee5f647b20e8bf778f9a162dc","channel_access_token":"0CeUTmfp8QUDFLy5Hsp6iIKWvK9xeE0GkIsHBFun7fm+HT5//DQylnQJsapJpkD/tfbSHrKOpyAGM5VVu2M+ga99+YGj91/S6miVjY2BLF+DKGyqKmLggJVVByfjuf6fwujbtD1JOvaFVwZdRxNvQwdB04t89/1O/w1cDnyilFU=","weblogin_channel_id":"1585977542","weblogin_channel_secret":"2ffa9f88bbcd2519db52d341f134d185"}'),
				'json_sns_link_wechat'=>array('WeChat連携',
					'{"label":"talkappi","sns_id":"wx250b263a55ba339f","webhook_url":"/wechat","admin_user_id":"gh_5593498baa4b","verify_token":"2020activalues","app_secret":"31d9b72b7b458ed9bc6c533b18de17e0","aes_key":"MeVv60tqj1E11dkJKrSz2ULMnqWGIaXT8tlWwMpuvpW"}'),
				'json_gpt_setting'=>array('ChatGPT連携','{"model":"gpt-4o-mini","api_key":"","limit":"30000","freetalk":"0"}'),
				/*
				'json_sns_link_facebook'=>array('SNS-Facebook接続情報',
						'{"label":"wuzhao bot","ref":"1995915364005084","sns_id":"1995915364005084","webhook_url":"/wubotapp","verify_token":"wubotapp123","app_secret":"dc321e99274cee086e2078e9b1421d49","page_access_token":"EAACq6OcHYiwBAI7Q3QAzCHZC6zj8lwn0O428mHLfb6WRuZBrSh9ANVWZCIkwVDTS7fd1F0aA1fmntJUmaLxOnv6WJFv6N4vOUqF0uFn7Pdd5pw9j7OzP95MVrrBWfw4aASnJwpJ5TZCoJ9tsYFwZB9AqJUBJ7xKkZAQSjcYf1iyQZDZD"}'),
				'json_sns_link_line'=>array('SNS-Line接続情報',
						'{"label":"woozo","sns_id":"ttq0866f","webhook_url":"/woozo","channel_id":"1571775313","channel_secret":"04a1e176f9a5db3545b1a930003d4f78","channel_access_token":"VEfpAUMUaT0FZjoipEp+/Svb6F1ZocXiqWEIvjxHEAZVXLtTQ4BaXN48i93ANC5ERTm21D+B19IpvcLB75d20+3AfXrEcL7VXX+VR93ksWV6Jl/Uo8fg5KH2hxaHpviXAI33yDWg1Qv07U4oZKUOXgdB04t89/1O/w1cDnyilFU=","weblogin_channel_id":"1585250942","weblogin_channel_secret":"fcf3e79f1b7dffe5183d77c3efd7c3e4"}'),
				'json_sns_link_wechat'=>array('SNS-Wechat接続情報',
						'{"label":"wuzhao测试号","sns_id":"wxbf9254b13bfe3b92","webhook_url":"/wechat","admin_user_id":"gh_04a9ff9517b2","verify_token":"wztest2000","app_secret":"7c7fc7b838a6411e9f2b15d542c6a28e","aes_key":"ptlx8Gnd85PZWzPfSOY96SO7Z6UFwYX9L7X2TyHQeNn"}'),
				*/

		)
);
