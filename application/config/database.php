<?php defined('SYSPATH') or die('No direct access allowed.');

return array
(
		'default' => array
		(
				'type'       => 'MySQLi',
				'connection' => array(
						/**
						 * The following options are available for MySQL:
						 *
						 * string   hostname     server hostname, or socket
						 * string   database     database name
						 * string   username     database username
						 * string   password     database password
						 * boolean  persistent   use persistent connections?
						 * array    variables    system variables as "key => value" pairs
						 *
						 * Ports and sockets may be appended to the hostname.
						 */
						
						'hostname'   => 'localhost',
						'database'   => 'talkbot',
						'username'   => 'root',
						'password'   => 'root',
						
						/*
						 'hostname'   => 'sqld.duapp.com:4050',
						 'database'   => 'RsZDJjBjPQzHUMwlJzJr',
						 'username'   => 'xNV0k8Tq2etBkLl6ZD2L9uY9',
						 'password'   => 'EQZE6Fvf5u6TGMh6mwxstBuY4ajb1GN9',
						 */
						'persistent' => FALSE,
				),
				'table_prefix' => '',
				'charset'      => 'utf8mb4',
				'caching'      => FALSE,
		),
		'slave' => array
		(
				'type'       => 'MySQLi',
				'connection' => array(
						/**
						 * The following options are available for MySQL:
						 *
						 * string   hostname     server hostname, or socket
						 * string   database     database name
						 * string   username     database username
						 * string   password     database password
						 * boolean  persistent   use persistent connections?
						 * array    variables    system variables as "key => value" pairs
						 *
						 * Ports and sockets may be appended to the hostname.
						 */
						
						'hostname'   => 'localhost',
						'database'   => 'talkbot',
						'username'   => 'root',
						'password'   => 'root',
						
						/*
						 'hostname'   => 'sqld.duapp.com:4050',
						 'database'   => 'RsZDJjBjPQzHUMwlJzJr',
						 'username'   => 'xNV0k8Tq2etBkLl6ZD2L9uY9',
						 'password'   => 'EQZE6Fvf5u6TGMh6mwxstBuY4ajb1GN9',
						 */
						'persistent' => FALSE,
				),
				'table_prefix' => '',
				'charset'      => 'utf8mb4',
				'caching'      => FALSE,
		),
);
