<?php defined('SYSPATH') or die('No direct access allowed.');

return array(
		'facebook_push_url' => 'https://graph.facebook.com/v3.2/me/messages?access_token=',
		'facebook_qrcode_url' => 'https://graph.facebook.com/v2.6/me/messenger_codes?access_token=',
		'line_push_url' => 'https://api.line.me/v2/bot/message/push',
		'line_richmenu_url' => 'https://api.line.me/v2/bot/richmenu',
		'line_richmenu_url_data' => 'https://api-data.line.me/v2/bot/richmenu',
		'line_followers_url' => 'https://api.line.me/v2/bot/insight/followers?date=',
		'line_message_url' => 'https://api.line.me/v2/bot/insight/message/delivery?date=',
		'line_quota_url' => 'https://api.line.me/v2/bot/message/quota',
		'line_consumption_url' => 'https://api.line.me/v2/bot/message/quota/consumption',
		'line_bot_url' => 'https://api.line.me/v2/bot/info',
		'wechat_token_url' => 'https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&',
		'wechat_push_url' => 'https://api.weixin.qq.com/cgi-bin/message/custom/send?access_token=',
		'wechat_qrcode_url' => 'https://api.weixin.qq.com/cgi-bin/qrcode/create?access_token=',
		'wechat_api_url' => 'https://api.weixin.qq.com/cgi-bin/',
		'holiday_api_url' => ['ja'=>'https://holidays-jp.github.io/api/v1/date.json'],

		'tl_config'=>['original_image_url'=>'https://test472.tl-lincoln.net/', 'mapping_image_url'=>'https://tl.talkappi.com:9087/'],

		'clear_log_days' => 10,
		'user_access_log' => true,
		'dashboard_real_upd' => false,
		'chat_max_record' => 40000,
		'redmine_translate'=>[
				'project_id' => 'translate',
				'tracker_id' => '',
				'assigned_to_id'=>117,
		],
		'redmine_translate_qa'=>[
			'project_id' => 'translate',
			'tracker_id' => '',
			'assigned_to_id'=>647,
		],
		'redmine_ai'=>[
				'project_id' => 'talkappi_bot_ai',
				'tracker_id' => '',
				'assigned_to_id'=>6,
		],
		'redmine_cs_sale_contents'=>[
				'project_id' => 'cs_sale_contents',
				'tracker_id' => '',
				'assigned_to_id'=>135,
		],
		'support_image_type' => ['jpg', 'jpeg', 'png', 'svg', 'gif', 'webp'],
		'support_file_type' => ['jpg', 'jpeg', 'png', 'svg', 'svg+xml', 'gif', 'webp', 'mp4','mov','pdf','ttf'],
		'support_mime_type' => ['jpg' => 'image/jpeg', 'svg' => 'image/svg+xml', 'pdf' => 'application/pdf', 'xls' => 'application/vnd.ms-excel', 'xlsx' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'doc' => 'application/msword', 'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document','ttf'=>'application/x-font-ttf'],
		// bot apply
		'link_valid_01' => '1 day',
		// order confirm
		'link_valid_02' => '30 minute',
		// password forget
		'link_valid_03' => '30 minute',
		'link_valid_90' => '365 day',

		'bot_tables' => [
				't_bot', 't_bot_intent', 't_bot_def_intent', 't_bot_setting', 't_bot_scene', 't_bot_busitime', 't_bot_context', 't_bot_class_code', 't_bot_keyword', 't_intent_skill',
				't_item', 't_item_description', 't_item_display', 't_item_keyword',
				't_product', 't_product_description',
				't_autoanswer_flow', 't_autoanswer_node', 't_autoanswer_action', 't_autoanswer_text', 
				't_bot_msg', 't_bot_msg_desc_txt', 't_bot_msg_desc_lst', 't_bot_msg_desc_img', 't_bot_msg_desc_car', 't_bot_msg_desc_tpl',
		],

		'language_iso_code' => [
			'cn' => 'zh-cn',
			'tw' => 'zh-tw',
			'kr' => 'ko',
		],
		// 'admin_support_lang' => ['ja'=>'日本語'],
		//多言語対応が完了するまでは、本番環境では、以下をコメントアウトする必要がある
		'admin_support_lang' => ['ja'=>'日本語', 'en'=>'English'],
		// VERY対応言語
		'very_support_lang' => [
            'ja', 'en', 'cn', 'tw', 'kr', 'it', 'fr', 'es', 'ru', 'de', 'pt', 'th',
            'vi', 'tl', 'ms', 'id', 'hi', 'mn', 'ar', 'km',
        ],
		'native_support_lang' => ["ja", "en", "cn", "tw", "kr"],
		'inquiry_settings' => ['template_bot_id'=>880],
);
