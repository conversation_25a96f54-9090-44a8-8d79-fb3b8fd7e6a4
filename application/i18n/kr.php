<?php defined('SYSPATH') OR die('No direct script access.');

return array(
		'html.title' =>'talkappi Booking',
		'index.findroom' => '객실 검색',
		'index.change' => '예약 변경',
		'index.range' => '숙박 기간',
		'index.checkin' => '체크인',
		'index.range_canot_change' => '변경 불가',
		'index.checkin_fix_label' => '',
		'index.checkin_nochange' => '체크인 날짜의 변경은 취소가 필요합니다.',
		'index.error_no_rooms' => '선택하신 날짜에는 잔여 객실이 없습니다.',
		'index.error_longterm' => '해당 플랜은 {longterm}박 이상 숙박이 필수입니다.',
		'index.error_max_longterm' => '해당 플랜은{longterm}박이상 숙박하실 수 없습니다.',
		'index.error_timeout' => '예기치 않은 오류가 발생했습니다.',
		'index.checkout' => '체크아웃',
		'index.rooms' => '객실 수',
		'index.adults' => '어른   ',
		'index.kids' => '어린이',
		'index.smoking' => '흡연',
		'index.nosmoking' => '금연',
		'index.bothsmoking' => '지정 없음',
		'index.fix_nosmoking' => '※전 객실 금연입니다.',
		'index.fix_smoking' => '※전 객실 흡연입니다.',
		'index.search' => '객실 선택',
		'index.close' => '닫기',
		'index.input' => '예약자 정보 입력',
		'index.customer' => '예약자 정보 입력',
		'index.name' => '이름(영문)',
		'index.name_mark' => '',
		'index.name_placeholder' => '예: John Kim',
		'index.name_kana' => '이름',
		'index.tel' => '전화번호',
		'index.tel_mark' => '',
		'index.tel_placeholder' => '예: +82-55-123-4567',
		'index.mail' => '메일',
		'index.mail_mark' => '',
		'index.mail_mark_dtl' => '입력하신 메일주소로 예약 완료 메일을 송신합니다.',
		'index.mail_placeholder' => '예: <EMAIL>',
		'index.remark' => '기타 리퀘스트',
		'index.confirm' => '예약을 확정',
		'index.save' => '보존',
		'index.notes' => '상세',
		'index.must' => '＊',
		'index.kids_age' =>'번째 자제 분의 연령(살)',
		'index.rooms_v2' => '객실',
		'index.adults_v2' => '어른',
		'index.kids_v2' => '어린이',
		'index.number_unit' => '명',
		'index.calendar_month' => '',
		'index.calendar_day' => '',
		'index.calendar_split' => '～',
		'index.apply' => '적용한다',
		'index.step' => '앞으로、2단계로 예약 완료',
		'notes.title'=>'취소 정책',
		'notes.hotel_info'=>'호텔 정보',
		'complete.title'=>'예약 결과',
		'complete.agent'=>'talkappi예약 결과',
		'complete.success_no' =>'예약번호',
		'complete.success' =>'예약이 확정되었습니다. 등록된 메일 앞으로 예약 확정 메일이 발송되었으니 확인 바랍니다.',
		'complete.fail_no' =>'에러 코드',
		'complete.fail' =>'예약이 확정되지 않았습니다. 죄송하지만 다시 한번 예약 절차를 진행해 주시기 바랍니다.',
		'error.invalid_access' =>'액세스가 불가합니다.',
		'error.exception' =>'이상이 발견되었습니다.',
		'index.error_name' => '한글로 입력해주시기 바랍니다.',
		'index.error_tel' => '유효한 전화번호를 입력해주시기 바랍니다.',
		'index.error_mail' => '유효한 메일주소를 입력해주시기 바랍니다.',
		'index.error_required' => '답변을 입력하세요.',
		'index.plan_detail' => '자세한 내용은 숙박 플랜보기',
		'index.amount' => '세금포함',
		'index.free_cancel_title' => '무료 캔슬 가능',
		'index.free_cancel_deadline' => '체크인 3 일전까지 취소 수수료 무료',
		'index.free_cancel' => '{date} {time} (도쿄 표준시)까지 무료 취소',
		'index.free_cancel_title_1' => '오늘부터 캔슬요금 발생',
		'index.free_cancel_1' => '자세한 사항은 캔슬 규정을 확인해주시기 바랍니다.',
		'index.free_cancel_title_2' => '캔슬 불가',
		'index.free_cancel_2' => '자세한 사항은 캔슬 규정을 확인해주시깁 바랍니다.',
		'index.privacy' => '이후의 예약을 위해, 이 정보를 보존한다.',
		'index.coupon' => '쿠폰',
		'index.coupon_count' => ' {num}장 가지고 있습니다.',
		'index.ignore_coupon' => '이용하지 않는다.',
		'index.confirmation' => '확인 사항',
		'index.confirmation_desc' => '당신보다 나은 서비스를 제공하기 위해 다음 질문의 답변을 부탁드립니다.',
		
		'index.amount.title' => '가격명세',
		'index.amount.detail' => '명세',
		'index.amount.calculating' => '',
		'index.pay.title' => '결제 방법을 선택',
		'index.pay.credit' => '온라인 신용 카드 결제',
		'index.pay.onsite' => '현지 결제',
		'index.pay' => '결제로',
		'index.adults_v2.male' => '성인 (남성)',
		'index.adults_v2.female' => '성인 (여성)',
		'index.error' => '오류',
		
		'index.link' => '법인회원 연계',
        'index.link_1' => '유저 인증',
        'index.link_2' => '법인회원 정보 확인',
        'index.link_forgot_pass' => '회원No.・비밀번호를 잊어버리신 분',
        'index.link_corp_name' => '{bot_name}대상 시설',
        'index.link_auth_desc' => '<b>등록하신 이메일 주소로 인증 코드를 보냈습니다.</b><br/>인증 코드를 입력하며 "예약 완료"를 누르십시오. <br/><br/>※인증 메일이 도착하지 않는 경우는 스팸 메일 폴더에 자동으로 배분되어 있을 가능성이 있으므로, 확인해 주시길 부탁드리겠습니다.',
        'index.link_auth_title' => '인증 코드 입력',
        'index.link_auth' => '메일로 송신하신 인증코드 (6자리)',
        'index.link_corp_desc' => '처음 이용하시는 분은、고객님의 정보 등록이 필요합니다.법인 회원이신 분은、하단의「법인 회원 No.」、「법인 비밀번호」를 입력하신 후에、「연계하기」버튼을 눌러주세요. ',
        'index.link_confirm' => '버희 호텔은{bot_name}의 대상시설이 있습니다.<br/>법인 회원정보를 입력하신 후 예약 부탁드립니다.',
        'index.link_title'=>'법인회원',
        'index.link_title_tip'=>'회원No・비밀번호를 잊어버리신 분',
        'index.link_corp_no'=>'법인 회원 No.',
        'index.link_password'=>'법인 비밀번호',
        'index.button.link'=>'연계하기',
        'index.link_success'=>'법인 정보가 확인되었습니다.',
        'index.link_fail'=>'법인 정보가 확인되지 않았습니다.',
        'index.link_member_title'=>'회원 상세 정보 등록',
        'index.link_member_gender'=>'성별',
        'index.link_member_birthday'=>'생년월일',
        'index.link_member_address'=>'주소',
        'index.link_member_postcode'=>'우편번호',
        'index.link_member_address1'=>'도도부현',
        'index.link_member_address2'=>'시구촌손',
        'index.link_member_address3'=>'번지',
        'index.link_member_address4'=>'건물명・방 번호(임의)',
        'index.link_member_mobile'=>'전화번호',
        'index.link_member_corp_title'=>'회원 회사 정보',
        'index.link_member_company'=>'근무처',
        'index.link_member_department'=>'소속',
        'index.link_member_company_tel'=>'근무처 전화번호',
        'index.link_member_privacy'=>'<a href="{privacy_url}" target="_blank">이용약관과 개인정보 사용 방침</a>에 동의한다',
        'index.button_link_reserve'=>'예약완료 페이지로',
        'index.label.link_modify'=>'변경',
        'index.error.link.input'=>'정확하게 입력해주세요',
        'index.error.link.input.footer'=>'입력해주신 내용에 틀린 부분이 있습니다.',

		'booking.template.title'=>'talkappi BOOKING',
		'booking.template.login'=> '로그인／가입',
		'booking.template.footer.discovery'=>'더 찾아보기',
		'booking.template.footer.news'=>'뉴스',
		'booking.template.footer.magzine'=>'메일 뉴스레터',
		'booking.template.footer.about'=>'ttalkappi에 대해',
		'booking.template.footer.aboutdtl'=>'여행을 통해 숙박시설과 여행자를 연결합니다.',
		'booking.template.step.1'=>'객실 선택',
		'booking.template.step.2'=>'고객 정보 입력 ',
		'booking.template.step.3'=>'예약 내용 확인 ',
		'booking.template.step.4'=>'입력 완료 ',
		'booking.template.button.detail'=>'상세 내용',
		'booking.template.button.select'=>'선택',
		'booking.template.button.back'=>'뒤로가기',
		'booking.template.label.comment'=>'후기',
		'booking.template.label.period'=>'투숙 기간',
		'booking.template.label.checkin'=>'체크인',
		'booking.template.label.checkin.s'=>'체크인',
		'booking.template.label.checkout'=>'체크아웃',
		'booking.template.label.checkout.s'=>'체크아웃',
		'booking.template.label.days'=>'박',
		'booking.template.label.pcount'=>'명',
		'booking.template.label.reserve.info'=>'예약 정보',
		'booking.template.label.reserve.infodtl'=>'예약 상세 정보',
		'booking.template.label.reserve.userinfo'=>'예약자 정보',
		'booking.template.label.rooms'=>'객실 수 ',
		'booking.template.label.adults'=>'성인/실',
		'booking.template.label.kids'=>'어린이',
		'booking.template.label.more'=>'더보기',
		'booking.template.label.price'=>'1박 요금',
		'booking.template.label.roomtype'=>'객실 타입',
		'booking.template.label.plan'=>'숙박 플랜',
		'booking.template.label.hotel.detail'=>'호텔',
		'booking.template.label.term'=>'이용약관',
		'booking.input.label.agree'=>'동의 후 예약 진행',
		'booking.input.label.title'=>'예약자 정보 입력',
		'booking.input.label.lastname'=>'성',
		'booking.input.label.firstname'=>'이름',
		'booking.input.label.mail'=>'메일 주소',
		'booking.input.label.vmail'=>'메일 주소 (확인)',
		'booking.input.label.phone'=>'전화번호',
		'booking.input.button.confirm'=>'예약 내용 확인',
		'booking.complete.title'=>'예약해 주셔서 감사합니다.',
		'booking.roomtype.button.find'=>'검색하기',
		'booking.roomtype.label.result'=>'검색 결과 (객실 타입) ',
		'booking.plan.label.title'=>'숙박 플랜 선택',
	
		'faq.button.home'=>'HP',
		'faq.button.switch'=>'전환', //翻訳必要
		'faq.title.category'=>'카테고리 선택',
		'faq.title.top'=>'자주 묻는 질문',
		'faq.title.keywords'=>'자주 사용되는 단어로 검색',
		'faq.title.faq_ai'=>'AI의 추천 질문',
		'faq.title.faq_most'=>'자주 있는 질문',
		'faq.title.faq_result'=>'로 검색 한 결과',
		'faq.title.faq_tag'=>'로 검색 한 결과',
		'faq.title.no_result'=>'검색 결과가 발견되지 않았습니다',
		'faq.title.faq_category'=>'의 FAQ',
		'faq.title.faq_relation'=>'관련 질문',
		'faq.title.category_search'=>'카테고리 선택',
		'faq.title.contents'=>'추천 정보를 발견했습니다',
		'faq.placeholder.keyword'=>'질문을 입력하세요.',
		'faq.button.search'=>'검색',
		'faq.title.contact'=>'※FAQ에 없는 질문은 여기서 물어보세요.',
		'faq.button.contact'=>'문의→',
		'faq.button.up'=>'TOP으로',
		'faq.title.page'=>'페이지',
		'faq.title.page.total'=>'건 중 ',
		'faq.title.page.range'=>'건 표시',
		
		'faq.title.fontsize'=>'글자 사이즈',
		'faq.title.close'=>'닫기',
		'faq.button.fontsize.large'=>'대',
		'faq.button.fontsize.normal'=>'중',
		'faq.button.fontsize.small'=>'소',
		'faq.title.detail'=>'상세한 내용은 여기를 확인 바랍니다.',
		'faq.title.detail2'=>'각 호텔의 정보를 알려드립니다.<br/>자세한 내용은 [자세히] 버튼을 눌러 확인해 주세요.',
		'faq.button.detail'=>'상세내용 확인',
		'faq.title.survey1'=>'FAQ 개선을 위해서 고객님의 의견을 묻고있습니다.',
		'faq.title.survey2'=>'해당 답변은 도움이 되었습니까?',
		'faq.title.survey_fin'=>'앙케이트에 협력해 주셔서 감사합니다.',
		'faq.title.survey_input_1'=>'고객님의 의견을 말씀해주세요.',
		'faq.title.survey_input_2'=>'문의는 답변되지 않습니다. 주의 바랍니다.',
		'faq.button.back'=>'뒤로가기',
		'faq.button.survey.yes'=>'네',
		'faq.button.survey.no'=>'아니요',
		'faq.button.survey.send'=>'보내기',
		'faq.label.left_bracket'=>'"',
		'faq.label.right_bracket'=>'"',
		'faq.button.show_inquiry'=>'문의 양식으로',
		'faq.text.show_inquiry'=>'찾으시는 질문이 없는 경우에는 여기로 문의 부탁드립니다.',
		
		'survey.html.title'=>'talkappi앙케이트',
        'survey.index.title'=>'앙케이트',
		'survey.index.title.name'=>'アンケート名',
        'survey.index.label.content'=>'개요',
		'survey.index.label.content.name'=>'アンケート概要',
        'survey.index.label.period'=>'리서치 기간',
        'survey.index.label.count'=>'대답 횟수',
        'survey.index.label.count.content'=>'최대{count}문',
        'survey.index.label.duration'=>'소요 시간',
        'survey.index.label.duration.content'=>'{duration}분정도',
        'survey.index.label.present'=>'선물',
        'survey.index.label.remark'=>'이 리서치는 계정 운영자가 실시하고 있습니다. 계정 운영자는 개인을 특정할 수 없는 형태로 답변 결과만을 확인합니다.',
        'survey.index.label.expired'=>'이 리서치는 종료합니다.',
		'survey.index.label.limit'=>'정말 죄송하지만 답변수가 상한에 이르렀으므로 신청이 마감됐습니다.',
        'survey.index.button.answer'=>'답변하기',
        'survey.input.label.must'=>'＊ 필수',
        'survey.input.button.send'=>'전송',
		'survey.input.button.prev'=>'이전',
		'survey.input.button.next'=>'다음',
		'survey.input.text.length'=>'{min_length} ~ {max_length}자로 입력해주세요.',
		'survey.input.text.max_length'=>'{max_length}자 이내로 입력해주세요.',
		'survey.input.text.min_length'=>'{min_length}자 이상으로 입력해주세요.',
		'survey.input.text.tel'=>'080-1234-5678(하이폰 생략 가능)',
		'survey.input.text.address'=>'123-4567(하이폰 생략 가능)',
		'survey.input.text.num'=>'숫자로 입력해주세요',
		'survey.input.text.num.dash'=>'숫자로 입력해주세요(하이폰 생략 가능)',
		'survey.input.text.email'=>'메일 주소를 입력 해주세요.',
		'survey.input.text.email.confirm'=>'(확인용)',
		'survey.input.text.email.confirm.error'=>'메일 주소가 일치하지 않습니다',
		'survey.input.text.mtx.error'=>'각 열에 입력해 주십시오.',
		'survey.input.text.mtx.error'=>'각 열에 대답해 주시기 바랍니다.',
		'survey.input.text.mtx.chk.message'=>'각 행에서 최소 {max_min}개씩 선택해 주십시오.',
		'survey.input.text.chk.message'=>'최소 {max_min}개 이상 선택해 주십시오.',
		'survey.input.text.chk.error'=>'지정된 갯수를 확인해 주십시오.',
		'survey.input.text.mtx.chk.min'=>'최소 {min}개',
		'survey.input.text.mtx.chk.max'=>'최대 {max}개',
		'survey.input.text.mtx.chk.num'=>'{num}개',
		'survey.input.text.date'=>'입력 예시에 따라 입력 해주세요.',
		'survey.input.text.fup'=>'파일 선택',
		'survey.input.text.sel'=>'선택해주세요.',
		'survey.input.label.required.not.input'=>'대답하지 않은 항목이 있습니다. 확인해주세요.',
		'survey.input.label.required.not.input.eachquestion'=>'이 질문은 필수 항목입니다',
        'survey.complete.title'=>'답변완료',
        'survey.complete.label.thanks'=>'바쁘신 와중에 답변해주셔서.<br> 대단히 감사합니다',
		'survey.complete.label.unusable'=>"해당 쿠폰의 유효 기간이 만료했으므로 사용하실 수 없습니다.<br>사용 기간:{start_date}~{end_date}<br>「쿠폰 저장 및 송신」 버튼에서 고객님의 메일 주소로 쿠폰을 송신해 주시기 바랍니다.",
        'survey.complete.button.coupon'=>'쿠폰 보기',
        'survey.complete.button.close'=>'채팅으로 돌아가기',
		'survey.complete.button.closewindow'=>'닫기',
        'survey.coupon.label.period1'=>'남은 {days}날짜',
        'survey.coupon.label.period2'=>'이용 마감일: {date}',
		'survey.coupon.label.period3'=>'이용 시작일: {date}',
		'survey.coupon.label.period4'=>'사용 기한：쿠폰 발행일로부터 {date}일 후까지<br>※사용 기한 내에 쿠폰을 클릭하면 발행됩니다.',
		'survey.coupon.label.period5'=>'사용 기한: 오늘부터 {date}일 후까지',
        'survey.coupon.label.present'=>'감사의 선물・쿠폰',
        'survey.coupon.label.remark'=>'쿠폰의 유효기간 일시는 UTC+09:00를 기준으로 표기합니다.',
        'survey.coupon.button.use'=>'이 쿠폰을 사용하기',
        'survey.coupon.dialog.title'=>'쿠폰을 사용하시겠습니까',
        'survey.coupon.dialog.content'=>'「네」를 선택하시면 쿠폰의 사용상태가 갱신됩니다. 결제전에 반드시 스태프에게 이 화면을 확인해주세요.',
        'survey.coupon.label.expired'=>'이 쿠폰은 유효기간이 지났습니다',
		'survey.coupon.label.notstart'=>'해당 쿠폰의 사용 기간이 아니므로 사용하실 수 없습니다.<br>사용 기간 내에 다시 한 번 접속해 주시기 바랍니다.',
        'survey.coupon.label.used'=>'이 쿠폰은 사용되었습니다.',
        'survey.coupon.label.maxused'=>'이 쿠폰은 이용상한에 달하였습니다',
        'survey.coupon.label.canotuse'=>'손님의 쿠폰이 아닙니다',
		'survey.coupon.label.issue'=>'발행 매수가 상한에 도달했기 때문에, 쿠폰 제공을 종료합니다.',
		'survey.coupon.expire.after.issue'=>'발행일로부터 {date}일 후까지',
		'survey.common.label.required'=>'필수',
		'survey.common.label.input'=>'입력해주세요',
		'survey.common.label.other'=>'그 외',
		'survey.common.label.other.input'=>'입력해주세요',
		'survey.common.label.other.input.required'=>'(필수)',
		'survey.common.label.other.input.optional'=>'',
		
		'inquiry.html.title'=>'talkappi앙케이트',
        'inquiry.index.title'=>'앙케이트',
        'inquiry.index.label.content'=>'개요',
        'inquiry.index.label.period'=>'실시 기간',
        'inquiry.index.label.count'=>'대답 횟수',
        'inquiry.index.label.count.content'=>'최대{count}문',
        'inquiry.index.label.duration'=>'소요 시간',
        'inquiry.index.label.duration.content'=>'{duration}분정도',
        'inquiry.index.label.present'=>'선물',
        'inquiry.index.label.remark'=>'이 리서치는 계정 운영자가 실시하고 있습니다. 계정 운영자는 개인을 특정할 수 없는 형태로 답변 결과만을 확인합니다.',
		'inquiry.index.label.invalid.title'=>'실행 제한',
		'inquiry.index.label.invalid.description'=>'죄송합니다.<br>이 폼은 실시기간중이 아니기때문에,<br>접수 할수없습니다',
		'inquiry.index.label.versionup.description'=>'불편을 끼쳐 드려 죄송합니다.<br>이 폼의 유효기간이 만료됐습니다.<br>10초 흐에 최신 폼으로 이동합니다.<br>번거로우시겠지만 새로운 폼으로 이동 후에 다시 한 번 입력해 주십시오.',
		'inquiry.index.label.inquiry_answer_limit.description'=>'신청 인원수 상한에 도달했으므로 접수를 종료합디다.',
		'inquiry.index.label.answer_limit.description'=>'이미 신청하신 이력이 있습니다.<br>협력해 주셔서 감사합니다.',
		'inquiry.index.button.answer'=>'답변하기',
        'inquiry.input.label.must'=>'＊ 필수',
        'inquiry.input.button.send'=>'전송',
		'inquiry.input.button.prev'=>'수정하기', 
		'inquiry.input.button.pay'=>'支払いへ',
		'inquiry.input.button.back'=>'前へ',
		'inquiry.input.button.confirm'=>'확인 화면으로',
		'inquiry.input.button.modify'=>'예약 변경',
		'inquiry.input.button.cancel'=>'예약 캔슬',
		'inquiry.modify.error'=>'갱신 가능한 기간은 지났습니다. 시설에 직접 문의해 주세요.',
		'inquiry.cancel.error.check'=>'캔슬 가능한 기간은 지났습니다. 시설에 직접 문의해 주세요.',
		'inquiry.cancel.error.canceled'=>'이미 취소되었습니다.',
		'inquiry.cancel.complete'=>'예약은 취소되었습니다.',
		'inquiry.cancel.complete.desc'=>'예약시 등록해 주신 메일 주소에 상세 정보를 송신했으니 확인해 주십시오. <br>이용해 주셔서 감사합니다. ',
		'inquiry.input.text.length'=>'{min_length} ~ {max_length}자로 입력해주세요.',
		'inquiry.input.text.max_length'=>'{max_length}자 이내로 입력해주세요.',
		'inquiry.input.text.min_length'=>'{min_length}자 이상으로 입력해주세요.',
        'inquiry.input.text.num.minmax'=>'{min}~{max} 내에서 입력해 주세요',        
        'inquiry.input.text.num.max'=>'{max} 이하을 입력해 주세요',
        'inquiry.input.text.num.min'=>'{min} 이상을 입력해 주세요',
		'inquiry.input.text.tel'=>'080-1234-5678(하이폰 생략 가능)',
		'inquiry.input.text.address'=>'123-4567(하이폰 생략 가능)',
		'inquiry.input.text.num'=>'숫자로 입력해주세요',
		'inquiry.input.text.num.dash'=>'숫자로 입력해주세요(하이폰 생략 가능)',
		'inquiry.input.text.email'=>'메일 주소를 입력 해주세요.',
		'inquiry.input.text.email.confirm'=>'(확인용)',
		'inquiry.input.text.email.confirm.error'=>'메일 주소가 일치하지 않습니다',
		'inquiry.input.text.clock'=>'시간을 정확하게 입력하세요.',
		'inquiry.input.text.date'=>'입력 예시에 따라 입력 해주세요.',
		'inquiry.input.text.fup'=>'파일 선택',
		'inquiry.input.text.sel'=>'선택해주세요.',
		'inquiry.input.text.other.confirm'=>'그 외의 기입',
		'inquiry.input.text.txt.date'=>'일자',
		'inquiry.input.text.txt.time'=>'시간대',
		'inquiry.input.spl.address.roomNo'=>'(자유)', 
		'inquiry.input.spl.address.country.label'=>'국가/지역',
		'inquiry.input.spl.address.postcode.label'=>'우편번호',
		'inquiry.input.spl.address.prefecture.label'=>'시도',
		'inquiry.input.spl.address.city.label'=>'시군구/읍면',
		'inquiry.input.spl.address.street.label'=>'도로명/건물번호',
		'inquiry.input.spl.address.room.label'=>'상세 주소(동호수)',
		'inquiry.input.spl.name.full'=>'성함',
		'inquiry.input.spl.name.first'=>'이름',
		'inquiry.input.spl.name.last'=>'성',
		'inquiry.input.price.tax'=>'(부가세 포함)',
		'inquiry.input.price.tax-service'=>'(세금・포함)',
		'inquiry.input.label.required.not.input'=>'대답하지 않은 항목이 있습니다. 확인해주세요.',
		'inquiry.input.label.required.not.input.eachquestion'=>'이 질문은 필수 항목입니다',
		'inquiry.input.transition.error'=>'브라우저의 자동 번역 기능을 사용하는 경우 양식의 일부 내용이 제대로 표시되지 않을 수 있습니다. 자동 번역 기능을 OFF로 한 후 이용해 주십시오.',
		'inquiry.input.message.no-pay-reserve'=>'決済が完了していない予約があります。決済画面で決済を完了してください。確保した予約枠を解放して予約し直す場合、「最初から予約」	を選択してください。',
		'inquiry.input.button.cancel-no-pay-reserve'=>'最初から予約',
		'inquiry.input.button.continue-no-pay-reserve'=>'予約再開',
		'inquiry.input.button.close'=>'閉じる',
        'inquiry.complete.title'=>'답변완료',
        'inquiry.complete.label.thanks'=>'바쁘신 와중에 답변해주셔서.<br> 대단히 감사합니다',
        'inquiry.complete.label.period'=>'유효 기간：{date}까지',
        'inquiry.complete.button.coupon'=>'감사의 쿠폰 보기',
        'inquiry.complete.button.close'=>'채팅으로 돌아가기',
		'inquiry.complete.button.closewindow'=>'닫기',
		'inquiry.complete.error.overtime_title'=>'予約失敗',
		'inquiry.complete.error.overtime'=>'決済は20分以内完了していないため、予約内容は自動的にキャンセルされました。',
		'inquiry.complete.button.input'=>'予約ページに戻る',
		'inquiry.complete.common.title'=>'접수 완료했습니다',
		'inquiry.complete.common.description'=>'문의해 주셔서 감사합니다. 보통 3영업일 이내에 회신하겠습니다.',
        'inquiry.coupon.label.period1'=>'남은 {days}날짜',
        'inquiry.coupon.label.period2'=>'{date} 까지 이용 가능',
        'inquiry.coupon.label.present'=>'감사의 선물・쿠폰',
        'inquiry.coupon.label.remark'=>'쿠폰의 유효기간 일시는 UTC+09:00를 기준으로 표기합니다.',
        'inquiry.coupon.button.use'=>'이 쿠폰을 사용하기',
        'inquiry.coupon.dialog.title'=>'쿠폰을 사용하시겠습니까',
        'inquiry.coupon.dialog.content'=>'「네」를 선택하시면 쿠폰의 사용상태가 갱신됩니다. 결제전에 반드시 스태프에게 이 화면을 확인해주세요.',
        'inquiry.coupon.label.expired'=>'이 쿠폰은 유효기간이 지났습니다',
        'inquiry.coupon.label.notstart'=>'이용전의 쿠폰입니다',
        'inquiry.coupon.label.used'=>'이 쿠폰은 사용되었습니다.',
        'inquiry.coupon.label.maxused'=>'이 쿠폰은 이용상한에 달하였습니다',
        'inquiry.coupon.label.canotuse'=>'손님의 쿠폰이 아닙니다',
		'inquiry.common.label.required'=>'필수',
		'inquiry.common.label.input'=>'입력해주세요',
		'inquiry.common.label.other'=>'그 외',
		'inquiry.common.label.other.input'=>'입력해주세요',
		'inquiry.common.label.other.input.error'=>'그 외의 상세를 입력해주세요',
		'inquiry.common.label.limit.num.error'=>'예약 가능한 예약 수 {unit}를 초과하므로 수량을 다시 선택하십시오.',
		'inquiry.common.label.limit.num.min.error'=>'ご予約可能な予約数{unit}を下回っているため、数量を選択し直してください。',
		'inquiry.common.label.reserve.alone.error'=>'선택한 종류는 단독으로 예약할 수 없습니다.',
		'inquiry.common.label.other.input.required'=>'(필수)',
		'inquiry.common.label.other.input.optional'=>'',
		'inquiry.common.tab.step1'=>'폼 입력',
		'inquiry.common.tab.step2'=>'입력 내용 확인',
		'inquiry.common.tab.step2.payment'=>'확인&결제',
		'inquiry.common.tab.step3'=>'송신 완료',
		'inquiry.common.complete.title'=>'접수 완료 되었습니다',
		'inquiry.common.complete.urlbtn'=>'HP로 돌아가가기',
		'inquiry.login.title' => '한정 공개',
		'inquiry.login.description' => '비밀 번호를 입력해주세요',
		'inquiry.login.error' => '정확한 비밀번호를 입력해주세요',
		'inquiry.login.placeholder' => '비밀번호',
		'inquiry.login.button' => '로그인',
		'inquiry.faq.button' => '자주 있는 질문',
		'inquiry.label.num' => '数量',
		'inquiry.label.close' => '閉じる',
		'inquiry.label.expand' => '展開する',
		'inquiry.button.coupon_apply' => '適用',
		
		'coupon.dialog.remark'=>'메일이 도착하지 않은 경우, 스팸 메일 폴더를 확인, 보안설정 조정 후 다시 한 번 시도해주십시오.',
		'coupon.dialog.title'=>'쿠폰 저장 및 송신',
		'coupon.dialog.content.link'=>'쿠폰 링크를 복사 후 공유',
		'coupon.dialog.copy'=>'복사',
		'coupon.dialog.content.send'=>'쿠폰을 메일로 송신',
		'coupon.dialog.mail'=>'메일 주소 입력',
		'coupon.dialog.button'=>'송신',
		'coupon.dialog.mail.success'=>'송신을 완료했습니다.',
		'coupon.dialog.mail.fail'=>'메일 주소가 올바르지 않습니다.',
		'coupon.button.yes' => '쿠폰 적용',
		'coupon.button.no' => '쿠폰 적용 해제',
		'coupon.dialog.use.store' => '사용처',
		'coupon.dialog.use.title'=>'この画面を<br>お店の方に提示してください。',
		'coupon.dialog.use.staff' => '＜店舗スタッフ様＞',
		'coupon.dialog.use.staff.remark' => '「使用する」を押すと、クーポンが使用済みの
		ステータスになってしまい、元に戻せません。
		店舗スタッフ様が「使用する」ボタンを押して、
		クーポン使用となります。',
		'coupon.facility.choose' => '시설 선택',
		'coupon.dialog.copy.success'=>'쿠폰 링크 주소가 복사되었습니다.',
		'coupon.label.all.maxused'=>'このクーポンは利用上限枚数に達しているため、利用できません。',

		'common.error.front.title' => '에러가 발생했습니다. 불편을 끼쳐 드려 죄송합니다.',
		'common.error.front.sub_title' => '조금 뒤에 다시 한 번 접속해 주시기 바랍니다.',
	);
