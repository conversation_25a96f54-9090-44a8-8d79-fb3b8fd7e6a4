<?php defined('SYSPATH') OR die('No direct script access.');

return array(
		'html.title' =>'talkappi Booking',
		'index.findroom' => '查询房间',
		'index.change' => '修改预订',
		'index.range' => '入住日期-退房日期',
		'index.range_canot_change' => '修改不可',
		'index.checkin' => '入住日期',
		'index.checkin_fix_label' => '',
		'index.checkin_nochange' => '只有取消预订才能更改入住日期。',
		'index.error_no_rooms' => '你搜索的日期很抢手哦！这家住宿已无空房。',
		'index.error_longterm' => '该套餐需要连续入住{longterm}晚以上。 ',
		'index.error_max_longterm' => '该套餐不支持{longterm}晚以上的住宿。',
		'index.error_timeout' => '发生了一个意外的错误。',
		'index.checkout' => '退房日期',
		'index.rooms' => '房间数',
		'index.adults' => '成人／室',
		'index.kids' => '儿童',
		'index.smoking' => '吸烟',
		'index.nosmoking' => '禁烟',
		'index.bothsmoking' => '全部',
		'index.fix_nosmoking' => '※全部房间均为禁烟房间。',
		'index.fix_smoking' => '※全部房间均为可吸烟房间。',
		'index.search' => '选择房间',
		'index.close' => '关闭',
		'index.input' => '预订信息',
		'index.customer' => '预订人信息',
		'index.name' => '姓名（英文）',
		'index.name_mark' => '',
		'index.name_placeholder' => '例: Wang Ming',
		'index.name_kana' => '拼音',
		'index.tel' => '联系电话',
		'index.tel_mark' => '',
		'index.tel_placeholder' => '例: +86-01-1234-5678',
		'index.mail' => '电子邮件',
		'index.mail_mark' => '',
		'index.mail_mark_dtl' => '预订完成的邮件会发至您输入的电子邮箱。',
		'index.mail_placeholder' => '例: <EMAIL>',
		'index.remark' => '备注',
		'index.confirm' => '确认预定',
		'index.save' => '保存',
		'index.notes' => '详情',
		'index.must' => '＊',
		'index.step' => '再两步骤预约即将完成',
		'index.kids_age' =>'孩童年龄(岁)',
		'index.rooms_v2' => '客房',
		'index.adults_v2' => '大人',
		'index.kids_v2' => '小孩',
		'index.number_unit' => '人',
		'index.calendar_month' => '',
		'index.calendar_day' => '',
		'index.calendar_split' => '～',
		'index.apply' => '适用',
		'notes.title'=>'取消政策',
		'notes.hotel_info'=>'酒店信息',
		'complete.title'=>'预订结果',
		'complete.agent'=>'talkappi预订结果',
		'complete.success_no' =>'订单编号',
		'complete.success' =>'预订成功。订单内容已经发送到您指定的电子邮箱，请确认。',
		'complete.fail_no' =>'错误编号',
		'complete.fail' =>'对不起，您的预订失败，请重新查询预定。',
		'error.invalid_access' =>'未经授权的访问。',
		'error.exception' =>'出错了。',
		'index.error_name' => '请输入姓名拼音',
		'index.error_tel' => '请输入有效电话号码',
		'index.error_mail' => '请输入有效电子信箱',
		'index.error_required' => '请输入问题的回答',
		'index.plan_detail' => '查看此套餐的详细信息',
		'index.amount' => '含消费税',
		'index.free_cancel_title' => '免费取消',
		'index.free_cancel_deadline' => '入住日期的{days}天之前可以免费取消',
		'index.free_cancel' => '{date} {time}（东京时间）之前可以免费取消',
		'index.free_cancel_title_1' => '从今日开始将产生取消费用',
		'index.free_cancel_1' => '详情请确认取消政策。',
		'index.free_cancel_title_2' => '不可取消',
		'index.free_cancel_2' => '详情请确认取消政策。',
		'index.privacy' => '保存信息，供今后的预订使用。',
		'index.coupon' => '优惠券',
		'index.coupon_count' => ' 您持有{num}枚',
		'index.ignore_coupon' => '不使用优惠券',
		'index.confirmation' => '确认事项',
		'index.confirmation_desc' => '为了更好的为您服务，请回答下吗的问题。',
		
		'index.amount.title' => '合计明细',
		'index.amount.detail' => '明细',
		'index.amount.calculating' => '',
		'index.pay.title' => '支付方式',
		'index.pay.credit' => '信用卡支付',
		'index.pay.onsite' => '现地支付',
		'index.pay' => '立即支付',
		'index.adults_v2.male' => '成人（男）',
		'index.adults_v2.female' => '成人（女）',
		'index.error' => '错误',

		'index.link' => '企业会员登录',
        'index.link_1' => '用户认证',
        'index.link_2' => '企业会员信息确认',
        'index.link_forgot_pass' => '忘记会员账号・密码',
        'index.link_corp_name' => '{bot_name}适用对象设施',
        'index.link_auth_desc' =>  '<b>已发送认证码至登录信箱</b><br/>请输入认证码，并按下“预约完成”。<br/><br/>※若没有收到验证信，有可能被自动归类到垃圾信件，请先行确认。',
        'index.link_auth_title' => '输入验证码',
        'index.link_auth' => '您收到的验证码(6位数)',
        'index.link_corp_desc' => '初次使用时，需填写个人基本信息。企业会员用户请输入「法人会员No.」、「法人密码」、点击「会员登录」按钮。',
        'index.link_confirm' => '本酒店为bot_name}适用对象设施。 <br/>请确认企业会员信息后再预约。',
        'index.link_title'=>'企业会员',
        'index.link_title_tip'=>'忘记会员账号・密码',
        'index.link_corp_no'=>'企业会员账号',
        'index.link_password'=>'企业会员密码',
        'index.button.link'=>'登录',
        'index.link_success'=>'已确认企业信息。',
        'index.link_fail'=>'无法确认企业信息。',
        'index.link_member_title'=>'会员个人详细信息',
        'index.link_member_gender'=>'性别',
        'index.link_member_birthday'=>'出身年月',
        'index.link_member_address'=>'住址',
        'index.link_member_postcode'=>'邮编号码',
        'index.link_member_address1'=>'都道府县',
        'index.link_member_address2'=>'市区町村',
        'index.link_member_address3'=>'番地',
        'index.link_member_address4'=>'建物名称、房间号码(任意)',
        'index.link_member_mobile'=>'手机号码',
        'index.link_member_corp_title'=>'会员勤务单位信息',
        'index.link_member_company'=>'勤务单位',
        'index.link_member_department'=>'所在部门',
        'index.link_member_company_tel'=>'勤务单位电话',
        'index.link_member_privacy'=>'<a href="{privacy_url}" target=""_blank"">使用条款与政策</a>同意',
        'index.button_link_reserve'=>'预约确定',
        'index.label.link_modify'=>'更改',
        'index.error.link.input'=>'请输入正确信息',
        'index.error.link.input.footer'=>'您输入的信息有误。',
		
		'booking.template.title'=>'talkappi BOOKING',
		'booking.template.login'=>'登录／注册',
		'booking.template.footer.discovery'=>'新发现',
		'booking.template.footer.news'=>'最新消息',
		'booking.template.footer.magzine'=>'电子邮件杂志',
		'booking.template.footer.about'=>'关于talkappi',
		'booking.template.footer.aboutdtl'=>'旅行前～旅行后 连接旅行者与设施',
		'booking.template.step.1'=>'选择客房',
		'booking.template.step.2'=>'输入个人资料 ',
		'booking.template.step.3'=>'预订内容确认 ',
		'booking.template.step.4'=>'预订完成 ',
		'booking.template.button.detail'=>'详细',
		'booking.template.button.select'=>'选择',
		'booking.template.button.back'=>'返回上一步',
		'booking.template.label.comment'=>'评价',
		'booking.template.label.period'=>'住宿期间',
		'booking.template.label.checkin'=>'入住日期',
		'booking.template.label.checkin.s'=>'入住日期',
		'booking.template.label.checkout'=>'退房日期',
		'booking.template.label.checkout.s'=>'退房日期',
		'booking.template.label.days'=>'晚',
		'booking.template.label.pcount'=>'名',
		'booking.template.label.reserve.info'=>'预订信息',
		'booking.template.label.reserve.infodtl'=>'详细预订信息',
		'booking.template.label.reserve.userinfo'=>'预定人信息',
		'booking.template.label.rooms'=>'房间数',
		'booking.template.label.adults'=>'成人／室',
		'booking.template.label.kids'=>'儿童',
		'booking.template.label.more'=>'显示更多',
		'booking.template.label.price'=>'每晚费用',
		'booking.template.label.roomtype'=>'房型',
		'booking.template.label.plan'=>'住宿套餐',
		'booking.template.label.hotel.detail'=>'酒店',
		'booking.template.label.term'=>'服务条款',
		'booking.input.label.agree'=>'同意〜并继续预约',
		'booking.input.label.title'=>'预订人信息输入',
		'booking.input.label.lastname'=>'姓',
		'booking.input.label.firstname'=>'名',
		'booking.input.label.mail'=>'电子邮件',
		'booking.input.label.vmail'=>'电子邮件（确认）',
		'booking.input.label.phone'=>'电话号码',
		'booking.input.button.confirm'=>'预约内容确认',
		'booking.complete.title'=>'感谢您的预约',
		'booking.roomtype.button.find'=>'检索',
		'booking.roomtype.label.result'=>'检索结果',
		'book.plan.label.title'=>'选择住宿套餐',

		'faq.button.home'=>'主页',
		'faq.button.switch'=>'切换',
		'faq.title.category'=>'分类选择',
		'faq.title.top'=>'常见问题',
		'faq.title.keywords'=>'按常用关键字搜索',
		'faq.title.faq_ai'=>'AI推荐的问题',
		'faq.title.faq_most'=>'常见问题',
		'faq.title.faq_result'=>'搜寻结果',
		'faq.title.faq_tag'=>'标签的结果',
		'faq.title.no_result'=>'搜索无结果。',
		'faq.title.faq_category'=>'内的FAQ',
		'faq.title.faq_relation'=>'相关问题',
		'faq.title.category_search'=>'类别选择',
		'faq.title.contents'=>'以下为推荐信息',
		'faq.placeholder.keyword'=>'请输入您的问题',
		'faq.button.search'=>'查找',
		'faq.title.contact'=>'※FAQ中没有的问题，请在这边尝试。',
		'faq.button.contact'=>'咨询→',
		'faq.button.up'=>'返回最上层',
		'faq.title.page'=>'页',
		'faq.title.page.total'=>'条目中 ',
		'faq.title.page.range'=>'条目显示',
		'faq.title.fontsize'=>'文字大小',
		'faq.title.close'=>'关闭',
		'faq.button.fontsize.large'=>'大',
		'faq.button.fontsize.normal'=>'中',
		'faq.button.fontsize.small'=>'小',
		'faq.title.detail'=>'获取详细资讯请点击详情。',
		'faq.title.detail2'=>'为您提供各个酒店的资讯。<br/>详细资讯请点击以下“详情”进行查看。',
		'faq.button.detail'=>'详情',
		'faq.title.survey1'=>'为了更好地帮您解答相关问题，请您对我们本次服务进行评价。',
		'faq.title.survey2'=>'本次服务的解答对您有帮助吗？',
		'faq.title.survey_fin'=>'非常感谢您的回答。',
		'faq.title.survey_input_1'=>'请输入您的意见或感想',
		'faq.title.survey_input_2'=>'非常抱歉，我们无法直接在此处回复您的提问。',
		'faq.button.back'=>'上一页',
		'faq.button.survey.yes'=>'满意',
		'faq.button.survey.no'=>'提建议',
		'faq.button.survey.send'=>'提交反馈',
		'faq.label.left_bracket'=>'"',
		'faq.label.right_bracket'=>'"',
		'faq.button.show_inquiry'=>'前往问询表单',
		'faq.text.show_inquiry'=>'还是没有解决您的问题吗？可点击下方按钮进行咨询',
		
		'survey.html.title'=>'talkappi问卷调查',
        'survey.index.title'=>'问卷调查',
		'survey.index.title.name'=>'アンケート名',
        'survey.index.label.content'=>'简介',
		'survey.index.label.content.name'=>'アンケート概要',
        'survey.index.label.period'=>'调查期间',
        'survey.index.label.count'=>'问题数量',
        'survey.index.label.count.content'=>'最多{count}问',
        'survey.index.label.duration'=>'所需时间',
        'survey.index.label.duration.content'=>'{duration}分左右',
        'survey.index.label.present'=>'谢礼',
        'survey.index.label.remark'=>'本调查由账号运营方实施。账号运营方无法得知您的个人信息，仅确认回答结果。',
        'survey.index.label.expired'=>'谢谢您的访问，本调查问卷已经结束。',
		'survey.index.label.limit'=>'对不起。因为超过了回答人数的上限，本调查问卷已经停止接受新的回答。',
        'survey.index.button.answer'=>'回答',
        'survey.input.label.must'=>'＊ 必须',
        'survey.input.button.send'=>'提交',
		'survey.input.button.prev'=>'上一个',
		'survey.input.button.next'=>'下一个',
		'survey.input.text.length'=>'请输入{min_length} ~ {max_length}字',
		'survey.input.text.max_length'=>'请输入{max_length}字以内',
		'survey.input.text.min_length'=>'请输入{min_length}字以上',
		'survey.input.text.tel'=>'080-1234-5678(可省略连字符)',
		'survey.input.text.address'=>'123-4567(可省略连字符)',
		'survey.input.text.num'=>'请输入数字',
		'survey.input.text.num.dash'=>'请输入半角数字(可省略连字符)',
		'survey.input.text.email'=>'请输入电子邮箱地址',
		'survey.input.text.email.confirm'=>'(再次确认)',
		'survey.input.text.email.confirm.error'=>'电子邮箱地址不一致',
		'survey.input.text.mtx.error'=>'请回答各列问题',
		'survey.input.text.mtx.chk.message'=>'请在各行选择{max_min}个',
		'survey.input.text.chk.message'=>'请选择{max_min}个',
		'survey.input.text.chk.error'=>'请确认所选选项',
		'survey.input.text.mtx.chk.min'=>'最少{min}个',
		'survey.input.text.mtx.chk.max'=>'最最多{max}个',
		'survey.input.text.mtx.chk.num'=>'{num}个',
		'survey.input.text.date'=>'请输入电子邮箱地址',
		'survey.input.text.fup'=>'选择文件',
		'survey.input.text.sel'=>'请选择',
		'survey.input.label.required.not.input'=>'有必填项未回答。',
		'survey.input.label.required.not.input.eachquestion'=>'本问题为必答项',
        'survey.complete.title'=>'回答完毕',
        'survey.complete.label.thanks'=>"感谢您在百忙之中<br>回答本问卷调查",
		'survey.complete.label.unusable'=>"此优惠券已过期。<br>有效期限：{start_date}~{end_date}<br>请点击“优惠券保存・发送（クーポンを保存・送信）”按钮，将优惠券发送至您的电子邮箱。",
        'survey.complete.button.coupon'=>'查看优惠券',
        'survey.complete.button.close'=>'回到聊天页面',
		'survey.complete.button.closewindow'=>'关闭',
        'survey.coupon.label.period1'=>'剩余 {days}天',
		'survey.coupon.label.period2'=>'有效期限: {date}',
        'survey.coupon.label.period3'=>'开始日期: {date}',
		'survey.coupon.label.period4'=>'有效期限：优惠券发行后{date}天内<br>※在使用期间内打开优惠券时即可发行。',
		'survey.coupon.label.period5'=>'有效期限：从今天起{date}天内',
        'survey.coupon.label.present'=>'谢礼・优惠券',
        'survey.coupon.label.remark'=>'优惠券的有效日期和时间以东九区时间为准。',
        'survey.coupon.button.use'=>'使用本券',
        'survey.coupon.dialog.title'=>'确定使用本券吗？',
        'survey.coupon.dialog.content'=>'如选择「是的」，优惠券的使用状态将进行更新。在结账前，请务必向工作人员确认本画面。',
        'survey.coupon.label.expired'=>'本优惠券已过期',
		'survey.coupon.label.notstart'=>'此优惠券目前处于使用期间前。<br>请在使用期间开始后再次打开。',
        'survey.coupon.label.used'=>'本优惠券已使用',
        'survey.coupon.label.maxused'=>'本优惠券已到达使用上限',
        'survey.coupon.label.canotuse'=>'本优惠券非客人所属',
		'survey.coupon.label.issue'=>'非常抱歉，优惠券的发行次数已达上限，领券活动已结束。',
		'survey.coupon.expire.after.issue'=>'发行后{date}天内',
		'survey.common.label.required'=>'必填项',
		'survey.common.label.input'=>'请输入',
		'survey.common.label.other'=>'其他',
		'survey.common.label.other.input'=>'如果您选择了"其他"，请输入',
		'survey.common.label.other.input.required'=>'（必填）',
		'survey.common.label.other.input.optional'=>'',

		'inquiry.html.title'=>'talkappi问卷调查',
        'inquiry.index.title'=>'问卷调查',
        'inquiry.index.label.content'=>'简介',
        'inquiry.index.label.period'=>'服务期间',
        'inquiry.index.label.count'=>'问题数量',
        'inquiry.index.label.count.content'=>'最多{count}问',
        'inquiry.index.label.duration'=>'所需时间',
        'inquiry.index.label.duration.content'=>'{duration}分左右',
        'inquiry.index.label.present'=>'谢礼',
        'inquiry.index.label.remark'=>'本调查由账号运营方实施。账号运营方无法得知您的个人信息，仅确认回答结果。',
		'inquiry.index.label.invalid.title'=>'暂停服务',
		'inquiry.index.label.invalid.description'=>'十分抱歉。<br>因不在使用期间，<br>本表单将无法提交。',
		'inquiry.index.label.versionup.description'=>'非常抱歉。本表单不在使用期间。<br>10秒钟后会自动跳转到新的表单。<br>跳转后烦请重新输入并提交表单。',
		'inquiry.index.label.inquiry_answer_limit.description'=>'因达到上限，已无法接受表单提交，尽请谅解。',
		'inquiry.index.label.answer_limit.description'=>'您已经提交过表单。无需再次提交。<br>谢谢您的配合。',
		'inquiry.index.button.answer'=>'回答',
        'inquiry.input.label.must'=>'＊ 必须',
        'inquiry.input.button.send'=>'提交',
		'inquiry.input.button.prev'=>'修改内容', 
		'inquiry.input.button.pay'=>'去支付',
		'inquiry.input.button.back'=>'后退',
		'inquiry.input.button.confirm'=>'去确认',
		'inquiry.input.button.modify'=>'更改预订',
		'inquiry.input.button.cancel'=>'取消预订',
		'inquiry.modify.error'=>'已超过可以办理更改的期限。请直接咨询预订入住的设施。',
		'inquiry.cancel.error.check'=>'已超过可以办理取消的期限。请直接咨询预订入住的设施。',
		'inquiry.cancel.error.canceled'=>'预订已取消。',
		'inquiry.cancel.complete'=>'您的预订已被取消。',
		'inquiry.cancel.complete.desc'=>'我们已将详细内容发至您的邮箱。<br>非常感谢您此次使用服务。',
		'inquiry.input.text.length'=>'请输入{min_length} ~ {max_length}字',
		'inquiry.input.text.max_length'=>'请输入{max_length}字以内',
		'inquiry.input.text.min_length'=>'请输入{min_length}字以上',
		'inquiry.input.text.num.minmax'=>'请输入{min} ~ {max}之间的数字',		
		'inquiry.input.text.num.max'=>'请输入小于{max}的数字',
		'inquiry.input.text.num.min'=>'请输入大于{min}的数字',
		'inquiry.input.text.tel'=>'080-1234-5678(可省略连字符)',
		'inquiry.input.text.address'=>'123-4567(可省略连字符)',
		'inquiry.input.text.num'=>'请输入数字',
		'inquiry.input.text.num.dash'=>'请输入半角数字(可省略连字符)',
		'inquiry.input.text.email'=>'请输入电子邮箱地址',
		'inquiry.input.text.email.confirm'=>'(再次确认)',
		'inquiry.input.text.email.confirm.error'=>'电子邮箱地址不一致',
		'inquiry.input.text.clock'=>'请正确输入时间',
		'inquiry.input.text.date'=>'请输入电子邮箱地址',
		'inquiry.input.text.fup'=>'选择文件',
		'inquiry.input.text.sel'=>'请选择',
		'inquiry.input.text.other.confirm'=>'其它',
		'inquiry.input.text.txt.date'=>'日期',
		'inquiry.input.text.txt.time'=>'时间',
		'inquiry.input.spl.address.roomNo'=>'(非必须项)', 
		'inquiry.input.spl.address.country.label'=>'国家/地区',
		'inquiry.input.spl.address.postcode.label'=>'邮编',
		'inquiry.input.spl.address.prefecture.label'=>'省/直辖市',
		'inquiry.input.spl.address.city.label'=>'市/县',
		'inquiry.input.spl.address.street.label'=>'详细地址1',
		'inquiry.input.spl.address.room.label'=>'详细地址2',
		'inquiry.input.spl.name.full'=>'姓名（全名）',
		'inquiry.input.spl.name.first'=>'名',
		'inquiry.input.spl.name.last'=>'姓',
		'inquiry.input.price.tax'=>'(含税)',
		'inquiry.input.price.tax-service'=>'(含税和服务费)',
		'inquiry.input.label.required.not.input'=>'有必填项未回答。',
		'inquiry.input.label.required.not.input.eachquestion'=>'本问题为必答项',
		'inquiry.input.transition.error'=>'使用浏览器的自动翻译功能时，表单的某些内容可能无法正确显示。请关闭自动翻译功能后使用。',
		'inquiry.input.message.no-pay-reserve'=>'決済が完了していない予約があります。決済画面で決済を完了してください。確保した予約枠を解放して予約し直す場合、「最初から予約」	を選択してください。',
		'inquiry.input.button.cancel-no-pay-reserve'=>'最初から予約',
		'inquiry.input.button.continue-no-pay-reserve'=>'予約再開',
		'inquiry.input.button.close'=>'閉じる',
    	'inquiry.complete.title'=>'回答完毕',
    	'inquiry.complete.label.thanks'=>"感谢您在百忙之中<br>回答本问卷调查",
    	'inquiry.complete.label.period'=>"有效期间：{date}为止",
    	'inquiry.complete.button.coupon'=>'查看谢礼优惠券',
    	'inquiry.complete.button.close'=>'回到聊天页面',
		'inquiry.complete.button.closewindow'=>'关闭',
		'inquiry.complete.error.overtime_title'=>'预订失败',
		'inquiry.complete.error.overtime'=>'由于20分钟内没有完成付款，预定内容已经被自动取消。',
		'inquiry.complete.button.input'=>'返回预订页',
		'inquiry.complete.common.title'=>'验收完成',
		'inquiry.complete.common.description'=>'感谢您的查询。我们通常会在 3 个工作日内回复。',
    	'inquiry.coupon.label.period1'=>'剩余 {days}天',
    	'inquiry.coupon.label.period2'=>'{date} 为止可以使用',
    	'inquiry.coupon.label.present'=>'谢礼・优惠券',
		'inquiry.coupon.label.remark'=>'优惠券的有效日期和时间以东九区时间为准。',
		'inquiry.coupon.button.use'=>'使用本券',
		'inquiry.coupon.dialog.title'=>'确定使用本券吗？',
		'inquiry.coupon.dialog.content'=>'如选择「是的」，优惠券的使用状态将进行更新。在结账前，请务必向工作人员确认本画面。',
		'inquiry.coupon.label.expired'=>'本优惠券已过期',
		'inquiry.coupon.label.notstart'=>'使用前优惠券',
		'inquiry.coupon.label.used'=>'本优惠券已使用',
		'inquiry.coupon.label.maxused'=>'本优惠券已到达使用上限',
		'inquiry.coupon.label.canotuse'=>'本优惠券非客人所属',
		'inquiry.common.label.required'=>'必填项',
		'inquiry.common.label.input'=>'请输入',
		'inquiry.common.label.other'=>'其他',
		'inquiry.common.label.other.input'=>'如果您选择了"其他"，请输入',
		'inquiry.common.label.other.input.error'=>'请填写具体内容',
		'inquiry.common.label.limit.num.error'=>'超过了可以预约的上限{unit}，请重新选择数量。',
		'inquiry.common.label.limit.num.min.error'=>'选择数量尚未达到预约下限，请重新选择数量。',
		'inquiry.common.label.reserve.alone.error'=>'您选择的种别不能单独预约。',
		'inquiry.common.label.other.input.required'=>'（必填）',
		'inquiry.common.label.other.input.optional'=>'',
		'inquiry.common.tab.step1'=>'填写表格',
		'inquiry.common.tab.step2'=>'确认内容',
		'inquiry.common.tab.step2.payment'=>'确认并付款',
		'inquiry.common.tab.step3'=>'提交',
		'inquiry.common.complete.title'=>'已完成受理',
		'inquiry.common.complete.urlbtn'=>'回到主页',
		'inquiry.login.title' => '限量公开',
		'inquiry.login.description' => '请输入密码',
		'inquiry.login.error' => '请输入正确的密码',
		'inquiry.login.placeholder' => '密码',
		'inquiry.login.button' => '进入',
		'inquiry.faq.button' => '查看常见问题',
		'inquiry.label.num' => '数量',
		'inquiry.label.close' => '閉じる',
		'inquiry.label.expand' => '展開する',
		'inquiry.button.coupon_apply' => '適用',
		
		'coupon.dialog.remark'=>'如果无法收到邮件，请在确认垃圾箱和安全设定后重新尝试。',
		'coupon.dialog.title'=>'保存、发送优惠券',
		'coupon.dialog.content.link'=>'复制并分享优惠券链接',
		'coupon.dialog.copy'=>'复制',
		'coupon.dialog.content.send'=>'通过邮件方式发送优惠券',
		'coupon.dialog.mail'=>'输入邮箱地址',
		'coupon.dialog.button'=>'发送',
		'coupon.dialog.mail.success'=>'发送完成。',
		'coupon.dialog.mail.fail'=>'请输入正确的邮箱地址',
		'coupon.button.yes' => '确认使用',
		'coupon.button.no' => '取消',
		'coupon.dialog.use.store' => '使用场所',
		'coupon.dialog.use.title'=>'この画面を<br>お店の方に提示してください。',
		'coupon.dialog.use.staff' => '＜店舗スタッフ様＞',
		'coupon.dialog.use.staff.remark' => '「使用する」を押すと、クーポンが使用済みの
		ステータスになってしまい、元に戻せません。
		店舗スタッフ様が「使用する」ボタンを押して、
		クーポン使用となります。',
		'coupon.facility.choose' => '请选择店铺或设施',
		'coupon.dialog.copy.success'=>'已复制到剪贴板',
		'coupon.label.all.maxused'=>'このクーポンは利用上限枚数に達しているため、利用できません。',

		'common.error.front.title' => '很抱歉，发生错误。',
		'common.error.front.sub_title' => '请稍后重试。',
		
	);
