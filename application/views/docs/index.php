<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <title>Document</title>
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
  <meta name="description" content="Description">
  <meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
  <meta name="robots" content="noindex" />
	<meta name="googlebot" content="noindex" />
  <link rel="stylesheet" href="//cdn.jsdelivr.net/npm/docsify/lib/themes/vue.css">
</head>

<body>
  <div id="app"></div>
  <script>
    window.$docsify = {
      name: '',
      repo: '',
      loadSidebar: true,
      search: 'auto', // default 

      search : [
        '/',
        '/login',
        '/faq',
        '/dashboard/',
      ],

      // complete configuration parameters
      search: {
        maxAge: 86400000, // Expiration time, the default one day
        paths: 'auto', // or 'auto'
        placeholder: '検索する',

        // Localization
        placeholder: {
          '/': '検索する'
        },

        noData: '検索結果はありません',

        // Localization
        noData: {
          '/': '検索結果はありません'
        },

        // Headline depth, 1 - 6
        depth: 2,

        hideOtherSidebarContent: false, // whether or not to hide other sidebar content

        // To avoid search index collision
        // between multiple websites under the same domain
        namespace: 'website-1',
      }
    }
  </script>
  <script src="//cdn.jsdelivr.net/npm/docsify/lib/docsify.min.js"></script>
  <script src="//cdn.jsdelivr.net/npm/docsify/lib/plugins/search.min.js"></script>
  
</body>

</html>