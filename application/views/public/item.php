<style type="text/css">
.btn.default:active, .btn.default.active {
  background-image: none;
  background-color: #45b6af;
  color: #fff;
}
.btn.default {
  margin:2px;
}
</style>	
			<!-- BEGIN PAGE HEADER-->
			<!-- BEGIN PAGE HEAD -->
			<div class="page-head">
				<!-- BEGIN PAGE TITLE -->
				<div class="page-title">
					<?php 
						if ($item_div == 1) {
							$title = '店舗情報';
						}
						else if ($item_div == 2) {
							$title = '店舗情報';
						}
						else if ($item_div == 3) {
							$title = 'グループ施設管理';
						}
						else if ($item_div == 4) {
							$title = 'メディア管理';
						}
						$page_no = Session::instance()->get('items_page_no', NULL);
					?>					
					<h1><?php echo $title?><small style="display: contents;">・<?php echo $post['item_name']?><?php if ($post['item_name'] == '') {?>新規登録<?php }?></small></h1>
				</div>
				<!-- END PAGE TITLE -->
			</div>
			<!-- END PAGE HEAD -->
			<!-- END PAGE HEADER-->
			<!-- BEGIN PAGE CONTENT-->
			        <div id="page-wrapper">
				        <div class="portlet light">
						<div class="portlet box">
						<div class="portlet-body">
							<input type="hidden" id="act" name="act" value="" />
							<input type="hidden" id="item_bot_id" name="item_bot_id" value="<?php echo $item_bot_id?>" />
							<input type="hidden" id="item_div" name="item_div" value="<?php echo $item_div?>" />
							<input type="hidden" id="lang_cd" name="lang_cd" value="<?php echo $lang_cd?>" />
							<input type="hidden" id="class_div" name="class_div" value="<?php echo $div_item_class?>" />
							<input type="hidden" id="area_div" name="area_div" value="<?php echo $div_item_area?>" />
							<input type="hidden" name="class_cd_hidden" id="class_cd_hidden" value="<?php echo $post['class_cd']?>" />
							<input type="hidden" name="area_cd_hidden" id="area_cd_hidden" value="<?php echo $post['area_cd']?>" />
							<input type="hidden" name="def_class_cd" id="def_class_cd" value="<?php echo $post['def_class_cd']?>" />
							<input type="hidden" id="item_image" value="<?php echo $post['item_image']?>" />
							<div class="row">
							<div class="col-md-9">								
								<div class="form-body">
									<?php if ($div_item_area != '') { ?>
									<div class="form-group" id="area_cd_div">
										<label class="control-label col-md-2">エリア</label>
										<div class="col-md-2">
											<?php echo Form::select('area_cd3', array(), '', array('id'=>'area_cd3','class'=>'form-control area'))?>
										</div>
										<div class="col-md-2">
											<?php echo Form::select('area_cd2', array(), '', array('id'=>'area_cd2','class'=>'form-control area'))?>
										</div>
										<div class="col-md-2">
											<?php echo Form::select('area_cd1', array(), '', array('id'=>'area_cd1','class'=>'form-control area'))?>
										</div>
										<div class="col-md-2">
											<?php echo Form::select('area_cd', array(), '', array('id'=>'area_cd','class'=>'form-control area'))?>
										</div>
									</div>
									<?php }?>
									<div class="form-group">
										<label class="control-label col-md-2">ジャンル（複数可）</label>
										<div class="col-md-10">
											<div class="input-group" id="class_cd_show" style="margin-top:6px;">
											<?php 
											$class_cd_array = explode(' ', $post['class_cd']);
												foreach($class_cd_array as $class_cd) {
													if ($class_cd == '') continue;
													echo('<span class="alert alert-success alert-dismissable" style="margin-right:10px;font-size: 14px;padding:6px 8px;cursor:pointer;" class_cd="' . $class_cd . '">' . $post['class_name_array'][$class_cd] . '<a class="delete-class" style="color:red;padding:3px 3px;margin-left:5px;cursor:pointer;">✕</a></span>');
												}			
												echo('<a class="add-class" style="padding:3px 3px;cursor:pointer;font-size: 14px;margin-left:10px;">追加</a>');
											?>
											</div>
										</div>
									</div>									
									<div class="form-group" id="class_cd_div" style="display:none;">
										<label class="control-label col-md-2"></label>
										<div class="col-md-2">
											<?php echo Form::select('class_cd3', array(), '', array('id'=>'class_cd3','class'=>'form-control class'))?>
										</div>
										<div class="col-md-2">
											<?php echo Form::select('class_cd2', array(), '', array('id'=>'class_cd2','class'=>'form-control class'))?>
										</div>
										<div class="col-md-2">
											<?php echo Form::select('class_cd1', array(), '', array('id'=>'class_cd1','class'=>'form-control class'))?>
										</div>
										<div class="col-md-2">
											<?php echo Form::select('class_cd', array(), '', array('id'=>'class_cd','class'=>'form-control class'))?>
										</div>
										<button type="button" id="show_class_cd" class="btn green mr10">保存</button>
										<button type="button" id="hide_class_cd" class="btn red mr10">取消</button>
									</div>																													
									<div class="form-group">
										<label class="control-label col-md-2">店舗名</label>
										<div class="col-md-5">
											<div class="input-icon right">
												<input name="item_name" id="item_name" type="text" class="form-control" value="<?php echo($post['item_name'])?>" placeholder="">
											</div>
										</div>
									</div>
									<div class="form-group">
										<label class="control-label col-md-2">紹介文（80文字）</label>
										<div class="col-md-10">
											<div class="input-icon right">
												<textarea name="sell_point" id="sell_point" class="form-control" maxlength="80" rows="4" placeholder=""><?php echo($post['sell_point'])?></textarea>
											</div>
										</div>
									</div>
									<div class="form-group">
										<label class="control-label col-md-2">URL</label>
										<div class="col-md-10">
											<div class="input-icon right">
												<input name="url" id="url" type="text" maxlength="1001" class="form-control" placeholder="" value="<?php echo($post['url'])?>">
											</div>
										</div>
									</div>
									<div class="form-group">
										<label class="control-label col-md-2">電話番号</label>
										<div class="col-md-3">
											<div class="input-icon right">
												<input name="tel" id="tel" type="text" class="form-control" value="<?php echo($post['tel'])?>" placeholder="">
											</div>
										</div>
									</div>										
									<div class="form-group">
										<label class="control-label col-md-2">住所</label>
										<div class="col-md-10">
											<div class="input-icon right">
												<input name="address" id="address" type="text" class="form-control" value="<?php echo($post['address'])?>" placeholder="">
											</div>
										</div>
									</div>																			
									<div class="form-group last">
										<label class="control-label col-md-2">写真</label>
										<div class="col-md-6">
											<div class="fileinput fileinput-new" data-provides="fileinput">
												<div class="input-group input-large">
													<div class="form-control uneditable-input span3" data-trigger="fileinput">
														<i class="fa fa-file fileinput-exists"></i>&nbsp; <span class="fileinput-filename">
														</span>
													</div>
													<span class="input-group-addon btn default btn-file">
													<span class="fileinput-new">
													アップロード </span>
													<span class="fileinput-exists">
													変更 </span>
													<input type="hidden" name="image_base64" value="" />
													<input type="file" name="image" id="file-image">
													</span>
													<a href="#" class="input-group-addon btn red fileinput-exists" data-dismiss="fileinput">
													削除 </a>
												</div>
											</div>
										</div>
										<label class="control-label col-md-4">3:2の写真、サイズは5MB以内</label>
									</div>																	
									<div class="form-group">
										<label class="control-label col-md-2">営業状況</label>
										<div class="col-md-2">
											<?php echo Form::select('item_status_cd', $status_codes, $post['item_status_cd'], array('id'=>'item_status_cd','class'=>'form-control'))?>
										</div>
									</div>
									<div class="form-group" style="display: none;">
										<label class="control-label col-md-2">表示期間</label>
										<div class="col-md-4">
											<input name="start_date" id="start_date" value="<?php if ($post != NULL)echo($post['start_date'])?>" style="float:left;" class="form-control form-control-inline input-small date-picker" size="16" data-date-format="yyyy-mm-dd"  type="text"/>
											<input name="end_date" id="end_date" value="<?php if ($post != NULL) echo($post['end_date'])?>" style="float:left; margin-left:10px;" class="form-control form-control-inline input-small date-picker" size="16" data-date-format="yyyy-mm-dd"  type="text"/>
										</div>									
									</div>
									<div class="form-group" style="display: none;">
										<label class="control-label col-md-2">例年表示(月日)</label>
										<div class="col-md-1">
											<?php echo Form::select('start_mm', $months, $post['start_mm'], array('id'=>'start_mm','class'=>'form-control month'))?>
										</div>
										<div class="col-md-1">
											<?php echo Form::select('start_dd', [$post['start_dd']=>$post['start_dd']], $post['start_dd'], array('id'=>'start_dd','class'=>'form-control'))?>
										</div>
										<label class="control-label col-md-1" style="text-align:center;">～</label>
										<div class="col-md-1">
											<?php echo Form::select('end_mm', $months, $post['end_mm'], array('id'=>'end_mm','class'=>'form-control month'))?>
										</div>
										<div class="col-md-1">
											<?php echo Form::select('end_dd', [$post['end_dd']=>$post['end_dd']], $post['end_dd'], array('id'=>'end_dd','class'=>'form-control'))?>
										</div>																									
									</div>																
									<div class="form-group" style="display: none;">
										<label class="control-label col-md-2">緯度・経度</label>
										<div class="col-md-2">
											<input name="location_lat" type="text" class="form-control" value="<?php if ($post != NULL) echo($post['location_lat'])?>">
										</div>
										<div class="col-md-2">
											<input name="location_lon" type="text" class="form-control" value="<?php if ($post != NULL) echo($post['location_lon'])?>">
										</div>
									</div>
									<?php 
									if (count($icons) > 0) {
										echo('<div class="form-group">');
										echo('<label class="control-label col-md-2">タグ</label>');
										echo('<div class="btn-group" data-toggle="buttons">');
										foreach($icons as $k=>$v) {
											if (in_array($k, $post['tags'])) {
												echo('<label class="btn default active">');
												echo('<input name="tags[]" type="checkbox" checked="true" value="' . $k . '" class="toggle">' . $k. '</label>');
											}
											else {
												echo('<label class="btn default">');
												echo('<input name="tags[]" type="checkbox" value="' . $k . '" class="toggle">' .  $k . '</label>');
											}
										}
										echo('</div>');
										echo('</div>');
									}
									?>
									<?php if (count($icons) > 0 || count($banners) > 0) {?>
									<div class="form-group">
										<label class="control-label col-md-2">写真即時反映</label>
										<div class="col-md-2">
											<input type="checkbox" name="flg_img_invalidate" value="1" class="make-switch" data-on-color="success" data-off-color="warning">
										</div>																		
									</div>	
									<?php }?>
									<?php if ($item_bot_id == 214001 || $item_bot_id == 214002) { ?>
 									<div class="form-group">
										<label class="control-label col-md-2"></label>
										<div class="col-md-10">
										当店舗は、管轄の保健所をはじめ、関係機関の許認可を受けており、保健所等から連絡がある場合には、適切に対処いたします。<br/>また、横浜市暴力団排除条例第２条に定める暴力団、暴力団員、暴力団員等、暴力団経営支配法人等と関係ないことを誓約いたします。
										</div>
									</div>
									<div class="form-group">
										<label class="control-label col-md-2"></label>
										<div class="checkbox-label">
											<input type="checkbox" name="agree_flg" id="agree" value="1">
											<label for="agree_flg">同意する</label>
										</div>
									</div>	
									<?php } ?>																							
								</div>
								<div class="form-actions" style="margin-top: 40px;">
									<div class="row">
										<div class="col-md-offset-2 col-md-9">
										<button type="button" id="previewButton" class="btn yellow mr10">プレビュー</button>	
										<button type="button" id="saveBaseButton" class="btn blue mr10">
										<i class="fa fa-save mr10"></i>保存</button>
										<?php echo(nl2br($public_apply_contents_abstract))?>
										</div>
									</div>
								</div>
							</div>
							<div id="msg-preview" class="col-md-3">
							<?php echo $chatpreview ?>
							</div>
							
							</div>
							</div>
							</div>
						</div>
			        </div>
			        <!-- /#page-wrapper -->
			<!-- END PAGE CONTENT-->