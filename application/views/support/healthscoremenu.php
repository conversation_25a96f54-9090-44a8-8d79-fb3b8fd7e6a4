<?php
// タブの設定
$menu_items = [
    // 本番レコード追加されたら公開
    // ヘルススコア
    'healthscore' => [
        'path' => 'healthscore',
        'div' => null,
        'label' => 'ヘルススコア',
        'use_translation' => true
    ],
    // 管理画面操作に関するタブ
    'noticeview' => [
        'path' => 'healthscorenoticeview',
        'div' => null,
        'label' => 'admin.healthscoremenu.label.healthscore_noticeview',
        'use_translation' => true
    ],
    'verybotuse' => [
        'path' => 'healthscoreverybotuse',
        'div' => null,
        'label' => 'admin.healthscoremenu.label.healthscore_verybotuse',
        'use_translation' => true
    ],
    'login' => [
        'path' => 'healthscoremgtaccess',
        'div' => '1',
        'label' => 'admin.healthscoremenu.label.healthscore_login',
        'use_translation' => true
    ],
    'faqedit' => [
        'path' => 'healthscoremgtaccess',
        'div' => '3',
        'label' => 'admin.healthscoremenu.label.healthscore_faqedit',
        'use_translation' => true
    ],
    'statview' => [
        'path' => 'healthscoremgtaccess',
        'div' => '5',
        'label' => 'admin.healthscoremenu.label.healthscore_statview',
        'use_translation' => true
    ],
    'veryupdate' => [
        'path' => 'healthscoremgtaccess',
        'div' => '4',
        'label' => 'admin.healthscoremenu.label.healthscore_veryupdate',
        'use_translation' => true
    ],
    // サービス利用状況に関するタブ
    'chatbot_users' => [
        'path' => 'healthscoreserviceusage',
        'div' => '1',
        'label' => 'admin.healthscoremenu.label.healthscore_chatbot_users',
        'use_translation' => true
    ],
    'chatbot_conversations' => [
        'path' => 'healthscoreserviceusage',
        'div' => '2',
        'label' => 'CHATBOT会話数',
        'use_translation' => false
    ],
    'inquiry_answers' => [
        'path' => 'healthscoreserviceusage',
        'div' => '3',
        'label' => 'INQUIRY回答数',
        'use_translation' => false
    ],
    'survey_answers' => [
        'path' => 'healthscoreserviceusage',
        'div' => '4',
        'label' => 'SURVEY回答数',
        'use_translation' => false
    ],
    'very_users' => [
        'path' => 'healthscoreserviceusage',
        'div' => '5',
        'label' => 'VERY利用者数',
        'use_translation' => false
    ]
];
?>

<nav class="top-nav">
    <ul class="">
    <?php foreach ($menu_items as $key => $item): ?>
        <?php 
        $is_active = false;
        if (!$item['div']) {
            // divがない場合は、pathだけで判定
            $is_active = ($_action == $item['path']);
        } else {
            // divがある場合は、pathとdivの組み合わせで判定
            $is_active = ($_action == $item['path'] && $div == $item['div']);
        }
        ?>
        <li class="<?php echo $is_active ? 'active' : ''; ?>">
            <a href="/<?php echo $_path?>/<?php echo $item['path'] . ($item['div'] ? "?div={$item['div']}" : ''); ?>">
            <?php echo $item['use_translation'] ? __($item['label']) : $item['label']; ?></a>
        </li>
    <?php endforeach; ?>
    </ul>
</nav>