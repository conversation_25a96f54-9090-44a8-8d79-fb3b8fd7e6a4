<div class="content-container white">
    <div class="form-body">		
        <div class="form-group">
            <label class="control-label col-md-1">送信日</label>
            <div class="col-md-4">
                <div class="talkappi-datepicker-range" data-name="range" data-value='<?php echo json_encode($range) ?>' data-time-format="hh:mm"></div>
            </div>
        </div>
        <div class="form-group">
            <label class="control-label col-md-1" style="padding-left:15px; padding-right:15px;">ボットID</label>
            <div class="col-md-4">
                <input name="bot_id" id="bot_id" value="<?php echo $bot_id; ?>" class="form-control form-control-inline input-small" type="text" placeholder="1"/>
            </div>
        </div>
        <!-- <div class="form-group">
            <label class="control-label col-md-1" style="white-space: nowrap;">メールアドレス</label>
            <div class="col-md-4">
                <input name="mail" id="mail" value="<?php echo $mail; ?>" class="form-control form-control-inline input-small" type="text" placeholder="<EMAIL>" style="min-width:350px;"/>
            </div>
        </div> -->
        <div class="form-group">
            <label class="control-label col-md-1">取得件数
                <span class="icon-detail" title="取得件数を超える場合は、送信日時の新しいメールが優先的に表示されます。"></span>
            </label>
            <div class="col-md-4">
                <select name="limit" id="limit" class="form-control form-control-inline input-small">
                    <?php
                    $limits = [500, 1000, 2000, 3000];
                    foreach ($limits as $value) {
                        $selected = ($value == $limit) ? 'selected' : '';
                        echo "<option value=\"$value\" $selected>$value</option>";
                    }
                    ?>
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="control-label col-md-1"></label>
            <div class="col-md-4">
                <button type="button" id="searchButton" class="btn yellow">
                    <i class="fa fa-search mr10"></i><?php echo __('admin.common.button.search') ?>
                </button>
            </div>
        </div>
    </div>		

    <table class="table table-striped table-bordered table-hover js-data-table" id="mailLogTable">
        <thead>
            <tr>
                <th>メールID</th>
                <th>ボットID</th>
                <th>送信者</th>
                <th>受信者</th>
                <th>受信者cc</th>
                <th>受信者bcc</th>
                <th>件名</th>
                <th>本文</th>
                <th>送信日</th>
                <th>エラー情報</th>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($logs as $log): ?>
                <tr>
                    <td><?php echo htmlspecialchars($log['id']); ?></td>
                    <td><?php echo htmlspecialchars($log['bot_id']); ?></td>
                    <td><?php echo htmlspecialchars($log['sender']); ?></td>
                    <td><?php echo htmlspecialchars($log['receiver']); ?></td>
                    <td><?php echo htmlspecialchars($log['receiver_cc']); ?></td>
                    <td><?php echo htmlspecialchars($log['receiver_bcc']); ?></td>
                    <td><?php echo htmlspecialchars($log['title']); ?></td>
                    <td><?php echo htmlspecialchars($log['body']); ?></td>
                    <td><?php echo htmlspecialchars($log['send_time']); ?></td>
                    <td><?php echo htmlspecialchars($log['error_info']); ?></td>
                </tr>
            <?php endforeach; ?>
        </tbody>
    </table>
</div>