<?php echo $menu?>

<div class="content-container white">
    <div class="form-body">		
        <div class="form-group">
            <label class="control-label col-md-1" style="padding-left:15px; padding-right:15px;"><?php echo __('admin.common.label.period') ?></label>
            <div class="flex">
                <input name="start_date" id="start_date" value="<?php echo $start_date !== "" ? date('Y-m', strtotime($start_date)) : ""; ?>" style="float:left;" class="talkappi-datepicker" size="16" data-date-format="yyyy-mm" type="text"/>
                <input name="end_date" id="end_date" value="<?php echo $end_date !== "" ? date('Y-m', strtotime($end_date)) : ""; ?>" style="float:left; margin-left:10px;" class="talkappi-datepicker" size="16" data-date-format="yyyy-mm" type="text"/>
                <button type="button" id="searchButton" class="btn yellow" style="margin-left:20px;">
                    <i class="fa fa-search mr10"></i><?php echo __('admin.common.button.search') ?>
                </button>
            </div>
        </div>
    </div>		

    <table class="table table-striped table-bordered table-hover js-data-table" id="healthscoredata">
        <thead>
            <tr>
                <th rowspan="2" style="vertical-align: middle;">ボットID</th>
                <th rowspan="2" style="min-width: 150px; vertical-align: middle;">施設名</th>
                <th colspan="<?php echo count($months ?? []); ?>" style="border-bottom: 1px solid #ddd;"><?php echo $div_title ?></th>
            </tr>
            <tr>
                <?php foreach ($months ?? [] as $month): ?>
                    <th><?php echo htmlspecialchars($month); ?></th>
                <?php endforeach; ?>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($data as $data_key => $data_value): ?>
                <tr>
                    <td><?php echo htmlspecialchars($data_value['bot_id']); ?></td>
                    <td><?php echo htmlspecialchars($data_value['bot_name']); ?></td>
                    <?php foreach ($months as $month): ?>
                        <td>
                            <?php 
                            echo htmlspecialchars($data_value['months'][$month] == 0 ? '0' : $data_value['months'][$month]); 
                            ?>
                        </td>
                    <?php endforeach; ?>
                </tr>
            <?php endforeach; ?>
        </tbody>
    </table>
</div>
