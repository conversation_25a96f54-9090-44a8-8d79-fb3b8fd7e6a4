<script>
    const _roleCd = <?php echo $_user->role_cd ?>;
    // 旧画面用（requestIdが1つ）
    const _requestId = <?php echo isset($request_id) ? json_encode($request_id) : 'null' ?>;
    // 新画面用（requestIdが複数）
    const _requestIds = <?php echo isset($request_ids) ? json_encode($request_ids) : 'null' ?>;
    const _translateInformation = <?php echo json_encode($translate_information) ?>;
    const _translateOperation = <?php echo json_encode($translate_operation) ?>;
    const _operationUsers = <?php echo json_encode($operation_users) ?>;
    const _config_button = <?php echo json_encode($config_button) ?>;
    const _login_user_id = <?php echo json_encode($login_user_id) ?>;
</script>

<div class="react-support-translaterequest content-container white border" data-lang_cd="<?php echo $_lang_cd ?>" style="flex-direction:column; display:flex; gap:24px; padding:24px 84px;"></div>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/1.12.4/jquery.min.js"></script>
<script type="text/javascript" src="/assets/common/react/pages/support/translaterequest.bundle.js"></script>

<script type="text/javascript">
    jQuery(document).ready(function($) {
        if (_translateInformation.request_detail) {
            _translateInformation.request_detail = JSON.parse(_translateInformation.request_detail);
        }
        window.talkappi_setupTranslaterequestPage({
            roleCd: _roleCd,
            requestId: _requestId,
            requestIds: _requestIds,
            _translateInformation: _translateInformation,
            _translateOperation: _translateOperation,
            _operationUsers: _operationUsers,
            _configButton: _config_button,
            _login_user_id: _login_user_id,
        })
    });
</script>