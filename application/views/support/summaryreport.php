<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.4.0/jspdf.umd.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/html2canvas@1.0.0-rc.5/dist/html2canvas.min.js"></script>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/1.12.4/jquery.min.js"></script>
<script type="text/javascript" src="/assets/common/react/pages/support/summaryreport.bundle.js"></script>
<script>
    const _data = <?php echo json_encode($data); ?>;
    const _is_report_generated = <?php echo json_encode($is_report_generated); ?>;
</script>

<div class="content-container white border">
    <h2>talkappi利用状況概要レポート</h2>
    <div class="form-group" style="display: flex; align-items: center; gap: 10px;">
        <!-- 年月選択 -->
        <label>対象月</label>
        <input name="target_date" id="target_date" value="<?php echo $target_date ?>" class="talkappi-datepicker" size="16" data-date-format="yyyy-mm" type="text"/>                
        <!-- レポート生成ボタン -->
        <button type="button" id="generateReport" class="btn-smaller btn-white">
            <span class="icon-edit"></span>
            レポートを作成する
        </button>
        <!-- レポート出力ボタン -->
        <?php if ($is_report_generated): ?>
            <button type="button" id="exportReport" class="btn-smaller btn-white">
                <span class="icon-export"></span>
                pdfで出力する
            </button>
        <?php endif; ?>
    </div>
    <div class="react-summary-report"></div>
</div>

<script type="text/javascript">
    jQuery(document).ready(function($) {
        window.talkappi_setupSummaryReport(_data, _is_report_generated);
    });
    window.html2canvas = html2canvas;
    window.jsPDF = window.jspdf.jsPDF;
</script>