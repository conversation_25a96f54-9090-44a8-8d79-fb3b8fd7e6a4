<script>
  const _ng_words = <?php echo $ng_words_json_encoded ?>;
  const _headers = <?php echo $formatted_headers_json_encoded ?>;
</script>

<div class="content-container white">
    <!-- 検索バー -->
    <div class="form-body">
        <div class="form-group">
            <div class="flexbox-x-axis">
                <div class="flexbox-x-axis" style="gap: 8px;">
                    <label>言語</label>
                    <td><div class="talkappi-pulldown" data-name="lang_cd" data-value="ja" style="" data-source='<?php echo $lang_cd_list_json_encoded; ?>'></div></td>
                </div>
                <div class="col-md-1">
                    <span class="btn-smaller btn-yellow js-search"><i class="fa fa-search mr10"></i><?php echo __('admin.common.button.search') ?></span>
                </div>	
            </div>
        </div>
    </div>
    <!-- デーブル -->
    <div class="react-support-ngwords" data-lang_cd="<?php echo $_lang_cd ?>"></div>
</div>

<script type="text/javascript" src="/assets/common/react/pages/support/ngwords.bundle.js"></script>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/1.12.4/jquery.min.js"></script>


<script type="text/javascript">
  _headers.forEach(column => {
    if (column.accessor === 'ng_word') {
        column.cell_type = 'input';
    } else if(column.accessor === 'txt_match'){
        column.cell_type = 'pulldown';
        column.cell_data = [
            { "value": "exact", "label": "完全一致" },
            { "value": "partial", "label": "部分一致" }
        ]
    } else if(column.accessor === 'type'){
        column.cell_type = 'pulldown';
        column.cell_data = [
            { "value": "txt", "label": "テキスト" }
        ]
    } else if(column.accessor === 'lang_cd'){
        column.cell_type = 'pulldown';
        column.cell_data = [
            { "value": "ja", "label": "日本語" },
            { "value": "en", "label": "英語" },
            { "value": "cn", "label": "簡体字" },
            { "value": "tw", "label": "繁体字" },
            { "value": "ko", "label": "韓国語" },
        ]
    } else if(column.accessor === 'operation'){
        column.cell_type = 'buttons';
        column.cell_data = [
            { "value": "delete", "label": "削除" }
        ]
    }
  });
  jQuery(document).ready(function($){
      window.talkappi_ngwords_setupNgwordsPage({
        ng_words: _ng_words,
        headers: _headers,
      })
    });
</script>