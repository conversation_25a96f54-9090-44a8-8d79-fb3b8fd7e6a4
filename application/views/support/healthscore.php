<?php echo $menu?>

<div class="content-container white">
    <div class="form-body">		
        <div class="form-group">
            <label class="control-label col-md-1" style="padding-left:15px; padding-right:15px;"><?php echo __('admin.common.label.period') ?></label>
            <div class="flex">
                <input name="start_date" id="start_date" value="<?php echo $start_date !== "" ? date('Y-m', strtotime($start_date)) : ""; ?>" style="float:left;" class="talkappi-datepicker" size="16" data-date-format="yyyy-mm" type="text"/>
                <input name="end_date" id="end_date" value="<?php echo $end_date !== "" ? date('Y-m', strtotime($end_date)) : ""; ?>" style="float:left; margin-left:10px;" class="talkappi-datepicker" size="16" data-date-format="yyyy-mm" type="text"/>
                <button type="button" id="searchButton" class="btn yellow" style="margin-left:20px;">
                    <i class="fa fa-search mr10"></i><?php echo __('admin.common.button.search') ?>
                </button>
            </div>
        </div>
    </div>		

    <table class="table table-striped table-bordered table-hover js-data-table" id="healthscoredata">
        <thead>
            <tr>
                <th rowspan="2" style="vertical-align: middle;">ボットID</th>
                <th rowspan="2" style="min-width: 150px; vertical-align: middle;">施設名</th>
                <th colspan="<?php echo count($months ?? []); ?>" style="border-bottom: 1px solid #ddd;">ヘルススコア（数値）</th>
            </tr>
            <tr>
                <?php foreach ($months ?? [] as $month): ?>
                    <th><?php echo htmlspecialchars($month); ?></th>
                <?php endforeach; ?>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($data as $data_key => $data_value): ?>
                <tr>
                    <td><?php echo htmlspecialchars($data_value['bot_id']); ?></td>
                    <td><?php echo htmlspecialchars($data_value['bot_name']); ?></td>
                    <?php foreach ($months as $month): ?>
                        <td>
                            <?php 
                            $val = isset($data_value['monthly_scores'][$month]) ? $data_value['monthly_scores'][$month] : '0';
                            $metrics = isset($data_value['monthly_metrics'][$month]) ? $data_value['monthly_metrics'][$month] : null;
                            $criteria = isset($data_value['monthly_criteria'][$month]) ? $data_value['monthly_criteria'][$month] : null;
                            
                            $sort_value = 0;
                            if ($val !== '0' && preg_match('/\((\d+)\)/', $val, $matches)) {
                                $sort_value = intval($matches[1]) + 10000;
                            }
                            ?>
                            <span style="display: none;"><?php echo $sort_value; ?></span>
                            <?php if ($val !== '0' && $metrics): ?>
                                <span>
                                    <?php echo htmlspecialchars($val); ?>
                                    <div class="btn round js-show-metrics btn-sm" data-metrics="<?php echo htmlspecialchars($metrics); ?>" data-criteria="<?php echo htmlspecialchars($criteria); ?>" style="font-size: 10px; padding: 1px 5px;">詳細</div>
                                </span>
                            <?php else: ?>
                                <?php echo htmlspecialchars($val); ?>
                            <?php endif; ?>
                        </td>
                    <?php endforeach; ?>
                </tr>
            <?php endforeach; ?>
        </tbody>
    </table>
</div>

