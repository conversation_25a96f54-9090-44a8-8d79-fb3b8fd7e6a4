<?php echo $menu?>

<div class="content-container white">
        <div class="form-body">
        <div class="form-group">
            <div class="flexbox-x-axis">
                <label class="control-label col-md-1" style="padding-right:15px; white-space:no-wrap;">お知らせID</label>
                <input name="start_ticket_id" id="start_ticket_id" value="<?php echo $start_ticket_id !== "" ? intval($start_ticket_id) : ""; ?>" class="form-control form-control-inline input-small" style="max-width:80px;" type="number" min="0"/>
                <span style="margin-left:10px; margin-right:10px;">〜</span>
                <input name="end_ticket_id" id="end_ticket_id" value="<?php echo $end_ticket_id !== "" ? intval($end_ticket_id) : ""; ?>" class="form-control form-control-inline input-small" style="max-width:80px;" type="number" min="0"/>
                <button type="button" id="searchButton" class="btn yellow" style="margin-left:20px;">
                <i class="fa fa-search mr10"></i><?php echo __('admin.common.button.search') ?></button>
                <span style="margin-left:20px;">
                    <label>施設ごとの合計表示</label>
                    <span class="talkappi-switch js-change_display" data-name="facility_total_view" data-value="<?php echo $facility_total_view === '1' ? '1' : '0'; ?>" style="margin-left:10px;"></span>
                </span>
            </div>
        </div>
    </div>
    <table class="table table-striped table-bordered table-hover js-data-table" id="healthscoredata">
        <thead>
            <tr>
                <th rowspan="2" style="vertical-align: middle;">ボットID</th>
                <th rowspan="2" style="min-width: 150px; vertical-align: middle;">施設名</th>
                <?php if ($facility_total_view === '0'): ?>
                    <th rowspan="2" style="vertical-align: middle;">ユーザー名</th>
                <?php endif; ?>
                <th colspan="<?php echo count($notice_ids); ?>" style="border-bottom: 1px solid #ddd;">お知らせID</th>
            </tr>
            <tr>
                <?php foreach ($notice_ids as $notice_id): ?>
                    <th><?php echo htmlspecialchars($notice_id); ?></th>
                <?php endforeach; ?>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($data as $user_data): ?>
                <tr>
                    <td><?php echo htmlspecialchars($user_data['bot_id']); ?></td>
                    <td><?php echo htmlspecialchars($user_data['bot_name']); ?></td>
                    <?php if ($facility_total_view === '0'): ?>
                        <td><?php echo htmlspecialchars($user_data['user_name']); ?></td>
                    <?php endif; ?>
                    <?php foreach ($notice_ids as $notice_id): ?>
                        <td>
                            <?php 
                                $count = array_sum($user_data['tickets'][$notice_id]);
                                echo htmlspecialchars($count == 0 ? '0' : $count); 
                             ?>
                        </td>
                    <?php endforeach; ?>
                </tr>
            <?php endforeach; ?>
        </tbody>
    </table>
</div>
