<script>
const _roleCd = <?php echo $_user->role_cd ?>;
const _translateData = <?php echo json_encode($formatted_translate_requests) ?>;
</script>

<div class="react-support-translaterequests"  data-lang_cd="<?php echo $_lang_cd ?>" data-isParent="<?php echo $isParentBot ? 'true' : 'false' ?>"></div>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/1.12.4/jquery.min.js"></script>
<script type="text/javascript" src="/assets/common/react/pages/support/translaterequests.bundle.js"></script>

<script type="text/javascript">
    jQuery(document).ready(function($){
    window.talkappi_setupTranslaterequestsPage({
        roleCd: _roleCd,
        translateData: _translateData,
    })});
</script>