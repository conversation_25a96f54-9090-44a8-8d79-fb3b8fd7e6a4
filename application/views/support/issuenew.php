<script>
    const _redmineUsers = <?php echo !empty($redmine_users) ? json_encode($redmine_users) : '[]' ?>;
    const _technicalAssignees = <?php echo !empty($technical_assignees) ? json_encode($technical_assignees) : '[]' ?>;
    const _service_categories = <?php echo !empty($service_categories) ? json_encode($service_categories) : '[]' ?>;
    const _isAdmin = <?php echo isset($isAdmin) ? json_encode($isAdmin) : 'false' ?>;
    const _retiredUsers = <?php echo json_encode($retired_users) ?>;
</script>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/1.12.4/jquery.min.js"></script>
<script type="text/javascript" src="/assets/common/react/pages/support/issuenew.bundle.js"></script>
<input type="hidden" name="redmine_setting_error" id="redmine_setting_error" value="<?php echo ($redmine_setting_error) ?>" />
<input type="hidden" name="redmine_user_error" id="redmine_user_error" value="<?php echo ($redmine_user_error) ?>" />

<div class="issuenew content-container white border" id="issuenew" data-lang_cd="<?php echo $_lang_cd ?>"></div>

<script type="text/javascript">
  jQuery(document).ready(function($) {
    window.talkappi_setupIssueNewPage({
      isNew: true,
      redmineUsers: _redmineUsers,
      technicalAssignees: _technicalAssignees,
      serviceCategories: _service_categories,
      isAdmin: _isAdmin,
      retiredUsers: _retiredUsers,
    });
  });
</script>

<?php if (isset($show_bot_box) && $show_bot_box === true) { ?>
    <script>
        const _show_bot_box = <?php echo $show_bot_box ?>
    </script>
<?php } ?>