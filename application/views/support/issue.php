<script>
    const _issueData = <?php echo json_encode($issue) ?>;
    const _redmineUsers = <?php echo json_encode($redmine_users) ?>;
    const _redmineAdminUsers = <?php echo json_encode($redmine_admin_users) ?>;
    const _logoUrlCustomer = <?php echo json_encode($_logo_url) ?>;
    const _logoUrlAdminUser = "/assets/common/images/activalues-webchat-logo.png";
    const _service_categories = <?php echo json_encode($service_categories) ?>;
    const _technicalAssignees = <?php echo json_encode($technical_assignees) ?>;
    const _retiredUsers = <?php echo json_encode($retired_users) ?>;
    const _isAdmin = <?php echo json_encode($isAdmin) ?>;
    const _loading = <?php echo isset($loading) ? json_encode($loading) : 'false' ?>;
</script>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/1.12.4/jquery.min.js"></script>
<script type="text/javascript" src="/assets/common/react/pages/support/issue.bundle.js"></script>

<input type="hidden" name="redmine_setting_error" id="redmine_setting_error" value="<?php echo ($redmine_setting_error) ?>" />
<input type="hidden" name="redmine_user_error" id="redmine_user_error" value="<?php echo ($redmine_user_error) ?>" />

<?php if (!isset($show_bot_box) || $show_bot_box !== true): ?>
    <!-- チケット説明 -->
    <div class="react-issue content-container white border" data-lang_cd="<?php echo $_lang_cd ?>" style="padding: 0px"></div>

    <!-- コメント欄 -->
    <div class="react-issue-notes content-container white border" data-lang_cd="<?php echo $_lang_cd ?>" style="padding: 0px; background-color: #EBEDF2; border: 0px; margin-bottom: 12px"></div>

    <!-- 編集 -->
    <div class="react-issue-reply content-container white border js-issue-reply"></div>

    <script type="text/javascript">
        jQuery(document).ready(function($) {
            window.talkappi_setupIssuePage({
                issueData: _issueData,
                redmineUsers: _redmineUsers,
                redmineAdminUsers: _redmineAdminUsers,
                logoUrlCustomer: _logoUrlCustomer,
                logoUrlAdminUser: _logoUrlAdminUser,
                serviceCategories: _service_categories,
                technicalAssignees: _technicalAssignees,
                isAdmin: _isAdmin,
                retiredUsers: _retiredUsers,
            });
        });
    </script>
<?php endif; ?>

<?php if (isset($show_bot_box) && $show_bot_box === true) { ?>
    <script>
        const _show_bot_box = <?php echo $show_bot_box ?>
    </script>
<?php } ?>