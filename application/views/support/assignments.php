<div class="content-container white border">
    <div class="content-header">
        <h2>担当案件数</h2>
    </div>
    
    <div class="assignments-table-container" style="padding: 20px;">
        <table class="table table-striped table-bordered table-hover">
            <thead>
                <tr>
                    <th>担当者名（Redmine ID）</th>
                    <th style="width: 200px;">サービスイン後</th>
                    <th style="width: 200px;">サービスイン前</th>
                    <th style="width: 200px;">合計</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($assignments as $assignment): ?>
                    <tr>
                        <td><?php echo htmlspecialchars($assignment['user_name']) . ' (' . ($assignment['user_id'] == 0 ? '？' : (int)$assignment['user_id']) . ')'; ?></td>
                        <td><?php echo htmlspecialchars($assignment['active_bot_count']); ?></td>
                        <td><?php echo htmlspecialchars($assignment['inactive_bot_count']); ?></td>
                        <td><?php echo htmlspecialchars($assignment['total_bot_count']); ?></td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
</div>
