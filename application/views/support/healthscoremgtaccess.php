<?php echo $menu?>

<div class="content-container white">
    <div class="form-body">		
        <div class="form-group">
            <label class="control-label col-md-1" style="padding-left:15px; padding-right:15px;"><?php echo __('admin.common.label.period') ?></label>
            <div class="flex">
                <input name="start_date" id="start_date" value="<?php echo $start_date !== "" ? date('Y-m', strtotime($start_date)) : ""; ?>" style="float:left;" class="talkappi-datepicker" size="16" data-date-format="yyyy-mm" type="text"/>
                <input name="end_date" id="end_date" value="<?php echo $end_date !== "" ? date('Y-m', strtotime($end_date)) : ""; ?>" style="float:left; margin-left:10px;" class="talkappi-datepicker" size="16" data-date-format="yyyy-mm" type="text"/>
                <?php if ($facility_total_view === '0') { ?>
                    <select class="form-control select2me" name="bot" style="width:200px;">
                        <?php foreach($bots as $bot_id => $bot_name) { ?>
                            <option value="<?php echo $bot_id; ?>" <?php echo ($selected_bot_id == $bot_id) ? 'selected' : '' ?>><?php echo $bot_name; ?></option>
                        <?php } ?>
                    </select>
                <?php } ?>
                <button type="button" id="searchButton" class="btn yellow" style="margin-left:20px;">
                <i class="fa fa-search mr10"></i><?php echo __('admin.common.button.search') ?></button>
                <span style="margin-left:20px;">
                    <label class="control-label">施設ごとの合計表示</label>
                    <span class="talkappi-switch js-change_display" data-name="facility_total_view" data-value="<?php echo $facility_total_view === '1' ? '1' : '0'; ?>" style="margin-left:10px;"></span>
                </span>
                
            </div>
        </div>
    </div>		

    <table class="table table-striped table-bordered table-hover js-data-table" id="healthscoredata">
        <thead>
            <tr>
                <th rowspan="2" style="vertical-align: middle;">ボットID</th>
                <th rowspan="2" style="min-width: 150px; vertical-align: middle;">施設名</th>
                <?php if ($facility_total_view === '0'): ?>
                    <th rowspan="2" style="vertical-align: middle;">ユーザー名</th>
                <?php endif; ?>
                <th colspan="<?php echo count($months ?? []); ?>" style="border-bottom: 1px solid #ddd;"><?php echo $div_title ?></th>
            </tr>
            <tr>
                <?php foreach ($months ?? [] as $month): ?>
                    <th><?php echo htmlspecialchars($month); ?></th>
                <?php endforeach; ?>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($data as $user_key => $user_data): ?>
                <tr>
                    <td><?php echo htmlspecialchars($user_data['bot_id']); ?></td>
                    <td><?php echo htmlspecialchars($user_data['bot_name']); ?></td>
                    <?php if ($facility_total_view === '0'): ?>
                        <td><?php echo htmlspecialchars($user_data['user_name']); ?></td>
                    <?php endif; ?>
                    <?php foreach ($months as $month): ?>
                        <td>
                            <?php 
                            $count = array_sum($user_data['months'][$month]);
                            echo htmlspecialchars($count == 0 ? '0' : $count); 
                            ?>
                        </td>
                    <?php endforeach; ?>
                </tr>
            <?php endforeach; ?>
        </tbody>
    </table>
    <?php if (isset($_GET['div']) && $_GET['div'] == 5): ?>
        <div>【カウント対象画面URL末尾】</div>
        <div>talkreport1, accessreport, faqlogword, faqlogwordselect, faqlogcate, faqlogfaq, faqlogexpand, faqlogexpandrelation, faqlogsurvey, faqlogfollow, faqreport1, faqreport2, faqlog, veryreport, report, report3</div>
    <?php endif; ?>
</div>
