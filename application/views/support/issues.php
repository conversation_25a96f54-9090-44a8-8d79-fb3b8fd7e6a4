<script>
    const _issuesData = <?php echo json_encode($issues_data) ?>;
    const _show_all_items = <?php echo isset($show_all_items) ? $show_all_items : 'null' ?>;
    const _service_categories = <?php echo json_encode($service_categories) ?>;
    const _technicalAssignees = <?php echo json_encode($technical_assignees) ?>;
    const _retiredUsers = <?php echo json_encode($retired_users) ?>;
</script>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/1.12.4/jquery.min.js"></script>
<script type="text/javascript" src="/assets/common/react/pages/support/issues.bundle.js"></script>
<input type="hidden" name="redmine_setting_error" id="redmine_setting_error" value="<?php echo ($redmine_setting_error) ?>" />
<input type="hidden" name="redmine_user_error" id="redmine_user_error" value="<?php echo ($redmine_user_error) ?>" />

<!-- 絞り込み -->
<div class="flex" style="background-color: #F6F7F9; padding: 12px; margin-bottom: 12px;">
    <div class="" style="display:flex; align-items:center;">
        <!-- statusで絞り込み -->
        <label style="flex:none; margin: 0 4px 0 0; font-weight: bold;"><?php echo __('admin.common.label.status_kana') ?></label>
        <div class="pulldown">
            <div class="talkappi-pulldown" style="margin:0 16px 0 0;" data-name="status" data-value="<?php echo isset($status) ? $status : NULL; ?>" data-source='<?php echo $statuses; ?>'></div>
        </div>
        <!-- カテゴリで絞り込み -->
        <label style="flex:none; margin: 0 4px 0 0; font-weight: bold;"><?php echo __('admin.common.label.category') ?></label>
        <div class="pulldown">
            <div class="talkappi-pulldown" style="margin:0 16px 0 0;" data-name="category" data-value="<?php echo isset($category) ? $category : NULL; ?>" data-source='<?php echo $categories; ?>'></div>
        </div>
        <label style="flex:none; margin: 0 4px 0 0; font-weight: bold;">完了/却下を表示する</label>
        <div class="talkappi-radio" data-name="show_all_items" style="margin:0 16px 0 0;"
            data-value='<?php echo $show_all_items ? $show_all_items : "0" ?>'
            data-source='{"0":"<?php echo __('admin.common.label.undisplay'); ?>", "1":"<?php echo __('admin.common.label.display'); ?>"}'>
        </div>
        <!-- 担当者で絞り込み（現在は非表示） -->
        <!-- <label style="flex:none; margin: 0 12px 0 0;"><?php echo __('admin.common.label.person_in_charge') ?></label>
        <div class="pulldown">
            <div class="talkappi-pulldown" data-name="assignee" data-value="<?php echo isset($assignee) ? $assignee : NULL; ?>" data-source='<?php echo $assignees; ?>'></div>
        </div> -->
        <button style="margin-left:0" type="button" id="searchButton" class="btn-smaller btn-blue"><span class="icon-filter"></span><?php echo __('admin.common.label.narrowdown'); ?></button>
        <button type="button" id="clearButton" class="btn-smaller btn-white"><?php echo __('admin.common.button.reset'); ?></button>
    </div>
</div>

<div class="flex-x-between" style="margin-bottom:12px">
    <span></span>
    <span class="btn-smaller btn-blue" id="add-issue">
        <span class="icon-add-white"></span>新規作成
    </span>
</div>

<!-- table -->
<div class="react-issues-table content-container white border" data-lang_cd="<?php echo $_lang_cd ?>" style="flex-direction:column; display:flex; gap:24px;"></div>

<script type="text/javascript">
    jQuery(document).ready(function($) {
        if (typeof _issuesData !== 'undefined' && _issuesData !== null) {
            window.talkappi_setupIssuesPage({
                issuesData: _issuesData,
                technicalAssignees: _technicalAssignees,
                serviceCategories: _service_categories,
                retiredUsers: _retiredUsers,
            });
        }
    });
</script>

<?php if (isset($show_bot_box) && $show_bot_box === true) { ?>
    <script>
        const _show_bot_box = <?php echo $show_bot_box ?>
    </script>
<?php } ?>