<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
		<meta name="author" content="colorlib.com">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>申し込み | talkappi</title>
    <!-- Font Icon -->
    <link rel="stylesheet" href="/assets/apply/fonts/material-icon/css/material-design-iconic-font.min.css">
    <!-- Main css -->
    <link rel="stylesheet" href="/assets/apply/css/style.css">
</head>

<body>

    <div class="main">

        <div class="container">
            <div class="signup-content">
                <div class="signup-desc">
                    <div class="signup-desc-content">
                        <h2><span>talk</span>appi</h2>
                        <p class="title">Sign up now to try 30 days for free AI chatbot</p>
                        <p class="desc">
                            MIT licensed illustrations for every project you can imagine and create
                        </p>
                        <img src="/assets/apply/images/botimage.png" alt="" style="width:360px;">
                    </div>
                </div>
                <div class="signup-form-content">
                    <form method="POST" id="signup-form" class="signup-form" enctype="multipart/form-data">
                    <input type="hidden" name="flg" value="1" />
                   	<div class="content clearfix">
                        <h3></h3>
                        <fieldset>
                        	<?php echo $content ?>
                        </fieldset>
                    </div>
					<div class="actions clearfix">
						<ul role="menu" aria-label="Pagination">
							<li aria-hidden="false" aria-disabled="false" <?php if ($_step < 3 || $_step == 5) echo('style="display: none;"')?> >
								<a href="javascript:void();" id="prev" role="menuitem">前へ</a></li>
							<li aria-hidden="false" aria-disabled="false"　<?php if ($_step == 5) echo('style="display: none;"')?>>
								<a href="javascript:void();" id="next" role="menuitem">次へ</a></li>
							<li aria-hidden="true" style="display: none;">
								<a href="javascript:void();" role="menuitem">Finish</a></li>
						</ul>
					</div>
                    </form>
                </div>
            </div>
        </div>

    </div>

    <!-- JS -->
    <script src="/assets/apply/vendor/jquery/jquery.min.js"></script>
    <script src="/assets/apply/vendor/jquery-validation/dist/jquery.validate.min.js"></script>
    <script src="/assets/apply/vendor/jquery-validation/dist/additional-methods.min.js"></script>
    <script src="/assets/apply/vendor/jquery-steps/jquery.steps.min.js"></script>
    <script src="/assets/apply/step.js"></script>
    <script src="/assets/apply/<?php echo($_js);?>"></script>
    <!-- 
    <script src="/assets/apply/js/main.js"></script>
     -->

</body>

</html>