<span class="step-current">Step <?php echo $_step ?> / 5</span>
<div class="form-group" style="display:none;"> 
	<input type="text" name="bot_name" id="bot_name" value="<?php echo $post['bot_name']?>" required/>
	<label for="bot_name">施設名</label>
	<label class="error"><?php if (array_key_exists('bot_name', $messages)) echo($messages['bot_name'])?></label>
</div>
<div class="form-group radio-wrap text-center" style="display:none;"> 
	<h2 class="input-title">部屋数</h2>
	<label class="label-radio"> <input type="radio" name="room_count" value="50"/> <span class="lever">50以下</span> </label> 
	<label class="label-radio"> <input type="radio" name="room_count" value="100"/> <span class="lever">50～100</span> </label> 
	<label class="label-radio"> <input type="radio" name="room_count" value="1000"/> <span class="lever">1000～</span> </label>
	<br/>
	<label class="error"><?php if (array_key_exists('room_count', $messages)) echo($messages['room_count'])?></label>
</div>
<div class="form-group checkbox-wrap text-center">
	<h2 class="input-title">言語選択</h2>
	<?php foreach($lang as $k=>$v) {
		echo('<label class="label-checkbox">');
		if (in_array($k, $post['lang_cd'])) {
			echo('<input type="checkbox" name="lang_cd[]" checked value="' . $k . '"/>');
		}
		else {
			echo('<input type="checkbox" name="lang_cd[]" value="' . $k . '"/>');
		}
		echo('<span class="lever">' . $v . '</span></label>');
	}
	?>
	<br/>
	<label class="error"><?php if (array_key_exists('lang_cd', $messages)) echo($messages['lang_cd'])?></label>
</div>
<div class="form-group checkbox-wrap text-center">
	<h2 class="input-title">SNS選択</h2>
	<?php foreach($sns as $k=>$v) {
		echo('<label class="label-checkbox">');
		if (in_array($k, $post['sns_type_cd'])) {
			echo('<input type="checkbox" name="sns_type_cd[]" checked value="' . $k . '"/>');
		}
		else {
			echo('<input type="checkbox" name="sns_type_cd[]" value="' . $k . '"/>');
		}
		echo('<span class="lever">' . $v . '</span></label>');
	}
	?>	
	<br/>
	<label class="error"><?php if (array_key_exists('sns_type_cd', $messages)) echo($messages['sns_type_cd'])?></label>		
</div>                                                                  
