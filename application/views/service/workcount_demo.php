<!DOCTYPE html>
<html>

<head>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css" rel="stylesheet"
        integrity="sha384-EVSTQN3/azprG1Anm3QDgpJLIm9Nao0Yz1ztcQTwFspd3yD65VohhpuuCOmLASjC" crossorigin="anonymous">

</head>

<body>
    <div class="container">
        <div class="row">
            <div  class="col-md-4">
                <form id="myForm">
                    <div class="mb-3">
                        <label for="bot_id" class="form-label">bot_id</label>
                        <input type="number" class="form-control" id="bot_id" value="17" required>
                    </div>
                    <div class="mb-3">
                        <label for="intent_type_cd" class="form-label">intent_type_cd</label>
                        <input type="text" class="form-control" id="intent_type_cd">
                    </div>
                    <div class="mb-3">
                        <label for="log_time_begin" class="form-label">log_time_begin</label>
                        <input type="date" class="form-control" id="log_time_begin" value=<?php echo($cur_date); ?> >
                    </div>
                    <div class="mb-3">
                        <label for="log_time_end" class="form-label">log_time_end</label>
                        <input type="date" class="form-control" id="log_time_end" value=<?php echo($cur_date); ?> >
                    </div>
                    <div class="mb-3">
                        <label for="lang_cd" class="form-label">lang_cd</label>
                        <input type="text" class="form-control" id="lang_cd" value="ja">
                    </div>
                    <button class="btn btn-primary">Submit</button>
                </form>
                <div id="resultWrapper">
                    <pre id="result"></pre>
                </div>
            </div>
            <div  class="col-md-4"><canvas height="400" width="400" id="canvas" class="mt-5"></canvas></div>
        </div>
    </div>
</body>
<style>
    #resultWrapper {
        height: 500px;
        overflow-y: scroll;
    }
</style>
<script src="/assets/service/wordcloud/js/wordcloud2.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/js/bootstrap.bundle.min.js"
    integrity="sha384-MrcW6ZMFYlzcLA8Nl+NtUVF0sA7MsXsP1UyJoMp4YLEuNSfAP+JcXn/tWtIaxVXM"
    crossorigin="anonymous"></script>
<script src="/assets/service/wordcloud/js/jquery-3.6.0.min.js"></script>
<script>
    let wordListLength = 30
    $('#myForm').submit(function (e) {
        e.preventDefault()
        let bot_id = $('#bot_id').val()
        let log_time_begin = $('#log_time_begin').val()
        let log_time_end = $('#log_time_end').val()
        let intent_type_cd = $('#intent_type_cd').val()
        let lang_cd = $('#lang_cd').val()
        let query_text = `?bot_id=${bot_id}`
        if (log_time_begin !== '') {
            query_text += `&log_time_begin=${log_time_begin}`
        }
        if (log_time_end !== '') {
            query_text += `&log_time_end=${log_time_end}`
        }
        if (intent_type_cd !== '') {
            query_text += `&intent_type_cd=${intent_type_cd}`
        }
        if (lang_cd !== '') {
            query_text += `&lang_cd=${lang_cd}`
        }
        $('#result').text("Process...")
        $.ajax({
            url: '/apiservice/wordcount/' + query_text,
            success: function (data) {
                data = JSON.parse(data);
                $('#result').text(JSON.stringify(data, null, 2))
                console.log(data)
                let wordList = []
                for (const [key, value] of Object.entries(data['word_count'])) {
                    wordList.push([key, value])
                }
                wordList.sort((a, b) => { return b[1] - a[1] })
                wordList = wordList.slice(0, wordListLength)
                console.log(wordList)
                WordCloud(document.getElementById("canvas"), {
                    list: wordList,
                    clearCanvas: true,
                    backgroundColor: '#EEF1DB',
                    shrinkToFit: true,
                    // drawOutOfBound: true,
                    rotateRatio: 0,
                    // rotationSteps: 2,
                    backgroundColor: '#ffe0e0',
                    // weightFactor: function (size) {
                    //     return Math.pow(size, 0.8) * $('#canvas').width() / 1024;
                    // },
                    gridSize: 3
                });
            },
            error: function (error) {
                $('#result').text(JSON.stringify(error.responseText, null, 2))
            }
        })
    })

</script>

</html>