<?php if ($signature_id) { ?>
<script src="/assets/common/react/components/blocks/adminlangtabs.bundle.js"></script>
<?php } ?>
<div class="content-container white border">
    <?php if ($signature_id) { ?>
    <div class="flex-x-between">
        <div id="react-adminlangtabs"></div>
    </div>
    <?php } ?>
    <div class="section-container">
        <div class="form-body">
            <div class="form-group">
                <label class="control-label col-md-1"><?php echo __('admin.signature.label.title') ?></label>
                <div class="col-md-9">
                    <input name="sign_title" id="sign_title" type="text" max-length="40" class="form-control" style="width:100%;" placeholder="" value="<?php echo $signature ? $signature['sign_title'] : ''; ?>">
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-1"><?php echo __('admin.signature.label.detail') ?></label>
                <div class="col-md-9">
                    <textarea id="sign_detail" name="sign_detail" class="form-control" rows="20"><?php echo $signature ? $signature['sign_detail'] : ''; ?></textarea>
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-1">自分に割当</label>
                <div class="control-label col-md-9">
                    <div class="checkbox-label" style="text-align: left;">
                        <input type="checkbox" name="sign_assign_self" id="sign_assign_self" <?php if ($assign_to_self) echo ('checked') ?> value="<?php echo $assign_to_self ? '1' : '0' ?>">
                        <label for="sign_assign_self" style="margin-right:0px; height: 0px;"></label>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="form-actions">
        <div class="row">
            <div class="col-md-offset-2 col-md-9">
                <div class="actions-container">
                    <button type="button" class="action-button page btn-blue js-action-save"><?php echo __('admin.common.button.save'); ?></button>
                    <a class="action-button page btn-white js-action-back"><?php echo __('admin.common.button.return_to_list'); ?></a>
                    <?php if ($signature_id) { ?>
                    <span class="action-button page btn-red-border icon-only js-action-delete"><span class="icon-delete"></span></span>
                    <?php } ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php if ($signature_id) { ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 表示言語
    const displayedLangs = Object.keys(<?php echo json_encode($display_lang, JSON_UNESCAPED_UNICODE) ?>);
    // サポート言語
    const allLangs = [];
    // 非表示言語
    const undisplayedLangs = allLangs.filter(key => !displayedLangs.includes(key));

    window.talkappi_admin_setupAdminLangTabs({
        displayed: displayedLangs,
        undisplayed: undisplayedLangs,
        url: `/admin/signature?id=<?= $signature_id ?>&lang=`,
        active: '<?= $lang_cd ?>',
    });
});
</script>
<?php } ?>