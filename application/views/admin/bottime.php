<?php 
	if (strlen($_active_menu) > 4) {
		$tab_menu = View::factory ('admin/menutab');
		echo($tab_menu);
	}
?>
<input type="hidden" name="busitime_no" id="busitime_no" value="<?php if ($post != NULL) echo($post['busitime_no'])?>" />
<input type="hidden" id="isedit" value="<?php echo($isEdit)?>" />
<div class="content-container white border">
	<table class="table table-striped table-bordered table-hover js-data-table">
	<thead>
	<tr>
		<th style="width: 40px;">No</th>							
		<th style="width: 150px;">期間名</th>
		<th style="width: 80px;">種類</th>
		<th style="width: 80px;">営業時間</th>
		<th>時間外処理</th>
		<th style="width: 150px;">編集操作</th>
	</tr>
	</thead>
	<tbody>
	<?php
		foreach ($busitimes as $item) {
	?>
	<tr class="gradeX odd" role="row">
		<td>
			<?php echo($item->no)?>
		</td>
		<td>
			<?php echo($item->busi_name)?>
		</td>
		<td>
			<?php echo($_codes['12'][$item->busi_type_cd])?>
		</td>
		<td>
		<?php echo(substr_replace($item->start_time, ':', 2, 0))?>～<?php echo(substr_replace($item->end_time, ':', 2, 0))?>
		</td>
		<td style="line-height:2">
			<!-- <?php echo($item->start_date)?>～<?php echo($item->end_date)?> -->
			<?php echo($item->message)?>
			<div busitime_no="<?php echo($item->no)?>">
			<div class="talkappi-skill-select" data-value='<?php echo(json_encode($skills[$item->busi_name . $item->no], JSON_UNESCAPED_UNICODE)) ?>' data-mode="skill"></div>				
			</div>
		</td>
		<td>
			<button type="button" class="btn yellow action" act="01" busitime_no="<?php echo($item->no)?>">編集</button>
			<button type="button" class="btn red action" act="02" busitime_no="<?php echo($item->no)?>">削除</button>
		</td>
	</tr>
	<?php } ?>
	</tbody>
	</table>
	<br/>
	<button type="button" class="btn green-meadow action"  act="00"><i class="fa fa-file mr10"></i>新規</button>
	<?php if ($isEdit) { ?>
	<h4 class="form-section"></h4>
		<div class="form-body">
			<div class="form-group">
				<label class="control-label col-md-2">期間名</label>
				<div class="col-md-3">
					<input name="busi_name" id="busi_name" type="text" class="form-control" value="<?php if ($post != NULL) echo($post['busi_name'])?>">
				</div>
			</div>
			<div class="form-group">
				<label class="control-label col-md-2">種類</label>
				<div class="col-md-3">
					<?php echo Form::select('busi_type_cd', $_codes['12'], $post['busi_type_cd'], array('id'=>'busi_type_cd','class'=>'form-control'))?>
				</div>
				<div class="col-md-5" style="display:none;" id="holiday_div">
					<?php echo Form::select('holiday', $holidays, '', array('id'=>'holiday','class'=>'form-control'))?>
				</div>										
			</div>
			<div class="form-group">
				<label class="control-label col-md-2">営業時間</label>
				<div class="col-md-2">
					<div class="input-group">
						<input name="start_time" type="text" class="form-control timepicker timepicker-24" value="<?php if ($post != NULL) echo($post['start_time'])?>">
						<span class="input-group-btn">
						<button class="btn default" type="button"><i class="far fa-clock"></i></button>
						</span>
					</div>
				</div>
				<div class="col-md-2">
					<div class="input-group">
						<input name="end_time" type="text" class="form-control timepicker timepicker-24" value="<?php if ($post != NULL) echo($post['end_time'])?>">
						<span class="input-group-btn">
						<button class="btn default" type="button"><i class="far fa-clock"></i></button>
						</span>
					</div>
				</div>
			</div>
			<div class="form-group">
				<label class="control-label col-md-2">営業日期間（特例）</label>
				<div class="col-md-2">
					<input name="start_date" value="<?php if ($post != NULL) echo($post['start_date'])?>" class="form-control form-control-inline input-small date-picker" data-date-format="yyyy-mm-dd" size="16" type="text"/>
				</div>
				<div class="col-md-2">
					<input name="end_date" value="<?php if ($post != NULL) echo($post['end_date'])?>" class="form-control form-control-inline input-small date-picker" data-date-format="yyyy-mm-dd" size="16" type="text"/>
				</div>
			</div>
			<div class="form-group">
				<label class="control-label col-md-2">時間外表示メッセージ</label>
				<div class="col-md-8">
					<div class="input-icon right">
						<input name="message" id="message" type="text" class="form-control" placeholder="営業時間外表示するメッセージのCODE" value="<?php if ($post != NULL) echo($post['message'])?>">
					</div>
				</div>
			</div>
		</div>
		<div class="form-actions">
			<div class="row">
				<div class="col-md-offset-2 col-md-9">
					<button type="button" id="saveBaseButton" class="btn blue mr10">
					<i class="fa fa-save mr10"></i>保存</button>
					<button type="reset" class="btn gray">リセット</button>
				</div>
			</div>
		</div>
		<?php } ?>
</div>



