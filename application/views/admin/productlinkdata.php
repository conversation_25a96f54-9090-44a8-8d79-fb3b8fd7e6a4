
<script type="text/javascript">
const json_data = <?php echo($json_data) ?>;
</script>
<div class="page-head">
	<!-- BEGIN PAGE TITLE -->
	<div class="page-title">
		<h1>商品管理 <small></small></h1>
	</div>
	<!-- <PERSON>ND PAGE TITLE -->
</div>
<!-- END PAGE HEAD -->
<!-- END PAGE HEADER-->
<!-- BEGIN PAGE CONTENT-->
<div class="row">
	<div class="col-md-12">
		<!-- Page Content -->
		<div id="page-wrapper">
			<div class="top-nav font-standard">
			<?php echo $menu ?>
				<div class="edit-container">
					<div class="settings-container">
						<input type="hidden" name="image_url" id="image_url" value="" />
						<div class="row">
							<div class="col-md-12">
								<div class="form-body">
								<?php 
								$image_urls = [];
								if (array_key_exists('RoomDescription', $link_data)) {
									$name = $link_data['RoomDescription']['$']['Name'];
									$desc = $link_data['RoomDescription']['Text'];
									if (array_key_exists('URL', $link_data['RoomDescription']))
										$image_urls = $link_data['RoomDescription']['URL'];
								}
								else {
									$name = $link_data['$']['RatePlanName'];
									$desc = $link_data['RatePlanDescription']['Text'];
									if (array_key_exists('URL', $link_data['RatePlanDescription']))
										$image_urls = $link_data['RatePlanDescription']['URL'];
								}
								if (is_array($image_urls)) {
									if ($image_urls[0] == '') $image_urls = [];
								}
								else {
									if ($image_urls != '') $image_urls = [$image_urls];
								}
								//if (!is_array($image_urls) && $image_urls != '') $image_urls = [$image_urls];
								?>
								<div class="form-group">
									<label class="control-label col-md-2" <?php if ($product_description->product_name != str_replace("\r", "", $name)) echo('style="color:red;"')?>>名称</label>
									<div class="col-md-8">
										<div class="input-icon right">
											<textarea name="product_name" class="form-control" maxlength="200" rows="2" placeholder=""><?php echo($name);?></textarea>													
										</div>
									</div>
								</div>
								<div class="form-group">
								<?php 
								if ($product_description->description != str_replace("\r", "", $desc)) {
									$style = 'style="color:red;"';
								}
								else {
									$style = '';
								}
								?>
									<label class="control-label col-md-2" <?php echo $style?> >詳細説明</label>
									<div class="col-md-10">
										<textarea name="product_description" class="form-control" maxlength="3000" rows="2" placeholder=""><?php echo($desc);?></textarea>
									</div>
								</div>
								<div class="form-group">
									<label class="control-label col-md-2">画像</label>										
									<?php if (count($image_urls) == 0) {
										echo('<div class="col-md-10" style="margin-top:10px;">');
										echo('画像が設定されていません');
									}
									else {											
									?>
									<div class="col-md-10">
									<div class="xscroll-wrapper">
										<?php 
										$tl_config = $config['tl_config'];
										foreach($image_urls as $item) {
											$item = str_replace($tl_config['original_image_url'], $tl_config['mapping_image_url'], $item);
											echo('<div class="card">');
											echo('<a href="' . $item . '" target="blank"><img class="image" alt="" src="' . $item . '" />');
											echo('<div class="item-caption">');
											echo($name);
											echo('</div>');
											echo('<div class="item-description">');
											echo($desc);
											echo('</div>');
											echo('<div class="item-button-horizontal">');
											echo('<a class="button use-image-car" url="' . $item . '" button_type="url" href="javascript:void(0);">' . 'メイン画像に' . '</a>');
											echo('<a class="button use-image" url="' . $item . '" button_type="url" href="javascript:void(0);">' . '画像に追加' . '</a>');
											echo('</div>');
											echo('</div>');
											}?>
									</div>
									<?php }?>
									</div>
								</div>
								<div class="form-group" <?php if ($_user->role_cd != '99') echo('style="display:none;"');?>>
									<label class="control-label col-md-2">データ <a id="show_data" href="javascript:void(0);">＋</a></label>
									<div class="col-md-10">
									<pre id="product_data" style="display:none;"></pre>
									</div>
								</div>																									
								</div>											
								<div class="form-actions">
									<div class="row">
										<div class="col-md-offset-2 col-md-9 flex">
											<button type="button" class="btn-larger btn-blue js-action-save" style="display:none;">>すべて反映</button>													
											<button type="button" onclick="top.location='/admin/products'" class="btn-larger btn-white js-action-back">戻る</button>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
<!-- END PAGE CONTENT-->

