<style>
    .page-content-header {
        display: block !important;
    }
    .nav>li>a {
        padding: 0 10px !important;
    }
    .menu-toggler.responsive-toggler {
        display: none !important;
    }
    @media (max-width: 767px) {
        .page-content-wrapper .page-content {
            padding: 20px 0px 10px 0px !important;
            overflow: visible !important;
        }
        .navbar-nav {
            margin: 0 -15px !important;
        }
    }
    /* 言語選択の下線　begin */
    .select-lang ul  {
        gap: 6px 0px;
    }
    .select-lang li.current-lang  {
        position: relative;
    }
    .select-lang li.current-lang::after {
        content: '';
        height: 1px;
        display: inline-block;
        background: #245BD6;
        position: absolute;
        bottom: -1px;
        left: 0;
        right: 0; 
    }
    /* 言語選択の下線  end*/

    .talkappi-pulldown.js-translation-type.auto-translation .talkappi-dropdown-container.pulldown {
        background-color: #FFE6D6;
    }

    .talkappi-pulldown.js-translation-type .talkappi-dropdown-container.pulldown {
        background-color: #E3E5E8;
    }

    .bottom-buttons .js-preview {
        display: none;
    }

    /* スマホ対応 */
    @media screen and (max-width: 671px) {
        .translate-container {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .translate-container .basic-label{
            margin: 0 !important;
        }

        .bottom-buttons {
            flex-direction: column;
            gap: 12px;
        }

        .bottom-buttons button{
            margin: 0;
        }

        .bottom-buttons .js-preview {
            display: flex;
        }

        .js-input-container.ui-sortable {
            padding: 0 !important;
            width: 80vw;
        }

        .lang-tab {
            flex-direction: column;
        }
    }
</style>

<!-- ポップアップ -->
<style>
    .freeze-body {
    overflow: hidden;
  }
  .talkappi-popup-window {
    position: fixed;
    z-index: 9999999999999;
    width: 100vw;
    height: 100%;
    inset: 0;
    background-color: white;
  }
  .talkappi-popup-window-header {
    height: 52px;
    width: 100vw;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 12px 0 24px;
    box-sizing: border-box;
    background: #F6F7F9;
    box-shadow: 0px -1px 0px 0px #EBEDF2 inset;
  }
  .talkappi-popup-window-header .js-popup-window-close {
    position: absolute;
    left: 24px;
  }
  .popup-window-title {
    font-size: 14px;
  }
  .talkappi-popup-window-body {
    width: 100%;
    height: 100vh;
    overflow-y: auto;
    padding-top: 52px;
  }
  .popup-window-body-wrapper {
    height: 100%;
  }
  .popup-window-body-wrapper, 
  .popup-window-body-wrapper .mobile-preview, 
  .popup-window-body-wrapper .mobile-preview .main-container {
    width: 100%;
    height: 100%;
  }
</style>

<script>
	const _results = <?php echo json_encode($results) ?>;
    const _msg_id = "<?php echo $msg_id ?>";
    const _msg_cd = "<?php if(isset($msg->msg_cd)) echo $msg->msg_cd ?>";
    const _lang_edit = '<?php echo $lang_edit ?>';
    const _is_chatbot_use = <?php echo json_encode($is_chatbot_use); ?>;
    const _preview_data = <?= json_encode($preview_data, JSON_UNESCAPED_UNICODE) ?>;
    const _bot_name = "<?= $bot_name ?>";
</script>

<!-- <input type="hidden" id="message" value="<?php echo $message?>" /> -->
<input type="hidden" name="lang" value="<?php echo $lang_edit?>" />
<input type="hidden" id="act" name="act" value="" />
<input type="hidden" id="msg" name="msg" value="" />

<div class="content-container white border">
    <?php if ($is_disaster_mode) { ?>
        <h2 class="disaster-mode-notice" style="color: red;">
        <span><?php echo $bot_name ?></span>
        <span style="display: inline-block; width: 1ch;"></span>
        <span><?php echo __('admin.disaster.label.disaster_mode'); ?></span>
        </h2>
    <?php } ?>
    <?php if($msg->msg_cd === 'welcome_disaster'){?>
        <div class="setting-header"><?php echo __('admin.disaster.label.welcome_setup') ?></div>
    <?php } else if($msg->msg_cd === 'talkappi_greeting_disaster'){?>
        <div class="setting-header"><?php echo __('admin.disaster.label.bubble_disaster_mode') ?></div>
    <?php }?>
    <!-- メインコンテンツ -->
    <div class="flexbox js-main">
        <div id="js-sort-content" style="flex-grow: 10; width: 50%;">
            <div class="lang-tab" style="display:flex;justify-content:space-between;align-items:flex-start;margin-top:30px;gap:20px;">
                <!-- 言語切り替えタブ -->
                <nav class="select-lang">
                    <ul class="flexbox" style="margin: 0; padding-inline-start: 0px; flex-flow: wrap;">
                        <?php 
                            $lang = NULL;
                            if ($msg_id != NULL) foreach($_bot_lang as $lang_cd=>$lang_name) {
                                if($lang_cd === $lang_edit){
                                    $lang = $lang_name;
                                    echo('<li class="current-lang">');
                                } else {
                                    echo('<li>');
                                }
                                echo('<a class="func-menu" href="/admin/disaster?id='. $msg_id .'&lang_cd='. $lang_cd . '">' . $lang_name . '</a></li>');
                            }
                        ?> 
                    </ul>
                </nav>
                <!-- 翻訳タイプ -->
                <?php 
                    $hide_translation_type = false;
                    if ($lang_edit == 'ja') {
                        $hide_translation_type = true;
                    }
                ?>
                <div class="talkappi-pulldown js-translation-type <?php echo $translation_type == '1' ? "auto-translation" : "" ?> <?php echo $hide_translation_type ? 'hide' : '' ?>" data-value="<?php echo $translation_type ?>" data-source='[{"code":"0","text":"<?php echo __('admin.common.label.native_translation') ?>"},{"code":"1","text":"<?php echo __('admin.common.label.auto_translation') ?>"}]'></div>
            </div>
            <div class="js-input-container" style="padding: 25px 25px 25px 0;"></div>
            <!-- 多言語反映 -->
			<?php if (count($_bot_lang) > 1) { ?>
            <div class="lines-container translate-container">
                <div class="">
                    <label class="basic-label" style="width: fit-content; margin-right: 24px !important;"><?php echo __('admin.common.label.reflect.all_lang') ?><?php echo $lang ?></label>
                </div>
                <div class="">
                    <div class="checkbox-label">
                        <input type="radio" name="translate" id="translate_no" value='translate_no'>
                        <label for="translate_no"><?php echo __('admin.common.label.not_reflect') ?></label>
                    </div>
                    <div class="checkbox-label">
                        <input type="radio" name="translate" id="translate_copy"  value='translate_copy'>
                        <label for="translate_copy"><?php echo __('admin.common.label.reflect.not.translate') ?></label>
                    </div>
                    <div class="checkbox-label js-translate_auto">
                        <input type="radio" name="translate" id="translate_auto" value='translate_auto'>
                        <label for="translate_auto"><?php echo __('admin.common.label.reflect.translate') ?></label>
                    </div>
                    <?php if ($flg_native_translate == "1") { ?>
                        <div class="checkbox-label js-request">
                            <input type="radio" name="translate" id="translate_native" value='translate_native'>
                            <label for="translate_native"><?php echo __('admin.common.label.reflect.native.translate') ?></label>
                        </div>
                    <?php } ?>
                </div>
            </div>
            <div class="lines-container js-trans-content" style="margin: 16px 0px 0px 200px; display: none;">
                <div class="edit-menu-container" style="width: auto; display: flex; gap: 8px; flex-direction: column; font-weight:300; min-width: auto;">
                    <div>
                        <p><?php echo __('admin.common.native.translation.desc') ?></p>
                        <p><?php echo __('admin.common.native.translation.explain') ?></p>
                    </div>
                    <div class="flexbox-x-axis" style="gap: 12px;">
                        <p><?php echo __('admin.common.label.expect.lang') ?></p>
                        <div class="flexbox-x-axis" style="gap: 8px; flex-wrap: wrap;">
                        <?php
                            foreach($_bot_lang as $k=>$v) {
                                $style='';
                                if ($k == $lang_edit || ($k == 'js' || $k == 'th' || $k == 'vi' || $k == 'ne')) $style='display: none;';
                                if (!in_array($k, $native_support_lang)) {
                                    $style='display: none;';
                                }
                                echo('<div class="checkbox-label translate-lang flexbox-x-axis" style="' . $style . '">');
                                echo('<input type="checkbox" value="' . $k . '" name="translate_lang[]" id="translate_to_' . $k.'">');
                                echo('<label for="translate_to_'.$k.'" style="margin: 0; white-space: nowrap;">'. $_bot_lang[$k] .'</label>');
                                echo('</div>');
                            }
                        ?>
                        </div>
                    </div>
                    <div style="display:flex">
                        <div class="talkappi-pulldown js-trans-priority" style="width: fit-content;" data-name="trans-priority" data-value="2" data-source='{"4":"<?php echo __('admin.common.label.priority') ?>：<?php echo __('admin.common.label.priority.high') ?>", "2":"<?php echo __('admin.common.label.priority') ?>：<?php echo __('admin.common.label.priority.normal') ?>", "1":"<?php echo __('admin.common.label.priority') ?>：<?php echo __('admin.common.label.priority.low') ?>"}'></div>
                        <div style="align-self: center; margin-left: 1em;" class="js-trans-estimate"></div>
                    </div>
                    <textarea class="fullwidth-textarea menu-text" style="white-space: pre-wrap;word-wrap: break-word;display:block;max-width:100%;" name="translate_comment" placeholder="<?php echo __('admin.common.native.translation.placeholder') ?>"></textarea>
                </div>
            </div>
            <?php } ?>
        </div>
        <div id="msg-preview" style="margin:35px 0 0 48px;">
            <div class="talkappi-preview js-msg-preview preview-sticky" data-type="message" style="margin: 0 0 0 auto; flex-basis: 30%; max-width:320px;">
            </div>
        </div>
    </div>
    <!-- 下部ボタン類 -->
	<div class="form-actions js-template-check" style="margin: 60px 0 0 0;" data-msgcd="<?php echo $post['msg_cd']?>" data-classcd="<?php echo $post['msg_class_cd']?>">
		<div class="row">
			<label class="control-label col-md-2 label-fix-6"></label>
			<div class="col-md-10 flex bottom-buttons">
				<?php 
					if ($_bot_id == 0) {
						echo('<button type="button" class="btn-larger btn-blue js-action-save">' . __('admin.common.button.save') . '</button>');
					}
					else {
						if ($mode == 'inherit') {
                            echo('<button type="button" class="btn-larger btn-blue js-action-save">' . __('admin.common.button.save') . '</button>');
							//echo('<button type="button" class="btn-larger btn-blue js-action-save">カスタマイズ</button>');
						}
						else {
							echo('<button type="button" class="btn-larger btn-blue js-action-save">' . __('admin.common.button.save') . '</button>');
						}
					}
				?>
                <button type="button" class="btn-larger btn-white js-preview"><?= __('admin.common.button.preview') ?></button>
                <?php if ($mode != 'inherit') {?>
                    <!-- 該当言語のみ削除 -->
                    <button type="button" class="btn-larger btn-red-border js-action-delete-lang" style="color: black;"
                    >
                        <span class="icon-delete" style="margin-right: 8px !important;"></span>
                        <?php
                        if ($lang_edit === 'ja') {
                            echo __('admin.common.button.delete_ja');
                        } elseif ($lang_edit === 'js') {
                            echo __('admin.common.button.delete_js');
                        } elseif ($lang_edit === 'en') {
                            echo __('admin.common.button.delete_en');
                        } elseif ($lang_edit === 'cn') {
                            echo __('admin.common.button.delete_cn');
                        } elseif ($lang_edit === 'tw') {
                            echo __('admin.common.button.delete_tw');
                        } elseif ($lang_edit === 'kr') {
                            echo __('admin.common.button.delete_kr');
                        } elseif ($lang_edit === 'th') {
                            echo __('admin.common.button.delete_th');
                        } elseif ($lang_edit === 'vi') {
                            echo __('admin.common.button.delete_vi');
                        } elseif ($lang_edit === 'ne') {
                            echo __('admin.common.button.delete_ne');
                        } elseif ($lang_edit === 'it') {
                            echo __('admin.common.button.delete_it');
                        } elseif ($lang_edit === 'es') {
                            echo __('admin.common.button.delete_es');
                        } elseif ($lang_edit === 'de') {
                            echo __('admin.common.button.delete_de');
                        } elseif ($lang_edit === 'fr') {
                            echo __('admin.common.button.delete_fr');
                        } elseif ($lang_edit === 'pt') {
                            echo __('admin.common.button.delete_pt');
                        } elseif ($lang_edit === 'ru') {
                            echo __('admin.common.button.delete_ru');
                        } elseif ($lang_edit === 'id') {
                            echo __('admin.common.button.delete_id');
                        } else {
                            echo __('admin.common.button.delete_other');
                        }
                        ?>
                    </button>
                <?php }?>
                <?php if ($mode != 'self' && $_bot_id > 0){ ?>
                    <!-- 全言語を削除 -->
                    <?php if ($mode != 'inherit') {?>
                        <button type="button" class="btn-larger btn-gray-black js-action-reset"><?php echo __('admin.common.label.reset_to_default_all')?></button>
                    <?php }?>
                <?php } else { ?>
                    <!-- 全言語を削除 -->
                    <button type="button" class="btn-larger btn-red-border js-action-delete" style="color: black;"
                    >
                        <span class="icon-delete" style="margin-right: 8px !important;"></span><?php echo __('admin.common.button.delete_all') ?>
                    </button>
                <?php } ?>
				<button type="button" onclick="top.location='/admin/disastersetting'" class="btn-larger btn-white js-action-back"><?php echo __('admin.itme.itemmeu.back') ?></button>
			</div>
		</div>
	</div>
</div>