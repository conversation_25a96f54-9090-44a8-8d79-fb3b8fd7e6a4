
				<?php 
					foreach ($items as $k=>$v) {
						echo('<li>');
						echo('<a href="javascript:void(0);" class="user_status" status="' . $k . '">');
						echo($v);
						echo('</a>');
						echo('</li>');
					}
				?>
				</li>
				<?php 
				$has_online = false;
				$online_count = count($online_user[1]);
				echo('<div style="background-color: #2E3138;">');
				echo('<li style="padding: 8px 14px;">');
				echo('<div style="padding-bottom: 6px;">' .  __('admin.chat.header.online_operator') . ' (' . $online_count . ')' . '</div>');
				echo('<div class="status-operator-languages">');
				foreach ($online_user[0] as $k=>$v) {
					if ($k == '') continue;
					echo('<span>' . $lang_arr[$k] . ' (' . $v . ')</span>');
					$has_online = true;
				}
				echo('</div>');
				echo('</li>');
				foreach ($online_user[1] as $v) {
					echo('<li style="padding: 8px 10px 8px 14px; width: fit-content; position: relative;">');
					if (is_array($v)) { // 親ボット
						echo('<span style="position: relative; padding-right: 10px;">');
						echo(htmlspecialchars($v['user_name']));
						echo('<span class="sidebar-user-online" style="top: 0;"></span>');
						echo('</span>');
						// ボット名を表示
						echo('<div style="font-size: 0.8em; color: gray;">(' . htmlspecialchars($v['bot_name']) . ')</div>');
					} else { // 通常または小ボット
						echo(htmlspecialchars($v));
						echo('<span class="sidebar-user-online"></span>');
					}
					echo('</li>');
				}
				echo('</div>');
				?>
				<li>
					<a href="/chat/logout">
					<i class="icon-key"></i><?php echo __('admin.template.menu.logout') ?></a>
				</li>
