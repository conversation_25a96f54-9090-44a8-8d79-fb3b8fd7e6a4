<?php 
	if (strlen($_active_menu) > 4) {
		$tab_menu = View::factory ('admin/menutab');
		echo($tab_menu);
	}
?>
<div class="content-container white border">
	<div class="form-body">
		<div class="form-group">
			<label class="control-label col-md-2">チャネル:</label>
			<div class="col-md-10">
				<?php
					$bot_snses = explode(',', $bot->sns_cd);
				?>
				<div class="talkappi-checkbox" data-name="snses" data-value='<?php echo json_encode($bot_snses)?>' data-source='<?php echo json_encode($_codes['16'], JSON_UNESCAPED_UNICODE) ?>'></div>
			</div>
		</div>
		<div class="form-group">
			<label class="control-label col-md-2">サポート言語:</label>
			<div class="col-md-10">
				<?php
					$support_langs = explode(',', $bot->support_lang);
				?>
				<div class="talkappi-checkbox js-support-language" data-name="support_langs" data-value='<?php echo json_encode($support_langs)?>' data-source='<?php echo json_encode($lang_array, JSON_UNESCAPED_UNICODE) ?>'></div>
			</div>
		</div>
		<div class="form-group">
			<label class="control-label col-md-2">表示言語:</label>
			<div class="col-md-10">
				<?php
					$bot_langs = explode(',', $bot->lang_cd);
				?>
				<div class="talkappi-checkbox js-language" data-name="langs" data-value='<?php echo json_encode($bot_langs)?>' data-source='<?php echo json_encode($lang_array, JSON_UNESCAPED_UNICODE) ?>'></div>
			</div>
		</div>
		<div class="form-group">
			<label class="control-label col-md-2">FAQ分類:</label>
			<div class="col-md-10">
				<?php
				$class_type = explode(',', $bot->class_type_cd);
				?>
				<div class="talkappi-checkbox" data-name="classes" data-value='<?php echo json_encode($class_type)?>' data-source='<?php echo json_encode($class_types, JSON_UNESCAPED_UNICODE) ?>'></div>
			</div>													
		</div>											
		<div class="form-group">
			<label class="control-label col-md-2">サービス分類:</label>
			<div class="col-md-10">
				<div class="talkappi-checkbox" data-name="intents" data-value='<?php echo json_encode($bot_def_intent)?>' data-source='<?php echo json_encode($intent_mst_array, JSON_UNESCAPED_UNICODE) ?>'></div>													
			</div>
		</div>											
	</div>

	<div class="form-action">
		<div class="form-group">
			<div class="col-md-offset-2 col-md-10">
				<button class="btn-larger btn-blue x-first js-action-save" <?php if ($_user->role_cd != "99") echo('style="display:none;"'); ?>>保存</button>
			</div>
		</div>											
	</div>
</div>



