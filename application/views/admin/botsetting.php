<script type="text/javascript">
    const _setting_template = <?php echo json_encode($settings_template, JSON_UNESCAPED_UNICODE) ?>;
</script>
<?php 
	if (strlen($_active_menu) > 4) {
		$tab_menu = View::factory ('admin/menutab');
		echo($tab_menu);
	}
?>
<div class="content-container white border">
	<div class="form-body">
		<?php
		foreach($settings_config as $k=>$v)
		{
			if (strpos($k, "group_separator") === 0) {
				echo('<div class="form-group">');
				echo('<label class="control-label col-md-2" style="font-weight: 600;">【' . $v[0] . '】</label>');
				echo('<div class="col-md-8">');
				echo('<hr>');
				echo('</div>');
				echo('</div>');
				continue;
			}
			echo('<div class="form-group">');
			echo('<div class="row">');
			if (array_key_exists($k, $settings) && $_bot->bot_id > 0) {
				$style = 'style="color:red;"';
			}
			else {
				$style = '';
			}
			$edit = '';
			if (strpos($k, 'json_') === 0) {
				if (array_key_exists($k, $settings)) {
					if (is_array(json_decode($settings[$k], true))) {
						$val = htmlspecialchars($settings[$k]);
						$edit = '<a href="javascript:void(0);" class="json" data-title="' . $v[0] . '" data-json="' . $val . '"><span class="badge badge-success">解析</span></a>';
					}
				}
				else {
					if (is_array(json_decode($v[1], true))) {
						$val = htmlspecialchars($v[1]);
						$edit = '<a href="javascript:void(0);" class="json" data-title="' . $v[0] . '" data-json="' . $val . '"><span class="badge badge-success">解析</span></a>';
					}
				}
			}
			$cols = '6';
			if (strpos($k, 'flg_') === 0 || substr_compare($k, '_flg', -strlen('_flg')) === 0) {
				$cols = '2';
			}
			echo('<label class="control-label col-md-4" ' . $style . '>' . $edit . $v[0] . '</label>');
			echo('<div class="col-md-' . $cols . '">');
			if (strpos($k, 'flg_') === 0 || substr_compare($k, '_flg', -strlen('_flg')) === 0) {
				if (array_key_exists($k, $settings)) {
					echo('<div class="talkappi-radio js-flg" data-name="' . $k . '" data-value="' . $settings[$k] . '" data-source=\'{"0":"OFF", "1":"ON"}\'></div>');
				}
				else {
					echo('<div class="talkappi-radio js-flg" data-name="' . $k . '" data-value="' . $v[1] . '" data-source=\'{"0":"OFF", "1":"ON"}\'></div>');
				}				
			}
			else if (strpos($k, 'json_') === 0) {
				if (array_key_exists($k, $settings)) {
					echo('<textarea class="form-control" name="' . $k . '" rows="2" placeholder="">' . htmlspecialchars($settings[$k]) . '</textarea>');
				}
				else {
					echo('<textarea class="form-control" name="' . $k . '" rows="2" placeholder="">' . htmlspecialchars($v[1]) . '</textarea>');
				}
				echo('<div class="json-error-message" style="color:red;display:none">JSON形式ではありません</div>');
			}
			else {
				if (array_key_exists($k, $settings)) {
					echo('<input type="text" class="form-control" name="' . $k . '" value="' . htmlspecialchars($settings[$k]) . '" >');
				}
				else {
					echo('<input type="text" class="form-control" name="' . $k . '" value="' . htmlspecialchars($v[1]) . '" >');
				}
			}
			echo('</div>');	
			if (array_key_exists($k, $settings)) {
				echo('<div class="col-md-1">');
				echo('<a href="javascript:void(0);" class="retdefault" data-setting="' . $k . '" ><span class="badge badge-danger">戻る</span></a>');
				echo('</div>');
			}
			else {
				if (strpos($k, 'flg_') !== 0 && substr_compare($k, '_flg', -strlen('_flg')) !== 0 && $settings_config[$k][1] === '' && $settings_template[$k][1] != '') {
					echo('<div class="col-md-1">');
					echo('<a href="javascript:void(0);" class="js-sample" data-setting="' . $k . '" ><span class="badge badge-warning">設定例</span></a>');
					echo('</div>');
				}
			}
			echo('</div>');
			echo('</div>');
		}
		?>
	</div>
	<div class="form-actions">
		<div class="row">
			<div class="col-md-offset-4 col-md-6 flex">
				<button type="button" class="btn-larger btn-blue js-action-save" data-bot-name="<?php echo $_bot->bot_name ?>" <?php if ($bot->bot_status_cd == '04' && $_user->role_cd != "99") echo("disabled"); ?>>
				保存</button>
				<button type="button" class="btn-larger btn-white js-action-reset">リセット</button>
			</div>
		</div>
	</div>
</div>

<?php echo $jsonbox ?>


