<?php $page_type = substr($_action, 0, strlen($_action) - 1);?>
<input type="hidden" name="task_id" id="task_id" value="" />
<input type="hidden" name="act" id="act" value="" />
<?php echo $menu ?>
<div class="content-container white border">
	<div class="row">	
		<div class="form-group flex">
			<label class="control-label col-md-2" style="padding-left:15px; padding-right:10px;">絞り込み条件</label>	
			<div class="col-md-2" style="width: 160px;">
				<?php echo Form::select('task_class_cd', $task_class, $post['task_class_cd'], array('id'=>'task_class_cd','class'=>'form-control'))?>
			</div>
			<div class="col-md-2" style="width: 160px;">
				<?php echo Form::select('task_status_cd', $task_status, $post['task_status_cd'], array('id'=>'task_status_cd','class'=>'form-control'))?>
			</div>		
			<div class="col-md-4 flex">
				<button type="submit" class="btn-smaller btn-yellow mr10">
				<i class="fa fa-search mr10"></i>検索</button>
			</div>																		
		</div>									
	</div>
	<table class="table table-striped table-bordered table-hover js-data-table">
		<thead>
			<tr>
				<th>タスク名</th>
				<th>ステータス</th>
				<th>実行予定時刻</th>
				<th>実行開始時刻 〜 実行終了時刻</th>
				<th>登録者</th>
				<th>登録時間</th>
				<th></th>										
			</tr>
		</thead>
		<tbody>
		<?php
			foreach ($tasks as $task) {
		?>	
		<tr class="gradeX odd" role="row">
			<td>
				<a href="/admin/<?php echo $page_type?>?id=<?php echo($task['task_id'])?>"><?php echo($task['task_name'])?></a> 
			</td>
			<td>
				<?php  
				if ($task['task_status_cd'] == "01") {
					echo('<span class="label label-primary" style="margin-left:5px;">' . $_codes['27'][$task['task_status_cd']] . '</span>');
				}
				else if ($task['task_status_cd'] == "02") {
					echo('<span class="label label-primary" style="margin-left:5px;">' . $_codes['27'][$task['task_status_cd']] . '</span>');
				}
				else if ($task['task_status_cd'] == "03") {
					echo('<span class="label label-success" style="margin-left:5px;">' . $_codes['27'][$task['task_status_cd']] . '</span>');
				}
				else if ($task['task_status_cd'] == "04") {
					echo('<span class="label label-warning" style="margin-left:5px;">' . $_codes['27'][$task['task_status_cd']] . '</span>');
				}
				else if ($task['task_status_cd'] == "05") {
					echo('<span class="label label-danger" style="margin-left:5px;">' . $_codes['27'][$task['task_status_cd']] . '</span>');
				}
				else if ($task['task_status_cd'] == "00") {
					echo('<span class="label label-default" style="margin-left:5px;">' . $_codes['27'][$task['task_status_cd']] . '</span>');
				}
				if ($task['task_type_cd'] == '03' && $task['task_status_cd'] == '03') {
					$path = APPPATH . "../../files/task/". $task['task_id'];
					$p = scandir($path);
					foreach($p as $val) {
						if($val !="." && $val !="..") {
							echo('<a href="#" class="js-filedownload" data-file-path="task/' . $task['task_id'] . '/" data-file-name="' . $val . '"><span class="icon-export" style="margin-left:6px;"></span></a>');
						}
					}
				}
				?>
			</td>
			<td>
				<?php echo($task['scheduled_time'])?>
			</td>
			<td>
				<?php  
				if ($task['task_status_cd'] == "02") {
					echo($task['process_time'] . ' 〜 ');
				}
				else if ($task['task_status_cd'] == "03" || $task['task_status_cd'] == "04" || $task['task_status_cd'] == "05") {
					echo($task['process_time'] . ' 〜 ' . $task['finish_time']);
				}
				?>
			</td>
			<td>
				<?php echo($task['name'])?> (<?php echo($task['create_program'])?>)
			</td>
			<td>
				<?php echo($task['upd_time'])?>
			</td>
			<td>
				<div class="btn round image copy js-copy" data-task_id="<?php echo ($task['task_id']) ?>">コピー</div>
			</td>								
		</tr>
		<?php } ?>
		</tbody>
	</table>
</div>
