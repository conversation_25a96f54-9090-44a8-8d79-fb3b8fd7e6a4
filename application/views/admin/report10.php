<style>
.portlet-body {
overflow-x:auto;
}
td {
text-align:right;
}
thead {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 2;
}
table {
    table-layout: fixed;
}
</style>
			<!-- BEGIN PAGE HEADER-->
			<!-- BEGIN PAGE HEAD -->
			<div class="page-head">
				<!-- BEGIN PAGE TITLE -->
				<div class="page-title">
					<h1>利用状況レポート<small></small></h1>
				</div>
				<!-- END PAGE TITLE -->
			</div>
			<!-- END PAGE HEAD -->
			<!-- END PAGE HEADER-->
			<!-- BEGIN PAGE CONTENT-->
			<div class="row">
				<div class="col-md-12">
			        <!-- Page Content -->
			        <div id="page-wrapper">
			        <div class="portlet light">			        
					<?php echo $reportmenu ?>
						<div class="portlet box">
							<div class="portlet-body">
								<div class="form-body">		
									<div class="form-group">
										<label class="control-label col-md-1" style="padding-left:15px; padding-right:15px;"><?php echo __('admin.common.label.period') ?></label>
										<div class="col-md-3" style="width: 265px;">
											<input name="start_date" id="start_date" value="<?php echo($start_date)?>" style="float:left;" class="form-control form-control-inline input-small date-picker" size="16" data-date-format="yyyy-mm-dd"  type="text"/>
											<input name="end_date" id="end_date" value="<?php echo($end_date)?>" style="float:left; margin-left:10px;" class="form-control form-control-inline input-small date-picker" size="16" data-date-format="yyyy-mm-dd"  type="text"/>
										</div>
										<?php echo $botcond ?>																				
										<label class="control-label col-md-1" style="width: 100px;"><?php echo __('admin.common.label.display_zero') ?></label>
										<div class="col-md-2">
											<input type="checkbox" id="utf-8" name="zero_flg" <?php if ($zero=='0') echo('checked')?> value="1" class="make-switch" data-on-color="success" data-off-color="warning">
										</div>											
										<div class="col-md-1">
											<button type="button" id="searchButton" class="btn yellow" disabled="disabled">
											<i class="fa fa-search mr10"></i><?php echo __('admin.common.button.search') ?></button>
										</div>		
										<div class="col-md-1" style="margin-left: 20px;">
											<button type="button" data-tableId="sample_3" data-title="国別統計" class="btn green exportCsv">
												<?php echo __('admin.common.button.csv_export') ?>
											</button>
										</div>
										<div class="col-md-1" style="display:none;">
											<button type="button" id="uaButton" class="btn yellow">
											UA出力</button>
										</div>								
									</div>
								</div>							
<?php if ($sel_bot_id == '') { ?>
							<table class="table table-striped table-bordered table-hover" id="sample_3">
							<thead>
							<tr>
								<th style="width:260px;text-align:center"><?php echo __('admin.common.label.by_country') ?></th>
								<th style="width:160px;text-align:center"><?php echo __('admin.common.label.total') ?></th>						
							<?php 
							$sns_array = explode(',', $_bot->sns_cd);
							foreach($sns_array as $sns_type_cd) {
								if ($sns_type_cd == 'wb') {
									echo('<th style="width:120px;text-align:center">');
									echo($_codes['08'][$sns_type_cd] . '<small>' . __('admin.common.label.mobile_bracket') .'</small>');
								}
								else {
									echo('<th style="width:80px;text-align:center">');
									echo($_codes['08'][$sns_type_cd]);
								}
								echo('</th>');
							}
							?>																								
							</tr>						
							</thead>							
							<tbody>	
							<tr>	
							<?php 
							foreach($country_arr as $country) {
								echo('<tr class="gradeX odd" role="row">');
								echo('<td style="text-align:left;">');
								if ($country != 'ZZ') echo('<img src="https://cdn.talkappi.com/common/country-flags/svg/' . strtolower($country) . '.svg" title="" style="width:24px;" onerror="this.src=\'\'">');
								echo($country_code[$country]);
								echo('</td>');
								$sum = 0;
								$td = '';
								foreach($sns_array as $sns_type_cd) {
									$td = $td . '<td>';
									if ($sns_type_cd == 'wb') {
										$month_key = $country . $sns_type_cd . 'pc';
										$month_key_m = $country . 'wb' . 'mobile';
										$total = 0;
										if (array_key_exists($month_key, $bots_month_result)) $total = $bots_month_result[$month_key];
										if (array_key_exists($month_key_m, $bots_month_result)) $total = $total + $bots_month_result[$month_key_m];
										if ($total > 0) $td = $td . $total;
										if (array_key_exists($month_key_m, $bots_month_result)) $td = $td . "(" . $bots_month_result[$month_key_m] . ")";
										$sum = $sum + $total;
									}
									else {
										$month_key = $country . $sns_type_cd;
										if (array_key_exists($month_key, $bots_month_result)) {
											$td = $td . $bots_month_result[$month_key];
											$sum = $sum + $bots_month_result[$month_key];
										}
									}
									echo('</td>');
								}
								echo('<td>');
								if ($sum > 0) {
									echo($sum);
								}
								else {
									if ($zero == '0') echo($zero);
								}
								echo('</td>');
								echo($td);
								echo('</tr>');
							}
							echo('<tr class="gradeX odd" role="row">');
							echo('<td>');
							echo __('admin.common.label.total');
							echo('</td>');
							$sum = 0;
							$td = '';
							foreach($sns_array as $sns_type_cd) {
								$td = $td . '<td>';
								if ($sns_type_cd == 'wb') {
									$total_key = $sns_type_cd . 'pc';
									$total_key_m = $sns_type_cd . 'mobile';
									$total = 0;
									if (array_key_exists($total_key, $bots_total_result)) $total = $bots_total_result[$total_key];
									if (array_key_exists($total_key_m, $bots_total_result)) $total = $total + $bots_total_result[$total_key_m];
									if ($total > 0) {
										$td = $td . $total;
									}
									else {
										if ($zero == '0') $td = $td . $zero;
									}
									if (array_key_exists($total_key_m, $bots_total_result)) $td = $td . "(" . $bots_total_result[$total_key_m] . ")";
									$sum = $sum + $total;
								}
								else {
									$total_key = $sns_type_cd;
									if (array_key_exists($total_key, $bots_total_result)) {
										$td = $td . $bots_total_result[$total_key];
										$sum = $sum + $bots_total_result[$total_key];
									}
									else {
										if ($zero == '0') $td = $td . $zero;
									}
								}
								echo('</td>');
							}
							echo('<td>');
							if ($sum > 0) {
								echo($sum);
							}
							else {
								if ($zero == '0') echo($zero);
							}
							echo('</td>');
							echo($td);
							echo('</tr>');
							?>
							</tbody>
							</table>
<?php } else { ?>
							<table class="table table-striped table-bordered table-hover" id="sample_3">
							<thead>
							<tr>
								<th style="width:200px;text-align:center"></th>
								<th style="width:80px;text-align:center"></th>													
							<?php 
							$sns_array = explode(',', $_bot->sns_cd);
							$sns_count = count($sns_array);
							$mobile_width = 0;
							if (in_array('wb', $sns_array)) {
								$mobile_width = 40;
							}
							foreach($scene as $k=>$v) {
								echo('<th style="width:' . (($sns_count-1) * 80 + 120) . 'px;text-align:center" colspan="' . $sns_count . '">');
								echo($v);
								echo('</th>');
							}
							?>															
							</tr>						
							</thead>							
							<tbody>	
							<tr>
							<?php 
							echo('<tr class="gradeX odd" role="row">');
							echo('<th style="width:260px;text-align:center">' . __('admin.common.label.by_country'));
							echo('</th>');
							echo('<th style="width:160px;text-align:center">' . __('admin.common.label.total'));
							echo('</th>');
							foreach($scene as $k=>$v) {
								foreach($sns_array as $sns_type_cd) {
									if ($sns_type_cd == 'wb') {
										echo('<th style="width:120px;text-align:center">');
										echo($_codes['08'][$sns_type_cd] . '<small>' . __('admin.common.label.mobile_bracket') .'</small>');
									}
									else {
										echo('<th style="width:80px;text-align:center">');
										echo($_codes['08'][$sns_type_cd]);
									}
									echo('</th>');
								}
							}?>																																			
							</tr>	
							<?php 
							foreach($country_arr as $country) {
								echo('<tr class="gradeX odd" role="row">');
								echo('<td style="text-align:left;">');
								if ($country != 'ZZ') echo('<img src="https://cdn.talkappi.com/common/country-flags/svg/' . strtolower($country) . '.svg" title="" style="width:24px;" onerror="this.src=\'\'">');
								echo(' ' . $country_code[$country]);
								echo('</td>');
								$sum = 0;
								$td = '';
								foreach($scene as $k=>$v) {
									foreach($sns_array as $sns_type_cd) {
										$td = $td . '<td>';
										if ($sns_type_cd == 'wb') {
											$month_key = $country . $k . $sns_type_cd . 'pc';
											$month_key_m = $country . $k . 'wb' . 'mobile';
											$total = 0;
											if (array_key_exists($month_key, $month_result)) $total = $month_result[$month_key];
											if (array_key_exists($month_key_m, $month_result)) $total = $total + $month_result[$month_key_m];
											if ($total > 0) {
												$td = $td . $total;
											}
											else {
												if ($zero == '0') $td = $td . $zero;
											}
											if (array_key_exists($month_key_m, $month_result)) $td = $td . "(" . $month_result[$month_key_m] . ")";
											$sum = $sum + $total;
										}
										else {
											$month_key = $country . $k . $sns_type_cd;
											if (array_key_exists($month_key, $month_result)) {
												$td = $td . $month_result[$month_key];
												$sum = $sum + $month_result[$month_key];
											}
											else {
												if ($zero == '0') $td = $td . $zero;
											}
										}
										$td = $td . '</td>';
									}
								}
								echo('<td>');
								if ($sum > 0) {
									echo($sum);
								}
								else {
									if ($zero == '0') echo($zero);
								}
								echo('</td>');
								echo($td);
								echo('</tr>');
							}
							echo('<tr class="gradeX odd" role="row">');
							echo('<td>');
							echo __('admin.common.label.total');
							echo('</td>');
							$sum = 0;
							$td = '';
							foreach($scene as $k=>$v) {
								foreach($sns_array as $sns_type_cd) {
									$td = $td . '<td>';
									if ($sns_type_cd == 'wb') {
										$total_key = $k . $sns_type_cd . 'pc';
										$total_key_m =  $k . $sns_type_cd . 'mobile';
										$total = 0;
										if (array_key_exists($total_key, $total_result)) $total = $total_result[$total_key];
										if (array_key_exists($total_key_m, $total_result)) $total = $total + $total_result[$total_key_m];
										if ($total > 0) {
											$td = $td . $total;
										}
										else {
											if ($zero == '0') $td = $td . $zero;
										}
										if (array_key_exists($total_key_m, $total_result)) $td = $td . "(" . $total_result[$total_key_m] . ")";
										$sum = $sum + $total;
									}
									else {
										$total_key = $k . $sns_type_cd;
										if (array_key_exists($total_key, $total_result)) {
											$td = $td . $total_result[$total_key];
											$sum = $sum + $total_result[$total_key];
										}
										else {
											if ($zero == '0') $td = $td . $zero;
										}
									}
									$td = $td . '</td>';
								}
							}
							echo('<td>');
							if ($sum > 0) {
								echo($sum);
							}
							else {
								if ($zero == '0') echo($zero);
							}
							echo('</td>');
							echo($td);
							echo('</tr>');
							?>
							</tbody>
							</table>
<?php } ?>														
							</div>
						</div>
					</div>
			        </div>
			        <!-- /#page-wrapper -->					
				</div>
			</div>
			<!-- END PAGE CONTENT-->
