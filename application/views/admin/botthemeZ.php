			
												<?php 
												foreach ($css_files as $k=>$v) {
													echo('<input type="hidden" name="' . $k . '" value="" />');
												}
												foreach ($css_files as $k=>$v) {
													echo('<div class="form-group">');
													echo('<label class="control-label col-md-3">'. $v . '</label>');
													echo('<div id="' . $k . '" contenteditable="true" class="control-label col-md-9 css-file" style="display:inline-block;text-align:left;border-left: 1px solid #cecece;">' . nl2br($post[$k]) . '</div>');
													echo('</div>');
												}?>	
												<div class="form-group">
													<label class="control-label col-md-3">talkappiアイコン</label>
													<div class="col-md-6">
														<div class="fileinput fileinput-new" data-provides="fileinput">
															<div class="input-group input-large">
																<div class="form-control uneditable-input span3" data-trigger="fileinput">
																	<i class="fa fa-file fileinput-exists"></i>&nbsp; <span class="fileinput-filename">
																	</span>
																</div>
																<span class="input-group-addon btn default btn-file">
																<span class="fileinput-new">
																Select file </span>
																<span class="fileinput-exists">
																Change </span>
																<input type="hidden" name="talkappi_file_base64" value="" />
																<input type="file" name="talkappi_file" id="talkappi_file">
																</span>
																<a href="#" class="input-group-addon btn red fileinput-exists" data-dismiss="fileinput">
																Remove </a>
															</div>
														</div>
													</div>
													<div class="col-md-2">
													<img src="<?php echo($post['talkappi-file'])?>?t=<?php echo(time())?>>" width="36px;" onerror="this.src=''"/>
													</div>
												</div>
												<div class="form-group">
													<label class="control-label col-md-3">talkappiアイコン(モバイル)</label>
													<div class="col-md-6">
														<div class="fileinput fileinput-new" data-provides="fileinput">
															<div class="input-group input-large">
																<div class="form-control uneditable-input span3" data-trigger="fileinput">
																	<i class="fa fa-file fileinput-exists"></i>&nbsp; <span class="fileinput-filename">
																	</span>
																</div>
																<span class="input-group-addon btn default btn-file">
																<span class="fileinput-new">
																Select file </span>
																<span class="fileinput-exists">
																Change </span>
																<input type="hidden" name="talkappi_m_file_base64" value="" />
																<input type="file" name="talkappi_m_file" id="talkappi_m_file">
																</span>
																<a href="#" class="input-group-addon btn red fileinput-exists" data-dismiss="fileinput">
																Remove </a>
															</div>
														</div>
													</div>
													<div class="col-md-2">
													<img src="<?php echo($post['talkappi-mobile-file'])?>?t=<?php echo(time())?>>" width="36px;" onerror="this.src=''"/>
													</div>
												</div>
												<div class="form-group">
													<label class="control-label col-md-3">JSコード</label>
													<div class="col-md-8">
														<div id="jscode" class="alert alert-success">
														</div>
														上記コードをお客様のホームページに埋込ください。
													</div>
												</div>



