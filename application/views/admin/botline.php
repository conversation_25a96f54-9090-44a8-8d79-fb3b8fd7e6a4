<input type="hidden" id="act" name="act" value="" />
<input type="hidden" id="div" name="div" value="<?php echo $post['div']?>" />
<input type="hidden" id="msg_id" name="msg_id" value="" />
<!-- Page Content -->
<div id="page-wrapper">
	<nav class="line-tab" style="margin-left: 8px;">
		<ul>
			<li>
				<a class="func-menu"  style="font-size: 14px;" href="/admin/linereport">LINE利用状況</a>
			</li>
			<li class="active">
				<a class="func-menu"  style="font-size: 14px;" href="/admin/botline">リッチメニュー</a >
			</li>
			<li>
				<a class="func-menu"  style="font-size: 14px;" href="/admin/linesetting">LINE連携の基本設定</a >
			</li>
		</ul>
	</nav>
	<div class="portlet menu" style="margin-bottom: 0 !important;padding-left: 0 !important;">
		<h5>現在の表示中のリッチメニュー</h5>
		<button type="button" id="newButton" class="btn-smaller btn-blue surveys-btn" style="background-color: #245BD6;">
			<span class="icon-add-white"></span>新規作成
        </button>
	</div>
	<?php if ($current_linemenu == null) {?>
		<h4 style="margin-left: 20px;">表示するリッチメニューが設定されていません</h4>
	<?php } else {?>
	<table class="table table-striped table-bordered table-hover">
		<tr>
		<td style="width:300px;"><img style="width:300px" src="
			<?php echo (isset($current_linemenu['msg_image']) && !empty(trim($current_linemenu['msg_image'])) ?
				($current_linemenu['msg_image'] . '?'. time())
			 	:
				 " "
				)
			?>" />
		</td>
		<td style="background-color: #f6f7f9;">
		メニュー名称　　<?php echo($current_linemenu['msg_name']) ?><br /><br/>
		表示期間　　<?php echo(substr($current_linemenu['start_date'], 0, 16) . '～' . substr($current_linemenu['end_date'], 0, 16)) ?><br/><br/>
		アクション<br />
		<?php
		foreach($current_linemenu['area'] as $area) {
			echo('<b>・</b>');
			if ($area['skill_kind'] == null || $area['skill_kind'] == "") {
				echo('設定しない');
			}
			else {
				echo($area['skill_kind']);
			}
			if ($area['skill_name'] != null && $area['skill_name'] != "") {
				echo(' - ' . $area['skill_name']);
			}
			echo('<br />');
		}
		?>
		</td>
		</tr>
	</table>
	<?php }?>
	<div class="portlet menu" style="margin-bottom: 0 !important;padding-left: 0 !important;">
		<h5>全てのリッチメニュー</h5>
	</div>
	<nav class="line-tab">
		<ul class="">
			<li class="<?php if ($post['div']==1) echo('active')?>" >
				<a class="func-menu" href="/admin/botline?div=1">予約・公開中</a >
			</li>
			<li class="<?php if ($post['div']==2) echo('active')?>">
				<a class="func-menu" href="/admin/botline?div=2">待機中</a >
			</li>
		</ul>
	</nav>
	<div class="portlet header">
	    <div class="form-group">
	        <div class="col-md-3 flex">
				<input name="start_date" class="talkappi-datepicker" id="start_date" value="<?php echo ($post['start_date']) ?>"/>
				<p style="margin-right: 10px;">〜</p>
				<input name="end_date" class="talkappi-datepicker" id="end_date" value="<?php echo ($post['end_date']) ?>"/>
	        </div>
	        <div class="col-md-3 flex">
	            <button type="submit" id="searchButton" class="btn blue surveys-btn">
	            <i class="fa fa-search"></i></button>
				<button type="reset" id="resetButton" class="action-button btn-white section">リセット</button>
	        </div>
	    </div>
	</div>
    <div class="portlet light">
		<div class="portlet box">
            <div class="portlet-body">
                <table class="table table-striped table-bordered table-hover js-data-table">
                    <thead>
                        <tr>
                            <th>メニュー名称</th>
							<th>画像</th>
                            <th style="width: 120px;">表示期間</th>
                            <th>アクション</th>
							<th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        foreach ($linemenu_list as $item) {
                        ?>
                            <tr class="gradeX odd" role="row">
								<!-- 名称 -->
								<td>
                                    <a href="/<?php echo $_path?>/botlinemenu?id=<?php echo ($item['msg_id']) ?>"><?php echo($item['msg_name']) ?></a>
                                </td>
								<!-- 画像 -->
                                <td>
                                	<a href="/<?php echo $_path?>/botlinemenu?id=<?php echo ($item['msg_id']) ?>">
										<img style="width:250px" src="<?php echo (isset($item['msg_image']) && !empty(trim($item['msg_image'])) ? ($item['msg_image'] . '?'. time()) : " ") ?>"/>
									</a>
                                </td>
								<!-- 表示期間 -->
                                <td style="text-align:left;">
                                    <?php
									if ($item['start_date'] == '' || $item['start_date'] == '') {
										echo ('期間未設定');
									}
									else {
										echo (substr($item['start_date'], 0, 16) . '<br />～<br />' . substr($item['end_date'], 0, 16));
									 }
									?>
                                </td>
								<!-- アクション -->
								<td>
                                	<?php
                                	foreach($item['area'] as $area) {
                                		echo('<b>・</b>');
										if ($area['skill_kind'] == null || $area['skill_kind'] == "") {
											echo('設定しない');
										}
										else {
											echo($area['skill_kind']);
										}
										if ($area['skill_name'] != null && $area['skill_name'] != "") {
											echo(' - ' . $area['skill_name']);
										}
                                		echo('<br />');
                                	}
                                	?>
                                </td>
								<!-- メニュー言語 -->
								<td style="text-align:left;">
									<div class="btn round image edit js-edit" data-href="/<?php echo $_path?>/botlinemenu?id=<?php echo ($item['msg_id']) ?>">編集</div>
									<div class="btn round image copy js-copy" data-msg_id="<?php echo $item['msg_id'] ?>">コピー</div>
									<?php 
										if ($div == 2) echo ('<div class="btn round image delete js-delete" data-msg_id="'. $item['msg_id'] .'">削除</div>');
									?>
                                </td>
                            </tr>
                        <?php } ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
<!-- /#page-wrapper -->
