<script type="text/javascript">
	const json_data = <?php echo($json_data) ?>;
	const _preview_data = <?php echo json_encode($preview_data, JSON_UNESCAPED_UNICODE) ?>;
	const _lang_edit = '<?php echo $lang_edit ?>';
</script>

<!-- BEGIN PAGE HEADER-->
<!-- BEGIN PAGE HEAD -->
<div class="page-head">
	<!-- BEGIN PAGE TITLE -->
	<div class="page-title">
		<h1>商品管理 <small></small></h1>
	</div>
	<!-- END PAGE TITLE -->
</div>
<!-- END PAGE HEAD -->
<!-- END PAGE HEADER-->
<!-- BEGIN PAGE CONTENT-->
		<!-- Page Content -->
		<div id="page-wrapper">
			<!-- <div class="top-nav"> -->
			<?php echo $menu ?>
				<div class="content-container white border">
					<div class="section-container">
						<div class="row">
							<div class="col-md-8">
								<div style="display:flex;justify-content:space-between;align-items:center;margin-bottom: 20px;">
									<nav class="line-tab" style="padding-top: 0;">
										<ul class="flexbox" style="margin: 0; padding-inline-start: 0px;flex-flow: wrap; gap: 6px 0px;">
										<?php
										$orm = ORM::factory('product', $product_id);
										$upd_arr = DB::select('lang_cd', 'upd_time')->from('t_product_description')->where('product_id', '=', $product_id)->execute()->as_array('lang_cd', 'upd_time');
										$display = ORM::factory('itemdisplay')->where('item_id', '=', $product_id)->where('item_div', '=', $orm->item_div)->find();
										$display_lang_arr = explode(',', $display->lang_display);
										foreach($_bot_lang as $lang_cd=>$lang_name) {
											if ($lang_cd==$lang_edit) {
												echo('<li class="active">');
											}
											else {
												echo('<li>');
											}
											if ($_bot_setting['flg_product_auto_update'] == 0) {
												if (array_key_exists($lang_cd, $upd_arr) && $orm->upd_user==0 && $upd_arr[$lang_cd] <= $orm->upd_time) {
													$lang_name = $lang_name . '(未反映)';
												}
											}
											if ($display->lang_display !== null && !in_array($lang_cd, $display_lang_arr)) {
												$lang_name = $lang_name . '(非表示)';
											}
											echo('<a class="func-menu" href="/admin/' . $func . 'desc?lang='. $lang_cd . '">' . $lang_name . '</a></li>');
										}
										?>
										</ul>
									</nav>
								</div>
								<div class="form-body">
								<input type="hidden" name="message" id="message" value="<?php echo($message)?>" />
								<input type="hidden" name="lang" value="<?php echo($lang_edit)?>" />
								<input type="hidden" name="image_url" id="image_url" value="" />
								<input type="hidden" name="act" id="act" value="" />
								<?php 
								$product_name_style = '';
								$product_description_style = '';
								$name = '';
								$desc = '';
								$last_name = '';
								$last_desc = '';
								$image_urls = [];
								if (($_user->role_cd == '99' || $_user->role_cd == '80') && 
										$product->upd_user == 0 && $link_data != null && isset($product_description_ja->product_id)) {
									if (array_key_exists('RoomDescription', $link_data)) {
										$name = str_replace("\r", "", $link_data['RoomDescription']['$']['Name']);
										$desc = str_replace("\r", "", $link_data['RoomDescription']['Text']);
										if (array_key_exists('URL', $link_data['RoomDescription']))
											$image_urls = $link_data['RoomDescription']['URL'];
									}
									else {
										$name = str_replace("\r", "", $link_data['$']['RatePlanName']);
										$desc = str_replace("\r", "", $link_data['RatePlanDescription']['Text']);
										if (array_key_exists('URL', $link_data['RatePlanDescription']))
											$image_urls = $link_data['RatePlanDescription']['URL'];
									}
									if ($last_data == null) {
										$last_data = [];
										$last_data['product_name'] = $product_description_ja->product_name;
										$last_data['description'] = $product_description_ja->description;
									}
									if (str_replace("\r", "", $last_data['product_name']) != $name) {
										$product_name_style = 'style="color:red;"';
									}
									if (str_replace("\r", "", $last_data['description']) != $desc) {
										$product_description_style = 'style="color:red;"';
									}
									if (!is_array($image_urls)) $image_urls = [$image_urls];
								}
								?>
								
								<div class="form-group">
									<label class="control-label col-md-2"></label>
									<?php 
									if ($message != '' && $_bot_setting['flg_product_auto_update'] == 0) { 
										echo('<div class="col-md-8" style="color:red;">保存した情報はまだ本番環境に反映されていません。<br/>反映したい場合、「本番反映」を押してください。</div>');
									}
									else if ($lang_edit == 'ja') {
										if (array_key_exists('RoomDescription', $link_data)) {
											$originalDate = $link_data['RoomDescription']['$']['LastModifyDateTime'];
											$date = new DateTime($originalDate);
											echo('<div class="col-md-8">最終取込日時：' . $date->format('Y/m/d H:i') . '</div>');
										}
										else if (array_key_exists('RatePlanDescription', $link_data)) {
											$originalDate = $link_data['RatePlanDescription']['$']['LastModifyDateTime'];
											$date = new DateTime($originalDate);
											echo('<div class="col-md-8">最終取込日時：' . $date->format('Y/m/d H:i') . '</div>');
										}
									}
									?>
								</div>
								
								<div class="form-group">
									<label class="control-label col-md-2">タイトル</label>
									<div class="col-md-8">
										<div class="input-icon right">
											<textarea name="product_name" id="product_name" class="form-control talkappi-textinput" data-max-input="80" rows="2" placeholder=""><?php if ($post != NULL) echo($post['product_name'])?></textarea>
										</div>
									</div>
								</div>
								<?php if ($product_name_style != '') {?>
								<div class="form-group" style="display:none;">
									<label class="control-label col-md-2">日本語名称</label>
									<div class="col-md-8">
										<div class="input-icon right">
											<textarea id="product_name_ja" class="form-control" rows="2" placeholder=""><?php echo $last_data['product_name']?></textarea>													
										</div>
									</div>
								</div>											
								<div class="form-group" style="display:none;">
									<label class="control-label col-md-2" <?php echo $product_name_style?>>連携名称</label>
									<div class="col-md-8">
										<div class="input-icon right">
											<textarea class="form-control" id="product_name_link" rows="2" placeholder=""><?php echo($name)?></textarea>													
										</div>
									</div>
								</div>
								<?php 
								if ($product_description->upd_user == 0) {
									$desc_style = '';
									$desc_btn = 'ー';
								}
								else {
									$desc_style = 'style="display:none;"';
									$desc_btn = '＋';
								}
								?>
								<div class="form-group">
									<label class="control-label col-md-2" <?php echo $product_description_style?>><a class="show_data" href="javascript:void(0);"><?php echo $desc_btn?></a></label>
									<div class="col-md-10" <?php echo $desc_style?>>
									<div id="diff_name"></div>
									<br/>
									<button type="button" id="copyName" class="btn yellow"><i class="fa fa-arrow-left mr10"></i>変更反映</button>	
									</div>
								</div>																					
								<?php }?>									
								<div class="form-group">
									<label class="control-label col-md-2">説明</label>
									<div class="col-md-8">
										<div class="input-icon right">
											<textarea name="sell_point" id="sell_point" class="form-control talkappi-textinput" data-max-input="80" rows="4" placeholder=""><?php if ($post != NULL) echo($post['sell_point'])?></textarea>
										</div>
									</div>
								</div>
								<div class="form-group">
									<label class="control-label col-md-2">詳細</label>
									<div class="col-md-10">
										<textarea name="product_description" id="product_description" class="form-control" maxlength="3000" rows="6" placeholder=""><?php if ($post != NULL) echo($post['product_description'])?></textarea>
									</div>											
								</div>
								<?php if ($product_description_style!= '') {?>
								<div class="form-group" style="display:none;">
									<label class="control-label col-md-2">日本語詳細説明</label>
									<div class="col-md-10">
										<textarea id="product_description_ja" class="form-control" rows="2" placeholder=""><?php echo $last_data['description']?></textarea>
									</div>											
								</div>											
								<div class="form-group" style="display:none;">
									<label class="control-label col-md-2" <?php echo $product_description_style?>>連携説明</label>
									<div class="col-md-10">
										<div class="input-icon right">
											<textarea class="form-control" id="product_description_link" placeholder=""><?php echo($desc)?></textarea>													
										</div>
									</div>
								</div>
								<?php 
								if ($product_description->upd_user == 0) {
									$desc_style = '';
									$desc_btn = 'ー';
								}
								else {
									$desc_style = 'style="display:none;"';
									$desc_btn = '＋';
								}
								?>
								<div class="form-group">
									<label class="control-label col-md-2" <?php echo $product_description_style?>><a class="show_data" href="javascript:void(0);"><?php echo $desc_btn?></a></label>
									<div class="col-md-10" <?php echo $desc_style?>>
									<div id="diff_desc"></div>
									<br/>
									<button type="button" id="copyDesc" class="btn yellow"><i class="fa fa-arrow-left mr10"></i>変更反映</button>	
									</div>
								</div>									
								<?php }?>											
								<div class="form-group" <?php if (strpos($product->class_cd, '01') === 0) echo('style="display:none;"'); ?>>
									<label class="control-label col-md-2" style="white-space: nowrap;">
										キャンセルポリシー<br/><a href="/admin/sysmsg?id=<?php echo($cancel_policy_msg_id)?>">編集</a>
									</label>
									<div class="col-md-10">
										<textarea name="notes" class="form-control" maxlength="1000" rows="2" placeholder=""><?php 
											if ($post['notes'] == '') {
												echo($cancel_policy);
											}
											else {
												echo($post['notes']);
											}
										?></textarea>
									</div>
								</div>																					
								<div class="form-group" style="display:none;">
									<label class="control-label col-md-2">画像アクション</label>
									<div class="col-md-10">
										<div class="input-icon right">
											<input name="url" id="url" type="text" maxlength="1001" class="form-control" placeholder="" value="<?php if ($post != NULL) echo($post['url'])?>">
										</div>
									</div>
								</div>											
								<?php for($i=1; $i<=3; $i++) {?>
								<div class="form-group">
									<label class="control-label col-md-2">ボタン<?php echo $i?></label>
									<div class="col-md-2" style="padding-right: 0px;">
									<?php echo Form::select('btn' . $i . '_name', $btn_select, $post['btn' . $i . '_name'], array('class'=>'form-control btn_type', 'id'=>'btn' . $i . '_name', 'style'=>"width:120px;"))?>
									</div>
									<div class="col-md-2" style="padding-left: 30px;">
									<?php echo Form::select('btn' . $i . '_url_lang_cd', $url_lang_cd, $post['btn' . $i . '_url_lang_cd'], array('class'=>'form-control btn_lang', 'id'=>'btn' . $i . '_url_lang_cd', 'style'=>"width:90px;"))?>
									</div>
									<div class="col-md-6">
										<input name="btn<?php echo $i?>_url" type="text" maxlength="1001" class="form-control" placeholder="" value="<?php if ($post != NULL) echo(htmlspecialchars($post['btn' . $i . '_url']))?>">
										<div id="btn<?php echo $i?>_url_show" style="line-height:2">
										<?php if (array_key_exists("btn" . $i . '_url_show', $skills))
										foreach($skills["btn" . $i . '_url_show'] as $skill) {
											if ($skill != '' && strpos($skill, 'http') !== 0) echo('<span class="alert alert-success alert-dismissable" style="padding:0px 5px;cursor:pointer;">' . $skill . '</span>');
										}?>					
										<span class="alert alert-success alert-dismissable" style="padding:0px 5px;cursor:pointer;">+</span>	
										</div>								
									</div>
								</div>
								<?php }?>
								<div class="form-group last">
									<label class="control-label col-md-2">画像</label>
									<div class="col-md-6">
										<div class="talkappi-upload" data-name="image_base64" data-type="img" data-label="<?php echo ($post['product_image'])?>" data-url="<?php echo $post['product_image']?>" data-max-size="2"></div>
									</div>
								</div>
								<?php if (count($image_urls) > 0) {?>
								<div class="form-group">
									<label class="control-label col-md-2">連携画像 <a class="show_data" href="javascript:void(0);">＋</a></label>
									<div class="col-md-10" id="product_data_diff" style="display:none;">
									<div class="xscroll-wrapper">
										<?php 
										$tl_config = $config['tl_config'];
										foreach($image_urls as $item) {
											$item = str_replace($tl_config['original_image_url'], $tl_config['mapping_image_url'], $item);
											echo('<div class="card">');
											echo('<a href="' . $item . '" target="blank"><img class="image" alt="" src="' . $item . '" />');
											echo('<div class="item-caption">');
											echo($name);
											echo('</div>');
											echo('<div class="item-description">');
											echo($desc);
											echo('</div>');
											echo('<div class="item-button-horizontal">');
											echo('<a class="button use-image" url="' . $item . '" button_type="url" href="javascript:void(0);">' . 'この画像を利用' . '</a>');
											echo('</div>');
											echo('</div>');
											}?>
									</div>
									</div>
								</div>
								<?php }?>										
								<div class="form-group">
									<label class="control-label col-md-2">全言語適用</label>
									<div class="col-md-2">
										<input type="checkbox" name="flg_apply_all_lang" value="1" class="make-switch" data-on-color="success" data-off-color="warning">
									</div>											
									<label class="control-label col-md-2">多言語翻訳</label>
									<div class="col-md-2">
										<input type="checkbox" name="flg_auto_translate" <?php if ($auto_translate == 1) echo('checked')?> value="1" class="make-switch" data-on-color="success" data-off-color="warning">
									</div>			
									<label class="control-label col-md-2">画像即時反映</label>
									<div class="col-md-2">
										<input type="checkbox" name="flg_img_invalidate" value="1" class="make-switch" data-on-color="success" data-off-color="warning">
									</div>																					
								</div>
								<?php 
								if ($_user->role_cd == '99' && $lang_edit == 'ja') {
								?>											
								<div class="form-group">
									<label class="control-label col-md-2">連携データ <a class="show_data" href="javascript:void(0);">＋</a></label>
									<div class="col-md-10" style="display:none;">
									<pre id="product_data"><?php echo json_encode($link_data, JSON_UNESCAPED_UNICODE) ?> </pre>
									</div>
								</div>										
								<?php }?>																														
								</div>											
								<div class="form-actions">
									<div class="row">
										<div class="col-md-offset-2 col-md-9 flex">
											<?php if ($_bot_setting['flg_product_auto_update'] == 0) { ?>										
											<button type="button" class="btn-larger btn-blue js-action-save"><?php if ($item_div==5) echo('一時')?>保存</button>	
											<?php if ($item_div==5 && isset($product_description_ja->product_id)) {?>
											<button type="button" class="btn-larger btn-yellow js-action-finish">本番反映</button>	
											<button type="button" class="btn-larger btn-yellow js-action-finishall">全言語本番反映</button>	
											<?php }?>
											<?php } ?>
											<button type="button" onclick="top.location='/admin/<?php echo $func?>s'" class="btn-larger btn-white js-action-back">戻る</button>
										</div>
									</div>
								</div>
							</div>
					
							<div id="msg-preview" class="col-md-4 preview-sticky">
								<div class="talkappi-preview js-msg-preview" data-type="message" style="margin:0 0 0 20px; flex-basis: 30%; max-width:320px;"></div>
							</div>
						</div>
						</div>
					</div>
				<!-- </div> -->
			</div>
<div id="item_class_cd" style="display:none;"><?php echo $product->class_cd?></div>
<!-- END PAGE CONTENT-->
<?php echo $skillbox ?>
