<input type="hidden" name="project_id" id="project_id" value="" />

<div class="flex content-container light-gray">  
        <label class="control-label col-md-1" <?php if ($json_newsletter_setting['user_in_charge_required'] != 1) echo('style="display:none;"')?>><?php echo __('admin.common.label.person_in_charge') ?></label>
        <div class="col-md-2" <?php if ($json_newsletter_setting['user_in_charge_required'] != 1) echo('style="display:none;"')?>>
            <?php echo Form::select('user_in_charge', $user_list, $user_id, array('id'=>'user_in_charge','class'=>'form-control select2me'))?>
        </div>

    <div id="filter_date_range" class="talkappi-datepicker-range" placeholder='登録日付：全期間' data-value='<?php echo json_encode($filter_date_range) ?>'>
    </div>
    <span class="btn-smaller btn-blue js-combo-filter">
        <span class="icon-filter"></span>
        <?php echo __('admin.common.label.narrowdown') ?>
    </span>
</div>
<div style="margin: 10px;">
    <div class="flex-x-between">
        <div><?php echo __('admin.newsletterproject.label.project_list') ?></div>
        <span class="btn-smaller btn-blue js-new-project">
            <span class="icon-add-white"></span>
            <?php echo __('admin.common.button.create_new') ?>
        </span>
    </div>
</div>
<div class="content-container white">
    <div class="react-admin-newsletter-projects" data-path="<?php echo $_path ?>"></div>
</div>

<?php
$columnItems = [];

foreach ($projects as $project) {
    // カラムのアイテム
    $columnItems[] = [
        "path"=> $_path,
        "project_id" => $project['project_id'],
        "project_name" =>  $project['project_name'],
        "description" => $project['description'],
        "person_in_charge" => $project['person_in_charge_name'],
        "member_count" => $project['member_count'],
        "tag_count" => $project['tag_count'],
        "task_count" => $project['task_count'],
        "last_updated" => date('Y/m/d H:i:s', strtotime($project['upd_time'])),
        "operation" => [
            "edit" => true,
            "addresses" => true,
            "tags" => true,
            "tasks" => true,
            "delete" => true,
        ],
    ];
}

// ヘッダー
$headers_for_table = [
    [
        'Header' => __('admin.newsletterproject.label.name'),
        'accessor' => 'project_name',
        'style' => ['width' => '10rem', 'wordBreak' => 'normal']
    ],
    [
        'Header' => __('admin.newsletterproject.label.description'),
        'accessor' => 'description',
        'style' => ['width' => '20rem', 'wordBreak' => 'normal']
    ],
    [
        'Header' => __('admin.common.label.person_in_charge'),
        'accessor' => 'person_in_charge',
        'style' => ['width' => '10rem', 'whiteSpace' => 'nowrap']
    ],
    [
        'Header' => __('admin.newsletterproject.th.address'),
        'accessor' => 'member_count',
        'style' => ['width' => '5rem', 'whiteSpace' => 'nowrap']
    ],
    [
        'Header' => __('admin.newsletterproject.th.tag'),
        'accessor' => 'tag_count',
        'style' => ['width' => '5rem', 'whiteSpace' => 'nowrap']
    ],
    [
        'Header' => __('admin.newsletterproject.th.newsletter'),
        'accessor' => 'task_count',
        'style' => ['width' => '5rem', 'whiteSpace' => 'nowrap']
    ],
    [
        'Header' => __('admin.newsletterproject.th.last_update'),
        'accessor' => 'last_updated',
        'style' => ['width' => '10rem', 'whiteSpace' => 'nowrap']
    ],
    [
        'Header' => __('admin.common.label.operation'),
        'accessor' => 'operation',
        'style' => ['textAlign' => '-webkit-center', 'width' => '5rem']
    ]
];
?>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/1.12.4/jquery.min.js"></script>
<script type="text/javascript" src="/assets/common/react/pages/admin/newsletterprojects.bundle.js"></script>

<script type="text/javascript">
const _columns_for_table = <?php echo json_encode($columnItems) ?>;
const _headers_for_table = <?php echo json_encode($headers_for_table) ?>;
const _lang_cd = '<?php echo $_lang_cd ?>';
jQuery(document).ready(function($){
    window.talkappi_setupNewsletterProjectsPage({
        tableColumns: _headers_for_table,
        tableData: Object.values(_columns_for_table),
        lang_cd: _lang_cd
    });
});
</script>