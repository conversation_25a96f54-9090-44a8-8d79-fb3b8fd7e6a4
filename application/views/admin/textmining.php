<style>
    .content-container {
        min-width: auto;
    }
</style>

<div class="flex" style="display:flex; align-items:center; gap:20px; margin-bottom:20px; flex-wrap:wrap;">
    <div class="" style="display:flex; align-items:center; gap:10px;">
        <label style="flex:none; margin-bottom:0px; font-weight:600"><?php echo __('admin.common.label.period') ?></label>
        <input name="start_date" class="talkappi-datepicker" id="start_date" value="<?php echo(substr($post['start_date'], 0, 10))?>"/>
        <p style="margin-right: 10px;">〜</p>
        <input name="end_date" class="talkappi-datepicker" id="end_date" value="<?php echo(substr($post['end_date'], 0, 10))?>"/>
    </div>
    <div class="flex" style="display:flex; align-items:center; gap:10px;">
        <label style="flex:none; margin-bottom:0px; font-weight:600"><?php echo __('admin.common.label.facility') ?></label>
        <?php echo Form::select('bot_id', $bots, $post['bot_id'], array('id' => 'bot_id', 'class' => 'form-control')) ?>                    
    </div>
    <div class="flex" style="display:flex; align-items:center; gap:10px;">
        <label style="flex:none; margin-bottom:0px; font-weight:600"><?php echo __('admin.textmining.label.language') ?></label>
        <?php echo Form::select('lang_cd', $lang_cd_array, isset($post['lang_cd']) ? $post['lang_cd'] : '', array('id' => 'lang_cd', 'class' => 'form-control')) ?>                    
    </div>
    <div class="flex">
        <button type="button" id="searchBtn" class="btn-smaller btn-blue"><i class="fa fa-search mr10"></i><?php echo __('admin.common.button.search') ?></button>
    </div>     
</div>

<div id="react-graphs-container"></div>


<script src="https://ajax.googleapis.com/ajax/libs/jquery/1.12.4/jquery.min.js"></script>
<script type="text/javascript" src="/assets/common/react/pages/admin/textmining.bundle.js"></script>
<script type="text/javascript">
    jQuery(document).ready(function($) {
        window.talkappi_veryreport_setupFunctionsGraph({
            category_data: <?php echo json_encode($category_data) ?>, 
            word_data: <?php echo json_encode($word_data) ?>,
            category_title: "<?php echo __('admin.textmining.label.by_attribute') ?>", 
            word_title: "<?php echo __('admin.textmining.label.by_extracted_words') ?>", 
            lang_cd: "<?php echo $admin_lang_cd ?>"
        });
    });
</script>