							<div class="form-group">
								<label class="control-label col-md-3">遅延表示(秒単位)</label>					
								<div class="col-md-1">
									<input name="delay_show" id="label" type="text" class="form-control" value="<?php echo($post['delay_show'])?>" placeholder="">
								</div>
								<label class="control-label col-md-3">スクロール表示</label>					
								<div class="col-md-1">
									<input name="scroll_show" id="label" type="text" class="form-control" value="<?php echo($post['scroll_show'])?>" placeholder="">
								</div>
							</div>
							<div class="form-group">
								<label class="control-label col-md-3">吹き出し位置</label>
								<div class="col-md-1" style="padding-right:0px;">
									<input name="greeting-bottom" type="text" class="form-control" placeholder="下"
										value="<?php echo($post['greeting-bottom'])?>">
								</div>
								<div class="col-md-1" style="padding-right:0px;">
									<input name="greeting-right" type="text" class="form-control" placeholder="右" id="greeting-right"
										value="<?php echo($post['greeting-right'])?>">
								</div>
								<div class="col-md-1" style="padding-right:0px;">
								<?php echo Form::select('greeting-posi', $posilist4, $post['greeting-posi'], array('style'=>'width:80px;','class'=>'form-control', 'id'=>'greeting-posi'))?>
								</div>													
								<label class="control-label col-md-2">モバイル</label>
								<div class="col-md-1" style="padding-right:0px;">
									<input name="greeting-bottom-m" type="text" class="form-control" placeholder="下"
										value="<?php echo($post['greeting-bottom-m'])?>">
								</div>
								<div class="col-md-1" style="padding-right:0px;">
									<input name="greeting-right-m" type="text" class="form-control" placeholder="右" id="greeting-right-m"
										value="<?php echo($post['greeting-right-m'])?>">
								</div>
								<div class="col-md-1" style="padding-right:0px;">
								<?php echo Form::select('greeting-posi-m', $posilist4, $post['greeting-posi-m'], array('style'=>'width:80px;','class'=>'form-control', 'id'=>'greeting-posi-m'))?>
								</div>													
							</div>
							
							<div class="form-group">
								<label class="control-label col-md-3">吹き出しフォントサイズ</label>
								<div class="col-md-1" style="padding-right:0px;">
									<input name="greeting-font-size" type="text" class="form-control" placeholder=""
										value="<?php echo($post['greeting-font-size'])?>">
								</div>
								<div class="col-md-1" style="padding-right:0px;">
								</div>
								<label class="control-label col-md-2">モバイル</label>
								<div class="col-md-1" style="padding-right:0px;">
									<input name="greeting-font-size-m" type="text" class="form-control" placeholder=""
										value="<?php echo($post['greeting-font-size-m'])?>">
								</div>
								<div class="col-md-1" style="padding-right:0px;">
								</div>
							</div>												
							<div class="form-group">
								<label class="control-label col-md-3">talkappiアイコン位置</label>
								<div class="col-md-1" style="padding-right:0px;">
									<input name="bottom" type="text" class="form-control" placeholder="下"
										value="<?php echo($post['bottom'])?>">
								</div>
								<div class="col-md-1" style="padding-right:0px;">
									<input name="right" type="text" class="form-control" placeholder="右" id="right"
										value="<?php echo($post['right'])?>">
								</div>
								<div class="col-md-1" style="padding-right:0px;">
								<?php echo Form::select('icon-posi', $posilist3, $post['icon-posi'], array('style'=>'width:80px;','class'=>'form-control', 'id'=>'icon-posi'))?>
								</div>
								<label class="control-label col-md-2">モバイル</label>
								<div class="col-md-1" style="padding-right:0px;">
									<input name="bottom-m" type="text" class="form-control" placeholder="下"
										value="<?php echo($post['bottom-m'])?>">
								</div>
								<div class="col-md-1" style="padding-right:0px;">
									<input name="right-m" type="text" class="form-control" placeholder="右" id="right-m"
										value="<?php echo($post['right-m'])?>">
								</div>
								<div class="col-md-1" style="padding-right:0px;">
								<?php echo Form::select('icon-posi-m', $posilist3, $post['icon-posi-m'], array('style'=>'width:80px;','class'=>'form-control', 'id'=>'icon-posi-m'))?>
								</div>													
							</div>
							<div class="form-group">
								<label class="control-label col-md-3">talkappiアイコンサイズ</label>
								<div class="col-md-1" style="padding-right:0px;">
									<input name="width" type="text" class="form-control" placeholder="幅"
										value="<?php echo($post['width'])?>">
								</div>
								<div class="col-md-1" style="padding-right:0px;">
									<input name="height" type="text" class="form-control" placeholder="高"
										value="<?php echo($post['height'])?>">
								</div>
								<label class="control-label col-md-2">モバイル</label>
								<div class="col-md-1" style="padding-right:0px;">
									<input name="width-m" type="text" class="form-control" placeholder="幅"
										value="<?php echo($post['width-m'])?>">
								</div>
								<div class="col-md-1" style="padding-right:0px;">
									<input name="height-m" type="text" class="form-control" placeholder="高"
										value="<?php echo($post['height-m'])?>">
								</div>
								<label class="control-label col-md-1">radius</label>
								<div class="col-md-2">
									<input name="radius" type="text" class="form-control" placeholder="0～100"
										value="<?php echo($post['radius'])?>">
								</div>														
							</div>
							<div class="form-group" style="display:none;">
								<label class="control-label col-md-3">talkappiアイコン影</label>
								<div class="col-md-1" style="padding-right:0px;">
									<input name="h-shadow" type="text" class="form-control" placeholder="0"
										value="<?php echo($post['h-shadow'])?>">
								</div>
								<div class="col-md-1" style="padding-right:0px;">
									<input name="v-shadow" type="text" class="form-control" placeholder="2"
										value="<?php echo($post['v-shadow'])?>">
								</div>
								<div class="col-md-1" style="padding-right:0px;">
									<input name="blur" type="text" class="form-control" placeholder="8"
										value="<?php echo($post['blur'])?>">
								</div>
								<div class="col-md-1" style="padding-right:0px;">
									<input name="spread" type="text" class="form-control" placeholder="1"
										value="<?php echo($post['spread'])?>">
								</div>																																																			
							</div>												
							
							<div class="form-group">
								<label class="control-label col-md-3">閉じるアイコンサイズ</label>
								<div class="col-md-1" style="padding-right:0px;">
									<input name="close-icon-size" type="text" class="form-control" placeholder=""
										value="<?php echo($post['close-icon-size'])?>">
								</div>
								<label class="control-label col-md-2" style="text-align:left;">※　0を設定する場合、非表示</label>
								<label class="control-label col-md-1">背景色</label>
								<div class="col-md-2">
									<div class="input-group color colorpicker-default" data-color="<?php echo($post['close-icon-bkcolor'])?>" data-color-format="rgba">
										<input type="text" name="close-icon-bkcolor" placeholder="透明" class="form-control" value="<?php echo($post['close-icon-bkcolor'])?>">
										<span class="input-group-btn">
										<button class="btn default" type="button"><i style="background-color: <?php echo($post['close-icon-bkcolor'])?>;"></i>&nbsp;</button>
										</span>
									</div>
								</div>
								<label class="control-label col-md-1">テーマ色</label>
								<div class="col-md-2">
									<div class="input-group color colorpicker-default" data-color="<?php echo($post['close-icon-color'])?>" data-color-format="rgba">
										<input type="text" name="close-icon-color" class="form-control" placeholder="透明" value="<?php echo($post['close-icon-color'])?>">
										<span class="input-group-btn">
										<button class="btn default" type="button"><i style="background-color: <?php echo($post['close-icon-color'])?>;"></i>&nbsp;</button>
										</span>
									</div>
								</div>													
							</div>		
							<div class="form-group">
								<label class="control-label col-md-3">閉じるアイコン位置</label>
								<div class="col-md-1" style="padding-right:0px;">
									<input name="close-icon-bottom" type="text" class="form-control" placeholder="下"
										value="<?php echo($post['close-icon-bottom'])?>">
								</div>
								<div class="col-md-1" style="padding-right:0px;">
									<input name="close-icon-right" type="text" class="form-control" placeholder="右" id="close-icon-right"
										value="<?php echo($post['close-icon-right'])?>">
								</div>
								<div class="col-md-1" style="padding-right:0px;">
								<?php echo Form::select('close-icon-posi', $posilist, $post['close-icon-posi'], array('style'=>'width:80px;','class'=>'form-control', 'id'=>'close-icon-posi'))?>
								</div>
							</div>
							<div class="form-group">
								<label class="control-label col-md-3">吹き出し幅</label>
								<div class="col-md-1" style="padding-right:0px;">
									<input name="greeting-width" type="text" class="form-control" placeholder=""
										value="<?php echo($post['greeting-width'])?>">
								</div>
							</div>				
							<div class="form-group">
								<label class="control-label col-md-3">吹き出し色</label>
								<div class="col-md-3">
									<div class="input-group color colorpicker-default" data-color="<?php echo($post['greeting-color'])?>" data-color-format="rgba">
										<input type="text" name="greeting-color" class="form-control" placeholder="透明" value="<?php echo($post['greeting-color'])?>">
										<span class="input-group-btn">
										<button class="btn default" type="button"><i style="background-color: <?php echo($post['greeting-color'])?>;"></i>&nbsp;</button>
										</span>
									</div>
								</div>
							</div>
							<div class="form-group">
								<label class="control-label col-md-3">吹き出し背景色</label>
								<div class="col-md-3">
									<div class="input-group color colorpicker-default" data-color="<?php echo($post['greeting-bkcolor'])?>" data-color-format="rgba">
										<input type="text" name="greeting-bkcolor" class="form-control" placeholder="透明" value="<?php echo($post['greeting-bkcolor'])?>">
										<span class="input-group-btn">
										<button class="btn default" type="button"><i style="background-color: <?php echo($post['greeting-bkcolor'])?>;"></i>&nbsp;</button>
										</span>
									</div>
								</div>
							</div>
							<div class="form-group">
								<label class="control-label col-md-3">吹き出し線色</label>
								<div class="col-md-3">
									<div class="input-group color colorpicker-default" data-color="<?php echo($post['greeting-bdcolor'])?>" data-color-format="rgba">
										<input type="text" name="greeting-bdcolor" class="form-control" placeholder="透明" value="<?php echo($post['greeting-bdcolor'])?>">
										<span class="input-group-btn">
										<button class="btn default" type="button"><i style="background-color: <?php echo($post['greeting-bdcolor'])?>;"></i>&nbsp;</button>
										</span>
									</div>
								</div>
							</div>
							<div class="form-group" style="display:none;">
								<label class="control-label col-md-3">吹き出し影</label>
								<div class="col-md-1" style="padding-right:0px;">
									<input name="greeting-h-shadow" type="text" class="form-control" placeholder="0"
										value="<?php echo($post['greeting-h-shadow'])?>">
								</div>
								<div class="col-md-1" style="padding-right:0px;">
									<input name="greeting-v-shadow" type="text" class="form-control" placeholder="2"
										value="<?php echo($post['greeting-v-shadow'])?>">
								</div>
								<div class="col-md-1" style="padding-right:0px;">
									<input name="greeting-blur" type="text" class="form-control" placeholder="8"
										value="<?php echo($post['greeting-blur'])?>">
								</div>
								<div class="col-md-1" style="padding-right:0px;">
									<input name="greeting-spread" type="text" class="form-control" placeholder="1"
										value="<?php echo($post['greeting-spread'])?>">
								</div>	
								<label class="control-label col-md-1">radius</label>
								<div class="col-md-2">
									<input name="greeting-radius" type="text" class="form-control" placeholder="0～100"
										value="<?php echo($post['greeting-radius'])?>">
								</div>																																																		
							</div>													
							<div class="form-group">
								<label class="control-label col-md-3">talkappiアイコン</label>
								<div class="col-md-6">
									<div class="fileinput fileinput-new" data-provides="fileinput">
										<div class="input-group input-large">
											<div class="form-control uneditable-input span3" data-trigger="fileinput">
												<i class="fa fa-file fileinput-exists"></i>&nbsp; <span class="fileinput-filename">
												</span>
											</div>
											<span class="input-group-addon btn default btn-file">
											<span class="fileinput-new">
											Select file </span>
											<span class="fileinput-exists">
											Change </span>
											<input type="hidden" name="talkappi_file_base64" value="" />
											<input type="file" name="talkappi_file" id="talkappi_file">
											</span>
											<a href="#" class="input-group-addon btn red fileinput-exists" data-dismiss="fileinput">
											Remove </a>
										</div>
									</div>
								</div>
								<div class="col-md-2">
								<?php if ($post['talkappi-file'] != '') {
									echo('<img src="' . $post['talkappi-file'] . '?t=' . time() . '" width="36px;">');
								}?>
								</div>
							</div>
							<div class="form-group">
								<label class="control-label col-md-3">talkappiアイコン(モバイル)</label>
								<div class="col-md-6">
									<div class="fileinput fileinput-new" data-provides="fileinput">
										<div class="input-group input-large">
											<div class="form-control uneditable-input span3" data-trigger="fileinput">
												<i class="fa fa-file fileinput-exists"></i>&nbsp; <span class="fileinput-filename">
												</span>
											</div>
											<span class="input-group-addon btn default btn-file">
											<span class="fileinput-new">
											Select file </span>
											<span class="fileinput-exists">
											Change </span>
											<input type="hidden" name="talkappi_m_file_base64" value="" />
											<input type="file" name="talkappi_m_file" id="talkappi_m_file">
											</span>
											<a href="#" class="input-group-addon btn red fileinput-exists" data-dismiss="fileinput">
											Remove </a>
										</div>
									</div>
								</div>
								<div class="col-md-2">
								<?php if ($post['talkappi-mobile-file'] != '') {
									echo('<img src="' . $post['talkappi-mobile-file'] . '?t=' . time() . '" width="36px;">');
								}?>													
								</div>
							</div>
							<div class="form-group">
								<label class="control-label col-md-3">Chatウインドウ位置</label>
								<div class="col-md-1" style="padding-right:0px;">
									<input name="w-bottom" type="text" class="form-control" placeholder="下"
										value="<?php if ($post != NULL) echo($post['w-bottom'])?>">
								</div>
								<div class="col-md-1" style="padding-right:0px;">
									<input name="w-right" type="text" class="form-control" placeholder="右"
										value="<?php if ($post != NULL) echo($post['w-right'])?>">
								</div>
								<div class="col-md-1" style="padding-right:0px;">
								<?php echo Form::select('t-w-right', $posilist2, $post['t-w-right'], array('style'=>'width:80px;','class'=>'form-control', 'id'=>'t-w-right'))?>
								</div>
								<label class="control-label col-md-2">サイズ</label>
								<div class="col-md-1" style="padding-right:0px;">
									<input name="w-width" type="text" class="form-control" placeholder="幅"
										value="<?php if ($post != NULL) echo($post['w-width'])?>">
								</div>
								<div class="col-md-1" style="padding-right:0px;">
									<input name="w-height" type="text" class="form-control" placeholder="高"
										value="<?php if ($post != NULL) echo($post['w-height'])?>">
								</div>
							</div>
							
							<div class="form-group">
							<label class="control-label col-md-3">z-index</label>
								<div class="col-md-2" style="padding-right:0px;">
									<input name="z-index" type="text" class="form-control" placeholder=""
										value="<?php if ($post != NULL) echo($post['z-index'])?>">
								</div>
							</div>
							<div class="form-group" style="display:none;">
								<label class="control-label col-md-3">JSコード</label>
								<div class="col-md-8">
									<div id="jscode" class="alert alert-success">
									</div>
									上記コードをお客様のホームページに埋込ください。
								</div>
							</div>



