<div class="classbox-container" <?php if ($hide == 1) echo('style="display:none;"') ?>>
	<div class="code-type" style="display: none;"><?php echo $code_type?></div>
	<div class="code-name" style="display: none;"><?php echo $code_name?></div>
	<div class="code-div" style="display: none;"><?php echo $code_div?></div>
	<div class="code-class" style="display: none;"><?php echo str_replace(',', ' ', $code_class)?></div>
	<div class="code-blank" style="display: none;"><?php echo $code_blank?></div>
	<div class="hide_first_level" style="display: none;"><?php echo $hide_first_level?></div>
	<div class="form-group" <?php if ($code_type=='search') echo('style="display:none;"')?>>
		<label class="control-label col-md-2"><?php echo $div_name?>(<?php echo __('admin.itme.itemmeu.multiple_allowed') ?>)</label>
		<div class="col-md-10">
			<div class="input-group class-show">
			<?php 
			$code_class = str_replace(',', ' ', $code_class);
			if (!isset($delimiter)) $delimiter = ' ';
			$class_cd_array = explode($delimiter, $code_class);
				foreach($class_cd_array as $class_cd) {
					if ($class_cd == '') continue;
					$class_cd_arr = [];
					for($i=strlen($class_cd)/2; $i>0; $i--) {
						$class_cd_arr[] = substr($class_cd, 0, $i*2);
					}
					$class_name = '';
					for($i=0; $i<count($class_cd_arr); $i++) {
						if ($i==2) break;
						if ($i==0) {
							$class_name = $class_dict[$class_cd_arr[$i]];
						}
						else {
							$class_name = $class_dict[$class_cd_arr[$i]] . ' > ' . $class_name;
						}
					}
					echo('<span class="select-class alert alert-success" style="line-height:32px;margin-right:10px;padding:3px 8px;cursor:pointer;" class_cd="' . $class_cd . '">' . $class_name . '<a class="delete-class" style="color:red;padding:3px 3px;margin-left:5px;cursor:pointer;">✕</a></span>');
				}			
				echo('<a class="add-class" style="padding:3px 3px;cursor:pointer;margin-left:10px;">' . __('admin.itme.item.classification_add')  . '</a>');
			?>
			</div>
		</div>
	</div>									
	<div class="form-group class-setting" <?php if ($code_type=='edit') echo('style="display:none;"')?>>
		<label class="control-label col-md-<?php if ($code_type=='edit') {echo('2');} else {echo('1');}?>"><?php if ($code_type=='edit') {echo(__(''));} else {echo($div_name);}?></label>
		<?php for($i=0; $i<4; $i++) {
			if ($i==0 && $hide_first_level == 1) {
				echo('<div class="col-md-2" style="display:none;">');
			}
			else {
				echo('<div class="col-md-2">');
			}
			echo Form::select('class_cd'.$i, [], '', array('class'=>'form-control class-dropdown'));
			echo('</div>');
		}?>
		<button type="button" class="btn green mr10 add-button-class" <?php if ($code_type=='search') echo('style="display:none;"')?>><?php echo __('admin.itme.item.additional_classification') ?></button>
		<button type="button" class="btn red mr10 cancel-button-class" <?php if ($code_type=='search') echo('style="display:none;"')?>><?php echo __('admin.itme.item.cancel_classification') ?></button>
	</div>
</div>