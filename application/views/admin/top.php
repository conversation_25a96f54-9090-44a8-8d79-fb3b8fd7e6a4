<?php if ($dash_data != NULL) {?>
  <script type="text/javascript">
    const _lang_cd_obj = '<?php echo json_encode($_lang_cd_obj, JSON_UNESCAPED_UNICODE) ?>'
    const _chanel_cd_obj = '<?php echo json_encode($_chanel_cd_obj, JSON_UNESCAPED_UNICODE) ?>'
    const _notify_tickets = '<?php echo json_encode($dash_data["notify_tickets"], JSON_UNESCAPED_UNICODE) ?>'
    const _flg_bot = '<?php echo $flg_bot ?>'
    // 動作確認用
    const _service_useflg = <?php echo json_encode($service_useflg, JSON_UNESCAPED_UNICODE) ?>;
  </script>

  <style>
    .list-separated {
      margin-top: 6px;
      margin-bottom: 12px;
    }
    .ticket-title {
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 1;
      overflow: hidden;
      margin: 0px;
      padding-right: 10px;
      position: relative;
      /* max-width: 320px; */
    }
    .ticket-createtime {
      width: 90px;
      display: inline-block;
    }
    .ticket-title.unread::after {
      content: '';
      height: 6px;
      width: 6px;
      background-color: #e53361;
      border-radius: 50%;
      display: inline-block;
      position: absolute;
      right: 0px;
      top: 6px;
    }
    .datepicker-dropdown.datepicker-orient-right::after, .datepicker-dropdown.datepicker-orient-right::before {
      left: unset;
    }
    /* お知らせポップアップ */
    .notify-popup {
      display: none;
      width: 100vw;
      height: 100vh;
      position:fixed;
      top: 0;
      left:0;
      z-index:9997!important;
    }
    .notify-popup .notify-background {
      background-color: #1119;
      width: 100%;
      height: 100%;
      position:fixed;
      z-index:9998!important;
    }
    .notify-popup .notify-container {
      background-color: white;
      height: 600px;
      width: 848px;
      padding: 24px;
      border-radius: 24px;
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      z-index:9999!important;
    }
    .notify-popup .notify-title {
      display: flex;
      align-items: baseline;
      justify-content: center;
    }
    .notify-popup .pagenation {
      display: flex;
      width: initial;
      margin-left: 4px;
      color: #77797D;
    }
    .notify-popup .notify-container h2 {
      margin: initial;
      text-align: center;
      font-family: Hiragino Sans;
      font-size: 18px;
      font-weight: 500;
    }
    <?php 
      
      if(count($dash_data["notify_tickets"]) > 1) {
        $width_of_contant = "740px";
      } else {
        $width_of_contant = "800px";
      }
    ?>
    .notify-popup .notify-container .notify-content {
      font-size: 1em;
      min-height: 457px;
      width: <?php echo $width_of_contant?>;
      margin-left: auto;
      margin-right: auto;
      background: #F6F7F9;
      padding: 12px 24px;
      margin-top: 12px;
    }
    .notify-popup .notify-container .notify-content h3 {
      text-align: center;
      display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 1;
      overflow: hidden;
      margin: 0px;
      color: #000;
      font-family: Hiragino Sans;
      font-size: 15px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
    }

    .notify-popup .notify-container .notify-content .notify-description {
      padding: 12px 0px 0px 0px;
      height: 422px;
      overflow: auto;
    }

    .notify-popup .notify-row {
      display: flex;
      justify-content: center;
      margin-top: 16px;
      margin-bottom: 24px;
    }
    .notify-row .action-button.notify {
      z-index: 1;
      min-width: 118px;
      height: 40px;
      padding: 0 24px;
      margin: 0 12px;
    }
    .swiper-slide {
      width: 100%;
      height: 100%;
    }

    .swiper-button-prev,
    .swiper-button-next {
      width:0 !important;
    }

    .swiper-button-prev:after,
    .swiper-button-next:after {
        font-size: 20px !important;
        color: #3D3F45
    }
  </style>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.css" />
  <script src="https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.js"></script>
  <input type="hidden" name="bot_id" id="bot_id" value="<?php echo $bot_id ?>" />
  <input type="hidden" name="bot_release_date" id="bot_release_date" value="<?php echo $bot_release_date ?>" />

  <!-- お知らせポップアップ BEGIN -->
  <?php
    $notifyCloseTime = "";
    if (array_key_exists('notifyCloseTime', $_COOKIE)) {
      $notifyCloseTime  = $_COOKIE["notifyCloseTime"];
    }
    if(strtotime($notifyCloseTime) < strtotime("now") && count($dash_data["notify_tickets"]) >= 1) { 
  ?>
    <div class="notify-popup">
      <div class="notify-container">
        <div class="notify-title">
        <h2><?php echo __('admin.top.notify.title') ?></h2>
        <div class="pagenation"></div>
        </div>
        <div class="swiper">
          <div class="swiper-wrapper">
            <?php foreach ($dash_data["notify_tickets"] as &$item) {?>
              <div class="swiper-slide">
              <div class="notify-content">
                <h3><?php  echo ($item['title']) ?></h3>
                <div class="notify-description"><?php  echo ($item['content']) ?></div>
              </div>
              <div class="notify-row">
                <button type="button" class="action-button notify btn-blue js-notify-detail"
                  onClick="location.href='ticketreply?id=<?php echo($item['ticket_id'])?>'"><?php echo __('admin.top.notify.readmore') ?></button>
                  <?php if(!$first_login){?> <!-- 初回ログイン時のみ非表示 -->
                    <button type="button" class="action-button notify btn-white js-notify-close"><?php echo __('admin.top.notify.readlater') ?></button>
                  <?php } ?>
              </div>
            </div>
            <?php }?>
          </div>
          <?php if(count($dash_data["notify_tickets"]) > 1) { ?>
          <div class="swiper-button-prev"></div>
          <div class="swiper-button-next"></div>
          <?php }?>
        </div>
      </div>
      <div class="notify-background"></div>
    </div>
  <?php }?>
  <!-- お知らせポップアップ END-->
  <!-- カード = ユーザー数・チャット数・リクエスト数... BEGIN -->
  <?php if($flg_bot){?>
    <div class="row">
      <!-- Page Content -->
      <div class="col-md-12" style="padding:0px 5px">
        <div id="page-wrapper">
          <div class="dashboard cards">
            <div class="dashboard card">
              <div class="dashboard card-container">
                <div class="number">
                  <h3><?php echo($dash_data['A01']+$dash_data['A02'])?></h3>
                </div>
                <div class="progress-info">
                  <small><?php echo __('admin.top.progress-info.customers') ?></small>
                  <p></p>
                </div>
                <div class="status-title">CUSTOMERS</div>
              </div>
            </div>
            <div class="dashboard card">
              <div class="dashboard card-container">
                <div class="number">
                  <h3><?php echo(isset($dash_data['A11']) ? $dash_data['A11'] : 0)?></h3>
                </div>
                <div class="progress-info">
                  <small><?php echo __('admin.top.progress-info.conversations') ?></small>
                  <p></p>
                </div>
                <div class="status-title">CONVERSATIONS</div>
              </div>
            </div>
            <div class="dashboard card" style=" <?php if ($dash_data['settings_request'] === null) echo ('display: none'); ?>">
              <div class="dashboard card-container">
                <div class="number">
                  <h3><?php echo($dash_data['total_requests'])?></h3>
                </div>
                <div class="progress-info">
                  <small><?php echo __('admin.top.progress-info.requests') ?></small>
                  <p></p>
                </div>
                <div class="status-title">REQUESTS</div>
              </div>
            </div>
            <div class="dashboard card" style=" <?php if ($dash_data['settings_order'] === null) echo ('display: none'); ?>">
              <div class="dashboard card-container">
                <div class="number">
                  <h3><?php echo($dash_data['total_reservations'])?></h3>
                </div>
                <div class="progress-info">
                  <small><?php echo __('admin.top.progress-info.reservations') ?></small>
                  <p></p>
                </div>
                <div class="status-title">RESERVATIONS</div>
              </div>
            </div>
            <div class="dashboard card">
              <div class="dashboard card-container">
                <div class="number">
                  <h3><?php echo($dash_data['total_talks'])?></h3>
                </div>
                <div class="progress-info">
                  <small><?php echo __('admin.top.progress-info.faqs') ?></small>
                  <p></p>
                </div>
                <div class="status-title">FAQs</div>
              </div>
            </div>
            <div class="dashboard card">
              <div class="dashboard card-container">
                <div class="number">
                  <h3><?php echo($dash_data['work_days'])?></h3>
                </div>
                <div class="progress-info">
                  <small><?php echo __('admin.top.progress-info.workingdays') ?></small>
                  <p></p>
                </div>
                <div class="status-title">
                  <?php 
                    if (isset($dash_data['start_date'])) {
                      echo(substr($dash_data['start_date'],2,14) . '～');
                    }
                    else {
                      echo('　');
                    }
                  ?></div>
              </div>
            </div>
          </div>
        </div>
        <!-- /#page-wrapper -->
      </div>
    </div>
  <?php } ?>
  <!-- カード = ユーザー数・チャット数・リクエスト数... END -->
  <!-- ユーザー数推移・お知らせ BEGIN -->
  <div class="row">
    <!-- ユーザー数推移 -->
    <?php if($flg_bot){?>
      <div class="col-md-6 col-sm-12">
        <!-- BEGIN PORTLET-->
        <div class="portlet light" style="padding:12px 20px 15px 20px;" id="newGraph">
          <div class="portlet-title">
            <div class="caption caption-md">
            <span class="caption-subject theme-font-color bold uppercase"><?php echo __('admin.top.title.number_of_users') ?></span>
            </div>
            <div class="actions">
              <div class="btn-group btn-group-devided" data-toggle="buttons">
                <label class="btn btn-transparent grey-salsa btn-circle btn-sm">
                <input type="radio" name="graph-option1" class="toggle" value="<?php echo($now->modify('-1 week')->format('Y-m-d') . '~' .$now->modify('-1 day')->format('Y-m-d'))?>"><?php echo __('admin.top.label.week') ?></label>
                <label class="btn btn-transparent grey-salsa btn-circle btn-sm active">
                <input type="radio" name="graph-option1" class="toggle" checked value="<?php echo($now->modify('-1 month')->format('Y-m-d') . '~' .$now->modify('-1 day')->format('Y-m-d'))?>"><?php echo __('admin.top.label.month') ?></label>
                <label class="btn btn-transparent grey-salsa btn-circle btn-sm">
                <input type="radio" name="graph-option1" class="toggle" value="<?php echo($now->modify('-6 month')->format('Y-m-d') . '~' .$now->modify('-1 day')->format('Y-m-d'))?>"><?php echo __('admin.top.label.half_year') ?></label>
                <label class="btn btn-transparent grey-salsa btn-circle btn-sm">
                <input type="radio" name="graph-option1" class="toggle" value="<?php echo($now->modify('-1 year')->format('Y-m-d') . '~' .$now->modify('-1 day')->format('Y-m-d'))?>"><?php echo __('admin.top.label.year') ?></label>
                <label class="btn btn-transparent grey-salsa btn-circle btn-sm">
                <input type="radio" name="graph-option1" class="toggle" value="<?php echo($bot_release_date . '~' .$now->modify('-1 day')->format('Y-m-d'))?>"><?php echo __('admin.top.label.all') ?></label>
              </div>
            </div>
          </div>
          <div class="portlet-body" style="height:330px">
            <p id="graph-result"></p>
            <div id="loading1" style="text-align: center;"><img src="/assets/admin/images/loading2.gif"/></div>
            <div id="graph_user" style="height:320px"></div>
          </div>
        </div>
        <!-- END PORTLET-->
      </div>
    <?php } ?>
    <!-- お知らせ -->
    <div class='col-sm-12 <?php if($flg_bot){ echo ('col-md-6'); } ?>'>
      <!-- BEGIN PORTLET-->
      <div class="portlet light" style="padding:12px 20px 15px 20px;">
        <div class="portlet-title">
          <div class="caption caption-md">
            <i class="icon-bar-chart theme-font-color hide"></i>
            <span class="caption-subject theme-font-color bold uppercase"><?php echo __('admin.top.title.news') ?></span>
            <span class="caption-helper hide">weekly stats...</span>
          </div>
          <div class="actions">
          </div>
        </div>
        <div class="actions">
          <?php
            $unreadAll = false;
            $unreadGeneral = false;
            $unreadNew = false;
            $unreadImprovement = false;
            $unreadAi = false;
            $unreadSeminar = false;
            foreach ($dash_data['tickets'] as $rank) {
              if (!in_array(Session::instance()->get('user_id', NULL), explode(",", $rank['content']))) {
                $unreadAll = true;
                if ($rank["ticket_tag_cd"] == "01") {
                  $unreadGeneral = true;
                } elseif ($rank["ticket_tag_cd"] == "02") {
                  $unreadNew = true;
                } elseif ($rank["ticket_tag_cd"] == "03") {
                  $unreadImprovement = true;
                } elseif ($rank["ticket_tag_cd"] == "04") {
                  $unreadAi = true;
                } elseif ($rank["ticket_tag_cd"] == "05") {
                  $unreadSeminar = true;
                } 
              }
            }
            ?>
          <div class="btn-group btn-group-devided" data-toggle="buttons">
            <label class="btn btn-transparent grey-salsa btn-circle btn-sm active">
            <input type="radio" name="tickets-display" class="toggle" id="all" value="" checked><?php echo($unreadAll == true ? '<span style="margin-right:5px;" class="unread-dot"></span>' : '')?><?php echo $_codes['29']['01']?></label>
            <label class="btn btn-transparent grey-salsa btn-circle btn-sm">
            <input type="radio" name="tickets-display" class="toggle" id="general" value="01"><?php echo($unreadGeneral == true ? '<span style="margin-right:5px;" class="unread-dot"></span>' : '')?><?php echo $_codes['40']['01']?></label>
            <label class="btn btn-transparent grey-salsa btn-circle btn-sm">
            <input type="radio" name="tickets-display" class="toggle" id="new" value="02"><?php echo($unreadNew == true ? '<span style="margin-right:5px;" class="unread-dot"></span>' : '')?><?php echo $_codes['40']['02']?></label>
            <label class="btn btn-transparent grey-salsa btn-circle btn-sm">
            <input type="radio" name="tickets-display" class="toggle" id="seminar" value="05"><?php echo($unreadSeminar == true ? '<span style="margin-right:5px;" class="unread-dot"></span>' : '')?><?php echo $_codes['40']['05']?></label>
            <label class="btn btn-transparent grey-salsa btn-circle btn-sm">
            <input type="radio" name="tickets-display" class="toggle" id="improvement" value="03"><?php echo($unreadImprovement == true ? '<span style="margin-right:5px;" class="unread-dot"></span>' : '')?><?php echo $_codes['40']['03']?></label>
            <label class="btn btn-transparent grey-salsa btn-circle btn-sm">
            <input type="radio" name="tickets-display" class="toggle" id="ai" value="04"><?php echo($unreadAi == true ? '<span style="margin-right:5px;" class="unread-dot"></span>' : '')?><?php echo $_codes['40']['04']?></label>
          </div>
        </div>
        <div class="portlet-body" style="height:306px">
          <div class="table-scrollable table-scrollable-borderless">
            <table class="table table-hover table-light">
              <tbody id="Tickets-body"></tbody>
            </table>
            <div style="display: flex; justify-content: flex-end; align-items: center;">
              <a href="/admin/tickets"><?php echo __('admin.top.title.all_news') ?></a>
            </div>
          </div>
        </div>
      </div>
      <!-- END PORTLET-->
    </div>
  </div>
  <!-- ユーザー数推移・お知らせ END -->
  <!-- ユーザー統計、ワードクラウド BEGIN -->
  <?php if($flg_bot){?>
    <div class="row">
      <div class="col-md-6 col-sm-12">
        <!-- BEGIN PORTLET-->
        <div class="portlet light" style="padding:12px 20px 0px 20px;">
          <div class="portlet-title">
            <div class="caption caption-md">
              <i class="icon-bar-chart theme-font-color hide"></i>
              <span class="caption-subject theme-font-color bold uppercase"><?php echo __('admin.top.label.user_statistics') ?></span>
              <span class="caption-helper hide">weekly stats...</span>
            </div>
            <div class="actions">
              <label><?php echo Form::select('chart-style', ['bar' => __('admin.top.label.bar_chart'),'pie' =>  __('admin.top.label.pie_chart')],'', array('id'=>'chart-style','class'=>'form-control'))?></label>
              <div class="btn-group btn-group-devided" data-toggle="buttons">
                <label class="btn btn-transparent grey-salsa btn-circle btn-sm">
                <input type="radio" name="chart-option1" class="toggle" value="<?php echo($now->modify('-1 week')->format('Y-m-d') . '~' .$now->format('Y-m-d'))?>"><?php echo __('admin.top.label.week') ?></label>
                <label class="btn btn-transparent grey-salsa btn-circle btn-sm active">
                <input type="radio" name="chart-option1" class="toggle" checked value="<?php echo($now->modify('-1 month')->format('Y-m-d') . '~' .$now->format('Y-m-d'))?>"><?php echo __('admin.top.label.month') ?></label>
                <label class="btn btn-transparent grey-salsa btn-circle btn-sm">
                <input type="radio" name="chart-option1" class="toggle" value="<?php echo($now->modify('-6 month')->format('Y-m-d') . '~' .$now->format('Y-m-d'))?>"><?php echo __('admin.top.label.half_year') ?></label>
                <label class="btn btn-transparent grey-salsa btn-circle btn-sm">
                <input type="radio" name="chart-option1" class="toggle" value="<?php echo($now->modify('-1 year')->format('Y-m-d') . '~' .$now->format('Y-m-d'))?>"><?php echo __('admin.top.label.year') ?></label>
                <label class="btn btn-transparent grey-salsa btn-circle btn-sm">
                <input type="radio" name="chart-option1" class="toggle" value="<?php echo($bot_release_date . '~' .$now->format('Y-m-d'))?>"><?php echo __('admin.top.label.all') ?></label>
              </div>
            </div>
          </div>
          <div class="portlet-body" style="height:300px">
            <div class="row list-separated">
              <div class="actions">
                <div class="btn-group btn-group-devided" data-toggle="buttons">
                  <label class="btn btn-transparent grey-salsa btn-circle btn-sm active" id="btnChanel">
                  <input type="radio" name="chart-option2" class="toggle" id="chanel" value="chanel" checked><?php echo __('admin.top.label.by_channel') ?></label>
                  <label class="btn btn-transparent grey-salsa btn-circle btn-sm" id="btnLung">
                  <input type="radio" name="chart-option2" class="toggle" id="lang" value="lang"><?php echo __('admin.top.label.by_language') ?></label>
                </div>
              </div>
              <div id="resultWrapper">
                <p id="UserStatistics-result"></p>
              </div>
              <div id="loading2" style="text-align: center;"><img src="/assets/admin/images/loading2.gif"/></div>
              <div id="piechartchanel"></div>
              <div id="piechartlang"></div>
              <div id="barchartchanel"></div>
              <div id="barchartlang"></div>
            </div>
          </div>
        </div>
      </div>
    
      <div class="col-md-6 col-sm-12">
        <!-- BEGIN PORTLET-->
        <div class="portlet light" style="padding:12px 20px 0px 20px;">
          <div class="portlet-title">
            <div class="caption caption-md" style="width: 100%;padding-bottom: 25px;">
              <i class="icon-bar-chart theme-font-color hide"></i>
              <div style="display: flex; justify-content:space-between; align-items: center;">
                <div style="display:flex;">
                  <span class="caption-subject theme-font-color bold uppercase"><?php echo __('admin.top.label.word_cloud') ?></span>
                  <span class="caption-helper" style="margin-left:12px;font-size:13px;"><?php echo __('admin.top.label.word_cloud.tip') ?></span>
                </div>
                <a style="font-size:12px;" href="/admin/textmining"><?php echo __('admin.common.label.details') ?></a>
              </div>
            </div>
          </div>
          <div class="portlet-body" id="WordCloud" style="height:300px">
              <input type="hidden" name="wordcloud-default" value="<?php echo($now->modify('-1 month')->format('Y-m-d') . '~' .$now->format('Y-m-d'))?>">
              <div id="react-wordcloud" style="height:85%"></div>
          </div>
        </div>
      </div>
    </div>
  <?php } ?>
  <!-- ユーザー統計、ワードクラウド END -->

  <script>
    <?php
    $start_date= date("Y-m-d",strtotime("+1 day",strtotime($start_date)));
    for($i=7; $i>0; $i--) {
      $last_date = date("Y-m-d",strtotime("-$i day",strtotime($start_date)));
      echo('var date_' . $i . '="' . $last_date . '";');
      echo('var total_log_' . $i . '=' . $dash_data[$last_date . '-D11'] . ';');
      echo('var total_members_' . $i . '=' . $dash_data[$last_date . '-D01']. ';');
      echo('var total_repeater_' . $i . '=' . $dash_data[$last_date . '-D02']. ';');
    }
    echo('var total_log_all ="' . $dash_data['A11'] . '";');
    echo('var total_log_month ="' . $dash_data['M11'] . '";');
    echo('var total_log_week ="' . $dash_data['W11'] . '";');
    echo('var total_log_today ="' . $dash_data['D11'] . '";');
    echo('var total_members_all ="' . $dash_data['A01'] . '";');
    echo('var total_members_month ="' . $dash_data['M01'] . '";');
    echo('var total_members_week ="' . $dash_data['W01'] . '";');
    echo('var total_members_today ="' . $dash_data['D01'] . '";');
    echo('var total_repeater_all ="' . $dash_data['A02'] . '";');
    echo('var total_repeater_month ="' . $dash_data['M02'] . '";');
    echo('var total_repeater_week ="' . $dash_data['W02'] . '";');
    echo('var total_repeater_today ="' . $dash_data['D02'] . '";');
    if ($dash_data['A11'] > 0)
      echo('var total_answer_all ="' . round($dash_data['A12']*100/$dash_data['A11'], 2) . '";');
      else
        echo('var total_answer_all ="0";');
    if ($dash_data['M11'] > 0)
      echo('var total_answer_month ="' . round($dash_data['M12']*100/$dash_data['M11'], 2) . '";');
    else
      echo('var total_answer_month ="0";');
    if ($dash_data['W11'] > 0)
      echo('var total_answer_week ="' . round($dash_data['W12']*100/$dash_data['W11'], 2) . '";');
    else
      echo('var total_answer_week ="0";');
    if ($dash_data['D11'] > 0)
      echo('var total_answer_today ="' . round($dash_data['D12']*100/$dash_data['D11'], 2) . '";');
    else
      echo('var total_answer_today ="0";');

    if ($dash_data['A11'] > 0)
      echo('var total_correct_all ="' . round($dash_data['A13']*100/$dash_data['A11'], 2). '";');
    else
      echo('var total_correct_all ="0";');
    if ($dash_data['M11'] > 0)
      echo('var total_correct_month ="' . round($dash_data['M13']*100/$dash_data['M11'], 2). '";');
    else
      echo('var total_correct_month ="0";');
    if ($dash_data['W11'] > 0)
      echo('var total_correct_week ="' . round($dash_data['W13']*100/$dash_data['W11'], 2). '";');
    else
      echo('var total_correct_week ="0";');
    if ($dash_data['D11'] > 0)
      echo('var total_correct_today ="' . round($dash_data['D13']*100/$dash_data['D11'], 2). '";');
    else
      echo('var total_correct_today ="0";');
    ?>
    var show_bot_box = false;
  </script>
<?php } else { ?>
  <script type="text/javascript">
    const _lang_cd_obj = '<?php echo json_encode($_lang_cd_obj, JSON_UNESCAPED_UNICODE) ?>'
    const _chanel_cd_obj = '<?php echo json_encode($_chanel_cd_obj, JSON_UNESCAPED_UNICODE) ?>'
    const _notify_tickets = '[]'
    const _flg_bot = '<?php echo $flg_bot ?>'
  </script>
  <script>
    var show_bot_box = true;
  </script>
<?php } ?>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<script src="/assets/common/react/components/blocks/wordcloud.bundle.js"></script>
<script type="text/javascript">
  document.addEventListener("DOMContentLoaded", function(){
    window.talkappi_admin_top_setupWordCloud({
            lang_cd: "<?php echo $admin_lang_cd ?>",
            words: <?php echo json_encode($wordcloud_data, true) ?>
        });
  })
</script>