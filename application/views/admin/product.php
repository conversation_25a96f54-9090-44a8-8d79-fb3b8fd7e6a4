<script>
var _cancel_policy_list = <?php echo(json_encode($item_data['cancel_policy']))?>;
var _default_cancel_policy = <?php echo(json_encode($default_cancel_policy))?>;
</script>
<?php 
$model = new Model_Adminmodel;
?>

<div class="top-nav">
	<?php echo $menu ?>
</div>
<div class="content-container white border">
	<input type="hidden" id="act" name="act" value="" />
	<input type="hidden" name="cancel_policy" id="cancel_policy" value="" />
	<input type="hidden" id="item_div" value="<?php echo $item_div?>" />
	<div class="form-body">
		<div class="form-group">
			<label class="control-label col-md-2">販売</label>
			<div class="col-md-2">
				<div class="talkappi-radio" data-name="public_flg" data-value="<?php echo($post['public_flg']) ?>" data-source='{"0":"OFF", "1":"ON"}'></div>
			</div>																		
		</div>
		<div class="form-group">
			<label class="control-label col-md-2">販売言語</label>
			<div class="col-md-9">
				<div class="talkappi-checkbox" data-name="lang_display" data-value='<?php echo(json_encode(explode(',', $post['lang_display']))) ?>' data-source='<?php echo(json_encode($_bot_lang, JSON_UNESCAPED_UNICODE)) ?>'></div>
			</div>
		</div>
		<div class="form-group">
			<label class="control-label col-md-2">機能分類</label>
			<div class="col-md-9">
				<div class="talkappi-category-select" data-name="class_cd" data-div='<?php echo $code_div ?>' data-value='<?php if ($post['class_cd'] == '') {echo('[]');} else {echo(json_encode(explode(' ', $post['class_cd'])));}?>'></div>
			</div>
		</div>
		<?php if ($item_div == 5 && strpos($post['class_cd'], '02') === 0 && count($post['payment_support']) > 0) {
		?>								
			<div class="form-group">
			<label class="control-label col-md-2">決済方法</label>
				<div class="col-md-9">
					<label class="control-label col-md-1" style="margin-left: -5px; margin-right: 20px; white-space: nowrap;">国内(日本語)</label>
					<div class="talkappi-checkbox" data-name="payment_jp" data-value='<?php echo(json_encode($item_data['payment_setting']['jp'])) ?>' data-source='<?php echo(json_encode($post['payment_support'], JSON_UNESCAPED_UNICODE)) ?>'></div>
				</div>
			</div>
			<div class="form-group">
			<label class="control-label col-md-2"></label>
				<div class="col-md-9">
					<label class="control-label col-md-1" style="margin-left: -5px; margin-right: 20px; white-space: nowrap;">海外(外国語)</label>
					<div class="talkappi-checkbox" data-name="payment_foreign" data-value='<?php echo(json_encode($item_data['payment_setting']['foreign'])) ?>' data-source='<?php echo(json_encode($post['payment_support'], JSON_UNESCAPED_UNICODE)) ?>'></div>
				</div>
			</div>
		<?php
		}
		?>
		<div class="form-group" style="display:none;">
			<label class="control-label col-md-2"><?php echo($item_div_text[$item_div])?>コード</label>
			<div class="col-md-2">
				<div class="input-icon right">
					<input readonly name="product_cd" type="text" class="form-control" value="<?php if ($post != NULL) echo($post['product_cd'])?>">
				</div>
			</div>
		</div>
		<div class="form-group" style="display:none;">
			<label class="control-label col-md-2"><?php if ($item_div==7) {echo('利用期間');} else{echo('販売期間');} ?></label>
			<div class="col-md-4">
				<input name="start_date" id="start_date" value="<?php if ($post != NULL)echo($post['start_date'])?>" style="float:left;" class="form-control form-control-inline input-small date-picker" size="16" data-date-format="yyyy-mm-dd"  type="text"/>
				<input name="end_date" id="end_date" value="<?php if ($post != NULL) echo($post['end_date'])?>" style="float:left; margin-left:10px;" class="form-control form-control-inline input-small date-picker" size="16" data-date-format="yyyy-mm-dd"  type="text"/>
			</div>																					
		</div>
		<?php 
		if (count($icons) > 0) {
			echo('<div class="form-group" style="display:none;">');
			echo('<label class="control-label col-md-2 mr10">写真TAG</label>');
			echo('<div class="btn-group" data-toggle="buttons">');
			foreach($icons as $k=>$v) {
				if (in_array($k, $post['tags'])) {
					echo('<label class="btn default active">');
					echo('<input name="tags[]" type="checkbox" checked value="' . $k . '" class="toggle">' . $k. '</label>');
				}
				else {
					echo('<label class="btn default">');
					echo('<input name="tags[]" type="checkbox" value="' . $k . '" class="toggle">' .  $k . '</label>');
				}
			}
			echo('</div>');
			echo('</div>');
		}
		?>
		<?php if (count($icons) > 0 || count($banners) > 0) {?>
		<div class="form-group" style="display:none;">
			<label class="control-label col-md-2">写真TAG即時反映</label>
			<div class="col-md-2">
				<input type="checkbox" name="flg_img_invalidate" value="1" class="make-switch" data-on-color="success" data-off-color="warning">
			</div>																		
		</div>	
		<?php }?>																		
		<?php foreach($item_data_def as $key=>$def) {
			if ($def['type'] == 'sel' || $def['type'] == 'opt' || $def['type'] == 'chk') {
				$def['list'] = $model->get_customize_list($def);
				if (count($def['list']) == 0) continue;
			}
			if ($key == 'discount_value2' || $key == 'discount_type2') {
				$show = ' style="display:none;"';
			}
			else {
				$show = '';
			}
			echo('<div class="form-group"' . $show . ' id="def_' .$key . '">');
			echo('<label class="control-label col-md-2">' . $def['title'] . '</label>');
			echo('<div class="col-md-8">');
			if ($def['type'] == 'opt') {
				echo('<div class="talkappi-radio" data-name="' . $key . '" data-value="' . $item_data[$key] . '" data-source=\'' .  json_encode($def['list'], JSON_UNESCAPED_UNICODE) .'\'></div>');
			} 
			else if ($def['type'] == 'sel') {
				echo('<div class="talkappi-pulldown" data-name="' . $key . '" data-value="' . $item_data[$key] . '" data-source=\'' .  json_encode($def['list'], JSON_UNESCAPED_UNICODE) .'\'></div>');
			}
			else if ($def['type'] == 'chk') {
				echo('<div class="talkappi-checkbox" data-name="' . $key . '" data-value=\'' . json_encode($item_data[$key]) . '\' data-source=\'' .  json_encode($def['list'], JSON_UNESCAPED_UNICODE) .'\'></div>');
			}
			else if ($def['type'] == 'num' || $def['type'] == 'txt') {
				$style = '';
				if ($def['type'] == 'num') {
					$style = 'style="width:100px;text-align: right;"';
				}
				if (array_key_exists($key, $item_data)) {
					$value = $item_data[$key];
				}
				else {
					$value = '';
				}
				echo('<input name="' . $key . '" type="text" class="form-control" value="' .  $value . '"' . $style . ' >');
			}
			echo('</div>');
			echo('</div>');
			if ($key == 'discount_value') {
				$child_discount = [];
				if (array_key_exists('child_discount', $item_data)) {
					$child_discount = $item_data['child_discount'];
				}
				echo('<div class="js-child-discount" style="display:none;">');
				foreach($child_price_jp as $child) {
					echo('<div class="form-group" id="def_child_discount">');
					echo('<label class="control-label col-md-2">' . $child['type'] . ':' . $child['label'] . '</label>');
					echo('<div class="col-md-1">');
					if (array_key_exists('child_' . $child['type'], $child_discount)) {
						$val = $child_discount['child_' . $child['type']];
					}
					else {
						$val = '';
					}
					echo('<input name="child_' . $child['type'] . '" type="text" class="form-control" style="width:100px;text-align: right;" value="' . $val . '" >');
					echo('</div>');
					echo('</div>');
				}
				echo('</div>');
			}
		}?>
		
		<?php if ($item_div == 5 && strpos($post['class_cd'], '02') === 0) {?>								
			<div class="form-group" style="display: none;">
				<label class="control-label col-md-2">プランタグ</label>
				<div class="col-md-9">
				<div class="btn-group" data-toggle="buttons">
				<?php foreach($code_tags as $k=>$v) {
					echo('<label class="btn default');
					if (in_array($k, $post['product_tags'])) echo(' active ');
					echo('"><input name="product_tags[]" type="checkbox"');
					if (in_array($k, $post['product_tags'])) echo(' checked ');
					echo(' value="' . $k . '" class="toggle">' . $v . '</label>');
				}
				?>
				</div>
				</div>										
			</div>
			<div class="form-group">
				<label class="control-label col-md-2">キャンセル設定</label>
				<div class="col-md-9">
					<label class="control-label">※個別に設定しない場合、全体で設定されたキャンセルポリシーが自動適用されます。</label>
					<br>
					<label class="control-label">※可能な限り、キャンセル料が発生しない期間を設定してください。</label>
				</div>
			</div>
			<div class="form-group js-cancel-policy-label">
				<label class="control-label col-md-2"></label>
				<div class="col-md-9">
					<label class="control-label"><a class="js-cancel-policy-add">キャンセルポリシー追加</a></label>
				</div>
			</div>	
		<?php }?>
	</div>
	<div class="form-actions">
		<div class="row">
			<div class="col-md-offset-2 col-md-9 flex">
				<button type="button" id="saveBaseButton" class="btn-larger btn-blue js-action-save">保存</button>
				<?php if ($post['end_date'] == '2000-01-01' && false) {?>
					<button type="button" class="btn-larger js-action-public">公開する</button>	
				<?php }?>
				<button type="button" onclick="top.location='/admin/<?php echo $func?>s'" class="btn-larger btn-white">戻る</button>
				<div class="btn-larger btn-red-border js-action-delete">
					<span class="icon-delete"></span>
				</div>
			</div>
		</div>
	</div>
</div>




