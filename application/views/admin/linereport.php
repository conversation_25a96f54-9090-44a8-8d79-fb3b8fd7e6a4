<style type="text/css">
.dashboard-cards-aline {
  margin: 0px 0px 5px 5px;
}
.dashboard-info-aline {
  margin: 0px 0px 10px 6px;
}
</style>

<!-- Page Content -->

<div id="page-wrapper">
  <nav class="line-tab" style="margin-left: 8px;">
  <ul>
      <li class="active">
        <a class="func-menu"  style="font-size: 14px;" href="/admin/linereport">LINE利用状況</a>
      </li>
      <li>
				<a class="func-menu"  style="font-size: 14px;" href="/admin/botline">リッチメニュー</a >
			</li>
      <li>
				<a class="func-menu"  style="font-size: 14px;" href="/admin/linesetting">LINE連携の基本設定</a >
			</li>
    </ul>
  </nav>
  <?php if ($is_line_coordination || $bot_id === '0') { ?>
  <div class="portlet menu" style="margin-bottom: 0 !important;padding-left: 0 !important;">
    <div class="dashboard-info-aline"><?php echo $displayName !== NULL ? $displayName : '' ?><?php echo $displayName !== NULL ? '(' . $basicId . ')' : '' ?></div>
  </div>
  <div class="form-group" style="margin-left: -4px;">
    <div class="col-md-8">
      <input type="text" class="talkappi-datepicker js-date" name="start_date" value="<?php echo (new DateTimeImmutable())->modify('-1 day')->format('Y-m-d') ?>" disabled />
    </div>
  </div>
  <p class="dashboard-cards-title dashboard-cards-aline">友だち</p>
  <div class="dashboard cards">
    <div class="dashboard card">
      <div class="dashboard card-container">
        <div class="number">
          <h3 id="followers"><?php echo $followers !== NULL ? $followers : 0 ?></h3>
        </div>
        <div class="progress-info">
          <small>友だち数</small>
          <p></p>
        </div>
        <div class="status-title">Friends</div>
      </div>
    </div>
    <div class="dashboard card">
      <div class="dashboard card-container">
        <div class="number">
          <h3 id="targetedReaches"><?php echo $targetedReaches !== NULL ? $targetedReaches : 0 ?></h3>
        </div>
        <div class="progress-info">
          <small>ターゲットリーチ数</small>
          <p></p>
        </div>
        <div class="status-title">Active Friends</div>
      </div>
    </div>
    <div class="dashboard card">
      <div class="dashboard card-container">
        <div class="number">
          <h3 id="blocks"><?php echo $blocks !== NULL ? $blocks : 0 ?></h3>
        </div>
        <div class="progress-info">
          <small>ブロック数</small>
          <p></p>
        </div>
        <div class="status-title">Blocked</div>
      </div>
    </div>
  </div>
  <p class="dashboard-cards-title dashboard-cards-aline">メッセージ</p>
  <div class="dashboard cards">
    <div class="dashboard card">
      <div class="dashboard card-container">
        <div class="number">
          <h3 id="apiReply"><?php echo $apiReply !== NULL ? $apiReply : 0 ?></h3>
        </div>
        <div class="progress-info">
          <small>当日のリプライ送信メッセージ数</small>
          <p></p>
        </div>
        <div class="status-title">Reply</div>
      </div>
    </div>
    <div class="dashboard card">
      <div class="dashboard card-container">
        <div class="number">
          <h3 id="apiPush"><?php echo $apiPush !== NULL ? $apiPush : 0 ?></h3>
        </div>
        <div class="progress-info">
          <small>当日プッシュ送信メッセージ数</small>
          <p></p>
        </div>
        <div class="status-title">Push</div>
      </div>
    </div>
    <div class="dashboard card">
      <div class="dashboard card-container">
        <div class="number">
          <h3 id="totalUsage"><?php echo $totalUsage !== NULL ? $totalUsage : 0 ?></h3>
        </div>
        <div class="progress-info">
          <small>今月のメッセージ数</small>
          <p></p>
        </div>
        <div class="status-title">Messages</div>
      </div>
    </div>
  </div>
  <?php } else {?>
    <div class="dashboard-info-aline">御社のLINE公式アカウントと未連携のため表示できません</div>
  <?php } ?>
</div>

<!-- /#page-wrapper -->