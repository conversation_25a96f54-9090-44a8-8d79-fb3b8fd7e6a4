<style>


</style>

<div  class="skill_box" tabindex="-1" >
	<div style="margin-left: 37px; margin-right:61px">
		<div >

			<input type="hidden" class="class_div" value=""/>
			<div class="intent_cd" style="display:none;"></div>
			<div class="sub_intent_cd" style="display:none;"></div>
			<div class="skill_data" style="display:none;"></div>
			<div class="param_data" style="display:none;"></div>
			<div class="param" style="display:none;"></div>
			<div class="area" style="display:none;"> value=""</div>
			<div class="form-group" style="margin:0;" >
				<div class="row" style="padding: 5px; display:flex">
					<label class="col-md-3 control-label text-align" style="margin-top:5px; display:inline-block; ">表示テキスト</label>
					<div class="col-md-9" style="display:flex; flex:auto">
						<input type="text" class="form-control action_text" value="" data="action_text">
					</div>
				</div>
			</div>

			<div class="form-group" style="margin:0;" >
				<div class="row" style="padding: 5px; display:flex">
					<label class="col-md-3 control-label text-align " style="margin-top:5px; display:inline-block; ">アクション</label>
					<div class="col-md-9" style="display:flex; flex:auto">
						<?php echo Form::select('skill_kind[]', $skill_kinds, '', array('class'=>'form-control rcm-area-text-box skill_kind'))?>
						<?php echo Form::select('skill', [], '', array('class'=>'form-control skill'))?>
					</div>
				</div>
			</div>
			<div class="skill_body" style="margin-bottom: 10px;">

			</div>
			<div>
				<?php if ($all_language !== NULL) { ?>
				<label style="float:left;" class="control-label col-md-2">全言語適用</label>
				<div class="col-md-2" style="float:left;">
					<input type="checkbox" name="flg_apply_all_lang" <?php if (true || $all_language == 1) echo('checked')?> value="1" class="make-switch" data-on-color="success" data-off-color="warning">
				</div>
				<?php }?>
				<?php if (isset($default_btn)) { ?>
				<!-- <button type="button" class="btn yellow act" class="default_skill">デフォルト戻す</button>
				<?php }?>
				<button type="button" class="btn default" data-dismiss="modal">キャンセル</button>
				<button type="button" class="btn red act" class="delete_skill">削除</button>
				<button type="button" class="btn green act" class="save_skill">確定</button> -->
			</div>
		</div>
		<!-- /.modal-content -->
	</div>
	<!-- /.modal-dialog -->
</div>