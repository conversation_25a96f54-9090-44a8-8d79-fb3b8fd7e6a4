<style>
  .report-menu {
    height: 140px;
  }

  .form-group {
    margin-left: 10px !important;
  }

  .dropdown-menu.open {
    width: fit-content;
  }
</style>

<script>
	var _grp_bot_id = <?php echo $grp_bot_id?>;
	const _flg_ai_auto_apply = <?php echo $flg_ai_auto_apply == '1' ? 'true' : 'false'; ?>;
</script>

<input type="hidden" id="class_cd_cond" name="class_cd_cond" value="<?php echo $scene_cd ?>" />
<input type="hidden" id="multiple" name="multiple" value="true" />

<!-- BEGIN PAGE HEADER-->
<!-- BEGIN PAGE HEAD -->
<div class="page-head">
	<!-- BEGIN PAGE TITLE -->
	<div class="page-title">
		<h1>FAQ利用状況統計<small></small></h1>
	</div>
	<!-- END PAGE TITLE -->
</div>
<input type="hidden" id="log_id" name="log_id" value="" />
<input type="hidden" id="log_content" name="log_content" value="" />
<input type="hidden" id="act" name="act" value="" />
<input type="hidden" name="lang_cd" id="lang_cd" value="" />
<!-- END PAGE HEAD -->
<!-- END PAGE HEADER-->
<!-- BEGIN PAGE CONTENT-->
<div class="report-menu" style="background-color: #f6f7f9;">
	<div class="report-menu-tub row menu-row">
		<div class="form-body" style="margin-top:10px;">		
			<div class="form-group">
				<label class="control-label col-md-1" style="min-width:100px;"><?php echo __('admin.common.label.status_kana') ?></label>
				<?php echo $reportmenu ?>
			</div>
		</div>			
	</div>
	<div class="report-menu-edit row menu-row">
		<div class="form-body">		
			<div class="form-group">
				<label class="control-label col-md-1" style="min-width:100px;"><?php echo __('admin.common.label.conditions') ?></label>
				<?php echo $botcond ?>
			</div>
			<div class="form-group">
				<label class="control-label col-md-1" style="min-width:100px;"><?php echo __('admin.common.label.period') ?></label>
				<div class="col-md-6" style="background-color: #f6f7f9;display:flex;">
					<div class="survey-period-date-container" style="width: 150px; background-color:white;">
						<span class="icon-calender" style="margin:8px 8px 8px 3px;"></span>
						<input name="start_date" id="start_date" value="<?php echo($start_date)?>" class="date-picker line-none" style="width: 100%;" size="12" data-date-format="yyyy-mm-dd" type="text" autocomplete="off" />
					</div>
					<p>〜</p>
					<div class="survey-period-date-container" style="width: 150px; background-color:white; margin-left: 5px; margin-right:12px; "> 
						<span class="icon-calender" style="margin:8px 10px 8px 5px;"></span>
						<input name="end_date" id="end_date" value="<?php echo($end_date)?>" class="date-picker line-none" style="width: 100%;" size="12" data-date-format="yyyy-mm-dd" type="text" autocomplete="off" />
					</div>
					<button type="button" id="searchButton" class="btn-smaller btn-blue">
						<i class="fa fa-search mr10"></i><?php echo __('admin.common.button.search') ?>
					</button>
					<button type="button" id="clearButton" class="btn-smaller btn-gray-black"><?php echo __('admin.common.button.reset') ?></button>
				</div>
			</div>
		</div>			
	</div>
</div>
<div class="row">
	<div class="col-md-12">
		<!-- Page Content -->
		<div id="page-wrapper">
		<div class="form-body" style="margin-top: 12px;margin-bottom: 0;">
			<div class="form-group">
			<div style="float:right; margin-right: 20px">
				<button type="button" data-tableId="talkreport2table" data-title="未回答質問" class="btn-smaller btn-white exportCsv">
					<span class="icon-export"></span><?php echo __('admin.common.button.csv_export') ?>
				</button>
			</div>
			</div>
		</div>	

		<div class="portlet light">			        
			<div class="col-md-2" style="margin-top: 12px;">
				<?php echo Form::select('lang_cd', $lang, $lang_cd, array('id'=>'lang_cd','class'=>'form-control', 'style'=>'height:28px;'))?>
			</div>
			<div class="portlet box">
				<div class="portlet-body">						
				<table class="table table-bordered table-hover js-data-table" id="talkreport2table">
				<thead style="background-color: #f6f7f9;">
				<tr>
					<th>
						<?php echo __('admin.talkreport2.label.sns') ?>
					</th>
					<th>
						<?php echo __('admin.talkreport2.label.name') ?>
					</th>
					<th>
						<?php echo __('admin.common.label.member_id') ?>
					</th>
					<th style="width:200px;">
						<?php echo __('admin.talkreport2.label.date_of_question') ?>
					</th>
					<th>
						<?php echo __('admin.talkreport2.label.question_content') ?>
					</th>
					<th>
						<?php echo __('admin.common.label.add_faq') ?>
					</th>																											
				</tr>
				</thead>

				<tbody>
				<?php
					foreach ($msgs as $msg) {
				?>	
				<tr class="gradeX odd" role="row">
					<td class="sorting_1">
						<?php 
						if ($msg['sns_type_cd']== 'fb' || $msg['sns_type_cd']== 'FB') {
								echo('<img src="/assets/common/images/icon_fb.png" style="margin:5px;width:16px;"/>');
							}
							if ($msg['sns_type_cd']== 'ln' || $msg['sns_type_cd']== 'LN') {
								echo('<img src="/assets/common/images/icon_ln.png" style="margin:5px;width:16px;"/>');
							}
							if ($msg['sns_type_cd']== 'wc' || $msg['sns_type_cd']== 'WC') {
								echo('<img src="/assets/common/images/icon_wc.png" style="margin:5px;width:16px;"/>');
							}
							if ($msg['sns_type_cd']== 'wb' || $msg['sns_type_cd']== 'WB') {
								echo('<img src="/assets/common/images/icon_wb.png" style="margin:5px;width:16px;"/>');
							}
						?>
					</td>
					<td class="sorting_1">
						<?php 
						if ($msg['sns_type_cd']== 'fb' || $msg['sns_type_cd']== 'FB') {
								echo($msg['last_name'] . ' ' . $msg['first_name']);
							}
							if ($msg['sns_type_cd']== 'ln' || $msg['sns_type_cd']== 'LN') {
								echo($msg['last_name'] . ' ' . $msg['first_name']);
							}
							if ($msg['sns_type_cd']== 'wc' || $msg['sns_type_cd']== 'WC') {
								echo($msg['last_name'] . ' ' . $msg['first_name']);
							}
							if ($msg['sns_type_cd']== 'wb' || $msg['sns_type_cd']== 'WB') {
								echo(__('admin.common.label.web_user'));
							}
						if ($msg['is_tester']== 1) {
							echo('・' . __('admin.common.label.web_tester'));
						}
						?>
					</td>
					<td>
						<?php echo($msg['member_id'])?>									
					</td>			
					<td>
						<?php 
							$date = $msg['log_time'];
							echo date('Y/m/d H:i', strtotime($date));
						?> 	
						<a class="pop_adminchat" member_id=<?php echo $msg['member_id'] ?><?php if ($bot_id!=null) echo(' bot_id="' . $bot_id . '"')?>><span class="badge" style="height: 24px;padding:6px 12px;color:black; background-color:#d3eeff; float: right;"><?php echo __('admin.common.label.historical_view') ?></span> </a>
					</td>
					<td>	
						<?php
						if ($msg['lang_cd'] == 'ja' || $msg['member_msg_t'] == '')
							echo(htmlspecialchars($msg['member_msg']));
						else
							echo(htmlspecialchars($msg['member_msg_t']) . "<br/>※" . __('admin.common.label.original_text') . "：" . htmlspecialchars($msg['member_msg']));
						?>								
					</td>
					<td style="text-align:center;">
						<?php 
						if (array_key_exists($msg['log_id'], $redmine_link)) {
							echo('<a class="redmine-link" target="_blank" href="' . $redmine_url . 'issues/' . $redmine_link[$msg['log_id']]['ticket_id'] . '"');
							echo('>');
							echo('<span class="badge" style="color: black;background-color: #cff2d7; height: 24px;padding: 6px 12px;width: 70px;" >' . __('admin.common.label.request_detail') . '</span> </a>');
						}
						else {
							echo('<a class="redmine" data-bot_id="' . $msg['bot_id'] . '" log_id="' . $msg['log_id'] . '"');
							if ($bot_id!=null) echo(' bot_id="' . $bot_id . '"');
							echo('>');
							echo('<span class="badge" style="color: black;background-color: #ffdce5; height: 24px;padding: 6px 12px;width: auto;" >' . __('admin.talkreport2.label.request') . '</span> </a>');
						}
						?>
					</td>																																		
				</tr>
				<?php } ?>
				</tbody>
				</table>
				</div>
			</div>
		</div>
		</div>
		<!-- /#page-wrapper -->					
	</div>
</div>
<!-- END PAGE CONTENT-->
<?php echo $redmine_box ?>
