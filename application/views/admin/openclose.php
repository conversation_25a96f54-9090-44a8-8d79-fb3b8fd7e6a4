<style>
	.icon-detail {
		width: 23px !important;
	}

	.basic-label {
		width: 15em !important;
	}

	.settings-container {
		margin: 12px 12px 12px 12px;
		padding: 12px 0;
		border: unset;
		border-bottom: 1px solid #EBEDF2;
		background: #fff;
	}

	.lines-container {
		margin: 10px 0 10px 0;
	}

	.section-container.hide-section .setting-header-conteiner div {
		margin: 0 !important;
	}

	.talkappi-dropdown-selected-text {
		max-width: 350px;
	}
</style>

<div class="page-head">
	<!-- BEGIN PAGE TITLE -->
	<div class="page-title">
		<h1>
			<?php echo ($_active_menu_name) ?><small></small>
		</h1>
	</div>
	<!-- END PAGE TITLE -->
</div>
<!-- END PAGE HEAD -->
<!-- END PAGE HEADER-->
<!-- BEGIN PAGE CONTENT-->
<!-- Page Content -->

<div id="page-wrapper">
				<?php
				if (strlen($_active_menu) > 4) {
					$tab_menu = View::factory('admin/menutab');
					echo ($tab_menu);
				}
				?>
  
	<input type="hidden" name="message" id="message" value="<?php echo ($message) ?>" />
	<input type="hidden" name="service" id="service" value="<?php echo ($service) ?>" />
	<input type="hidden" name="bots" id="bots" value="" />

	<div class="content-container white border">
	<!-- 開通・停止処理（※）-->
	<?php if ($_user->role_cd == "99" && ($bot == null || $bot->bot_id != 0)) { ?>
	<div class="settings-container">
		<div class="setting-header">開通・停止処理（※）</div>
		<?php foreach ($contract_services as $contract_service) {
			// 親契約なし、子契約の場合、$contract_service['flg'] = 1
			if ($contract_service['option_cd'] == 'bot') {
				$opened = $bot != null && $contract_service['start_date'] != null && $bot->bot_status_cd == '04' || isset($contract_service['flg']);
			}
			else {
				$opened = $bot != null && $contract_service['start_date'] != null || isset($contract_service['flg']);
			}
			?>
			<div class="lines-container">
				<label class="basic-label"></label>
				<div class="col-md-8">
					<input type="text" class="form-control "
						style="width:160px; background-color: #D3EEFF;border-radius: 12px;" readonly
						value="<?php echo ($contract_service['name']) ?>">
				</div>
			</div>
			<?php if ($contract_service['option_cd'] == 'bot') { ?>
			<div class="lines-container" <?php if ($_user->role_cd != "99")
				echo ('style="display:none;"'); ?>>
				<label class="basic-label">ステータス</label>
				<div class="col-md-3">
					<?php 
					 if ($bot != null && $bot->bot_status_cd == '04'){
						echo Form::select('bot_status_cd', $_codes['04'], $post['bot_status_cd'], array('id' => 'bot_status_cd', 'class' => 'form-control', 'disabled' => 'disabled'));
					 }
					 else {
						echo Form::select('bot_status_cd', $_codes['04'], $post['bot_status_cd'], array('id' => 'bot_status_cd', 'class' => 'form-control'));
					 }
					
					?>
				</div>
			</div>
			<?php } ?>
			<div class="lines-container">
				<label class="basic-label">利用開始日</label>
				<div class="col-md-2" style="width: 150px;">
					<input name="<?php echo $contract_service['option_cd'] ?>_start_date" class="talkappi-datepicker js-date" <?php if ($opened) echo "disabled" ?> value="<?php if ($post != NULL) echo ($contract_service['start_date']) ?>"
							class="form-control form-control-inline input-small date-picker" data-date-format="yyyy-mm-dd"
							size="16" type="text" />
				</div>
				<div class="col-md-2" style="width: 150px;">
					<input name="<?php echo $contract_service['option_cd'] ?>_start_time" class="talkappi-timepicker js-time" <?php if ($opened) echo "disabled" ?> 
								value="<?php echo ($contract_service['start_time']?($contract_service['start_time']) : 0) ?>" />
				</div>
				<?php if ($contract_service['option_cd'] == 'bot' || $contract_service['option_cd'] == 'inquiry' || $contract_service['option_cd'] == 'survey' || $contract_service['option_cd'] == 'very') { ?>
				<div class="col-md-2" style="width: 150px;">
					<button type="button" option-service="<?php echo $contract_service['option_cd'] ?>" class="btn green mr10 js-init-button" <?php if ($opened)
					echo ("style='display:none;' readonly"); ?>>開通する</button>
				</div>
				<?php } ?>
				<?php if ($contract_service['option_cd'] == 'faq') { ?>
				<div class="col-md-5">
					<label>CHATBOTにて処理を行ってください</label>
				</div>
				<?php } ?>
			</div>

			<div class="lines-container">
				<label class="basic-label">利用終了日</label>
				<div class="col-md-2" style="width: 150px;">
					<input name="<?php echo $contract_service['option_cd'] ?>_end_date" class="talkappi-datepicker js-date" value="<?php if ($post != NULL)
						echo ($contract_service['end_date']) ?>"
							class="form-control form-control-inline input-small date-picker" data-date-format="yyyy-mm-dd"
							size="16" type="text" />
				</div>
				<div class="col-md-2" style="width: 150px;">
					<input name="<?php echo $contract_service['option_cd'] ?>_end_time" class="talkappi-timepicker js-time"
								value="<?php echo ($contract_service['end_time']? ($contract_service['end_time']) : 0) ?>" />
				</div>
				<div class="col-md-2" style="width: 150px;">
					<button type="button" option-service="<?php echo $contract_service['option_cd'] ?>" class="btn red mr10 js-stop-button" <?php if (isset($contract_service['flg'])) echo ("style='display:none;' readonly"); ?>>停止する</button>
				</div>
			</div>

			<?php if ($grp_bot_id == 0) {?>
			<div style="margin-left:64px;">
			<div class="lines-container">
				<label class="basic-label">グループ施設一括処理</label>
				<div class="js-add-bot-container" style="display:flex;">
					<ul class="btn round light-blue pointer js-selected-bots" style="list-style:none;display:none;"></ul>
					<div class="btn round light-blue pointer"><img src="./../assets/admin/css/img/icon-add.svg">施設選択</div>
				</div>
			</div>
			<div class="lines-container">
				<label class="basic-label">利用開始日</label>
				<div class="col-md-2" style="width: 150px;">
					<input name="<?php echo $contract_service['option_cd'] ?>_start_date_bat" class="form-control form-control-inline input-small date-picker" data-date-format="yyyy-mm-dd" size="16" type="text" />
				</div>
				<div class="col-md-2" style="width: 150px;">
					<input name="<?php echo $contract_service['option_cd'] ?>_start_time_bat" class="talkappi-timepicker js-time" />
				</div>
				<div class="col-md-2" style="width: 150px;">
					<button type="button" option-service="<?php echo $contract_service['option_cd'] ?>" class="btn green mr10 js-init-button js-bat">開通する</button>
				</div>
			</div>
			<div class="lines-container">
				<label class="basic-label">利用終了日</label>
				<div class="col-md-2" style="width: 150px;">
					<input name="<?php echo $contract_service['option_cd'] ?>_end_date_bat" class="form-control form-control-inline input-small date-picker" data-date-format="yyyy-mm-dd" size="16" type="text" />
				</div>
				<div class="col-md-2" style="width: 150px;">
					<input name="<?php echo $contract_service['option_cd'] ?>_end_time_bat" class="talkappi-timepicker js-time" />
				</div>
				<div class="col-md-2" style="width: 150px;">
					<button type="button" option-service="<?php echo $contract_service['option_cd'] ?>" class="btn red mr10 js-stop-button js-bat">停止する</button>
				</div>
			</div>
			</div>		
			<?php }?>
		<?php } ?>
	</div>
	<?php } ?>
</div>
</div>
<!-- END PAGE CONTENT-->