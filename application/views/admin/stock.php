			<!-- BEGIN PAGE HEADER-->
			<!-- BEGIN PAGE HEAD -->
			<div class="page-head">
				<!-- BEGIN PAGE TITLE -->
				<div class="page-title">
					<h1>予約枠設定<small></small></h1>
				</div>
				<!-- END PAGE TITLE -->
			</div>
			<!-- END PAGE HEAD -->
			<!-- END PAGE HEADER-->
			<!-- BEGIN PAGE CONTENT-->
			<div class="row">
				<div class="col-md-12">
			        <!-- Page Content -->
			        <div id="page-wrapper">
			        <div class="portlet light">
						<div class="tabbable-line">
							<ul class="nav nav-tabs ">							
								<li class="<?php if ($_action == 'stocklist') echo('active'); ?>">
									<a href="/admin/stocklist">
									枠定義一覧</a>
								</li>
								<li class="<?php if ($_action == 'stock') echo('active'); ?>">
									<a href="/admin/stock">
									新規枠定義</a>
								</li>
							</ul>
						</div>
								<input type="hidden" name="stock_id" id="stock_id" value="<?php echo($stock_id) ?>">
								<div class="form-group">
									<label class="control-label col-md-3">コード</label>
									<div class="col-md-3">
										<div class="input-group">
											<input name="stock_cd" id="stock_cd"  type="text" class="form-control" style="width:300px;" placeholder="" value="<?php echo($post["stock_cd"])?>">
										</div>
									</div>
								</div>								
								<div class="form-group">
									<label class="control-label col-md-3">名称</label>
									<div class="col-md-3">
										<div class="input-group">
											<input name="stock_name" id="stock_name" type="text" class="form-control" style="width:400px;" placeholder="" value="<?php echo($post["stock_name"])?>">
										</div>
									</div>
								</div>							
								<div class="form-group">
									<label class="control-label col-md-3">枠単位タイプ</label>
									<div class="col-md-3">
										<div class="input-group">
											<span class="input-group-addon">
											<i class="fa fa-filter"></i>
											</span>
												<?php echo Form::select('stock_type_cd', $_codes['18'], $post['stock_type_cd'], array('id'=>'stock_type_cd','class'=>'form-control'))?>
										</div>
									</div>
								</div>
								<div class="form-group">
									<label class="control-label col-md-3">説明</label>
									<div class="col-md-3">
										<div class="input-group">
											<input name="description" type="text" class="form-control" style="width:600px;" placeholder="" value="<?php echo($post["description"])?>">
										</div>
									</div>
								</div>								
							<div class="form-actions">
								<div class="row">
									<div class="col-md-offset-3 col-md-9">
										<button type="button" id="saveButton" class="btn blue">
										<i class="fa fa-save"></i>&nbsp;&nbsp;保存&nbsp;&nbsp;</button>
										<button type="button" onclick="top.location='/admin/stocklist'" class="btn grey-steel">戻る</button>
									</div>
								</div>
							</div>						
					</div>
			        </div>
			        <!-- /#page-wrapper -->					
				</div>
			</div>
			<!-- END PAGE CONTENT-->
