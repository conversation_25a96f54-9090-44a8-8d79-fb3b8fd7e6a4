<div class="row">
	<div class="col-md-12">
		<div id="page-wrapper">
		<div class="portlet light">			        
		<?php echo $reportmenu ?>
			<div class="portlet box">
				<div class="portlet-body">
					<div class="form-body">		
						<div class="form-group">
							<label class="control-label col-md-1" style="padding-left:15px; padding-right:15px;"><?php echo __('admin.common.label.period') ?></label>
							<div class="col-md-4">
								<input name="start_date" id="start_date" value="<?php echo $start_date !== "" ? date('Y-m', strtotime($start_date)) : ""; ?>" style="float:left;" class="form-control form-control-inline input-small date-picker" size="16" data-date-format="yyyy-mm" type="text"/>
								<input name="end_date" id="end_date" value="<?php echo $end_date !== "" ? date('Y-m', strtotime($end_date)) : ""; ?>" style="float:left; margin-left:10px;" class="form-control form-control-inline input-small date-picker" size="16" data-date-format="yyyy-mm" type="text"/>
							</div>
						</div>
						<div class="form-group">
							<label class="control-label col-md-1" style="padding-left:15px; padding-right:15px;"><?php echo __('admin.common.label.conditions') ?></label>
							<div class="col-md-2">
								<?php echo Form::select('lang_cd', $lang, $lang_cd, array('id'=>'lang_cd','class'=>'form-control'))?>
							</div>
							<?php echo $botcond ?>	
							<div class="col-md-1">
								<button type="button" id="searchButton" class="btn yellow" disabled="disabled">
								<i class="fa fa-search mr10"></i><?php echo __('admin.common.button.search') ?></button>
							</div>											
						</div>
					</div>		
					<?php 
					// ヘッダー生成
					echo '<table class="table table-striped table-bordered table-hover js-data-table">';
					echo '<thead>';
					echo '<tr>';
					echo '<th style="border-bottom: 1px solid #ddd;"></th>';
                    echo '<th colspan="' . count(array_keys($monthly_data)) . '" style="text-align: center; border-bottom: 1px solid #ddd;">'. __('admin.report7daily.label.count') .'</th>';
                    echo '</tr>';
					echo '<tr>';
					echo '<th>'. __('admin.report7daily.label.day') .'</th>';
					foreach (array_keys($monthly_data) as $month) {
						echo '<th>' . $month . '</th>';
					}
					echo '</tr>';
					echo '</thead>';

					// ボディ生成
					echo '<tbody>';
					for ($day = 1; $day <= 31; $day++) {
						$day_str = str_pad($day, 2, '0', STR_PAD_LEFT);
						echo '<tr>';
						echo '<td style="text-align: center;">' . $day_str . '日</td>';
						foreach (array_keys($monthly_data) as $month) {
							if (isset($monthly_data[$month][$day_str])) {
								$value = $monthly_data[$month][$day_str];
								echo '<td style="text-align: center;">' . $value;
								if ($value !== 0 && $value !== '-') {
									$date = $month . '-' . $day_str;
									$query_params = ['date' => $date];
									if (isset($lang_cd) && $lang_cd !== "") {
										$query_params['lang_cd'] = $lang_cd;
									}
									if (isset($scene_cd) && $scene_cd !== "") {
										$query_params['scene_cd'] = $scene_cd;
									}
									if (isset($bot_id) && $bot_id !== "") {
										$query_params['child_bot_id'] = $bot_id;
									}
									$query_string = http_build_query($query_params);
									echo '<a href="/admin/report7?' . $query_string . '" style="margin-left: 5px;"><span class="icon-link-2"></span></a>';
								}
								echo '</td>';
							} else {
								echo '<td style="text-align: center;">-</td>';
							}
						}
						echo '</tr>';
					}
					echo '</tbody>';
					echo '</table>';
					?>											
					</div>
			</div>
		</div>
		</div>
	</div>
</div>

