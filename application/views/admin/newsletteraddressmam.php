<link rel="stylesheet" type="text/css" href="/assets/admin/css/newsletter.css"/>

<style>
    .search_bars {
        /* Frame 282 */
        /* Auto layout */
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 10px 24px;
        margin-bottom: 14px;
        gap: 12px;
    }
    .filter_cell_drop_down_width_fixed {
        /* Cell/drop down_width_fixed Copy */
        box-sizing: border-box;
        /* Auto layout */
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: flex-start;
        padding: 5px 8px;

        width: 192px;
        height: 28px;
        /* pure white */
        background: #FFFFFF;
        border: 1px solid #E3E5E8;
        border-radius: 4px;
        /* Inside auto layout */
        flex: none;
        /* Order increasing */
        order: 0;
        flex-grow: 0;
    }
    .button_groups {
        /* Frame 160 */
        /* Auto layout */
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 0px;
        gap: 12px;

        width: 176px;
        height: 18px;
        /* Inside auto layout */
        flex: none;
        order: 0;
        align-self: stretch;
        flex-grow: 0;
    }
    .filter_cell_column_head_name {
        /* テキスト */
        width: 152px;
        height: 18px;

        font-family: 'Hiragino Sans';
        font-style: normal;
        font-weight: 300;
        font-size: 12px;
        line-height: 18px;
        /* identical to box height */
        /* pure black */
        color: #000000;
        /* Inside auto layout */
        flex: none;
        order: 0;
        flex-grow: 1;
    }

    .nav_items {
        /* Frame 1340 */
        /* Auto layout */
        display: flex;
        flex-direction: row;
        align-items: flex-start;
        padding: 0px;
        gap: 2px;

        border-radius: 0;
        /* position: absolute; */
        width: 122px;
        height: 29px;
    }
    .all_member_head {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        /* align-self: stretch; */
        align-items: center;
        padding: 12px 0px 10px 0px;
        height: 28px;
    }
    .txt_table_title {
        font-size: 14px;
        color: #000000;
    }
    
    .icon-export {
        background: url("/assets/admin/css/img/icon-output.svg") no-repeat;
        height: 12px;
        width: 12px;
    }
    .table_all_member {
        /* Frame 1314 */
        /* Auto layout */
        display: flex;
        flex-direction: column;
        /* align-items: flex-start; */
        align-items: stretch;
        align-self: stretch;
        gap: 12px;
        border-radius: 0px 4px 4px 4px;
    }

    .txt_table_action_target {
        width: 113px;
        height: 18px;
        font-size: 12px;
        line-height: 18px;
        color: #000000;
        flex: none;
    }
    ._table-scrollable {
        overflow-x: auto;
        overflow-y: scroll;
    }
    #table1preview_wrapper>.table-scrollable>.dataTables_scroll>.dataTables_scrollHead {
        border-bottom: unset !important;
    }
    #table1preview_wrapper>.table-scrollable>.dataTables_scroll>.dataTables_scrollHead>.dataTables_scrollHeadInner {
        border-bottom: 2px solid #ddd !important;
    }

    #table0list.table>thead>tr>th:first-child, #table0list.table>tbody>tr>td:first-child, 
    #table2list.table>thead>tr>th:first-child, #table2list.table>tbody>tr>td:first-child {
        padding: 2px !important;
        vertical-align: middle;
        text-align: center;
    }
    .row_table_head_content_frame {
        display: flex;
        flex-direction: row;
        align-items: flex-start;
        padding: 0px;

        /* width: 1071px; */
        /* width: 796px;
        width: 806px; */
        width: 100%;
        /* height: 38px; */
        /* Inside auto layout */
        flex: none;
        /* order increasing */
        order: 0;
        align-self: stretch;
        flex-grow: 0;
    }

    .cell_column_head_frame {
        flex-direction: row;
        align-items: center;
        padding: 10px 12px;
        border: 1px solid #E3E5E8;
        flex: none;
        flex-grow: 0;
    }

    .cell_column_diff {
        background: #FFFFFF;
    }

    .cell_column_del_diff {
        opacity: 0.2;
        border: none;
    }

    .cell_op {
        text-align: center;
        width: 40px;
        /* pale grey for line */
        border: 1px solid #E3E5E8;
    }
  
    .row_bottom_table_status_diff{
        width: 1071px;
        height: 32px;
        gap: 71px;
        order: 2;
    }

    .csv_import_float_panel {
        /* CSV入力 */
        /* Auto layout */
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        padding: 24px;
        gap: 12px;

        position: relative;
        width: 854px;
        /* height: 750px; */
        height: 826px;
        /* pure white */
        background: #FFFFFF;
        border-radius: 4px;
    }

    .tag_select_float_panel {
        /* Frame 1332 */
        box-sizing: border-box;
        /* Auto layout */
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        padding: 16px 32px 16px 24px;
        gap: 12px;

        position: relative;
        width: 390px;
        /* height: 146px; */
        /* pure white */
        background: #FFFFFF;
        /* pale grey for line */
        border: 1px solid #E3E5E8;
        /* drop shadow blur */
        box-shadow: 1px 2px 8px rgba(61, 63, 69, 0.24);
        border-radius: 4px;
        background-color: #FFFFFF;
    }
    .tag_select_float_panel.fix_panel {
        position: fixed;
        z-index: 1;
    }

    .tag_input_select_wrapper {
        display:flex;
        flex-direction: column;
    }

    .row_tag_input {
        gap: 2px;
        display:flex;
        flex-direction: row;
    }

    .txt_title_name_upload_label_count_float {
        font-weight: 300;
        font-size: 12px;
        line-height: 18px;
        /* identical to box height */
        /* pure black */
        color: #000000;
        /* smoke grey
        color: #A1A4AA; */
        /* Inside auto layout */
        flex: none;
        order: 0;
        flex-grow: 0;
    }

    .input_file_upload {
        /* input item for system */
        box-sizing: border-box;
        /* Auto layout */
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 5px 8px 5px 12px;
        gap: 10px;

        /* width: 806px; */
        /* width: 100%; */
        height: 28px;
        /* pure white */
        background: #FFFFFF;
        /* pale grey for line */
        border: 1px solid #E3E5E8;
        border-radius: 4px;
        /* Inside auto layout */
        flex: none;
        order: 1;
        align-self: stretch;
        flex-grow: 0;
    }
    .icon-tick {
        background: url("/assets/admin/css/img/icon-tick.svg") no-repeat;
        height: 12px;
        width: 12px;
    }

    /* td.pin>i{
        background: url("/assets/admin/css/img/icon-pin-grey.svg") no-repeat;
        height: 12px;
        width: 12px;
    } */
    /* td>.icon-pin:before { */
    td>i:before {
        color: #C8CACE;
    }
    /* tr.selected>td>.icon-pin:before { */
    tr.selected>td>i:before {
        color: #245BD6;
    }

    .icon-pin-grey {
        background: url("/assets/admin/css/img/icon-pin-grey.svg") no-repeat;
        height: 12px;
        width: 12px;
    }

    .btn_label_diff {
        flex-direction: column;
        padding: 3px 12px;
        /* pale grey */
        background: #EBEDF2;
        cursor: default;
    }

    .csv-table-title {
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 0px;
        gap: 14px;

        /* width: 806px; */
        height: 28px;
        /* Inside auto layout */
        flex: none;
        /* order: 1; */
        align-self: stretch;
        flex-grow: 0;
    }
    .txt_btn_large_highlight {
        /* 保存 */
        /* width: 70px; */
        height: 21px;

        font-family: 'Hiragino Sans';
        font-style: normal;
        font-weight: 500;
        font-size: 14px;
        line-height: 21px;
        /* identical to box height */
        text-align: center;
        /* pure white */
        color: #FFFFFF;
        /* Inside auto layout */
        flex: none;
        order: 0;
        flex-grow: 0;
    }
    .txt_btn_large {
        width: 70px;
        height: 21px;
        font-weight: 400;
        font-size: 14px;
        text-align: center;
        color: #3D3F45;
    }

    .input_tags {
        padding-left: 3px;
        overflow: auto;
        position: relative;
    }

    #tabContentContainerMembers .btn_tag_w_ico.cancel {
        z-index: 2;
    }

    .row_tags_list {
        display: flex;
        flex-direction: column;
        gap: 12px;
        overflow-y: auto;
        max-height: 160px;
    }

    .talkappi-pulldown.js-import>.talkappi-dropdown-container {
        border: 1px solid #C8CACE;
    }
    .talkappi-pulldown.js-import>.talkappi-dropdown-container>.talkappi-dropdown-selected>.image-action-group {
        background: url(../../assets/admin/css/img/icon-form-dropdown.svg) no-repeat center right;
        margin-right: 4px;
    }
    .talkappi-pulldown.js-import>.talkappi-dropdown-container>.talkappi-dropdown-selected>.image-action-group>img {
        background: unset;
        margin-right: 4px;
    }
</style>

<input type="hidden" name="current_bot_id" id="current_bot_id" value='<?php echo ($bot_id) ?>'>
<input type="hidden" name="current_tab" id="current_tab" value="<?php echo ($tab) ?>">
<input type="hidden" name="project_id" id="project_id" value="<?php if ( isset($project_id) && $project_id != null ) echo $project_id ?>" />

<!-- hidden/float panel BEGIN -->
<div class="modal fade" id="boxCSVImport" tabindex="-1" role="basic" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="csv_import_float_panel">
                <div id="import_progress" class="import_progress"></div>
                <input type="hidden" name="import_no" id="import_no" value="<?php if ( isset($post['import_no']) && $post['import_no'] != null ) echo $post['import_no'] ?>">
                <div class="csv-modal-title stretch" data-dismiss="modal" aria-hidden="true">
                    <div class="txt_title_float_panel"><?php echo __('admin.send.addr.title.import') ?></div>
					<img src="/assets/common/images/Icon-cancel_large.svg" class="closeImg" style="float:right;cursor:pointer;">
				</div>
                <div class="container_item_in_float_panel_frame">
                    <div class="txt_title_name_upload_label_count_float"><?php echo __('admin.send.addr.label.csv_upload') ?></div>
                    <div id="upload_csv" class="talkappi-upload js-upload" data-name="csv_file" 
                        data-label="<?php echo __('admin.send.addr.placeholder.csv_upload') ?>" data-type="csv" data-size="100">
                    </div>
				</div>
                <!-- TODO: 未実装の機能は非表示にする: CSVから入力:インポート管理名称 -->
                <!-- <div class="container_item_in_float_panel_frame">
                    <div class="txt_title_name_upload_label_count_float"><?php echo __('admin.send.addr.label.import_mgmt_name') ?></div>
                    <div class="csv-table-title" style="width: 500px;">
                        <input type="text" name="import_mgmt_name" value="" class="form-control talkappi-textinput" data-max-input="200" placeholder="<?php echo __('admin.send.addr.placeholder.import_mgmt_name') ?>" />
                    </div>
				</div> -->
                <!-- TODO: 案件設定:案件管理名称 -->
                <?php if ( $is_newsletter_project_enabled && isset($project_id) && $project_id != null && is_numeric($project_id)): ?>
                <div class="container_item_in_float_panel_frame">
                    <div class="txt_title_name_upload_label_count_float"><?php echo __('admin.newsletterproject.label.project_setting') ?></div>
                    <div class="csv-table-title" style="width: 500px;">
                        <div class="txt_title_name_upload_label_count_float">                        
                            <div project_id="<?php echo $project_id?>" class="btn_label_w_ico btn_label_diff"><?php echo $project_name?></div>
                        </div>
                    </div>
				</div>
                <?php endif; ?>
                <div class="container_item_in_float_panel_frame" style="height: 48px;">
                    <div class="txt_title_name_upload_label_count_float"><?php echo __('admin.send.addr.label.import_destination') ?></div>
                    <?php 
                        if( ! $is_parent_bot ) {
                            echo 
                    '<div id="target_bots_selected" data-selected_bots="'.$bot_id.'" class="csv-table-title">'.
                        '<div bot_id="'.$bot_id.'" class="btn_label_w_ico btn_label_diff">'.
                            '<div class="txt_title_name_upload_label_count_float">'.$bot_name.'</div>'.
                        '</div>';
                        } else {
                            echo 
                    '<div id="target_bots_selected" class="csv-table-title">'.
                        '<div id="target_bot_select" class="btn_label_w_ico">'.
                            '<span class="icon-add"></span>'.
                            '<div class="txt_title_name_upload_label_count_float">'.__('admin.send.addr.button.add_bots').'</div>'.
                        '</div>';
                        }
                    ?>
                    </div>
				</div>

                <!-- TODO: 未実装の機能は非表示にする: CSVから入力:下記のユーザーにタグを一括付き -->
                <!-- <div class="container_item_in_float_panel_frame" style="display: none;">
                    <div class="txt_title_name_upload_label_count_float">タグの一括付与</div>
                    <div class="csv-table-title">
                        <div class="input_file_upload" style="width: 692px;order: 0;">
                            <div class="txt_title_name_upload_label_count_float" style="color: #A1A4AA;flex-grow: 1;">タグを選択、または入力して追加</div>
                        </div>
                        <div id="btn_tag_append" class="btn_w_ico pointer tag vivid disabled">
                            <i></i>
                            <div class="txt_btn_w_ico"><?php echo __('admin.send.addr.button.new_tag') ?></div>
                        </div>
                    </div>
				</div> -->
                <div class="container_item_in_float_panel_frame" style="height: 366px;flex-grow: 1;">
                    <div class="csv-table-title" style="gap: 6px;height: 18px;order: 0;">
                        <div id="preview_member_count" class="txt_title_name_upload_label_count_float"><?php echo str_replace( "{target_count}", ' <span class="selected_count">0</span> ', __('admin.send.task.count.context') ) ?></div>
                    </div>
                    <!-- container_item_in_float_panel_frame -->
                    <table id="table1preview" class="table table-striped table-bordered table-hover js-data-table1 ">
                        <!-- <div class="row_table_head_content_frame" style="width: 806px;"> -->
                            <!--  style="width: 370px;"  style="width: 24px;"-->

                        <thead>
                            <tr class="">
                                <th>
                                    <?php echo __('admin.common.label.mail.address') ?>
                                </th>
                                <th>
                                    <?php echo __('admin.common.label.name.human') ?>
                                </th>
                                <th>
                                    <?php echo __('admin.common.button.exclude') ?>
                                </th>
                            </tr>
                        </thead>
                            <tbody id="preview_members">
                            <!--  class="" style="width: 100%;height: 304px;order: 0;align-self: auto;flex-grow: 1;" -->
                                <!-- preview sample rows 
                                preview data rows loop
                                Cell contents read from data
                                PHP loop echo BEGIN
                                row_table_head_content_frame
                                cell_column_head_frame
                                txt_column_head_content -->
                                <?php
                                    foreach($preview_members as $member) {
                                        echo('
                                <tr>
                                    <td style="width: 370px;">
                                        <div class="">'.$member['email'].'</div>
                                    </td>
                                    <td style="width: 370px;">
                                        <div class="">'.$member['name'].'</div>
                                    </td>
                                    <td>
                                        <span class="icon-delete"></span>
                                    </td>            
                                </tr>');                    
                                    }
                                ?>
                                <!-- PHP loop echo END -->
                            </tbody>

                        <!-- </tbody> -->
                    </table>
                   
				</div>
                <div class="group_btn_large_general_frame">
                    <div id = "btnApply" class="btn_large_blue"><div class="txt_btn_large_highlight"><?php echo __('admin.common.label.add') ?></div></div>
                    <div id = "btnCancel" class="btn_large_white"><div class="txt_btn_large"><?php echo __('admin.common.button.cancel') ?></div></div>
                    <div id = "btnEnd" class="btn_large_blue" style="display:none;"><div class="txt_btn_large_highlight"><?php echo __('admin.send.addr.button.import_completed') ?></div></div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="boxTagAttach" tabindex="-1" role="basic" aria-hidden="true">
	<div class="modal-dialog">
		<div id="tabContentContainerMembers" class="modal-content modal_content_medium_float_panel" style="width: 650px;">
            <div class="csv-modal-title" data-dismiss="modal" aria-hidden="true">
                <div class="txt_title_float_panel"><?php echo __('admin.send.addr.button.attach_tag') ?></div>
                <img src="/assets/common/images/Icon-cancel_large.svg" class="closeImg" style="float:right;cursor:pointer;">
            </div>
            <div class="container_item_in_float_panel_frame">
                <div class="csv-table-title" style="height: 30px;">
                    <!-- <div class="input_file_upload"> -->
                        <!-- <div class="txt_title_name_upload_label_count_float" style="color: #A1A4AA;flex-grow: 1;">タグを入力して新規追加</div> -->
                        <div class="csv-table-title input_tags">
                            <!--attach candidate tags to be inserted here -->
                            <input id="iAttachTag" class="form-control borderless"/>
                        </div>
                        <div id="btnPreviewAttach" class="btn_w_ico pointer tag vivid disabled">
                            <i></i><?php echo __('admin.send.addr.button.attach_tag') ?>
                        </div>
                    <!-- </div> -->
                </div>
                <!-- tags list -->
                <div id="tag_select_box" class="modal-content tag_select_float_panel fix_panel">
                    <div class="txt_title_name_upload_label_count_float">タグを選択、または入力して追加</div>
                    <div class="row_tags_list"></div>
                </div>
            </div>
            <!-- preview datatable -->
            <div class="container_item_in_float_panel_frame" style="height:auto">
                <div class="txt_title_name_upload_label_count_float"><?php echo __('admin.send.addr.label.target_user') ?></div>
                <table id="table3preview" class="table table-striped table-bordered table-hover js-data-table3">
                    <thead>
                        <tr class="row_table_head_content_frame">
                            <th>
                                <?php echo __('admin.common.label.mail.address') ?>
                            </th>
                            <th>
                                <?php echo __('admin.common.label.name.human') ?>
                            </th>
                            <th>
                                <?php echo __('admin.send.addr.th.tag') ?>
                            </th>
                            <th>
                                <?php echo __('admin.common.button.exclude') ?>
                            </th>
                        </tr>
                    </thead>
                    <tbody>

                    </tbody>
                </table>
            </div>
            <div class="group_btn_large_general_frame">
                <div id = "btnApplyAttach" class="btn_large_blue"><div class="txt_btn_large_highlight">OK</div></div>
                <div id = "btnCancelAttach" class="btn_large_white"><div class="txt_btn_large"><?php echo __('admin.common.button.cancel') ?></div></div>
            </div>
        </div>
    </div>
</div>


<div class="modal fade" id="boxTagDetach" tabindex="-1" role="basic" aria-hidden="true">
	<div class="modal-dialog">
		<div id="tabContentContainerMembers" class="modal-content modal_content_medium_float_panel" style="width: 650px;">
            <div class="csv-modal-title" data-dismiss="modal" aria-hidden="true">
                <div class="txt_title_float_panel"><?php echo __('admin.send.addr.button.clear_tag') ?></div>
                <img src="/assets/common/images/Icon-cancel_large.svg" class="closeImg" style="float:right;cursor:pointer;">
            </div>
            <div class="container_item_in_float_panel_frame">
                <div class="txt_title_name_upload_label_count_float"><?php echo str_replace( "{target_count}", '<span class="selected_count">0</span>', __('admin.send.addr.count.context') ).__('admin.send.addr.button.clear_tag') ?></div>
                <div class="csv-table-title" style="height: 30px;">
                    <div class="csv-table-title input_tags" style="width:461px;height: 30px;flex-wrap: wrap;gap: 3px;" >
                        <!-- id=detach_tag---- detach candidate tags to be inserted here --style="color: #A1A4AA;" -->
                        <input id="iDetachTag" class="form-control borderless" />
                    </div>
                    <div id="btnPreviewDetach" class="btn_w_ico pointer tag vivid red disabled">
                        <i></i><?php echo __('admin.common.button.batch_clear') ?>
                    </div>
                </div>
                <div id="tag_select_box" class="modal-content tag_select_float_panel fix_panel">
                    <div class="txt_title_name_upload_label_count_float">タグを選択</div>
                    <div class="row_tags_list"></div>
                </div>
            </div>

            <!-- preview datatable -->
            <div class="container_item_in_float_panel_frame" style="height: 353px;">
                <div class="txt_title_name_upload_label_count_float"><?php echo __('admin.send.addr.label.target_user') ?></div>
                <table id="table3preview" class="table table-striped table-bordered table-hover js-data-table3">
                    <thead>
                        <tr class="row_table_head_content_frame">
                            <th>
                                <?php echo __('admin.common.label.mail.address') ?>
                            </th>
                            <th>
                                <?php echo __('admin.common.label.name.human') ?>
                            </th>
                            <th>
                                <?php echo __('admin.send.addr.th.tag') ?>
                            </th>
                            <th>
                                <?php echo __('admin.common.button.exclude') ?>
                            </th>
                        </tr>
                    </thead>
                    <tbody>

                    </tbody>
                </table>
            </div>
            <!-- preview datatable end-->

            <div class="group_btn_large_general_frame">
                <div id = "btnApplyDetach" class="btn_large_blue"><div class="txt_btn_large_highlight">OK</div></div>
                <div id = "btnCancelDetach" class="btn_large_white"><div class="txt_btn_large"><?php echo __('admin.common.button.cancel') ?></div></div>
            </div>
        </div>
    </div>
</div>

<script>
const _sub_bots = <?php echo json_encode($sub_bots, JSON_UNESCAPED_UNICODE) ?>;
const _projects_json = <?php echo json_encode($projects_json, JSON_UNESCAPED_UNICODE) ?>;
</script>

<!-- hidden/float panel END -->

<div class="search_bars content-container light-gray">    
    <!-- <div class="filter_cell_drop_down_width_fixed">
        <div class="button_groups">
            <div class="filter_cell_column_head_name">すべてのユーザー</div>
            <span class="drop-down-close-icon"></span>
        </div>
    </div> -->
    <?php
    if (count($bots) > 1) {
        echo Form::select('bot_id', $bots, $bot_id, array('id'=>'filter_bot_id','class'=>'form-control', 'style' => 'background: #FFFFFF;'));
    }
    ?>
    <!-- 案件 -->
    <?php if ($is_newsletter_project_enabled): ?>
        <div class="pulldown">
            <div class="talkappi-pulldown js-project" style="width: 240px" data-name="project" data-value="<?php echo isset($project_id) ? $project_id : '0'; ?>" data-source='<?php echo $projects_json; ?>'></div>
        </div>
    <?php endif; ?>

    <!-- <label class="control-label col-md-8 label-fix-3">登録日付：全期間</label> -->
    <div id="filter_date_range" class="talkappi-datepicker-range" data-value='<?php echo json_encode($filter_date_range) ?>'>
    </div>
    <!-- TODO: 未実装の機能は非表示にする: タグ絞り込み -->
    <!-- <div class="filter_cell_drop_down_width_fixed" style="order: 2;">
        <div class="button_groups">
            <div class="filter_cell_column_head_name">タグなし</div>
            <span class="drop-down-close-icon"></span>
        </div>
    </div> -->
    <!-- <div class="filter_cell_drop_down_width_fixed" style="background: #F6F7F9;order: 3;">
        <div class="button_groups">
            <div class="filter_cell_column_head_name">-</div>
            <span class="drop-down-close-icon"></span>
        </div>
    </div> -->
    <div id="btn_combo_filter" class="btn_w_ico pointer">
        <span class="icon-filter"></span>
        <div class="txt_btn_w_ico"><?php echo __('admin.common.label.narrowdown') ?></div>
    </div>
</div>

<div class="nav_items content-container">
    <div id="0" class="tab <?php if ($tab != '0') echo 'inactive';?>" data-target="tabContentContainerMembers">
        <div class="tab_bar_text">
            <div class="txt_tab"><?php echo __('admin.send.addr.tab.label.addr')?></div>
        </div>
        <div class="tab-bar-shadow"></div>
    </div>
    <div id="1" class="tab <?php if ($tab != '1') echo 'inactive';?>" data-target="tabContentContainerTags">
        <div class="tab_bar_text">
            <div class="txt_tab"><?php echo __('admin.send.addr.tab.label.tag')?></div>
        </div>
        <div class="tab-bar-shadow"></div>
    </div>
    <div id="2" class="tab <?php if ($tab != '2') echo 'inactive';?>" data-target="tabContentContainerExtends">
        <div class="tab_bar_text">
            <div class="txt_tab"><?php echo __('admin.send.addr.tab.label.extend')?></div>
        </div>
        <div class="tab-bar-shadow"></div>
    </div>
</div>

<div id="tabContentContainerMembers" class="table_all_member content-container white" <?php if ($tab != '0') echo 'style="display:none;"';?>>
        <div class="all_member_head">
            <div class="txt_table_title"></div>
            <div class="button_container">
                <div id="btnCSVDownload" style="height: 100%;display: flex;align-items: center;">
                    <a href="<?php echo $sample_csv_download_url ?>"><?php echo __('admin.common.label.csv_sample') ?></a>
                </div>
                <div class="talkappi-pulldown js-import" data-type="menu" data-action="<?php echo __('admin.send.addr.button.import_members') ?>" style="width:160px;" data-value="" data-source='<?php echo json_encode($import_buttons_source, JSON_UNESCAPED_UNICODE) ?>'></div>
                <!-- 連絡先を導出CSV -->
                <button type="button" id="csvexport" class="btn-smaller btn-white" style="background-color: #EBEDF2; border: none; margin: 0;">
                    <span class="icon-export"></span>
                    <?php echo __('admin.common.button.csv_export'); ?>
                </button>
            </div>
        </div>

        <table id="table0list" class="table table-striped table-bordered table-hover js-data-table0">
            <div class="table_action_container  custom-data-table-header">
                <div class="txt_table_action_target"><?php echo str_replace( "{target_count}", '<span class="selected_count">0</span>', __('admin.send.addr.count.context') ) ?></div>
                <div id="btnTagAttachShowPanel" data-scope="all" class="btn_w_ico pointer tag pale_grey disabled">
                    <i></i><?php echo __('admin.send.addr.button.attach_tag') ?>
                </div>
                <div id="btnTagDetachShowPanel" class="btn_w_ico pointer delete pale_grey disabled">
                    <i></i><?php echo __('admin.send.addr.button.clear_tag') ?>
                </div>
                <div id="btn_addr_del" class="btn_w_ico pointer delete pale_grey disabled">
                    <i></i><?php echo __('admin.send.addr.button.delete') ?>
                </div>
            </div>

            <thead>
                <!--  class="row_table_head_content_frame" -->
                <tr>
                    <th style="width: 15px;"> 
                        <div class="js-check-container" style="padding:0 0 0 5px;">
                            <span class="icon-check">
                        </div>
                    </th>
                    <th class="cell_column_head_frame">
                        <div class="txt_column_head_content"><?php echo __('admin.common.label.mail.address') ?></div>
                    </th>
                    <!--  style="width: 180px;" -->
                    <th class="cell_column_head_frame">
                        <div class="txt_column_head_content"><?php echo __('admin.common.label.name.human') ?></div>
                    </th>

                    <?php 
                    //  extended columns
                        if ( isset($members_extend_columns) && $members_extend_columns ) {
                            foreach($members_extend_columns as $extend_no=>$extend_name) {
                                $th_attrs = ['class'=>'cell_column_head_frame extend', 'extend_no'=>$extend_no];
                                echo('
                    <th '.HTML::attributes($th_attrs).'>
                        <div class="txt_column_head_content">'.$extend_name.'</div>
                    </th>
                                ');
                            }
                        }
                    ?>

                    <th id="regist_time" 
                        <?php if ( isset($order_ico_colors['regist_time']['order']) ) {
                            echo('order="'.$order_ico_colors['regist_time']['order'].'"');
                            }
                        ?> 
                    class="cell_column_head_frame pointer">
                        <div class="txt_column_head_content"><?php echo __('admin.send.addr.th.regist_date') ?></div>
                    </th>
                    <!--  style="width: 387px;" -->
                    <th class="cell_column_head_frame">
                        <div class="txt_column_head_content"><?php echo __('admin.send.addr.th.tag') ?></div>
                    </th>
                </tr>
            </thead>

            <tbody>
                <!-- Cell contents read from data -->
                <!-- PHP loop echo BEGIN -->
                <?php
                    $tr_attrs = [];
                    if ( $is_newsletter_project_enabled && isset($project_id) && $project_id != null && is_numeric($project_id) ) $tr_attrs['class'] = 'project';
                    foreach($members as $member) {
                        $tr_attrs['member_id'] = $member->mail_member_id;
                        $tags_html = '';
                        if ( isset($member_tag_mapping[$member->mail_member_id]) && count($member_tag_mapping[$member->mail_member_id]) > 0 ) {
                            $tags_html = '';
                            foreach ( $member_tag_mapping[$member->mail_member_id] as $tag_no ) {
                                $tags_html .= '
                        <span tag_no="'.$tag_no.'" class="btn_tag_w_ico cancel">
                            <span class="txt_column_head_content">'.$tags_all[$tag_no]->tag_name.'</span>
                            <span class="icon-cancel pointer"></span>
                        </span>';
                            }
                        }

                        // $extend_attributes = [];
                        $extend_attributes_html = '';
                        //  extended attributes
                        if ( isset($members_extend_columns) && $members_extend_columns ) {
                            foreach($members_extend_columns as $extend_no=>$extend_name) {
                                // $set = false;
                                $attribute = '';
                                if( isset($members_extends[$member->mail_member_id])) {
                                    $members_extend_data = $members_extends[$member->mail_member_id];
                                    if( isset($members_extend_data[$extend_no]) ) {
                                        // $extend_attributes []= $members_extend_data[$extend_no];
                                        $attribute = $members_extend_data[$extend_no];
                                        // echo('Attr'.$attribute); 
                                        // $set = true;
                                    }
                                    else {
                                        // $attribute = 'NoAttr'.$extend_no; 
                                    }
                                }
                                else {
                                    // $attribute = 'NoExtend'.$member->mail_member_id; 
                                }
                                // if( !$set ) {
                                //     $extend_attributes []= '';
                                // }

                                $extend_attributes_html .= 
                            '<td class="cell_column_head_frame extend">
                                <div class="txt_column_head_content">'.$attribute.'</div>
                            </td>';
                            }
                        }

                        $member_html = '
                        <tr'.HTML::attributes($tr_attrs).'>
                            <td style="width: 15px;">
                                <div class="js-check-container" style="padding:0 0 0 5px;">
                                    <span class="icon-check">
                                </div>
                            </td>
                            <td class="cell_column_head_frame email">
                                <div class="txt_column_head_content">'.$member->email.'</div>
                            </td>
                            <td class="cell_column_head_frame name">
                                <div class="txt_column_head_content">'.$member->first_name.$member->last_name.'</div>
                            </td>';                 
                        
                        $member_html .= $extend_attributes_html;

                        $member_html .= 
                            '<td class="cell_column_head_frame">
                                <div class="txt_column_head_content">'.date('Y/m/d', strtotime($member->regist_time)).'</div>   
                            </td>
                            <td class="cell_column_head_frame tags" style="display:flex;gap: 8px;overflow-x: auto;">
                                '.$tags_html.'
                                <div class="btn_tag_w_ico pointer">
                                    <span class="icon-add"></span>
                                    <div class="txt_column_head_content">'.__('admin.send.addr.button.tag').'</div>
                                </div>
                                <div class="tag_input_select_wrapper" style="display:none;">
                                    <div class="row_tag_input">
                                        <input id="attach_tag" placeholder="'. __('admin.send.addr.input.hint.new_tag').'" class="form-control" />
                                        <span class="btn_extra_small_blue pointer">'. __('admin.common.button.add').'</span>
                                    </div>
                                    <div id="tag_select_box" class="modal-content tag_select_float_panel">
                                        <div class="txt_title_name_upload_label_count_float">タグを選択、または入力して追加</div>
                                    </div>
                                </div>
                            </td>               
                        </tr>';

                        echo($member_html);                    
                    }
                ?>
                <!-- PHP loop echo END -->
            </tbody>

        </table>
        
    <!-- </div> -->
</div>

<!-- 2ND hidden/float panel BEGIN -->

<div class="modal fade" id="boxTagNew" tabindex="-1" role="basic" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content modal_content_medium_float_panel">
                <div class="csv-modal-title" data-dismiss="modal" aria-hidden="true">
                    <div class="txt_title_float_panel"><?php echo __('admin.send.addr.title.new_tag') ?></div>
					<img src="/assets/common/images/Icon-cancel_large.svg" class="closeImg" style="float:right;cursor:pointer;">
				</div>
                <div class="container_item_in_float_panel_frame">
                    <div class="txt_title_name_upload_label_count_float"><?php echo __('admin.send.addr.label.new_tag') ?></div>
                    <div class="csv-table-title">
                        <!-- <div class="input_file_upload"> -->
                            <!-- <div class="txt_title_name_upload_label_count_float" style="color: #A1A4AA;flex-grow: 1;">タグを入力して新規追加</div> -->
                            <input id="iNewTag" placeholder="<?php echo __('admin.send.addr.input.hint.new_tag') ?>" class="form-control" />
                        <!-- </div> -->
                    </div>
				</div>
                <div class="group_btn_large_general_frame">
                    <div id = "btnApplyNew" class="btn_large_blue"><div class="txt_btn_large_highlight">OK</div></div>
                    <div id = "btnCancelNew" class="btn_large_white"><div class="txt_btn_large"><?php echo __('admin.common.button.cancel') ?></div></div>
                </div>
        </div>
    </div>
</div>

<div class="modal fade" id="boxTagMerge" tabindex="-1" role="basic" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content modal_content_medium_float_panel">
                <div class="csv-modal-title" data-dismiss="modal" aria-hidden="true">
                    <div class="txt_title_float_panel"><?php echo __('admin.send.addr.title.merge_tag') ?></div>
					<img src="/assets/common/images/Icon-cancel_large.svg" class="closeImg" style="float:right;cursor:pointer;">
				</div>
                <div class="container_item_in_float_panel_frame">
                    <div class="txt_column_head_content"><?php echo __('admin.send.addr.desc.merge_tag') ?></div>
                    <div id="source_tags" class="row_filter_tags_frame_965 rows_flex"></div>
                </div>
                <div class="container_item_in_float_panel_frame">
                    <div class="csv-table-title">
                        <input id="iTargetTag" placeholder="<?php echo __('admin.send.addr.input.hint.merge_tag') ?>" class="form-control" />
                    </div>
				</div>
                <div class="group_btn_large_general_frame">
                    <div id = "btnApplyMerge" class="btn_large_blue"><div class="txt_btn_large_highlight"><?php echo __('admin.common.button.merge') ?></div></div>
                    <div id = "btnCancelMerge" class="btn_large_white"><div class="txt_btn_large"><?php echo __('admin.common.button.cancel') ?></div></div>
                </div>
        </div>
    </div>
</div>

<div class="modal fade" id="boxTagEdit" tabindex="-1" role="basic" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content modal_content_medium_float_panel">
            <div class="csv-modal-title" data-dismiss="modal" aria-hidden="true">
                <div class="txt_title_float_panel"><?php echo __('admin.send.addr.title.edit_tag') ?></div>
                <img src="/assets/common/images/Icon-cancel_large.svg" class="closeImg" style="float:right;cursor:pointer;">
            </div>
            <div class="container_item_in_float_panel_frame">
                <div class="txt_title_name_upload_label_count_float"><?php echo __('admin.send.addr.label.edit_tag') ?></div>
                <div class="csv-table-title">
                    <input id="edit_tag" placeholder="<?php echo __('admin.send.addr.input.hint.edit_tag') ?>" class="form-control" />
                </div>
            </div>
            <div class="group_btn_large_general_frame">
                <div id = "btnApplyEditTag" class="btn_large_blue"><div class="txt_btn_large_highlight">OK</div></div>
                <div id = "btnCancelEditTag" class="btn_large_white"><div class="txt_btn_large"><?php echo __('admin.common.button.cancel') ?></div></div>
            </div>
        </div>
    </div>
</div>
<!-- 2ND hidden/float panel END -->

<div id="tabContentContainerTags" class="table_all_member content-container white" <?php if ($tab != '1') echo 'style="display:none;"';?>>
    <div class="all_member_head">
        <div class="txt_table_title"></div>
        <div class="button_container">
            <div class="btn_w_ico pointer"  id="btnTagNew" style="width: 103px;order: 0;">
                <span class="icon-add-white"></span>
                <div class="txt_btn_w_ico"><?php echo __('admin.send.addr.button.new_tag') ?></div>
            </div>
        </div>
    </div>

    <table id="table2list" class="table table-striped table-bordered table-hover js-data-table2">
            <div class="table_action_container  custom-data-table-header">
                <div class="txt_table_action_target"><?php echo str_replace( "{target_count}", '<span class="selected_count">0</span>', __('admin.send.tag.count.context') ) ?></div>
                <div id="btnTagMerge" class="btn_small_white pointer disabled" data-min-target='2'>
                    <?php echo __('admin.common.button.merge') ?>
                </div>
            </div>

        <thead>
            <tr>
                <th style="width: 15px;"> 
                    <div class="js-check-container" style="padding:0 0 0 5px;">
                        <span class="icon-check">
                    </div>
                </th>
                <th>
                    <div class="txt_column_head_content"><?php echo __('admin.send.addr.th.tag_no') ?></div>
                </th>
                <th>
                    <div class="txt_column_head_content"><?php echo __('admin.send.addr.th.tag_name') ?></div>
                </th>
                <th>
                    <div class="txt_column_head_content"><?php echo __('admin.send.addr.th.last_update') ?></div>
                </th>
                <th>
                    <div class="txt_column_head_content"><?php echo __('admin.send.addr.th.user_count') ?></div>
                </th>
                <th>
                    <div class="txt_column_head_content"><?php echo __('admin.send.addr.th.user_detail') ?></div>
                </th>
                <th>
                    <div class="txt_column_head_content"><?php echo __('admin.common.label.operation') ?></div>
                </th>
            </tr>
        </thead>
        <tbody>
            <!-- Cell contents read from data -->
            <!-- PHP loop echo BEGIN -->
            <?php
                $tr_attrs = [];
                if ( $is_newsletter_project_enabled &&  isset($project_id) && $project_id != null && $project_id != 'other' ) $tr_attrs['class'] = 'project';
                foreach($tags as $tag) {
                    $tr_attrs['tag_no'] = $tag->tag_no;
                    $tag_count = 0;
                    if ( isset($member_tag_counts[$tag->tag_no])  ) {
                        $tag_count = $member_tag_counts[$tag->tag_no];
                    }
                    $user_detail_html = '';
                    if ($tag_count > 0) {
                        $href = '/'.$_path.'/newsletteraddressmam?';
                        $user_list_query = [];
                        if ( $is_newsletter_project_enabled &&  isset($project_id) && $project_id != null && $project_id != 'other' ) $user_list_query['proj'] = $project_id;
                        $user_list_query['tag_no'] = $tag->tag_no;
                        $href .= http_build_query($user_list_query);
                        $user_detail_html = HTML::anchor($href, __('admin.send.addr.td.user_list'), ['class' => 'link-animate']);
                    }

                    echo('
            <tr'.HTML::attributes($tr_attrs).'>
                <td style="width: 15px;">
                    <div class="js-check-container" style="padding:0 0 0 5px;">
                        <span class="icon-check">
                    </div>
                </td>
                <td class="cell_column_head_frame">
                    <div class="txt_column_head_content">'.$tag->tag_no.'</div>
                </td>
                <td class="cell_column_head_frame">
                    <div class="txt_column_head_content tag_name" style="display: flex;">
                        <div class="tag_basic txt_column_head_content inline" style="align-items: center;display: flex;">'.$tag->tag_name.'</div>
                    </div>
                </td>
                <td class="cell_column_head_frame">
                    <div class="txt_column_head_content">'.date('Y/m/d', strtotime($tag->upd_time)).'</div>   
                </td>
                <td class="cell_column_head_frame">
                    <div class="txt_column_head_content">'.$tag_count.'</div>
                </td>
                <td class="cell_column_head_frame" style="color: #245BD6;">'.$user_detail_html.'</td>'
                .
                '<td>
                    <span>
                        <div class="btn round image edit" style="margin-bottom: 4px;">'.__('admin.common.button.edit').'</div>
                    </span>
                    <span>
                        <div class="btn round image delete" style="margin-bottom: 4px;">'.__('admin.common.button.delete').'</div>
                    </span>
                </td>     
            </tr>');                    
                }
            ?>
            <!-- PHP loop echo END -->
        </tbody>
    </table>
</div>

<!-- 3RD hidden/float panel BEGIN -->

<div class="modal fade" id="boxExtendNew" tabindex="-1" role="basic" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content modal_content_medium_float_panel">
                <div class="csv-modal-title" data-dismiss="modal" aria-hidden="true">
                    <div class="txt_title_float_panel"><?php echo str_replace('{extend_name}', $extend_alias_next, __('admin.send.addr.title.new_extend')) ?></div>
					<img src="/assets/common/images/Icon-cancel_large.svg" class="closeImg" style="float:right;cursor:pointer;">
				</div>
                <div class="container_item_in_float_panel_frame">
                    <div class="csv-table-title">
                        <input id="iExtend" data-extend_next="<?php echo($extend_no_next); ?>" value="<?php echo($extend_alias_next); ?>" placeholder="<?php echo __('admin.send.addr.input.hint.edit_extend') ?>" class="form-control" />
                    </div>
				</div>
                <div class="group_btn_large_general_frame">
                    <div id = "btnApplyNew" class="btn_large_blue"><div class="txt_btn_large_highlight">OK</div></div>
                    <div id = "btnCancelNew" class="btn_large_white"><div class="txt_btn_large"><?php echo __('admin.common.button.cancel') ?></div></div>
                </div>
        </div>
    </div>
</div>
<div class="modal fade" id="boxEditExtend" tabindex="-1" role="basic" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content modal_content_medium_float_panel">
            <div class="csv-modal-title" data-dismiss="modal" aria-hidden="true">
                <div class="txt_title_float_panel"><?php echo __('admin.send.addr.title.edit_extend') ?></div>
                <img src="/assets/common/images/Icon-cancel_large.svg" class="closeImg" style="float:right;cursor:pointer;">
            </div>
            <div class="container_item_in_float_panel_frame">
                <div class="csv-table-title">
                    <input id="iEditExtend" data-sample-suffix="<?php echo __('admin.send.addr.th.alias') ?>" placeholder="<?php echo __('admin.send.addr.input.hint.edit_label') ?>" class="form-control" />
                </div>
            </div>
            <div class="group_btn_large_general_frame">
                <div id = "btnApplyEditExtend" class="btn_large_blue"><div class="txt_btn_large_highlight">OK</div></div>
                <div id = "btnCancelEditExtend" class="btn_large_white"><div class="txt_btn_large"><?php echo __('admin.common.button.cancel') ?></div></div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="boxEditExtendTPL" tabindex="-1" role="basic" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content modal_content_medium_float_panel">
            <div class="csv-modal-title" data-dismiss="modal" aria-hidden="true">
                <div class="txt_title_float_panel"><?php echo __('admin.send.addr.title.edit_extend_tpl') ?></div>
                <img src="/assets/common/images/Icon-cancel_large.svg" class="closeImg" style="float:right;cursor:pointer;">
            </div>
            <div class="container_item_in_float_panel_frame">
                <div class="csv-table-title">
                    <input id="iEditExtendTPL" placeholder="<?php echo __('admin.send.addr.input.hint.edit_extend') ?>" class="form-control" />
                </div>
            </div>
            <div class="group_btn_large_general_frame">
                <div id = "btnApplyEditExtendTPL" class="btn_large_blue"><div class="txt_btn_large_highlight">OK</div></div>
                <div id = "btnCancelEditExtendTPL" class="btn_large_white"><div class="txt_btn_large"><?php echo __('admin.common.button.cancel') ?></div></div>
            </div>
        </div>
    </div>
</div>

<!-- 3RD hidden/float panel END -->
<div id="tabContentContainerExtends" class="table_all_member content-container white" <?php if ($tab != '2') echo 'style="display:none;"';?>>
    <div class="all_member_head">
        <div class="txt_table_title"><?php echo __('admin.send.addr.tab.title.extend')?>
        </div>
        <div class="button_container">
            <div id="anchorCSVDownload" style="height: 100%;display: flex;align-items: center;gap: 3px;">
                <span class="selected_info"></span>
                <a href="<?php echo $sample_csv_download_url ?>"><?php echo __('admin.common.label.csv_sample') ?></a>
            </div>
            <div class="btn_w_ico pointer"  id="btnExtendNew" <?php if (!$extend_exceed) echo 'style="display:none;"';?>>
                <span class="icon-add-white"></span>
                <div class="txt_btn_w_ico"><?php echo __('admin.send.addr.button.new_extend') ?></div>
            </div>
        </div>
    </div>

    <table id="table4list" class="table table-striped table-bordered table-hover js-data-table4">
        <!-- batch action buttons -->
        <thead>
            <tr>
                <!-- icon check column -->
                <th>
                    <div class="txt_column_head_content"><?php echo __('admin.send.addr.th.extend') ?></div>
                </th>
                <th>
                    <div class="txt_column_head_content"><?php echo __('admin.send.addr.th.alias') ?></div>
                </th>
                <th>
                    <div class="txt_column_head_content"><?php echo __('admin.send.addr.th.tpl_param_alias') ?></div>
                </th>
                <th>
                    <div class="txt_column_head_content"><?php echo __('admin.send.addr.th.last_update') ?></div>
                </th>
                <th>
                    <div class="txt_column_head_content"><?php echo __('admin.common.label.edit') ?></div>
                </th>
            </tr>
        </thead>
        <tbody>
            <!-- Cell contents read from data -->
            <!-- PHP loop echo BEGIN -->
            <?php
                foreach($extensions as $extend_no=>$extend_name) {
                    $btn_alias_html = 
                    '<span>
                        <div class="btn round image alias edit" style="margin-bottom: 4px;">'.__('admin.common.button.alias_edit').'</div>
                    </span>';
                    $btn_tpl_alias_html = 
                    '<span>
                        <div class="btn round image tpl edit" style="margin-bottom: 4px;">'.__('admin.common.button.tpl_alias_edit').'</div>
                    </span>';
                    $btn_delete_html = '';
                    $pin_html = '<i class="icon-pin"></i>';
                    if($extend_no < 1 || $extend_no > 10) {
                        $pin_html = '<i class="icon-pencil"></i>';
                        $btn_delete_html = 
                    '<span>
                        <div class="btn round image alias delete" style="margin-bottom: 4px;">'.__('admin.common.button.extend_delete').'</div>
                    </span>';
                    } else if ( $extend_name ){
                        $btn_delete_html = 
                    '<span>
                        <div class="btn round image clear" style="margin-bottom: 4px;">'.__('admin.common.button.alias_delete').'</div>
                    </span>';
                    } else {
                        $btn_alias_html = 
                    '<span>
                        <div class="btn round image alias add" style="margin-bottom: 4px;">'.__('admin.common.button.alias').'</div>
                    </span>';
                    } 
                    $upd_time = '';
                    $tpl_param_alias = '';
                    if( isset($extension_details[$extend_no]) ) {
                        $upd_time = $extension_details[$extend_no]['upd_time'];
                        $upd_time = date('Y/m/d', strtotime($upd_time));

                        $tpl_param_alias = $extension_details[$extend_no]['template_param'];
                    }
                    
                    echo('
            <tr extend_no="'.$extend_no.'">
                
                <td class="cell_column_head_frame extend_name">'.$pin_html.
                    '<span class="txt_column_head_content">'.__('admin.send.addr.th.extend').$extend_no.'</span>
                </td>
                <td class="cell_column_head_frame alias">
                    <span class="txt_column_head_content">'.$extend_name.'</span>
                </td>

                <td class="cell_column_head_frame tpl_alias">
                    <span class="txt_column_head_content">'.$tpl_param_alias.'</span>   
                </td> 
                <td class="cell_column_head_frame">
                    <span class="txt_column_head_content">'.$upd_time.'</span>   
                </td> 

                <td>'
                    .$btn_alias_html
                    // .$btn_delete_html
                    .$btn_tpl_alias_html.
                '</td>     
            </tr>');                    
                }
            ?>
            <!-- PHP loop echo END -->
        </tbody>
    </table>
</div>
