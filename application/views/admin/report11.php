<!-- BEGIN PAGE HEADER-->
<!-- BEGIN PAGE HEAD -->
<div class="page-head">
	<!-- BEGIN PAGE TITLE -->
	<div class="page-title">
		<h1><?php echo __('admin.report.monthly.title') ?><small></small></h1>
	</div>
	<!-- END PAGE TITLE -->
</div>
<!-- END PAGE HEAD -->
<!-- END PAGE HEADER-->
<!-- BEGIN PAGE CONTENT-->

<!-- menu & botcond -->
<div class="row">
	<div class="col-md-12">
		<div class="page-wrapper">
			<div class="portlet chatbot light">
				<?php echo $reportmenu ?>
				<div class="portlet chatbot box">
					<div class="portlet-body">
						<div class="form-body">		
							<div class="form-group">
								<label class="control-label col-md-1" style="padding-left:15px; padding-right:15px;"><?php echo __('admin.common.label.period') ?></label>
								<div class="col-md-3" style="width: 265px;">
									<input name="start_date" id="start_date" value="<?php echo($start_date)?>" style="float:left;" class="form-control form-control-inline input-small date-picker" size="16" data-date-format="yyyy-mm-dd"  type="text"/>
									<input name="end_date" id="end_date" value="<?php echo($end_date)?>" style="float:left; margin-left:10px;" class="form-control form-control-inline input-small date-picker" size="16" data-date-format="yyyy-mm-dd"  type="text"/>
								</div>
								<label class="control-label col-md-3" style="width: 112px;"><?php echo __('admin.common.label.number_of_repeaters_and_unasked questions') ?></label>
								<div class="col-md-2">
									<input type="checkbox" id="utf-8" name="repeat_flg" <?php if ($repeat_flg==1) echo('checked')?> value="1" class="make-switch" data-on-color="success" data-off-color="warning">
								</div>
								<label class="control-label col-md-1" style="width: 100px;"><?php echo __('admin.common.label.display_zero') ?></label>
								<div class="col-md-2">
									<input type="checkbox" id="utf-8" name="zero_flg" <?php if ($zero=='0') echo('checked')?> value="1" class="make-switch" data-on-color="success" data-off-color="warning">
								</div>										
							</div>
							<div class="form-group">
								<label class="control-label col-md-1" style="padding-left:15px; padding-right:15px;"><?php echo __('admin.common.label.user_flow') ?></label>
								<?php echo $botcond ?>	
								<div class="col-md-1">
									<button type="button" id="searchButton" class="btn yellow" disabled="disabled">
									<i class="fa fa-search mr10"></i><?php echo __('admin.common.button.search') ?></button>
								</div>											
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<div class="row">
	<div class="col-md-12">
		<div class="chart-wrapper">
			<div class="portlet chatbot light">
				<div class="portlet chatbot box">
					<div class="portlet-body">
						<div id="chart-dashboard" style="position:relative;display:none;">
							<div class="chart-controll" style="position:absolute;left:0;top:0;z-index:10;">
								<div class="talkappi-radio js-payment" data-name="payment" data-value="01" data-source='{"01":"<?php echo __('admin.report.monthly.by_new') ?>", "02":"<?php echo __('admin.report.monthly.by_language') ?>", "03":"<?php echo __('admin.report.monthly.by_channel') ?>"}'></div>
							</div>
						</div>
						<div id="chart-div"></div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<div class="row">
	<div class="col-md-12">
		<!-- Page Content -->
		<div id="page-wrapper">
			<div class="portlet chatbot light">			        
				<div class="portlet chatbot box">
					<div class="portlet-body">							

					<table class="table table-striped table-bordered table-hover" id="sample_3" style="width:auto;">
					<thead>
						<tr>
							<th style="width:70px;text-align:center" rowspan="2">
							<button type="button" id="csvButton" data-tableId="sample_3" data-title="<?php echo __('admin.report.monthly.user_count_by_time') ?>" class="btn green exportCsv"><?php echo __('admin.common.button.csv_export') ?></button>
							</th>								
							<?php
							$sns_array = explode(',', $_bot->sns_cd);
							$display_lang = explode(',', $_bot->lang_cd);
							foreach($sns_array as $sns_type_cd) {
								echo('<th style="width:200px;text-align:center" colspan="' . count($display_lang) . '">');
								echo($_codes['08'][$sns_type_cd]);
								echo('</th>');
							}
							?>
							<th style="width:200px;text-align:center"><?php echo __('admin.common.label.total') ?></th>																									
						</tr>
					</thead>
					<tbody>
					<tr>
						<?php 
						echo('<tr class="gradeX odd" role="row">');
						echo('<th>');
						echo('</th>');
						for($i=0; $i<count($sns_array); $i++) {
							foreach($display_lang as $k) {
								echo('<th style="width:40px;text-align:center">');
								echo($lang_array[$k]);
								echo('</th>');
						}
						}?>
						<th style="text-align:center;"><small><?php echo __('admin.common.label.new_user') ?><?php if ($repeat_flg==1) echo __('admin.common.label.repeaters_unasked_questions') ?></small></th>
					</tr>
					<?php 
					$sum_new_members = [];
					$sum_repeaters = [];
					$sum_leave_members = [];
					foreach($sns_array as $sns_type_cd) {
						foreach($_bot_lang as $k=>$v) {
							$sum_new_members[$sns_type_cd . $k] = 0;
							$sum_repeaters[$sns_type_cd . $k] = 0;
							$sum_leave_members[$sns_type_cd . $k] = 0;
						}
					}
					for($i=0; $i<24;$i++) {
						$cur_day = sprintf('%02s', $i);
						echo('<tr class="gradeX odd" role="row" style="text-align:center;">');
						echo('<td>');
						echo($cur_day . ':00～');
						echo('</td>');
						$total_new_day = 0;
						$total_repeater_day = 0;
						$total_leave_day = 0;
						foreach($sns_array as $sns_type_cd) {
							foreach($display_lang as $k) {
								echo('<td>');
								$key = $cur_day . $sns_type_cd . $k;
								if (array_key_exists($key, $result['all'])) {
									if (array_key_exists($key, $result['new'])) {
										echo($result['new'][$key]);
										$total_new_day = $total_new_day + $result['new'][$key];
										$result_new = $result['new'][$key];
									}
									else {
										echo($zero);
										$result_new = 0;
									}
									$sum_new_members[$sns_type_cd . $k] = $sum_new_members[$sns_type_cd . $k] + $result_new;
									if ($repeat_flg == 1) {
										echo('[');
										$repeat_temp = $result['all'][$key] - $result_new;
										if ($repeat_temp > 0) {
											echo($repeat_temp);
											$sum_repeaters[$sns_type_cd . $k] = $sum_repeaters[$sns_type_cd . $k] + $repeat_temp;
										}
										else {
											echo($zero);
										}
										$total_repeater_day = $total_repeater_day + $repeat_temp;
										echo('・');
										if (array_key_exists($key, $result['notleave'])) {
											$leave_temp = $result_new - $result['notleave'][$key];
											if ($result['notleave'][$key] == 0) {
												echo(($result_new == 0?$zero:$result_new));
											}
											else {
												echo(($leave_temp == 0?$zero:$leave_temp));
											}
											$sum_leave_members[$sns_type_cd . $k] = $sum_leave_members[$sns_type_cd . $k] + $leave_temp;
											$total_leave_day = $total_leave_day + $leave_temp;
										}
										else {
											echo(($result_new == 0?$zero:$result_new));
											$sum_leave_members[$sns_type_cd . $k] = $sum_leave_members[$sns_type_cd . $k] + $result_new;
											$total_leave_day = $total_leave_day + $result_new;
										}
										echo(']');
									}
								}
								else {
									if ($zero=='0') echo('0[0・0]');
								}
								echo('</td>');
							}
						}
						echo('<td>');
						if ($total_new_day > 0 || $total_repeater_day >0 || $total_leave_day >0) {
							if ($total_new_day> 0) {
								echo($total_new_day);
							}
							else {
								echo($zero);
							}
							if ($repeat_flg == 1) {
								if ($total_repeater_day > 0) {
									echo(' [' . $total_repeater_day . '・');
								}
								else {
									echo(' [' . $zero . '・');
								}
								if ($total_leave_day > 0) {
									echo($total_leave_day . ']');
								}
								else {
									echo($zero . ']');
								}
							}
						}
						else {
							if ($zero=='0') echo('0[0・0]');
						}
						echo('</td>');
						echo('</tr>');
					}
					echo('<tr class="gradeX odd" role="row" style="text-align:center;font-weight:bold;">');
					echo('<td>' . __('admin.common.label.total') . '</td>');
					$total_sum_members = 0;
					$total_sum_repeaters = 0;
					$total_sum_leave = 0;
					
					foreach($sns_array as $sns_type_cd) {
						foreach($display_lang as $k) {
							$key = $sns_type_cd . $k;
							echo('<td>');
							if ($sum_new_members[$key] > 0) {
								echo($sum_new_members[$key]);
								$total_sum_members =  $total_sum_members + $sum_new_members[$key];
							}
							else {
								if ($zero=='0') echo($zero);
							}
							if ($repeat_flg == 1) {
							echo('[');
								if ($sum_repeaters[$key] > 0) {
									echo($sum_repeaters[$key]);
									$total_sum_repeaters=  $total_sum_repeaters+ $sum_repeaters[$key];
								}
								else {
									if ($zero=='0') echo($zero);
								}
								echo('・');
								if ($sum_leave_members[$key] > 0) {
									echo($sum_leave_members[$key]);
									$total_sum_leave=  $total_sum_leave + $sum_leave_members[$key];
								}
								else {
									if ($zero=='0') echo($zero);
								}
								echo(']');
							}
							echo('</td>');
						}
					}
					
					echo('<td>' . ($total_sum_members>0?$total_sum_members:$zero));
					if ($repeat_flg == 1) {
						echo('[' . ($total_sum_repeaters>0?$total_sum_repeaters:$zero) . '・' . ($total_sum_leave>0?$total_sum_leave:$zero) . ']');
					}
					echo('</td>');
					echo('</tr>');
					?>
					</tbody>
					</table>							
					</div>
				</div>
			</div>
		</div>
		<!-- /#page-wrapper -->					
	</div>
</div>
<!-- END PAGE CONTENT-->

<script>
	const _result_json = '<?php echo json_encode($result['new']) ?>';
	const _session_result_json = '<?php echo json_encode($sessionResult) ?>';
</script>

<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/1.12.4/jquery.min.js"></script>