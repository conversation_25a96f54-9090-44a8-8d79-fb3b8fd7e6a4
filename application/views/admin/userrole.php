<style type="text/css">
	.btn.default:active,
	.btn.default.active {
		background-image: none;
		background-color: #45b6af;
		color: #fff;
	}

	.multitext-language-input>.edit-group>.edit-group-input {
		border: 1px solid #cecece;
		border-radius: 4px;
	}

	.multitext-language-input>.edit-group>.edit-group-input>input.form-control.talkappi-textinput {
		border: none;
		width: calc(100% - 56px);
	}

	.disabled-area {
		position: relative;
		pointer-events: none;
		padding: 8px;
	}
	.disabled-area::after {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background: rgba(235, 237, 242, 0.2);
		opacity: 0.8;
		z-index: 10;
		pointer-events: auto;
  		cursor: not-allowed;  
	}
	.talkappi-multitext-show-dialog {
		display: none;
	}
</style>
<script>
  const _page_action = <?php echo ($page_action) ?>;
  const _current_bot_id = <?php echo ($current_bot_id) ?>;
</script>
<!-- BEGIN PAGE HEADER-->
<!-- <PERSON><PERSON>IN PAGE HEAD -->
<div class="page-head">
	<!-- BEGIN PAGE TITLE -->
	<div class="page-title">
		<h1><?php echo ($_active_menu_name) ?><small></small></h1>
	</div>
	<!-- END PAGE TITLE -->
</div>
<!-- Page Content -->
<div id="page-wrapper">
	<div class="">
		<?php
			if (strlen($_active_menu) > 4) {
				$tab_menu = View::factory('admin/menutab');
				echo ($tab_menu);
			}
		?>
		<div class="edit-container">
			<div class="settings-container">
				<div style="display: flex; justify-content: flex-end;">
					<button type="button" class="btn green-meadow action" act="00"><i class="fa fa-file mr10"></i><?php echo __('admin.common.button.new') ?></button>
				</div>
				<input type="hidden" name="role_cd" id="role_cd" value="<?php echo ($post['role_cd']) ?>" />
				<table class="table table-striped table-bordered table-hover js-data-table">
					<thead>
						<tr>
							<th>
								<?php echo __('admin.account.label.role_cd') ?>
							</th>
							<th>
								<?php echo __('admin.account.label.role_name') ?>
							</th>
							<th>
								<?php echo __('admin.account.label.role_description') ?>
							</th>
							<th>
								<?php echo __('admin.account.th.user_count') ?>
							</th>
							<th style="width: 80px;">
								<?php echo __('admin.account.th.edit_operations') ?>
							</th>
						</tr>
					</thead>
					<tbody>
						<?php
							foreach ($results as $row) {
						?>
							<tr class="gradeX odd" role="row">
								<td>
									<?php echo ($row->role_cd) ?>
								</td>
								<td>
									<?php if (isset($role_names[$row->role_cd])) echo $role_names[$row->role_cd];
									else echo ($row->role_name); ?>
								</td>
								<td>
									<?php if (isset($role_descriptions[$row->role_cd])) echo $role_descriptions[$row->role_cd];
									else echo ($row->description) ?>
								</td>
								<td style="text-align:right;">
									<?php
									if (array_key_exists($row->role_cd, $user_count)) {
										echo ($user_count[$row->role_cd]);
										unset($user_count[$row->role_cd]);
									} else {
										echo ('-');
									}
									?>
								</td>
								<td style="text-align:center;">
									<?php if($_user->role_cd == '99' || ($_user->role_cd == '09' && $row->bot_id == $current_bot_id)){ ?>
										<button type="button" class="btn yellow action" act="01" role_cd="<?php echo ($row->role_cd) ?>"><?php echo __('admin.common.button.edit') ?></button>
										<button type="button" class="btn red action" style="margin-top:5px;" act="02" role_cd="<?php echo ($row->role_cd) ?>"><?php echo __('admin.common.button.delete') ?></button>
									<?php } else if($_user->role_cd == '09' && $row->bot_id !== $current_bot_id){ ?>
										<button type="button" class="btn green action" style="margin-top:5px;" act="01" role_cd="<?php echo ($row->role_cd) ?>">閲覧</button>
									<?php } ?>
								</td>
							</tr>
						<?php } ?>
					</tbody>
				</table>
				<br />
				<?php if ($isEdit) { ?>
					<?php
						$arr = explode(',', $post['functions']); // 権限がONのリスト
						$arr09 = explode(',', $post['functions09']); // 09の権限のリスト
						$current_parent_cd = 'undefined';
						$names = array_column($functions, 'name', 'class_cd');
						$parent_cds = array_column($functions, 'parent_cd');
						$permission = 'common';
						// 権限が09の場合：
							// 同じボットの権限：編集可能
							// 他のボットの権限：閲覧不可
							// 共通権限　　　　：閲覧のみ可能
						if($_user->role_cd == '09'){
							if($post['role_bot_id'] !== $current_bot_id){
								$permission = 'bot_read_only';
							} else {
								$permission = 'bot_editable_only';
							}
							if($isNew){
								$permission = 'bot_editable_only';
							}
							$filteredFunctions = array_filter($functions, function ($function) use ($arr09) {
								return in_array($function['class_cd'], $arr09, true) && $function['class_cd'] !== '38';
							});
							$functions = $filteredFunctions;
						} else if($_user->role_cd == '99'){
							if($isNew && $current_bot_id != 0){
								$permission = 'all_editable_only';
							}
						}
					?>

					<div style="<?php if ($permission === 'bot_read_only') echo ('position: relative;'); ?>">
						<h4 class="form-section"></h4>
						<div class="<?php if ($permission === 'bot_read_only') echo ('disabled-area'); ?>">
							<div class="form-body">
								<div class="form-group"  style="<?php if($_user->role_cd != '99') { echo 'display: none;';}?>">
									<label class="control-label col-md-2">注意点</label>
									<span class="col-md-2" style="margin-left:8px; padding: 6px;background: #f6f7f9; width: auto;">
										全施設に共通する権限：basebotで編集してください（ロールCDは1~99で設定してください）</br>
										各ボットで特有の権限：各ボットで編集してください（ロールCDは自動採番されます）</br>
										＊本注意点はスーパー管理者にのみ表示されています。
									</span>
								</div>
								<div class="form-group" style="<?php if($isNew && $current_bot_id != 0) { echo 'display: none;';}?>">
									<label class="control-label col-md-2"><?php echo __('admin.account.label.role_cd') ?></label>
									<div class="col-md-2">
										<input name="role_cd_edit" id="role_cd_edit" type="text" max-length="2"
										<?php if ($post['role_cd_edit'] != '' || $permission !== 'common') echo ('readonly'); ?>
										class="form-control" value="<?php if ($isNew) { echo (''); } else {echo ($post['role_cd_edit']);} ?>">
									</div>
								</div>
								<div class="form-group">
									<label class="control-label col-md-2"><?php echo __('admin.account.label.role_name') ?></label>
									<div class="col-md-5">
										<!-- <input name="role_name" id="role_name" type="text" class="form-control" value="<?php echo ($post['role_name']) ?>"> -->
										<div id="role_name" class="talkappi-multitext multitext-title" data-name="role_name" data-max-input='50' data-language='<?php echo json_encode(array_keys($bot_lang), JSON_UNESCAPED_UNICODE) ?>' data-value='<?php echo ($role_name) ?>' title="<?php echo __('admin.account.label.role_name') ?>"></div>
									</div>
								</div>
								<div class="form-group">
									<label class="control-label col-md-2"><?php echo __('admin.account.label.role_description') ?></label>
									<div class="col-md-9">
										<!-- <input name="description" id="description" type="text" class="form-control" value="<?php echo ($post['description']) ?>"> -->
										<div id="description" class="talkappi-multitext multitext-title" data-name="description" data-max-input='255' data-language='<?php echo json_encode(array_keys($bot_lang), JSON_UNESCAPED_UNICODE) ?>' data-value='<?php echo ($role_description) ?>' title="<?php echo __('admin.account.label.role_description') ?>"></div>
									</div>
								</div>
	
								<?php
								foreach ($functions as $function) {
									if( $current_parent_cd != $function["parent_cd"]) {
										if ($current_parent_cd != 'undefined' && $current_parent_cd != $function["parent_cd"]) {
											echo ('</div></div></div>');
										}
										
										$label_name =  "基本機能";
										if ($function["parent_cd"] != "") {
											$label_name = $names[$function["parent_cd"]];
										} else {
	
										}
	
										echo ('<div class="form-group">');
										echo ('<label class="control-label col-md-2">' . $label_name . '</label>');
										echo ('<div class="col-md-9">');
										echo ('<div class="btn-group" data-toggle="buttons">');
										$current_parent_cd = $function["parent_cd"];
	
										//親コードを追加する
										if ($function["parent_cd"] != "") {
											if (in_array($function["parent_cd"], $arr)) {
												echo ('<label class="btn default active" style="margin: 1px !important">');
												echo ('<input name="classes[]" type="checkbox" checked="true" value="' . $function["parent_cd"] . '" class="toggle">' . $names[$function["parent_cd"]] . '</label>');
											} else {
												echo ('<label class="btn default" style="margin: 1px !important">');
												echo ('<input name="classes[]" type="checkbox" value="' . $function["parent_cd"] . '" class="toggle">' . $names[$function["parent_cd"]] . '</label>');
											}
										}
									}
	
									if ($function["parent_cd"] == "" && in_array($function["class_cd"], $parent_cds)) continue;
									if (in_array($function["class_cd"], $arr)) {
										echo ('<label class="btn default active" style="margin: 1px !important">');
										echo ('<input name="classes[]" type="checkbox" checked="true" value="' . $function["class_cd"] . '" class="toggle">' . $function["name"] . '</label>');
									} else {
										echo ('<label class="btn default" style="margin: 1px !important">');
										echo ('<input name="classes[]" type="checkbox" value="' . $function["class_cd"] . '" class="toggle">' . $function["name"] . '</label>');
									}
								}
								?>
							</di>
						</div>
						<div class="form-actions">
							<?php if($_user->role_cd == '99' || ($permission == 'bot_editable_only')){ ?>
								<div class="row">
									<div class="col-md-offset-2 col-md-9">
										<button type="button" id="saveBaseButton" class="btn blue mr10">
											<i class="fa fa-save mr10"></i><?php echo __('admin.common.button.save') ?></button>
										<!-- <button type="reset" class="btn gray"><?php echo __('admin.common.button.reset') ?></button> -->
									</div>
								</div>
							<?php } else if($_user->role_cd == '09' && $row->bot_id !== $current_bot_id){ ?>
								
							<?php } ?>
						</div>
					</div>
				<?php } ?>
			</div>
		</div>
	</div>
</div>
<div id="err_message_role" style="display:none;"><?php if (count($user_count) > 0) echo (__('admin.account.message.error.using_undefined_role')) ?></div>

<!-- END PAGE CONTENT-->