<style>
	.icon-detail {
		width: 23px !important;
	}

	.basic-label {
		width: 15em !important;
	}

	.settings-container {
		margin: 12px 12px 12px 12px;
		padding: 12px 0;
		border: unset;
		border-bottom: 1px solid #EBEDF2;
		background: #fff;
	}

	.lines-container {
		margin: 10px 0 10px 0;
	}

	.section-container.hide-section .setting-header-conteiner div {
		margin: 0 !important;
	}

	.talkappi-dropdown-selected-text {
		max-width: 350px;
	}
</style>

<div class="page-head">
	<!-- BEGIN PAGE TITLE -->
	<div class="page-title">
		<h1>
			<?php echo ($_active_menu_name) ?><small></small>
		</h1>
	</div>
	<!-- END PAGE TITLE -->
</div>
<!-- END PAGE HEAD -->
<!-- END PAGE HEADER-->
<!-- BEGIN PAGE CONTENT-->
<!-- Page Content -->

<div id="page-wrapper">
	<?php
		if (strlen($_active_menu) > 4) {
			$tab_menu = View::factory('admin/menutab');
			echo ($tab_menu);
		}
	?>
  
	<input type="hidden" name="message" id="message" value="<?php echo ($message) ?>" />
	<input type="hidden" name="service" id="service" value="<?php echo ($service) ?>" />
	<input type="hidden" name="mode" id="mode" value="<?php if ($bot == null) echo ("new"); ?>" />

	<div class="content-container white border">
		<div class="settings-container">
			<div class="setting-header">
				<?php echo __('admin.common.label.basic.setting'); ?>
			</div>
			<div class="lines-container">
				<label class="basic-label">業界</label>
				<div class="col-md-4">
					<?php echo Form::select('bot_class_cd', $bot_class, $post['bot_class_cd'], array('id' => 'bot_class_cd', 'class' => 'form-control')) ?>
				</div>
			</div>
			<div class="lines-container js-bot-type" <?php if (!isset($post['new'])) echo('style="display:none;"') ?>>
				<label class="basic-label">タイプ</label>
				<div class="talkappi-radio" data-name="bot_type" data-value='1' data-source='{"1":"単館", "2":"グループ（親）", "3":"グループ（子）"}' style="padding:0 8px;"></div>
			</div>
			<div class="lines-container js-group-select" style="display:none;">
				<label class="basic-label">グループ選択</label>
				<div class="col-md-4">
					<?php echo Form::select('bot_parent_id', $groups_kv, '', array('id' => 'bot_parent_id', 'class' => 'form-control')) ?>
				</div>
			</div>
			<div class="lines-container">
				<label class="basic-label">ボットID</label>
				<div class="col-md-4">
					<input name="bot_id" maxlength="20" type="text" class="form-control" placeholder="ホテルの場合自動採番可能" <?php if ($bot != null)
							echo (" readonly "); ?> value="<?php if ($post != NULL)
								   echo ($post['bot_id']) ?>">
				</div>
			</div>
			<?php if ($post != NULL && $post['scene_cd'] === ""){ ?>
				<div class="lines-container">
					<label class="basic-label">デフォルト導線</label>
					<div class="col-md-4">
						<input name="scene_cd" id="scene_cd" maxlength="32" type="text" class="form-control">
					</div>
				</div>
			<?php } ?>
			<div class="lines-container">
				<label class="basic-label">施設名</label>
				<div class="col-md-4">
					<input name="bot_name" type="text" class="form-control" placeholder="必須" value="<?php if ($post != NULL)
						echo ($post['bot_name']) ?>">
					
				</div>
			</div>
			<div class="lines-container">
				<label class="basic-label">CS担当者</label>
				<div class="col-md-4">
					<?php echo Form::select('assignee_id', $users, $post['assignee_id'], array('id' => 'assignee_id', 'class' => 'form-control')); ?>
				</div>
			</div>
			<div class="setting-header">連絡先</div>
			<div class="lines-container">
				<label class="basic-label">メール</label>
				<div class="col-md-4">
					<input name="mail" type="text" class="form-control" placeholder="" value="<?php if ($post != NULL)
						echo ($post['mail']) ?>">
				</div>
			</div>
			<div class="lines-container">
				<label class="basic-label">電話</label>
				<div class="col-md-4">
					<input name="tel" type="text" class="form-control" placeholder="" value="<?php if ($post != NULL)
						echo ($post['tel']) ?>">
				</div>
			</div>
			<div class="lines-container">
				<label class="basic-label">問合わせ先</label>
				<div class="col-md-4">
					<input name="inquiry" type="text" class="form-control" placeholder="URLまたメール"
						value="<?php if ($post != NULL)
						echo ($post['inquiry']) ?>">
				</div>
			</div>
		</div>
	<!-- ボタン -->
	<?php if ($is_admin_user): ?>
		<div class="submit-btn-container" style="margin: 60px 0 0 134px;">
			<div class="btn-larger btn-blue js-action-save" id="saveButton">
				<?php echo __('admin.common.button.save'); ?>
			</div>
		</div>
	<?php endif; ?>
</div>
</div>
<!-- END PAGE CONTENT-->