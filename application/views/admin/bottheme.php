<?php echo $menu ?>
<input type="hidden" id="scene_cd" name="scene_cd" value="<?php echo ($scene_cd) ?>" />
<input type="hidden" id="func_type" name="func_type" value="<?php echo ($func_type) ?>" />
<input type="hidden" id="act" name="act" value="" />
<div class="content-container white border">
	<div class="section-container bottom-line">
		<?php if ($func_type == 'bot' || $func_type == 'icon' || $func_type == 'webchat'  || $func_type == 'bubbles'  || $func_type == 'otapricecompare') {
			echo('<nav class="line-tab" style="padding-top: 0;"><ul class="flexbox" style="margin: 0; padding-inline-start: 0px;flex-flow: wrap; gap: 6px 0px;">');
			foreach($tab as $t) {
				$active = ($func_type == $t['label'])? 'active':'';
				echo('<li class="' . $active . '"><a class="func-menu" href="/admin/bottheme?scene=' . $scene_cd . '&type=' . $t['label'] . '">' . $t['menu'] . '</a></li>');
			}
			echo('</ul></nav>');
		}
		?>
		<div class="form-body" <?php if ($func_type != 'survey' && $func_type != 'inquiry') echo ('style="display:none;"'); ?>>
			<div class="form-group" style="display: none;">
				<label class="control-label col-md-3">ユーザー導線<?php echo $debug ?></label>
				<label class="control-label col-md-8" style="text-align:left;font-weight:bold;"><?php echo $scene_label ?></label>
			</div>
			<div class="lines-container">
				<h4 class="basic-label">テンプレート</h4>
				<div style="display:flex;">
					<div class="talkappi-pulldown js-template-cd" style="margin-right:10px;" data-name="template_cd" data-value="<?php echo $template_cd ?>" data-size="longer" data-source='<?php echo json_encode($template_list, JSON_UNESCAPED_UNICODE) ?>'></div>
					<input name="template_cd_input" id="template_cd_input" type="text" maxlength="32" placeholder="テンプレートID" class="form-control" style="width:200px;margin-right:10px;" value="">
					<input name="template_name" id="template_name" type="text" maxlength="32" placeholder="テンプレート名" class="form-control" style="width:200px;margin-right:10px;" value="">
					<button type="button" class="btn-smaller btn-blue js-copy"><span class="icon-add-white"></span>コピー作成</button>
				</div>
			</div>
		</div>

		<div class="form-body" <?php if ($func_type != 'site') { echo ('style="display:none;"'); }  ?>>
			<div class="form-group" style="display: none;">
				<label class="control-label col-md-3">ユーザー導線<?php echo $debug ?></label>
				<label class="control-label col-md-8" style="text-align:left;font-weight:bold;"><?php echo $scene_label ?></label>
			</div>
			<div class="lines-container">
				<h4 class="basic-label">テンプレート</h4>
				<div style="display:flex;">
					<div class="talkappi-pulldown js-template-cd" style="margin-right:10px;" data-name="template_cd" data-value="<?php echo $template_cd ?>" data-size="longer" data-source='<?php echo json_encode($template_list, JSON_UNESCAPED_UNICODE) ?>'></div>
				</div>
			</div>
		</div>

		<?php echo $theme_view ?>

		<?php if ($func_type == 'bot') {?>
			<div class="submit-btn-container" style="margin: 60px 0 0 134px;">
				<div type="button" class="btn-larger btn-blue js-action-save-bot">保存</div>
				<div type="button" class="btn-larger btn-white" onclick="top.location='/admin/botscenes'">一覧に戻る</div>
			</div>
		<?php } else { ?>
		
		<div class="submit-btn-container" style="margin: 60px 0 0 134px;">
			<div type="button" class="btn-larger btn-light-green js-action-save">Step1 設定を保存する</div>
			<?php if ($_SERVER['HTTP_HOST'] == 'admin.talkappi.com' || $_SERVER['HTTP_HOST'] == 'st7.talkappi.com' || $_SERVER['HTTP_HOST'] == 'localhost') { ?>
				<div type="button" class="btn-larger btn-light-blue js-action-upload">Step2 設定を適用する</div>
			<?php } ?>
			<div type="button" class="btn-larger btn-red-border js-action-delete"><span class="icon-delete"></span></div>
			<!-- <div type="button" class="btn-larger btn-light-yellow js-action-preview"><img src="/assets/admin/css/img/icon-pc.svg" style="margin-right:8px">プレビュー</div> -->
			<!-- <div type="button" class="btn-larger btn-light-yellow js-action-botpreview">サーバプレビュー</div> -->
			<div type="button" class="btn-larger btn-white" onclick="top.location='/admin/<?php echo $back_url ?>'">一覧に戻る</div>
		</div>
		<?php } ?>
	</div>
</div>
<!-- END PAGE CONTENT-->