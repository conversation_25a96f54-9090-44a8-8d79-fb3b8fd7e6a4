<?php echo $menu ?>

<input type="hidden" name="survey_id" value="<?php echo $survey_id ?>">
<input type="hidden" name="type" value="<?php echo $type ?>" >

<div class="content-container white border">
    <div class="left-container">
        <div class="form-body">
            <div class="form-group">
                <label id="survey-title" class="control-label col-md-8" style="text-align:left;font-size:13px;font-weight:400;"><?php echo __('admin.surveyresult.label.title'); ?> <?php echo $survey_name ?></label>
            </div>
            <div class="form-group">
                <label class="control-label col-md-1" style="min-width:100px;"><?php echo __('admin.surveyreport.label.view') ?></label>
                <div class="col-md-6">
                    <nav class="button-tab" style="padding:0;">
                        <ul>
                            <li class="<?php if ($type=='monthly') echo ('active')?>">
                                <a href="/adminsurvey/surveyreport73?fullview=true&id=<?php echo($survey_id); ?>&type=monthly"><?php echo __('admin.surveyreport.label.monthly') ?></a>
                            </li>
                            <li class="<?php if ($type=='daily') echo('active')?>">
                                <a href="/adminsurvey/surveyreport73?fullview=true&id=<?php echo($survey_id); ?>&type=daily"><?php echo __('admin.surveyreport.label.daily') ?></a>
                            </li>
                        </ul>
                    </nav>
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-1"><?php echo __('admin.common.label.period') ?></label>
                <div class="col-md-6" style="display: flex;align-items: center;">
                    <?php 
                        $start_date_after = substr($start_date, 0, 10);
                        $end_date_after = substr($end_date, 0, 10);
                        $date_format = 'yyyy-mm-dd';
                        $max_length = 10;
                        if ($type == 'daily') { 
                            $start_date_after = substr($start_date, 0, 10);
                            $end_date_after = substr($end_date, 0, 10);
                        } else if ($type == 'monthly') {
                            $start_date_after = substr($start_date, 0, 7);
                            $end_date_after = substr($end_date, 0, 7);
                            $date_format = 'yyyy-mm';
                            $max_length = 7;
                        }
                    ?> 
                    <input name="start_date" class="talkappi-datepicker" id="start_date" value="<?php echo($start_date_after)?>" data-date-format="<?php echo($date_format)?>" placeholder="<?php echo($date_format)?>" max-length="<?php echo ($max_length);?>" />
                    <p style="margin-right: 10px;">〜</p>
                    <input name="end_date" class="talkappi-datepicker" id="end_date" value="<?php echo($end_date_after)?>" data-date-format="<?php echo($date_format)?>" placeholder="<?php echo($date_format)?>" max-length="<?php echo ($max_length);?>" />
                    <div>
                        <button type="button" id="searchButton" class="btn blue">
                        <i class="fa fa-search mr10"></i><?php echo __('admin.common.button.search') ?></button>
                    </div>
                    <div>
                        <button type="button" id="csvexport" class="btn-smaller btn-white"><span class="icon-export"></span><?php echo __('admin.common.button.csv_export'); ?></button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="left-container">
        <div class="chart-wrapper">
            <div id="chart-div"></div>
        </div>
    </div>

    <div class="left-container">
        <div class="table-wrapper">
            <table class="table table-scripted table-bordered table-hover js-data-table">
                <thead>
                    <tr>
                        <th><?php echo $type == 'monthly' ?  __('admin.surveyreport.label.monthly') : __('admin.surveyreport.label.daily') ?></th>
                        <th><?php echo __('admin.surveyreport.label.count_answer') ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach($results as $result) { ?>
                    <tr>
                        <td><?php echo $result['time'] ?></td>
                        <td><?php echo $result['total'] ?></td>
                    </tr>
                    <?php } ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/1.12.4/jquery.min.js"></script>

<script>
    const _chart_data = '<?php echo json_encode($results, JSON_UNESCAPED_UNICODE) ?>';
    const _chart_type = '<?php echo $type ?>';
</script>