<style type="text/css">
	.btn.default:active,
	.btn.default.active {
		background-image: none;
		background-color: #45b6af;
		color: #fff;
	}

	span.active {
		background-color: #245BD6;
		border-radius: 12px;
		height: 24px;
		padding: 3px 12px;
	}

	input[type="radio"]:checked+.radio-text:before {
		border-color: #245BD6;
	}

	.coupon {
		flex-flow: column;
		margin-top: 35px;
		margin-left: -10px;
	}

	.coupon-3 {
		flex-flow: column;
		margin-top: 50px;
		margin-left: -10px;
	}

	.form-horizontal .radio {
		padding-top: 2px;
		margin-right: 10px;
		padding: 3px 12px;
	}

	.icon-round-corners-small.active {
		background-color: #D3EEFF !important;
	}

	.coupon_input {
		border: 1px solid #E3E5E8;
		border-radius: 4px;
		padding: 5px 12px;
		height: 28px;
		width: 108px;
		margin: 0 12px;
	}

	input[type='radio'] {
		margin-right: 12px;
	}

	label {
		padding-left: 10px;
	}

	.basic-label {
		width:  160px !important;
	}
</style>
<?php
$model = Model::factory('adminmodel');
?>
<input type="hidden" name="coupon_data" id="coupon_data" value="" />
<input type="hidden" name="act" id="act" value="" />
<input type="hidden" name="coupon_id" value="<?php echo $coupon_id ?>" />
<input type="hidden" name="class_cd" value="" />
<input type="hidden" name="class_cd_hidden" value="<?php echo $post['class_cd'] ?>" />

<?php echo $menu ?>
<div class="content-container white border">
	<!-- 基本設定 -->
	<div class="section-container bottom-line">
		<div class="lines-container">
			<div class="basic-label">クーポン名</div>
			<input type="text" name="coupon_name" value="<?php echo $post['coupon_name'] ?>" class="text-input-longer" placeholder="入力してください">
		</div>
		<!-- 分類 -->
		<div class="lines-container category-container">
			<div class="basic-label">機能分類</div>
			<div class="talkappi-category-select" data-name="class_cd" data-div='<?php echo $code_div ?>' data-value='<?php if ($post['class_cd'] == '') {
																															echo ('[]');
																														} else {
																															echo (json_encode(explode(' ', $post['class_cd'])));
																														} ?>'></div>
		</div>
	</div>
	<!-- コード設定 -->
	<div class="section-container bottom-line js-input-code" style="<?php if (strpos($post['class_cd'], '04') === false) echo "display:none;" ?>">
		<div class="lines-container">
			<div class="basic-label">コード設定</div>
			<input name="input_cd" type="text" class="code-readonly" value="<?php echo $post['input_cd']; ?>" style="background: white;">
		</div>
	</div>
	<!-- 抽選設定 -->
	<div class="section-container bottom-line js-input-lottery" style="<?php if (strpos($post['class_cd'], '06') === false) echo "display:none;" ?>">
		<div class="lines-container">
			<div class="basic-label">抽選設定</div>
			<div class="talkappi-pulldown" id="lottery_probability" data-name="lottery_probability" style="width: 400px;" data-value="<?php echo $data['lottery_probability'] ?>" data-source='<?php echo json_encode($lottery_probability, JSON_UNESCAPED_UNICODE) ?>'></div>
		</div>
		<div class="lines-container" style="margin-top:-15px;">
			<div class="basic-label">当選者数の上限</div>
			<div class="coupon">
				<label>
					<input type="radio" name="lottery_type" value="1" style="margin-left:0;padding-top:1px;" <?php if ($data['lottery_type'] == '1') echo 'checked' ?> />無制限
				</label><br>
				<label>
					<input type="radio" name="lottery_type" value="2" style="margin-left:0;padding-top:1px;" <?php if ($data['lottery_type'] == '2') echo 'checked' ?> />合計上限
				</label>
				<label class="js-coupon-lottery" style="<?php if ($data['lottery_type'] == '1') echo 'display:none;' ?>"><input type="text" class="coupon_input" name="lottery_maximum" value="<?php echo $data['lottery_maximum'] ?>">回まで</label>
			</div>
		</div>
	</div>
	<!-- クーポンコード -->
	<div class="section-container bottom-line">
		<div class="lines-container">
			<div class="basic-label">コード</div>
			<input readonly name="product_cd" type="text" class="code-readonly" value="<?php if ($post != NULL) echo $coupon_id ?>" placeholder="自動生成いたします">
		</div>
		<!-- 担当者 -->
		<div class="lines-container">
			<div class="basic-label">担当者</div>
			<div class="">
				<?php echo Form::select('user_in_charge', $user_list, $post['user_in_charge'], array('id' => 'user_in_charge', 'class' => 'form-control surveys-pulldown select2me', 'style' => 'width:200px;')) ?>
			</div>
		</div>
		<!-- 実施期間 -->
		<div class="lines-container">
			<div class="basic-label">利用期間</div>
			<input name="start_date" class="talkappi-datepicker" id="start_date" value="<?php echo ($post['start_date']?substr($post['start_date'], 0, 10):'') ?>" />
			<p>〜&nbsp;&nbsp;</p>
			<input name="end_date" class="talkappi-datepicker" id="end_date" value="<?php echo ($post['end_date']?substr($post['end_date'], 0, 10):'') ?>" />
		</div>
		<!-- 実施期間 -->
		<div class="lines-container">
			<div class="basic-label">利用期限</div>
			クーポン発行後<input name="expiration_date" value="<?php echo $data['expiration_date'] ?>" class="coupon_input" /><span>日後まで</span>
		</div>
	</div>
	<!--割引区分 -->
	<div class="section-container bottom-line">
		<div class="lines-container">
			<div class="basic-label"><?php echo $item_def['discount_div']['title'] ?></div>
			<div class="talkappi-radio js-discount" data-name="discount" data-value="<?php echo $data['discount_div']; ?>" data-source='<?php echo json_encode($item_def['discount_div']['list'], JSON_UNESCAPED_UNICODE) ?>'></div>
		</div>
		<!-- 種類 -->
		<div class="lines-container" style="margin-top:-15px;">
			<div class="basic-label"><?php echo $item_def['discount_type']['title'] ?></div>
			<div class="coupon">
				<label>
					<input type="radio" name="coupon_type" value="1" style="margin-left:0;padding-top:10px;" <?php if ($data['discount_type'] == '1') echo 'checked' ?> /><?php echo $item_def['discount_type']['list']['1'] ?>
				</label>
				<label class="js-coupon-mny" style="<?php if ($data['discount_type'] == '2') echo 'display:none;' ?>"><input value="<?php echo $data['discount_amount'] ?>" type="text" class="coupon_input" name="discount_amount_yen">円 OFF</label><br>
				<label>
					<input type="radio" name="coupon_type" value="2" style="margin-left:0;padding-top:1px;" <?php if ($data['discount_type'] == '2') echo 'checked' ?> /><?php echo $item_def['discount_type']['list']['2'] ?>
				</label>
				<label class="js-coupon-per" style="<?php if ($data['discount_type'] == '1') echo 'display:none;' ?>"><input value="<?php echo $data['discount_amount'] ?>" type="text" class="coupon_input" name="discount_amount_per">% OFF</label>
			</div>
		</div>
	</div>
	<!-- 発行総数 -->
	<div class="section-container bottom-line">
		<div class="lines-container" style="margin-top:-15px;">
			<div class="basic-label"><?php echo $item_def['stock_type']['title'] ?></div>
			<div class="coupon">
				<label>
					<input type="radio" name="coupon_count" value="1" style="margin-left:0;padding-top:1px;" <?php if ($data['stock_type'] == '1') echo 'checked' ?> /><?php echo $item_def['stock_type']['list']['1'] ?>
				</label><br>
				<label>
					<input type="radio" name="coupon_count" value="2" style="margin-left:0;padding-top:1px;" <?php if ($data['stock_type'] == '2') echo 'checked' ?> /><?php echo $item_def['stock_type']['list']['2'] ?>
				</label>
				<label class="js-coupon-count" style="<?php if ($data['stock_type'] == '1') echo 'display:none;' ?>"><input type="text" class="coupon_input" name="stock" value="<?php echo $data['stock'] ?>">回まで</label>
			</div>
		</div>
	</div>
	<!-- 利用回数 -->
	<div class="section-container bottom-line">
		<div class="lines-container" style="margin-top:-15px;">
			<div class="basic-label"><?php echo $item_def['use_amount_type']['title'] ?></div>
			<div class="coupon">
				<label>
					<input type="radio" name="coupon_used_all" value="1" style="margin-left:0;padding-top:1px;" <?php if ($data['use_amount_type'] == '1') echo 'checked' ?> /><?php echo $item_def['use_amount_type']['list']['1'] ?>
				</label><br>
				<label>
					<input type="radio" name="coupon_used_all" value="2" style="margin-left:0;padding-top:1px;" <?php if ($data['use_amount_type'] == '2') echo 'checked' ?> /><?php echo $item_def['use_amount_type']['list']['2'] ?>
				</label>
				<label class="js-coupon-used-all" style="<?php if ($data['use_amount_type'] == '1') echo 'display:none;' ?>"><input type="text" class="coupon_input" name="use_amount" value="<?php echo $data['use_amount'] ?>">回まで</label>
			</div>
		</div>
		<!-- ユーザー利用回数 -->
		<div class="lines-container" style="margin-top:-15px;">
			<div class="basic-label"><?php echo $item_def['stock_type_member']['title'] ?></div>
			<div class="coupon">
				<label>
					<input type="radio" name="coupon_used" value="1" style="margin-left:0;padding-top:1px;" <?php if ($data['stock_type_member'] == '1') echo 'checked' ?> /><?php echo $item_def['stock_type_member']['list']['1'] ?>
				</label><br>
				<label>
					<input type="radio" name="coupon_used" value="2" style="margin-left:0;padding-top:1px;" <?php if ($data['stock_type_member'] == '2') echo 'checked' ?> /><?php echo $item_def['stock_type_member']['list']['2'] ?>
				</label>
				<label class="js-coupon-used" style="<?php if ($data['stock_type_member'] == '1') echo 'display:none;' ?>"><input type="text" class="coupon_input" name="stock_member" value="<?php echo $data['stock_member'] ?>">回まで</label>
			</div>
		</div>
	</div>
	<!-- 利用施設 -->
	<div class="section-container bottom-line">
		<div class="lines-container" style="margin-top:-15px;">
			<div class="basic-label">利用施設の選択</div>
			<div class="coupon">
				<label>
					<input type="radio" name="coupon_facility" value="1" style="margin-left:0;padding-top:1px;" <?php if ($data['use_facilitys'] == '') echo 'checked' ?> />なし
				</label><br>
				<label>
					<input type="radio" name="coupon_facility" value="2" style="margin-left:0;padding-top:1px;" <?php if ($data['use_facilitys'] != '') echo 'checked' ?> />あり
				</label>
			</div>
		</div>
		<div class="lines-container js-facilities" style="<?php if ($data['use_facilitys'] == '') echo 'display:none;' ?>">
			<div class="basic-label">利用可能な施設</div>
			<div class="talkappi-pulldown" id="use_facilitys" data-name="use_facilitys" data-value="<?php echo $data['use_facilitys'] ?>" style="width: 400px;" data-source='<?php echo json_encode($use_facilitys, JSON_UNESCAPED_UNICODE) ?>'></div>
			<p class="edit_link js-edit">編集</p>
		</div>
	</div>
	<!-- 利用条件 -->
	<!-- <div class="lines-container" style="margin-top:-15px;">
				<div class="basic-label">利用条件</div>
				<div class="coupon-3">
					<label>
						<input type="radio" name="coupon_conditions" value="なし" style="margin-left:0;padding-top:1px;" <?php if ($data['condition'] == 'なし') echo 'checked' ?>/>なし
					</label><br>
					<label>
						<input type="radio" name="coupon_conditions" value="キャンペーンコード" style="margin-left:0;padding-top:1px;" <?php if ($data['condition'] == 'キャンペーンコード') echo 'checked' ?>/>キャンペーンコード
					</label>
					<label class="js-coupon-code" style="<?php if ($data['condition'] == 'なし' || $data['condition'] == 'コード自動生成') echo 'display:none;' ?>"><input type="text" class="coupon_input" name="condition_cd" value="<?php echo $data['condition_cd'] ?>"></label><br>
					<label>
						<input type="radio" name="coupon_conditions" value="コード自動生成" style="margin-left:0;padding-top:1px;" <?php if ($data['condition'] == 'コード自動生成') echo 'checked' ?>/>コード自動生成
					</label>
				</div>
			</div> -->
	<!-- 多言語表示 -->

	<div class="section-container bottom-line">
		<div class="lines-container">
			<div class="basic-label">表示言語</div>
			<div class="talkappi-checkbox js-language" data-name="lang_display" data-value='<?php echo json_encode($post['lang_display']) ?>' data-source='<?php echo json_encode(array_intersect_key($_bot_lang, array_flip($native_support_lang)), JSON_UNESCAPED_UNICODE) ?>'></div>
		</div>
	</div>

	<!-- 公開設定　開始 -->
	<div class="section-container">
		<div class="setting-header"><?php echo __('admin.survey.label.public_setting'); ?></div>
		<!-- 公開URL -->
		<div class="lines-container">
			<div class="basic-label"><?php echo __('admin.survey.label.public_url'); ?></div>
			<div class="public-url-input-container">
				<p class="public-url-area">
					<span class="public-url-link public-url-raw copy" @click="copyUri" data-clipboard-action="copy" data-clipboard-text="<?php echo htmlspecialchars($open_url) ?>"><?php echo $open_url ?></span>
					<span class="copy font-standard font-family-v1 font-color-v1 survey-copy-url" @click="copyUri" data-clipboard-action="copy" data-clipboard-text="<?php echo htmlspecialchars($open_url) ?>">コピー</span>
				</p>
			</div>
		</div>
	</div>
	<!-- 公開設定　終了 -->
	<!-- ボタン -->
	<div class="submit-btn-container" style="margin: 60px 0 0 119px;">
		<button class="btn-larger btn-blue js-action-save">保存</button>
		<div class="btn-larger btn-gray-black">
				<a class="flexbox-center height-100 width-100" href="<?php echo $open_url?>" target="_blank" style="color: #000;" onfocus="this.blur();"><?php echo __('admin.common.button.verify'); ?></a>
		</div>
		<a class="btn-larger btn-white js-action-back">一覧に戻る</a>
		<?php if ($coupon_id !== "") { ?>
			<div class="btn-larger btn-red-border js-action-delete">
				<span class="icon-delete"></span>
			</div>
		<?php } ?>
	</div>

</div>
</div>