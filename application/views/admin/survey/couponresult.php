<style text="css">
  .form-control {
    width: 180px;
    margin-right: 12px;
  }

  .col-md-6 {
    display: block;
  }

  .survey-period-date-container {
    background-color: white;
    width: 204px;
  }
  .use_history_wrapper {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }
  .use_history_wrapper .use_history {
    position: relative;
    padding: 4px 8px;
    border-radius: 5px;
    background: #e3e5e8;
    display: flex;
    justify-content: space-between;
  }
</style>

<?php echo $menu ?>
<input type="hidden" name="act" id="act" value="" />
<input type="hidden" name="result_no" id="result_no" value="" />
<input type="hidden" name="seq" id="seq" value="" />
<input type="hidden" name="bot_id" id="bot_id" value="" />
<input type="hidden" name="coupon_id" id="coupon_id" value="<?php echo $coupon_id ?>" />
<input type="hidden" name="type" id="type" value="<?php echo $post['type'] ?>" />

<div class="content-container white border">
  <div class="">
    <div class="form-body">
      <div class="form-group flexbox-x-axis" style="position:relative;">
        <label id="survey-title" class="control-label" style="text-align:left;font-size:13px;font-weight:400;">クーポン名 : <?php echo $coupon->coupon_name ?></label>
        <div style="position:absolute; right:0;">
          <button type="button" class="btn-smaller btn-white js-export" style="width:92px;"><span class="icon-export"></span>CSV出力</button>
        </div>
      </div>
      <!-- <nav class="line-tab">
        <ul class="">
          <li style="width:108px; text-align:center;">統計</li>
          <li style="width:108px; text-align:center;" class="active">詳細</li>
        </ul>
      </nav> -->
      <div class="result-main-container">
        <div class="flexbox-x-axis result-container">
          <p class="result-container-title">取得期間</p>
          <input type="text" class="talkappi-datepicker js-date" name="get_start_date" value="<?php echo ($post['get_start_date']) ?>" />
          <span style="margin-right:10px;">〜</span>
          <input type="text" class="talkappi-datepicker js-date" name="get_end_date" value="<?php echo ($post['get_end_date']) ?>" />
        </div>
        <div class="flexbox-x-axis result-container">
          <p class="result-container-title">利用期間</p>
          <input type="text" class="talkappi-datepicker js-date" name="use_start_date" value="<?php echo ($post['use_start_date']) ?>" />
          <span style="margin-right:10px;">〜</span>
          <input type="text" class="talkappi-datepicker js-date" name="use_end_date" value="<?php echo ($post['use_end_date']) ?>" />
        </div>
        <div class="flexbox-x-axis result-container">
          <p class="result-container-title">絞り込み</p>
          <?php echo Form::select('lang_cd', $lang_cd_list, $post['lang_cd'], array('id' => 'lang_cd', 'class' => 'form-control', 'style' => 'width:120px;')) ?>
          <?php echo Form::select('sns_type_cd', $sns_type_cd_list, $post['sns_type_cd'], array('id' => 'sns_type_cd', 'class' => 'form-control')) ?>
          <?php echo Form::select('status_cd', $status_cd, $post['status_cd'], array('id' => 'status_cd', 'class' => 'form-control')) ?>
          <div class="btn-smaller btn-blue js-search" style="width: 124px;"><img src="./../assets/admin/css/img/icon-download.svg" width="16" height="16" class="add-url" style="padding-right:3px;">絞り込み</div>
          <div class="btn-smaller btn-white js-reset" style="width: 60px;">リセット</div>
        </div>
      </div>
      <table class="table table-bordered table-striped table-hover js-data-table">
        <thead>
          <tr>
            <th style="width:80px;">利用者</th>
            <th style="width:60px;">言語</th>
            <th style="width:120px;">取得日時</th>
            <th style="width:220px;">取得方法</th>
            <th style="width:80px;">入力コード</th>
            <th style="width:60px;">利用可能回数</th>
            <th style="width:120px;">利用日時</th>
            <th>利用履歴</th>
            <th style="width:80px;">操作</th>
          </tr>
        </thead>
        <tbody>
          <?php
          foreach ($coupon_result as $res) { ?>
            <tr class="gradeX odd" role="row">
              <td style="vertical-align: middle;" data-member_id="<?php echo ($res['member_id']); ?>">
                <?php
                if ($res['sns_type_cd'] == 'fb') {
                  echo ('<img src="/assets/common/images/icon_fb.png" style="margin:5px;width:16px;"/>');
                  echo ($res['last_name'] . ' ' . $res['first_name']);
                }
                if ($res['sns_type_cd'] == 'ln') {
                  echo ('<img src="/assets/common/images/icon_ln.png" style="margin:5px;width:16px;"/>');
                  echo ($res['last_name'] . ' ' . $res['first_name']);
                }
                if ($res['sns_type_cd'] == 'wc') {
                  echo ('<img src="/assets/common/images/icon_wc.png" style="margin:5px;width:16px;"/>');
                  echo ($res['last_name'] . ' ' . $res['first_name']);
                }
                if ($res['sns_type_cd'] == 'wb') {
                  echo ('<img src="/assets/common/images/icon_wb.png" style="margin:5px;width:16px;"/>');
                  echo ('Webユーザ');
                }
                ?>
              </td>
              <td style="vertical-align: middle;">
                <?php echo ($_codes['02'][$res['lang_cd']]); ?>
              </td>
              <td style="vertical-align: middle;">
                <?php echo date('Y/m/d H:i', strtotime($res['get_date'])); ?>
              </td>
              <td style="vertical-align: middle;">
                <?php 
                if ($res['get_route'] == 'survey') {
                  echo ($res['get_route'] . " " . '<a class="link-animate" href="/' . $_path . '/survey?id=' . $res["get_route_detail"] . '">' . $res["get_route_detail"] . '</a>');
                }
                else if ($res['get_route'] == 'inquiry') {
                  echo ($res['get_route']);
                  $result_data = json_decode($res['result_data'], true);
                  echo (' <a href="/admininquiry/inquiryresult?result_id=' . $result_data['result_id'] . '&type=detail">' . $result_data['result_id'] . '</a>');
                  if (isset($result_data['labels']) && isset($result_data['labels']['inquiry_reserve']) && isset($result_data['labels']['inquiry_reserve'][1])) echo(' お客様：' . $result_data['labels']['inquiry_reserve']['1']);
                }
                else {
                  echo ($res['get_route']);
                }
                ?>
              </td>
              <td style="vertical-align: middle;">
                <?php
                  echo $res['apply_code'];
                ?>
              </td>   
              <td style="vertical-align: middle;text-align:center;">
                <?php 
                if ($res['get_route'] == 'inquiry') {
                  echo $res['num'] === NULL ? '無制限' : $res['num'];
                }
                else {
                  echo $res['num'] === NULL ? '無制限' : $res['num'];
                }
                ?>
              </td>                         
              <td style="vertical-align: middle;">
                <?= $res['last_use_date'] ? substr($res['last_use_date'], 0, 16) : "未利用"; ?>
              </td>
              <td style="vertical-align: middle;">
                <?php if (isset($res['coupon_use_result'])) {
                  echo "<div class='use_history_wrapper'>";
                  foreach ($res['coupon_use_result'] as $use_result) {
                    $use_facility = json_decode($use_result['use_facility'], true);
                    $facility = '';
                    if (is_array($use_facility)) {
                      if ($use_facility['type'] === 'inquiry') {
                        $result_id = $use_facility['result_id'];
                        $facility = '<a href="/admininquiry/inquiryresult?result_id=' . $use_facility['result_id'] . '&type=detail">' . $use_facility['result_id'] . '</a>';
                      } else {
                        $facility = $use_facility['type'];
                      }
                    } else {
                      $facility = $use_facilitys[$use_result['use_facility']];
                    }
                    echo "<div class='use_history'><div class='use_history_content'>利用日時:" . $use_result['use_date'];
                    echo "<br/>利用先施設(予約):$facility";
                    echo "</div>";
                    echo "<div class='use_history_delete'>";
                    echo "<div class='btn round image js-delete-seq delete' data-id='" . $res['result_no'] . "' data-seq='" . $use_result['seq'] . "'></div>";
                    echo "</div>";
                    echo "</div>";
                  }
                  echo "</div>";
                } ?>
              </td>
              <td>
                  <div class="btn round image js-delete-result delete" data-id="<?php echo $res['result_no'] ?: ''; ?>">削除</div>
              </td>
            </tr>
          <?php } ?>
        </tbody>
      </table>
    </div>
  </div>
</div>