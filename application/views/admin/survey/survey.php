<style>
.basic-label {
    width: 12em !important;
}
</style>

<script type="text/javascript">
	var _survey_entries = <?php echo $entries?>;
	var login_class_cd = <?php if ($login_class_cd == '') {echo('[]');} else {echo($login_class_cd);}?>;
</script>

<?php echo $menu?>
<?php $user_object = json_decode($post['mail_users'])?>
<input type="hidden" name="mail_users" id="mail_users" value="<?php echo $post['mail_users']?>" />
<input type="hidden" name="act" id="act" value="" />
<input type="hidden" name="public_flg" value="1" />
<input type="hidden" name="survey_id" value="<?php echo $survey_id?>" />
<input type="hidden" id="param_login" value="<?php if (isset($json_next_survey_setting['param_login'])) echo $json_next_survey_setting['param_login']?>" />
<input type="hidden" id="param_user_in_charge_required" value="<?php if (isset($json_next_survey_setting['user_in_charge_required'])) echo $json_next_survey_setting['user_in_charge_required']?>" />

<div class="content-container white border">
	<!-- 基本設定 -->
	<div class="section-container bottom-line">
		<div class="setting-header"><?php echo __('admin.survey.label.survey_setting'); ?></div>
			<div class="lines-container">
				<div class="basic-label"><?php echo __('admin.surveys.label.name'); ?></div>
				<input type="text" name="survey_name" value="<?php echo htmlspecialchars($post['survey_name'])?>" class="text-input-longer">
			</div>
			<!-- 導線 -->
			<div class="lines-container">
				<div class="basic-label"><?php echo __('admin.survey.label.scene_cd'); ?></div>
				<div class="talkappi-pulldown js-scene-cd" data-name="scene_cd" data-value="<?php echo $post['scene_cd']?>" data-size="longer" data-source='<?php echo json_encode($scene_list, JSON_UNESCAPED_UNICODE|JSON_HEX_APOS) ?>'></div>
			</div>			
			<!-- テンプレート -->
			<div class="lines-container">
				<div class="basic-label"><?php echo __('admin.survey.label.template_cd'); ?></div>
				<div class="talkappi-pulldown js-template-cd" data-name="template_cd" data-value="<?php echo $post['template_cd']?>" data-size="longer" data-source='<?php echo json_encode($template_list, JSON_UNESCAPED_UNICODE|JSON_HEX_APOS) ?>'></div>
			</div>
			<!-- 担当者 -->
			<div class="lines-container" <?php if ($json_next_survey_setting['user_in_charge_required'] != 1) echo('style="display:none;"')?>>
				<div class="basic-label"><?php echo __('admin.survey.label.user_in_charge'); ?></div>
				<div class="">
					<div 
						class="react-multi-select"
						data-items='<?php echo $user_list_for_react_select; ?>'
						data-initial-selected-items='<?php echo $user_in_charge_list_for_react_select; ?>'
					></div>
					<input type="hidden" name="user_in_charge">
				</div>
			</div>
			<!-- 分類 -->
			<div class="lines-container category-container">
				<div class="basic-label"><?php echo __('admin.survey.label.class_cd'); ?></div>
				<div class="talkappi-category-select" data-name="class_cd" data-div='<?php echo $code_div ?>' data-value='<?php if ($post['class_cd'] == '') {echo('[]');} else {echo(json_encode(explode(' ', $post['class_cd'])));}?>'></div>
			</div>
			<!-- コード -->
			<div class="lines-container">
				<div class="basic-label"><?php echo __('admin.survey.label.code'); ?></div>
				<input type="text" class="code-readonly" value="<?php echo $post['survey_cd']?>" placeholder="自動生成されます" readonly>
			</div>
			<!-- 実施期間 -->
			<div class="lines-container">
				<div class="basic-label"><?php echo __('admin.survey.label.period'); ?></div>
				<input name="start_date" class="talkappi-datepicker" id="start_date" value="<?php echo(substr($post['start_date'], 0, 10))?>"/>
				<input name="start_time" id="start_time" type="text" style="width:64px;height:28px;" class="form-control timepicker timepicker-24" value="<?php echo(substr($post['start_date'], 11, 5))?>" />			
				<p>〜</p>
				<input name="end_date" class="talkappi-datepicker" id="end_date" value="<?php echo(substr($post['end_date'], 0, 10))?>"/>
				<input name="end_time" id="end_time" type="text" style="width:64px;height:28px;" class="form-control timepicker timepicker-24" value="<?php echo(substr($post['end_date'], 11, 5))?>" />
			</div>
			<!-- 所要時間 -->
			<div class="lines-container">
				<div class="basic-label"><?php echo __('admin.survey.label.duration'); ?></div>
				<div class="talkappi-pulldown" data-name="duration" data-value="<?php echo $post['duration']?>" data-size="longer" data-source='<?php echo json_encode($duration, JSON_UNESCAPED_UNICODE|JSON_HEX_APOS) ?>'></div>
			</div>
			<!-- サポート言語 -->
			<div class="lines-container">
				<div class="basic-label"><?php echo __('admin.survey.label.supported_languages'); ?></div>
				<div class="talkappi-checkbox js-support-language" data-name="lang_support_cd" data-value='<?php echo json_encode($post['support_lang_cd'])?>' data-source='<?php echo json_encode($_bot_lang, JSON_UNESCAPED_UNICODE|JSON_HEX_APOS) ?>'></div>
			</div>
			<!-- 多言語表示 -->
			<div class="lines-container">
				<div class="basic-label"><?php echo __('admin.survey.label.lang_display'); ?></div>
				<div class="talkappi-checkbox js-language" data-name="lang_display" data-value='<?php echo json_encode($post['lang_display'])?>' data-source='<?php echo json_encode($_bot_lang, JSON_UNESCAPED_UNICODE|JSON_HEX_APOS) ?>'></div>
			</div>
			<div class="lines-container">
				<div class="basic-label"><?php echo __('admin.survey.label.survey_answer_limit'); ?></div>
				<input class="text-input" name="survey_answer_limit" value="<?php echo($post['survey_answer_limit'])?>" type="number">
				<div style="margin-left:10px;"><?php echo __('admin.survey.label.survey_answer_limit_hint'); ?></div>
			</div>
			<div class="lines-container">
				<div class="basic-label"><?php echo __('admin.survey.label.answer_limit'); ?></div>
				<div class="talkappi-radio" data-name="answer_limit" data-value='<?php echo $post['answer_limit']?>' data-source='{"0":"<?php echo __('admin.survey.label.answer_limit_no'); ?>", "1":"<?php echo __('admin.survey.label.answer_limit_yes'); ?>"}'></div>
			</div>
		</div>
		<!-- アンケート設定　終了 -->
	<!-- 回答後設定  -->
	<div class="section-container bottom-line">
			<div class="setting-header"><?php echo __('admin.survey.label.answer_setting'); ?></div>
			<!-- 遷移URL -->
			<div class="lines-container" style="display:flex;">
				<div class="basic-label"><?php echo __('admin.survey.label.redirect_url'); ?></div>
				<input class="text-input-longer" name="redirect_url" value="<?php echo($post['redirect_url'])?>" type="text">
			</div>
			<!-- ユーザーへ送信 -->
			<div class="lines-container" style="display:flex;">
				<div class="basic-label">
					<?php echo __('admin.survey.label.member_mail_template'); ?>
					<span class="icon-detail" style="margin: 0 0 0 0.5rem;" title="<?php echo htmlspecialchars(__('survey.inquiry.common.info.member_mail_template')) ?>"></span>
				</div>
				<div class="talkappi-pulldown" data-name="member_mail_template" data-value="<?php echo $post['member_mail_template']?>" data-blank="1" data-blank-text="<?php echo __('admininquiry.baseinfo.not_send_mail') ?>" style="width: 400px;" data-source='<?php echo json_encode($member_mail_template_list, JSON_UNESCAPED_UNICODE|JSON_HEX_APOS) ?>'></div>
				<p class="js-edit edit_link"><?php echo __('admin.common.button.edit'); ?></p>
			</div>
			<!-- 管理者へ送信 -->
			<div class="lines-container" style="display:flex;">
				<div class="basic-label"><?php echo __('admin.survey.label.user_mail_template'); ?></div>
				<div class="talkappi-pulldown" data-name="user_mail_template" data-value="<?php echo $post['user_mail_template']?>" data-blank="1" data-blank-text="<?php echo __('admininquiry.baseinfo.not_send_mail') ?>" style="width: 400px;" data-source='<?php echo json_encode($user_mail_template_list, JSON_UNESCAPED_UNICODE|JSON_HEX_APOS) ?>'></div>
				<p class="js-edit edit_link"><?php echo __('admin.common.button.edit'); ?></p>
			</div>
			<div class="lines-container js-select-user-container" style="display:none;">
				<div class="basic-label"></div>			
				<div class="js-add-user-container" style="display:flex;">
					<ul class="btn round light-blue pointer js-selected-users" style="list-style:none;display:none;"></ul>
					<div class="btn round light-blue pointer"><img src="./../assets/admin/css/img/icon-add.svg"><?php echo __('admin.survey.label.mail_users'); ?></div>
				</div>
				<input type="hidden" name="mail_users" value="<?php echo $post['mail_users']?>">
			</div>
		</div>
		<!-- 回答後設定 終了-->


	<!-- 公開設定 -->
	<div class="section-container">
			<div class="setting-header"><?php echo __('admin.survey.label.public_setting'); ?></div>
			<!-- 公開URL -->
			<div class="lines-container">
				<div class="basic-label"><?php echo __('admin.survey.label.public_url'); ?></div>
				<div class="public-url-input-container">
					<p class="public-url-area">
						<span class="public-url-link public-url-raw copy" @click="copyUri" data-clipboard-action="copy" data-clipboard-text="<?php echo htmlspecialchars($open_url) ?>"><?php echo $open_url ?></span>
						<span class="copy font-standard font-family-v1 font-color-v1 survey-copy-url" @click="copyUri" data-clipboard-action="copy" data-clipboard-text="<?php echo htmlspecialchars($open_url) ?>"><?php echo __('admin.common.label.copy'); ?></span>
					</p>
				</div>
			</div>
			<!-- リダイレクト先 -->
			<div class="lines-container">
				<div class="basic-label">
					<?php echo __('admin.common.label.redirect_to'); ?>
					<span class="icon-detail" style="margin: 0 0 0 0.5rem;" title="<?php echo __('admin.common.label.redirect_to_hint'); ?>"></span>
				</div>
				<input type="text" name="redirect_url2" value="<?php echo $post['redirect_url2'] ?>" class="text-input-longer">
			</div>
		</div>
		<!-- 公開設定　終了 -->
		<!-- ボタン -->
		<div class="actions-container" style="margin: 60px 0 0 140px;">
			<div class="btn-larger btn-blue x-first js-action-save"><?php echo __('admin.common.button.save'); ?></div>
			<div class="btn-larger btn-gray-black">
				<a class="flexbox-center height-100 width-100" href="<?php echo $verify_url?>" target="_blank" style="color: #000;" onfocus="this.blur();"><?php echo __('admin.common.button.verify'); ?></a>
			</div>
			<div class="btn-larger btn-white js-action-back"><?php echo __('admin.common.button.return_to_list'); ?></div>
			<?php if($post['survey_cd'] !== "") {?>
				<div class="btn-larger btn-red-border js-action-delete">
					<span class="icon-delete"></span>
				</div>
			<?php }?>
		</div>
	</div>
</div>

<!-- 複数選択コンポーネント -->
<script src="/assets/common/react/components/atoms/multiselect.bundle.js"></script>