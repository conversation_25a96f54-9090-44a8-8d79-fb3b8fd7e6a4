<script>
const _paging = <?php if (isset($post['paging'])) { echo json_encode($post['paging']); } else { echo 'null'; } ?>;
</script>
<!-- styles -->
<style>
    .answer_td span.emo_posi {
        background-color: rgba(50, 206, 85, 0.2);
        padding: 2px;
    }
    .answer_td span.emo_nega {
        background-color: rgba(229, 51, 97, 0.2);
        padding: 2px;
    }
    .emotion_td {
        text-align: center;
    }
    .emotion-total-positive {
        color: #32CE55;
    }
    .emotion-total-negative {
        color: #E53361;
    }
    .nps-mtx-recommend {
        background-color: #CFF2D7;
    }
    .nps-mtx-neutral {
        background-color: #FFF0BB;
    }
    .nps-mtx-criticize {
        background-color: #FFDCE5;
    }
    .eval-result-area {
        margin: 16px 0;
    }
    .filter-conditions span{
        text-decoration: underline;
        cursor: pointer;
    }
    .filter-conditions.disabled span {
        color: #A1A4AA;
        text-decoration: unset;
        cursor: unset;
    }
    .filter-conditions .survey-branch {
        color: #245BD6;
    }
</style>

<?php echo $menu ?>
<input type="hidden" name="act" id="act" value="" />
<input type="hidden" name="filter_entry_delete" id="filter_entry_delete" value="" />
<input type="hidden" name="filter_entry_answer_delete" id="filter_entry_answer_delete" value="" />
<input type="hidden" name="member_id" id="member_id" value="" />
<input type="hidden" name="bot_id" id="bot_id" value="" />
<input type="hidden" name="survey_id" id="survey_id" value="<?php echo $survey_id ?>" />
<input type="hidden" name="type" id="type" value="<?php echo $post['type'] ?>" />
<input type="hidden" name="result_no" id="result_no" value="" />
<input type="hidden" name="branches" id="branchDatas" value="<?php echo htmlspecialchars($surveyFilters) ?>" />
<div class="content-container white border">
        <div class="left-container">
            <div class="form-body">
                <div class="form-group" <?php echo(count($user_access_limit_survey)<=1?'style="display:none;"':'') ?>>
                    <div class="col-md-4">
                        <?php echo Form::select('user_access_limit_survey', $user_access_limit_survey, $survey_id, array('id' => 'user_access_limit_survey', 'class' => 'form-control')) ?>
                    </div>
                    <div class="col-md-2">
                        <button type="button" id="switch-survey" class="btn-smaller btn-white"><?php echo __('faq.button.switch'); ?></button>
                    </div>
                </div>
                <div class="form-group">
                    <label id="survey-title" class="control-label col-md-5" style="text-align:left;font-size:13px;font-weight:400;"><?php echo __('admin.surveyresult.label.title'); ?> <?php echo ($survey->survey_name) ?></label>
                    <div class="col-md-4" style="display: flex; width: 29%">
                        <p style="white-space: nowrap;"><?php echo __('admin.surveyresult.label.csv.label'); ?></p>
                        <?php 
                            $csv_type_texts = [];
                            foreach ($csv_type as $key => $value) {
                                $csv_type_texts[$key] = __('admin.surveyresult.label.csv_type_cd_'.$key);
                            }
                        ?>
                        <?php echo Form::select('csv_type_cd', $csv_type_texts, $post['csv_type_cd'], array('id' => 'csv_type_cd', 'class' => 'form-control')) ?>
                    </div>
                    <div class="col-md-3" style="display: flex; width: 26%">
                        <button type="button" id="csvexport" class="btn-smaller btn-white"><span class="icon-export"></span><?php echo __('admin.common.button.csv_export'); ?></button>
                        <!-- pdf output button move in -->
                        <?php if ($post['type'] == 'total') { ?>
                            <button type="button" value="" onclick="htmltoPDF()" class="btn-smaller btn-white link-animate"><span class="icon-export"></span><?php echo __('admin.surveyresult.label.pdf_export'); ?></button>
                        <?php } ?>
                    </div>
                    
                </div>
                <nav class="line-tab">
                    <ul class="">
                        <li class="<?php if ($post['type'] == 'total') echo ('active'); ?>"><a class="func-menu" href="/adminsurvey/surveyresult?type=total&id=<?php echo $survey_id . $full_size ?>"><?php echo __('admin.surveyresult.label.type_total'); ?></a></li>
                        <li class="<?php if ($post['type'] == 'detail') echo ('active'); ?>"><a class="func-menu" href="/adminsurvey/surveyresult?type=detail&id=<?php echo $survey_id . $full_size ?>"><?php echo __('admin.surveyresult.label.type_detail'); ?></a></li>
                    </ul>
                </nav>
                <div class="form-group">
                    <div class="col-md-2">
                        <?php echo Form::select('lang_cd', $survey_support_languages, $post['lang_cd'], array('id' => 'lang_cd', 'class' => 'form-control')) ?>                    
                    </div>     
                    <div class="col-md-6" style="display: flex;align-items: center;width: auto;">
                        <!-- update to inquiryresult style -->
                        <div class="talkappi-datepicker-range">
                            <input name="start_date" id="start_date" value="<?php echo(substr($post['start_date'], 0, 10)) ?>"/>
                            <p>〜</p>
                            <input name="end_date" id="end_date" value="<?php echo(substr($post['end_date'], 0, 10)) ?>"/>
                        </div>
                        <?php if ($has_emotion_analytics) { ?>
                            <div class="talkappi-pulldown js-emotion-mode" data-name="emotion-mode" data-value="01" data-source='{"01":"<?php echo __('admin.surveyresult.label.emotion_mode_all') ?>", "02":"<?php echo __('admin.surveyresult.label.emotion_mode_positive') ?>", "03":"<?php echo __('admin.surveyresult.label.emotion_mode_negative') ?>"}'></div>
                        <?php } ?>
                    </div>   
                    <!-- apply filter button move in -->
                    <div class="col-md-2" style="padding: 0;">
                        <button type="button" id="searchButton" class="btn-smaller btn-blue" style="margin: 0;"><span class="icon-filter"></span><?php echo __('admin.common.label.narrowdown'); ?></button>
                    </div>        
                </div>
                <div class="form-group">
                    <div class="form-control flex-x-between filter-conditions disabled" id="filter-conditions" style="max-width:350px;margin-left:10px; <?php if ($_user->role_cd == '74') echo('display:none;') ?>">
                        <span class="js-set-conditions"></span>
                        <div class="survey-branch pointer js-reset-conditions"><?php echo __('admin.common.button.clear'); ?></div>
                    </div>
                    <!-- apply filter button move out -->
                    <!-- hide reset button -->
                    <!-- <button type="button" id="clearButton" class="btn-smaller btn-gray-black"><?php echo __('admin.common.button.reset'); ?></button> -->
                    <!-- pdf output button move out -->
                </div>
                <div class="form-group">
                    <div class="col-md-12 flex">
                        <?php
                        $colors = ['icon-background-light-blue', 'icon-background-light-pink'];
                        $i = 0;
                        foreach ($survey_result_tages as $k => $vv) {
                            if (array_key_exists($k, $survey_entry_data)) {
                                foreach($vv as $v) {
                                    $t = $survey_entry_data[$k][intval($v)];
                                    if (is_array($t)) $t = $t['title'];
                                    if (isset($user_access_limit_entry[$k]) && in_array($v, $user_access_limit_entry[$k])) {
                                        echo ('<div class="icon-round-corners-small flexbox-x-axis ' . $colors[$i%2] . '" style="margin-right: 5px;"><span>' . $t . '</span></div>');
                                    }
                                    else {
                                        echo ('<div class="icon-round-corners-small flexbox-x-axis ' . $colors[$i%2] . '" style="margin-right: 5px;"><span>' . $t . '</span><div class="tag-group-btn-delete" entry="' . $k . '" entry_answer="' . $v . '"></div></div>');
                                    }
                                }
                            }
                            $i++;
                        }
                        ?>
                    </div>
                </div>
            </div>
            <?php
            $index = 1;
            ?>
            <?php if ($post['type'] == 'total') { ?>
                <div id="result-total">
                    <?php

                    foreach ($section as $no) {
                        $survey_result_total[$no]["number"] = $no; //3/9 全ての質問に質問番号追加。
                        $entry = $survey_result_total[$no];
                        //if ($post['survey_entry'] != '' && $post['survey_entry'] != $no) continue;
                        if($entry['entry_type_cd'] == 'frs') continue;
                        if ($_user->role_cd == '73') {
                            if (isset($user_access_limit_entry[$entry['number']])) continue;
                        }
                        echo ('<div style="width:75%;display:flex;"><span class="result-title">' . $entry['title'] . '</span>');
                        if ($entry['entry_type_cd'] !== 'scr') {
                            echo ('<span class="label kind-label">' . $_codes['10'][$entry['entry_type_cd']] . '</span>');
                        } else {
                            echo ('<span class="label kind-label">' . $_codes['10'][$entry['scale']] . '</span>');
                        }
                        if ($entry['required'] == 1) echo ('<span class="label required-label">' .  __('survey.common.label.required') . '</span>');
                        if ($entry['entry_type_cd'] == 'fup') echo('<button type="button" style="float:right;" class="btn blue fup-download" entry_no="' . $no . '">' . __('admin.surveyresult.label.fup_download') . '</button>');
                        if ($entry['emotion_analytics']) echo ('<span class="label kind-label">' . __('survey.inquiry.common.txt.txa.emotion_analytics') . '</span>');
                        echo ('</div>');
                        if ($entry['entry_type_cd'] == 'txt' || $entry['entry_type_cd'] == 'txa' || $entry['entry_type_cd'] == 'fup' || $entry['entry_type_cd'] == 'spl') {
                        	if ($entry['entry_type_cd'] == 'txt' || $entry['entry_type_cd'] == 'txa' || $entry['entry_type_cd'] == 'spl') {
                                echo ('<table class="table paginateTbl' . $index . ' table-striped table-bordered table-hover" style="width:75%; margin-top:15px;">');
                                $total = 0;
                                foreach ($entry['entry_count'] as $ec) {
                                    if ($ec['answer'] != '') {
                                        $total = $total + 1;
                                    }
                                }
                                // echo ('<thead><tr><th style="width: 300px;">UID</th><th>' . __('admin.common.label.answer') . '(' . $total . ')</th></tr></thead>');
                                echo ('<thead><tr>');
                                echo ('<th style="width: 300px;">'.__('admin.common.label.member_id').'</th>');
                                echo ('<th>' . __('admin.common.label.answer') . '(' . $total . ')</th>');
                                if ($entry['emotion_analytics']) {
                                    echo ('<th>'. __('admin.surveyresult.label.posneg') .'</th>');
                                }
                                echo ('</thead></tr>');
                                echo ('<tbody class="paginateBody' . $index . '">');
                                $chart_data = [];
                                foreach ($entry['entry_count'] as $k => $ec) {
                                    $i = $k + 1;
                                    if ($ec['answer'] != '') {
                                        echo ('<tr>');
                                        echo ('<td>' . $ec['uid'] . '</td>');
                                        if ($entry['emotion_analytics']) {
                                            echo ('<td class="answer_td"><div id="show_answer-'. $no . '-' . ($i) . '">' . $ec['answer'] . '</div></td>');
                                            if (array_key_exists('emotion_analytics', $ec)) {
                                                $emotion_analytics = $ec['emotion_analytics'];
                                                if (count($emotion_analytics['positive']['texts']) == 0 && count($emotion_analytics['negative']['texts']) == 0) {
                                                    if (array_key_exists('neutral', $chart_data)) {
                                                        $chart_data['neutral'] += 1;
                                                    } else {
                                                        $chart_data['neutral'] = 1;
                                                    }
                                                } else {
                                                    if (array_key_exists('positive', $chart_data)) {
                                                        $chart_data['positive'] += count($emotion_analytics['positive']['texts']);
                                                    } else {
                                                        $chart_data['positive'] = count($emotion_analytics['positive']['texts']);
                                                    }
                                                    if (array_key_exists('negative', $chart_data)) {
                                                        $chart_data['negative'] += count($emotion_analytics['negative']['texts']);
                                                    } else {
                                                        $chart_data['negative'] = count($emotion_analytics['negative']['texts']);
                                                    }
                                                }
                                                echo ('<td id="show_score-'. $no . '-' . ($i) . '" class="emotion_td"><div class="score"></div></td>');
                                                echo ('<input type="hidden" id="emotion_answer-'. $no . '-' . ($i) . '" value="'. htmlspecialchars($ec['answer']) .'" >');
                                                echo ('<input type="hidden" id="emotion_data-'. $no . '-' . ($i) . '" value="'. htmlspecialchars(json_encode($emotion_analytics, JSON_UNESCAPED_UNICODE)) .'" >');
                                            } else {
                                                echo ('<td></td>');
                                            }
                                        } else {
                                            echo ('<td>' . $ec['answer'] . '</td>');
                                        }
                                        echo ('</tr>');
                                    }
                                }
                                echo ('</tbody></table>');
                                // graph
                                if ($entry['emotion_analytics'] && count($chart_data) > 0) {
                                    echo ('<div class="graph-btn-wrapper" style="flex-wrap: wrap;">');
                                    echo ('<div id="tx-emotion-chart' . $no . '" class="btn btn-secondary graph-btn">' . __('admin.surveyresult.label.emotion_graph') . '</div>');
                                    echo ('<div class="charts" style="width: 100%">');
                                    echo ('<div id="tx-emotion-piechart' . $no . '"></div>');
                                    $after_chart_data = [];
                                    foreach ($chart_data as $key => $value) {
                                        $after_chart_data[__('admin.surveyresult.label.emotion_graph.legend_' . $key)] = $value;
                                    }
                                    echo ('<input type="hidden" id="tx-emotion-chart-data' . $no . '" data-type="' . htmlspecialchars($entry['entry_type_cd']) .'" value="' . htmlspecialchars(json_encode($after_chart_data, JSON_UNESCAPED_UNICODE)) . '" >');
                                    echo ('</div>');
                                    echo ('</div>');
                                }
                            } else if ($entry['entry_type_cd'] == 'fup') {
                                echo ('<table class="table paginateTbl' . $index . ' table-striped table-bordered table-hover" style="width:75%; margin-top:15px;">');
                                $total = 0;
                                foreach ($entry['entry_count'] as $ec) {
                                    if ($ec['answer'] != '') {
                                        $total = $total + 1;
                                    }
                                }
                                echo ('<thead><tr><th style="width: 300px;">'.__('admin.common.label.member_id').'</th><th>' . __('admin.common.label.file') . '(' . $total . ')</th></tr></thead>');
                                echo ('<tbody class="paginateBody' . $index . '">');
                                foreach ($entry['entry_count'] as $ec) {
                                    if ($ec['answer'] != '') {
                                        echo ('<tr>');
                                        echo ('<td>' . $ec['uid'] . '</td>');
                                        echo ('<td>');
                                        /*
                                        $file_info = json_decode($ec['answer'], true);
                                        if ($file_info != null) {
                                            foreach ($file_info as $f) {
                                                $parts = explode('/', $f['url']);
                                                echo ('<a href="' . $f['url'] . '" target="_blank" rel="noopener noreferrer">' . $parts[count($parts) - 1] . '</a>');
                                            }
                                        }
                                        */
                                        $parts = explode('/', $ec['answer']);
                                        echo ('<a href="' . $ec['answer'] . '" target="_blank" rel="noopener noreferrer">' . $parts[count($parts) - 1] . '</a>');
                                        echo ('</td>');
                                        echo ('</tr>');
                                    }
                                }
                                echo ('</tbody></table>');
                            }
                            $index++;
                        } else if ($entry['entry_type_cd'] == 'mtx') {
                            $input_rules = json_decode($entry['input_rules'], true);
                            $percentages = [];
                            $answer_total_num = [];
                            $mtx_bar_data = [];
                            // 人数と割合テーブル
                            echo ('<table class="table table-striped table-bordered table-hover table-'.$no.'" style="width:75%; margin-top:15px; table-layout: fixed">');
                            echo ('<thead><tr><th style="width: 120px;"></th>');
                            foreach ($input_rules['mtx_x'] as $mtx_x) {
                                echo ('<th>' .str_replace('&lt;br&gt;', ' ', $mtx_x). '</th>');
                            }
                            echo ('</tr></thead>');
                            echo ('<tbody>');
                            $i = 1;
                            $allTotal = 0;
                            foreach ($input_rules['mtx_y'] as $mtx_y) {
                                $mtx_y=str_replace('/', '／', $mtx_y);
                                echo ('<tr>');
                                echo ('<td>' .$mtx_y. '</td>');
                                $total = 0;
                                foreach ($entry['mtx_count'][$i] as $num) {
                                    $total = $num + $total;
                                }
                                $allTotal = $allTotal + $total;
                                $percentages[$mtx_y] = [];
                                $answer_total_num[$mtx_y] = [];
                                foreach ($entry['mtx_count'][$i] as $num) {
                                    if ($total > 0) {
                                        array_push($percentages[$mtx_y], round($num*100/$total, 1));
                                        array_push($answer_total_num[$mtx_y], $num);
                                    }
                                    echo ('<td style="text-align:center; vertical-align: middle;">' . $num . ' (' .($total == 0 ? 0 : round($num*100/$total, 1)).'%)</td>');
                                }
                                echo ('</tr>');
                                foreach($input_rules["mtx_x"] as $k => $mtx_x) {
                                    $mtx_bar_data[$mtx_y.'##'.$mtx_x.'##'.$k] = $entry['mtx_count'][$i][$k+1];
                                }
                                $i++;
                            }
                            echo ('</tbody></table>');
                            echo ('<div class="graph-btn-wrapper" style="flex-wrap: wrap;">');
                            echo ('<div class="graph-btn graph-btn-selected js-table-toggle" for="table-'.$no.'">' . __('admin.surveyresult.label.table') . '</div>');
                            foreach ($input_rules['mtx_y'] as $key => $mtx_y) {
                                $mtx_y=str_replace('/', '／', $mtx_y);
                                if (count($answer_total_num[$mtx_y]) > 0) {
                                    echo ('<div id="mtx-option' . $no . '-' . ($key+1) . '" class="btn btn-secondary graph-btn" style="margin-bottom: 12px;">' . __('admin.surveyresult.label.pie_chart') . '(' . $mtx_y . ')</div>');
                                }
                            }
                            if ($allTotal > 0) {
                                echo ('<div id="mtx-option' . $no . '" class="btn btn-secondary graph-btn" style="margin-bottom: 12px;">' . __('admin.surveyresult.label.vertical_bar_chart') . '</div>');
                                echo ('<div class="charts" style="width: 100%;">');
                                foreach ($input_rules['mtx_y'] as $key => $mtx_y) {
                                    $mtx_y=str_replace('/', '／', $mtx_y);
                                    echo ('<input type="hidden" data-answer_total_num="'.json_encode($answer_total_num[$mtx_y]).'" data-percentage="'.json_encode($percentages[$mtx_y]).'" id="data' . $no . '-' . ($key+1) . '" autocomplete="off">');
                                    echo ('<div id="piechart' . $no . '-' . ($key+1) . '" class="mtxpiechart"></div>');
                                    echo ('<button type="button" id="mtxpiechart_custom'.$no . '-' . ($key+1).'" class="btn white chart-custom-btn-margin">' . __('admin.surveyresult.label.customize_button') . '</button>');
                                }
                            // print_r($percentages);
                                echo ('<input type="hidden" id="mtxbarchart-data' . $no . '" value="' . htmlspecialchars(json_encode($mtx_bar_data, JSON_UNESCAPED_UNICODE)) . '" data-answer_total_num="' .  htmlspecialchars(json_encode($answer_total_num, JSON_UNESCAPED_UNICODE)) . '" data-percentage="' .  htmlspecialchars(json_encode($percentages, JSON_UNESCAPED_UNICODE))  . '">');
                                echo ('<div id="mtxbarchart' . $no . '" class="mtxbarchart"></div>');
                                echo ('<button type="button" id="mtxbarchart_custom'.$no.'" class="btn white chart-custom-btn-margin">' . __('admin.surveyresult.label.customize_button') . '</button>');
                                echo ('</div>');
                            }
                            echo ('</div>');
                        } else if ($entry['entry_type_cd'] == 'scr') {
                            if ($entry['select'] === 'opt') {
                                $total = 0;
                                foreach ($entry['entry_count'] as $ec) {
                                    $total = $total + $ec['count'];
                                }
                                echo ('<table class="table table-striped table-bordered table-hover" style="width:75%; margin-top:15px;">');
                                echo ('<thead><tr><th style="width: 100px;">' . __('admin.common.label.answer') . '</th><th style="width: 60px;">' . __('admin.surveyresult.label.ratio') . '</th><th style="width: 60px;">' . __('survey.index.label.count') . '(' . $total . ')</th></tr></thead>');
                                echo ('<tbody>');
                                $percentages = [];
                                $answer_total_num = [];
                                $results = [];
                                $scale = $entry['scale'];
                                if ($scale === 'nps') {
                                    foreach ($entry['entry_count'] as $point => $ec) {
                                        echo ('<tr>');
                                        echo ('<td style="text-align:center;">' . $point . '点</td>');
                                        if ($total > 0) {
                                            array_push($percentages,round($ec['count'] * 100 / $total, 1));
                                            array_push($answer_total_num, $ec['count']);
                                            echo ('<td style="text-align:center;">' . round($ec['count'] * 100 / $total, 1) . '%</td>');
                                        } else {
                                            echo ('<td style="text-align:center;">-%</td>');
                                        }
                                        echo ('<td style="text-align:center;">' . $ec['count'] . '</td>');
                                        echo ('</tr>');
                                        $result = [
                                            'title' => $point,
                                            'score' => intval($point),
                                            'count' => $ec['count']
                                        ];
                                        $results[] = $result;
                                    }
                                } else {
                                    $entry_count = $entry['entry_count'];
                                    $entry_data = $entry['entry_data'];
                                    foreach ($entry_data as $ed) {
                                        echo ('<tr>');
                                        echo ('<td style="text-align: center">' . $ed['label'] . '</td>');
                                        // $score = 0;
                                        $count = 0;
                                        foreach ($entry_count as $ec) {
                                            if ($ec['title'] === $ed['label']) {
                                                $count = $ec['count'];
                                            }
                                        }
                                        if ($total > 0) {
                                            array_push($percentages, round($count * 100 / $total, 1));
                                            array_push($answer_total_num, $count);
                                            echo ('<td style="text-align:center;">' . round($count * 100 / $total, 1) . '%</td>');
                                        } else {
                                            echo ('<td style="text-align:center;">-%</td>');
                                        }
                                        echo ('<td style="text-align:center;">' . $count . '</td>');
                                        echo ('</tr>');
                                        // $score = 0;
                                        // foreach ($entry['entry_data'] as $ed) {
                                        //     if ($ec['title'] === $ed['label']) {
                                        //         $score = $ed['score'];
                                        //     }
                                        // }
                                        $result = [
                                            'title' => $ed['label'],
                                            'score' => $ed['score'],
                                            'count' => $count
                                        ];
                                        $results[] = $result;
                                    }
                                }
                                echo ('</tbody>');
                                echo ('</table>');
                                if ($total > 0) {
                                    echo ('<div class="graph-btn-wrapper">');
                                    echo ('<div class="graph-btn graph-btn-selected js-table-toggle">' . __('admin.surveyresult.label.table') . '</div>');
                                    echo ('<input type="radio" class="hide btn-check" name="options" id="option' . $no . '-1" autocomplete="off">');
                                    echo ('<label class="btn btn-secondary graph-btn" for="option' . $no . '-1">' . __('admin.surveyresult.label.pie_chart') . '</label>');
                                    echo ('<input type="radio" class="hide btn-check" name="options" id="option' . $no . '-2" autocomplete="off">');
                                    echo ('<label class="btn btn-secondary graph-btn"  for="option' . $no . '-2">' . __('admin.surveyresult.label.vertical_bar_chart') . '</label>');
                                    echo ('<input type="radio" class="hide btn-check" name="options" id="option' . $no . '-3" autocomplete="off">');
                                    echo ('<label class="btn btn-secondary graph-btn" for="option' . $no . '-3">' . __('admin.surveyresult.label.horizontal_bar_chart') . '</label>');
                                    echo ('<div id="eval-option-' . $no . '" class="btn btn-secondary graph-btn" >'. __('admin.surveyresult.label.eval') .'</div>');
                                    echo ('</div>');

                                    echo ('<input type="hidden" data-answer_total_num="'.json_encode($answer_total_num).'" data-percentage="'.json_encode($percentages).'" id="data' . $no .'" autocomplete="off">');
                                    echo ('<div id="piechart' . $no  . '"></div>');
                                    echo ('<button type="button" id="piechart_custom'.$no.'" class="btn white chart-custom-btn-margin">' . __('admin.surveyresult.label.customize_button') . '</button>');
                                    // echo ('<button type="button" id="piechart_dl'.$no.'" value="" class="btn white">グラフ画像保存</button>');
                                    echo ('<div id="columnchart' . $no  . '"></div>');
                                    echo ('<button type="button" id="columnchart_custom'.$no.'" class="btn white chart-custom-btn-margin">' . __('admin.surveyresult.label.customize_button') . '</button>');
                                    // echo ('<button type="button" id="columnchart_dl'.$no.'" value="" class="btn white">グラフ画像保存</button>');
                                    echo ('<div id="barchart' . $no  . '"></div>');
                                    echo ('<button type="button" id="barchart_custom'.$no.'" class="btn white chart-custom-btn-margin">' . __('admin.surveyresult.label.customize_button') . '</button>');
                                    // echo ('<button type="button" id="barchart_dl'.$no.'" value="" class="btn white">グラフ画像保存</button>');
                                    echo ('<div id="eval-result-' . $no . '" class="eval-result-area">');
                                    echo ('<div class="talkappi-eval" data-select="' . $entry['select'] . '" data-scale="' . $scale . '" data-results="' . htmlspecialchars(json_encode($results, JSON_UNESCAPED_UNICODE)) . '"></div>');
                                    echo ('</div>');
                                }   
                            } else if ($entry['select'] === 'mtx') {
                                $input_rules = json_decode($entry['input_rules'], true);
                                $answer_total_num = [];
                                $mtx_bar_data = [];
                                $result_count = $entry['result_count'];
                                // 人数と割合テーブル
                                echo ('<table class="table table-striped table-bordered table-hover table-'.$no.'" style="width:75%; margin-top:15px; table-layout: fixed">');
                                echo ('<thead><tr><th style="width: 120px;">' . __('admin.common.label.answer') . '</th>');
                                if ($entry['scale'] === 'nps') {
                                    for($score = 10; $score >=0; $score--) {
                                        $class_name = '';
                                        if ($score >= 9) {
                                            $class_name = 'nps-mtx-recommend';
                                        } else if ($score >= 7) {
                                            $class_name = 'nps-mtx-neutral';
                                        } else {
                                            $class_name = 'nps-mtx-criticize';
                                        }
                                        echo ('<th class="' . $class_name . '" style="text-align: center">' .$score. '点</th>');
                                    }
                                } else {
                                    $mtx_x = $input_rules['mtx_x'];
                                    usort($mtx_x, function($a, $b){ return ($b['score'] - $a['score']); });
                                    foreach ($mtx_x as $x) {
                                        $score = intval($x['score']);
                                        echo ('<th style="text-align: center">' .$score. '点</th>');
                                    }
                                }
                                echo ('</tr></thead>');
                                echo ('<tbody>');
                                $i = 1;
                                $allTotal = 0;
                                foreach ($input_rules['mtx_y'] as $mtx_y) {
                                    $mtx_y=str_replace('/', '／', $mtx_y);
                                    echo ('<tr>');
                                    echo ('<td style="text-align: center">' .$mtx_y. '</td>');
                                    $total = 0;
                                    foreach ($entry['mtx_count'][$i] as $num) {
                                        $total = $num + $total;
                                    }
                                    $allTotal = $allTotal + $total;
                                    $answers = [];
                                    foreach ($entry['mtx_count'][$i] as $key => $num) {
                                        if ($total > 0) {
                                            $answers[] = ['score' => $key, 'count' => $num];
                                        }
                                        echo ('<td style="text-align:center; vertical-align: middle;">' . $num . ' (' .($total == 0 ? 0 : round($num*100/$total, 1)).'%)</td>');
                                    }
                                    echo ('</tr>');
                                    $answer_total_num[] = ["title" => $mtx_y, "answers" => $answers];
                                    $i++;
                                }
                                $answer_total = ['result_count' => $result_count, 'result_datas' => $answer_total_num];
                                echo ('</tbody></table>');
                                if ($allTotal > 0) {
                                    echo ('<div class="graph-btn-wrapper">');
                                    echo ('<div class="graph-btn graph-btn-selected js-table-toggle">' . __('admin.surveyresult.label.table') . '</div>');
                                    echo ('<input type="radio" class="hide btn-check disabled" name="options" id="option' . $no . '-1" autocomplete="off">');
                                    echo ('<label class="btn btn-secondary graph-btn disabled" for="option' . $no . '-1">' . __('admin.surveyresult.label.pie_chart') . '</label>');
                                    echo ('<input type="radio" class="hide btn-check" name="options" id="option' . $no . '-2" autocomplete="off">');
                                    echo ('<label class="btn btn-secondary graph-btn"  for="option' . $no . '-2">' . __('admin.surveyresult.label.vertical_bar_chart') . '</label>');
                                    echo ('<input type="radio" class="hide btn-check" name="options" id="option' . $no . '-3" autocomplete="off">');
                                    echo ('<label class="btn btn-secondary graph-btn" for="option' . $no . '-3">' . __('admin.surveyresult.label.horizontal_bar_chart') . '</label>');
                                    echo ('<input type="radio" class="hide btn-check" name="options" id="option' . $no . '-4" autocomplete="off">');
                                    echo ('<label class="btn btn-secondary graph-btn" for="option' . $no . '-4">' . __('admin.surveyresult.label.other_input') . '</label>');
                                    echo ('<div id="eval-option-' . $no . '" class="btn btn-secondary graph-btn" >'. __('admin.surveyresult.label.eval') .'</div>');
                                    echo ('</div>');
                                    echo ('<div id="piechart' . $no  . '"></div>');
                                    echo ('<button type="button" id="piechart_custom'.$no.'" class="btn white chart-custom-btn-margin">' . __('admin.surveyresult.label.customize_button') . '</button>');
                                    // echo ('<button type="button" id="piechart_dl'.$no.'" value="" class="btn white">グラフ画像保存</button>');
                                    echo ('<div id="columnchart' . $no  . '"></div>');
                                    echo ('<button type="button" id="columnchart_custom'.$no.'" class="btn white chart-custom-btn-margin">' . __('admin.surveyresult.label.customize_button') . '</button>');
                                    // echo ('<button type="button" id="columnchart_dl'.$no.'" value="" class="btn white">グラフ画像保存</button>');
                                    echo ('<div id="barchart' . $no  . '"></div>');
                                    echo ('<button type="button" id="barchart_custom'.$no.'" class="btn white chart-custom-btn-margin">' . __('admin.surveyresult.label.customize_button') . '</button>');
                                    // echo ('<button type="button" id="barchart_dl'.$no.'" value="" class="btn white">グラフ画像保存</button>');
                                    echo ('<div id="eval-result-' . $no . '" class="eval-result-area">');
                                    echo ('<div class="talkappi-eval" data-select="'.$entry['select'].'" data-scale="'.$entry['scale'].'" data-results="'. htmlspecialchars(json_encode($answer_total, JSON_UNESCAPED_UNICODE)) .'"></div>');
                                    echo ('</div>');
                                }
                            }
                        } else {
                            $total = 0;
                            foreach ($entry['entry_count'] as $ec) {
                                $total = $total + $ec['count'];
                                //array_push($titles, $ec['title']);   compile error
                            }
                            echo ('<table class="table table-striped table-bordered table-hover" style="width:75%; margin-top:15px;">');
                            echo ('<thead><tr><th style="width: 100px;">' . __('admin.common.label.answer') . '</th><th style="width: 60px;">' . __('admin.surveyresult.label.ratio') . '</th><th style="width: 60px;">' . __('survey.index.label.count') . '(' . $total . ')</th></tr></thead>');
                            echo ('<tbody>');
                            $percentages = [];
                            $answer_total_num = [];
                            foreach ($entry['entry_count'] as $ec) {
                                echo ('<tr>');
                                // 写真選択
                                if (is_array($ec['title'])) {
                                	$ec['title'] = $ec['title']['title'];
                                }
                                if ($ec['title'] == __('survey.common.label.other'). "*..." || $ec['title'] == __('survey.common.label.other'). "...") {
                                    echo ('<td>' . __('survey.common.label.other') . '</td>');
                                } else {
                                    echo ('<td>' . $ec['title'] . '</td>');
                                }
                                if ($total > 0) {
                                    array_push($percentages,round($ec['count'] * 100 / $total, 1));
                                    array_push($answer_total_num,$ec['count']);
                                    echo ('<td style="text-align:center;">' . round($ec['count'] * 100 / $total, 1) . '%</td>');
                                } else {
                                    echo ('<td style="text-align:center;">-%</td>');
                                }
                                echo ('<td style="text-align:center;">' . $ec['count'] . '</td>');
                                echo ('</tr>');
                            }
                            echo ('</tbody>');
                            echo ('</table>');

                            if ($total > 0) {
                                echo ('<div class="graph-btn-wrapper">');
                                echo ('<div class="graph-btn graph-btn-selected js-table-toggle">' . __('admin.surveyresult.label.table') . '</div>');

                                    echo ('<input type="radio" class="hide btn-check" name="options" id="option' . $no . '-1" autocomplete="off">');
                                    echo ('<label class="btn btn-secondary graph-btn" for="option' . $no . '-1">' . __('admin.surveyresult.label.pie_chart') . '</label>');
                                    echo ('<input type="radio" class="hide btn-check" name="options" id="option' . $no . '-2" autocomplete="off">');
                                    echo ('<label class="btn btn-secondary graph-btn"  for="option' . $no . '-2">' . __('admin.surveyresult.label.vertical_bar_chart') . '</label>');
                                    echo ('<input type="radio" class="hide btn-check" name="options" id="option' . $no . '-3" autocomplete="off">');
                                    echo ('<label class="btn btn-secondary graph-btn" for="option' . $no . '-3">' . __('admin.surveyresult.label.horizontal_bar_chart') . '</label>');
                                    echo ('<input type="radio" class="hide btn-check" name="options" id="option' . $no . '-4" autocomplete="off">');
                                    echo ('<label class="btn btn-secondary graph-btn" for="option' . $no . '-4">' . __('admin.surveyresult.label.other_input') . '</label>');
                                    echo ('</div>');
                                    echo ('<input type="hidden" data-answer_total_num="'.json_encode($answer_total_num).'" data-percentage="'.json_encode($percentages).'" id="data' . $no .'" autocomplete="off">');
                                    echo ('<div id="piechart' . $no  . '"></div>');
                                    echo ('<button type="button" id="piechart_custom'.$no.'" class="btn white chart-custom-btn-margin">' . __('admin.surveyresult.label.customize_button') . '</button>');
                                    // echo ('<button type="button" id="piechart_dl'.$no.'" value="" class="btn white">グラフ画像保存</button>');
                                    echo ('<div id="columnchart' . $no  . '"></div>');
                                    echo ('<button type="button" id="columnchart_custom'.$no.'" class="btn white chart-custom-btn-margin">' . __('admin.surveyresult.label.customize_button') . '</button>');
                                    // echo ('<button type="button" id="columnchart_dl'.$no.'" value="" class="btn white">グラフ画像保存</button>');
                                    echo ('<div id="barchart' . $no  . '"></div>');
                                    echo ('<button type="button" id="barchart_custom'.$no.'" class="btn white chart-custom-btn-margin">' . __('admin.surveyresult.label.customize_button') . '</button>');
                                    // echo ('<button type="button" id="barchart_dl'.$no.'" value="" class="btn white">グラフ画像保存</button>');
                            }
                            echo('<div class id="otherTable'.$no.'">');
                            echo ('<table class="table table-striped  table-bordered table-hover paginateTbl-other'.$no.'" style="width:75%; margin-top:15px; position:relative">');
                            echo ('<thead><tr><th style="width: 100px;">'.__('admin.common.label.member_id').'</th><th style="width: 120px;">' . __('survey.common.label.other') . '</th></tr></thead>');
                            echo ('<tbody class="paginateBody-other' . $no . '">');
                            foreach ($survey_result_entry_dict as $eds) {
                                foreach ($eds as $ed) {
                                    if ($ed["no"] == $no) {
                                        $other_optional = strpos($ed["entry_data"], __('survey.common.label.other') . '...(');
                                        $other_must = strpos($ed["entry_data"], __('survey.common.label.other') . '(');
                                        if ($other_optional !== false || $other_must !== false) {
                                            echo ('<tr>');
                                            echo ('<td>' . $ed["member_id"] . '</td>');
                                            $text = $ed["entry_data"];
                                            $pattern = "{\((.*)\)}";
                                            preg_match($pattern, $text, $matchedText);
                                            echo ('<td style="text-align:center;">' . $matchedText[1] . '</td>');
                                            echo ('</tr>');
                                        }
                                    }
                                }
                            }
                            echo ('</tbody>');
                            echo ('</table>');
                            echo ('</div>');
                        }
                        $entryLength = count($survey_entry);
                        if ($entryLength > $no) {
                            echo ("<div class='q-separator'></div>");
                        }
                    }
                    ?>
                </div>
            <?php } else { 
            	$survey_entry_arr = [];
            	foreach($survey_entry as $ie) {
            		$survey_entry_arr[$ie->no] = $ie;
            	}
            	?>
                <div id="result-detail">
                    <table class="table table-striped table-bordered table-hover js-data-table">
                        <thead>
                            <tr>
                                <th style="width: 100px;"><?php echo ($_bot_setting['flg_ai_bot'] == 0 ? __('admin.common.label.member_id') : __('admin.surveyresult.label.flg_ai_bot_name')); ?></th>
                                <th style="width: 120px;"><?php echo __('admin.surveyresult.label.answser_date'); ?></th>
                                <th style="width: 350px;"><?php echo __('admin.common.label.question'); ?></th>
                                <th style="width: 360px;"><?php echo __('admin.surveyresult.label.answer_content'); ?></th>
                                <?php if ($has_emotion_analytics) { ?>
                                    <th><?php echo __('admin.surveyresult.label.posneg') ?></th>
                                <?php } ?>
                                <?php if ($_user->role_cd != '73') { ?>
                                <th style="width: 80px;<?php if ($_bot_setting['flg_ai_bot'] == 0) echo ('display:none;') ?>"><?php echo __('admin.surveyresult.label.follow_reply'); ?></th>
                                <th style="width: 80px;"><?php echo __('admin.common.label.operation'); ?></th>
                                <?php } ?>
                            </tr>
                        </thead>
                        <tbody>
                            <?php

                            if ($survey_result != NULL) foreach ($survey_result as $member) {
                                // $is_tester = 'badge badge-success';
                                // if ($member['is_tester'] == 1) $is_tester = 'badge badge-warning';
                            ?>
                                <tr class="gradeX odd" role="row">
                                    <!-- メンバーID -->
                                    <td>
                                        <?php
                                        if ($_bot_setting['flg_ai_bot'] == 0) {
                                            if ($member['link_id'] == null) {
                                                echo ($member['member_id']);
                                            } else {
                                                echo ($member['link_id']);
                                            }
                                        } else {
                                            if ($member['name'] != '') {
                                                echo ($member['name']);
                                            } else if ($member['sns_type_cd'] == 'wb') {
                                                echo  __('admin.push.web.user');
                                            } else {
                                                echo ($member['last_name'] . ' ' . $member['first_name']);
                                            }
                                            echo ('<br /><br />');
                                            if ($member['sns_type_cd'] == 'fb') {
                                                echo ('<span class="label label-primary" style="margin-left: 5px;" >Facebook</span>');
                                            }
                                            if ($member['sns_type_cd'] == 'ln') {
                                                echo ('<span class="label label-success" style="margin-left: 5px;" >Line</span>');
                                            }
                                            if ($member['sns_type_cd'] == 'wc') {
                                                echo ('<span class="label label-success" style="margin-left: 5px;" >WeChat</span>');
                                            }
                                            if ($member['sns_type_cd'] == 'wb') {
                                                echo ('<span class="label label-warning" style="margin-left: 5px;" >Web</span>');
                                            }
                                            if ($_user->role_cd == '99' || $_user->role_cd == '01' || $_user->role_cd == '07' || $_user->role_cd == '09')
                                            echo ('</br></br><label class="badge badge-info js-memberinfo" style="cursor:pointer;" data-member-id="' . $member['member_id'] .'">' . __('admin.common.label.journey')  . '</label>');
                                        }
                                        ?>
                                    </td>
                                    <!-- 回答日時 -->
                                    <td>
                                        <?php
                                        $diff = round((strtotime($member['end_time']) - strtotime($member['start_time'])) / 60, 1);
                                        echo (substr($member['start_time'], 0, 10));
                                        echo ('<br/>') ?>
                                        <?php echo (substr($member['start_time'], 11, 5)) ?>～<?php echo (substr($member['end_time'], 11, 5));
                                                                                                echo ('<br/><br/><b>' . __('survey.index.label.duration') . ' ' . $diff . ' ' . __('admin.surveyresult.label.minute') . '</b>');
                                                                                                ?>
                                    </td>
                                    <!-- 質問 -->
                                    <td>
                                        <?php
                                        $answer_arr = [];
                                        if (count($section) > 0) {
                                        	$pos = 0;
                                        	foreach($section as $s) {
                                        		$answer_arr[] = (++$pos) . '. ' . strip_tags($survey_entry_arr[$s]->title);
                                        	}
                                        }
                                        else {
                                        	foreach ($survey_entry as $se) {
                                        		$answer_arr[] = $se->no . '. ' . $se->title;
                                        	}
                                        }
                                        echo(implode('<br />', $answer_arr));
                                        ?>
                                    </td>
                                    <!-- 回答内容 -->
                                    <td class="answer_td">
                                        <?php
                                        if (array_key_exists($member['id'], $survey_result_entry_dict)) {
                                            $results = $survey_result_entry_dict[$member['id']];
                                            $answer_arr = [];
                                            if (count($section) > 0) {
                                            	$pos = 0;
                                            	foreach($section as $s) {
                                            		$answer_arr[] = (++$pos) . '. ';
                                            	}
                                            	foreach ($results as $r) {
                                            		$pos = array_search($r['no'], $section);
                                                    $answer_html = '';
                                                    if ($r['emotion_data']) {
                                                        echo ('<input type="hidden" id="emotion_answer-'. $r['result_id'] . '-' . $r['no'] . '" value="'. htmlspecialchars($r['entry_data']) .'" >');
                                                        $answer_html = '<span>' . ($pos + 1) . '. ' . '</span><div style="display:inline;" id="show_answer-'. $r['result_id'] . '-' . $r['no'] . '">' . $r['entry_data'] . '</div>';
                                                    } else {
                                                        if (filter_var($r['entry_data'], FILTER_VALIDATE_URL)) {
                                                            $answer_html = ($pos + 1) . '. ' . '<a href="' . $r['entry_data'] . '" target="_blank">' . $survey_entry_arr[$r['no']]->title. '</a>';
                                                        } 
                                                        else {
                                                            $answer_html = ($pos + 1) . '. ' . $r['entry_data'];
                                                        }
                                                    }
                                                    $answer_arr[$pos] = $answer_html;
                                            	}
                                            }
                                            else {
                                            	foreach ($results as $r) {
                                            		$answer_arr[] = $r['no'] . '. ' . $r['entry_data'];
                                            	}
                                            }
                                            echo(implode('<br />', $answer_arr));
                                        }
                                        ?>
                                    </td>
                                    <?php if ($has_emotion_analytics) { ?>
                                    <!-- 感情分析 -->
                                    <td>
                                    <?php
                                        if (array_key_exists($member['id'], $survey_result_entry_dict)) {
                                            $results = $survey_result_entry_dict[$member['id']];
                                            
                                            if (count($section) > 0) {
                                                $scores_arr = [
                                                    'positive' => 0,
                                                    'negative' => 0
                                                ];
                                                echo ('<div class="emotion_td_total">');
                                                foreach ($results as $r) {
                                                    $pos = array_search($r['no'], $section);
                                                    $emoData = $r['emotion_data'];
                                                    if ($emoData) {
                                                        $scores_arr['positive'] += count($emoData['positive']['texts']);
                                                        $scores_arr['negative'] += count($emoData['negative']['texts']);
                                                        echo ('<input type="hidden" id="emotion_data-'. $r['result_id'] . '-' . $r['no'] . '" value="'. htmlspecialchars(json_encode($emoData, JSON_UNESCAPED_UNICODE)) .'" >');
                                                    }
                                                }
                                                echo ('<span class="score"></span>');
                                                echo ('<input type="hidden" id="emotion_scores_'. $member['id'] .'" value="' . htmlspecialchars(json_encode($scores_arr, JSON_UNESCAPED_UNICODE)) . '" >');
                                                echo ('</div>');
                                            }
                                        }
                                    ?>
                                    </td>
                                    <?php } ?>
                                    <?php if ($_user->role_cd != '73') { ?>
                                    <td <?php if ($_bot_setting['flg_ai_bot'] == 0) echo ('style="display:none;"') ?>>
                                        <a class="pop_adminchat" member_id="<?php echo $member['member_id'] ?>" service_type="survey" service_id="<?php echo $survey_id ?>">
                                            <span class="badge badge-primary" style="margin: 5px;"><?php echo __('admin.surveyresult.label.message') ?></span> </a>
                                    </td>
                                    <!-- 操作 -->
                                    <td>
                                        <div class="btn round image js-delete delete" data-id="<?php echo $member['id'] ?: ''; ?>"><?php echo __('admin.common.button.delete') ?></div>
                                    </td>
                                    <?php }?>
                                </tr>
                            <?php } ?>
                        </tbody>
                    </table>
                </div>
            <?php } ?>
        </div>
</div>
<?php
foreach ($survey_result_total as &$each) {//php参照を用いて直接$survey_result_totalの内容を書き変える
    foreach ($each["entry_count"] as &$eachOpt) {
        if (array_key_exists("title", $eachOpt) && ($eachOpt["title"] == __('survey.common.label.other') . "*..." || $eachOpt["title"] == __('survey.common.label.other') . "...")) {
            $eachOpt["title"] = __('survey.common.label.other');
        }
    }
    unset($eachOpt);
}
unset($each);
$js_result_total = [];

foreach ($section as $no) {
array_push($js_result_total,$survey_result_total[$no]);
};
?>
<script>
    const _result_total =<?php echo json_encode($js_result_total) ?>;
    const _colors_template = <?php echo json_encode($js_colors_template, JSON_UNESCAPED_UNICODE) ?>;
</script>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/1.12.4/jquery.min.js"></script>
<script type="text/javascript" src="/assets/common/paginathing.js"></script>
<script type="text/javascript">
    const _survey_branch_entries = <?php echo json_encode($survey_branch_entries, JSON_UNESCAPED_UNICODE) ?>;
    jQuery(document).ready(function($) {
        $("tbody[class^=paginateBody]").each(function(i, v) {
            if ($(`.paginateBody${i + 1}`).children().length > 0) {
                $(`.paginateBody${i + 1}`).paginathing({
                    perPage: 10,
                    insertAfter: `.paginateTbl${i + 1}`,
                    pageNumbers: true
                });
            };
        });
        $("tbody[class^=paginateBody-other]").each(function(index, value) {
            if ($(`.paginateBody-other${index + 1}`).children().length > 0) {
                $(`.paginateBody-other${index + 1}`).paginathing({
                    perPage: 10,
                    insertAfter: `.paginateTbl-other${index + 1}`,
                    pageNumbers: true
                });
            };
        });
    });
</script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.4.0/jspdf.umd.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/html2canvas@1.0.0-rc.5/dist/html2canvas.min.js"></script>
<script>
	window.html2canvas = html2canvas;
	window.jsPDF = window.jspdf.jsPDF;
</script>

<link rel="stylesheet" href="/assets/common/talkappi-eval/css/talkappi-eval.css">
<script src="/assets/common/talkappi-eval/js/talkappi-eval.js"></script>
<script src="/assets/common/talkappi-eval/js/locales/talkappi-eval-<?php echo $_lang_cd ?>.js"></script>