<script src="/assets/common/react/components/blocks/multilingualreflect.bundle.js"></script>
<?php 
    $to_lang_cds = [];
    foreach ($supprt_lang_cd as $key => $value) {
        $to_lang_cds[] = [
            'lang_cd' => $key,
            'lang' => $value
        ];
    }
?>
<script type="text/javascript">
  <?php $model = new Model_Adminmodel();?>
  let sections = <?php echo json_encode($post['section'], JSON_UNESCAPED_UNICODE) ?>;
  let buttons_pc = <?php echo json_encode($post['button_pc'], JSON_UNESCAPED_UNICODE) ?>;
  let buttons_sp = <?php echo json_encode($post['button_sp'], JSON_UNESCAPED_UNICODE) ?>;
  const _to_lang_cds = '<?php echo json_encode($to_lang_cds, JSON_UNESCAPED_UNICODE) ?>';
  const lang_edit = '<?php echo $lang_edit ?>';
</script>

<?php echo $menu ?>
<input type="hidden" name="button_pc" id="button_pc" value="" />
<input type="hidden" name="button_sp" id="button_sp" value="" />
<input type="hidden" name="section" id="section" value="" />
<input type="hidden" name="message" id="message" value="<?php echo ($message) ?>" />
<input type="hidden" name="lang" id="lang" value="<?php echo ($lang_edit) ?>" />
<input type="hidden" name="coupon_id" value="<?php echo $coupon_id ?>" />

<div class="content-container white border">
  <div class="js-content" style="display:flex;">
    <!-- 左側 -->
    <div class="" style="width: 100%;max-width: 750px;padding: 0 42px 0 0;font-size: 12px;">
      <!-- 言語選択 -->
      <div class="flex-x-between">
      <nav class="line-tab">
        <ul style="flex-flow: wrap; gap: 6px 0px;">
          <?php
          $display_lang_arr = explode(',', $lang_display);
          $translate_from_lang = [];
          foreach ($display_lang_arr as $k => $v) {
            if ($v == $lang_edit) {
              echo ('<li class="active">');
            } else {
              $translate_from_lang[$v] = $_codes['02'][$v];
              echo ('<li>');
            }
            echo ('<a class="func-menu" href="coupondesc?id=' . $coupon_id . '&lang=' . $v . '">' . $_codes['02'][$v] . '</a ></li>');
          }
          ?>
        </ul>
      </nav>
			</div>

      <div class="title-setting-container">
        <!-- タイトル設定 -->
        <div class="flexbox-x-axis desc-container js-desc-title-container">
          <div class="desc-container-title">タイトル</div>
          <div class="summernote-edit js-title-input" data-name="title" data-value='<?php echo $model->summernote_encode($post['title']) ?>' title="タイトル" data-upload-image="0"></div>
        </div>
        <div class="flexbox-x-axis desc-container">
          <div class="desc-container-title">サブタイトル</div>
          <input type="text" id="coupon_sub_title" class="talkappi-textinput text-input js-coupon-title-input" name="sub_title" data-max-input="40" style="width: 100%; padding-right: 68px;" value="<?php if ($post != NULL) echo ($post['sub_title']) ?>" placeholder="" />
        </div>
        <div class="flexbox-x-axis desc-container js-survey-header-image-container">
          <div class="desc-container-title">ヘッダー画像</div>
          <div class="talkappi-upload" data-name="image_base64" data-type="img" data-label="<?php echo basename($post['image']) ?>" data-url="<?php echo $post['image'] ?>" data-max-size="2" style="max-width: 660px;"></div>
        </div>
        <div class="desc-container desc-container-modal">
          <div>
            <div class="flexbox-x-axis">
              <h5 class="desc-container-modal-title">スタイル</h5>
            </div>
            <div class="flexbox-x-axis form-group js-close-open-modal">
              <label class="control-label desc-control-label" style="padding-right:56px;">背景色</label>
              <div class="talkappi-colorpicker js-theme-color" data-name="theme-bk-color" data-value="<?php echo ($post['style']['theme_color']) ?>"></div>
            </div>
            <div class="flexbox-x-axis form-group js-close-open-modal">
              <label class="control-label desc-control-label" style="padding-right:56px;">文字色</label>
              <div class="talkappi-colorpicker js-title-color" data-name="theme-color" data-value="<?php echo ($post['style']['title_color']) ?>"></div>
            </div>
          </div>
        </div>
        <!-- ボタン（パソコン） -->
        <div class="js-button-container js-button-container-pc">
          <div class="js-button-add-container flexbox-x-axis">
            <div class="desc-container-title" style="color:white;">ボタン</div>
            <div class="talkappi-pulldown js-button-pop" style="margin-top:12px;" data-type="menu" data-action="ボタンを追加" data-value="" data-source='{"クーポンを使う":"クーポンを使う", "URL遷移":"URL遷移", "クーポンを保存・送信":"クーポンを保存・送信"}'></div>
          </div>
          <p class="js-add-limit display-none" style="margin:10px 0 0 150px;">これ以上追加できません。</p>
          <div class="flexbox-x-axis desc-container">
            <div class="desc-container-title">モバイル別仕様</div>
            <div class="talkappi-switch js-mobile-switch" data-value="0"></div>
          </div>
        </div>
        <!-- ボタン（モバイル） -->
        <div class="js-button-container js-button-container-sp" style="display:none;">
          <div class="js-button-add-container flexbox-x-axis">
            <div class="desc-container-title" style="color:white;">ボタン</div>
            <div class="talkappi-pulldown js-button-pop" style="margin-top:12px;" data-type="menu" data-action="ボタンを追加" data-value="" data-source='{"クーポンを使う":"クーポンを使う", "URL遷移":"URL遷移", "クーポンを保存・送信":"クーポンを保存・送信"}'></div>
          </div>
          <p class="js-add-limit display-none" style="margin:10px 0 0 150px;">これ以上追加できません。</p>
        </div>
      </div>
      <!-- 説明セクション -->
      <div class="description-section-main-container js-description-section-main-container">
        <!-- 「説明セクションを追加する」 -->
        <div class="flexbox-x-axis js-add-section pointer" style="margin-top:50px; margin-left:24px;">
          <img src="./../assets/admin/css/img/icon-add.svg" width="12" height="12" class="add-icon">
          <p class="font-standard font-family-v2">セクションを追加する</p>
        </div>
      </div>
      <!-- 翻訳セクション -->
      <div id="react-multilingualreflect" style="margin-top: 20px;"></div>
      <!-- ボタンコンテナ -->
      <div class="submit-btn-container" style="margin: 60px 0 0 119px;">
        <div type="button" class="btn-larger btn-blue js-action-save">保存</div>
        <div class="btn-larger btn-white js-action-back">一覧に戻る</div>
      </div>
    </div>
    <!-- 右側 -->
    <div class="mobile-preview" style="height:100%;">
      <header class="header-container flexbox-x-axis js-pc-prev">
        <div>
          <img style="margin: 0 10px 0px 0" src="/assets/admin/css/img/icon-pc.svg" alt="">
          <span>プレビュー</span>
          <img style="margin: 0 10px 0px 10px" src="/assets/admin/css/img/icon-refresh.svg" alt="">
        </div>
        <div style="margin-left:auto;">
          <img class="prev-coupon-icon-switch pointer js-toggle" src="/assets/admin/css/img/icon-sp.svg" alt="prev-toggle" style="float:right;">
        </div>
      </header>
      <header class="header-container flexbox-x-axis js-sp-prev" style="display:none;">
        <div>
          <img style="margin: 0 10px 0px 0" src="/assets/admin/css/img/icon-sp.svg" alt="">
          <span>プレビュー (モバイル)</span>
          <img style="margin: 0 10px 0px 10px" src="/assets/admin/css/img/icon-refresh.svg" alt="">
        </div>
        <div style="margin-left:auto;">
          <img class="prev-coupon-icon-switch pointer js-toggle" src="/assets/admin/css/img/icon-pc.svg" alt="prev-toggle" style="float:right;">
        </div>
      </header>
      <main class="main-container" style="height:100%; border-radius: 0 0 10px 10px;">
        <div class="survey-main" style='border-radius: 12px;'>
          <div class="title-container js-preview-color" style="margin:0px; padding: 22px 0 12px 24px; height: 100%;">
            <?php $isTitleSummernote = strpos($post['title'], '<p>') !== false || strpos($post['title'], '<span>') !== false ?>
            <div class="js-preview-title" style="<?php if (!$isTitleSummernote) echo 'font-size: 36px; ' ?> <?php if ($isTitleSummernote) echo 'color: initial; ' ?> word-break: break-word; margin-right: 15px;">
              <?php if ($post['title'] != NULL) {
                echo ($post['title']);
              } else {
                echo 'タイトル';
              } ?>
            </div>
            <h3 class="js-preview-coupon-title" style="font-size:18px;word-break: break-word; margin-right: 15px;">
              <?php if ($post['sub_title'] != NULL) {
                echo ($post['sub_title']);
              } else {
                echo 'クーポンタイトル';
              } ?>
            </h3>
            <?php if ($start_date) : ?>
              <p class="js-start-day" style="font-size: 14px; margin:8px 0;"><?php if ($post != NULL) echo "利用開始: $start_date"; ?></p>
            <?php endif; ?>
            <?php if ($end_date) : ?>
              <p class="js-expire-day" style="font-size: 14px;"><?php if ($post != NULL) echo "利用期限: $end_date" ?></p>
              <?php endif; ?>
            <?php if ($days) : ?>
              <p class="js-remain-day" style="font-size: 14px; margin:8px 0;"><?php if ($post != NULL) echo '残り' . ($days) . '日'; ?></p>
            <?php endif; ?>
            </div>
          <img src="<?php echo $post['image'];
                    if ($post['image'] != '') echo ('?' . time()) ?>" alt="" id="preview" class="survey-preview-header-image js-coupon-preview-header-image" style="border-radius:0px;">
          <?php if ($coupon_data['use_facilitys'] != '') : ?>
            <div style="border: 1px solid #3D3F45;border-radius: 4px;padding: 9px 12px; margin: 12px; color: #A1A4AA;">利用施設を選択
              <img src="/assets/admin/css/img/dropdown.svg" style="float:right; margin-top:5px;">
            </div>
          <?php endif; ?>
          <section class="contents-container js-preview-main-container">
          </section>
          <div class="font-standard font-size-v4 font-family-v1 font-color-v3" style="padding: 10px 14px 20px 14px;"><?php echo __('survey.coupon.label.remark') ?></div>
          <div class="js-preview-description-button-container">
          </div>
        </div>
      </main>
    </div>
  </div>
</div>