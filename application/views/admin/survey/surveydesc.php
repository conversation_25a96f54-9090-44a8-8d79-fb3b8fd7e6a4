<?php echo $menu?>

<input type="hidden" name="description_extra" id="description_extra" value="" />
<input type="hidden" name="message" id="message" value="<?php echo($message)?>" />
<input type="hidden" name="lang" id="lang" value="<?php echo($lang_edit)?>" />
<input type="hidden" name="survey_id" value="<?php echo $survey_id?>" />
<input type="hidden" name="page" id="page" value="<?php echo($post['page'])?>" />
<input type="hidden" name="complete_area" id="complete_area" value="" />

<script type="text/javascript">
	<?php 
	$model = new Model_Adminmodel();
	$_item_upd_timestamp = str_replace(['-',' ',':'], '', $item->upd_time);
	?>
	const _item_upd_timestamp = '<?php echo $_item_upd_timestamp ?>';
</script>

<style>
	.survey-content-complete {
		background-color: white;
		border: solid 1px #e3e5e8;
		padding-top: 35px;
		color: #3d3f45;
		border-radius: 0 0 8px 8px;
		display: block;
		text-align: center;
		min-height: 470px;
	}
	.survey-content-complete p {
		margin: 15px 0;
	}
	.complete-message {
		max-width: 351px;
		margin: 30px 0;
		color: #3d3f45;
		font-size: 1.3rem;
		line-height: 1.54;
	}
	.complete-btn {
		width: 100%;
		height: 50px;
		border-radius: 4px;
		background-color: #231c19;
		color: #fff;
		display: -ms-flexbox;
		display: flex;
		justify-content: center;
		-ms-flex-align: center;
		margin: 0 auto;
		align-items: center;
		-ms-flex-align: center;
		max-width: 222px;
		margin-bottom: 16px;
	}
</style>

<div class="content-container white border flex">
		<!-- 左側 -->
		<div style="width:100%;">
			<div class="flex-x-between" style="padding-right:50px;">
				<nav class="line-tab">
					<ul style="flex-flow: wrap; gap: 6px 0px;"	>
						<?php
							$display_lang_arr = explode(',', $lang_display);
							foreach($display_lang_arr as $k=>$v) {
								if ($v == $lang_edit) {
									echo('<li class=" active">');
								}
								else {
									echo('<li>');
								}
								echo('<a class="func-menu" href="/adminsurvey/surveydesc?id=' . $survey_id . '&lang='. $v . '">' . $_codes['02'][$v] . '</a ></li>');
							}
						?>
					</ul>
				</nav>
				<?php if (count($translate_from_lang) > 0) { ?>
					<div style="display:flex;">
						<div class="talkappi-pulldown js-tranlate-from-lang" data-name="translate_from_lang" data-value="" data-blank-text="<?php echo __('admin.surveydesc.label.base_language'); ?>" data-source='<?php echo(json_encode($translate_from_lang, JSON_UNESCAPED_UNICODE)) ?>'></div>
						<button type="button" class="btn-smaller btn-blue js-tranlate"><?php echo __('admin.inquirydesc.label.automatic_translate'); ?></button>
					</div>
				<?php }?>
			</div>
			<!-- 編集箇所選択(回答画面、完了画面) -->
			<nav class="line-tab">
				<ul class="pointer" style="background: #ebedf2; width: fit-content;border-radius: 4px;">
					<li class="btn-smaller-square flexbox-center <?php if ($post['page'] == 'input') echo ('active'); ?>"><a class="func-menu font-standard font-family-v4 font-color-v2" href="/adminsurvey/surveydesc?page=input&lang=<?php echo($lang_edit)?>&id=<?php echo $survey_id ?>"><?php echo __('admin.surveydesc.label.input_page'); ?></a></li>
					<?php if ($post['new'] == 0) {?>
						<li class="btn-smaller-square flexbox-center <?php if ($post['page'] == 'complete') echo ('active'); ?> js-select-complete"><a class="func-menu font-standard font-family-v4 font-color-v2" href="/adminsurvey/surveydesc?page=complete&lang=<?php echo($lang_edit)?>&id=<?php echo $survey_id ?>"><?php echo __('admin.surveydesc.label.complete_page'); ?></a></li>
					<?php } else { ?>
						<li class="btn-smaller-square flexbox-center" style="font-weight:100;color:#231c19;cursor:default;"><?php echo __('admin.surveydesc.label.complete_page'); ?></li>
					<?php } ?>
				</ul>
			</nav>
			<!-- 完了画面 -->
			<?php if ($post['page'] == 'complete') { ?>
				<div class="section-container">
				<!-- 基本情報 -->
				<div class="setting-header"><?php echo __('admin.surveydesc.label.complete_basic_info'); ?></div>
					<!-- タイトル -->
					<div class="lines-container">
						<div class="basic-label"><?php echo __('survey.index.title.name'); ?></div>
						<textarea class="text-input-longer js-complete-title"><?php echo $post['complete_area']['title'] ?></textarea>
					</div>
					
					<!-- 概要 -->
					<div class="lines-container">
						<div class="basic-label"><?php echo __('admin.surveydesc.label.complete_summary'); ?></div>
						<textarea class="text-input-longer js-complete-summary-textarea js-input-des-section"><?php echo $post['complete_area']['description'] ?></textarea>
					</div>

					<!-- 画像 -->
					<div class="lines-container">
						<div class="basic-label"><?php echo __('admin.surveydesc.label.icon'); ?></div>
						<?php if($post['complete_area']['icon'] != "/assets/common/images/thank_answer.jpeg") {?>
							<div class="talkappi-radio js-payment" style="max-width:500px; width:100%" data-name="payment" data-value="02" data-source='{"01":"<?php echo __('admin.surveydesc.label.icon_default'); ?>", "02":"<?php echo __('admin.surveydesc.label.icon_setting'); ?>"}'></div>
						<?php } else {?>
							<div class="talkappi-radio js-payment" style="max-width:500px; width:100%" data-name="payment" data-value="01" data-source='{"01":"<?php echo __('admin.surveydesc.label.icon_default'); ?>", "02":"<?php echo __('admin.surveydesc.label.icon_setting'); ?>"}'></div>
						<?php }?>
					</div>

					<?php if ($post['complete_area']['icon'] != "/assets/common/images/thank_answer.jpeg") {?>
						<div class="lines-container js-complete-image-upload">
							<div class="basic-label"></div>
							<div class="talkappi-upload js-talkappi-upload" data-name="image_base64" data-type="img" data-label="<?php echo ($post['complete_area']['icon'])?>" data-url="<?php echo $post['complete_area']['icon']?>" data-max-size="2"></div>
						</div>
					<?php } else {?>
						<div class="lines-container js-complete-image-upload" style="display:none;">
							<div class="basic-label"></div>
							<div class="talkappi-upload js-talkappi-upload" data-name="image_base64" data-type="img" data-max-size="2"></div>
						</div>
					<?php }?>
					<hr>
					<!-- アクション -->
					<div class="setting-header"><?php echo __('admin.surveydesc.label.action'); ?></div>
						<div class="lines-container">
							<div class="basic-label"><?php echo __('admin.surveydesc.label.display_text'); ?></div>
							<input type="text" name="complete_action_name" value="<?php echo $post['actions'][0]['title']; ?>" class="text-input-longer js-input-complete-action-name">
						</div>
						<div class="lines-container">
							<div class="basic-label"><?php echo __('admin.surveydesc.label.destination_url'); ?></div>
							<input type="text" name="complete_action_url" value="<?php echo $post['actions'][0]['url']; ?>" class="text-input-longer js-input-complete-action-url" placeholder="<?php echo __('admin.surveydesc.label.placeholder_destination_url'); ?>">
						</div>
				</div>
			<?php } else { ?> <!-- 回答画面 -->
				<div class="section-container bottom-line">
					<h2><?php echo __('admin.surveydesc.label.input_setting'); ?></h2>
					<div class="form-body">
						<div class="lines-container">
							<label class="basic-label" style="width:110px;"><?php echo __('survey.index.title.name'); ?></label>
							<div class="col-md-10">
								<textarea name="title" class=" text-input-longer form-control js-input-title"><?php echo($post['title'])?></textarea>
							</div>
						</div>
						<div class="lines-container">
							<label class="basic-label" style="width:110px;"><?php echo __('admin.surveydesc.label.input_description_summary'); ?></label>
							<div class="col-md-10">
								<div class="summernote-edit js-input-description-summer" data-name="description" data-value='<?php echo $model->summernote_encode($post['description']) ?>' title="<?php echo __('admin.surveydesc.label.input_description_summary'); ?>" data-upload-image="1" data-position="bottom">
									<textarea class="text-input-longer form-control" rows="2"></textarea>
								</div>
							</div>
						</div>
						<div class="lines-container">
							<label class="basic-label" style="width:110px;"><?php echo __('admin.surveydesc.label.survey_image'); ?></label>
							<div class="col-md-10">
								<div class="talkappi-upload" data-name="image_base64" data-type="img" data-label="<?php echo ($post['survey_image'])?>" data-url="<?php echo $post['survey_image']?>" data-max-size="2"></div>
							</div>
						</div>		
					</div>
				</div>
				<?php 
				if($post['description_extra'] != null) {
					foreach($post['description_extra'] as $desc) { ?>
					<div class="section-container bottom-line js-section ">
						<div class="flex-x-between">
							<h2><?php echo __('admin.surveydesc.label.description_section'); ?></h2>
							<span class="icon-delete js-delete-section pointer" style="margin:24px;"></span>
						</div>
						<div class="lines-container">
							<label class="basic-label" style="width:110px;"><?php echo __('admin.surveydesc.label.input_extra_title'); ?></label>
							<div class="col-md-10">
								<input type="text" class="text-input-longer form-control js-input-extra-title" value="<?php echo $desc['title'] ?>">
							</div>
						</div>
						<div class="lines-container">
							<label class="basic-label" style="width:110px;"><?php echo __('admin.surveydesc.label.input_extra_description_summary'); ?></label>
							<div class="col-md-10">
								<div class="summernote-edit js-input-extra-description-summer" title="内容" data-value="<?php echo htmlspecialchars($desc['description']) ?>">
									<textarea class="text-input-longer form-control" rows="2"></textarea>
								</div>
							</div>
						</div>				
					</div>
				<?php }
				}?>			
				<div class="image-action-group js-add-section">
					<img src="./../assets/admin/css/img/icon-add.svg" width="12" height="12">
					<span><?php echo __('admin.surveydesc.label.add_description_section'); ?></span>
				</div>
				<!-- clone 用 secton -->
				<div class="section-container bottom-line for-clone">
					<div class="flex-x-between">
						<h2><?php echo __('admin.surveydesc.label.description_section'); ?></h2>
						<span class="icon-delete js-delete-section pointer" style="margin:24px;"></span>
					</div>
					<div class="lines-container">
						<label class="basic-label" style="width:110px;"><?php echo __('admin.surveydesc.label.input_extra_title'); ?></label>
						<div class="col-md-10">
							<input type="text" class="text-input-longer form-control js-input-extra-title" value="">
						</div>
					</div>
					<div class="lines-container">
						<label class="basic-label" style="width:110px;"><?php echo __('admin.surveydesc.label.input_extra_description_summary'); ?></label>
						<div class="col-md-10">
							<div class="summernote-edit js-input-extra-description-summer" title="<?php echo __('admin.surveydesc.label.input_extra_description_summary'); ?>" data-value="">
								<textarea class="text-input-longer form-control" rows="2"></textarea>
							</div>
						</div>
					</div>				
				</div>			
			<?php } ?>
			<!-- ボタン　コンテナ -->
			<div class="actions-container" style="margin: 60px 0 50px 90px;">
				<span class="btn-larger btn-blue js-action-save"><?php echo __('admin.common.button.save'); ?></span>
				<span class="btn-larger btn-gray-black js-action-verify"><a href="<?php echo $verify_url?>" target="_blank" style="color: #000;" onfocus="this.blur();"><?php echo __('admin.common.button.verify'); ?></a></span>
				<span class="btn-larger btn-white js-action-back"><?php echo __('admin.common.button.return_to_list'); ?></span>
			</div>
		</div>
		<!-- 右側 -->
		<div class="mobile-preview">
			<header class="header-container flexbox-x-axis">
				<h4 class="font-standard font-family-v2" style="margin: 0 auto 0 0;"><?php echo __('admin.surveydesc.label.mobile_preview'); ?></h4>
			</header>
			<main class="main-container">
				<div class="survey-main">
					<header class="main-header" style="background-color: <?php echo $config['theme-bk-color']?>"></header>
					<!-- 回答画面 -->
					<?php if ($post['page'] == 'input' || $post['page'] == NULL) { ?> 
						<div class="title-container flexbox-x-axis">
							<div class="desc-preview-logo"><img src="<?php echo $logo_url . '?' . $_item_upd_timestamp ?>" alt="" class="survey-preview-icon"></div>
							<h4 class="font-standard font-family-v3 font-size-v2 js-preview-title"style="white-space: pre-wrap;word-wrap: break-word;"><?php echo $post['title'] ?></h4>
						</div>
						<img src="<?php echo $post['survey_image'];if ($post['survey_image']!='') echo('?' . $_item_upd_timestamp)?>" alt="" id="preview" class="survey-preview-header-image js-preview-header-image">
						<section class="contents-container js-preview-main-container">
							<div>
								<h4 class="font-standard font-size-v3 font-family-v4"><?php echo $preview_title ?></h4>
								<p class="font-standard font-size-v4 font-family-v1 font-color-v2 survey-space-top-3 js-preview-description"style="white-space: pre-wrap;word-wrap: break-word;"><?php echo $post['description'] ?></p>
							</div>
							<div>
								<h4 class="font-standard font-size-v3 font-family-v4"><?php echo $preview_survey_period ?></h4>
								<p>
									<span class="font-standard font-size-v4 font-family-v1 font-color-v2 survey-space-top-3 js-preview-start-date"><?php echo $survey->start_date==null?'':str_replace('-', '/',substr($survey->start_date, 0, 16)) ?></span>
									<span class="font-standard font-size-v4 font-family-v1 font-color-v2 survey-space-top-3"> 〜 </span>
									<span class="font-standard font-size-v4 font-family-v1 font-color-v2 survey-space-top-3 js-preview-end-date"><?php echo $survey->end_date==null?'':str_replace('-', '/',substr($survey->end_date, 0, 16)); ?></span>
								</p>
							</div>
							<div>
								<h4 class="font-standard font-size-v3 font-family-v4"><?php echo $preview_input_time_cost_label ?></h4>
								<p class="font-standard font-size-v4 font-family-v1 font-color-v2 survey-space-top-3"><span class="js-preview-required-time"><?php echo $preview_input_time_cost_value ?></span></p>
							</div>
							<div class="js-preview-extra-container"></div>
						</section>
						<div class="js-preview-description-container for-preview-clone">
							<h4  class="font-standard font-size-v3 font-family-v4 js-preview-description-title"></h4>
							<p class="font-standard font-size-v4 font-family-v1 font-color-v2 survey-space-top-3 js-preview-extra-description"></p>
						</div>
						<div class="font-standard font-size-v4 font-family-v1 font-color-v3 survey-preview-notes"><?php echo $preview_notes ?></div>
					<!-- 完了画面 -->
					<?php } else if ($post['page'] == 'complete') { ?> 
						<div class="">
							<!-- 受付完了メッセージ -->
							<div class="survey-content-complete" style="border: none;">
								<div class="js-preview-complete-image">
									<img class = "js-preview-complete-image-img" src="/assets/common/images/icon_complete.svg" alt="" style="width:60px;height:60px;">
								</div>
								<p class="js-preview-complete-title" style="white-space: pre-wrap;word-wrap: break-word;font-weight: 500;font-size: 15px;"><?php echo $post['complete_area']['title']; ?></p>
								<div class="complete-message js-preview-complete-summary-contents" style="padding:0 34px;white-space: pre-wrap;word-wrap: break-word;text-align: center;"><?php echo $post['complete_area']['description'];?></div>
								<div class="js-preview-complete-image">
									<img class = "js-preview-complete-image-img" src="<?php echo $post['complete_area']['icon']?>" alt="" style="max-width:180px;max-height:135px;">
								</div>
								<a style="text-decoration: none !important; display: none;" href="<?php echo $post['actions'][0]['url']; ?>" class="js-complete-btn cloned">
									<div class="complete-btn primary-btn complete-submit-button js-preview-action-btn" style="background:<?php echo $config['primary-btn-bk-color']; ?>; color:<?php echo $config['primary-btn-color']; ?>;">
										<?php echo $post['actions'][0]['title']; ?>
									</div>
								</a>
							</div>
					<?php } ?>
				</div>
			</main>

			<?php
				// 回答画面
				if ($post['page'] == 'input' || $post['page'] == NULL) {

					echo('<footer class="footer-survey-container flexbox-center" style="background-color:' . $config['theme-bk-color']. '">');
					echo('<p class="font-standard font-size-v3 font-family-v3 font-color-v4">' . __('survey.index.button.answer') . '</p>');
					echo('</footer>');
				}
				//完了画面の場合
				else {
					echo('<footer class="footer-container flexbox-center" style="background: #fff;">');
					echo('</footer>');
				}
			?>
		</div>
	</div>
</div>