<style>
	.js-select-presents .pulldown {
		margin: 0 2px 0 0;
		background: #f6f7f9;
		border-radius: 4px 4px 0 0;
		border: solid #e3e5e8;
		border-width: 1px 1px 0 1px;
		list-style: none;
		font-size: 12px;
		height: 36px;
	}
</style>

<script>
	window.addEventListener('DOMContentLoaded', function(){
		// クーポン利用状況のプルダウンから「クーポン利用状況」を非表示
		$(".js-select-presents").find(".js-dropdown-option").each(function(i, e){
			if($(e).attr("data-value") == "01"){
				$(e).hide();
			}
		});
		// 指定のクーポン結果に遷移
		$(document).on("click", ".js-select-presents .js-dropdown-option", function (e) {
			Metronic.blockUI({animate: true});
			window.location.href = 'couponresult?id=' + $(e.target).attr("data-value");
		});
	});
</script>

<nav class="top-nav">
	<ul class="">
		<?php if ($survey_id == NULL) { ?>
			<li class="<?php if ($_action == 'surveys') echo('active'); ?>">
				<a href="/<?php echo $_path?>/surveys">
				アンケート一覧</a>
			</li>
		<?php }?>
		<?php if ($_user->role_cd != '73') {?>
		<li class="<?php if ($_action == 'survey') echo('active'); ?>">
			<?php if ($survey_id != NULL) {
				echo('<a href="/' . $_path . '/survey?id=' . $survey_id . '">');
				echo __('admin.surveymenu.label.survey');
				echo('</a>');
			}
			else {
				echo('<a href="/' . $_path . '/survey">');
				echo __('admin.surveymenu.label.new');
				echo('</a>');
			}?>
		</li>
		<?php }?>
		<?php if ($survey_id != NULL) { ?>
			<!-- php if survey_id != NULL で条件分岐 -->
			<?php if ($_user->role_cd != '73') {?>
			<li class="<?php if ($_action == 'surveydesc') echo('active'); ?>">
				<?php 
					echo('<a href="/' . $_path . '/surveydesc?id=' . $survey_id . '">');
					echo __('admin.surveymenu.label.desc');
					echo('</a>');
				?>
			</li>
			<li class="<?php if ($_action == 'surveyentry') echo('active'); ?>">
				<?php 
					echo('<a href="/' . $_path . '/surveyentry?id=' . $survey_id . '">');
					echo __('admin.surveymenu.label.entry');
					echo('</a>');
				?>
			</li>
			<?php }?>
			<?php if ($_user->role_cd=='99' || in_array($_path . "/surveyresult", $_user_function)) {?>
			<li class="<?php if ($_action == 'surveyresult') echo('active'); ?>">
				<?php 
					if ($_user->role_cd == '73') {
						echo('<a href="/' . $_path . '/surveyresult?id=' . $survey_id . '">');
					}
					else {
						echo('<a href="/' . $_path . '/surveyresult?id=' . $survey_id . '">');
					}
					echo __('admin.surveymenu.label.result');
					echo('</a>');
				?>
			</li>
			<?php }?>
			<li class="<?php if ($_action == 'surveyreport' || $_action == 'surveyreport73') echo('active'); ?>">
				<?php 
					if ($_user->role_cd == '73') {
						echo('<a href="/' . $_path . '/surveyreport73?id=' . $survey_id . '">');
					}
					else {
						echo('<a href="/' . $_path . '/surveyreport?id=' . $survey_id . '">');
					}
					echo __('admin.surveymenu.label.report');
					echo('</a>');
				?>
			</li>
			<?php if ($_user->role_cd != '73') {?>
			<?php 
			if ($_user->role_cd=='99' || in_array($_path . "/couponresult", $_user_function)) {
				$orm = ORM::factory('survey', $survey_id);
				$adminsurveymodel = new Model_Adminsurveymodel();
				$presentData = $adminsurveymodel->get_undeleted_coupons($orm->present);
			}{
			?>
				<?php if($presentData){?>
					<div class="talkappi-pulldown js-select-presents" data-name="present" data-value="01" data-source='<?php echo json_encode($presentData) ?>'></div>
				<?php }?>
			<?php 
				}
			?>
			<li class="<?php if ($_action == 'surveys') echo('active'); ?>">
				<a href="/<?php echo $_path?>/surveys">
					<?php echo __('admin.surveymenu.label.surveys'); ?>
				</a>
			</li>
		<?php }}?>
	</ul>
</nav>
