<style>
.table-scrollable {
    overflow-x: unset;
    overflow-y: unset;
}
</style>

<input type="hidden" id="act" name="act" value="" />
<input type="hidden" id="item_div" name="item_div" value="<?php echo $item_div ?>" />
<input type="hidden" id="class_div" name="class_div" value="<?php echo $div_item_class ?>" />
<input type="hidden" id="class_cd_cond" name="class_cd_cond" value="<?php echo $post['class_cd_cond'] ?>" />
<input type="hidden" id="survey_id" name="survey_id" value="" />

<div class="content-container light-gray">
    <div class="form-group">
        <label class="control-label col-md-1" <?php if ($json_next_survey_setting['user_in_charge_required'] != 1) echo('style="display:none;"')?>><?php echo __('admin.common.label.person_in_charge') ?></label>
        <div class="col-md-3" <?php if ($json_next_survey_setting['user_in_charge_required'] != 1) echo('style="display:none;"')?>>
            <?php echo Form::select('user_in_charge', $user_list, $post['user_in_charge'], array('id'=>'user_in_charge','class'=>'form-control select2me'))?>
        </div>
        <label class="control-label col-md-1" style="white-space: nowrap;"><?php echo __('admin.common.label.template') ?></label>
        <div class="col-md-3">
            <div class="talkappi-pulldown" data-name="template_cd" data-value="<?php echo $post['template_cd']?>" data-source='<?php echo json_encode($template_list, JSON_UNESCAPED_UNICODE) ?>'></div>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-md-1"><?php echo __('admin.common.label.classification_tags') ?></label>
        <div class="col-md-3">
            <select name="class_cd" class="bs-select form-control" multiple>
            <?php foreach($code_div_dict as $k=>$v) {
                echo('<option value="' . $k . '">' . $v .'</option>');
            }?>
            </select>
        </div>   
        <label class="control-label col-md-1"><?php echo __('admin.common.label.period') ?></label>
        <div class="col-md-3">
            <div class="talkappi-datepicker-range">
                <input name="start_date" value="<?php echo ($post['start_date']) ?>"/><p>〜</p>
                <input name="end_date" value="<?php echo ($post['end_date']) ?>"/>
            </div>
        </div>
        <div class="col-md-1">
            <span class="btn-smaller btn-yellow js-search"><i class="fa fa-search mr10"></i><?php echo __('admin.common.button.search') ?></span>
        </div>	
    </div>
</div>

<div class="content-container" style="padding-left: 0;">
    <div style="display: flex;justify-content: end;">
        <span class="btn-smaller btn-blue js-new-survey">
            <span class="icon-add-white"></span>
            <?php echo __('admin.common.button.create_new') ?>
        </span>
    </div>
</div>

<div class="content-container white">
    <div class="react-adminsurvey-surveys" data-lang_cd="<?php echo $_lang_cd ?>"></div>
</div>

<?php
$columnItems = [];

foreach ($items as $item) {
    if ($item['scene_cd'] == '') $item['scene_cd'] =  $_bot->facility_cd;
    if (isset($json_next_survey_setting['base_url'])) {
        $public_url = $json_next_survey_setting['base_url'];
    }
    else {
        $public_url = 'https://survey.talkappi.com';
    }
    if (strpos($public_url, 'https://survey.talkappi.com') === 0) {
        if (isset($json_next_survey_setting['param_id'])) {
            $param_id = trim($json_next_survey_setting['param_id']) ?: 'id';
            $public_url = $public_url . '?f=' . $item['scene_cd'] . '&' . $param_id . '=' . $item['survey_cd'];
        }
        else {
            $public_url = $public_url . '?f=' . $item['scene_cd'];
        }
    }
    else {
        $param_id = isset($json_next_survey_setting['param_id']) ? trim($json_next_survey_setting['param_id']) : 'id';
        $public_url = $public_url . '?' . $param_id . '=' . $item['survey_cd'];
    }
    if (array_key_exists('login', $json_next_survey_setting)) {
        $class_arr = explode(' ', $item['class_cd']);
        foreach($class_arr as $c) {
            if (is_array($json_next_survey_setting['login']) && in_array($c, $json_next_survey_setting['login'])) {
                $public_url = $public_url . '&' . $json_next_survey_setting['param_login'];
                break;
            }
        }
    }

    // 分類タグの生成
    $category_tags = [];
    foreach (explode(' ', $item['class_cd']) as $class_cd) {
        if ($class_cd != '') {
            $category_tags[] = $code_div_dict[$class_cd] ?? __('admin.common.label.unknown');
        }
    }

    // カラムのアイテム
    $columnItems[] = [
        "path"=> $_path,
        "public_url"=>$public_url,
        "lang_cd"=>$_lang_cd,
        "survey_id" => $item["survey_id"],
        "code" => $item["survey_cd"],
        "survey_name" =>  $item["survey_name"],
        "isCoupon" => count($item["present"]) !== 0,
        "period" => ($item["start_date"] ? substr($item["start_date"], 0, 10) : '') . '〜' . ($item["end_date"] ? substr($item["end_date"], 0, 10) : ''),
        "category" => $category_tags,
        "count" => array_key_exists($item["survey_id"], $results) ? $results[$item["survey_id"]] : '',
        "user_name" => $item["user_name"],
        "operation" => '', //operationList
    ];
}
    // ヘッダー
    $headers_for_table = [
        [
            'Header' => __('admin.common.label.code'),
            'accessor' => 'code',
            'style' => ['width' => '5rem', 'whiteSpace' => 'nowrap']
        ],
        [
            'Header' => __('admin.surveys.label.name'),
            'accessor' => 'survey',
            'style' => ['width' => '10rem', 'wordBreak' => 'normal']
        ],
        [
            'Header' => __('admin.common.label.period_of_execution'),
            'accessor' => 'period',
            'style' => ['width' => '5rem', 'wordBreak' => 'normal']
        ],
        [
            'Header' => __('admin.common.label.classification_tags'),
            'accessor' => 'category',
            'style' => ['width' => '10rem']
        ],
        [
            'Header' => __('admin.common.label.number_of_answers'),
            'accessor' => 'count',
            'style' => ['textAlign' => 'center', 'width' => '3rem']
        ],
        // '担当者' ヘッダーを追加
        [
            'Header' => __('admin.common.label.operation'),
            'accessor' => 'operation',
            'style' => ['textAlign' => '-webkit-center', 'width' => '3rem']
        ]
    ];

    if ($json_next_survey_setting['user_in_charge_required'] == 1) {
        $addHeader = [
            'Header' => __('admin.common.label.person_in_charge'),
            'accessor' => 'user_name',
            'style' => ['width' => '5rem', 'wordBreak' => 'break-all']
        ];
        array_splice($headers_for_table, 5, 0, [$addHeader]);
    }
?>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/1.12.4/jquery.min.js"></script>
<script type="text/javascript" src="/assets/common/react/pages/adminsurvey/surveys.bundle.js"></script>

<script type="text/javascript">
const _columns_for_table = <?php echo json_encode($columnItems) ?>;
const _headers_for_table = <?php echo json_encode($headers_for_table) ?>;
  jQuery(document).ready(function($){
      window.talkappi_setupSurveysPage({
        tableColumns: _headers_for_table,
        tableData: Object.values(_columns_for_table)
      })});
</script>
