<input type="hidden" id="class_cd_cond" name="class_cd_cond" value="<?php echo $post['class_cd_cond'] ?>" />
<input type="hidden" id="act" name="act" value="" />
<input type="hidden" id="coupon_id" name="coupon_id" value="" />

<div class="content-container light-gray">
  <div class="form-group">
    <label class="control-label col-md-1" <?php if ($json_next_survey_setting['user_in_charge_required'] != 1) echo ('style="display:none;"') ?>>担当者</label>
    <div class="col-md-2" <?php if ($json_next_survey_setting['user_in_charge_required'] != 1) echo ('style="display:none;"') ?>>
      <?php echo Form::select('user_in_charge', $user_list, $post['user_in_charge'], array('class' => 'form-control js_user_in_charge select2me')) ?>
    </div>
  </div>
  <div class="form-group">
    <label class="control-label col-md-1">分類</label>
    <div class="col-md-2 surveys-pulldown">
      <select name="class_cd" class="bs-select form-control surveys-pulldown" multiple>
        <?php foreach ($code_div_dict as $k => $v) {
          echo ('<option value="' . $k . '">' . $v . '</option>');
        } ?>
      </select>
    </div>
  </div>
</div>

<div class="content-container" style="padding-left: 0;">
  <div class="flex-x-between">
    <button type="button" class="btn-smaller btn-blue js-new-coupon" style="margin: 0 0 0 auto;">
      <span class="icon-add-white"></span>新規作成
    </button>
  </div>
</div>

<div class="content-container table" style="position: relative;">
  <table class="table table-bordered table-hover js-data-table">
    <div style="display:flex;position: absolute; top: 36px; align-items: center;">
      <input type="text" class="talkappi-datepicker js-date" name="start_date" value="<?php echo ($post['start_date']) ?>" />
      <span class="mr10">〜</span>
      <input type="text" class="talkappi-datepicker js-date" name="end_date" value="<?php echo ($post['end_date']) ?>" />
    </div>
    <thead style="background-color: #F6F7F9;">
      <tr>
        <th style="width:100px;">コード</th>
        <th>クーポン名</th>
        <th style="width:240px;">利用期間</th>
        <th style="width:80px;">発行回数</th>
        <th style="width:80px;">利用回数 </th>
        <th style="width:128px;">更新日時</th>
        <th style="width:240px;">操作</th>
      </tr>
    </thead>
    <tbody>
      <?php
      foreach ($items as $item) { ?>
        <tr class="gradeX odd" role="row">
          <td>
            <?php echo ($item['coupon_id']); ?>
          </td>
          <td>
            <a class="link-animate" href="coupon?id=<?php echo ($item['coupon_id']) ?>">
              <?php if ($item['coupon_name'] == NULL) echo ("未命名");
              else echo ($item['coupon_name']) ?></a>
          </td>
          <td>
            <?php

            $startDate = date_format(date_create($item['start_date']?$item['start_date']:'1970/01/01'), 'Y/m/d');
            $endDate = date_format(date_create($item['end_date']?$item['end_date']:'1970/01/01'), 'Y/m/d');

            if ($startDate != '1970/01/01' && $endDate != '1970/01/01') {
              echo ($startDate . ' 〜 ' . $endDate);
            } 
            
            if ($startDate == '1970/01/01' && $endDate != '1970/01/01') {
              echo (' 〜 ' . $endDate);
            } 
            
            if ($startDate != '1970/01/01' && $endDate == '1970/01/01') {
              echo ($startDate . ' 〜 ');
            } 
            
            if ($startDate == '1970/01/01' && $endDate == '1970/01/01') {
              echo '';
            };
            ?>
          </td>
          <td style="text-align:right;">
            <?php if ($results != null) { ?>
              <?php if (array_key_exists($item['coupon_id'], $results)) { ?>
                <?php echo ($results[$item['coupon_id']]) ?><span style="margin-left:5px;">回</span>
              <?php } ?>
            <?php } ?>
          </td>
          <td style="text-align:right;">
            <?php if ($use_results != null) { ?>
              <?php if (array_key_exists($item['coupon_id'], $use_results)) { ?>
                <?php echo ($use_results[$item['coupon_id']]) ?><span style="margin-left:5px;">回</span>
              <?php } ?>
            <?php } ?>
          </td>
          <td style="text-align:left;">
              <?php echo (substr($item['upd_time'], 0, 19) . '<br/>' . $item['name']) ?>
          </td>          
          <td style="display:flex;">
            <div class="btn round image edit js-edit" id="<?php echo $item['coupon_id']; ?>">編集</div>
            <div class="btn round image copy js-copy" id="<?php echo $item['coupon_id']; ?>">コピー</div>
            <div class="btn round image result js-result" id="<?php echo $item['coupon_id']; ?>">利用状況</div>
          </td>
        </tr>
      <?php } ?>
    </tbody>
  </table>
</div>