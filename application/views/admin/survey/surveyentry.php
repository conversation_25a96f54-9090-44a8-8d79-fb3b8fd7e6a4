<!-- BEGIN PAGE HEADER-->
<!-- BEGIN PAGE CONTENT-->

<!-- 共通：ページ遷移タブ(基本設定、多言語情報、アンケート質問) -->
<script>
	var _user_template_flg = <?php echo $user_template_flg?>;
	var _survey_entries = <?php echo $entries?>;
	var _survey_sections = <?php echo $sections?>;
	var _survey_branchs = <?php echo $branchs?>;
	var _template_survey_entries = <?php echo $template_entries?>;
	var _lang_display = <?php echo json_encode($lang_display)?>;
	var _survey_coupons = <?php echo json_encode($coupons)?>;
	var _coupon_setting = <?php echo json_encode($present, JSON_UNESCAPED_UNICODE) ?>;
	const _item_upd_timestamp = '<?php echo str_replace(['-',' ',':'], '', $survey->upd_time) ?>';
</script>

</script>

<input type="hidden" id="survey_entries" name="survey_entries" value='>' />
<input type="hidden" id="survey_sections" name="survey_sections" value='' />
<input type="hidden" id="survey_branchs" name="survey_branchs" value='' />
<input type="hidden" id="lang_cd" name="lang_cd" value='<?php echo $lang_cd?>' />
<input type="hidden" id="message" name="message" value='<?php echo $message?>' />
<input type="hidden" name="survey_id" value="<?php echo $survey_id?>" />
<input type="hidden" id="coupon_setting" name="coupon_setting" value='<?php echo $present?>' />

<style>
	.fleq-text,
	.fleq-text p {
		max-width: calc(200px - 1.25rem);
		height: 36px;
		line-height: 36px;
		overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;
	}
	.pulldown-list-children2 {
		top: auto;
	}
	.chk-limit-num div {
		margin: 0 6px 0 0;
	}
	.js-dropdown-option[data-value="-1"]{
		display: none;
	}
	.long-text-container {
		display: flex;
		flex-direction: column;
	}
</style>

<!-- summernote -->
<link rel="stylesheet" type="text/css" href="/assets/global/plugins/bootstrap-wysihtml5/bootstrap-wysihtml5.css"/>
<link rel="stylesheet" type="text/css" href="/assets/global/plugins/bootstrap-markdown/css/bootstrap-markdown.min.css">

<?php echo $menu?>
<div class="edit-container">
	<!-- アンケート質問 -->
	<div class="survey-survey-questions-container flexbox js-survey-survey-questions-container">
		<div class="left-container js-left-container sectionSortable"  data-fold-survey="false">
			<div class="flex-x-between" style="padding-right:30px;">
				<nav class="line-tab">
					<ul style="flex-flow: wrap; gap: 6px 0px;">
						<?php
							$display_lang_arr = explode(',', $lang_display);
							foreach($display_lang_arr as $k=>$v) {
								if ($v == $lang_cd) {
									echo('<li class=" active">');
								}
								else {
									echo('<li>');
								}
								echo('<a class="func-menu" href="/' . $_path . '/surveyentry?id=' . $survey_id . '&lang='. $v . '">' . $_codes['02'][$v] . '</a></li>');
							}
						?>
					</ul>
				</nav>			
				<?php if (count($translate_from_lang) > 0) { ?>
				<div style="display:flex;">
					<div class="talkappi-pulldown js-tranlate-from-lang" data-name="translate_from_lang" data-value="" data-blank-text="<?php echo __('admin.surveydesc.label.base_language') ?>" data-source='<?php echo(json_encode($translate_from_lang, JSON_UNESCAPED_UNICODE)) ?>'></div>
					<button type="button" class="btn-smaller btn-blue js-tranlate"><?php echo __('admin.surveydesc.label.automatic_translate') ?></button>
				</div>
				<?php }?>
			</div>
			<div class="survey-space-top-1 not-sortable">
				<!-- アンケート名 -->
				<div class="survey-survey-questions-lines-container">
					<h4 class="font-standard font-family-v1 survey-space-top-bottom-1" style="width: 109px;"><?php echo __('survey.index.title.name') ?></h4>
					<div class="survey-title-input-container relative">
						<p type="text" class="survey-title-input flexbox-x-axis js-survey-title-input"><?php if(isset($survey_title)) echo($survey_title) ?></p>
						<a class="font-standard font-family-v2 font-color-v1 survey-position-absolute-v1 js-edit-desc"><?php echo __('admin.common.button.edit') ?></a>
					</div>
				</div>
				<!-- アンケート説明 -->
				<div class="survey-survey-questions-lines-container">
					<h4 class="font-standard font-family-v1 survey-space-top-bottom-1" style="width: 109px;"><?php echo __('survey.index.label.content') ?></h4>
					<div class="survey-summary-textarea-container flexbox-x-axis relative" style="white-space: pre-wrap;word-wrap: break-word;">
						<div name=""  cols="" rows="" class="survey-summary-textarea" style="min-height: 28px;"><?php if(isset($survey_description)) echo($survey_description) ?></div>
						<a class="font-standard font-family-v2 font-color-v1 survey-position-absolute-v1 js-edit-desc"><?php echo __('admin.common.button.edit') ?></a>
					</div>
				</div>
				<div class="survey-questions-border"></div>
			</div>
			<!-- 質問編集セクション -->
			<div class="survey-editing-title-container js-survey-editing-title-container flexbox-x-axis survey-space-around-5 not-sortable">
				<h4 class="font-standard font-family-v4 left-aligned"><?php echo __('survey.inquiry.common.edit') ?></h4>
				<div class="js-icon-fold-survey flexbox-x-axis pointer survey-space-around-4">
					<div class="survey-fold-icon">
						<span class="icon-fold-open"></span>
					</div>
					<span class="font-standard font-family-v1"><?php echo __('survey.inquiry.common.list') ?></span>
				</div>
			</div>
			<section class="survey-editing-container js-survey-editing-container cloned" data-secno="1" data-fold-section="false" id="sortable1">
				<!-- セクションの番号 -->
				<div class="survey-section-num js-survey-section-num display-none">
					<?php echo __('survey.inquiry.common.label.section') ?>
					<span class="js-section-numerator"></span>
					/
					<span class="js-section-denominator"></span>
				</div>
				<!-- セクションのタイトルコンテナ -->
				<section class="js-section-title-container flexbox not-sortable display-none">
					<input type="text" class="survey-space-all-around-5 text-input-longer js-section-title">
					<div class="js-num-of-surveys font-standard font-family-v4 survey-space-top-bottom-3"></div>
					<div class="flexbox-x-axis js-section-icon-container pointer" style="margin:12px 0 0 auto;">
						<div class="survey-btn" title="<?php echo __('survey.inquiry.common.section.delete') ?>">
							<span class="icon-delete delete-icon js-survey-delete-section-icon"></span>
						</div>
						<div class="survey-btn js-jump-icon-container js-jump-section-icon-container" title="<?php echo __('survey.inquiry.common.branch.add') ?>">
							<img src="./../assets/admin/css/img/icon-jump-unactive.svg" width="12" height="12" class="js-edidind-survey-jump-unactive-icon">
							<img src="./../assets/admin/css/img/icon-jump-active.svg" width="12" height="12" class="js-edidind-survey-jump-active-icon" style="display: none;">
						</div>
						<div class="js-survey-fold-section" style="width:auto;" title="<?php echo __('survey.inquiry.common.section.fold_up') ?>">
							<span class="icon-fold-section-open"></span>
						</div>
						<!-- 分岐追加のモーダルウィンドウ -->
						<div class="survey-branch-modal-container js-survey-questions-jump-section-setting-container display-none">
							<h4 class="font-standard font-size-v5 font-family-v4"><?php echo __('survey.inquiry.common.branch.set') ?></h4>
							<p class="font-standard font-size-v3 font-family-v4 font-color-v1 survey-space-all-around"><?php echo __('survey.inquiry.common.branch.destination') ?><span class="js-current-survey"></span></p>
							<div class="modal-container-context">
								<div class="survey-branch-container js-branch-setting-container cloned">
									<div class="flexbox-x-axis">
										<input type="text" class="text-input-longer survey-space-all-around-5 js-branch-setting-title">
										<span class="icon-delete right-aligned js-delete-icon" style="width: 50px; cursor: pointer;"></span>
									</div>
									<div class="survey-space-all-around-4 js-add-branch-container">
										<h4 class="font-standard font-family-v4"><?php echo __('admin.common.label.conditions') ?></h4>
										<div class="add-branch-pulldown-container js-add-pulldown-container">
											<div class="dropdown-container dropdown-longer js-question-dropdown">
												<div class="dropdown-selected">
													<span class="dropdown-selected-text"><?php echo __('survey.inquiry.common.branch.select.title') ?></span>
													<span class="icon-drop-down-close"></span>
												</div>
												<ul class="dropdown-options survey-width-fit dropdown-longer">
													<li class="dropdown-option js-survey-add-branch-modal-select-survey-title for-clone display-none"></li>
												</ul>
											</div>
											<div class="dropdown-container dropdown-longer js-answer-dropdown">
												<div class="dropdown-selected">
													<span class="dropdown-selected-text"><?php echo __('survey.inquiry.common.branch.select.answer') ?></span>
													<span class="icon-drop-down-close"></span>
												</div>
												<ul class="dropdown-options survey-width-fit dropdown-longer">
													<li class="dropdown-option js-survey-add-branch-modal-select-survey-option for-clone display-none"></li>
												</ul>
											</div>
											<div class="dropdown-container dropdown-longer js-branch-option-container" style="min-width: auto;">
												<div class="dropdown-selected">
													<span class="dropdown-selected-text" data-option="equal"><?php echo __('survey.branch.select.equal') ?></span>
													<span class="icon-drop-down-close"></span>
												</div>
												<ul class="dropdown-options survey-width-fit dropdown-longer"style="min-width: auto;">
													<li class="dropdown-option" data-option="equal"><?php echo __('survey.branch.select.equal') ?></li>
													<li class="dropdown-option" data-option="not_equal"><?php echo __('survey.branch.select.not_equal') ?></li>
													<li class="dropdown-option" data-option="include"><?php echo __('survey.branch.select.include') ?></li>
													<li class="dropdown-option" data-option="not_include"><?php echo __('survey.branch.select.not_include') ?></li>
													<li class="dropdown-option" data-option="greater"><?php echo __('survey.branch.select.greater') ?></li>
													<li class="dropdown-option" data-option="smaller"><?php echo __('survey.branch.select.smaller') ?></li>
													<li class="dropdown-option" data-option="equal_or_greater"><?php echo __('survey.branch.select.equal_or_greater') ?></li>
													<li class="dropdown-option" data-option="equal_or_smaller"><?php echo __('survey.branch.select.equal_or_smaller') ?></li>
												</ul>
											</div>
											<div class="btn-smaller btn-gray-white js-add-button">
												<span class="icon-add-white icon-add-white-svg"></span>
												<?php echo __('admin.itme.item.classification_add') ?>
											</div>
										</div>
										<div>
											<span class="font-standard font-family-v1 font-color-v3 js-text"><?php echo __('survey.inquiry.common.branch.add.and') ?></span>
											<div class="survey-space-all-around-3 flexbox-inline-x-axis icon-round-corners-small icon-background-light-blue js-added-branch-condition for-clone display-none">
												<span class="font-standard font-family-v1 flexbox-x-axis survey-space-right-1" style="overflow: hidden;"></span>
												<img src="./../assets/admin/css/img/icon-cancel-small.svg" class="js-delete-icon" width="12" height="12">
											</div>
											<span class="font-standard font-family-v1 font-color-v3 js-omit-next" style="display: none">･･･</span>
										</div>
									</div>
									<!-- 分岐条件削除時のモーダルウィンドウ -->
									<div class="modal-smaller-container js-survey-delete-container" style="display: none;">
										<div class="modal-small-title-container">
											<h4 class="modal-small-title"><?php echo __('survey.inquiry.common.branch.delete') ?></h4>

											<span class="icon-cancel-large survey-modal-close-button js-survey-modal-close-button"></span>
										</div>
										<p style="margin: 32px 0 0 10px;"><?php echo __('admin.common.modal.delete.confirm') ?></p>
										<div class="submit-btn-container" style="position: fixed; bottom: 12px;">
											<div class="btn-larger btn-red js-survey-modal-delete-branch-button"><?php echo __('admin.common.button.delete') ?></div>
											<div class="btn-larger btn-white survey-modal-cancel-button js-survey-modal-branch-cancel-button"><?php echo __('admin.common.button.cancel') ?></div>
										</div>
									</div>
									<div class="modal-background js-survey-questions-modal-background display-none"></div>
								</div>
								<div class="survey-branch-container js-branch-setting-container for-clone display-none">
									<div class="flexbox-x-axis">
										<input type="text" class="text-input-longer survey-space-all-around-5 js-branch-setting-title">
										<span class="icon-delete right-aligned js-delete-icon" style="width: 50px; cursor: pointer;"></span>
									</div>
									<div class="survey-space-all-around-4 js-add-branch-container">
										<h4 class="font-standard font-family-v4"><?php echo __('admin.common.label.conditions') ?></h4>
										<div class="add-branch-pulldown-container js-add-pulldown-container">
											<div class="dropdown-container dropdown-longer js-question-dropdown">
												<div class="dropdown-selected">
													<span class="dropdown-selected-text"><?php echo __('survey.inquiry.common.branch.select.title') ?></span>
													<span class="icon-drop-down-close"></span>
												</div>
												<ul class="dropdown-options survey-width-fit dropdown-longer">
													<li class="dropdown-option js-survey-add-branch-modal-select-survey-title for-clone display-none"></li>
												</ul>
											</div>
											<div class="dropdown-container dropdown-longer js-answer-dropdown">
												<div class="dropdown-selected">
													<span class="dropdown-selected-text"><?php echo __('survey.inquiry.common.branch.select.answer') ?></span>
													<span class="icon-drop-down-close"></span>
												</div>
												<ul class="dropdown-options survey-width-fit dropdown-longer">
													<li class="dropdown-option js-survey-add-branch-modal-select-survey-option for-clone display-none"></li>
												</ul>
											</div>
											<div class="dropdown-container dropdown-longer js-branch-option-container" style="min-width: auto;">
												<div class="dropdown-selected">
													<span class="dropdown-selected-text" data-option="equal"><?php echo __('survey.branch.select.equal') ?></span>
													<span class="icon-drop-down-close"></span>
												</div>
												<ul class="dropdown-options survey-width-fit dropdown-longer"style="min-width: auto;">
													<li class="dropdown-option" data-option="equal"><?php echo __('survey.branch.select.equal') ?></li>
													<li class="dropdown-option" data-option="not_equal"><?php echo __('survey.branch.select.not_equal') ?></li>
													<li class="dropdown-option" data-option="include"><?php echo __('survey.branch.select.include') ?></li>
													<li class="dropdown-option" data-option="not_include"><?php echo __('survey.branch.select.not_include') ?></li>
													<li class="dropdown-option" data-option="greater"><?php echo __('survey.branch.select.greater') ?></li>
													<li class="dropdown-option" data-option="smaller"><?php echo __('survey.branch.select.smaller') ?></li>
													<li class="dropdown-option" data-option="equal_or_greater"><?php echo __('survey.branch.select.equal_or_greater') ?></li>
													<li class="dropdown-option" data-option="equal_or_smaller"><?php echo __('survey.branch.select.equal_or_smaller') ?></li>
												</ul>
											</div>
											<div class="btn-smaller btn-gray-white js-add-button">
												<span class="icon-add-white icon-add-white-svg"></span>
												<?php echo __('admin.itme.item.classification_add') ?>
											</div>
										</div>
										<div>
											<span class="font-standard font-family-v1 font-color-v3 js-text"><?php echo __('survey.inquiry.common.branch.add.and') ?></span>
											<div class="survey-space-all-around-3 flexbox-inline-x-axis icon-round-corners-small icon-background-light-blue js-added-branch-condition for-clone display-none">
												<span class="font-standard font-family-v1 flexbox-x-axis survey-space-right-1" style="overflow: hidden;"></span>
												<img src="./../assets/admin/css/img/icon-cancel-small.svg" class="js-delete-icon" width="12" height="12">
											</div>
											<span class="font-standard font-family-v1 font-color-v3 js-omit-next" style="display: none">･･･</span>
										</div>
									</div>
									<!-- 分岐条件削除時のモーダルウィンドウ -->
									<div class="modal-smaller-container js-survey-delete-container" style="display: none;">
										<div class="modal-small-title-container">
											<h4 class="modal-small-title"><?php echo __('survey.inquiry.common.branch.delete') ?></h4>

											<span class="icon-cancel-large survey-modal-close-button js-survey-modal-close-button"></span>
										</div>
										<p style="margin: 32px 0 0 10px;"><?php echo __('admin.common.modal.delete.confirm') ?></p>
										<div class="submit-btn-container" style="position: fixed; bottom: 12px;">
											<div class="btn-larger btn-red js-survey-modal-delete-branch-button"><?php echo __('admin.common.button.delete') ?></div>
											<div class="btn-larger btn-white survey-modal-cancel-button js-survey-modal-branch-cancel-button"><?php echo __('admin.common.button.cancel') ?></div>
										</div>
									</div>
									<div class="modal-background js-survey-questions-modal-background display-none"></div>
									</div>
								<div class="btn-add-branch js-add-branch-modal-add-container-button">
									<p>
									<span class="font-standard font-size-v3 font-family-v3 font-color-v1 survey-space-around-4"><?php echo __('survey.inquiry.common.branch.label.or') ?></span>
									<span class="icon-add"></span>
										<?php echo __('survey.inquiry.common.branch.add') ?></p>
								</div>
							</div>
							<div class="submit-btn-container" style="margin: 20px 0 12px 0;">
								<div class="btn-larger btn-blue js-survey-modal-add-button active"><?php echo __('survey.inquiry.common.branch.set_btn') ?></div>
								<div class="btn-larger btn-white js-survey-modal-cancel-button"><?php echo __('admin.common.button.cancel') ?></div>
								<div class="btn-larger btn-red-border js-delete-all-modal-branch">
									<span class="icon-delete"></span>
								</div>
							</div>
						</div>
						<div class="modal-background js-survey-questions-jump-setting-modal-background display-none"></div>
					</div>
					<!-- コンテナ削除時のモーダルウィンドウ -->
					<div class="modal-smaller-container js-survey-delete-container" style="display: none;">
						<div class="modal-small-title-container">
							<h4 class="modal-small-title"><?php echo __('survey.inquiry.common.section.delete.confirm') ?></h4>
							<span class="icon-cancel-large survey-modal-close-button js-survey-modal-close-button"></span>
						</div>
						<p style="margin: 32px 0 0 12px;"><?php echo __('admin.common.modal.delete.confirm') ?></p>
						<div class="submit-btn-container" style="position: fixed; bottom: 12px;">
							<div class="btn-larger btn-red js-survey-modal-delete-section-button"><?php echo __('admin.common.button.delete') ?></div>
							<div class="btn-larger btn-white js-survey-modal-close-button"><?php echo __('admin.common.button.cancel') ?></div>
						</div>
					</div>
					<div class="modal-background js-survey-questions-modal-background display-none"></div>
				</section>
				<!-- 質問入力コンテナ -->
				<div class="survey-input-container js-survey-input-container cloned">
					<div class="survey-editing-order-select flexbox-x-axis pointer js-fold-item">
						<span class="js-survey-editing-order-num left-aligned">1</span>
						<span class="icon-fold-open"></span>
					</div>
					<div class="survey-editing-input-container js-survey-editing-input-container" id="survey-editing-container1">
						<!-- タイトルコンテナ -->
						<div class="title-container js-title-container js-survey-questions-title-focus" style="position:relative;     margin: 0px 24px;">
							<span class="icon-drag survey-icon-draggable"></span>
							<input type="text" class="survey-title js-survey-title js-survey-title-input" placeholder="<?php echo __('survey.inquiry.common.item.placeholder.title') ?>">
							<div class="preview-contents survey-title js-survey-title"></div>
							<div class="js-to-open-summernote-editor to-open-summernote-editor flexbox-x-axis display-none">
								<span class="icon-form-zoom-in"></span>
								<span><?php echo __('survey.inquiry.common.item.to_edit_HTML') ?></span>
							</div>
							<!-- リッチテキストエディタ -->
							<div class="survey-branch-modal-container js-survey-questions-input-title-modal rich-text-editor display-none">
								<div class="flexbox flexbox-baselines">
									<h1 class="font-standard font-size-v5 font-family-v4"style="margin:0 auto 24px 0;"><?php echo __('survey.inquiry.common.item.edit_HTML.title') ?></h1>
									<span class="icon-cancel-large js-survey-modal-cancel-button pointer"></span>
								</div>
								<textarea type="text" class="survey-title js-survey-title-editor"></textarea>
								<div class="submit-btn-container" style="position: fixed; bottom: 12px;">
									<div class="btn-larger btn-blue js-suevey-save-title js-survey-modal-add-button"><?php echo __('survey.inquiry.common.branch.set_btn') ?></div>
									<div class="btn-larger btn-white js-survey-modal-cancel-button"><?php echo __('admin.common.button.cancel') ?></div>
								</div>
							</div>
							<div class="modal-background js-survey-questions-input-title-modal-background display-none"></div>
							<!-- 質問形式の選択プルダウン -->
							<div class="dropdown-container dropdown-middle">
								<div class="dropdown-selected">
									<span class="dropdown-selected-text"><?php echo __('survey.inquiry.common.opt') ?></span>
									<span class="icon-drop-down-close"></span>
								</div>
								<ul class="dropdown-options dropdown-middle" style="top: -10px;">
									<li class="dropdown-option current-type js-survey-type-single">
										<span class="icon-form-single-option-on-large"></span><?php echo __('survey.inquiry.common.opt') ?>
									</li>
									<li class="dropdown-option js-survey-type-multiple">
										<span class="icon-form-multi-option-on"></span><?php echo __('survey.inquiry.common.chk') ?>
									</li>
									<li class="dropdown-option js-survey-type-pulldown">
										<span class="icon-form-pulldown-option-on"></span><?php echo __('survey.inquiry.common.sel') ?>
									</li>
									<li class="dropdown-option js-survey-type-short-text">
										<span class="icon-form-writing-short-text"></span><?php echo __('survey.inquiry.common.txt') ?>
									</li>
									<li class="dropdown-option js-survey-type-long-text">
										<span class="icon-form-writing-text"></span><?php echo __('survey.inquiry.common.txa') ?>
									</li>
									<li class="dropdown-option js-survey-type-attachment">
										<span class="icon-form-upload"></span><?php echo __('survey.inquiry.common.fup') ?>
									</li>
									<li class="dropdown-option js-survey-type-freespace">
										<span class="icon-form-fq"></span><?php echo __('survey.inquiry.common.frs') ?>
									</li>
									<li class="dropdown-option js-survey-type-matrix">
										<span class="icon-form-matrix-option-on"></span><?php echo __('survey.inquiry.common.mtx') ?>
								  </li>
									<!-- よく聞く質問 -->
									<li class="dropdown-option js-pulldown-parent relative">
										<span class="icon-form-fleq" style="margin: 0 8px 0 0;"></span>	
										<?php echo __('survey.inquiry.common.fleq') ?>
										<span class="pulldown-more"></span>
										<ul class="pulldown-list-children2">
											<li class="dropdown-option js-survey-type-address">
												<span class="icon-edit-bar-add-q"></span><?php echo __('survey.inquiry.common.spl.address.prefecture_city') ?>
											</li>
										</ul>
									</li>
								</ul>
							</div>
						</div>
						<div class="js-options-main-container">
							<!-- 選択肢のコンテナ -->
							<div class="width-100 js-options-container">
								<!-- 選択肢1 -->
								<div class="flexbox-x-axis width-100 survey-space-top-1 js-option-container" style="padding: 0 24px;">
									<div class="js-radio">
										<span class="icon-form-single-option-off"></span>
									</div>
									<div class="js-checkbox display-none">
										<span class="icon-check"></span>
									</div>
									<div class="pulldown-num js-pulldown-num display-none" ></div>
									<input type="text" class="width-100 border-none survey-space-around-2 survey-height-32 js-option-input" onpaste="return false" placeholder="<?php echo (str_replace('{num}', "1", __('survey.inquiry.common.item.option.placeholder'))) ?>">
									<div class="js-add-image-input-container"></div>
									<input name="file" type="hidden" style="display:none;" class="js-input-image-to-save">
									<div class="delete-icon js-delete-icon js-delete-option pointer display-none">
										<span class="icon-cancel-small"></span>
									</div>
								</div>
								<!-- 選択肢2 -->
								<div class="flexbox-x-axis width-100 survey-space-top-1 js-option-container" style="padding: 0 24px;">
									<div class="js-radio">
										<span class="icon-form-single-option-off"></span>
									</div>
									<div class="js-checkbox display-none">
										<span class="icon-check"></span>
									</div>
									<div class="pulldown-num js-pulldown-num display-none" ></div>
									<input type="text" class="width-100 border-none survey-space-around-2 survey-height-32 js-option-input" onpaste="return false" placeholder="<?php echo (str_replace('{num}', "2", __('survey.inquiry.common.item.option.placeholder'))) ?>">
									<div class="js-add-image-input-container"></div>
									<input name="file" type="hidden" style="display:none;" class="js-input-image-to-save">
									<div class="delete-icon js-delete-icon js-delete-option pointer display-none">
										<span class="icon-cancel-small"></span>
									</div>
								</div>
								<!-- 選択肢コピー用 -->
								<div class="flexbox-x-axis width-100 survey-space-top-1 js-option-container for-clone display-none" style="padding: 0 24px;">
									<div class="js-radio">
										<span class="icon-form-single-option-off"></span>
									</div>
									<div class="js-checkbox display-none">
										<span class="icon-check"></span>
									</div>
									<div class="pulldown-num js-pulldown-num display-none" ></div>
									<input type="text" class="width-100 border-none survey-space-around-2 survey-height-32 js-option-input" onpaste="return false" placeholder="<?php echo (str_replace('{num}', "2", __('survey.inquiry.common.item.option.placeholder'))) ?>">
									<div class="js-add-image-input-container"></div>
									<input name="file" type="hidden" style="display:none;" class="js-input-image-to-save">
									<div class="delete-icon js-delete-icon js-delete-option pointer display-none">
										<span class="icon-cancel-small"></span>
									</div>
								</div>
								<!-- その他 -->
								<div class="flexbox-x-axis survey-space-top-1 js-other-container not-sortable display-none" style="padding: 0px 24px;">
									<div class="js-radio">
										<span class="icon-form-single-option-off"></span>
									</div>
									<div class="js-checkbox display-none">
										<span class="icon-check"></span>
									</div>
									<div class="pulldown-num js-pulldown-num display-none"></div>
									<p class="js-option-other survey-width-50 survey-space-around-2"><?php echo __('survey.common.label.other') ?></p>
									<form action="#" method="post" class="checkbox-small-v1-container js-survey-checkbox-container js-option-other-checkbox">
										<span class="font-color-v3"><?php echo __('survey.common.label.other.input') ?></span>
										<span class="right-aligned"><?php echo __('survey.common.label.required') ?></span>
										<label class="js-label checkbox-small-v1-label-off">
											<span class="js-span checkbox-small-v1-span-off"></span>
										</label>
										<input type="checkbox" name="" value="" class="display-none">
									</form>
									<div class="pointer js-delete-icon js-other-delete-icon display-none">
										<span class="icon-cancel-small"></span>
									</div>
								</div>
								<!-- 「選択肢」または「その他」を追加する -->
								<div class="survey-space-top-1 flexbox-x-axis js-add-option-or-other-container not-sortable display-none" style="padding: 0px 24px;">
									<div class="js-radio">
										<span class="icon-form-single-option-off"></span>
									</div>
									<div class="js-checkbox display-none">
										<span class="icon-check"></span>
									</div>
									<div class="pulldown-num js-pulldown-num display-none" ></div>
									<div class="flexbox-x-axis survey-space-left-1">
										<span class="font-standard font-family-v1 font-color-v1 pointer js-survey-link-add"><?php echo __('survey.inquiry.common.item.add.option') ?></span>
										<span class="font-standard font-family-v1 font-color-v2 survey-space-around-1 js-survey-text"><?php echo __('survey.inquiry.common.item.option.or') ?></span>
										<span class="font-standard font-family-v1 font-color-v1 pointer js-survey-link-other"><?php echo __('survey.inquiry.common.item.add.other') ?></span>
									</div>
								</div>
							</div>
							<!-- 短文のコンテナ -->
							<div class="survey-space-top-1 width-100 js-short-text-container display-none" style="padding: 0px 24px;">
								<input type="text" class="short-text-input" placeholder="<?php echo __('survey.inquiry.common.txt') ?>" readonly>
								<div class="width-100">
									<div class="survey-space-top-1 flexbox-x-axis width-100">
										<div class="dropdown-container dropdown-middle">
											<div class="dropdown-selected">
												<span class="dropdown-selected-text"><?php echo __('survey.inquiry.common.txt.text') ?></span>
												<span class="icon-drop-down-close"></span>
											</div>
											<ul class="dropdown-options dropdown-middle">
												<li class="dropdown-option js-survey-select-input-type-of-short-text js-survey-short-text-dont-need-num"><?php echo __('survey.inquiry.common.txt.postcode') ?></li>
												<li class="dropdown-option js-survey-select-input-type-of-short-text js-survey-short-text-dont-need-num"><?php echo __('admin.common.label.phone_number') ?></li>
												<li class="dropdown-option js-survey-select-input-type-of-short-text js-select-mail"><?php echo __('admin.common.label.mail.address') ?></li>
												<li class="dropdown-option js-survey-select-input-type-of-short-text js-survey-short-text-dont-need-num"><?php echo __('admin.common.label.date') ?></li>
												<li class="dropdown-option js-survey-select-input-type-of-short-text js-survey-short-text-need-num"><?php echo __('survey.inquiry.common.txt.text') ?></li>
												<li class="dropdown-option js-survey-select-input-type-of-short-text js-survey-short-text-dont-need-num"><?php echo __('survey.inquiry.common.txt.num') ?></li>
											</ul>
										</div>
										<div class="right-aligned flexbox-x-axis js-short-text-count-container">
											<div class="survey-space-left-2 js-short-text-count js-short-text-count-min"><?php echo __('survey.inquiry.common.txa.min') ?>　<input class ="survey-short-text-input" type="text"></div>
											<div class="survey-space-left-2 js-short-text-count js-short-text-count-max"><?php echo __('survey.inquiry.common.txa.max') ?>　<input class ="survey-short-text-input" type="text"></div>
										</div>
										<div class="right-aligned flexbox-x-axis js-check-mail-confirm pointer" style="display: none;">
											<span class="icon-check js-icon-check"></span>
											<p> <?php echo __('survey.inquiry.common.txt.reconfirm.mail') ?></p>
										</div>
										<div class="right-aligned flexbox-x-axis js-send-mail pointer" style="display: none;">
											<span class="icon-check js-send-mail-check"></span>
											<p> <?php echo __('survey.inquiry.common.txt.mail.sendmail') ?></p>
										</div>
										<div class="right-aligned flexbox-x-axis js-short-text-emotion-analytics-container">
											<div class="survey-space-left-2" style="display:inline-flex;">
												<?php echo __('survey.inquiry.common.txt.txa.emotion_analytics') ?>　
												<div class="talkappi-switch js-short-text-emotion-analytics" data-value="0"></div>
											</div>
										</div>
									</div>
									<div class="survey-space-top-1 flexbox-x-axis js-short-text-privacy-masking-container">
										<div class="survey-space-left-2" style="display:inline-flex; margin: 0 0 0 auto;">
											<?php echo __('survey.inquiry.common.txt.txa.privacy_masking') ?>　
											<div class="talkappi-switch js-short-text-privacy-masking" data-value="0"></div>
										</div>
									</div>
								</div>
							</div>
							<!-- 長文のコンテナ -->
							<div class="long-text-container js-long-text-container display-none" style="padding: 0px 24px;">
								<input type="text" class="survey-space-top-1 long-text-input" placeholder="<?php echo __('survey.inquiry.common.txa.placeholder') ?>" readonly>
								<div class="right-aligned flexbox-x-axis">
									<div class="survey-space-left-2 js-long-text-count-min"><?php echo __('survey.inquiry.common.txa.min') ?>　<input class ="survey-short-text-input" type="text"></div>
									<div class="survey-space-left-2 js-long-text-count-max"><?php echo __('survey.inquiry.common.txa.max') ?>　<input class ="survey-short-text-input" type="text"></div>
									<div class="survey-space-left-2" style="display:inline-flex;">
										<?php echo __('survey.inquiry.common.txt.txa.emotion_analytics') ?>　
										<div class="talkappi-switch js-long-text-emotion-analytics" data-value="0"></div>
									</div>
								</div>
							</div>
							<!-- ファイルアップロードのコンテナ -->
							<div class="survey-attachment-container survey-space-top-1 js-attachment-container display-none" style="padding: 0px 24px;">
								<p class="font-standard font-family-v1 font-color-v3 survey-space-top-1 flexbox-x-axis">
									<span class="icon-form-upload"></span><?php echo __('survey.inquiry.common.fup') ?>
								</p>
								<div style="display: flex;gap: 17px;padding: 1rem;">
									<?php echo $upload_limit_size . __('admin.common.label.file_upload_limit_size')?>,
									<?php echo __('admin.common.label.file_upload_extension') . " : " . $extensions ?>
								</div>
							</div>
							<!-- フリースペースのコンテナ -->
							<div class="survey-freespace-container survey-space-top-1 js-freespace-container display-none">
							</div>
							<!-- マトリクスのコンテナ -->
							<div class="survey-space-top-5 js-matrix-container display-none">
								<!-- タイトルのコンテナ -->
								<div class="matrix-rows-container" style="padding: 0px 24px;">
									<div>
										<p class="survey-space-top-bottom-7"><?php echo __('survey.inquiry.common.mtx.title.column') ?></p>
									</div>
									<div class="sortable-matrix">
										<!-- タイトル（列）1 -->
										<div class="flexbox-x-axis width-100 js-matrix-row-container matrix-column-container survey-space-top-3">
											<span class="icon-drag matrix-icon-draggable"></span>
											<div class="pulldown-num js-pulldown-num"></div>
											<input type="text" class="width-100 border-none survey-space-around-2 survey-height-28 mtx-option-input mtx-option-y-input" onpaste="return false" placeholder="<?php echo (str_replace('{num}', "1", __('survey.inquiry.common.mtx.title.num'))) ?>">
											<div class="delete-icon js-delete-icon js-delete-option pointer">
												<span class="icon-cancel-small"></span>
											</div>
										</div>
										<!-- タイトル（列）2 -->
										<div class="flexbox-x-axis width-100 js-matrix-row-container matrix-column-container survey-space-top-3">
											<span class="icon-drag matrix-icon-draggable"></span>
											<div class="pulldown-num js-pulldown-num"></div>
											<input type="text" class="width-100 border-none survey-space-around-2 survey-height-28 mtx-option-input mtx-option-y-input" onpaste="return false" placeholder="<?php echo (str_replace('{num}', "2", __('survey.inquiry.common.mtx.title.num'))) ?>">
											<div class="delete-icon js-delete-icon js-delete-option pointer">
												<span class="icon-cancel-small"></span>
											</div>
										</div>
										<!-- タイトル（列）コピー用-->
										<div class="flexbox-x-axis width-100 js-matrix-row-container matrix-column-container survey-space-top-3 for-clone display-none">
											<span class="icon-drag matrix-icon-draggable"></span>
											<div class="pulldown-num js-pulldown-num"></div>
											<input type="text" class="width-100 border-none survey-space-around-2 survey-height-28 mtx-option-input mtx-option-y-input" onpaste="return false" placeholder="<?php echo (str_replace('{num}', "2", __('survey.inquiry.common.mtx.title.num'))) ?>">
											<div class="delete-icon js-delete-icon js-delete-option pointer">
												<span class="icon-cancel-small"></span>
											</div>
										</div>
									</div>
									<!-- タイトルを追加する -->
									<div class="flexbox-x-axis width-100  js-add-matrix-row survey-space-top-5">
									    <div class="pulldown-num js-pulldown-num"></div>
										<div class="flexbox-x-axis survey-space-left-1">
											<span class="font-standard font-family-v1 font-color-v1 survey-space-left-3 pointer js-row-add"><?php echo __('survey.inquiry.common.mtx.add.title') ?></span>
										</div>
									</div>
								</div>

								<!-- 選択肢（行）のコンテナ -->
								<div class="matrix-column-container" style="padding: 0px 24px;">
									<div>
										<p class="survey-space-top-bottom-6"><?php echo __('survey.inquiry.common.mtx.option.column') ?></p>
									</div>
									<div class="sortable-matrix">
										<!-- 選択肢（行）1 -->
										<div class="flexbox-x-axis width-100 js-matrix-column-container matrix-column-container survey-space-top-3">
											<span class="icon-drag matrix-icon-draggable"></span>
											<div class="js-radio"><span class="icon-form-single-option-off"></span></div>
											<div class="js-checkbox display-none"><span class="icon-check"></span></div>
											<input type="text" class="width-100 border-none survey-space-around-2 survey-height-28 mtx-option-input mtx-option-x-input" onpaste="return false" placeholder="<?php echo (str_replace('{num}', "1", __('survey.inquiry.common.item.option.placeholder'))) ?>">
											<div class="delete-icon js-delete-icon js-delete-option pointer">
												<span class="icon-cancel-small"></span>
											</div>
										</div>
										<!-- 選択肢（行）2 -->
										<div class="flexbox-x-axis width-100 js-matrix-column-container matrix-column-container survey-space-top-3">
											<span class="icon-drag matrix-icon-draggable"></span>
											<div class="js-radio"><span class="icon-form-single-option-off"></span></div>
											<div class="js-checkbox display-none"><span class="icon-check"></span></div>
											<input type="text" class="width-100 border-none survey-space-around-2 survey-height-28 mtx-option-input mtx-option-x-input" onpaste="return false" placeholder="<?php echo (str_replace('{num}', "2", __('survey.inquiry.common.item.option.placeholder'))) ?>">
											<div class="delete-icon js-delete-icon js-delete-option pointer">
												<span class="icon-cancel-small"></span>
											</div>
										</div>
										<!-- 選択肢（行）コピー用 -->
										<div class="flexbox-x-axis width-100 js-matrix-column-container matrix-column-container survey-space-top-3 for-clone display-none">
											<span class="icon-drag matrix-icon-draggable"></span>
											<div class="js-radio"><span class="icon-form-single-option-off"></span></div>
											<div class="js-checkbox display-none"><span class="icon-check"></span></div>
											<input type="text" class="width-100 border-none survey-space-around-2 survey-height-28 mtx-option-input mtx-option-x-input" onpaste="return false" placeholder="<?php echo (str_replace('{num}', "2", __('survey.inquiry.common.item.option.placeholder'))) ?>">
											<div class="delete-icon js-delete-icon js-delete-option pointer">
												<span class="icon-cancel-small"></span>
											</div>
										</div>
									</div>
									<!-- 選択肢を追加する -->
									<div class="flexbox-x-axis width-100  js-add-matrix-column survey-space-top-5">
									    <div class="js-radio"><span class="icon-form-single-option-off"></span></div>
										<div class="js-checkbox display-none"><span class="icon-check"></div>
										<div class="flexbox-x-axis survey-space-left-1">
											<span class="font-standard font-family-v1 font-color-v1 pointer survey-space-left-3 js-column-add"><?php echo __('survey.inquiry.common.item.add.option') ?></span>
										</div>
									</div>
									<!-- 単一選択or複数選択 -->
									<div class="survey-space-top-1 flexbox-x-axis width-100">
										<div class="dropdown-container dropdown-shorter js-single-multiple">
											<div class="dropdown-selected">
												<span class="dropdown-selected-text"><?php echo __('survey.inquiry.common.opt') ?></span>
												<span class="icon-drop-down-close"></span>
											</div>
											<ul class="dropdown-options dropdown-shorter" style="top: -10px;">
												<li class="dropdown-option js-select-type-of-matrix js-select-type-of-matrix-dont-need-num"><span class="icon-form-single-option-on-large"></span><?php echo __('survey.inquiry.common.opt') ?></li>
												<li class="dropdown-option js-select-type-of-matrix js-select-type-of-matrix-need-num"><span class="icon-form-multi-option-on"></span><?php echo __('survey.inquiry.common.chk') ?></li>
											</ul>
										</div>
										<!-- 最低選択 -->
										<div class="right-aligned flexbox-x-axis js-matrix-count-container display-none">
											<div class="js-matrix-count-min dropdown-container dropdown-shorter">
												<div class="dropdown-selected js-dropdown-matrix-min">
													<span class="dropdown-selected-text"><?php echo __('survey.inquiry.common.item.select.min') ?></span>
													<span class="icon-drop-down-close"></span>
												</div>
												<ul class="dropdown-options dropdown-shorter" style="top: -30px;">
													<li class="dropdown-option js-matrix-type-single"><?php echo __('survey.inquiry.common.item.select.min') ?></li>
													<li class="dropdown-option js-matrix-type-single" data-min="1"><?php echo (str_replace('{num}', "1", __('survey.inquiry.common.mtx.select.num'))) ?></li>
													<li class="dropdown-option js-matrix-type-single display-none" data-min="2"><?php echo (str_replace('{num}', "2", __('survey.inquiry.common.mtx.select.num'))) ?></li>
													<li class="dropdown-option js-matrix-type-single display-none" data-min="3"><?php echo (str_replace('{num}', "3", __('survey.inquiry.common.mtx.select.num'))) ?></li>
													<li class="dropdown-option js-matrix-type-single display-none" data-min="4"><?php echo (str_replace('{num}', "4", __('survey.inquiry.common.mtx.select.num'))) ?></li>
													<li class="dropdown-option js-matrix-type-single display-none" data-min="5"><?php echo (str_replace('{num}', "5", __('survey.inquiry.common.mtx.select.num'))) ?></li>
													<li class="dropdown-option js-matrix-type-single display-none" data-min="6"><?php echo (str_replace('{num}', "6", __('survey.inquiry.common.mtx.select.num'))) ?></li>
													<li class="dropdown-option js-matrix-type-single display-none" data-min="7"><?php echo (str_replace('{num}', "7", __('survey.inquiry.common.mtx.select.num'))) ?></li>
													<li class="dropdown-option js-matrix-type-single display-none" data-min="8"><?php echo (str_replace('{num}', "8", __('survey.inquiry.common.mtx.select.num'))) ?></li>
												</ul>
											</div>
											<!-- 最大選択 -->
											<div class="js-matrix-count-max matrix-count-max dropdown-container dropdown-shorter">
												<div class="dropdown-selected js-dropdown-matrix-max">
													<span class="dropdown-selected-text"><?php echo __('survey.inquiry.common.item.select.max') ?></span>
													<span class="icon-drop-down-close"></span>
												</div>
												<ul class="dropdown-options dropdown-shorter" style="top: -30px;">
													<li class="dropdown-option js-matrix-type-multiple"><?php echo __('survey.inquiry.common.item.select.max') ?></li>
												    <li class="dropdown-option js-matrix-type-multiple" data-min="2"><?php echo (str_replace('{num}', "2", __('survey.inquiry.common.mtx.select.num'))) ?></li>
													<li class="dropdown-option js-matrix-type-multiple display-none" data-min="3"><?php echo (str_replace('{num}', "3", __('survey.inquiry.common.mtx.select.num'))) ?></li>
												    <li class="dropdown-option js-matrix-type-multiple display-none" data-min="4"><?php echo (str_replace('{num}', "4", __('survey.inquiry.common.mtx.select.num'))) ?></li>
													<li class="dropdown-option js-matrix-type-multiple display-none" data-min="5"><?php echo (str_replace('{num}', "5", __('survey.inquiry.common.mtx.select.num'))) ?></li>
													<li class="dropdown-option js-matrix-type-multiple display-none" data-min="6"><?php echo (str_replace('{num}', "6", __('survey.inquiry.common.mtx.select.num'))) ?></li>
													<li class="dropdown-option js-matrix-type-multiple display-none" data-min="7"><?php echo (str_replace('{num}', "7", __('survey.inquiry.common.mtx.select.num'))) ?></li>
													<li class="dropdown-option js-matrix-type-multiple display-none" data-min="8"><?php echo (str_replace('{num}', "8", __('survey.inquiry.common.mtx.select.num'))) ?></li>
													<li class="dropdown-option js-matrix-type-multiple display-none" data-min="9"><?php echo (str_replace('{num}', "9", __('survey.inquiry.common.mtx.select.num'))) ?></li>
												</ul>
											</div>
										</div>
									</div>
								</div>
							</div>
							<!-- 都道府県 + 市区町村：よくある質問のコンテナ -->
							<div class="survey-address-container survey-space-top-1 js-address-container display-none focusout" style="padding: 0px 24px;">
								<div class="flexbox-x-axis survey-space-top-1">
									<div class="dropdown-middle background-pale-gray flexbox-x-axis" style="margin:0 12px 0 0;padding:0 0 0 12px ;height:32px;"><?php echo __('survey.inquiry.common.txt.postcode') ?></div>
									<input type="text" class="survey-title js-input-column js-address-postcode font-standard font-family-v1 font-color-v3" placeholder="123-4567">
								</div>
								<div class="flexbox-x-axis survey-space-top-1">
									<div class="dropdown-middle background-pale-gray flexbox-x-axis" style="margin:0 12px 0 0;padding:0 0 0 12px ;height:32px;"><?php echo __('survey.inquiry.common.txt.prefecture') ?></div>
									<input type="text" class="survey-title js-input-column js-address-prefecture font-standard font-family-v1 font-color-v3" value="<?php echo __('survey.input.text.sel') ?>">
								</div>
								<div class="flexbox-x-axis survey-space-top-1">
									<div class="dropdown-middle background-pale-gray flexbox-x-axis" style="margin:0 12px 0 0;padding:0 0 0 12px ;height:32px;"><?php echo __('survey.inquiry.common.txt.city') ?></div>
									<input type="text" class="survey-title js-input-column js-address-municipalities font-standard font-family-v1 font-color-v3" placeholder="<?php echo __('survey.inquiry.common.txt.city.placeholder') ?>">
								</div>
								<!-- <div class="flexbox-x-axis survey-space-top-1">
									<div class="dropdown-middle background-pale-gray flexbox-x-axis" style="margin:0 12px 0 0;padding:0 0 0 12px ;height:32px;">番地</div>
									<input type="text" class="survey-title js-input-column js-address-houseNumber font-standard font-family-v1 font-color-v3" placeholder="12-34">
								</div>
								<div class="flexbox-x-axis survey-space-top-1">
									<div class="dropdown-middle background-pale-gray flexbox-x-axis" style="margin:0 12px 0 0;padding:0 0 0 12px ;height:32px;">建物名・部屋番号</div>
									<input type="text" class="survey-title js-input-column js-address-roomNumber font-standard font-family-v1 font-color-v3" placeholder="Aマンション 301号">
								</div> -->
							</div>
						</div>
						<!-- 削除、回答必須のコンテナ -->
						<div class="submit-btn-container js-button-container" style="margin:32px 0 0 auto; width: fit-content; width: -moz-fit-content; padding: 0 24px 0 0;">
							<div class="survey-btn" title="<?php echo __('survey.inquiry.common.delete.item') ?>">
								<span class="icon-delete js-edidind-survey-delete-icon"></span>
							</div>
							<!-- 項目コピー -->
							<div class="survey-btn js-copy-icon-container" title="<?php echo __('admin.common.label.item_copy') ?>">
								<img src="./../assets/admin/css/img/icon-copy-dark.svg" width="12" height="12">
							</div>
							<!-- 画像追加 -->
							<div class="survey-btn js-add-photo-icon" title="<?php echo __('survey.inquiry.common.item.image.add.title') ?>">
								<img src="./../assets/admin/css/img/icon-photo.svg" width="12" height="12" class="js-icon-photo-active" style="display:none;">
								<img src="./../assets/admin/css/img/icon-photo-unactive.svg" width="12" height="12" class="js-icon-photo-unactive">
							</div>
							<div class="survey-btn js-jump-icon-container" title="<?php echo __('survey.inquiry.common.branch.add') ?>">
								<img src="./../assets/admin/css/img/icon-jump-unactive.svg" width="12" height="12" class="js-edidind-survey-jump-unactive-icon">
								<img src="./../assets/admin/css/img/icon-jump-active.svg" width="12" height="12" class="js-edidind-survey-jump-active-icon" style="display: none;">
							</div>
							<div class="survey-btn js-from-page-icon-container" title="<?php echo __('survey.inquiry.common.item.title.new_page') ?>">
								<span class="icon-form-page-unactive js-edidind-survey-from-page-unactive-icon"></span>
								<span class="icon-form-page-active js-edidind-survey-from-page-active-icon display-none"></span>
							</div>
							<form action="#" method="post" class="js-survey-checkbox-container">
								<span class=""><?php echo __('survey.inquiry.common.label.required') ?></span>
								<label class="js-label checkbox-small-v1-label-off">
									<span class="js-span checkbox-small-v1-span-off"></span>
								</label>
								<input type="checkbox" name="" value="" class="display-none">
							</form>
						</div>
						<!-- コンテナ削除時のモーダルウィンドウ -->
						<div class="modal-smaller-container js-survey-delete-container" style="display: none;">
							<div class="modal-small-title-container">
								<h4 class="modal-small-title"><?php echo __('survey.inquiry.common.delete.item.title') ?></h4>
								<span class="icon-cancel-large js-survey-modal-close-button"></span>
							</div>
							<p style="margin: 32px 0 0 10px;"><?php echo __('admin.common.modal.delete.confirm') ?></p>
							<div class="submit-btn-container" style="position: fixed; bottom: 12px;">
								<div class="btn-larger btn-red js-survey-modal-delete-button"><?php echo __('admin.common.button.delete') ?></div>
								<div class="btn-larger btn-white js-survey-modal-cancel-button"><?php echo __('admin.common.button.cancel') ?></div>
							</div>
						</div>
						<div class="modal-background js-survey-questions-modal-background display-none"></div>
						<!-- 分岐追加のモーダルウィンドウ -->
						<div class="survey-branch-modal-container js-survey-questions-jump-setting-container display-none">
							<h4 class="font-standard font-size-v5 font-family-v4"><?php echo __('survey.inquiry.common.branch.set') ?></h4>
							<p class="font-standard font-size-v3 font-family-v4 font-color-v1 survey-space-all-around"><?php echo __('survey.inquiry.common.branch.destination') ?><span class="js-current-survey"></span></p>
							<div class="modal-container-context">
								<div class="survey-branch-container js-branch-setting-container cloned">
									<div class="flexbox-x-axis">
										<input type="text" class="text-input-longer survey-space-all-around-5 js-branch-setting-title">
										<span class="icon-delete right-aligned js-delete-icon" style="width: 50px; cursor: pointer;"></span>
									</div>
									<div class="survey-space-all-around-4 js-add-branch-container">
										<h4 class="font-standard font-family-v4"><?php echo __('admin.common.label.conditions') ?></h4>
										<div class="add-branch-pulldown-container js-add-pulldown-container">
											<div class="dropdown-container dropdown-longer js-question-dropdown">
												<div class="dropdown-selected">
													<span class="dropdown-selected-text"><?php echo __('survey.inquiry.common.branch.select.title') ?></span>
													<span class="icon-drop-down-close"></span>
												</div>
												<ul class="dropdown-options survey-width-fit dropdown-longer">
													<li class="dropdown-option js-survey-add-branch-modal-select-survey-title for-clone display-none"></li>
												</ul>
											</div>
											<div class="dropdown-container dropdown-longer js-answer-dropdown">
												<div class="dropdown-selected">
													<span class="dropdown-selected-text"><?php echo __('survey.inquiry.common.branch.select.answer') ?></span>
													<span class="icon-drop-down-close"></span>
												</div>
												<ul class="dropdown-options survey-width-fit dropdown-longer">
													<li class="dropdown-option js-survey-add-branch-modal-select-survey-option for-clone display-none"></li>
												</ul>
											</div>
											<div class="dropdown-container dropdown-longer js-branch-option-container" style="min-width: auto;">
												<div class="dropdown-selected">
													<span class="dropdown-selected-text" data-option="equal"><?php echo __('survey.branch.select.equal') ?></span>
													<span class="icon-drop-down-close"></span>
												</div>
												<ul class="dropdown-options survey-width-fit dropdown-longer"style="min-width: auto;">
													<li class="dropdown-option" data-option="equal"><?php echo __('survey.branch.select.equal') ?></li>
													<li class="dropdown-option" data-option="not_equal"><?php echo __('survey.branch.select.not_equal') ?></li>
													<li class="dropdown-option" data-option="include"><?php echo __('survey.branch.select.include') ?></li>
													<li class="dropdown-option" data-option="not_include"><?php echo __('survey.branch.select.not_include') ?></li>
													<li class="dropdown-option" data-option="greater"><?php echo __('survey.branch.select.greater') ?></li>
													<li class="dropdown-option" data-option="smaller"><?php echo __('survey.branch.select.smaller') ?></li>
													<li class="dropdown-option" data-option="equal_or_greater"><?php echo __('survey.branch.select.equal_or_greater') ?></li>
													<li class="dropdown-option" data-option="equal_or_smaller"><?php echo __('survey.branch.select.equal_or_smaller') ?></li>
												</ul>
											</div>
											<div class="btn-smaller btn-gray-white js-add-button">
												<span class="icon-add-white icon-add-white-svg"></span>
												<?php echo __('admin.itme.item.classification_add') ?>
											</div>
										</div>
										<div>
											<span class="font-standard font-family-v1 font-color-v3 js-text"><?php echo __('survey.inquiry.common.branch.add.and') ?></span>
											<div class="survey-space-all-around-3 flexbox-inline-x-axis icon-round-corners-small icon-background-light-blue js-added-branch-condition for-clone display-none">
												<span class="font-standard font-family-v1 flexbox-x-axis survey-space-right-1" style="overflow: hidden;"></span>
												<img src="./../assets/admin/css/img/icon-cancel-small.svg" class="js-delete-icon" width="12" height="12">
											</div>
											<span class="font-standard font-family-v1 font-color-v3 js-omit-next" style="display: none">･･･</span>
										</div>
									</div>
									<!-- 分岐条件削除時のモーダルウィンドウ -->
									<div class="modal-smaller-container js-survey-delete-container" style="display: none;">
										<div class="modal-small-title-container">
											<h4 class="modal-small-title"><?php echo __('survey.inquiry.common.branch.delete') ?></h4>

											<span class="icon-cancel-large survey-modal-close-button js-survey-modal-close-button"></span>
										</div>
										<p style="margin: 32px 0 0 10px;"><?php echo __('admin.common.modal.delete.confirm') ?></p>
										<div class="submit-btn-container" style="position: fixed; bottom: 12px;">
											<div class="btn-larger btn-red js-survey-modal-delete-branch-button"><?php echo __('admin.common.button.delete') ?></div>
											<div class="btn-larger btn-white survey-modal-cancel-button js-survey-modal-branch-cancel-button"><?php echo __('admin.common.button.cancel') ?></div>
										</div>
									</div>
									<div class="modal-background js-survey-questions-modal-background display-none"></div>
								</div>
								<div class="survey-branch-container js-branch-setting-container for-clone display-none">
									<div class="flexbox-x-axis">
										<span class="font-standard font-size-v3 font-family-v3 font-color-v1 survey-space-all-around-5 branch-or-title"><?php echo __('survey.inquiry.common.branch.label.or') ?></span>
										<input type="text" class="text-input-longer survey-space-top-5 js-branch-setting-title branch-or-title-input">
										<span class="icon-delete right-aligned js-delete-icon" style="width: 50px; cursor: pointer;"></span>
									</div>
									<div class="survey-space-all-around-4 js-add-branch-container">
										<h4 class="font-standard font-family-v4"><?php echo __('admin.common.label.conditions') ?></h4>
										<div class="add-branch-pulldown-container js-add-pulldown-container">
											<div class="dropdown-container dropdown-longer js-question-dropdown">
												<div class="dropdown-selected">
													<span class="dropdown-selected-text"><?php echo __('survey.inquiry.common.branch.select.title') ?></span>
													<span class="icon-drop-down-close"></span>
												</div>
												<ul class="dropdown-options survey-width-fit dropdown-longer">
													<li class="dropdown-option js-survey-add-branch-modal-select-survey-title for-clone display-none"></li>
												</ul>
											</div>
											<div class="dropdown-container dropdown-longer js-answer-dropdown">
												<div class="dropdown-selected">
													<span class="dropdown-selected-text"><?php echo __('survey.inquiry.common.branch.select.answer') ?></span>
													<span class="icon-drop-down-close"></span>
												</div>
												<ul class="dropdown-options survey-width-fit dropdown-longer">
													<li class="dropdown-option js-survey-add-branch-modal-select-survey-option for-clone display-none"></li>
												</ul>
											</div>
											<div class="dropdown-container dropdown-longer js-branch-option-container" style="min-width: auto;">
												<div class="dropdown-selected">
													<span class="dropdown-selected-text" data-option="equal"><?php echo __('survey.branch.select.equal') ?></span>
													<span class="icon-drop-down-close"></span>
												</div>
												<ul class="dropdown-options survey-width-fit dropdown-longer"style="min-width: auto;">
													<li class="dropdown-option" data-option="equal"><?php echo __('survey.branch.select.equal') ?></li>
													<li class="dropdown-option" data-option="not_equal"><?php echo __('survey.branch.select.not_equal') ?></li>
													<li class="dropdown-option" data-option="include"><?php echo __('survey.branch.select.include') ?></li>
													<li class="dropdown-option" data-option="not_include"><?php echo __('survey.branch.select.not_include') ?></li>
													<li class="dropdown-option" data-option="greater"><?php echo __('survey.branch.select.greater') ?></li>
													<li class="dropdown-option" data-option="smaller"><?php echo __('survey.branch.select.smaller') ?></li>
													<li class="dropdown-option" data-option="equal_or_greater"><?php echo __('survey.branch.select.equal_or_greater') ?></li>
													<li class="dropdown-option" data-option="equal_or_smaller"><?php echo __('survey.branch.select.equal_or_smaller') ?></li>
												</ul>
											</div>
											<div class="btn-smaller btn-gray-white js-add-button">
												<span class="icon-add-white icon-add-white-svg"></span>
												<?php echo __('admin.itme.item.classification_add') ?>
											</div>
										</div>
										<div>
											<span class="font-standard font-family-v1 font-color-v3 js-text"><?php echo __('survey.inquiry.common.branch.add.and') ?></span>
											<div class="survey-space-all-around-3 flexbox-inline-x-axis icon-round-corners-small icon-background-light-blue js-added-branch-condition for-clone display-none" style="align-items: center;">
												<span class="font-standard font-family-v1 flexbox-x-axis survey-space-right-1" style="overflow: hidden;"></span>
												<img src="./../assets/admin/css/img/icon-cancel-small.svg" class="js-delete-icon" width="12" height="12">
											</div>
											<span class="font-standard font-family-v1 font-color-v3 js-omit-next" style="display: none">･･･</span>
										</div>
									</div>
									<!-- 分岐条件削除時のモーダルウィンドウ -->
									<div class="modal-smaller-container js-survey-delete-container" style="display: none;">
										<div class="modal-small-title-container">
											<h4 class="modal-small-title"><?php echo __('survey.inquiry.common.branch.delete') ?></h4>

											<span class="icon-cancel-large survey-modal-close-button js-survey-modal-close-button"></span>
										</div>
										<p style="margin: 32px 0 0 10px;"><?php echo __('admin.common.modal.delete.confirm') ?></p>
										<div class="submit-btn-container" style="position: fixed; bottom: 12px;">
											<div class="btn-larger btn-red js-survey-modal-delete-branch-button"><?php echo __('admin.common.button.delete') ?></div>
											<div class="btn-larger btn-white survey-modal-cancel-button js-survey-modal-branch-cancel-button"><?php echo __('admin.common.button.cancel') ?></div>
										</div>
									</div>
									<div class="modal-background js-survey-questions-modal-background display-none"></div>
								</div>
								<div class="btn-add-branch js-add-branch-modal-add-container-button">
									<p>
									<span class="font-standard font-size-v3 font-family-v3 font-color-v1 survey-space-around-4"><?php echo __('survey.inquiry.common.branch.label.or') ?></span>
									<span class="icon-add"></span>
										<?php echo __('survey.inquiry.common.branch.add') ?></p>
								</div>
							</div>
							<div class="submit-btn-container" style="margin: 20px 0 12px 0;">
								<div class="btn-larger btn-blue js-survey-modal-add-button active"><?php echo __('survey.inquiry.common.branch.set_btn') ?></div>
								<div class="btn-larger btn-white js-survey-modal-cancel-button"><?php echo __('admin.common.button.cancel') ?></div>
								<div class="btn-larger btn-red-border js-delete-all-modal-branch">
									<span class="icon-delete"></span>
								</div>
							</div>
						</div>
						<div class="modal-background js-survey-questions-jump-setting-modal-background display-none"></div>
					</div>
				</div>
				<!-- 質問入力コンテナコピー用 -->
				<div class="survey-input-container js-survey-input-container for-clone display-none">
					<div class="survey-editing-order-select flexbox-x-axis pointer js-fold-item">
						<span class="js-survey-editing-order-num left-aligned">1</span>
						<span class="icon-fold-open"></span>
					</div>
					<div class="survey-editing-input-container js-survey-editing-input-container" id="survey-editing-container1">
						<!-- タイトルコンテナ -->
						<div class="title-container js-title-container js-survey-questions-title-focus" style="position:relative;     margin: 0px 24px;">
							<span class="icon-drag survey-icon-draggable"></span>
							<input type="text" class="survey-title js-survey-title js-survey-title-input" placeholder="<?php echo __('survey.inquiry.common.item.placeholder.title') ?>">
							<div class="preview-contents survey-title js-survey-title"></div>
							<div class="js-to-open-summernote-editor to-open-summernote-editor flexbox-x-axis display-none">
								<span class="icon-form-zoom-in"></span>
								<span><?php echo __('survey.inquiry.common.item.to_edit_HTML') ?></span>
							</div>
							<!-- リッチテキストエディタ -->
							<div class="survey-branch-modal-container js-survey-questions-input-title-modal rich-text-editor display-none">
								<div class="flexbox flexbox-baselines">
									<h1 class="font-standard font-size-v5 font-family-v4"style="margin:0 auto 24px 0;"><?php echo __('survey.inquiry.common.item.edit_HTML.title') ?></h1>
									<span class="icon-cancel-large js-survey-modal-cancel-button pointer"></span>
								</div>
								<textarea type="text" class="survey-title js-survey-title-editor"></textarea>
								<div class="submit-btn-container" style="position: fixed; bottom: 12px;">
									<div class="btn-larger btn-blue js-suevey-save-title js-survey-modal-add-button"><?php echo __('survey.inquiry.common.branch.set_btn') ?></div>
									<div class="btn-larger btn-white js-survey-modal-cancel-button"><?php echo __('admin.common.button.cancel') ?></div>
								</div>
							</div>
							<div class="modal-background js-survey-questions-input-title-modal-background display-none"></div>
							<!-- 質問形式の選択プルダウン -->
							<div class="dropdown-container dropdown-middle">
								<div class="dropdown-selected">
									<span class="dropdown-selected-text"><?php echo __('survey.inquiry.common.opt') ?></span>
									<span class="icon-drop-down-close"></span>
								</div>
								<ul class="dropdown-options dropdown-middle" style="top: -10px;">
									<li class="dropdown-option current-type js-survey-type-single">
										<span class="icon-form-single-option-on-large"></span><?php echo __('survey.inquiry.common.opt') ?>
									</li>
									<li class="dropdown-option js-survey-type-multiple">
										<span class="icon-form-multi-option-on"></span><?php echo __('survey.inquiry.common.chk') ?>
									</li>
									<li class="dropdown-option js-survey-type-pulldown">
										<span class="icon-form-pulldown-option-on"></span><?php echo __('survey.inquiry.common.sel') ?>
									</li>
									<li class="dropdown-option js-survey-type-short-text">
										<span class="icon-form-writing-short-text"></span><?php echo __('survey.inquiry.common.txt') ?>
									</li>
									<li class="dropdown-option js-survey-type-long-text">
										<span class="icon-form-writing-text"></span><?php echo __('survey.inquiry.common.txa') ?>
									</li>
									<li class="dropdown-option js-survey-type-attachment">
										<span class="icon-form-upload"></span><?php echo __('survey.inquiry.common.fup') ?>
									</li>
									<!-- フリースペース -->
									<li class="dropdown-option js-survey-type-freespace">
										<span class="icon-form-fq"></span><?php echo __('survey.inquiry.common.frs') ?>
									</li>
									<!-- マトリクス -->
									<li class="dropdown-option js-survey-type-matrix">
										<span class="icon-form-matrix-option-on"></span><?php echo __('survey.inquiry.common.mtx') ?>
									</li>
									<!-- よく聞く質問 -->
									<li class="dropdown-option js-pulldown-parent relative">
										<span class="icon-form-fleq" style="margin: 0 8px 0 0;"></span>	
										<?php echo __('survey.inquiry.common.fleq') ?>
										<span class="pulldown-more"></span>
										<ul class="pulldown-list-children2">
											<li class="dropdown-option js-survey-type-address">
												<span class="icon-edit-bar-add-q"></span><?php echo __('survey.inquiry.common.spl.address.prefecture_city') ?>
											</li>
										</ul>
									</li>
								</ul>
							</div>
						</div>
						<div class="js-options-main-container">
							<!-- 選択肢のコンテナ -->
							<div class="width-100 js-options-container">
								<!-- 選択肢1 -->
								<div class="flexbox-x-axis width-100 survey-space-top-1 js-option-container" style="padding: 0 24px;">
									<div class="js-radio">
										<span class="icon-form-single-option-off"></span>
									</div>
									<div class="js-checkbox display-none">
										<span class="icon-check"></span>
									</div>
									<div class="pulldown-num js-pulldown-num display-none" ></div>
									<input type="text" class="width-100 border-none survey-space-around-2 survey-height-32 js-option-input" onpaste="return false" placeholder="<?php echo (str_replace('{num}', "1", __('survey.inquiry.common.item.option.placeholder'))) ?>">
									<div class="js-add-image-input-container"></div>
									<input name="file" type="hidden" style="display:none;" class="js-input-image-to-save">
									<div class="delete-icon js-delete-icon js-delete-option pointer display-none">
										<span class="icon-cancel-small"></span>
									</div>
								</div>
								<!-- 選択肢2 -->
								<div class="flexbox-x-axis width-100 survey-space-top-1 js-option-container" style="padding: 0 24px;">
									<div class="js-radio">
										<span class="icon-form-single-option-off"></span>
									</div>
									<div class="js-checkbox display-none">
										<span class="icon-check"></span>
									</div>
									<div class="pulldown-num js-pulldown-num display-none" ></div>
									<input type="text" class="width-100 border-none survey-space-around-2 survey-height-32 js-option-input" onpaste="return false" placeholder="<?php echo (str_replace('{num}', "2", __('survey.inquiry.common.item.option.placeholder'))) ?>">
									<div class="js-add-image-input-container"></div>
									<input name="file" type="hidden" style="display:none;" class="js-input-image-to-save">
									<div class="delete-icon js-delete-icon js-delete-option pointer display-none">
										<span class="icon-cancel-small"></span>
									</div>
								</div>
								<!-- 選択肢コピー用 -->
								<div class="flexbox-x-axis width-100 survey-space-top-1 js-option-container for-clone display-none" style="padding: 0 24px;">
									<div class="js-radio">
										<span class="icon-form-single-option-off"></span>
									</div>
									<div class="js-checkbox display-none">
										<span class="icon-check"></span>
									</div>
									<div class="pulldown-num js-pulldown-num display-none" ></div>
									<input type="text" class="width-100 border-none survey-space-around-2 survey-height-32 js-option-input" onpaste="return false" placeholder="<?php echo (str_replace('{num}', "2", __('survey.inquiry.common.item.option.placeholder'))) ?>">
									<div class="js-add-image-input-container"></div>
									<input name="file" type="hidden" style="display:none;" class="js-input-image-to-save">
									<div class="delete-icon js-delete-icon js-delete-option pointer display-none">
										<span class="icon-cancel-small"></span>
									</div>
								</div>
								<!-- その他 -->
								<div class="flexbox-x-axis survey-space-top-1 js-other-container not-sortable display-none" style="padding: 0px 24px;">
									<div class="js-radio">
										<span class="icon-form-single-option-off"></span>
									</div>
									<div class="js-checkbox display-none">
										<span class="icon-check"></span>
									</div>
									<div class="pulldown-num js-pulldown-num display-none"></div>
									<p class="js-option-other survey-width-50 survey-space-around-2"><?php echo __('survey.common.label.other') ?></p>
									<form action="#" method="post" class="checkbox-small-v1-container js-survey-checkbox-container js-option-other-checkbox">
										<span class="font-color-v3"><?php echo __('survey.common.label.other.input') ?></span>
										<span class="right-aligned"><?php echo __('survey.common.label.required') ?></span>
										<label class="js-label checkbox-small-v1-label-off">
											<span class="js-span checkbox-small-v1-span-off"></span>
										</label>
										<input type="checkbox" name="" value="" class="display-none">
									</form>
									<div class="pointer js-delete-icon js-other-delete-icon display-none">
										<span class="icon-cancel-small"></span>
									</div>
								</div>
								<!-- 「選択肢」または「その他」を追加する -->
								<div class="survey-space-top-1 flexbox-x-axis js-add-option-or-other-container not-sortable display-none" style="padding: 0px 24px;">
									<div class="js-radio">
										<span class="icon-form-single-option-off"></span>
									</div>
									<div class="js-checkbox display-none">
										<span class="icon-check"></span>
									</div>
									<div class="pulldown-num js-pulldown-num display-none" ></div>
									<div class="flexbox-x-axis survey-space-left-1">
										<span class="font-standard font-family-v1 font-color-v1 pointer js-survey-link-add"><?php echo __('survey.inquiry.common.item.add.option') ?></span>
										<span class="font-standard font-family-v1 font-color-v2 survey-space-around-1 js-survey-text"><?php echo __('survey.inquiry.common.item.option.or') ?></span>
										<span class="font-standard font-family-v1 font-color-v1 pointer js-survey-link-other"><?php echo __('survey.inquiry.common.item.add.other') ?></span>
									</div>
								</div>
							</div>
							<!-- 短文のコンテナ -->
							<div class="survey-space-top-1 width-100 js-short-text-container display-none" style="padding: 0px 24px;">
								<input type="text" class="short-text-input" placeholder="<?php echo __('survey.inquiry.common.txt') ?>" readonly>
								<div class="width-100">
									<div class="survey-space-top-1 flexbox-x-axis width-100">
										<div class="dropdown-container dropdown-middle">
											<div class="dropdown-selected">
												<span class="dropdown-selected-text"><?php echo __('survey.inquiry.common.txt.text') ?></span>
												<span class="icon-drop-down-close"></span>
											</div>
											<ul class="dropdown-options dropdown-middle">
												<li class="dropdown-option js-survey-select-input-type-of-short-text js-survey-short-text-dont-need-num"><?php echo __('survey.inquiry.common.txt.postcode') ?></li>
												<li class="dropdown-option js-survey-select-input-type-of-short-text js-survey-short-text-dont-need-num"><?php echo __('admin.common.label.phone_number') ?></li>
												<li class="dropdown-option js-survey-select-input-type-of-short-text js-select-mail"><?php echo __('admin.common.label.mail.address') ?></li>
												<li class="dropdown-option js-survey-select-input-type-of-short-text js-survey-short-text-dont-need-num"><?php echo __('admin.common.label.date') ?></li>
												<li class="dropdown-option js-survey-select-input-type-of-short-text js-survey-short-text-need-num"><?php echo __('survey.inquiry.common.txt.text') ?></li>
												<li class="dropdown-option js-survey-select-input-type-of-short-text js-survey-short-text-dont-need-num"><?php echo __('survey.inquiry.common.txt.num') ?></li>
											</ul>
										</div>
										<div class="right-aligned flexbox-x-axis js-short-text-count-container">
											<div class="survey-space-left-2 js-short-text-count js-short-text-count-min"><?php echo __('survey.inquiry.common.txa.min') ?>　<input class ="survey-short-text-input" type="text"></div>
											<div class="survey-space-left-2 js-short-text-count js-short-text-count-max"><?php echo __('survey.inquiry.common.txa.max') ?>　<input class ="survey-short-text-input" type="text"></div>
										</div>
										<div class="right-aligned flexbox-x-axis js-check-mail-confirm pointer" style="display: none;">
											<span class="icon-check js-icon-check"></span>
											<p> <?php echo __('survey.inquiry.common.txt.reconfirm.mail') ?></p>
										</div>
										<div class="right-aligned flexbox-x-axis js-send-mail pointer" style="display: none;">
											<span class="icon-check js-send-mail-check"></span>
											<p> <?php echo __('survey.inquiry.common.txt.mail.sendmail') ?></p>
										</div>
										<div class="right-aligned flexbox-x-axis js-short-text-emotion-analytics-container">
											<div class="survey-space-left-2" style="display:inline-flex;">
												<?php echo __('survey.inquiry.common.txt.txa.emotion_analytics') ?>　
												<div class="talkappi-switch js-short-text-emotion-analytics" data-value="0"></div>
											</div>
										</div>
									</div>
									<div class="survey-space-top-1 flexbox-x-axis js-short-text-privacy-masking-container">
										<div class="survey-space-left-2" style="display:inline-flex; margin: 0 0 0 auto;">
											<?php echo __('survey.inquiry.common.txt.txa.privacy_masking') ?>　
											<div class="talkappi-switch js-short-text-privacy-masking" data-value="0"></div>
										</div>
									</div>
								</div>
							</div>
							<!-- 長文のコンテナ -->
							<div class="long-text-container js-long-text-container display-none" style="padding: 0px 24px;">
								<input type="text" class="survey-space-top-1 long-text-input" placeholder="<?php echo __('survey.inquiry.common.txa.placeholder') ?>" readonly>
								<div class="right-aligned flexbox-x-axis">
									<div class="survey-space-left-2 js-long-text-count-min"><?php echo __('survey.inquiry.common.txa.min') ?>　<input class ="survey-short-text-input" type="text"></div>
									<div class="survey-space-left-2 js-long-text-count-max"><?php echo __('survey.inquiry.common.txa.max') ?>　<input class ="survey-short-text-input" type="text"></div>
									<div class="survey-space-left-2" style="display:inline-flex;">
										<?php echo __('survey.inquiry.common.txt.txa.emotion_analytics') ?>　
										<div class="talkappi-switch js-long-text-emotion-analytics" data-value="0"></div>
									</div>
								</div>
							</div>
							<!-- ファイルアップロードのコンテナ -->
							<div class="survey-attachment-container survey-space-top-1 js-attachment-container display-none" style="padding: 0px 24px;">
								<p class="font-standard font-family-v1 font-color-v3 survey-space-top-1 flexbox-x-axis">
									<span class="icon-form-upload"></span><?php echo __('survey.inquiry.common.fup') ?>
								</p>
								<div style="display: flex;gap: 17px;padding: 1rem;">
									<?php echo $upload_limit_size . __('admin.common.label.file_upload_limit_size')?>,
									<?php echo __('admin.common.label.file_upload_extension') . " : " . $extensions ?>
								</div>
							</div>
							<!-- フリースペースのコンテナ -->
							<div class="survey-freespace-container survey-space-top-1 js-freespace-container display-none">
							</div>
							<!-- マトリクスのコンテナ -->
							<div class="survey-space-top-5 js-matrix-container display-none">
								<!-- タイトル（列）のコンテナ -->
								<div class="matrix-rows-container" style="padding: 0px 24px;">
									<div>
										<p class="survey-space-top-bottom-7"><?php echo __('survey.inquiry.common.mtx.title.column') ?></p>
									</div>
									<div class="sortable-matrix">
										<!-- タイトル（列）1 -->
										<div class="flexbox-x-axis width-100 js-matrix-row-container matrix-column-container survey-space-top-3">
											<span class="icon-drag matrix-icon-draggable"></span>
											<div class="pulldown-num js-pulldown-num"></div>
											<input type="text" class="width-100 border-none survey-space-around-2 survey-height-28 mtx-option-input mtx-option-y-input" onpaste="return false" placeholder="<?php echo (str_replace('{num}', "1", __('survey.inquiry.common.mtx.title.num'))) ?>">
											<div class="delete-icon js-delete-icon js-delete-option pointer">
												<span class="icon-cancel-small"></span>
											</div>
										</div>
										<!-- タイトル（列）2 -->
										<div class="flexbox-x-axis width-100 js-matrix-row-container matrix-column-container survey-space-top-3">
											<span class="icon-drag matrix-icon-draggable"></span>
											<div class="pulldown-num js-pulldown-num"></div>
											<input type="text" class="width-100 border-none survey-space-around-2 survey-height-28 mtx-option-input mtx-option-y-input" onpaste="return false" placeholder="<?php echo (str_replace('{num}', "2", __('survey.inquiry.common.mtx.title.num'))) ?>">
											<div class="delete-icon js-delete-icon js-delete-option pointer">
												<span class="icon-cancel-small"></span>
											</div>
										</div>
										<!-- タイトル（列）コピー用-->
										<div class="flexbox-x-axis width-100 js-matrix-row-container matrix-column-container survey-space-top-3 for-clone display-none">
											<span class="icon-drag matrix-icon-draggable"></span>
											<div class="pulldown-num js-pulldown-num"></div>
											<input type="text" class="width-100 border-none survey-space-around-2 survey-height-28 mtx-option-input mtx-option-y-input" onpaste="return false" placeholder="<?php echo (str_replace('{num}', "2", __('survey.inquiry.common.mtx.title.num'))) ?>">
											<div class="delete-icon js-delete-icon js-delete-option pointer">
												<span class="icon-cancel-small"></span>
											</div>
										</div>
									</div>
									<!-- タイトルを追加する -->
									<div class="flexbox-x-axis width-100  js-add-matrix-row survey-space-top-5">
									    <div class="pulldown-num js-pulldown-num"></div>
										<div class="flexbox-x-axis survey-space-left-1">
											<span class="font-standard font-family-v1 font-color-v1 survey-space-left-3 pointer js-row-add"><?php echo __('survey.inquiry.common.mtx.add.title') ?></span>
										</div>
									</div>
								</div>

								<!-- 選択肢（行）のコンテナ -->
								<div class="matrix-column-container" style="padding: 0px 24px;">
									<div>
										<p class="survey-space-top-bottom-6"><?php echo __('survey.inquiry.common.mtx.option.column') ?></p>
									</div>
									<div class="sortable-matrix">
										<!-- 選択肢（行）1 -->
										<div class="flexbox-x-axis width-100 js-matrix-column-container matrix-column-container survey-space-top-3">
											<span class="icon-drag matrix-icon-draggable"></span>
											<div class="js-radio"><span class="icon-form-single-option-off"></span></div>
											<div class="js-checkbox display-none"><span class="icon-check"></span></div>
											<input type="text" class="width-100 border-none survey-space-around-2 survey-height-28 mtx-option-input mtx-option-x-input" onpaste="return false" placeholder="<?php echo (str_replace('{num}', "1", __('survey.inquiry.common.item.option.placeholder'))) ?>">
											<div class="delete-icon js-delete-icon js-delete-option pointer">
												<span class="icon-cancel-small"></span>
											</div>
										</div>
										<!-- 選択肢（行）2 -->
										<div class="flexbox-x-axis width-100 js-matrix-column-container matrix-column-container survey-space-top-3">
											<span class="icon-drag matrix-icon-draggable"></span>
											<div class="js-radio"><span class="icon-form-single-option-off"></span></div>
											<div class="js-checkbox display-none"><span class="icon-check"></span></div>
											<input type="text" class="width-100 border-none survey-space-around-2 survey-height-28 mtx-option-input mtx-option-x-input" onpaste="return false" placeholder="<?php echo (str_replace('{num}', "2", __('survey.inquiry.common.item.option.placeholder'))) ?>">
											<div class="delete-icon js-delete-icon js-delete-option pointer">
												<span class="icon-cancel-small"></span>
											</div>
										</div>
										<!-- 選択肢（行）コピー用 -->
										<div class="flexbox-x-axis width-100 js-matrix-column-container matrix-column-container survey-space-top-3 for-clone display-none">
											<span class="icon-drag matrix-icon-draggable"></span>
											<div class="js-radio"><span class="icon-form-single-option-off"></span></div>
											<div class="js-checkbox display-none"><span class="icon-check"></span></div>
											<input type="text" class="width-100 border-none survey-space-around-2 survey-height-28 mtx-option-input mtx-option-x-input" onpaste="return false" placeholder="<?php echo (str_replace('{num}', "2", __('survey.inquiry.common.item.option.placeholder'))) ?>">
											<div class="delete-icon js-delete-icon js-delete-option pointer">
												<span class="icon-cancel-small"></span>
											</div>
										</div>
									</div>
									<!-- 選択肢を追加する -->
									<div class="flexbox-x-axis width-100  js-add-matrix-column survey-space-top-5">
									    <div class="js-radio"><span class="icon-form-single-option-off"></span></div>
										<div class="js-checkbox display-none"><span class="icon-check"></div>
										<div class="flexbox-x-axis survey-space-left-1">
											<span class="font-standard font-family-v1 font-color-v1 pointer survey-space-left-3 js-column-add"><?php echo __('survey.inquiry.common.item.add.option') ?></span>
										</div>
									</div>
                                    <!-- 単一選択or複数選択 -->
									<div class="survey-space-top-1 flexbox-x-axis width-100">
										<div class="dropdown-container dropdown-shorter js-single-multiple">
											<div class="dropdown-selected">
												<span class="dropdown-selected-text"><?php echo __('survey.inquiry.common.opt') ?></span>
												<span class="icon-drop-down-close"></span>
											</div>
											<ul class="dropdown-options dropdown-shorter" style="top: -10px;">
												<li class="dropdown-option js-select-type-of-matrix js-select-type-of-matrix-dont-need-num"><span class="icon-form-single-option-on-large"></span><?php echo __('survey.inquiry.common.opt') ?></li>
												<li class="dropdown-option js-select-type-of-matrix js-select-type-of-matrix-need-num"><span class="icon-form-multi-option-on"></span><?php echo __('survey.inquiry.common.chk') ?></li>
											</ul>
										</div>
										<!-- 最低選択 -->
										<div class="right-aligned flexbox-x-axis js-matrix-count-container display-none">
											<div class="js-matrix-count-min dropdown-container dropdown-shorter">
												<div class="dropdown-selected js-dropdown-matrix-min">
													<span class="dropdown-selected-text"><?php echo __('survey.inquiry.common.item.select.min') ?></span>
													<span class="icon-drop-down-close"></span>
												</div>
												<ul class="dropdown-options dropdown-shorter" style="top: -30px;">
													<li class="dropdown-option js-matrix-type-single"><?php echo __('survey.inquiry.common.item.select.min') ?></li>
													<li class="dropdown-option js-matrix-type-single" data-min="1"><?php echo (str_replace('{num}', "1", __('survey.inquiry.common.mtx.select.num'))) ?></li>
													<li class="dropdown-option js-matrix-type-single display-none" data-min="2"><?php echo (str_replace('{num}', "2", __('survey.inquiry.common.mtx.select.num'))) ?></li>
													<li class="dropdown-option js-matrix-type-single display-none" data-min="3"><?php echo (str_replace('{num}', "3", __('survey.inquiry.common.mtx.select.num'))) ?></li>
													<li class="dropdown-option js-matrix-type-single display-none" data-min="4"><?php echo (str_replace('{num}', "4", __('survey.inquiry.common.mtx.select.num'))) ?></li>
													<li class="dropdown-option js-matrix-type-single display-none" data-min="5"><?php echo (str_replace('{num}', "5", __('survey.inquiry.common.mtx.select.num'))) ?></li>
													<li class="dropdown-option js-matrix-type-single display-none" data-min="6"><?php echo (str_replace('{num}', "6", __('survey.inquiry.common.mtx.select.num'))) ?></li>
													<li class="dropdown-option js-matrix-type-single display-none" data-min="7"><?php echo (str_replace('{num}', "7", __('survey.inquiry.common.mtx.select.num'))) ?></li>
													<li class="dropdown-option js-matrix-type-single display-none" data-min="8"><?php echo (str_replace('{num}', "8", __('survey.inquiry.common.mtx.select.num'))) ?></li>
												</ul>
											</div>
											<!-- 最大選択 -->
										　　<div class="js-matrix-count-max matrix-count-max dropdown-container dropdown-shorter">
												<div class="dropdown-selected js-dropdown-matrix-max">
													<span class="dropdown-selected-text"><?php echo __('survey.inquiry.common.item.select.max') ?></span>
													<span class="icon-drop-down-close"></span>
												</div>
												<ul class="dropdown-options dropdown-shorter" style="top: -30px;">
													<li class="dropdown-option js-matrix-type-multiple"><?php echo __('survey.inquiry.common.item.select.max') ?></li>
												    <li class="dropdown-option js-matrix-type-multiple" data-min="2"><?php echo (str_replace('{num}', "2", __('survey.inquiry.common.mtx.select.num'))) ?></li>
													<li class="dropdown-option js-matrix-type-multiple display-none" data-min="3"><?php echo (str_replace('{num}', "3", __('survey.inquiry.common.mtx.select.num'))) ?></li>
												    <li class="dropdown-option js-matrix-type-multiple display-none" data-min="4"><?php echo (str_replace('{num}', "4", __('survey.inquiry.common.mtx.select.num'))) ?></li>
													<li class="dropdown-option js-matrix-type-multiple display-none" data-min="5"><?php echo (str_replace('{num}', "5", __('survey.inquiry.common.mtx.select.num'))) ?></li>
													<li class="dropdown-option js-matrix-type-multiple display-none" data-min="6"><?php echo (str_replace('{num}', "6", __('survey.inquiry.common.mtx.select.num'))) ?></li>
													<li class="dropdown-option js-matrix-type-multiple display-none" data-min="7"><?php echo (str_replace('{num}', "7", __('survey.inquiry.common.mtx.select.num'))) ?></li>
													<li class="dropdown-option js-matrix-type-multiple display-none" data-min="8"><?php echo (str_replace('{num}', "8", __('survey.inquiry.common.mtx.select.num'))) ?></li>
													<li class="dropdown-option js-matrix-type-multiple display-none" data-min="9"><?php echo (str_replace('{num}', "9", __('survey.inquiry.common.mtx.select.num'))) ?></li>
												</ul>
											</div>
										</div>
									</div>
								</div>
							</div>
							<!-- 都道府県 + 市区町村：よくある質問のコンテナ -->
							<div class="survey-address-container survey-space-top-1 js-address-container display-none focusout" style="padding: 0px 24px;">
								<div class="flexbox-x-axis survey-space-top-1">
									<div class="dropdown-middle background-pale-gray flexbox-x-axis" style="margin:0 12px 0 0;padding:0 0 0 12px ;height:32px;"><?php echo __('survey.inquiry.common.txt.postcode') ?></div>
									<input type="text" class="survey-title js-input-column js-address-postcode font-standard font-family-v1 font-color-v3" placeholder="123-4567">
								</div>
								<div class="flexbox-x-axis survey-space-top-1">
									<div class="dropdown-middle background-pale-gray flexbox-x-axis" style="margin:0 12px 0 0;padding:0 0 0 12px ;height:32px;"><?php echo __('survey.inquiry.common.txt.prefecture') ?></div>
									<input type="text" class="survey-title js-input-column js-address-prefecture font-standard font-family-v1 font-color-v3" value="<?php echo __('survey.input.text.sel') ?>">
								</div>
								<div class="flexbox-x-axis survey-space-top-1">
									<div class="dropdown-middle background-pale-gray flexbox-x-axis" style="margin:0 12px 0 0;padding:0 0 0 12px ;height:32px;"><?php echo __('survey.inquiry.common.txt.city') ?></div>
									<input type="text" class="survey-title js-input-column js-address-municipalities font-standard font-family-v1 font-color-v3" placeholder="<?php echo __('survey.inquiry.common.txt.city.placeholder') ?>">
								</div>
								<!-- <div class="flexbox-x-axis survey-space-top-1">
									<div class="dropdown-middle background-pale-gray flexbox-x-axis" style="margin:0 12px 0 0;padding:0 0 0 12px ;height:32px;">番地</div>
									<input type="text" class="survey-title js-input-column js-address-houseNumber font-standard font-family-v1 font-color-v3" placeholder="12-34">
								</div>
								<div class="flexbox-x-axis survey-space-top-1">
									<div class="dropdown-middle background-pale-gray flexbox-x-axis" style="margin:0 12px 0 0;padding:0 0 0 12px ;height:32px;">建物名・部屋番号</div>
									<input type="text" class="survey-title js-input-column js-address-roomNumber font-standard font-family-v1 font-color-v3" placeholder="Aマンション 301号">
								</div> -->
							</div>
						</div>
						<!-- 削除、回答必須のコンテナ -->
						<div class="submit-btn-container js-button-container" style="margin:32px 0 0 auto; width: fit-content; width: -moz-fit-content; padding: 0 24px 0 0;">
							<div class="survey-btn" title="<?php echo __('survey.inquiry.common.delete.item') ?>">
								<span class="icon-delete js-edidind-survey-delete-icon"></span>
							</div>
							<!-- 項目コピー -->
							<div class="survey-btn js-copy-icon-container" title="<?php echo __('admin.common.label.item_copy') ?>">
								<img src="./../assets/admin/css/img/icon-copy-dark.svg" width="12" height="12">
							</div>
							<!-- 画像追加 -->
							<div class="survey-btn js-add-photo-icon" title="<?php echo __('survey.inquiry.common.item.image.add.title') ?>">
								<img src="./../assets/admin/css/img/icon-photo.svg" width="12" height="12" class="js-icon-photo-active" style="display:none;">
								<img src="./../assets/admin/css/img/icon-photo-unactive.svg" width="12" height="12" class="js-icon-photo-unactive">
							</div>
							<div class="survey-btn js-jump-icon-container" title="<?php echo __('survey.inquiry.common.branch.add') ?>">
								<img src="./../assets/admin/css/img/icon-jump-unactive.svg" width="12" height="12" class="js-edidind-survey-jump-unactive-icon">
								<img src="./../assets/admin/css/img/icon-jump-active.svg" width="12" height="12" class="js-edidind-survey-jump-active-icon" style="display: none;">
							</div>
							<div class="survey-btn js-from-page-icon-container" title="<?php echo __('survey.inquiry.common.item.title.new_page') ?>">
								<span class="icon-form-page-unactive js-edidind-survey-from-page-unactive-icon"></span>
								<span class="icon-form-page-active js-edidind-survey-from-page-active-icon display-none"></span>
							</div>
							<form action="#" method="post" class="js-survey-checkbox-container">
								<span class=""><?php echo __('survey.inquiry.common.label.required') ?></span>
								<label class="js-label checkbox-small-v1-label-off">
									<span class="js-span checkbox-small-v1-span-off"></span>
								</label>
								<input type="checkbox" name="" value="" class="display-none">
							</form>
						</div>
						<!-- コンテナ削除時のモーダルウィンドウ -->
						<div class="modal-smaller-container js-survey-delete-container" style="display: none;">
							<div class="modal-small-title-container">
								<h4 class="modal-small-title"><?php echo __('survey.inquiry.common.delete.item.title') ?></h4>
								<span class="icon-cancel-large js-survey-modal-close-button"></span>
							</div>
							<p style="margin: 32px 0 0 10px;"><?php echo __('admin.common.modal.delete.confirm') ?></p>
							<div class="submit-btn-container" style="position: fixed; bottom: 12px;">
								<div class="btn-larger btn-red js-survey-modal-delete-button"><?php echo __('admin.common.button.delete') ?></div>
								<div class="btn-larger btn-white js-survey-modal-cancel-button"><?php echo __('admin.common.button.cancel') ?></div>
							</div>
						</div>
						<div class="modal-background js-survey-questions-modal-background display-none"></div>
						<!-- 分岐追加のモーダルウィンドウ -->
						<!-- aaa -->
						<div class="survey-branch-modal-container js-survey-questions-jump-setting-container display-none">
							<h4 class="font-standard font-size-v5 font-family-v4"><?php echo __('survey.inquiry.common.branch.set') ?></h4>
							<p class="font-standard font-size-v3 font-family-v4 font-color-v1 survey-space-all-around"><?php echo __('survey.inquiry.common.branch.destination') ?><span class="js-current-survey"></span></p>
							<div class="modal-container-context">
								<div class="survey-branch-container js-branch-setting-container cloned">
									<div class="flexbox-x-axis">
										<input type="text" class="text-input-longer survey-space-all-around-5 js-branch-setting-title">
										<span class="icon-delete right-aligned js-delete-icon" style="width: 50px; cursor: pointer;"></span>
									</div>
									<div class="survey-space-all-around-4 js-add-branch-container">
										<h4 class="font-standard font-family-v4"><?php echo __('admin.common.label.conditions') ?></h4>
										<div class="add-branch-pulldown-container js-add-pulldown-container">
											<div class="dropdown-container dropdown-longer js-question-dropdown">
												<div class="dropdown-selected">
													<span class="dropdown-selected-text"><?php echo __('survey.inquiry.common.branch.select.title') ?></span>
													<span class="icon-drop-down-close"></span>
												</div>
												<ul class="dropdown-options survey-width-fit dropdown-longer">
													<li class="dropdown-option js-survey-add-branch-modal-select-survey-title for-clone display-none"></li>
												</ul>
											</div>
											<div class="dropdown-container dropdown-longer js-answer-dropdown">
												<div class="dropdown-selected">
													<span class="dropdown-selected-text"><?php echo __('survey.inquiry.common.branch.select.answer') ?></span>
													<span class="icon-drop-down-close"></span>
												</div>
												<ul class="dropdown-options survey-width-fit dropdown-longer">
													<li class="dropdown-option js-survey-add-branch-modal-select-survey-option for-clone display-none"></li>
												</ul>
											</div>
											<div class="dropdown-container dropdown-longer js-branch-option-container" style="min-width: auto;">
												<div class="dropdown-selected">
													<span class="dropdown-selected-text" data-option="equal"><?php echo __('survey.branch.select.equal') ?></span>
													<span class="icon-drop-down-close"></span>
												</div>
												<ul class="dropdown-options survey-width-fit dropdown-longer"style="min-width: auto;">
													<li class="dropdown-option" data-option="equal"><?php echo __('survey.branch.select.equal') ?></li>
													<li class="dropdown-option" data-option="not_equal"><?php echo __('survey.branch.select.not_equal') ?></li>
													<li class="dropdown-option" data-option="include"><?php echo __('survey.branch.select.include') ?></li>
													<li class="dropdown-option" data-option="not_include"><?php echo __('survey.branch.select.not_include') ?></li>
													<li class="dropdown-option" data-option="greater"><?php echo __('survey.branch.select.greater') ?></li>
													<li class="dropdown-option" data-option="smaller"><?php echo __('survey.branch.select.smaller') ?></li>
													<li class="dropdown-option" data-option="equal_or_greater"><?php echo __('survey.branch.select.equal_or_greater') ?></li>
													<li class="dropdown-option" data-option="equal_or_smaller"><?php echo __('survey.branch.select.equal_or_smaller') ?></li>
												</ul>
											</div>
											<div class="btn-smaller btn-gray-white js-add-button">
												<span class="icon-add-white icon-add-white-svg"></span>
												<?php echo __('admin.itme.item.classification_add') ?>
											</div>
										</div>
										<div>
											<span class="font-standard font-family-v1 font-color-v3 js-text"><?php echo __('survey.inquiry.common.branch.add.and') ?></span>
											<div class="survey-space-all-around-3 flexbox-inline-x-axis icon-round-corners-small icon-background-light-blue js-added-branch-condition for-clone display-none">
												<span class="font-standard font-family-v1 flexbox-x-axis survey-space-right-1" style="overflow: hidden;"></span>
												<img src="./../assets/admin/css/img/icon-cancel-small.svg" class="js-delete-icon" width="12" height="12">
											</div>
											<span class="font-standard font-family-v1 font-color-v3 js-omit-next" style="display: none">･･･</span>
										</div>
									</div>
									<!-- 分岐条件削除時のモーダルウィンドウ -->
									<div class="modal-smaller-container js-survey-delete-container" style="display: none;">
										<div class="modal-small-title-container">
											<h4 class="modal-small-title"><?php echo __('survey.inquiry.common.branch.delete') ?></h4>

											<span class="icon-cancel-large survey-modal-close-button js-survey-modal-close-button"></span>
										</div>
										<p style="margin: 32px 0 0 10px;"><?php echo __('admin.common.modal.delete.confirm') ?></p>
										<div class="submit-btn-container" style="position: fixed; bottom: 12px;">
											<div class="btn-larger btn-red js-survey-modal-delete-branch-button"><?php echo __('admin.common.button.delete') ?></div>
											<div class="btn-larger btn-white survey-modal-cancel-button js-survey-modal-branch-cancel-button"><?php echo __('admin.common.button.cancel') ?></div>
										</div>
									</div>
									<div class="modal-background js-survey-questions-modal-background display-none"></div>
								</div>
								<div class="survey-branch-container js-branch-setting-container for-clone display-none">
									<div class="flexbox-x-axis">
										<span class="font-standard font-size-v3 font-family-v3 font-color-v1 survey-space-all-around-5 branch-or-title"><?php echo __('survey.inquiry.common.branch.label.or') ?></span>
										<input type="text" class="text-input-longer survey-space-top-5 js-branch-setting-title branch-or-title-input">
										<span class="icon-delete right-aligned js-delete-icon" style="width: 50px; cursor: pointer;"></span>
									</div>
									<div class="survey-space-all-around-4 js-add-branch-container">
									<h4 class="font-standard font-family-v4"><?php echo __('admin.common.label.conditions') ?></h4>
										<div class="add-branch-pulldown-container js-add-pulldown-container">
											<div class="dropdown-container dropdown-longer js-question-dropdown">
												<div class="dropdown-selected">
													<span class="dropdown-selected-text"><?php echo __('survey.inquiry.common.branch.select.title') ?></span>
													<span class="icon-drop-down-close"></span>
												</div>
												<ul class="dropdown-options survey-width-fit dropdown-longer">
													<li class="dropdown-option js-survey-add-branch-modal-select-survey-title for-clone display-none"></li>
												</ul>
											</div>
											<div class="dropdown-container dropdown-longer js-answer-dropdown">
												<div class="dropdown-selected">
													<span class="dropdown-selected-text"><?php echo __('survey.inquiry.common.branch.select.answer') ?></span>
													<span class="icon-drop-down-close"></span>
												</div>
												<ul class="dropdown-options survey-width-fit dropdown-longer">
													<li class="dropdown-option js-survey-add-branch-modal-select-survey-option for-clone display-none"></li>
												</ul>
											</div>
											<div class="dropdown-container dropdown-longer js-branch-option-container" style="min-width: auto;">
												<div class="dropdown-selected">
													<span class="dropdown-selected-text" data-option="equal"><?php echo __('survey.branch.select.equal') ?></span>
													<span class="icon-drop-down-close"></span>
												</div>
												<ul class="dropdown-options survey-width-fit dropdown-longer"style="min-width: auto;">
													<li class="dropdown-option" data-option="equal"><?php echo __('survey.branch.select.equal') ?></li>
													<li class="dropdown-option" data-option="not_equal"><?php echo __('survey.branch.select.not_equal') ?></li>
													<li class="dropdown-option" data-option="include"><?php echo __('survey.branch.select.include') ?></li>
													<li class="dropdown-option" data-option="not_include"><?php echo __('survey.branch.select.not_include') ?></li>
													<li class="dropdown-option" data-option="greater"><?php echo __('survey.branch.select.greater') ?></li>
													<li class="dropdown-option" data-option="smaller"><?php echo __('survey.branch.select.smaller') ?></li>
													<li class="dropdown-option" data-option="equal_or_greater"><?php echo __('survey.branch.select.equal_or_greater') ?></li>
													<li class="dropdown-option" data-option="equal_or_smaller"><?php echo __('survey.branch.select.equal_or_smaller') ?></li>
												</ul>
											</div>
											<div class="btn-smaller btn-gray-white js-add-button">
												<span class="icon-add-white icon-add-white-svg"></span>
												<?php echo __('admin.itme.item.classification_add') ?>
											</div>
										</div>
										<div>
											<span class="font-standard font-family-v1 font-color-v3 js-text"><?php echo __('survey.inquiry.common.branch.add.and') ?></span>
											<div class="survey-space-all-around-3 flexbox-inline-x-axis icon-round-corners-small icon-background-light-blue js-added-branch-condition for-clone display-none" style="align-items: center;">
												<span class="font-standard font-family-v1 flexbox-x-axis survey-space-right-1" style="overflow: hidden;"></span>
												<img src="./../assets/admin/css/img/icon-cancel-small.svg" class="js-delete-icon" width="12" height="12">
											</div>
											<span class="font-standard font-family-v1 font-color-v3 js-omit-next" style="display: none">･･･</span>
										</div>
									</div>
									<!-- 分岐条件削除時のモーダルウィンドウ -->
									<div class="modal-smaller-container js-survey-delete-container" style="display: none;">
										<div class="modal-small-title-container">
											<h4 class="modal-small-title"><?php echo __('survey.inquiry.common.branch.delete') ?></h4>

											<span class="icon-cancel-large survey-modal-close-button js-survey-modal-close-button"></span>
										</div>
										<p style="margin: 32px 0 0 10px;"><?php echo __('admin.common.modal.delete.confirm') ?></p>
										<div class="submit-btn-container" style="position: fixed; bottom: 12px;">
											<div class="btn-larger btn-red js-survey-modal-delete-branch-button"><?php echo __('admin.common.button.delete') ?></div>
											<div class="btn-larger btn-white survey-modal-cancel-button js-survey-modal-branch-cancel-button"><?php echo __('admin.common.button.cancel') ?></div>
										</div>
									</div>
									<div class="modal-background js-survey-questions-modal-background display-none"></div>
								</div>
								<div class="btn-add-branch js-add-branch-modal-add-container-button">
									<p>
									<span class="font-standard font-size-v3 font-family-v3 font-color-v1 survey-space-around-4"><?php echo __('survey.inquiry.common.branch.label.or') ?></span>
									<span class="icon-add"></span>
										<?php echo __('survey.inquiry.common.branch.add') ?></p>
								</div>
							</div>
							<div class="submit-btn-container" style="margin: 20px 0 12px 0;">
								<div class="btn-larger btn-blue js-survey-modal-add-button active"><?php echo __('survey.inquiry.common.branch.set_btn') ?></div>
								<div class="btn-larger btn-white js-survey-modal-cancel-button"><?php echo __('admin.common.button.cancel') ?></div>
								<div class="btn-larger btn-red-border js-delete-all-modal-branch">
									<span class="icon-delete"></span>
								</div>
							</div>
						</div>
						<div class="modal-background js-survey-questions-jump-setting-modal-background display-none"></div>
					</div>
				</div>
				<!-- 質問形式選択 -->
				<div class="survey-add-container relative js-survey-add-container not-sortable">
					<div class="btn-add-survey js-add-survey-button js-add-survey-button-in-section">
						<img src="./../assets/admin/css/img/icon-add.svg" width="12" height="12">
						<span><?php echo __('survey.inquiry.common.label.item.add') ?></span>
					</div>
					<div class="dropdown-container" style="height: 0; border: 0;">
						<ul class="dropdown-options dropdown-middle" style="top: auto;">
							<li class="dropdown-option js-survey-add-edit-container-single">
								<span class="icon-form-single-option-on-large"></span><?php echo __('survey.inquiry.common.opt') ?>
							</li>
							<li class="dropdown-option js-survey-add-edit-container-multi">
								<span class="icon-form-multi-option-on"></span><?php echo __('survey.inquiry.common.chk') ?>
							</li>
							<li class="dropdown-option js-survey-add-edit-container-pulldown">
								<span class="icon-form-pulldown-option-on"></span><?php echo __('survey.inquiry.common.sel') ?>
							</li>
							<li class="dropdown-option js-survey-add-edit-container-short-text">
								<span class="icon-form-writing-short-text"></span><?php echo __('survey.inquiry.common.txt') ?>
							</li>
							<li class="dropdown-option js-survey-add-edit-container-long-text">
								<span class="icon-form-writing-text"></span><?php echo __('survey.inquiry.common.txa') ?>
							</li>
							<li class="dropdown-option js-survey-add-edit-container-attachment">
								<span class="icon-form-upload"></span><?php echo __('survey.inquiry.common.fup') ?>
							<!-- フリースペース -->
							<li class="dropdown-option js-survey-add-edit-container-freespace">
								<span class="icon-form-fq"></span><?php echo __('survey.inquiry.common.frs') ?>
							</li>
							<li class="dropdown-option js-survey-add-edit-container-matrix">
								<span class="icon-form-matrix-option-on"></span><?php echo __('survey.inquiry.common.mtx') ?>
							</li>
							<!-- よく聞く質問 -->
							<li class="dropdown-option js-pulldown-parent relative">
								<span class="icon-form-fleq" style="margin: 0 8px 0 0;"></span>	
								<?php echo __('survey.inquiry.common.fleq') ?>
								<span class="pulldown-more"></span>
								<ul class="pulldown-list-children2">
									<li class="dropdown-option js-survey-add-edit-container-address">
										<span class="icon-edit-bar-add-q"></span><?php echo __('survey.inquiry.common.spl.address.prefecture_city') ?>
									</li>
								</ul>
							</li>
						</ul>
					</div>
				</div>
			</section>
			<section class="survey-editing-container js-survey-editing-container for-clone display-none" data-fold-section="false">
				<!-- セクションの番号 -->
				<div class="survey-section-num js-survey-section-num display-none">
					<?php echo __('survey.inquiry.common.label.section') ?>
					<span class="js-section-numerator"></span>
					/
					<span class="js-section-denominator"></span>
				</div>
				<!-- セクションのタイトルコンテナ -->
				<section class="js-section-title-container flexbox not-sortable display-none">
					<input type="text" class="survey-space-all-around-5 text-input-longer js-section-title">
					<div class="js-num-of-surveys font-standard font-family-v4 survey-space-top-bottom-3"></div>
					<div class="flexbox-x-axis js-section-icon-container pointer" style="margin:12px 0 0 auto;">
						<div class="survey-btn" title="<?php echo __('survey.inquiry.common.section.delete') ?>">
							<span class="icon-delete delete-icon js-survey-delete-section-icon"></span>
						</div>
						<div class="survey-btn js-jump-icon-container js-jump-section-icon-container" title="<?php echo __('survey.inquiry.common.branch.add') ?>">
							<img src="./../assets/admin/css/img/icon-jump-unactive.svg" width="12" height="12" class="js-edidind-survey-jump-unactive-icon">
							<img src="./../assets/admin/css/img/icon-jump-active.svg" width="12" height="12" class="js-edidind-survey-jump-active-icon" style="display: none;">
						</div>
						<div class="js-survey-fold-section" style="width:auto;" title="<?php echo __('survey.inquiry.common.section.fold_up') ?>">
							<span class="icon-fold-section-open"></span>
						</div>
						<!-- 分岐追加のモーダルウィンドウ -->
						<div class="survey-branch-modal-container js-survey-questions-jump-section-setting-container display-none">
							<h4 class="font-standard font-size-v5 font-family-v4"><?php echo __('survey.inquiry.common.branch.set') ?></h4>
							<p class="font-standard font-size-v3 font-family-v4 font-color-v1 survey-space-all-around"><?php echo __('survey.inquiry.common.branch.destination') ?><span class="js-current-survey"></span></p>
							<div class="modal-container-context">
								<div class="survey-branch-container js-branch-setting-container cloned">
									<div class="flexbox-x-axis">
										<input type="text" class="text-input-longer survey-space-all-around-5 js-branch-setting-title">
										<span class="icon-delete right-aligned js-delete-icon" style="width: 50px; cursor: pointer;"></span>
									</div>
									<div class="survey-space-all-around-4 js-add-branch-container">
										<h4 class="font-standard font-family-v4"><?php echo __('admin.common.label.conditions') ?></h4>
										<div class="add-branch-pulldown-container js-add-pulldown-container">
											<div class="dropdown-container dropdown-longer js-question-dropdown">
												<div class="dropdown-selected">
													<span class="dropdown-selected-text"><?php echo __('survey.inquiry.common.branch.select.title') ?></span>
													<span class="icon-drop-down-close"></span>
												</div>
												<ul class="dropdown-options survey-width-fit dropdown-longer">
													<li class="dropdown-option js-survey-add-branch-modal-select-survey-title for-clone display-none"></li>
												</ul>
											</div>
											<div class="dropdown-container dropdown-longer js-answer-dropdown">
												<div class="dropdown-selected">
													<span class="dropdown-selected-text"><?php echo __('survey.inquiry.common.branch.select.answer') ?></span>
													<span class="icon-drop-down-close"></span>
												</div>
												<ul class="dropdown-options survey-width-fit dropdown-longer">
													<li class="dropdown-option js-survey-add-branch-modal-select-survey-option for-clone display-none"></li>
												</ul>
											</div>
											<div class="dropdown-container dropdown-longer js-branch-option-container" style="min-width: auto;">
												<div class="dropdown-selected">
													<span class="dropdown-selected-text" data-option="equal"><?php echo __('survey.branch.select.equal') ?></span>
													<span class="icon-drop-down-close"></span>
												</div>
												<ul class="dropdown-options survey-width-fit dropdown-longer"style="min-width: auto;">
													<li class="dropdown-option" data-option="equal"><?php echo __('survey.branch.select.equal') ?></li>
													<li class="dropdown-option" data-option="not_equal"><?php echo __('survey.branch.select.not_equal') ?></li>
													<li class="dropdown-option" data-option="include"><?php echo __('survey.branch.select.include') ?></li>
													<li class="dropdown-option" data-option="not_include"><?php echo __('survey.branch.select.not_include') ?></li>
													<li class="dropdown-option" data-option="greater"><?php echo __('survey.branch.select.greater') ?></li>
													<li class="dropdown-option" data-option="smaller"><?php echo __('survey.branch.select.smaller') ?></li>
													<li class="dropdown-option" data-option="equal_or_greater"><?php echo __('survey.branch.select.equal_or_greater') ?></li>
													<li class="dropdown-option" data-option="equal_or_smaller"><?php echo __('survey.branch.select.equal_or_smaller') ?></li>
												</ul>
											</div>
											<div class="btn-smaller btn-gray-white js-add-button">
												<span class="icon-add-white icon-add-white-svg"></span>
												<?php echo __('admin.itme.item.classification_add') ?>
											</div>
										</div>
										<div>
											<span class="font-standard font-family-v1 font-color-v3 js-text"><?php echo __('survey.inquiry.common.branch.add.and') ?></span>
											<div class="survey-space-all-around-3 flexbox-inline-x-axis icon-round-corners-small icon-background-light-blue js-added-branch-condition for-clone display-none">
												<span class="font-standard font-family-v1 flexbox-x-axis survey-space-right-1" style="overflow: hidden;"></span>
												<img src="./../assets/admin/css/img/icon-cancel-small.svg" class="js-delete-icon" width="12" height="12">
											</div>
											<span class="font-standard font-family-v1 font-color-v3 js-omit-next" style="display: none">･･･</span>
										</div>
									</div>
									<!-- 分岐条件削除時のモーダルウィンドウ -->
									<div class="modal-smaller-container js-survey-delete-container" style="display: none;">
										<div class="modal-small-title-container">
											<h4 class="modal-small-title"><?php echo __('survey.inquiry.common.branch.delete') ?></h4>

											<span class="icon-cancel-large survey-modal-close-button js-survey-modal-close-button"></span>
										</div>
										<p style="margin: 32px 0 0 10px;"><?php echo __('admin.common.modal.delete.confirm') ?></p>
										<div class="submit-btn-container" style="position: fixed; bottom: 12px;">
											<div class="btn-larger btn-red js-survey-modal-delete-branch-button"><?php echo __('admin.common.button.delete') ?></div>
											<div class="btn-larger btn-white survey-modal-cancel-button js-survey-modal-branch-cancel-button"><?php echo __('admin.common.button.cancel') ?></div>
										</div>
									</div>
									<div class="modal-background js-survey-questions-modal-background display-none"></div>
								</div>
								<div class="survey-branch-container js-branch-setting-container for-clone display-none">
									<div class="flexbox-x-axis">
										<input type="text" class="text-input-longer survey-space-all-around-5 js-branch-setting-title">
										<span class="icon-delete right-aligned js-delete-icon" style="width: 50px; cursor: pointer;"></span>
									</div>
									<div class="survey-space-all-around-4 js-add-branch-container">
										<h4 class="font-standard font-family-v4"><?php echo __('admin.common.label.conditions') ?></h4>
										<div class="add-branch-pulldown-container js-add-pulldown-container">
											<div class="dropdown-container dropdown-longer js-question-dropdown">
												<div class="dropdown-selected">
													<span class="dropdown-selected-text"><?php echo __('survey.inquiry.common.branch.select.title') ?></span>
													<span class="icon-drop-down-close"></span>
												</div>
												<ul class="dropdown-options survey-width-fit dropdown-longer">
													<li class="dropdown-option js-survey-add-branch-modal-select-survey-title for-clone display-none"></li>
												</ul>
											</div>
											<div class="dropdown-container dropdown-longer js-answer-dropdown">
												<div class="dropdown-selected">
													<span class="dropdown-selected-text"><?php echo __('survey.inquiry.common.branch.select.answer') ?></span>
													<span class="icon-drop-down-close"></span>
												</div>
												<ul class="dropdown-options survey-width-fit dropdown-longer">
													<li class="dropdown-option js-survey-add-branch-modal-select-survey-option for-clone display-none"></li>
												</ul>
											</div>
											<div class="dropdown-container dropdown-longer js-branch-option-container" style="min-width: auto;">
												<div class="dropdown-selected">
													<span class="dropdown-selected-text" data-option="equal"><?php echo __('survey.branch.select.equal') ?></span>
													<span class="icon-drop-down-close"></span>
												</div>
												<ul class="dropdown-options survey-width-fit dropdown-longer"style="min-width: auto;">
													<li class="dropdown-option" data-option="equal"><?php echo __('survey.branch.select.equal') ?></li>
													<li class="dropdown-option" data-option="not_equal"><?php echo __('survey.branch.select.not_equal') ?></li>
													<li class="dropdown-option" data-option="include"><?php echo __('survey.branch.select.include') ?></li>
													<li class="dropdown-option" data-option="not_include"><?php echo __('survey.branch.select.not_include') ?></li>
													<li class="dropdown-option" data-option="greater"><?php echo __('survey.branch.select.greater') ?></li>
													<li class="dropdown-option" data-option="smaller"><?php echo __('survey.branch.select.smaller') ?></li>
													<li class="dropdown-option" data-option="equal_or_greater"><?php echo __('survey.branch.select.equal_or_greater') ?></li>
													<li class="dropdown-option" data-option="equal_or_smaller"><?php echo __('survey.branch.select.equal_or_smaller') ?></li>
												</ul>
											</div>
											<div class="btn-smaller btn-gray-white js-add-button">
												<span class="icon-add-white icon-add-white-svg"></span>
												<?php echo __('admin.itme.item.classification_add') ?>
											</div>
										</div>
										<div>
											<span class="font-standard font-family-v1 font-color-v3 js-text"><?php echo __('survey.inquiry.common.branch.add.and') ?></span>
											<div class="survey-space-all-around-3 flexbox-inline-x-axis icon-round-corners-small icon-background-light-blue js-added-branch-condition for-clone display-none">
												<span class="font-standard font-family-v1 flexbox-x-axis survey-space-right-1" style="overflow: hidden;"></span>
												<img src="./../assets/admin/css/img/icon-cancel-small.svg" class="js-delete-icon" width="12" height="12">
											</div>
											<span class="font-standard font-family-v1 font-color-v3 js-omit-next" style="display: none">･･･</span>
										</div>
									</div>
									<!-- 分岐条件削除時のモーダルウィンドウ -->
									<div class="modal-smaller-container js-survey-delete-container" style="display: none;">
										<div class="modal-small-title-container">
											<h4 class="modal-small-title"><?php echo __('survey.inquiry.common.branch.delete') ?></h4>

											<span class="icon-cancel-large survey-modal-close-button js-survey-modal-close-button"></span>
										</div>
										<p style="margin: 32px 0 0 10px;"><?php echo __('admin.common.modal.delete.confirm') ?></p>
										<div class="submit-btn-container" style="position: fixed; bottom: 12px;">
											<div class="btn-larger btn-red js-survey-modal-delete-branch-button"><?php echo __('admin.common.button.delete') ?></div>
											<div class="btn-larger btn-white survey-modal-cancel-button js-survey-modal-branch-cancel-button"><?php echo __('admin.common.button.cancel') ?></div>
										</div>
									</div>
									<div class="modal-background js-survey-questions-modal-background display-none"></div>
									</div>
								<div class="btn-add-branch js-add-branch-modal-add-container-button">
									<p>
									<span class="font-standard font-size-v3 font-family-v3 font-color-v1 survey-space-around-4"><?php echo __('survey.inquiry.common.branch.label.or') ?></span>
									<span class="icon-add"></span>
										<?php echo __('survey.inquiry.common.branch.add') ?></p>
								</div>
							</div>
							<div class="submit-btn-container" style="margin: 20px 0 12px 0;">
								<div class="btn-larger btn-blue js-survey-modal-add-button active"><?php echo __('survey.inquiry.common.branch.set_btn') ?></div>
								<div class="btn-larger btn-white js-survey-modal-cancel-button"><?php echo __('admin.common.button.cancel') ?></div>
								<div class="btn-larger btn-red-border js-delete-all-modal-branch">
									<span class="icon-delete"></span>
								</div>
							</div>
						</div>
						<div class="modal-background js-survey-questions-jump-setting-modal-background display-none"></div>
					</div>
					<!-- コンテナ削除時のモーダルウィンドウ -->
					<div class="modal-smaller-container js-survey-delete-container" style="display: none;">
						<div class="modal-small-title-container">
							<h4 class="modal-small-title"><?php echo __('survey.inquiry.common.section.delete.confirm') ?></h4>
							<span class="icon-cancel-large survey-modal-close-button js-survey-modal-close-button"></span>
						</div>
						<p style="margin: 32px 0 0 12px;"><?php echo __('admin.common.modal.delete.confirm') ?></p>
						<div class="submit-btn-container" style="position: fixed; bottom: 12px;">
							<div class="btn-larger btn-red js-survey-modal-delete-section-button"><?php echo __('admin.common.button.delete') ?></div>
							<div class="btn-larger btn-white js-survey-modal-close-button"><?php echo __('admin.common.button.cancel') ?></div>
						</div>
					</div>
					<div class="modal-background js-survey-questions-modal-background display-none"></div>
				</section>
				<!-- 質問入力コンテナ -->
				<div class="survey-input-container js-survey-input-container">
					<div class="survey-editing-order-select flexbox-x-axis pointer js-fold-item">
						<span class="js-survey-editing-order-num left-aligned">1</span>
						<span class="icon-fold-open"></span>
					</div>
					<div class="survey-editing-input-container js-survey-editing-input-container" id="survey-editing-container1">
						<!-- タイトルコンテナ -->
						<div class="title-container js-title-container js-survey-questions-title-focus" style="position:relative; margin: 0px 24px;">
							<span class="icon-drag survey-icon-draggable"></span>
							<input type="text" class="survey-title js-survey-title js-survey-title-input" placeholder="<?php echo __('survey.inquiry.common.item.placeholder.title') ?>">
							<div class="preview-contents survey-title js-survey-title"></div>
							<div class="js-to-open-summernote-editor to-open-summernote-editor flexbox-x-axis display-none">
								<span class="icon-form-zoom-in"></span>
								<span><?php echo __('survey.inquiry.common.item.to_edit_HTML') ?></span>
							</div>
							<!-- リッチテキストエディタ -->
							<div class="survey-branch-modal-container js-survey-questions-input-title-modal rich-text-editor display-none">
								<div class="flexbox flexbox-baselines">
									<h1 class="font-standard font-size-v5 font-family-v4"style="margin:0 auto 24px 0;"><?php echo __('survey.inquiry.common.item.edit_HTML.title') ?></h1>
									<span class="icon-cancel-large js-survey-modal-cancel-button pointer"></span>
								</div>
								<textarea type="text" class="survey-title js-survey-title-editor"></textarea>
								<div class="submit-btn-container" style="position: fixed; bottom: 12px;">
									<div class="btn-larger btn-blue js-suevey-save-title js-survey-modal-add-button"><?php echo __('survey.inquiry.common.branch.set_btn') ?></div>
									<div class="btn-larger btn-white js-survey-modal-cancel-button"><?php echo __('admin.common.button.cancel') ?></div>
								</div>
							</div>
							<div class="modal-background js-survey-questions-input-title-modal-background display-none"></div>
							<!-- 質問形式の選択プルダウン -->
							<div class="dropdown-container dropdown-middle">
								<div class="dropdown-selected">
									<span class="dropdown-selected-text"><?php echo __('survey.inquiry.common.opt') ?></span>
									<span class="icon-drop-down-close"></span>
								</div>
								<ul class="dropdown-options dropdown-middle" style="top: -10px;">
									<li class="dropdown-option current-type js-survey-type-single">
										<span class="icon-form-single-option-on-large"></span><?php echo __('survey.inquiry.common.opt') ?>
									</li>
									<li class="dropdown-option js-survey-type-multiple">
										<span class="icon-form-multi-option-on"></span><?php echo __('survey.inquiry.common.chk') ?>
									</li>
									<li class="dropdown-option js-survey-type-pulldown">
										<span class="icon-form-pulldown-option-on"></span><?php echo __('survey.inquiry.common.sel') ?>
									</li>
									<li class="dropdown-option js-survey-type-short-text">
										<span class="icon-form-writing-short-text"></span><?php echo __('survey.inquiry.common.txt') ?>
									</li>
									<li class="dropdown-option js-survey-type-long-text">
										<span class="icon-form-writing-text"></span><?php echo __('survey.inquiry.common.txa') ?>
									</li>
									<li class="dropdown-option js-survey-type-attachment">
										<span class="icon-form-upload"></span><?php echo __('survey.inquiry.common.fup') ?>
									</li>
									<!-- フリースペース -->
									<li class="dropdown-option js-survey-type-freespace">
										<span class="icon-form-fq"></span><?php echo __('survey.inquiry.common.frs') ?>
									</li>
									<!-- マトリクス -->
									<li class="dropdown-option js-survey-type-matrix">
										<span class="icon-form-matrix-option-on"></span><?php echo __('survey.inquiry.common.mtx') ?>
									</li>
									<!-- よく聞く質問 -->
									<li class="dropdown-option js-pulldown-parent relative">
										<span class="icon-form-fleq" style="margin: 0 8px 0 0;"></span>	
										<?php echo __('survey.inquiry.common.fleq') ?>
										<span class="pulldown-more"></span>
										<ul class="pulldown-list-children2">
											<li class="dropdown-option js-survey-type-address">
												<span class="icon-edit-bar-add-q"></span><?php echo __('survey.inquiry.common.spl.address.prefecture_city') ?>
											</li>
										</ul>
									</li>
								</ul>
							</div>
						</div>
						<div class="js-options-main-container">
							<!-- 選択肢のコンテナ -->
							<div class="width-100 js-options-container">
								<!-- 選択肢1 -->
								<div class="flexbox-x-axis width-100 survey-space-top-1 js-option-container" style="padding: 0 24px;">
									<div class="js-radio">
										<span class="icon-form-single-option-off"></span>
									</div>
									<div class="js-checkbox display-none">
										<span class="icon-check"></span>
									</div>
									<div class="pulldown-num js-pulldown-num display-none" ></div>
									<input type="text" class="width-100 border-none survey-space-around-2 survey-height-32 js-option-input" onpaste="return false" placeholder="<?php echo (str_replace('{num}', "1", __('survey.inquiry.common.item.option.placeholder'))) ?>">
									<div class="js-add-image-input-container"></div>
									<input name="file" type="hidden" style="display:none;" class="js-input-image-to-save">
									<div class="delete-icon js-delete-icon js-delete-option pointer display-none">
										<span class="icon-cancel-small"></span>
									</div>
								</div>
								<!-- 選択肢2 -->
								<div class="flexbox-x-axis width-100 survey-space-top-1 js-option-container" style="padding: 0 24px;">
									<div class="js-radio">
										<span class="icon-form-single-option-off"></span>
									</div>
									<div class="js-checkbox display-none">
										<span class="icon-check"></span>
									</div>
									<div class="pulldown-num js-pulldown-num display-none" ></div>
									<input type="text" class="width-100 border-none survey-space-around-2 survey-height-32 js-option-input" onpaste="return false" placeholder="<?php echo (str_replace('{num}', "2", __('survey.inquiry.common.item.option.placeholder'))) ?>">
									<div class="js-add-image-input-container"></div>
									<input name="file" type="hidden" style="display:none;" class="js-input-image-to-save">
									<div class="delete-icon js-delete-icon js-delete-option pointer display-none">
										<span class="icon-cancel-small"></span>
									</div>
								</div>
								<!-- 選択肢　コピー用 -->
								<div class="flexbox-x-axis width-100 survey-space-top-1 js-option-container for-clone display-none" style="padding: 0 24px;">
									<div class="js-radio">
										<span class="icon-form-single-option-off"></span>
									</div>
									<div class="js-checkbox display-none">
										<span class="icon-check"></span>
									</div>
									<div class="pulldown-num js-pulldown-num display-none" ></div>
									<input type="text" class="width-100 border-none survey-space-around-2 survey-height-32 js-option-input" onpaste="return false" placeholder="<?php echo (str_replace('{num}', "2", __('survey.inquiry.common.item.option.placeholder'))) ?>">
									<div class="js-add-image-input-container"></div>
									<input name="file" type="hidden" style="display:none;" class="js-input-image-to-save">
									<div class="delete-icon js-delete-icon js-delete-option pointer display-none">
										<span class="icon-cancel-small"></span>
									</div>
								</div>
								<!-- その他 -->
								<div class="flexbox-x-axis survey-space-top-1 js-other-container not-sortable display-none" style="padding: 0px 24px;">
									<div class="js-radio">
										<span class="icon-form-single-option-off"></span>
									</div>
									<div class="js-checkbox display-none">
										<span class="icon-check"></span>
									</div>
									<div class="pulldown-num js-pulldown-num display-none"></div>
									<p class="js-option-other survey-width-50 survey-space-around-2"><?php echo __('survey.common.label.other') ?></p>
									<form action="#" method="post" class="checkbox-small-v1-container js-survey-checkbox-container js-option-other-checkbox">
										<span class="font-color-v3"><?php echo __('survey.common.label.other.input') ?></span>
										<span class="right-aligned"><?php echo __('survey.common.label.required') ?></span>
										<label class="js-label checkbox-small-v1-label-off">
											<span class="js-span checkbox-small-v1-span-off"></span>
										</label>
										<input type="checkbox" name="" value="" class="display-none">
									</form>
									<div class="pointer js-delete-icon js-other-delete-icon display-none">
										<span class="icon-cancel-small"></span>
									</div>
								</div>
								<!-- 「選択肢」または「その他」を追加する -->
								<div class="survey-space-top-1 flexbox-x-axis js-add-option-or-other-container not-sortable display-none" style="padding: 0px 24px;">
									<div class="js-radio">
										<span class="icon-form-single-option-off"></span>
									</div>
									<div class="js-checkbox display-none">
										<span class="icon-check"></span>
									</div>
									<div class="pulldown-num js-pulldown-num display-none" ></div>
									<div class="flexbox-x-axis survey-space-left-1">
										<span class="font-standard font-family-v1 font-color-v1 pointer js-survey-link-add"><?php echo __('survey.inquiry.common.item.add.option') ?></span>
										<span class="font-standard font-family-v1 font-color-v2 survey-space-around-1 js-survey-text"><?php echo __('survey.inquiry.common.item.option.or') ?></span>
										<span class="font-standard font-family-v1 font-color-v1 pointer js-survey-link-other"><?php echo __('survey.inquiry.common.item.add.other') ?></span>
									</div>
								</div>
							</div>
							<!-- 短文のコンテナ -->
							<div class="survey-space-top-1 width-100 js-short-text-container display-none" style="padding: 0px 24px;">
								<input type="text" class="short-text-input" placeholder="<?php echo __('survey.inquiry.common.txt') ?>" readonly>
								<div class="width-100">
									<div class="survey-space-top-1 flexbox-x-axis width-100">
										<div class="dropdown-container dropdown-middle">
											<div class="dropdown-selected">
												<span class="dropdown-selected-text"><?php echo __('survey.inquiry.common.txt.text') ?></span>
												<span class="icon-drop-down-close"></span>
											</div>
											<ul class="dropdown-options dropdown-middle">
												<li class="dropdown-option js-survey-select-input-type-of-short-text js-survey-short-text-dont-need-num"><?php echo __('survey.inquiry.common.txt.postcode') ?></li>
												<li class="dropdown-option js-survey-select-input-type-of-short-text js-survey-short-text-dont-need-num"><?php echo __('admin.common.label.phone_number') ?></li>
												<li class="dropdown-option js-survey-select-input-type-of-short-text js-select-mail"><?php echo __('admin.common.label.mail.address') ?></li>
												<li class="dropdown-option js-survey-select-input-type-of-short-text js-survey-short-text-dont-need-num"><?php echo __('admin.common.label.date') ?></li>
												<li class="dropdown-option js-survey-select-input-type-of-short-text js-survey-short-text-need-num"><?php echo __('survey.inquiry.common.txt.text') ?></li>
												<li class="dropdown-option js-survey-select-input-type-of-short-text js-survey-short-text-dont-need-num"><?php echo __('survey.inquiry.common.txt.num') ?></li>
											</ul>
										</div>
										<div class="right-aligned flexbox-x-axis js-short-text-count-container">
											<div class="survey-space-left-2 js-short-text-count js-short-text-count-min"><?php echo __('survey.inquiry.common.txa.min') ?>　<input class ="survey-short-text-input" type="text"></div>
											<div class="survey-space-left-2 js-short-text-count js-short-text-count-max"><?php echo __('survey.inquiry.common.txa.max') ?>　<input class ="survey-short-text-input" type="text"></div>
										</div>
										<div class="right-aligned flexbox-x-axis js-check-mail-confirm pointer" style="display: none;">
											<span class="icon-check js-icon-check"></span>
											<p> <?php echo __('survey.inquiry.common.txt.reconfirm.mail') ?></p>
										</div>
										<div class="right-aligned flexbox-x-axis js-send-mail pointer" style="display: none;">
											<span class="icon-check js-send-mail-check"></span>
											<p> <?php echo __('survey.inquiry.common.txt.mail.sendmail') ?></p>
										</div>
										<div class="right-aligned flexbox-x-axis js-short-text-emotion-analytics-container">
											<div class="survey-space-left-2" style="display:inline-flex;">
												<?php echo __('survey.inquiry.common.txt.txa.emotion_analytics') ?>　
												<div class="talkappi-switch js-short-text-emotion-analytics" data-value="0"></div>
											</div>
										</div>
									</div>
									<div class="survey-space-top-1 flexbox-x-axis js-short-text-privacy-masking-container">
										<div class="survey-space-left-2" style="display:inline-flex; margin: 0 0 0 auto;">
											<?php echo __('survey.inquiry.common.txt.txa.privacy_masking') ?>　
											<div class="talkappi-switch js-short-text-privacy-masking" data-value="0"></div>
										</div>
									</div>
								</div>
							</div>
							<!-- 長文のコンテナ -->
							<div class="long-text-container js-long-text-container display-none" style="padding: 0px 24px;">
								<input type="text" class="survey-space-top-1 long-text-input" placeholder="<?php echo __('survey.inquiry.common.txa.placeholder') ?>" readonly>
								<div class="right-aligned flexbox-x-axis">
									<div class="survey-space-left-2 js-long-text-count-min"><?php echo __('survey.inquiry.common.txa.min') ?>　<input class ="survey-short-text-input" type="text"></div>
									<div class="survey-space-left-2 js-long-text-count-max"><?php echo __('survey.inquiry.common.txa.max') ?>　<input class ="survey-short-text-input" type="text"></div>
									<div class="survey-space-left-2" style="display:inline-flex;">
										<?php echo __('survey.inquiry.common.txt.txa.emotion_analytics') ?>　
										<div class="talkappi-switch js-long-text-emotion-analytics" data-value="0"></div>
									</div>
								</div>
							</div>
							<!-- ファイルアップロードのコンテナ -->
							<div class="survey-attachment-container survey-space-top-1 js-attachment-container display-none" style="padding: 0px 24px;">
								<p class="font-standard font-family-v1 font-color-v3 survey-space-top-1 flexbox-x-axis">
									<span class="icon-form-upload"></span><?php echo __('survey.inquiry.common.fup') ?>
								</p>
								<div style="display: flex;gap: 17px;padding: 1rem;">
									<?php echo $upload_limit_size . __('admin.common.label.file_upload_limit_size')?>,
									<?php echo __('admin.common.label.file_upload_extension') . " : " . $extensions ?>
								</div>
							</div>
							<!-- フリースペースのコンテナ -->
							<div class="survey-freespace-container survey-space-top-1 js-freespace-container display-none">
							</div>
							<!-- マトリクスのコンテナ -->
							<div class="survey-space-top-5 js-matrix-container display-none">
								<!-- タイトル（列）のコンテナ -->
								<div class="matrix-rows-container" style="padding: 0px 24px;">
									<div>
										<p class="survey-space-top-bottom-7"><?php echo __('survey.inquiry.common.mtx.title.column') ?></p>
									</div>
									<div class="sortable-matrix">
										<!-- タイトル（列）1 -->
										<div class="flexbox-x-axis width-100 js-matrix-row-container matrix-column-container survey-space-top-3">
											<span class="icon-drag matrix-icon-draggable"></span>
											<div class="pulldown-num js-pulldown-num"></div>
											<input type="text" class="width-100 border-none survey-space-around-2 survey-height-28 mtx-option-input mtx-option-y-input" onpaste="return false" placeholder="<?php echo (str_replace('{num}', "1", __('survey.inquiry.common.mtx.title.num'))) ?>">
											<div class="delete-icon js-delete-icon js-delete-option pointer">
												<span class="icon-cancel-small"></span>
											</div>
										</div>
										<!-- タイトル（列）2 -->
										<div class="flexbox-x-axis width-100 js-matrix-row-container matrix-column-container survey-space-top-3">
											<span class="icon-drag matrix-icon-draggable"></span>
											<div class="pulldown-num js-pulldown-num"></div>
											<input type="text" class="width-100 border-none survey-space-around-2 survey-height-28 mtx-option-input mtx-option-y-input" onpaste="return false" placeholder="<?php echo (str_replace('{num}', "2", __('survey.inquiry.common.mtx.title.num'))) ?>">
											<div class="delete-icon js-delete-icon js-delete-option pointer">
												<span class="icon-cancel-small"></span>
											</div>
										</div>
										<!-- タイトル（列）コピー用-->
										<div class="flexbox-x-axis width-100 js-matrix-row-container matrix-column-container survey-space-top-3 for-clone display-none">
											<span class="icon-drag matrix-icon-draggable"></span>
											<div class="pulldown-num js-pulldown-num"></div>
											<input type="text" class="width-100 border-none survey-space-around-2 survey-height-28 mtx-option-input mtx-option-y-input" onpaste="return false" placeholder="<?php echo (str_replace('{num}', "2", __('survey.inquiry.common.mtx.title.num'))) ?>">
											<div class="delete-icon js-delete-icon js-delete-option pointer">
												<span class="icon-cancel-small"></span>
											</div>
										</div>
									</div>
									<!-- タイトルを追加する -->
									<div class="flexbox-x-axis width-100  js-add-matrix-row survey-space-top-5">
									    <div class="pulldown-num js-pulldown-num"></div>
										<div class="flexbox-x-axis survey-space-left-1">
											<span class="font-standard font-family-v1 font-color-v1 survey-space-left-3 pointer js-row-add"><?php echo __('survey.inquiry.common.mtx.add.title') ?></span>
										</div>
									</div>
								</div>

								<!-- 選択肢（行）のコンテナ -->
								<div class="matrix-column-container" style="padding: 0px 24px;">
									<div>
										<p class="survey-space-top-bottom-6"><?php echo __('survey.inquiry.common.mtx.option.column') ?></p>
									</div>
									<div class="sortable-matrix">
										<!-- 選択肢（行）1 -->
										<div class="flexbox-x-axis width-100 js-matrix-column-container matrix-column-container survey-space-top-3">
											<span class="icon-drag matrix-icon-draggable"></span>
											<div class="js-radio"><span class="icon-form-single-option-off"></span></div>
											<div class="js-checkbox display-none"><span class="icon-check"></span></div>
											<input type="text" class="width-100 border-none survey-space-around-2 survey-height-28 mtx-option-input mtx-option-x-input" onpaste="return false" placeholder="<?php echo (str_replace('{num}', "1", __('survey.inquiry.common.item.option.placeholder'))) ?>">
											<div class="delete-icon js-delete-icon js-delete-option pointer">
												<span class="icon-cancel-small"></span>
											</div>
										</div>
										<!-- 選択肢（行）2 -->
										<div class="flexbox-x-axis width-100 js-matrix-column-container matrix-column-container survey-space-top-3">
											<span class="icon-drag matrix-icon-draggable"></span>
											<div class="js-radio"><span class="icon-form-single-option-off"></span></div>
											<div class="js-checkbox display-none"><span class="icon-check"></span></div>
											<input type="text" class="width-100 border-none survey-space-around-2 survey-height-28 mtx-option-input mtx-option-x-input" onpaste="return false" placeholder="<?php echo (str_replace('{num}', "2", __('survey.inquiry.common.item.option.placeholder'))) ?>">
											<div class="delete-icon js-delete-icon js-delete-option pointer">
												<span class="icon-cancel-small"></span>
											</div>
										</div>
										<!-- 選択肢（行）コピー用 -->
										<div class="flexbox-x-axis width-100 js-matrix-column-container matrix-column-container survey-space-top-3 for-clone display-none">
											<span class="icon-drag matrix-icon-draggable"></span>
											<div class="js-radio"><span class="icon-form-single-option-off"></span></div>
											<div class="js-checkbox display-none"><span class="icon-check"></span></div>
											<input type="text" class="width-100 border-none survey-space-around-2 survey-height-28 mtx-option-input mtx-option-x-input" onpaste="return false" placeholder="<?php echo (str_replace('{num}', "2", __('survey.inquiry.common.item.option.placeholder'))) ?>">
											<div class="delete-icon js-delete-icon js-delete-option pointer">
												<span class="icon-cancel-small"></span>
											</div>
										</div>
									</div>
									<!-- 選択肢を追加する -->
									<div class="flexbox-x-axis width-100  js-add-matrix-column survey-space-top-5">
									    <div class="js-radio"><span class="icon-form-single-option-off"></span></div>
										<div class="js-checkbox display-none"><span class="icon-check"></div>
										<div class="flexbox-x-axis survey-space-left-1">
											<span class="font-standard font-family-v1 font-color-v1 pointer survey-space-left-3 js-column-add"><?php echo __('survey.inquiry.common.item.add.option') ?></span>
										</div>
									</div>
									<!-- 単一選択or複数選択 -->
									<div class="survey-space-top-1 flexbox-x-axis width-100">
										<div class="dropdown-container dropdown-shorter js-single-multiple">
											<div class="dropdown-selected">
												<span class="dropdown-selected-text"><?php echo __('survey.inquiry.common.opt') ?></span>
												<span class="icon-drop-down-close"></span>
											</div>
											<ul class="dropdown-options dropdown-shorter" style="top: -10px;">
												<li class="dropdown-option js-select-type-of-matrix js-select-type-of-matrix-dont-need-num"><span class="icon-form-single-option-on-large"></span><?php echo __('survey.inquiry.common.opt') ?></li>
												<li class="dropdown-option js-select-type-of-matrix js-select-type-of-matrix-need-num"><span class="icon-form-multi-option-on"></span><?php echo __('survey.inquiry.common.chk') ?></li>
											</ul>
										</div>
										<!-- 最低選択 -->
										<div class="right-aligned flexbox-x-axis js-matrix-count-container display-none">
											<div class="js-matrix-count-min dropdown-container dropdown-shorter">
												<div class="dropdown-selected js-dropdown-matrix-min">
													<span class="dropdown-selected-text"><?php echo __('survey.inquiry.common.item.select.min') ?></span>
													<span class="icon-drop-down-close"></span>
												</div>
												<ul class="dropdown-options dropdown-shorter" style="top: -30px;">
													<li class="dropdown-option js-matrix-type-single"><?php echo __('survey.inquiry.common.item.select.min') ?></li>
													<li class="dropdown-option js-matrix-type-single" data-min="1"><?php echo (str_replace('{num}', "1", __('survey.inquiry.common.mtx.select.num'))) ?></li>
													<li class="dropdown-option js-matrix-type-single display-none" data-min="2"><?php echo (str_replace('{num}', "2", __('survey.inquiry.common.mtx.select.num'))) ?></li>
													<li class="dropdown-option js-matrix-type-single display-none" data-min="3"><?php echo (str_replace('{num}', "3", __('survey.inquiry.common.mtx.select.num'))) ?></li>
													<li class="dropdown-option js-matrix-type-single display-none" data-min="4"><?php echo (str_replace('{num}', "4", __('survey.inquiry.common.mtx.select.num'))) ?></li>
													<li class="dropdown-option js-matrix-type-single display-none" data-min="5"><?php echo (str_replace('{num}', "5", __('survey.inquiry.common.mtx.select.num'))) ?></li>
													<li class="dropdown-option js-matrix-type-single display-none" data-min="6"><?php echo (str_replace('{num}', "6", __('survey.inquiry.common.mtx.select.num'))) ?></li>
													<li class="dropdown-option js-matrix-type-single display-none" data-min="7"><?php echo (str_replace('{num}', "7", __('survey.inquiry.common.mtx.select.num'))) ?></li>
													<li class="dropdown-option js-matrix-type-single display-none" data-min="8"><?php echo (str_replace('{num}', "8", __('survey.inquiry.common.mtx.select.num'))) ?></li>
												</ul>
											</div>
											<!-- 最大選択 -->
										　　<div class="js-matrix-count-max matrix-count-max dropdown-container dropdown-shorter">
												<div class="dropdown-selected js-dropdown-matrix-max">
													<span class="dropdown-selected-text"><?php echo __('survey.inquiry.common.item.select.max') ?></span>
													<span class="icon-drop-down-close"></span>
												</div>
												<ul class="dropdown-options dropdown-shorter" style="top: -30px;">
													<li class="dropdown-option js-matrix-type-multiple"><?php echo __('survey.inquiry.common.item.select.max') ?></li>
												    <li class="dropdown-option js-matrix-type-multiple" data-min="2"><?php echo (str_replace('{num}', "2", __('survey.inquiry.common.mtx.select.num'))) ?></li>
													<li class="dropdown-option js-matrix-type-multiple display-none" data-min="3"><?php echo (str_replace('{num}', "3", __('survey.inquiry.common.mtx.select.num'))) ?></li>
												    <li class="dropdown-option js-matrix-type-multiple display-none" data-min="4"><?php echo (str_replace('{num}', "4", __('survey.inquiry.common.mtx.select.num'))) ?></li>
													<li class="dropdown-option js-matrix-type-multiple display-none" data-min="5"><?php echo (str_replace('{num}', "5", __('survey.inquiry.common.mtx.select.num'))) ?></li>
													<li class="dropdown-option js-matrix-type-multiple display-none" data-min="6"><?php echo (str_replace('{num}', "6", __('survey.inquiry.common.mtx.select.num'))) ?></li>
													<li class="dropdown-option js-matrix-type-multiple display-none" data-min="7"><?php echo (str_replace('{num}', "7", __('survey.inquiry.common.mtx.select.num'))) ?></li>
													<li class="dropdown-option js-matrix-type-multiple display-none" data-min="8"><?php echo (str_replace('{num}', "8", __('survey.inquiry.common.mtx.select.num'))) ?></li>
													<li class="dropdown-option js-matrix-type-multiple display-none" data-min="9"><?php echo (str_replace('{num}', "9", __('survey.inquiry.common.mtx.select.num'))) ?></li>
												</ul>
											</div>
										</div>
									</div>
								</div>
							</div>
							<!-- 都道府県 + 市区町村：よくある質問のコンテナ -->
							<div class="survey-address-container survey-space-top-1 js-address-container display-none focusout" style="padding: 0px 24px;">
								<div class="flexbox-x-axis survey-space-top-1">
									<div class="dropdown-middle background-pale-gray flexbox-x-axis" style="margin:0 12px 0 0;padding:0 0 0 12px ;height:32px;"><?php echo __('survey.inquiry.common.txt.postcode') ?></div>
									<input type="text" class="survey-title js-input-column js-address-postcode font-standard font-family-v1 font-color-v3" placeholder="123-4567">
								</div>
								<div class="flexbox-x-axis survey-space-top-1">
									<div class="dropdown-middle background-pale-gray flexbox-x-axis" style="margin:0 12px 0 0;padding:0 0 0 12px ;height:32px;"><?php echo __('survey.inquiry.common.txt.prefecture') ?></div>
									<input type="text" class="survey-title js-input-column js-address-prefecture font-standard font-family-v1 font-color-v3" value="<?php echo __('survey.input.text.sel') ?>">
								</div>
								<div class="flexbox-x-axis survey-space-top-1">
									<div class="dropdown-middle background-pale-gray flexbox-x-axis" style="margin:0 12px 0 0;padding:0 0 0 12px ;height:32px;"><?php echo __('survey.inquiry.common.txt.city') ?></div>
									<input type="text" class="survey-title js-input-column js-address-municipalities font-standard font-family-v1 font-color-v3" placeholder="<?php echo __('survey.inquiry.common.txt.city.placeholder') ?>">
								</div>
								<!-- <div class="flexbox-x-axis survey-space-top-1">
									<div class="dropdown-middle background-pale-gray flexbox-x-axis" style="margin:0 12px 0 0;padding:0 0 0 12px ;height:32px;">番地</div>
									<input type="text" class="survey-title js-input-column js-address-houseNumber font-standard font-family-v1 font-color-v3" placeholder="12-34">
								</div>
								<div class="flexbox-x-axis survey-space-top-1">
									<div class="dropdown-middle background-pale-gray flexbox-x-axis" style="margin:0 12px 0 0;padding:0 0 0 12px ;height:32px;">建物名・部屋番号</div>
									<input type="text" class="survey-title js-input-column js-address-roomNumber font-standard font-family-v1 font-color-v3" placeholder="Aマンション 301号">
								</div> -->
							</div>
						</div>
						<!-- 削除、回答必須のコンテナ -->
						<div class="submit-btn-container js-button-container" style="margin:32px 0 0 auto; width: fit-content; width: -moz-fit-content; padding: 0 24px 0 0;">
							<div class="survey-btn" title="<?php echo __('survey.inquiry.common.delete.item') ?>">
								<span class="icon-delete js-edidind-survey-delete-icon"></span>
							</div>
							<!-- 項目コピー -->
							<div class="survey-btn js-copy-icon-container" title="<?php echo __('admin.common.label.item_copy') ?>">
								<img src="./../assets/admin/css/img/icon-copy-dark.svg" width="12" height="12">
							</div>
							<!-- 画像追加 -->
							<div class="survey-btn js-add-photo-icon" title="<?php echo __('survey.inquiry.common.item.image.add.title') ?>">
								<img src="./../assets/admin/css/img/icon-photo.svg" width="12" height="12" class="js-icon-photo-active" style="display:none;">
								<img src="./../assets/admin/css/img/icon-photo-unactive.svg" width="12" height="12" class="js-icon-photo-unactive">
							</div>
							<div class="survey-btn js-jump-icon-container" title="<?php echo __('survey.inquiry.common.branch.add') ?>">
								<img src="./../assets/admin/css/img/icon-jump-unactive.svg" width="12" height="12" class="js-edidind-survey-jump-unactive-icon">
								<img src="./../assets/admin/css/img/icon-jump-active.svg" width="12" height="12" class="js-edidind-survey-jump-active-icon" style="display: none;">
							</div>
							<div class="survey-btn js-from-page-icon-container" title="<?php echo __('survey.inquiry.common.item.title.new_page') ?>">
								<span class="icon-form-page-unactive js-edidind-survey-from-page-unactive-icon"></span>
								<span class="icon-form-page-active js-edidind-survey-from-page-active-icon display-none"></span>
							</div>
							<form action="#" method="post" class="js-survey-checkbox-container">
								<span class=""><?php echo __('survey.inquiry.common.label.required') ?></span>
								<label class="js-label checkbox-small-v1-label-off">
									<span class="js-span checkbox-small-v1-span-off"></span>
								</label>
								<input type="checkbox" name="" value="" class="display-none">
							</form>
						</div>
						<!-- コンテナ削除時のモーダルウィンドウ -->
						<div class="modal-smaller-container js-survey-delete-container" style="display: none;">
							<div class="modal-small-title-container">
								<h4 class="modal-small-title"><?php echo __('survey.inquiry.common.delete.item.title') ?></h4>
								<span class="icon-cancel-large js-survey-modal-close-button"></span>
							</div>
							<p style="margin: 32px 0 0 10px;"><?php echo __('admin.common.modal.delete.confirm') ?></p>
							<div class="submit-btn-container" style="position: fixed; bottom: 12px;">
								<div class="btn-larger btn-red js-survey-modal-delete-button"><?php echo __('admin.common.button.delete') ?></div>
								<div class="btn-larger btn-white js-survey-modal-cancel-button"><?php echo __('admin.common.button.cancel') ?></div>
							</div>
						</div>
						<div class="modal-background js-survey-questions-modal-background display-none"></div>
						<!-- 分岐追加のモーダルウィンドウ -->
						<div class="survey-branch-modal-container js-survey-questions-jump-setting-container display-none">
							<h4 class="font-standard font-size-v5 font-family-v4"><?php echo __('survey.inquiry.common.branch.set') ?></h4>
							<p class="font-standard font-size-v3 font-family-v4 font-color-v1 survey-space-all-around"><?php echo __('survey.inquiry.common.branch.destination') ?><span class="js-current-survey"></span></p>
							<div class="modal-container-context">
								<div class="survey-branch-container js-branch-setting-container cloned">
									<div class="flexbox-x-axis">
										<input type="text" class="text-input-longer survey-space-all-around-5 js-branch-setting-title">
										<span class="icon-delete right-aligned js-delete-icon" style="width: 50px; cursor: pointer;"></span>
									</div>
									<div class="survey-space-all-around-4 js-add-branch-container">
										<h4 class="font-standard font-family-v4"><?php echo __('admin.common.label.conditions') ?></h4>
										<div class="add-branch-pulldown-container js-add-pulldown-container">
											<div class="dropdown-container dropdown-longer js-question-dropdown">
												<div class="dropdown-selected">
													<span class="dropdown-selected-text"><?php echo __('survey.inquiry.common.branch.select.title') ?></span>
													<span class="icon-drop-down-close"></span>
												</div>
												<ul class="dropdown-options survey-width-fit dropdown-longer">
													<li class="dropdown-option js-survey-add-branch-modal-select-survey-title for-clone display-none"></li>
												</ul>
											</div>
											<div class="dropdown-container dropdown-longer js-answer-dropdown">
												<div class="dropdown-selected">
													<span class="dropdown-selected-text"><?php echo __('survey.inquiry.common.branch.select.answer') ?></span>
													<span class="icon-drop-down-close"></span>
												</div>
												<ul class="dropdown-options survey-width-fit dropdown-longer">
													<li class="dropdown-option js-survey-add-branch-modal-select-survey-option for-clone display-none"></li>
												</ul>
											</div>
											<div class="dropdown-container dropdown-longer js-branch-option-container" style="min-width: auto;">
												<div class="dropdown-selected">
													<span class="dropdown-selected-text" data-option="equal"><?php echo __('survey.branch.select.equal') ?></span>
													<span class="icon-drop-down-close"></span>
												</div>
												<ul class="dropdown-options survey-width-fit dropdown-longer"style="min-width: auto;">
													<li class="dropdown-option" data-option="equal"><?php echo __('survey.branch.select.equal') ?></li>
													<li class="dropdown-option" data-option="not_equal"><?php echo __('survey.branch.select.not_equal') ?></li>
													<li class="dropdown-option" data-option="include"><?php echo __('survey.branch.select.include') ?></li>
													<li class="dropdown-option" data-option="not_include"><?php echo __('survey.branch.select.not_include') ?></li>
													<li class="dropdown-option" data-option="greater"><?php echo __('survey.branch.select.greater') ?></li>
													<li class="dropdown-option" data-option="smaller"><?php echo __('survey.branch.select.smaller') ?></li>
													<li class="dropdown-option" data-option="equal_or_greater"><?php echo __('survey.branch.select.equal_or_greater') ?></li>
													<li class="dropdown-option" data-option="equal_or_smaller"><?php echo __('survey.branch.select.equal_or_smaller') ?></li>
												</ul>
											</div>
											<div class="btn-smaller btn-gray-white js-add-button">
												<span class="icon-add-white icon-add-white-svg"></span>
												<?php echo __('admin.itme.item.classification_add') ?>
											</div>
										</div>
										<div>
											<span class="font-standard font-family-v1 font-color-v3 js-text"><?php echo __('survey.inquiry.common.branch.add.and') ?></span>
											<div class="survey-space-all-around-3 flexbox-inline-x-axis icon-round-corners-small icon-background-light-blue js-added-branch-condition for-clone display-none">
												<span class="font-standard font-family-v1 flexbox-x-axis survey-space-right-1" style="overflow: hidden;"></span>
												<img src="./../assets/admin/css/img/icon-cancel-small.svg" class="js-delete-icon" width="12" height="12">
											</div>
											<span class="font-standard font-family-v1 font-color-v3 js-omit-next" style="display: none">･･･</span>
										</div>
									</div>
									<!-- 分岐条件削除時のモーダルウィンドウ -->
									<div class="modal-smaller-container js-survey-delete-container" style="display: none;">
										<div class="modal-small-title-container">
											<h4 class="modal-small-title"><?php echo __('survey.inquiry.common.branch.delete') ?></h4>

											<span class="icon-cancel-large survey-modal-close-button js-survey-modal-close-button"></span>
										</div>
										<p style="margin: 32px 0 0 10px;"><?php echo __('admin.common.modal.delete.confirm') ?></p>
										<div class="submit-btn-container" style="position: fixed; bottom: 12px;">
											<div class="btn-larger btn-red js-survey-modal-delete-branch-button"><?php echo __('admin.common.button.delete') ?></div>
											<div class="btn-larger btn-white survey-modal-cancel-button js-survey-modal-branch-cancel-button"><?php echo __('admin.common.button.cancel') ?></div>
										</div>
									</div>
									<div class="modal-background js-survey-questions-modal-background display-none"></div>
								</div>
								<div class="survey-branch-container js-branch-setting-container for-clone display-none">
									<div class="flexbox-x-axis">
										<span class="font-standard font-size-v3 font-family-v3 font-color-v1 survey-space-all-around-5 branch-or-title"><?php echo __('survey.inquiry.common.branch.label.or') ?></span>
										<input type="text" class="text-input-longer survey-space-top-5 js-branch-setting-title branch-or-title-input">
										<span class="icon-delete right-aligned js-delete-icon" style="width: 50px; cursor: pointer;"></span>
									</div>
									<div class="survey-space-all-around-4 js-add-branch-container">
									<h4 class="font-standard font-family-v4"><?php echo __('admin.common.label.conditions') ?></h4>
										<div class="add-branch-pulldown-container js-add-pulldown-container">
											<div class="dropdown-container dropdown-longer js-question-dropdown">
												<div class="dropdown-selected">
													<span class="dropdown-selected-text"><?php echo __('survey.inquiry.common.branch.select.title') ?></span>
													<span class="icon-drop-down-close"></span>
												</div>
												<ul class="dropdown-options survey-width-fit dropdown-longer">
													<li class="dropdown-option js-survey-add-branch-modal-select-survey-title for-clone display-none"></li>
												</ul>
											</div>
											<div class="dropdown-container dropdown-longer js-answer-dropdown">
												<div class="dropdown-selected">
													<span class="dropdown-selected-text"><?php echo __('survey.inquiry.common.branch.select.answer') ?></span>
													<span class="icon-drop-down-close"></span>
												</div>
												<ul class="dropdown-options survey-width-fit dropdown-longer">
													<li class="dropdown-option js-survey-add-branch-modal-select-survey-option for-clone display-none"></li>
												</ul>
											</div>
											<div class="dropdown-container dropdown-longer js-branch-option-container" style="min-width: auto;">
												<div class="dropdown-selected">
													<span class="dropdown-selected-text" data-option="equal"><?php echo __('survey.branch.select.equal') ?></span>
													<span class="icon-drop-down-close"></span>
												</div>
												<ul class="dropdown-options survey-width-fit dropdown-longer"style="min-width: auto;">
													<li class="dropdown-option" data-option="equal"><?php echo __('survey.branch.select.equal') ?></li>
													<li class="dropdown-option" data-option="not_equal"><?php echo __('survey.branch.select.not_equal') ?></li>
													<li class="dropdown-option" data-option="include"><?php echo __('survey.branch.select.include') ?></li>
													<li class="dropdown-option" data-option="not_include"><?php echo __('survey.branch.select.not_include') ?></li>
													<li class="dropdown-option" data-option="greater"><?php echo __('survey.branch.select.greater') ?></li>
													<li class="dropdown-option" data-option="smaller"><?php echo __('survey.branch.select.smaller') ?></li>
													<li class="dropdown-option" data-option="equal_or_greater"><?php echo __('survey.branch.select.equal_or_greater') ?></li>
													<li class="dropdown-option" data-option="equal_or_smaller"><?php echo __('survey.branch.select.equal_or_smaller') ?></li>
												</ul>
											</div>
											<div class="btn-smaller btn-gray-white js-add-button">
												<span class="icon-add-white icon-add-white-svg"></span>
												<?php echo __('admin.itme.item.classification_add') ?>
											</div>
										</div>
										<div>
											<span class="font-standard font-family-v1 font-color-v3 js-text"><?php echo __('survey.inquiry.common.branch.add.and') ?></span>
											<div class="survey-space-all-around-3 flexbox-inline-x-axis icon-round-corners-small icon-background-light-blue js-added-branch-condition for-clone display-none" style="align-items: center;">
												<span class="font-standard font-family-v1 flexbox-x-axis survey-space-right-1" style="overflow: hidden;"></span>
												<img src="./../assets/admin/css/img/icon-cancel-small.svg" class="js-delete-icon" width="12" height="12">
											</div>
											<span class="font-standard font-family-v1 font-color-v3 js-omit-next" style="display: none">･･･</span>
										</div>
									</div>
									<!-- 分岐条件削除時のモーダルウィンドウ -->
									<div class="modal-smaller-container js-survey-delete-container" style="display: none;">
										<div class="modal-small-title-container">
											<h4 class="modal-small-title"><?php echo __('survey.inquiry.common.branch.delete') ?></h4>

											<span class="icon-cancel-large survey-modal-close-button js-survey-modal-close-button"></span>
										</div>
										<p style="margin: 32px 0 0 10px;"><?php echo __('admin.common.modal.delete.confirm') ?></p>
										<div class="submit-btn-container" style="position: fixed; bottom: 12px;">
											<div class="btn-larger btn-red js-survey-modal-delete-branch-button"><?php echo __('admin.common.button.delete') ?></div>
											<div class="btn-larger btn-white survey-modal-cancel-button js-survey-modal-branch-cancel-button"><?php echo __('admin.common.button.cancel') ?></div>
										</div>
									</div>
									<div class="modal-background js-survey-questions-modal-background display-none"></div>
								</div>
								<div class="btn-add-branch js-add-branch-modal-add-container-button">
									<p>
									<span class="font-standard font-size-v3 font-family-v3 font-color-v1 survey-space-around-4"><?php echo __('survey.inquiry.common.branch.label.or') ?></span>
									<span class="icon-add"></span>
										<?php echo __('survey.inquiry.common.branch.add') ?></p>
								</div>
							</div>
							<div class="submit-btn-container" style="margin: 20px 0 12px 0;">
								<div class="btn-larger btn-blue js-survey-modal-add-button active"><?php echo __('survey.inquiry.common.branch.set_btn') ?></div>
								<div class="btn-larger btn-white js-survey-modal-cancel-button"><?php echo __('admin.common.button.cancel') ?></div>
								<div class="btn-larger btn-red-border js-delete-all-modal-branch">
									<span class="icon-delete"></span>
								</div>
							</div>
						</div>
						<div class="modal-background js-survey-questions-jump-setting-modal-background display-none"></div>
					</div>
				</div>
				<!-- 質問入力コンテナ コピー用 -->
				<div class="survey-input-container js-survey-input-container for-clone display-none">
					<div class="survey-editing-order-select flexbox-x-axis pointer js-fold-item">
						<span class="js-survey-editing-order-num left-aligned">1</span>
						<span class="icon-fold-open"></span>
					</div>
					<div class="survey-editing-input-container js-survey-editing-input-container" id="survey-editing-container1">
						<!-- タイトルコンテナ -->
						<div class="title-container js-title-container js-survey-questions-title-focus" style="position:relative; margin: 0px 24px;">
							<span class="icon-drag survey-icon-draggable"></span>
							<input type="text" class="survey-title js-survey-title js-survey-title-input" placeholder="<?php echo __('survey.inquiry.common.item.placeholder.title') ?>">
							<div class="preview-contents survey-title js-survey-title"></div>
							<div class="js-to-open-summernote-editor to-open-summernote-editor flexbox-x-axis display-none">
								<span class="icon-form-zoom-in"></span>
								<span><?php echo __('survey.inquiry.common.item.to_edit_HTML') ?></span>
							</div>
							<!-- リッチテキストエディタ -->
							<div class="survey-branch-modal-container js-survey-questions-input-title-modal rich-text-editor display-none">
								<div class="flexbox flexbox-baselines">
									<h1 class="font-standard font-size-v5 font-family-v4"style="margin:0 auto 24px 0;"><?php echo __('survey.inquiry.common.item.edit_HTML.title') ?></h1>
									<span class="icon-cancel-large js-survey-modal-cancel-button pointer"></span>
								</div>
								<textarea type="text" class="survey-title js-survey-title-editor"></textarea>
								<div class="submit-btn-container" style="position: fixed; bottom: 12px;">
									<div class="btn-larger btn-blue js-suevey-save-title js-survey-modal-add-button"><?php echo __('survey.inquiry.common.branch.set_btn') ?></div>
									<div class="btn-larger btn-white js-survey-modal-cancel-button"><?php echo __('admin.common.button.cancel') ?></div>
								</div>
							</div>
							<div class="modal-background js-survey-questions-input-title-modal-background display-none"></div>
							<!-- 質問形式の選択プルダウン -->
							<div class="dropdown-container dropdown-middle">
								<div class="dropdown-selected">
									<span class="dropdown-selected-text"><?php echo __('survey.inquiry.common.opt') ?></span>
									<span class="icon-drop-down-close"></span>
								</div>
								<ul class="dropdown-options dropdown-middle" style="top: -10px;">
									<li class="dropdown-option current-type js-survey-type-single">
										<span class="icon-form-single-option-on-large"></span><?php echo __('survey.inquiry.common.opt') ?>
									</li>
									<li class="dropdown-option js-survey-type-multiple">
										<span class="icon-form-multi-option-on"></span><?php echo __('survey.inquiry.common.chk') ?>
									</li>
									<li class="dropdown-option js-survey-type-pulldown">
										<span class="icon-form-pulldown-option-on"></span><?php echo __('survey.inquiry.common.sel') ?>
									</li>
									<li class="dropdown-option js-survey-type-short-text">
										<span class="icon-form-writing-short-text"></span><?php echo __('survey.inquiry.common.txt') ?>
									</li>
									<li class="dropdown-option js-survey-type-long-text">
										<span class="icon-form-writing-text"></span><?php echo __('survey.inquiry.common.txa') ?>
									</li>
									<li class="dropdown-option js-survey-type-attachment">
										<span class="icon-form-upload"></span><?php echo __('survey.inquiry.common.fup') ?>
									</li>
									<!-- フリースペース -->
									<li class="dropdown-option js-survey-type-freespace">
										<span class="icon-form-fq"></span><?php echo __('survey.inquiry.common.frs') ?>
									</li>
									<!-- マトリクス -->
									<li class="dropdown-option js-survey-type-matrix">
										<span class="icon-form-matrix-option-on"></span><?php echo __('survey.inquiry.common.mtx') ?>
									</li>
									<!-- よく聞く質問 -->
									<li class="dropdown-option js-pulldown-parent relative">
										<span class="icon-form-fleq" style="margin: 0 8px 0 0;"></span>	
										<?php echo __('survey.inquiry.common.fleq') ?>
										<span class="pulldown-more"></span>
										<ul class="pulldown-list-children2">
											<li class="dropdown-option js-survey-type-address">
												<span class="icon-edit-bar-add-q"></span><?php echo __('survey.inquiry.common.spl.address.prefecture_city') ?>
											</li>
										</ul>
									</li>
								</ul>
							</div>
						</div>
						<div class="js-options-main-container">
							<!-- 選択肢のコンテナ -->
							<div class="width-100 js-options-container">
								<!-- 選択肢1 -->
								<div class="flexbox-x-axis width-100 survey-space-top-1 js-option-container" style="padding: 0 24px;">
									<div class="js-radio">
										<span class="icon-form-single-option-off"></span>
									</div>
									<div class="js-checkbox display-none">
										<span class="icon-check"></span>
									</div>
									<div class="pulldown-num js-pulldown-num display-none" ></div>
									<input type="text" class="width-100 border-none survey-space-around-2 survey-height-32 js-option-input" onpaste="return false" placeholder="<?php echo (str_replace('{num}', "1", __('survey.inquiry.common.item.option.placeholder'))) ?>">
									<div class="js-add-image-input-container"></div>
									<input name="file" type="hidden" style="display:none;" class="js-input-image-to-save">
									<div class="delete-icon js-delete-icon js-delete-option pointer display-none">
										<span class="icon-cancel-small"></span>
									</div>
								</div>
								<!-- 選択肢2 -->
								<div class="flexbox-x-axis width-100 survey-space-top-1 js-option-container" style="padding: 0 24px;">
									<div class="js-radio">
										<span class="icon-form-single-option-off"></span>
									</div>
									<div class="js-checkbox display-none">
										<span class="icon-check"></span>
									</div>
									<div class="pulldown-num js-pulldown-num display-none" ></div>
									<input type="text" class="width-100 border-none survey-space-around-2 survey-height-32 js-option-input" onpaste="return false" placeholder="<?php echo (str_replace('{num}', "2", __('survey.inquiry.common.item.option.placeholder'))) ?>">
									<div class="js-add-image-input-container"></div>
									<input name="file" type="hidden" style="display:none;" class="js-input-image-to-save">
									<div class="delete-icon js-delete-icon js-delete-option pointer display-none">
										<span class="icon-cancel-small"></span>
									</div>
								</div>
								<!-- 選択肢　コピー用 -->
								<div class="flexbox-x-axis width-100 survey-space-top-1 js-option-container for-clone display-none" style="padding: 0 24px;">
									<div class="js-radio">
										<span class="icon-form-single-option-off"></span>
									</div>
									<div class="js-checkbox display-none">
										<span class="icon-check"></span>
									</div>
									<div class="pulldown-num js-pulldown-num display-none" ></div>
									<input type="text" class="width-100 border-none survey-space-around-2 survey-height-32 js-option-input" onpaste="return false" placeholder="<?php echo (str_replace('{num}', "2", __('survey.inquiry.common.item.option.placeholder'))) ?>">
									<div class="js-add-image-input-container"></div>
									<input name="file" type="hidden" style="display:none;" class="js-input-image-to-save">
									<div class="delete-icon js-delete-icon js-delete-option pointer display-none">
										<span class="icon-cancel-small"></span>
									</div>
								</div>
								<!-- その他 -->
								<div class="flexbox-x-axis survey-space-top-1 js-other-container not-sortable display-none" style="padding: 0px 24px;">
									<div class="js-radio">
										<span class="icon-form-single-option-off"></span>
									</div>
									<div class="js-checkbox display-none">
										<span class="icon-check"></span>
									</div>
									<div class="pulldown-num js-pulldown-num display-none"></div>
									<p class="js-option-other survey-width-50 survey-space-around-2"><?php echo __('survey.common.label.other') ?></p>
									<form action="#" method="post" class="checkbox-small-v1-container js-survey-checkbox-container js-option-other-checkbox">
										<span class="font-color-v3"><?php echo __('survey.common.label.other.input') ?></span>
										<span class="right-aligned"><?php echo __('survey.common.label.required') ?></span>
										<label class="js-label checkbox-small-v1-label-off">
											<span class="js-span checkbox-small-v1-span-off"></span>
										</label>
										<input type="checkbox" name="" value="" class="display-none">
									</form>
									<div class="pointer js-delete-icon js-other-delete-icon display-none">
										<span class="icon-cancel-small"></span>
									</div>
								</div>
								<!-- 「選択肢」または「その他」を追加する -->
								<div class="survey-space-top-1 flexbox-x-axis js-add-option-or-other-container not-sortable display-none" style="padding: 0px 24px;">
									<div class="js-radio">
										<span class="icon-form-single-option-off"></span>
									</div>
									<div class="js-checkbox display-none">
										<span class="icon-check"></span>
									</div>
									<div class="pulldown-num js-pulldown-num display-none" ></div>
									<div class="flexbox-x-axis survey-space-left-1">
										<span class="font-standard font-family-v1 font-color-v1 pointer js-survey-link-add"><?php echo __('survey.inquiry.common.item.add.option') ?></span>
										<span class="font-standard font-family-v1 font-color-v2 survey-space-around-1 js-survey-text"><?php echo __('survey.inquiry.common.item.option.or') ?></span>
										<span class="font-standard font-family-v1 font-color-v1 pointer js-survey-link-other"><?php echo __('survey.inquiry.common.item.add.other') ?></span>
									</div>
								</div>
							</div>
							<!-- 短文のコンテナ -->
							<div class="survey-space-top-1 width-100 js-short-text-container display-none" style="padding: 0px 24px;">
								<input type="text" class="short-text-input" placeholder="<?php echo __('survey.inquiry.common.txt') ?>" readonly>
								<div class="width-100">
									<div class="survey-space-top-1 flexbox-x-axis width-100">
										<div class="dropdown-container dropdown-middle">
											<div class="dropdown-selected">
												<span class="dropdown-selected-text"><?php echo __('survey.inquiry.common.txt.text') ?></span>
												<span class="icon-drop-down-close"></span>
											</div>
											<ul class="dropdown-options dropdown-middle">
												<li class="dropdown-option js-survey-select-input-type-of-short-text js-survey-short-text-dont-need-num"><?php echo __('survey.inquiry.common.txt.postcode') ?></li>
												<li class="dropdown-option js-survey-select-input-type-of-short-text js-survey-short-text-dont-need-num"><?php echo __('admin.common.label.phone_number') ?></li>
												<li class="dropdown-option js-survey-select-input-type-of-short-text js-select-mail"><?php echo __('admin.common.label.mail.address') ?></li>
												<li class="dropdown-option js-survey-select-input-type-of-short-text js-survey-short-text-dont-need-num"><?php echo __('admin.common.label.date') ?></li>
												<li class="dropdown-option js-survey-select-input-type-of-short-text js-survey-short-text-need-num"><?php echo __('survey.inquiry.common.txt.text') ?></li>
												<li class="dropdown-option js-survey-select-input-type-of-short-text js-survey-short-text-dont-need-num"><?php echo __('survey.inquiry.common.txt.num') ?></li>
											</ul>
										</div>
										<div class="right-aligned flexbox-x-axis js-short-text-count-container">
											<div class="survey-space-left-2 js-short-text-count js-short-text-count-min"><?php echo __('survey.inquiry.common.txa.min') ?>　<input class ="survey-short-text-input" type="text"></div>
											<div class="survey-space-left-2 js-short-text-count js-short-text-count-max"><?php echo __('survey.inquiry.common.txa.max') ?>　<input class ="survey-short-text-input" type="text"></div>
										</div>
										<div class="right-aligned flexbox-x-axis js-check-mail-confirm pointer" style="display: none;">
											<span class="icon-check js-icon-check"></span>
											<p> <?php echo __('survey.inquiry.common.txt.reconfirm.mail') ?></p>
										</div>
										<div class="right-aligned flexbox-x-axis js-send-mail pointer" style="display: none;">
											<span class="icon-check js-send-mail-check"></span>
											<p> <?php echo __('survey.inquiry.common.txt.mail.sendmail') ?></p>
										</div>
										<div class="right-aligned flexbox-x-axis js-short-text-emotion-analytics-container">
											<div class="survey-space-left-2" style="display:inline-flex;">
												<?php echo __('survey.inquiry.common.txt.txa.emotion_analytics') ?>　
												<div class="talkappi-switch js-short-text-emotion-analytics" data-value="0"></div>
											</div>
										</div>
									</div>
									<div class="survey-space-top-1 flexbox-x-axis js-short-text-privacy-masking-container">
										<div class="survey-space-left-2" style="display:inline-flex; margin: 0 0 0 auto;">
											<?php echo __('survey.inquiry.common.txt.txa.privacy_masking') ?>　
											<div class="talkappi-switch js-short-text-privacy-masking" data-value="0"></div>
										</div>
									</div>
								</div>
							</div>
							<!-- 長文のコンテナ -->
							<div class="long-text-container js-long-text-container display-none" style="padding: 0px 24px;">
								<input type="text" class="survey-space-top-1 long-text-input" placeholder="<?php echo __('survey.inquiry.common.txa.placeholder') ?>" readonly>
								<div class="right-aligned flexbox-x-axis">
									<div class="survey-space-left-2 js-long-text-count-min"><?php echo __('survey.inquiry.common.txa.min') ?>　<input class ="survey-short-text-input" type="text"></div>
									<div class="survey-space-left-2 js-long-text-count-max"><?php echo __('survey.inquiry.common.txa.max') ?>　<input class ="survey-short-text-input" type="text"></div>
									<div class="survey-space-left-2" style="display:inline-flex;">
										<?php echo __('survey.inquiry.common.txt.txa.emotion_analytics') ?>　
										<div class="talkappi-switch js-long-text-emotion-analytics" data-value="0"></div>
									</div>
								</div>
							</div>
							<!-- ファイルアップロードのコンテナ -->
							<div class="survey-attachment-container survey-space-top-1 js-attachment-container display-none" style="padding: 0px 24px;">
								<p class="font-standard font-family-v1 font-color-v3 survey-space-top-1 flexbox-x-axis">
									<span class="icon-form-upload"></span><?php echo __('survey.inquiry.common.fup') ?>
								</p>
								<div style="display: flex;gap: 17px;padding: 1rem;">
									<?php echo $upload_limit_size . __('admin.common.label.file_upload_limit_size')?>,
									<?php echo __('admin.common.label.file_upload_extension') . " : " . $extensions ?>
								</div>
							</div>
							<!-- フリースペースのコンテナ -->
							<div class="survey-freespace-container survey-space-top-1 js-freespace-container display-none">
							</div>
							<!-- マトリクスのコンテナ -->
							<div class="survey-space-top-5 js-matrix-container display-none">
								<!-- タイトル（列）のコンテナ -->
								<div class="matrix-rows-container" style="padding: 0px 24px;">
									<div>
										<p class="survey-space-top-bottom-7"><?php echo __('survey.inquiry.common.mtx.title.column') ?></p>
									</div>
									<div class="sortable-matrix">
										<!-- タイトル（列）1 -->
										<div class="flexbox-x-axis width-100 js-matrix-row-container matrix-column-container survey-space-top-3">
											<span class="icon-drag matrix-icon-draggable"></span>
											<div class="pulldown-num js-pulldown-num"></div>
											<input type="text" class="width-100 border-none survey-space-around-2 survey-height-28 mtx-option-input mtx-option-y-input" onpaste="return false" placeholder="<?php echo (str_replace('{num}', "1", __('survey.inquiry.common.mtx.title.num'))) ?>">
											<div class="delete-icon js-delete-icon js-delete-option pointer">
												<span class="icon-cancel-small"></span>
											</div>
										</div>
										<!-- タイトル（列）2 -->
										<div class="flexbox-x-axis width-100 js-matrix-row-container matrix-column-container survey-space-top-3">
											<span class="icon-drag matrix-icon-draggable"></span>
											<div class="pulldown-num js-pulldown-num"></div>
											<input type="text" class="width-100 border-none survey-space-around-2 survey-height-28 mtx-option-input mtx-option-y-input" onpaste="return false" placeholder="<?php echo (str_replace('{num}', "2", __('survey.inquiry.common.mtx.title.num'))) ?>">
											<div class="delete-icon js-delete-icon js-delete-option pointer">
												<span class="icon-cancel-small"></span>
											</div>
										</div>
										<!-- タイトル（列）コピー用-->
										<div class="flexbox-x-axis width-100 js-matrix-row-container matrix-column-container survey-space-top-3 for-clone display-none">
											<span class="icon-drag matrix-icon-draggable"></span>
											<div class="pulldown-num js-pulldown-num"></div>
											<input type="text" class="width-100 border-none survey-space-around-2 survey-height-28 mtx-option-input mtx-option-y-input" onpaste="return false" placeholder="<?php echo (str_replace('{num}', "2", __('survey.inquiry.common.mtx.title.num'))) ?>">
											<div class="delete-icon js-delete-icon js-delete-option pointer">
												<span class="icon-cancel-small"></span>
											</div>
										</div>
									</div>
									<!-- タイトルを追加する -->
									<div class="flexbox-x-axis width-100  js-add-matrix-row survey-space-top-5">
									    <div class="pulldown-num js-pulldown-num"></div>
										<div class="flexbox-x-axis survey-space-left-1">
											<span class="font-standard font-family-v1 font-color-v1 survey-space-left-3 pointer js-row-add"><?php echo __('survey.inquiry.common.mtx.add.title') ?></span>
										</div>
									</div>
								</div>

								<!-- 選択肢（行）のコンテナ -->
								<div class="matrix-column-container" style="padding: 0px 24px;">
									<div>
										<p class="survey-space-top-bottom-6"><?php echo __('survey.inquiry.common.mtx.option.column') ?></p>
									</div>
									<div class="sortable-matrix">
										<!-- 選択肢（行）1 -->
										<div class="flexbox-x-axis width-100 js-matrix-column-container matrix-column-container survey-space-top-3">
											<span class="icon-drag matrix-icon-draggable"></span>
											<div class="js-radio"><span class="icon-form-single-option-off"></span></div>
											<div class="js-checkbox display-none"><span class="icon-check"></span></div>
											<input type="text" class="width-100 border-none survey-space-around-2 survey-height-28 mtx-option-input mtx-option-x-input" onpaste="return false" placeholder="<?php echo (str_replace('{num}', "1", __('survey.inquiry.common.item.option.placeholder'))) ?>">
											<div class="delete-icon js-delete-icon js-delete-option pointer">
												<span class="icon-cancel-small"></span>
											</div>
										</div>
										<!-- 選択肢（行）2 -->
										<div class="flexbox-x-axis width-100 js-matrix-column-container matrix-column-container survey-space-top-3">
											<span class="icon-drag matrix-icon-draggable"></span>
											<div class="js-radio"><span class="icon-form-single-option-off"></span></div>
											<div class="js-checkbox display-none"><span class="icon-check"></span></div>
											<input type="text" class="width-100 border-none survey-space-around-2 survey-height-28 mtx-option-input mtx-option-x-input" onpaste="return false" placeholder="<?php echo (str_replace('{num}', "2", __('survey.inquiry.common.item.option.placeholder'))) ?>">
											<div class="delete-icon js-delete-icon js-delete-option pointer">
												<span class="icon-cancel-small"></span>
											</div>
										</div>
										<!-- 選択肢（行）コピー用 -->
										<div class="flexbox-x-axis width-100 js-matrix-column-container matrix-column-container survey-space-top-3 for-clone display-none">
											<span class="icon-drag matrix-icon-draggable"></span>
											<div class="js-radio"><span class="icon-form-single-option-off"></span></div>
											<div class="js-checkbox display-none"><span class="icon-check"></span></div>
											<input type="text" class="width-100 border-none survey-space-around-2 survey-height-28 mtx-option-input mtx-option-x-input" onpaste="return false" placeholder="<?php echo (str_replace('{num}', "2", __('survey.inquiry.common.item.option.placeholder'))) ?>">
											<div class="delete-icon js-delete-icon js-delete-option pointer">
												<span class="icon-cancel-small"></span>
											</div>
										</div>
									</div>
									<!-- 選択肢を追加する -->
									<div class="flexbox-x-axis width-100  js-add-matrix-column survey-space-top-5">
									    <div class="js-radio"><span class="icon-form-single-option-off"></span></div>
										<div class="js-checkbox display-none"><span class="icon-check"></div>
										<div class="flexbox-x-axis survey-space-left-1">
											<span class="font-standard font-family-v1 font-color-v1 pointer survey-space-left-3 js-column-add"><?php echo __('survey.inquiry.common.item.add.option') ?></span>
										</div>
									</div>
									<!-- 単一選択or複数選択 -->
									<div class="survey-space-top-1 flexbox-x-axis width-100">
										<div class="dropdown-container dropdown-shorter js-single-multiple">
											<div class="dropdown-selected">
												<span class="dropdown-selected-text"><?php echo __('survey.inquiry.common.opt') ?></span>
												<span class="icon-drop-down-close"></span>
											</div>
											<ul class="dropdown-options dropdown-shorter" style="top: -10px;">
												<li class="dropdown-option js-select-type-of-matrix js-select-type-of-matrix-dont-need-num"><span class="icon-form-single-option-on-large"></span><?php echo __('survey.inquiry.common.opt') ?></li>
												<li class="dropdown-option js-select-type-of-matrix js-select-type-of-matrix-need-num"><span class="icon-form-multi-option-on"></span><?php echo __('survey.inquiry.common.chk') ?></li>
											</ul>
										</div>
										<!-- 最低選択 -->
										<div class="right-aligned flexbox-x-axis js-matrix-count-container display-none">
											<div class="js-matrix-count-min dropdown-container dropdown-shorter">
												<div class="dropdown-selected js-dropdown-matrix-min">
													<span class="dropdown-selected-text"><?php echo __('survey.inquiry.common.item.select.min') ?></span>
													<span class="icon-drop-down-close"></span>
												</div>
												<ul class="dropdown-options dropdown-shorter" style="top: -30px;">
													<li class="dropdown-option js-matrix-type-single"><?php echo __('survey.inquiry.common.item.select.min') ?></li>
													<li class="dropdown-option js-matrix-type-single" data-min="1"><?php echo (str_replace('{num}', "1", __('survey.inquiry.common.mtx.select.num'))) ?></li>
													<li class="dropdown-option js-matrix-type-single display-none" data-min="2"><?php echo (str_replace('{num}', "2", __('survey.inquiry.common.mtx.select.num'))) ?></li>
													<li class="dropdown-option js-matrix-type-single display-none" data-min="3"><?php echo (str_replace('{num}', "3", __('survey.inquiry.common.mtx.select.num'))) ?></li>
													<li class="dropdown-option js-matrix-type-single display-none" data-min="4"><?php echo (str_replace('{num}', "4", __('survey.inquiry.common.mtx.select.num'))) ?></li>
													<li class="dropdown-option js-matrix-type-single display-none" data-min="5"><?php echo (str_replace('{num}', "5", __('survey.inquiry.common.mtx.select.num'))) ?></li>
													<li class="dropdown-option js-matrix-type-single display-none" data-min="6"><?php echo (str_replace('{num}', "6", __('survey.inquiry.common.mtx.select.num'))) ?></li>
													<li class="dropdown-option js-matrix-type-single display-none" data-min="7"><?php echo (str_replace('{num}', "7", __('survey.inquiry.common.mtx.select.num'))) ?></li>
													<li class="dropdown-option js-matrix-type-single display-none" data-min="8"><?php echo (str_replace('{num}', "8", __('survey.inquiry.common.mtx.select.num'))) ?></li>
												</ul>
											</div>
											<!-- 最大選択 -->
										　　<div class="js-matrix-count-max matrix-count-max dropdown-container dropdown-shorter">
												<div class="dropdown-selected js-dropdown-matrix-max">
													<span class="dropdown-selected-text"><?php echo __('survey.inquiry.common.item.select.max') ?></span>
													<span class="icon-drop-down-close"></span>
												</div>
												<ul class="dropdown-options dropdown-shorter" style="top: -30px;">
													<li class="dropdown-option js-matrix-type-multiple"><?php echo __('survey.inquiry.common.item.select.max') ?></li>
												    <li class="dropdown-option js-matrix-type-multiple" data-min="2"><?php echo (str_replace('{num}', "2", __('survey.inquiry.common.mtx.select.num'))) ?></li>
													<li class="dropdown-option js-matrix-type-multiple display-none" data-min="3"><?php echo (str_replace('{num}', "3", __('survey.inquiry.common.mtx.select.num'))) ?></li>
												    <li class="dropdown-option js-matrix-type-multiple display-none" data-min="4"><?php echo (str_replace('{num}', "4", __('survey.inquiry.common.mtx.select.num'))) ?></li>
													<li class="dropdown-option js-matrix-type-multiple display-none" data-min="5"><?php echo (str_replace('{num}', "5", __('survey.inquiry.common.mtx.select.num'))) ?></li>
													<li class="dropdown-option js-matrix-type-multiple display-none" data-min="6"><?php echo (str_replace('{num}', "6", __('survey.inquiry.common.mtx.select.num'))) ?></li>
													<li class="dropdown-option js-matrix-type-multiple display-none" data-min="7"><?php echo (str_replace('{num}', "7", __('survey.inquiry.common.mtx.select.num'))) ?></li>
													<li class="dropdown-option js-matrix-type-multiple display-none" data-min="8"><?php echo (str_replace('{num}', "8", __('survey.inquiry.common.mtx.select.num'))) ?></li>
													<li class="dropdown-option js-matrix-type-multiple display-none" data-min="9"><?php echo (str_replace('{num}', "9", __('survey.inquiry.common.mtx.select.num'))) ?></li>
												</ul>
											</div>
										</div>
									</div>
								</div>
							</div>
							<!-- 都道府県 + 市区町村：よくある質問のコンテナ -->
							<div class="survey-address-container survey-space-top-1 js-address-container display-none focusout" style="padding: 0px 24px;">
								<div class="flexbox-x-axis survey-space-top-1">
									<div class="dropdown-middle background-pale-gray flexbox-x-axis" style="margin:0 12px 0 0;padding:0 0 0 12px ;height:32px;"><?php echo __('survey.inquiry.common.txt.postcode') ?></div>
									<input type="text" class="survey-title js-input-column js-address-postcode font-standard font-family-v1 font-color-v3" placeholder="123-4567">
								</div>
								<div class="flexbox-x-axis survey-space-top-1">
									<div class="dropdown-middle background-pale-gray flexbox-x-axis" style="margin:0 12px 0 0;padding:0 0 0 12px ;height:32px;"><?php echo __('survey.inquiry.common.txt.prefecture') ?></div>
									<input type="text" class="survey-title js-input-column js-address-prefecture font-standard font-family-v1 font-color-v3" value="<?php echo __('survey.input.text.sel') ?>">
								</div>
								<div class="flexbox-x-axis survey-space-top-1">
									<div class="dropdown-middle background-pale-gray flexbox-x-axis" style="margin:0 12px 0 0;padding:0 0 0 12px ;height:32px;"><?php echo __('survey.inquiry.common.txt.city') ?></div>
									<input type="text" class="survey-title js-input-column js-address-municipalities font-standard font-family-v1 font-color-v3" placeholder="<?php echo __('survey.inquiry.common.txt.city.placeholder') ?>">
								</div>
								<!-- <div class="flexbox-x-axis survey-space-top-1">
									<div class="dropdown-middle background-pale-gray flexbox-x-axis" style="margin:0 12px 0 0;padding:0 0 0 12px ;height:32px;">番地</div>
									<input type="text" class="survey-title js-input-column js-address-houseNumber font-standard font-family-v1 font-color-v3" placeholder="12-34">
								</div>
								<div class="flexbox-x-axis survey-space-top-1">
									<div class="dropdown-middle background-pale-gray flexbox-x-axis" style="margin:0 12px 0 0;padding:0 0 0 12px ;height:32px;">建物名・部屋番号</div>
									<input type="text" class="survey-title js-input-column js-address-roomNumber font-standard font-family-v1 font-color-v3" placeholder="Aマンション 301号">
								</div> -->
							</div>
						</div>
						<!-- 削除、回答必須のコンテナ -->
						<div class="submit-btn-container js-button-container" style="margin:32px 0 0 auto; width: fit-content; width: -moz-fit-content; padding: 0 24px 0 0;">
							<div class="survey-btn" title="<?php echo __('survey.inquiry.common.delete.item') ?>">
								<span class="icon-delete js-edidind-survey-delete-icon"></span>
							</div>
							<!-- 項目コピー -->
							<div class="survey-btn js-copy-icon-container" title="<?php echo __('admin.common.label.item_copy') ?>">
								<img src="./../assets/admin/css/img/icon-copy-dark.svg" width="12" height="12">
							</div>
							<!-- 画像追加 -->
							<div class="survey-btn js-add-photo-icon" title="<?php echo __('survey.inquiry.common.item.image.add.title') ?>">
								<img src="./../assets/admin/css/img/icon-photo.svg" width="12" height="12" class="js-icon-photo-active" style="display:none;">
								<img src="./../assets/admin/css/img/icon-photo-unactive.svg" width="12" height="12" class="js-icon-photo-unactive">
							</div>
							<div class="survey-btn js-jump-icon-container" title="<?php echo __('survey.inquiry.common.branch.add') ?>">
								<img src="./../assets/admin/css/img/icon-jump-unactive.svg" width="12" height="12" class="js-edidind-survey-jump-unactive-icon">
								<img src="./../assets/admin/css/img/icon-jump-active.svg" width="12" height="12" class="js-edidind-survey-jump-active-icon" style="display: none;">
							</div>
							<div class="survey-btn js-from-page-icon-container" title="<?php echo __('survey.inquiry.common.item.title.new_page') ?>">
								<span class="icon-form-page-unactive js-edidind-survey-from-page-unactive-icon"></span>
								<span class="icon-form-page-active js-edidind-survey-from-page-active-icon display-none"></span>
							</div>
							<form action="#" method="post" class="js-survey-checkbox-container">
								<span class=""><?php echo __('survey.inquiry.common.label.required') ?></span>
								<label class="js-label checkbox-small-v1-label-off">
									<span class="js-span checkbox-small-v1-span-off"></span>
								</label>
								<input type="checkbox" name="" value="" class="display-none">
							</form>
						</div>
						<!-- コンテナ削除時のモーダルウィンドウ -->
						<div class="modal-smaller-container js-survey-delete-container" style="display: none;">
							<div class="modal-small-title-container">
								<h4 class="modal-small-title"><?php echo __('survey.inquiry.common.delete.item.title') ?></h4>
								<span class="icon-cancel-large js-survey-modal-close-button"></span>
							</div>
							<p style="margin: 32px 0 0 10px;"><?php echo __('admin.common.modal.delete.confirm') ?></p>
							<div class="submit-btn-container" style="position: fixed; bottom: 12px;">
								<div class="btn-larger btn-red js-survey-modal-delete-button"><?php echo __('admin.common.button.delete') ?></div>
								<div class="btn-larger btn-white js-survey-modal-cancel-button"><?php echo __('admin.common.button.cancel') ?></div>
							</div>
						</div>
						<div class="modal-background js-survey-questions-modal-background display-none"></div>
						<!-- 分岐追加のモーダルウィンドウ -->
						<div class="survey-branch-modal-container js-survey-questions-jump-setting-container display-none">
							<h4 class="font-standard font-size-v5 font-family-v4"><?php echo __('survey.inquiry.common.branch.set') ?></h4>
							<p class="font-standard font-size-v3 font-family-v4 font-color-v1 survey-space-all-around"><?php echo __('survey.inquiry.common.branch.destination') ?><span class="js-current-survey"></span></p>
							<div class="modal-container-context">
								<div class="survey-branch-container js-branch-setting-container cloned">
									<div class="flexbox-x-axis">
										<input type="text" class="text-input-longer survey-space-all-around-5 js-branch-setting-title">
										<span class="icon-delete right-aligned js-delete-icon" style="width: 50px; cursor: pointer;"></span>
									</div>
									<div class="survey-space-all-around-4 js-add-branch-container">
										<h4 class="font-standard font-family-v4"><?php echo __('admin.common.label.conditions') ?></h4>
										<div class="add-branch-pulldown-container js-add-pulldown-container">
											<div class="dropdown-container dropdown-longer js-question-dropdown">
												<div class="dropdown-selected">
													<span class="dropdown-selected-text"><?php echo __('survey.inquiry.common.branch.select.title') ?></span>
													<span class="icon-drop-down-close"></span>
												</div>
												<ul class="dropdown-options survey-width-fit dropdown-longer">
													<li class="dropdown-option js-survey-add-branch-modal-select-survey-title for-clone display-none"></li>
												</ul>
											</div>
											<div class="dropdown-container dropdown-longer js-answer-dropdown">
												<div class="dropdown-selected">
													<span class="dropdown-selected-text"><?php echo __('survey.inquiry.common.branch.select.answer') ?></span>
													<span class="icon-drop-down-close"></span>
												</div>
												<ul class="dropdown-options survey-width-fit dropdown-longer">
													<li class="dropdown-option js-survey-add-branch-modal-select-survey-option for-clone display-none"></li>
												</ul>
											</div>
											<div class="dropdown-container dropdown-longer js-branch-option-container" style="min-width: auto;">
												<div class="dropdown-selected">
													<span class="dropdown-selected-text" data-option="equal"><?php echo __('survey.branch.select.equal') ?></span>
													<span class="icon-drop-down-close"></span>
												</div>
												<ul class="dropdown-options survey-width-fit dropdown-longer"style="min-width: auto;">
													<li class="dropdown-option" data-option="equal"><?php echo __('survey.branch.select.equal') ?></li>
													<li class="dropdown-option" data-option="not_equal"><?php echo __('survey.branch.select.not_equal') ?></li>
													<li class="dropdown-option" data-option="include"><?php echo __('survey.branch.select.include') ?></li>
													<li class="dropdown-option" data-option="not_include"><?php echo __('survey.branch.select.not_include') ?></li>
													<li class="dropdown-option" data-option="greater"><?php echo __('survey.branch.select.greater') ?></li>
													<li class="dropdown-option" data-option="smaller"><?php echo __('survey.branch.select.smaller') ?></li>
													<li class="dropdown-option" data-option="equal_or_greater"><?php echo __('survey.branch.select.equal_or_greater') ?></li>
													<li class="dropdown-option" data-option="equal_or_smaller"><?php echo __('survey.branch.select.equal_or_smaller') ?></li>
												</ul>
											</div>
											<div class="btn-smaller btn-gray-white js-add-button">
												<span class="icon-add-white icon-add-white-svg"></span>
												<?php echo __('admin.itme.item.classification_add') ?>
											</div>
										</div>
										<div>
											<span class="font-standard font-family-v1 font-color-v3 js-text"><?php echo __('survey.inquiry.common.branch.add.and') ?></span>
											<div class="survey-space-all-around-3 flexbox-inline-x-axis icon-round-corners-small icon-background-light-blue js-added-branch-condition for-clone display-none">
												<span class="font-standard font-family-v1 flexbox-x-axis survey-space-right-1" style="overflow: hidden;"></span>
												<img src="./../assets/admin/css/img/icon-cancel-small.svg" class="js-delete-icon" width="12" height="12">
											</div>
											<span class="font-standard font-family-v1 font-color-v3 js-omit-next" style="display: none">･･･</span>
										</div>
									</div>
									<!-- 分岐条件削除時のモーダルウィンドウ -->
									<div class="modal-smaller-container js-survey-delete-container" style="display: none;">
										<div class="modal-small-title-container">
											<h4 class="modal-small-title"><?php echo __('survey.inquiry.common.branch.delete') ?></h4>
											<span class="icon-cancel-large survey-modal-close-button js-survey-modal-close-button"></span>
										</div>
										<p style="margin: 32px 0 0 10px;"><?php echo __('admin.common.modal.delete.confirm') ?></p>
										<div class="submit-btn-container" style="position: fixed; bottom: 12px;">
											<div class="btn-larger btn-red js-survey-modal-delete-branch-button"><?php echo __('admin.common.button.delete') ?></div>
											<div class="btn-larger btn-white survey-modal-cancel-button js-survey-modal-branch-cancel-button"><?php echo __('admin.common.button.cancel') ?></div>
										</div>
									</div>
									<div class="modal-background js-survey-questions-modal-background display-none"></div>
								</div>
								<div class="survey-branch-container js-branch-setting-container for-clone display-none">
									<div class="flexbox-x-axis">
										<span class="font-standard font-size-v3 font-family-v3 font-color-v1 survey-space-all-around-5 branch-or-title"><?php echo __('survey.inquiry.common.branch.label.or') ?></span>
										<input type="text" class="text-input-longer survey-space-top-5 js-branch-setting-title branch-or-title-input">
										<span class="icon-delete right-aligned js-delete-icon" style="width: 50px; cursor: pointer;"></span>
									</div>
									<div class="survey-space-all-around-4 js-add-branch-container">
									<h4 class="font-standard font-family-v4"><?php echo __('admin.common.label.conditions') ?></h4>
										<div class="add-branch-pulldown-container js-add-pulldown-container">
											<div class="dropdown-container dropdown-longer js-question-dropdown">
												<div class="dropdown-selected">
													<span class="dropdown-selected-text"><?php echo __('survey.inquiry.common.branch.select.title') ?></span>
													<span class="icon-drop-down-close"></span>
												</div>
												<ul class="dropdown-options survey-width-fit dropdown-longer">
													<li class="dropdown-option js-survey-add-branch-modal-select-survey-title for-clone display-none"></li>
												</ul>
											</div>
											<div class="dropdown-container dropdown-longer js-answer-dropdown">
												<div class="dropdown-selected">
													<span class="dropdown-selected-text"><?php echo __('survey.inquiry.common.branch.select.answer') ?></span>
													<span class="icon-drop-down-close"></span>
												</div>
												<ul class="dropdown-options survey-width-fit dropdown-longer">
													<li class="dropdown-option js-survey-add-branch-modal-select-survey-option for-clone display-none"></li>
												</ul>
											</div>
											<div class="dropdown-container dropdown-longer js-branch-option-container" style="min-width: auto;">
												<div class="dropdown-selected">
													<span class="dropdown-selected-text" data-option="equal"><?php echo __('survey.branch.select.equal') ?></span>
													<span class="icon-drop-down-close"></span>
												</div>
												<ul class="dropdown-options survey-width-fit dropdown-longer"style="min-width: auto;">
													<li class="dropdown-option" data-option="equal"><?php echo __('survey.branch.select.equal') ?></li>
													<li class="dropdown-option" data-option="not_equal"><?php echo __('survey.branch.select.not_equal') ?></li>
													<li class="dropdown-option" data-option="include"><?php echo __('survey.branch.select.include') ?></li>
													<li class="dropdown-option" data-option="not_include"><?php echo __('survey.branch.select.not_include') ?></li>
													<li class="dropdown-option" data-option="greater"><?php echo __('survey.branch.select.greater') ?></li>
													<li class="dropdown-option" data-option="smaller"><?php echo __('survey.branch.select.smaller') ?></li>
													<li class="dropdown-option" data-option="equal_or_greater"><?php echo __('survey.branch.select.equal_or_greater') ?></li>
													<li class="dropdown-option" data-option="equal_or_smaller"><?php echo __('survey.branch.select.equal_or_smaller') ?></li>
												</ul>
											</div>
											<div class="btn-smaller btn-gray-white js-add-button">
												<span class="icon-add-white icon-add-white-svg"></span>
												<?php echo __('admin.itme.item.classification_add') ?>
											</div>
										</div>
										<div>
											<span class="font-standard font-family-v1 font-color-v3 js-text"><?php echo __('survey.inquiry.common.branch.add.and') ?></span>
											<div class="survey-space-all-around-3 flexbox-inline-x-axis icon-round-corners-small icon-background-light-blue js-added-branch-condition for-clone display-none" style="align-items: center;">
												<span class="font-standard font-family-v1 flexbox-x-axis survey-space-right-1" style="overflow: hidden;"></span>
												<img src="./../assets/admin/css/img/icon-cancel-small.svg" class="js-delete-icon" width="12" height="12">
											</div>
											<span class="font-standard font-family-v1 font-color-v3 js-omit-next" style="display: none">･･･</span>
										</div>
									</div>
									<!-- 分岐条件削除時のモーダルウィンドウ -->
									<div class="modal-smaller-container js-survey-delete-container" style="display: none;">
										<div class="modal-small-title-container">
											<h4 class="modal-small-title"><?php echo __('survey.inquiry.common.branch.delete') ?></h4>

											<span class="icon-cancel-large survey-modal-close-button js-survey-modal-close-button"></span>
										</div>
										<p style="margin: 32px 0 0 10px;"><?php echo __('admin.common.modal.delete.confirm') ?></p>
										<div class="submit-btn-container" style="position: fixed; bottom: 12px;">
											<div class="btn-larger btn-red js-survey-modal-delete-branch-button"><?php echo __('admin.common.button.delete') ?></div>
											<div class="btn-larger btn-white survey-modal-cancel-button js-survey-modal-branch-cancel-button"><?php echo __('admin.common.button.cancel') ?></div>
										</div>
									</div>
									<div class="modal-background js-survey-questions-modal-background display-none"></div>
								</div>
								<div class="btn-add-branch js-add-branch-modal-add-container-button">
									<p>
										<span class="font-standard font-size-v3 font-family-v3 font-color-v1 survey-space-around-4"><?php echo __('survey.inquiry.common.branch.label.or') ?></span>
										<span class="icon-add"></span><?php echo __('survey.inquiry.common.branch.add') ?></p>
								</div>
							</div>
							<div class="submit-btn-container" style="margin: 20px 0 12px 0;">
								<div class="btn-larger btn-blue js-survey-modal-add-button active"><?php echo __('survey.inquiry.common.branch.set_btn') ?></div>
								<div class="btn-larger btn-white js-survey-modal-cancel-button"><?php echo __('admin.common.button.cancel') ?></div>
								<div class="btn-larger btn-red-border js-delete-all-modal-branch">
									<span class="icon-delete"></span>
								</div>
							</div>
						</div>
						<div class="modal-background js-survey-questions-jump-setting-modal-background display-none"></div>
					</div>
				</div>
				<!-- 質問形式選択 -->
				<div class="survey-add-container relative js-survey-add-container not-sortable">
					<div class="btn-add-survey js-add-survey-button js-add-survey-button-in-section">
						<img src="./../assets/admin/css/img/icon-add.svg" width="12" height="12">
						<span><?php echo __('survey.inquiry.common.label.item.add') ?></span>
					</div>
					<div class="dropdown-container" style="height: 0; border: 0;">
						<ul class="dropdown-options dropdown-middle" style="top: auto;">
							<li class="dropdown-option js-survey-add-edit-container-single">
								<span class="icon-form-single-option-on-large"></span><?php echo __('survey.inquiry.common.opt') ?>
							</li>
							<li class="dropdown-option js-survey-add-edit-container-multi">
								<span class="icon-form-multi-option-on"></span><?php echo __('survey.inquiry.common.chk') ?>
							</li>
							<li class="dropdown-option js-survey-add-edit-container-pulldown">
								<span class="icon-form-pulldown-option-on"></span><?php echo __('survey.inquiry.common.sel') ?>
							</li>
							<li class="dropdown-option js-survey-add-edit-container-short-text">
								<span class="icon-form-writing-short-text"></span><?php echo __('survey.inquiry.common.txt') ?>
							</li>
							<li class="dropdown-option js-survey-add-edit-container-long-text">
								<span class="icon-form-writing-text"></span><?php echo __('survey.inquiry.common.txa') ?>
							</li>
							<li class="dropdown-option js-survey-add-edit-container-attachment">
								<span class="icon-form-upload"></span><?php echo __('survey.inquiry.common.fup') ?>
							</li>
							<li class="dropdown-option js-survey-add-edit-container-freespace">
								<span class="icon-form-fq"></span><?php echo __('survey.inquiry.common.frs') ?>
							</li>
							<li class="dropdown-option js-survey-add-edit-container-matrix">
								<span class="icon-form-matrix-option-on"></span><?php echo __('survey.inquiry.common.mtx') ?>
							</li>
							<!-- よく聞く質問 -->
							<li class="dropdown-option js-pulldown-parent relative">
								<span class="icon-form-fleq" style="margin: 0 8px 0 0;"></span>	
								<?php echo __('survey.inquiry.common.fleq') ?>
								<span class="pulldown-more"></span>
								<ul class="pulldown-list-children2">
									<li class="dropdown-option js-survey-add-edit-container-address">
										<span class="icon-edit-bar-add-q"></span><?php echo __('survey.inquiry.common.spl.address.prefecture_city') ?>
									</li>
								</ul>
							</li>
						</ul>
					</div>
				</div>
			</section>
			<!-- 追加ボタン(質問、セクション) -->
			<div class="survey-add-container relative js-survey-add-container not-sortable" style="position:relative;">
				<div class="btn-add-section js-add-section-button">
					<img src="./../assets/admin/css/img/icon-add-section.svg" width="12" height="12">
					<span><?php echo __('survey.inquiry.common.label.section.add') ?></span>
				</div>
			</div>
			<!-- 追加ボタン(クーポン設定) -->
			<div class="survey-add-container relative js-survey-add-container not-sortable" style="position:relative;">
				<div class="btn-add-section js-coupon-setting-button">
					<img src="./../assets/admin/css/img/icon-add-coupon.svg" width="12" height="12">
					<span><?php echo __('survey.inquiry.common.label.coupon.setting') ?></span>
				</div>
			</div>
			<div class="js-coupon-setting-wrapper" style="margin:24px 0 0 24px; <?php echo (empty($present) || $present === "" || $present === []) ? 'display:none;' : ''; ?>">
				<div class="survey-questions-border"></div>
				<div style="display: flex;">
					<h4 class="font-standard font-family-v4 left-aligned"><?php echo __('survey.inquiry.common.label.coupon.setting') ?></h4>
					<div  class="js-coupon-delete-all" style="display: flex; cursor: pointer;">
						<span class="icon-action-section delete"></span>
						<span style="margin-left:10px"><?php echo __('survey.inquiry.common.coupon.delete.all') ?></span>
					</div>
				</div>
				<div class="js-coupon-setting-container couponSettingSortable">
					<!-- クーポン追加 コピー用 -->
					<div class="js-coupon-setting for-clone display-none" style="border: solid 1px #e3e5e8;">
						<div>
							<div style="display:flex;">
								<div style="display:flex;">
									<span class="icon-drag js-icon-drag-coupon" style="cursor: move;display: block;margin-right: 10px;"></span>
								</div>
								<div class="js-pulldown-setting-container" style="display:flex; width:100%;">
									<div class="basic-label"><?php echo __('admin.survey.label.present'); ?><span class="js-coupon-number"></span></div>
								</div>
								<div style="margin-left:auto">
									<span class="js-coupon-delete-icon icon-action-section delete"></span>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="js-add-coupon border" style="cursor:pointer; border: solid 1px #e3e5e8; display:flex;">
					<img src="./../assets/admin/css/img/icon-add.svg" width="12" height="12">
					<div style="margin-left:40px;"><?php echo __('survey.inquiry.common.coupon.add') ?></div>
				</div>
			</div>
			<!-- submit ボタン -->
			<div class="submit-btn-container" style="margin: 44px 0 0 110px;">
				<div class="btn-larger btn-blue js-survey-entry-save-button" id="saveBaseButton"><?php echo __('admin.common.button.save') ?></div>
				<div class="survey-preview-button btn-larger btn-gray-black">
					<!-- survey/?f=ozmall&survey_id=777001&surveyonly=1 -->
					<a class="flexbox-center height-100 width-100" href="<?php echo $verify_url?>" target="_blank" style="color: #000;" onfocus="this.blur();"><?php echo __('admin.common.button.verify') ?></a>
				</div>
				<div class="btn-larger btn-white js-back-btn"><?php echo __('admin.common.button.return_to_list') ?></div>
			</div>
		</div>
	</div>
</div>

<!-- template -->
<!-- 画像追加 選択肢表示-->
<div class="flexbox-center add-image-container js-add-image-container clone"style="min-width: 144px; height: 108px;display: none;position: relative;">
	<div class="dropdown-container">
		<span class="icon-form-photo" style="margin:0 10px 0 0;"></span><?php echo __('survey.input.text.fup') ?>
	</div>
</div>
<!-- template -->
<!-- 画像追加モーダル -->
<div class="modal-image-container js-modal-image-container clone" style="display: none;">
	<div class="relative" style="min-height: 100%;">
		<!-- タイトル -->
		<div class="flexbox relative">
			<h4 class="font-standard font-size-v5 font-family-v2" style="margin: 0;"><?php echo __('survey.input.text.fup') ?></h4>
			<span class="js-close-modal-image icon-cancel-large survey-position-absolute-v2 pointer"></span>
		</div>
		<!-- アップロード -->
		<div class="" style="margin: 22px 0 0 24px;">
			<label class="survey-drag-or-click flexbox-center">
			<svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12">
				<g fill="none" fill-rule="evenodd" stroke-linecap="round" stroke-linejoin="round">
					<g stroke="#245BD6" stroke-width="2">
						<g>
							<path d="M1 8L1 11 11 11 11 8" transform="translate(-512 -298) translate(512 298)"/>
							<path d="M4 2L6 4 8 2" transform="translate(-512 -298) translate(512 298) matrix(1 0 0 -1 0 6)"/>
							<path d="M6 8L6 2" transform="translate(-512 -298) translate(512 298) matrix(1 0 0 -1 0 10)"/>
						</g>
					</g>
				</g>
			</svg>
				<input type="file" name="image" id="myImage" accept="image/*" style="display:none;">
				<span class="font-standard font-family-v2 font-color-v3"><?php echo __('admin.common.modal.image.flie.upload') ?></span>
			</label>
			<p class="font-standard font-family-v2 survey-space-top-4"><?php echo __('admin.common.modal.image.flie.category') ?></p>
			<div class="js-modal-image-preview modal-image-preview flexbox" style="display: none;">
				<div>
					<img src="" class="" style="width: 300px;height: 200px;object-fit: cover;" id="preview">
				</div>
				<div>
					<div class="flexbox-x-axis survey-width-430">
					</div>
				</div>
			</div>
		</div>
		<!-- SUBMIT -->
		<div class="submit-btn-container modal-image-button" style="margin: 0 0 0 25px;">
			<div class="btn-larger btn-gray-white js-save-modal-image" type="button"><?php echo __('survey.inquiry.common.branch.set_btn') ?></div>
			<div class="btn-larger btn-white js-cancel-modal-image" type="button"><?php echo __('admin.common.button.cancel') ?></div>
		</div>
	</div>
</div>
<!-- template -->
<!-- 写真削除 -->
<div class="modal-smaller-container js-modal-delete-image" style="display: none;">
	<div class="modal-small-title-container">
		<h4 class="modal-small-title"><?php echo __('survey.inquiry.common.item.image.confirm.delete') ?></h4>
		<svg class="survey-modal-close-button js-close-modal-image" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
			<g fill="none" fill-rule="evenodd" stroke-linecap="round">
				<g stroke="#3D3F45" stroke-width="2">
					<g>
						<path d="M16 16L0 0" transform="translate(4 4)"/>
						<path d="M16 16L0 0" transform="translate(4 4) matrix(-1 0 0 1 16 0)"/>
					</g>
				</g>
			</g>
		</svg>
	</div>
	<p style="margin: 32px 0 0 10px;"><?php echo __('admin.common.modal.delete.confirm') ?></p>
	<div class="submit-btn-container" style="position: fixed; bottom: 12px;">
		<div class="btn-larger btn-red js-modal-delete-image-btn"><?php echo __('admin.common.button.delete') ?></div>
		<div class="btn-larger btn-white js-modal-close-image-btn"><?php echo __('admin.common.button.cancel') ?></div>
	</div>
</div>
<!-- template -->
<!-- 写真削除 -->
<div class="modal-smaller-container js-modal-hide-image-input" style="display: none;">
	<div class="modal-small-title-container">
		<h4 class="modal-small-title"><?php echo __('survey.inquiry.common.item.image.confirm.display_none') ?></h4>
		<svg class="survey-modal-close-button js-close-modal-image" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
			<g fill="none" fill-rule="evenodd" stroke-linecap="round">
				<g stroke="#3D3F45" stroke-width="2">
					<g>
						<path d="M16 16L0 0" transform="translate(4 4)"/>
						<path d="M16 16L0 0" transform="translate(4 4) matrix(-1 0 0 1 16 0)"/>
					</g>
				</g>
			</g>
		</svg>
	</div>
	<p style="margin: 32px 0 0 10px;"><?php echo __('survey.inquiry.common.item.image.confirm.delete.title') ?></p>
	<div class="submit-btn-container" style="position: fixed; bottom: 12px;">
		<div class="btn-larger btn-red js-modal-hide-image-input-btn"><?php echo __('admin.common.button.delete') ?></div>
		<div class="btn-larger btn-white js-modal-close-image-btn"><?php echo __('admin.common.button.cancel') ?></div>
	</div>
</div>
<!-- template -->
<!-- モーダル背景 -->
<div class="modal-background js-modal-background" style="display: none;"></div>