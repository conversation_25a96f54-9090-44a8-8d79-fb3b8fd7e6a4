<script>
	const _talk_data = <?php echo json_encode($talk_data, JSON_UNESCAPED_UNICODE)?>;
	const _talk_data_ja = <?php echo json_encode($talk_data_ja, JSON_UNESCAPED_UNICODE)?>;
</script>
<!-- BEGIN PAGE HEADER-->
			<!-- BEGIN PAGE HEAD -->
			<div id="page_type" style="display:none;"><?php echo $_action?></div>
			<div class="page-head">
				<!-- BEGIN PAGE TITLE -->
				<div class="page-title" style="float:none;width:100%;">
					<h1 style="float:left;margin-right:20px;">FAQ編集<small></small></h1>
				</div>
				<div class="page-title" style="float:none;width:100%;display:inline-block;">
					<?php 
					if ($faq_div != '') {
						echo($classbox);
					}
					if (count($history_list) > 0) {
						echo('<div style="float:right;margin-top:-5px;">');
						echo Form::select('history_no', $history_list, $history_no, array('id'=>'history_no','class'=>'form-control'));
						echo('</div>');
						echo('<label class="control-label" style="float:right;margin:-5px 10px 0px 10px;"><i class="fas fa-history"></i></label>');
					}
					?>					
				</div>				
				<!-- END PAGE TITLE -->
			</div>
			<!-- END PAGE HEAD -->
			<!-- END PAGE HEADER-->
			<!-- BEGIN PAGE CONTENT-->

	        <!-- Page Content -->
	        <div id="page-wrapper">
		        <div class="portlet light">
			        <div class="tabbable-line">
				        <ul class="nav nav-tabs ">
				        	<?php
				        	foreach($_bot_lang as $k=>$v) {
								if (!in_array($k, $faq_langs)) continue;
				        		$style='';
				        		if ($k == $lang_cd) $style='active';
				        		if ($mode == 'new') {
				        			echo('<li class="' . $style . '"><a href="/admin/' . $_action . '?mode=new&class_cd='. $class_cd .  '&lang=' . $k . '">');
				        		}
				        		else {
				        			echo('<li class="' . $style . '"><a href="/admin/' . $_action . '?cd=' . $intent_cd . '&lang=' . $k . '&sub_cd=' . $sub_intent_cd . '">');
				        		}
				        		echo($_bot_lang[$k]);
				        		echo('</a></li>');
				        	}
				        	if ($faq_div != '') {
				        		$style='';
				        		if (strpos($_uri, 'mode') !== false) $style="active";
				        		echo('<li style="display:none;" class="' . $style . '"><a href="/admin/' . $_action . '?mode=cate&lang=ja&cd=' . $intent_cd . '&sub_cd=' . $sub_intent_cd . '">カテゴリー</a></li>');
				        	}
				        	?>
				        	<li class="">
								<a href="/admin/<?php echo $_action?>s">
								一覧に戻る</a>
							</li>
				        </ul>
					</div>
					<div class="portlet box">
						<div class="portlet-body">			        
						<div class="row">
							<?php if (count($sub_bots) > 0) { ?>
							<div class="talkappi-pulldown js-select-bot" style="margin-bottom:20px;width:400px;" data-name="bot_id" data-value=<?php echo $bot_id?> data-source='<?php echo json_encode($sub_bots, JSON_UNESCAPED_UNICODE)?>'></div>
							<?php }?>
							<blockquote>
								<p>
									 <?php
									 if ($item_talk['facility_question_title'] != NULL && $item_talk['facility_question_title'] != '') {
										echo($item_talk['facility_question_title']);
									 }
									 else {
										 echo($item_talk['question']);
									}
									?>
								</p>
								<?php 
								if ($item_talk['upd_time']!=NULL) echo('</br><small style="font-size:60%;float:right;">' . $item_talk['upd_time'] . ' by ' . $item_talk['name'] . '</small>');
								if ($mode != 'cate')
								for($i = 1; $i <= $max_no; $i++) {
									$answer = $item_talk['answer' . $i];
									if ($answer != NULL) {
										$pos = -1;
										$num = 1;
										echo('<p style="font-size:10px;margin-right:10px;">回答例</p><p id="sample' . $i . '" style="font-size:12px;>');
										do {
											$pos = strpos($answer, '{');
											if (!$pos) {
												echo('<span id="span'. $i . $num .'">' . $answer . '</span>');
												break;
											}
											echo('<span id="span'. $i . $num .'">' . substr($answer, 0, $pos) . '</span>');
											$answer = substr($answer, $pos);
											
											$pos = strpos($answer, '}');
											
											echo('<a href="#" id="param'. $i . $num .'" data-type="text" data-pk="1" data-original-title="パラメータを記入" class="editable editable-click">');
											echo(substr($answer, 0, $pos+1));
											echo('</a>');
											$num++;
						
											$answer = substr($answer, $pos+1);
										} while (TRUE);
										echo('</p>');
										echo('<div class="btn-group btn-group-solid" style="margin:8px 0px 8px 20px;">');
										$color = array();
										$color[] = 'red';
										$color[] = 'green';
										$color[] = 'blue';
										$color[] = 'yellow';
										$color[] = 'grey';
										$color[] = 'red';
										$color[] = 'green';
										$color[] = 'blue';
										$color[] = 'yellow';
										$color[] = 'grey';
										for($j = 1; $j <= $max_no; $j++) {
											echo('<button type="button" id="btn'. $i . $j . '" class="btn ' . $color[$j-1] . ' copy-answer">' . '回答' . $j . 'へ' . '</button>');
										}
										echo('</div>');
									}
								}
								?>									
							</blockquote>						
							<div class="col-md-8">
									<input type="hidden" name="act" id="act" value="" />
									<input type="hidden" name="cd" id="cd" value="<?php echo($intent_cd)?>" />
									<input type="hidden" name="sub_cd" id="sub_cd" value="<?php echo($sub_intent_cd)?>" />			
									<input type="hidden" name="lang" id="lang" value="<?php echo($lang_cd)?>" />
									<input type="hidden" name="question" id="question" value="<?php echo($item_talk['question'])?>" />
									<input type="hidden" id="class_div" name="class_div" value="<?php echo $faq_div?>" />
									<input type="hidden" name="class_cd_hidden" id="class_cd_hidden" value="<?php echo $intent_type_cd?>" />
									<input type="hidden" id="mode" name="mode" value="<?php echo $mode?>" />			
									<input type="hidden" name="max_no" id="max_no" value="<?php echo $max_no?>" />				
									<div class="form-body">
								<?php /* 
								if (count($sub_intent_array) > 1) {
									echo('<div class="tabbable-line" style="margin:0 0 10px 40px">');
									echo('<ul class="nav nav-tabs ">');
									foreach($sub_intent_array as $sub_intent) {
										if ($sub_intent == $sub_intent_cd) {
											echo('<li class="active">');
										}
										else {
											echo('<li class="">');
										}
										$sub_intent_title = $sub_intent;
										if ($sub_intent == '') $sub_intent_title = 'デフォルト';
										echo('<a href="/admin/talk?cd=' . $intent_cd . '&lang=' . $lang_cd . '&sub_cd=' . $sub_intent .'">' . $sub_intent_title . '</a>');
										echo('</li>');
									}
									echo('</ul></div>');
									
								}*/?>
<!-- 								
									<div class="form-group">
										<div class="col-md-2">
											<div class="bootstrap-switch-container" style="margin-left: 60px;">
												<label class="bootstrap-switch-label">&nbsp;</label>
												<input name="fix_answer" id="switch-mode" type="checkbox" class="make-switch" checked="on" data-on-color="success" data-off-color="warning">	
												標準回答利用										
											</div>		
										</div>
										<div class="col-md-10"></div>								
									</div>
 -->
									<?php if (false && $faq_div != '') { ?>
  									<div class="form-group" <?php if ($mode!= 'cate') echo('style="display:none;"');?>>
 										<label class="control-label col-md-2">カテゴリー</label>
										<div class="col-md-2">
											<?php echo Form::select('class_cd3', [], '', array('id'=>'class_cd3','class'=>'form-control class'))?>
										</div>
										<div class="col-md-2" <?php if ($class_cd_len<4) echo(' style="display:none;"');?>>
											<?php echo Form::select('class_cd2', [], '', array('id'=>'class_cd2','class'=>'form-control class'))?>
										</div>
										<div class="col-md-2" <?php if ($class_cd_len<6) echo(' style="display:none;"');?>>
											<?php echo Form::select('class_cd1', [], '', array('id'=>'class_cd1','class'=>'form-control class'))?>
										</div>
										<div class="col-md-2" <?php if ($class_cd_len<8) echo(' style="display:none;"');?>>
											<?php echo Form::select('class_cd', [], '', array('id'=>'class_cd','class'=>'form-control class'))?>
										</div>
									</div>
									<?php }
									else if (false && $class_cd != NULL) {?>
  									<div class="form-group" <?php if ($mode!= 'cate') echo('style="display:none;"');?>>
 										<label class="control-label col-md-2">カテゴリー</label>								
										<div class="col-md-3">
										<?php echo Form::select('class_cd', $class_types, $class_cd, array('id'=>'class_type','class'=>'form-control'))?>
										</div>
									</div>
									<?php }?>
									<?php if ($mode!= 'cate') {?>	
 									<?php if ($class_cd != NULL) {
 										unset($_codes['03']['car']);
 										?>
 									<?php }?>							
  									<div class="form-group" <?php if($item_talk != null && $item_talk['level'] != 4) echo('style="display:none;"');?>>
 										<label class="control-label col-md-2" style="font-size:12px;">質問</label>
										<div class="col-md-10">
										<input name="question_input" type="text" maxlength="500" class="form-control" placeholder="" value="<?php echo($item_talk['question'])?>">
										</div>
									</div>
										
  									<div class="form-group" <?php if (count($area_arr) < 2) echo(' style="display:none;"')?>>
 										<label class="control-label col-md-2">エリアコード</label>
										<div class="col-md-3">
											<?php echo Form::select('area_cd', $area_arr, $area_cd, array('id'=>'area_cd','class'=>'form-control'))?>										
										</div>
									</div>
 									<div class="form-group">
 										<label class="control-label col-md-2" style="font-size:12px;">サマリータイトル</label>
										<div class="col-md-10">
										<textarea name="answer_summary_title" id="answer_summary_title" maxlength="500" class="form-control" placeholder="回答サマリーが設定された場合のみ、設定必要" rows="2"><?php echo($item_talk['answer_summary_title'])?></textarea>
										</div>
									</div>
									<?php 
									$border_colors = array('#b93524','#35aa47','#1a6ef6','#ffa415','#cccccc','#b93524','#35aa47','#1a6ef6','#ffa415','#cccccc');
									
									for($j = 1; $j <= $max_no; $j++) {
										echo('<div class="form-group">');
										//echo('<div class="col-md-1" style="text-align:right;"><span class="badge badge-danger"> 回答' . $i . '</span></div>');
										echo('<div class="col-md-2" style="text-align:right;">');
										echo('<select name="answer' . $j . '_type_cd" id="answer' . $j . '_type_cd" class="form-control answer-type" style="font-size:12px;width:100px;float:right;border-color:' . $border_colors[$j-1] . '">');
										foreach($_codes['03'] as $code3=>$name3) {
											$select = '';
											if ($item_talk['answer' . $j . '_type_cd'] == $code3) $select = ' selected ';
											$disabled = '';
											if ($item_talk['answer' . $j . '_type_cd'] == 'car' && $_user->role_cd != "99") $disabled = 'disabled';
											echo('<option '. $disabled . ' value ="' . $code3. '"' . $select . '>' . $name3 . '</option>');
										}
										echo('</select>');
										?>
										<?php 
										echo('</div>');
										echo('<div class="col-md-10">');
										$readonly = '';
										if ($item_talk['answer' . $j . '_type_cd'] == 'car' && $_user->role_cd != "99") $readonly = 'readonly';
										echo('<textarea name="' . 'answer' . $j . '_summary' . '" class="form-control" placeholder="回答サマリー" maxlength="200">' .  $item_talk['answer' . $j . '_summary'] . '</textarea>');
										echo('<div><textarea ' . $readonly . ' class="form-control answer-content" id="answer' . $j . '" name="answer' . $j . '" rows="2">' . $item_talk['user_answer' . $j] . '</textarea>');
										if ($item_talk['answer' . $j . '_type_cd'] == 'car') {
											echo('<div id="answer_skill_show' . $j . '" style="line-height:2;word-break: break-all;">');
											foreach($talk_skills["answer_skill_show" . $j] as $skill) {
												echo('<span class="alert alert-success alert-dismissable" style="padding:0px 5px;cursor:pointer;">' .$skill . '</span>');
											}					
											echo('<span class="alert alert-success alert-dismissable" style="padding:0px 5px;cursor:pointer;">+</span>');
											echo('</div>');
										}
										else {
											echo('<div id="answer_skill_show' . $j . '" style="line-height:2;word-break: break-all;display:none;">');
											echo('<span class="alert alert-success alert-dismissable" style="padding:0px 5px;cursor:pointer;">+</span>');
											echo('</div>');
										}
										echo('</div>');
										echo('<input type="hidden" id="answer_no' . $j . '" name="answer_no' . $j . '" value="" />' );
										?>
										<div style="display:none;" id="answer<?php echo $j?>_file">
											<div class="fileinput fileinput-new" data-provides="fileinput">
												<div class="input-group input-large">
													<div class="form-control uneditable-input span3" data-trigger="fileinput">
														<i class="fa fa-file fileinput-exists"></i>&nbsp; <span class="fileinput-filename">
														</span>
													</div>
													<span class="input-group-addon btn default btn-file">
													<span class="fileinput-new">
													Select file </span>
													<span class="fileinput-exists">
													Change </span>
													<input type="file" class="file-image" no="<?php echo $j?>" name="answer<?php echo $j?>_img">
													</span>
													<a href="#" class="input-group-addon btn red fileinput-exists" data-dismiss="fileinput">
													Remove </a>
												</div>
											</div>									
										</div>								
										<?php 
										echo('</div></div>');
									}
									?>
									<?php }?>
									</div>
									<div class="col-md-offset-4 col-md-8">
										<button type="button" id="previewButton" class="btn yellow mr10">プレビュー</button>										
										<button type="button" id="publicButton" class="btn blue mr10">保存する</button>
										<button type="button" id="saveButton" class="btn blue mr10">
										<i class="fa fa-save mr10"></i>一時保存</button>
										<?php if ($item_talk['level'] == 4 && $mode!= 'cate') {?><button type="button" id="deleteButton" class="btn red mr10">削除</button><?php }?>
										<button onclick="top.location='/admin/<?php echo $_action?>s'" type="button" class="btn default">戻る</button>
									</div>
							</div>
							<div id="faq-preview" class="col-md-4">
							<div class="talkappi-preview js-faq-preview" data-type="message" style="margin:0 0 0 20px; flex-basis: 30%; max-width:320px;"></div>
							</div>							
						</div>									
			        </div>
			    </div>		
			</div>
		</div>
		<!-- END PAGE CONTENT-->
		<?php echo $skillbox ?>