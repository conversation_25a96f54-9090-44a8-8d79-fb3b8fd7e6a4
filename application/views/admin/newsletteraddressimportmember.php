<link rel="stylesheet" type="text/css" href="/assets/admin/css/newsletter.css"/>
<input type="hidden" name="branches" id="branchDatas" value="[]" />
<style>
.content-container .content-wrapper {
  padding: 12px 6%;
  display:flex;
  flex-direction:column;
  gap:24px;
}
/* #filter-conditions span{
    text-decoration: underline;
    cursor: pointer;
}
#filter-conditions.disabled span {
    color: #A1A4AA;
    text-decoration: unset;
}
#filter-conditions .inquiry-branch {
    color: #245BD6;
} */
.icon-undo {
  height: 12px;
  width: 12px;
}

</style>

<div class="content-container white">
  <div class="content-wrapper">
    <!-- 案件所属 -->
    <?php if($is_newsletter_project_enabled && $project_id && is_numeric($project_id) ) : ?>
      <div style="display:flex;gap:6px;">
        <span><?php echo __('admin.newsletterimport.label.project') ?></span>
        <span><?php echo $project_name ?></span>
      </div>
    <?php endif; ?>


    <div style="display:flex;">
      <button class="btn-smaller page btn-blue js-import" style="margin-left:0"><?php echo __('admin.newsletterimport.label.import_member') ?></button>
    </div>

    <div style="display:flex;flex-direction:column;gap:6px;">
      <div class="result_data_count">
        <?php echo __('admin.common.label.total') ?><span></span>
        <?php echo __('admin.newsletterimport.label.pcount') ?></div>
      <div>
        <table class="table table-striped table-bordered table-hover js-data-table">
          <thead>
            <tr>
              <th><?php echo __('admin.common.label.mail.address') ?></th>
              <th><?php echo __('admin.common.label.name.human') ?></th>
              <!-- <th><?php echo __('admin.newsletterimport.label.tag') ?></th> -->
              <th style="width:40px!important"><?php echo __('admin.newsletterimport.button.exclude') ?></th>
            </tr>
          </thead>
          <tbody></tbody>
        </table>
      </div>
    </div>

    <div class="flex">
      <button class="action-button page btn-blue js-export-to" style="margin-left:0"><?php echo __('admin.newsletterimport.label.export') ?></button>
      <?php echo HTML::anchor($navi_parent_url, __('admin.common.button.return_to_list'), ['class' => 'action-button page btn-white js-action-back']) ?>
    </div>
  </div>
</div>

<script>
const _extends = '<?php echo $extends ?>';
const _project_id = <?php echo isset($project_id) && $project_id && is_numeric($project_id) ? $project_id : 'null'; ?>;
const _is_newsletter_project_enabled = <?php echo $is_newsletter_project_enabled ? 'true' : 'false'; ?>;
</script>