			<!-- B<PERSON>IN PAGE HEADER-->
			<!-- BEGIN PAGE HEAD -->
			<div class="page-head">
				<!-- BEGIN PAGE TITLE -->
				<div class="page-title">
					<h1>FAQページ利用状況レポート<small></small></h1>
				</div>
				<!-- END PAGE TITLE -->
			</div>
			<!-- END PAGE HEAD -->
			<!-- END PAGE HEADER-->
			<!-- BEGIN PAGE CONTENT-->
			<div class="row">
				<div class="col-md-12">
			        <!-- Page Content -->
			        <div id="page-wrapper">
			        <div class="portlet light">
					<?php echo $reportmenu ?> 
						<div class="portlet box">
							<div class="portlet-body">
								<div class="form-body">		
									<div class="form-group">
										<label class="control-label col-md-1" style="padding-left:15px; padding-right:15px;"><?php echo __('admin.common.label.period') ?></label>
										<div class="col-md-4">
											<input name="start_date" id="start_date" value="<?php echo($start_date)?>" style="float:left;" class="form-control form-control-inline input-small date-picker" size="16" data-date-format="yyyy-mm-dd"  type="text"/>
											<input name="end_date" id="end_date" value="<?php echo($end_date)?>" style="float:left; margin-left:10px;" class="form-control form-control-inline input-small date-picker" size="16" data-date-format="yyyy-mm-dd"  type="text"/>
										</div>
									</div>
									<div class="form-group">
									<label class="control-label col-md-1" style="padding-left:15px; padding-right:15px;"><?php echo __('admin.common.label.lang') ?></label>
										<div class="col-md-2">
											<?php echo Form::select('lang_cd', $lang, $lang_cd, array('id'=>'lang_cd','class'=>'form-control'))?>
										</div>
									</div>
									<div class="form-group">
										<label class="control-label col-md-1" style="padding-left:15px; padding-right:15px;"><?php echo __('admin.common.label.user_flow') ?></label>
										<?php echo $botcond ?>
										<div id="scene_use_flg" style="display:none;">use_faq_flg</div>																											
										<div class="col-md-1">
											<button type="button" id="searchButton" class="btn yellow" disabled="disabled">
											<i class="fa fa-search mr10"></i><?php echo __('admin.common.button.search') ?></button>
										</div>									
										<div class="col-md-1" style="margin-left: 25px;">
											<button type="button" data-tableId="faqtable" data-title="FAQアクセス履歴" class="btn green exportCsv">
												<?php echo __('admin.common.button.csv_export') ?>
											</button>
										</div>
									</div>
								</div>													
							<table class="table table-striped table-bordered table-hover js-data-table" id="faqtable">
							<thead>
							<tr>
								<th>
									<?php echo __('admin.common.label.date_of_use') ?>
								</th>
								<?php if (count($bot_grp_dict) > 0) {?><th><?php echo __('admin.common.label.facility') ?></th> <?php }?>
								<th>
									<?php echo __('admin.common.label.type') ?>
								</th>
								<th>
									<?php echo __('admin.common.label.usage_environment') ?>
								</th>								
								<th>
									<?php echo __('admin.common.label.lang') ?>
								</th>								
								<th>
									<?php echo __('admin.common.label.content') ?>
								</th>
								<th>
									<?php echo __('admin.common.label.number_of_results') ?>
								</th>																		
							</tr>
							</thead>

							<tbody>
							<?php
								foreach ($logs as $log) {
							?>	
							<tr class="gradeX odd" role="row">
								<td class="sorting_1">
									 <?php echo($log['search_date']); ?>
								</td>
								<?php if (count($bot_grp_dict) > 0) {?><td class="sorting_1"><?php echo($bot_grp_dict[$log['bot_id']])?></td> <?php }?>
								<td class="sorting_1">
									 <?php echo($_codes['32'][$log['search_div']]); ?>
								</td>
								<td class="sorting_1">
									<?php 
								 	if ($log['mobile'] == 0) {
								 		echo __('admin.common.label.pc');
								 	}
								 	else if ($log['mobile'] == 1){
								 		echo __('admin.common.label.mobile');
								 	}
									else {
										echo __('admin.common.label.unknown');
									}
									?>
								</td>
								<td class="sorting_1">
									 <?php echo($_codes['02'][$log['lang_cd']]); ?>
								</td>
								<td class="sorting_1">
									 <?php echo($log['content']);?>
								</td>
								<td class="sorting_1">
									 <?php echo($log['result']); ?>
								</td>																																														
							</tr>
							<?php } ?>
							</tbody>
							</table>
							</div>
						</div>
					</div>
			        </div>
			        <!-- /#page-wrapper -->					
				</div>
			</div>
			<!-- END PAGE CONTENT-->
