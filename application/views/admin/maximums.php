<input type="hidden" name="act" id="act" value="" />
<input type="hidden" name="maximum_id" id="maximum_id" value="" />

<div class="content-container" style="padding-left: 0;">
    <div class="flex-x-between">
        <div><?php echo __('admin.maximums.stock_list') ?></div>
        <span class="btn-smaller btn-blue js-new-maximum">
            <span class="icon-add-white"></span>
            <?php echo __('admin.common.button.create_new') ?>
        </button>
    </div>
</div>

<div class="content-container white">
  <table class="table table-striped table-bordered table-hover js-data-table">
    <thead>
      <tr>
        <th style="width:80px;"><?php echo __('admin.common.label.code') ?></th>
        <th><?php echo __('admin.maximums.stock_title') ?></th>
        <th style="width:80px;"><?php echo __('admin.common.label.type') ?></th>
        <th style="display:none;">seq</th>
        <th style="width:80px;"><?php echo __('admin.maximums.remaining_stock') ?></th>
        <th style="width:140px;"><?php echo __('admin.common.label.last_update') ?></th>
        <th style="width:120px;"><?php echo __('admin.common.label.operation') ?></th>
      </tr>
    </thead>
    <tbody>
      <?php
      $model = new Model_Adminmodel();
      $authority = $model->check_authority('admin/maximum');
        foreach ($items as $item) {
      ?>
        <tr class="gradeX odd" role="row">
          <!-- コード -->
          <td class="js-maximum_id"><?php echo($item['id']) ?></td>
          <!-- 枠名称 -->
          <td class="js-maximum_name">
            <?php if ($authority) { ?>
              <a class="link-animate" href="/<?php echo $_path?>/maximum?id=<?php echo ($item['id']) ?>" class="js-name"><?php echo($item['name']) ?></a>
            <?php } else {
              echo($item['name']);
            } ?>
          </td>
          <!-- 枠タイプ -->
          <td class="span <?php echo($item['span']) ?>">
            <?php echo($span_codes[$item['span']]) ?>
          </td>
          <!-- seq -->
          <td style="display:none;" class="js-seq"><?php echo($item['seq']) ?></td>
          <!-- 在庫数 -->
          <td class="" style="text-align:center;">
              <?php
                if($item['span'] == "all") {
                  $maximum_data = json_decode($item['maximum_data'], true);
                  echo('<span class="js-remains-count">' . $item['maximum_remains']);
                  if ($maximum_data != null && array_key_exists('maximum', $maximum_data)) echo(' / '. $maximum_data['maximum']);
                  echo('</span><br/>');
                  echo '<p style="margin:0 0 0 10px;" class="font-standard font-color-v1 pointer js-change-remains">' .  __('admin.common.label.change') . '</p>';
                }
                else {
                  echo '<p style="margin:0 0 0 10px;" class="font-standard font-color-v1 pointer"><a href="/' . $_path . '/maximum?id=' . $item["id"]. '">'.  __('admin.common.label.change') . '</a></p>';
                }
              ?>
          </td>
          <!-- 最終更新 -->
          <td class="">
            <span class="">
              <?php echo substr($item['upd_time'], 0, 16); ?><br>
              <?php echo($item['upd_user_name']) ?>
            </span>
          </td>
          <!-- 詳細 -->
          <td class="flex">
          <?php if ($authority) { ?>
            <a class="link-animate" href="/<?php echo $_path?>/maximum?id=<?php echo ($item['id']) ?>"><div class="btn round image edit js-memo"><?php echo __('admin.common.label.edit') ?></div></a>
            <?php if($_user->role_cd != '63'){?>
              <div class="btn round image copy js-copy"><?php echo __('admin.common.label.copy') ?></div>
            <?php } ?>
          <?php }?>
            <a class="link-animate" href="/<?php echo $_path?>/maximumorders?id=<?php echo ($item['id']) ?>"><div class="btn round image result js-result"><?php echo __('admin.common.label.reservations') ?></div></a>
          </td>
        </tr>
      <?php } ?>
    </tbody>
  </table>
</div>

<!-- template -->
<!-- モーダル背景 -->
<div class="modal-background js-modal-background" style="display: none;"></div>
<!-- template -->
<!-- 在庫数変更モーダル -->
<div class="modal-image-container js-modal-remains-container js-modal" style="display: none;height: 176px; min-height: 176px;">
	<div class="relative" style="min-height: 100%;">
		<!-- タイトル -->
		<div class="flexbox relative">
			<h4 class="font-standard font-size-v5 font-family-v2" style="margin: 0;">
    <span class="js-max-modal-name"><?php echo __('admin.maximums.christmas_cake') ?></span>  <?php echo __('admin.maximums.stock_change') ?></h4>
			<span class="js-close-modal icon-cancel-large survey-position-absolute-v2 pointer"></span>
		</div>
    <!-- 期限 -->
    <div class="survey-space-top-2 flexbox-x-axis">
      <div class="width-96"><?php echo __('admin.maximums.number_of_stock') ?></div>
      <input type="text" class="survey-period-date-container js-remains-input" style="width:100px;padding:0 12px;" placeholder="">
    </div>
		<!-- SUBMIT -->
		<div class="submit-btn-container modal-image-button">
			<div class="flexbox-center btn-smaller btn-blue js-save-remains" style="margin:0 12px 0 0;" type="button">OK</div>
			<div class="flexbox-center btn-smaller btn-white js-close-modal" type="button"><?php echo __('admin.common.button.cancel') ?></div>
		</div>
	</div>
</div>
