<div id="page-wrapper">
	<div class="top-nav font-standard">
		<ul>
			<?php if ($_bot_setting['json_reserve_settings'] != '' && $item_div == 5) { ?>
				<li <?php if (strpos($post['class_cd_cond'], '02') === 0) echo ('class="active"') ?>>
					<a class="link-animate" href="/admin/<?php echo $func ?>s?class=02">
						宿泊プラン一覧</a>
				</li>
				<li <?php if (strpos($post['class_cd_cond'], '01') === 0) echo ('class="active"') ?>>
					<a class="link-animate" href="/admin/<?php echo $func ?>s?class=01">
						部屋タイプ一覧</a>
				</li>
			<?php } else { ?>
				<li class="active">
					<a class="link-animate" href="/admin/<?php echo $func ?>s?div=<?php echo ($item_div) ?>">
						<?php echo ($item_div_text[$item_div]) ?>一覧</a>
				</li>
			<?php } ?>
			<!-- <li class="">
					<a href="/admin/<?php echo $func ?>">
						新規登録</a>
				</li> -->
		</ul>

		<div class="edit-container">
			<div class="settings-container">
				<input type="hidden" id="act" name="act" value="" />
				<input type="hidden" id="item_div" name="item_div" value="<?php echo $item_div ?>" />
				<input type="hidden" id="class_cd_cond" name="class_cd_cond" value="<?php echo $post['class_cd_cond'] ?>" />
				<input type="hidden" id="product_cd" name="product_cd" value="" />
				<input type="hidden" id="func" value="<?php echo $func ?>" />
				<!-- not hotel -->
				<?php echo $classbox ?>
				<div class="form-group">
					<label class="control-label col-md-1" <?php if ($_bot_setting['json_reserve_settings'] != '' && $item_div == 5) echo ('style="display:none;"') ?>>販売期間</label>
					<div class="col-md-4" <?php if ($_bot_setting['json_reserve_settings'] != '' && $item_div == 5) echo ('style="display:none;"') ?>>
						<input name="start_date" id="start_date" value="<?php echo ($post['start_date']) ?>" style="float:left;" class="form-control form-control-inline input-small date-picker" size="16" data-date-format="yyyy-mm-dd" type="text" />
						<input name="end_date" id="end_date" value="<?php echo ($post['end_date']) ?>" style="float:left; margin-left:10px;" class="form-control form-control-inline input-small date-picker" size="16" data-date-format="yyyy-mm-dd" type="text" />
					</div>
					<!-- all -->
					<div class="col-md-3">
						<label class="control-label ml20">販売中</label>
						<input type="checkbox" id="show_flg" name="show_flg" value="1" class="make-switch" <?php if ($post['show_flg'] == 1) echo ("checked") ?> data-on-color="success" data-off-color="warning">
					</div>
					<!-- hotel -->
					<?php if ($_bot_setting['json_reserve_settings'] != '' && $item_div == 5) { ?>
						<div class="col-md-3">
							<label class="control-label ml20" id="stock_label">在庫あり</label>
							<input type="checkbox" id="stock_checkbox" name="stock_flg" value="1" class="make-switch" <?php if ($post['stock_flg'] == 1) echo ("checked") ?> data-on-color="success" data-off-color="warning">
							<!-- 
					<label class="control-label">表示順設定</label>
					<input type="checkbox" name="sort_flg" id="sort_flg" value="1" class="make-switch" <?php if ($post['sort_flg'] == 1) echo ("checked") ?> data-on-color="success" data-off-color="warning">
						-->
						</div>
					<?php } ?>
					<?php if ($_bot_setting['json_reserve_settings'] != '' && $item_div == 5) { ?>
						<div class="col-md-1">
							<button type="button" class="btn yellow searchButton">
								<i class="fa fa-search mr10"></i>検索</button>
						</div>
						<div class="col-md-1">
							<button type="button" id="importButton" class="btn green">プラン・部屋の取込</button>
						</div>
					<?php } ?>
				</div>
				<table class="table table-striped table-bordered table-hover js-data-table" id="dtProducts">
					<thead>
						<tr>
							<?php if ($_bot_setting['json_reserve_settings'] != '' && $item_div == 5 && strpos($post['class_cd_cond'], '02') === 0) { ?>
								<th style="width:120px;">
									宿泊プランコード
								</th>
								<th style="width:180px;">
									宿泊プラン名
								</th>
							<?php } else if ($_bot_setting['json_reserve_settings'] != '' && $item_div == 5 && strpos($post['class_cd_cond'], '01') === 0) { ?>
								<th style="width:120px;">
									部屋タイプコード
								</th>
								<th style="width:180px;">
									部屋タイプ名
								</th>
							<?php } else { ?>
								<th>
									<?php echo ($item_div_text[$item_div]) ?>コード
								</th>
								<th>
									<?php echo ($item_div_text[$item_div]) ?>名
								</th>
							<?php } ?>

							<th style="width:80px;<?php if ($post['show_flg'] == 0) echo ("display:none;") ?>">
								表示順
							</th>
							<th>
								説明
							</th>
							<?php if ($_bot_setting['json_reserve_settings'] != '' && $item_div == 5) { ?>
								<th style="width:80px;">
									連携状態
								</th>
							<?php } else { ?>
								<th>
									状態
								</th>
							<?php } ?>
						</tr>
					</thead>
					<tbody>
						<?php
						if ($post['sort_flg'] == 1) {
							$sort_style = 'style="margin-right:2px;"';
						} else {
							$sort_style = 'style="display:none;"';
						}
						$link_key_arr = [];
						$link_key_color = ['success', 'warning', 'danger'];
						foreach ($items as $item) {
						?>
							<tr class="gradeX odd" role="row">
								<td style="vertical-align: middle;">
									<?php
									if ($_bot_setting['json_reserve_settings'] != '' && $item_div == 5) {
										echo ($item['product_cd']);
									} else {
										echo ($item['product_cd']);
										echo ('<br />');
										$class_cd = explode(' ', $item['class_cd']);
										foreach ($class_cd as $cd) {
											echo ($code_div_dict[$cd] . '<br>');
										}
									}
									?>
								</td>
								<td style="vertical-align: middle;">
									<?php if ($item['link_key'] != '') {
										if (array_key_exists($item['link_key'], $link_key_arr)) {
											$color = $link_key_arr[$item['link_key']];
										} else {
											$link_key_arr[$item['link_key']] = $link_key_color[count($link_key_arr) % 3];
											$color = $link_key_arr[$item['link_key']];
										}
										echo ('<span class="badge badge-' . $color . '">' . $item['link_key'] . '</span>');
									} ?>
									<a class="link-animate" href="/admin/<?php echo $func ?>?id=<?php echo ($item['product_id']) ?>">
										<?php
										if ($item['product_name'] == NULL) {
											echo ("未命名");
										} else {
											echo ($item['product_name']);
										} ?>
									</a>
								</td>
								<td style="text-align:center;vertical-align: middle;<?php if ($post['show_flg'] == 0) echo ("display:none;") ?>">
									<?php
									echo ('<span style="display:none;">' . $item['sort_no1'] . '</span>');
									echo ('<a href="javascript:void(0);" style="margin-right:10px;" product_id="' . $item['product_id'] . '" class="sort-up"><span><i class="fas fa-arrow-alt-circle-up fa-2x"></i></span></a>');
									echo ('<a href="javascript:void(0);" product_id="' . $item['product_id'] . '" class="sort-down"><span><i class="fas fa-arrow-alt-circle-down fa-2x"></i></span></a><br/>');
									// if ($item['recommend'] == 1) echo ('<i class="fas fa-thumbs-up" style="color: #fecb81"></i>');
									// if ($item['lang_display'] === '') echo ('<i class="fas fa-eye-slash" style="color: #aaa"></i>');
									?>
								</td>
								<td style="vertical-align: middle;">
									<?php if ($cancel_policy == '' && $item['notes'] == '' && strpos($item['class_cd'], '02') === 0) echo ('<span class="badge badge-warning">キポ（未）</span>'); ?>
									<?php echo (htmlspecialchars($item['sell_point'])) ?>
								</td>
								<?php
								if ($_bot_setting['json_reserve_settings'] != '' && $item_div == 5) {
									echo ('<td style="vertical-align: middle;">');
									if ($item['link_type_cd'] != '' && $_bot_setting['json_reserve_settings'] != '') {
										if ($item['public_flg'] == 0) {
											$style = 'danger';
											$link_txt = '追加(未公開)';
										} else if ($item['end_date'] != '') {
											$style = 'danger';
											$link_txt = '削除済み';
										} else {
											if ($item['upd_user'] == 0 && $_bot_setting['flg_product_auto_update'] == 0) {
												$style = 'warning';
												$link_txt = '更新あり';
											} else {
												$style = 'success';
												$link_txt = '最新状態';
											}
										}
										echo ('<a href="javascript:void(0);" class="import-one" product_cd="' . $item['product_cd'] . '"><span class="badge badge-' .  $style . '" style="margin-left:5px;">' . $link_txt . '</span></a>');
										$link_data = json_decode($item['link_data'], true);
										if (is_array($link_data)) {
											if (array_key_exists('RoomDescription', $link_data)) {
												$upd_time = $link_data['RoomDescription']['$']['LastModifyDateTime'];
											} else if (array_key_exists('RatePlanDescription', $link_data)) {
												$upd_time = $link_data['RatePlanDescription']['$']['LastModifyDateTime'];
											}
											$upd_time = str_replace('+09:00', '', str_replace('T', ' ', $upd_time));
											$pos = strpos($upd_time, '.');
											if ($pos > 0) $upd_time = substr($upd_time, 0, 16);
											$upd_time = str_replace('-', '/', $upd_time);
											echo ('<br/>' . $upd_time);
										}
									}
									echo ('</td>');
								} else {
									echo ('<td></td>');
								}
								?>
							</tr>
						<?php } ?>
					</tbody>
				</table>
			</div>
		</div>
	</div>
</div>