<style>
  .report-menu {
    height: 140px;
  }

  .form-group {
    margin-left: 10px !important;
  }

  .dropdown-menu.open {
    width: fit-content;
  }
</style>

<script>
	var _grp_bot_id = <?php echo $grp_bot_id?>;
</script>

<input type="hidden" id="class_cd_cond" name="class_cd_cond" value="<?php echo $scene_cd ?>" />
<input type="hidden" id="multiple" name="multiple" value="true" />

<!-- BEGIN PAGE HEADER-->
<!-- BEGIN PAGE HEAD -->
<div class="page-head">
  <!-- BEGIN PAGE TITLE -->
  <div class="page-title">
    <h1>FAQ利用状況統計<small></small></h1>
  </div>
  <!-- END PAGE TITLE -->
</div>
<!-- END PAGE HEAD -->
<!-- END PAGE HEADER-->
<!-- BEGIN PAGE CONTENT-->
<input type="hidden" name="lang_cd" id="lang_cd" value="" />
<input type="hidden" name="answer" id="answer" value="" />
<div class="report-menu" style="background-color: #f6f7f9;">
	<div class="report-menu-tub row menu-row">
	    <div class="form-body" style="margin-top:10px;">		
			<div class="form-group" style="display: flex;">
				<label class="control-label col-md-1" style="min-width:100px;"><?php echo __('admin.common.label.status_kana') ?></label>
				<?php echo $reportmenu ?>
        <div style="display: flex; margin: 6px 0px 0px 30px; ">
          <label style="margin-right: 6px;"><?php echo __('admin.talkreport8.label.answer_question_display') ?></label>
          <div class="talkappi-switch" data-value="<?php echo $answer ?>"></div>
        </div>
			</div>
		</div>			
	</div>
	<div class="report-menu-edit row menu-row">
        <div class="form-body">		
          <div class="form-group">
          <label class="control-label col-md-1" style="min-width:100px;"><?php echo __('admin.common.label.user_flow') ?></label>
          <?php echo $botcond ?>
        </div>
        <div class="form-group">
          <label class="control-label col-md-1" style="min-width:100px;"><?php echo __('admin.common.label.period') ?></label>
          <div class="col-md-6" style="background-color: #f6f7f9;display:flex;">
            <div class="survey-period-date-container" style="width: 150px; background-color:white;">
              <span class="icon-calender" style="margin:8px 8px 8px 3px;"></span>
              <input name="start_date" id="start_date" value="<?php echo($start_date)?>" class="date-picker line-none" style="width: 100%;" size="12" data-date-format="yyyy-mm-dd" type="text" autocomplete="off" />
            </div>
            <p>〜</p>
            <div class="survey-period-date-container" style="width: 150px; background-color:white; margin-left: 5px; margin-right:12px; "> 
              <span class="icon-calender" style="margin:8px 10px 8px 5px;"></span>
              <input name="end_date" id="end_date" value="<?php echo($end_date)?>" class="date-picker line-none" style="width: 100%;" size="12" data-date-format="yyyy-mm-dd" type="text" autocomplete="off" />
            </div>
            <button type="button" id="searchButton" class="btn-smaller btn-blue">
                <i class="fa fa-search mr10"></i><?php echo __('admin.common.button.search') ?>
            </button>
            <button type="button" id="clearButton" class="btn-smaller btn-gray-black"><?php echo __('admin.common.button.reset') ?></button>
          </div>
        </div>
		</div>			
	</div>
</div>

<div class="row">
  <div class="col-md-12 col-sm-12">
    <div class="form-body" style="margin-top: 0px;margin-bottom: 0;">
      <div class="form-group">
        <div style="float:right; margin-right: 20px">
          <button type="button" data-tableId="talkreport8table" data-title="FAQ利用状況統計" class="btn-smaller btn-white exportCsv">
            <span class="icon-export"></span><?php echo __('admin.common.button.csv_export') ?>
          </button>
        </div>
      </div>
    </div>
    <!-- BEGIN PORTLET-->
    <!-- BEGIN PORTLET-->
    <div class="portlet light ">
      <div class="col-md-2" style="margin-top: 12px;">
        <?php echo Form::select('lang_cd', $lang, $lang_cd, array('id'=>'lang_cd','class'=>'form-control', 'style'=>'height:28px;'))?>
      </div>
      <div class="portlet box">  
        <div class="portlet-body">				
          <table class="table table-bordered table-hover js-data-table" id="talkreport8table">
          <thead style="background-color: #f6f7f9;">
          <tr>
            <th><?php echo __('admin.common.label.question') ?></th>
            <th><?php echo __('admin.talkreport8.label.count') ?></th>
            <th><?php echo __('admin.talkreport8.label.latest_response_status') ?></th>
          </tr>
          </thead>
          <tbody>
          <?php
          $count = 0;
          foreach ($report_data as $rank) {
          	if (in_array($rank['intent_cd'] . '#' . $rank['sub_intent_cd'], $skill_dict)) continue;
          	
            echo('<tr>');
            echo('<td>');
            echo('<a class="link-animate" href="/admin/talknew?cd=' . $rank['intent_cd'] . '&lang=ja&sub_cd=' . $rank['sub_intent_cd'] . '">');
            echo($rank['question']);
            echo('</a>');
            echo('</td>');
            echo('<td style="text-align:center;">');
            echo('<a class="link-animate" href="/admin/talkreport8_1?intent_cd=' . $rank['intent_cd'] . '&sub_intent_cd=' . $rank['sub_intent_cd'] .'"><span class="badge-talk" style="background-color: #d3eeff"">');
            echo($rank['count']);
            //if ($rank['no_c'] != '') echo('(' . $rank['no_c'] . ')');
            echo('</span></a>');
            echo('</td>');
            echo('<td style="text-align:center;">');
            if ($rank['answer1'] == '') {
            	echo(__('admin.talkreport8.label.unregistered'));
            }
            else {
            	echo(__('admin.talkreport8.label.answered'));
            }
            echo('</td>');
            echo('</tr>');
            $count++;
            //if ($count>=8) break;
          }
          ?>
          </tbody></table>
        </div>
      </div>
      </div>
    </div>
    <!-- END PORTLET-->
    <!-- END PORTLET-->
  </div>
</div>
<!-- END PAGE CONTENT-->
