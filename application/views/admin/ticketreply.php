<link href="/assets/admin/pages/css/blog.css" rel="stylesheet"/>
<link rel="stylesheet" type="text/css" href="/assets/global/plugins/bootstrap-wysihtml5/bootstrap-wysihtml5.css"/>
<link rel="stylesheet" type="text/css" href="/assets/global/plugins/bootstrap-markdown/css/bootstrap-markdown.min.css">
<link rel="stylesheet" type="text/css" href="/assets/global/plugins/bootstrap-summernote/summernote.css">
<style>
.media-body {
	display:block;
}

</style>
<!-- BEGIN PAGE HEADER-->
<!-- B<PERSON>IN PAGE HEAD -->
<div class="page-head">
	<!-- BEGIN PAGE TITLE -->
	<div class="page-title">
		<h1><?php echo($_active_menu_name)?><small></small></h1>
	</div>
	<!-- <PERSON><PERSON> PAGE TITLE -->
</div>
<!-- <PERSON>ND PAGE HEAD -->
<!-- <PERSON><PERSON> PAGE HEADER-->
<!-- BEGIN PAGE CONTENT-->
<div class="row">
	<div class="col-md-12">
        <!-- Page Content -->
        <div id="page-wrapper">
			<!-- <div class="portlet light"> -->
			<?php 
				if (strlen($_active_menu) > 4) {
					$tab_menu = View::factory ('admin/menutab');
					echo($tab_menu);
				}
			?>
			<div class="edit-container">
        		<div class="settings-container">
					<div class="portlet-body">
						<div class="row">
							<div class="col-md-12 blog-page">
								<div class="row">
									<div class="col-md-12 article-block" style="padding-left: 30px;padding-right: 30px;">
										<h4 style="margin-top:0;"><?php echo $ticket['title']?></h4>
										<div class="blog-tag-data">
											<div class="row">
												<div class="col-md-6">
													<ul class="list-inline blog-tags">
														<li>
															<i class="fa fa-tags"></i>
															<a href="#">
															<?php if (isset($_codes['40'][$ticket['ticket_tag_cd']])) echo $_codes['40'][$ticket['ticket_tag_cd']] ?> </a>
														</li>
													</ul>
												</div>
												<div class="col-md-6 blog-tag-data-inner">
													<ul class="list-inline">
														<li>
															<i class="fa fa-calendar"></i>
															<a href="#">
															<?php echo substr($ticket['create_time'],0,10) ?> </a>
														</li>
														<li>
															<i class="fa fa-user"></i>
															<a href="#">
															<?php echo $ticket['name'] ?> </a>
															<?php 
															if ($_user->role_cd == "99") echo('<span>  <a href="/admin/ticket?id=' . $ticket['ticket_id'] . '" style="color: #78cff8;">編集 </a></span>');
															?>								
														</li>
													</ul>
												</div>
											</div>
										</div>
										<hr>
										<!--end news-tag-data-->
										<div style="overflow-x:scroll;">
											<p>
											<?php echo $ticket['content']?>
											</p>
											<?php if ($ticket['update_time']!=NULL) {?>
											<blockquote class="hero">								
												<small>最近編集 <cite title="<?php echo $ticket['update_time']?>"><?php echo $ticket['update_time']?></cite></small>
												<?php if ($_user->role_cd == "99") {?>
												<small>既読ユーザー  ※<?php echo $read_user_list?></small>
												<?php }?>
											</blockquote>
											<?php }?>						
										</div>
										<hr>
									
										<?php if ($ticket['ticket_type_cd'] != '01') { ?>
										<div class="post-comment">
												<button type="button" onclick="top.location='/admin/tickets'" class="btn grey-steel">戻る</button>
										</div>
										<?php }?>
									</div>
									<!--end col-md-12 article-block-->
								</div>
							</div>
						</div>
					</div>
				</div>
	</div>
	<!-- END PAGE CONTENT-->
</div>
</div>
</div>
