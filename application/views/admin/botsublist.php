			<!-- BEGIN PAGE HEADER-->
			<!-- BEGIN PAGE HEAD -->
			<div class="page-head">
				<!-- BEGIN PAGE TITLE -->
				<div class="page-title">
					<h1><?php echo($_active_menu_name)?><small></small></h1>
				</div>
				<!-- <PERSON>ND PAGE TITLE -->
			</div>
			<!-- END PAGE HEAD -->
			<!-- END PAGE HEADER-->
			<!-- BEGIN PAGE CONTENT-->
			        <!-- Page Content -->
			        <div id="page-wrapper">
				        <div class="portlet light">
						<?php 
							if (strlen($_active_menu) > 4) {
								$tab_menu = View::factory ('admin/menutab');
								echo($tab_menu);
							}
						?>
						<div class="portlet box">
						<div class="portlet-body">
							<input type="hidden" name="message" id="message" value="<?php echo($message)?>" />
							<input type="hidden" name="bot_id" id="bot_id" value="" />
							<table class="table table-striped table-bordered table-hover js-data-table">
							<thead>
							<tr>
								<th>
									ID
								</th>
								<th>
									ボット名
								</th>
								<th>
									デフォルト導線
								</th>
								<th>
									 編集操作
								</th>
							</tr>
							</thead>

							<tbody>
							<?php
								foreach ($botlist as $orm) {
							?>
							<tr class="gradeX odd" role="row">
								<td>
									 <?php echo($orm->bot_id)?>
								</td>							
								<td>
									 <?php echo($orm->bot_name)?>
								</td>
								<td>
									 <?php echo($orm->facility_cd)?>
								</td>
								<td>
									<div class="btn round image edit js-action" act="base" bot_id="<?php echo($orm->bot_id)?>">基本設定</div>
									<div class="btn round image copy js-action" act="message" bot_id="<?php echo($orm->bot_id)?>">多言語設定</div>
									<?php if ($_user->role_cd == "99") {?>
										<div class="btn round image public js-action" act="setting" bot_id="<?php echo($orm->bot_id)?>">高度な設定</div>
										<div class="btn round image add js-action" act="scene" bot_id="<?php echo($orm->bot_id)?>">ユーザー導線</div>
										<div class="btn round image delete js-action" act="close" bot_id="<?php echo($orm->bot_id)?>">利用終了※</div>
										<div class="btn round image delete js-action" act="delete" bot_id="<?php echo($orm->bot_id)?>">削除※</div>
									<?php }?>
								</td>
							</tr>
							<?php } ?>
							</tbody>
							</table>
							<br/>
							<?php if ($_user->role_cd == "99") {?>
							<div class="form-group">
								<label class="control-label col-md-2">親基本設定※</label>
								<div class="col-md-4">
									<?php echo Form::select('bot_setting', $bot_settings, $post['bot_setting'], array('id'=>'bot_setting','class'=>'form-control'))?>
								</div>
								<div class="col-md-2">
								<button type="button" id="updateSetting" class="btn green action">一括更新</button>
								</div>
							</div>							
							<div class="form-group">
								<label class="control-label col-md-2">LINEメニュー※</label>
								<div class="col-md-4">
									<?php echo Form::select('line_menu', $line_menus, $post['line_menu'], array('id'=>'line_menu','class'=>'form-control'))?>
								</div>
								<label class="control-label col-md-1">←参照先</label>								
								<label class="control-label col-md-1">上書き</label>
								<div class="col-md-2">
									<input type="checkbox" name="line_menu_overwrite_flg" value="1" class="make-switch" data-on-color="success" data-off-color="warning">
								</div>											
								<div class="col-md-1">
									<button type="button" id="createLineMenu" class="btn blue action">一括作成</button>
								</div>
							</div>
							<div class="form-group">
								<label class="control-label col-md-2">グループ施設情報※</label>
								<div class="col-md-4">
									<?php echo Form::select('group_item', $bot_dict, $post['group_item'], array('id'=>'group_item','class'=>'form-control'))?>
								</div>								
								<div class="col-md-2">
								<button type="button" id="updateItemAll" class="btn yellow action">一括更新</button>
								</div>
							</div>							
							<div class="form-group">
								<label class="control-label col-md-2">子BOT一括作成※</label>
								<div class="col-md-2">
									<input name="count" id="count" type="text" class="form-control" value="1">
								</div>
								<label class="control-label col-md-1">←新規数</label>						
								<div class="col-md-2">			
								<button type="button" id="createBot" class="btn green-meadow action">一括作成</button>
								</div>
							</div>
							<div class="form-group">
								<label class="control-label col-md-2">子BOT新規作成※</label>
								<div class="col-md-2">
									<input name="sub_bot_id" id="sub_bot_id" type="text" class="form-control" value="">
								</div>
								<label class="control-label col-md-1">←子BOTID</label>						
								<div class="col-md-2">			
								<button type="button" id="createBotOne" class="btn green-meadow action">作成</button>
								</div>
							</div>							
							<?php }?>
							</div>
							</div>
						</div>
			        </div>
			        <!-- /#page-wrapper -->
			<!-- END PAGE CONTENT-->



