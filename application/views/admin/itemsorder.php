<style>
  .mobile-preview .main-container {
    display:flex;
    gap: 10px;
    border: 1px solid #D8D8D8;
    height: 400px;
  }
  .js-category-pulldown {
    margin-top: 0 !important;
    width: 192px;
  }
  .js-category-select {
    display: flex;
    gap: 20px;
    align-items: flex-start;
  }
  .js-selected-table tbody {
    position: relative;
  }
  .js-selected-cd {
    cursor: move;
    position: relative;
  }
  .js-selected-cd.ui-sortable-helper {
    background-color: rgba(255, 255, 255, 0.8);
    height: 40px;
    width: 100%;
    display: flex;
  }
  .js-selected-cd.ui-sortable-placeholder {
    visibility: visible !important;
  }
  .js-selected-cd.ui-sortable-placeholder::after {
    content: '';
    display: block;
    width: 100%;
    height: 2px;
    background-color: #245BD6;
    position: absolute;
    left: 0;
    bottom: 0;
    right: 0;
    z-index: 100;
  }
  .js-selected-cd td:first-child {
    width: 107px;
  }
  .js-selected-cd td:last-child {
    width: calc(100% - 107px);
  }
  .cursor-not-allow {
    cursor:not-allowed !important;
  }
</style>


<ol class="survey-details-breadcrumbs-list" style="display:flex;align-items:center;gap:12px;padding-left:0px">
  <li class="active">
    <a href="/<?php echo $_path?>/items"><?php echo $back_title ?></a>
  </li>
	<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16">
		<g fill="none" fill-rule="evenodd" stroke-linecap="round" stroke-linejoin="round">
			<g stroke="#3D3F45">
				<g>
					<g>
						<path d="M6 4L10 8.105 6 12" transform="translate(-572 -829) translate(419 506) translate(153 323)"/>
					</g>
				</g>
			</g>
		</g>
	</svg>
  <li>
    <span itemprop="name"><?php echo __('admin.items.item_order.label') ?></span>
  </li>
</ol>
<input type="hidden" id="class_cd_cond" name="class_cd_cond" value="<?php echo $class_cd_cond ?>">
<input type="hidden" id="search_item_div" name="search_item_div" value="<?php echo $search_item_div ?>">
<input type="hidden" name="save_data" id="save_data" value>

<div class="content-container white border">
  <div class="section-container">
    <div>
      <p style="margin-bottom:10px;"><?php echo __('admin.items.item_order.tips_label') ?></p>
      <div class="row">
        <div class="col-md-2">
          <div class="talkappi-pulldown js-item-div" data-name="item_div" data-value="<?php echo $item_div ?>" data-source="<?php echo htmlspecialchars(json_encode($item_divs, JSON_UNESCAPED_UNICODE)) ?>"></div>
        </div>
        <div class="col-md-10" style="display:flex;gap:20px;">
          <div class="action-button section btn-blue js-button-search" style="width:43px;margin-left:0;">OK</div>
        </div>
      </div>
    </div>
    <div class="row" style="margin-top:26px">
      <div class="col-md-8">
        <div style="margin-bottom:14px">「<?php echo $class_cd_cond_name ?>」<?php echo __('admin.items.item_order.order_title') ?></div>
        <div style="margin-bottom:110px;">
          <table class="table table-bordered js-selected-table">
            <thead style="background-color:#F6F7F9;">
              <tr>
                <th style="text-align:end;">#</th>
                <th style="text-align:start;"><?php echo __('admin.common.label.name') ?></th>
              </tr>
            </thead>
            <?php if ($item_count == 0) { ?>
              <tbody>
                <tr>
                  <td colspan="2" style="color:#D8D8D8;">
                    <?php echo __('admin.items.item_order.tips_label_2') ?>
                  </td>
                </tr>
              </tbody>
            <?php } else {
              foreach ($items_by_type as $type => $items) {
            ?>
              <tbody class="js-sortable-tbody" id="sortable-items-<?php echo $type ?>" data-type="<?php echo $type ?>">
              <?php foreach($items as $key => $item) { ?>
                <tr class="js-selected-cd" data-id="<?php echo $item['item_id'] ?>" data-order1="<?php echo ($type == '1') ? ($key + 1) : $type ?>" data-order2="<?php echo ($type != "1") ? ($key + 1) : '' ?>">
                  <td style="vertical-align:middle;">
                    <div class="wrapper" style="display:flex;justify-content:space-between;align-items:center">
                      <img src="/assets/admin/css/img/icon-drag.svg">
                      <span><?php echo ($type == '1') ? ($key + 1) : '-' ?></span>
                    </div>
                  </td>
                  <td>
                    <div class="wrapper" style="display:flex;justify-content:space-between;align-items:center;">
                      <span class="name-part"><?php echo $item['item_id'] . ' ' . $item['item_name'] ?></span>
                      <div class="random-setting-part">
                        <?php
                          $random_data_source = [
                            "-1" => __('admin.items.item_order.random_type_-1'),
                            "1" => __('admin.items.item_order.random_type_1'), 
                            "0" => __('admin.items.item_order.random_type_0')
                          ]
                        ?>
                        <div class="talkappi-pulldown js-random-type" data-value="<?php echo $type ?>" data-source='<?php echo json_encode($random_data_source, JSON_UNESCAPED_UNICODE) ?>'></div>
                      </div>
                    </div>
                  </td>
                </tr>
              <?php } ?>
              </tbody>
            <?php }} ?>
          </table>
        </div>
        <div class="buttons-wrapper" style="display:flex;justify-content:center;">
          <button type="button" class="action-button page btn-blue js-action-save"><?php echo __('admin.items.item_order.apply_title') ?></button>
          <a href="/admin/items" class="action-button page btn-white js-action-back"><?php echo __('admin.common.button.return_to_list') ?></a>
        </div>
      </div>
      <div class="col-md-4">
        <div class="talkappi-preview js-msg-preview preview-sticky" data-type="message" style="margin:30px 0 0 20px; flex-basis: 30%; max-width:320px;"></div>
      </div>
    </div>
  </div>
</div>

<script>
  const _class_cd_cond = '<?php echo $class_cd_cond ?>';
  const _preview_item_datas = <?php echo json_encode($preview_item_datas, JSON_UNESCAPED_UNICODE) ?>;
  const _lang_edit = '<?php echo $lang_edit ?>';
</script>