<style>
    .txt_head_title {
        font-weight: 500;
        font-size: 12px;
        line-height: 18px;
    }
</style>
<div class="content-container white border">
    <div class="section-container">
        <div class="form_head">
            <div class="txt_head_title"><?php echo __('admin.newsletterproject.label.project_setting') ?></div>
        </div>
        <div class="form-body">
            <div class="form-group">
                <label class="control-label col-md-1"><?php echo __('admin.newsletterproject.label.name') ?></label>
                <div class="col-md-9">
                    <input name="project_name" id="project_name" type="text" max-length="40" class="form-control" style="width:100%;" placeholder="" value="<?php echo $project ? $project['project_name'] : ''; ?>">
                </div>
            </div>
            <!-- 担当者 -->
			<div class="form-group" <?php if ($json_newsletter_setting['user_in_charge_required'] != 1) echo('style="display:none;"')?>>
				<label class="control-label col-md-1"><?php echo __('admin.survey.label.user_in_charge'); ?></label>
				<div class="col-md-9">
					<div 
						class="react-multi-select"
						data-items='<?php echo $user_list_for_react_select; ?>'
						data-initial-selected-items='<?php echo $user_in_charge_list_for_react_select; ?>'
					></div>
					<input type="hidden" name="user_in_charge">
				</div>
			</div>
            <div class="form-group">
                <label class="control-label col-md-1"><?php echo __('admin.newsletterproject.label.description') ?></label>
                <div class="col-md-9">
                    <textarea id="description" name="description" class="form-control" rows="20"><?php echo $project ? $project['description'] : ''; ?></textarea>
                </div>
            </div>
        </div>
    </div>
    <div class="form-actions">
        <div class="row">
            <div class="col-md-offset-2 col-md-9">
                <div class="actions-container">
                    <button type="button" class="action-button page btn-blue js-action-save"><?php echo __('admin.common.button.save'); ?></button>
                    <a class="action-button page btn-white js-action-back"><?php echo __('admin.common.button.return_to_list'); ?></a>
                    <span class="action-button page btn-red-border icon-only js-action-delete"><span class="icon-delete"></span></span>
                </div>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">
    const projectData = {
        project_name: `<?php echo $project ? $project['project_name'] : ''; ?>`,
        description: `<?php echo $project ? $project['description'] : ''; ?>`,
    };
</script>

<!-- 複数選択コンポーネント -->
<script src="/assets/common/react/components/atoms/multiselect.bundle.js"></script>