			<!-- BEGIN PAGE HEADER-->
			<!-- BEGIN PAGE HEAD -->
			<div class="page-head">
				<!-- BEGIN PAGE TITLE -->
				<div class="page-title">
					<h1>投稿管理<small></small></h1>
				</div>
				<!-- END PAGE TITLE -->
			</div>
			<!-- END PAGE HEAD -->
			<!-- END PAGE HEADER-->
			<!-- BEGIN PAGE CONTENT-->
			<div class="row">
				<div class="col-md-12">
			        <!-- Page Content -->
			        <div id="page-wrapper">
			        <div class="portlet light">
						<div class="tabbable-line"> 
							<ul class="nav nav-tabs ">
								<li class="active">
									<a href="/admin/postlist">投稿一覧 </a>
								</li>
							</ul>
						</div>
						<div class="portlet box">
							<div class="portlet-body">					
							<input type="hidden" name="post_id" id="post_id" value="" />
							<input type="hidden" name="sns_type_cd" id="sns_type_cd" value="" />
							<div class="row">
								<div class="form-group">
									<label class="control-label col-md-1">日付</label>
									<div class="col-md-2">
										<input name="start_date" id="start_date" value="<?php echo($post["start_date"])?>" class="form-control form-control-inline input-small date-picker" size="16" data-date-format="yyyy-mm-dd"  type="text"/>
									</div>
									<div class="col-md-2">
										<input name="end_date" id="end_date" value="<?php echo($post["end_date"])?>" class="form-control form-control-inline input-small date-picker" size="16" data-date-format="yyyy-mm-dd"  type="text"/>
									</div>
									<label class="control-label col-md-1">状態</label>
									<div class="col-md-2">
										<?php echo Form::select('approval_flg', $approval_flg, $post['approval_flg'], array('id'=>'approval_flg','class'=>'form-control'))?>
									</div>									
									<div class="col-md-3">
										<button type="submit" id="searchButton" class="btn blue">
										<i class="fa fa-search"></i>&nbsp;&nbsp;検&nbsp;索&nbsp;&nbsp;</button>
									</div>
								</div>												
							</div>
							<table class="table table-striped table-bordered table-hover js-data-table" id="postlisttable">
							<thead>
							<tr>
								<th>
								投稿日時
								</th>
								<th>
								投稿人
								</th>
								<th>
								内容
								</th>
								<th>
								ステータス
								</th>
								<th>
									 
								</th>															
							</tr>
							</thead>

							<tbody>
							<?php
								foreach ($msgs as $msg) {
							?>	
							<tr class="gradeX odd" role="row">
								<td class="sorting_1">
									 <?php echo($msg['post_time'])?>
								</td>
								<td>				
									<?php echo($msg['member_id'])?> 
									<br/> 
									<?php
										if ($msg['sns_type_cd']== 'fb') {
											echo('<span class="label label-primary" style="margin-left: 5px;" >Facebook</span>');
										}
										if ($msg['sns_type_cd']== 'ln') {
											echo('<span class="label label-success" style="margin-left: 5px;" >Line</span>');
										}
										if ($msg['sns_type_cd']== 'wc') {
											echo('<span class="label label-success" style="margin-left: 5px;" >WeChat</span>');
										}
										if ($msg['sns_type_cd']== 'wb') {
											echo('<span class="label label-warning" style="margin-left: 5px;" >Web</span>');
										}
									?>
								</td>
								<td>
									<?php echo(nl2br($msg['post_msg']))?>
									<br/>
									<a href="<?php echo($msg['post_image_url'])?>" target="blank" class="image_url" data-thumbnail-src="<?php echo($msg['post_image_url'])?>"><span class="badge badge-warning">写真表示</span></a>				 
								</td>								
								<td>														
									<?php
										if ($msg['approval_flg']==0) {
											echo('<span class="badge badge-warning">');
										}
										else if ($msg['approval_flg']==1) {
											echo('<span class="badge badge-success">');
										}
										else {
											echo('<span class="badge badge-danger">');
										}
										echo($_codes['19'][$msg['approval_flg']]);
										echo('</span>');
									?>									
								</td>
								<td>
								<?php if ($msg['approval_flg'] == 0) {?>
								<button type="button" class="btn green action" sns_type_cd="<?php echo($msg['sns_type_cd'])?>" sid="<?php echo($msg['post_id'])?>" act="1">許可</button>
								<button type="button" class="btn red action" sns_type_cd="<?php echo($msg['sns_type_cd'])?>" sid="<?php echo($msg['post_id'])?>" act="2">却下</button>
								<?php } ?>
								<?php if ($msg['approval_flg'] == 2) {?>
								<button type="button" class="btn green action" sid="<?php echo($msg['post_id'])?>" act="1">許可</button>
								<?php } ?>
								</td>								
							</tr>
							<?php } ?>
							</tbody>
							</table>
							</div>
						</div>
					</div>
			        </div>
			        <!-- /#page-wrapper -->					
				</div>
			</div>
			<!-- END PAGE CONTENT-->
