			<!-- BEGIN PAGE HEADER-->
			<!-- BEGIN PAGE HEAD -->
			<div class="page-head">
				<!-- BEGIN PAGE TITLE -->
				<div class="page-title">
					<h1>コンテンツアクセス状況<small></small></h1>
				</div>
				<!-- <PERSON>ND PAGE TITLE -->
			</div>
			<!-- END PAGE HEAD -->
			<!-- END PAGE HEADER-->
			<!-- BEGIN PAGE CONTENT-->
			<div class="row">
				<div class="col-md-12">
			        <!-- Page Content -->
			        <div id="page-wrapper">
			        <div class="portlet light">			        
					<?php echo $reportmenu ?>
						<div class="portlet box">
							<div class="portlet-body">
							<input type="hidden" name="ref_div" value="<?php echo $ref_div?>" />
								<div class="form-body">		
									<div class="form-group">
										<label class="control-label col-md-1" style="padding-left:15px; padding-right:15px;">日付</label>
										<div class="col-md-4">
											<input name="start_date" id="start_date" value="<?php echo($start_date)?>" style="float:left;" class="form-control form-control-inline input-small date-picker" size="16" data-date-format="yyyy-mm-dd"  type="text"/>
											<input name="end_date" id="end_date" value="<?php echo($end_date)?>" style="float:left; margin-left:10px;" class="form-control form-control-inline input-small date-picker" size="16" data-date-format="yyyy-mm-dd"  type="text"/>
										</div>
										<label class="control-label col-md-1">言語</label>
										<div class="col-md-2">
											<?php echo Form::select('lang_cd', $lang_cd_array, $lang_cd, array('id'=>'lang_cd','class'=>'form-control'))?>
										</div>																			
									</div>
									<div class="form-group">
										<label class="control-label col-md-1" style="padding-left:15px; padding-right:15px;"></label>
										<?php echo $botcond ?>	
										<div class="col-md-1">
											<button type="button" id="searchButton" class="btn yellow" disabled="disabled">
											<i class="fa fa-search mr10"></i>検索</button>
										</div>		
									</div>									
								</div>													
							<table class="table table-striped table-bordered table-hover js-data-table">
							<thead>
							<tr>
								<th>
									 名称
								</th>
								<th style="display: none;">
									 種類
								</th>
								<?php 
								foreach($ref_type as $k=>$v) {
									echo('<th style="width: 80px;">');
									echo $v;
									echo('</th>');
								}
								?>																						
							</tr>
							</thead>

							<tbody>
							<?php
								foreach ($results as $k=>$v) {
							?>	
							<tr class="gradeX odd" role="row">
								<td class="sorting_1">
									<?php echo($v['item_name'])?>
								</td>
								<td style="display:none;">
									<?php 
									$class_cd = explode(' ', $v['class_cd']);
									foreach($class_cd as $cd) {
										if (array_key_exists($cd, $code_div_dict)) echo($code_div_dict[$cd] . ' ');
									}
									?>						
								</td>
								
								<?php 
								foreach($ref_type as $m=>$n) {
									echo('<td class="right">');
									if (array_key_exists($m, $v)) echo($v[$m]);
									echo('</td>');
								}
								?>																												
							</tr>
							<?php } ?>
							</tbody>
							</table>
							</div>
						</div>
					</div>
			        </div>
			        <!-- /#page-wrapper -->					
				</div>
			</div>
			<!-- END PAGE CONTENT-->
