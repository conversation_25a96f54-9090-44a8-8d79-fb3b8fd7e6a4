<style>
  .form-group {
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
  }
  .form-group > * {
    margin-right: 15px;
  }
  .control-label {
    padding-top: 0px !important;
  }
  .trial_color {
    color: orange;
}
</style>

<div class="content-container light-gray">
    <div class="form-group">
        <label class="control-label">基準日</label>
        <input name="base_date" id="base_date" value="<?php echo($base_date)?>" class="talkappi-datepicker" type="text"/>
        <label class="control-label" style="white-space: nowrap;">セグメント</label>
        <select name="bot_class_selected" style="border: 1px solid #e3e5e8; border-radius: 4px">
            <?php foreach ($bot_class as $key => $value): ?>
                <option value="<?= $key ?>" <?= $key == $bot_class_selected ? 'selected' : '' ?>><?= $value ?></option>
            <?php endforeach; ?>
        </select>
        <button type="button" id="searchButton" class="btn-smaller btn-blue">
            <i class="fa fa-search mr10"></i>検索
        </button>
        <button type="button" id="resetSearchButton" class="btn-smaller btn-yellow">
            <i class="fa fa-search mr10"></i>検索条件をリセット
        </button>
        <button type="button" data-tableId="contracts" data-title="契約管理" class="btn-smaller btn-white exportCsv">
            <span class="icon-export"></span>CSV出力
        </button>
    </div>
</div>
<div class="content-container" style="padding-left: 0;">
    <div class="flex-x-between">
        <div>施設一覧</div>
    </div>
</div>
<div class="content-container white">
    <p style="margin-bottom: 20px;"><?php echo "<strong>" . $number_of_rows . "</strong> 件見つかりました"; ?></p>
    <table class="table table-striped table-bordered table-hover js-data-table"  id="contracts">
        <thead>
            <tr>
                <th>ボットID</th>
                <th>セグメント</th>
                <th>施設名</th>
                <?php foreach ($_codes['51'] as $key => $value): ?>
                <th><?php echo $value ?></th>
                <?php endforeach; ?>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($display_data as $row): ?>
                <tr>
                    <td><?php echo $row['bot_id']; ?></td>
                    <td><?php echo $row['facility_category']; ?></td>
                    <td><a href="/admin/contract?id=<?php echo htmlspecialchars($row['bot_id']); ?>"><?php echo htmlspecialchars($row['facility_name']); ?></a></td>
                    <?php foreach ($_codes['51'] as $key => $value): ?>
                    <td><?php echo nl2br(str_replace("試用期間", "<span class='trial_color'>試用期間</span>", htmlspecialchars($row[$key]))); ?></td>
                <?php endforeach; ?>
                </tr>
            <?php endforeach; ?>
        </tbody>
    </table>
</div>

