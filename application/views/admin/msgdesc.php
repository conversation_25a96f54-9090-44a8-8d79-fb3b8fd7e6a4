<script>
    const _preview_data = <?php echo json_encode($preview_data, JSON_UNESCAPED_UNICODE) ?>;
	let _msg_no = <?php echo $msg_no ?>;
	const _new = <?php echo $new ?>;
	const _lang_edit = '<?php echo $lang_edit ?>';
	const _msg_type_cd = '<?php echo $msg->msg_type_cd ?>';
	const _msg_action_type = '<?php echo $msg_action_type ?>';
</script>
<style>
	.talkappi-preview table {
		width: 300px;
    margin: 0 auto 0 0;
	}
</style>
<input type="hidden" name="content" id="content" value="" />
<?php $html = substr($post['content'], 0, 1) == '<' && substr($post['content'], -1) == '>'; ?>
<nav class="top-nav">
	<ul class="">
		<li class="">
			<a class="func-menu" href="/admin/<?php echo $msg_action_type?>msg?id=<?php echo($msg->msg_id)?>&back=<?php echo $back_action?>">
				<?php echo __('admin.itme.itemmeu.info') ?>
			</a>
		</li>
		<?php
		foreach($_bot_lang as $lang_cd=>$lang_name) {
				if ($lang_cd==$lang_edit) {
					echo('<li class="active">');
				}
				else {
					echo('<li>');
				}
				echo('<a class="func-menu" href="/admin/' .  $msg_action_type . 'msgdesc?lang='. $lang_cd . '&no=' . $msg_no . '">' . $lang_name . '</a></li>');
			}
			echo('<li class=""><a class="func-menu" href="/admin/' .  $msg_action_type . 'msglist">' . __('admin.itme.itemmeu.back') . '</a></li>');
		?>
	</ul>
</nav>

<div class="content-container white border">
	<?php
	$add = 0;
	echo('<div class="tabbable-line" style="margin-bottom: 10px;">');
	echo('<ul class="nav nav-tabs " style="margin-left: 12px;">');
	if (false && $msg->msg_type_cd == 'rcm' && $msg_no == 1) {
		// can not sort
	}
	else {
		echo('<li class=""><a class="deschref" style="background-color:#FFF;" href="/admin/' .  $msg_action_type . 'msgdesc?lang='. $lang_edit. '&no=' . $msg_no . '&move=prev"><span class="badge badge-warning">'. '<' . '</span></a></li>');
		echo('<li class=""><a class="deschref" style="background-color:#FFF;" href="/admin/' .  $msg_action_type . 'msgdesc?lang='. $lang_edit. '&no=' . $msg_no . '&move=next"><span class="badge badge-warning">'. '>' . '</span></a></li>');
	}
	$add_class = "active";
	foreach($msg_descs as $desc) {
		$link = $desc->no;
		//$link = $desc->title;
		if ($desc->no == $msg_no) {
			echo('<li class="active">');
			$add = 1;
			$add_class = "";
			echo('<a class="deschref" style="background-color:#FFF;" href="/admin/' . $msg_action_type . 'msgdesc?lang='. $lang_edit. '&no=' . $desc->no . '"><span class="badge badge-success">' . $link . '</span></a></li>');
		}
		else {
			echo('<li>');
			$link = $desc->no;
			echo('<a class="deschref" style="background-color:#FFF;" href="/admin/' . $msg_action_type . 'msgdesc?lang='. $lang_edit. '&no=' . $desc->no . '"><span class="badge badge-default">' . $link . '</span></a></li>');
		}
	}
	//if ($msg->msg_type_cd != 'tpl' && $msg->msg_type_cd != 'mal') {
		echo('<li class="' . $add_class .'"> <a href="/admin/' .  $msg_action_type . 'msgdesc?lang='. $lang_edit. '&no=-1"><span class="badge badge-warning">' . __('admin.common.button.add') . '</span></a> </li>');
	//}
	echo('</ul>');
	echo('</div>');	
	?>						

	<div class="portlet box">
		<div class="portlet-body">
			<div class="row">
				<div class="col-md-8">
					<div class="form-body">
					<input type="hidden" name="message" id="message" value="<?php echo($message)?>" />
					<input type="hidden" name="lang" value="<?php echo($lang_edit)?>" />
					<input type="hidden" name="no" value="<?php echo($msg_no)?>" />
					<input type="hidden" name="act" id="act" value="" />
					<input type="hidden" name="msg_type_cd" id="msg_type_cd" value="<?php echo($msg->msg_type_cd);?>" />
					<?php if ($msg->msg_type_cd == 'car') {
							$title_max_length = 160;
						}
						else {
							$title_max_length = 350;
						}
					?>
					<div class="form-group" <?php if ($msg->msg_type_cd == 'txt' || $msg->msg_type_cd == 'tpl' || (($msg->msg_type_cd == 'mnu' || $msg->msg_type_cd == 'rcm' || $msg->msg_type_cd == 'btn' || $msg->msg_type_cd == 'lst') && $msg_no > 1)) echo(' style="display:none;" ');?>>
						<label class="control-label col-md-2"><?php echo __('admin.common.label.title') ?></label>
						<div class="col-md-8">
							<textarea name="title" id="title" maxlength="<?php echo($title_max_length);?>" class="form-control" rows="2" placeholder=""><?php if (array_key_exists('title', $post)) echo($post['title'])?></textarea>
						</div>
					</div>
					<?php if ($msg->msg_type_cd != 'img' && $msg->msg_type_cd != 'mov' && $msg->msg_type_cd != 'rcm') { 
						if ($msg->msg_type_cd == 'mnu' || $msg->msg_type_cd == 'rcm' || $msg->msg_type_cd == 'btn' || $msg->msg_type_cd == 'lst') {
							$max_length = 50;
						}
						else if ($msg->msg_type_cd == 'car') {
							$max_length = 160;
						}
						else {
							$max_length = 65536;
						}
					?>
					<?php 
					$content_title = __('admin.common.label.content');
					if ($msg->msg_type_cd == 'mnu' || $msg->msg_type_cd == 'rcm' || $msg->msg_type_cd == 'btn' || $msg->msg_type_cd == 'lst') {
						$content_title = __('admin.common.button.customize');
					?>											
					<div class="form-group">
						<label class="control-label col-md-2"><?php echo __('admin.common.label.button_name_standard') ?></label>
						<div class="col-md-3">
						<?php echo Form::select('menu_button', $btn_select, $post['content'], array('class'=>'form-control', 'id'=>"menu_button"))?>
						</div>
					</div>											
					<?php } ?>
					<?php if($msg->msg_type_cd == "mal"){?>
						<div class="form-group">
							<label class="control-label col-md-2"><?php echo __('admin.common.button.edit_type') ?></label>
							<div class="talkappi-radio js-edit-style col-md-10" data-name="edit-style" data-value=<?php if($html == false){ echo "01";} else { echo "02";} ?> data-source='{"01":"テキスト", "02":"HTML"}'></div>
						</div>
					<?php } ?>
					<div class="form-group">
						<label class="control-label col-md-2"><?php echo($content_title);?></label>
						<div class="col-md-10">
							<?php if($msg->msg_type_cd == "mal"){?>
								<!-- メールテンプレートのみsummernote編集可能 -->
								<div class="js-mal-html" style="<?php if($html == false) echo 'display: none;' ?>">
									<div id="summernote"><?php 
										if (array_key_exists('content', $post) && $html != false) {
											// HTML編集の場合:不必要な改行を削除
											echo(str_replace(PHP_EOL,"",$post['content']));
										}?></div>
								</div>
								<div class="js-mal-textarea" style="<?php if($html != false) echo 'display: none;' ?>">
									<textarea name="content" id="content" maxlength="<?php echo($max_length);?>" class="form-control" rows="2" placeholder=""><?php
									if (array_key_exists('content', $post) && $html == false) {
										echo($post['content']);
									}?></textarea>
								</div>
							<?php } else { ?>
								<textarea name="content" id="content" maxlength="<?php echo($max_length);?>" class="form-control" rows="2" placeholder=""><?php
								if (array_key_exists('content', $post)) {
									if (strpos($post['content'], "BTN_") === 0) {
										echo($btn_select[$post['content']]);
									}
									else {
										echo($post['content']);
									}
								}?></textarea>
							<?php } ?>
							<?php if ($msg->msg_type_cd == 'mnu' || $msg->msg_type_cd == 'rcm' || $msg->msg_type_cd == 'btn' || $msg->msg_type_cd == 'lst') { ?>
							<br /><?php echo __('admin.msgdesc.button_message') ?>
							<?php } ?>
						</div>
					</div>
					<?php }?>
					<?php if ($msg->msg_type_cd == 'rcm') { ?>
					<div class="form-group">
						<label class="control-label col-md-2">エリア</label>
						<div class="col-md-2">
							<input type="text" name="area_x" class="form-control" value="<?php if ($post != NULL) echo($post['area_x'])?>" placeholder="X">
						</div>
						<div class="col-md-2">
							<input type="text" name="area_y" class="form-control" value="<?php if ($post != NULL) echo($post['area_y'])?>" placeholder="Y">
						</div>
						<div class="col-md-2">
							<input type="text" name="area_w" class="form-control" value="<?php if ($post != NULL) echo($post['area_w'])?>" placeholder="Width">
						</div>
						<div class="col-md-2">
							<input type="text" name="area_h" class="form-control" value="<?php if ($post != NULL) echo($post['area_h'])?>" placeholder="Height">
						</div>
					</div>											
					<?php }?>
					<?php if ($msg->msg_type_cd == 'car') { ?>
					<div class="form-group">
						<label class="control-label col-md-2"><?php echo __('admin.common.label.mobile') ?></label>
						<div class="col-md-2">
							<input type="checkbox" name="flg_mobile" id="flg_mobile" value="1" <?php if ($mobile_flg == 1) echo('checked');?> class="make-switch" data-on-color="success" data-off-color="warning">
						</div>
					</div>											
					<?php for($i=1; $i<=3; $i++) {?>
					<div class="form-group">
						<label class="control-label col-md-2"><?php echo __('admin.common.label.button') ?><?php echo $i?></label>
						<div class="col-md-2" style="padding-right: 0px;">
						<?php echo Form::select('btn' . $i . '_name', $btn_select, $post['btn' . $i . '_name'], array('id'=>'btn' . $i . '_name', 'class'=>'form-control btn_type', 'style'=>"width:120px;"))?>
						</div>
						<div class="col-md-2" style="padding-left: 30px;">
						<?php echo Form::select('btn' . $i . '_url_lang_cd', $url_lang_cd, $post['btn' . $i . '_url_lang_cd'], array('id'=>'btn' . $i . '_url_lang_cd', 'class'=>'form-control btn_lang', 'style'=>"width:90px;"))?>
						</div>
						<div class="col-md-6">
						<input name="btn<?php echo $i?>_url" type="text" maxlength="1001" class="form-control pc" placeholder="" value="<?php if ($post != NULL) echo(htmlspecialchars($post['btn' . $i . '_url']))?>">
							<div class="input-group" id="btn<?php echo $i ?>_url_sp" style="margin-top: 2px;<?php if ($mobile_flg == 0) echo ('display:none;'); ?>">
								<span class="input-group-addon">
									<i class="fa fa-mobile-alt"></i>
								</span>
								<input name="btn<?php echo $i ?>_url_sp" type="text" maxlength="1001" class="form-control" placeholder="Mobile用" value="<?php if ($post != NULL) echo (htmlspecialchars($post['btn' . $i . '_url_sp'])) ?>">
							</div>												
							<div class="talkappi-skill-select" data-name="btn<?php echo $i ?>_url_skill" data-value='<?php echo json_encode($skills['btn' . $i . '_url_skill'], JSON_UNESCAPED_UNICODE) ?>' data-mode="skill"></div>	
						</div>
					</div>
					<?php }?>
					<?php }?>
					<?php if ($msg->msg_type_cd == 'img' || $msg->msg_type_cd == 'mov' || $msg->msg_type_cd == 'car'|| 
							($msg->msg_type_cd == 'rcm' && $msg_no == 1)) { 
					?>
					<div class="form-group">
						<label class="control-label col-md-2"></label>
						<div class="col-md-8">
						<?php 
						if ($msg->msg_type_cd == 'img') {
							if ($image_option == 'line_imagemap') {
								echo('<div class="talkappi-upload" data-name="image_base64" data-type="img" data-ratio="1:1 (1040x1040)" data-label="'. $post['msg_image'] . '" data-url="'. $post['msg_image'] . '" data-max-size="1"></div>');
							}
							else if ($image_option == 'line_carousel') {
								echo('<div class="talkappi-upload" data-name="image_base64" data-type="img" data-ratio="1:1" data-label="'. $post['msg_image'] . '" data-url="'. $post['msg_image'] . '" data-max-size="1"></div>');
							}
							else {
								echo('<div class="talkappi-upload" data-name="image_base64" data-type="img" data-label="'. $post['msg_image'] . '" data-url="'. $post['msg_image'] . '" data-max-size="2"></div>');
							}
						}
						else if ($msg->msg_type_cd == 'mov') {
							echo('<div class="talkappi-upload" data-name="image_base64" data-type="mov" data-label="'. $post['msg_image'] . '" data-url="'. $post['msg_image'] . '" data-max-size="20"></div>');
						}
						else {
							echo('<div class="talkappi-upload" data-name="image_base64" data-type="img" data-label="'. $post['msg_image'] . '" data-url="'. $post['msg_image'] . '" data-max-size="2"></div>');
						}
						?>
						</div>
					</div>
					<?php }?>
					<?php if ($msg->msg_type_cd != 'txt' && $msg->msg_type_cd != 'tpl' && $msg->msg_type_cd != 'mal') { 								
						if ($msg->msg_type_cd == 'car') {
							?>
						<div class="form-group">
							<label class="control-label col-md-2"><?php echo __('admin.common.label.photo_action') ?></label>
							<div class="col-md-10">
								<div class="talkappi-skill-select" data-name="url" data-value='<?php echo json_encode($action_skills, JSON_UNESCAPED_UNICODE) ?>' data-mode="skill"></div>
							</div>
						</div>
						<?php 
						}
						else {
							?>
						<div class="form-group">
							<label class="control-label col-md-2"><?php echo __('admin.common.label.action') ?></label>
							<div class="col-md-10">
								<div class="talkappi-skill-select" data-name="url" data-value='<?php echo json_encode($action_skills, JSON_UNESCAPED_UNICODE) ?>' data-mode="skill"></div>										
							</div>
						</div>	
					<?php } ?>
						<div class="form-group">
							<label class="control-label col-md-2"><?php echo __('admin.common.label.click_counts') ?></label>
							<div class="col-md-2">
								<input type="checkbox" name="flg_ref" <?php if ($post['flg_ref'] == 1) echo('checked')?> value="1" class="make-switch" data-on-color="success" data-off-color="warning">
							</div>											
						</div>										
					
					<?php }?>
						<div class="form-group">
							<label class="control-label col-md-2"><?php echo __('admin.common.label.applies_to_all_languages') ?></label>
							<div class="col-md-2">
								<input type="checkbox" name="flg_apply_all_lang" value="1" class="make-switch" data-on-color="success" data-off-color="warning">
							</div>											
							<label class="control-label col-md-2"><?php echo __('admin.common.label.multilingual_translation') ?></label>
							<div class="col-md-2">
								<input type="checkbox" name="flg_auto_translate" <?php if ($auto_translate == 1) echo('checked')?> value="1" class="make-switch" data-on-color="success" data-off-color="warning">
							</div>
							<?php if ($msg->msg_type_cd == 'img' || $msg->msg_type_cd == 'mov' || $msg->msg_type_cd == 'car'|| ($msg->msg_type_cd == 'rcm' && $msg_no == 1)) { ?>
							<label class="control-label col-md-2"><?php echo __('admin.common.label.immediate_photo_reflection') ?></label>
							<div class="col-md-2">
								<input type="checkbox" name="flg_img_invalidate" value="1" class="make-switch" data-on-color="success" data-off-color="warning">
							</div>
							<?php }?>																				
						</div>
					<?php if ($msg->msg_type_cd == 'mnu' || $msg->msg_type_cd == 'rcm' || $msg->msg_type_cd == 'btn' || $msg->msg_type_cd == 'lst') {?>	
						<div class="form-group">
							<label class="control-label col-md-2"><?php echo __('admin.common.label.channel') ?></label>
							<div class="col-md-10">
								<div class="talkappi-checkbox" data-name="snses" data-value='<?php echo json_encode($post['snses'])?>' data-source='<?php echo json_encode($bot_snses, JSON_UNESCAPED_UNICODE) ?>'></div>
							</div>
						</div>
						<div class="form-group">
							<label class="control-label col-md-2"><?php echo __('admin.common.label.applying_css_styles') ?></label>
							<div class="col-md-10">
								<textarea name="style" class="form-control" rows="3" placeholder="background: #181818; color: #ffffff;"><?php if ($post != NULL) echo($post['style'])?></textarea>
							</div>
						</div>						
					<?php }?>																														 		 
					</div>
					<div class="form-actions">
						<div class="row">
							<div class="col-md-offset-2 col-md-9 flex">
								<button type="button" class="btn-larger btn-blue js-action-save"><?php echo __('admin.common.button.save') ?></button>
								<?php if ($add == 1) {?>
								<button type="button" class="btn-larger btn-red-border js-action-delete" <?php if ($can_edit == false || $msg->msg_type_cd == 'rcm' && $msg_no == 1) echo('disabled');?>><span class="icon-delete"></span></button>
								<?php }?>
								<button type="button" onclick="top.location='/admin/<?php echo $back_action?>'" class="btn-larger btn-white js-action-back"><?php echo __('admin.common.button.back') ?></button>
							</div>
						</div>
					</div>
				</div>
				<div id="msg-preview" class="col-md-4 preview-sticky">
                <div class="talkappi-preview js-msg-preview" data-type="message" style="margin:0 0 0 20px; flex-basis: 30%; max-width:320px;">
                </div>
				</div>
			</div>
		</div>
	</div>
</div>



