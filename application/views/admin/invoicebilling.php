			<!-- B<PERSON>IN PAGE HEADER-->
			<!-- BEGIN PAGE HEAD -->
<style type='text/css'>
  /* Style to hide Dates / Months */
  .ui-datepicker-calendar,.ui-datepicker-month { display: none; }​
</style>
			<div class="page-head">
				<!-- BEGIN PAGE TITLE -->
				<div class="page-title">
					<h1>請求書一覧<small></small></h1>
				</div>
				<!-- END PAGE TITLE -->
			</div>
			<!-- END PAGE HEAD -->
			<!-- END PAGE HEADER-->
			<!-- BEGIN PAGE CONTENT-->
			<div class="row">
				<div class="col-md-12">
			        <!-- Page Content -->
			        <div id="page-wrapper">
			        <div class="portlet light">
						<div class="portlet box">
							<div class="portlet-body">
								<div class="form-body">		
									<div class="form-group">
										<label class="control-label col-md-1">期間</label>
										<div class="col-md-4">
											<div class="input-group input-medium date date-picker" style="float:left;width:128px !important;" data-date="<?php echo(substr($post['start_date'], 0, 7))?>" data-date-format="yyyy-mm" data-date-startview="months" data-date-viewmode="months" data-date-minviewmode="months">
												<input type="text" name="start_date" value="<?php echo(substr($post['start_date'], 0, 7))?>" class="form-control" readonly>
												<span class="input-group-btn">
												<button class="btn default" type="button"><i class="fa fa-calendar"></i></button>
												</span>
											</div>
											<div class="input-group input-medium date date-picker" style="float:left; margin-left:10px;width:128px !important;" data-date="<?php echo(substr($post['end_date'], 0, 7))?>" data-date-format="yyyy-mm" data-date-startview="months" data-date-viewmode="months" data-date-minviewmode="months">
												<input type="text" name="end_date" value="<?php echo(substr($post['end_date'], 0, 7))?>" class="form-control" readonly>
												<span class="input-group-btn">
												<button class="btn default" type="button"><i class="fa fa-calendar"></i></button>
												</span>
											</div>																		
										</div>
										<label class="control-label col-md-1" <?php if (count($bots) == 1) echo('style="display:none;"')?>>ボット</label>
										<div class="col-md-3" <?php if (count($bots) == 1) echo('style="display:none;"')?>>
											<?php echo Form::select('bot_id', $bots, $post['bot_id'], array('id'=>'bot_id','class'=>'form-control'))?>
										</div>										
										<div class="col-md-1">
											<button type="button" id="searchButton" class="btn blue">
											<i class="fa fa-search mr10"></i>検索</button>
										</div>																	
									</div>									
								</div>							
							<table class="table table-striped table-bordered table-hover js-data-table">
							<thead>
							<tr>
								<th>
									 作成日
								</th>
								<th>
									タイトル
								</th>
								<th>
									金額
								</th>
								<th>
									 お支払期限
								</th>							
								<th>
									
								</th>
							</tr>
							</thead>

							<tbody>
							<?php
							foreach($billings as $billing) {
							?>	
							<tr class="gradeX odd" role="row">
								<td>
								<?php echo($billing['billing_date'])?>
								</td>
								<td>
								<?php echo($billing['title'])?>
								</td>
								<td style="text-align: right;">
								<?php echo(number_format($billing['excise_price']))?>
								</td>								
								<td>
								<?php echo($billing['due_date'])?>
								</td>			
								<td style="text-align: center">
								<a href="/ajax/invoicebillingdl?url=<?php echo(urlencode($billing['pdf_url']))?>" target="_blank"><span class="badge badge-success" style="margin-left:5px;">ダウンロード</span></a>
								<?php if ($billing['bot_mail_flg'] == 0) {?>
								<span class="badge badge-danger" style="margin-left:5px;">未送信</span>
								<?php }?>
								</td>																												
							</tr>
							<?php } ?>
							</tbody>
							</table>
							</div>
						</div>
					</div>
			        </div>
			        <!-- /#page-wrapper -->					
				</div>
			</div>
			<!-- END PAGE CONTENT-->
