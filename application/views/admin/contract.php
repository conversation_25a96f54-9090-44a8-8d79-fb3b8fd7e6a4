<script type="text/javascript">
	const _bot_data = <?php echo json_encode($bot_data, JSON_UNESCAPED_UNICODE)?>;
	const _contracts = <?php echo json_encode($contracts, JSON_UNESCAPED_UNICODE)?>;
    const _contract_types = <?php echo json_encode($_codes['50'], JSON_UNESCAPED_UNICODE)?>;
    const _contract_service = <?php echo json_encode($_codes['51'], JSON_UNESCAPED_UNICODE)?>;
    const _select_bots = <?php echo json_encode($select_bots, JSON_UNESCAPED_UNICODE)?>;
</script>

<style>
    .dataTables_wrapper .row {
        display: none;
    }
    .table-scrollable {
        overflow-x: inherit;
        overflow-y: inherit;
    }

    .edit-contract div,
    .add-contract {
        cursor: pointer;
    }

    /* 施設カテゴリ：クリック禁止 */
    .category-pulldown .talkappi-dropdown-container {
        pointer-events: none;
        background-color: #eeeeee;
    }

    .input_width {
        width: 300px;
    }
</style>

<input type="hidden" id="contracts" name="contracts" value='<?php echo json_encode($contracts, JSON_UNESCAPED_UNICODE)?>' />
<input type="hidden" id="deleted" name="deleted" value='[]' />

<section class='content-container white'>
    <!-- 設定 -->
    <section>
        <div class="setting-header"><?php echo __('admin.common.label.setting') ?></div>
        <!-- 施設名 -->
        <div class="form-body">
            <div class="lines-container">
                <label class="basic-label">施設名</label>
                <div class="talkappi-pulldown js-select-bot input_width" 
                    data-name="bot_id" data-value=<?php echo $bot_id?> data-source='{}'>
                </div>
            </div>
        </div>
        <!-- カテゴリ -->
        <div class="form-body">
            <div class="lines-container">
                <label class="basic-label">施設カテゴリ</label>
                <div class="talkappi-pulldown category-pulldown input_width"
                    data-name="bot_category" data-value="<?php echo $bot_data['bot_class_cd']?>"
                    data-source='<?php echo json_encode($bot_class, JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES|JSON_PRETTY_PRINT) ?>'>
                </div>
            </div>
        </div>
        <!-- bot_id -->
        <div class="lines-container">
            <label class="basic-label">BOT ID</label>
            <input name="" id="" type="text" class="form-control input_width" disabled value='<?php echo $bot_id?>'>
        </div>
    </section>
    <!-- 各サービスの契約情報 -->
    <section class='js-contracts'>
    </section>
    <!-- 下部ボタン -->
    <section class="actions-container" style='margin: 54px 0 0 80px;'>
        <div class="action-button page btn-blue js-action-save">保存する</div>
        <a class="action-button page btn-white js-action-back">一覧に戻る</a>
    </section>
</section>