<style>
a, a:hover, a:active, a:visited, a:focus {
    text-decoration:none;
}
.blue.btn {
    color: #FFFFFF;
    background-color: #aaa;
    border-color: "";
    margin:2px;
}
</style>
			<!-- BEGIN PAGE HEADER-->
			<!-- BEGIN PAGE HEAD -->
			<div class="page-head">
				<!-- BEGIN PAGE TITLE -->
				<div class="page-title">
					<h1>FAQページの設定<small></small></h1>
				</div>
				<!-- END PAGE TITLE -->
			</div>
			<!-- END PAGE HEAD -->
			<!-- END PAGE HEADER-->
			<!-- BEGIN PAGE CONTENT-->
				<div class="col-md-12">
			        <!-- Page Content -->
			        <div id="page-wrapper">
				        <div class="portlet light">
							<div class="portlet box">
								<div class="portlet-body">
									<div class="row">
										<div class="col-md-10">
											<input type="hidden" name="act" id="act"/>
											<input type="hidden" name="message" id="message" value="<?php echo($message)?>" />
											<div id="base_url" style="display: none;"><?php echo $base_url?></div>
											<div class="form-body">
												<div class="form-group">
													<label class="control-label col-md-3"><?php echo __('admin.common.label.scene') ?></label>
													<div class="col-md-7">
														<?php echo Form::select('scene_cd', $scene, $post['scene_cd'], array('id'=>'scene_cd','class'=>'form-control'))?>
													</div>
												</div>
												<div class="form-group">
													<label class="control-label col-md-3" <?php echo $post['faq_keywords_flg'] ?>><?php echo __('admin.common.label.frequently_used_keywords') ?></label>
													<div class="col-md-8">
														<div class="alert alert-<?php echo $post['faq_keywords_must'] ?>" style="padding:5px;">
														<a href="/admin/sysmsg?id=<?php echo $post['faq_keywords_msgid']?>&back=faqpage"><span> <?php echo $post['faq_keywords']?> </span></a>
														</div>
													</div>
												</div>								
												<div class="form-group">
													<label class="control-label col-md-3" <?php echo $post['faq_page_title_flg'] ?>><?php echo __('admin.common.label.header_title') ?></label>
													<div class="col-md-8">
														<div class="alert alert-<?php echo $post['faq_page_title_must'] ?>" style="padding:5px;">
														<a href="/admin/sysmsg?id=<?php echo $post['faq_page_title_msgid']?>&back=faqpage"><span> <?php echo $post['faq_page_title']?> </span></a>
														</div>
													</div>
												</div>
												<div class="form-group">
													<label class="control-label col-md-3" <?php echo $post['faq_button_detail_flg'] ?>><?php echo __('admin.common.label.detail_button_title') ?></label>
													<div class="col-md-8">
														<div class="alert alert-<?php echo $post['faq_button_detail_must'] ?>" style="padding:5px;">
														<a href="/admin/sysmsg?id=<?php echo $post['faq_button_detail_msgid']?>&back=faqpage"><span> <?php echo $post['faq_button_detail']?> </span></a>
														</div>
													</div>
												</div>
											</div>												
											<div class="form-actions">
												<div class="form-group">	
													<span class="col-md-offset-3 col-md-9" style="display: none;"><?php echo __('admin.faqpage.message.setup') ?></span>
												</div>												
												<div class="form-group">	
													<div class="col-md-offset-3 col-md-6">
													<button type="button" id="themeButton" <?php if (count($scene) == 0) echo("disabled")?> class="btn yellow mr10"><?php echo __('admin.common.label.hp_customization') ?></button>
													<button type="button" id="previewButton" <?php if (count($scene) == 0) echo("disabled")?> class="btn green mr10"><?php echo __('admin.common.label.preview') ?></button>
													
													</div>
												</div>
											</div>
									</div>
								</div>
							</div>
						</div>
					</div>
			    </div>
			</div>

			<!-- END PAGE CONTENT-->



