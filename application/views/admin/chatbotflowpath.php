<!-- BEGIN PAGE HEADER-->
<!-- BEGIN PAGE HEAD -->
<div class="page-head">
    <!-- BEGIN PAGE TITLE -->
    <div class="page-title">
        <h1>利用状況レポート<small></small></h1>
    </div>
    <!-- END PAGE TITLE -->
</div>
<!-- END PAGE HEAD -->
<!-- END PAGE HEADER-->
<!-- BEGIN PAGE CONTENT-->
<?php echo $reportmenu ?>
<div class="row">
    <div class="col-md-12">
        <!-- Page Content -->
        <div id="page-wrapper">
        <div class="portlet light">			        
            <div class="portlet box">
                <div class="portlet-body">
                    <div class="form-body">		
                        <div class="form-group">
                            <label class="control-label col-md-1" style="padding-left:15px; padding-right:15px;"><?php echo __('admin.common.label.period') ?></label>
                            <div class="col-md-4">
                                <input name="start_date" id="start_date" value="<?php echo($start_date)?>" style="float:left;" class="form-control form-control-inline input-small date-picker" size="16" data-date-format="yyyy-mm-dd"  type="text"/>
                                <input name="end_date" id="end_date" value="<?php echo($end_date)?>" style="float:left; margin-left:10px;" class="form-control form-control-inline input-small date-picker" size="16" data-date-format="yyyy-mm-dd"  type="text"/>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="control-label col-md-1" style="padding-left:15px; padding-right:15px;"><?php echo __('admin.common.label.conditions') ?></label>
                            <div id="bot_cond_scene" class="col-md-3">
                                <?php echo Form::select('cond_scene_cd', $scene, $scene_cd, array('id'=>'cond_scene_cd','class'=>'form-control'))?>
                            </div>
                            <div class="col-md-1">
                                <button type="button" id="searchButton" class="btn yellow">
                                <i class="fa fa-search mr10"></i><?php echo __('admin.common.button.search') ?></button>
                            </div>
                        </div>
                    </div>
                <table class="table table-striped table-bordered table-hover js-data-table">
                <thead>
                <tr>
                    <th>URL</th>
                    <th><?php echo __('admin.common.label.count') ?></th>
                </tr>
                </thead>

                <tbody>
                <?php foreach ($reports as $report) { ?>
                    <?php if ($report['refer'] !== '' && $report['refer'] !== null){ ?>
                    <tr class="gradeX odd" role="row">
                        <td>
                            <?php echo $report['refer'] ?>
                        </td>
                        <td>
                            <?php echo intval($report['refer_count']) ?>
                        </td>
                    </tr>
                    <?php }?>
                <?php } ?>
                </tbody>
                </table>
                </div>
            </div>
        </div>
        </div>
        <!-- /#page-wrapper -->					
    </div>
</div>
<!-- END PAGE CONTENT-->
