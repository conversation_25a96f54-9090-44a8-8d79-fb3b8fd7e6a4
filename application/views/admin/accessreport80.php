			<!-- B<PERSON>IN PAGE HEADER-->
			<!-- BEGIN PAGE HEAD -->
			<div class="page-head">
				<!-- BEGIN PAGE TITLE -->
				<div class="page-title">
					<h1>予約エンジン連携状況<small></small></h1>
				</div>
				<!-- END PAGE TITLE -->
			</div>
			<!-- END PAGE HEAD -->
			<!-- END PAGE HEADER-->
			<!-- BEGIN PAGE CONTENT-->
			<div class="row">
				<div class="col-md-12">
			        <!-- Page Content -->
			        <div id="page-wrapper">
			        <div class="top-nav font-standard">			        
					<?php echo $reportmenu ?>
						<div class="edit-container">
							<div class="settings-container">
							<input type="hidden" name="ref_div" value="<?php echo $ref_div?>" />
								<div class="form-body">		
									<div class="form-group">
										<label class="control-label col-md-1" style="padding-left:15px; padding-right:15px;"><?php echo __('admin.common.label.period') ?></label>
										<div class="col-md-4">
											<input name="start_date" id="start_date" value="<?php echo($start_date)?>" style="float:left;" class="form-control form-control-inline input-small date-picker" size="16" data-date-format="yyyy-mm-dd"  type="text"/>
											<input name="end_date" id="end_date" value="<?php echo($end_date)?>" style="float:left; margin-left:10px;" class="form-control form-control-inline input-small date-picker" size="16" data-date-format="yyyy-mm-dd"  type="text"/>
										</div>																										
										<label class="control-label col-md-1"><?php echo __('admin.common.label.markup_lang') ?></label>
										<div class="col-md-2">
											<?php echo Form::select('lang_cd', $lang_cd_array, $lang_cd, array('id'=>'lang_cd','class'=>'form-control'))?>
										</div>
										<?php echo $botcond ?>
										<div class="col-md-3 flex">
											<button type="button" id="searchButton" class="btn-smaller btn-yellow" disabled="disabled">
											<i class="fa fa-search mr10"></i><?php echo __('admin.common.button.search') ?></button>
											<button type="button" data-tableId="sample" data-title="予約エンジン連携状況統計" class="btn-smaller btn-white exportCsv">
												<span class="icon-export"></span><?php echo __('admin.common.button.csv_export') ?>
											</button>
										</div>				
									</div>				
								</div>	
							<table class="table table-striped table-bordered table-hover" id="sample">
							<thead>
							<tr>
								<th><?php echo __('admin.common.label.class') ?></th>
								<th><?php echo __('admin.common.label.count') ?></th>																	
							</tr>
							</thead>

							<tbody>
							<?php
							foreach ($results_common as $it) {
							?>	
							<tr class="gradeX odd" role="row">
								<td class="sorting_1">
									<?php
										if ($it['ref_type_cd'] == "SHOW") {
											echo __('admin.common.label.impressions');
										}
										else if ($it['ref_type_cd'] == "CLICK") {
											echo __('admin.common.label.count_click_button');
										}
									?>
								</td>
								<td>
									<?php echo($it['access_count'])?>		
								</td>																																			
							</tr>
							<?php } ?>
							</tbody>
							</table>
							</div>
						</div>
					</div>
			        </div>
			        <!-- /#page-wrapper -->					
				</div>
			</div>
			<!-- END PAGE CONTENT-->
