  <div class="row">
	<div class="col-md-1 col-sm-4"> 
		<?php 
		echo Form::select('page_size', ["10"=>"10", "20"=>"20", "50"=>"50", "10000"=>"全", ], $page_size, array('id'=>'page_size','class'=>'form-control page_size'))?>
	</div>
   <div class="col-md-3 col-sm-8"> 
    <div class="dataTables_info" id="sample_3_info" role="status" aria-live="polite">
      <?php 
      $pages = ceil($count / $page_size);
      $temp = ($no+1)*$page_size;
      if ($temp > $count) $temp = $count;
	//   echo('Showing ' .  ($no*$page_size+1) . ' to ' . $temp . ' of ' . $count . ' entries');
	  echo($count . '件中' . ($no*$page_size+1) . 'から' . $temp . 'まで表示');

      ?> 
    </div>
   </div>   
   <div class="col-md-8 col-sm-12"> 
    <div class="dataTables_paginate paging_simple_numbers" id="sample_3_paginate"> 
     <ul class="pagination"> 
     	<?php 
     	if ($no == 0) {
     		echo('<li class="paginate_button previous disabled" ><a href="#"><i class="fa fa-angle-left"></i></a></li>');
     	}
     	else {
     		echo('<li class="paginate_button previous " ><a href="' . $action . '?no=' . ($no - 1) . '"><i class="fa fa-angle-left"></i></a></li>');
     	}
     	?>

		<?php for($i = 0; $i < $pages; $i++) {
			if ($i == $no) {
				echo('<li class="paginate_button active" ><a href="#">' . ($i+1) . '</a></li>');			
			}
			else {
				echo('<li class="paginate_button " ><a href="' . $action . '?no=' . $i . '">' . ($i+1) . '</a></li>');		
			}
		}
		?>
		<?php 
		if ($no == $pages - 1) {
			echo('<li class="paginate_button next disabled" ><a href="#"><i class="fa fa-angle-right"></i></a></li>');
		}
		else {
			echo('<li class="paginate_button next" ><a href="' . $action . '?no=' . ($pages - 1) . '"><i class="fa fa-angle-right"></i></a></li>');
		}
		?>
     </ul>
    </div>
   </div>
  </div>