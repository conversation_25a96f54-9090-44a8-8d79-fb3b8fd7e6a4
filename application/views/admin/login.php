<!DOCTYPE html>
<!--[if IE 8]> <html class="ie8 no-js"> <![endif]-->
<!--[if IE 9]> <html class="ie9 no-js"> <![endif]-->
<!--[if !IE]><!-->
<html>
<!--<![endif]-->
<!-- BEGIN HEAD -->
<head>
<meta charset="utf-8"/>
<title><?php echo __('admin.login.head.title') ?></title>
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<meta http-equiv="Content-type" content="text/html; charset=utf-8">
<meta content="" name="description"/>
<meta name="robots" content="noindex">
<meta name="googlebot" content="noindex">
<meta content="" name="author"/>
<!-- <PERSON><PERSON><PERSON> GLOBAL MANDATORY STYLES -->

<link href="/assets/common/fontawesome/css/all.css" rel="stylesheet" type="text/css">
<link href="https://cdnjs.cloudflare.com/ajax/libs/simple-line-icons/2.4.1/css/simple-line-icons.min.css" rel="stylesheet" type="text/css">
<link href="<?php echo($_assets)?>global/plugins/bootstrap/css/bootstrap.min.css" rel="stylesheet" type="text/css"/>
<link href="<?php echo($_assets)?>global/plugins/uniform/css/uniform.default.css" rel="stylesheet" type="text/css"/>
<!-- END GLOBAL MANDATORY STYLES -->
<!-- BEGIN PAGE LEVEL STYLES -->
<link href="<?php echo($_assets)?>global/plugins/select2/select2.css" rel="stylesheet" type="text/css"/>
<link href="<?php echo($_assets)?>admin/pages/css/login-soft.css" rel="stylesheet" type="text/css"/>
<!-- END PAGE LEVEL SCRIPTS -->
<!-- BEGIN THEME STYLES -->
<link href="<?php echo($_assets)?>global/css/components-rounded.css" id="style_components" rel="stylesheet" type="text/css"/>
<link href="<?php echo($_assets)?>global/css/plugins.css" rel="stylesheet" type="text/css"/>
<link href="<?php echo($_assets)?>admin/layout/css/layout.css" rel="stylesheet" type="text/css"/>
<link id="style_color" href="<?php echo($_assets)?>admin/layout/css/themes/default.css" rel="stylesheet" type="text/css"/>
<link href="<?php echo($_assets)?>admin/layout/css/custom.css" rel="stylesheet" type="text/css"/>
<!-- END THEME STYLES -->
</head>
<!-- END HEAD -->
<!-- BEGIN BODY -->
<body class="login">
<!-- BEGIN LOGO -->
<div class="logo">
	<img src="/assets/admin/images/talkappi_logo_white.svg" alt="" width="320px"/>
</div>
<!-- END LOGO -->
<!-- BEGIN SIDEBAR TOGGLER BUTTON -->
<div class="menu-toggler sidebar-toggler">
</div>
<!-- END SIDEBAR TOGGLER BUTTON -->
<!-- BEGIN LOGIN -->
<style>
	.text-divider {
		display: flex;
		align-items: center;
		width: 100%;
	}

	.text-divider::before, .text-divider::after {
		flex: 1;
		content: '';
		padding: 1px;
		background-color: white;
		margin: 5px;
	}
</style>
<div class="content">

<?php if (isset($forget_input)) { ?>
	<!-- BEGIN FORGET PASSWORD MODIFY FORM -->
	<form class="password-form" action="/login/forgetinput" method="post">
		<h3 class="form-title"><?php echo __('admin.login.title.forgot_password_input') ?></h3>
 		<?php 
		if ($finish == 1) {
			$style = 'success';
			$message = __('admin.login.message.forgot_password_complete');
		}
		else {
			$style = 'danger';
		}
 		?>
		<div class="alert alert-<?php echo $style ?>" id="modify-message" <?php if ($message == '') echo('style="display:none;"')?>>
			<button class="close" data-close="alert"></button>
			<span>
			<?php echo $message?> </span>
		</div>
		<?php if ($finish === 0) {?>
		<div class="form-group">
			<label class="control-label"><?php echo __('admin.login.title.forgot_password_login.id') ?></label>
			<div class="input-icon">
				<i class="fa fa-user"></i>
				<input class="form-control placeholder-no-fix" type="text" autocomplete="off" placeholder="<?php echo __('admin.login.placeholder.mail') ?>" name="email" value="<?php echo $email ?>"/>
			</div>
		</div>
		<div class="form-group">
			<label class="control-label"><?php echo __('admin.login.title.new_password') ?></label>
			<div class="input-icon">
				<i class="fa fa-lock"></i>
				<input class="form-control placeholder-no-fix" type="password" autocomplete="off" placeholder="<?php echo __('admin.login.placeholder.password') ?>" name="password1"/>
			</div>
		</div>		
		<div class="form-group">
			<label class="control-label"><?php echo __('admin.login.title.new_password_confirm') ?></label>
			<div class="input-icon">
				<i class="fa fa-lock"></i>
				<input class="form-control placeholder-no-fix" type="password" autocomplete="off" placeholder="<?php echo __('admin.login.placeholder.password') ?>" name="password2"/>
			</div>
		</div>
		<?php }?>	
		<div class="form-actions">
			<?php if ($finish === 0) {?>
			<button type="button" id="modifyButton" class="btn blue pull-right">
			<?php echo __('admin.login.title.confirm') ?>
			</button>
			<?php }?>	
			<button type="button" class="btn back-login">
			<?php echo __('admin.login.title.back_to_login') ?></button>		
		</div>
	</form>
	<!-- END FORGET PASSWORD MODIFY FORM-->
	<?php } elseif (isset($two_factor_auth_required) && $two_factor_auth_required) { ?>
    <!-- BEGIN 2FA FORM -->
	<form class="2fa-form" method="post">
		<?php if (isset($two_factor_auth_error_type) && !empty($two_factor_auth_error_type)): ?> 
			<div class="alert alert-danger">
				<button class="close" data-close="alert"></button>
				<div class="error-message">
					<?php 
					if ($two_factor_auth_error_type == 'invalid_code') {
						echo __('admin.login.message.invalid_2fa_code');
					} elseif ($two_factor_auth_error_type == 'expired') {
						echo __('admin.login.message.expired_2fa_code');
					}
					?>
				</div>
			</div>
		<?php else: ?>
            <div class="alert alert-info">
                <button class="close" data-close="alert"></button>
                <div class="message">
                    <?php echo __('admin.login.message.enter_2fa_code'); ?>
                </div>
            </div>
        <?php endif; ?>
		<?php if ($two_factor_auth_error_type != 'expired'): ?>
			<div class="form-group">
				<label class="control-label"><?php echo __('admin.login.message.2fa_code') ?></label>
				<div class="input-icon">
					<i class="fa fa-key"></i>
					<input class="form-control placeholder-no-fix" type="text" autocomplete="off" placeholder="<?php echo __('admin.login.message.2fa_code') ?>" name="2fa_code" required/>
				</div>
			</div>
			<div class="form-actions">
				<button type="submit" class="btn blue pull-right">
					<?php echo __('admin.login.title.login') ?> 
				</button>
			</div>
		<?php endif; ?>
		<div class="form-actions">
			<button type="button" class="btn back-login">
				<?php echo __('admin.login.message.back_to_id_pass_form') ?>
			</button>		
		</div>
	</form>
	<!-- END 2FA FORM -->
<?php } else { ?> 
	<!-- BEGIN LOGIN FORM -->
	<form class="login-form" method="post">
		<input type="hidden" name="redirect" value="<?php echo($redirect)?>" />
		<input type="hidden" name="facility_cd" value="<?php echo($facility_cd)?>" />
		<?php if ($message != NULL) {?>
		<div class="alert alert-danger">
			<button class="close" data-close="alert"></button>
			<span>
			<?php echo $message?> </span>
		</div>
		<?php }?>
		<div class="form-group">
			<!--ie8, ie9 does not support html5 placeholder, so we just show field title for that-->
			<label class="control-label"><?php echo __('admin.login.label.login.id') ?></label>
			<div class="input-icon">
				<i class="fa fa-user"></i>
				<input  id="email" class="form-control placeholder-no-fix" type="text" autocomplete="off" placeholder="<?php echo __('admin.login.placeholder.mail') ?>" name="email" value="<?php echo $email ?>"/>
			</div>
		</div>
		<div class="form-group">
			<label class="control-label"><?php echo __('admin.login.placeholder.password') ?></label>
			<div class="input-icon">
				<i class="fa fa-lock"></i>
				<input id="password" class="form-control placeholder-no-fix" type="password" autocomplete="off" placeholder="<?php echo __('admin.login.placeholder.password') ?>" name="password"/>
			</div>
		</div>
		<?php if (count($admin_support_lang) >= 2) {?>
		<div class="form-group">
			<label class="control-label"><?php echo __('admin.login.label.language') ?></label>
			<div class="input-icon">
				<i class="fa fa-globe"></i>
				<?php echo Form::select('lang_cd_admin', $admin_support_lang,'', array('id'=>'lang_cd_admin','class'=>'form-control'))?>
			</div>
		</div>
		<?php }?>
		<div class="form-actions">
			<button type="button" id="loginButton" class="btn blue pull-right">
			<?php echo __('admin.login.title.login') ?>
			</button>
			<div class="text-divider" id="passkeyDivider" style="display: none;"><span style="color: white;">または</span></div>
			<button type="button" id="passkeyLoginButton" class="btn green" style="margin-right: 10px; background-color: white; color: #245BD6; display: none;">
				<i class="fa fa-key" style="margin-right: 5px;"></i> <?php echo __('admin.login.message.passkey_login') ?>
			</button>
			<span id="passkey-message" class="help-block"></span>
			<label class="checkbox">
			<input type="checkbox" name="remember" value="1"/> <?php echo __('admin.login.checkbox.keep_login') ?> </label>
			<label class="checkbox">
			<input type="checkbox" name="disaster" value="1"/> <?php echo __('admin.login.checkbox.disaster_login') ?> </label>
		</div>
		<div class="forget-password">
			<h4 style="font-size: 14px;"><?php echo __('admin.login.title.forgot_password_case') ?>
				<a href="javascript:void(0);" id="forget-password"><?php echo __('admin.login.title.reissue') ?></a>
			</h4>
			<h4 style="font-size: 14px;"><?php echo __('admin.login.title.change_password_case') ?>
				<a href="javascript:void(0);" id="modify-password"><?php echo __('admin.login.title.modify') ?></a>
			</h4>			
		</div>
	</form>
	<!-- END LOGIN FORM -->
	
	<!-- BEGIN FORGOT PASSWORD FORM -->
	<form class="forget-form" action="/login/forget" method="post">
		<?php if (($action=='login' || $action=='forget') && $email==null || $message!='') {?>
		<h3><?php echo __('admin.login.title.forgot_password') ?></h3>
		<p>
			<?php echo __('admin.login.title.forgot_password_before_sent') ?>
		</p>
		<?php if ($message !='') {?>
		<div class="alert alert-danger">
			<button class="close" data-close="alert"></button>
			<span>
			<?php echo $message?> </span>
		</div>
		<?php }?>
		<div class="form-group">
			<div class="input-icon">
				<i class="fa fa-envelope"></i>
				<input class="form-control placeholder-no-fix" type="text" autocomplete="off" placeholder="Email" name="email"/>
			</div>
		</div>
		<div class="form-actions">
			<button type="button" id="forgetButton" class="btn blue pull-right">
			<?php echo __('admin.login.title.reissue') ?>
			</button>
			<button type="button" class="btn back-login">
			<?php echo __('admin.login.title.back_to_login') ?> </button>
		</div>
		<?php } else { ?>
		<h3><?php echo __('admin.login.title.forgot_password') ?></h3>
		<p>
			 <?php echo $email ?><?php echo __('admin.login.title.forgot_password_after_sent') ?>
		</p>
		<div class="form-actions">
			<button type="button" id="back-btn" class="btn back-login">
			<?php echo __('admin.login.title.back_to_login') ?> </button>
		</div>		
		<?php }?>	
	</form>
	<!-- END FORGOT PASSWORD FORM -->
	
	<!-- BEGIN MODIFY PASSWORD FORM -->
	<form class="password-form" action="/login/modify" method="post" style="display:none;">
		<input type="hidden" name="facility_cd" value="<?php echo($facility_cd)?>" />
		<h3 class="form-title"><?php echo __('admin.login.title.change_password') ?></h3>

 		<?php 
 		if ($message == 'success') { 
 			$style = 'success';
 			$message = __('admin.login.message.changed_password.');
 		}
 		else {
 			$style = 'danger';
 		}
 		?>
		<div class="alert alert-<?php echo $style ?>" id="modify-message" <?php if ($message == '') echo('style="display:none;"')?>>
			<button class="close" data-close="alert"></button>
			<span>
			<?php echo $message?> </span>
		</div>

		<div class="form-group">
			<label class="control-label"><?php echo __('admin.login.label.login.id') ?></label>
			<div class="input-icon">
				<i class="fa fa-user"></i>
				<input class="form-control placeholder-no-fix" type="text" autocomplete="off" placeholder="<?php echo __('admin.login.placeholder.mail') ?>" name="email" value="<?php echo $email ?>"/>
			</div>
		</div>
		<div class="form-group">
			<label class="control-label"><?php echo __('admin.login.title.old_password') ?></label>
			<div class="input-icon">
				<i class="fa fa-lock"></i>
				<input class="form-control placeholder-no-fix" type="password" autocomplete="off" placeholder="<?php echo __('admin.login.placeholder.password') ?>" name="old_password"/>
			</div>
		</div>
		<div class="form-group">
			<label class="control-label"><?php echo __('admin.login.title.new_password') ?></label>
			<div class="input-icon">
				<i class="fa fa-lock"></i>
				<input class="form-control placeholder-no-fix" type="password" autocomplete="off" placeholder="<?php echo __('admin.login.placeholder.password') ?>" name="password1"/>
			</div>
		</div>		
		<div class="form-group">
			<label class="control-label"><?php echo __('admin.login.title.new_password_confirm') ?></label>
			<div class="input-icon">
				<i class="fa fa-lock"></i>
				<input class="form-control placeholder-no-fix" type="password" autocomplete="off" placeholder="<?php echo __('admin.login.placeholder.password') ?>" name="password2"/>
			</div>
		</div>		
		<div class="form-actions">
			<button type="button" id="modifyButton" class="btn blue pull-right">
			<?php echo __('admin.login.title.confirm') ?>
			</button>
			<button type="button" class="btn back-login">
			<?php echo __('admin.login.title.back_to_login') ?></button>		
		</div>
	</form>
	<!-- END MODIFY PASSWORD FORM -->
<?php } ?> 
</div>
<div id="page-action" style="display:none;"><?php echo $action ?></div>
<!-- END LOGIN -->
<!-- BEGIN COPYRIGHT -->
<div class="copyright">
	 TALKAPPI <?php echo __('admin.login.title.admin_site') ?>
	 2025 &copy; ActiValues, Inc.
</div>
<!-- END COPYRIGHT -->
<!-- BEGIN JAVASCRIPTS(Load javascripts at bottom, this will reduce page load time) -->
<!-- BEGIN CORE PLUGINS -->
<!--[if lt IE 9]>
<script src="<?php echo($_assets)?>global/plugins/respond.min.js"></script>
<script src="<?php echo($_assets)?>global/plugins/excanvas.min.js"></script>
<![endif]-->
<script src="<?php echo($_assets)?>global/plugins/jquery.min.js" type="text/javascript"></script>
<script src="<?php echo($_assets)?>global/plugins/jquery-migrate.min.js" type="text/javascript"></script>
<script src="<?php echo($_assets)?>global/plugins/bootstrap/js/bootstrap.min.js" type="text/javascript"></script>
<script src="<?php echo($_assets)?>global/plugins/jquery.blockui.min.js" type="text/javascript"></script>
<script src="<?php echo($_assets)?>global/plugins/uniform/jquery.uniform.min.js" type="text/javascript"></script>
<script src="<?php echo($_assets)?>global/plugins/jquery.cokie.min.js" type="text/javascript"></script>
<!-- END CORE PLUGINS -->
<!-- BEGIN PAGE LEVEL PLUGINS -->
<script src="<?php echo($_assets)?>global/plugins/jquery-validation/js/jquery.validate.min.js" type="text/javascript"></script>
<script src="<?php echo($_assets)?>global/plugins/backstretch/jquery.backstretch.min.js" type="text/javascript"></script>
<script type="text/javascript" src="<?php echo($_assets)?>global/plugins/select2/select2.min.js"></script>
<!-- END PAGE LEVEL PLUGINS -->
<!-- BEGIN PAGE LEVEL SCRIPTS -->
<script src="<?php echo($_assets)?>global/scripts/metronic.js" type="text/javascript"></script>
<script src="<?php echo($_assets)?>admin/layout/scripts/layout.js" type="text/javascript"></script>
<script src="<?php echo($_assets)?>admin/layout/scripts/demo.js" type="text/javascript"></script>
<script src="<?php echo($_assets)?>admin/pages/scripts/login-soft.js" type="text/javascript"></script>
<!-- END PAGE LEVEL SCRIPTS -->
<script>
jQuery(document).ready(function() {
  Metronic.init(); // init metronic core components
  Layout.init(); // init current layout
  Login.init();
  if ($('#page-action').html() == 'forget') {
	  $('.login-form').hide();
	  $('.password-form').hide();
	  $('.forget-form').show();
  }
  if ($('#page-action').html() == 'modify') {
	  $('.login-form').hide();
	  $('.forget-form').hide();
	  $('.password-form').show();
  }
  
  Demo.init();
  // init background slide images
  $.backstretch([
        "/assets/admin/pages/media/bg/loginpic_bg.png",
        ]);
  $("#modify-password").click(function() {
	  $('.alert-danger').hide();
	  $('.login-form').hide();
	  $('.forget-form').hide();
	  $('.password-form').show();
  });

  $('#forget-password').click(function () {
	  $('.alert-danger').hide();
      $('.login-form').hide();
      $('.password-form').hide();
      $('.forget-form').show();
  });
  
	$('.back-login').click(function(){
		window.location.href = '/login';
	});
	$('#loginButton').click(function(){
		$(".login-form").submit();
	});
	$('#forgetButton').click(function(){
		$(".forget-form").submit();
	});
	$('#modifyButton').click(function(){
		var reg = /^(?=.*[0-9])(?=.*[a-zA-Z])(?=.*[^a-zA-Z0-9]).{11,30}$/;
		if (!$("input[name='password1']").val().match(reg)) {
			$("#modify-message").show();
			$("#modify-message span").html('<?php echo __('admin.login.message.password_rule_1')?>');
			return;
		}
		if ($("input[name='password1']").val() == $("input[name='old_password']").val()) {
			$("#modify-message").show();
			$("#modify-message span").html('<?php echo __('admin.login.message.password_rule_2')?>');
			return;
		}
		if ($("input[name='password1']").val() != $("input[name='password2']").val()) {
			$("#modify-message").show();
			$("#modify-message span").html('<?php echo __('admin.login.message.password_rule_3')?>');
			return;
		}
		$(".password-form").submit();
	});
	
	// Passkey login button handler
	$('#passkeyLoginButton').click(function(){
		authenticateWithPasskey();
	});

	// Check passkey support using async IIFE
	(async function() {
		try {
			if (!window.PublicKeyCredential) {
				throw new Error('WebAuthnはこのブラウザでサポートされていません');
			}
			
			const available = await PublicKeyCredential.isUserVerifyingPlatformAuthenticatorAvailable();
			if (available) {
				// Show passkey login button and divider only if supported
				$('#passkeyLoginButton').show();
				$('#passkeyDivider').show();
			}
		} catch (error) {
			console.error('WebAuthnチェックに失敗しました:', error);
			// Keep button and divider hidden (default state)
		}
	})();
});

function showPasskeyMessage(message, isError = false) {
	const messageDiv = $('#passkey-message');
	messageDiv.hide();
	messageDiv.css('color', isError ? '#E53361' : '#245BD6');
	messageDiv.text(message).show();
}

// Base64URL utility functions
function base64URLToBuffer(base64URL) {
	const base64 = base64URL.replace(/-/g, '+').replace(/_/g, '/');
	const padLen = (4 - (base64.length % 4)) % 4;
	const padded = base64 + '='.repeat(padLen);
	const binary = atob(padded);
	const buffer = new Uint8Array(binary.length);
	for (let i = 0; i < binary.length; i++) {
		buffer[i] = binary.charCodeAt(i);
	}
	return buffer.buffer;
}

function bufferToBase64URL(buffer) {
	const bytes = new Uint8Array(buffer);
	let binary = '';
	for (let i = 0; i < bytes.byteLength; i++) {
		binary += String.fromCharCode(bytes[i]);
	}
	const base64 = btoa(binary);
	return base64.replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');
}

// WebAuthn error handler
function handleWebAuthnError(error) {
	if (error.name === 'NotAllowedError') {
		return '<?php echo __('admin.login.message.passkey_auth_canceled') ?>';
	} else if (error.name === 'SecurityError') {
		return '<?php echo __('admin.login.message.security_error') ?>';
	} else if (error.name === 'NotSupportedError') {
		return '<?php echo __('admin.login.message.browser_not_supported') ?>';
	} else if (error.name === 'passkey_auth_failed') {
		return '<?php echo __('admin.login.message.passkey_auth_failed') ?>';
	} else {
		return error.message || '<?php echo __('admin.login.message.passkey_login_failed') ?>';
	}
}

// Main passkey authentication function
async function authenticateWithPasskey() {
	try {
		// Hide previous passkey message
		$('#passkey-message').hide();
		// 1. Get authentication options (no email needed)
		const response = await fetch('/apinotification/passkey_auth_start', {
			method: 'GET'
		});

		if (!response.ok) {
			const errorData = await response.json();
			throw new Error(errorData.message || '<?php echo __('admin.login.message.passkey_auth_start_failed') ?>');
		}

		const options = await response.json();
		if (options.result == 'error') {
			throw new Error(options.message || '<?php echo __('admin.login.message.passkey_auth_start_failed') ?>');
		}
		// Extract challengeId for finish API
		const challengeId = options.challengeId;
		delete options.challengeId;

		// 2. Convert data format
		options.challenge = base64URLToBuffer(options.challenge);

		if (options.allowCredentials) {
			options.allowCredentials = options.allowCredentials.map(cred => ({
				...cred,
				id: base64URLToBuffer(cred.id)
			}));
		}

		// 3. Get credentials
		const credential = await navigator.credentials.get({
			publicKey: options,
			mediation: 'optional'
		});
		showPasskeyMessage('<?php echo __('admin.login.message.passkey_auth_starting') ?>', false);
		// 4. Convert response format
		const credentialForServer = {
			id: credential.id,
			rawId: bufferToBase64URL(credential.rawId),
			response: {
				authenticatorData: bufferToBase64URL(credential.response.authenticatorData),
				clientDataJSON: bufferToBase64URL(credential.response.clientDataJSON),
				signature: bufferToBase64URL(credential.response.signature),
				userHandle: credential.response.userHandle ? 
					bufferToBase64URL(credential.response.userHandle) : null
			},
			type: credential.type
		};

		// 5. Send to server for verification
		const verificationResponse = await fetch('/login', {
			method: 'POST',
			headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
			body: new URLSearchParams({
				passkey_credential: JSON.stringify(credentialForServer),
				passkey_challengeId: challengeId,
				facility_cd: $('input[name="facility_cd"]').val() || '',
				lang_cd_admin: $('#lang_cd_admin').val() || 'ja',
				redirect: $('input[name="redirect"]').val() || '',
				disaster: $('input[name="disaster"]').prop('checked') ? 1 : 0
			})
		});

		// Check if it's a JSON response (error) or HTML redirect (success)
		let redirectUrl = $('input[name="redirect"]').val() || '';
		const contentType = verificationResponse.headers.get('content-type');
		if (contentType && contentType.includes('application/json')) {
			const result = await verificationResponse.json();
			if (!result.ok) {
				const customError = new Error(result.message);
				customError.name = 'passkey_auth_failed';
				throw customError;
			}
			if (result.ok && result.redirect) {
				redirectUrl = result.redirect;
			}
		}
		// Redirect to the specified URL or default
		showPasskeyMessage('<?php echo __('admin.login.message.passkey_auth_success') ?>', false);
		if (redirectUrl) {
			window.location.href = decodeURIComponent(redirectUrl);
		} else {
			window.location.href = '/admin/top';
		}
		
	} catch (error) {
		console.error('Error:', error);
		let errorMessage = handleWebAuthnError(error);
		showPasskeyMessage(errorMessage, true);
	}
}
</script>
<!-- END JAVASCRIPTS -->
</body>
<!-- END BODY -->
</html>
