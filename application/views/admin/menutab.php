<nav class="top-nav">
	<ul>
	<?php 
	foreach($_tab_menu[substr($_active_menu, 0, 4)]['children'] as $menu) {
		if ($menu['name'] == '_') continue;
		// if ($_user->role_cd == '01' && $menu['word'] == 'admin/user') continue;
		if ($_user->role_cd != '99') {
			if ($menu['word'] != '') {
				$has_authority = false;
				foreach($_user_function as $f) {
					if (substr($f, -1, 1) == "*") {
						if (strpos($menu['word'], substr($f, 0, strlen($f) - 1)) === 0) {
							$has_authority = true;
							break;
						}
					}
					else {
						if ($menu['word']== $f) {
							$has_authority = true;
							break;
						}
					}
				}
				if ($has_authority == false) {
					continue;
				}
			}
		}
		if ($_active_menu == $menu['class_cd']) {
			echo('<li class="active">');
		}
		else {
			echo('<li class="">');
		}
		echo('<a class="func-menu" href="/' . $menu['word'] . '">');
		echo($menu['name']);
		echo('</a></li>');
	}									
	?>
	</ul>
</nav>
