<?php echo $menu ?>
<div class="content-container white border">
	<div class="form-body">		
		<div class="form-group">
			<label class="control-label col-md-1">期間</label>
			<div class="col-md-10 flex">
				<div class="talkappi-datepicker-range" data-date-format="yyyy-mm">
					<input name="start_date" style="border:0;" value="<?php echo (substr($start_date, 0, 7)) ?>"/><p>〜</p>
					<input name="end_date" style="border:0;" value="<?php echo (substr($end_date, 0, 7)) ?>"/>
				</div>	
				<?php echo $botcond ?>
				<div class="col-md-1">
					<button type="button" id="searchButton" class="btn yellow" disabled="disabled">
					<i class="fa fa-search mr10"></i>検索</button>
				</div>																		
			</div>								
		</div>								
	</div>							
	<table class="table table-striped table-bordered table-hover js-data-table">
		<thead>
			<tr>
				<th>対象月</th>
				<?php foreach($_codes['25'] as $k=>$v) {
					echo('<th>' . $v . '</th>');
				}?>
				<th>すべて</th>
			</tr>
		</thead>
		<tbody>
		<?php
		for($i=0; ;$i++) {
			$cur_start_day = date("Y-m-d",strtotime("+" . $i ." month",strtotime($start_date)));
			if ($cur_start_day > $end_date) break;
			$month = substr($cur_start_day, 0, 7);
		?>	
		<tr class="gradeX odd" role="row">
			<td class="sorting_1">
			<?php echo($month)?>
			</td>
			<?php 
			$total = 0;
			foreach($_codes['25'] as $k=>$v) {
				if (array_key_exists($month, $results) && 
						array_key_exists($k, $results[$month])) {
					echo('<td style="text-align:right">' . $results[$month][$k] . '</td>');
					if ($k == '01') {
						$total = $total + $results[$month][$k];
						if (array_key_exists('04', $results[$month])) {
							$total = $total + $results[$month]['04'];
						}
					}
					else if ($k == '02') {
						$total = $total + $results[$month][$k];
						if (array_key_exists('05', $results[$month])) {
							$total = $total + $results[$month]['05'];
						}
					}
					else {
						$total = $total + $results[$month][$k];
					}
				}
				else {
					echo('<td style="text-align:right">-</td>');
				}
			}?>
			<td style="text-align:right">
			<?php 
			if ($total > 0) {
				echo($total);
			}
			else {
				echo('-');
			}
			?>
			</td>																																
		</tr>
		<?php } ?>
		</tbody>
	</table>
</div>

