			<!-- BEGIN PAGE HEADER-->
			<!-- BEGIN PAGE HEAD -->
			<div class="page-head">
				<!-- BEGIN PAGE TITLE -->
				<div class="page-title">
					<h1>ボット申込一覧<small></small></h1>
				</div>
				<!-- END PAGE TITLE -->
			</div>
			<!-- END PAGE HEAD -->
			<!-- END PAGE HEADER-->
			<!-- BEGIN PAGE CONTENT-->
			<div class="row">
				<div class="col-md-12">
			        <!-- Page Content -->
			        <div id="page-wrapper">
			        <div class="portlet light">			        
						<div class="portlet box">
							<div class="portlet-body">
								<div class="form-body">		
									<div class="form-group">
										<label class="control-label col-md-1">申請日付</label>
										<div class="col-md-4">
											<input name="start_date" id="start_date" value="<?php echo($post['start_date'])?>" style="float:left;" class="form-control form-control-inline input-small date-picker" size="16" data-date-format="yyyy-mm-dd"  type="text"/>
											<input name="end_date" id="end_date" value="<?php echo($post['end_date'])?>" style="float:left; margin-left:10px;" class="form-control form-control-inline input-small date-picker" size="16" data-date-format="yyyy-mm-dd"  type="text"/>
										</div>										
										<label class="control-label col-md-2">ステータス</label>
										<div class="col-md-2">
											<?php echo Form::select('apply_status_cd', $apply_status, $post['apply_status_cd'], array('id'=>'lang_cd','class'=>'form-control'))?>
										</div>																			
										<div class="col-md-2">
											<button type="button" id="searchButton" class="btn blue">
											<i class="fa fa-search mr10"></i>検索</button>
										</div>
									</div>
								</div>						
							<table class="table table-striped table-bordered table-hover js-data-table">
							<thead>
							<tr>
								<th>
									 施設名
								</th>
								<th>
									 ステータス
								</th>
								<th style="width: 80px;">
									 申請日付
								</th>			
								<th>
									 会社名
								</th>																																		
							</tr>
							</thead>

							<tbody>
							<?php
								foreach ($results as $orm) {
							?>	
							<tr class="gradeX odd" role="row">
								<td class="sorting_1">
									<?php echo($orm->bot_name)?>
								</td>
								<td>
									<?php echo($apply_status[$orm->apply_status_cd])?>						
								</td>
								<td>
									<?php echo($orm->apply_time)?>						
								</td>
								<td>
									<?php echo($orm->company_name)?>						
								</td>																																								
							</tr>
							<?php } ?>
							</tbody>
							</table>
							</div>
						</div>
					</div>
			        </div>
			        <!-- /#page-wrapper -->					
				</div>
			</div>
			<!-- END PAGE CONTENT-->
