<link href="/assets/admin/pages/css/blog.css" rel="stylesheet"/>
<link rel="stylesheet" type="text/css" href="/assets/global/plugins/bootstrap-wysihtml5/bootstrap-wysihtml5.css"/>
<link rel="stylesheet" type="text/css" href="/assets/global/plugins/bootstrap-markdown/css/bootstrap-markdown.min.css">
<link rel="stylesheet" type="text/css" href="/assets/global/plugins/bootstrap-summernote/summernote.css">
<style>
.media-body {
	display:block;
}

</style>
<!-- BEGIN PAGE HEADER-->
<!-- B<PERSON>IN PAGE HEAD -->
<div class="page-head">
	<!-- BEGIN PAGE TITLE -->
	<div class="page-title">
		<h1><?php echo($_active_menu_name)?><small></small></h1>
	</div>
	<!-- <PERSON><PERSON> PAGE TITLE -->
</div>

<input type="hidden" name="notice_id" value=<?php echo $notice_id ?>>
<!-- END PAGE HEAD -->
<!-- E<PERSON> PAGE HEADER-->
<!-- BEGIN PAGE CONTENT-->
<div class="row">
	<div class="col-md-12">
        <!-- Page Content -->
        <div id="page-wrapper">
			<!-- <div class="portlet light"> -->
			<nav class="top-nav">
				<ul>
					<?php
					$translate_from_lang = [];
					$lang_arr = explode(',', $_bot->support_lang);
					foreach($lang_arr as $k=>$v) {
						//veryの対応言語(日本語、英語、韓国語、中国語簡体、中国語繁体、タイ語、イタリア語、スペイン語、フランス語)のみタブに表示
						if (!in_array($v, ["ja","en","kr","cn","tw","th","it","es","fr"])) continue;
						if ($v == $lang_edit) {
							echo('<li class=" active">');
						}
						else {
							$translate_from_lang[$v] = $_codes['02'][$v];
							echo('<li>');
						}
						echo('<a class="func-menu" href="' . $_action . '?id=' . $notice_id . '&lang=' . $v . '">' . $_codes['02'][$v] . '</a ></li>');
					}
					?>
				</ul>
			</nav>
			<div class="edit-container">
        		<div class="settings-container">
					<div class="portlet-body">
						<?php if (count($translate_from_lang) > 0) { ?>
						<div style="display:flex;justify-content: flex-end;margin-bottom:16px;">
							<div class="talkappi-pulldown js-tranlate-from-lang" data-name="translate_from_lang" data-value="" data-blank-text="<?php echo __('admin.inquirydesc.label.base_language') ?>" data-source='<?php echo(json_encode($translate_from_lang, JSON_UNESCAPED_UNICODE)) ?>'></div>
							<button type="button" class="btn-smaller btn-blue js-tranlate"><?php echo __('admin.inquirydesc.label.automatic_translate') ?></button>
						</div>
						<?php } ?>
						<div class="row">
							<div class="col-md-12 blog-page">
								<div class="row">
									<div class="col-md-12 article-block" style="padding-left: 30px;padding-right: 30px;">
										<h4 style="margin-top:0;"><?php echo $notice['title']?></h4>
										<div class="blog-tag-data">
											<div class="row">
												<div class="col-md-6">
													<ul class="list-inline blog-tags">
														<li>
															<i class="fa fa-tags"></i>
															<a href="#">
															<?php if (isset($_codes['40'][$notice['notice_tag_cd']])) echo $_codes['40'][$notice['notice_tag_cd']] ?> </a>
														</li>
													</ul>
												</div>
												<div class="col-md-6 blog-tag-data-inner">
													<ul class="list-inline">
														<li>
															<i class="fa fa-calendar"></i>
															<a href="#">
															<?php echo substr($notice['create_time'],0,10) ?> </a>
														</li>
														<li>
															<i class="fa fa-user"></i>
															<a href="#">
															<?php echo $notice['name'] ?> </a>
															<?php 
															echo('<span>  <a href="/admin/notice?id=' . $notice_id . '&lang=' . $lang_edit . '" style="color: #78cff8;">' .  __('admin.common.button.edit') . '</a></span>');
															?>								
														</li>
													</ul>
												</div>
											</div>
										</div>
										<hr>
										<!--end notice-tag-data-->
										<div>
											<p>
											<?php echo $notice['content']?>
											</p>
											<?php if ($notice['update_time']!=NULL) {?>
											<?php }?>						
										</div>
										<hr>
									
										<?php if ($notice['notice_type_cd'] != '01') { ?>
										<div class="post-comment">
												<button type="button" onclick="top.location='/admin/notices'" class="btn grey-steel"><?php echo __('admin.common.button.return_to_list') ?></button>
										</div>
										<?php }?>
									</div>
									<!--end col-md-12 article-block-->
								</div>
							</div>
						</div>
					</div>
				</div>
	</div>
	<!-- END PAGE CONTENT-->
</div>
</div>
</div>
