<style>
	.code-table.btn {
		margin: 0px 0px !important;
		padding: 0px 1px;
		background-color: transparent;
		float: right;
    }
	.sort-notice {
		padding-top: 5px;
		padding-bottom: 10px;
	}
	#code-sort-table {
		display: flex;
		align-items: flex-start;
		overflow: auto;
		white-space: nowrap;
	}

	#sortButton {
		margin: 5px 0px 10px 0px !important;
	}

	#sortButton.click-active {
		background-color: white;
		border: 1px solid #C8CACE;
		border-radius: 4px;
		color: #3D3F45;
	}

	.table-container {
		background-color: #F6F7F9;
		padding: 5px 10px 10px 10px;
		margin: 0px 3px;
	}

	.table-additional-container {
		background-color: white;
		border-bottom: 1px solid #E3E5E8;
		border-right: 1px solid #E3E5E8;
		border-left: 1px solid #E3E5E8;
		padding: 5px 0px;
	}
	.table-code-layer {
		background-color: white;
		margin-bottom: 0px;
	}
	.table-code-layer th{
		font-size: 12px !important;
		font-weight: 500 !important;
	}

	.add-class-cd {
		background: #EBEDF2;
		border-radius: 4px;
		border-color: transparent;
		margin: 0px 3px;
	}

	.table-container-title {
		font-weight: 600;
		width: auto;
		border-bottom: 1px solid #E3E5E8;
		margin-bottom: 5px;
		padding-bottom: 5px;
	}
	.table-list-sortable tr.select-active {
		background-color: rgba(211, 238, 255, 0.5);
	}
	.table-list-sortable tr:hover {
		background-color: rgba(211, 238, 255, 1);
	}
</style>
			<!-- BEGIN PAGE HEADER-->
			<!-- BEGIN PAGE HEAD -->
			<script type="text/javascript">
    			var layer_data = <?php echo $layer_data ?>;
			</script>
			<div class="page-head">
				<!-- BEGIN PAGE TITLE -->
				<div class="page-title">
					<h1><?php echo($_active_menu_name)?><small></small></h1>
				</div>
				<!-- END PAGE TITLE -->
			</div>
			<!-- END PAGE HEAD -->
			<!-- END PAGE HEADER-->
			<!-- BEGIN PAGE CONTENT-->
			        <!-- Page Content -->
			        <div id="page-wrapper">
						<div style="position: absolute; right: 250px; top: 15px;">
							<a href=<?php echo '"/admin/botclasscodeedit?id=' . $code_div . '"' ?> type="button" id="newButton" class="btn blue surveys-btn" style="background-color: #245BD6;">
								旧画面でCODE設定を編集する
							</a>
						</div>
						<p><a href="/admin/botclasscode" style="color: #000000;">CODE設定</a><p>
						<?php if (!$isEdit) { ?>
						<button type="button" id="sortButton" data-is-sort="false" class="btn blue">↑↓ 並べ替え/詳細</button>
						<?php } ?>
				        <div class="portlet light">
						<?php 
							if (strlen($_active_menu) > 4) {
								$tab_menu = View::factory ('admin/menutab');
								echo($tab_menu);
							}
						?>
					<div class="portlet box">
						<div class="portlet-body">
							<input type="hidden" name="class_cd_sel" id="class_cd_sel" value="<?php if ($post != NULL) echo($post['class_cd_sel'])?>" />
							<input type="hidden" name="code_div" id="code_div" value="<?php echo($code_div)?>" />
							<input type="hidden" id="isedit" value="<?php echo($isEdit)?>" />
							<input type="hidden" name="message" id="message" value="<?php echo($message)?>" />
							<?php if (!$isEdit) { ?>
							<div class="text-right">
								<button type="button" class="btn green-meadow action" act="00"><i class="fa fa-file mr10"></i>新規</button>
							</div>
							<p class="sort-notice"></p>
							<div id="code-table"></div>
							<div id="code-sort-table"></div>
							<?php } ?>
							<br/>
							<?php if ($isEdit) { ?>
							<h4 class="form-section"></h4>
								<div class="form-body">
									<div class="form-group">
										<label class="control-label col-md-2">表示順／<?php echo($div_title['class_cd']);?></label>
										<div class="col-md-1">
											<input name="sort" id="sort" type="text" class="form-control" value="<?php if ($post != NULL) echo($post['sort'])?>">
										</div>
										<div class="col-md-2">
											<input name="class_cd" id="class_cd" maxlength="8" type="text" class="form-control" value="<?php if ($post != NULL) echo($post['class_cd'])?>">
										</div>
									</div>
									<div class="form-group">
										<label class="control-label col-md-2"><?php echo($div_title['parent_cd']);?></label>
										<div class="col-md-2">
											<input name="parent_cd" id="parent_cd" type="text" maxlength="8" class="form-control" value="<?php if ($post != NULL) echo($post['parent_cd'])?>">
										</div>
									</div>
									<div class="form-group">
										<label class="control-label col-md-2">全件表示</label>
										<div class="col-md-2">
											<input name="show_items_flg" id="show_items_flg" type="text" maxlength="8" class="form-control" value="<?php if ($post != NULL) echo($post['show_items_flg'])?>">
										</div>
									</div>
									<div class="form-group">
										<label class="control-label col-md-2">ボット専用</label>
										<div class="col-md-2">
											<input name="bot_id" id="bot_id" type="text" maxlength="8" class="form-control" value="<?php if ($post != NULL) echo($post['bot_id'])?>">
										</div>
									</div>									
									<?php foreach($_bot_lang as $k=>$v) {?>											
									<div class="form-group">
										<label class="control-label col-md-2"><?php echo($v)?>／説明</label>
										<div class="col-md-3">
											<textarea name="name_<?php echo($k)?>" id="name_<?php echo($k)?>" class="form-control" maxlength="100"><?php if ($post != NULL) echo($post['name_' . $k])?></textarea>
										</div>
										<div class="col-md-6">
											<textarea name="word_<?php echo($k)?>" id="word_<?php echo($k)?>" class="form-control" maxlength="1000"><?php if ($post != NULL) echo(htmlspecialchars($post['word_' . $k]))?></textarea>
										</div>										
									</div>
									<?php }?>
									<div class="form-group">
										<label class="control-label col-md-2">アイコン写真</label>
										<div class="col-md-8">
											<div class="input-icon">
												<div class="talkappi-upload" data-name="grid_pic_url" data-type="img" data-upload-now=<?php echo '"classcode/'. $code_div . '"' ?> data-ratio="1:1" data-label="<?php if ($post != NULL) echo(htmlspecialchars($post['grid_pic_url']))?>" data-url="<?php if ($post != NULL) echo(htmlspecialchars($post['grid_pic_url']))?>" data-max-size="2"></div>
											</div>
										</div>
									</div>
								</div>
								<div class="form-actions">
									<div class="row">
										<div class="col-md-offset-2 col-md-9">
											<button type="button" id="saveBaseButton" class="btn blue mr10">保存</button>
											<a href=<?php echo '"/admin/botclasscodeeditnew?id=' . $code_div . '"' ?> type="button" style="border: 1px solid #C8CACE; border-radius: 4px; padding: 6px 10px 10px; color: #3D3F45;">戻る</a>
										</div>
									</div>
								</div>
								<?php } ?>
							</div>
							</div>
						</div>
			        </div>