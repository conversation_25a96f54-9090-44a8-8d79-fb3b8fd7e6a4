<!-- BEGIN PAGE CONTENT-->

<?php echo $menu ?>				
<div class="content-container white border">
	<div class="section-container">
	<input type="hidden" name="message" id="message" value="<?php echo ($message) ?>" />
	<input type="hidden" name="act" id="act" value="" />
	<input type="hidden" name="log_id" id="log_id" value="" />
	<input type="hidden" name="bot_id" id="bot_id" value="" />
	<div class="form-body">							
		<div class="form-group flex">
			<label class="control-label col-md-2" style="padding-left:15px; padding-right:15px;"><?php echo __('admin.common.label.sns'); ?>・<?php echo __('admin.common.label.lang'); ?></label>
			<div class="col-md-4" style="width: 270px;">
				<div class="btn-group" data-toggle="buttons">
					<?php 
						foreach($_codes['16'] as $channel_cd=>$channel_name) {
							if (!in_array($channel_cd, $bot_snses)) continue;
							if (in_array($channel_cd, $channels)) {
								echo('<label class="btn default active">');
								echo('<input name="channel_cd[]"  type="checkbox" checked="true" value="' . $channel_cd . '" class="toggle">' . $channel_name. '</label>');
							}
							else {
								echo('<label class="btn default">');
								echo('<input name="channel_cd[]" type="checkbox" value="' . $channel_cd . '" class="toggle">' . $channel_name . '</label>');
							}
						}
					?>	
				</div>
			</div>
			<div class="col-md-6">
				<div class="btn-group" data-toggle="buttons">
				<?php 
					foreach($_bot_lang as $lang_cd=>$lang_name) {
						if (!in_array($lang_cd, $lang_display)) continue;
						if (in_array($lang_cd, $langs)) {
							echo('<label class="btn default active">');
							echo('<input name="lang_cd[]" type="checkbox" checked="true" value="' . $lang_cd . '" class="toggle">' . $lang_name. '</label>');
						}
						else {
							echo('<label class="btn default">');
							echo('<input name="lang_cd[]" type="checkbox" value="' . $lang_cd . '" class="toggle">' . $lang_name. '</label>');
						}
					}
				?>	
				</div>
			</div>												
		</div>
		<div class="form-group flex" style="align-items: center;">
			<label class="control-label col-md-2"><?php echo __('admin.push.register'); ?></label>

			<div class="col-md-1 mr10"  style="width:130px" >
				<input name="regist_date_from" value="<?php echo ($regist_date_from) ?>" class="talkappi-datepicker" type="text"/>
			</div>
			<p>〜</p>
			<div class="col-md-1 mr10"  style="width:130px" >
				<input name="regist_date_to" value="<?php echo ($regist_date_to) ?>" class="talkappi-datepicker" type="text"/>
			</div>
		</div>

		<div class="form-group flex" style="align-items: center;">
			<label class="control-label col-md-2"><?php echo __('admin.push.last.talk'); ?></label>
			<div class="col-md-1 mr10" style="width: 130px;" >
				<input name="last_talk_time_from" value="<?php echo ($last_talk_time_from)?>" class="talkappi-datepicker" type="text"/>
			</div>
			<span>〜</span>
			<div class="col-md-1 mr10" style="width: 130px; margin-right:200px" >
				<input name="last_talk_time_to" value="<?php echo ($last_talk_time_to)?>" class="talkappi-datepicker" type="text"/>
			</div> 
		</div>
		<?php if ($parent_bot_id === 0) { ?>
		<div class="form-group flex" style="align-items: center;">
			<label class="control-label col-md-2"><?php echo __('admin.push.facility.cd'); ?></label>
			<?php echo $botcond ?>	
		</div>
		<?php } ?>
		<div class="form-group col-md-8">
			<button type="submit" class="btn-smaller btn-yellow js-search" style="margin-left: auto; margin-top: -42px;">
			<i class="fa fa-search mr10"></i><?php echo __('admin.common.button.search'); ?></button>
		</div>	
	</div>
	<table class="table table-striped table-bordered table-hover" id="sample_2">
	<thead>
	<tr>
		<th>
		<?php echo __('admin.push.segment'); ?>
		</th>
		<th>
		<?php echo __('admin.push.target.num'); ?>
		</th>
	</tr>
	</thead>
	<tbody>							
		<?php
		if ($attrs != NULL)
		foreach ($attrs as $attr) {
		?>	
		<tr class="gradeX odd" role="row">
			<td>							
				<?php 
					echo($attr['name']);
				?>
			</td>
			<td>
				<?php 
					echo($attr['attr_num']);
				?>
			</td>
		</tr>
		<?php } ?>
		</tbody>
	</table>
	<?php if ($attrs != NULL) echo($paging);?>
	</div>
</div>


