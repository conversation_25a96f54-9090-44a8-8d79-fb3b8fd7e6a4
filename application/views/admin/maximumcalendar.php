<link rel="stylesheet" type="text/css" href="<?php echo($_assets)?>global/plugins/fullcalendar-scheduler/lib/main.css"/>
<script src="<?php echo($_assets)?>global/plugins/fullcalendar-scheduler/lib/main.js" type="text/javascript"></script>
<script src="<?php echo($_assets)?>global/plugins/fullcalendar-scheduler/lib/locales-all.js" type="text/javascript"></script>
<link rel="stylesheet" type="text/css" href="/assets/admin/css/maximumcalendar.css"/>

<script>
  const js_calendar_array = <?php echo(json_encode($maximums)) ?>;
  const js_calendar_span = <?php echo($span) ?>;
  const style_as_page_maximum = <?php echo $style_as_page_maximum ? 'true' : 'false'; ?>;
  const _labelcolor = <?php echo json_encode($labelcolor) ?>;
  const _comment_required = <?= $comment_required ? 'true' : 'false' ?>;
</script>

<?php 
  $url = $_SERVER['REQUEST_URI'];
  if (strpos($url, "fullview=true")) {
?>
  <div class="full-view">
    <div id="calendar"></div>
    <?php 
      for ($i = 0; $i < count($maximums); $i++) {
    ?>
      <div class="section-container js-inquiry-results" id="inquiry-results<?php echo $i ?>" style="display: none;">
        <div>
          <div class="js-maximum-lists" style="margin: 0 0 20px 0;"></div>
          <div class="flex-x-between" style="align-items:center;margin-top:-15px;margin-bottom:10px;">
          </div>
          <table class="table table-striped table-bordered table-hover js-data-table<?php echo $i ?>">
            <thead>
                <tr>
                    <th style="min-width: 50px; width: 80px;"><?php echo __('inquiry.common.label.name') ?></th>
                    <th><?php echo __('admin.maximumcalendar.num') ?></th>
                    <th style="min-width: 50px;"><?php echo __('admin.maximumcalendar.price') ?></th>
                    <th style="min-width: 30px;"><?php echo __('admin.inquiryresult.label.response_status'); ?></th>
                    <th style="min-width: 50px;"><?php echo __('admin.inquiryresult.label.response_history'); ?></th>
                </tr>
            </thead>
            <tbody></tbody>
          </table>
        </div>
      </div>
    <?php } ?>
  </div>
<?php
  } else {
?>
  <div class="content-container white border">
    <div class="section-container bottom-line">
      <h2 style="margin-top:0;"><?php echo __('admin.common.label.setting') ?></h2>
      <div class="form-body">
        <div class="form-group">
          <label class="control-label col-md-3"><?php echo __('admin.maximumcalendar.calendar_name') ?></label>
          <div class="col-md-6">
          <div class="readonly-input flex-x-between">
              <span><?php echo $item['title'] ?></span>
              <a class="js-edit edit-cal-input-button" data-maximumcalendar-id=<?php echo ($item['id']) ?>  href="#"><?php echo __('admin.common.button.edit') ?></a>
          </div>
          </div>
        </div>
        <div class="form-group">
          <label class="control-label col-md-3"><?php echo __('admin.maximumcalendar.add_edit') ?></label>
          <div class="col-md-6">
            <div class="readonly-input flex-x-between">
                <span><?php echo $title ?></span>
                <a class="js-edit edit-cal-input-button" data-maximumcalendar-id=<?php echo ($item['id']) ?>  href="#"><?php echo __('admin.common.button.edit') ?></a>
            </div>
          </div>
        </div>
        <div class="form-group js-fullview">
          <label class="control-label col-md-3"><?php echo __('admin.maximumcalendar.large_view') ?></label>
          <div class="col-md-6">
            <a href="/<?php echo $_path?>/maximumcalendar?id=<?php echo ($item['id']) ?>&fullview=true" target="_blank" rel="noopener noreferrer" class="action-button section btn-blue" style="margin:0; width:fit-content; color: white;"><span class="calendar-icon"></span><?php echo __('admin.maximumcalendar.large_view') ?></a>
          </div>
        </div>
      </div>
    </div>
    <div class="section-container">
      <div id="calendar" class="js-calendar"></div>
    </div>
    <?php 
      for ($i = 0; $i < count($maximums); $i++) {
    ?>
      <div class="section-container js-inquiry-results" id="inquiry-results<?php echo $i ?>" style="display: none;">
        <div>
          <div class="js-maximum-lists" style="margin: 0 0 20px 0;"></div>
          <div class="flex-x-between" style="align-items:center;margin-top:-15px;margin-bottom:10px;">
          </div>
          <table class="table table-striped table-bordered table-hover js-data-table<?php echo $i ?>">
            <thead>
                <tr>
                    <th style="min-width: 50px; width: 80px;"><?php echo __('inquiry.common.label.name') ?></th>
                    <th><?php echo __('admin.maximumcalendar.num') ?></th>
                    <th style="min-width: 50px;"><?php echo __('admin.maximumcalendar.price') ?></th>
                    <th style="min-width: 30px;"><?php echo __('admin.inquiryresult.label.response_status'); ?></th>
                    <th style="min-width: 50px;"><?php echo __('admin.inquiryresult.label.response_history'); ?></th>
                </tr>
            </thead>
            <tbody></tbody>
          </table>
        </div>
      </div>
    <?php } ?>
    <div class="form-actions">
      <div class="row">
        <div class="col-md-offset-2 col-md-9">
          <div class="actions-container">
            <a href="/admin/maximumcalendars" class="action-button page btn-white"><?php echo __('admin.common.button.return_to_list') ?></a>
            <span class="action-button page btn-red-border icon-only js-action-delete"><span class="icon-delete"></span></span>
          </div>
        </div>
      </div>
    </div>
  </div>
<?php
  }
?>

<!-- template -->
<!-- モーダル背景 -->
<div class="modal-background js-modal-background" style="display: none;"></div>
<!-- template -->
<!-- 在庫数変更モーダル -->
<div class="modal-image-container js-modal-remains-container js-modal" style="display: none;height: 220px; min-height: 200px;">
	<form class="relative" style="min-height: 100%;">
		<!-- タイトル -->
		<div class="flexbox relative">
			<h4 class="font-standard font-size-v5 font-family-v2" style="margin: 0;">
    		<span class="js-max-modal-name"></span><?php echo __('admin.maximums.stock_change') ?></h4>
			<span class="js-close-modal icon-cancel-large survey-position-absolute-v2 pointer"></span>
		</div>
		<!-- 期限 -->
    <div class="js-remains-resource"></div>
		<div class="survey-space-top-2 flexbox-x-axis" style="margin-bottom:10px;">
			<span class="js-remains-date" style="padding-right:12px;"></span>
			<span class="js-remains-time" style="padding-right:60px;"></span>
			<div style="margin-right:12px;"><?php echo __('admin.maximums.number_of_stock') ?></div>
			<input type="number" required min="0" style="margin-right:60px;" class="survey-period-date-container js-remains-input" style="width:100px;padding:0 12px;" placeholder="">
      <div style="padding-right:12px;"><?php echo __('admin.maximums.stop_selling') ?></div>
			<div class="talkappi-switch js-selling-status-toggle" data-value="0"></div>
		</div>
    <!-- コメント -->
		<div class="js-comment-container flexbox-x-axis" style="margin-bottom:10px;">
			<div style="padding-right:12px;width:120px;">コメント<?php if ($comment_required) echo '(必須)' ?></div>
			<input type="text" class="form-control talkappi-textinput js-remains-comment" data-max-input="100" placeholder="" />
    </div>
		<!-- 変更履歴 -->
		<div class="table-scrollable" style="display:none;  width: 100%; overflow-x: auto; overflow-y: visible; border: 1px solid #dddddd; margin: 20px 0 !important; height: 172px;">
			<table id="table1members" class="table table-striped table-bordered table-hover js-data-table1 mail_address_table dataTable no-footer" role="grid" ready="true" style="width: 100%;">
				<thead>
					<tr role="row">
						<th style="width: 110px;" class="sorting_disabled" rowspan="1" colspan="1">
							<!-- txt_title_name_upload_label_count_float -->
							変更時刻
						</th>
						<th style="width: 120px;" class="sorting_disabled" rowspan="1" colspan="1">
							変更者
						</th>
						<th style="width: 100px;" class="sorting_disabled" rowspan="1" colspan="1">
							在庫数(変更後)
						</th>
						<th style="width: 100px;" class="sorting_disabled" rowspan="1" colspan="1">
							販売中止(変更後)
						</th>
            <th style="width: 100px;" class="sorting_disabled" rowspan="1" colspan="1">
							コメント
						</th>
					</tr>
				</thead>
				<tbody class="js_maximum_edit_history_table">
				</tbody>
			</table>
		</div>
    <!-- SUBMIT -->
    <div class="checkbox-label js-chk-force-update">
			<input type="checkbox" id="chk-force-update" value="1">
			<label for="chk-force-update" style="color:red;"><?php echo __('admin.maximums.force_update') ?></label>
		</div>
    <div class="flexbox" style="justify-content:space-between;align-items:baseline;padding-bottom:10px;">
			<div class="submit-btn-container">
				<button class="flexbox-center btn-smaller btn-blue js-save-remains" style="margin:0 12px 0 0;" type="submit">OK</button>
				<button class="flexbox-center btn-smaller btn-white js-close-modal" type="button"><?php echo __('admin.common.button.cancel') ?></button>
			</div>
			<div style="display:flex;flex-direction:column;align-items:flex-end;gap:5px;">
				<div class="js-display-history" style="display: none;">
					<div style="font-size: 10px;display: flex;">変更履歴を表示する
					<div class="talkappi-switch js-maximum-eidt-toggle" data-value="1" style="margin-left: 10px;">
						<label class="js-switch-select switch-label"><span class="switch-span off"></span></label></div>
					</div>
				</div>
				<div class="js-last-update" style="font-size: 10px;"></div>
			</div>
		</div>
  </form>
</div>

<!-- 複数在庫数変更モーダル -->
<div class="modal-image-container js-modal-all-days-maximum js-modal" style="display: none; height: 620px; padding-bottom:100px;">
	<form class="relative" style="min-height: 100%;">
		<!-- タイトル -->
		<div class="flexbox relative">
			<div>
				<h4 class="font-standard font-size-v5 font-family-v2" style="margin: 0;">
					<span class="js-max-modal-name"></span><?php echo __('admin.maximums.stock_change') ?>
				</h4>
				<p class="js-max-modal-resource"></p>
				<p class="js-max-modal-date"></p>
			</div>
			<span class="js-close-modal icon-cancel-large survey-position-absolute-v2 pointer"></span>
		</div>
		<!-- 期限 -->
		<div class="all-days-maximum-body" style="height:420px;overflow-y:auto;margin-bottom:20px;"></div>
    <div class="checkbox-label js-chk-force-update-all">
			<input type="checkbox" id="chk-force-update-all" value="1">
			<label for="chk-force-update-all" style="color:red;"><?php echo __('admin.maximums.force_update') ?></label>
		</div>
		<!-- SUBMIT -->
		<div class="submit-btn-container modal-image-button" style="bottom:-40px;">
			<button class="flexbox-center btn-smaller btn-blue js-save-all-day-remains" style="margin:0 12px 0 0;" type="submit">OK</button>
			<button class="flexbox-center btn-smaller btn-white js-close-modal" type="button"><?php echo __('admin.common.button.cancel') ?></button>
		</div>
  </form>
</div>
<?php echo($memo_box);?>

<script type="text/javascript">
    const bot_id = <?php echo json_encode($bot_id); ?>;
</script>