<style type="text/css">
.btn.default:active, .btn.default.active {
  background-image: none;
  background-color: #45b6af;
  color: #fff;
}
</style>
			<!-- BEGIN PAGE HEADER-->
			<!-- BEGIN PAGE HEAD -->
			<div class="page-head">
				<!-- BEGIN PAGE TITLE -->
				<div class="page-title">
					<h1><?php echo($_active_menu_name)?><small></small></h1>
				</div>
				<!-- END PAGE TITLE -->
			</div>
			<!-- END PAGE HEAD -->
			<!-- END PAGE HEADER-->
			<!-- BEGIN PAGE CONTENT-->
			        <!-- Page Content -->
			        <div id="page-wrapper">
                        <div class="portlet menu">
                            <h5>法人会員一覧</h5>
                            <button type="button" id="newButton" class="btn blue surveys-btn" onclick="top.location='/admin/corp'" style="background-color: #245BD6;">
                                <img src="./../assets/admin/css/img/icon-add-white.svg" width="12" height="12" class="surveys-details-icon-img">新規作成
                            </button>
                        </div>
				        <div class="portlet light">
							<div class="portlet box">
								<div class="portlet-body">
                                <table class="table table-striped table-bordered table-hover" id="sample_1">
                                    <thead>
                                    <tr>
                                        <th style="width:60px;">会員No</th>
                                        <th style="width:60px;">法人PW</th>
                                        <th style="width:80px;">会員名</th>
                                        <th style="width:80px;">有効期間</th>
                                        <th style="width:80px;">会社詳細情報</th>
                                        <th style="width:120px;">持ち物</th>
                                        <th style="width:60px;">追記情報</th>
                                        <th style="width:60px;">OB会員No</th>
                                        <th style="width:60px;">最終更新</th>
                                        <th style="width:60px;">操作</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <?php
                                    foreach($corps as $corp){
                                        echo('<tr class="odd gradeX">');
                                        echo('<td>' . $corp->corp_id . '</td>');
                                        echo('<td>' . $corp->corp_password . '</td>');
                                        echo('<td>' . '<a href="/admin/corp?id='. $corp->corp_id .'">' . $corp->corp_name . '</a>');
                                        if ($corp->is_available  === '0'){echo('<br><span class="badge badge-danger">受付不可</span></td>');}
                                        echo('<td>' . $corp->contract_date_start . '〜' . '<br>' . $corp->contract_date_end  .'</td>');
                                        echo('<td>');
                                        if( !empty($corp->postal_code) ){echo '〒';}
                                        echo( $corp->postal_code . '<br>' . $corp->address .'<br>');
                                        if( !empty($corp->tel) ){echo '電話番号: ';}
                                        echo( $corp->tel .'</td>');
                                        echo('<td>' . $corp->bring_list . '</td>');
                                        $extra_info_array = json_decode($corp->extra_info, true);
                                        if ($extra_info_array != null && count($extra_info_array) > 0) {
                                            echo('<td>');
                                            foreach($extra_info_array as $extra_info){
                                                echo($extra_info['info'] . '<br>');
                                            }
                                            echo('</td>');
                                        }
                                        else{
                                            echo('<td></td>');
                                        }
                                        echo('<td>' . $corp->ob_corp_id . '</td>');
                                        echo('<td>' .  substr($corp->upd_time,2,8) . '<br>' . substr($corp->upd_time,11,8)  . '<br><br>' . ORM::factory('user',$corp->upd_user)->name . '</td>');
                                        echo('<td><a href="/admin/corp?id='. $corp->corp_id .'"><span class="badge badge-success" style="margin: 4px;">編集</span></a>');
                                    }
                                    ?>
                                    </tbody>
							    </table>
							    </div>
							</div>
						</div>
			        </div>
			        <!-- /#page-wrapper -->
			<!-- END PAGE CONTENT-->


