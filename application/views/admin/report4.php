			<!-- B<PERSON>IN PAGE HEADER-->
			<!-- BEGIN PAGE HEAD -->
			<div class="page-head">
				<!-- BEGIN PAGE TITLE -->
				<div class="page-title">
					<h1>利用状況レポート<small></small></h1>
				</div>
				<!-- END PAGE TITLE -->
			</div>
			<!-- END PAGE HEAD -->
			<!-- END PAGE HEADER-->
			<!-- BEGIN PAGE CONTENT-->
			<div class="row">
				<div class="col-md-12">
			        <!-- Page Content -->
			        <div id="page-wrapper">
			        <div class="portlet chatbot light">			        
					<?php echo $reportmenu ?>
						<div class="portlet chatbot box">
							<div class="portlet-body">
								<div class="form-body">		
									<div class="form-group">
										<label class="control-label col-md-1" style="padding-left:15px; padding-right:15px;"><?php echo __('admin.common.label.period') ?></label>
										<div class="col-md-4">
											<input name="start_date" id="start_date" value="<?php echo($start_date)?>" style="float:left;" class="form-control form-control-inline input-small date-picker" size="16" data-date-format="yyyy-mm-dd"  type="text"/>
											<input name="end_date" id="end_date" value="<?php echo($end_date)?>" style="float:left; margin-left:10px;" class="form-control form-control-inline input-small date-picker" size="16" data-date-format="yyyy-mm-dd"  type="text"/>
										</div>
										<label class="control-label col-md-1"><?php echo __('admin.common.label.display_zero') ?></label>
										<div class="col-md-2">
											<input type="checkbox" id="utf-8" name="zero_flg" <?php if ($zero=='0') echo('checked')?> value="1" class="make-switch" data-on-color="success" data-off-color="warning">
										</div>											
									</div>
									<div class="form-group">
										<label class="control-label col-md-1" style="padding-left:15px; padding-right:15px;"><?php echo __('admin.common.label.user_flow') ?></label>
										<?php echo $botcond ?>	
										<div class="col-md-1">
											<button type="button" id="searchButton" class="btn yellow" disabled="disabled">
											<i class="fa fa-search mr10"></i><?php echo __('admin.common.button.search') ?></button>
										</div>											
									</div>									
									</div>

								</div>							

							<table class="table table-striped table-bordered table-hover" id="sample_3">
							<thead>
							<tr>
								<th style="width:200px;text-align:center">
									※[<?php echo __('admin.common.label.number_of_user_utterances') ?>] 
								</th>	
								<?php
								$display_lang = explode(',', $_bot->lang_cd);
								$sns_array = explode(',', $_bot->sns_cd);
								foreach($sns_array as $sns_type_cd) {
									echo('<th style="width:200px;text-align:center">');
									echo($_codes['08'][$sns_type_cd]);
									echo('</th>');
								}
								?>							
								<th style="width:200px;text-align:center">
									 <?php echo __('admin.common.label.total') . '(' . __('admin.common.label.ratio') .')' ?>
								</th>																											
							</tr>
							</thead>
							<?php
							$sns_mnt = array();
							$sns_mnt_all = array();
							$show_total = $total;
							if ($total == 0) $total = 1;
							foreach($display_lang as $k) {
								echo('<tr>');
								echo('<td style="text-align:center;font-weight:bold;">' . $lang_array[$k] . '</td>');
								$sum = 0;
								$sum_all = 0;
								foreach($sns_array as $sns_type_cd) {
									echo('<td style="text-align:center;">');
									if (array_key_exists($sns_type_cd . $k, $month_result_all)) {
										echo($month_result_all[$sns_type_cd . $k]);
										$sum_all = $sum_all+ $month_result_all[$sns_type_cd . $k];
										if (array_key_exists($sns_type_cd, $sns_mnt_all)) {
											$sns_mnt_all[$sns_type_cd] = $sns_mnt_all[$sns_type_cd] + $month_result_all[$sns_type_cd . $k];
										}
										else {
											$sns_mnt_all[$sns_type_cd] = $month_result_all[$sns_type_cd . $k];
										}
									}
									else {
										if ($zero == '0') echo($zero);
									}
									if (array_key_exists($sns_type_cd . $k, $month_result)) {
										echo(' [' . $month_result[$sns_type_cd . $k] . ']');
										$sum = $sum + $month_result[$sns_type_cd . $k];
										if (array_key_exists($sns_type_cd, $sns_mnt)) {
											$sns_mnt[$sns_type_cd] = $sns_mnt[$sns_type_cd] + $month_result[$sns_type_cd . $k];
										}
										else {
											$sns_mnt[$sns_type_cd] = $month_result[$sns_type_cd . $k];
										}
									}
									else {
										if ($zero == '0') echo('[' . $zero . ']');
									}
									echo('</td>');
								}
								if ($total_all == 0) {
									echo('<td style="text-align:center;font-weight:bold;">' . $sum_all . ' (' . round(0, 2) . '%)</td>');
								} else {
									echo('<td style="text-align:center;font-weight:bold;">' . $sum_all . ' (' . round($sum_all/$total_all * 100, 2) . '%)</td>');
								}
								echo('</tr>');
							}
							echo('<tr>');
							echo('<td></td>');
							foreach($sns_array as $sns_type_cd) {
								echo('<td style="text-align:center;font-weight:bold;">');
								if (array_key_exists($sns_type_cd, $sns_mnt_all)) {
									if ($total == 0) {
										echo($sns_mnt_all[$sns_type_cd] . ' (' . round(0, 2) . '%)');
									} else {
										echo($sns_mnt_all[$sns_type_cd] . ' (' . round($sns_mnt_all[$sns_type_cd] /$total_all * 100, 2) . '%)');
									}
								}
								echo('</td>');
							}
							echo('<td style="text-align:center;font-weight:bold;font-size:16px;">' . $total_all . ' [' . $total . ']' . '</td>');
							echo('</tr>');
							?>																
							</tbody>
							</table>
							
							<table class="table table-striped table-bordered table-hover" id="sample_2">
							<thead>
							<tr>
								<th style="width:70px;text-align:center" rowspan="2">
								<button type="button" id="csvButton" data-tableId="sample_2" data-title="チャンネル別会話数" class="btn green exportCsv"><?php echo __('admin.common.button.csv_export') ?></button>
								</th>								
							<?php 
							foreach($sns_array as $sns_type_cd) {
								echo('<th style="width:200px;text-align:center" colspan="' . count($display_lang) . '">');
								echo($_codes['08'][$sns_type_cd]);
								echo('</th>');
							}
							?>																													
							</tr>						
							</thead>							
							<tbody>	
							<tr>
							<?php 
							echo('<tr class="gradeX odd" role="row">');
							echo('<td>');
							echo('</td>');
							for($i=0; $i<count($sns_array); $i++) {
								foreach($display_lang as $k) {
									echo('<th style="width:40px;text-align:center">');
									echo($lang_array[$k]);
									echo('</th>');
							}
							}?>																																					
							</tr>	
							<?php 
							for($i=0; ;$i++) {
								$cur_day = date("Y-m-d",strtotime("+" . $i ." day",strtotime($start_date)));
								if ($cur_day > $end_date) break;
								echo('<tr class="gradeX odd" role="row">');
								echo('<td>');
								echo(substr($cur_day, 2));
								echo('</td>');
								foreach($sns_array as $sns_type_cd) {
									foreach($display_lang as $k) {
										echo('<td>');
										if (array_key_exists($cur_day . $sns_type_cd . $k, $day_result_all)) {
											echo($day_result_all[$cur_day . $sns_type_cd . $k]);
										}
										else {
											if ($zero == '0') echo($zero);
										}
										if (array_key_exists($cur_day . $sns_type_cd . $k, $day_result)) {
											echo(' [' . $day_result[$cur_day . $sns_type_cd . $k] .']');
										}
										else {
											if ($zero == '0') echo('[' . $zero . ']');
										}
										echo('</td>');
									}
								}
								echo('</tr>');
							}?>
							</tbody>
							</table>
								
							</div>
						</div>
					</div>
			        </div>
			        <!-- /#page-wrapper -->					
				</div>
			</div>
			<!-- END PAGE CONTENT-->
