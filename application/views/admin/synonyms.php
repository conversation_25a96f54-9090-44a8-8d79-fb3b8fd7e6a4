<style type="text/css">
	.synonym-span {
	  padding: 3px 14px;
	  width: fit-content;
	  width: -moz-fit-content;
	  margin: 3px 3px;
	}
	
	td.synonym-edit-delete a {
	  display: inline-block;
	}

  .col-md-6 {
    display:block;
  }
</style>

<input type="hidden" name="act" id="act" value="" />
<input type="hidden" name="bot_id" id="bot_id" value="<?php echo $post['bot_id']; ?>" />
<input type="hidden" name="lang_cd" id="lang_cd" value="" />
<input type="hidden" name="example" id="example" value="" />
<input type="hidden" id="message" name="message" value='<?php echo $message?>' />
<div id="page-wrapper"> 
  <div class="menu" style="margin-bottom:15px;">
    <h5>類義語一覧</h5>
  </div>
  <div class="form-group flexbox-x-axis"  style="margin-left: -10px; position:relative">
    <!-- スーパー管理者 -->
    <?php if ($_user->role_cd=='99'):?>
      <div style="margin-left:12px;">
        <div>
            <?php echo Form::select('bot_select', $bots, $post['bot_select'], array('id'=>'bot_select','class'=>'form-control', 'style'=>'width:192px;'))?>
        </div>
      </div>
    <?php endif; ?> 
    <div style="margin-left:12px;">
      <?php echo Form::select("lang_cd", $lang_cd , $post["lang_cd"], array('id'=>'lang_cd','class'=>'form-control', 'style' => 'width: 192px;') )?>
    </div>
    <button type="button" id="newButton" class="btn blue" onclick="top.location='/admin/synonym'" style="background: #245BD6; position:absolute; right:134px;">
      <img src="./../assets/admin/css/img/icon-add-white.svg" width="12" height="12" class="surveys-details-icon-img">
      新規
    </button>  
    <button type="button" id="indexButton" class="btn blue" style="background: #245BD6; position:absolute; right:15px;">
      登録内容を更新
    </button>    
  </div>
  <div class="portlet light">
    <div class="portlet box">
      <div class="portlet-body">
        <table class="table table-bordered" id="sample_4">
          <thead style="background-color: #F6F7F9;">
            <tr>
              <th style="width:96px;">ボットID</th>
              <th style="width:84px;">言語</th>
              <th style="width:108px;">グループ名</th>
              <th style="width:515px;">類義語</th>
              <th style="width:100px;">最終更新</th>
              <th style="width:180px;">操作</th>
            </tr>
          </thead>
          <tbody>
            <?php foreach ($data as $item){ ?>
            <tr class="gradeX odd synonym-table" role="row">
              <td style="vertical-align: middle;"><?php echo $item["bot_id"]?></td>
              <td style="vertical-align: middle;"><?php echo ($_codes['02'][$item['lang_cd']])?></td>
              <td style="vertical-align: middle;"><?php echo $item["example"]?></td>
              <td style="vertical-align: middle;">
                <?php $synonymItems = trim($item["synonym"]); ?>
                <?php $arraySynonyms = preg_split('/[\s]+/', $synonymItems); ?>
                <?php foreach($arraySynonyms as $arraySynonym) { ?>
                  <span class="icon-round-corners-small icon-background-pale-blue font-standard font-family-v1 synonym-span">
                    <?php echo $arraySynonym ?>
                  </span>
                <?php } ?>
              </td>
              <td style="vertical-align: middle;">
                <?php echo $item["upd_user_name"]; ?><br>
                <?php echo date('Y/m/d', strtotime($item["upd_time"])) ?><br>
                <?php echo date('G:i', strtotime($item["upd_time"])) ?>
              </td>
              <td class="synonym-edit-delete" style="vertical-align: middle;">
                <a href="/admin/synonym?id=<?php echo ($item['bot_id'])?>&lang_cd=<?php echo ($item['lang_cd'])?>&example=<?php echo ($item['example'])?>" class="icon-round-corners-small icon-background-pale-blue survey-space-all-around-2 flexbox-x-axis" >
                    <span class="font-standard font-family-v1">
                        <img src="./../assets/admin/css/img/icon-edit.svg" width="14" height="14" style="padding-right: 3px;">編集
                    </span>
                </a>
                <a href="javascript:void(0);" bot_id="<?php echo $item['bot_id'] ?>" lang_cd="<?php echo $item['lang_cd'] ?>" example="<?php echo $item['example'] ?>" class="js-delete-icon icon-round-corners-small icon-background-pale-blue survey-space-all-around-2 flexbox-x-axis">
                    <span class="font-standard font-family-v1">
                    <img src="./../assets/admin/css/img/icon-delete.svg" width="14" height="14" style="padding-right: 3px;">削除
                    </span>
                </a>
              </td>
              </tr>
              <?php } ?>
          </tbody>
        </table>   
      </div>
    </div>
  </div>
</div>