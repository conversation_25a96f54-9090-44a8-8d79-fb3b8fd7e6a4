<!-- <PERSON><PERSON><PERSON> PAGE HEADER-->
<!-- BEGIN PAGE HEAD -->

<style>
	
	.container_frame_1222 {
		/* Frame 1222 */


		/* Auto layout */

		display: flex;
		flex-direction: row;
		align-items: flex-start;
		padding: 0px;
		gap: 12px;

		/* position: absolute; */
		/* width: 1116px; */
		/* width: 516px; */

		width: 100%;
		/* height: 774px; */
		/* height: 865px; */
		/* height: 889px; */
		/* left: 300px; */
		/* top: 102px; */
	}

	.user_info_left {
		/* user info */


		/* Auto layout */

		display: flex;
		flex-direction: column;
		align-items: flex-end;
		padding: 0px;
		gap: 12px;

		width: 696px;
		/* height: 774px; */
		/* height: 865px; */

		/* Inside auto layout */

		flex: none;
		order: 0;
		flex-grow: 0;
	}



	.memberinfo_flex {
		/* user info */


		box-sizing: border-box;

		/* Auto layout */

		display: flex;
		flex-direction: row;
		align-items: center;
		padding: 17px 36px 20px;
		gap: 36px;

		width: 696px;
		/* height: 210px; */

		/* pure white */

		background: #FFFFFF;
		/* pale grey for line */

		border: 1px solid #E3E5E8;
		border-radius: 4px;

		/* Inside auto layout */

		flex: none;
		order: 0;
		flex-grow: 0;
	}



	.profile_frame_1188 {
		/* Frame 1188 */
		/* Auto layout */
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 0px;
		gap: 12px;

		width: 120px;
		height: 166px;
		/* Inside auto layout */
		flex: none;
		order: 0;
		flex-grow: 0;
	}
	.profile_img_group_863 {
		/* Group 863 */
		/* position: absolute; */

		width: 120px;
		height: 120px;


		/* Inside auto layout */

		flex: none;
		order: 0;
		flex-grow: 0;
	}

	.profile_img {
		position: absolute;
		width: 120px;
		height: 120px;
		/* left: 0px;
		top: 0px; */

		/* background: url(.jpg); */
		background: center / contain no-repeat url(<?php echo ($member_avatar); ?>);
		border-radius: 60px;

		/* Inside auto layout */

		flex: none;
		order: 0;
		flex-grow: 0;
		/* margin: -24px 0px; */
		margin: 0px 0px;
	}

	.mobile_channel_icon {
		/* Mobile/Icon/channel_line Copy 6 */
		position: absolute;

		width: 24px;
		height: 24px;
		/* Inside auto layout */
		flex: none;
		order: 1;
		flex-grow: 0;

		/* 120px - 24px */
		margin: 96px 96px;
	}

	.ip_frame_965 {
		/* Frame 965 */


		/* Auto layout */

		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 0px;

		/* width: 64px; */
		width: 74px;

		height: 34px;

		order: 1;
	}
	
	.ip_label_flex {
		/* width: 54px; */
		width: 55px;
		height: 17px;
		font-family: 'Hiragino Sans';
		font-style: normal;
		font-weight: 400;
		font-size: 11px;
		line-height: 16px;
		color: #A1A4AA;
		/* Inside auto layout */
		flex: none;
		order: 0;
		flex-grow: 0;
	}
	
	.ip_addr_flex {
		/* width: 64px; */
		width: 74px;

		height: 17px;
		font-family: 'Hiragino Sans';
		font-style: normal;
		font-weight: 400;
		font-size: 11px;
		line-height: 16px;
		color: #3D3F45;
		/* Inside auto layout */
		flex: none;
		order: 1;
		flex-grow: 0;
	}

	.profile_frame_964 {
		/* Frame 964 */
		/* Auto layout */
		display: flex;
		flex-direction: column;
		align-items: flex-start;
		padding: 0px;
		gap: 12px;

		width: 468px;
		/* height: 173px; */
		/* height: 197px; */
		/* Inside auto layout */
		flex: none;
		order: 1;
		flex-grow: 1;
	}
	.profile_float_panel {
		/* ユーザー属性タグ */
		/* Auto layout */
		display: flex;
		flex-direction: column;
		align-items: flex-start;
		padding: 24px 36px 36px;

		position: relative;
		width: 660px;
		/* height: 253px; */

		background: #FFFFFF;
		border: 1px solid #E3E5E8;
		border-radius: 4px;
	}
	.profile_float_cancel_frame_62 {
		/* Frame 52 */
		/* Auto layout */
		display: flex;
		flex-direction: row;
		justify-content: flex-end;
		align-items: center;
		padding: 0px;

		width: 588px;
		height: 24px;
		/* Inside auto layout */
		flex: none;
		order: 0;
		align-self: stretch;
		flex-grow: 0;
		margin: -12px 0px;
	}
	.profile_float_frame_1236 {
		/* Frame 1236 */
		/* Auto layout */
		display: flex;
		flex-direction: column;
		align-items: flex-start;
		padding: 0px;
		gap: 12px;

		width: 588px;
		/* height: 181px; */
		/* Inside auto layout */
		flex: none;
		order: 1;
		align-self: stretch;
		flex-grow: 0;
	}
	.name_id_push_frame_1243 {
		/* Frame 1243 */
		/* Auto layout */
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: flex-start;
		padding: 0px;
		gap: 12px;

		width: 468px;
		/* height: 71px; */
		/* Inside auto layout */
		flex: none;
		order: 0;
		align-self: stretch;
		flex-grow: 0;
	}
	.name_id_labels_frame_963 {
		/* Frame 963 */
		/* Auto layout */
		display: flex;
		flex-direction: column;
		align-items: flex-start;
		padding: 0px;
		gap: 4px;

		width: 468px;
		/* height: 95px; */
		/* height: 119px; */

		/* Inside auto layout */
		flex: none;
		order: 0;
		align-self: stretch;
		flex-grow: 0;
	}
	.name_id_frame_963 {
		/* Frame 963 */
		/* Auto layout */
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: flex-start;
		padding: 0px;
		gap: 4px;
		/* Height Hug (71px) */
		/* width: 468px; */
		width: 344px;
		/* Inside auto layout */
		flex: none;
		order: 0;
		flex-grow: 0;
	}
	.memeber_label_rows_frame_1242{
		/* contains rows memeber_labels_frame_960 */
		/* Frame 1242 */
		/* Auto layout */
		display: flex;
		flex-direction: column;
		align-items: flex-start;
		padding: 0px;
		gap: 4px;

		width: 433px;
		/* height: 44px; */
		/* Inside auto layout */
		flex: none;
		order: 1;
		flex-grow: 0;
	}
	.push_msg_button_with_icon {
		/* button with icon */
		/* Auto layout */
		cursor: pointer;
		/* position: absolute; */
		/* align: right; */

		display: flex;
		flex-direction: row;
		align-items: center;
		padding: 5px 12px 5px 10px;
		gap: 6px;
		width: 112px;
		height: 28px;
		/* talkappi blue */
		background: #245BD6;
		border-radius: 4px;
		/* Inside auto layout */
		flex: none;
		order: 1;
		flex-grow: 0;
	}
	.push_msg_btn_icon {
		/* Icon */
		width: 12px;
		height: 12px;
		/* Inside auto layout */
		flex: none;
		order: 0;
		flex-grow: 0;
		/* pure white */
		/* color: #FFFFFF; */
		/* border: 2px solid #FFFFFF; */
	}
	.push_msg_btn_txt {
		/* 追加 */
		width: 72px;
		height: 18px;

		font-family: 'Hiragino Sans';
		font-style: normal;
		font-weight: 500;
		font-size: 12px;
		line-height: 18px;
		/* identical to box height */
		text-align: center;
		/* pure white */
		color: #FFFFFF;
		/* Inside auto layout */
		flex: none;
		order: 1;
		flex-grow: 0;
	}
	.name_id_location_time_device_frame_1237 {
		/* Frame 1237 */
		/* Auto layout */
		display: flex;
		flex-direction: column;
		align-items: flex-start;
		padding: 0px;
		gap: 6px;

		width: 588px;
		height: 117px;
		/* Inside auto layout */
		flex: none;
		order: 0;
		align-self: stretch;
		flex-grow: 0;
	}
	
	.namelabel_flex{
		/* お名前 */
		height: 18px;
		font-family: 'Hiragino Sans';
		font-style: normal;
		font-weight: 400;
		font-size: 12px;
		line-height: 18px;
		/* identical to box height */
		color: #3D3F45;
		/* Inside auto layout */
		flex: none;
		order: 0;
		flex-grow: 0;
	}
	
	.member_name_frame_961 {
		/* Frame 961 */
		/* Auto layout */
		display: flex;
		flex-direction: row;
		align-items: flex-start;
		padding: 0px;
		gap: 6px;

		width: 237px;
		height: 27px;
		/* Inside auto layout */
		flex: none;
		order: 1;
		flex-grow: 0;
	}
	.member_name_float_frame {
		/* お名前 */
		/* width: 231px; */
		height: 21px;
		font-family: 'Hiragino Sans';
		font-style: normal;
		font-weight: 400;
		font-size: 14px;
		line-height: 21px;
		/* identical to box height */
		color: #3D3F45;
		/* Inside auto layout */
		flex: none;
		order: 0;
		flex-grow: 0;
	}
	.member_name_flex {
		width: 225px;
		height: 27px;
		font-family: 'Hiragino Sans';
		font-style: normal;
		font-weight: 500;
		font-size: 18px;
		line-height: 27px;
		/* identical to box height */
		color: #000000;
		/* Inside auto layout */
		flex: none;
		order: 0;
		flex-grow: 0;
	}
	.member_name_oval {
		/* Oval */
		width: 6px;
		height: 6px;

		background: #32CE55;
		/* Inside auto layout */
		flex: none;
		order: 1;
		flex-grow: 0;
	}
	
	.member_id_flex {
		/* メンバーID：Ud07922700b5f */
		width: 468px;
		height: 18px;

		font-family: 'Hiragino Sans';
		font-style: normal;
		font-weight: 300;
		font-size: 12px;
		line-height: 18px;
		/* identical to box height */
		color: #3D3F45;
		/* Inside auto layout */
		flex: none;
		order: 2;
		align-self: stretch;
		flex-grow: 0;
	}
	.memeber_labels_frame_960 {
		/* Frame 960 */
		/* Auto layout */
		display: flex;
		flex-direction: row;
		align-items: flex-start;
		padding: 0px;
		gap: 10px;
		/* width: 349px; */
		height: 20px;
		/* Inside auto layout */
		flex: none;
		/* order: 3; */
		order: 0;
		flex-grow: 0;
	}
	.color_tag_flex_0 {
		/* text */


		/* Auto layout */

		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
		padding: 1px 7px;

		/* width: 50px; */
		height: 20px;

		/* light blue */

		background: #D3EEFF;
		border-radius: 2px;

		/* Inside auto layout */

		flex: none;
		order: 0;
		flex-grow: 0;
	}
	
	.tag_text_flex {
		/* width: 36px; */
		height: 18px;

		font-family: 'Hiragino Sans';
		font-style: normal;
		font-weight: 300;
		font-size: 12px;
		line-height: 18px;
		color: #000000;
		flex: none;
		order: 0;
		flex-grow: 0;
	}
	

	.color_tag_flex {
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
		padding: 1px 7px;
		height: 20px;
		/* light green */
		background: var(--color);
		border-radius: 2px;
		/* Inside auto layout */
		flex: none;
		order: var(--order);
		flex-grow: 0;
	}


	.location_time_device_frame_962 {
		/* Frame 962 */
		/* Auto layout */
		display: flex;
		flex-direction: column;
		align-items: flex-start;
		padding: 0px;
		gap: 6px;

		width: 468px;
		/* height: 66px; */
		/* Inside auto layout */
		flex: none;
		order: 1;
		align-self: stretch;
		flex-grow: 0;
	}
	.location_time_frame_1238_1240 {
		/* Frame 1238 */
		/* Auto layout */
		display: flex;
		flex-direction: row;
		align-items: flex-start;
		padding: 0px;
		gap: 6px;

		width: 468px;
		height: 18px;
		/* Inside auto layout */
		flex: none;
		order: 0;
		align-self: stretch;
		flex-grow: 0;
	}
	.location_frame_957 {
		/* Frame 957 */


		/* Auto layout */

		display: flex;
		flex-direction: row;
		align-items: center;
		padding: 0px;
		gap: 8px;

		/* width: 468px; */
		width: 231px;
		height: 18px;

		/* Inside auto layout */

		flex: none;
		order: 0;
		align-self: stretch;
		flex-grow: 0;
	}
	.location_icon {
		/* Icon/mobile Copy 2 */


		width: 12px;
		height: 12px;


		/* Inside auto layout */

		flex: none;
		order: 0;
		flex-grow: 0;
	}
	.location_icon_oval_dot {
		/* Oval */


		position: absolute;
		left: 41.67%;
		right: 41.67%;
		top: 33.33%;
		bottom: 50%;

		background: #3D3F45;

		
	}
	.location_icon_oval_edge {
		/* Oval */


		position: absolute;
		left: 16.67%;
		right: 16.67%;
		top: 8.33%;
		bottom: 8.33%;

		border: 1px solid #3D3F45;
	}
	.location_text {
		/* 日本｜東京都｜UTC+09:00 */


		/* width: 153px; */
		/* width: 173px; */

		height: 18px;

		font-family: 'Hiragino Sans';
		font-style: normal;
		font-weight: 300;
		font-size: 12px;
		line-height: 18px;
		/* identical to box height */


		color: #3D3F45;


		/* Inside auto layout */

		flex: none;
		order: 1;
		flex-grow: 0;
	}

	.time_frame_958 {
		/* Frame 958 */


		/* Auto layout */

		display: flex;
		flex-direction: row;
		align-items: center;
		padding: 0px;
		gap: 8px;

		/* width: 468px; */
		width: 231px;
		height: 18px;

		/* Inside auto layout */

		flex: none;
		order: 1;
		align-self: stretch;
		flex-grow: 0;
	}
	.time_icon {
		/* Icon/mobile Copy */


		width: 12px;
		height: 12px;


		/* Inside auto layout */

		flex: none;
		order: 0;
		flex-grow: 0;
	}
	.time_text {
		/* 2021/10/01 21:20 */


		/* width: 121px; */
		/* width: 131px; */

		height: 18px;

		font-family: 'Hiragino Sans';
		font-style: normal;
		font-weight: 300;
		font-size: 12px;
		line-height: 18px;
		/* identical to box height */


		color: #3D3F45;


		/* Inside auto layout */

		flex: none;
		order: 1;
		flex-grow: 0;
	}
	.device_mail_frame_1239_959 {
		/* Frame 959 1239 */
		/* Auto layout */
		display: flex;
		flex-direction: row;
		align-items: center;
		padding: 0px;
		gap: 8px;
		/* width: 468px; */
		width: 231px;
		height: 18px;
		/* Inside auto layout */
		flex: none;
		order: 0;
		/* order: 2; */
		/* align-self: stretch; */
		/* flex-grow: 0; */
		flex-grow: 1;
	}
	.device_icon {
		/* Icon-preview-mobile */


		width: 12px;
		height: 12px;


		/* Inside auto layout */

		flex: none;
		order: 0;
		flex-grow: 0;
	}
	.device_client_mail {
		/* iPhone X | LINE */
		/* width: 93px;		 */
		height: 18px;

		font-family: 'Hiragino Sans';
		font-style: normal;
		font-weight: 300;
		font-size: 12px;
		line-height: 18px;
		/* identical to box height */
		color: #3D3F45;
		/* Inside auto layout */
		flex: none;
		order: 1;
		/* flex-grow: 0; */
		flex-grow: 1;
	}

	/* user activity summary */
	.user_activity_summary {
		/* user dashboard */


		box-sizing: border-box;

		/* Auto layout */

		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 12px 0px;

		width: 695px;
		/* height: 552px; */
		/* height: 643px; */

		/* pure white */

		background: #FFFFFF;
		/* pale grey for line */

		border: 1px solid #E3E5E8;
		border-radius: 4px;

		/* Inside auto layout */

		flex: none;
		order: 1;
		flex-grow: 0;
	}
	.summary_tabs_frame_966 {
		/* Frame 966 */


		/* Auto layout */
		/* display: inline-block; */
		display: flex;
		flex-direction: row;
		align-items: flex-start;
		padding: 0px 1px;

		/* width: 695px; */
		width: 694px;
		height: 30px;


		/* Inside auto layout */

		flex: none;
		order: 0;
		align-self: stretch;
		flex-grow: 0;
	}
	.summary_tab_label_flex {
		/* Label tab */
		/* Auto layout */
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: flex-start;
		padding: 6px 12px;
		/* width: 173.25px; */
		width: 173px;
		height: 30px;
		/* pure white */
		background: #FFFFFF;
		/* Inside auto layout */
		flex: none;
		order: 0;
		flex-grow: 1;
		font-family: 'Hiragino Sans';
		font-style: normal;
		font-weight: 400;
		color: #3D3F45;
		font-size: 12px;
		line-height: 18px;
		/* identical to box height */
		text-align: center;
		box-shadow: inset 0px -1px 0px #E3E5E8;
		cursor: pointer;
	}
	.summary_tab_label_flex.active{
		box-shadow: inset 0px -1px 0px #245BD6;
		color: #245BD6;
	}
	.tab_label_text_flex {
		/* アップロード */
		/* width: 113px; */
		height: 18px;
	}
	
	.summary_contents_frame_1203 {
		/* Frame 1203 */


		/* Auto layout */

		display: flex;
		flex-direction: column;
		align-items: flex-start;
		padding: 24px 24px 12px;
		gap: 10px;

		width: 695px;
		/* height: 498px; */
		/* height: 589px; */
		height: 689px;


		/* Inside auto layout */

		flex: none;
		order: 1;
		align-self: stretch;
		flex-grow: 0;
	}
	.summary_top_banner_frame_1191 {
		/* usage_since_frequency_frame_1191 */
		/* Frame 1191 */


		/* Auto layout */

		display: flex;
		flex-direction: row;
		align-items: center;
		padding: 8px 24px;
		gap: 48px;

		width: 647px;
		height: 93px;

		/* grey white */

		background: #F6F7F9;
		border-radius: 4px;

		/* Inside auto layout */

		flex: none;
		order: 0;
		align-self: stretch;
		flex-grow: 0;
	}
	.middle_row_frame_1200 {
		/* utterance_couple_frame_1200 */
		/* Frame 1200 */
		/* Auto layout */
		display: flex;
		flex-direction: row;
		/* align-items: flex-start; */
		align-items: center;

		padding: 0px;
		gap: 10px;
		width: 647px;
		height: 81px;
		/* Inside auto layout */
		flex: none;
		order: 1;
		align-self: stretch;
		flex-grow: 0;
	}
	.middle_row_logs_frame_1195_1197 {
		/* Frame 1197 */
		/* Auto layout */
		display: flex;
		flex-direction: column;
		align-items: flex-start;
		padding: 8px 24px 12px;
		gap: 12px;

		width: 647px;
		height: 179px;
		/* grey white */
		background: #F6F7F9;
		border-radius: 4px;
		/* Inside auto layout */
		flex: none;
		order: 0;
		flex-grow: 1;
		/* Frame 1195 
		align-self: stretch;
		flex-grow: 0;
		*/
	}
	.middle_row_logs_wrapper_frame_1229 {
		/* Frame 1229 */
		/* Auto layout */
		display: flex;
		flex-direction: column;
		align-items: flex-start;
		padding: 0px;
		gap: 10px;

		width: 394px;
		height: 130px;
		/* Inside auto layout */
		flex: none;
		order: 1;
		flex-grow: 0;
	}
	.middle_row_log_text {
		/* ・満足度調査 */
		/* width: 72px; */
		height: 18px;

		font-family: 'Hiragino Sans';
		font-style: normal;
		font-weight: 400;
		font-size: 12px;
		line-height: 18px;
		/* identical to box height */
		/* pure black */
		color: #000000;
		/* Inside auto layout */
		flex: none;
		order: 0;
		flex-grow: 0;
	}
	.middle_row_log_status {
		/* Cell/mobile_chat bubble */
		/* Auto layout */
		display: flex;
		flex-direction: row;
		align-items: flex-start;
		padding: 0px 5px;

		/* width: 54px; */
		height: 17px;
		/* light yellow */
		background: #FFF0BB;
		border-radius: 8px;
		/* Inside auto layout */
		flex: none;
		order: 1;
		flex-grow: 0;
	}
	.middle_row_log_status_text {
		/* 1 */
		/* width: 44px; */
		height: 17px;

		font-family: 'Hiragino Sans';
		font-style: normal;
		font-weight: 400;
		font-size: 11px;
		line-height: 16px;
		/* identical to box height */
		display: flex;
		align-items: center;
		text-align: center;
		/* talkappi dark grey */
		color: #3D3F45;
		/* Inside auto layout */
		flex: none;
		order: 0;
		flex-grow: 0;
	}
	.middle_row_log_time {
		/* 昨日 16:23 */
		/* width: 64px; */
		height: 18px;

		font-family: 'Hiragino Sans';
		font-style: normal;
		font-weight: 400;
		font-size: 12px;
		line-height: 18px;
		/* identical to box height */
		/* smoke grey */
		color: #A1A4AA;
		/* Inside auto layout */
		flex: none;
		order: 1;
		flex-grow: 0;
	}
	.middle_row_log_frame_1227 {
		/* Frame 1227 */
		/* Auto layout */
		display: flex;
		flex-direction: row;
		align-items: flex-start;
		padding: 0px;
		gap: 10px;

		/* width: 146px; */
		height: 18px;
		/* Inside auto layout */
		flex: none;
		order: 0;
		flex-grow: 0;
	}
	.last_usage_frame_1199 {
		/* Frame 1199 */
		/* Auto layout */
		display: flex;
		flex-direction: column;
		align-items: flex-start;
		padding: 8px 24px;
		gap: 8px;

		width: 647px;
		height: 81px;
		/* grey white */
		background: #F6F7F9;
		border-radius: 4px;
		/* Inside auto layout */
		flex: none;
		order: 4;
		align-self: stretch;
		flex-grow: 0;
	}
	.usage_sub_frame_1192_1194 {
		/* Frame 1192 */
		/* Frame 1194 */
		/* Auto layout */
		display: flex;
		flex-direction: column;
		align-items: flex-start;
		padding: 8px 24px;
		gap: 8px;
		width: 318.5px;
		height: 81px;
		/* grey white */
		background: #F6F7F9;
		border-radius: 4px;
		/* Inside auto layout */
		flex: none;
		order: 0;
		flex-grow: 1;
	}
	.last_usage_time_frame_1198 {
		/* Frame 1198 */
		/* Auto layout */

		display: flex;
		flex-direction: row;
		align-items: center;
		padding: 0px;
		gap: 8px;

		width: 162px;
		height: 21px;


		/* Inside auto layout */

		flex: none;
		order: 0;
		flex-grow: 0;
	}
	.last_usage_time {
		/* 2021/08/31 23:45 */
		width: 240px;
		height: 36px;

		font-family: 'Hiragino Sans';
		font-style: normal;
		font-weight: 500;
		font-size: 24px;
		line-height: 36px;
		/* identical to box height */


		color: #3D3F45;


		/* Inside auto layout */

		flex: none;
		order: 1;
		flex-grow: 0;
	}
	.last_usage_timezone {
		/* UTC+09:00 */
		width: 70px;
		height: 18px;

		font-family: 'Hiragino Sans';
		font-style: normal;
		font-weight: 500;
		font-size: 12px;
		line-height: 18px;
		/* identical to box height */


		color: #A1A4AA;


		/* Inside auto layout */

		flex: none;
		order: 1;
		flex-grow: 0;
	}
	.usage_frequency_frame_1190 {
		/* Frame 1190 */
		/* Auto layout */
		display: flex;
		flex-direction: column;
		align-items: flex-start;
		padding: 0px;
		gap: 8px;
		width: 391px;
		height: 77px;
		/* Inside auto layout */
		flex: none;
		order: 0;
		flex-grow: 1;
	}
	.usage_item_title {
		/* CHATBOT利用 */
		/* width: 128px; */
		height: 21px;
		font-family: 'Hiragino Sans';
		font-style: normal;
		font-weight: 400;
		font-size: 14px;
		line-height: 21px;
		/* identical to box height */
		color: #3D3F45;
		/* Inside auto layout */
		flex: none;
		order: 0;
		flex-grow: 0;
	}
	.usage_item_value_frame {
		width: 96px;
		height: 48px;
		/* gap: 10px; */

		display: flex;
		order:1;
	}
	.usage_item_contents {
		/* ・チェックアウト一番遅く時間は？ ・10 */
		width: 270.5px;
		height: 88px;

		font-family: 'Hiragino Sans';
		font-style: normal;
		font-weight: 400;
		font-size: 12px;
		line-height: 18px;

		color: #3D3F45;
		/* Inside auto layout */
		flex: none;
		order: 1;
		align-self: stretch;
		flex-grow: 1;
	}
	.usage_item_value_detail_frame_1196 {
		/* Frame 1196 */
		/* Auto layout */
		display: flex;
		flex-direction: row;
		align-items: center;
		padding: 0px;
		gap: 24px;
		width: 270.5px;
		height: 36px;
		/* Inside auto layout */
		flex: none;
		order: 1;
		align-self: stretch;
		flex-grow: 0;
	}
	.usage_item_detail_label {
		/* text label small */
		/* Auto layout */
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		padding: 0px;
		/* width: 60px; */
		height: 24px;
		/* Inside auto layout */
		flex: none;
		order: 1;
		flex-grow: 0;
		cursor: pointer;
	}
	.usage_item_detail_label_frame_44 {
		/* Frame 44 */
		/* Auto layout */
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
		padding: 3px 12px;
		/* width: 60px; */
		height: 24px;
		/* pale grey for line */
		background: #E3E5E8;
		border-radius: 12px;
		/* Inside auto layout */
		flex: none;
		order: 0;
		flex-grow: 0;
	}
	.usage_item_value {
		/* 64 DAYS */
		/* width: 48px; */
		height: 48px;

		display: flex;
		flex-direction: row;

		font-family: 'Hiragino Sans';
		font-style: normal;
		font-weight: 400;
		/* font-size: 32px; */
		font-size: 24px;
		/* line-height: 48px; */
		line-height: 36px;
		/* identical to box height */
		color: #000000;
		/* Inside auto layout */
		flex: none;
		order: 0;
		flex-grow: 0;
	}
	.usage_item_detail_label_text {
		/* 日本語 */
		/* width: 36px; */
		height: 18px;
		font-family: 'Hiragino Sans';
		font-style: normal;
		font-weight: 300;
		font-size: 12px;
		line-height: 18px;
		/* identical to box height */
		/* pure black */
		color: #000000;
		/* Inside auto layout */
		flex: none;
		order: 0;
		flex-grow: 0;
	}
	.usage_item_unit {
		/* 64 DAYS */
		width: 10px;
		height: 48px;

		display: flex;
		flex-direction: row;

		font-family: 'Hiragino Sans';
		font-style: normal;
		font-weight: 400;
		font-size: 14px;
		line-height: 48px;
		/* identical to box height */
		color: #000000;
		/* Inside auto layout */
		flex: none;
		order: 1;
		flex-grow: 0;

		padding: 7px 0px 0px 10px;
		/* margin: 7px 0px 0px 10px; */
	}
	.usage_since_text {
		/* 2021/03/01 20:09 から */


		width: 160px;
		height: 18px;

		font-family: 'Hiragino Sans';
		font-style: normal;
		font-weight: 500;
		font-size: 12px;
		line-height: 18px;
		/* identical to box height */


		color: #3D3F45;


		/* Inside auto layout */

		flex: none;
		order: 1;
		flex-grow: 0;
	}
	.customer_journey {
		/* カスタマージャーニー */


		box-sizing: border-box;

		/* Auto layout */

		display: flex;
		flex-direction: column;
		align-items: flex-start;
		padding: 24px 24px 0px;
		gap: 24px;

		/* width: 408px; */
		/* width: 100%; */
		/* height: 774px; */
		/* height: 865px; */
		/* height: 889px; */

		/* pure white */

		background: #FFFFFF;
		/* pale grey for line */

		border: 1px solid #E3E5E8;
		border-radius: 4px;

		/* Inside auto layout */

		flex: none;
		order: 1;
		align-self: stretch;
		flex-grow: 1;
	}
	.journey_head {
		/* カスタマージャーニー */


		/* width: 139px; */
		height: 21px;

		font-family: 'Hiragino Sans';
		font-style: normal;
		font-weight: 400;
		font-size: 14px;
		line-height: 21px;
		/* identical to box height */


		color: #3D3F45;


		/* Inside auto layout */

		flex: none;
		order: 0;
		flex-grow: 0;
	}
	.journey_body_frame_1219 {
		/* Frame 1219 */


		/* Auto layout */

		display: flex;
		flex-direction: row;
		align-items: flex-start;
		padding: 0px;

		/* width: 360px; */
		width: 100%;
		/* height: 705px; */
		height: 796px;

		/* Inside auto layout */

		flex: none;
		order: 1;
		align-self: stretch;
		flex-grow: 1;
	}
	.journey_timeline_frame_1220 {
		/* Frame 1220 */


		/* Auto layout */

		display: flex;
		flex-direction: row;
		align-items: flex-start;
		padding: 0px 5px;

		width: 12px;
		/* height: 705px; */
		height: 796px;


		/* Inside auto layout */

		flex: none;
		order: 0;
		align-self: stretch;
		flex-grow: 0;
		margin: 0px -12px;
	}
	.timeline_draw {
		/* Line */


		width: 2px;
		height: 705px;

		/* pale grey for line */

		border: 1px dashed #E3E5E8;

		/* Inside auto layout */

		flex: none;
		order: 0;
		align-self: stretch;
		flex-grow: 0;
	}
	.journey_contents_frame_1218 {
		/* Frame 1218 */

		overflow-y: scroll;
		overflow-x: hidden;
		/* max-width: 100%; */

		/* Auto layout */

		display: flex;
		flex-direction: column;
		align-items: flex-start;
		padding: 0px;
		gap: 16px;

		/* width: 360px; */
		width: 100%;
		/* height: 703px; */
		height: 783px;


		/* Inside auto layout */

		flex: none;
		order: 1;
		flex-grow: 1;
	}
	.journey_user_creation_frame_1211 {
		/* Frame 1211 */


		/* Auto layout */

		display: flex;
		flex-direction: row;
		align-items: center;
		padding: 0px;
		gap: 16px;

		/* width: 360px; */
		height: 33px;


		/* Inside auto layout */

		flex: none;
		order: 0;
		align-self: stretch;
		flex-grow: 0;
	}
	.timeline_spot_frame_1210 {
		/* Frame 1210 */


		/* Auto layout */

		display: flex;
		flex-direction: row;
		align-items: flex-start;
		padding: 6px 0px;

		width: 12px;
		height: 33px;


		/* Inside auto layout */

		flex: none;
		order: 0;
		align-self: stretch;
		flex-grow: 0;
	}
	.timeline_spot_icon {
		/* Icon-timeline_spot */


		width: 12px;
		height: 12px;


		/* Inside auto layout */

		flex: none;
		order: 0;
		flex-grow: 0;
	}
	.spot_icon_check {
		position: absolute;
		visibility: hidden;
		left: 0%;
		right: 0%;
		top: 0%;
		bottom: 0%;
	}
	.user_creation_content_frame_967 {
		/* Frame 967 */


		/* Auto layout */

		display: flex;
		flex-direction: column;
		align-items: flex-start;
		padding: 0px;

		/* width: 332px; */
		width: 100%;
		height: 33px;


		/* Inside auto layout */

		flex: none;
		order: 1;
		flex-grow: 1;
	}

	.user_creation_text {
		/* ユーザー生成 */
		width: 72px;
		height: 18px;
		font-family: 'Hiragino Sans';
		font-style: normal;
		font-weight: 500;
		font-size: 12px;
		line-height: 18px;
		/* identical to box height */
		/* talkappi dark grey */
		color: #3D3F45;
		/* Inside auto layout */
		flex: none;
		order: 1;
		flex-grow: 0;
	}
	.journey_faq_frame_1213 {
		/* Frame 1213 */


		/* Auto layout */

		display: flex;
		flex-direction: row;
		align-items: center;
		padding: 0px;
		gap: 16px;

		/* width: 360px; */
		width: 100%;
		height: 132px;


		/* Inside auto layout */

		flex: none;
		order: 1;
		align-self: stretch;
		flex-grow: 0;
	}
	.journey_frame_flex {
		/* Frame 1206 */
		/* Auto layout */
		display: flex;
		flex-direction: row;
		align-items: flex-start;
		padding: 0px;
		gap: 16px;
		/* width: 360px; */
		/* height: 114px; */
		/* Inside auto layout */
		flex: none;
		order: 0;
		align-self: stretch;
		flex-grow: 0;
	}

	.timeline_spot_frame_flex {
		/* Frame 1207 */
		/* Auto layout */
		display: flex;
		flex-direction: row;
		align-items: flex-start;
		padding: 6px 0px;
		width: 12px;
		/* height: 114px; */
		/* Inside auto layout */
		flex: none;
		order: 0;
		align-self: stretch;
		flex-grow: 0;
	}
	.spot_icon_draw {
		/* Oval Copy */


		position: absolute;
		left: 8.33%;
		right: 8.33%;
		top: 8.33%;
		bottom: 8.33%;

		/* label color 2 */

		background: #CFF2D7;
	}

	.journey_content_frame_flex {
		/* Frame 978 */
		/* Auto layout */
		display: flex;
		flex-direction: column;
		align-items: flex-start;
		padding: 0px;
		gap: 12px;
		/* width: 332px; */
		width: 100%;
		/* height: 132px; */
		/* Inside auto layout */
		flex: none;
		order: 1;
		flex-grow: 1;
	}

	.journey_type_label_flex {
		/* Label/form_24x Copy */
		/* Auto layout */
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		padding: 0px;
		/* width: 135px; */
		height: 24px;
		/* Inside auto layout */
		flex: none;
		order: 0;
		flex-grow: 0;
	}
	.type_label_frame_flex {
		/* Frame 43 */
		/* Auto layout */
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
		padding: 3px 12px;
		/* width: 100px; */
		height: 24px;
		/* light green */
		background: #CFF2D7;
		border-radius: 12px;
		/* Inside auto layout */
		flex: none;
		order: 0;
		flex-grow: 0;
	}
	.type_label_text_flex {
		/* 日本語 */
		/* width: 76px; */
		height: 18px;

		font-family: 'Hiragino Sans';
		font-style: normal;
		font-weight: 300;
		font-size: 12px;
		line-height: 18px;
		/* identical to box height */
		/* pure black */
		color: #000000;
		/* Inside auto layout */
		flex: none;
		order: 0;
		flex-grow: 0;
	}

	.journey_item_frame_flex {
		/* Frame 970 */
		/* Auto layout */
		display: flex;
		flex-direction: column;
		align-items: flex-start;
		padding: 0px;

		/* width: 332px; */
		width: 100%;
		/* height: 51px; */
		/* Inside auto layout */
		flex: none;
		order: 0;
		align-self: stretch;
		flex-grow: 0;
	}
	.journey_item_time_flex {
		/* 2021/03/01 22:54 */
		/* width: 97px; */
		height: 15px;
		font-family: 'Hiragino Sans';
		font-style: normal;
		font-weight: 500;
		font-size: 10px;
		line-height: 15px;
		/* identical to box height */
		/* smoke grey */
		color: #A1A4AA;
		/* Inside auto layout */
		flex: none;
		order: 0;
		flex-grow: 0;
	}
	.journey_item_text_flex {
		/* FAQ「チェックイン前・チェックアウト後 */
		width: 332px;
		/* width: 100%; */
		/* width: 232px; */

		/* height: 36px; */
		font-family: 'Hiragino Sans';
		font-style: normal;
		font-weight: 400;
		font-size: 12px;
		line-height: 18px;
		/* talkappi dark grey */
		color: #3D3F45;
		/* Inside auto layout */
		flex: none;
		order: 1;
		align-self: stretch;
		flex-grow: 0;
	}
</style>
<script>
// Get the root element
var r = document.querySelector(':root');

// Create a function for setting a variable value
function myFunction_setColor(color) {
  // Set the value of variable --color to another value (in this case "lightblue")
  r.style.setProperty('--color', color);
}

// Create a function for setting a variable value
function myFunction_setOrder(order) {
  // Set the value of variable --color to another value (in this case "lightblue")
  r.style.setProperty('--order', order);
}
</script>

<div class="modal fade" id="attribute_tag_box" tabindex="-1" role="basic" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="profile_float_panel">
				<div class="profile_float_cancel_frame_62" data-dismiss="modal" aria-hidden="true">
				<!-- type="button"  -->
					<img src="/assets/common/images/Icon-cancel_large.svg" class="closeImg" style="float:right;cursor:pointer;">
				</div>
				<div class="profile_float_frame_1236">
					<div class="name_id_location_time_device_frame_1237">
						<div class="member_name_float_frame">
						ユーザー名：<?php echo($member_name==''?'NO NAME':$member_name); ?>
						</div>
						<div class="member_id_flex" style="order:1;">メンバーID：<?php echo ($member_id); ?></div>
						
						<div class="location_time_frame_1238_1240" style="order:2;">
							<div class="location_frame_957">
								<img src="./../assets/admin/css/img/icon-light-location.svg" class="location_icon">
								<div class="location_text">
									<?php 
										echo($country);
										if($country != '' && $city != '') 
											echo(' | ');
										echo($city);
									?>
								</div>
							</div>
							<div class="time_frame_958">
								<img src="./../assets/admin/css/img/icon-light-timezone.svg" class="time_icon">							
								<div class="time_text"><?php echo($member_now); ?></div>
							</div>
						</div>
						<div class="location_time_frame_1238_1240" style="order:3;">
							<div class="device_mail_frame_1239_959">
								<img src="./../assets/admin/css/img/icon-preview-mobile.svg" class="device_icon">
								<div class="device_client_mail">
									<?php 
										if($mobile == 1) 
											echo($device); 
										else 
											echo($member_browser);
										if(($mobile == 1 || $member_browser != '') && ($member_phone_number != '' || $sns_type_cd != NULL)) 
											echo(' | ');
										if($member_phone_number != '') 
											echo($member_phone_number); 
										else if($sns_type_cd != NULL) 
											echo(strtoupper($_codes['08'][$sns_type_cd])); 
									?>
								</div><!-- TODO -->
							</div>
							<div class="device_mail_frame_1239_959" style="order:1;">
								<img src="./../assets/admin/css/img/icon-preview-mail.svg" class="mail_icon">
								<div class="device_client_mail">
									<?php echo($member_email_address);?>
								</div>
							</div>
						</div>
					</div>
					<!-- to have all tags display -->
					<div class="memeber_labels_frame_960" style="order:1;">
						<!-- php loop generated tags -->
						<?php
							$attr_index = 0;
							$words_length = 0;
							$line_index = 0;
							$attr_index_offset = 0;
							foreach($member_attributes as $attr => $attr_name) {
								$tag_color = $attribute_tag_colors[$attr_index%6];
								if (2+strlen($attr)+$words_length > 125) {
									echo('
					</div>
					<div class="memeber_labels_frame_960" style="order:'.($line_index+1).';">
									');
									$line_index ++;
									$attr_index_offset = $attr_index;
									$words_length = 0;
								}
								$div = '<div class="color_tag_flex_0" style="background: ' . $tag_color . '; order: ' . ($attr_index-$attr_index_offset) . ';"><div class="tag_text_flex">' . $attr . '</div></div>';
								echo($div);
								$attr_index++;
								$words_length += strlen($attr) + 2;
							}
						?>
					</div>
				</div>
			</div>
		</div>
		<!-- /.modal-content -->
	</div>
	<!-- /.modal-dialog -->
</div>
<div class="container_frame_1222">
	<div class="user_info_left">
		<div class="memberinfo_flex">
			<div class="profile_frame_1188">
				<div class="profile_img_group_863">
					<div class="profile_img"></div>
					<!-- css/img/icon-mobile-channel-line -->
					<?php
						$img_sns_prefix = '<img src="./../assets/admin/images/channel=';
						$img_sns_suffix = '.svg" class="mobile_channel_icon">';
						if ($sns_type_cd == 'ln') echo($img_sns_prefix . 'line' . $img_sns_suffix);
						else if ($sns_type_cd == 'wc') echo($img_sns_prefix . 'wechat' . $img_sns_suffix);
						else if ($sns_type_cd == 'wb') echo($img_sns_prefix . 'web' . $img_sns_suffix);
						else if ($sns_type_cd == 'fb') echo($img_sns_prefix . 'fb' . $img_sns_suffix);
					?>
				</div>	
				<div class="ip_frame_965">
					<div class="ip_label_flex">IPアドレス</div>
					<div class="ip_addr_flex"><?php echo ($member_ip); ?></div>
				</div>
			</div>
			<div class="profile_frame_964">
				<!-- <div class="name_id_labels_frame_963"> -->
				<div class="name_id_push_frame_1243">
					<div class="name_id_frame_963">
						<div class="namelabel_flex">ユーザー名</div>
						<div class="member_name_frame_961">
							<div class="member_name_flex"><?php echo($member_name==''?'NO NAME':$member_name); ?></div>
							<?php if ($chat_online_status == 1) echo ('<div class="member_name_oval"></div>'); ?>
						</div>
						<div class="member_id_flex">メンバーID：<?php echo ($member_id); ?></div>
					</div>
					<div class="js-action-push-msg push_msg_button_with_icon" date-bot_id="<?php echo($bot_id); ?>" date-member_id="<?php echo($member_id); ?>" id="push_msg_btn">
						<img src="/assets/admin/images/icon-navi-marketing.svg" class="push_msg_btn_icon">
						<div class="push_msg_btn_txt">プッシュ配信</div>
					</div>
				</div>
				<div class="memeber_label_rows_frame_1242">
					<div class="memeber_labels_frame_960">
						<!-- php loop generated tags -->
						<?php
							$attr_index = 0;
							$words_length = 0;
							$line_index = 0;
							$attr_index_offset = 0;
							foreach($member_attributes as $attr => $attr_name) {
								$tag_color = $attribute_tag_colors[$attr_index%6];
								if (2+strlen($attr)+$words_length > 105 && $line_index < 1) {
									echo('
					</div>
					<div class="memeber_labels_frame_960">
									');
									$line_index ++;
									$attr_index_offset = $attr_index;
									$words_length = 0;
								} else if (2+strlen($attr)+$words_length > 95 && $line_index > 0) {
									echo('<div class="color_tag_flex_0" id="show_attribute_tag_box" style="background: #E3E5E8; order: ' . ($attr_index-$attr_index_offset) . ';cursor: pointer;"><div class="tag_text_flex">...</div></div>');
									break;
								}
								$div = '<div class="color_tag_flex_0" style="background: ' . $tag_color . '; order: ' . ($attr_index-$attr_index_offset) . ';"><div class="tag_text_flex">' . $attr . '</div></div>';
								echo($div);
								$attr_index++;
								$words_length += strlen($attr) + 2;
							}
						?>
					</div>
				</div>
				<!-- </div> -->
				<div class="location_time_device_frame_962">
					<div class="location_time_frame_1238_1240">
						<div class="location_frame_957">
							<img src="./../assets/admin/css/img/icon-light-location.svg" class="location_icon">
							<div class="location_text">
								<?php 
									echo($country);
									if($country != '' && ($city != '')) // || $timezone != ''
										echo(' | ');
									echo($city);
									// if($city != '' && $timezone != '') 
									// 	echo(' | ');
									// echo($timezone); 
								?>
							</div>
						</div>
						<div class="time_frame_958">
							<img src="./../assets/admin/css/img/icon-light-timezone.svg" class="time_icon">
							<!-- member_regist_date OR member_last_time -->
							<div class="time_text"><?php echo($member_now); ?></div>
						</div>
					</div>
					<div class="location_time_frame_1238_1240" style="order:1;">
						<div class="device_mail_frame_1239_959">
							<img src="./../assets/admin/css/img/icon-preview-mobile.svg" class="device_icon">
							<div class="device_client_mail">
								<?php 
									if($mobile == 1) 
										echo($device); 
									else 
										echo($member_browser);
									if(($mobile == 1 || $member_browser != '') && ($member_phone_number != '' || $sns_type_cd != NULL)) 
										echo(' | ');
									if($member_phone_number != '') 
										echo($member_phone_number); 
									else if($sns_type_cd != NULL) 
										echo(strtoupper($_codes['08'][$sns_type_cd])); 
								?>
							</div><!-- TODO -->
						</div>
						<div class="device_mail_frame_1239_959" style="order:1;">
							<img src="./../assets/admin/css/img/icon-preview-mail.svg" class="mail_icon">
							<div class="device_client_mail">
								<?php echo($member_email_address);?>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="user_activity_summary">
			<div class="summary_tabs_frame_966">
				<div class="js-function-tab summary_tab_label_flex active" data-target="summaryContentsChatbot">						
					<div class="tab_label_text_flex">AIチャットボット</div>		
				</div>		
				<div class="js-function-tab summary_tab_label_flex" data-target="summaryContentsFAQ">						
					<div class="tab_label_text_flex">FAQシステム</div>			
				</div>	
				<div class="js-function-tab summary_tab_label_flex" data-target="summaryContentsINQUIRY">						
					<div class="tab_label_text_flex">問合せ・予約フォーム</div>			
				</div>	
				<div class="js-function-tab summary_tab_label_flex" data-target="summaryContentsSURVEY">						
					<div class="tab_label_text_flex"">アンケート・クーポン</div>			
				</div>
			</div>
			<div class="summary_frame_wrapper_w_scroll" style="overflow-y: scroll;">
			<div id="summaryContentsChatbot" class="summary_contents_frame_1203 js-function-area">
				<div id="topBannerChatbot" class="summary_top_banner_frame_1191">
					<div class="usage_frequency_frame_1190">
						<!-- 利用回数 -->
						<div class="usage_item_title">チャットボットを開いた回数</div>
						<div class="usage_item_value_frame">
							<div class="usage_item_value" style="font-size: 32px; line-height: 48px;"><?php echo($chatbot_follow_count + 0); ?></div>
							<div class="usage_item_unit">回</div>					
						</div>
					</div>
					<div class="usage_since_text">
						<?php echo empty($member_regist_date) ? '' : $member_regist_date . '	から';?>
					</div>
				</div>	
				<div class="middle_row_frame_1200">
					<div class="usage_sub_frame_1192_1194">
						<div class="usage_item_title">チャット数</div>
						<div class="usage_item_value_frame">
							<div class="usage_item_value"><?php echo($member_msg_count + 0); ?></div>
							<div class="usage_item_unit">件</div>					
						</div>
					</div>
					<div class="usage_sub_frame_1192_1194" style="order:1;">
						<!-- 自動回答 -->
						<div class="usage_item_title">有人対応</div>
						<div class="usage_item_value_detail_frame_1196">
							<div class="usage_item_value_frame">
								<div class="usage_item_value"><?php echo($total_human_answers + 0); ?></div>
								<div class="usage_item_unit">回</div>					
							</div>
							<div id="humanAnswerDetailBtn" date-bot_id="<?php echo($bot_id); ?>" date-member_id="<?php echo($member_id); ?>" class="js-action-humananswer-detail usage_item_detail_label">
								<div class="usage_item_detail_label_frame_44">
									<div class="usage_item_detail_label_text">詳細へ</div>
								</div>					
							</div>
						</div>
					</div>
				</div>
				<div class="middle_row_frame_1200" style="order:2; justify-content: center;">
					<div class="usage_sub_frame_1192_1194" style="order:1;">
						<!-- 予約回数 -->
						<div class="usage_item_title">宿泊予約回数</div>
						<div class="usage_item_value_detail_frame_1196">
							<div class="usage_item_value_frame">
								<div class="usage_item_value"><?php echo($order_count + 0); ?></div>
								<div class="usage_item_unit">回</div>					
							</div>
							<div class="js-action-chatbook-detail usage_item_detail_label" date-bot_id="<?php echo($bot_id); ?>" date-member_id="<?php echo($member_id); ?>">
								<div class="usage_item_detail_label_frame_44">
									<div class="usage_item_detail_label_text">詳細へ</div>
								</div>					
							</div>
						</div>
					</div>
					<div class="usage_sub_frame_1192_1194" style="order:1;">
						<!-- リクエスト受付回数 -->
						<div class="usage_item_title">リクエスト依頼回数</div>
						<div class="usage_item_value_detail_frame_1196">
							<div class="usage_item_value_frame">
								<div class="usage_item_value"><?php echo($request_count + 0); ?></div>
								<div class="usage_item_unit">回</div>					
							</div>
							<div class="js-action-request-detail usage_item_detail_label" date-bot_id="<?php echo($bot_id); ?>" date-member_id="<?php echo($member_id); ?>">
								<div class="usage_item_detail_label_frame_44">
									<div class="usage_item_detail_label_text">詳細へ</div>
								</div>					
							</div>
						</div>
					</div>
				</div>					
				<div class="middle_row_frame_1200" style="order:3; height: 177px;">
					<div class="usage_sub_frame_1192_1194" style="height: 177px; gap: 12px; align-self: stretch;">
						<div class="usage_item_title">最近のメッセージ</div>
						<div class="usage_item_contents">
						<?php echo($last_member_messages); ?>
						<!-- ・チェックアウト一番遅く時間は？</br>
						・10月の予約ができますか</br>
						・英語話せるスタッフがいますか</br>
						・24時間チェックインできますか</br>
						・朝食県の現地購入は可能ですか -->
						</div>					
						<div class="js-action-humananswer-detail usage_item_detail_label" style="order:2;" date-bot_id="<?php echo($bot_id); ?>" date-member_id="<?php echo($member_id); ?>">
							<div class="usage_item_detail_label_frame_44">
								<div class="usage_item_detail_label_text">すべてのメッセージ</div>
							</div>					
						</div>
					</div>
					<div class="usage_sub_frame_1192_1194" style="order:1; height: 177px; gap: 12px; align-self: stretch;">
						<div class="usage_item_title">最近のリクエスト内容</div>
						<div class="usage_item_contents" style="height: 128px;">
						<!-- ・宿泊予約確認</br>
						・記念日対応</br>
						・忘れ物 -->
						<?php echo($last_requests_intent); ?>
						</div>					
					</div>
				</div>
				<div class="last_usage_frame_1199">
					<div class="last_usage_time_frame_1198">
						<div class="usage_item_title">最終利用時間</div>
						<div class="last_usage_timezone"><?php echo($timezone); ?></div>
					</div>			
					<div class="last_usage_time"><?php echo($member_chatbot_last_time); ?></div>		
				</div>
			</div>	
			<div id="summaryContentsFAQ" class="summary_contents_frame_1203 js-function-area" style="display:none;">	
				<div id="topBannerFAQ" class="summary_top_banner_frame_1191">
					<div class="usage_frequency_frame_1190">
						<!-- 利用回数 -->
						<div class="usage_item_title">FAQページを訪問した回数</div>
						<div class="usage_item_value_frame">
							<!-- 実は follow table から data を利用するはずです -->
							<div class="usage_item_value" style="font-size: 32px; line-height: 48px;"><?php echo($total_faq_logs + 0); ?></div>
							<div class="usage_item_unit">回</div>					
						</div>
					</div>
					<div class="usage_since_text">
						<?php echo empty($first_faq_usage) ? '' : $first_faq_usage . '	から';?>
					</div>

				</div>	
				<div class="middle_row_logs_frame_1195_1197" style="order:2; height: 179px; align-self: stretch; flex-grow: 0;">
					<div class="usage_item_title">表示した質問</div>
					<div class="usage_item_contents" style="width: 599px; height: 90px;">
						<?php echo($last_chosen_displayed_questions); ?>
						<!-- ・駐輪場はありますか？</br> 
						・近くに高速道路はありますか？最寄の出口はどこですか？</br> 
						・タバコの自動販売機はありますか？</br> 
						・コーヒー・お茶（紅茶、緑茶など）はありますか。</br> 
						・外出時にルームキーを預ける必要？ -->
						</div>	
					<div class="js-action-faqchonsendisplay-detail usage_item_detail_label" style="order:2;" date-bot_id="<?php echo($bot_id); ?>" date-member_id="<?php echo($member_id); ?>">
						<div class="usage_item_detail_label_frame_44">
							<div class="usage_item_detail_label_text">すべての質問</div>
						</div>					
					</div>
				</div>
				<div class="middle_row_frame_1200" style="order:3; height: 179px;">
					<div class="usage_sub_frame_1192_1194" style="height: 179px; gap: 12px; align-self: stretch;">
						<div class="usage_item_title">検索したキーワード</div>
						<div class="usage_item_contents" style="height: 90px;">
						<?php echo($last_searched_keywords_questions); ?>
						</div>					
						<div class="js-action-faqsearchedkeywords-detail usage_item_detail_label" style="order:2;" date-bot_id="<?php echo($bot_id); ?>" date-member_id="<?php echo($member_id); ?>">
							<div class="usage_item_detail_label_frame_44">
								<div class="usage_item_detail_label_text">すべてのキーワード</div>
							</div>					
						</div>
					</div>
					<div class="usage_sub_frame_1192_1194" style="order:1; height: 179px; gap: 12px; align-self: stretch;">
						<div class="usage_item_title">選択したカテゴリ</div>
						<div class="usage_item_contents" style="height: 90px;">
						<?php echo($last_chosen_categories); ?>
						</div>			
						<div class="js-action-faqchonsencategories-detail usage_item_detail_label" style="order:2;" date-bot_id="<?php echo($bot_id); ?>" date-member_id="<?php echo($member_id); ?>">
							<div class="usage_item_detail_label_frame_44">
								<div class="usage_item_detail_label_text">すべてのカテゴリ</div>
							</div>					
						</div>
					</div>
				</div>
				<div class="middle_row_frame_1200" style="order:4; justify-content: center;">
					<div class="usage_sub_frame_1192_1194" style="order:1;">
						<!-- 十分評価 -->
						<div class="usage_item_title">十分と評価した回数</div>
						<div class="usage_item_value_detail_frame_1196">
							<div class="usage_item_value_frame">
								<div class="usage_item_value"><?php echo($total_faq_satisfied_surveys + 0); ?></div>
								<div class="usage_item_unit">回</div>					
							</div>
							<div class="js-action-satisfied-detail usage_item_detail_label" date-bot_id="<?php echo($bot_id); ?>" date-member_id="<?php echo($member_id); ?>">
								<div class="usage_item_detail_label_frame_44">
									<div class="usage_item_detail_label_text">詳細へ</div>
								</div>					
							</div>
						</div>
					</div>
					<div class="usage_sub_frame_1192_1194" style="order:1;">
						<!-- 不十分評価 -->
						<div class="usage_item_title">不十分と評価した回数</div>
						<div class="usage_item_value_detail_frame_1196">
							<div class="usage_item_value_frame">
								<div class="usage_item_value"><?php echo($total_faq_unsatisfied_surveys + 0); ?></div>
								<div class="usage_item_unit">回</div>					
							</div>
							<div class="js-action-unsatisfied-detail usage_item_detail_label" date-bot_id="<?php echo($bot_id); ?>" date-member_id="<?php echo($member_id); ?>">
								<div class="usage_item_detail_label_frame_44">
									<div class="usage_item_detail_label_text">詳細へ</div>
								</div>					
							</div>
						</div>
					</div>
				</div>	
				<div class="last_usage_frame_1199" style="order:5;">
					<div class="last_usage_time_frame_1198">
						<div class="usage_item_title">最終利用時間</div>
						<div class="last_usage_timezone"><?php echo($timezone); ?></div>
					</div>			
					<div class="last_usage_time"><?php echo($last_faq_usage); ?></div>		
				</div>		
			</div>	
			<div id="summaryContentsINQUIRY" class="summary_contents_frame_1203 js-function-area" style="display:none;">	
				<div id="topBannerINQUIRY" class="summary_top_banner_frame_1191">
					<div class="usage_frequency_frame_1190">
						<!-- 利用回数 -->
						<div class="usage_item_title">問合せ・予約回数</div>
						<div class="usage_item_value_frame">
							<div class="usage_item_value" style="font-size: 32px; line-height: 48px;"><?php echo($total_inquiry_results + 0); ?></div>
							<div class="usage_item_unit">回</div>					
						</div>
					</div>
					<div class="usage_since_text">
						<?php echo empty($first_inquiry_result_time) ? '' : $first_inquiry_result_time . '	から';?>
					</div>
				</div>	
				<div class="middle_row_logs_frame_1195_1197" style="order:1; height: 331px; align-self: stretch; flex-grow: 0;">
					<div class="usage_item_title">問合せ・予約したフォーム</div>
					<div class="middle_row_logs_wrapper_frame_1229" style="height: 242px;">
						<?php 
							$inquiry_index = 0;
							foreach($last_inquiry_results as $inquiry_result) {
								$inquiry_time = $inquiry_result['inquiry_time'];
								$inquiry_name = $inquiry_result['inquiry_name'];
								$status_cd = $inquiry_result['status_cd'];
								$div = 
						'<div class="middle_row_log_frame_1227" style="order: ' . $attr_index . ';">
							<div class="middle_row_log_text">' . $inquiry_name . '</div>
							<div class="middle_row_log_status" style="background: ' . $inquiry_status_tag_colors[$status_cd] . ';">
								<div class="middle_row_log_status_text">' . 
									$_codes['25'][$status_cd] . 
									'</div>
								</div>
							<div class="middle_row_log_time">' . $inquiry_time . '</div>
						</div>';
								echo($div);
								$inquiry_index++;
							}
						?>
					</div>
					<div class="usage_item_detail_label" style="order:5;">
						<div class="usage_item_detail_label_frame_44">
							<div class="usage_item_detail_label_text">すべてのフォーム</div>
						</div>					
					</div>
				</div>	
				<div class="last_usage_frame_1199">
					<div class="last_usage_time_frame_1198">
						<div class="usage_item_title">最終利用時間</div>
						<div class="last_usage_timezone"><?php echo($timezone); ?></div>
					</div>			
					<div class="last_usage_time"><?php echo($last_inquiry_result_time); ?></div>		
				</div>		
			</div>	
			<div id="summaryContentsSURVEY" class="summary_contents_frame_1203 js-function-area" style="display:none;">	
				<div id="topBannerSURVEY" class="summary_top_banner_frame_1191">
					<div class="usage_frequency_frame_1190">
						<!-- 利用回数 -->
						<div class="usage_item_title">アンケートに回答した回数</div>
						<div class="usage_item_value_frame">
							<div class="usage_item_value" style="font-size: 32px; line-height: 48px;"><?php echo($total_survey_results + 0); ?></div>
							<div class="usage_item_unit">回</div>					
						</div>
					</div>
					<div class="usage_since_text">
						<?php echo empty($first_survey_result_time) ? '' : $first_survey_result_time . '	から';?>						
					</div>
				</div>	
				<div class="middle_row_logs_frame_1195_1197" style="order:1; height: 219px; align-self: stretch; flex-grow: 0;">
					<div class="usage_item_title">回答したアンケート</div>
					<div class="middle_row_logs_wrapper_frame_1229">
						<?php 
						$survey_index = 0;
						foreach($last_survey_results as $survey_result) {
							$survey_time = $survey_result['survey_time'];
							$survey_name = $survey_result['survey_name'];
							$div = 
						'<div class="middle_row_log_frame_1227" style="order: ' . $attr_index . ';">
							<div class="middle_row_log_text">' . $survey_name . '</div>
							<div class="middle_row_log_time">' . $survey_time . '</div>
						</div>';
							echo($div);
							$survey_index++;
						}
						?>
					</div>
					<div class="usage_item_detail_label" style="order:2;">
						<div class="usage_item_detail_label_frame_44">
							<div class="usage_item_detail_label_text">すべてのアンケート</div>
						</div>					
					</div>
				</div>	
				<div class="middle_row_frame_1200" style="order:2; justify-content: center;">
					<div class="usage_sub_frame_1192_1194" style="order:1;">
						<!-- クーポン獲得枚数 -->
						<div class="usage_item_title">クーポン獲得数</div>
						<div class="usage_item_value_detail_frame_1196">
							<div class="usage_item_value_frame">
								<div class="usage_item_value"><?php echo($total_coupon_results + 0); ?></div>
								<div class="usage_item_unit">枚</div>					
							</div>
							<div class="usage_item_detail_label">
								<div class="usage_item_detail_label_frame_44">
									<div class="usage_item_detail_label_text">詳細へ</div>
								</div>					
							</div>
						</div>
					</div>
					<div class="usage_sub_frame_1192_1194" style="order:1;">
						<!-- クーポン利用回数 -->
						<div class="usage_item_title">クーポン利用回数</div>
						<div class="usage_item_value_detail_frame_1196">
							<div class="usage_item_value_frame">
								<div class="usage_item_value"><?php echo($total_coupon_use_results + 0); ?></div>
								<div class="usage_item_unit">回</div>					
							</div>
							<div class="usage_item_detail_label">
								<div class="usage_item_detail_label_frame_44">
									<div class="usage_item_detail_label_text">詳細へ</div>
								</div>					
							</div>
						</div>
					</div>
				</div>		
				<div class="last_usage_frame_1199">
					<div class="last_usage_time_frame_1198">
						<div class="usage_item_title">最終利用時間</div>
						<div class="last_usage_timezone"><?php echo($timezone); ?></div>
					</div>			
					<div class="last_usage_time"><?php echo($last_survey_log_time); ?></div>		
				</div>	
			</div>	
			</div>
		</div>	
	</div>
	<div class="customer_journey">
		<div class="journey_head">カスタマージャーニー</div>	
		<div class="journey_body_frame_1219">
			<div class="journey_timeline_frame_1220">
				<div class="timeline_draw"></div>
			</div>
			<div class="journey_contents_frame_1218">
				<div class="journey_user_creation_frame_1211">
					<div class="timeline_spot_frame_1210">
						<img src="./../assets/admin/css/img/icon-timeline-spot-blue.svg" class="timeline_spot_icon">
					</div>
					<div class="user_creation_content_frame_967">
						<div class="journey_item_time_flex"><?php echo($member_regist_date); ?></div>
						<div class="user_creation_text">ユーザー生成</div>
					</div>
				</div>
				
				<!-- php loop generated journey follows -->
				<?php
					$follow_index = 0;
					$survey_action_names_jp =
						array (
								'begin' => '開始',
								'input' => '入力',
								'complete' => '送信',
						);
					$inquiry_action_names_jp =
						array (
								'begin' => '開始',
								'input' => '入力',
								'complete' => '送信',
						);
					$faq_action_names_jp =
						array (
								'',
								'検索窓入力',//1
								'カテゴリ選択',//2
								'質問選択',//3
								'キーワード選択',//4
								'表示',//5
								'関連質問表示',//6
						);
					foreach($followed_logs as $followed_log) {
						$order = $follow_index+1;
						$log_type = 'CHATBOT';
						$time_time_spot_color = 'lightblue';
						$type_label_background_color = '#D3EEFF';
						if (array_key_exists('sns_id', $followed_log) && $followed_log['sns_id'] == 'survey01')
							$log_type = 'SURVEY';
						else if (array_key_exists('sns_id', $followed_log) && $followed_log['sns_id'] == 'inquiry01')
							$log_type = 'INQUIRY';
						else if (array_key_exists('sns_id', $followed_log) && $followed_log['sns_id'] == 'faq01')
							$log_type = 'FAQ';
						if ($log_type == 'SURVEY'){
							$time_time_spot_color = 'lightyellow';
							$type_label_background_color = '#FFF0BB';
						} else if ($log_type == 'INQUIRY'){
							$time_time_spot_color = 'lightpurple';
							$type_label_background_color = '#E7E4FF';
						} else if ($log_type == 'FAQ'){
							$time_time_spot_color = 'lightgreen';
							$type_label_background_color = '#CFF2D7';
						}  
						
						$div = 
				'<div class="journey_frame_flex" style="order: ' . $order . ';">';
						echo($div);
						$div = 
					'<div class="timeline_spot_frame_flex">
						<img src="./../assets/admin/css/img/icon-timeline-spot-' . $time_time_spot_color . '.svg" class="timeline_spot_icon">
					</div>	
					<div class="journey_content_frame_flex">
						<div class="journey_type_label_flex">
							<div class="type_label_frame_flex" style="background: '. $type_label_background_color . ';">
								<div class="type_label_text_flex">talkappi '. $log_type . '</div>
							</div>
						</div>';
						echo($div);
						if ($log_type == 'CHATBOT'){
							foreach($followed_log as $log_time => $member_msg) {
								$time = strtotime($log_time);
								$log_time_minute = date("Y/m/d H:i", $time);
								$div = 
						'<div class="journey_item_frame_flex" style="order: 1;">
							<div class="journey_item_time_flex">' . $log_time_minute . '</div>
							<div class="journey_item_text_flex">' . $member_msg . '</div>
						</div>';
								echo($div);
							}
						} else if ($log_type == 'SURVEY'){
							unset($followed_log["sns_id"]);
							foreach($followed_log as $access_time => $survey_action) {
								$time = strtotime($access_time);
								$log_time_minute = date("Y/m/d H:i", $time);
								$action = $survey_action['action'];
								if ( isset($survey_action_names_jp[$action])) {
									$action = $survey_action_names_jp[$action];
								}
								$div = 
						'<div class="journey_item_frame_flex" style="order: 1;">
							<div class="journey_item_time_flex">' . $log_time_minute . '</div>
							<div class="journey_item_text_flex">SURVEY「' . $survey_action['survey_name'] . '」' . $action . '</div>
						</div>';
								echo($div);
							}
						} else if ($log_type == 'INQUIRY'){
							unset($followed_log["sns_id"]);
							foreach($followed_log as $access_time => $inquiry_action) {
								$time = strtotime($access_time);
								$log_time_minute = date("Y/m/d H:i", $time);
								$action = $inquiry_action['action'];
								if ( isset($inquiry_action_names_jp[$action])) {
									$action = $inquiry_action_names_jp[$action];
								}
								$div = 
						'<div class="journey_item_frame_flex" style="order: 1;">
							<div class="journey_item_time_flex">' . $log_time_minute . '</div>
							<div class="journey_item_text_flex">INQUIRY「' . $inquiry_action['inquiry_name'] . '」' . $action . '</div>
						</div>';
								echo($div);
							}
						} else if ($log_type == 'FAQ'){
							unset($followed_log["sns_id"]);
							foreach($followed_log as $search_date => $faq_content) {
								$time = strtotime($search_date);
								$log_time_minute = date("Y/m/d H:i", $time);
								$div = 
						'<div class="journey_item_frame_flex" style="order: 1;">
							<div class="journey_item_time_flex">'.$log_time_minute.'</div>
							<div class="journey_item_text_flex">「'.$faq_content['content'].'」'.$faq_action_names_jp[$faq_content['search_div']].'</div>
						</div>';
								echo($div);
							}
						}
						$div = 
					'</div>
				</div>';
						echo($div);
						$follow_index++;
					}
				?>
			</div>
		</div>	
	</div>	
</div>
