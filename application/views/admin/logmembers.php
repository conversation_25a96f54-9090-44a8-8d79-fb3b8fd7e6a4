<!-- BEGIN PAGE HEADER-->
<!-- BEGIN PAGE HEAD -->
<style>
	.selectmember:link{
		text-decoration: none;
		color: #333;					
	}
	.selectmember:hover{
		text-decoration: none;
		color: #333;					
	}				
	.selectmember:visited{
		text-decoration: none;
		color: #333;					
	}				
	
	.selected {
		font-weight: bolder;
	}
</style>

<input type="hidden" name="act" id="act" value="" />
<input type="hidden" name="member_id" id="member_id" value="" />
<input type="hidden" name="bot_id" id="bot_id" value="" />
<input type="hidden" name="selected_user_attrs" id="selected_user_attrs" value="">

<?php if ($_action == 'pushmsgmember') echo $logmenu ?>		

<div class="content-container white border">
	<div class="section-container">
		<div class="form-group">
			<label class="control-label col-md-1" style="width: 120px;padding-left:15px; padding-right:10px;"><?php echo __('admin.logmembers.label.search_from_basic_information') ?></label>				
			<div class="col-md-1" style="width: 90px;">
				<?php echo Form::select('lang_cd', $lang_cd_list, $post['lang_cd'], array('id'=>'lang_cd','class'=>'form-control','style' => 'width:80px'))?>
			</div>
			<div class="col-md-1" style="width: 120px;">
				<?php echo Form::select('sns_type_cd', $sns_type_cd_list, $post['sns_type_cd'], array('id'=>'sns_type_cd','class'=>'form-control','style' => 'width:110px'))?>
			</div>
			
			<?php echo $botcond ?>		

			<div class="col-md-3" style="width: 190px;">
				<input name="member_name" placeholder="<?php echo __('admin.common.label.name') ?>" value="<?php echo($post['member_name'])?>" class="form-control" style="width:180px;"/>
			</div>
			<div class="col-md-2" style="width: 120px;">
				<input name="member_no" placeholder="<?php echo __('admin.common.label.user') . ' No' ?>" class="form-control"/>
			</div>									
		</div>					
		<div class="form-group flex">
			<label class="control-label col-md-1" style="width: 120px; padding-left:15px; padding-right:10px;"><?php echo __('admin.logmembers.label.search_from_chat_history') ?></label>								
			<div class="col-md-5 flex mr10" style="width: 250px">
				<input name="start_date" id="start_date" value="<?php echo($post['start_date'])?>" style="float:left;" class="talkappi-datepicker" type="text"/>
				<input name="end_date" id="end_date" value="<?php echo($post['end_date'])?>" style="float:left; margin-left:10px;" class="talkappi-datepicker" type="text"/>
			</div>
			<div class="col-md-5">
				<input name="member_log" placeholder="<?php echo __('admin.common.label.chat_contents') ?>" value="<?php echo($post['member_log'])?>" class="form-control" />
			</div>
		</div>
		<div class="form-group">
			<label class="control-label col-md-1" style="width: 120px;padding-left:15px; padding-right:10px;"><?php echo __('admin.logmembers.label.search_from_user_attributes') ?></label>		
			<div class="col-md-5">
				<?php echo Form::select('user_attr_cd[]', $all_user_attrs, $user_attrs, array('id'=>'user_attr_multiple','class'=>'form-control','multiple'=>'multiple')); ?>
			</div>
		</div>
		<div class="form-group flex" style="align-items:baseline;">
			<label class="control-label col-md-1" style="width: 120px;padding-left:15px; padding-right:10px;"><?php echo __('admin.logmembers.label.search_from_manned_response_information') ?></label>		
			<div class="col-md-2" style="width: 180px;">
				<?php 
				if (count($tags) > 0) {
					echo Form::select('member_tag', $tags, $post['member_tag'], array('id'=>'member_tag','class'=>'form-control','style' => 'width:160px'));
				}
				else {
					echo ('<input name="member_tag" placeholder="名前" value="' . $post['member_tag'] . '" class="form-control" style="width:80px;"/>');												
				}
				?>
			</div>
			<div class="col-md-3" style="width: 240px;">
				<input name="member_remark" placeholder="<?php echo __('admin.common.label.note') ?>" value="<?php echo($post['member_remark'])?>" class="form-control" />
			</div>
			<div class="col-md-3 flex"><?php echo __('admin.common.label.spam_display') ?>
				<div class="ml10 talkappi-switch js-spam" data-name="spam_user" data-value="<?php echo($post['spam_user'])?>"></div>
			</div>
			<div class="col-md-1">
				<button type="submit" class="btn-smaller btn-yellow js-search"><i class="fa fa-search mr10"></i><?php echo __('admin.common.button.search') ?></button>
			</div>			
		</div>

		<?php if ($_action == 'pushmsgmember') { ?>
		<div class="form-group">		
			<div class="col-md-3">
			<button type="button" id="selectButton" class="btn green">
			全選択</button>										
			<button type="button" id="clearButton" class="btn grey">
			クリア</button></div>
		</div>
		<?php }?>																							

	<table class="table table-striped table-bordered table-hover js-data-table">
		<thead>
			<tr>
				<th style="width: 100px;"><?php echo __('admin.common.label.username') ?></th>
				<th><?php echo __('admin.common.label.sns') ?></th>
				<th><?php echo __('admin.common.label.member_id') ?></th>
				<th style="width: 160px;"><?php echo __('admin.common.label.last_conversation_date') ?></th>
				<th><?php if($post['member_log'] == '' ||$_action == 'logmembers') {
						echo __('admin.common.label.number_of_conversations');
					} else {
						echo __('admin.common.label.conversation');
					}
				?></th>
				<th><?php echo __('admin.common.label.attribute_information') ?></th>
				<th><?php echo __('admin.common.label.note') ?></th>																
			</tr>
		</thead>
		<tbody>							
		<?php
		if ($members != NULL) foreach ($members as $member) {
			$is_tester = 'badge badge-success';
			if ($member['is_tester'] == 1) $is_tester = 'badge badge-warning';
		?>	
		<tr class="gradeX odd" role="row">
			<td>
				<a href="javascript:void(0);" class="selectmember <?php if (key_exists($member['member_id'], $select_members)) echo(" selected");?>" member_id="<?php echo($member['member_id'])?>">														
					<?php 
					if ($member['name']!= '') {
						echo($member['name']);
					}
					else if ($member['sns_type_cd']== 'wb') {
						if ($member['is_tester'] == 1) {
							echo __('admin.common.label.web_tester');
						}
						else {
							echo __('admin.common.label.web_user');
						}
					}
					else {
						echo($member['last_name'] . ' ' . $member['first_name']);
					}
					if ($_user->role_cd == '99' || $_user->role_cd == '01' || $_user->role_cd == '07' || $_user->role_cd == '09')
					echo ('</br><label class="badge badge-info js-memberinfo" style="cursor:pointer;" data-member-id="' . $member['member_id'] .'">' . __('admin.common.label.journey')  . '</label>');
					?>
					</a>
			</td>
			<td>
				<img src="/assets/common/images/chat_<?php echo $member['lang_cd']?>.png" style="margin:5px;width:24px;"/>
                <img src="/assets/common/images/icon_<?php echo $member['sns_type_cd']?>.png" style="margin:5px;width:24px;"/>
				<?php 
					if($member['is_blocked'] === '0') {
						echo ('<label class="btn round spam-user js-block-user" member_id="' . $member['member_id'] .'">' . __('admin.common.label.spam_user') . '</label>');
					}
					else {
						echo ('<label class="btn round spam-user block js-block-user"  member_id="' . $member['member_id'] .'">' . __('admin.common.label.spam_user') . '</label>');
					}
				?>
			</td>
			<td>
				<?php echo($member['member_id'])?>
			</td>
			<td>
				<?php echo date('Y-m-d H:i:s', strtotime($member['last_talk_date']))?>
				<?php if ($_action == 'logmembers' || true) {?>
				<br><a class="pop_adminchat" <?php if ($member['is_tester'] == 1) echo('title="' .  __('admin.common.label.web_tester')  .'"');?> member_id=<?php echo $member['member_id'] ?> bot_id=<?php echo $member['bot_id'] ?> ><span class="<?php echo($is_tester)?>" style="margin: 5px;" ><?php echo __('admin.common.button.showlog') ?></span> </a>
				<?php 
				if ($member['sns_type_cd']== 'fb') {
					$now = date("Y-m-d H:i:s", strtotime("-24 hours"));
					if ($member['last_talk_date'] < $now) {
						echo('<span class="badge badge-warning">24時間以上</span>');
					}
				}
				}?>
				<?php if ($_action == 'logmembers') {?>
				<a class="js-delete-log" member_id=<?php echo $member['member_id'] ?> bot_id=<?php echo $member['bot_id'] ?> ><span class="badge badge-danger" style="margin: 5px;" ><?php echo __('admin.common.button.deletelog') ?></span> </a>
				<?php } ?>
			</td>
			<td>
				<?php 
				if($post['member_log'] == '' || $_action == 'logmembers') {
					echo($member['logs']);
				}
				else {
					echo($member['member_msg']);
				}
				?>
			</td>
			<td>
			<?php echo($member['attr_name']); ?>
			</td>
			<td>
			<?php echo($member['remark']); ?>
			</td>
		</tr>
		<?php } ?>
		</tbody>
	</table>
</div>
</div>