<!-- BEGIN PAGE HEADER-->
<!-- BEGIN PAGE HEAD -->
<div class="page-head">
    <!-- BEGIN PAGE TITLE -->
    <div class="page-title">
        <h1>コンテンツアクセス状況<small></small></h1>
    </div>
    <!-- <PERSON>ND PAGE TITLE -->
</div>
<!-- END PAGE HEAD -->
<!-- END PAGE HEADER-->

<div class="content-container light-gray search-conditions" style="margin-bottom: 32px;">
	<label class="control-label" style="padding-left:15px; padding-right:15px;">
		<?php echo __('admin.common.label.period') ?>
	</label>
	<div>
		<input name="start_date" id="start_date" value="<?php echo ($start_date) ?>"
			style="float:left;height: 28px;"
			class="form-control form-control-inline input-small date-picker" size="16"
			data-date-format="yyyy-mm-dd" type="text" />
		<input name="end_date" id="end_date" value="<?php echo ($end_date) ?>"
			style="float:left; margin-left:10px;height: 28px;"
			class="form-control form-control-inline input-small date-picker" size="16"
			data-date-format="yyyy-mm-dd" type="text" />
	</div>
	<label class="control-label">
		<?php echo __('admin.common.label.markup_lang') ?>
	</label>
	<div>
		<?php echo Form::select('lang_cd', $lang_cd_array, $lang_cd, array('id' => 'lang_cd', 'class' => 'form-control')) ?>
	</div>
	<?php echo $botcond ?>
	<div class="flex ml20">
		<button type="button" id="searchButton" class="btn-smaller btn-yellow"
			disabled="disabled">
			<i class="fa fa-search mr0"></i>
			<?php echo __('admin.common.button.search') ?>
		</button>
		<button type="button" data-tableId="sample"
			data-title="<?php echo $csv_file_name ?>利用状況統計"
			class="btn-smaller btn-white exportCsv">
			<span class="icon-export"></span>
			<?php echo __('admin.common.button.csv_export') ?>
		</button>
	</div>
</div>

<!-- BEGIN PAGE CONTENT-->
<div class="row">
    <div class="col-md-12">
        <!-- Page Content -->
        <div id="page-wrapper">
            <div class="top-nav font-standard">
                <?php echo $reportmenu ?>
                <div class="edit-container">
                    <div class="settings-container">
                        <input type="hidden" name="ref_div" value="<?php echo $ref_div ?>" />
                        <table class="table table-striped table-bordered table-hover js-data-table" id="sample">
                            <thead>
                                <?php
								$output = '';
								if (empty($ref_type)) {
									$output .= '<tr>';
									if ($ref_div == 6 || $bots_product_ref == 1) {
										$output .= '<th>' . htmlspecialchars(__('admin.common.label.facility')) . '</th>';
									}
									if ($bots_product_ref == 0) {
										$output .= '<th>' . htmlspecialchars(__('admin.common.label.name')) . '</th>';
										$output .= '<th style="display: none;">' . htmlspecialchars(__('admin.common.label.class')) . '</th>';
									}
									$output .= '</tr>';
								} else {
									$show_exists = array_key_exists('SHOW', $ref_type);
									$colspan = $show_exists ? count($ref_type) - (array_search('SHOW', array_keys($ref_type)) + 1) : count($ref_type);
									$output .= '<tr>';
									if ($ref_div == 6 || $bots_product_ref == 1) {
										$output .= '<th rowspan="' . ($colspan > 0 ? '2' : '1') . '" style="vertical-align: middle;">' . htmlspecialchars(__('admin.common.label.facility')) . '</th>';
									}
									if ($bots_product_ref == 0) {
										$output .= '<th rowspan="' . ($colspan > 0 ? '2' : '1') . '" style="vertical-align: middle;">' . htmlspecialchars(__('admin.common.label.name')) . '</th>';
										$output .= '<th style="display: none; vertical-align: middle;" rowspan="' . ($colspan > 0 ? '2' : '1') . '">' . htmlspecialchars(__('admin.common.label.class')) . '</th>';
									}
									foreach ($ref_type as $k => $v) {
										if ($k == 'SHOW') {
											$output .= '<th rowspan="' . ($colspan > 0 ? '2' : '1') . '" style="width: 80px; vertical-align: middle;">' . htmlspecialchars($v) . '</th>';
										} else {
											$output .= '<th colspan="' . $colspan . '" style="width: 80px; border-bottom: 1px solid #ddd;">' . htmlspecialchars(__('admin.common.label.count_click_button')) . '</th>';
											break;
										}
									}
									$output .= '</tr>';
									if ($colspan > 0) {
										$output .= '<tr>';
										foreach ($ref_type as $k => $v) {
											if ($k === 'SHOW') {
												continue;
											}
											$output .= '<th style="width: 80px; vertical-align: middle; border-right: 1px solid #ddd;">' . htmlspecialchars($v) . '</th>';
										}
										$output .= '</tr>';
									}
								}
								echo $output;
								?>
                            </thead>

                            <tbody>
                                <?php
								foreach ($results as $k => $v) {
									echo ('<tr class="gradeX odd" role="row">');
									if ($ref_div == 6 || $bots_product_ref == 1) {
										echo ('<td class="sorting_1">' . $v['bot_name'] . '</td>');
									}
									if ($bots_product_ref == 0) {
										echo ('<td class="sorting_1">' . $v['item_name'] . '</td>');
										echo ('<td style="display:none;">');
										$class_cd = explode(' ', $v['class_cd']);
										foreach ($class_cd as $cd) {
											if (array_key_exists($cd, $code_div_dict))
												echo ($code_div_dict[$cd] . ' ');
										}
										echo ('</td>');
									}
									foreach ($ref_type as $m => $n) {
										echo ('<td class="right">');
										if (array_key_exists($m, $v))
											echo ($v[$m]);
										echo ('</td>');
									}
									echo ('</tr>');
								} ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <!-- /#page-wrapper -->
    </div>
</div>
<!-- END PAGE CONTENT-->