<div id="live-chat-container">
	<div class="chat-content-wrapper">
		<ul class="chat-content">
			<li class="in" style="list-style:none;">
				<div class="">
					<span class="arrow"> </span>
					<a href="javascript:;" class="name"> </a>
					<span class="datetime"> </span>
					<?php 
					if (!isset($messages)) return;
					foreach($messages as $message) {
						$msg_type_cd = $message['msg_type_cd'];
						if ($msg_type_cd == 'txt' || $msg_type_cd == 'mal' || $msg_type_cd == 'tpl') {
							echo('<span class="body" style="margin-bottom:10px;">' . nl2br($message['content']) . '</span>');
						}
						else if ($msg_type_cd == 'img') {
							echo('<span class="body" style="margin-bottom:10px;">' . '<img class="talkappi-img-circle" style="max-width:200px;" alt="" src="' . $message['content'] . '"></span>');
						}
						else if ($msg_type_cd == 'mov') {
							echo('<span class="body" style="margin-bottom:10px;">' . '<img class="talkappi-img-circle" style="max-width:200px;" alt="" src="' . $message['content'] . '"></span>');
						}
						else if ($msg_type_cd == 'rcm') {
							echo('<span class="body" style="margin-bottom:10px;">' . '<img class="talkappi-img-circle" style="max-width:200px;" alt="" src="' . $message['content'] . '"></span>');
						}
						else if ($msg_type_cd == 'btn' || $msg_type_cd == 'lst'  || $msg_type_cd == 'mnu') {
							$items = json_decode($message['content']);
							echo('<span class="body" style="line-height:32px;margin-bottom:10px;">');
							echo(nl2br($items[0]->title) . '<br/>');
							foreach($items as $item) {
								if ($item->content == '') continue;
								$item_type = "postback";
								if (isset($item->type)) {
									$item_type = $item->type;
								}
								echo('<a href="javascript:;" class="webchat-list" type="postback" lang_cd="' . $item->lang_cd . '"' . ' no="' . $item->no . '" title="' . $item->content. '" content="' . $item->content  . '">');
								if ($msg_type_cd == 'lst') {
									echo('<span class="badge badge-success" style="height:auto;min-height:18px;margin-right:10px;background-color:#e0e0e0;color:#333;font-size:20px;">' . $item->content. '</span></a>');
									echo('<br/>');
								}
								else {
									echo('<span class="badge badge-success" style="height:auto;min-height:18px;display:inline-block;margin-right:6px;background-color:#ffffff;color: #57606f;border: solid 1px #57606f;font-size:20px;white-space: pre;">' . $item->content. '</span></a>');
								}
							}
							echo('</span>');
						}
						else if ($msg_type_cd == 'car') {
							$items = json_decode($message['content']);
							echo('<div class="xscroll-wrapper">');
							foreach($items as $item) {
								echo('<div class="card">');
								echo('<a href="' . $item->url . '" target="blank"><img class="image" alt="" src="' . $item->msg_image . '" /></a>');
								echo('<div class="item-caption">' . nl2br($item->title) . '</div>');
								echo('<div class="item-description">' . nl2br($item->content) . '</div>');
								echo('<div class="item-button-horizontal">');
								if (array_key_exists($item->btn1_name, $buttons)) $item->btn1_name = $buttons[$item->btn1_name];
								if (array_key_exists($item->btn2_name, $buttons)) $item->btn2_name = $buttons[$item->btn2_name];
								if (array_key_exists($item->btn3_name, $buttons)) $item->btn3_name = $buttons[$item->btn3_name];
								if ($item->btn1_name!= '') echo('<a class="button" href="' . $item->btn1_url . '" target="blank">' . $item->btn1_name . '</a>');
								if ($item->btn2_name!= '') echo('<a class="button" href="' . $item->btn2_url . '" target="blank">' . $item->btn2_name . '</a>');
								if ($item->btn3_name!= '') echo('<a class="button" href="' . $item->btn3_url . '" target="blank">' . $item->btn3_name . '</a>');
								echo('</div>');
								echo('</div>');
							}
							echo('</div>');
						}
						echo("<br>");
					}
				?>
				</div>
			</li>
		</ul>
	</div>
</div>
