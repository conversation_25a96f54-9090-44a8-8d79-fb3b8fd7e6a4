<style>
.portlet-body {
overflow-x:auto;
}
td {
text-align:right;
}
thead {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 2;
}
table {
    table-layout: fixed;
}
</style>
			<!-- BEGIN PAGE HEADER-->
			<!-- BEGIN PAGE HEAD -->
			<div class="page-head">
				<!-- BEGIN PAGE TITLE -->
				<div class="page-title">
					<h1>利用状況レポート<small></small></h1>
				</div>
				<!-- END PAGE TITLE -->
			</div>
			<!-- END PAGE HEAD -->
			<!-- END PAGE HEADER-->
			<!-- BEGIN PAGE CONTENT-->
			<div class="row">
				<div class="col-md-12">
			        <!-- Page Content -->
			        <div id="page-wrapper">
			        <div class="portlet light">			        
					<?php echo $reportmenu ?>
						<div class="portlet box">
							<div class="portlet-body">
								<div class="form-body">		
									<div class="form-group">
										<label class="control-label col-md-1" style="padding-left:15px; padding-right:15px;"><?php echo __('admin.common.label.period') ?></label>
										<div class="col-md-3" style="width: 265px;">
											<input name="start_date" id="start_date" value="<?php echo($start_date)?>" style="float:left;" class="form-control form-control-inline input-small date-picker" size="16" data-date-format="yyyy-mm-dd"  type="text"/>
											<input name="end_date" id="end_date" value="<?php echo($end_date)?>" style="float:left; margin-left:10px;" class="form-control form-control-inline input-small date-picker" size="16" data-date-format="yyyy-mm-dd"  type="text"/>
										</div>								
										<?php echo $botcond ?>
										<label class="control-label col-md-1" style="width: 100px;"><?php echo __('admin.common.label.display_zero') ?></label>
										<div class="col-md-2">
											<input type="checkbox" id="utf-8" name="zero_flg" <?php if ($zero=='0') echo('checked')?> value="1" class="make-switch" data-on-color="success" data-off-color="warning">
										</div>							
										<div class="col-md-1">
											<button type="button" id="searchButton" class="btn yellow" disabled="disabled">
											<i class="fa fa-search mr10"></i><?php echo __('admin.common.button.search') ?></button>
										</div>	
										<div class="col-md-1" style="margin-left: 20px;">
											<button type="button" id="csvButton" class="btn green">
												<?php echo __('admin.common.button.csv_export') ?>
											</button>
										</div>
										<div class="col-md-1" style="display:none;">
											<button type="button" id="uaButton" class="btn yellow">
											UA出力</button>
										</div>								
									</div>
								</div>							

							<table class="table table-striped table-bordered table-hover">
							<thead>
							<tr>
								<th style="width:100px;text-align:center" rowspan="2">
								</th>														
							<?php 
							$sns_array = explode(',', $_bot->sns_cd);
							$sns_count = count($sns_array);
							$mobile_width = 0;
							if (in_array('wb', $sns_array)) {
								$mobile_width = 40;
							}
							
							foreach($scene as $k=>$v) {
								echo('<th style="width:' . $sns_count * 80 . 'px;text-align:center" colspan="' . $sns_count . '">');
								echo($v);
								echo('</th>');
							}
							?>
								<th style="width:80px;text-align:center"><?php echo __('admin.common.label.total') ?>
								</th>														
							</tr>						
							</thead>							
							<tbody>	
							<tr>
							<?php 
							echo('<tr class="gradeX odd" role="row">');
							echo('<td>');
							echo('</td>');
							foreach($scene as $k=>$v) {
								foreach($sns_array as $sns_type_cd) {
									if ($sns_type_cd == 'wb') {
										echo('<th style="width:80px;text-align:center">');
										echo($_codes['08'][$sns_type_cd] . '<small>' . __('admin.common.label.mobile_bracket') .  '</small>');
									}
									else {
										echo('<th style="width:80px;text-align:center">');
										echo($_codes['08'][$sns_type_cd]);
									}
									echo('</th>');
								}
							}?>	
							<td></td>																																			
							</tr>	
							<?php 
							echo('<tr class="gradeX odd" role="row">');
							echo('<td>');
							echo __('admin.common.label.total');
							echo('</td>');
							$sum = 0;
							foreach($scene as $k=>$v) {
								foreach($sns_array as $sns_type_cd) {
									echo('<td>');
									if ($sns_type_cd == 'wb') {
										$day_key = $k . $sns_type_cd . 'pc';
										$day_key_m =  $k . $sns_type_cd . 'mobile';
										$total = 0;
										if (array_key_exists($day_key, $month_result)) $total = $month_result[$day_key];
										if (array_key_exists($day_key_m, $month_result)) $total = $total + $month_result[$day_key_m];
										if ($total > 0) {
											echo($total);
										}
										else {
											if ($zero == '0') echo($zero);
										}
										if (array_key_exists($day_key_m, $month_result)) echo("(" . $month_result[$day_key_m] . ")");
										$sum = $sum + $total;
									}
									else {
										$day_key = $k . $sns_type_cd;
										if (array_key_exists($day_key, $month_result)) {
											echo($month_result[$day_key]);
											$sum = $sum + $month_result[$day_key];
										}
										else {
											if ($zero == '0') echo($zero);
										}
									}
									echo('</td>');
								}
							}
							echo('<td>');
							if ($sum > 0) echo($sum);
							echo('</td>');
							echo('</tr>');
							?>							
							<?php 
							for($i=0; ;$i++) {
								$cur_day = date("Y-m-d",strtotime("+" . $i ." day",strtotime($start_date)));
								if ($cur_day > $end_date) break;
								echo('<tr class="gradeX odd" role="row">');
								echo('<td>');
								echo($cur_day);
								echo('</td>');
								$sum = 0;
								foreach($scene as $k=>$v) {
									foreach($sns_array as $sns_type_cd) {
										echo('<td>');
										if ($sns_type_cd == 'wb') {
											$day_key = $cur_day . $k . $sns_type_cd . 'pc';
											$day_key_m = $cur_day . $k . 'wb' . 'mobile';
											$total = 0;
											if (array_key_exists($day_key, $day_result)) $total = $day_result[$day_key];
											if (array_key_exists($day_key_m, $day_result)) $total = $total + $day_result[$day_key_m];
											if ($total > 0) {
												echo($total);
											}
											else {
												if ($zero == '0') echo($zero);
											}
											if (array_key_exists($day_key_m, $day_result)) echo("(" . $day_result[$day_key_m] . ")");
											$sum = $sum + $total;
										}
										else {
											$day_key = $cur_day . $k . $sns_type_cd;
											if (array_key_exists($day_key, $day_result)) {
												echo($day_result[$day_key]);
												$sum = $sum + $day_result[$day_key];
											}
											else {
												if ($zero == '0') echo($zero);
											}
										}
										echo('</td>');
									}
								}
								echo('<td>');
								if ($sum > 0) echo($sum);
								echo('</td>');
								echo('</tr>');
							}
							echo('<tr class="gradeX odd" role="row">');
							echo('<td>');
							echo __('admin.common.label.total');
							echo('</td>');
							$sum = 0;
							foreach($scene as $k=>$v) {
								foreach($sns_array as $sns_type_cd) {
									echo('<td>');
									if ($sns_type_cd == 'wb') {
										$day_key = $k . $sns_type_cd . 'pc';
										$day_key_m =  $k . $sns_type_cd . 'mobile';
										$total = 0;
										if (array_key_exists($day_key, $month_result)) $total = $month_result[$day_key];
										if (array_key_exists($day_key_m, $month_result)) $total = $total + $month_result[$day_key_m];
										if ($total > 0) {
											echo($total);
										}
										else {
											if ($zero == '0') echo($zero);
										}
										if (array_key_exists($day_key_m, $month_result)) echo("(" . $month_result[$day_key_m] . ")");
										$sum = $sum + $total;
									}
									else {
										$day_key = $k . $sns_type_cd;
										if (array_key_exists($day_key, $month_result)) {
											echo($month_result[$day_key]);
											$sum = $sum + $month_result[$day_key];
										}
										else {
											if ($zero == '0') echo($zero);
										}
									}
									echo('</td>');
								}
							}
							echo('<td>');
							echo('</td>');
							echo('</tr>');
							?>
							</tbody>
							</table>
														
							</div>
						</div>
					</div>
			        </div>
			        <!-- /#page-wrapper -->					
				</div>
			</div>
			<!-- END PAGE CONTENT-->
