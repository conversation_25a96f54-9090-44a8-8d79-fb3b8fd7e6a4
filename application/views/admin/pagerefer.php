<input type="hidden" name='page_cd' value='<?php echo $page_cd; ?>'>
<nav class="top-nav">
    <ul>
        <li><a href="/admin/sitepage?id=<?php echo $page_cd ?>"><?php echo __('admin.common.label.setting') ?></a></li>
        <li><a href="/admin/sitepagedetail?id=<?php echo $page_cd ?>"><?php echo __('admin.site.setting') ?></a></li>
        <li class="active"><a href="/admin/pagerefer?id=<?php echo $page_cd ?>"><?php echo __('admin.site.refer') ?></a></li>
    </ul>
</nav>
<div class="content-container white border">
    <div class="form-body">
        <div class="form-group">
            <div class="col-md-2" style="max-width: 150px;">
                <?php
                $lang_cd_list_after = [];
                foreach ($lang_cd_list as $key => $value) {
                    if ($key == '') {
                        $lang_cd_list_after[$key] = __('admin.common.label.all_lang');
                    } else {
                        $lang_cd_list_after[$key] = $value;
                    }
                }
                ?>
                <?php echo Form::select('lang_cd', $lang_cd_list_after, $post['lang_cd'], array('id' => 'lang_cd', 'class' => 'form-control')) ?>
            </div>
            <div class="col-md-6" style="display: flex;align-items: center; max-width: 300px;">
                <input name="start_date" class="talkappi-datepicker" id="start_date" value="<?php echo (substr($post['start_date'], 0, 10)) ?>" />
                <p style="margin-right: 10px;">〜</p>
                <input name="end_date" class="talkappi-datepicker" id="end_date" value="<?php echo (substr($post['end_date'], 0, 10)) ?>" />
            </div>
            <div class="col-md-4">
                <button type="submit" class="btn-smaller btn-blue js-action-search"><span class="icon-filter"></span><?php echo __('admin.common.label.narrowdown'); ?></button>
            </div>
        </div>
    </div>
    <div id="pagerefer-tables"></div>
</div>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/1.12.4/jquery.min.js"></script>
<script type="text/javascript" src="/assets/common/react/pages/admin/pagerefer.bundle.js"></script>

<script type="text/javascript">
    var domainRefersData = <?php echo json_encode($domain_refers); ?>;
    var refersData = <?php echo json_encode($refers); ?>;
    var citiesData = <?php echo json_encode($cities); ?>;
    var langCd = <?php echo json_encode($lang_cd_admin); ?>;
    jQuery(document).ready(function($) {
        window.talkappi_setupPageReferPage({
            domainRefersData,
            refersData,
            citiesData,
            langCd,
        });
    });
</script>