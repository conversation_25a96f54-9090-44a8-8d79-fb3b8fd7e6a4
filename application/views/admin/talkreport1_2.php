			<!-- B<PERSON>IN PAGE HEADER-->
			<!-- BEGIN PAGE HEAD -->
			<div class="page-head">
				<!-- BEGIN PAGE TITLE -->
				<div class="page-title">
					<h1>利用状況レポート<small></small></h1>
				</div>
				<!-- END PAGE TITLE -->
			</div>
			<!-- END PAGE HEAD -->
			<!-- END PAGE HEADER-->
			<!-- BEGIN PAGE CONTENT-->
			<div class="content-container">
				<div class="flex-x-between">
					<div><a href="talkreport1"><?php echo __('admin.talkreport1.label.statistics') ?></a> > <?php echo __('admin.talkreport1.label.statistics.detail') ?></div>
				</div>
			</div>
			<div class="row">
				<div class="col-md-12">
			        <!-- Page Content -->
			        <div id="page-wrapper">
						<div class="portlet light">			        
							<div class="portlet box">
							<div style="padding:10px 10px;">
								<h4 style="font-family: HiraginoSans-W5;"><?php echo($msgs[0]['question']);?></h4>
							</div>
							<div style="padding: 0 10px;">
							    <?php echo('<a style="color: black;" class="link-animate" href="/admin/talknew?cd=' . $msgs[0]['intent_cd'] . '&lang=ja&sub_cd=' . $msgs[0]['sub_intent_cd'] . '">'); ?>
								    <button type="button" data-tableId="report1_2_table" class="answerButton btn mr10 pb2" style="float:left; border-radius:12px; height: 30px;background-color: #cff2d7;"><?php echo __('admin.talkreport1.label.edit_faq') ?></button>
								</a>
								<button type="button" data-tableId="report1_2_table" data-title="よく聞かれる質問十分不十分回答詳細" class="btn primary mr10 exportCsv" style="float:right; background-color: white; border: solid 1px #c8cace; color: #000;"><span class="icon-export"></span><?php echo __('admin.common.button.csv_export') ?></button>
							</div>	
							<div class="portlet-body" style="border-top: solid 1px #e3e5e8; margin-top: 45px;">	
								<table class="table table-bordered table-hover js-data-table" id="report1_2_table">
								<thead style="background-color: #f6f7f9;">
									<tr>
										<th><?php echo __('admin.talkreport1.label.channel') ?></th>
										<th><?php echo __('admin.talkreport1.label.name') ?></th>
										<th style="width: 108px;"><?php echo __('admin.talkreport1.label.language') ?></th>
										<th style="width: 292px;"><?php echo __('admin.talkreport1.label.date') ?></th>
										<th style="width: 109px;"><?php echo __('admin.talkreport1.label.satisfaction') ?></th>
										<th style=" width: 279px;"><?php echo __('admin.talkreport1.label.reasons_for_inadequate') ?></th>
									</tr>
								</thead>
								<tbody>
									<?php
										if (!isset($post['survey'])) $post['survey'] = 2;
										foreach ($msgs as $msg) {
											if ($msg['answer'] != $post['survey']) continue;
									?>	
									<tr class="gradeX odd " role="row">
										<td class="sorting_1">
											<?php 
											if ($msg['sns_type_cd']== 'fb') {
													echo('<img src="/assets/common/images/icon_fb.png" style="margin:5px;width:16px;"/>');
												}
												if ($msg['sns_type_cd']== 'ln') {
													echo('<img src="/assets/common/images/icon_ln.png" style="margin:5px;width:16px;"/>');
												}
												if ($msg['sns_type_cd']== 'wc') {
													echo('<img src="/assets/common/images/icon_wc.png" style="margin:5px;width:16px;"/>');
												}
												if ($msg['sns_type_cd']== 'wb') {
													echo('<img src="/assets/common/images/icon_wb.png" style="margin:5px;width:16px;"/>');
												}
											?>
										</td>
										<td class="sorting_1">
										<?php 
										if ($msg['sns_type_cd'] == 'fb' || $msg['sns_type_cd'] == 'ln' || $msg['sns_type_cd'] == 'wc') {
											echo($msg['last_name'] . ' ' . $msg['first_name']);
										}
										if ($msg['sns_type_cd'] == 'wb') {
											echo __('admin.push.web.user');
										}
										if ($_user->role_cd == '99' || $_user->role_cd == '01' || $_user->role_cd == '07' || $_user->role_cd == '09')
										echo ('</br><label class="badge badge-info js-memberinfo" style="cursor:pointer;" data-member-id="' . $msg['member_id'] .'">' . __('admin.common.label.journey')  . '</label>');
										?>
										</td>
										<td>
										<?php echo($codes['02'][$msg['lang_cd']])?>									
										</td>
										<td>
										<?php 
										  $date = $msg['survey_time'];
										  echo date("Y/m/d H:i", strtotime($date));
										?>
										<?php if ($faq == 0) {?>
										<a class="pop_adminchat" member_id=<?php echo $msg['member_id'] ?>><span class="badge" style="min-width: 72px;height: 24px;padding:6px 12px;color:black; background-color:#d3eeff; float: right;"><?php echo __('admin.common.label.historical_view');?></span> </a>
										<?php }?>
										</td>						
										<td style="text-align:center;">
											<?php
											if ($msg['answer'] == '1') {
												echo __('admin.talkreport1.label.adequate');
											}
											else if ($msg['answer'] == '2') {
												echo "<font color=\"RED\">" . __('admin.talkreport1.label.inadequate') . "</font>";
											}
											?>
										</td>						
										<td>
										<?php
										echo($msg['reason']);
										?>								
										</td>																										
									</tr>
									<?php } ?>
								</tbody>
								</table>
							</div>
							</div>
						</div>
			        </div>
			        <!-- /#page-wrapper -->					
				</div>
			</div>
			<!-- END PAGE CONTENT-->
