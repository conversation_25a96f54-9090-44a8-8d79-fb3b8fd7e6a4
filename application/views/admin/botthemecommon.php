<style>
	.section-wrapper .talkappi-section {
		margin-left: 0 !important;
	}
</style>

<input type="hidden" name="channels" id="channels" value="" />
<div class="setting-container">
	<div class="setting-header">ベース</div>
	<div class="lines-container">
		<div class="basic-label">CHATBOT起動時の表示内容</div>
		<div scene_name="<?php echo ($post['ref_scene_cd']) ?>">
			<div class="talkappi-skill-select" data-name="welcome_skill" data-value='<?php echo (is_array($post['welcome_skill'])) ? json_encode($post['welcome_skill']) : $post['welcome_skill'] ?>' data-mode="skill"></div>
		</div>
	</div>
	<div class="lines-container">
		<div class="basic-label">チャンネル表示の順番<span class="icon-detail" title="スマートフォンでチャットボットを開いたときに表示するチャンネルの順番を設定できます。ドラッグ & ドロップで順番の変更が可能です。"></span></div>
		<nav class="button-tab" style="padding:0;">
			<ul class="js-channels">
				<?php
				foreach ($post['channels'] as $k) {
					echo ('<li class="js-channel active" data-value="' . $k . '"><a href="javascript:void(0);">' . $_codes['08'][$k] . '</a></li>');
				}
				?>
			</ul>
		</nav>
	</div>
	<div class="lines-container">
		<div class="basic-label">WebChat自動リセット</div>
		<div class="talkappi-switch js-webchat_reset_flg" data-name="webchat_reset_flg" data-value="<?php echo ($post['webchat_reset_flg']) ?>"></div>
		<input name="webchat_auto_reset_intval" style="display:none;margin-left:20px;" class="text-input js-webchat_auto_reset_intval" type="number" maxlength="10" class="text-input" value="<?php echo ($post['webchat_auto_reset_intval']) ?>">
		<label style="display:none;margin-left:8px;" class="js-webchat_auto_reset_intval">秒後、チャットボット内の表示が自動でリセット</label>
	</div>
</div>
<div class="setting-container">
	<div class="setting-header">プレビュー</div>
	<?php
	foreach ($_bot_lang as $k => $v) {
		echo ('<div class="lines-container">');
		echo ('<div class="basic-label">' . $v . '</div>');
		$val = '';
		if (isset($post['url'][$k])) {
			$val = $post['url'][$k];
		}
		echo ('<input name="url_' . $k . '" id="label" type="text" class="text-input-longer" placeholder="https://example.com" value="' . $val . '">');
		echo ('<div class="col-md-4">');
		echo ('<button data-href="https://talkappi-preview.com/chatbot/?fid=' . $scene_cd . '&admin=0&lang=' . $k . '" type="button" class="js-url-open btn-smaller btn-white">プレビュー</button>');
		echo ('</div>');
		echo ('</div>');
	}
	?>
</div>

<div class="setting-container" style="border-bottom: none;">
	<div class="setting-header">QRコード設定</div>
	<div class="lines-container">
		<div class="basic-label">QRコード作成<span class="icon-detail" title="ONにして保存すると、QRコードが作成されます。"></span></div>
		<div class="talkappi-switch" data-name="qr_create_flg" data-value="0"></div>
	</div>
	<div class="lines-container">
		<div class="basic-label">QRコード</div>
		<?php
		$bot_snses = explode(',', $_bot->sns_cd);
		if (in_array('wb', $bot_snses)) {
			$qr_file = $scene_path . $scene_cd . "/wb-" . $scene_cd . ".png";
			if (file_exists($qr_file)) {
				echo ('<a href="' . $scene_url_path . $scene_cd . '/wb-t-' . $scene_cd . '.png" target="_blank"><button type="button" class="btn grey action mr10 qr-code-btn"><img class="icon-download" src="/assets/admin/css/img/icon-import-blue.svg">汎用コード</button></a>');
				echo ('<a href="' . $scene_url_path . $scene_cd . '/wb-' . $scene_cd . '.png" target="_blank"><button type="button" class="btn grey action mr10 qr-code-btn"><img class="icon-download" src="/assets/admin/css/img/icon-import-blue.svg">Web</button></a>');
			} else {
				echo ('<button type="button" style="cursor:initial" class="btn grey mr10 qr-code-btn"><img class="icon-download" src="/assets/admin/css/img/icon-import-grey.svg">汎用コード</button>');
				echo ('<button style="cursor:initial" type="button" class="btn grey mr10 qr-code-btn"><img class="icon-download" src="/assets/admin/css/img/icon-import-grey.svg">Web</button>');
			}
		}
		?>
		<?php
		$bot_snses = explode(',', $_bot->sns_cd);
		foreach ($bot_snses as $sns_type_cd) {
			if ($sns_type_cd != 'wb') {
				$path = $scene_cd;
				$qr_file = $scene_path . $scene_cd . "/$sns_type_cd-" . $scene_cd . ".png";
				if (file_exists($qr_file)) {
					echo ('<a href="' . $scene_url_path . $scene_cd . '/' . $sns_type_cd . '-' . $scene_cd . '.png" target="_blank"><button type="button" class="btn grey action mr10 qr-code-btn"><img class="icon-download" src="/assets/admin/css/img/icon-import-blue.svg">' . $_codes['16'][$sns_type_cd] . '</button></a>');
				} else {
					//echo ('<button type="button" class="btn grey mr10">' . $_codes['16'][$sns_type_cd] . '</button>');
					echo ('<button style="cursor:initial" type="button" class="btn grey mr10 qr-code-btn"><img class="icon-download" src="/assets/admin/css/img/icon-import-grey.svg">' . $_codes['16'][$sns_type_cd] . '</button>');
				}
			}
		}
		//VERY導線のトグルONの場合のみ、veryのQRコードを表示
		if ($scene_cd == $post['very_scene_cd']) {
			$qr_file = $scene_path . $scene_cd . "/vt-" . $scene_cd . ".png";
			if (file_exists($qr_file)) {
				echo ('<a href="' . $scene_url_path . $scene_cd . '/vt-' . $scene_cd . '.png" target="_blank"><button type="button" class="btn grey action mr10 qr-code-btn"><img class="icon-download" src="/assets/admin/css/img/icon-import-blue.svg">' . 'very' . '</button></a>');
			} else {
				echo ('<button style="cursor:initial" type="button" class="btn grey mr10 qr-code-btn"><img class="icon-download" src="/assets/admin/css/img/icon-import-grey.svg">' . 'very' . '</button>');
			}
		}
		?>
	</div>
</div>
<div class="setting-container" style="border-bottom: none;">
	<div class="setting-header">掲載設定</div>
	<div class="lines-container">
		<div class="basic-label">表示スタイル</div>
		<?php echo Form::select('bot_theme_cd', $bot_theme_cd, $post['bot_theme_cd'], array('id' => 'bot_theme_cd', 'class' => 'dropdown-container dropdown-shorter')); ?>
	</div>
	<div class="form-group" style="display:none;">
		<div class="basic-label"></div>
		<div class="col-md-5">
			<label class="control-label"><?php if ($post != NULL) echo ($post['scene_data']) ?></label>
		</div>
	</div>
</div>