<script>
const _paging = <?php echo json_encode($post['paging']) ?>;
</script>
<div class="content-container light-gray">
    <div class="form-group">
        <label class="control-label col-md-1" style="width: 80px;">
            <?php echo __('admin.itemlink.publication_period') ?>
        </label>
        <div class="col-md-4" style="width: 270px;">
            <input name="start_date" id="start_date" value="<?php echo ($post['start_date']) ?>"
                style="float:left;height: 28px;" class="form-control form-control-inline input-small date-picker"
                size="16" data-date-format="yyyy-mm-dd" type="text" />
            <input name="end_date" id="end_date" value="<?php echo ($post['end_date']) ?>"
                style="height: 28px; float:left; margin-left:10px;"
                class="form-control form-control-inline input-small date-picker" size="16" data-date-format="yyyy-mm-dd"
                type="text" />
        </div>
        <div class="col-md-2">
            <?php echo Form::select('item_div', $item_div_dict, $post['item_div'], array('id' => 'item_div', 'class' => 'form-control')) ?>
        </div>
        <div class="col-md-2">
            <?php echo Form::select('class_cd', $class, $post['class_cd'], array('id' => 'class_cd', 'class' => 'form-control')) ?>
        </div>
        <!-- エリア検索は一旦非表示 -->
        <div class="col-md-2" style="display:none;">
            <?php echo Form::select('area_cd', $area, $post['area_cd'], array('id' => 'area_cd', 'class' => 'form-control')) ?>
        </div>
        <div class="col-md-2">
            <?php echo Form::select('distance', $_codes['31'], $post['distance'], array('id' => 'distance', 'class' => 'form-control')) ?>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-md-1" style="width: 80px;">
            <?php echo __('admin.common.label.keyword') ?>
        </label>
        <div class="col-md-4">
            <input name="keyword" id="keyword" value="<?php echo ($post['keyword']) ?>" class="form-control" type="text"
                placeholder="" />
        </div>
        <label class="control-label col-md-1" style="width: fit-content; display:none;">
            <?php echo __('admin.itemlink.publication_display') ?>
        </label>
        <div class="col-md-2" style="display:none;">
            <div class="talkappi-radio" data-name="show_flg" data-source='{"1":"ON", "0":"OFF"}'
                data-value="<?php echo $post['show_flg'] ?>"></div>
        </div>

        <div class="col-md-1 ml20">
            <span class="btn-smaller btn-yellow" id="searchButton"><i class="fa fa-search mr10"></i>
                <?php echo __('admin.common.button.search') ?>
            </span>
        </div>
    </div>
</div>

<?php if ($post['item_div'] != 17): ?>
    <div class="content-container" style="padding-left: 0;">
        <div style="display: flex;justify-content: end;">
            <span class="btn-smaller btn-blue" id="linkAllButton">
                <span class="icon-add-white"></span>
                <?php echo __('admin.common.button.link_all') ?>
            </span>
            <span class="btn-smaller btn-red" id="unlinkAllButton">
                <span class="icon-minus-white"></span>
                <?php echo __('admin.common.button.unlink_all') ?>
            </span>
        </div>
    </div>
<?php else: ?>
    <div class="content-container" style="padding: 10px 0;"></div>
<?php endif; ?>

<!-- BEGIN PAGE CONTENT-->

<div class="content-container white border">
    <input type="hidden" id="lang_cd" name="lang_cd" value="<?php echo $lang_cd ?>" />
    <div class="portlet-body">
        <table class="table table-striped table-bordered table-hover js-data-table">
            <thead>
                <tr>
                    <th>
                        <?php echo __('admin.common.label.classification') ?>
                    </th>
                    <th style="width: 80px;">
                        <?php echo __('admin.common.label.provider') ?>
                    </th>
                    <th>
                        <?php echo __('admin.common.label.name') ?>
                    </th>
                    <th>
                        <?php echo __('admin.common.label.description') ?>
                    </th>
                    <th>
                        <?php echo __('admin.itemlink.distance') ?>
                    </th>
                    <th>
                        <?php echo __('admin.common.label.display_information') ?>
                    </th>
                </tr>
            </thead>
            <tbody>
                <?php
                foreach ($items as $item) {
                    ?>
                <tr class="gradeX odd" role="row">
                    <td>
                        <?php
                            $class_cd = explode(' ', $item['class_cd']);
                            foreach ($class_cd as $cd) {
                                if (array_key_exists($cd, $code_div_dict))
                                    echo ($code_div_dict[$cd] . '<br>');
                            }
                            ?>
                    </td>
                    <td>
                        <?php
                            if ($item['link_type_cd'] == 'as') {
                                echo ("ASOVIEW");
                            }
                            else if ($item['link_type_cd'] == "kl") {
                                echo ("KLOOK");
                            }
                            else if ($item['link_type_cd']  == "vy") {
                                echo ("VERY");
                            }
                            else if ($item['link_type_cd']  == "ta") {
                                echo (__('admin.marketlink.tabelog'));
                            }
                            ?>
                    </td>
                    <td>
                        <?php echo ($item['item_cd']) ?><br />
                        <!--<a href="/admin/item?id=<?php echo ($item['item_id']) ?>"> -->
                            <?php if ($item['item_name'] == NULL)
                                    echo ("未命名");
                                else
                                    echo ($item['item_name']) ?>
                        <!--</a> -->
                    </td>
                    <td>
                        <?php echo ($item['description']) ?>
                    </td>
                    <td style="text-align:right;">
                        <?php
                            if ($item['distance'] != null) {
                                if ($item['distance'] > 10) {
                                    echo round($item['distance']);
                                } else if ($item['distance'] > 1) {
                                    echo round($item['distance'], 1);
                                } else {
                                    echo round($item['distance'], 3);
                                }
                            }
                        ?>
                    </td>
                    <td style="text-align:left;line-height:26px;">
                        <?php
                            if (isset($link_items[$item['item_id']])) {
                                /*
                                if ($link_items[$item['item_id']]['sort_no1'] == 0) {
                                    echo ('<a href="/admin/itemdisplay?id=' . $item['item_id'] . '"><span class="label label-warning">');
                                } else {
                                    echo ('<a href="/admin/itemdisplay?id=' . $item['item_id'] . '"><span class="label label-success">');
                                }
                                echo ($link_items[$item['item_id']]['sort_no1']);
                                echo ('</span></a>');
                                if ($link_items[$item['item_id']]['recommend'] == 1)
                                    echo ('<i class="fas fa-thumbs-up" style="float:right;color: #fecb81"></i>');
                                echo ('<br>');
                                */
                                echo ('<a href="javascript:void();" class="unlink" sid="' . $item['item_id'] . '" item_div="' . $item['item_div'] . '"><span class="btn btn-red" style="color: white; border-radius: 4px; padding: 3px 6px;"><span class="icon-minus-white"></span>' . __('admin.common.label.unlink') . '</span>');
                            } else {
                                echo ('<a href="javascript:void();" class="link" sid="' . $item['item_id'] . '" item_div="' . $item['item_div'] . '"><span class="btn btn-blue" style="color: white; border-radius: 4px; padding: 3px 6px;"><span class="icon-add-white"></span>' . __('admin.common.label.link') . '</span>');
                            }

                            ?>
                    </td>
                </tr>
                <?php } ?>
            </tbody>
        </table>
    </div>
</div>
<!-- END PAGE CONTENT-->