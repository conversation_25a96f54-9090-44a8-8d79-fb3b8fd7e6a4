<input type="hidden" name="msg_cd" id="msg_cd" value="" />
<input type="hidden" name="msg_id" id="msg_id" value="" />
<input type="hidden" name="act" id="act" value="" />

<div class="content-container light-gray search-conditions">
    <label class="control-label">
        <?php echo __('admin.common.label.classification') ?>
    </label>
    <div>
        <?php echo Form::select('msg_class_cd', $msg_class, $post['msg_class_cd'], array('id' => 'msg_class_cd', 'class' => 'form-control')) ?>
    </div>
    <label class="control-label">
        <?php echo __('admin.common.label.content') ?>
    </label>
    <div style="width:250px;">
        <input name="keyword" id="keyword" value="<?php echo ($post['keyword']) ?>" class="form-control" type="text"
            placeholder="" />
    </div>
    <label class="control-label">
        <?php echo __('admin.common.label.uncustomized') ?>
    </label>
    <div class="talkappi-radio" data-name="show_uncustomized"
        data-value='<?php echo $post['show_uncustomized'] ? $post['show_uncustomized'] : "0" ?>'
        data-source='{"0":"<?php echo __('admin.common.label.undisplay'); ?>", "1":"<?php echo __('admin.common.label.display'); ?>"}'>
    </div>
    <div class="col-md-1">
        <span class="btn-smaller btn-yellow js-search"><i class="fa fa-search mr10"></i>
            <?php echo __('admin.common.button.search') ?>
        </span>
    </div>
</div>

<div class="content-container" style="padding-left: 0;">
    <div style="display: flex;justify-content: end;">
        <span class="btn-smaller btn-blue js-new">
            <span class="icon-add-white"></span>
            <?php echo __('admin.common.button.create_new') ?>
        </span>
    </div>
</div>

<?php echo $msgmenu ?>

<div class="content-container white border">
    <table class="table table-striped table-bordered table-hover js-data-table">
        <thead>
            <tr>
                <th>
                    <?php echo __('admin.common.label.type') ?>
                </th>
                <th>
                    <?php echo __('admin.common.label.classification') ?>
                </th>
                <th>
                    <?php echo __('admin.common.label.name') ?>
                </th>
                <?php if ($_user->role_cd == '99') { ?>
                <th>
                    <?php echo __('admin.common.label.code') ?>
                </th>
                <?php } ?>
                <th>
                    <?php echo __('admin.common.label.last_update') ?>
                </th>
                <th style="width:100px;" <?php if ($_bot->bot_id != 0 && $msg_menu_cd == 'sys')
                    echo ('style="display:none;"') ?>>
                    <?php echo __('admin.common.label.operation') ?>
                </th>
            </tr>
        </thead>

        <tbody>
            <?php
            foreach ($msgs_all_dict as $msg) {
                $button = '';
                $style = '';
                if ($_bot->bot_id == 0) {
                    if ($_user->role_cd == '99') {
                        $button = '削除';
                        $style = 'red';
                    }
                } else {
                    if (array_key_exists($msg['msg_cd'], $msgs_self_dict)) {
                        if (array_key_exists($msg['msg_cd'], $msgs_parent_dict)) {
                            $button = 'デフォルトに戻す';
                            $style = 'yellow';
                        } else {
                            $button = '削除';
                            $style = 'red';
                        }
                    }
                }
                ?>
            <tr class="gradeX odd" role="row">
                <!-- タイプ -->
                <td>
                    <?php
                        if ($button == '削除') {
                            echo ('<span class="badge badge-info">' . __('admin.common.label.new_created') . ' </span><br>');
                        } else if ($button == 'デフォルトに戻す') {
                            echo ('<span class="badge badge-success">' . __('admin.common.label.customized') . ' </span><br>');
                        } else {
                            echo ('<span class="badge badge-warning">' . __('admin.common.label.uncustomized') . ' </span><br>');
                        }
                        if ($msg_type_cd == "") {
                            echo $msg_type_list[$msg['msg_type_cd']];
                        }
                        ?>
                </td>
                <!-- 分類 -->
                <td>
                    <?php if (array_key_exists($msg['msg_class_cd'], $msg_class_dict))
                            echo ($msg_class_dict[$msg['msg_class_cd']]) ?>
                </td>
                <!-- 名称 -->
                <td>
                    <a
                        class="link-animate"
                        href="/admin/<?php echo $msg_action_type ?>msgnew?id=<?php echo ($msg['msg_id']) ?>&lang_cd=<?php echo ($lang_cd) ?>">
                        <?php echo ($msg['msg_name']) ?>
                    </a>
                </td>
                <!-- コード -->
                <?php if ($_user->role_cd == '99') { ?>
                <td class=" sorting_1">
                    <?php
                                echo ($msg['msg_cd']);
                                if ($default_line_menu != '' && $msg['msg_cd'] == $default_line_menu) {
                                    echo ('<span class="badge badge-success" style="margin: 5px;">' . __('admin.common.label.default_line_menu') . '</span>');
                                }
                                ?>
                </td>
                <?php } ?>
                <!-- 最終更新 -->
                <td>
                    <?php if ($msg['upd_time'] != NULL) {
                            echo date('Y/m/d H:i', strtotime($msg['upd_time'])) . '<br/>' . $msg['name'];
                        } ?>
                </td>
                <td>
                    <div class="btn round image copy js-action js-copy" sid="<?php echo ($msg['msg_id']) ?>">
                        <?php echo __('admin.common.label.copy') ?>
                    </div>
                    <?php
                        if ($button == '削除') {
                            echo ('<div style="margin-top: 2px;" class="btn round image delete js-action js-delete" data-msgcd="' . $msg['msg_cd'] . '" data-classcd="' . $msg['msg_class_cd'] . '"  sid="' . $msg['msg_cd'] . '">' . __('admin.common.button.delete') . '</div>');
                            if ($grp_bot_id > 0 && $msg_menu_cd == 'sys') {
                                echo ('<div style="margin-top: 2px;" class="btn round image public js-action js-transfer" sid="' . $msg['msg_id'] . '">' . __('admin.common.label.transition_to_parents') . '</div>');
                            }
                        } else {
                            if ($button != '') {
                                if ($button == 'デフォルトに戻す') {
                                    echo ('<div style="margin-top: 2px;" class="btn round image edit js-action js-restore" sid="' . $msg['msg_cd'] . '">' . __('admin.common.label.reset_to_default') . '</div>');
                                } else {
                                    echo ('<div style="margin-top: 2px;" class="btn round image edit js-action js-restore" sid="' . $msg['msg_cd'] . '">' . $button . '</div>');
                                }

                            }

                        }
                        ?>
                </td>
            </tr>
            <?php } ?>
        </tbody>
    </table>

    <?php if (false && $_user->role_cd == '99') { ?>
    <div class=" form-group">
        <label class="control-label col-md-2">
            <?php echo __('admin.common.label.csv_file') ?>※
        </label>
        <div class="col-md-4">
            <div class="fileinput fileinput-new" data-provides="fileinput">
                <div class="input-group input-large">
                    <div class="form-control uneditable-input span3" data-trigger="fileinput">
                        <i class="fa fa-file fileinput-exists"></i>&nbsp; <span class="fileinput-filename">
                        </span>
                    </div>
                    <span class="input-group-addon btn default btn-file">
                        <span class="fileinput-new">
                            Select file </span>
                        <span class="fileinput-exists">
                            Change </span>
                        <input id="csvdata" type="file" name="csvdata">
                    </span>
                    <a href="#" class="input-group-addon btn red fileinput-exists" data-dismiss="fileinput">
                        Remove </a>
                </div>
            </div><br />
        </div>
        <div class="col-md-1">
            <a href="/docs/samples/message.csv" style="margin-left: 10px;">
                <?php echo __('admin.common.label.sample') ?>
            </a>
        </div>
        <div class="col-md-1">
            <button type="button" id="importButton" class="btn red">
                <?php echo __('admin.common.button.import') ?>※
            </button>
        </div>
    </div>
    <?php } ?>
</div>

<script type="text/javascript">
    const msg_action_type = <?php echo json_encode($msg_action_type); ?>;
</script>