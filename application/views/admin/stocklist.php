			<!-- BEGIN PAGE HEADER-->
			<!-- BEGIN PAGE HEAD -->
			<div class="page-head">
				<!-- BEGIN PAGE TITLE -->
				<div class="page-title">
					<h1>予約枠設定<small></small></h1>
				</div>
				<!-- END PAGE TITLE -->
			</div>
			<!-- END PAGE HEAD -->
			<!-- END PAGE HEADER-->
			<!-- BEGIN PAGE CONTENT-->
			<div class="row">
				<div class="col-md-12">
			        <!-- Page Content -->
			        <div id="page-wrapper">
			        <div class="portlet light">
						<div class="tabbable-line">
							<ul class="nav nav-tabs ">
								<li class="<?php if ($_action == 'stocklist') echo('active'); ?>">
									<a href="/admin/stocklist">
									枠定義一覧</a>
								</li>
								<li class="<?php if ($_action == 'stock') echo('active'); ?>">
									<a href="/admin/stock">
									新規枠定義</a>
								</li>
							</ul>
						</div>
							<input type="hidden" name="stock_id" id="stock_id" value="" />
							<div class="form-body">
							</div>							
						<div class="portlet box">
							<div class="portlet-body">
							<table class="table table-striped table-bordered table-hover js-data-table">
							<thead>
							<tr>
								<th style="width:50px;">
									コード
								</th>
								<th>
									名称
								</th>
								<th style="width:160px;">
									枠単位タイプ
								</th>
								<th style="width:160px;">
									
								</th>																					
							</tr>
							</thead>
							<tbody>
							<?php
								foreach ($stocklist as $stock) {
							?>	
							<tr class="gradeX odd" role="row">
								<td>
									<?php echo($stock->stock_cd)?>
								</td>
								<td>					
									<a href="/admin/stock?id=<?php echo($stock->stock_id)?>"><?php echo($stock->stock_name)?></a>				
								</td>	
								<td>				
									<?php echo($_codes["18"][$stock->stock_type_cd])?>					
								</td>							
								<td>								
									<button type="button" class="btn green action" onclick="top.location='/admin/stockday?id=<?php echo($stock->stock_id)?>'" >設定</button>
								<?php
									if ($_user->role_cd == "99") {
								?>									
									<button type="button" class="btn red action "  sid="<?php echo($stock->stock_id)?>">削除※</button>
								<?php }?>
								</td>
							</tr>
							<?php } ?>
							</tbody>
							</table>
							</div>
						</div>
					</div>
			        </div>
			        <!-- /#page-wrapper -->					
				</div>
			</div>
			<!-- END PAGE CONTENT-->
