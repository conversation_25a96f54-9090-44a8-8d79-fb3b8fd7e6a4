<?php echo $menu ?>
<div class="content-container white border">
	<input type="hidden" name="message" id="message" value="<?php echo ($message) ?>" />
	<input type="hidden" name="act" id="act" />

	<div class="section-container">
		<div class="form-body">
			<div class="form-group">
				<label class="control-label col-md-2"></label>
				<img class="system_link_logo" src="https://admin.talkappi.com/docs/manual/talkappi_lineworks.png"></img>
			</div>
			<div class="form-group">
				<label class="control-label col-md-2" style="font-weight: 600;">【概要説明】</label>
				<div class="col-md-8">
					<hr>
				</div>
			</div>
			<div class="form-group">
				<label class="control-label col-md-3"></label>
				<label class="col-md-8">LINE WORKS連携をすると、ユーザーがリクエストすると業務用端末のLINE WORKSに通知が来ます。</label>
			</div>
			<div class="form-group">
				<label class="control-label col-md-3"></label>
				<div class="col-md-9" style="display:flex;">
					<a href="https://admin.talkappi.com/docs/manual/talkappi_lineworks_manual.pdf" target="_blank"><div type="button" class="btn-smaller btn-blue">システム連携手順書</div></a>
				</div>
			</div>
			<div class="form-group">
				<label class="control-label col-md-2" style="font-weight: 600;">【基本設定】</label>
				<div class="col-md-8">
					<hr>
				</div>
			</div>
			<div class="form-group">
				<label class="control-label col-md-3">Client ID</label>
				<div class="col-md-8">
					<input name="client_id" id="client_id" type="text" class="form-control" value="<?php echo $client_id ?>" placeholder="LINE WORKS上作成したアプリのClient ID">
				</div>
			</div>
			<div class="form-group">
				<label class="control-label col-md-3">Client Secret	</label>
				<div class="col-md-8">
					<input name="client_secret" id="client_secret" type="text" class="form-control" value="<?php echo $client_secret ?>" placeholder="LINE WORKS上作成したアプリのClient Secret">
				</div>
			</div>
			<div class="form-group">
				<label class="control-label col-md-3">Service Account</label>
				<div class="col-md-8">
					<input name="service_account" id="service_account" type="text" class="form-control" value="<?php echo $service_account ?>" placeholder="LINE WORKS上作成したアプリのService Account">
				</div>
			</div>
			<div class="form-group">
				<label class="control-label col-md-3">Private key</label>
				<div class="col-md-6">
					<textarea name="private_key" id="private_key" rows="2" class="form-control" placeholder="LINE WORKS上発行したPirvate Keyファイルの内容"><?php echo $private_key ?></textarea>
				</div>
			</div>

			<div class="form-group">
				<label class="control-label col-md-2" style="font-weight: 600;">【全体通知先】</label>
				<div class="col-md-8">
					<hr>
				</div>
			</div>
			
			<div class="form-group">
				<label class="control-label col-md-3">Bot ID</label>
				<div class="col-md-8">
					<input name="bot_id" id="bot_id" type="text" class="form-control" value="<?php echo $bot_id ?>" placeholder="LINE WORKS上作成したBotのBot ID">
				</div>
			</div>
			<div class="form-group">
				<label class="control-label col-md-3">Channel No</label>
				<div class="col-md-8">
					<input name="channel_no" id="channel_no" type="text" class="form-control" value="<?php echo $channel_no ?>" placeholder="LINE WORKS上作成したトークルームのChannel No">
				</div>
			</div>

			<?php 

				echo('<div class="js-notify-rows">');

				for($i=0; $i<count($notify_channels); $i++) {
					echo('<div class="js-notify-row">');
					echo('<div class="form-group js-notify-row">');
					echo('<label class="control-label col-md-2" style="font-weight: 600;">【個別通知先】</label>');
					echo('<div class="col-md-8">');
					echo('<hr>');
					echo('</div>');
					echo('<div class="delete-icon js-notify-delete pointer col-md-2">');
					echo('<span class="icon-cancel-small"></span>');
					echo('</div>');
					echo('</div>');

					echo('<div class="form-group">');
					echo('<label class="control-label col-md-3">Intent CD</label>');
					echo('<div class="col-md-8">');
					echo('<input name="notify' . $i . '_intent" id="notify' . $i . '_intent" type="text" class="form-control" value="' . $notify_channels[$i]["intent"] . '" placeholder="リクエストやINQUIRYフォームの識別CD">');
					echo('</div>');
					echo('</div>');

					echo('<div class="form-group">');
					echo('<label class="control-label col-md-3">Bot ID</label>');
					echo('<div class="col-md-8">');
					echo('<input name="notify' . $i . '_bot_id" id="notify' . $i . '_bot_id" type="text" class="form-control" value="' . $notify_channels[$i]["bot_id"] . '" placeholder="LINE WORKS上作成したBotのBot ID">');
					echo('</div>');
					echo('</div>');

					echo('<div class="form-group">');
					echo('<label class="control-label col-md-3">Channel No</label>');
					echo('<div class="col-md-8">');
					echo('<input name="notify' . $i . '_channel_no" id="notify' . $i . '_channel_no" type="text" class="form-control" value="' . $notify_channels[$i]["channel_no"] . '" placeholder="LINE WORKS上作成したトークルームのChannel No">');
					echo('</div>');
					echo('</div>');
					echo('</div>');
				}

				echo('</div>');
			?>

			<div class="form-group">
				<label class="control-label col-md-1 main-label-display">
				</label>
				<div style="font-weight: 600;" class="col-md-11">
					<div class="image-action-group js-notify-section-add">
						<img src="./../assets/admin/css/img/icon-add.svg" width="12" height="12">
						<span style="font-weight: 600;"><?php echo __('admin.systemlink.notice_channel') . __('admin.verytop.add'); ?></span>
					</div>
					<ul class="js-notice-rows" style="padding-left:0"></ul>
				</div>
			</div>

			<div class="form-group">
				<label class="control-label col-md-2" style="font-weight: 600;"></label>
				<div class="col-md-8">
					<hr>
				</div>
			</div>
			<div class="form-actions">
				<div class="row">
					<div class="col-md-offset-3 col-md-10" style="display:flex;">
						<button type="button" id="saveButton" class="btn-larger btn-blue"><i class="fa fa-save mr10"></i>保存</button>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<!-- END PAGE CONTENT-->