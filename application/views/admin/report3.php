			<!-- B<PERSON>IN PAGE HEADER-->
			<!-- BEGIN PAGE HEAD -->
<style type='text/css'>
  /* Style to hide Dates / Months */
  .ui-datepicker-calendar,.ui-datepicker-month { display: none; }​
</style>
			<div class="page-head">
				<!-- BEGIN PAGE TITLE -->
				<div class="page-title">
					<h1>利用状況レポート<small></small></h1>
				</div>
				<!-- END PAGE TITLE -->
			</div>
			<!-- END PAGE HEAD -->
			<!-- END PAGE HEADER-->
			<!-- BEGIN PAGE CONTENT-->
			<div class="row">
				<div class="col-md-12">
			        <!-- Page Content -->
			        <div id="page-wrapper">
			        <div class="portlet light">
						<?php echo $reportmenu ?>
						<div class="portlet box">
							<div class="portlet-body">
								<div class="form-body">		
									<div class="form-group">
										<label class="control-label col-md-1"><?php echo __('admin.common.label.period') ?></label>
										<div class="col-md-8">
											<div class="input-group input-medium date date-picker" style="float:left;width:128px !important;" data-date="<?php echo(substr($start_date, 0, 7))?>" data-date-format="yyyy-mm" data-date-startview="months" data-date-viewmode="months" data-date-minviewmode="months">
												<input type="text" name="start_date" value="<?php echo(substr($start_date, 0, 7))?>" class="form-control" readonly>
												<span class="input-group-btn">
												<button class="btn default" type="button"><i class="fa fa-calendar"></i></button>
												</span>
											</div>
											<div class="input-group input-medium date date-picker" style="float:left; margin-left:10px;width:128px !important;" data-date="<?php echo(substr($end_date, 0, 7))?>" data-date-format="yyyy-mm" data-date-startview="months" data-date-viewmode="months" data-date-minviewmode="months">
												<input type="text" name="end_date" value="<?php echo(substr($end_date, 0, 7))?>" class="form-control" readonly>
												<span class="input-group-btn">
												<button class="btn default" type="button"><i class="fa fa-calendar"></i></button>
												</span>
											</div>																		
										</div>
										<label class="control-label col-md-1"><?php echo __('admin.common.label.display_zero') ?></label>
										<div class="col-md-2">
											<input type="checkbox" id="utf-8" name="zero_flg" <?php if ($zero=='0') echo('checked')?> value="1" class="make-switch" data-on-color="success" data-off-color="warning">
										</div>
									</div>
									<div class="form-group">
									<label class="control-label col-md-1" style="padding-left:15px; padding-right:15px;"><?php echo __('admin.common.label.user_flow') ?></label>
									<?php echo $botcond ?>	
									<div class="col-md-1">
										<button type="button" id="searchButton" class="btn yellow" disabled="disabled">
										<i class="fa fa-search mr10"></i><?php echo __('admin.common.button.search') ?></button>
									</div>
									<div class="col-md-2" style="margin-left: 20px;">
										<button type="button" data-tableId="sample_3" data-title="月別統計" class="btn green exportCsv">
											<?php echo __('admin.common.button.csv_export') ?>
										</button>
									</div>													
								</div>								
								</div>							
							<table class="table table-striped table-bordered table-hover" id="sample_3">
							<thead>
							<tr>
								<th>
									<?php echo __('admin.common.label.month') ?>
								</th>
								<th>
									<?php echo __('admin.report3.label.new_user_not_asked') ?>
								</th>
								<?php if (count($repeat) > 0) {?>
								<th>
									<?php echo __('admin.report3.label.number_of_repeat_user_by_month') ?>
								</th>
								<?php }?>
								<th>
									<?php echo __('admin.report3.label.new_conversation_number_of_user_utterances') ?>
								</th>
								<th>
									<?php echo __('admin.common.label.auto_response') ?>
								</th>
							</tr>
							</thead>

							<tbody>
							<?php
							for($i=0; ;$i++) {
								$cur_start_day = date("Y-m-d",strtotime("+" . $i ." month",strtotime($start_date)));
								if ($cur_start_day > $end_date) break;
								$month = substr($cur_start_day, 0, 7);
							?>	
							<tr class="gradeX odd" role="row">
								<td class="sorting_1" style="text-align: center;">
								<?php echo($month)?>
								</td>
								<td style="text-align: right;">
								<?php 
								if ($month <= date('Y-m')) {
									if (array_key_exists($month, $result['members'])) {
										echo($result['members'][$month]);
										$leave_num = $result['members'][$month] - $result_no_leave[$month];
										if ($leave_num > 0) {
											echo(' [' . $leave_num . ']');
										}
										else {
											echo(' [' . $zero . ']');
										}
									}
									else {
										echo($zero . '[' . $zero . ']');
									}
								}
								?>		
								</td>
								<?php 
								if (count($repeat) > 0) {
									echo('<td style="text-align: right;">');
									if ($month <= date('Y-m')) {
										if (array_key_exists($month, $repeat)) {
											if ($repeat[$month] > 0) {
												echo($repeat[$month]);
											}
											else {
												echo($zero . '[' . $zero . ']');
											}
										}
										else {
											echo($zero . '[' . $zero . ']');
										}
									}
									echo('</td>');
								}
								?>								
								<td style="text-align: right;">
								<?php 
								if ($month <= date('Y-m')) {
									if (array_key_exists($month, $result['all_logs'])) {
										echo($result['all_logs'][$month]);
										if ($result['logs'][$month] > 0) echo(' [' . $result['logs'][$month] . ']');
									}
									else {
										echo($zero . '[' . $zero . ']');
									}
								}
								?>					
								</td>			
								<td style="text-align: right;">
								<?php 
								if ($month <= date('Y-m')) {
									if (array_key_exists($month, $result['logs'])) {
										if ($result['logs'][$month] != 0) {
											if ($_bot_id == 1000013) {
												echo(round(($result['all_logs'][$month] - $result['un_logs'][$month]) / $result['all_logs'][$month] * 100, 2) . "%");
											}
											else {
												echo(round(($result['logs'][$month] - $result['un_logs'][$month]) / $result['logs'][$month] * 100, 2) . "%");
											}
										}
										else {
											echo($zero . "%");
										}
									}
									else {
										echo($zero . "%");
									}
								}
								?>
								</td>																												
							</tr>
							<?php } ?>
							</tbody>
							</table>
							</div>
						</div>
					</div>
			        </div>
			        <!-- /#page-wrapper -->					
				</div>
			</div>
			<!-- END PAGE CONTENT-->
