							<div class="top-nav">
								<ul>
									<li class="<?php if ($type=='item') echo('active')?>">
										<a class="func-menu" href="/admin/item?id=<?php echo($item_id)?>">
										<?php echo __('admin.itme.itemmeu.info') ?></a>
									</li>
									<?php if ($_bot_id == 9999999 && $item_id != NULL) {?>
									<li class="<?php if ($type=='itemcode') echo('active')?>">
										<a class="func-menu" href="/admin/itemcode?id=<?php echo($item_id)?>">
										<?php echo __('admin.itme.itemmeu.code') ?></a>
									</li>
									<?php }?>
									<?php
										$page_no = Session::instance()->get('items_page_no', NULL);
										if ($item_id != NULL) {

											if ($type=='itemdesc') {
												echo('<li class="active">');
											}
											else {
												echo('<li>');
											}
											echo('<a class="func-menu" href="/admin/itemdesc?id='. $item_id .'&lang='. $bot_local_lang . '">' . __('admin.itme.itemmeu.description') . '</a>');
											echo('</li>');
											if ($item_div != 3) {
												if ($type=='keyword') {
													echo('<li class="active">');
												}
												else {
													echo('<li>');
												}
												echo('<a class="func-menu" href="/admin/itemkeyword">' . __('admin.itme.itemmeu.keyword') . '</a>');
												echo('</li>');
											}
										} ?>
									<?php
									if ($item_id != NULL) {
										if ($type=='display') {
											echo('<li class="active">');
										}
										else {
											echo('<li>');
										}
										echo('<a class="func-menu" href="/admin/itemdisplay">' . __('admin.itme.itemmeu.display') . '</a>');
									    echo('</li>');
									}
									?>
									<li class="">
										<a class="func-menu" href="/admin/items<?php if ($page_no!=NULL) echo("?no=$page_no")?>">
										<?php echo __('admin.itme.itemmeu.back') ?></a>
									</li>
									<?php if ($item_id != NULL) { ?>
									<li class="">
										<a class="func-menu" href="/admin/item?id=<?php echo $next_item_id ?>">
										<?php echo __('admin.itme.itemmeu.next') ?></a>
									</li>
									<?php }?>
								</ul>
							</div>
