<div class="category-separator">
	<div class="setting-header"><?php echo __('user_flow.faq.label.header.setting'); ?></div>
</div>
<div class="lines-container" style="align-items: center;">
	<div class="basic-label"><?php echo __('user_flow.faq.label.title.color'); ?></div>
	<div class="">
		<div class="talkappi-colorpicker js-theme-color" data-name="theme-title-color" data-value="<?php if ($post != NULL) echo ($post['theme-title-color']) ?>"></div>
	</div>
</div>
<div class="lines-container" style="align-items: center;">
	<div class="basic-label"><?php echo __('user_flow.faq.label.title.backgroundcolor'); ?></div>
	<div class="">
		<div class="talkappi-colorpicker js-theme-color" data-name="theme-bk-color" data-value="<?php if ($post != NULL) echo ($post['theme-bk-color']) ?>"></div>
	</div>
</div>
<div class="lines-container">
	<div class="basic-label"><?php echo __('user_flow.faq.label.logo'); ?></div>
	<div class="col-md-4">
		<div class="talkappi-upload" data-name="logo_file_base64" data-label="<?php echo basename($post['logo-file']) ?>" data-type="img" data-ratio="1:1" data-url="<?php echo ($post['logo-file']) ?>"></div>
	</div>
</div>
<div style="height:60px" class="lines-container">
	<div class="basic-label"></div>
	<div class="col-md-2">
		<img id="logo_file_base64_prev" src="<?php echo ($post['logo-file']) ?>?t=<?php echo (time()) ?>" style="max-height: 60px;" onerror="this.src=''" />
	</div>
</div>
<div class="flexbox" style="flex-wrap:wrap">
	<div class="device-setting-container flexbox-space-top">
		<div class="font-standard font-family-v3 device-setting-container-title"><?php echo __('user_flow.faq.label.pc'); ?></div>
		<div class="flexbox-x-axis flexbox-space-top">
			<div class="basic-label"><?php echo __('user_flow.faq.label.title.text'); ?></div>
			<div class="flexbox-x-axis" style="padding-left: 10px;">
				<div class="checkbox-label">
					<input type="checkbox" id="title_show_flg_pc" name="title_show_flg_pc" <?php echo $post["title_show_flg_pc"] == "inline" ? "checked" : '' ?> value="inline">
					<label for="title_show_flg_pc"><?php echo __('user_flow.faq.label.checkbox.show'); ?></label>
				</div>
			</div>
		</div>
		<div class="flexbox-x-axis flexbox-space-top">
			<div class="basic-label"><?php echo __('user_flow.faq.label.logo.size'); ?></div>
			<div class="device-setting-size-container">
				<input name="logo-width" type="text" class="device-setting-size-input" placeholder="幅" value="<?php echo ($post['logo-width']) ?>">
				<span class="device-setting-size-letter">W</span>
			</div>
			<div class="device-setting-size-container">
				<input name="logo-height" type="text" class="device-setting-size-input" placeholder="高さ" value="<?php echo ($post['logo-height']) ?>">
				<span class="device-setting-size-letter">H</span>
			</div>
		</div>
		<div class="flexbox-x-axis flexbox-space-top">
			<div class="basic-label"><?php echo __('user_flow.faq.label.logo.position'); ?></div>
			<div class="talkappi-pulldown" data-name="logo-position" data-value='<?php if($post['logo-position']){echo $post['logo-position'];} else {"0 auto 0 0";} ?>' data-size="longer" data-source='<?php echo json_encode($logo_position, JSON_UNESCAPED_UNICODE) ?>'></div>
	</div>
	</div>
	<div class="device-setting-container flexbox-space-top">
		<div class="font-standard font-family-v3 device-setting-container-title"><?php echo __('user_flow.faq.label.mobile'); ?></div>
		<div class="flexbox-x-axis flexbox-space-top">
			<div class="basic-label"><?php echo __('user_flow.faq.label.title.text'); ?></div>
			<div class="flexbox-x-axis" style="padding-left: 10px;">
				<div class="checkbox-label">
					<input type="checkbox" id="title_show_flg_mobile" name="title_show_flg_mobile" <?php echo $post["title_show_flg_mobile"] == "inline" ? "checked" : '' ?> value="inline">
					<label for="title_show_flg_mobile"><?php echo __('user_flow.faq.label.checkbox.show'); ?></label>
				</div>
			</div>
		</div>
		<div class="flexbox-x-axis flexbox-space-top">
			<div class="basic-label"><?php echo __('user_flow.faq.label.logo.size'); ?></div>
			<div class="device-setting-size-container">
				<input name="logo-width-m" type="text" class="device-setting-size-input" placeholder="幅" value="<?php echo ($post['logo-width-m']) ?>">

				<span class="device-setting-size-letter">W</span>
			</div>
			<div class="device-setting-size-container">
				<input name="logo-height-m" type="text" class="device-setting-size-input" placeholder="高さ" value="<?php echo ($post['logo-height-m']) ?>">

				<span class="device-setting-size-letter">H</span>
			</div>
		</div>
	</div>
</div>


<div class="category-separator">
	<div class="setting-header"><?php echo __('user_flow.faq.label.button.setting'); ?></div>
</div>
<div class="lines-container" style="align-items: center;">
	<div class="basic-label"><?php echo __('user_flow.faq.labe.button.color'); ?></div>
	<div class="">
		<div class="talkappi-colorpicker js-theme-color" data-name="theme-button-bk-color" data-value="<?php if ($post != NULL) echo ($post['theme-button-bk-color']) ?>"></div>
	</div>
</div>
<div class="lines-container" style="align-items: center;">
	<div class="basic-label"><?php echo __('user_flow.faq.label.hover'); ?></div>
	<div class="">
		<div class="talkappi-colorpicker js-theme-color" data-name="theme-active-button-bk-color" data-value="<?php if ($post != NULL) echo ($post['theme-active-button-bk-color']) ?>"></div>
	</div>
</div>
<div class="lines-container">
	<div class="basic-label"><?php echo __('user_flow.faq.label.button.inquiry'); ?></div>
	<div class="flexbox-x-axis" style="padding-left: 10px;">
		<div class="checkbox-label">
			<input type="checkbox" id="inquiry_show_flg" name="inquiry_show_flg" <?php echo $post["inquiry_show_flg"] == "inline" ? "checked " : '' ?> value="inline">
			<label for="inquiry_show_flg"><?php echo __('user_flow.faq.label.checkbox.show'); ?></label>
		</div>
	</div>
</div>
<div class='flexbox-space-top'>
	<div class="basic-label"><?php echo __('user_flow.faq.label.url.inquiry'); ?></div>
	<?php
	foreach ($bot_langs as $k => $v) {
	?>
		<div class="lines-container">
			<div class="basic-label"><?php echo $v ?></div>
			<div class="col-md-7">
				<input name="inquiry_url_<?php echo $k ?>" type="text" class="text-input-longer" placeholder="<?php echo __('user_flow.faq.label.url.inquiry'); ?>" value="<?php if (array_key_exists('inquiry_url_' . $k, $post)) echo ($post['inquiry_url_' . $k]) ?>">
			</div>
		</div>
	<?php
	}
	?>
</div>
<div class="category-separator">
	<div class="setting-header"><?php echo __('user_flow.faq.label.font.setting'); ?></div>
</div>
<?php foreach ($_bot_lang as $k => $v) { ?>
	<div class="lines-container">
		<div class="basic-label"><?php echo ($v); ?></div>
		<div class="col-md-9">
			<input name="theme-font-family-<?php echo ($k) ?>" type="text" class="text-input-longer" value="<?php if (array_key_exists('theme-font-family-' . $k, $post)) echo (htmlspecialchars($post['theme-font-family-' . $k])) ?>">
		</div>
	</div>
<?php } ?>
<div class="category-separator">
	<div class="setting-header"><?php echo __('user_flow.faq.label.fotter.setting'); ?></div>
</div>
<div class="lines-container" style="align-items: center;">
	<div class="basic-label"><?php echo __('user_flow.faq.label.fotter.backgroundcolor'); ?></div>
	<div class="">
		<div class="talkappi-colorpicker js-theme-color" data-name="footer-bk-color" data-value="<?php if ($post != NULL) echo ($post['footer-bk-color']) ?>"></div>
	</div>
</div>
<div class="lines-container">
	<div class="basic-label"><?php echo __('user_flow.faq.label.footer.logo'); ?></div>
	<div class="talkappi-pulldown" data-name="talkappi-logo" data-value="<?php echo $post['talkappi-logo'] ?>" data-size="longer" data-source='<?php echo json_encode($talkappi_logoes, JSON_UNESCAPED_UNICODE) ?>'></div>
</div>