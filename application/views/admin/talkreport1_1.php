			<!-- BEGIN PAGE HEADER-->
			<!-- BEGIN PAGE HEAD -->
			<div class="page-head">
				<!-- BEGIN PAGE TITLE -->
				<div class="page-title">
					<h1>利用状況レポート<small></small></h1>
				</div>
				<!-- END PAGE TITLE -->
			</div>
			<!-- END PAGE HEAD -->
			<!-- END PAGE HEADER-->
			<!-- BEGIN PAGE CONTENT-->
			<div class="content-container">
				<div class="flex-x-between">
					<div><a href="talkreport1"><?php echo __('admin.talkreport1.label.statistics') ?></a> > <?php echo __('admin.talkreport1.label.statistics.detail') ?></div>
				</div>
			</div>
			<div class="row">
				<div class="col-md-12">
			        <!-- Page Content -->
			        <div id="page-wrapper">
			        <div class="portlet light">			        
					<input type="hidden" id="intent_cd" value="<?php echo $intent_cd?>" />
					<input type="hidden" id="sub_intent_cd" value="<?php echo $sub_intent_cd?>" /> 
						<div class="portlet box">
							<div style="padding:10px 10px;">
								<h4 style="font-family: HiraginoSans-W5;">
								<?php 
								
								if ($msgs[0]['facility_question_title'] != NULL && $msgs[0]['facility_question_title'] != '') {
									echo($msgs[0]['facility_question_title']);
								}
								else {
									echo($msgs[0]['question']);
								}
								
								//echo($msgs[0]['question']);
								?>
								</h4>
							</div>
							<div style="padding: 0 10px;">
							    <?php echo('<a style="color: black;" class="link-animate" href="/admin/talknew?cd=' . $intent_cd . '&lang=ja&sub_cd=' . $sub_intent_cd . '">'); ?>
								<button type="button" data-tableId="report1_1_table" class="answerButton btn mr10 pb2" style="float:left; border-radius:12px; height: 30px;background-color: #cff2d7;"><?php echo __('admin.talkreport1.label.edit_faq') ?></button>
								</a>
								<button type="button" data-tableId="report1_1_table" data-title="よく聞かれる質問回答詳細" class="btn primary mr10 exportCsv" style="float:right; background-color: white; border: solid 1px #c8cace; color: #000;"><span class="icon-export"></span><?php echo __('admin.common.button.csv_export') ?></button>
							</div>			
							<div class="portlet-body" style="border-top: solid 1px #e3e5e8; margin-top: 45px;">		
							<table class="table table-bordered table-hover js-data-table" id="report1_1_table">
							<thead style="background-color: #f6f7f9;">
							<tr>
								<th><?php echo __('admin.talkreport1.label.channel') ?></th>
								<th><?php echo __('admin.talkreport1.label.name') ?></th>
								<th><?php echo __('admin.talkreport1.label.language') ?></th>
								<?php if ($faq == 0) { ?>
								<th><?php echo __('admin.talkreport1.label.scene') ?></th>								
								<th><?php echo __('admin.talkreport1.label.date') ?></th>
								<?php } else {?>
								<th><?php echo __('admin.talkreport1.label.content') ?></th>
								<?php }?>
								<?php if (count($context_kv) > 0) echo('<th>'. __('admin.talkreport1.label.context') .'</th>')?>
								<th><?php echo __('admin.talkreport1.label.content') ?></th>
								<th style="display:none;"><?php echo __('admin.talkreport1.label.score') ?></th>
								<!-- 
            					<th>満足度</th>								
            					<th>不十分理由</th>				
            					 -->											
							</tr>
							</thead>

							<tbody>
							<?php
								foreach ($msgs as $msg) {
							?>	
							<tr class="gradeX odd" role="row">
								<td class="sorting_1"><?php 
									if ($msg['sns_type_cd']== 'fb') {
										echo('<img src="/assets/common/images/icon_fb.png" style="margin:5px;width:16px;"/>');
									}
									if ($msg['sns_type_cd']== 'ln') {
										echo('<img src="/assets/common/images/icon_ln.png" style="margin:5px;width:16px;"/>');
									}
									if ($msg['sns_type_cd']== 'wc') {
										echo('<img src="/assets/common/images/icon_wc.png" style="margin:5px;width:16px;"/>');
									}
									if ($msg['sns_type_cd']== 'wb') {
										echo('<img src="/assets/common/images/icon_wb.png" style="margin:5px;width:16px;"/>');
									}
									?></td>
								<td class="sorting_1"><?php 
								if ($msg['sns_type_cd'] == 'fb' || $msg['sns_type_cd'] == 'ln' || $msg['sns_type_cd'] == 'wc') {
									echo($msg['last_name'] . ' ' . $msg['first_name']);
								}
								if ($msg['sns_type_cd'] == 'wb') {
									echo __('admin.push.web.user');
								}
								if ($_user->role_cd == '99' || $_user->role_cd == '01' || $_user->role_cd == '07' || $_user->role_cd == '09')
								echo ('</br><label class="badge badge-info js-memberinfo" style="cursor:pointer;" data-member-id="' . $msg['member_id'] .'">' . __('admin.common.label.journey')  . '</label>');
								?></td>
								<td style="vertical-align: middle;"><?php echo($codes['02'][$msg['lang_cd']])?></td>
								<?php if ($faq == 0) { ?>
								<td style="vertical-align: middle;"><?php echo($msg['label'])?></td>
								<td style="vertical-align: middle;"><?php 
								  $date = $msg['log_time'];
								  echo date("Y/m/d H:i", strtotime($date));
								?><a class="pop_adminchat" member_id=<?php echo $msg['member_id'] ?>>
									<span class="badge badge-faq" style="min-width: 72px;height: 24px;padding:6px 12px;color:black; background-color:#d3eeff; float: right;" >
									<?php echo __('admin.common.label.historical_view');?>
								</span>
								</a></td>
								<?php 
								if (count($context_kv) > 0) {
									echo('<td>');
									if (array_key_exists($msg['context_id'], $context_kv)) echo($context_kv[$msg['context_id']]);
									echo('</td>');
								}
								?>
								<td style="vertical-align: middle;"><?php
								if ($msg['lang_cd'] == 'ja' || $msg['member_msg_t'] == '')
									echo($msg['member_msg']);
								else
									echo($msg['member_msg_t'] . "<br/>※原文：". $msg['member_msg']);
								?></td>
								<td style="display:none;"><?php echo($msg['score']); ?></td>
								<?php } else {?>
								<td><?php echo($msg['search_date'])?></td>
								<td><?php echo($msg['content'])?></td>
								<?php }?>
								
								<?php  if (false) { ?>
								<td><?php
								if ($msg['answer'] == '1') {
									echo('<i class="fas fa-thumbs-up"></i>');
								}
								else if ($msg['answer'] == '2') {
									echo('<i class="far fa-thumbs-down"></i>');
								}
								?></td>
								<td style="display:none;"><?php echo($msg['reason']);?></td>
								<?php }?>																									
							</tr>
							<?php } ?>
							</tbody>
							</table>
							</div>
						</div>
					</div>
			        </div>
			        
			        <!-- /#page-wrapper -->					
				</div>
			</div>
			<!-- END PAGE CONTENT-->
