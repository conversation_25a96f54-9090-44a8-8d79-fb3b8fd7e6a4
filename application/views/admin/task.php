	<input type="hidden" id="act" name="act" value="" />
	<input type="hidden" name="message" id="message" value="<?php echo($message)?>" />			
	<input type="hidden" name="selected_msgs" id="selected_msgs" value="">
	<input type="hidden" name="selected_attrs" id="selected_attrs" value="">
	<?php echo $menu ?>
	<!-- Page Content -->
	<div class="content-container white border">
		<div class="section-container">
			<div class="row">
				<div class="col-md-8">				
					<div class="form-body">
						<?php if ($_user->role_cd == "99") {?>
							<div class="form-group">
								<label class="control-label col-md-3">分類</label>
								<div class="col-md-4">
									<?php echo Form::select('task_class_cd', $task_class, $post['task_class_cd'], array('id'=>'task_class_cd','class'=>'form-control'))?>
								</div>
							</div>
						<?php }?>	
						<div class="form-group">
							<label class="control-label col-md-3">タスク名</label>
							<div class="col-md-5">
								<div class="input-icon right">
									<input name="task_name" id="task_name" type="text" class="form-control" value="<?php if ($post != NULL) echo($post['task_name'])?>">
								</div>
							</div>
						</div>
						<?php if ($_user->role_cd == "99") {?>
						<div class="form-group">
							<label class="control-label col-md-3">繰り返し※</label>
							<div class="col-md-3">
								<?php echo Form::select('repeat_cd', $_codes['06'], $post['repeat_cd'], array('id'=>'repeat_cd','class'=>'form-control'))?>
							</div>											
						</div>		
						<?php }?>										
						<div class="form-group flex">
							<label class="control-label col-md-3">実行予定時刻</label>
							<div class="col-md-3 mr10"  style="width:120px" >
								<input name="scheduled_date" class="talkappi-datepicker" id="scheduled_date" value="<?php if ($post != NULL)echo($post['scheduled_date'])?>"/>
							</div>
							<div class="col-md-3">
								<input name="scheduled_time" id="scheduled_time" type="text" class="talkappi-timepicker" value="<?php if ($post != NULL) echo($post['scheduled_time'])?>">
							</div>																	
						</div>
						<?php 
						foreach($ext_data as $k=>$v) {
							if ($k=='bot_id_list' && $parent_bot_id != 0) continue;
						?>
						<div class="form-group flex">
							<label class="control-label col-md-3"><?php echo $v?></label>
							<?php 
							if ($k == 'sns_type_cd') {
								echo('<div class="col-md-9">');
								echo('<div class="btn-group" data-toggle="buttons">');
								$bot_snses = explode(',', $_bot->sns_cd);
								foreach($_codes['16'] as $sns_cd=>$sns_name) {
									if (!in_array($sns_cd, $bot_snses)) continue;
									$select_sns = explode(',', $post[$k]);
									if (in_array($sns_cd, $select_sns) || $task_id==null) {
										echo('<label class="btn default active">');
										echo('<input name="' . $k . '[]"  type="checkbox" checked="true" value="' . $sns_cd . '" class="toggle">' . $sns_name. '</label>');
									}
									else {
										echo('<label class="btn default">');
										echo('<input name="' . $k . '[]" type="checkbox" value="' . $sns_cd . '" class="toggle">' . $sns_name . '</label>');
									}
								}
								echo('</div>');
								echo('</div>');
								//echo Form::select('sns_type_cd',[''=>'-'] +  $_codes['08'], $post[$k], array('id'=>'sns_type_cd','class'=>'form-control'));
							}
							else if ($k == 'lang_cd') {
								echo('<div class="col-md-9">');
								echo('<div class="btn-group" data-toggle="buttons">');
								$bot_langs = explode(',', $_bot->lang_cd);
								foreach($_bot_lang as $lang_cd=>$lang_name) {
									if (!in_array($lang_cd, $bot_langs)) continue;
									$select_lang = explode(',', $post[$k]);
									if (in_array($lang_cd, $select_lang) || $task_id==null) {
										echo('<label class="btn default active">');
										echo('<input name="' . $k . '[]" type="checkbox" checked="true" value="' . $lang_cd . '" class="toggle">' . $lang_name. '</label>');
									}
									else {
										echo('<label class="btn default">');
										echo('<input name="' . $k . '[]" type="checkbox" value="' . $lang_cd . '" class="toggle">' . $lang_name. '</label>');
									}
								}
								echo('</div>');
								echo('</div>');
								//echo Form::select('lang_cd',[''=>'-'] +  $_bot_lang, $post[$k], array('id'=>'lang_cd','class'=>'form-control'));
							}
							else if (strpos($k, 'time')!==false || strpos($k, 'date')!==false) {
								echo('<div class="col-md-3 mr10"  style="width:120px" >');
								echo('<input name="' . $k . '" value="' . $post[$k] .'" class="talkappi-datepicker" type="text"/>');
								echo('</div>');
								if (strpos($k, 'time')!==false) {
									echo('<div class="col-md-3">');
									echo('<input name="' . $k . '_time" type="text" class="talkappi-timepicker"  value="' . $post[$k . '_time'] .'">');
									echo('</div>');
								}
							}
							else if ($k=='test') {
								echo('<div class="col-md-9">');
								echo('<div class="talkappi-radio" data-name="test" data-value=' . $test . ' data-source=\'{"1":"テスト", "2":"本番"}\'></div>');
								echo('</div>');
							}
							else if ($k=='message_cd') {
								echo('<div class="col-md-9">');
								echo Form::select('message_cd[]', $bot_msgs, $send_msgs, array('id'=>'send-msg-multiple','class'=>'form-control','multiple'=>'multiple'));
								echo('</div>');
							}
							else if ($k=='user_attr_cd') {
								echo('<div class="col-md-9">');
								echo Form::select('user_attr_cd[]', $all_user_attrs, $user_attrs, array('id'=>'user_attr_multiple','class'=>'form-control','multiple'=>'multiple'));
								echo('</div>');
							}
							else if ($k=='user_attr_cond') {
								echo('<div class="col-md-9">');
								echo('<div class="talkappi-radio js_user_attr_cond" data-name="user_attr_cond" data-value=' . $user_attr_cond . ' data-source=\'{"AND":"AND条件", "OR":"OR条件"}\'></div>');
								echo('</div>');
							}
							else if ($k=='target_num') {
								echo('<div class="col-md-3">');
								echo('<label id="member_count" class="control-label" style="color:red; font-size:16px;"> ' . $target_num . '</label>');
								echo('</div>');
							}
							else if ($k=='cmd') {
								echo('<div class="col-md-9">');
								echo('<textarea name="' . $k . '" class="form-control" maxlength="1000" rows="2" placeholder="">' . $post[$k] .'</textarea>');
								echo('</div>');
							}
							else {
								echo('<div class="col-md-9">');
								echo('<input name="' . $k . '" type="text" class="form-control" value="' . htmlspecialchars($post[$k]) .'">');
								echo('</div>');
							}
						echo('</div>');
						}?>
					</div>																					
					<div class="form-actions">
						<div class="row">
							<div class="col-md-offset-3 col-md-9">
								<button type="button" id="saveBaseButton" class="btn blue mr10">
								<i class="fa fa-save mr10"></i>保存</button>
								<?php if ($task->task_status_cd == '01' || $task->task_status_cd == '00' || true) {?>
								<button type="button" id="deleteButton" class="btn red mr10">削除</button>
								<?php } ?>
								<button type="button" onclick="top.location='<?php echo $return_url ?>'" class="btn grey-steel">戻る</button>
							</div>
						</div>
					</div>
				</div>
				<div class="col-md-4">
					<?php echo $msgpreview ?>
				</div>
			</div>
		</div>
	</div>



