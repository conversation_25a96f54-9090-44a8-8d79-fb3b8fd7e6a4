
			<!-- BEGIN PAGE HEADER-->
			<!-- BEGIN PAGE HEAD -->
			<div class="page-head">
				<!-- BEGIN PAGE TITLE -->
				<div class="page-title">
					<h1>管理サイト操作履歴<small></small></h1>
				</div>
				<!-- <PERSON>ND PAGE TITLE -->
			</div>
			<!-- END PAGE HEAD -->
			<!-- END PAGE HEADER-->
			<!-- BEGIN PAGE CONTENT-->
			<div class="row">
				<div class="col-md-12">
			        <!-- Page Content -->
			        <div id="page-wrapper">
			        <div class="portlet light">
							<?php echo($menu);?>
							<div class="form-body">
								<div class="row">
								<br/>
								</div>
								<div class="row">
									<label class="control-label col-md-1" style="padding-left:15px; padding-right:15px;">期間</label>
									<div class="col-md-4">
										<input name="start_date" id="start_date" value="<?php echo($post['start_date'])?>" style="float:left;" class="form-control form-control-inline input-small date-picker" size="16" data-date-format="yyyy-mm-dd"  type="text"/>
										<input name="end_date" id="end_date" value="<?php echo($post['end_date'])?>" style="float:left; margin-left:10px;" class="form-control form-control-inline input-small date-picker" size="16" data-date-format="yyyy-mm-dd"  type="text"/>
									</div>
									<?php if ($_user->role_cd=='99') {?>
									<div class="col-md-4">
										<?php echo Form::select('bot_id', $bots, $post['bot_id'], array('id'=>'bot_id','class'=>'form-control'))?>
									</div>
									<div class="col-md-2">
										<?php echo Form::select('role_cd', [''=>__('admin.common.label.all_user')] + $role_kv, '', array('id'=>'role_cd','class'=>'form-control'))?>
									</div>
									<?php }?>
									<div class="col-md-1">
										<div class="form-group">
											<button type="submit" class="btn yellow">
											<i class="fa fa-search mr10"></i>検索</button>
										</div>
									</div>																		
								</div>
							</div>						
						<div class="portlet box">
							<div class="portlet-body">
							<table class="table table-striped table-bordered table-hover js-data-table">
							<thead>
							<tr>
								<th style="width:80px;">
									アクセス日時
								</th>
								<th>
									  施設名
								</th>
								<th>
									 操作機能
								</th>
								<th style="display: none;">
									 操作内容詳細
								</th>								
								<th style="width:200px;">
									 ユーザー
								</th>																				
							</tr>
							</thead>

							<tbody>
							<?php
								foreach ($accesslist as $access) {
							?>	
							<tr class="gradeX odd" role="row">
								<td class="sorting_1" style="white-space: nowrap;">
									<?php echo(date('Y-m-d H:i:s', strtotime($access['access_time'])))?>
								<td>
									<?php echo($access['bot_name'])?>
								</td>
								<td>
									<?php 
									if (array_key_exists($access['action'], $func_dict)) {
										echo($func_dict[$access['action']]);
										if ($_user->role_cd=='99') echo(' (' . $access['action'] . ')');
									}
									else {
										echo($access['action']);
									}
									if ($access['content'] != '') {
										echo('<br/>');
										echo($access['content']);
									}
									?>				
								</td>
								<td style="display: none;">					
									<?php echo( $access['content'])?>				
								</td>								
								<td>				
									<?php echo($access['name'])?>					
								</td>														
							</tr>
							<?php } ?>
							</tbody>
							</table>
							</div>
						</div>
					</div>
			        </div>
			        <!-- /#page-wrapper -->					
				</div>
			</div>
			<!-- END PAGE CONTENT-->
