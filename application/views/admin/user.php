<script>
var role_description = <?php echo json_encode($role_description, JSON_UNESCAPED_UNICODE) ?>;
</script>
<input type="hidden" id="message" value="<?php echo $message?>" />
<input type="hidden" id="user_id" value="<?php echo $user_id?>" />
<input type="hidden" id="opt_do_not_receive" value="<?php echo $_codes['14']['']?>" />

<?php 
	if (strlen($_active_menu) > 4) {
		$tab_menu = View::factory ('admin/menutab');
		echo($tab_menu);
	}
?>
<div class="content-container white border">
	<div class="section-container bottom-line">
		<h2><?php echo __('admin.account.label.basic_info_settings') ?></h2>
			<?php
			if (count($role_kv) > 0) {
			?>
			<div class="form-group">
				<label class="control-label col-md-3 label-fix-14"><?php echo __('admin.account.label.user_role') ?></label>
				<div class="col-md-8">
					<div class="input-group">
						<?php 
						echo Form::select('role_cd', $role_kv, $post['role_cd'], array('id'=>'role_cd','class'=>'form-control','style'=>'border-radius: 4px;
						width: 250px;'))?>
					</div>
					<div id="role_description" style="padding:4px;color:gray;"></div>
				</div>
			</div>
			<?php }?>
			
			<div class="form-group">
				<label class="control-label col-md-3 label-fix-14">
					<?php echo __('admin.account.th.login_id') ?>
					<span class="icon-detail" style="margin: 0 0 0 0.5rem;" title="<?php echo __('admin.account.label.login_id_info') ?>"></span>
				</label>
				<div class="col-md-4">
					<div class="input-icon right">
						<i class="fa icon-envelope"></i>
						<input name="email" id="email" readonly="readonly" onfocus="this.removeAttribute('readonly');" type="text"  placeholder="<EMAIL>" class="form-control autocomplete-off" autocomplete="new-password" value="<?php echo $post['email']?>">
					</div>
				</div>											
			</div>

			<div class="form-group">
				<label class="control-label col-md-3 label-fix-14">
					<?php echo __('admin.account.th.name') ?>
					<span class="icon-detail" style="margin: 0 0 0 0.5rem;" title="<?php echo __('admin.account.label.name_info') ?>"></span>
				</label>
				<div class="col-md-4">
					<div class="input-icon right">
						<i class="fa icon-user"></i>
						<input name="name" id="name" type="text"  placeholder="<?php echo __('admin.account.message.input_fullname') ?>" class="form-control" placeholder="" value="<?php echo $post['name']?>">
					</div>
				</div>
			</div>
			<div class="form-group">
				<label class="control-label col-md-3 label-fix-14"><?php echo __('admin.account.label.auto_password') ?></label>
				<div class="col-md-2">
					<div class="talkappi-radio" data-name="autopwd_flg" data-value='<?php echo((!$user_id)?'1':'0') ?>' data-source='{"0":"OFF", "1":"ON"}'></div>
				</div>				
			</div>																				
			<div class="form-group" id="div-password">
				<label class="control-label col-md-3 label-fix-14"><?php echo __('admin.account.label.password') ?></label>
				<div class="col-md-4">
					<div class="input-icon right">
						<i class="fa icon-key"></i>
						<input name="password" readonly="readonly" onfocus="this.removeAttribute('readonly');" placeholder="<?php echo __('admin.account.message.password_rule_1') ?>" type="password" id="password" class="form-control autocomplete-off" autocomplete="new-password">
					</div>
				</div>
			</div>	
			<div class="form-group" id="div-password2">
				<label class="control-label col-md-3 label-fix-14"><?php echo __('admin.account.label.confirm_password') ?></label>
				<div class="col-md-4">
					<div class="input-icon right">
						<i class="fa icon-key"></i>
						<input name="password2" readonly="readonly" onfocus="this.removeAttribute('readonly');" placeholder="<?php echo __('admin.account.message.password_repeat') ?>" type="password" id="password2" class="form-control autocomplete-off" autocomplete="new-password">
					</div>
				</div>
			</div>
			<div class="form-group" id="div-2fa">
				<label class="control-label col-md-3 label-fix-14">
					<?php echo __('admin.account.label.2fa') ?>
					<span class="icon-detail" style="margin: 0 0 0 0.5rem;" title="<?php echo __('admin.account.label.two_fa_info') ?>"></span>
				</label>
				<div class="col-md-8">				
					<div><?php echo __('admin.account.label.2fa_description') ?></div>
					<span style="color: crimson;"><?php echo __('admin.account.label.2fa_description.required') ?></span>
				</div>
				
			</div>

			<?php
			if ($flg_ai_bot == "1" || $flg_very == "1" || $_bot_id == 0) {
			?>									
			<div class="form-group">
				<label class="control-label col-md-3 label-fix-14"><?php echo __('admin.account.th.available_lang') ?></label>
				<div class="col-md-9">
					<div class="talkappi-checkbox" data-name="chat_available_lang" data-value='<?php echo json_encode($post['chat_available_lang'])?>' data-source='<?php echo json_encode($_bot_lang, JSON_UNESCAPED_UNICODE|JSON_HEX_APOS) ?>'></div>
				</div>
			</div>
			<?php }?>
			<div class="form-group">
				<label class="control-label col-md-3 label-fix-14"><?php echo __('admin.account.label.mail_account_info') ?></label>
				<div class="col-md-2">
					<div class="talkappi-radio" data-name="notice_flg" data-value='<?php echo((!$user_id)?'1':'0') ?>' data-source='{"0":"OFF", "1":"ON"}'></div>
				</div>
				<label class="control-label col-md-5" style="display:none;color:red;text-align:left;">※OFFの場合、アカウント名とパスワード通知は行いません</label>				
			</div>
	</div>
	<?php
	if ($flg_next_survey == "1" || $flg_next_inquiry == "1"  || $_bot_id == 0) {
	?>
	<div class="section-container bottom-line">
		<h2><?php echo __('admin.account.label.access_permission_settings') ?></h2>			
		<div class="form-group">
			<label class="control-label col-md-3 label-fix-14"><?php echo __('admin.account.label.personal_info_access_all') ?>
				<span class="icon-detail" style="margin: 0 0 0 0.5rem;" title="<?php echo __('admin.account.label.personal_info_access_all_hint') ?>"></span>
			</label>
			<div class="col-md-2" style="width: fit-content;">
				<div class="talkappi-radio" data-name="privacy_show_flg" data-value='<?php echo($post['privacy_show_flg']) ?>' data-source='{"0":"OFF", "1":"ON"}'></div>
			</div>
			<div class="js-privacy_self_show_flg">
				<label class="control-label col-md-3 label-fix-14" style="width: fit-content;"><?php echo __('admin.account.label.personal_info_access_in_charge') ?></label>
				<div class="col-md-2">
					<div class="talkappi-radio" data-name="privacy_self_show_flg" data-value='<?php echo($post['privacy_self_show_flg']) ?>' data-source='{"0":"OFF", "1":"ON"}'></div>
				</div>			
			</div>		
		</div>									
		<div class="form-group">
			<label class="control-label col-md-3 label-fix-14"><?php echo __('admin.account.label.access_not_in_charge') ?>
				<span class="icon-detail" style="margin: 0 0 0 0.5rem;" title="<?php echo __('admin.account.label.access_not_in_charge_hint') ?>"></span>
			</label>
			<div class="col-md-2">
				<div class="talkappi-radio" data-name="auth_all_contents_flg" data-value='<?php echo($post['auth_all_contents_flg']) ?>' data-source='{"0":"OFF", "1":"ON"}'></div>
			</div>			
		</div>
		<?php if ($grp_bot_id == 0) { ?>
		<div class="form-group">
			<label class="control-label col-md-3 label-fix-14"><?php echo __('admin.account.label.auth_all_groups_flg') ?></label>
			<div class="col-md-2">
				<div class="talkappi-radio" data-name="auth_all_groups_flg" data-value='<?php echo($post['auth_all_groups_flg']) ?>' data-source='{"0":"OFF", "1":"ON"}'></div>
			</div>			
		</div>
		<?php } ?>		
	</div>				
	<?php } else { ?>

	<?php if ($grp_bot_id == 0) { ?>
		<h2><?php echo __('admin.account.label.access_permission_settings') ?></h2>	
		<div class="form-group">
			<label class="control-label col-md-3 label-fix-14"><?php echo __('admin.account.label.auth_all_groups_flg') ?></label>
			<div class="col-md-2">
				<div class="talkappi-radio" data-name="auth_all_groups_flg" data-value='<?php echo($post['auth_all_groups_flg']) ?>' data-source='{"0":"OFF", "1":"ON"}'></div>
			</div>			
		</div>
	<?php } }?>	

	<div class="section-container bottom-line">
		<h2><?php echo __('admin.account.label.notification_settings') ?></h2>	
			<div class="form-group">
				<label class="control-label col-md-3 label-fix-14"><?php echo __('admin.account.label.notify_by_email') ?></label>
				<div class="col-md-2">
					<div class="talkappi-radio" data-name="email_flg" data-value='<?php echo($post['email_flg']) ?>' data-source='{"0":"OFF", "1":"ON"}'></div>
				</div>			
			</div>	

			<?php
			if ($flg_ai_bot == "1" || $_bot_id == 0) {
			?>	
			<div class="form-group">
				<label class="control-label col-md-3 label-fix-14"><?php echo __('admin.account.label.notification_lang') ?></label>
				<div class="col-md-9">
					<div class="talkappi-checkbox" data-name="receive_lang" data-value='<?php echo json_encode($post['receive_lang'])?>' data-source='<?php echo json_encode($_bot_lang, JSON_UNESCAPED_UNICODE|JSON_HEX_APOS) ?>'></div>
				</div>
			</div>
			<?php }?>
			
			<?php
			if ($flg_very == "1" || $_bot_id == 0) {
			?>	
			<div class="form-group">
				<label class="control-label col-md-3 label-fix-14"><?php echo __('admin.account.label.call_flg') ?></label>
				<div class="col-md-2">
					<div class="talkappi-radio" data-name="call_flg" data-value='<?php echo($post['call_flg']) ?>' data-source='{"0":"OFF", "1":"ON"}'></div>
				</div>			
			</div>

			<div class="form-group" id="call_group_cd" <?php if ($post['call_flg']==0) echo("style='display:none;'") ?>>
				<label class="control-label col-md-3 label-fix-14"><?php echo __('admin.account.label.call_group_cd') ?></label>
				<div class="col-md-9">
					<div class="talkappi-checkbox" data-name="call_group_cd" data-value='<?php echo json_encode($post['call_group_cd'])?>' data-source='<?php echo json_encode($call_group_cds, JSON_UNESCAPED_UNICODE|JSON_HEX_APOS) ?>'></div>
				</div>
			</div>

			<div class="form-group" id="call_type" <?php if ($post['call_flg']==0) echo("style='display:none;'") ?>>
				<label class="control-label col-md-3 label-fix-14"><?php echo __('admin.account.label.call_type') ?></label>
				<div class="col-md-9">
					<div class="talkappi-checkbox" data-name="call_type" data-value='<?php echo json_encode($post['call_type'])?>' data-source='<?php echo json_encode(["audio"=>__('admin.account.label.call_type_audio'),"video"=>__('admin.account.label.call_type_video')], JSON_UNESCAPED_UNICODE|JSON_HEX_APOS) ?>'></div>
				</div>
			</div>
			<?php }?>

			<?php
			if ($flg_ai_bot == "1" || $_bot_id == 0) {
			?>	
			<div class="form-group">
				<label class="control-label col-md-3 label-fix-14"><?php echo __('admin.account.label.notify_by_browser') ?></label>
				<div class="col-md-2">
					<div class="talkappi-radio" data-name="adminsite_flg" data-value='<?php echo($post['adminsite_flg']) ?>' data-source='{"0":"OFF", "1":"ON"}'></div>
				</div>			
			</div>
			<div class="form-group" style="display:none;">
				<label class="control-label col-md-3 label-fix-14"><?php echo __('admin.account.label.notify_by_messenger') ?></label>
				<div class="col-md-4">
					<input name="facebook" type="text" id="facebook" class="form-control" placeholder="" value="<?php echo $post['facebook']?>">
				</div>
				<div class="col-md-2">
					<div class="talkappi-radio" data-name="facebook_flg" data-value='<?php echo($post['facebook_flg']) ?>' data-source='{"0":"OFF", "1":"ON"}'></div>
				</div>
			</div>
			<div class="form-group">
				<label class="control-label col-md-3 label-fix-14"><?php echo __('admin.account.label.notify_by_line') ?></label>
				<div class="col-md-2">
					<div class="talkappi-radio" data-name="line_flg" data-value='<?php echo($post['line_flg']) ?>' data-source='{"0":"OFF", "1":"ON"}'></div>
				</div>	
				<div class="col-md-4">
					<input name="line" type="text" id="line" class="form-control" placeholder="LINE UID" value="<?php echo $post['line']?>">
				</div>
			</div>
			<div class="form-group" style="display:none;">
				<label class="control-label col-md-3 label-fix-14"><?php echo __('admin.account.label.notify_by_wechat') ?></label>
				<div class="col-md-4">
					<input name="wechat" type="text" id="wechat" class="form-control" placeholder="" value="<?php echo $post['wechat']?>">
				</div>
				<div class="col-md-2">
					<div class="talkappi-radio" data-name="wechat_flg" data-value='<?php echo($post['wechat_flg']) ?>' data-source='{"0":"OFF", "1":"ON"}'></div>
				</div>											
			</div>	
			<div class="form-group" style="display:none;">
				<label class="control-label col-md-3 label-fix-14"><?php echo __('admin.account.label.notify_by_slack') ?></label>
				<div class="col-md-4">
					<input name="slack" type="text" id="slack" class="form-control" placeholder="" value="<?php echo $post['slack']?>">
				</div>
				<div class="col-md-2">
					<div class="talkappi-radio" data-name="slack_flg" data-value='<?php echo($post['slack_flg']) ?>' data-source='{"0":"OFF", "1":"ON"}'></div>
				</div>											
			</div>										
			<?php
			echo('<div id="email_settings">');
			foreach($intents as $intent) {
				if ($intent['intent_cd'] == 'system.jtb_book_pay_error') {
					$jtb = false;
					$reserve_setting = json_decode($_bot_setting['json_reserve_settings'], true);
					if (is_array($reserve_setting) && array_key_exists('payment_support', $reserve_setting)) {
						$payment_support = $reserve_setting['payment_support'];
						foreach($payment_support as $ps) {
							if ($ps['type'] == '2' && $ps['issue'] == 'jtb') {
								$jtb = true;
							}
						}
					}
					if ($jtb == false) continue;
				}
				echo('<div class="form-group">');
				echo('<label class="control-label col-md-3 label-fix-14">' . $intent['intent_name'] . '</label>');
				echo('<div class="col-md-2">');
				if (array_key_exists($intent['intent_cd'], $post)) {
					$intent_notify = $post[$intent['intent_cd']];
				}
				else {
					$intent_notify = '';
				}
				echo(Form::select($intent['intent_cd'], $_codes['14'], $intent_notify, array('class'=>'form-control')));
				echo('</div>');

				if ($intent['intent_cd'] == 'system.unknown') {
					echo('<div class="col-md-3">');
					echo(Form::select('system_unknown_scene', $scenes, $post['system.unknown.scene'], array('class'=>'form-control')));
					echo('</div>');
					echo('<div class="col-md-2" style="display:none;">');
				}
				else {
					echo('<div class="col-md-2">');
				}
				if (array_key_exists($intent['intent_cd'].'div', $post)) {
					$intent_notify_div = $post[$intent['intent_cd'].'div'];
				}
				else {
					$intent_notify_div = '';
				}
				$style = '';
				if ($_user->role_cd != "99" || $_user->role_cd != "09") {
					$style = 'style="display:none;"';
				}
				echo('<input ' . $style . ' name="div_' . $intent['intent_cd'] . '" maxlength="32" type="text" class="form-control" value="'. $intent_notify_div. '" placeholder="'.__('admin.account.placeholder.notify_condition_setting').'">');
				echo('</div>');

				echo('</div>');
			}
			echo('</div>');
			?>
	<?php }?>
	</div>
	<div class="section-container bottom-line">
		<h2><?php echo __('admin.account.label.support_settings') ?></h2>	
			<div class="form-group">
				<label class="control-label col-md-3 label-fix-14"><?php echo __('admin.account.label.support_use') ?></label>
				<div class="col-md-2">
					<div class="talkappi-radio" data-name="support_use" data-value='<?php echo($post['support_use']?'1':'0') ?>' data-source='{"0":"OFF", "1":"ON"}'></div>
				</div>				
			</div>
	</div>

	<div class="form-actions">
		<div class="row">
			<label class="control-label col-md-3 label-fix-14"></label>
			<div class="col-md-9 flex">
				<button type="button" id="saveButton" class="btn-larger btn-blue js-action-save" <?php if (!($_user->role_cd == '99' || $_user->role_cd == '09' || $_user->role_cd == '19'|| $_user->role_cd == '79')) echo('disabled');?>>
				<?php echo __('admin.common.button.save0') ?></button>
				<button type="button" onclick="top.location='/admin/users'" class="btn-larger btn-white js-action-back"><?php echo __('admin.common.button.return_to_list') ?></button>
			</div>
		</div>
	</div>
</div>

