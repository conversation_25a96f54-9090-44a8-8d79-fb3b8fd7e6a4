<script type="text/javascript">
    const _maximum_remains = <?php echo json_encode($maximum_remains) ?>;
	const _order = <?php echo json_encode($order->as_array()) ?>;
</script>
<input type="hidden" id="maximum_order_id" name="maximum_order_id" value="<?php echo($order->order_id) ?>" />
<input type="hidden" id="order_info" name="order_info" value="" />
<?php echo $menu ?>	
<div class="content-container white border">
	<div class="section-container">
		<?php 
		$maximum_arr = [];
		if ($order->order_status_cd == '01' || $order->order_status_cd == '02') {
		foreach($entries as $entry) { 
			$entry_data = json_decode($entry->entry_data, true);
			$maximum = ORM::factory('botmaximum')->where('id', '=', $entry_data[0]['maximum'])->where('bot_id', '=', $_bot_id)->find();
			if (in_array($entry_data[0]['maximum'], $maximum_arr)) continue; 
			$maximum_arr[] = $entry_data[0]['maximum'];
			?>
			<div class="form-group" style="margin-bottom:30px;">
				<div><?php echo $maximum->name ?></div>
			</div>
			<div class="form-group flex js-entry" data-entry-no="<?php echo $entry->no ?>" data-maximum-id="<?php echo $maximum->id ?>">
				<div>日付：<?php echo $order->day ?></div>
				<div style="width:120px;margin-left:10px;">
					<input type="text" readonly="readonly" autocomplete="off" class="form-control datepicker js-day" value="" pattern="\d{4}-?\d{1,2}-?\d{1,2}" placeholder="yyyy-mm-dd">
				</div>
				<?php if ($maximum->span == '1h') { ?>
				<div style="margin-left:10px;">時間帯：<?php echo $order->time ?></div>
				<?php for($i=0; $i<count(explode(',', $order->time)); $i++) { ?>
					<div style="width:120px;margin-left:10px;">
					<select class="form-control js-time"></select>
					</div>
				<?php }} ?>
				<div style="display:none;">
					<select class="form-control js-num"><option value="1">1</option></select>
				</div>
			</div>
		<?php }} ?>

		<div class="form-group flex">
			<div>メール：<?php echo $order->order_mail ?></div>
			<div style="margin-left:10px;width:310px;">
				<input type="text" name="mail" class="form-control js-mail" value="">
			</div>
		</div>

		<div class="form-group">
			<div class="col-md-10">問合わせ詳細</div>
		</div>
		<div>
			<table>
			<?php echo $order_info ?>
			</table>
		</div>
	</div>
	<div class="actions-container" style="margin: 60px 0 0 140px;">
		<div class="btn-larger btn-blue x-first js-action-save">保存</div>
		<div class="btn-larger btn-gray-black js-action-reset">画面内容リセット</div>
		<div class="btn-larger btn-white js-action-back">一覧に戻る</div>
	</div>
</div>
