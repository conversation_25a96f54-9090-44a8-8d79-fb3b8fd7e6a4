<style type="text/css">

    div.setting-title{
        padding: 12px 0px 12px 0px;
    }
    div.setting-title>div.title-text{
        white-space: nowrap;
        height: 18px;

        font-family: Hiragino Sans;
        font-size: 12px;
        font-weight: 500;
        line-height: 18px;
        text-align: left;
    }
    div.setting-title>div.title-border{
        width: calc(100% - 120px);
        align-self: center;
        height: 0;
        border: 0.5px solid #E3E5E8
    }

    .setting-item {
        padding: 16px 24px 24px 24px;
        gap: 24px;
        justify-content: flex-start;
    }

    .setting-item-label{
        padding: 5px 0px 5px 0px;
        text-align: right;
        width: 200px;
    }

    div.setting-container{
        flex-direction: column;
        gap: 12px;
    }
    div.setting-input-button{
        gap: 12px;
    }
    input {
        height: 28px;
        font-family: 'Hiragino Sans';
        font-weight: 300;
        font-size: 12px;
        cursor: default;
        padding: 0px 5px 0px 5px;
        border-width: 1px;
        border-color: rgba(227, 229, 232, 1);
        border-radius: 4px;
        border-style: solid;
    }
    div.setting-input-button>button{
        border: none;
        font-family: Hiragino Sans;
        font-size: 12px;
        font-weight: 400;
        line-height: 18px;
        text-align: center;
    }
    .setting-detail-container{
        width: 695px;
        padding: 12px;
        gap: 12px;
        border-radius: 4px 0px 0px 0px;
        border-width: 1px;
        border-color: rgba(227, 229, 232, 1);
        border-style: solid;
        flex-direction: column;
    }
    .setting-detail-top{
        justify-content: space-between;
        width: 100%;
    }
    .setting-detail-top>button{
        border: none;
        font-family: Hiragino Sans;
        font-size: 12px;
        font-weight: 400;
        line-height: 18px;
        text-align: center;
    }

    .setting-detail-container>table td, .setting-detail-container>table th{
        text-align: center;
        vertical-align: middle;
    }

    .setting-detail-container>table>thead {
        background: rgba(246, 247, 249, 1);
    }

    .setting-detail-container>table>thead>tr>th {
        font-size: 12px;
        font-weight: 300;
    }

    .setting-detail-container>table td:first-child, .setting-detail-container>table th:first-child {
        width: 120px;
    }
    .setting-detail-container>table td:last-child, .setting-detail-container>table th:last-child {
        width: 60px;
    }
    .setting-detail-container>table td:last-child>a {
        text-decoration: underline;
    }

</style>

<!-- Page Content -->

<div id="page-wrapper">
    <!-- メニュー -->
    <nav class="line-tab" style="margin-left: 8px;">
        <ul>
            <li>
                <a class="func-menu"  style="font-size: 14px;" href="/admin/linereport">LINE利用状況</a>
            </li>
            <li>
                <a class="func-menu"  style="font-size: 14px;" href="/admin/botline">リッチメニュー</a >
            </li>
            <li class="active">
                <a class="func-menu"  style="font-size: 14px;" href="/admin/linesetting">LINE連携の基本設定</a >
            </li>
        </ul>
    </nav>
    <!-- 設定 -->
    <div class="edit-container">
        <div class="settings-container">
            <div class="settings-segment">
                <div class="setting-title flex"><div class="title-text">【LINE Official Account Manager】</div><div class="title-border"></div></div>
                <div class="setting-item flex" style="display: flex; flex-direction: column; gap: 12px;">
                    <div class="flexbox-x-axis" style="justify-content: flex-start; gap: 24px;">
                        <label class="setting-item-label">Channel ID</label>
                        <input name="channel_id" type="text" class="" style="width:400px;" 
                            placeholder="<?php echo isset($sns_link_line_setting_basebot['channel_id']) ? $sns_link_line_setting_basebot['channel_id'] : ''; ?>"
                            value="<?php echo isset($sns_link_line_setting['channel_id']) ? $sns_link_line_setting['channel_id'] : ''; ?>">
                    </div>
                    <div class="flexbox-x-axis" style="justify-content: flex-start; gap: 24px;">
                        <label class="setting-item-label">Channel Secret</label>
                        <input name="channel_secret" type="text" class="" style="width:400px;"
                            placeholder="<?php echo isset($sns_link_line_setting_basebot['channel_secret']) ? $sns_link_line_setting_basebot['channel_secret'] : ''; ?>"
                            value="<?php echo isset($sns_link_line_setting['channel_secret']) ? $sns_link_line_setting['channel_secret'] : ''; ?>">
                    </div>
                    <div class="flexbox-x-axis" style="justify-content: flex-start; gap: 24px;">
                        <label class="setting-item-label">Webhook URL</label>
                        <input name="webhook_url" type="text" class="" style="width:400px;"
                            placeholder="<?php echo isset($sns_link_line_setting_basebot['webhook_url']) ? 'https://i.talkappi.com/webhook' . $sns_link_line_setting_basebot['webhook_url'] : ''; ?>"
                            value="<?php echo isset($sns_link_line_setting['webhook_url']) ? 'https://i.talkappi.com/webhook' . $sns_link_line_setting['webhook_url'] : ''; ?>">
                    </div>
                </div>
            </div>
            <div class="settings-segment">
                <div class="setting-title flex"><div class="title-text">【LINE Developers】</div><div class="title-border"></div></div>
                <div class="setting-item flex" style="display: flex; flex-direction: column; gap: 12px;">
                    <div class="flexbox-x-axis" style="justify-content: flex-start; gap: 24px;">
                        <label class="setting-item-label">ボットのベーシックID</label>
                        <input name="sns_id" type="text" class="" style="width:400px;"
                            placeholder="<?php echo isset($sns_link_line_setting_basebot['sns_id']) ? '@' . $sns_link_line_setting_basebot['sns_id'] : ''; ?>"
                            value="<?php echo isset($sns_link_line_setting['sns_id']) ? '@' . $sns_link_line_setting['sns_id'] : ''; ?>">
                    </div>
                    <div class="flexbox-x-axis" style="justify-content: flex-start; gap: 24px;">
                        <label class="setting-item-label">チャネルアクセストークン</label>
                        <input name="channel_access_token" type="text" class="" style="width:400px;"
                            placeholder="<?php echo isset($sns_link_line_setting_basebot['channel_access_token']) ? $sns_link_line_setting_basebot['channel_access_token'] : ''; ?>"
                            value="<?php echo isset($sns_link_line_setting['channel_access_token']) ? $sns_link_line_setting['channel_access_token'] : ''; ?>">
                    </div>
                    <div class="flexbox-x-axis" style="justify-content: flex-start; gap: 24px;">
                        <label class="setting-item-label">チャネルID</label>
                        <input name="weblogin_channel_id" type="text" class="" style="width:400px;"
                            placeholder="<?php echo isset($sns_link_line_setting_basebot['weblogin_channel_id']) ? $sns_link_line_setting_basebot['weblogin_channel_id'] : ''; ?>"
                            value="<?php echo isset($sns_link_line_setting['weblogin_channel_id']) ? $sns_link_line_setting['weblogin_channel_id'] : ''; ?>">
                    </div>
                    <div class="flexbox-x-axis" style="justify-content: flex-start; gap: 24px;">
                        <label class="setting-item-label">チャネルシークレット</label>
                        <input name="weblogin_channel_secret" type="text" class="" style="width:400px;"
                            placeholder="<?php echo isset($sns_link_line_setting_basebot['weblogin_channel_secret']) ? $sns_link_line_setting_basebot['weblogin_channel_secret'] : ''; ?>"
                            value="<?php echo isset($sns_link_line_setting['weblogin_channel_secret']) ? $sns_link_line_setting['weblogin_channel_secret'] : ''; ?>">
                    </div>
                </div>
            </div>
            <div class="settings-segment">
                <div class="setting-title flex"><div class="title-text">【チャットボットLINEチャンネルの公開設定】</div><div class="title-border"></div></div>
                <div class="setting-item flex" style="display: flex; flex-direction: column; gap: 12px;">
                    <div class="flexbox-x-axis" style="justify-content: flex-start; gap: 24px;">
                        <label class="setting-item-label">LINEの公開</label>
                        <div class="talkappi-radio" data-name="is_line_public"
                            data-value='<?php echo $support_line ? "1" : "0"?>'
                            data-source='{"0":"OFF", "1":"ON"}'>
                        </div>
                    </div>
                </div>
            </div>
            <div class="submit-btn-container" style="margin: 60px 0 0 134px;">
                <div class="btn-larger btn-blue js-action-save"><?php echo __('admin.common.button.save'); ?></div>
            </div>
        </div>
    </div>
</div>

<!-- /#page-wrapper -->