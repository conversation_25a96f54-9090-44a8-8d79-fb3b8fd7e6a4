<?php echo $menu ?>
<div class="content-container white border">
	<input type="hidden" name="message" id="message" value="<?php echo ($message) ?>" />
	<input type="hidden" name="act" id="act" />

	<div class="section-container">
		<div class="form-body">
			<div class="form-group">
				<label class="control-label col-md-2"></label>
				<img class="system_link_logo" src="https://admin.talkappi.com/docs/manual/talkappi_chatgpt.png"></img>
			</div>
			<div class="form-group">
				<label class="control-label col-md-2" style="font-weight: 600;">【概要説明】</label>
				<div class="col-md-8">
					<hr>
				</div>
			</div>
			<div class="form-group">
				<label class="control-label col-md-3"></label>
				<label class="col-md-8">OpenAI APIとの連携について、お持ちのAPI Keyで設定することですぐに連携が完了します。
					<br>利用するAIモデルを設定していただけます。
					<br>
					<br>※OpenAI APIはChatGPT Plusとは別物で、ご注意ください。
			</label>
			</div>
			<div class="form-group">
				<label class="control-label col-md-3"></label>
				<div class="col-md-9" style="display:flex;">
					<a href="https://cdn.talkappi.com/share/talkappi_chatgpt_0305.pdf" target="_blank"><div type="button" class="btn-smaller btn-blue">連携の概要説明</div></a>
				</div>
			</div>		
			<div class="form-group">
				<label class="control-label col-md-2" style="font-weight: 600;">【基本設定】</label>
				<div class="col-md-8">
					<hr>
				</div>
			</div>
			<div class="form-group">
				<div class="control-label col-md-3">
					API KEY
					<span class="icon-detail" style="margin: 0 0 0 0.5rem; text-align: left;" title="<?php echo('API keyはこちらのURLより取得できます。
					https://platform.openai.com/account/api-keys') ?>"></span>
				</div>
				<div class="col-md-8">
					<input name="api_key" id="api_key" type="text" class="form-control" value="<?php echo $api_key ?>" placeholder="XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX">
				</div>
			</div>
			<div class="form-group">
				<div class="control-label col-md-3">
					AIモデル
					<span class="icon-detail" style="margin: 0 0 0 0.5rem; text-align: left;" title="<?php echo('OpenAI社料金: https://openai.com/pricing') ?>"></span>
				</div>
				<div class="col-md-5">
					<div class="btn-group" id="model" data-toggle="buttons">
						<label class="btn default <?php if ($model === "text-davinci-003") echo ('active') ?>"><input name="model" type="radio" <?php if ($model === "text-davinci-003") echo ('checked') ?> value="text-davinci-003" class="toggle">text-davinci-003</label>
						<label class="btn default <?php if ($model === "gpt-3.5-turbo") echo ('active') ?>"><input name="model" type="radio" <?php if ($model === "gpt-3.5-turbo") echo ('checked') ?> value="gpt-3.5-turbo" class="toggle">gpt-3.5-turbo</label>
						<label class="btn default <?php if ($model === "gpt-3.5-turbo-16k") echo ('active') ?>"><input name="model" type="radio" <?php if ($model === "gpt-3.5-turbo-16k") echo ('checked') ?> value="gpt-3.5-turbo-16k" class="toggle">gpt-3.5-turbo-16k</label>
						<label class="btn default <?php if ($model === "gpt-4") echo ('active') ?>"><input name="model" type="radio" <?php if ($model === "gpt-4") echo ('checked') ?> value="gpt-4" class="toggle">gpt-4</label>
						<label class="btn default <?php if ($model === "gpt-4-32k") echo ('active') ?>"><input name="model" type="radio" <?php if ($model === "gpt-4-32k") echo ('checked') ?> value="gpt-4-32k" class="toggle">gpt-4-32k</label>
						<label class="btn default <?php if ($model === "gpt-4-turbo") echo ('active') ?>"><input name="model" type="radio" <?php if ($model === "gpt-4-turbo") echo ('checked') ?> value="gpt-4-turbo" class="toggle">gpt-4-turbo</label>
						<label class="btn default <?php if ($model === "gpt-4o") echo ('active') ?>"><input name="model" type="radio" <?php if ($model === "gpt-4o") echo ('checked') ?> value="gpt-4o" class="toggle">gpt-4o</label>
						<label class="btn default <?php if ($model === "gpt-4o-mini") echo ('active') ?>"><input name="model" type="radio" <?php if ($model === "gpt-4o-mini") echo ('checked') ?> value="gpt-4o-mini" class="toggle">gpt-4o-mini</label>
					</div>
				</div>
			</div>
			<div class="form-group">
				<label class="control-label col-md-2" style="font-weight: 600;">【詳細設定】</label>
				<div class="col-md-8">
					<hr>
				</div>
			</div>
			<div class="form-group">
				<div class="control-label col-md-3">
					公開モード
					<span class="icon-detail" style="margin: 0 0 0 0.5rem; text-align: left;" title="<?php echo('検証のみの場合new ai onを入力してchatGPTに切替、一般のユーザーに影響しない。new ai offを入力すると通常AIに戻す') ?>"></span>
				</div>
				<div class="col-md-4">
					<div class="btn-group" id="flg_ai_auto_apply" data-toggle="buttons">
						<label class="btn default <?php if ($flg_ai_auto_apply === "1") echo ('active') ?>"><input name="flg_ai_auto_apply" type="radio" <?php if ($flg_ai_auto_apply === "1") echo ('checked') ?> value="1" class="toggle">全員公開</label>
						<label class="btn default <?php if ($flg_ai_auto_apply === "0") echo ('active') ?>"><input name="flg_ai_auto_apply" type="radio" <?php if ($flg_ai_auto_apply === "0") echo ('checked') ?> value="0" class="toggle">検証のみ</label>
					</div>
				</div>
			</div>
			<div class="form-group">
				<div class="control-label col-md-3">
					登録情報以外の自由回答有無
					<span class="icon-detail" style="margin: 0 0 0 0.5rem; text-align: left;" title="<?php echo('禁止の場合、talkappiに登録した情報範囲内にAIが回答する。
					許可の場合、talkappiに登録情報を優先に回答した上、登録していない情報はchatGPTの判断で回答される。予想外の回答されるケースがあります。') ?>"></span>
				</div>
				<div class="col-md-4">
					<div class="btn-group" id="freetalk" data-toggle="buttons">
						<label class="btn default <?php if ($freetalk === "1") echo ('active') ?>"><input name="freetalk" type="radio" <?php if ($freetalk === "1") echo ('checked') ?> value="1" class="toggle">許可</label>
						<label class="btn default <?php if ($freetalk === "0") echo ('active') ?>"><input name="freetalk" type="radio" <?php if ($freetalk === "0") echo ('checked') ?> value="0" class="toggle">禁止</label>
					</div>
				</div>
			</div>
			<div class="form-group">
				<label class="control-label col-md-3">利用上限設定（千token単位）</label>
				<div class="col-md-8">
					<input name="limit" id="limit" type="text" class="form-control" value="<?php echo $limit ?>" placeholder="30000 (設定しない場合、上限なし）">
				</div>
			</div>


			<div class="submit-btn-container" style="margin: 60px 0 0 154px;">
				<div type="button" class="btn-larger btn-blue" id="saveButton">保存</div>
				<div class="btn-larger btn-red-border" id="deleteButton">
					<svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12">
						<g fill="none" fill-rule="evenodd" stroke-linecap="round">
							<g stroke="#e53361" stroke-width="2">
								<path d="M1 2.5L11 2.5M6 2L6 1M6 8L6 6" />
								<path stroke-linejoin="round" d="M2 6L2 11 10 11 10 6" />
							</g>
						</g>
					</svg>
				</div>
			</div>
		
		</div>
	</div>
</div>

<!-- END PAGE CONTENT-->