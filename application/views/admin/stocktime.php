<style>
.mybadge-success {
    background-color: #c6c6c6;
    background-image: none;
}

.mybadge-default {
    background-color: #45B6AF;
    background-image: none;
}
.mybadge {
	color:#fff;
    font-size: 18px;    
    font-weight: 300;
    text-align: center;
    height: 18px;
    padding: 3px 6px 3px 6px;
    -webkit-border-radius: 12px !important;
    -moz-border-radius: 12px !important;
    border-radius: 12px !important;
    text-shadow: none !important;
    text-align: center;
    vertical-align: middle;
}
</style>
			<!-- BEGIN PAGE HEADER-->
			<!-- BEGIN PAGE HEAD -->
			<div class="page-head">
				<!-- BEGIN PAGE TITLE -->
				<div class="page-title">
					<h1><?php echo($stock->stock_name) ?> <small style="margin-left: 40px;">
					<a href="/admin/stocktime?id=<?php echo($stock_id)?>&date=<?php echo(date('Y-m-d',strtotime("$stock_date -1 day")))?>" style="margin-right: 20px;">前日</a>
					<?php echo($stock_date) ?>
					<a href="/admin/stocktime?id=<?php echo($stock_id)?>&date=<?php echo(date('Y-m-d',strtotime("$stock_date +1 day")))?>" style="margin-left: 20px;">後日</a>
					<a href="/admin/stockday?id=<?php echo($stock_id)?>" style="margin-left: 40px;">戻る</a>
					</small></h1>
				</div>
			</div>
			<!-- END PAGE HEAD -->
			<!-- END PAGE HEADER-->
			<!-- BEGIN PAGE CONTENT-->
			<div class="row">
				<div class="col-md-12">
						<input type="hidden" name="stock_id" id="stock_id" value="<?php echo($stock_id) ?>">
						<input type="hidden" name="stock_date" id="stock_date" value="<?php echo($stock_date) ?>">
						<input type="hidden" name="range_start_date" id="range_start_date" value="<?php echo($stock_date) ?>">
						<input type="hidden" name="range_end_date" id="range_end_date" value="<?php echo($stock_date) ?>">
						<input type="hidden" name="no" id="no" value="">
						<input type="hidden" name="operation" id="operation" value="">
						<div class="form-body">
							<div class="portlet box">
							<div class="portlet-body">
							<table class="table table-striped table-bordered table-hover" id="">	
								<tbody>
								<tr>
								<?php
									for ($i = 0; $i < count($stocks); $i++) {
										if ($i%2==0 && $i!=0) {
											echo('</tr><tr>');
										}
										$item = $stocks[$i];
										$start_time = substr($item->start_time, 0, 5);
										$end_time = substr($item->end_time, 0, 5);
										echo("<td>");
										if ($item->stock == 0) {
											echo("<span class='mybadge mybadge-default'>$start_time ～ $end_time</span>");
											echo('<button type="button" class="btn gray action" style="margin-left:10px;float:right;" operation="1" no="' . $item->no . '">✖</button>');
											echo('<input name="end_date" id="end_date" value="' . $stock_date . '" style="float:right;" class="form-control form-control-inline input-small date-picker" size="16" data-date-format="yyyy-mm-dd" type="text"/>');
											echo('<input name="start_date" id="start_date" value="' . $stock_date . '" style="float:right;" class="form-control form-control-inline input-small date-picker" size="16" data-date-format="yyyy-mm-dd" type="text"/>');
										}
										else {
											echo("<span class='mybadge mybadge-success'>$start_time ～ $end_time</span>");
											echo('<button type="button" class="btn green-meadow action" style="margin-left:10px;float:right;" operation="0" no="' . $item->no . '">〇</button>');
											echo('<input name="end_date" id="end_date" value="' . $stock_date . '" style="float:right;" class="form-control form-control-inline input-small date-picker" size="16" data-date-format="yyyy-mm-dd" type="text"/>');
											echo('<input name="start_date" id="start_date" value="' . $stock_date . '" style="float:right;" class="form-control form-control-inline input-small date-picker" size="16" data-date-format="yyyy-mm-dd" type="text"/>');
										}
										echo("</td>");
									}
								?>
								</tr>
								</tbody>
							</table>																
							</div>
							</div>
						</div>
				</div>
			</div>

