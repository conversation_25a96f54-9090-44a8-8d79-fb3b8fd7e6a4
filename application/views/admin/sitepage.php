<style>
    .embed-options {
        margin-bottom: 8px;
    }
    .embed-option {
        cursor: pointer;
        border: 2px solid transparent;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 4px;
    }
    .embed-option.selected {
        border-color: #245BD6;
    }
</style>

<input type="hidden" name='cd' value='<?php echo $post['cd'] ?>'>
<input type="hidden" name='id' value='<?php echo $post['id'] ?>'>
<?php 
$page_data = $post['page_data']
?>
<nav class="top-nav">
    <ul class="">
        <li class="active"><a><?php echo __('admin.common.label.setting') ?></a></li>
        <?php if ($post['cd']) { ?>
            <li><a href="/admin/sitepagedetail?id=<?php echo $post['cd'] ?>"><?php echo __('admin.site.setting') ?></a></li>
            <li><a href="/admin/pagerefer?id=<?php echo $post['cd'] ?>"><?php echo __('admin.site.refer') ?></a></li>
        <?php } ?>
    </ul>
</nav>
<div class="content-container white border">
    <div class="section-container bottom-line">
        <h4><?php echo __('admin.common.label.basic.setting') ?></h4>
        <div class="form-body">
            <div class="form-group">
                <label class="col-md-2" style="width:120px;"><?php echo __('admin.common.label.title') ?></label>
                <div class="col-md-8">
                    <input type="text" name="name" class="form-control talkappi-textinput" data-max-input="20" placeholder="" value='<?php echo ($post['name']) ?>'/>
                    <p style="color: #245BD6;"><?php echo __('admin.site.name.explanation') ?></p> 
                </div>
            </div>
            <div class="form-group">
                <label class=" col-md-2" style="width:120px;"><?php echo __('admin.common.label.f.cd') ?></label>
                <div class="col-md-5">
                    <div class="talkappi-pulldown js-scene-cd" style="margin-right:10px;" data-name="scene_cd" data-value="<?php echo array_key_exists($post['scene_cd'], $scene_list) ? $post['scene_cd'] : ''; ?>" data-size="longer" data-source='<?php echo htmlspecialchars(json_encode($scene_list, JSON_UNESCAPED_UNICODE)) ?>'></div>
                </div>
            </div>
            <div class="form-group">
                <label class=" col-md-2" style="width:120px;"><?php echo __('admin.common.label.template') ?></label>
                <div class="template-select-radio">
                <input type="radio" name="template_cd" value="template1" id="template1" <?php if($post['template_cd']==""||$post['template_cd']=="template1") echo 'checked'?>>
                    <label for="template1">
                        <img src="../../assets/apps/site/template/template1/thmb.png" width="160" height="160">
                    </label>
                <input type="radio" name="template_cd" value="template2" id="template2" <?php if($post['template_cd']=="template2") echo 'checked'?>>
                    <label for="template2">
                        <img src="../../assets/apps/site/template/template2/thmb.png" width="160" height="160">
                    </label>
                </div>
            </div>
            <div class="form-group">
                <label class=" col-md-2" style="width:120px;"><?php echo __('admin.site.id') ?></label>
                <div class="col-md-4">
                    <div class="readonly-input flex-x-between" style="color: #A1A4AA;">
                        <span><?php echo $post['cd'] ?: __('admin.maximums.code_placeholder') ?></span>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class=" col-md-2" style="width:120px;"><?php echo __('admin.common.label.support.lang') ?></label>
                <div class="col-md-5">
                    <div class="talkappi-checkbox js-language" data-name="support_lang_cd" data-value='<?php echo json_encode($post['support_lang_cd']) ?>' data-source='<?php echo json_encode($_bot_lang, JSON_UNESCAPED_UNICODE|JSON_HEX_APOS) ?>'></div>
                </div>
            </div>
            <div class="form-group">
                <label class=" col-md-2" style="width:120px;"><?php echo __('admin.common.label.display.lang') ?></label>
                <div class="col-md-5">
                    <div class="talkappi-checkbox js-language" data-name="display_lang_cd" data-value='<?php echo json_encode($post['display_lang_cd']) ?>' data-source='<?php echo json_encode($_bot_lang, JSON_UNESCAPED_UNICODE|JSON_HEX_APOS) ?>'></div>
                </div>
            </div>
            <!-- GTM設定 -->
            <div class="lines-container">
                <label class=" col-md-2" style="width:120px;"><?php echo __('admin.inquiry.label.gtm_tag_setting'); ?>
                    <span class="icon-detail" style="margin: 0 0 0 0.5rem;" title="<?php echo __('admin.inquiry.label.gtm_detail') ?>"></span>
                </label>
                <div class="col-md-4">
                    <input type="text" name="gtm_tag_id" value="<?php echo isset($page_data['gtm_tag_id']) ? $page_data['gtm_tag_id'] : '' ?>" placeholder="GTM-XXXXXX" class="text-input-longer" style="width: 200px;">
                </div>
            </div>
        </div>
    </div>
    <div class="section-container">
        <h2><?php echo __('admin.common.label.public_setting') ?></h2>
        <div class="form-body">
            <div class="form-group">
                <label class=" col-md-2" style="width:140px;"><?php echo __('admin.common.label.public_url') ?></label>
                <div class="col-md-8">
                <p class="public-url-area">
                    <span class="public-url-raw public-url-link copy" @click="copyUri" data-clipboard-action="copy" data-clipboard-text="<?php echo htmlspecialchars($url) ?>"><?php echo htmlspecialchars($url) ?></span>
                    <span class="copy font-standard font-family-v1 font-color-v1 survey-copy-url" @click="copyUri" data-clipboard-action="copy" data-clipboard-text="<?php echo htmlspecialchars($url) ?>"><?php echo __('admin.common.label.copy')?></span>
                </p>
                </div>
            </div>
        </div>
        <div class="form-group">
            <label class="col-md-2" style="width:140px;"><?php echo __('admin.sitepage.label.embed_link') ?></label>
            <div class="col-md-8">
                <div class="embed-options" style="display:flex;gap:24px;align-items:flex-start">
                    <div class="embed-option selected" data-type="scroll">
                        <img src="/assets/common/images/sitepage-embed.gif" width="150" height="100" />
                        <p><?= __('admin.sitepage.label.embed_scroll_mode') ?></p>
                    </div>
                    <div class="embed-option" data-type="grid">
                        <img src="/assets/common/images/sitepage-embed-grid.gif" width="150" height="100" />
                        <p><?= __('admin.sitepage.label.embed_grid_mode') ?></p>
                    </div>
                </div>
                <div class="embed-codes">
                    <div class="embed-code embed-scroll-code">
                        <?php $embed_code = '<iframe width="100%" height="100%" src="' . $embed_url . '"></iframe>'?>
                        <p class="public-url-area">
                            <span class="public-embed-url-raw public-url-link copy" @click="copyUri" data-clipboard-action="copy" data-clipboard-text="<?php echo htmlspecialchars($embed_code) ?>"><?php echo htmlspecialchars($embed_code) ?></span>
                            <span class="copy font-standard font-family-v1 font-color-v1 survey-copy-url" @click="copyUri" data-clipboard-action="copy" data-clipboard-text="<?php echo htmlspecialchars($embed_code) ?>"><?php echo __('admin.common.label.copy')?></span>
                        </p>
                    </div>
                    <div class="embed-code embed-grid-code hide">
                        <?php $embed_grid_code = '<iframe width="100%" height="100%" src="' . $embed_url . '&display=grid"></iframe>'?>
                        <p class="public-url-area">
                            <span class="public-embed-url-raw public-url-link copy" @click="copyUri" data-clipboard-action="copy" data-clipboard-text="<?php echo htmlspecialchars($embed_grid_code) ?>"><?php echo htmlspecialchars($embed_grid_code) ?></span>
                            <span class="copy font-standard font-family-v1 font-color-v1 survey-copy-url" @click="copyUri" data-clipboard-action="copy" data-clipboard-text="<?php echo htmlspecialchars($embed_grid_code) ?>"><?php echo __('admin.common.label.copy')?></span>
                        </p>
                    </div>
                </div>
            </div>
        </div>
        <div class="form-group">
            <label class="col-md-2" style="width:140px;">
                <?php echo __('admin.common.label.redirect_to'); ?>
				<span class="icon-detail" style="margin: 0 0 0 0.5rem;" title="<?php echo __('admin.common.label.redirect_to_hint'); ?>"></span>
            </label>
            <div class="col-md-8">
			<input type="text" name="redirect_url" value="<?php echo isset($page_data['redirect_url']) ? $page_data['redirect_url'] : '' ?>" class="text-input-longer">
            </div>
		</div>
        <div class="form-group">
            <label class="col-md-2" style="width:140px;">
                <?php echo __('inquiry.login.title'); ?>
            </label>
            <div class="col-md-8">
                <div class="talkappi-radio" data-name="secret_mode" style="padding-bottom: 16px;" data-value='<?php echo $page_data['secret_mode'] ? $page_data['secret_mode'] : '0' ?>' data-source='{"0":"<?php echo __('admin.inquiry.label.answer_limit_no'); ?>", "1":"<?php echo __('admin.inquiry.label.answer_limit_yes'); ?>"}'></div>
                <input class="text-input-longer js-password" name="password" style='<?php if (!$page_data['secret_mode'] || $page_data['secret_mode'] == 0) echo "display:none" ?>' value="<?php echo $page_data['password']?>" placeholder="<?php echo __('inquiry.login.description'); ?>">
            </div>
        </div>
    </div>
    <div class="form-actions">
        <div class="row">
            <div class="col-md-offset-2 col-md-9">
                <div class="actions-container">
                    <button type="button" class="action-button page btn-blue js-action-save"><?php echo __('admin.common.button.save') ?></button>
                    <?php if ($post['cd']): ?>
                        <a class="action-button page btn-white js-action-verify" href="<?php echo $verify_url ?>" target="_blank"><?php echo __('admin.common.button.verify') ?></a>
                    <?php endif; ?>
                    <a class="action-button page btn-white js-action-back"><?php echo __('admin.common.button.return_to_list') ?></a>
                    <?php if ($post['cd']): ?>
                        <span class="action-button page btn-red-border icon-only js-action-delete"><span class="icon-delete"></span></span>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>