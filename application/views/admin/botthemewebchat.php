<div class="flexbox">
	<div style="width:500px">
		<div class="category-separator">
			<h2 class="setting-header" style="margin: 0;">スタイル</h2>
		</div>
		<div class="lines-container" style="align-items: center;">
			<div class="basic-label">ヘッダー部背景色</div>
			<div class="">
				<div class="talkappi-colorpicker js-theme-color" data-name="theme-bkcolor" data-value="<?php if ($post != NULL) echo ($post['theme-bkcolor']) ?>"></div>
			</div>
		</div>
		<div class="lines-container" style="align-items: center;">
			<div class="basic-label">ヘッダー部文字色</div>
			<div class="">
				<div class="talkappi-colorpicker js-theme-color" data-name="theme-color" data-value="<?php if ($post != NULL) echo ($post['theme-color']) ?>"></div>
			</div>
		</div>
		<div class="lines-container" style="align-items: center;">
			<div class="basic-label">リンク色</div>
			<div class="">
				<div class="talkappi-colorpicker js-theme-color" data-name="href-color" data-value="<?php if ($post != NULL) echo ($post['href-color']) ?>"></div>
			</div>
		</div>
		<div class="lines-container" style="align-items: center;">
			<div class="basic-label">閉じるアイコンの色</div>
			<div class="">
				<div class="talkappi-colorpicker js-theme-color" data-name="close-button-color" data-value="<?php if ($post != NULL) echo ($post['close-button-color']) ?>"></div>
			</div>
		</div>
		<div class="form-group" style="display: none;">
			<label class="control-label col-md-3">ロゴradius</label>
			<div class="col-md-2">
				<input name="logo-radius" type="text" class="form-control" placeholder="フォント" value="<?php if ($post != NULL) echo ($post['logo-radius']) ?>">
			</div>
		</div>
		<div class="lines-container">
			<div class="basic-label">ロゴ画像</div>
			<div style="display: block; width:75%">
				<div style="" class="talkappi-upload" data-name="logo_file_base64" data-label="<?php echo basename($post['logo-file']) ?>" data-type="img" data-ratio="1:1" data-url="<?php echo ($post['logo-file']) ?>"></div>
			</div>
		</div>
		<div class="category-separator">
			<h2 class="font-standard font-family-v3" style="margin: 0;">フォント設定</h2>
		</div>
		<?php foreach ($_bot_lang as $k => $v) { ?>
			<div class="lines-container">
				<div class="basic-label"><?php echo ($v); ?>フォント</div>
				<div class="col-md-9">
					<input name="theme-font-family-<?php echo ($k) ?>" type="text" class="text-input-longer" value="<?php if (array_key_exists('theme-font-family-' . $k, $post)) echo (htmlspecialchars($post['theme-font-family-' . $k])) ?>">
				</div>
			</div>
		<?php } ?>
	</div>
	<div class="chatwindow-prev" style="width: 370px; margin-left:114px">
		<!-- <select class="chatwindow-prev-update"></select> -->
		<div class="chatwindow-prev-contents">
			<div class="chatwindow-prev-header">
				<img style="margin: 0 10px 0px 16px" src="/assets/admin/css/img/icon-pc.svg" alt="">
				<span>プレビュー</span>

			</div>
			<div class="chatwindow-prev-bot-name" style="background:<?php if ($post['theme-bkcolor']) echo ($post['theme-bkcolor']) ?>;color:<?php if ($post['theme-color']) echo ($post['theme-color']) ?>">
				<img <?php if ($post['logo-file'] == '') echo ('style="display:none;"'); ?> id="logo_file_base64_prev" src="<?php echo ($post['logo-file']) ?>?t=<?php echo (time()) ?>" alt="">
				<div id="js-bot-name" <?php if ($post['logo-file'] == '') echo ('style="margin-left:12px;"'); ?>>
					<?php
					foreach ($_bot_lang as $lang => $lang_name) {
						if (array_key_exists($lang, $bot_name)) {
							$show_bot_name = $bot_name[$lang];
						}
						else {
							$show_bot_name = '';
						}
						if (array_key_exists($lang, $ai_title)) {
							$show_ai_title = $ai_title[$lang];
						}
						else {
							$show_ai_title = '';
						}
						if ($lang === $default_lang) {
							echo ('<p id="' . $lang . '_prev_bot_name" style="font-size: 15px; font-weight: 600;">' . $show_bot_name . '</p>');
							echo ('<p id="' . $lang . '_prev_ai_title" style="font-size: 10px; margin-top: 3px;">' . $show_ai_title . '</p>');
						} else {
							echo ('<p id="' . $lang . '_prev_bot_name" style="font-size: 15px; font-weight: 600; display:none;">' . $show_bot_name . '</p>');
							echo ('<p id="' . $lang . '_prev_ai_title" style="font-size: 10px; margin-top: 3px; display:none;">' . $show_ai_title . '</p>');
						}
					}
					?>
				</div>
				<svg class="" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
					<g fill="none" fill-rule="evenodd" stroke-linecap="round">
						<g stroke="<?php if (isset($post['close-button-color'])) echo $post['close-button-color']; ?>" stroke-width="2">
							<g>
								<path d="M16 16L0 0" transform="translate(4 4)" />
								<path d="M16 16L0 0" transform="translate(4 4) matrix(-1 0 0 1 16 0)" />
							</g>
						</g>
					</g>
				</svg>
			</div>
			<div class="chatwindow-prev-bot-main">
				<?php
				foreach ($_bot_lang as $lang => $lang_name) {
					if (array_key_exists($default_lang, $bot_name)) {
						$show_bot_name = $bot_name[$default_lang];
					}
					else {
						$show_bot_name = '';
					}
					if (array_key_exists($lang, $msg_welcome_bot)) {
						$show_msg_welcome = $msg_welcome_bot[$lang];
					}
					else {
						$show_msg_welcome = '';
					}
					if ($lang === $default_lang) {
						echo ('<div id="' . $lang . '_prev_message" class="chatwindow-prev-bot-main-message">');
					} else {
						echo ('<div id="' . $lang . '_prev_message" class="chatwindow-prev-bot-main-message" style="display:none">');
					}
					echo ('<p>' . str_replace('{bot_name}', $show_bot_name, $show_msg_welcome) . '</p>');
					echo ('</div>');
				}
				?>
			</div>

			<div class="chatwindow-prev-bot-footer">
				<div class="chatwindow-prev-bot-footer-top">
					<span>実際のUIは「基本設定」の「多言語プレビュー」からご確認をお願いします</span>
				</div>
				<div class="chatwindow-prev-bot-footer-bottom">
					<div class="chatwindow-prev-bot-footer-bottom-title">言語別プレビュー</div>
					<div class="prev-lang-wrap">
						<?php
						foreach ($_bot_lang as $lang => $lang_name) {
							if ($lang === $default_lang) {
								$checked = "checked";
							} else {
								$checked = "";
							}
							echo ('<input id="' . $lang . '" style="display: none; "' . $checked . ' name="lang-prev" type="radio" />');
							echo ('<label class="prev-lang-label" for="' . $lang . '">' . $lang_name . '</label>');
						}
						?>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>