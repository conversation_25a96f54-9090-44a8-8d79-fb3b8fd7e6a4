<style>
	table.table thead tr th {
		font-size: 14px;
		font-weight: 600;
		font-stretch: normal;
		font-style: normal;
		line-height: normal;
		letter-spacing: normal;
		-webkit-font-smoothing: auto;
	}

	.js-multi-select-bot-container .bs-select .dropdown-menu {
		width: fit-content;
	}

</style>
<?php $page_type = substr($_action, 0, strlen($_action) - 1);?>

<?php 
	$last_gpt_faq_update_time = new DateTime($faq_update_by_gpt_batch_last_time);
	$current_time = new DateTime();
	$last_gpt_faq_update_time_interval = $current_time->diff($last_gpt_faq_update_time);
	$update_under_1month = $last_gpt_faq_update_time_interval->days < 31 && $faq_update_by_gpt_batch_last_time !== '1970-01-01 00:00:00';
	$next_update_time = clone $last_gpt_faq_update_time;
    $next_update_time->modify('+31 days');
?>
<script type="text/javascript">
	const _update_under_1month = <?php echo $update_under_1month ? 'true' : 'false'; ?>;
	const _is_updating_faq_by_ai = <?php echo $is_updating_faq_by_ai ? 'true' : 'false'; ?>;
	const _role_cd = <?php echo $_user->role_cd; ?>;
	const _next_update_time = '<?php echo $next_update_time->format('Y-m-d'); ?>';
	const _flg_update_faq_by_gpt = <?php echo $flg_update_faq_by_gpt == '1' ? 'true' : 'false'; ?>;
	const _flg_create_faq_by_gpt = <?php echo $flg_create_faq_by_gpt == '1' ? 'true' : 'false'; ?>;
	const _enable_create_faq = <?php echo $enable_create_faq ? 'true' : 'false'; ?>;
</script>

<div id="page_type" style="display:none;"><?php echo $page_type?></div>
<input type="hidden" name="bot_ids" value="<?php echo $post['bot_ids']?>">
<input type="hidden" name="bot_id" value="<?php echo $_bot_id?>">
<input type="hidden" name="intent_cd" value="<?php echo $post['intent_cd']?>">
<input type="hidden" name="sub_intent_cd" value="<?php echo $post['sub_intent_cd']?>">

<input type="hidden" name="act" id="act" value="">
<input type="hidden" name="edit_intent_cd" value="">
<input type="hidden" name="edit_sub_intent_cd" value="">
<input type="hidden" name="edit_lang_cd" value="">
<input type="hidden" name="edit_bot_id" value="">

<div style="margin:-15px -15px 0 -15px;background:#F6F7F9;padding-left:10px;padding-right:10px;">
	<nav class="button-tab">
		<ul class="">
		<?php
			$i=0;
			foreach ($faq_type as $type_cd) {
				if ($i == 0) {
					if ($post['type']=='00') {
						echo('<li class="active">');
					} else {
						echo('<li>');
					}
					echo('<a class="func-menu" href="/admin/' . $page_type . 's?type=00">' . __('admin.common.button.all_classifications') . '</a>');
					echo('</li>');
					if ($faq_div != '') {
						if ($post['type']=='') {
							echo('<li class="active">');
						} else {
							echo('<li>');
						}
						echo('<a class="func-menu" href="/admin/' . $page_type . 's?type=">' . __('admin.common.button.uncategorized') . '</a>');
						echo('</li>');
					}
				}
				$i++;
				if ($type_cd == $post['type']) {
					echo('<li class="active">');
				}
				else {
					echo('<li>');
				}
				echo('<a class="func-menu" href="/admin/' . $page_type . 's?type=' . $type_cd . '" >');
				if (array_key_exists($type_cd, $class_types)) echo ($class_types[$type_cd]);
				echo('</a>');
			 }
		?>	
		</ul>	
	</nav>  
</div>

<div class="flex-x-between" style="margin-top:10px;margin-bottom:10px;">
	<div style="display: flex;align-items: center;gap: 4px;">
		<div><?php echo __('admin.template.menu.faq_list') ?></div>
		<?php if ($admin_lang_cd == 'ja') { 
				// FAQ自動作成、自動更新ボタン
				// admin/talksページのみ表示 
				// 日本語の管理画面のみFAQ更新機能を表示する
			?>
				<?php echo('<div id="react-update-facility-all-faq-by-gpt"></div>'); ?>
		<?php } ?>
	</div>
	<div>
		
	</div>
	
		<div class="flex-x-between">
			<?php 
				if (count($sub_bots) > 0) {
					$single_sub_bots = ['0'=>'ー ' .  __('admin.faqs.label.check_multiple_facility') . 'ー'] + $sub_bots;
					echo('<span>' . __('admin.template.menu.change_bot') . ':</span>');
					echo('<div class="pulldown" style="width: fit-content;">');
					echo('<div class="talkappi-pulldown js-select-bot" data-name="bot_id" data-value="'. $post['bot_id'] . '" data-source=\'' . json_encode($single_sub_bots, JSON_UNESCAPED_UNICODE | JSON_HEX_APOS) . '\'></div>');
					echo('</div>');
				}
			?>

			<?php if ($_user->role_cd == "99" || ($_user->role_cd == '09' && $_bot->bot_class_cd != "01" && $_bot->bot_class_cd != "04")) { ?>
				<a href="/ajax/intentcsv?answer_type=<?php echo $post['answer_type']?>">
					<button type="button" class="btn-smaller btn-white">
						<span class="icon-export"></span><?php echo __('admin.common.button.csv_export_internal') ?>
					</button>
				</a>
			<?php }?>
			<?php if ($_bot_setting['flg_faq_site'] == '1' && $page_type == 'faq') {?>
				<button type="button" class="btn-smaller btn-blue js-add-faq"><span class="icon-add-white"></span><?php echo __('admin.faqs.label.faq_page_question_add') ?></button>
			<?php }?>	
			<?php if ($_bot_setting['flg_ai_bot'] == '1' && $page_type == 'talk') {?>
				<button type="button" class="btn-smaller btn-blue js-add-talk"><span class="icon-add-white"></span><?php echo __('admin.faqs.label.chatbot_question_add') ?></button>
			<?php }?>	
		</div>
</div>

<div class="content-container white" style="position: relative;">
	<table class="table table-striped table-bordered table-hover js-data-table">
		<div class="custom-data-table-header" style="top: 16px;">
			<div class="js-lang">
				<div class="talkappi-pulldown js-edit-lang" style="margin-right:10px;" data-name="lang_cd" data-value=<?php echo $post['lang_cd']?> data-source='<?php echo(json_encode($lang_array, JSON_UNESCAPED_UNICODE)) ?>'></div>
			</div>
			<div class="pulldown">
				<div class="talkappi-pulldown js-answer_type" style="margin-right:10px;" data-name="answer_type" data-value=<?php echo $post['answer_type']?> data-source='{"01":"<?php echo __('admin.common.label.all_registration_status') ?>", "02":"<?php echo __('admin.common.label.registered') ?>", "03":"<?php echo __('admin.common.label.unregistered') ?>"}'></div>
			</div>
			<div class="pulldown">
				<div class="talkappi-pulldown js-required" style="margin-right:10px;" data-name="required" data-value=<?php echo $post['required']?> data-source='{"01":"<?php echo __('admin.common.label.all_registration_types') ?>", "02":"<?php echo __('admin.common.label.required_registration') ?>", "03":"<?php echo __('admin.common.label.optional_registration') ?>"}'></div>
			</div>
			<?php
			if (isset($flg_update_faq_by_gpt) && $flg_update_faq_by_gpt == '1'  && $admin_lang_cd == 'ja') {
				$data_source = [
					"01"=>"すべてのAI自動更新の可否", 
					"02"=>"AI更新対象&nbsp;-&nbsp;全量対象", 
					"03"=>"AI更新対象&nbsp;-&nbsp;更新有効", 
					"04"=>"AI更新対象&nbsp;-&nbsp;更新停止", 
					"05"=>"AI更新対象外&nbsp;-&nbsp;対象外"
				];
				$faq_update_by_gpt_filter_data_name = 'faq_update_by_ai';
				if ($page_type == 'faq') {
					$faq_update_by_gpt_filter_data_name = 'faq_update_by_ai_faq';
				}
				echo('<div class="pulldown" style="width: 190px;">
					<div class="talkappi-pulldown js-faq-update-by-ai" style="margin-right:10px;" data-name="' . $faq_update_by_gpt_filter_data_name . '" data-value=');
				echo($post[$faq_update_by_gpt_filter_data_name]);
				echo(' data-source=');
				echo(json_encode($data_source, JSON_UNESCAPED_UNICODE));
				echo( '></div></div>');
			}
				
			?>
		</div>
		<div class="custom-data-table-header" style="top: 50px; width:calc(100% - 40px)">
			<?php 
			if (count($sub_bots) > 0 && $post['bot_id'] == '0') {
				//echo('<span>FAQ施設横比較：</span>');
				echo('<div class="pulldown js-multi-select-bot-container" style="width:320px;">');
				echo('<select class="bs-select form-control js-multi-select-bot" multiple>');
				echo('<option value="0">すべての施設</option>');
				foreach($sub_bots as $k=>$v) {
					echo('<option value="' . $k . '">' . $v .'</option>');
				}
				echo('</select>');
				echo('</div>');
				echo('<div class="pulldown" style="margin-left:10px;flex:auto;">');
				echo('<select class="form-control select2me js-select2-faq">');
				foreach($m_faqs as $k=>$v) {
					if ($k == $post['intent_cd'] . '#' . $post['sub_intent_cd']) {
						echo('<option selected value="' . $k . '">' . $v . '</option>');
					}
					else {
						echo('<option value="' . $k . '">' . $v . '</option>');
					}
				}
				echo('</select>');
				echo('</div>');
			}
			?>
		</div>
		<thead>
			<tr>
				<?php 
				if (array_key_exists('bot_id', $post) && $post['bot_id'] == '0') {
					echo('<th style="width:60px;">' . __('admin.common.label.facility') . '</th>');
				}
				else {
					if ($_bot->bot_class_cd == '01' || $_bot->bot_class_cd == '04') {
						echo('<th style="width:60px;">' . __('admin.common.label.faq_number') . '</th>');
					}
				}
				?>
				<th style="width:180px;"><?php echo __('admin.common.label.question') ?></th>
				<th class="table-answer"><?php echo __('admin.common.label.details_of_registration') ?></th>
				<th style="width:50px;"><?php echo __('admin.common.label.edit_answer') ?></th>
				<?php if ($action == 'faqs' && $_user->role_cd == '99') {
					echo('<th style="width:60px;">' . __('admin.common.label.user_screen_display_order') . '</th>');
				}?>
				<!-- <th style="width:60px;">修正依頼</th> -->
			</tr>
		</thead>
		<tbody>
			<?php
				if ($_bot_id == 0) {
					// 基準BOT
					$cur_bot_id = 0;
				}
				else {
					$cur_bot_id = $_bot_id;
					if (isset($post['bot_id']) && $post['bot_id'] == '0') {
						// 横並び
						$cur_bot_id = null;
					}
					else {
						
						if (isset($post['bot_id'])) $cur_bot_id = $post['bot_id'];
					}
				}
				foreach ($item_talks as $key=>$talks) {
					$answer_info = ['title'=>'', 'answer'=>'', 'self'=>false];
					if ($cur_bot_id === null) {
						$talk = $talks;
						$answer_info['self'] = true;
						$answer_info['title'] = $talk['question'];
						if ($talk['facility_question_title'] != '') $answer_info['title'] = $talk['facility_question_title'];
						if ($answer_info['answer'] == '') $answer_info['answer'] = $talk[$post['lang_cd']];
					}
					else {
						$talk = current($talks);
						if($page === 'talk' && strpos($talk['faq_id'],'faq-') === 0){
							continue;
						}
						//選択した言語のm_bot_intentデータが存在しなければ、非表示
						if(!array_key_exists($post['lang_cd'], $talk)) continue;
						// 登録必須かどうか
						// 全て => 何もしない
						// 必須
						if($post['required'] === "02" && $talk['level'] != 1) continue;
						// 任意
						if($post['required'] === "03" && $talk['level'] != 2) continue;

						// AI自動更新可否
						// ボットを切替後にfilterまた適用されたのバグを修正 -> Filterを適用前に、高度な設定に『FAQ更新自動検出利用必ずチェック』する。
						if (isset($flg_update_faq_by_gpt) && $flg_update_faq_by_gpt == '1' && $admin_lang_cd == 'ja') {
							if ($page_type == 'talk') { // チャットボットページのFilter
								// AI更新対象（全量）
								if($post['faq_update_by_ai'] === "02" && !array_key_exists($talk['intent_cd'] . '-' . $talk['sub_intent_cd'], $faqs_can_update_by_gpt)) continue;
								// AI更新対象（更新有効中）
								if($post['faq_update_by_ai'] === "03" && (!array_key_exists($talk['intent_cd'] . '-' . $talk['sub_intent_cd'], $faqs_can_update_by_gpt) || $faqs_can_update_by_gpt[$talk['intent_cd'] . '-' . $talk['sub_intent_cd']] === 0)) continue;
								// AI更新対象（更新停止中）
								if($post['faq_update_by_ai'] === "04" && (!array_key_exists($talk['intent_cd'] . '-' . $talk['sub_intent_cd'], $faqs_can_update_by_gpt) || $faqs_can_update_by_gpt[$talk['intent_cd'] . '-' . $talk['sub_intent_cd']] === 1)) continue;
								// AI更新対象外
								if($post['faq_update_by_ai'] === "05" && array_key_exists($talk['intent_cd'] . '-' . $talk['sub_intent_cd'], $faqs_can_update_by_gpt)) continue;
							}
							if ($page_type == 'faq') { // FAQページのFilter
								// AI更新対象（全量）
								if($post['faq_update_by_ai_faq'] === "02" && !array_key_exists($talk['intent_cd'] . '-' . $talk['sub_intent_cd'], $faqs_can_update_by_gpt)) continue;
								// AI更新対象（更新有効中）
								if($post['faq_update_by_ai_faq'] === "03" && (!array_key_exists($talk['intent_cd'] . '-' . $talk['sub_intent_cd'], $faqs_can_update_by_gpt) || $faqs_can_update_by_gpt[$talk['intent_cd'] . '-' . $talk['sub_intent_cd']] === 0)) continue;
								// AI更新対象（更新停止中）
								if($post['faq_update_by_ai_faq'] === "04" && (!array_key_exists($talk['intent_cd'] . '-' . $talk['sub_intent_cd'], $faqs_can_update_by_gpt) || $faqs_can_update_by_gpt[$talk['intent_cd'] . '-' . $talk['sub_intent_cd']] === 1)) continue;
								// AI更新対象外
								if($post['faq_update_by_ai_faq'] === "05" && array_key_exists($talk['intent_cd'] . '-' . $talk['sub_intent_cd'], $faqs_can_update_by_gpt)) continue;
							}
						}
						foreach($talks as $k=>$v) {
							if (array_key_exists($post['lang_cd'], $v) && $v[$post['lang_cd']] != "") {
								if ($k == $cur_bot_id) {
									if ($v['facility_question_title'] != '') $answer_info['title'] = $v['facility_question_title'];
									$answer_info['answer'] = $v[$post['lang_cd']];
									$answer_info['self'] = true;
								}
								else {
									if ($v['inherit'] == 1) {
										if ($answer_info['answer'] == '') $answer_info['answer'] = $v[$post['lang_cd']];
										if ($answer_info['title'] == '') $answer_info['title'] = $v['facility_question_title'];
									}
								}
							}
						}
						if ($answer_info['title'] == '') $answer_info['title'] = $talk['question'];
						// 登録済みかどうか
						// 全て => 何もしない
						// 登録済みを選択した場合
						if($post['answer_type'] === "02" && $answer_info['answer'] == '') continue;
						// 未登録を選択した場合
						if($post['answer_type'] === "03" && $answer_info['answer'] != '') continue;
					}
					echo('<tr class="odd gradeX">');
						if ($cur_bot_id === null) {
							// 施設
							echo('<td data-bot-id="' . $talk['bot_id'] . '">');
							echo($talk['bot_id'] . '<br/>');
							echo($sub_bots[$talk['bot_id']]);
							echo('</td>');
						}
						else {
							// FAQ番号
							if ($_bot->bot_class_cd == '01' || $_bot->bot_class_cd == '04') {
								echo('<td>' . $talk['faq_id'] .'');
								if ($talk['level'] == 1) echo('<br/><span class="btn light-red" style="margin: 10px 0 0 0 !important;">' . __('admin.common.label.required_registration') .  '</span>');
								if ($talk['level'] == 4) echo('<br/><span class="btn light-gray" style="margin: 10px 0 0 0 !important;">' . __('admin.faqs.label.for_faq_system') .  '</span>');
								echo('</td>');
							}
						}
						// 質問
						echo('<td>');
							echo('<div>');
							echo(str_replace(array("\r\n", "\r", "\n"), "<br />", $answer_info['title']));
							if ($_bot->bot_class_cd == '01') {
								if ($talk['area_cd'] != '') echo('<br /><span class="badge badge-primary badge-roundless">' . $talk['area_cd'] . '</span>');
								//echo('<br /><br />');
								//echo('<select class="context_cd" intent_cd="' . $talk['intent_cd'] . '" >');
								//foreach($context_cd as $cd=>$val) {
								//	echo('<option value="' . $cd . '">' . $val . ' </option>');
								//}
								//echo('</select>');
								//echo Form::select('context_cd', $context_cd, $talk['context_cd'], array('class' => 'context_cd','intent_cd' => $talk['intent_cd']));
							}
							if ($_user->role_cd == '99' || $_user->role_cd == '09') {
								if ($talk['sub_intent_cd'] === '') {
									echo('<br/>' . $talk['intent_cd']);
								}
								else {
									echo('<br/>' . $talk['intent_cd'] . " . " . $talk['sub_intent_cd']);
								}
							}
							echo('</div>');
							// 更新できるFAQのアイコン表示
							echo('<div style="margin-top:8px;display: flex;">');
							if (array_key_exists($talk['intent_cd'] . '-' . $talk['sub_intent_cd'], $faqs_can_update_by_gpt) && isset($flg_update_faq_by_gpt) && $flg_update_faq_by_gpt == '1' && $admin_lang_cd == 'ja') {
								$key = $talk['intent_cd'] . '-' . $talk['sub_intent_cd'];
    							$iconClass = $faqs_can_update_by_gpt[$key] === 1 ? 'icon-generative-ai' : 'icon-generative-ai-grey';
								$aiUpdateOnText = $faqs_can_update_by_gpt[$key] === 1 ? '<span class="ai-update-text" style="display: flex;flex-direction: row; color: black;">自動更新有効中</span>' : '<span class="ai-update-text" style="display: flex;flex-direction: row; color: rgb(161, 164, 170);">自動更新停止中</span>';
								$tooltipText = $faqs_can_update_by_gpt[$key] === 1 ? 'クリックして更新停止にします' : 'クリックして更新有効にします';
								echo('<div style="position: relative; display: inline-block;">
										<span style="background-color: white;margin-right: 10px;padding: 0px 5px;height: 24px;display:flex;justify-content:center;align-items:center;border-radius: 4px;cursor: pointer;flex-direction:row;gap:4px;border: 1px solid rgb(200, 202, 206);" 
											class="js-add-faq-to-intent-gpt-settings" 
											intent_cd=' . $talk['intent_cd']. ' 
											sub_intent_cd=' . $talk['sub_intent_cd'] . '
									  	>
										  <span style="position: absolute; bottom: -100%; left: 100%; transform: translateX(-50%); background-color: #e0e0e0; color: black; padding: 3px 3px; border-radius: 1px; white-space: nowrap; font-size: 9px; z-index: 1000; margin-bottom: 5px; display: none; 0 0 4px rgba(214, 214, 214);" class="generate-faq-by-ai-tooltip">' . 
										  	htmlspecialchars($tooltipText) . 
										  	'
										  </span>
									    	<span class="' . $iconClass . ' ai-update-icon" style="margin-top:0px;"></span>
											<span class="ai-update-text" style="display: flex;flex-direction: row; color: rgb(161, 164, 170);">'. $aiUpdateOnText . '</span>
									  	</span>
									  </div>'); 
							}
							if (array_key_exists('bot_id', $post) && $post['bot_id'] == '0') {
								if (array_key_exists($talk['bot_id'], $faq_editing) /*&& in_array($post['lang_cd'], $faq_editing[$talk['bot_id']])*/) {
									echo('<a href="/admin/talknew?cd=' . $talk['intent_cd'] . '&sub_cd=' .$talk['sub_intent_cd'] . '&lang=' . $post['lang_cd']. '">');
									echo('<span class="btn square yellow" ' . __('admin.common.label.editing') . '</span>');
									echo('</a>');
								}
							}
							else {
								if (in_array($talk['intent_cd'] . '#' . $talk['sub_intent_cd'], $faq_editing)) {
									echo('<a href="/admin/talknew?cd=' . $talk['intent_cd'] . '&sub_cd=' .$talk['sub_intent_cd'] . '&lang=' . $post['lang_cd']. '">');
									echo('<span class="btn square yellow" >' . __('admin.common.label.editing') . '</span>');
									echo('</a>');
								}
							}
							echo('</div>');
							
						echo('</td>');
						// 回答
						echo('<td>');
							if (isset($faqs_that_have_diff_found_by_ai[$talk['intent_cd'] . '-' . $talk['sub_intent_cd']]) && isset($flg_update_faq_by_gpt) && $flg_update_faq_by_gpt == '1' && $admin_lang_cd == 'ja') {
								echo("<div style='margin-top:5px; margin-bottom:5px; border-radius: 2px; border: 1px solid var(--solid-color-solid-blue, #245BD6); background: var(--support-pure-white, #FFF); padding: 1px 6px; width: fit-content;'><span style='color: var(--solid-color-solid-blue, #245BD6);'><span class='icon-generative-ai'></span><span style='padding-left: 2px;'>更新が必要な内容があります。</span></span></div>");
							} // 日本語の管理画面のみFAQ更新機能を表示する
							if ($answer_info['self'] == false) {
								echo('<div style="color:#bbb; margin-bottom:5px;">');
							}
							else {
								echo('<div style="margin-bottom:5px;">');
							}
							echo(str_replace(array("\r\n", "\r", "\n"), "<br />", $answer_info['answer']));
							echo("</div>");

							if ($talk['level'] != 4 ) {
								echo('<div intent_cd="' . $talk['intent_cd'] . '" sub_intent_cd="' . $talk['sub_intent_cd']. '" style="line-height:2;word-break:break-all;">');
								if (array_key_exists('bot_id', $post) && $post['bot_id'] == '0') {
									if (array_key_exists($key, $skills)) {
										if (array_key_exists($post['lang_cd'], $skills[$key])) {
											$skill = $skills[$key][$post['lang_cd']];
										}
										else {
											$skill = $skills[$key][''];
										}
										echo('<div class="talkappi-skill-select" data-value=\'' . json_encode($skill, JSON_UNESCAPED_UNICODE) . '\' data-mode="skill"></div>');
									}
									else {
										echo('<div class="talkappi-skill-select" data-value="[]" data-mode="skill"></div>');
									}
								}
								else {
									$has_skill = false;
									$intent_keys = [$talk['intent_cd'] . "#" . $talk['sub_intent_cd'] . "#" . $post['lang_cd'], $talk['intent_cd'] . "#" . $talk['sub_intent_cd'] . "#"];
									$bot_skills = [];
									foreach($intent_keys as $intent_key) {
										if (array_key_exists($intent_key, $skills)) {
											$skill_bots = array_keys($skills[$intent_key]);
											foreach($skill_bots as $bot_id) {
												$bot_skills[$bot_id] = $skills[$intent_key][$bot_id];
											}
										}
									}
									if (count($bot_skills) > 0) {
										$has_skill = true;
										$skill = $bot_skills[$_bot_id];
										if (!$skill) {
											$skill = $bot_skills[array_key_first($bot_skills)];
										} 
										echo('<div class="talkappi-skill-select" data-value=\'' . json_encode($skill, JSON_UNESCAPED_UNICODE) . '\' data-mode="skill"></div>');
									}
									if (!$has_skill) echo('<div class="talkappi-skill-select" data-value="[]" data-mode="skill"></div>');
								}
								echo('</div>');	
							}
						echo('</td>');
						// 回答の更新
						echo('<td style="text-align: center;vertical-align: middle;">');
						if ($talk['upd_time'] != NULL) {
							echo('<p class="font-standard font-size-6" style="white-space: nowrap; margin-bottom: 10px;">');
							echo(date("Y/m/d H:i", strtotime($talk['upd_time'])) . '<br/>' . $talk['name']);
							echo('</p>');
						}

						// 10言語以上の施設は言語ボタンを表示せず、編集ボタンのみ表示
						if (count(explode(',', $_bot->lang_cd)) > 10) {
							echo('<div><a href="/admin/talknew?cd=' . $talk['intent_cd'] . '&sub_cd=' .$talk['sub_intent_cd'] . '&lang=' . $post['lang_cd']. '"> <span class="btn skill link-animate" style="margin: 0 0 10px 0 !important;" >'. __('admin.common.button.edit_action') . '</span></a></div>');
						}
						else {
							foreach(explode(',', $_bot->lang_cd) as $lang_cd) {
								if(array_key_exists($lang_cd, $talk)) {
									if ($talk[$lang_cd]!='') {
										$class = 'btn round light-gray selected';
									}
									else {
										$class = 'btn round light-gray';
									}
									if (array_key_exists('bot_id', $post) && $post['bot_id'] == '0') {
										if (array_key_exists($talk['bot_id'], $faq_editing) && in_array($lang_cd, $faq_editing[$talk['bot_id']])) {
											$class = 'btn round yellow';
										}
									}
									else {
										if (in_array($talk['intent_cd'] . '#' . $talk['sub_intent_cd'] . '#' . $lang_cd, $faq_editing)) {
											$class = 'btn round yellow';
										}
									}
									$edit_bot_id_param = ' data-bot_id=""';
									if (count($sub_bots) > 0) {
										if ($post['bot_id'] == '0') {
											$edit_bot_id_param = ' data-bot_id="' . $talk['bot_id'] . '"';
										}
										else {
											$edit_bot_id_param = ' data-bot_id="' . $post['bot_id'] . '"';
										}
									}
									echo('<div><a href="javascript:void(0);" data-intent_cd="' . $talk['intent_cd'] . '" data-sub_intent_cd="' . $talk['sub_intent_cd'] . '" data-lang_cd="' . $lang_cd . '"' . $edit_bot_id_param . 'class="js-faq-edit"> <span class="' . $class . '" style="margin: 0 0 10px 0 !important;" >' . $_bot_lang[$lang_cd] .' </span></a></div>');
								}
								else {
									if (isset($m_talks[$talk['intent_cd'] . '#' . $talk['sub_intent_cd']]) && isset($m_talks[$talk['intent_cd'] . '#' . $talk['sub_intent_cd']][$lang_cd])) {
										echo('<div><a href="javascript:void(0);" data-intent_cd="' . $talk['intent_cd'] . '" data-sub_intent_cd="' . $talk['sub_intent_cd'] . '" data-lang_cd="' . $lang_cd . '"' . $edit_bot_id_param . 'class="js-faq-edit"> <span class="btn round light-gray" style="margin: 0 0 10px 0 !important;" >' . $_bot_lang[$lang_cd] .' </span></a></div>');
									}
								}
							}
						}
						echo('</td>');
					if ($action == 'faqs'  && $_user->role_cd == '99') {
						echo('<td><span style="display:none;">' . sprintf("%04d", $talk['sort_no']) . '</span>');
						echo('<input type="number" min="1" max="9999" class="sort_no form-control" intent_cd="' . $talk['intent_cd'] . '" sub_intent_cd="' . $talk['sub_intent_cd'] . '" value="' . $talk['sort_no'] . '" />');
						echo('</td>');
					}
					/*
						echo('<td>');
						$view_text = '';
						$reqing_msgs = [];
						if (array_key_exists($talk['faq_id'], $req_tasks)) {
							foreach($req_tasks[$talk['faq_id']] as $req) {
								$req_task_view = View::factory ('admin/reqtaskview');
								$req_task_view->req = $req;
								$view_text =  $view_text . $req_task_view;
								$req_data = json_decode($req['req_data'], true);
								if (is_array($req_data) && array_key_exists('msg_cd', $req_data)) {
									if (!in_array($req_data['msg_cd'], $reqing_msgs)) {
										$reqing_msgs[] = $req_data['msg_cd'];
									}
								}
							}
						}
						if ($_user->role_cd == '80' || $_user->role_cd == '99') {
							echo('<a href="javascript:void(0);" class="reqinput" item_id="' . $talk['faq_id'] . '"><span class="btn-smaller-round btn-lightblue" style="margin: 4px;"><span class="icon-add"></span>依頼</span></a>');
							foreach($req_task_msgs as $msg) {
								if (!in_array($msg['msg_cd'], $reqing_msgs)) {
									echo('<a href="javascript:void(0);" class="reqtask" msg_cd="' . $msg['msg_cd'] . '" item_id="' . $talk['faq_id'] . '"><span class="badge badge-primary" style="margin: 4px;">' . $msg['msg_name'] . '</span></a>');
								}
							}
						}
						echo($view_text);
						echo('</td>');
					*/
					?>
				</tr>
			<?php }?>
		</tbody>
	</table>
</div>

<!-- END PAGE CONTENT-->
<?php echo($req_box);?>
<?php echo $redmine_box ?>

<!-- React components that show update faq by gpt button and alert modal -->
<script src="/assets/common/react/components/blocks/updatefacilityallfaqbygpt.bundle.js"></script>