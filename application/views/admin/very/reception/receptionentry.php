<div class="content-container">
    <div class="flex-x-between">
        <div><a href="receptions"><?php echo __('admin.waiting.reception') ?></a> > <?php echo __('survey.inquiry.common.edit') ?></div>
    </div>
</div>
<nav class="top-nav">
    <ul class="">
        <li class=""><a href="<?php echo '/adminvery/reception?id='. $reception_id ?>"><?php echo __('admin.waiting.basic.setting') ?></a></li>
        <li class="active"><a href=""><?php echo __('survey.inquiry.common.edit') ?></a></li>
        <?php if(!empty($display)){ ?>
        <li class=""><a href="<?php echo '/adminvery/receptiondisplay?id='. $reception_id ?>"><?php echo __('adminvery.receptiondisplay.display_settings') ?></a></li>
        <?php } ?>
    </ul>
</nav>
<div id="react-container" 
    data-entry='<?php echo json_encode($post, JSON_HEX_APOS)?>'
    data-verify_url='<?php echo json_encode($verify_url)?>'
    data-admin_lang_cd='<?php echo json_encode($admin_lang_cd)?>' 
    data-edit_lang_cd='<?php echo json_encode($edit_lang_cd)?>'>
</div>

<script type="text/javascript" src="/assets/common/react/pages/adminvery/receptionentry.bundle.js"></script>