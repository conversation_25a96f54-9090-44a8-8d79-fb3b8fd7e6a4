<style>
@media screen and (min-width: 992px) {
    .form-group label.col-md-2 {
        width: 180px;
    }
.content-container {
    min-width: auto;
}
}
.modal-label {
    display: flex;
    margin-top: 20px;
}
.modal-label .control-label {
    width: 100px;
}
.multitext-title .max-input-tip {
    top: 5px;
}
.multitext-description .max-input-tip {
    top: 14px;
}
.btn-smaller.js-category-cancel {
    width: max-content;
}
.js-multitext-language {
    display: none;
}
</style>
<script>
    const _weekdays = <?php echo json_encode($_codes['46'], JSON_UNESCAPED_UNICODE)?>;
    const _lang = <?php echo json_encode($lang, JSON_UNESCAPED_UNICODE)?>;
    const _user_lang = '<?php echo $_lang_cd ?>';
    const _multitext = <?php echo json_encode($post['support_lang_cd']) ?>;
    const _capacity = <?php echo json_encode($capacity_data, JSON_UNESCAPED_UNICODE) ?>;
    const _periods = <?php echo json_encode($post['reception_data']['period_new'], true) ?>;
    let _items = <?php echo $data === null ? "[]" : $data; ?>;
</script>
<input type="hidden" name="id" value="<?php echo $post['reception_id'] ?>">
<input type="hidden" name="capacity" value>
<input type="hidden" name="item_div" value>
<input type="hidden" name="printer_settings" value>

<?php
    $display = $post['reception_data']['display'] ?? [];
    $display_on_screen = !empty($display) ? '1' : '0';
    $waiting_display = in_array('1', $display);
    $calling_display = in_array('3', $display);
    $auto_cancel_display = in_array('6', $display);
    $text_display = in_array('9', $display);

    $display_style = empty($display) ? "display: none;" : '';
?>

<div class="content-container">
    <div class="flex-x-between">
        <div><a href="receptions"><?php echo __('admin.waiting.reception') ?></a> > <?php echo __('admin.waiting.reception.setting') ?></div>
    </div>
</div>
<nav class="top-nav">
    <ul class="">
        <li class="active"><a href=""><?php echo __('admin.waiting.basic.setting') ?></a></li>
        <li class=""><a href="<?php echo '/adminvery/receptionentry?id='. $reception_id . '&lang_cd=' . array_keys($bot_lang)[0] ?>"><?php echo __('survey.inquiry.common.edit') ?></a></li>
        <?php if(!empty($display)){ ?>
        <li class=""><a href="<?php echo '/adminvery/receptiondisplay?id='. $reception_id . '&lang_cd=' . array_keys($bot_lang)[0] ?>"><?php echo __('adminvery.receptiondisplay.display_settings') ?></a></li>
        <?php } ?>
    </ul>
</nav>
<div class="content-container white border">
    <div class="section-container bottom-line">
        <h4><?php echo __('admin.waiting.fasility.setting') ?></h4>
        <div class="form-body">
            <div class="form-group">
                <label class="col-md-2"><?php echo __('admin.waiting.basic.setting') ?></label>
                <div class="col-md-5">
                    <div class="talkappi-radio js-facility" data-name="facility" data-value="<?php echo $post['item_id'] ? '02' : '01' ?>" data-source='{"01":"<?php echo __('admin.waiting.add.fasility') ?>", "02":"<?php echo __('admin.waiting.pick.fasilities') ?>"}'></div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-md-2"></label>
                <div class="col-md-8 back-ground-gray">
                    <div class="js-facility-new" style="<?php echo $post['item_id'] ? 'display:none' : '' ?>">
                        <div class="form-group">
                            <label class="col-md-2" style="width:80px;"><?php echo __('admin.common.label.name') ?></label>
                            <div class="col-md-10">
                                <div class="talkappi-multitext multitext-title" data-name="reception-title" data-max-input='50' data-language='<?php echo json_encode($post['support_lang_cd']) ?>' data-value='<?php echo json_encode($title, JSON_UNESCAPED_UNICODE)?>' title="<?php echo __('admin.common.label.name') ?>"></div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-2" style="width:80px;"><?php echo __('inquiry.index.label.content') ?></label>
                            <div class="col-md-10">
                                <div class="talkappi-multitext multitext-description" data-name="description" data-max-input='100' data-type='textarea' data-language='<?php echo json_encode($post['support_lang_cd']) ?>' data-value='<?php echo json_encode($description, JSON_UNESCAPED_UNICODE)?>' title="<?php echo __('inquiry.index.label.content') ?>"></div>
                            </div>
                        </div>
                    </div>
                    <div class="js-facility-contents" style="<?php echo $post['item_id'] ? '' : 'display:none' ?>">
                        <div class="form-group">
                            <label class="col-md-1" style="width:90px;"><?php echo __('admin.itme.item.code') ?></label>
                            <div class="col-md-10" style="display: flex;">
                            <input type="text" name="item_id" value="<?php echo $post['item_id'] ?>" class="text-input-longer">
                            <div class="action-button section btn-blue js-item-search" style="min-width: 58px;height: 28px;"><?php echo __('admin.common.button.search')?></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-md-2"><?php echo __('admin.common.label.image') ?></label>
                <div class="col-md-5">
                    <div class="talkappi-upload js-upload" data-name="image" data-url='<?php echo $post['image'] ?>' data-label="<?php echo $post['image'] ?>" data-type='img' data-upload-now='very'></div>
                </div>      
            </div>
            <div class="form-group js-class-cd" style="<?php echo $post['item_id'] ? 'display:none' : '' ?>">
                <label class="col-md-2"><?php echo __('admin.common.label.classification') ?></label>
                <div class="col-md-5">
                    <div class="talkappi-category-select js-category" data-name="class_cd" data-type='reception' data-div='<?php echo $class_div ?>' data-value='<?php echo json_encode($post['class_cd']) ?>'></div>
                </div>      
            </div>
            <div class="form-group">
                <label class="col-md-2"><?php echo __('admin.common.label.support.lang') ?></label>
                <div class="col-md-8">
                    <div class="talkappi-checkbox" data-name="support_lang_cd" data-value='<?php echo json_encode($post['support_lang_cd']) ?>' data-source='<?php echo json_encode($bot_lang, JSON_UNESCAPED_UNICODE)?>'></div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-md-2"><?php echo __('admin.common.label.display.lang') ?></label>
                <div class="col-md-8">
                    <div class="talkappi-checkbox" data-name="display_lang_cd" data-value='<?php echo json_encode($post['display_lang_cd']) ?>' data-source='<?php echo json_encode($bot_lang, JSON_UNESCAPED_UNICODE)?>'></div>
                </div>
            </div>
        </div>
    </div>
    <div class="section-container bottom-line">
        <h4><?php echo __('admin.waiting.ticket.setting') ?></h4>
        <div class="form-body">
            <div class="form-group">
                <label class="col-md-2"><?php echo __('admin.waiting.ticket.time') ?></label>
                <div class="section-wrapper" style="width:80%">
                    <div class="talkappi-schedule-section" type='business' data-value='<?php echo $periods === "" ? "\"\"" : $periods; ?>;' data-name="items" style="display: grid;row-gap: 2rem;">
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-md-2" style="align-items: center;display: flex;">
                    <span><?php echo __('admin.reception.reception.no') ?></span>
                    <span class="icon-detail" style="margin: 0 0 0 0.5rem;" title="<?php echo __('admin.reception.reception.no.desc') ?>"></span>
                </label>
                <div class="col-md-9">
                    <input type="text" name="reception_no_prefix" value="<?php echo isset($post['reception_data']['reception_no_prefix']) ? $post['reception_data']['reception_no_prefix'] : ""?>" class="text-input-longer" style="width: 200px;">
                </div>
            </div>
            <div class="form-group">
                <?php $registrationNeeded = (isset($post['reception_data']['registration_necessary']) && $post['reception_data']['registration_necessary'] == '1') || !isset($post['reception_data']['registration_necessary']) ?>
                <label class="col-md-2"><?php echo __('admin.reception.member') ?></label>
                <div class="col-md-9">
                    <div class="talkappi-switch" data-name="registration_necessary" data-value="<?php echo $registrationNeeded ? '1' : '0' ?>" data-label="<?php echo __('admin.reception.member.necessary') ?>"></div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-md-2"><?php echo __('admin.waiting.max.reception.party') ?></label>
                <div class="col-md-9">
                    <input class="text-input" name="max_reception_party" value="<?php echo isset($post['reception_data']['max_reception_party']) ? $post['reception_data']['max_reception_party'] : ""?>" class="text-input" style="width: 72px;">
                    <?php echo __('admin.waiting.call.pair') ?>
                </div>
            </div>
            <div class="form-group">
                <label class="col-md-2"><?php echo __('admin.waiting.waiting.time.caliculate') ?></label>
                <div class="col-md-9" style="display: flex; column-gap: 40px;">
                    <div>
                        <?php echo __('admin.waiting.waiting.time') ?>
                        <input class="text-input" name="waiting_time" style="width: 48px;" value="<?php echo $post['reception_data']['waiting']['time'] ?>">
                        <?php echo __('admin.waiting.minute.pair'). "  " ?>
                    </div>
                    <div>
                        <?php echo __('admin.waiting.available.num') ?>
                        <input class="text-input" name="waiting_party" style="width: 48px;" value="<?php echo $post['reception_data']['waiting']['party'] ?>">
                        <?php echo __('admin.waiting.call.pair') ?>
                    </div>
                    <!-- シュミレーション一旦対応なし -->
                    <!-- <div>
                        <?php echo __('admin.waiting.time.simulation') ?>
                        <input class="text-input" style="width: 48px;">
                        <?php echo __('admin.waiting.pair.num') ?>
                        <span>=</span>
                        <?php echo __('admin.waiting.waiting.about') ?>
                        <span>0</span>
                        <?php echo __('admin.waiting.time.num') ?>
                    </div> -->
                </div>
            </div>
            <div class="form-group">
                <label class="col-md-2"><?php echo __('admin.waiting.call.soon') ?></label>
                <div class="col-md-5">
                    <?php echo __('admin.waiting.call.pair.after') ?>
                    <input class="text-input" name="notification" style="width: 48px;" value="<?php echo $post['reception_data']['notification'] ?>">
                    <?php echo __('admin.waiting.call.pair') ?>
                </div>
            </div>
            <div class="form-group">
                <label class="col-md-2"><?php echo __('admin.waiting.auto.cancel') ?></label>
                <div class="col-md-5">
                    <?php echo __('admin.waiting.call.atfer') ?>
                    <input class="text-input" name="cancel" style="width: 48px;" value="<?php echo $post['reception_data']['cancel'] ?>">
                    <?php echo __('admin.waiting.call.minute') ?>
                </div>
            </div>
            <div class="form-group">
                <label class="col-md-2"><?php echo __('admin.waiting.online.ticket') ?></label>
                <?php 
                $pause_flg_source = '{"0":"'. __('admin.waiting.on'). '", "1":"' .__('admin.waiting.off').'"}';
                if ($post['pause_flg'] == '2') {
                    $pause_flg_source = '{"0":"'. __('admin.waiting.on'). '", "1":"' .__('admin.waiting.off').'", "2":"' .__('admin.waiting.auto.off').'"}';
                }
                ?>
                <div class="col-md-5">
                    <div class="talkappi-radio" data-name="pause_flg" data-value="<?php echo $post['pause_flg'] ?>" data-source='<?php echo $pause_flg_source ?>'></div>
                </div>
            </div>
            <div class="form-group">
                <?php $isAutoPauseOn = (isset($post['reception_data']['auto_pause']) && $post['reception_data']['auto_pause'] == '1') ?>
                <label class="col-md-2" style="align-items: center;display: flex;">
                    <span><?php echo __('admin.reception.auto.pause') ?></span>
                    <span class="icon-detail" style="margin: 0 0 0 0.5rem;" title="<?php echo __('admin.reception.auto.pause.tooltip') ?>"></span>
                </label>
                <div class="col-md-5">
                    <div class="talkappi-radio" data-name="auto_pause" data-value="<?php echo $isAutoPauseOn ? '1' : '0' ?>" data-source='{"0":"OFF", "1":"ON"}'></div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-md-2" style="align-items: center;display: flex;">
                    <span><?php echo __('admin.common.label.public') ?></span>
                </label>
                <div class="col-md-5">
                    <div class="talkappi-radio" data-name="display_flg" data-value="<?php echo $post['display_flg'] ?>" data-source='{"0":"OFF", "1":"ON"}'></div>
                </div>
            </div>
        </div>
    </div>
    <div class="section-container bottom-line">
        <div class="section-container">
            <h4><?php echo __('admin.reception.printer.setting') ?></h4>
            <div class="form-group">
                <label class="col-md-2"><?php echo __('admin.reception.printer') ?></label>
                <div class="col-md-5">
                    <div class="talkappi-radio js-printer" data-name="printer" data-value="<?php echo (!$post['printer_settings'])  ? '0' : '1'?>" data-source='{"0":"OFF", "1":"ON"}'></div>
                </div>
            </div>
            <div class="js-printer-settings" style="<?php if (!$post['printer_settings']) echo "display:none;" ?>">
                <div class="form-group">
                    <label class="col-md-2"><?php echo __('admin.reception.printer.ip.address') ?></label>
                    <div class="col-md-9">
                        <input type="text" name="ip_address" value="<?php echo isset($post['printer_settings']['ip_address']) ? $post['printer_settings']['ip_address'] : ""?>" class="text-input-longer" style="width: 200px;">
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-2"><?php echo __('admin.reception.printer.port.no') ?></label>
                    <div class="col-md-9">
                        <input type="text" name="port_number" value="<?php echo isset($post['printer_settings']['port_number']) ? $post['printer_settings']['port_number'] : ""?>" class="text-input-longer" style="width: 200px;">
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-2"><?php echo __('admin.reception.printer.receipt.description') ?></label>
                    <div class="col-md-9">
                        <textarea name="receipt_description" id="receipt_description" class="text-input-longer" style="height:100px;"><?php echo isset($post['printer_settings']['receipt_description']) ? $post['printer_settings']['receipt_description'] : ""?></textarea>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="section-container bottom-line">
        <h4><?php echo __('admin.reception.capacity.setting') ?></h4>
        <div class="form-body js-capacity-setting" style="row-gap: 20px;display: grid;"></div>
        <div class="image-action-group js-capacity-add">
            <img src="./../assets/admin/css/img/icon-add.svg" width="12" height="12">
            <span><?php echo __('admin.reception.capacity.item.add') ?></span>
        </div>
    </div>
    <div class="section-container">
        <h4><?php echo __('adminvery.receptiondisplay.display_settings') ?></h4>
        <div class="form-group">
            <label class="col-md-2"><?php echo __('adminvery.receptiondisplay.usage') ?></label>
            <div class="col-md-7" style="gap: 16px; display: flex; flex-direction: column;">
                <div class="talkappi-radio js-display-on-screen" data-name="display_on_screen"
                    data-value="<?php echo $display_on_screen; ?>" data-source='{"0":"<?php echo __('admin.common.label.do_not_use') ?>", "1":"<?php echo __('admin.common.label.use') ?>"}'></div>
                <div class="readonly-input flex-x-between display-settings"
                    style="height: auto; white-space: normal; display: flex; flex-direction: column; align-items: flex-start; gap: 8px; max-width: 100%; <?php echo $display_style; ?>">
                    <div>
                        <p><?php echo __('adminvery.receptiondisplay.items') ?></p>
                    </div>
                    <div style="display: flex; gap:8px; flex-wrap: wrap;">
                        <div class="talkappi-switch" data-name="waiting_display"
                            data-value="<?php echo $waiting_display ? '1' : '0'; ?>" data-label="<?php echo __('adminvery.receptionlist.label.status.waiting') ?>"></div>
                        <div class="talkappi-switch" data-name="calling_display"
                            data-value="<?php echo $calling_display ? '1' : '0'; ?>" data-label="<?php echo __('adminvery.receptionlist.label.status.calling') ?>"></div>
                        <div class="talkappi-switch" data-name="auto_cancel_display"
                            data-value="<?php echo $auto_cancel_display ? '1' : '0'; ?>" data-label="<?php echo __('admin.waiting.auto.cancel') ?>"></div>
                        <div class="talkappi-switch" data-name="text_display"
                            data-value="<?php echo $text_display ? '1' : '0'; ?>" data-label="<?php echo __('admin.common.label.description') ?>"></div>
                    </div>
                </div>
            </div>
        </div>
        <?php if($reception_id){ ?>
        <div class="form-group display-public-url" style="<?php echo $display_style; ?>">
            <label class="col-md-2"><?php echo __('adminvery.receptiondisplay.public_url') ?></label>
            <div class="col-md-9">
                <p class="public-url-area" style="margin: 0">
                    <span class="public-url-raw public-url-link copy" @click="copyUri" data-clipboard-action="copy" data-clipboard-text="<?php echo htmlspecialchars($public_display_url) ?>"><?php echo htmlspecialchars($public_display_url) ?></span>
                    <span class="copy font-standard font-family-v1 font-color-v1 survey-copy-url" @click="copyUri" data-clipboard-action="copy" data-clipboard-text="<?php echo htmlspecialchars($public_display_url) ?>"><?php echo __('admin.common.label.copy')?></span>
                </p>
            </div>
        </div>
        <?php } ?>
    </div>
    <div class="form-actions">
        <div class="row">
            <div class="col-md-offset-2 col-md-9">
                <div class="actions-container">
                    <button type="button" class="action-button page btn-blue js-action-save"><?php echo __('admin.common.button.save') ?></button>
                    <a class="action-button page btn-white js-action-back" href="/adminvery/receptions"><?php echo __('admin.common.button.return_to_list') ?></a>
                    <?php if ($post['reception_id']) {?>
                    <?php if ($preview_url) { ?>
                    <div class="btn-larger btn-gray-black" id="previewButton">
                        <a class="flexbox-center height-100 width-100" href="<?php echo $preview_url ?>" target="_blank" style="color: #000;" onfocus="this.blur();"><?php echo __('admin.common.button.verify'); ?></a>
                    </div>
                    <?php } ?>
                    <span class="action-button page btn-red-border icon-only js-action-delete"><span class="icon-delete"></span></span>
                    <?php } ?>
                </div>
            </div>
        </div>
    </div>
</div>