<style>
    .top-nav>ul>li {
        width: 130px;
        text-align: center;
    }

    table.table-bordered tbody td {
        vertical-align: middle;
    }

    .setting-icon {
        display: inline-block;
        padding-left: 4px;
    }
</style>

<?php
if (isset($_GET['pause'])) {
    $pause = $_GET['pause'];
} else {
    $pause = "0";
} 
if (isset($_GET['not_public'])) {
    $not_public = $_GET['not_public'];
} else {
    $not_public = "0";
}
$isReceptionOnlyRole = $_user->role_cd == 11;
?>

<?php if (!$isReceptionOnlyRole) { ?>
    <div class="content-container" style="margin-top: -20px;">
        <div class="flex-x-between">
            <div></div>
            <a href="/<?php echo $_path ?>/reception">
                <span class="btn-smaller btn-blue js-new-maximum">
                <span class="icon-add-white"></span>
                <?php echo __('admin.waiting.add.fasility') ?>
            </a>
        </div>
    </div>
<?php } ?>

<input name="reception_id" id="reception_id" hidden>
<input name="display_flg" id="display_flg" hidden>

<nav class="top-nav">
    <ul class="">
        <li class="<?php if ($pause == '0' && $not_public == '0') echo "active"; ?>"><a href="receptions"><?php echo __('admin.waiting.on') ?></a></li>
        <li class="<?php if ($pause == '1') echo "active"; ?>"><a href="receptions?pause=1"><?php echo __('admin.waiting.off') ?></a></li>
        <li class="<?php if ($not_public == '1') echo "active"; ?>"><a href="receptions?not_public=1"><?php echo __('admin.waiting.not_public') ?></a></li>
    </ul>
</nav>

<div class="content-container white">
    <table class="table table-striped table-bordered table-hover js-data-table" style="height: 100%">
        <thead>
            <tr>
                <th>ID</th>
                <th><?php echo __('admin.reception.facility.name') ?></th>
                <th><?php echo __('admin.common.label.classification') ?></th>
                <th><?php echo __('admin.reception.waiting.parties') ?></th>
                <th><?php echo __('admin.waiting.waiting.time') ?></th>
                <?php if ($pause == '1') { ?>
                    <th><?php echo __('admin.reception.reception_status') ?></th>
                <?php } ?>
                <th><?php echo __('admin.common.label.last_update') ?></th>
                <th><?php echo __('admin.common.label.operation') ?></th>
            </tr>
        </thead>
        <tbody>
            <!-- <?php
                    foreach ($items as $item) {
                    ?> -->
            <?php $reception_data = json_decode($item['reception_data'], true); ?>
            <tr class="gradeX odd js-reception-table" data-id="<?php echo $item['reception_id']; ?>" role="row">
                <td style="white-space: nowrap;">
                    <?php echo $item['reception_id']; ?></a>
                </td>
                <!-- 施設名 -->
                <td style="width: 240px;">
                    <?php 
                        $name = ($item['item_id'] === null) ? $item['title'] : $item['item_name'];
                        if ($isReceptionOnlyRole) {
                            echo $name;
                        } else {
                            echo '<a class="js-reception">' . $name . '</a>';
                        }
                    ?>
                </td>
                <!-- 分類 -->
                <td>
                    <?php if ($item['tags'] !== null) echo $item['tags']; ?>
                </td>
                <!-- 待ち組数 -->
                <td style="height: 100%">
                    <span style="font-size: 20px;"><?php echo $item['waiting_count']; ?></span>
                    <span style="display: inline-block;margin-top: 8px;margin-left: 4px;"><?php echo __('admin.waiting.call.pair') ?></span>
                </td>
                <!-- 待ち時間 -->
                <td>
                    <?php if ($reception_data['waiting']['time'] && $reception_data['waiting']['party']) { ?>
                        <?php echo  __('admin.waiting.waiting.about') . ' ' .  (round($reception_data['waiting']['time'] * ($item['waiting_count'] / $reception_data['waiting']['party'])) . ' ' .   __('admin.waiting.call.minute')) ?>
                    <?php } ?>
                </td>
                <?php if ($pause == '1') { ?>
                    <!-- 受付ステータス -->
                    <td>
                        <?php if (isset($item['pause_flg']) && $item['pause_flg'] && $item['pause_flg'] === '2') { ?>
                            <?php echo  __('admin.reception.status.automatic_stop') ?>
                        <?php } else {?>
                            <?php echo  __('admin.reception.status.manual_stop') ?>
                        <?php } ?>
                    </td>
                <?php } ?>
                <!-- 最終更新 -->
                <td>
                    <?php echo ($item['upd_time']) ?><br>
                    <?php echo ($item['upd_user_name']) ?>
                </td>
                <!-- 操作 -->
                <td style="display:flex; flex-direction:column;">
                    <div class="btn round light-blue" style="margin:5px;">
                        <img src="/assets/admin/css/img/Icon-lining-up.svg" alt="lining-up" height=12 width=12 />
                        <a class="setting-icon" href="receptionlist?id=<?php echo $item['reception_id']; ?>" style="color: #000;" target="_blank" rel="noopener noreferrer"><?php echo __('admin.reception.result.list') ?></a>
                    </div>
                    <div class="btn round light-blue" style="margin:5px;">
                        <img src="/assets/admin/css/img/icon-form-description-active.svg" alt="setting" height=12 width=12 />
                        <a class="setting-icon" href="reception_operation?id=<?php echo $item['reception_id']; ?>" style="color: #000;" target="_blank" rel="noopener noreferrer"><?php echo __('admin.reception.operation.list') ?></a>
                    </div>
                    <?php if (!$isReceptionOnlyRole) { ?>
                        <div class="btn round light-blue js-reception" style="margin:5px;">
                            <img src="/assets/admin/css/img/Icon-setting.svg" alt="setting" height=12 width=12 />
                            <span class="setting-icon"><?php echo __('admin.common.button.edit_action') ?></span>
                        </div>
                        <div class="btn round light-blue image delete js-delete" style="margin: 5px;"><?php echo __('admin.common.button.delete_action') ?></div>
                    <?php } ?>
                </td>
            </tr>
            <!-- <?php } ?> -->
        </tbody>
    </table>
</div>