<style>
:root {
  font-size: 12px;
}

@media screen and (min-width: 1200px) {
  :root {
    font-size: 16px;
  }
}

@media screen and (min-width: 2560px) {
  :root {
    font-size: 24px;
  }
}

@media screen and (min-width: 3840px) {
  :root {
    font-size: 32px;
  }
}

.page-content-wrapper .page-content {
  padding: 0 !important;
  margin-left: unset !important;
}

.page-content-header,
.page-sidebar-wrapper {
  display: none;
}

.talkappibot {
  display: none;
}
.page-footer {
  display: none;
}
.full-view {
  height: 100%;
  min-height: 100vh;
  width: 100vw;
  /* position: fixed; */
  top: 0;
  left: 0;
  z-index: 10;
  background-color: white;
  padding: 0px;
  display: flex;
  flex-direction: column;
}
</style>

<div id="react-graphs-container" class="full-view"></div>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/1.12.4/jquery.min.js"></script>
<script type="text/javascript" src="/assets/common/react/pages/adminvery/reception_display.bundle.js"></script>
<script type="text/javascript">
    let display_item = <?php echo json_encode($display_item); ?>;
    let display_text = <?php echo json_encode($display_text); ?>;
    let secondary_display_text = <?php echo json_encode($secondary_display_text); ?>;
    let display_style = <?php echo json_encode($display_style); ?>;
    let reception_items = <?php echo json_encode($reception_items); ?>;
    let lang_cd = "<?php echo $lang_cd; ?>";

    jQuery(document).ready(function($) {
        window.talkappi_reception_display_setupFunctionsGraph({
          display_item, 
          display_text, 
          secondary_display_text,
          display_style, 
          reception_items,
          lang_cd
        });
    });
</script>