<link rel="stylesheet" type="text/css" href="/assets/admin/css/reception_operation.css" />
<script>
    const bot_id = <?php echo ($bot_id) ?>;
    const lang_cd = '<?php echo ($_lang_cd) ?>';
    const reception_id = <?php echo ($reception_id) ?>;
    const capacity_data = <?php echo json_encode($capacity_data) ?>;
    const reception_ja_entry = <?php echo json_encode($reception_ja_entry) ?>;
    const reception_en_entry = <?php echo json_encode($reception_en_entry) ?>;
    const wait_time = <?php echo ($reception_data['waiting']['time'] ?: 0) ?>;
    const wait_party = <?php echo ($reception_data['waiting']['party'] ?: 0) ?>;
    const printer_settings = <?php if ($printer_settings == NULL) { ?> null <?php } else {
                                                                            echo json_encode($printer_settings, JSON_UNESCAPED_UNICODE);
                                                                        } ?>;
</script>

<div class="loading-container">
    <div class="loading connecting">
        <img id="connecting" src="../../assets/admin/images/processsmall_black.svg" />
        <div class="loading-status"><?php echo __('adminvery.reception_operation.status.connecting') ?></div>
    </div>
    <div class="loading reconnecting">
        <img id="reconnecting" src="../../assets/admin/images/processsmall_black.svg" />
        <div class="loading-status"><?php echo __('adminvery.reception_operation.status.reconnecting') ?></div>
    </div>
    <div class="loading success">
        <img id="success" src="../../assets/admin/images/inquiry-success.svg" />
        <div class="loading-status"><?php echo __('adminvery.reception_operation.status.complete') ?></div>
    </div>
    <div class="loading error">
        <img id="error" src="../../assets/admin/images/inquiry-error.svg" />
        <div class="loading-status"><?php echo __('adminvery.reception_operation.status.error') ?></div>
        <div class="back-button"><?php echo __('adminvery.reception_operation.back') ?></div>
    </div>
</div>
<div class="reception-content-container">
    <div class="reception-header-title">
        <span class="reception-title-ja"><?php echo __('adminvery.reception_operation.title_ja'); ?></span>
        <span class="reception-title-en"><?php echo __('adminvery.reception_operation.title_en'); ?></span>
    </div>
    <div class="reception-info-wrapper">
        <div class="reception-info-content">
            <p class="reception-info-title text-nowrap"><?php echo __('adminvery.receptionlist.label.number-waiting_ml') ?></p>
            <p class="reception-info-detail text-nowrap" id="number-waiting"><?php echo ($groups_wait . __('adminvery.receptionlist.label.groups_ml')) ?></p>
        </div>
        <?php if ($reception_data['waiting']['time'] && $reception_data['waiting']['party']) { ?>
            <div class="reception-info-content">
                <p class="reception-info-title text-nowrap"><?php echo __('adminvery.receptionlist.label.waiting-time_ml') ?></p>
                <p class="reception-info-detail text-nowrap" id="waiting-time">
                    <?php echo (round($reception_data['waiting']['time'] * ($groups_wait / $reception_data['waiting']['party'])) . __('adminvery.receptionlist.label.minutes')) ?></p>
            </div>
        <?php } ?>
    </div>
    <div class="reception-option-wrapper">
        <!-- ここにオプションのコンテンツを表示させる -->
    </div>
    <div id="error-reporter" class="error-reporter-wrapper">
        <!-- Error & Alert Reporter -->
    </div>
    <div class="<?php echo ($pause_flg == 0) ? 'receipt-button accept' : 'receipt-button pause'; ?>" id="receipt-button">
        <span class="receipt-text-ja"><?php echo __('adminvery.reception_operation.receipt_ja'); ?></span>
        <span class="receipt-text-en"><?php echo __('adminvery.reception_operation.receipt_en'); ?></span>
    </div>
</div>

<script src="/assets/global/plugins/epson/epos-2.24.0.js"></script>
<script src="/assets/admin/very/reception_printer.js"></script>