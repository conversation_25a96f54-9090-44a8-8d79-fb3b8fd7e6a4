<script>
    const _reception_id = <?php echo json_encode($reception_id) ?>;
    const _lang_edit = <?php echo json_encode($lang_edit) ?>;
    const _support_lang = <?php echo json_encode($support_lang) ?>;
    const _display_settings = <?php echo json_encode($display_settings) ?>;
    const _display_data = JSON.parse(<?php echo json_encode($display_data) ?>);
</script>
<div class="content-container">
    <div class="flex-x-between">
        <div><a href="receptions"><?php echo __('admin.waiting.reception') ?></a> > <?php echo __('adminvery.receptiondisplay.display_settings') ?></div>
    </div>
</div>
<nav class="top-nav">
    <ul class="">
        <li class=""><a href="<?php echo "/adminvery/reception?id=$reception_id" ?>"><?php echo __('admin.waiting.basic.setting') ?></a></li>
        <li class=""><a href="<?php echo "/adminvery/receptionentry?id=$reception_id&lang_cd=" . array_keys($support_lang)[0] ?>"><?php echo __('survey.inquiry.common.edit') ?></a></li>
        <li class="active"><a href=""><?php echo __('adminvery.receptiondisplay.display_settings') ?></a></li>
    </ul>
</nav>
<div class="reception-display-page" data-lang_cd="<?php echo $_lang_cd ?>"></div>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/1.12.4/jquery.min.js"></script>
<script type="text/javascript" src="/assets/common/react/pages/adminvery/receptiondisplay.bundle.js" defer></script>
<script type="text/javascript">
    jQuery(document).ready(function($) {
        window.talkappi_setupReceptionDisplay({
            receptionId: _reception_id,
            supportLang: _support_lang,
            langEdit: _lang_edit,
            displaySettings: _display_settings,
            displayData: _display_data,
        })
    });
</script>