<script>
    const _qr_url = <?php echo json_encode($qr_url , JSON_UNESCAPED_UNICODE); ?>;
	const _obfuscation_url = <?php echo json_encode($obfuscation_url , JSON_UNESCAPED_UNICODE); ?>;
	const _obfuscation_urls_by_room = <?php echo json_encode($obfuscation_urls_by_room , JSON_UNESCAPED_UNICODE); ?>;
	const _flg_very_access_by_bot_id = <?php echo $flg_very_access_by_bot_id; ?>;
	const _base64_logo_url = <?php echo json_encode($base64_logo_url , JSON_UNESCAPED_UNICODE); ?>;
	const _canvas_size = <?php echo json_encode($canvas_size, JSON_UNESCAPED_UNICODE); ?>;
	let _settings = <?php echo json_encode($settings, JSON_UNESCAPED_UNICODE); ?>;
	const scene_cd = <?php echo json_encode($scene_cd, JSON_UNESCAPED_UNICODE); ?>;
</script>
<input type="hidden" name="settings" id="settings" value="" />
<style>
/* .js-section-container.content-container,.js-add-icon.content-container {
        min-width: initial;
} */

.js-button-icon {
	background-color: #F6F7F9;
	padding: 4px 10px;
	margin-right: 10px;
	border-radius: 4px;
	display: flex;
	flex-direction: row;
	gap: 12px;
	align-items: center;
}

li .icon-delete {
    position: static;
}

.mobile-preview .main-container {
    background: white;
}

</style>

<div class="content-container white border">
	<div style="display:flex;">
		<!-- 左側 -->
		<div class="mobile-preview-left-content">
			<!-- 導線選択 -->
			<div class="section-container">
				<h2><?php echo __('admin.common.label.user_flow') ?></h2>
				<div class="form-group">
				<div class="talkappi-pulldown js-scene-cd" data-name="scene_cd" data-value="<?php echo $scene_cd ?? ''?>" data-size="longer" data-source='<?php echo ($scene_list) ?>'></div>
				</div>
			</div>
			<!-- QRコードURL表示 -->
			<div class="section-container" >
				<h2><?php echo __('adminvery.veryqr.label.qr_setting') ?></h2>
				<div class="form-group">
					<label class="control-label col-md-2 label-fix-10"><?php echo __('adminvery.veryqr.label.qr_code_color') ?></label>
					<div class="col-md-5">
						<div class="talkappi-colorpicker js-qr-color" data-name="qr_color" data-value="<?php echo($settings['qr_color'])?>"></div>
					</div>
				</div>
				<?php
					$obfuscatorUrlFlg = $settings['obfuscation_url_flg'] ?? '0';
					if ($flg_very_access_by_bot_id == 0) {
						$obfuscatorUrlFlg = '1';
					}
				?>
				<div class="form-group">
					<label class="control-label col-md-2 label-fix-10">
						<?php echo __('admin.common.label.qr_redirect_url') ?>
						<span class="icon-detail" style="margin: 0 0 0 0.5rem;" title="<?php echo __('adminvery.veryqr.label.notice_room_number')?>"></span>
					</label>
					<div class="col-md-5" style="width: 75%;">
						<p class="public-url-area">
							<span class="js-url" style="display: <?php echo ($obfuscatorUrlFlg == '0') ? 'block' : 'none'; ?>;">
								<?php 
									$qrUrl = $qr_url ?? '';
									if (!empty(trim($scene_cd))) {
										$qrUrl = $qr_url . '&f=' . urlencode($scene_cd);
									}
									echo $qrUrl
								?>
							</span>
							<span class="js-obfuscation-url" style="display: <?php echo ($obfuscatorUrlFlg == '1') ? 'block' : 'none'; ?>;">
								<?php 
									echo $obfuscation_url;
								?>
							</span>
						</p>
					</div>
				</div>
				<?php if ($flg_very_access_by_bot_id == 1) { ?>
				<div class="form-group">
					<label class="control-label col-md-2 label-fix-10">
						<?php echo __('admin.common.label.obfuscation_url') ?>
					</label>
					<div class="col-md-10">
						<div class="talkappi-radio js-obfuscation-url-flg" data-name="obfuscation_url_flg" data-value='<?php echo $obfuscatorUrlFlg; ?>' data-source='{"0":"利用しない", "1":"利用する"}'></div>
					</div>
				</div>
				<?php } ?>
			</div>
			<!-- 部屋番号設定 -->
			<div class="section-container">
				<h2><?php echo __('adminvery.veryqr.label.room_setting') ?></h2>
				<div class="form-group">
					<label class="control-label col-md-2 label-fix-10"><?php echo __('adminvery.veryqr.label.room_number') ?></label>
					<div class="col-md-10">
						<div class="talkappi-radio js-room-flg" data-name="room_flg" data-value='<?php echo($settings['room_flg']); ?>' data-source='{"0":"OFF", "1":"ON"}'></div>
					</div>
				</div>
				<div class="form-group js-room-lists" style="display: <?php echo ($settings['room_flg'] == '1') ? 'block' : 'none'; ?>;">
					<label class="control-label col-md-2 label-fix-10">
						<?php echo __('adminvery.veryqr.label.room_number_list') ?>
						<span class="icon-detail" style="margin: 0 0 0 0.5rem;" title="部屋番号をカンマ区切りで入力してください。部屋番号は数字のみ入力可能です。"></span>
					</label>
					<?php const MAX_ROOM_NUMBER_LENGTH = 600; ?>
					<div class="col-md-10">
						<textarea type="text" class="form-control talkappi-textinput js-input-room-number" data-max-input="<?php echo MAX_ROOM_NUMBER_LENGTH; ?>" placeholder="101,102,103"><?php echo($settings['room_number']); ?></textarea>
					</div>
				</div>
			</div>
			<!-- POP設定 -->
			<div class="section-container">
				<h2><?php echo __('adminvery.veryqr.label.pop_setting') ?></h2>
				<?php $popFlg = (isset($settings['pop_flg']) ? $settings['pop_flg'] : '1'); ?>
				<div class="form-group">
					<label class="control-label col-md-2 label-fix-10"><?php echo __('adminvery.veryqr.label.pop_design') ?></label>
					<div class="col-md-10">
						<div class="talkappi-radio js-pop-flg" data-name="pop-flg" data-value='<?php echo $popFlg; ?>' data-source='{"0":"OFF", "1":"ON"}'></div>
					</div>
				</div>
				<div class="form-body js-pop-settings" style="display: <?php echo ($popFlg == '1') ? 'block' : 'none'; ?>;">
					<div class="form-group">
						<label class="control-label col-md-2 label-fix-10"><?php echo __('adminvery.veryqr.label.size') ?></label>
						<div class="col-md-10">
							<div class="talkappi-radio js-print-size" data-name="print_size" data-value='<?php echo($settings['size']); ?>' data-source='{"A6":"A6", "A4":"A4"}'></div>
						</div>
					</div>
					<div class="form-group">
                        <label class="control-label col-md-2 label-fix-10"><?php echo __('adminvery.veryqr.label.background_color') ?></label>
                        <div class="col-md-5">
                            <div class="talkappi-colorpicker js-background-color" data-name="background_color" data-value="<?php echo($settings['background_color'])?>"></div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="control-label col-md-2 label-fix-10"><?php echo __('adminvery.veryqr.label.text_color') ?></label>
                        <div class="col-md-5">
                            <div class="talkappi-colorpicker js-color" data-name="color" data-value="<?php echo($settings['color'])?>"></div>
                        </div>
                    </div>
					<div class="form-group">
						<label class="control-label col-md-2 label-fix-10"><?php echo __('adminvery.veryqr.label.subtitle') ?></label>
						<div class="col-md-10">
                        <input type="text" class="form-control talkappi-textinput js-input-text-1" data-max-input="30" placeholder="" value='<?php echo($settings['text_1']); ?>'/>
						</div>
					</div>
                    <div class="form-group">
						<label class="control-label col-md-2 label-fix-10"><?php echo __('adminvery.veryqr.label.main_title') ?></label>
						<div class="col-md-10">
                        <input type="text" class="form-control talkappi-textinput js-input-text-2" data-max-input="15" placeholder="" value='<?php echo($settings['text_2']); ?>'/>
						</div>
					</div>
					<div class="form-group">
						<label class="control-label col-md-2 label-fix-10"><?php echo __('adminvery.veryqr.label.logo_image') ?></label>
						<div class="col-md-10">
							<div class="talkappi-upload js-main-pic-upload" data-name="main-pic" data-type="img" data-upload-now="very" data-ratio="3:1" data-label="<?php echo($settings['logo_url'])?>" data-url="<?php echo($settings['logo_url'])?>" data-max-size="2"></div>
						</div>
					</div>
                    <div class="form-group">
					<label class="control-label col-md-2 label-fix-10">
							<?php echo __('adminvery.veryqr.label.icon') ?>
							<span class="icon-detail js-send-address" style="margin: 0 0 0 0.5rem;" title="最大6つまで選択できます。"></span>
						</label>
						<div class="col-md-10 js-icon-container">
							<div class="flexbox-x-axis js-button-icons"></div>
                            <div class="image-action-group js-button-add-icon">
                                <img src="./../assets/admin/css/img/icon-add.svg" width="12" height="12">
                                <span><?php echo __('adminvery.veryqr.label.add_icon') ?></span>
                            </div>
						</div>
					</div>
					<?php
					if ($settings['room_flg'] == '0'){
						echo('<div id="qrcode" class="qr-container" style="display: none;"></div>');
					} else {
						foreach(explode( ',', $settings['room_number'] ) as $room){
							echo('<div id="qrcode_' . $room . '" class="qr-container" style="display: none;"></div>');
						}
					}
					?>
                    
				</div>		
			</div>
			<!-- ボタン　コンテナ -->
			<div class="actions-container" style="margin: 0 0 0 80px;">
				<span class="btn-larger btn-blue js-action-save"><?php echo __('index.save') ?></span>
                <!-- <span class="icon-export js-action-download">ダウンロード</span> -->
                <div class="action-button section btn-white js-action-download" style="height:40px;"><span class="icon-export"></span><?php echo __('adminvery.veryqr.label.export_data') ?></div>
			</div>
		</div>
		<!-- 右側 -->
		<div class="mobile-preview" style="width: 400px;height: 600px;position: sticky;top: 0;">
			<header class="header-container flexbox-x-axis" style="width: unset;">
				<h4 class="font-standard font-family-v2" style="margin: 0 auto 0 0;"><?php echo __('adminvery.veryqr.label.preview') ?></h4>
			</header>
			<main class="main-container" style="width: 400px; height: 560px; padding: 5px 20px 15px 20px;">
				<?php
					if ($settings['room_flg'] == '0'){
						$style = ($settings['pop_flg'] === '0') ? 'style="width: 360px; padding: 80px;"' : 'style="width: 360px;"';
						echo('<canvas id="canvas" width=' . $canvas_size['width'] . ' height=' . $canvas_size['height'] . ' ' . $style . '></canvas>');
					} else {
						foreach(explode( ',', $settings['room_number'] ) as $room){
							$style = ($settings['pop_flg'] === '0') ? 'style="width: 360px; padding: 80px;"' : 'style="width: 360px; padding-top:10px;"';
							echo('<canvas id="canvas_' . $room . '" width=' . $canvas_size['width'] . ' height=' . $canvas_size['height'] . ' ' . $style . '></canvas>');
						}
					}
				?>
			</main>
			<footer>
			</footer>
		</div>
	</div>
</div>
<script type="text/javascript" src="https://cdn.jsdelivr.net/npm/jszip@3.2.1/dist/jszip.js"></script>
