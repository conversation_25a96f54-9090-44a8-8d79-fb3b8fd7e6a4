<script>
	const bot_id = <?php echo $_bot_id; ?>;
  const _user_counts = <?php echo $user_counts_json_encoded ?>;
  const _clicks_count_data = <?php echo $click_counts_json_encoded ?>;
  const _clicks_count_data_for_table = <?php echo $click_counts_for_table_json_encoded ?>;
  const _columns_for_table = <?php echo $columns_for_table_json_encoded ?>;
</script>

<style type="text/css">
  .graphTitle {
    display: flex;
    align-items: center;
  }
  .linecharts {
    display: flex;
    flex-wrap: wrap;
  }
  .small-linechart {
    display:flex;
    flex-direction:column;
    justify-content: flex-end;
    align-items: center;
    min-width: 25%;
    max-width: 300px;
    background-color: #FFFFFF;
    padding: 16px 18px;
  }
.dashboard.card {
  height: 310px;
}

</style>

<div class="flex-x-between">
  <div><?php echo __('admin.veryreport.title') ?></div>
  <div style="display:flex;align-items:center">
    <a href="/adminvery/veryreport" class="btn-smaller btn light-blue" style="color:black;margin-right:8px"><?php echo __('admin.very.report.goto_old_ver') ?></a>
    <div class="talkappi-datepicker-range" data-date-format="yyyy-mm">
      <input name="start_date" value="<?php echo isset($post['start_date']) ? $post['start_date'] : ''; ?>"/><p>〜</p>
      <input name="end_date" value="<?php echo isset($post['end_date']) ? $post['end_date'] : ''; ?>"/>
    </div>
    <span class="btn-smaller btn-blue js-action-search"><?php echo __('admin.common.label.narrowdown') ?></span>
    <button type="button" id="csvexport_user" class="btn-smaller btn-white"><span class="icon-export"></span><?php echo __('admin.common.button.csv_export') . '（' . __('admin.top.label.user_statistics'). '）'; ?></button>
    <button type="button" id="csvexport_click" class="btn-smaller btn-white"><span class="icon-export"></span><?php echo __('admin.common.button.csv_export') . '（' . __('admin.common.label.count_click'). '）'; ?></button>
  </div>
</div>

<div id="page-wrapper" style="margin-top: 12px;">
  <div class="dashboard-cards">
    <!-- 新規ユーザー統計 -->
    <div class="col-md-6 col-sm-12 dashboard card">
      <div class="portlet light graph-card" style="padding:12px 20px 15px 20px;">
        <div class="graphTitle">
          <span style="font-size:14px;color:#3D4352;"><?php echo __('admin.veryreport1.user-log') ?></span>
        </div>
        <div class="react-user-count-group"></div>
      </div>
    </div>
    <!-- ユーザー統計（言語割合） -->
    <div class="col-md-6 col-sm-12 dashboard card">
      <div class="portlet light graph-card" style="padding:12px 20px 15px 20px;">
        <div class="graphTitle">
          <span style="font-size:14px;color:#3D4352;"><?php echo __('admin.veryreport1.user-log-lang') ?></span>
          <span class="icon-detail" style="margin: 0 0 0 0.5rem;" title="<?php echo htmlspecialchars(__('admin.veryreport.not_unique_user_count_tip')) ?>"></span>
        </div>
        <div class="react-donut-chart" data-lang_cd="ja" data-datas='<?php echo $language_user_counts_json_encoded ?>'></div>
      </div>
    </div>
    <!-- クリック数 -->
    <div class="col-md-12 col-sm-12 content-container white border" style="padding:20px;">
      <div class="react-functions-graph" data-lang_cd="<?php echo $_lang_cd ?>"></div>
    </div>
  </div>
</div>

<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/1.12.4/jquery.min.js"></script>
<script type="text/javascript" src="/assets/common/paginathing.js"></script>
<script type="text/javascript" src="/assets/common/react/pages/adminvery/veryreport.bundle.js"></script>
<script type="text/javascript" src="/assets/common/react/components/blocks/donutchart.bundle.js"></script>

<script type="text/javascript">
  _columns_for_table.forEach(column => {
    if (column.accessor !== 'title') {
      column.style = { textAlign: 'right' };
    }
    if (column.isTotalColumn) {
      column.style = { ...column.style, fontWeight: 'bold' };
    }
  });

  const languagePulldownChangeHandler = (lang) => {
    // console.log(lang)
  }

  const functionSelectionChandeHandler = (func) => {
    // console.log(func)
  }

  jQuery(document).ready(function($){
    window.talkappi_veryreport_setupUserContentGraph(_user_counts, 248, 
      "<?php echo __('admin.veryreport.month') ?>", "<?php echo __('admin.very.report.user_count') ?>");// _tooltip_template
    window.talkappi_veryreport_setupFunctionsGraph(_clicks_count_data, "<?php echo __('admin.veryreport.click_count_graph_title') ?>", languagePulldownChangeHandler, functionSelectionChandeHandler, _clicks_count_data_for_table, _columns_for_table,
      "<?php echo __('admin.veryreport.month') ?>", "<?php echo __('admin.very.report.click_count') ?>");// _tooltip_template
	});
</script>