<?php
define("NOTIFICATION_UNIT_PRICE", 3); // 通知単価
?>
<style>
    .content-title {
        margin-top: -20px;
        margin-left: -20px;
        display: flex;
        align-items: center;
    }

    .talkappi-month-picker-container>input {
        width: 100px;
    }

    .talkappi-month-picker-container {
        display: flex;
        column-gap: 3px;
        align-items: center;
    }

    .month-picker-month-table .ui-button {
        width: auto;
    }
</style>

<div class="content-container content-title">
    <div><?php echo __('admin.adminveryreport.bot_name'); ?></div>
    <select name="cond_bot_id" class="form-control js-grp-bot" style="width:25em;margin-left:8px" <?php if (count($sub_bots) === 1) echo 'disabled="disabled"' ?>>
        <option value=""><?php echo __('admin.push.all.facility') ?></option>
        <?php foreach ($sub_bots as $bot) { ?>
            <option value="<?php echo $bot['bot_id'] ?>" <?php if ($bot['bot_id'] === $cond_bot_id) echo 'selected="selected"' ?>><?php echo $bot['bot_name'] ?></option>
        <?php } ?>
    </select>
    <div style="margin:0 12px 0 48px;"><?php echo __('admin.pay.target.month') ?></div>
    <div class="talkappi-month-picker-container">
        <input class="form-control month-start" type="text" name="start_date" value="<?php echo isset($post['start_date']) ? $post['start_date'] : ''; ?>" />
        <span>〜</span>
        <input class="form-control month-end" type="text" name="end_date" value="<?php echo isset($post['end_date']) ? $post['end_date'] : ''; ?>" />
    </div>
</div>

<div class="content-container white">
    <table class="table table-striped table-bordered table-hover js-data-table">
        <thead>
            <tr class="odd">
                <th><?php echo __('admin.adminveryreport.bot_name'); ?></th>
                <th><?php echo __('admin.pay.target.month'); ?></th>
                <th><?php echo __('adminvery.receptionlog.label.notification_counts'); ?></th>
                <th><?php echo __('adminvery.receptionlog.label.notification_unit_price'); ?></th>
                <th><?php echo __('adminvery.receptionlog.label.notification_total_price'); ?></th>
            </tr>
        </thead>
        <tbody>
            <?php
            foreach ($result as $item) {
            ?>
                <tr class="gradeX odd" role="row">
                    <!-- 施設名 -->
                    <td>
                        <?php echo $item["bot_name"]; ?>
                    </td>
                    <!-- 対象月 -->
                    <td>
                        <?php echo $item["month"]; ?>
                    </td>
                    <!-- 通知件数 -->
                    <td style="text-align: right;">
                        <?php echo $item["notification_count"]; ?> 件
                    </td>
                    <!-- 通知単価 -->
                    <td style="text-align: right;">
                        <?php echo NOTIFICATION_UNIT_PRICE; ?> 円
                    </td>
                    <!-- 金額合計 -->
                    <td style="text-align: right;">
                        <?php echo number_format($item["notification_count"] *  NOTIFICATION_UNIT_PRICE) ?> 円
                    </td>
                </tr>
            <?php
            }
            ?>
        </tbody>
    </table>
</div>