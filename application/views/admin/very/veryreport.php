<script>
	const bot_id = <?php echo $_bot_id; ?>;
</script>

<style type="text/css">
.dashboard-cards-aline {
  margin: 0px 0px 5px 5px;
}
.dashboard-info-aline {
  margin: 0px 0px 10px 6px;
}
.list-item {
  display: flex;
  justify-content: space-between;
}
.pagination-container {
  margin: 0;
}
.graph-card {
  height: 480px;
}
</style>

<div id="page-wrapper">
  <p class="dashboard-cards-title dashboard-cards-aline"><?php echo __('admin.veryreport.title') ?></p>
  <div class="dashboard cards">
    <div class="dashboard card">
      <div class="dashboard card-container">
        <div class="number">
          <h3 id="user"><?php echo $user ?></h3>
        </div>
        <div class="progress-info">
          <small><?php echo __('admin.veryreport.progress-info.user') ?></small>
          <p></p>
        </div>
        <div class="status-title">VERY USER</div>
      </div>
    </div>
    <div class="dashboard card">
      <div class="dashboard card-container">
        <div class="number">
          <h3 id="member"><?php echo $member ?></h3>
        </div>
        <div class="progress-info">
          <small><?php echo __('admin.veryreport.progress-info.member') ?></small>
          <p></p>
        </div>
        <div class="status-title">VERY MEMBER</div>
      </div>
    </div>
    <div class="dashboard card">
      <div class="dashboard card-container">
        <div class="number">
          <h3 id="guest"><?php echo $guest ?></h3>
        </div>
        <div class="progress-info">
          <small><?php echo __('admin.veryreport.progress-info.guest') ?></small>
          <p></p>
        </div>
        <div class="status-title">VERY GUEST</div>
      </div>
    </div>
  </div>

  <div class="dashboard-cards">
    <div class="col-md-8 col-sm-12 dashboard card">
      <div class="portlet light graph-card" style="padding:12px 20px 15px 20px;">
        <div class="portlet-title">
          <div class="caption caption-md">
            <span class="caption-subject theme-font-color bold uppercase"><?php echo __('admin.veryreport.main-click') ?></span>
            <span class="caption-helper hide">main click stats...</span>
          </div>
          <div class="actions">
            <div class="btn-group btn-group-devided" data-toggle="buttons" id="main-click-buttons">
              <label class="btn btn-transparent grey-salsa btn-circle btn-sm" id="btnToday" data-type="day">
              <input type="radio" name="options" class="toggle" id="option1"><?php echo __('admin.veryreport.day') ?></label>
              <label class="btn btn-transparent grey-salsa btn-circle btn-sm active" id="btnWeek" data-type="week">
              <input type="radio" name="options" class="toggle" id="option2"><?php echo __('admin.veryreport.week') ?></label>
              <label class="btn btn-transparent grey-salsa btn-circle btn-sm" id="btnMonth" data-type="month">
              <input type="radio" name="options" class="toggle" id="option3"><?php echo __('admin.veryreport.month') ?></label>
              <label class="btn btn-transparent grey-salsa btn-circle btn-sm" id="btnYear" data-type="year">
              <input type="radio" name="options" class="toggle" id="option4"><?php echo __('admin.veryreport.year') ?></label>
            </div>
          </div>
        </div>
        <div class="portlet-body">
          <div class="table-scrollable table-scrollable-borderless">
            <div id="loading1" style="text-align: center;"><img src="/assets/admin/images/loading2.gif"/></div>
            <div id="main-click-body"></div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-md-4 col-sm-12 dashboard card">
      <div class="portlet light graph-card" style="padding:12px 20px 15px 20px;">
        <div class="portlet-title">
          <div class="caption caption-md">
            <span class="caption-subject theme-font-color bold uppercase"><?php echo __('admin.veryreport.top-click') ?></span>
            <span class="caption-helper hide">top click stats...</span>
          </div>
        </div>
        <div id="top-click-table" class="portlet-body" style="padding-top: 0; margin-bottom: 20px;">
          <div class="table-scrollable table-scrollable-borderless">
            <table class="table table-hover table-light">
            <tbody id="top-click-body" class="top-click-body">
              <?php
                $num_rows = ceil(count($top_clicks) / 10) * 10;
                for ($x=0; $x < $num_rows; $x++) {
                  if (count($top_clicks) > $x) {
                    echo('<tr style="display: none;"><td><div class="list-item"><span>' . json_decode($top_clicks[$x]["operate_detail"])->title . '</span><span>' . $top_clicks[$x]["amount"] . __('admin.veryreport.times') .'</span></div></td></tr>');
                  } else {
                    echo('<tr style="display: none;"><td><div>-</div></td></tr>');
                  }
                }
              ?>
            </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/1.12.4/jquery.min.js"></script>
<script type="text/javascript" src="/assets/common/paginathing.js"></script>

<script type="text/javascript">
   jQuery(document).ready(function($){
		$('#top-click-body').paginathing({
      perPage: 10,
      insertAfter: '#top-click-table',
      containerClass: 'pagination-container'
		})
	});
</script>