<style>
    html {
        touch-action: manipulation;
    }

    :root {
        --talkappi-blue: #245BD6;
        --activalues-red: #E53361;
        --solid-orange: #FF9551;
        --solid-green: #32CE55;
        --gray-white: #F6F7F9;
        --pale-gray: #E3E5E8;
        --common-gray: #C8CACE;
    }

    .page-content-wrapper .page-content {
        padding: 0 !important;
        margin-left: unset !important;
    }

    .line-tab li {
        padding: 0px 24px;
    }

    /* ヘッダーとメニューサイドバーを非表示 */
    .page-content-header,
    .page-sidebar-wrapper {
        display: none;
    }

    /* ページ内のデザイン */
    .reception-header {
        display: flex;
        margin-bottom: 10px;
    }

    .reception-header .reception-title {
        align-self: center;
        font-size: 2rem;
        font-weight: 500;
    }

    .reception-header .reception-status {
        align-self: center;
        padding: 0px 12px;
        color: var(--talkappi-blue);
        font-size: 1.5rem;
        white-space: nowrap;
    }

    .reception-header .reception-status.accept {
        color: var(--talkappi-blue);
    }

    .reception-header .reception-status.pause {
        color: var(--activalues-red);
    }

    .reception-header .reception-status-button {
        display: flex;
        align-self: center;
        padding: 10px 20px;
        border: 1px solid var(--common-gray);
        font-size: 1.5rem;
        margin-left: auto;
        border-radius: 4px;
        white-space: nowrap;
    }

    .reception-header .reception-status-button.accept {
        color: var(--talkappi-blue);
        background: #FFFFFF;
    }

    .reception-header .reception-status-button.pause {
        color: var(--activalues-red);
        background: #FFFFFF;
    }

    .reception-header .reception-status-button img {
        align-self: center;
        margin-right: 10px;
    }

    .reception-info-wrapper {
        display: flex;
    }

    .reception-info-wrapper .reception-info-content {
        display: flex;
        height: 52px;
        background-color: var(--gray-white);
        margin: 6px;
        padding: 12px;
        border-radius: 4px;
    }

    @media screen and (max-width: 1023px) {
        .reception-info-wrapper {
            display: block;
            width: auto;
        }

        .reception-info-wrapper .reception-info-content {
            width: auto;
        }

        .reception-info-wrapper .reception-info-content.list {
            width: auto;
        }
    }

    @media screen and (min-width: 1024px) {
        .reception-info-wrapper .reception-info-content {
            width: 20%;
        }

        .reception-info-wrapper .reception-info-content.list {
            width: 100%;
        }
    }

    .reception-info-wrapper .reception-info-title {
        align-self: center;
        margin-right: 12px;
    }

    .reception-info-wrapper .reception-info-list {
        display: flex;
        gap: 5px;

        align-items: center;
    }

    .reception-info-wrapper .reception-info-item {
        text-align: center;
        background: #FFFFFF;
        padding: 6px 8px;
        border: 1px solid var(--pale-gray);
        border-radius: 2px;

        font-size: 1.5rem;
        font-weight: 600;
    }

    .reception-info-wrapper .reception-info-detail {
        align-self: center;
        margin: 0 0 0 auto;
        font-size: 1.4rem;
        font-weight: 600;
    }

    .reception-table {
        width: auto;
        min-width: 100%;
        border-collapse: separate;
        border-spacing: 0;
    }

    .reception-table .nodata td {
        text-align: center;
    }


    .reception-table th,
    .reception-table tr,
    .reception-table td {
        border: 1px solid var(--pale-gray);
        text-align: left;
        padding: 10px;
        white-space: nowrap;
    }

    .reception-table th {
        background: var(--gray-white);
    }

    .reception-table .number.waiting {
        font-size: 1.5rem;
        font-weight: 600;
    }

    .reception-table .number.calling {
        color: var(--solid-orange);
        font-size: 1.5rem;
        font-weight: 600;
    }

    .reception-table .status.waiting {
        font-size: 1.3rem;
        font-weight: 600;
    }

    .reception-table .status.soon {
        font-size: 1.3rem;
        color: var(--solid-green);
        font-weight: 600;
    }

    .reception-table .status.calling {
        font-size: 1.3rem;
        color: var(--solid-orange);
        font-weight: 600;
    }

    .reception-table .status.complete {
        color: var(--solid-green);
    }

    .reception-table .status.cancel,
    .reception-table .status.auto_cancel {
        color: var(--activalues-red);
    }

    .status-button {
        height: 44px;
        width: -webkit-fill-available;
        min-width: 130px;
        padding: 12px 0px;
        white-space: nowrap;
        text-align: center;
        border: 1px solid;
        border-radius: 4px;
    }

    .status-button.call {
        color: var(--talkappi-blue);
        background: #D3EEFF80;
        border-color: var(--talkappi-blue);

    }

    .status-button.skip {
        color: var(--talkappi-blue);
        background: #FFFFFF;
    }

    .status-button.complete {
        color: #FFFFFF;
        background: var(--talkappi-blue);
        border-color: var(--talkappi-blue);
    }

    .status-button.cancel {
        color: #FFFFFF;
        background: var(--activalues-red);
        border-color: var(--activalues-red);
    }

    .status-button.sendback {
        color: var(--activalues-red);
        background: #FFFFFF;
        border-color: var(--activalues-red);
    }

    .status-button.disabled {
        color: #FFFFFF;
        background: var(--pale-gray);
        border-color: var(--pale-gray);
    }

    .reception-table input[type="checkbox"] {
        clip: auto;
        position: static;
        margin: 0 auto;
        display: block;
        cursor: pointer;
    }

    .bulk-action-container {
        display: flex;
        gap: 12px;
        align-items: center;
    }

    .bulk-action-buttons {
        display: flex;
        gap: 12px;
        margin-bottom: 10px;
    }

    .bulk-action-buttons button {
        padding: 8px;
        min-width: 60px;
        width: auto;
    }

    .table-container {
        overflow-x: auto;
        width: 100%;
        margin-top: 10px;
    }

    .reception-table th,
    .reception-table td {
        background-color: #fff; 
    }

    :root {
        --left-col-1-width: 40px;
        --left-col-2-width: 70px;
        --left-col-3-width: 110px;
        --right-col-1-width: 110px;
        --right-col-2-width: 150px;
        --right-col-3-width: 160px;
        --right-col-4-width: 160px;
    }

    .reception-table th.fixed-left,
    .reception-table td.fixed-left {
        position: sticky;
        left: 0;
        z-index: 1;
        border-right: 2px solid var(--pale-gray); 
        min-width: var(--left-col-1-width); 
        max-width: var(--left-col-1-width);
        width: var(--left-col-1-width); 
    }

    .reception-table th.fixed-left-2,
    .reception-table td.fixed-left-2 {
        position: sticky;
        left: var(--left-col-1-width); 
        z-index: 1;
        border-right: 2px solid var(--pale-gray);
        box-shadow: 4px 0 4px -4px rgba(0, 0, 0, 0.15);
    }
    
    .reception-table th.fixed-left-3,
    .reception-table td.fixed-left-3 {
        position: sticky;
        left: calc(var(--left-col-1-width) + var(--left-col-2-width)); 
        z-index: 1;
        background-color: #fff; 
        border-right: 2px solid var(--pale-gray);
        box-shadow: 4px 0 4px -4px rgba(0, 0, 0, 0.15);
    }

    .reception-table thead th.fixed-left-3 {
        background-color: var(--gray-white);
        z-index: 2;
    }

    .reception-table th.fixed-right,
    .reception-table td.fixed-right {
        position: sticky;
        right: 0;
        z-index: 1;
        border-left: 2px solid var(--pale-gray); 
    }

    .reception-table th.fixed-right-2,
    .reception-table td.fixed-right-2 {
        position: sticky;
        right: var(--right-col-1-width); 
        z-index: 1;
        border-left: 2px solid var(--pale-gray); 
        box-shadow: -4px 0 4px -4px rgba(0, 0, 0, 0.15);
    }

    .reception-table th.fixed-right-3,
    .reception-table td.fixed-right-3 {
        position: sticky;
        right: calc(var(--right-col-1-width) + var(--right-col-2-width)); 
        z-index: 1;
        border-left: 2px solid var(--pale-gray); 
        box-shadow: -4px 0 4px -4px rgba(0, 0, 0, 0.15);
    }

    .reception-table th.fixed-right-4,
    .reception-table td.fixed-right-4 {
        position: sticky;
        right: calc(var(--right-col-1-width) + var(--right-col-2-width) + var(--right-col-3-width)); 
        z-index: 1;
        border-left: 2px solid var(--pale-gray); 
        box-shadow: -4px 0 4px -4px rgba(0, 0, 0, 0.15);
    }

    .reception-table thead th {
        z-index: 2; 
        background-color: var(--gray-white);
    }

    .reception-table.no-data thead {
      display: none;
    }

    .reception-table.no-data tbody.nodata td {
      text-align: center;
    }
</style>
<?php if (isset($_GET['status'])) {
    $status = $_GET['status'];
} else {
    $status = "waiting";
} ?>

<script>
    const bot_id = <?php echo ($bot_id) ?>;
    const lang_cd = "<?php echo $lang_cd ?>";
    const reception_notification = <?php echo ($reception_data['notification'] ?: 0) ?>;
    const reception_id = <?php echo ($reception_id) ?>;
    const wait_time = <?php echo ($reception_data['waiting']['time'] ?: 0) ?>;
    const wait_party = <?php echo ($reception_data['waiting']['party'] ?: 0) ?>;
    const capacity_data = <?php echo json_encode($capacity, JSON_UNESCAPED_UNICODE) ?>;
    const reception_entry = <?php echo json_encode($reception_entry, JSON_UNESCAPED_UNICODE) ?>;
</script>
<div class="content-container white border">
    <div class="reception-header">
        <div class="reception-title">
            <?php echo $title ?>
        </div>
        <?php
        if ($pause_flg == 0) {
            echo ('<div class="reception-status accept">' . __('adminvery.receptionlist.facility-status.taken') . '</div>');
            echo ('<button type="button" class="reception-status-button pause js-button-pause"  id="' . $reception_id . '"><img src="../../assets/common/images/icon-powerbutton-off.svg">' . __('adminvery.receptionlist.facility-status.pause') . '</button>');
        } else {
            if ($pause_flg == 1) {
                echo ('<div class="reception-status pause">' . __('adminvery.receptionlist.facility-status.paused') . '</div>');
            } else if ($pause_flg == 2) {
                echo ('<div class="reception-status pause">' . __('adminvery.receptionlist.facility-status.auto.paused') . '</div>');
            }
            echo ('<button type="button" class="reception-status-button accept js-button-accept"  id="' . $reception_id . '"><img src="../../assets/common/images/icon-powerbutton-on.svg">' . __('adminvery.receptionlist.facility-status.resume') . '</button>');
        } ?>
    </div>
    <div class="reception-info-wrapper">
        <div class="reception-info-content list">
            <p class="reception-info-title text-nowrap"><?php echo __('adminvery.receptionlist.label.number-called') ?>
            <p>
            <div class="reception-info-list">
                <?php
                $calling_list = array_filter($reception_list_items, function ($k) {
                    return $k["status_cd"] == '3';
                });
                foreach ($calling_list as $item) { ?>
                    <div class="reception-info-item"><?php echo ($item["reception_no"]); ?></div>
                <?php } ?>
            </div>
        </div>
        <div class="reception-info-content">
            <p class="reception-info-title text-nowrap"><?php echo __('adminvery.receptionlist.label.number-waiting') ?></p>
            <p class="reception-info-detail text-nowrap" id="number-waiting"><?php echo ($groups_wait . __('adminvery.receptionlist.label.groups')) ?></p>
        </div>
        <?php if ($reception_data['waiting']['time'] && $reception_data['waiting']['party']) { ?>
            <div class="reception-info-content">
                <p class="reception-info-title text-nowrap"><?php echo __('adminvery.receptionlist.label.waiting-time') ?></p>
                <p class="reception-info-detail text-nowrap" id="waiting-time">
                    <?php echo (round($reception_data['waiting']['time'] * ($groups_wait / $reception_data['waiting']['party'])) . __('adminvery.receptionlist.label.minutes')) ?></p>
            </div>
        <?php } ?>
        <?php if ($reception_data['notification']) { ?>
            <div class="reception-info-content">
                <p class="reception-info-title text-nowrap"><?php echo __('adminvery.receptionlist.label.calling-soon-notice') ?>
                <p>
                <p class="reception-info-detail text-nowrap">
                    <?php echo (($reception_data['notification']) . __('adminvery.receptionlist.label.groups')); ?>
                <p>
            </div>
        <?php } ?>
    </div>
    <div style="display:flex;justify-content: space-between;align-items: center;">
        <nav class="line-tab">
            <ul>
                <li class=<?php
                            if ($status == 'waiting') {
                                echo "active";
                            } ?>>
                    <a href="<?php echo ("receptionlist?id=" . $reception_id . '&status=waiting') ?>"><?php echo __('adminvery.receptionlist.label.status.waiting') ?></a>
                </li>
                <li class=<?php
                            if ($status == 'complete') {
                                echo "active";
                            } ?>>
                    <a href="<?php echo ("receptionlist?id=" . $reception_id . '&status=complete') ?>"><?php echo __('adminvery.receptionlist.label.status.complete') ?></a>
                </li>
                <li class=<?php
                            if ($status == 'auto_cancel' || $status == 'cancel') {
                                echo "active";
                            } ?>>
                    <a href="<?php echo ("receptionlist?id=" . $reception_id . '&status=cancel') ?>"><?php echo __('adminvery.receptionlist.label.status.cancel') ?></a>
                </li>
            </ul>
        </nav>
        <label style="position: relative;">
            <input 
                type="search" 
                class="form-control input-inline" 
                id="table-search"
                placeholder="<?php echo __('admin.common.button.search') ?>" 
                style="margin: 0px 10px 0px 0px; padding: 0px 0px 0px 30px; width: 400px;"
            >
            <span 
                class="icon-search" 
                style="position: absolute;bottom: 0;left: 12px;top: 50%;-webkit-transform: translateY(-50%);-ms-transform: translateY(-50%);">
            </span>
        </label>
    </div>

    <div class="bulk-action-container"></div>

    <div class="table-container">
        <table class="reception-table status-<?php echo $status ?>" id="reception-list-table">
            <thead>
                <tr>
                    <th class="fixed-left"><input type="checkbox" id="select-all"></th>
                    <th class="fixed-left-2"><?php echo __('adminvery.receptionlist.label.reception-number') ?></th>
                    <?php
                    if (count($capacity) > 0) {
                        $is_first_capacity = true; // Flag for the first capacity column
                        foreach ($capacity as $key=>$val) {
                            $class_attr = '';
                            // Add class only to the first capacity column
                            if ($is_first_capacity) {
                                $class_attr = ' class="fixed-left-3"';
                                $is_first_capacity = false;
                            }
                            // Note: Removed the invalid class='capacity_"$key"' part
                            echo ('<th' . $class_attr . '>' .  $val . '</th>');
                        }
                    }
                    ?>
                    <?php
                    if (count($reception_entry) > 0) {
                        foreach ($reception_entry as $entry) {
                    ?>
                    <th><?php echo $entry['title'] ?></th>
                    <?php
                        }
                    }
                    ?>
                    <?php
                    if ($status === 'complete') {
                        echo ('<th class="fixed-right-4">' .  __('adminvery.receptionlist.label.complete-at') . '</th>');
                    }
                    if ($status === 'cancel') {
                        echo ('<th class="fixed-right-4">' .  __('adminvery.receptionlist.label.canceled-at') . '</th>');
                    } ?>
                    <th class="fixed-right-3"><?php echo __('adminvery.receptionlist.label.time-issued') ?></th>
                    <th class="fixed-right-2"><?php echo __('adminvery.receptionlist.label.status') ?></th>
                    <th class="fixed-right"><?php echo __('adminvery.receptionlist.label.operation') ?></th>
                </tr>
            </thead>
            <tbody></tbody>
        </table>
    </div>
</div>