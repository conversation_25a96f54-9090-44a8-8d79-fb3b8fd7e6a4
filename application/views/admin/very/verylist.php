<input type="hidden" name="settings" id="settings" value="" />
<input type="hidden" name="apply_template" id="apply_template" value="" />
<input type="hidden" name="apply_template_scene" id="apply_template_scene" value="" />
<input type="hidden" name="top_content_select" id="top_content_select" value="" />
<input type="hidden" name="travel_check_input" id="travel_check_input" value='' />


<div class="content-container" style="justify-items: end;">
    <div class="react-adminvery-create-button" data-lang_cd="<?php echo $_lang_cd ?>"></div>
</div>

<!-- テーブル -->
<div class="content-container white">
    <div class="react-adminvery-verylist" data-lang_cd="<?php echo $_lang_cd ?>"></div>
</div>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/1.12.4/jquery.min.js"></script>
<script src="/assets/common/react/components/atoms/colorpicker.bundle.js"></script>
<script src="/assets/common/react/components/blocks/multilingualreflect.bundle.js"></script>
<script src="/assets/common/react/pages/adminvery/verylist.bundle.js"></script>
<script src="/assets/common/react/pages/adminvery/createverybutton.bundle.js"></script>

<script type="text/javascript">
    const sceneList = <?php echo ($scene_list_without_very_setting) ?>;
    const veryList = <?php echo ($very_list) ?>;
    const templateBotList = <?php echo ($template_bot_list) ?>;
  
    jQuery(document).ready(function($) {
        window.talkappi_setupVeryListPage({
            tableData: veryList
        });
        window.talkappi_setupCreateVeryButton({
            templateBotList: templateBotList,
            sceneList: sceneList,
        });
    });
</script>