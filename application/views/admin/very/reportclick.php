<script>
  const bot_id = <?php echo $_bot_id; ?>;
  const _clicks_count_data = <?php echo $click_counts_json_encoded ?>;
  const _clicks_count_data_for_table = <?php echo $click_counts_for_table_json_encoded ?>;
  const _columns_for_table = <?php echo $columns_for_table_json_encoded ?>;
  const sceneCd = '<?php echo $scene_cd ?>';
  const scenesWithData = <?php echo ($scenes_with_data) ?>;
  const startDateCsv ='<?php echo isset($post['start_date']) ? $post['start_date'] : date('Y-m', strtotime('-11 months')) ?>';
  const endDateCsv ='<?php echo isset($post['end_date']) ? $post['end_date'] : date('Y-m'); ?>';
</script>

<style type="text/css">
  .linecharts {
    display: flex;
    flex-wrap: wrap;
  }
  .small-linechart {
    display:flex;
    flex-direction:column;
    justify-content: flex-end;
    align-items: center;
    min-width: 25%;
    max-width: 300px;
    background-color: #FFFFFF;
    padding: 16px 18px;
  }
</style>

<nav class="top-nav">
    <ul class="">
        <li><a href="/adminvery/reportuser"><?php echo __('admin.very.report.user_statistics') ?></a></li>
        <li class="active"><a href="/adminvery/reportclick"><?php echo __('admin.very.report.click_statistics') ?></a></li>
    </ul>
</nav>
<div class="content-container white" style="min-width:auto;">
  <div class="flex" style="justify-content:flex-end;align-items:center;flex-wrap:wrap;gap:5px;">
      <a href="/adminvery/veryreport" class="btn-smaller btn light-blue" style="color:black;margin-right:8px"><?php echo __('admin.very.report.goto_old_ver') ?></a>
      <div class="talkappi-pulldown js-scene-cd" style="width:200px;" data-name="scene_cd" data-value="<?php echo $scene_cd ?>" data-source='<?php echo ($scenes_with_data) ?>'></div>
      <div class="talkappi-datepicker-range" data-date-format="yyyy-mm">
        <input name="start_date" value="<?php echo isset($post['start_date']) ? $post['start_date'] : date('Y-m', strtotime('-11 months')) ?>"/><p>〜</p>
        <input name="end_date" value="<?php echo isset($post['end_date']) ? $post['end_date'] : date('Y-m'); ?>"/>
      </div>
      <span class="btn-smaller btn-blue js-action-search"><?php echo __('admin.common.label.narrowdown') ?></span>
        <button type="button" id="csvexport_click" class="btn-smaller btn-white"><span class="icon-export"></span><?php echo __('admin.common.button.csv_export') ?></button>
  </div>

  <div id="page-wrapper" style="margin-top: 12px;">
    <!-- クリック数 -->
    <div class="white" style="">
        <div class="react-functions-graph" data-lang_cd="<?php echo $_lang_cd ?>"></div>
    </div>
  </div>
</div>



<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/1.12.4/jquery.min.js"></script>
<script type="text/javascript" src="/assets/common/react/pages/adminvery/reportclick.bundle.js"></script>

<script type="text/javascript">
  _columns_for_table.forEach(column => {
    if (column.accessor !== 'title') {
      column.style = { textAlign: 'right' };
    }
    if (column.isTotalColumn) {
      column.style = { ...column.style, fontWeight: 'bold' };
    }
  });
  jQuery(document).ready(function($){
      window.talkappi_veryreport_setupFunctionsGraph(_clicks_count_data, "<?php echo __('admin.veryreport.click_count_graph_title') ?>", _clicks_count_data_for_table, _columns_for_table,
      "<?php echo __('admin.veryreport.month') ?>", "<?php echo __('admin.very.report.click_count') ?>");
	});
</script>