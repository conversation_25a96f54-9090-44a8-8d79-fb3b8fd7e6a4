<?php 
    if(isset($items->$lang_edit)) { 
        $current_item = json_encode($items->$lang_edit, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES | JSON_HEX_AMP | JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_QUOT); 
    } else { 
        $current_item = null; 
    } 
?>
<script>
    let _items = null;
    <?php if (isset($items)) { ?>
        _items = <?php echo json_encode($items, JSON_UNESCAPED_UNICODE) ?>;
    <?php } ?>  
    const all_lang_items = <?php echo json_encode($items, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES | JSON_HEX_AMP | JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_QUOT) ?>;
    const _to_lang_cds = <?php echo json_encode($translate_to_lang, JSON_UNESCAPED_UNICODE) ?>;
    const _from_lang_cd = '<?php echo $lang_edit ?>';
</script>
<style>
    .section-wrapper {
        display: flex;
        align-items: flex-start;
    }
    .talkappi-section {
        width: 60%;
        margin-right: 5%;
    }
   .js-section-container.content-container,
   .js-add-icon.content-container {
        min-width: initial;
    }
    .col-md-5 {
        width : 80%;
    }
    .note-editor {
        margin:initial;
        max-width: 710px;
    }
    .note-editable {
        background: white;
    }
    #react-multilingualreflect {
        margin-top: 20px;
    }
    #react-multilingualreflect .control-answer {
        max-width: 500px;
    }
</style>
<div class="content-container white border">
    <div class="section-container bottom-line">
		<div class="setting-header"><?php echo __('admin.common.label.user_flow') ?></div>
		<div class="form-group">
			<div class="talkappi-pulldown js-scene-cd" data-name="scene_cd" data-value="<?php echo $scene_cd ?? ''?>" data-size="longer" data-source='<?php echo $scene_list ?>'></div>
		</div>
	</div>
    <div style="display:flex; justify-content:space-between; width:60%; margin-top:12px;">
        <nav class="line-tab" style="padding-top: 0;">
            <ul class="flexbox" style="margin: 0; padding-inline-start: 0px;flex-flow: wrap; gap: 6px 0px;">
            <?php
                $lang_arr = explode(',', $_bot->support_lang);
                foreach($lang_arr as $k=>$v) {
                    if ($v == $lang_edit) {
                        echo('<li class=" active">');
                    }
                    else {
                        echo('<li>');
                    }
                    echo('<a class="func-menu" href="/adminvery/userguide?lang='. $v . '&scene_cd=' . $scene_cd . '">' . $_codes['02'][$v] . '</a ></li>');
                }
            ?>
            </ul>
        </nav>
    </div>

    <div style="display:flex; justify-content:space-between; width:60%;">
        <button type="button" class="btn-smaller js-apply-template" style="background: #EBEDF2; border:solid 1px #EBEDF2;"><?php echo __('admin.veryuserguide.apply_template') ?></button>
    </div>
    <div class="section-wrapper">
    <!-- section components -->
    <div class="talkappi-section" data-value='<?php echo $current_item ?>' data-name="items" data-type="userguide" style="display: grid;row-gap: 2rem;">
    </div>
    <!-- section components -->
    <!-- mobile preview -->
    <div class="mobile-preview" style="width: 400px;height: 844px;position: sticky;top: 0; display:flex; flex-direction:column;">
        <span style="padding:10px 0px; text-align: center;"><?php echo __('admin.verytop.preview_message') ?></span>
        <header class="header-container flexbox-x-axis" style="width:unset;">
            <h4 class="font-standard font-family-v2" style="margin: 0 auto 0 0;"><?php echo __('admin.common.label.preview') ?></h4>
        </header>
        <main class="main-container" style="width: 400px; height: 800px; padding: 0;">
        <iframe id="talkappi-very-frame" src="<?php echo $very_userguide_url ?>" width="100%" height="100%" style="border:0;" marginwidth="0" marginheight="0" allowtransparency="yes"></iframe>
        </main>
        <footer></footer>
    </div>
    <!-- mobile preview -->
    </div>
    <?php 
        $to_lang_cds = array_filter($translate_to_lang, function($key) use ($lang_edit) {
            return $key != $lang_edit;
        }, ARRAY_FILTER_USE_KEY);
        if (count($to_lang_cds) > 0) {
    ?>
    <div id="react-multilingualreflect"></div>
    <?php } ?>
    <div class="form-actions">
        <div class="row">
            <div class="col-md-offset-1">
                <div class="actions-container" style="margin-top: 48px;">
                    <button type="button" class="action-button page btn-blue js-action-save"><?php echo __('admin.common.button.save') ?></button>
                    <a class="action-button page btn-white js-action-back"><?php echo __('admin.common.button.return_to_list') ?></a>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="/assets/common/react/components/blocks/multilingualreflect.bundle.js"></script>