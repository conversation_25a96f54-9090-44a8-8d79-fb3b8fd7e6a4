<script>
	const _settings = <?php echo json_encode($settings, JSON_UNESCAPED_UNICODE); ?>;
	const _bot_lang = '<?php echo json_encode(explode(',', $_bot->support_lang), JSON_UNESCAPED_UNICODE); ?>';
	<?php 
	if ($_bot_setting['div_item_class_2_very'] != '') $_bot_setting['div_item_class_2'] = $_bot_setting['div_item_class_2_very'];
	?>
	const _very_function = <?php echo json_encode($applicable_functions, JSON_UNESCAPED_UNICODE); ?>;
	const _very_footer = <?php echo json_encode($applicable_footers, JSON_UNESCAPED_UNICODE); ?>;
	const _func_def = <?php echo json_encode($func_def, JSON_UNESCAPED_UNICODE); ?>;
	const _lang_edit = <?php echo json_encode($lang_edit); ?>;
	const _area_selection_values = <?php echo json_encode($area_selection_values); ?>;
	const _area_selection_dict = <?php echo json_encode($area_selection_dict); ?>;
	const _show_page = <?php echo json_encode($show_page); ?>;
	const _show_survey = <?php echo json_encode($show_survey); ?>;
	const _all_class_code = <?php echo json_encode($all_class_code); ?>;
	const _to_lang_cds = <?php echo json_encode($very_supported_langs) ?>;
	const _from_lang_cd = "<?php echo $lang_edit ?>";
	const _verify_url = '<?php echo $verify_url ?>';
	const _user_role = "<?php echo $user_role ?>";
</script>
<input type="hidden" name="settings" id="settings" value="" />
<input type="hidden" name="top_content_select" id="top_content_select" value="" />
<style>
/* メイン画像、メイン機能などのlabelの改行防止(現状7文字まで) */
.label-fix-4 {
	max-width: 120px;
}

/* 以下の対応のためCSS追加
https://github.com/talkappi/very/pull/292#issuecomment-1445556434 */
.js-talkappi-fup-modal-input-method-url{
 display:none;
}

.js-top-content>.js-func-kind>.classbox-container>.form-group {
	display: flex;
	/* padding: 12px; */
	/* gap: 12px; */
}
.js-top-content>.js-func-kind>.classbox-container>.form-group>.talkappi-pulldown {
	margin-left: 10px;
}

.js-category-pulldown {
    margin-top: 0 !important;
    width: 192px;
}

.js-category-select {
    display: flex;
    gap: 10px;
    align-items: flex-start;
}

.js-func-params-category-item-name {
	position: relative;
	height: auto; 
	white-space: normal; 
	padding-right: 30px; 
	min-height: 30px; 
	text-wrap: balance; 
	background: #FFF;
}
.js-item-select-items-id-modal {
	position: absolute;
	transform: translateY(-50%);
	right: 1rem; 
	top: 50%; 
	color: #245BD6; 
	cursor: pointer;
}

.js-main-pic-button,
.js-main-function-button,
.js-top-content-button {
	display: flex;
}

div.js-top-content div.form-group {
	display: flex;
	flex-direction: column;
	gap: 8px;
	margin: 0;
}

/* ボタン(talkappi dark grey) */
.btn-black-border {
  border: solid 1px #3D3F45 !important;
  background: #fff;
  min-width:unset !important;
  width:unset !important;
}

.icon-arrow-top {
  width: 12px;
  height: 12px;
  background-image: url(/assets/common/images/icon-arrow-top.svg);
  background-repeat: no-repeat;
  margin-bottom: 1px;
}
.icon-arrow {
  width: 12px;
  height: 12px;
  background-image: url(/assets/common/images/icon-arrow-up.svg);
  background-repeat: no-repeat;
  margin-bottom: 2px;
}
/* main menu button tab in left side */
.panel.button-tab>ul {
	column-count: 2;
	column-gap: 10px;
	row-gap: 10px;
	max-height: 612px;
	/* smaller height for debug */
	overflow-y: auto;
	overflow-x: hidden;
}
.panel.button-tab>ul>li {
	width: 90px;
	height: 40px;
	border-radius: 8px;
	padding: 0 12px;
	margin: unset;
	align-items: center;
}
.panel.button-tab>ul>li>a {    
    font-weight: 400;
	overflow: hidden;
	margin: auto;

	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	word-wrap: break-word;
	text-align: center;
}
.panel.button-tab>ul>li.talkappi-pulldown {    
    font-weight: 400;
}

.sortable-placeholder {
	border: 1px dashed blue;
	background-color: pink;
}

/**
style of talkappi-dropdown-container and talkappi-dropdown-options overwritten by .panel.button-tab,
so fix the style here
*/
.panel.button-tab>ul>li.talkappi-pulldown>div.talkappi-dropdown-container {
	position: inherit;
	background-color: unset;
	padding-top: 12px;
}

.panel.button-tab>ul>li.talkappi-pulldown>div.talkappi-dropdown-container>ul.talkappi-dropdown-options {
	min-width: unset;
	/* initially do not display */
	display: none;
	/* position dropdown options near container */
	top: unset;
	left: unset;
}

.panel.button-tab>ul>li.talkappi-pulldown>div.talkappi-dropdown-container>ul.talkappi-dropdown-options>li.dropdown-option {
	border-radius: unset;
	white-space: nowrap;
	padding: 0 12px;
	border-top: 1px solid #b5b5b5;
	border-left: 1px solid #b5b5b5;
	margin: unset;
}
.panel.button-tab>ul>li.talkappi-pulldown>div.talkappi-dropdown-container>div.talkappi-dropdown-selected>div.image-action-group>img {
	margin: unset;
	background-color: unset;
}
#react-multilingualreflect {
	padding: 16px 12px;
}
#react-multilingualreflect .control-title {
	text-align: left;
	padding-left: 10px;
}

.history-record-container{
	width: 400px;
    position: relative;
}
.history-record-container .history-record-title {
	display: flex;
	padding: 6px 12px;
	align-items: center;
	gap: 12px;
	align-self: stretch;
	border-radius: 999px;
	background: #EBEDF2;
	cursor: pointer;
}
.history-record-container .history-record-items {
	display: none;
	width: 400px;
	position: absolute;
    top: 100%;
    left: 0;
	border-radius: 12px;
	border: 1px solid #E3E5E8;
	box-shadow: 1px 2px 8px 0px rgba(61, 63, 69, 0.24);
	background-color: #FFFFFF;
	overflow-y: auto;
	max-height: 300px;
}
.history-record-container .history-record-items .history-record-item {
	display: flex;
	padding: 8px 12px;
	flex-direction: column;
	justify-content: center;
	align-items: flex-start;
	gap: 4px;
	background: #FFF;
	cursor: pointer;
	position: relative;
    z-index: 999;
}
.history-record-container .history-record-items .history-record-item:nth-child(2n-1) {
	background: #F6F7F9;
}
.history-record-container .history-record-items .history-record-item .history-record-item-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	gap: 12px;
	width: 100%;
}
.history-record-item .history-record-item-header .history-record-item-info {
	color: #000;
	font-size: 12px;
	font-weight: 600;
}
.history-record-item .history-record-item-header .history-record-item-version {
	color: #1CA38B;
	font-family: "Hiragino Sans";
	font-size: 12px;
	font-weight: 400;
}
.history-record-item .history-record-item-note {
	color: #77797D;
	font-size: 12px;
	font-weight: 300;
}

.overlay-wrapper {
    position: relative;
}

.overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 50;
    pointer-events: all;
	border-radius: 16px;
    background-color: rgba(255, 255, 255, 0.8);
	border: 1px solid #E3E5E8;
}

.content-overlay.overlay {
    background-color: rgba(255, 255, 255, 0.5);
	border: none;
}

.overlay-text {
    color: #000;
    font-size: 16px;
    font-weight: 700;
	padding: 10px 10px;
    display: inline-block;
    text-align: left;
}
#talkappi_file_upload_memo {
	color: #77797D;
}
</style>

<nav class="top-nav"
    style="position: relative;">
    <?php
    // Get the 'f' parameter from the URL if it exists
    $f_param = isset($_GET['scene_cd']) ? '?f=' . urlencode($_GET['scene_cd']) : '';
    $scene_cd_param = isset($_GET['scene_cd']) ? '&scene_cd=' . urlencode($_GET['scene_cd']) : '';
    ?>
    <ul class="">
        <li><a href="/adminvery/very_setting<?php echo $f_param; ?>"><?php echo __('admin.common.label.basic.setting') ?></a></li>
        <li class="active"><a href="#"><?php echo __('admin.verytop.very.setting') ?></a></li>
    </ul>
    <a href="/adminvery/verytop?lang=<?php echo $lang_edit; ?><?php echo $scene_cd_param; ?>"	
		class="action-button section light-orange"
		style="position: absolute; right: 0; top: 0; border-radius: 4px; color: #fff; background: #FF9551;">
			<?php echo __('admin.common.label.edit.old_screen'); ?>
	</a>
</nav>

<div class="content-container white border">
	<div style="display:flex;justify-content:space-between;align-items:center;width: calc(100% - 424px);">
		<div id="react-adminlangtabs"></div>
	</div>
	<div style="display:flex;">
		<!-- 左側 -->
		<div class="mobile-preview-left-content">
			<?php 
				$label_message = '';
				if ($version == 'current') {
					$label_message = __('admin.item.itemdesc.version.current');
				} else {
					$label_message = __('admin.item.itemdesc.version.previous');
				}

				if ($label_message !== '' && $requested_version_record['upd_user'] && $requested_version_record['upd_time']) {
					$label_message .= '・' . $requested_version_record['upd_user'] . ' - ' . substr($requested_version_record['upd_time'], 0, 16);
				}
			?>
			<?php if ($label_message !== '') { ?>
				<div class="flex" style="padding:12px 0;gap:40px;align-items:center">
					<div class="update-information" style="font-size:14px;font-weight:600">
						<?php echo $label_message ?>
					</div>
					<div style="display:flex;align-items:center;gap:22px;">
						<?php 
							if ($history_mode === 'read') {
								echo '<div>' . __('admin.item.itemdesc.readonly') . '</div>';
								echo '<div class="btn round image edit js-edit-from">' . __('admin.item.itemdesc.edit_from_the_version') . '</div>';
							}
						?>
					</div>
				</div>
			<?php } ?>
			<div class="overlay-wrapper">
				<?php if($version !== 'current' && $history_mode === 'read'): ?>
					<div class="content-overlay overlay">
					</div>
				<?php endif; ?>
				<div class="section-container">
					<div class="form-body">
						<div class="form-group">
							<label class="control-label col-md-2 label-fix-4">
								<?php echo __('admin.verytop.font_setting') ?>
							</label>
							<div class="col-md-10">
								<input type="text" class="form-control js-font" value="<?php echo htmlspecialchars($settings['font'] ?? '') ?>" placeholder="<?php echo __('admin.verytop.font_setting.placeholder') ?>">
							</div>
						</div>
					</div>
				</div>
				<!-- ウィジェット設定 -->
				<div class="section-container">
					<div class="setting-header"><?php echo __('admin.verytop.widget.settings') ?></div>
					<div class="form-body">
						<!-- 天気ウィジェット -->
						<div class="form-group">
							<label class="control-label col-md-2 label-fix-4"><?php echo __('admin.verytop.widget.weather_settings') ?></label>
							<div class="col-md-10">
								<div style="margin-bottom:20px; padding-top: 4px;">
									<div class="talkappi-radio js-widget-weather-select" data-type="menu" data-action="<?php echo __('admin.verytop.category_type'); ?>" data-name="js-weather-widget" data-value="<?php echo isset($settings['widget']['weather']) ? 1 : 0; ?>" data-source='{"0":"<?php echo __('admin.verytop.widget.hide'); ?>","1":"<?php echo __('admin.verytop.widget.show'); ?>"}'></div>
								</div>
								<div class="weather-widget-container" style="background-color:#F6F7F9; border: 1px solid #E3E5E8; border-radius:4px; padding:12px; margin-bottom:10px; flex-wrap: wrap; <?php echo isset($settings['widget']['weather']) ? "display: flex;" : "display: none;" ?>">
									<div class="checkbox-label" style="display: flex; align-items: center; width: 50%;">
										<input type="checkbox" name="temperature" id="widget-weather-temperature" style="display: none;">
										<label for="widget-weather-temperature" style="display: flex; align-items: center;  cursor: pointer;">
											<img src="/assets/admin/images/widget_temperature.svg" style="margin-right: 5px;">
											<?php echo __('admin.verytop.widget.wether_temperature') ?>
										</label>
									</div>
									<div class="checkbox-label" style="display: flex; align-items: center; width: 50%;">
										<input type="checkbox" name="sun" id="widget-weather-sun" style="display: none;">
										<label for="widget-weather-sun" style="display: flex; align-items: center; cursor: pointer;">
											<img src="/assets/admin/images/widget_sun.svg" style="margin-right: 5px;">
											<?php echo __('admin.verytop.widget.sunrise_sunset') ?>
										</label>
									</div>
								</div>
							</div>
						</div>
						<!-- Wi-fiウィジェット -->
						<div class="form-group">
							<label class="control-label col-md-2 label-fix-4"><?php echo __('admin.verytop.widget.wifi_settings') ?></label>
							<div class="col-md-10">
								<div style="margin-bottom:20px; padding-top: 4px;">
									<div class="talkappi-radio js-widget-wifi-select" data-type="menu" data-action="<?php echo __('admin.verytop.category_type'); ?>" data-name="js-wifi-widget" data-value="<?php echo isset($settings['widget']['wi-fi']) ? 1 : 0; ?>" data-source='{"0":"<?php echo __('admin.verytop.widget.hide'); ?>","1":"<?php echo __('admin.verytop.widget.show'); ?>"}'></div>
								</div>
								<div class="wifi-widget-container" style="background-color:#F6F7F9; border: 1px solid #E3E5E8; border-radius:4px; padding:12px; margin-bottom:10px; flex-wrap: wrap; <?php echo isset($settings['widget']['wi-fi']) ? "display: flex; flex-direction: column;" : "display: none;" ?>">
									<div style="display: flex; align-items: center; width: 100%;">
										<label style="display: flex; align-items: center; margin-bottom: 0; flex-shrink: 0;">
											<?php echo __('admin.verytop.widget.wifi_content_label'); ?><img src="/assets/admin/images/icon-wifi.svg" style="margin-left: 8px;">
										</label>
										<div class="col-md-8" style="flex-grow: 1;">
											<input type="text" class="form-control talkappi-textinput js-input-wifi-description1" data-max-input="25" placeholder="SSID：talkappi_guest" value='<?php echo($settings['widget']['wi-fi']['description1'] ?? ''); ?>' />
										</div>
										<div class="col-md-8" style="flex-grow: 1; padding-left: 0;">
											<input type="text" class="form-control talkappi-textinput js-input-wifi-description2" data-max-input="25" placeholder="PW：talkappi2016" value='<?php echo($settings['widget']['wi-fi']['description2'] ?? ''); ?>' />
										</div>
									</div>
									<div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px; align-items: center; margin-top: 10px;">
										<div style="display: flex; align-items: center;">
											<label style="display: flex; align-items: center; margin-bottom: 0;">
												<?php echo __('admin.verytop.background_color'); ?>
											</label>
											<div class="col-md-5" style="flex-grow: 1;">
												<div class="talkappi-colorpicker js-theme-color" data-name="wifiBackgroundColor" data-value="<?php echo($settings['widget']['wi-fi']['background-color'] ?? "#ffffff")?>" style="width: 100%;"></div>
											</div>
										</div>
										<div style="display: flex; align-items: center;">
											<label style="display: flex; align-items: center; margin-bottom: 0;">
												<?php echo __('admin.verytop.text_color'); ?>
											</label>
											<div class="col-md-5" style="flex-grow: 1;">
												<div class="talkappi-colorpicker js-theme-color" data-name="wifiTextColor" data-value="<?php echo($settings['widget']['wi-fi']['text-color'])?>" style="width: 100%;"></div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<!-- 基本設定 -->
			<div class="section-container">
				<div class="setting-header"><?php echo __('admin.verytop.basic_settings'); ?></div>
				<div class="form-body">
					<div class="overlay-wrapper">
						<?php if($version !== 'current' && $history_mode === 'read'): ?>
							<div class="content-overlay overlay"></div>
						<?php endif; ?>
						
						<div class="form-group">
							<label class="control-label col-md-2 label-fix-4">
								<?php echo __('admin.verytop.title'); ?>
							</label>
							<div class="col-md-10">
								<input type="text" class="form-control talkappi-textinput js-input-title" data-max-input="20" placeholder="" value='<?php echo($settings['title']); ?>'/>
							</div>
						</div>
						
						<div class="form-group">
							<label class="control-label col-md-2 label-fix-4">
								<?php echo __('admin.verytop.logo_image'); ?>
							</label>
							<div class="col-md-10 js-logo-pic-container">
							</div>
						</div>
					</div>
						
					<!-- main image -->
					<div class="form-group" id="main-image">
						<label class="control-label col-md-2 label-fix-4">
							<?php echo __('admin.verytop.main_image'); ?>
						</label>
						<div class="col-md-10 js-main-pic-container main-pic-container">
							<nav class="button-tab" style="padding:0;">
							<ul class="js-main-pic-buttons" data-can-add="<?php echo ($version === 'current' || $history_mode === 'write') ? '1' : '0'; ?>">
							    <?php if($version === 'current' || $history_mode === 'write'): ?>
							        <li class="js-main-pic-add-button">
							            <span class="icon-add"></span>
							            <?php echo __('admin.verytop.main_image_add'); ?>
							        </li>
							    <?php endif; ?>
							</ul>
							</nav>
							<div class="overlay-wrapper">
								<?php if($version !== 'current' && $history_mode === 'read'): ?>
									<div class="content-overlay overlay"></div>
								<?php endif; ?>
								<div style="background-color:#F6F7F9;border: 1px solid #E3E5E8;border-radius:4px;padding:12px;margin-bottom:10px;">
									<div class="js-main-pic"></div>
									<div class="actions-group-container js-main-pic-action" style="justify-content: space-between;">
										<span style="display:flex;">
											<!-- 並び替え -->
											<span class="btn-smaller btn-black-border icon-only js-reshuffle" data-action="first" style="transform:rotate(270deg);">
												<span class="icon-arrow-top"></span>
											</span>
											<span class="btn-smaller btn-black-border icon-only js-reshuffle" data-action="before" style="transform:rotate(270deg);">
												<span class="icon-arrow"></span>
											</span>
											<span class="btn-smaller btn-black-border icon-only js-reshuffle" data-action="after" style="transform:rotate(90deg);">
												<span class="icon-arrow"></span>
											</span>
											<span class="btn-smaller btn-black-border icon-only js-reshuffle" data-action="last" style="transform:rotate(90deg);">
												<span class="icon-arrow-top"></span>
											</span>
										</span>
										<span class="btn-smaller btn-red-border icon-only js-main-pic-delete" style="margin-right: 12px;">
											<span class="icon-delete"></span>
										</span>
									</div> 
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			<!-- 内容設定 -->
			<div class="section-container" style="margin-top:-28px;">
				<div class="setting-header"><?php echo __('admin.verytop.content_setting');?></div>
				<div class="form-body">
					<div class="form-group">
						<label class="control-label col-md-2 label-fix-4">
							<?php echo __('admin.verytop.top_functions'); ?>
							<span class="icon-detail" style="vertical-align: sub;" title="<?php echo __('admin.verytop.select_five') ?>"></span>
						</label>
						<div class="col-md-10">
							<nav class="button-tab" style="padding:0;">
								<ul class="js-main-function-buttons">
									<?php
									$active = ' active';
									$sort_index = 1;
									foreach($settings['functions'] as $f) {
										if (isset($f['title']) && $f['title'] !== "") {
											echo('<li class="js-main-function-button' . $active . '" value=' . $sort_index . '><a href="javascript:void(0);">' . $f['title'] . '</a></li>');
										} else {
											echo('<li class="js-main-function-button' . $active . '" value=' . $sort_index . '><a href="javascript:void(0);">' . $_codes['VERY_FUNCTION'][$f['func']['kind']] . '</a></li>');
										}
										$active = '';
										$sort_index++;
									}
									if ($active == '') {
										$plus = 'icon-add';
									}
									else {
										$plus = 'icon-add-white';
									}
									?>
								</ul>	
							</nav>
							<div class="overlay-wrapper">
								<?php if($version !== 'current' && $history_mode === 'read'): ?>
									<div class="content-overlay overlay"></div>
								<?php endif; ?>
								<div class="talkappi-pulldown js-select-main-function-type" data-type="menu" data-action="<?php echo __('admin.verytop.add'); ?>" data-name="main-function-type" data-value="" data-source='{"basic":"<?php echo __('admin.verytop.basic_functions'); ?>", "customize":"<?php echo __('admin.verytop.customize'); ?>"}'></div>
								<div style="background-color:#F6F7F9;border: 1px solid #E3E5E8;border-radius:4px;padding:12px;margin-bottom:10px;">
									<div class="js-main-function"></div>
									<div class="actions-group-container js-main-function-action" style="justify-content: space-between;">
										<span style="display:flex;">
											<!-- 並び替え -->
											<span class="btn-smaller btn-black-border icon-only js-reshuffle" data-action="first" style="transform:rotate(270deg);"><span class="icon-arrow-top"></span></span>
											<span class="btn-smaller btn-black-border icon-only js-reshuffle" data-action="before" style="transform:rotate(270deg);"><span class="icon-arrow"></span></span>
											<span class="btn-smaller btn-black-border icon-only js-reshuffle" data-action="after" style="transform:rotate(90deg);"><span class="icon-arrow"></span></span>
											<span class="btn-smaller btn-black-border icon-only js-reshuffle" data-action="last" style="transform:rotate(90deg);"><span class="icon-arrow-top"></span></span>
										</span>
										<span class="btn-smaller btn-red-border icon-only js-main-function-delete" style="margin-right: 12px;"><span class="icon-delete"></span></span>
									</div> 
								</div>
							</div>
						</div>
					</div>
					<div class="overlay-wrapper">
						<?php if($version !== 'current' && $history_mode === 'read'): ?>
							<div class="content-overlay overlay"></div>
						<?php endif; ?>
						<div class="form-group">
							<label class="control-label col-md-2 label-fix-4">
								<?php echo __('admin.verytop.notices'); ?>
								<span class="icon-detail" style="vertical-align: sub;" title="<?php echo __('admin.verytop.select_five') ?>"></span>
							</label>
							<div class="col-md-10 js-div-notices"></div>
					</div>
					<div class="form-group">
						<label class="control-label col-md-2 label-fix-4">
							<?php echo __('admin.verytop.notices.half.modal'); ?>
						</label>
						<div class="col-md-10 js-div-notices-half-modal">
							</div>
						</div>
						<div class="form-group" style="display:flex; align-items:center;">
							<label class="control-label col-md-2 label-fix-4" style="padding-top:0; padding-left:10px; margin-top:0;">
								<?php echo __('admin.verytop.main_contents'); ?>
							</label>
							<div style="width:83.33%; display:flex; justify-content:flex-end; padding-right:10px;">
								<div class="talkappi-radio js-category-type-select" data-type="menu" data-action="<?php echo __('admin.verytop.category_type'); ?>" data-name="js-category-type-select" data-value="<?php echo $settings['categorys_type'] ?? 1 ?>" data-source='{"1":"<?php echo __('admin.verytop.category_type_button'); ?>","2":"<?php echo __('admin.verytop.category_type_icon'); ?>"}'></div>
							</div>
						</div>
					</div>
					<div class="form-group col-md-12">
						<div class="flex" style="gap: 12px;">
							<nav class="panel button-tab" style="padding:0; width: <?php echo ($settings['categorys_type'] == "2") ? "305px" : "205px"; ?>;">
								<ul class="js-top-content-buttons" style="width: <?php echo ($settings['categorys_type'] == "2") ? "305px" : "205px"; ?>;">
									<?php
									$active = ' active';
									$sort_index = 1;
									foreach($settings['categorys'] as $c) {
										echo('<li class="js-top-content-button' . $active . '" value=' . $sort_index . '><a href="javascript:void(0);">' . $c['title'] . '</a></li>');
										$active = '';
										$sort_index++;
									}
									if ($active == '') {
										$plus = 'icon-add';
									}
									else {
										$plus = 'icon-add-white';
									}
									?>
									<?php if($version === 'current' || $history_mode === 'write'): ?>
										<li class="talkappi-pulldown js-select-top-content-type" data-type="menu" data-action="<?php echo __('admin.verytop.add_contents'); ?>" data-name="js-top-content-type" data-value="" data-source='<?php echo json_encode($func_def, JSON_UNESCAPED_UNICODE); ?>'></li>
									<?php endif; ?>
								</ul>	
							</nav>
							<div class="overlay-wrapper">
								<?php if($version !== 'current' && $history_mode === 'read'): ?>
									<div class="content-overlay overlay"></div>
								<?php endif; ?>
								<div style="background-color:#F6F7F9;border: 1px solid #E3E5E8;border-radius:4px;padding:12px;margin-bottom:10px;width:100%;height:fit-content;">
									<div class="js-top-content"></div>
									<div class="actions-group-container js-top-content-action" style="justify-content: space-between;">
										<span style="display:flex;">
											<!-- 並び替え -->
											<span class="btn-smaller btn-black-border icon-only js-reshuffle" data-action="first"><span class="icon-arrow-top"></span></span>
											<span class="btn-smaller btn-black-border icon-only js-reshuffle" data-action="before"><span class="icon-arrow"></span></span>
											<span class="btn-smaller btn-black-border icon-only js-reshuffle" data-action="after" style="transform:rotate(180deg);"><span class="icon-arrow"></span></span>
											<span class="btn-smaller btn-black-border icon-only js-reshuffle" data-action="last" style="transform:rotate(180deg);"><span class="icon-arrow-top"></span></span>
										</span>
										<span class="btn-smaller btn-red-border icon-only js-top-content-delete" style="margin-right: 12px;"><span class="icon-delete"></span></span>
									</div> 
								</div>
							</div>
						</div>
					</div>
					<div class="form-group">
						<label class="control-label col-md-2 label-fix-4">
							<?php echo __('admin.verytop.official_sns') ?>
						</label>
						<div class="col-md-10">
							<div class="overlay-wrapper">	
								<?php if($version !== 'current' && $history_mode === 'read'): ?>
									<div class="content-overlay overlay"></div>
								<?php endif; ?>
								<div class="talkappi-pulldown js-sns-pulldown" data-type="menu" data-action="<?php echo __('admin.verytop.add'); ?>" data-value="" data-source='{"instagram":"Instagram", "facebook":"Facebook", "line":"LINE", "youtube":"YouTube", "twitter":"X (Twitter)", "tiktok":"TikTok"}'></div>
								<ul class="js-sns-rows" style="padding-left:0"></ul>
							</div>
						</div>
					</div>
					<?php if (isset($settings['footer'])) { ?>
					<div class="form-group">
						<label class="control-label col-md-2 label-fix-4">
							<?php echo __('admin.verytop.footer'); ?>
							<span class="icon-detail" style="vertical-align: sub;" title="<?php echo __('admin.verytop.select_three') ?>"></span>
						</label>
						<div class="col-md-10">
							<nav class="button-tab" style="padding:0;">
								<ul class="js-footer-buttons">
									<?php
									$active = ' active';
									$sort_index = 1;
									foreach($settings['footer'] as $f) {
										if ($f['func']['type'] == 'basic'){
											echo('<li class="js-footer-button' . $active . '" value=' . $sort_index . '><a href="javascript:void(0);">' . $_codes['VERY_FOOTER'][$f['func']['kind']] . '</a></li>');
										}
										else {
											echo('<li class="js-footer-button' . $active . '" value=' . $sort_index . '><a href="javascript:void(0);">' . $f['title'] . '</a></li>');
										}
										$active = '';
										$sort_index++;
									}
									if ($active == '') {
										$plus = 'icon-add';
									}
									else {
										$plus = 'icon-add-white';
									}
									?>
								</ul>	
							</nav>
							<div class="overlay-wrapper">
								<?php if($version !== 'current' && $history_mode === 'read'): ?>
									<div class="content-overlay overlay"></div>
								<?php endif; ?>
								<div class="image-action-group js-footer-add" data-source='{"basic":"<?php echo __('admin.verytop.basic_functions'); ?>"}'>
									<img src="./../assets/admin/css/img/icon-add.svg" width="12" height="12">
									<span><?php echo __('admin.verytop.add'); ?></span>
								</div>
								<div style="background-color:#F6F7F9;border: 1px solid #E3E5E8;border-radius:4px;padding:12px;margin-bottom:10px;">
									<div class="js-footer"></div>
									<div class="actions-group-container js-footer-action" style="justify-content: flex-end;">
										<span class="btn-smaller btn-red-border icon-only js-footer-delete" style="margin-right: 12px;"><span class="icon-delete"></span></span>
									</div> 
								</div>
							</div>
						</div>
					</div>
					<?php }?>
				</div>
			</div>
			<!-- 多言語翻訳 -->
			<?php if ($history_mode !== 'read'): ?>
				<?php 
					$to_lang_cds = [];
					if (isset($lang_cd)) {
						$to_lang_cds = array_filter($display_langs, function ($key) use ($lang_cd) {
							return $key != $lang_cd;
						}, ARRAY_FILTER_USE_KEY);
					}
					if (count($to_lang_cds) > 0): 
				?>
					<div id="react-multilingualreflect"></div>
				<?php endif; ?>
			<?php endif; ?>
			<!-- ボタン　コンテナ -->
			<div class="actions-container" style="margin: 20px 0 0 80px;">
				<?php if($version === 'current'): ?>
					<span class="btn-larger btn-blue js-action-save"><?php echo __('admin.common.button.save') ?></span>
					<div class="btn-larger btn-gray-black js-action-verify"><?php echo __('admin.common.button.verify') ?></div>
					<!-- <span class="btn-larger btn-gray-black js-action-temp">下書き保存</a></span> -->
					<span class="btn-larger btn-white js-action-back"><?php echo __('admin.common.button.return_to_list') ?></span>
				<?php endif; ?>
				<?php if($version !== 'current' && $history_mode === 'read'): ?>
					<span class="btn-larger btn-white js-action-back-to-current-version"><?php echo __('admin.item.itemdesc.back_to_current') ?></span>
					<span class="btn-larger btn-white js-action-back"><?php echo __('admin.common.button.return_to_list') ?></span>
				<?php endif; ?>
				<?php if($version !== 'current' && $history_mode === 'write'): ?>
					<span class="btn-larger btn-blue js-action-save"><?php echo __('admin.common.button.save') ?></span>
					<span class="btn-larger btn-white js-action-back"><?php echo __('admin.common.button.return_to_list') ?></span>
				<?php endif; ?>
			</div>
		</div>
		<!-- 右側 -->
		<div class="mobile-preview" style="width: 400px;height: 844px;position: sticky;top: 0; display:flex; flex-direction:column;">
			<?php if (is_array($history_records) && count($history_records) > 1) { ?>
			<div class="history-record-container">
				<div class="history-record-title js-history-record"><?php echo $requested_version_record['upd_user'] . ' - ' . $requested_version_record['upd_time'] ?></div>
				<div class="history-record-items js-history-pulldown">
					<?php foreach ($history_records as $index => $record) { ?>
					    <div class="history-record-item js-history-record-item" data-version="<?php echo $index === 0 ? 'current' : $record['version'] ?>">
					        <div class="history-record-item-header">
					            <div class="history-record-item-info">
					                <?php if ($record['translate_type'] === 1) echo __('admin.item.itemdesc.auto_translate') . ' - ' ?>
					                <?php echo $record['upd_user'] ?> - <?php echo $record['upd_time'] ?>
					            </div>
					            <div class="history-record-item-version">
					                <?php 
					                    echo $index === 0 
					                        ? __('admin.item.itemdesc.version.current') 
					                        : '';
					                ?>
					            </div>
					        </div>
					        <div class="history-record-item-note">
					            <?php echo htmlspecialchars($record['note']) ?? __('admin.item.itemdesc.no_comment') ?>
					        </div>
					    </div>
					<?php } ?>
				</div>
			</div>
			<?php } ?>
			<span style="padding:10px 0px; text-align: center;"><?php echo __('admin.verytop.preview_message') ?></span>
			<header class="header-container flexbox-x-axis" style="width: unset;">
				<h4 class="font-standard font-family-v2" style="margin: 0 auto 0 0;"><?php echo __('admin.common.label.preview') ?></h4>
			</header>
			<main class="main-container" style="width: 400px; height: 800px; padding: 0; display: flex; justify-content: center; align-items: center;">
				<?php if($version !== 'current'): ?>
					<span style="text-align: center;"><?php echo __('admin.verytop.history.cannot_show_mobile_preview_for_old_versions') ?></span>
				<?php else: ?>
					<?php 
						echo ( '<iframe id="talkappi-very-frame" src="' . $verify_url . '" width="100%" height="100%" style="border:0;" marginwidth="0" marginheight="0" allowtransparency="yes"></iframe>');
					?>
				<?php endif; ?>
			</main>
			<footer>
			</footer>
		</div>
	</div>
</div>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/1.12.4/jquery.min.js"></script>
<script src="/assets/common/react/components/atoms/colorpicker.bundle.js"></script>
<script src="/assets/common/react/components/blocks/multilingualreflect.bundle.js"></script>
<script src="/assets/common/react/components/blocks/adminlangtabs.bundle.js"></script>

<script type="text/javascript">
    jQuery(document).ready(function($) {
		const displayedLangs = Object.keys(<?php echo json_encode($display_langs) ?>);
		const allLangs = Object.keys(<?php echo json_encode($very_supported_langs)?>);
		const undisplayedLangs = allLangs.filter(key => !displayedLangs.includes(key));
		const scene_cd = '<?php echo $settings['scene_cd']; ?>';

        window.talkappi_admin_setupAdminLangTabs({
            displayed: displayedLangs, 
            undisplayed: undisplayedLangs,
            url: `/adminvery/very_top?scene_cd=${scene_cd}&lang=`,
            active: '<?php echo $lang_edit ?>', 
        });
    });
</script>
