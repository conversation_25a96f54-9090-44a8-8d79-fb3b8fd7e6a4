<input type="hidden" name="setting_data" id="setting_data" />
<style>
.template-select-radio input[type="radio"]:checked + label img {
    border-radius: 6px;
}
</style>

<?php
// Get the 'f' parameter from the URL if it exists
$f_param = isset($_GET['f']) ? '&scene_cd=' . urlencode($_GET['f']) : '';
?>

<nav class="top-nav"
    style="position: relative;">
    <ul class="">
        <li class="active"><a href="#"><?php echo __('admin.common.label.basic.setting') ?></a></li>
        <?php if ($f_param && count($default_display_langs) > 0) { ?>
            <li>
            <a href="/adminvery/very_top?lang=<?php echo urlencode(array_keys($default_display_langs)[0]); ?><?php echo $f_param; ?>">
                <?php echo __('admin.verytop.very.setting') ?>
            </a>
            </li>
        <?php } ?>
    </ul>
    <a href="/adminvery/verytop?lang=ja<?php echo $f_param; ?>"
       class="action-button section light-orange"
       style="position: absolute; right: 0; top: 0; border-radius: 4px; color: #fff; background: #FF9551;">
        <?php echo __('admin.common.label.edit.old_screen'); ?>
    </a>
</nav>
<div 
    class="very-setting-page white border content-container" 
    style="padding: 0px;" 
    data-lang_cd="<?php echo $_lang_cd ?>
">
</div>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/1.12.4/jquery.min.js"></script>
<script type="text/javascript" src="/assets/common/react/pages/adminvery/verysetting.bundle.js"></script>

<script type="text/javascript">
    jQuery(document).ready(function($) {
        window.talkappi_setupVerySetting({
            userRole: '<?php echo $user_role ?>',
            sceneList: <?php echo json_encode($scene_list) ?>,
            defaultDisplayLangs: <?php echo json_encode($default_display_langs) ?>,
            settingData: <?php echo json_encode($settings) ?>,
        })
    });
</script>
