
<?php 
	$user = Session::instance()->get('user', NULL);
	$style = '';
	if ($req['link_status_cd'] == '01') {
		if ($user->role_cd == '99') {
			echo('<a href="javascript:void(0);" class="canceltask" req_id="' . $req['id'] . '" status_cd="02"><span class="badge badge-default" style="margin: 4px;">' . __('admin.common.label.cancellation_request') . '</span></a><br/>');
		}
		else {
			echo('<a href="javascript:void(0);" class="updatetask" req_id="' . $req['id'] . '" status_cd="02"><span class="badge badge-warning" style="margin: 4px;">' . __('admin.common.label.mark_as_supported') . '</span></a><br/>');
		}
	}
	else if ($req['link_status_cd'] == '02' && $user->role_cd == '99') {
		echo('<a href="javascript:void(0);" class="updatetask" req_id="' . $req['id'] . '" status_cd="03"><span class="badge badge-warning" style="margin: 4px;">確認済にする </span></a><br/>');
	}
	else if ($req['link_status_cd'] == '03') {
		$style = ' confirm';
	}
	echo('<div class="req-task-info' . $style . '">');
	echo($req['title'] . '<br />');
	if (date('Y-m-d') > $req['limited_date']) {
		$style=' style="color:red;"';
	}
	else {
		$style='';
	}
	if ($req['limited_date'] != '') echo('<span class="small">(期日: <span'. $style . '>' . $req['limited_date'] . '</span>まで）</span><br />');
	echo($req['description']);
	if ($req['res_user'] != null) {
		echo('<span class="small">' . $req['res_user_name'] . ' は' .  $req['res_time'] . 'に対応済）</span>');
	}
	echo('</div>');
?>
