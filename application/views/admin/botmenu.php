							<div class="tabbable-line">
								<ul class="nav nav-tabs ">
									<li class="<?php if ($_action == 'bot') echo 'active'?>">
										<a class="func-menu" href="/admin/bot">
										契約情報</a>
									</li>
									<li class="<?php if ($_action == 'botclass') echo 'active'?>">
										<a class="func-menu" href="/admin/botclass">
										基本設定</a>
									</li>
									<?php if ($_bot->bot_id >= 201000 && $_bot->bot_id < 299999 && $_bot->bot_id % 1000 == 0) {?>
									<li class="<?php if ($_action == 'botsublist') echo 'active'?>">
										<a class="func-menu" href="/admin/botsublist">
										子ボット一覧</a>
									</li>	
									<?php }?>
									<?php if ($_bot->bot_id >= 2001000 && $_bot->bot_id < 2999999 && $_bot->bot_id % 1000 == 0) {?>
									<li class="<?php if ($_action == 'botsublist') echo 'active'?>">
										<a class="func-menu" href="/admin/botsublist">
										子ボット一覧</a>
									</li>	
									<?php }?>
									<?php if ($_user->role_cd == "99") {?>
									<li class="<?php if ($_action == 'botcheck') echo 'active'?>">
										<a class="func-menu" href="/admin/botcheck">
										詳細設定※</a>
									</li>																					
									<li class="<?php if ($_action == 'botscene' || $_action == 'bottheme') echo 'active'?>">
										<a class="func-menu" href="/admin/botscene">
										ユーザー導線※</a>
									</li>
									<li class="<?php if ($_action == 'bottime') echo 'active'?>">
										<a class="func-menu" href="/admin/bottime">
										営業時間※</a>
									</li>
									<li class="<?php if ($_action == 'botcontext') echo 'active'?>">
										<a class="func-menu" href="/admin/botcontext">
										コンテキスト※</a>
									</li>																								
									<li class="<?php if ($_action == 'botskill') echo 'active'?>">
										<a href="/admin/botskill">
										SKILL設定※</a>
									</li>
									<li class="<?php if ($_action == 'botclasscode') echo 'active'?>">
										<a href="/admin/botclasscode">
										CODE設定※</a>
									</li>
									<?php }?>
								</ul>
							</div>
