			<!-- BEGIN PAGE HEADER-->
			<!-- BEGIN PAGE HEAD -->
			<div class="page-head">
				<!-- BEGIN PAGE TITLE -->
				<div class="page-title">
					<h1>パターン管理<small></small></h1>
				</div>
				<!-- END PAGE TITLE -->
			</div>
			<!-- END PAGE HEAD -->
			<!-- END PAGE HEADER-->
			<!-- BEGIN PAGE CONTENT-->
			<div class="row">
				<div class="col-md-12">
			        <!-- Page Content -->
			        <div id="page-wrapper">
			        <div class="portlet light">
						<div class="tabbable-line"> 
							<ul class="nav nav-tabs ">
								<li class="active">
									<a href="/admin/patternlist">
									パターン一覧</a>
								</li>
								<li class="">
									<a href="/admin/pattern">
									パターン作成</a>
								</li>
							</ul>
						</div>
						<div class="portlet box">
							<div class="portlet-body">
							<table class="table table-striped table-bordered table-hover" id="sample_3">
							<thead>
							<tr>
								<th>
									パターンID
								</th>
								<th>
									  種類
								</th>								
								<th>
									  パターン名
								</th>
								<th>
									 説明
								</th>															
							</tr>
							</thead>

							<tbody>
							<?php
								foreach ($users as $user) {
							?>	
							<tr class="gradeX odd" role="row">
								<td class="sorting_1">
									 <?php echo($user['name'])?>
								</td>
								<td>
									<?php echo($user['email'])?>
								</td>
								<td>
									
								</td>								
								<td>
									 <?php 
									 	echo($user['facebook']);
									 	if ($user['facebook_flg'] == 1) {
									 		echo('<span class="badge badge-success">Active</span>');
									 	}
									 ?>										 
								</td>
								<td>
									 <?php 
									 	echo($user['line']);
									 	if ($user['line_flg'] == 1) {
									 		echo('<span class="badge badge-success">Active</span>');
									 	}
									 ?>	
								</td>
								<td>
									 <?php 
									 	echo($user['wechat']);
									 	if ($user['wechat_flg'] == 1) {
									 		echo('<span class="badge badge-success">Active</span>');
									 	}
									 ?>	
								</td>									
							</tr>
							<?php } ?>
							</tbody>
							</table>
							</div>
						</div>
					</div>
			        </div>
			        <!-- /#page-wrapper -->					
				</div>
			</div>
			<!-- END PAGE CONTENT-->
