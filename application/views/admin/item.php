<script>
	const _bot_id = <?php echo $bot_id ?>;
	const _item_id = <?php echo isset($item_id) ? $item_id : json_encode(null) ?>;
	const _current_lang_display = <?php echo $lang_display = $post['lang_display'] == null || $post['lang_display'] == '' ? json_encode([]) : json_encode(explode( ',', $post['lang_display'])) ?>;
</script>
<?php $model = new Model_Adminmodel();?>
<!-- BEGIN PAGE CONTENT-->
<?php echo $menu ?>
<div class="content-container white border">
	<div class="section-container">
		<input type="hidden" id="act" name="act" value="" />
		<input type="hidden" id="item_div" name="item_div" value="<?php echo $item_div?>" />
		<input type="hidden" id="class_div" name="class_div" value="<?php echo $div_item_class?>" />
		<input type="hidden" id="area_div" name="area_div" value="<?php echo $div_item_area?>" />
		<input type="hidden" name="class_cd_hidden" id="class_cd_hidden" value="<?php echo $post['class_cd']?>" />
		<input type="hidden" name="area_cd_hidden" id="area_cd_hidden" value="<?php echo $post['area_cd']?>" />
		
		<div class="form-body">	
			<?php if ($item_id != NULL) { ?>
				<div class="form-group">
					<label class="control-label col-md-2"><?php echo __('admin.item.itemdisplay.name') ?></label>
					<div class="col-md-6 readonly-input flex-x-between" style="width:500px;height:fit-content;margin-left:10px;">
						<p><?php echo $post['item_name'] ?></p>
						<a class="js-edit-desc" data-lang="<?php echo $bot_local_lang?>"><?php echo __('admin.common.button.edit') ?></a>
					</div>
				</div>
			<?php } ?>
			<?php echo($classbox);?>
			<?php if ($div_item_area != '') { echo($areabox);}?>
			<div class="form-group"<?php if ($_user->role_cd != "99") echo('style="display:none;"');?>>
				<label class="control-label col-md-2"><?php echo __('admin.itme.item.code') ?></label>
				<div class="col-md-5">
					<div class="input-icon right">
						<input name="item_cd" type="text" class="form-control" value="<?php if ($post != NULL) echo($post['item_cd'])?>" placeholder=<?php echo __('admin.itme.item.code.placeholder') ?>>
					</div>
				</div>
			</div>
			<div class="form-group">
				<label class="control-label col-md-2"><?php echo __('admin.itme.item.status') ?></label>
				<div class="col-md-2">
					<?php echo Form::select('item_status_cd', $_codes['11'], $post['item_status_cd'], array('id'=>'item_status_cd','class'=>'form-control'))?>
				</div>
			</div>
			<div class="form-group"<?php if ($_user->role_cd != "99") echo('style="display:none;"');?>>
				<label class="control-label col-md-2"><?php echo __('admin.itme.item.link_code') ?></label>
				<div class="col-md-5">
					<div class="input-icon right">
						<input name="link_id" type="text" class="form-control" value="<?php if ($post != NULL) echo($post['link_id'])?>">
					</div>
				</div>
			</div>
			<div class="form-group"<?php if ($_user->role_cd != "99") echo('style="display:none;"');?>>
				<label class="control-label col-md-2"><?php echo __('admin.itme.item.subclass') ?></label>
				<div class="col-md-5">
					<div class="input-icon right">
						<input name="item_class_type_sub_cd" type="text" class="form-control" value="<?php if ($post != NULL) echo($post['item_class_type_sub_cd'])?>">
					</div>
				</div>
			</div>									
			<div class="form-group">
				<label class="control-label col-md-2"><?php echo __('admin.itme.item.display_period') ?></label>
				<div class="col-md-4">
					<input name="start_date" id="start_date" value="<?php if ($post != NULL)echo($post['start_date'])?>" style="float:left;" class="form-control form-control-inline input-small date-picker" size="16" data-date-format="yyyy-mm-dd"  type="text"/>
					<input name="end_date" id="end_date" value="<?php if ($post != NULL) echo($post['end_date'])?>" style="float:left; margin-left:10px;" class="form-control form-control-inline input-small date-picker" size="16" data-date-format="yyyy-mm-dd"  type="text"/>
				</div>									
			</div>
			<div class="form-group"<?php if ($_user->role_cd != "99") echo('style="display:none;"');?>>
				<label class="control-label col-md-2"><?php echo __('admin.itme.item.annual_display') ?>  </label>
				<div class="col-md-1">
					<?php echo Form::select('start_mm', $months, $post['start_mm'], array('id'=>'start_mm','class'=>'form-control month'))?>
				</div>
				<div class="col-md-1">
					<?php echo Form::select('start_dd', [$post['start_dd']=>$post['start_dd']], $post['start_dd'], array('id'=>'start_dd','class'=>'form-control'))?>
				</div>
				<label class="control-label col-md-1" style="text-align:center;">～</label>
				<div class="col-md-1">
					<?php echo Form::select('end_mm', $months, $post['end_mm'], array('id'=>'end_mm','class'=>'form-control month'))?>
				</div>
				<div class="col-md-1">
					<?php echo Form::select('end_dd', [$post['end_dd']=>$post['end_dd']], $post['end_dd'], array('id'=>'end_dd','class'=>'form-control'))?>
				</div>																									
			</div>													
			<div class="form-group">
				<label class="control-label col-md-2"><?php echo __('admin.itme.item.longitude_latitude') ?></label>
				<div class="col-md-2">
					<input name="location_lat" type="text" class="form-control" value="<?php if ($post != NULL) echo($post['location_lat'])?>">
				</div>
				<div class="col-md-2">
					<input name="location_lon" type="text" class="form-control" value="<?php if ($post != NULL) echo($post['location_lon'])?>">
				</div>
			</div>
			<div class="form-group" <?php if ($_bot->bot_class_cd!='90') echo('style="display:none;"');?>>
				<label class="control-label col-md-2"> <?php echo __('admin.itme.item.location_information') ?></label>
				<div class="col-md-2">
					<input name="position_x" id="label" type="text" class="form-control" placeholder="X" value="<?php if ($post != NULL) echo($post['position_x'])?>">
				</div>
				<div class="col-md-2">
					<input name="position_y" id="label" type="text" class="form-control" placeholder="Y" value="<?php if ($post != NULL) echo($post['position_y'])?>">
				</div>
				<div class="col-md-2">
					<input name="position_z" id="label" type="text" class="form-control" placeholder="Z" value="<?php if ($post != NULL) echo($post['position_z'])?>">
				</div>																				
			</div>
			<?php 
			if (count($icons) > 0) {
				echo('<div class="form-group">');
				echo('<label class="control-label col-md-2">' . __('admin.itme.item.tag') . '</label>');
				echo('<div class="col-md-9">');
				echo('<div class="btn-group" data-toggle="buttons">');
				foreach($icons as $k=>$v) {
					if (in_array($k, $post['tags'])) {
						echo('<label class="btn default active">');
						echo('<input name="tags[]" type="checkbox" checked="true" value="' . $k . '" class="toggle">' . $k. '</label>');
					}
					else {
						echo('<label class="btn default">');
						echo('<input name="tags[]" type="checkbox" value="' . $k . '" class="toggle">' .  $k . '</label>');
					}
				}
				echo('</div>');
				echo('</div>');
				echo('</div>');
			}
			?>
			<!-- <?php 
			if (count($icons) > 0) { ?>
				<div class="form-group">
					<label class="control-label col-md-2"><?php echo __('admin.itme.item.tag') ?></label>
					<div class="col-md-9">
						<?php $icons_data_source = [];
							foreach($icons as $key => $value){
								$icons_data_source[$key] = $key;
							}
						?>
						<div class="talkappi-checkbox js-tags" data-name="tags" data-value='<?php echo json_encode($post['tags']) ?>' 
						data-source='<?php echo json_encode($icons_data_source, JSON_UNESCAPED_UNICODE|JSON_HEX_APOS) ?>'></div>
					</div>
				</div>
			<?php } ?> -->

			<?php
				foreach($item_data_def as $key=>$def) {
					if ($def['type'] == 'sel' || $def['type'] == 'opt' || $def['type'] == 'chk') {
						$def['list'] = $model->get_customize_list($def);
						if (count($def['list']) == 0) continue;
					}
					echo('<div class="form-group">');
					echo('<label class="control-label col-md-2">' . $def['title'] . '</label>');
					echo('<div class="col-md-8">');
					if (!array_key_exists($key, $item_data)) $item_data[$key] = '';
					if ($def['type'] == 'opt') {
						echo('<div class="talkappi-radio" data-name="' . $key . '" data-value="' . $item_data[$key] . '" data-source=\'' .  json_encode($def['list'], JSON_UNESCAPED_UNICODE) .'\'></div>');
					} 
					else if ($def['type'] == 'sel') {
						echo('<div class="talkappi-pulldown" data-name="' . $key . '" data-value="' . $item_data[$key] . '" data-source=\'' .  json_encode($def['list'], JSON_UNESCAPED_UNICODE) .'\'></div>');
					}
					else if ($def['type'] == 'chk') {
						echo('<div class="talkappi-checkbox" data-name="' . $key . '" data-value=\'' . json_encode($item_data[$key]) . '\' data-source=\'' .  json_encode($def['list'], JSON_UNESCAPED_UNICODE) .'\'></div>');
					}
					else if ($def['type'] == 'num' || $def['type'] == 'txt') {
						$style = '';
						if ($def['type'] == 'num') {
							$style = 'style="width:100px;text-align: right;"';
						}
						if (array_key_exists($key, $item_data)) {
							$value = $item_data[$key];
						}
						else {
							$value = '';
						}
						echo('<input name="' . $key . '" type="text" class="form-control" value="' .  $value . '"' . $style . ' >');
					}
					echo('</div>');
					echo('</div>');
				}
			?>

			<?php if (count($icons) > 0 || count($banners) > 0) {?>
			<div class="form-group">
				<label class="control-label col-md-2"><?php echo __('admin.itme.item.immediate_reflection_photos') ?> </label>
				<div class="col-md-2">
					<input type="checkbox" name="flg_img_invalidate" value="1" class="make-switch" data-on-color="success" data-off-color="warning">
				</div>																		
			</div>	
			<?php }?>
			<div class="form-group">
                <label class="control-label col-md-2"><?php echo __('admin.itme.item.language_display') ?>:</label>
                <div class="col-md-9">
					<?php $lang_display = $post['lang_display'] == null || $post['lang_display'] == '' ? [] : explode( ',', $post['lang_display']) ?>
                    <div class="talkappi-checkbox js-language" data-name="lang_display" data-value='<?php echo json_encode($lang_display) ?>' data-source='<?php echo json_encode($_bot_lang, JSON_UNESCAPED_UNICODE|JSON_HEX_APOS) ?>'></div>
                </div>
            </div>
			<div class="form-group">
                <label class="control-label col-md-2"><?php echo __('admin.itme.item.photo_display') ?>:</label>
                <div class="col-md-9">
					<?php $show_image = $post['show_image'] == null || $post['show_image'] == '' ? [] : explode( ',', $post['show_image']) ?>
                    <div class="talkappi-checkbox js-image" data-name="show_image" data-value='<?php echo json_encode($show_image) ?>' data-source='<?php echo json_encode($_bot_lang, JSON_UNESCAPED_UNICODE|JSON_HEX_APOS) ?>'></div>
                </div>
            </div>
			<div class="form-group">
				<label class="control-label col-md-2"><?php echo __('admin.itme.item.public_flag') ?></label>
				<div class="col-md-2">
					<input type="checkbox" name="public_flg" value="1" <?php if ($post['public_flg'] == 1) echo ('checked')?> class="make-switch" data-on-color="success" data-off-color="warning">
				</div>																		
			</div>
			<div class="form-group">
				<label class="control-label col-md-2"><?php echo __('admin.itme.item.business_hours') ?> </label>
				<div class="col-md-3">
					<div class="input-icon right">
						<input name="business_hours" type="text" class="form-control" value="<?php if (($post != NULL) && isset($post['business_hours'])) echo($post['business_hours'])?>">
					</div>
				</div>
			</div>
			<?php if (isset($post['item_id'])) {?>
			<div class="form-group">
				<label class="control-label col-md-2"><?php echo __('admin.item.item.business.hours') ?></label>
				<div class="col-md-7">
					<div class="readonly-input flex-x-between" style="height: auto; white-space: normal;">
						<div class="js-businesshours-setting"  data-value='<?php echo( json_encode($post['item_business_schedule'], JSON_UNESCAPED_UNICODE|JSON_HEX_APOS))?>' ></div>
						<a class="js-businesshours-time" style="white-space:nowrap;" href="/admin/businesshours?item_id=<?php echo($post['item_id'])?>"><?php echo __('admin.common.button.edit') ?></a>
					</div>
				</div>
			</div>
			<div class="form-group">
				<label class="control-label col-md-2"><?php echo __('admin.itme.item.congestion_status_display') ?></label>
				<div class="col-md-2">
					<input type="checkbox" id="congestion_flg" name="congestion_flg" value="1" <?php if ($post['congestion_flg'] === "1") echo ('checked')?> class="make-switch" data-on-color="success" data-off-color="warning">
				</div>
			</div>
			<div class="form-group flex" id="congestion_time">
				<label class="control-label col-md-2"><?php echo __('admin.itme.item.waiting_time') ?></label>
				<label class="control-label mr10" style="width: 90px;"><?php echo __('admin.itme.item.vacant') ?></label>
				<div style="width: 100px;">
					<input name="congestion_time1" type="text" class="form-control" value="<?php if ($post != NULL) echo($post['congestion_time1'])?>">
				</div>
				<label class="control-label mr10" style="width: 80px;"><?php echo __('admin.itme.item.slightly_crowded') ?></label>
				<div style="width: 100px;">
					<input name="congestion_time2" type="text" class="form-control" value="<?php if ($post != NULL) echo($post['congestion_time2'])?>">
				</div>
				<label class="control-label mr10" style="width: 80px;"><?php echo __('admin.itme.item.crowded') ?></label>
				<div style="width: 100px;">
					<input name="congestion_time3" type="text" class="form-control" value="<?php if ($post != NULL) echo($post['congestion_time3'])?>">
				</div>
			</div>
			<div class="form-group" id="congestion_schedule_setting">
				<label class="control-label col-md-2"><?php echo __('admin.item.item.plan.setting') ?></label>
				<div class="col-md-7">
					<div class="readonly-input flex-x-between" style="height: auto; white-space: normal;">
						<div class="js-congestion-setting"  data-value='<?php echo( json_encode($post['item_congestion_schedule'], JSON_UNESCAPED_UNICODE|JSON_HEX_APOS))?>' ></div>
						<a class="js-congestion-time" href="/admin/congestionforecast?item_id=<?php echo($post['item_id'])?>"><?php echo __('admin.common.button.edit') ?></a>
					</div>
				</div>
			</div>
			<div class="form-group" id="congestion_flg_setting">
				<label class="control-label col-md-2"><?php echo __('admin.itme.item.congestion_url') ?></label>
				<div class="col-md-7">
					<p class="public-url-area">
						<a class="public-url-raw public-url-link" href="<?php echo($admin_url)?>admin/congestion?id=<?php echo($post['item_id'])?>" target="_blank"><?php echo $admin_url . "admin/congestion?id=" . $post['item_id'] ?></a>
						<span class="copy font-standard font-family-v1 font-color-v1 survey-copy-url" @click="congestionUri" data-clipboard-action="copy" data-clipboard-target=".public-url-raw"><?php echo __('admin.itme.item.copy') ?></span>
					</p>
				</div>
			</div>
			<?php }?>
		</div>
		<div class="actions-container" style="margin: 60px 0 0 140px;">
			<?php if ($permission > 0) {?>
				<button type="button" id="saveBaseButton" class="btn-larger btn-blue"><?php echo __('admin.common.button.save') ?></button>
				<?php if ($post['end_date'] == '2000-01-01' && false) {?>
					<button type="button" id="finishButton" class="btn-larger"><?php echo __('admin.common.button.publish') ?></button>	
				<?php }?>											
			<?php }?>
				<span class="btn-larger btn-white js-action-back"><?php echo __('admin.common.button.return_to_list') ?></span>
			<?php if ($permission > 0) {?>
				<div id="deleteButton" class="btn-larger btn-red-border">
					<span class="icon-delete"></span>
				</div>
			<?php }?>
		</div>
		
		<!-- 混雑状況の詳細設定-->
	</div>
</div>
<!-- END PAGE CONTENT-->