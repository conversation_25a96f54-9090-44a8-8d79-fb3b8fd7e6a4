<link rel="stylesheet" href="https://unpkg.com/leaflet/dist/leaflet.css" />
<script src="https://unpkg.com/leaflet/dist/leaflet.js"></script>
<style>
    #map {
        height: 600px;
        width: 100%;
    }
    #locationList {
        margin-top: 20px;
    }
    .location-item {
        padding: 5px;
        border: 1px solid #ccc;
        margin-bottom: 5px;
    }
</style>

<script>
    var locations = <?php echo $locations?>;
</script>

<!-- 検索フォーム -->
<div class="content-container light-gray flexbox-x-axis">
    <input type="text" id="searchInput" placeholder="地名を入力">
    <div class="col-md-1">
        <span class="btn-smaller btn-yellow" style="width: 120px;" id="searchBtn"><i class="fa fa-search mr10"></i>地名で検索</span>
    </div>
</div>
<div class="content-container light-gray flexbox-x-axis">
    <input type="text" id="textSearchInput" placeholder="施設名を入力">
    <div class="col-md-1">
        <span class="btn-smaller btn-yellow" style="width: 120px;" id="textSearchBtn"><i class="fa fa-search mr10"></i>施設名で検索</span>
    </div>
</div>
<div class="content-container light-gray flexbox-x-axis">
    <input type="text" id="botIdSearchInput" placeholder="bot_idを入力">
    <div class="col-md-1">
        <span class="btn-smaller btn-yellow" style="width: 120px;" id="botIdSearchBtn"><i class="fa fa-search mr10"></i>bot_idで検索</span>
    </div>
</div>

<!-- 地図表示 -->
<div id="map"></div>

<!-- 検索結果一覧 -->
<div id="locationList"></div>