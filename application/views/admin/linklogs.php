			<!-- BEGIN PAGE HEADER-->
			<!-- BEGIN PAGE HEAD -->
			<div class="page-head">
				<!-- BEGIN PAGE TITLE -->
				<div class="page-title">
					<h1>メールリンク管理<small></small></h1>
				</div>
				<!-- <PERSON>ND PAGE TITLE -->
			</div>
			<!-- END PAGE HEAD -->
			<!-- END PAGE HEADER-->
			<!-- BEGIN PAGE CONTENT-->
			<div class="row">
				<div class="col-md-12">
			        <!-- Page Content -->
			        <div id="page-wrapper">
			        <div class="portlet light">
							<div class="tabbable-line">
							<ul class="nav nav-tabs ">
								<li class="active">
									<a class="func-menu" href="/admin/linklogs">
									リンク一覧</a>
								</li>							
								<li class="">
									<a class="func-menu" href="/admin/linklog">
									リンク管理</a>
								</li>					
							</ul>
							</div>			        
						<div class="portlet box">
							<div class="portlet-body">						
							<div class="row">	
								<div class="form-group">
									<label class="control-label col-md-2">分類:</label>
									<div class="col-md-2">
										<?php echo Form::select('link_type_cd', $link_types, $post['link_type_cd'], array('id'=>'link_type_cd','class'=>'form-control'))?>
									</div>
									<label class="control-label col-md-2" style="display:none;">ステータス:</label>
									<div class="col-md-2" style="display:none;">
										<?php //echo Form::select('link_status_cd', $link_status, $post['link_status_cd'], array('id'=>'link_status_cd','class'=>'form-control'))?>
									</div>		
									<div class="col-md-4">
										<button type="submit" class="btn yellow mr10">
										<i class="fa fa-search mr10"></i>検索</button>
										<button type="button" id="addButton" class="btn blue mr10">
										<i class="fa fa-file mr10"></i>新規</button>
									</div>																		
								</div>
								<div class="form-group">
									<label class="control-label col-md-2">CSVファイル※</label>
									<div class="col-md-4">
										<div class="fileinput fileinput-new" data-provides="fileinput">
											<div class="input-group input-large">
												<div class="form-control uneditable-input span3" data-trigger="fileinput">
													<i class="fa fa-file fileinput-exists"></i>&nbsp; <span class="fileinput-filename">
													</span>
												</div>
												<span class="input-group-addon btn default btn-file">
												<span class="fileinput-new">
												Select file </span>
												<span class="fileinput-exists">
												Change </span>
												<input id="linkdata" type="file" name="linkdata">
												</span>
												<a href="#" class="input-group-addon btn red fileinput-exists" data-dismiss="fileinput">
												Remove </a>
											</div>
										</div><br/>
									</div>
									<div class="col-md-1">
										<a href="/docs/samples/link_type_90.csv">サンプル</a>
									</div>
									<div class="col-md-1">
										<button type="button" id="importButton" class="btn red" >一括インポート※</button>
									</div>
								</div>								
							</div>
							<table class="table table-striped table-bordered table-hover js-data-table">
							<thead>
							<tr>
								<th>
								リンクID
								</th>
								<th>
								メール
								</th>
								<th>
								パラメータ
								</th>
								<th>
								有効時間
								</th>
								<th>
								アクセス時間
								</th>																						
							</tr>
							</thead>

							<tbody>
							<?php
								foreach ($links as $link) {
							?>	
							<tr class="gradeX odd" role="row">
								<td>
									 <a href="/admin/linklog?id=<?php echo($link['link_id'])?>"><?php echo($link['link_id'])?></a> 
								</td>
								<td>
									 <?php echo($link_types[$link['link_type_cd']])?>
								</td>
								<td>
									<?php echo($link['param1'])?>
								</td>
								<td <?php if ($link['valid_date_time'] < date('Y-m-d H:i:s')) echo(' style="color:red;"')?>>
									 <?php echo(substr($link['valid_date_time'],2,14))?>
								</td>			
								<td>
									 <?php echo(substr($link['access_time'],2,14))?>
								</td>							
							</tr>
							<?php } ?>
							</tbody>
							</table>
							</div>
						</div>
					</div>
			        </div>
			        <!-- /#page-wrapper -->					
				</div>
			</div>
			<!-- END PAGE CONTENT-->
