			<!-- BEGIN PAGE HEADER-->
			<!-- BEGIN PAGE HEAD -->
<style type='text/css'>
  /* Style to hide Dates / Months */
  .ui-datepicker-calendar,.ui-datepicker-month { display: none; }​
</style>
<style type="text/css">
.btn.default:active, .btn.default.active {
  background-image: none;
  background-color: #45b6af;
  color: #fff;
}
</style>
			<div class="page-head">
				<!-- BEGIN PAGE TITLE -->
				<div class="page-title">
					<h1>利用状況レポート<small></small></h1>
				</div>
				<!-- END PAGE TITLE -->
			</div>
			<!-- END PAGE HEAD -->
			<!-- END PAGE HEADER-->
			<!-- BEGIN PAGE CONTENT-->
			<div class="row">
				<div class="col-md-12">
			        <!-- Page Content -->
			        <div id="page-wrapper">
			        <div class="portlet light">
						<?php echo $reportmenu ?>
						<div class="portlet box">
							<div class="portlet-body">
								<div class="form-body">		
									<div class="form-group">
										<label class="control-label col-md-1" style="padding-left:15px; padding-right:15px;"><?php echo __('admin.common.label.reception_period') ?></label>
										<div class="col-md-4">
											<input name="start_date" id="start_date" value="<?php echo($post['start_date'])?>" style="float:left;" class="form-control form-control-inline input-small date-picker" size="16" data-date-format="yyyy-mm-dd"  type="text"/>
											<input name="end_date" id="end_date" value="<?php echo($post['end_date'])?>" style="float:left; margin-left:10px;" class="form-control form-control-inline input-small date-picker" size="16" data-date-format="yyyy-mm-dd"  type="text"/>
										</div>
										<label class="control-label col-md-1" style="padding-left:15px; padding-right:15px;"><?php echo __('admin.common.label.sns') ?></label>
										<div class="col-md-2">
										<?php echo Form::select('sns_type_cd',[''=> __('admin.common.label.sns_all')] +  $_codes['08'], $post['sns_type_cd'], array('id'=>'sns_type_cd','class'=>'form-control')); ?>
										</div>										
									</div>
									<div class="form-group">
										<label class="control-label col-md-1"><?php echo __('admin.common.label.statistics_type') ?></label>
										<div class="col-md-4">
											<div class="btn-group" id="report_type" data-toggle="buttons">
											<?php foreach($report_types as $k=>$v) {
												if ($post['report_type'] == $k) {
													$active = ' active';
													$checked = 'checked';
												}
												else {
													$active = '';
													$checked = '';
												}
												echo('<label class="btn default'. $active . '"><input name="report_type" type="radio" ' . $checked . ' value="' . $k . '" class="toggle">' . $v . '</label>');
											}?>				
											</div>										
										</div>
										<?php echo $botcond ?>
										<div class="col-md-1">
											<button type="button" id="searchButton" class="btn yellow" disabled="disabled">
											<i class="fa fa-search mr10"></i><?php echo __('admin.common.button.search') ?></button>
										</div>										
										<div class="col-md-1" style="margin-left: 20px;">
											<button type="button" id="csvoutput" class="btn green">
												<?php echo __('admin.common.button.csv_export') ?>
											</button>
										</div>												
									</div>										
								</div>							
							<table class="table table-striped table-bordered table-hover js-data-table">
							<thead>
							<tr>
								<th><?php echo($report_types[$post['report_type']])?></th>
								<th><?php echo __('admin.common.label.reception') ?></th>
								<th><?php echo __('admin.common.label.response') ?></th>
								<th><?php echo __('admin.common.label.completion') ?></th>
								<th><?php echo __('admin.common.label.cancellation') ?></th>
								<th><?php echo __('admin.common.label.total') ?></th>
							</tr>
							</thead>

							<tbody>
							<?php
								foreach ($results as $k=>$v) {
							?>	
							<tr class="gradeX odd" role="row">
								<td class="sorting_1" title="<?php echo($k)?>">
								<?php echo($k);?></td>
								<td <?php if ($v['01'] > 0) echo(' style="background-color:#ffe4e1;"')?>><?php echo($v['01'])?></td>
								<td <?php if ($v['02'] > 0) echo(' style="background-color:#faebd7;"')?>><?php echo($v['02'])?></td>
								<td><?php echo($v['03'])?></td>
								<td><?php echo($v['04'])?></td>		
								<td><?php echo($v['01'] + $v['02'] + $v['03'] + $v['04'])?></td>																										
							</tr>
							<?php } ?>
							</tbody>
							</table>
							</div>
						</div>
					</div>
			        </div>
			        <!-- /#page-wrapper -->					
				</div>
			</div>
			<!-- END PAGE CONTENT-->
