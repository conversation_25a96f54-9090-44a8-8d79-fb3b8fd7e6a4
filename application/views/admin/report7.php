
			<!-- BEGIN PAGE HEADER-->
			<!-- BEGIN PAGE HEAD -->
			<div class="page-head">
				<!-- BEGIN PAGE TITLE -->
				<div class="page-title">
					<h1>利用状況レポート<small></small></h1>
				</div>
				<!-- END PAGE TITLE -->
			</div>
			<!-- END PAGE HEAD -->
			<!-- END PAGE HEADER-->
			<!-- BEGIN PAGE CONTENT-->
			<div class="row">
				<div class="col-md-12">
			        <!-- Page Content -->
			        <div id="page-wrapper">
			        <div class="portlet light">			        
					<?php echo $reportmenu ?>
						<div class="portlet box">
							<div class="portlet-body">
								<div class="form-body">		
									<div class="form-group">
										<label class="control-label col-md-1" style="padding-left:15px; padding-right:15px;"><?php echo __('admin.common.label.period') ?></label>
										<div class="col-md-4">
											<input name="start_date" id="start_date" value="<?php echo($start_date)?>" style="float:left;" class="form-control form-control-inline input-small date-picker" size="16" data-date-format="yyyy-mm-dd"  type="text"/>
											<input name="end_date" id="end_date" value="<?php echo($end_date)?>" style="float:left; margin-left:10px;" class="form-control form-control-inline input-small date-picker" size="16" data-date-format="yyyy-mm-dd"  type="text"/>
										</div>
									</div>
									<div class="form-group">
										<label class="control-label col-md-1" style="padding-left:15px; padding-right:15px;"><?php echo __('admin.common.label.conditions') ?></label>
										<div class="col-md-2">
											<?php echo Form::select('lang_cd', $lang, $lang_cd, array('id'=>'lang_cd','class'=>'form-control'))?>
										</div>
										<?php echo $botcond ?>	
										<div class="col-md-1">
											<button type="button" id="searchButton" class="btn yellow" disabled="disabled">
											<i class="fa fa-search mr10"></i><?php echo __('admin.common.button.search') ?></button>
										</div>											
									</div>
								</div>													
							<table class="table table-striped table-bordered table-hover js-data-table">
							<thead>
							<tr>
								<!-- 対応時間 -->
								<th><?php echo __('admin.common.label.request_time') ?></th>
								<!-- 担当者 -->
								<th><?php echo __('admin.common.label.person_in_charge') ?></th>
								<!-- 施設名 -->
								<?php 
									if($bot_id == null ) { 
										echo ("<th>" . __('admin.common.label.facility') ."</th>");
								 	} 
								?>
								<!-- 名前 -->
								<th><?php echo __('admin.common.label.name.human') ?></th>
								<!-- チャンネル -->
								<th><?php echo __('admin.common.label.sns') ?></th>
								<!-- ユーザーID -->
								<th><?php echo __('admin.common.label.user_id') ?></th>
							</tr>
							</thead>

							<tbody>
							<?php
								foreach ($msgs as $msg) {
							?>	
							<tr class="gradeX odd" role="row">
								<!-- 対応時間 -->		
								<td class="sorting_1">
								<?php echo($msg['log_time'])?> 	
								</td>
								<!-- 担当者 -->		
								<td>
								<?php echo($msg['user_name'])?> 	
								</td>
								<!-- 施設名 -->
								<?php 
									if($bot_id == null ) { 
										echo("<td>" . $msg['bot_name']. "</td>");
									} 
								?>	
								<!-- 名前 -->
								<td>
									 <?php 
									 if ($msg['sns_type_cd']== 'wb') {
									 	echo __('admin.common.label.web_user');
									 }
									 else {
									 	echo($msg['last_name'] . ' ' . $msg['first_name']);
									 }
									 ?>
								</td>
								<!-- チャンネル -->
								<td>
									<img src="/assets/common/images/chat_<?php echo $msg['lang_cd']?>.png" style="margin:5px;width:24px;"/>
									<img src="/assets/common/images/icon_<?php echo $msg['sns_type_cd']?>.png" style="margin:5px;width:24px;"/>							
								</td>	
								<!-- ユーザーID -->
								<td>
									<?php echo($msg['member_id'])?>			
									<a class="pop_adminchat" member_id=<?php echo $msg['member_id'] ?>  <?php if ($bot_id!=null) echo(' bot_id="' . $bot_id . '"')?>><span class="badge badge-primary" style="margin: 5px;" ><?php echo __('admin.common.label.historical_view') ?></span> </a>						
								</td>																										
							</tr>
							<?php } ?>
							</tbody>
							</table>
							</div>
						</div>
					</div>
			        </div>
			        <!-- /#page-wrapper -->					
				</div>
			</div>
			<!-- END PAGE CONTENT-->

