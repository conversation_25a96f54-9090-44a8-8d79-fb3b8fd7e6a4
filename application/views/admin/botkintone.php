<?php echo $menu ?>
<div class="content-container white border">
	<input type="hidden" name="message" id="message" value="<?php echo ($message) ?>" />
	<input type="hidden" name="act" id="act" />

	<div class="section-container">
		<div class="form-body">
			<div class="form-group">
				<label class="control-label col-md-2"></label>
				<img class="system_link_logo" src="https://admin.talkappi.com/docs/manual/talkappi_kintone.png"></img>
			</div>
			<div class="form-group">
				<label class="control-label col-md-2" style="font-weight: 600;">【概要説明】</label>
				<div class="col-md-8">
					<hr>
				</div>
			</div>
			<div class="form-group">
				<label class="control-label col-md-3"></label>
				<label class="col-md-8">Webデータベース型の業務アプリ構築クラウドサービスkintoneと連携することによって、<br>ユーザーがtalkappiでリクエストをするとのkintone上のAPPに通知します。</label>
			</div>
			<div class="form-group">
				<label class="control-label col-md-3"></label>
				<div class="col-md-9" style="display:flex;">
					<a href="https://admin.talkappi.com/docs/manual/talkappi_kintone_manual.pdf" target="_blank"><div type="button" class="btn-smaller btn-blue">システム連携手順書</div></a>
				</div>
			</div>		
			<div class="form-group">
				<label class="control-label col-md-2" style="font-weight: 600;">【連携の基本設定】</label>
				<div class="col-md-8">
					<hr>
				</div>
			</div>
			<div class="form-group">
				<label class="control-label col-md-3">Domain</label>
				<div class="col-md-8">
					<input name="domain" id="domain" type="text" class="form-control" value="<?php echo $domain ?>" placeholder="xxxx.you-domain.com">
				</div>
			</div>
			<div class="form-group">
				<label class="control-label col-md-3">API Token</label>
				<div class="col-md-8">
					<input name="api_token" id="api_token" type="text" class="form-control" value="<?php echo $api_token ?>" placeholder="XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX">
				</div>
			</div>
			<div class="form-group">
				<label class="control-label col-md-3">APP ID</label>
				<div class="col-md-8">
					<input name="app_id" id="app_id" type="text" class="form-control" value="<?php echo $app_id ?>" placeholder="xxx">
				</div>
			</div>
			<div class="form-group">
				<label class="control-label col-md-2" style="font-weight: 600;"></label>
				<div class="col-md-8">
					<hr>
				</div>
			</div>
			<div class="form-actions">
				<div class="row">
					<div class="col-md-offset-3 col-md-10" style="display:flex;">
						<button type="button" id="saveButton" class="btn-larger btn-blue"><i class="fa fa-save mr10"></i>保存</button>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<!-- END PAGE CONTENT-->