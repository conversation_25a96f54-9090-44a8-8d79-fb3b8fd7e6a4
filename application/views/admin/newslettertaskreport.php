<!-- <link rel="stylesheet" type="text/css" href="/assets/admin/css/newsletter.css"/> -->
<?php echo HTML::style('/assets/admin/css/newsletter.css'); ?>

<style>
    .dashboard-row-container {
        display: flex;
        padding: 10px 0px;
        gap: 12px;
    }
    .row_item.detail {
    position: relative;
    }
    .tag_inline {
        border-radius: 2px;
        background: #EBEDF2;
        padding: 1px 7px;
    }
    .txt_value.tags {
        height: unset;
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
    }
</style>

<!--  rectangle_body -->
<div class="content-container white row_sparse">
    <div class="report_header_container">
        <div class="row_item">
            <div class="txt_label_small"><?php echo __('admin.send.task.label.name') ?></div>
            <div class="txt_value title">
                <?php 
                    $task_name = $mail_task->mail_task_name;
                    if ($task_name == NULL || $task_name == '') {
                        $task_name = '[件名]'.$mail_task->title;
                    }
                    echo $task_name; 
                ?>
            </div>
        </div>
        <div id="email_object" class="row_item multi">
            <div class="txt_label_small"><?php echo __('admin.send.task.label.object') ?></div>
            <div class="column_item">
                <div class="txt_value"><?php echo str_replace( "{target_count}", ' <b class="selected_count">'.$task_member_count.'</b> ', __('admin.send.task.count.context') ) ?></div>
                <div class="txt_value">対象施設：
                    <?php 
                        $bot_member_count_text = '';
                        if ( is_array($task_bots) && count($task_bots) > 0 ) {
                            
                            foreach ( $task_bots as $bot_id=>$bot_info ) {
                                $bot_text = $bot_info['name'].'('. $bot_info['count'].')';
                                if ( $bot_member_count_text != '' ) {
                                    $bot_text = '; '.$bot_text;
                                }
                                $bot_member_count_text .= $bot_text;
                            }
                        }
                        echo $bot_member_count_text;
                    ?>
                </div>
                <div class="txt_value tags">タグ：
                    <?php 
                        $tag_member_count_text = '';
                        if ( is_array($tag_member_counts) && count($tag_member_counts) > 0 ) {
                            foreach ( $tag_member_counts as $tag_no=>$tag_info ) {
                                $tag_name = $tag_info['name'];
                                $tags_html = '<span class="tag_inline">'.$tag_name.'('. $tag_info['count'].')</span>';
                                $tag_member_count_text .= $tags_html;
                            }
                        }
                        echo $tag_member_count_text;
                    ?>
                </div>
            </div>
        </div>
        <div class="row_item">
            <div class="txt_label_small"><?php echo __('admin.send.label.date')?></div>
            <div class="txt_value"><?php echo $mail_task->upd_time ?></div>
        </div>
    </div>


    <div class="button_container">
        <!-- メールを見る＆ -->
        <?php 
            $button_html = '<div id = "show_mail" class="btn_w_ico pointer pale_grey" style="width: fit-content; margin:20px 0; ">
            <span class="icon-email-blue"></span><div class="txt_btn_w_ico">'.__('admin.send.report.button.read_email').'</div></div>';
            $mam_html = HTML::anchor('/'.$_path.'/newsletternewtask?id='.$mail_task->mail_task_id, $button_html);
            echo $mam_html;
        ?>  
        <!-- TODO: 未実装の機能は非表示にする: 連絡先を導出 -->
        <!-- <div class="btn_w_ico pointer pale_grey" style="width: 112px;">
            <span class="icon-export"></span>
            <div class="txt_btn_w_ico"><?php echo __('admin.send.addr.button.export') ?></div>
        </div> -->
    </div>


</div>


<div class="dashboard cards dashboard-row-container">
    <?php 
        $dashboard_html = '';
        foreach($dashboard_indicators as $event=>$labels) {
            $dashboard_html .= '<div class="dashboard card"><div id="'.$event.'" class="dashboard card-container dashboard_frame">';//dashboard_frame
            $dashboard_html .=      '<div class="row_dashboard_label"><span class="txt_indic">'.$task_event_counts[$event].'</span><span class="txt_indic pct">'.($task_event_counts[$event.'Percentage'] == 0 ? '-' : number_format($task_event_counts[$event.'Percentage'], 1, '.', '')).'%</span></div>';

            $label_html = '<span class="color_label_status txt_tag_blue">詳細へ</span>';
            if ($task_event_counts[$event] > 0) {
                $href = '/'.$_path.'/newslettertaskreportdetail?id='.$mail_task->mail_task_id.'&filter='.$event;
                $label_html = HTML::anchor($href, $label_html, ['class' => 'label']);
            } else {
                $label_html = '<span class="color_label_status txt_tag_blue disabled">詳細へ</span>';
            }

            $dashboard_html .=      '<div class="row_dashboard_label"><span class="txt_label_indic">'.$labels[0].'</span>'.$label_html.'</div>';
            if ( is_array($labels) && count($labels) > 1 ) {
                $dashboard_html .=      '<span class="txt_label_indic top-shadow">'.$labels[1].'</span>';
            }
            $dashboard_html .= '</div></div>';
        }
        echo $dashboard_html;
    ?>
</div> 

<div class="content-container white column_title_content">
    <div class="txt_title_small"><?php echo __('admin.send.report.title.key_indicators')?></div>
    <div class="dashboard-num-container wrap">

        <?php 
            $indicators_html = '';
            foreach($key_indicators as $indicator) {
                $indicators_html .= '<div class="row_item">'.$indicator['name'].'<span class="line_dotted"></span>';
                $value = $indicator['value'];
                if ( is_array($value) ) {
                    //  count and percentage
                    $indicators_html .= '<span class="txt_key"><b>'.$value[0].'  </b> '.number_format($value[1], 1, '.', '').'%</span>';
                } else if ( isset($indicator['style']) && $indicator['style'] == '%' ){
                    // percentage
                    $indicators_html .= '<span class="txt_key">'.number_format($value, 1, '.', '').'%</span>';
                } else if ( isset($indicator['anchor']) && $indicator['anchor'] != '' ){
                    //	<a> html tag: anchor/hyperlink
                    // anchor: open/click
                    $href = '/'.$_path.'/newslettertaskreportdetail?id='.$mail_task->mail_task_id.'&filter='.$indicator['anchor'];
                    $indicators_html .= HTML::anchor($href, $value, ['class' => 'txt_key']);
                } else {
                    $indicators_html .= $value;
                }
                $indicators_html .= '</div>';
            }
            echo $indicators_html;
        ?>
        
    </div> 
</div> 

<div style="padding: 10px 0px;"></div>

<div class="content-container white column_title_content">
    <?php 
            $sub_dashboard_html = '';
            foreach($sub_indicator as $event=>$sub_types) {
                // echo $event.json_encode($sub_types);
                $href = '/'.$_path.'/newslettertaskreportdetail?id='.$mail_task->mail_task_id.'&filter='.$event;
                $label_html = '<span class="color_label_status txt_tag_blue">詳細へ</span>';
                $sub_counts = $sub_indicators_array[$event];

                if ($task_event_counts[$event] > 0) {
                    $indicators_html = HTML::anchor($href, $label_html, ['class' => 'label']);
                } else {               
                    $indicators_html = '<span class="color_label_status txt_tag_blue disabled">詳細へ</span>';
                }

                $sub_dashboard_html .= '<div class="row_item">'.$dashboard_indicators[$event][0].'  '.str_replace( "{target_count}", ' <b class="selected_count">'
                    .$task_event_counts[$event].'</b> ', __('admin.send.report.count.context') ).$indicators_html.'</div>' ;

                $sub_dashboard_html .=  '<div class="dashboard-num-container wrap">';
                foreach($sub_types as $sub_type => $value) {
                    $sub_dashboard_html .= '<div class="row_item detail">'.$value['label'].'<span class="icon-detail" style="margin: 0 0 0 0.5rem;"  title="'.$value['detail'].'"></span>'.'<span class="line_dotted"></span>'.$sub_counts[$sub_type].'</div>';
                }
                $sub_dashboard_html .= '</div>';
            }
            echo $sub_dashboard_html;
    ?>
    
</div> 
