			<!-- BEGIN PAGE HEADER-->
			<!-- BEGIN PAGE HEAD -->
			<div class="page-head">
				<!-- BEGIN PAGE TITLE -->
				<div class="page-title">
					<h1>FAQページ利用状況レポート<small></small></h1>
				</div>
				<!-- END PAGE TITLE -->
			</div>
			<!-- END PAGE HEAD -->
			<!-- END PAGE HEADER-->
			<!-- BEGIN PAGE CONTENT-->
			<div class="row">
				<div class="col-md-12">
			        <!-- Page Content -->
			        <div id="page-wrapper">
			        <div class="portlet light">
					<?php echo $reportmenu ?> 
						<div class="portlet box">
							<div class="portlet-body">
								<div class="form-body">		
									<div class="form-group">
										<label class="control-label col-md-1" style="padding-left:15px; padding-right:15px;">期間</label>
										<div class="col-md-3">
											<input name="start_date" id="start_date" value="<?php echo($start_date)?>" style="float:left;" class="form-control form-control-inline input-small date-picker" size="16" data-date-format="yyyy-mm-dd"  type="text"/>
											<input name="end_date" id="end_date" value="<?php echo($end_date)?>" style="float:left; margin-left:10px;" class="form-control form-control-inline input-small date-picker" size="16" data-date-format="yyyy-mm-dd"  type="text"/>
										</div>									
										<div class="col-md-2">
											<?php echo Form::select('lang_cd', $lang, $lang_cd, array('id'=>'lang_cd','class'=>'form-control'))?>
										</div>
										<?php echo $botcond ?>
										<div id="scene_use_flg" style="display:none;">use_faq_flg</div>
										<div class="col-md-1">
											<button type="button" id="searchButton" class="btn yellow" disabled="disabled">
											<i class="fa fa-search mr10"></i>検索</button>
										</div>							
										<div class="col-md-1">
											<button type="button" data-tableId="faqsurveytable" data-title="アンケート履歴" class="btn green exportCsv">
											CSV出力</button>
										</div>																			
									</div>
								</div>													
							<table class="table table-striped table-bordered table-hover js-data-table" id="faqsurveytable">
							<thead>
							<tr>
								<th>アンケート日時</th>
								<?php if (count($bot_grp_dict) > 0) {?><th>施設</th> <?php }?>
								<th>質問</th>
								<th>言語</th>								
								<th>結果</th>
								<th style="display:none;">結果値</th>
								<th>内容</th>																					
							</tr>
							</thead>

							<tbody>
							<?php
								foreach ($logs as $log) {
							?>	
							<tr class="gradeX odd" role="row">
								<td class="sorting_1">
									 <?php echo(substr($log['survey_time'], 0, 19)); ?>
								</td>
								<?php if (count($bot_grp_dict) > 0) {?><td class="sorting_1"><?php echo($bot_grp_dict[$log['bot_id']])?></td> <?php }?>
								
								<td class="sorting_1">
									 <?php echo($log['question']); ?>
								</td>
								<td class="sorting_1">
									 <?php echo($_codes['02'][$log['lang_cd']]); ?>
								</td>
								<td class="sorting_1">									
									 <?php
									 echo('<span style="display:none;">' . $log['answer'] . '</span>');
									 if ($log['answer'] == '1') {
									 	echo('<i class="fas fa-thumbs-up"></i>');
									 }
									 else if ($log['answer'] == '2') {
									 	echo('<i class="far fa-thumbs-down"></i>');
									 }
									 ?>
								</td>
								<td style="display:none;"><?php
									 if ($log['answer'] == '1') {
									 	echo('十分');
									 }
									 else if ($log['answer'] == '2') {
									 	echo('不十分');
									 }
									 ?></td>
								<td class="sorting_1"><?php echo($log['reason']); ?></td>																																														
							</tr>
							<?php } ?>
							</tbody>
							</table>
							</div>
						</div>
					</div>
			        </div>
			        <!-- /#page-wrapper -->					
				</div>
			</div>
			<!-- END PAGE CONTENT-->
