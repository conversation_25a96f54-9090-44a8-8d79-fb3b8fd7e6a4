<style text="css">
    .nav-bar {
        background-color: #F6F7F9;
        height: 42px;
        margin:-15px -40px 12px -15px;
        padding:12px 0 12px 24px;
        display:flex;
    }

    .col-md-6 {
        display:block;
    }

    .dataTables_length {
        display:none;
    }

    .table thead tr th {
        font-size: 12px;
        font-weight: normal;
        color: #000;
    }
</style>

<input type="hidden" name="task_status_cd" id="task_status_cd" value="">
<input type="hidden" name="act" id="act" value="">
<input type="hidden" name="task_id" id="task_id" value="">

<div class="nav-bar" style="display:flex;">
    <div style="padding-right:48px;"><?php echo __('admin.push.status')?></div>
    <nav class="button-tab" style="display:inline-block; margin-top:-20px;">
        <ul>
            <?php foreach($task_status as $key => $value): ?>
            <li class="js-button-tab <?php if($post['task_status_cd'] == $key) echo 'active'; ?>" id="<?php echo $key ?>"><?php echo $value ?></a></li>	
            <?php endforeach; ?>
        </ul>	
    </nav>            
</div>   

<div id="page-wrapper">
    <div class="flex-x-between" style="margin-bottom:10px;">
        <h5><?php echo __('admin.push.lists')?></h5>
        <button type="button" id="newButton" class="btn blue surveys-btn" style="background-color: #245BD6;">
            <img src="./../assets/admin/css/img/icon-add-white.svg" width="12" height="12"><?php echo __('admin.push.new'); ?>
        </button>
    </div>
    <div class="portlet light">
		<div class="portlet box">  
            <div class="portlet-body" style="position:relative;">
                <table class="table table-striped table-bordered table-hover" id="sample_3">
                    <div class="flexbox-x-axis" style="position: absolute; top: 22px;">
                        <div style="margin-right:12px;">
                        <?php  echo Form::select('task_register', $task_register, $post['task_register'], array('id'=>'task_register','class'=>'form-control', 'style' => "width:132px;"))?>
                        </div>
                        <div>
                        <?php  echo Form::select('task_type_cd', $task_type_cd, $post['task_type_cd'], array('id'=>'task_type_cd','class'=>'form-control', 'style' => "width:132px;"))?>
                        </div>
                    </div>
                    <thead>
                        <tr>
                            <th style="min-width:160px;"><?php echo __('admin.push.date')?></th>
                            <th><?php echo __('admin.push.name')?></th>
                            <th><?php echo __('admin.push.type')?></th>
                            <th><?php echo __('admin.push.status')?></th>
                            <th><?php echo __('admin.push.content')?></th>
                            <th><?php echo __('admin.push.target')?></th>
                            <th><?php echo __('admin.push.created.by')?></th>
                            <th><?php echo __('admin.common.label.operation')?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        foreach ($tasks as $task) {
                        ?>
                            <tr class="gradeX odd" role="row">
                                <!-- 配信日時 -->
                                <td>
                                    <?php if(strpos($task['scheduled_time'], '0000-00-00') !== false) {
                                        $task['scheduled_time'] = '-';
                                    }
                                    ?>
                                    <?php echo __('admin.push.created').'：' .$task['upd_time']. '<br>' ?>
                                    <?php echo __('admin.push.plan').'：' .$task['scheduled_time']. '<br>' ?>
                                    <?php echo __('admin.push.execute').'：' .$task['process_time']. '<br>' ?>
                                    <?php echo __('admin.push.done').'：' .$task['finish_time'] ?>
                                </td>
                                <!-- タスク名 -->
								<td>
									<a class="link-animate" href="/admin/pushmsgtaskdetail?id=<?php echo($task['task_id'])?>"><?php echo($task['task_name'])?></a> 
								</td>
                                <!-- 配信タイプ -->
                                <td>
                                    <?php if($task['task_register'] == '01') echo ($_codes['282'][$task['task_register']]) ?>
                                    <?php if($task['task_register'] == '02') echo ($_codes['282'][$task['task_register']]) ?>
                                    <?php 
                                    $task_data = json_decode($task['task_data'], true); 
                                    if (isset($task_data['test']) && $task_data['test'] == '1') {
                                        echo '<br>('. __('admin.push.test'). ')';
                                    }
                                    ?>
                                </td>
                                <!-- ステータス -->
								<td>
                                    <?php  
                                    if ($task['task_status_cd'] == "01") {
                                    echo('<a class="link-animate" href="/admin/pushmsgtaskdetail?id='. $task['task_id'].'" class="btn light-blue">' . $_codes['27'][$task['task_status_cd']] . '</a>');
                                    }
                                    else if ($task['task_status_cd'] == "02") {
                                    echo('<a class="link-animate" href="/admin/pushmsgtaskdetail?id='. $task['task_id'].'" class="btn light-yellow">' . $_codes['27'][$task['task_status_cd']] . '</a>');
                                    }
                                    else if ($task['task_status_cd'] == "03") {
                                    echo('<a class="link-animate" href="/admin/pushmsgtaskresultnew?id='. $task['task_id'].'" class="btn light-green">' . $_codes['27'][$task['task_status_cd']] . '</a>');
                                    }
                                    else if ($task['task_status_cd'] == "04") {
                                    echo('<a class="link-animate" href="/admin/pushmsgtaskresultnew?id='. $task['task_id'].'" class="btn light-orange">' . $_codes['27'][$task['task_status_cd']] . '</a>');
                                    }
                                    else if ($task['task_status_cd'] == "05") {
                                    echo('<a class="link-animate" href="/admin/pushmsgtaskresultnew?id='. $task['task_id'].'" class="btn light-red">' . $_codes['27'][$task['task_status_cd']] . '</a>');
                                    }
                                    else if ($task['task_status_cd'] == "00") {
                                    echo('<a class="link-animate" href="/admin/pushmsgtaskdetail?id='. $task['task_id'].'" class="btn light-gray"">' . $_codes['27'][$task['task_status_cd']] . '</a>');
                                    }
									?>
								</td>
                                <!-- 配信コンテンツ -->
                                <td>
                                    <?php if (isset($task_msg_cd[$task['task_id']])): ?>
                                    <?php foreach(($task_msg_cd[$task['task_id']]) as $content_cd_name):?>
                                    <?php if (isset($bot_msgs[$content_cd_name])): ?>
                                    <?php echo $bot_msgs[$content_cd_name]. '<br>'; ?>
                                    <?php endif; ?>
                                    <?php endforeach; ?>
                                    <?php endif; ?>
                                </td>
                                <!-- ターゲット -->
                                <td>
                                    <?php if ($task['task_type_cd'] !== '') :?>
                                    <?php echo ($_codes['281'][$task['task_type_cd']]) ?>
                                    <?php endif ?>
                                </td>
                                <!-- 作成者 -->
                                <td>
                                <?php echo($task['name'])?>
                                </td>
                                <!-- 操作 -->
                                <td>
                                    <?php if ($task['finish_time'] == "") { ?>
                                        <a class="link-animate" href="/admin/<?php echo ($task['task_type_cd'] == '01') ?'pushmsgtask': 'pushmsgmember'; ?>new?id=<?php echo $task['task_id'] ?>" style='display: block;'>
                                        <div class="btn round image edit js-memo"><?php echo __('admin.common.button.edit')?></div>
                                        </a>
                                    <?php } else { ?>
                                        <div data-task_id="<?php echo $task['task_id'] ?>" class="btn round js-repush" style='display: block;'>
                                        <img src="/assets/admin/css/img/icon-send_small.svg" style="margin-right:3px;"><?php echo __('admin.push.resend')?>
                                        </div>
                                    <?php } ?>
                                    <div class="btn round image delete js-delete-icon" data-task_id=<?php echo $task['task_id'] ?> style='margin-top:10px;'><?php echo __('admin.common.button.delete')?></div>
                                </td>
                            </tr>
                        <?php } ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>