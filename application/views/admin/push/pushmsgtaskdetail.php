<style text="css">
.readonly-input {
    white-space: normal;
}
p.readonly-input-title {
    min-width:110px;
    margin: 0px 4px 0 4px;
}
.flexbox-x-axis {
    padding-bottom:8px;
    align-items: flex-start;
}
.form-horizontal .control-label {
    text-align: left;
}
.top-title {
    font-family: Hiragino Sans;
    font-size: 14px;
    padding-bottom:28px;
    padding-left:6px;
}

</style>

<input type="hidden" name="act" id="act" value="">

<div id="page-wrapper">
    <div class="portlet light">
        <div class="portlet box">
            <div class="portlet-body">
                <div class="row" style="display: flex;">
                    <div style="flex-basis: 65%;">				
                        <div class="form-body">
                            <div class="top-title"><?php echo __('index.amount.detail')?></div>
                            <div class="form-group">
                                <label class="control-label col-md-3"><?php echo __('admin.push.name')?></label>
                                <div class="col-md-8">
                                    <div class="input-icon right">
                                        <input name="task_name" id="task_name" type="text" class="form-control" value="<?php if ($post != NULL) echo($post['task_name'])?>">
                                    </div>
                                </div>
                            </div>
                            <!-- <div class="form-group">
                                <label class="control-label col-md-3">フィルター設定</label>
                                <div class="col-md-8">
                                    <div class="readonly-input flex-x-between">
                                        <span>talkappiフィルター</span>
                                    </div>
                                </div>           
                            </div> -->
                            <div class="form-group">
                                <label class="control-label col-md-3"><?php echo __('admin.push.target.range')?></label>
                                <div class="col-md-8">
                                    <!-- 全体配信 -->
                                    <?php if ($post['task_type_cd'] == '01') : ?>
                                    <div class="readonly-input" style="height:auto;">	
                                        <div class="flexbox-x-axis">
                                            <p class="readonly-input-title"><?php echo __('admin.push.target')?></p>
                                            <?php echo ($_codes['281'][$post['task_type_cd']]) ?>
                                        </div>
                                        <div class="flexbox-x-axis">
                                            <p class="readonly-input-title"><?php echo __('admin.common.label.sns')?></p>
                                            <?php 
                                            if (isset($task_data['sns_type_cd']) && $task_data['sns_type_cd'] !== '') {
                                                $channels = explode(',', $task_data['sns_type_cd']);
                                                $count = count($channels);
                                                $i = 1;
                                                foreach($channels as $sns) {
                                                    ++$i;
                                                    if ($i > $count) {
                                                        echo $_codes['08'][$sns]; 
                                                    } else {
                                                        echo $_codes['08'][$sns]. ', '; 
                                                    }
                                                }
                                            }
                                            ?>
                                        </div>
                                        <div class="flexbox-x-axis">
                                            <p class="readonly-input-title"><?php echo __('admin.common.label.lang')?></p>
                                            <?php 
                                            if (isset($task_data['lang_cd']) && $task_data['lang_cd'] !== '') {
                                                $languages = explode(',', $task_data['lang_cd']);
                                                $count = count($languages);
                                                $i = 1;
                                                foreach($languages as $lang) {
                                                    ++$i;
                                                    if ($i > $count) {
                                                        echo $_codes['02'][$lang]; 
                                                    } else {
                                                        echo $_codes['02'][$lang]. ', '; 
                                                    }
                                                }
                                            }
                                            ?>
                                        </div>
                                        <div class="flexbox-x-axis">
                                            <p class="readonly-input-title"><?php echo __('admin.push.last.talk')?></p>
                                            <?php if ( (!array_key_exists('last_talk_time_from', $task_data)) || ($task_data['last_talk_time_from'] == NULL) && ($task_data['last_talk_time_to'] == NULL)): ?>
                                                <?php echo __('index.bothsmoking'); ?>
                                            <?php else: ?>
                                                <?php echo $task_data['last_talk_time_from']. '〜 ' .$task_data['last_talk_time_to'] ; ?>
                                            <?php endif; ?>
                                        </div>
                                        <div class="flexbox-x-axis">
                                            <p class="readonly-input-title"><?php echo __('admin.push.register')?></p>
                                            <?php if ( (!array_key_exists('regist_date_from', $task_data)) || ($task_data['regist_date_from'] == NULL) && ($task_data['regist_date_to'] == NULL)): ?>
                                                <?php echo __('index.bothsmoking'); ?>
                                            <?php else: ?>
                                                <?php echo $task_data['regist_date_from']. '〜 ' .$task_data['regist_date_to'] ; ?>
                                            <?php endif; ?>
                                        </div>
                                        <div class="flexbox-x-axis">
                                            <p class="readonly-input-title"><?php echo __('admin.push.facility.cd')?></p>
                                            <?php echo $scene_label; ?>
                                        </div>
                                        <div class="flexbox-x-axis user-props-container">
                                            <p class="readonly-input-title"><?php echo __('admin.push.user.attr')?></p>
                                            <?php if (!isset($task_data['user_attr_cd']) || $task_data['user_attr_cd'] === ''): ?>
                                                <?php echo __('admin.push.user.attr.all'); ?>
                                            <?php endif ?>
                                        </div>
                                        <?php if (count($pushFacilities) > 0): ?>
                                            <div class="flexbox-x-axis">
                                                <p class="readonly-input-title"><?php echo __('admin.push.facility')?></p>
                                                <?php 
                                                    $count = count($pushFacilities);
                                                    $i = 1;
                                                    foreach ($pushFacilities as $k=>$v) {
                                                        ++$i;
                                                        if ($i > $count) {
                                                            echo $v;
                                                        } else {
                                                            echo $v . ', ';
                                                        }
                                                    } 
                                                ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <!-- 個別配信 -->
                                    <?php else: ?>
                                    <div class="readonly-input" style="height:66px;">	
                                        <div class="flexbox-x-axis">
                                            <p class="readonly-input-title"><?php echo __('admin.push.target')?></p>
                                            <div><?php if(array_key_exists($post['task_type_cd'],$_codes['281'])) echo  ($_codes['281'][$post['task_type_cd']]) ?></div>
                                        </div>
                                        <div class="flexbox-x-axis">
                                            <p class="readonly-input-title"><?php echo __('admin.push.send.member')?></p>
                                            <div class="member-list word-break">
                                            <?php
                                            $count = count($selected_members);
                                            $i = 1;
                                            foreach($selected_members as $selected_member) {
                                                ++$i;
                                                if ($i > $count) {
                                                    echo $selected_member[0]['last_name'] . $selected_member[0]['first_name'];
                                                } else {
                                                    echo $selected_member[0]['last_name'] . $selected_member[0]['first_name'] . ', ';
                                                }
                                            }
                                            ?>
                                            </div>
                                        </div>
                                    </div>
                                    <?php endif; ?>
                                </div>           
                            </div>
                            <div class="form-group">
                                <label class="control-label col-md-3"><?php echo __('admin.push.content')?></label>
                                <div class="col-md-8">
                                    <div class="readonly-input flex-x-between word-break">
                                        <span>
                                        <?php
                                        $count = count($bot_msgs_temp);
                                        $i = 1;
                                        foreach($bot_msgs_temp as $cd => $name) {
                                            ++$i;
                                            if ($i > $count) {
                                                echo "<span class='js-selected-cd' id='$cd'>$name</span>";
                                            } else {
                                                echo "<span class='js-selected-cd' id='$cd'>$name; </span>";
                                            }
                                        }
                                        ?>
                                        </span>
                                    </div>
                                </div>           
                            </div>
                            <div class="form-group">
                                <label class="control-label col-md-3"><?php echo __('admin.push.mode')?></label>
                                <div class="col-md-8">
                                    <div class="readonly-input">
                                        <span>
                                        <?php if (isset($task_data['test'])) {
                                            if ($task_data['test'] == '1') {
                                                echo __('admin.push.test');
                                            } else {
                                                echo __('admin.push.real');
                                            }
                                        } ?>
                                        </span>
                                    </div>
                                </div>           
                            </div>		
                            <div class="form-group">
                                <label class="control-label col-md-3"><?php echo __('admin.push.type_and_date')?></label>
                                <div class="col-md-8">
                                    <div class="readonly-input">
                                        <span><?php if(array_key_exists($post['task_register'],$_codes['282'] )) echo ($_codes['282'][$post['task_register']]) ?></span>
                                        <?php if ($post['task_register'] == '02' || $post['task_register'] == ''): ?>
                                        <span><?php echo '(' .$post['scheduled_date']. ' ' .$post['scheduled_time']. ')' ?></span>   
                                        <?php endif; ?>
                                    </div>
                                </div>           
                            </div>		
                            <div class="form-group">
                                <label class="control-label col-md-3"><?php echo __('admin.push.state')?></label>
                                <div class="col-md-8">
                                    <div class="readonly-input flex-x-between">
                                        <span><?php echo ($_codes['27'][$post['task_status_cd']]) ?></span>
                                        <?php if ($post['finish_date'] != '') : ?>
                                        <a class="" href="pushmsgtaskresultnew?id=<?php echo $task_id ?>"><?php echo __('admin.push.result')?></a>
                                        <?php endif; ?>
                                    </div>
                                </div>           
                            </div>																					
                        </div>																					
                        <div class="form-actions">
                            <div class="row">
                                <div class="col-md-offset-3 col-md-9">
                                    <div class="actions-container">
                                        <button class="btn-larger btn-blue js-action-save" id="saveBaseButton"><?php echo __('admin.common.button.save')?></button>
                                        <a class="btn-larger btn-white js-repush"><?php echo __('admin.push.resend')?></a>
                                        <a class="btn-larger btn-white js-action-back"><?php echo __('admin.itme.itemmeu.back')?></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        </div>
                    <div class="talkappi-preview js-pushmsg-preview" data-type="message" style="margin:0 0 0 20px; flex-basis: 30%; max-width:320px;">
                    </div>
                </div>       
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
    const userPropsArray = '<?php if(isset($task_data['user_attr_cd'])) echo $task_data['user_attr_cd']; ?>'.split(',');
    const _msgsData = <?php echo json_encode($bot_msgs_data, JSON_UNESCAPED_UNICODE) ?>;
</script>