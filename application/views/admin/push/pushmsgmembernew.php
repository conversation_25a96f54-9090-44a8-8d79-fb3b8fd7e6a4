<style text="css">
.input-large {
    width: 220px!important;
}
.top-tab {
  padding:0;
  font-size: 12px;
}
.top-tab > ul {
  display: flex;
  margin: 0;
  padding: 0;
  height: 40px;
  list-style:none;
}
.top-tab > ul > li {
  margin:0 2px 0 0;
  padding:5px;
  background:  #fff;
  border-radius:4px 4px 0 0;
  border: solid #e3e5e8;
  border-width: 1px 1px 0 1px;
  height:29px;
}
.top-tab > ul > li a {
  color: #3d3f45;
}
.top-tab ul li.active {
box-shadow: 0 3px 0px -1px #f6f7f9;
  background: #f6f7f9;
}
.top-tab ul li.active a {
  color: #000;
}
.content-container {
    border: 1px solid #E3E5E8;
    margin-top:-10px;
    border-radius: 0px 4px 4px 4px;
    min-width: auto;
}
.top-title {
    font-family: <PERSON><PERSON><PERSON> Sans;
    font-size: 14px;
    padding-bottom:14px;
    padding-left:6px;
}
.readonly-input-title {
    min-width:120px;
    color: #3D3F45;
}
.portlet.box > .portlet-body {
    padding-left: 20px;
}
.form-group {
    margin-bottom:0;
}
.table thead tr th {
    font-size: 12px;
    font-weight: normal;
    color: #000;
}
#bot_cond_scene .form-control {
    width: 276px;
}

.selected-members {
    min-height: 500px;
    border: 1px solid #E3E5E8;
    border-radius: 4px;
    margin-top: 64px;
    padding: 14px;
    position: sticky;
    top: 5%;
}
.img-back {
    width: 24px;
    height: 24px;
    background: #E3E5E8;
    border-radius: 4px;
    padding:2px 6px;
}
.member-list {
    border-top: solid 1px #EBEDF2;
    padding:12px 0;
    display: none;
}
.member-list img {
    margin-right:6px;
}
.col-md-2 {
    width: auto;
}

.line-count-container {
    margin-bottom: 2rem;
}
.line-count {
    display: flex;
    justify-content: space-between;
    white-space: nowrap;
    font-size: 14px;
    margin-left: 6px;
}
.line-count > div:first-child {
    text-align: left;
    flex-grow: 1
}
.line-count > div:not(:first-child) {
    text-align: right;
}
.line-count > div:nth-child(2) {
    border-right: 1px solid #ccc;
    padding-right: 20px;
}

#line-count-error-message {
    font-size: 14px;
    margin-left: 6px;
    margin-bottom: 2rem;
}
</style>

<div id="page-wrapper">
    <div class="portlet light">
        <div class="portlet box">
            <div class="portlet-body">
                <input type="hidden" name="act" id="act" value="" />
                <input type="hidden" name="task_id" id="task_id" value="<?php echo $post['task_id'] ?>" />
                <input type="hidden" name="task_name" id="task_name" value="<?php echo $post['task_name'] ?>">
                <input type="hidden" name="task_register" id="task_register" value="<?php echo $post['task_register'] ?>">
                <input type="hidden" name="scheduled_time" id="scheduled_time" value="<?php echo $post['scheduled_time'] ?>">
                <input type="hidden" name="user_attr_cd" id="user_attr_cd" value="">
                <input type="hidden" name="test" id="test" value="<?php if (isset($post['test'])) echo $post['test'] ?>">
                <input type="hidden" name="message_cd" id="message_cd" value="<?php if (array_key_exists('message_cd', $post)) echo $post['message_cd'] ?>" />
                <input type="hidden" name="member_id" id="member_id" value="" />
                <input type="hidden" name="start_date"/>
                <input type="hidden" name="end_date" />
                <input type="hidden" name="regist_start_date"/>
                <input type="hidden" name="regist_end_date" />
                <div class="row" style="display: flex;">
                    <div style="flex-basis: 65%;">				
                        <div class="form-body">
                            <div class="top-title"><?php echo __('admin.push.new')?></div>
                            <nav class="top-tab">
                                <ul class="">
                                    <li class="active"><a href="#"><?php echo __('admin.push.filter')?></a></li>
                                    <!-- <li><a href="#">LINEフィルター設定</a></li> -->
                                </ul>
                            </nav>
                            <div class="content-container light-gray">
                                <div class="flexbox-x-axis" style="padding-bottom:12px;">
                                    <p class="readonly-input-title"><?php echo __('admin.push.target')?></p>
                                    <div class="talkappi-radio js-task-type" data-name="task_type_cd" data-value="02" data-source='{"01":"<?php echo __('admin.push.to.all')?>", "02":"<?php echo __('admin.push.individual')?>"}'></div> 
                                </div>
                                <div class="flexbox-x-axis" style="padding-bottom:12px;">
                                    <p class="readonly-input-title"><?php echo __('admin.common.label.sns')?></p>
                                    <div class="talkappi-checkbox js-sns" data-name="sns_type_cd" data-value='<?php echo json_encode($post['sns_type_cd']) ?>' data-source='{"fb":"Facebook", "ln":"LINE", "wc":"Wechat", "wb":"Web"}'></div>
                                </div>
                                <div class="flexbox-x-axis" style="padding-bottom:12px;">
                                    <p class="readonly-input-title"><?php echo __('admin.common.label.lang')?></p>
                                    <?php 
                                        $bot_langs = explode(',', $_bot->lang_cd); 
                                        $bot_array = [];
                                        if ( $bot_langs && count($bot_langs) > 1 ) {
                                            // select all lang label button
                                            $bot_array['_select_all'] = __('admin.common.label.select_all_lang');
                                        }
                                        foreach ($bot_langs as $k => $lang_cd) {
                                            $bot_array[$lang_cd] = $_bot_lang[$lang_cd];
                                        }
                                        $bot_lang_json = json_encode($bot_array, JSON_UNESCAPED_UNICODE);
                                    ?>
                                    <div class="talkappi-checkbox js-language" data-name="lang_cd" data-value='<?php echo json_encode($post['lang_cd']) ?>' data-source='<?php echo $bot_lang_json ?>'></div>
                                </div>
                                <?php $terms = [['title' => __('admin.push.last.talk'), 'target' => '', 'name' => 'last_talk_time'],['title' => __('admin.push.register'), 'target' => 'regist_', 'name' => 'regist_date']]; ?>
                                <?php foreach($terms as $term) { ?>
                                <div class="flexbox-x-axis" style="padding-bottom:12px;">
                                    <p class="readonly-input-title"><?php echo $term['title'] ?></p>
                                    <div class="form-group flexbox-x-axis">
                                    <?php   $startDate = isset($post[$term['target']. 'start_date']) ? substr($post[$term['target']. 'start_date'], 0, 10) : '';
                                            $startTime = isset($post[$term['target']. 'start_date']) && $post[$term['target']. 'start_date'] !== '' ? substr($post[$term['target']. 'start_date'], 11, 5) : '0:00';
                                            $endDate = isset($post[$term['target']. 'end_date']) ? substr($post[$term['target']. 'end_date'], 0, 10) : '';
                                            $endTime = isset($post[$term['target']. 'end_date']) &&  $post[$term['target']. 'end_date'] !== '' ? substr($post[$term['target']. 'end_date'], 11, 5) : '0:00';
                                            $range = ["startDate" => "$startDate","endDate" => "$endDate","startTime" => "$startTime","endTime" => "$endTime"];
                                    ?>
                                        <div class="talkappi-datepicker-range" data-name='<?php echo $term['name']; ?>' data-time-format="hh:mm" data-value='<?php echo json_encode($range) ?>'></div>
                                    </div>
                                </div>
                                <?php } ?>
                                <div class="flexbox-x-axis" style="padding-bottom:12px;">
                                    <p class="readonly-input-title"><?php echo __('admin.push.facility.cd')?></p>
                                    <div class="form-group" style='margin-left: -10px; display: flex;'>
                                    <?php 
                                    echo $botcond; 
                                    ?>
                                    </div>
                                </div>
                                <div class="flexbox-x-axis" style="padding-bottom:12px;">
                                    <p class="readonly-input-title"><?php echo __('admin.push.user.attr')?></p>
                                    <div class="talkappi-radio js_user_attr_cond" data-name="user_attr_cond" data-value='<?php if (array_key_exists('user_attr_cond', $post)) echo $post['user_attr_cond'] ?>' data-source='{"AND":"AND", "OR":"OR"}'></div>
                                </div>
                                <div class="flexbox-x-axis" style='margin-left:120px; margin-bottom:10px;'> 
                                    <div class='flexbox-x-axis user-props-container'>
                                    </div>
                                    <label class="btn round light-blue action-button js-button-user-prop"><img src="/assets/admin/css/img/icon-add.svg" style="padding-right:2px;"><?php echo __('admin.itme.item.classification_add')?></label>
                                </div>
                                <div class="flexbox-x-axis" style="padding-bottom:12px;">
                                    <p class="readonly-input-title"><?php echo __('admin.push.search.key')?></p>
                                    <input type="text" class="form-control" name='keyword' value="<?php if (array_key_exists('keyword', $post)) echo  $post['keyword'] ; ?>" placeholder="<?php echo __('admin.push.search.name')?>" style="width: 314px;"/>
                                </div>
                                <div class="flexbox-x-axis" style="padding-bottom:12px;">
                                    <p class="readonly-input-title"></p>
                                    <input type="text" class="form-control" name='keyword-log' value="<?php if (array_key_exists('keyword-log', $post)) echo $post['keyword-log'] ; ?>" placeholder="<?php echo __('admin.push.search.chat')?>" style="width: 314px;"/>
                                </div>
                                <div class="form-group" style="margin:0 0 24px 110px;">
                                    <div class="flex">
                                        <div class="btn-medium btn-blue js-button-search" style="min-width: 64px;"><i class="fa fa-search" style="margin-right:8px;"></i><?php echo __('admin.common.button.search')?></div>
                                        <div class="btn-medium btn-white js-button-clear" style="min-width: 60px;"><?php echo __('admin.common.button.clear')?></div>
                                    </div>           
                                </div>
                                <div class="line-count-container" style="display:none;">
                            <?php if ($quota_result != -1): ?>
                                <div class="line-count">
                                <div><?php echo __('admin.push.line_target') ?> : <span id="line_user_count" style="color:#E53361; font-weight:bold;"></span> <?php echo __('admin.push.member.numbers') ?> (<?php echo __('admin.push.member.posts') ?>)</div>
                                <div><?php echo __('admin.push.line_available_count') ?> <span id="line_available_count" style="color:#245BD6; font-weight:bold;"><?php echo  $quota_result - $consumption_result ?></span> <?php echo __('admin.push.member.posts') ?></div>
                                <div style="margin-left:2rem"><?php echo __('admin.push.line_limit_count') ?> <span  id="line_limit_count" style="font-weight:bold;"><?php echo $quota_result ?></span> <?php echo __('admin.push.member.posts') ?></div>
                            </div>
                            <div id="line-count-error-message" style="color:#E53361; font-weight:bold; display:none;"><?php echo __('admin.push.line_limit_error_message') ?></div>
                            <?php endif; ?>
                        </div>
                                <div>
                                    <div class="top-title"><?php echo __('admin.push.pick.member')?></div>
                                    <table class="table table-striped table-bordered table-hover" id="sample_3">
                                        <thead>
                                            <tr>
                                                <th style="text-align: left;"><span class="icon-check"></span><?php echo __('admin.common.label.name')?></th>
                                                <th style="width:32px;"><?php echo __('admin.common.label.lang')?></th>
                                                <th><?php echo __('admin.common.label.sns')?></th>
                                                <th><?php echo __('admin.push.member.id')?></th>
                                                <th><?php echo __('admin.push.last.talk.time')?></th>
                                                <th><?php echo __('admin.push.user.attr')?></th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php
                                            foreach ($members as $member) {
                                                $is_tester = 'badge badge-success';
                                                if ($member['is_tester'] == 1) $is_tester = 'badge badge-warning';
                                            ?>
                                                <tr class="gradeX odd js-contents" data-channel="<?php echo $member['sns_type_cd'] ?>" role="row">
                                                    <!-- お名前 -->
                                                    <td class="js-member-name"><span class="icon-check"></span>
                                                    <?php 
                                                        if ($member['name']!= '') {
                                                            echo($member['name']);
                                                        }
                                                        else if ($member['sns_type_cd']== 'wb') {
                                                            if ($member['is_tester'] == 1) {
                                                                echo __('admin.push.test.user');
                                                            }
                                                            else {
                                                                echo __('admin.push.web.user');
                                                            }
                                                        }
                                                        else {
                                                            echo($member['last_name'] . ' ' . $member['first_name']);
                                                        }
                                                    ?></td>
                                                    <!-- 備考 -->
                                                    <td><?php echo $_codes['02'][$member['lang_cd']]; ?></td>
                                                    <!-- チャンネル -->
                                                    <td>
                                                    <?php echo $_codes['16'][$member['sns_type_cd']]; ?>
                                                    </td>
                                                    <!-- メンバーID -->
                                                    <td class="js-member-id <?php echo($member['member_id'])?>"><?php echo($member['member_id'])?></td>
                                                    <!-- 最終会話日時 -->
                                                    <td>
                                                        <span style="white-space: nowrap;">
                                                            <?php echo(date('Y/m/d H:i', strtotime($member['last_talk_date'])))?>
                                                        </span>
                                                        <?php if ($_action == 'logmembers' || true) :?>
                                                        <a class="pop_adminchat" <?php if ($member['is_tester'] == 1) echo ('title="テストユーザー"');?> member_id=<?php echo $member['member_id'] ?> bot_id=<?php echo $member['bot_id'] ?> ><span class="btn round light-blue" style="margin: 5px;" ><?php echo __('admin.common.button.showlog')?></span></a>
                                                        <?php endif; ?>
                                                    </td>
                                                    <!-- 備考 -->
                                                    <td><?php echo($member['attr_name']); ?></td>
                                                </tr>
                                            <?php } ?>
                                        </tbody>
                                    </table> 
                                </div>	
                            </div>
                        </div>																						
                    </div>
                    <div style="margin:0 0 0 20px;flex-basis: 30%;max-width:320px;">
                        <div class="selected-members">
                            <div style="display:flex;">
                                <div><?php echo __('admin.push.send.member')?>（<span class="js-selected-num">0 </span><?php echo __('admin.push.member.numbers') ?>）</div>
                                <div class="img-back js-member-check" style="margin-left:auto;">
                                    <img src="/assets/admin/css/img/icon-search.svg">
                                </div>
                            </div>
                            <div style="margin-top:24px;">
                                <p class="js-member-message"></p>
                                <ul class="member-list js-member-fb"><img src="/assets/admin/images/channel=fb.svg">Facebook</ul>
                                <ul class="member-list js-member-ln"><img src="/assets/admin/images/channel=line.svg">LINE</ul>
                                <ul class="member-list js-member-wc"><img src="/assets/admin/images/channel=wechat.svg">Wechat</ul>
                                <ul class="member-list js-member-wb"><img src="/assets/admin/images/channel=web.svg">Web</ul>
                                <?php if (count($selected_members) > 0) { ?>
                                    <?php foreach($selected_members as $selected_member): ?>
                                        <li class="js-selected-member js-saved-member" id="<?php echo $selected_member[0]['member_id'] ?>" channel="<?php echo $selected_member[0]['sns_type_cd'] ?>" style="margin-top:12px;">
                                        <span class="icon-cancel-small"></span><?php echo $selected_member[0]['last_name'] . $selected_member[0]['first_name'] ?></li>
                                    <?php endforeach; ?>
                                <?php } ?>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- ボタン　コンテナ -->
                <div class="actions-container" style="margin: 60px 0 0 110px;">
                    <span class="btn-larger btn-blue js-action-next"><?php echo __('survey.input.button.next')?></span>
                    <span class="btn-larger btn-white js-action-save"><?php echo __('admin.push.save.once')?></span>
                    <span class="btn-larger btn-white js-action-back"><?php echo __('admin.itme.itemmeu.back')?></span>
                </div>
            </div>
        </div>
    </div>
</div>


<script type="text/javascript">
    const userPropsArray = '<?php if (array_key_exists('user_attr_cd', $post))  echo $post['user_attr_cd']; ?>'.split(',');
</script>