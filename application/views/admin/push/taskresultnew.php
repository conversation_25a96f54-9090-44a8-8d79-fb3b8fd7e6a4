<style text='css'>
.action-button.page {
    max-width: 76px;
    min-width: 76px;
    margin-top: 40px;
    margin-bottom: 60px;
}
</style>

<div class="content-container white">
    <div class='flex-x-between' style='font-size: 14px;'>
        <div>
            <?php echo __('admin.push.name')?>：<?php echo $task_name; ?>
        </div>
        <?php if ($admin === true): ?>
            <a href="pushmsgtasklog?id=<?php echo $task_id ?>">
            <span class="btn-smaller btn-blue"><?php echo __('admin.push.execute.log')?></span>
            </a>
        <?php endif; ?>
    </div>
    <table class="table table-striped table-bordered table-hover js-data-table">
    <div class="flexbox-x-axis" style="margin: 12px 0;">
        <div style="margin-right:12px;">
            <?php  echo Form::select('sns_type_cd', $sns_type_cd_list, $post['sns_type_cd'], array('id'=>'sns_type_cd','class'=>'form-control', 'style' => "width:160px;"))?>
            </div>
            <div>
            <?php  echo Form::select('process_result_cd', $process_result_list, $post['process_result_cd'], array('id'=>'process_result_cd','class'=>'form-control', 'style' => "width:160px;"))?>
            </div>
        </div>
        <thead>
            <tr>
                <th><?php echo __('admin.push.date')?></th>
                <th><?php echo __('admin.common.label.name')?></th>
                <th><?php echo __('admin.common.label.sns')?></th>
                <th><?php echo __('admin.push.result')?></th>
            </tr>
        </thead>
        <tbody>
            <?php
                foreach ($results as $log) :
            ?>	
            <tr class="gradeX odd" role="row">
                <!-- 配信日時 -->
                <td>
                <?php
                    $original_date = $log['process_time'];
                    $datetime = new DateTime($original_date);
                    $formatted_date = $datetime->format('Y/m/d H:i:s');
                    echo $formatted_date;
                ?>
                </td>
                <!-- 配信先名前 -->
                <td class="flexbox-x-axis">
                    <?php 
                        if ($log['name'] != NULL && isset($member['name'])) {
                        echo($member['name']);
                        }
                        else if ($log['sns_type_cd'] == 'wb') {
                        echo __('admin.push.web.user');
                        }
                        else if ($log['first_name'] != NULL && $log['first_name'] != '') {
                        echo($log['last_name'] . ' ' . $log['first_name']);
                        }
                    ?>
                    <div style='text-align:right; margin-left:auto;'>
                        <a class="pop_adminchat" member_id="<?php echo $log['member_id'] ?>">
                        <span class="btn round light-blue" style="margin-left: auto;" ><?php echo __('admin.common.label.chat') ?></span> </a>
                    </div>
                </td>
                <!-- チャンネル -->
                <td style="text-align:left;">
                    <?php
                    if ($log['sns_type_cd']== 'fb') {
                    echo('Facebook');
                    }
                    if ($log['sns_type_cd']== 'ln') {
                    echo('LINE');
                    }
                    if ($log['sns_type_cd']== 'wc') {
                    echo('WeChat');
                    }
                    if ($log['sns_type_cd']== 'wb') {
                    echo('Web');
                    }
                    ?>
                </td>
                <!-- 結果 -->
                <td>
                    <?php if ($log['process_result'] === '2') { ?>
                    <div style='color: #E53361;'>
                    <?php echo($_codes['15'][$log['process_result']])?>
                    </div>
                    <?php } else if($log['process_result'] === '-1') { ?>
                    <?php echo __('admin.push.not.send') ?>
                    <?php } else { ?>
                    <div>
                    <?php echo($_codes['15'][$log['process_result']])?>
                    </div>
                    <?php } ?>
                </td>
            </tr>
            <?php endforeach; ?>
        </tbody>
    </table>
    <a href="pushmsgtaskdetail?id=<?php echo $task_id ?>" class="action-button page btn-white" style="min-width:100px;"><?php echo __('admin.push.detail.back') ?></a>
</div>