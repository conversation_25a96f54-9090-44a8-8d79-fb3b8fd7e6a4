<style text="css">
.top-tab {
    padding:0;
    font-size: 12px;
}
.top-tab > ul {
    display: flex;
    margin: 0;
    padding: 0;
    height: 40px;
    list-style:none;
}
.top-tab > ul > li {
    margin:0 2px 0 0;
    padding:5px;
    background:  #fff;
    border-radius:4px 4px 0 0;
    border: solid #e3e5e8;
    border-width: 1px 1px 0 1px;
    height:29px;
}
.top-tab > ul > li a {
    color: #3d3f45;
}
.top-tab ul li.active {
box-shadow: 0 3px 0px -1px #f6f7f9;
    background: #f6f7f9;
}
.top-tab ul li.active a {
    color: #000;
}
.content-container {
    border: 1px solid #E3E5E8;
    margin-top:-10px;
    border-radius: 0px 4px 4px 4px;
    min-width: auto;
}
.top-title {
    font-family: Hiragino Sans;
    font-size: 14px;
    padding-bottom:14px;
    padding-left:6px;
}
.readonly-input-title {
    min-width:120px;
    color: #3D3F45;
}
.portlet.box > .portlet-body {
    padding-left: 20px;
}
.form-group {
    margin-bottom:0;
}
.dataTables_length {
    display:none;
}
input[type="search"] {
    position: absolute;
    left:162px;
    top: -14px;
}
.table-scrollable {
    margin-top:20px!important;
}
.table thead tr th {
    font-size: 12px;
    font-weight: normal;
    color: #000;
}
span.selected-num {
    display:inline-block;
    margin-left:28px;
}
#bot_cond_scene .form-control {
    width: 276px;
}
.mobile-preview {
    position: sticky;
    top: 5%;
}
.col-md-2 {
    width: auto;
}
.modal-select-container .select .list {
    display: block;
}
.modal-select-container .select {
    overflow-y:auto;
}
.list {
    font-size: 12px;
}
.bot-id-list-container {
    width: 100%;
    /* height: 28px; */
    padding: 5px 12px;
    border-radius: 4px;
    background: #EBEDF2;
    border: 1px solid #E3E5E8;
    white-space: pre-wrap;
    word-wrap: break-word;
}
.line-count-container {
    margin-bottom: 2rem;
}
.line-count {
    display: flex;
    justify-content: space-between;
    white-space: nowrap;
    font-size: 14px;
    margin-left: 6px;
}
.line-count > div:first-child {
    text-align: left;
    flex-grow: 1
}
.line-count > div:not(:first-child) {
    text-align: right;
}
.line-count > div:nth-child(2) {
    border-right: 1px solid #ccc;
    padding-right: 20px;
}

#line-count-error-message {
    font-size: 14px;
    margin-left: 6px;
    margin-bottom: 2rem;
}
.icon-detail-box {
    top: auto;
    left: auto;
    margin-left:200px
}
</style>

<script>
    const _msgsData = <?php echo json_encode($bot_msgs_data, JSON_UNESCAPED_UNICODE) ?>;
</script>
<input type="hidden" name="act" id="act" value="">
<input type="hidden" name="task_id" id="task_id" value="<?php if (isset($post['task_id'])) echo $post['task_id'] ?>">
<input type="hidden" name="task_name" id="task_name" value="<?php if (isset($post['task_name'])) echo $post['task_name'] ?>">
<input type="hidden" name="task_register" id="task_register" value="<?php echo $post['task_register'] ?>">
<input type="hidden" name="scheduled_time" id="scheduled_time" value="<?php echo $post['scheduled_time'] ?>">
<input type="hidden" name="task_type_cd" id="task_type_cd" value="<?php if (isset($post['task_type_cd'])) echo $post['task_type_cd'] ?>">
<input type="hidden" name="test" id="test" value="<?php if (isset($post['test'])) echo $post['test'] ?>">
<input type="hidden" name="member_id" id="member_id" value="<?php echo $member_id_json ?>">
<input type="hidden" name="message_cd" id="message_cd" value="<?php echo $message_cd_json ?>">
<input type="hidden" name="user_attr_cd" id="user_attr_cd" value="">
<?php if ($bot_id_list === 1): ?>
    <input type="hidden" name="bot_id_list" value="<?php echo $post['bot_id_list']; ?>">
<?php endif; ?>
<input type="hidden" name="last_talk_time_from"/>
<input type="hidden" name="last_talk_time_to" />
<input type="hidden" name="regist_date_from"/>
<input type="hidden" name="regist_date_to" />


<div id="page-wrapper">
    <div class="portlet light">
        <div class="portlet box">
            <div class="portlet-body">
            <div class="row" style="display: flex;">
                <div style="flex-basis: 65%;">	
                    <?php if ($post['task_type_cd'] == NULL || $post['task_type_cd'] == '01'): ?> 			
                    <div class="form-body">
                        <div class="top-title"><?php echo __('admin.push.new')?></div>
                        <nav class="top-tab">
                            <ul class="">
                                <li class="active"><a href="#"><?php echo __('admin.push.filter')?></a></li>
                                <!-- <li><a href="#">LINEフィルター設定</a></li> -->
                            </ul>
                        </nav>
                        <div class="content-container light-gray">
                            <div class="flexbox-x-axis" style="padding-bottom:12px;">
                                <p class="readonly-input-title"><?php echo __('admin.push.method')?></p>
                                <div class="talkappi-radio js-task-type" data-name="task_type_cd" data-value="01" data-source='<?php echo json_encode($_codes['281']) ?>'></div> 
                            </div>
                            <div class="flexbox-x-axis" style="padding-bottom:12px;">
                                <p class="readonly-input-title"><?php echo __('admin.common.label.sns')?></p>
                                <div class="talkappi-checkbox js-sns" data-name="sns_type_cd" data-value='<?php if (isset($post['sns_type_cd'])) echo ($post['sns_type_cd']) ?>' data-source='<?php echo json_encode($_codes['08']) ?>'></div>
                            </div>
                            <div class="flexbox-x-axis" style="padding-bottom:12px;">
                                <p class="readonly-input-title"><?php echo __('admin.common.label.lang')?></p>
                                <?php 
                                    $bot_langs = explode(',', $_bot->lang_cd); 
                                    $bot_array = [];
                                    if ( $bot_langs && count($bot_langs) > 1 ) {
                                        // select all lang label button
                                        $bot_array['_select_all'] = __('admin.common.label.select_all_lang');
                                    }
                                    foreach ($bot_langs as $k => $lang_cd) {
                                        $bot_array[$lang_cd] = $_bot_lang[$lang_cd];
                                    }
                                    $bot_lang_json = json_encode($bot_array, JSON_UNESCAPED_UNICODE);
                                ?>
                                <div class="talkappi-checkbox js-language" data-name="lang_cd" data-value='<?php if (isset($post['lang_cd'])) echo ($post['lang_cd']) ?>' data-source='<?php echo $bot_lang_json ?>'></div>
                            </div>
                            <?php $terms = [['title' => __('admin.push.last.talk'), 'target' => 'last_talk_time'],['title' => __('admin.push.register'), 'target' => 'regist_date']]; ?>
                            <?php foreach($terms as $term) { ?>
                            <div class="flexbox-x-axis" style="padding-bottom:12px;">
                                <p class="readonly-input-title"><?php echo $term['title'] ?></p>
                                <div class="form-group flexbox-x-axis">
                                    <?php   $startDate = isset($post[$term['target']. '_from']) ? substr($post[$term['target']. '_from'], 0, 10) : '';
                                            $startTime = isset($post[$term['target']. '_from']) ? substr($post[$term['target']. '_from'], 11, 5) : '0:00';
                                            $endDate = isset($post[$term['target']. '_to']) ? substr($post[$term['target']. '_to'], 0, 10) : '';
                                            $endTime = isset($post[$term['target']. '_to']) ? substr($post[$term['target']. '_to'], 11, 5) : '0:00';
                                            $range = ["startDate" => "$startDate","endDate" => "$endDate","startTime" => "$startTime","endTime" => "$endTime"];
                                    ?>
                                    <div class="talkappi-datepicker-range" data-name='<?php echo $term['target']; ?>' data-time-format="hh:mm" data-value='<?php echo json_encode($range) ?>'></div>
                                </div>
                            </div>
                            <?php } ?>
                            <?php if ($bot_id_list !== 1): ?>
                            <div class="flexbox-x-axis" style="padding-bottom:12px;">
                                <p class="readonly-input-title"><?php echo __('admin.push.facility.cd')?></p>
                                <div class="form-group" style='margin-left: -10px; display: flex;'>
                                <?php 
                                echo $botcond; 
                                ?>
                                </div>
                            </div>
                            <?php endif; ?>
                            <div class="flexbox-x-axis" style="padding-bottom:12px;">
                                <p class="readonly-input-title"><?php echo __('admin.push.user.attr')?></p>
                                <div class="talkappi-radio js_user_attr_cond" data-name="user_attr_cond" data-value='<?php if (isset($post['user_attr_cond'])) echo $post['user_attr_cond'] ?>' data-source='{"AND":"AND", "OR":"OR"}'></div>
                            </div>
                            <div class="flexbox-x-axis" style='margin-left:120px; margin-bottom:10px;'> 
                                <div class='flexbox-x-axis user-props-container'>
                                </div>
                                <label class="btn round light-blue action-button js-button-user-prop"><img src="/assets/admin/css/img/icon-add.svg" style="padding-right:2px;"><?php echo __('admin.itme.item.classification_add')?></label>
                            </div>
                            <?php if ($bot_id_list === 1): ?>
                            <div class="flexbox-x-axis" style="padding-bottom:12px;">
                                <p class="readonly-input-title"><?php echo __('admin.push.facility')?></p>
                                <label class="btn round light-blue action-button js-button-bot-id-list js-new"><img src="/assets/admin/css/img/icon-add.svg" style="padding-right:2px;"><?php echo __('admin.common.button.select')?></label>
                            </div>
                            <?php endif; ?>
                            <div class="form-group" style="margin:0 0 12px 110px;">
                                <div class="flex">
                                    <div class="btn-medium btn-white js-button-clear" style="min-width: 0px;"><?php echo __('admin.common.button.clear')?></div>
                                </div>           
                            </div>
                        </div>
                    </div>
                    <?php else: ?>
                    <div class="form-body">
                        <!-- <div class="top-title">新規配信・メッセージ選択</div>
                        <div class="form-group" style="margin-bottom: 15px">
                            <label class="control-label col-md-2" style="width:110px;">フィルター設定</label>
                            <div class="col-md-9">
                                <p>talkappiフィルター設定</p>
                            </div>
                        </div> -->
                        <div class="form-group">
                            <label class="control-label col-md-2" style="width:110px;"><?php echo __('admin.push.target.range')?></label>
                            <div class="col-md-9" style="position:relative;">
                            <div class="readonly-input" style="height:66px; white-space: normal;">	
                                <div class="flexbox-x-axis">
                                    <p class="readonly-input-title"><?php echo __('admin.push.target')?></p>
                                    <?php echo __('admin.push.individual').'(' . $selected_members_count . ' '. __('admin.push.member.numbers'). ')' ?>
                                </div>
                                <div class="flexbox-x-axis">
                                    <p class="readonly-input-title"><?php echo __('admin.push.send.member')?></p>
                                    <div class="word-break">
                                        <?php
                                        $count = count($selected_members);
                                        $i = 1;
                                        foreach($selected_members as $selected_member) {
                                            ++$i;
                                            if ($i > $count) {
                                                echo $selected_member[0]['last_name'] . $selected_member[0]['first_name'];
                                            } else {
                                                echo $selected_member[0]['last_name'] . $selected_member[0]['first_name'] . ', ';
                                            }
                                        }
                                        ?>
                                    </div>
                                </div>
                            </div>
                            <a class="js-action-pre" style="position:absolute; right:23px; bottom:25px;"><?php echo __('admin.common.button.edit')?></a>
                        </div>            
                    </div>
                    </div>
                    <?php endif; ?>
                    <div class="form-body">
                        <?php if ($post['task_type_cd'] == NULL || $post['task_type_cd'] == '01'): ?>
                        <div class="top-title"><?php echo __('admin.push.all_channel_target') ?> : <span id="member_count"></span><?php echo __('admin.push.member.numbers') ?></div>
                        <div class="line-count-container">
                            <?php if ($quota_result != -1): ?>
                                <div class="line-count" style="display:none">
                                <div><?php echo __('admin.push.line_target') ?> : <span id="line_user_count" style="color:#E53361; font-weight:bold;"></span> <?php echo __('admin.push.member.numbers') ?> (<?php echo __('admin.push.member.posts') ?>)</div>
                                <div><?php echo __('admin.push.line_available_count') ?> <span id="line_available_count" style="color:#245BD6; font-weight:bold;"><?php echo  $quota_result - $consumption_result ?></span> <?php echo __('admin.push.member.posts') ?></div>
                                <div style="margin-left:2rem"><?php echo __('admin.push.line_limit_count') ?> <span  id="line_limit_count" style="font-weight:bold;"><?php echo $quota_result ?></span> <?php echo __('admin.push.member.posts') ?></div>
                            </div>
                            <div id="line-count-error-message" style="color:#E53361; font-weight:bold; display:none;"><?php echo __('admin.push.line_limit_error_message') ?></div>
                            <?php endif; ?>
                        </div>
                        <?php endif; ?>
                        <div class="top-title"><?php echo __('admin.push.pick.content')?><span class="icon-detail" style="margin: 0 0 0 0.5rem;" title="<?php echo __('admin.push.pick.content_notice') ?>"></span></div>
                        <div>
                            <table class="table table-bordered js-selected-table" style="display:none;">
                                <thead style="background-color:#F6F7F9;">
                                    <tr>
                                        <th style="text-align:end;">#</th>
                                        <th><?php echo __('admin.push.type')?></th>
                                        <th><?php echo __('admin.push.content.cd')?></th>
                                        <th><?php echo __('admin.push.content.name')?></th>
                                    </tr>
                                </thead>
                            <tbody id="sortable-contents-cd"></tbody>
                            </table> 
                        </div>
                        <div class="content-container light-gray" style="position:relative; padding-top:0px;">
                        <table class="table table-striped table-bordered table-hover" id="sample_3">
                            <div class="flexbox-x-axis" style="position: absolute; top: 15px; position:relative;">
                                <div style="margin-right:12px;">
                                <?php  echo Form::select('msg_type_cd', $msg_type_cd, $post['msg_type_cd'], array('id'=>'msg_type_cd','class'=>'form-control', 'style' => "width:150px;"))?>
                                </div>
                                <button type="button" id="newButton" class="btn blue" style="background-color: #245BD6; height:28px; position:absolute; right:0;">
                                    <img src="./../assets/admin/css/img/icon-add-white.svg" width="12" height="12">
                                    <?php echo __('admin.push.content.new')?>
                                </button>
                            </div>
                            <thead>
                                <tr>
                                    <th><?php echo __('admin.push.type')?></th>
                                    <th><?php echo __('admin.push.content.cd')?></th>
                                    <th><?php echo __('admin.push.content.name')?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                foreach ($msg_list as $msg) {
                                ?>
                                    <tr class="gradeX odd js-contents" role="row">
                                        <!-- タイプ -->
                                        <td class="flexbox-x-axis">
                                            <div class="js-check-container" style="padding:0 0 0 5px;">
                                                <span class="icon-check">
                                            </div>
                                                <div style="padding:0 20px;" class="js-con-type"><?php echo $_codes['13'][$msg['msg_type_cd']]; ?>
                                                </div>
                                            </label>
                                        </td>
                                        <!-- コンテンツCD -->
                                        <td class="js-con-cd"><?php echo $msg['msg_cd']; ?></td>
                                        <!-- コンテンツ名 -->
                                        <td class="js-con-name"><a class="link-animate" href="msgnew?id=<?php echo $msg['msg_id'] ?>"><?php echo $msg['msg_name']; ?></a></td>
                                    </tr>
                                <?php } ?>
                            </tbody>
                        </table> 
                        </div>
                        <?php if (count($bot_msgs_temp) > 0): ?>
                        <div style="display:none;">
                            <?php foreach ($bot_msgs_temp as $cd => $name): ?>
                                <li class="js-saved-msgs" id="<?php echo $cd ?>"></li>
                            <?php endforeach; ?>
                        </div>
                        <?php endif; ?>
                    </div>
                    <div class="form-actions">
                        <div class="row">
                            <div class="col-md-offset-3 col-md-9">
                                <div class="actions-container">
                                    <button type="button" class="btn-larger btn-blue js-action-next" id="nextButton"><?php echo __('survey.input.button.next')?></button>
                                    <?php if ($post['task_type_cd'] == '02'): ?> 	
                                        <a class="btn-larger btn-white js-action-pre"><?php echo __('survey.input.button.prev')?></a>
                                    <?php endif; ?>
                                    <a class="btn-larger btn-white js-action-save"><?php echo __('admin.push.save.once')?></a>
                                    <a class="btn-larger btn-white js-action-back"><?php echo __('admin.common.button.return_to_list')?></a>
                                </div>
                            </div>
                        </div>
                    </div>																							
                </div>
                <div class="talkappi-preview js-pushmsg-preview" data-type="message" style="margin:0 0 0 20px; flex-basis: 30%; max-width:320px;">
                </div>
            </div>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
    const userPropsArray = '<?php if (isset($post['user_attr_cd'])) echo $post['user_attr_cd']; ?>'.split(',');
    const typeCd = '<?php echo $post['task_type_cd']; ?>'
    const childBots = <?php echo json_encode($sub_bot, true); ?>
</script>