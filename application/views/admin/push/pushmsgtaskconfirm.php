<style text="css">
.readonly-input {
    white-space: normal;
}
p.readonly-input-title {
    min-width:120px;
    margin: 0px 4px 0 4px;
}
.flexbox-x-axis {
    padding-bottom:8px;
    align-items: flex-start;
}
.btn-larger {
    min-width: 96px;
}
</style>
<?php 
    foreach($post as $k=>$v) {
        if ($k !== 'task_register' && $k !== 'cond_scene_cd') {
            echo ('<input type="hidden" id="' . $k . '" name="' . $k . '" value="' . $v . '" />' );
        }
    }
    foreach($task_data as $k=>$v) {
        if ($k !== 'sns_type_cd' && $k !== 'lang_cd') {
            echo ('<input type="hidden" name="' . $k . '" value="' . $v . '" />' );
        }
    }
?>
<input type="hidden" name="act" id="act">
<input type="hidden" name="cond_scene_cd" value="<?php echo $post['cond_scene_cd']; ?>">

<?php if (is_array($task_data['sns_type_cd']) === true) : ?>
    <input type="hidden" name="sns_type_cd" value="<?php echo implode(',', $task_data['sns_type_cd']) ?>" />
<?php endif; ?>
<?php if (is_array($task_data['lang_cd']) === true) : ?>
    <input type="hidden" name="lang_cd" value="<?php echo implode(',', $task_data['lang_cd']) ?>" />
<?php endif; ?>

<div class="content-container white border">
	<div style="display:flex;">
		<!-- 左側 -->
		<div style="flex-basis: 65%;">
			<div class="section-container">
				<h2></h2>
				<div class="form-body">
					<!-- <div class="form-group">
						<label class="control-label col-md-2" style="width:110px;">フィルター設定</label>
						<div class="col-md-9">
							<p>talkappiフィルター設定</p>
						</div>
					</div> -->
                    <div class="form-group">
                        <label class="control-label col-md-2" style="width:110px;"><?php echo __('admin.push.target.range')?></label>
                        <?php if($post['task_type_cd'] == '01'): ?>
                        <div class="col-md-9" style="position:relative;">
                            <div class="readonly-input" style="height:auto;">
                                <div class="flexbox-x-axis">
                                    <p class="readonly-input-title"><?php echo __('admin.push.target')?></p>
                                    <?php echo $_codes['281'][$post['task_type_cd']]. '（' .$member_count . ' '. __('admin.push.member.numbers'). ')' ?>
                                </div>
                                <div class="flexbox-x-axis">
                                    <p class="readonly-input-title"><?php echo __('admin.common.label.sns')?></p>
                                    <?php 
                                        if (is_array($task_data['sns_type_cd']) === false) {
                                            $task_data['sns_type_cd'] = explode(',', $task_data['sns_type_cd']);
                                        };
                                        $count = count($task_data['sns_type_cd']);
                                        $i = 1;
                                        foreach($task_data['sns_type_cd'] as $sns) {
                                            ++$i;
                                            if ($i > $count) {
                                                echo $_codes['08'][$sns]; 
                                            } else {
                                                echo $_codes['08'][$sns]. ', '; 
                                            }
                                        }
                                    ?>
                                </div>
                                <div class="flexbox-x-axis">
                                    <p class="readonly-input-title"><?php echo __('admin.common.label.lang')?></p>
                                    <?php
                                        if (is_array($task_data['lang_cd']) === false) {
                                            $task_data['lang_cd'] = explode(',', $task_data['lang_cd']);
                                        };
                                        $count = count($task_data['lang_cd']);
                                        $i = 1;
                                        foreach($task_data['lang_cd'] as $lang) {
                                            ++$i;
                                            if ($i > $count) {
                                                echo $_codes['02'][$lang]; 
                                            } else {
                                                echo $_codes['02'][$lang]. ', '; 
                                            }
                                        }
                                    ?>
                                </div>
                                <div class="flexbox-x-axis">
                                    <p class="readonly-input-title"><?php echo __('admin.push.last.talk')?></p>
                                    <?php if ( ($task_data['last_talk_time_from'] == NULL) && ($task_data['last_talk_time_to'] == NULL)): ?>
                                        <?php echo __('index.bothsmoking'); ?>
                                    <?php else: ?>
                                        <?php echo $task_data['last_talk_time_from']. '〜 ' .$task_data['last_talk_time_to'] ; ?>
                                    <?php endif; ?>
                                </div>
                                <div class="flexbox-x-axis">
                                    <p class="readonly-input-title"><?php echo __('admin.push.register')?></p>
                                    <?php if ( ($task_data['regist_date_from'] == NULL) && ($task_data['regist_date_to'] == NULL)): ?>
                                        <?php echo __('index.bothsmoking'); ?>
                                    <?php else: ?>
                                        <?php echo $task_data['regist_date_from']. '〜 ' .$task_data['regist_date_to'] ; ?>
                                    <?php endif; ?>
                                </div>
                                <div class="flexbox-x-axis" style="padding-right: 30px;">
                                    <p class="readonly-input-title"><?php echo __('admin.push.facility.cd')?></p>
                                    <?php 
                                    echo $scene_label;
                                    ?>
                                </div>
                                <div class="flexbox-x-axis">
                                    <p class="readonly-input-title"><?php echo __('admin.push.user.attr')?></p>
                                    <div class='flexbox-x-axis user-props-container'>
                                    <?php if ($post['user_attr_cd'] === ''): ?>
                                        <?php echo __('admin.push.user.attr.all'); ?>
                                    <?php endif ?>
                                    </div>
                                </div>
                                <?php if (count($pushFacilities) > 0): ?>
                                    <div class="flexbox-x-axis">
                                        <p class="readonly-input-title"><?php echo __('admin.push.facility')?></p>
                                        <?php 
                                            $count = count($pushFacilities);
                                            $i = 1;
                                            echo '<p style="width: 60%">';
                                            foreach ($pushFacilities as $k=>$v) {
                                                ++$i;
                                                if ($i > $count) {
                                                    echo $v;
                                                } else {
                                                    echo $v . ', ';
                                                }
                                            } 
                                            echo '</p>';
                                        ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <a class="js-action-pre" style="position:absolute; right:23px; bottom:90px;"><?php echo __('admin.common.button.edit')?></a>
                        </div>
                        <?php else: ?>
                        <div class="col-md-9" style="position:relative;">
                            <div class="readonly-input" style="height:66px;">	
                                <div class="flexbox-x-axis">
                                    <p class="readonly-input-title"><?php echo __('admin.push.target')?></p>
                                    <?php echo __('admin.push.individual').'(' . $selected_members_count . ' '. __('admin.push.member.numbers'). ')' ?>
                                </div>
                                <div class="flexbox-x-axis">
                                    <p class="readonly-input-title"><?php echo __('admin.push.send.member')?></p>
                                        <?php
                                        $count = count($selected_members);
                                        $i = 1;
                                        foreach($selected_members as $selected_member) {
                                            ++$i;
                                            if ($i > $count) {
                                                echo $selected_member[0]['last_name'] . $selected_member[0]['first_name'];
                                            } else {
                                                echo $selected_member[0]['last_name'] . $selected_member[0]['first_name'] . ', ';
                                            }
                                        }
                                        ?>
                                    <div class="word-break"></div>
                                </div>
                            </div>
                            <a href='pushmsgmembernew' style="position:absolute; right:23px; bottom:25px;"></a>
                        </div>
                        <?php endif; ?>            
                    </div>
					<div class="form-group">
						<label class="control-label col-md-2" style="width:110px;"><?php echo __('admin.push.content')?></label>
                        <div class="col-md-9">
                            <div class="readonly-input flex-x-between word-break" style="height: 27px; padding-right: 40px; position:relative;">
                                <div>
                                    <?php
                                    $count = count($bot_msgs_temp);
                                    $i = 1;
                                    foreach($bot_msgs_temp as $cd => $name) {
                                        ++$i;
                                        if ($i > $count) {
                                            echo "<span class='js-selected-cd' id='$cd'>$name</span>";
                                        } else {
                                            echo "<span class='js-selected-cd' id='$cd'>$name; </span>";
                                        }
                                    }
                                    ?>
                                </div>
                                <a class="js-action-pre" style="position:absolute; right:10px; top:3px;"><?php echo __('admin.common.button.edit')?></a>
                            </div>
                        </div>   
					</div>
                    <div class="form-group">
						<label class="control-label col-md-2" style="width:110px;"><?php echo __('admin.push.name')?></label>
						<div class="col-md-9">
							<input name="task_name" class="form-control js-input-title" value="<?php if (isset($post['task_name'])) echo($post['task_name'])?>"></input>
						</div>
					</div>
                    <div class="form-group">
						<label class="control-label col-md-2" style="width:110px;"><?php echo __('admin.push.mode')?></label>
                        <div class="col-md-9">
                            <div class="talkappi-radio" data-name="test" data-value="<?php echo empty($post['test']) ? '1' : $post['test']; ?>" data-source='{"1":"<?php echo __('admin.push.test') ?>", "2":"<?php echo __('admin.push.real') ?>"}'></div>
                        </div>    
					</div>
                    <div class="form-group">
						<label class="control-label col-md-2" style="width:110px;"><?php echo __('admin.push.type')?></label>
                        <div class="col-md-9">
                            <div class="talkappi-radio js-task-register" data-name="task_register" data-value="<?php echo isset($post['task_register']) ? $post['task_register']: '01' ?>" data-source='<?php echo json_encode($_codes['282']) ?>'></div>
                        </div>    
					</div>
                    <div class="form-group js-register-type" style="display: none">
						<label class="control-label col-md-2" style="width:110px;"><?php echo __('admin.push.date.plan')?></label>
                        <div class="col-md-8 flexbox-x-axis">
                            <input type="text" class="talkappi-datepicker js-date" name="scheduled_date" value="<?php if (isset($post['scheduled_time'])) echo substr($post['scheduled_time'],0,10) ?>" />
                            <input type="text" class="talkappi-timepicker js-time" name="scheduled_hour" value="<?php if (isset($post['scheduled_time'])) echo substr($post['scheduled_time'],11,5) ?>" />
                        </div>
					</div>		
				</div>
			</div>				
			<!-- ボタン コンテナ -->
			<div class="actions-container" style="margin: 40px 0 198px 110px;">
				<span class="btn-larger btn-blue js-action-push"><?php echo __('admin.push.send')?></span>
				<span class="btn-larger btn-white js-action-pre"><?php echo __('booking.template.button.back')?></span>
                <span class="btn-larger btn-white js-action-save"><?php echo __('admin.push.save.once')?></span>
				<span class="btn-larger btn-white js-action-back" style="margin-right:10px;"><?php echo __('admin.common.button.return_to_list')?></span>
			</div>
		</div>
		<!-- プレビュー -->
        <div class="talkappi-preview js-pushmsg-preview" data-type="message" style="margin:0 0 0 20px; flex-basis: 30%; max-width:320px;">
        </div>
	</div>
</div>

<script type="text/javascript">
    const userPropsArray = '<?php echo $post['user_attr_cd']; ?>'.split(',');
    const _msgsData = <?php echo json_encode($bot_msgs_data, JSON_UNESCAPED_UNICODE) ?>;
</script>