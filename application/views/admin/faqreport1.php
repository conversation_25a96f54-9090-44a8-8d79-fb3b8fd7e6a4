			<!-- B<PERSON>IN PAGE HEADER-->
			<!-- BEGIN PAGE HEAD -->
			<div class="page-head">
				<!-- BEGIN PAGE TITLE -->
				<div class="page-title">
					<h1>FAQページ利用状況レポート<small></small></h1>
				</div>
				<!-- END PAGE TITLE -->
			</div>
			<!-- END PAGE HEAD -->
			<!-- END PAGE HEADER-->
			<!-- BEGIN PAGE CONTENT-->
			<div class="row">
				<div class="col-md-12">
			        <!-- Page Content -->
			        <div id="page-wrapper">
			        <div class="portlet light">
					<?php echo $reportmenu ?> 
						<div class="portlet box">
							<div class="portlet-body">
								<div class="form-body">		
									<div class="form-group">
										<label class="control-label col-md-1" style="padding-left:15px; padding-right:15px;"><?php echo __('admin.common.label.period') ?></label>
										<div class="col-md-4">
											<input name="start_date" id="start_date" value="<?php echo($start_date)?>" style="float:left;" class="form-control form-control-inline input-small date-picker" size="16" data-date-format="yyyy-mm-dd"  type="text"/>
											<input name="end_date" id="end_date" value="<?php echo($end_date)?>" style="float:left; margin-left:10px;" class="form-control form-control-inline input-small date-picker" size="16" data-date-format="yyyy-mm-dd"  type="text"/>
										</div>
										<label class="control-label col-md-1"><?php echo __('admin.common.label.display_zero') ?></label>
										<div class="col-md-2">
											<input type="checkbox" id="utf-8" name="zero_flg" <?php if ($zero=='0') echo('checked')?> value="1" class="make-switch" data-on-color="success" data-off-color="warning">
										</div>										
									</div>
									<div class="form-group">
										<label class="control-label col-md-1" style="padding-left:15px; padding-right:15px;"><?php echo __('admin.common.label.lang') ?></label>
										<div class="col-md-2">
											<?php echo Form::select('lang_cd', $lang, $lang_cd, array('id'=>'lang_cd','class'=>'form-control'))?>
										</div>
									</div>
									<div class="form-group">
										<label class="control-label col-md-1" style="padding-left:15px; padding-right:15px;"><?php echo __('admin.common.label.user_flow') ?></label>
										<?php echo $botcond ?>
										<div class="col-md-1">
											<button type="button" id="searchButton" class="btn yellow" disabled="disabled">
											<i class="fa fa-search mr10"></i><?php echo __('admin.common.button.search') ?></button>
										</div>	
										<div id="scene_use_flg" style="display:none;">use_faq_flg</div>																																				
										<div class="col-md-1" style="margin-left: 25px;">
											<button type="button" data-tableId="faqreporttable" data-title="利用状況（日別）" class="btn green exportCsv">
												<?php echo __('admin.common.button.csv_export') ?>
											</button>
										</div>
									</div>
									<div>
										<?php echo __('admin.common.label.mobile_annotation') ?>
									</div>
								</div>													
							<table class="table table-striped table-bordered table-hover js-data-table" id="faqreporttable">
							<thead>
							<tr>
								<th rowspan="2" style="text-align: center; vertical-align: middle;"><?php echo __('admin.common.label.date_of_use') ?></th>
								<th rowspan="2" style="text-align: center; vertical-align: middle;"><?php echo __('admin.common.label.number_of_user_visits') ?></th>
								<th colspan="6" style="border-bottom: 1px solid #ddd;"><?php echo __('admin.common.label.number_of_user_operations') ?></th>																					
							</tr>
							<tr>
								<th><?php echo __('admin.faqreportmenu.search_window_input') ?></th>
								<th><?php echo __('admin.faqreportmenu.keyword_selection') ?></th>
								<th><?php echo __('admin.faqreportmenu.category_selection') ?></th>
								<th><?php echo __('admin.faqreportmenu.question_selection') ?></th>
								<th><?php echo __('admin.faqreportmenu.question_display') ?></th>
								<th><?php echo __('admin.faqreportmenu.related_question_display') ?></th>		
							</tr>							
							</thead>

							<tbody>
							<?php 
							for($i=0; ;$i++) {
								$cur_day = date("Y-m-d",strtotime("+" . $i ." day",strtotime($start_date)));
								if ($cur_day > $end_date) break;
								echo('<tr class="gradeX odd" role="row">');
								echo('<td>');
								echo($cur_day);
								echo('</td>');
								echo('<td style="text-align:right">');
								if (array_key_exists($cur_day, $logs_member)) {
									$total = 0;
									if (array_key_exists('_0', $logs_member[$cur_day])) {
										$total = $total + $logs_member[$cur_day]['_0'];
									}
									if (array_key_exists('_1', $logs_member[$cur_day])) {
										$total = $total + $logs_member[$cur_day]['_1'];
									}
									if (array_key_exists('_2', $logs_member[$cur_day])) {
										$total = $total + $logs_member[$cur_day]['_2'];
									}
									echo($total);
									if (array_key_exists('_1', $logs_member[$cur_day])) {
										echo('(' . $logs_member[$cur_day]['_1'] . ')');
									}
									else {
										if ($zero == '0') echo('(' . $zero . ')');
									}
								}
								else {
									if ($zero == '0') echo($zero . '(' . $zero . ')');
								}
								echo('</td>');
								if (array_key_exists($cur_day, $logs)) {
									$search_div_list = ['1','4','2','3','5','6'];
									foreach($search_div_list as $div) {
										echo('<td style="text-align:right">');
										if (array_key_exists($div . '_0', $logs[$cur_day]) || array_key_exists($div . '_1', $logs[$cur_day]) || array_key_exists($div . '_2', $logs[$cur_day])) {
											$total = 0;
											if (array_key_exists($div . '_0', $logs[$cur_day])) {
												$total = $total + $logs[$cur_day][$div . '_0'];
											}
											if (array_key_exists($div . '_2', $logs[$cur_day])) {
												$total = $total + $logs[$cur_day][$div . '_2'];
											}
											if (array_key_exists($div . '_1', $logs[$cur_day])) {
												$total = $total + $logs[$cur_day][$div . '_1'];
												$mobile = $logs[$cur_day][$div . '_1'];
											}
											else {
												$mobile = 0;
											}
											echo($total);
											if ($mobile > 0) {
												echo('(' . $mobile . ')');
											}
											else {
												if ($zero == '0') echo('(' . $zero . ')');
											}
										}
										else {
											if ($zero == '0') echo($zero . '(' . $zero . ')');
										}
										echo('</td>');
									}
								}
								else {
									if ($zero == '0') {
										echo('<td style="text-align:right">0(0)</td><td style="text-align:right">0(0)</td><td style="text-align:right">0(0)</td><td style="text-align:right">0(0)</td><td style="text-align:right">0(0)</td><td style="text-align:right">0(0)</td>');
									}
									else {
										echo('<td></td><td></td><td></td><td></td><td></td><td></td>');
									}
								}
								echo('</tr>');
							}
							?>

							</tbody>
							</table>
							</div>
						</div>
					</div>
			        </div>
			        <!-- /#page-wrapper -->					
				</div>
			</div>
			<!-- END PAGE CONTENT-->
