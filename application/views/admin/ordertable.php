<input type="hidden" name="new_flg" id="new_flg" value="<?php echo ($new_flg) ?>" />

<?php
foreach ($orders as $order) {
	$order_data = json_decode($order['order_data'], true);
	echo('<tr class="gradeX odd" role="row">');
	echo('<td class="sorting_1">');
	echo('<a class="link-animate" href="/admin/order?id=' . $order['order_id'] . '">' . $order['order_no'] . '</a><br/>');
	echo('<img src="/assets/common/images/chat_' . $order['lang_cd'] . '.png" style="margin:5px;width:24px;" />');
	echo('</td>');
	if ($order['order_status_cd'] == '03') {
		echo('<td><span style="color:red;font-weight:bold;">' . $codes['25'][$order['order_status_cd']] . '</span></td>');
	}
	else {
		echo('<td>' . $codes['25'][$order['order_status_cd']] . '</td>');
	}
	echo('<td>');
	foreach($order_data['cart'] as $cart) {
		echo($cart['plan']['name'] . '<br/>');
		echo($cart['room']['name'] . '<span style="margin-left:5px;margin-right:5px;font-weight:bold"> x ' . $cart['quantity'] . '</span><br/>');
		echo('金額：<span style="margin-left:5px;margin-right:5px;font-weight:bold">' . $cart['price']['currency'] . ' ' . number_format($cart['price']['price']) . '</span> 支払方法：<span style="margin-left:5px;font-weight:bold">' . $codes['38'][$order_data['payment']['type']] . '</span><br/>');
	}
	echo('</td>');
	echo('<td style="text-align:center;">' . $order['order_name'] . '</td>');
	echo('<td style="text-align:center;">');
	echo((new DateTime($order['checkin_date']))->format('Y/n/j') . '～' . (new DateTime($order['checkout_date']))->format('Y/n/j') . '<br/>');
	echo('<span style="margin-left:5px;margin-right:5px;font-weight:bold">' . $order['nights'] . '</span>泊');
	echo('</td>');
	echo('<td style="text-align:center;">' . substr($order['order_date'], 0, 16) . '</td>');
	echo('<td>');
	echo(nl2br($order['bot_name']));
	if ($order_data['api_result'][0]['link_key'] != null) echo ("(" . $service['link_key'] . ')');
	echo('<br/>');
	echo('予約者の名前：' . $order['order_name'] . '<br/>');
	echo('電話番号：' . $order_data['order_tel'] . '<br/>');
	echo('メールアドレス：' . $order_data['order_email'] . '<br/>');
	echo('</td>');
	echo('</tr>');
}

foreach ($services as $service) {
	?>
	<tr class="gradeX odd" role="row">
		<td class="sorting_1">
			<a class="link-animate" href="/admin/order?id=<?php echo ($service['order_id']) ?>">
				<?php echo ($service['order_no']) ?></a>
			<br />
			<span class="badge badge-warning"
				style="display:none;margin-top: 5px;"><?php echo ($_bot_lang[$service['lang_cd']]) ?></span>
			<?php if ($service['sns_type_cd'] == null) { ?>
				<img src="/assets/common/images/chat_<?php echo $service['order_lang_cd'] ?>.png"
					style="margin:5px;width:24px;" />
				<img src="/assets/common/images/icon_wb.png" style="margin:5px;width:28px;" />
			<?php } else { ?>
				<img src="/assets/common/images/chat_<?php echo $service['lang_cd'] ?>.png" style="margin:5px;width:24px;" />
				<img src="/assets/common/images/icon_<?php echo $service['sns_type_cd'] ?>.png" style="margin:5px;width:28px;" />
			<?php } ?>
			<span class="badge badge-primary"
				style="display:none;margin-top: 5px;"><?php echo ($_codes['08'][$service['sns_type_cd']]) ?></span>
			<br />
		</td>
		<td>
			<?php
			if ($service['order_status_cd'] == '04')
				$service['order_status_cd'] = '01';
			if ($service['order_status_cd'] == '05')
				$service['order_status_cd'] = '02';
			if ($service['order_status_cd'] == '03')
				echo ('<span style="color:red;font-weight:bold;">');
			echo ($codes['25'][$service['order_status_cd']]);
			if ($service['order_status_cd'] == '03')
				echo ('</span>');
			?>
		</td>
		<td>
			<?php
			echo (nl2br($service['bot_name']));
			if (array_key_exists('link_key_name', $service))
				echo ("(" . $service['link_key_name'] . ')');
			?><br />
			<?php echo (nl2br($service['product_name'])) ?>
			✕<span style="margin-left:5px;font-weight:bold"><?php echo ($service['num']) ?></span><br />
			金額：<span style="margin-left:5px;font-weight:bold"><?php echo ($service['total_amount']) ?></span><br />
			<?php
			$order_data = json_decode($service['order_data'], true);
			$payment_type = $order_data['payment_type'];
			$card_type = $order_data['card_type'] == 'jtb' ? 'JTB' : $order_data['card_type'];
			?>
			支払方法：<span style="margin-left:5px;font-weight:bold"><?php echo $payment_type . ' ' . $card_type ?></span><br />
		</td>
		<td style="text-align:center;">
			<?php
			$member_name = '';
			if ($service['name'] != NULL) {
				$member_name = $service['name'];
			} else {
				if ($service['sns_type_cd'] == 'wb') {
					$member_name = "Webユーザー";
				} else {
					if ($service['last_name'] . $service['first_name'] != '') {
						$member_name = $service['last_name'] . $service['first_name'];
					}
				}
			}
			?>
			<?php
			echo (nl2br($member_name));
			if ($_user->role_cd == '99' || $_user->role_cd == '01' || $_user->role_cd == '07' || $_user->role_cd == '09')
				echo ('<br><label class="badge badge-info js-memberinfo" style="cursor:pointer;" data-member-id="' . $service['member_id'] . '">' . __('admin.common.label.journey') . '</label><br>');
			?>
			<br />
			<?php if ($service['sns_type_cd'] != null) { ?>
				<a class="pop_adminchat" member_id="<?php echo $service['member_id'] ?>">
					<span class="badge badge-primary" style="margin: 5px;">チャット</span> </a>
				<br />
				<?php if ($service['order_data'] != '' && false) { ?>
					<a class="pop_servicebox" sid="<?php echo ($service['order_id']) ?>"><span class="badge badge-warning"
							style="margin: 5px;">用件編集</span></a>
				<?php } ?>
			<?php } else {
				echo ('非会員');
			} ?>
		</td>
		<td style="text-align:center;">
			<?php
			$checkin_date = new DateTime($service['checkin_date']);
			$checkout_date = new DateTime($service['checkout_date']);
			?>
			<?php echo $checkin_date->format('Y/n/j'); ?>～<br />
			<?php echo $checkout_date->format('Y/n/j'); ?>
		</td>
		<td style="text-align:center;">
			<?php
			if ($service['link_order_date'] == '') {
				echo substr($service['order_date'], 0, 16);
			} else {
				echo substr($service['link_order_date'], 0, 16);
			}
			?>
		</td>
		<td>
			代表者の名前：<?php echo (nl2br($service['checkin_name'])) ?> <br />
			電話番号：<?php echo (nl2br($service['checkin_tel'])) ?> <br />
			メールアドレス：<?php echo (nl2br($service['checkin_mail'])) ?> <br />
		</td>
	</tr>
<?php } ?>