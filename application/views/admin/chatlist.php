				<?php
				$chat_member_exist = 0;
				foreach($chatlist as $lang_cd=>$chat) {
					if ($lang_cd == '') continue;
					$style = 'display: block;';
					if (strpos($expand_lang, $lang_cd) === false) $style = 'display: none;';
					?>
				<li>
					<a href="javascript:;">
					<span class="title" style="vertical-align: middle; margin-left: 0;"><?php echo($codes['02'][$lang_cd])?></span>
					<span class="arrow <?php if ($style=='display: block;') echo('open'); ?>"></span>
					</a>
					<ul class="sub-menu" id="<?php echo($lang_cd); ?>" style="<?php echo($style); ?>">
					<?php foreach($chat as $member_id=>$member) {
							$member_id = substr($member_id, 2);
							echo("<li ");
							if ($member_id == $chat_member) {
								$chat_member_exist = 1;
								echo('class="active"');
							}
							echo('>');
							echo('<a style="display: block;" class="chat-member sidebar-user-container" member_id="' . $member_id . '" lang_cd="' . $member['lang_cd'] . '" sns_type_cd="' . $member['sns_type_cd'] . '"');
							$user = Session::instance()->get('user', NULL);
							$user_me = $member['user_id'] == $user->user_id;

							if ($member['chat_mode'] == 0) {
								echo(' chat_user="" ');
							}
							else {
								if ($user_me) { 
									echo(' chat_user="" ');
								}
								else { 
									echo(' chat_user="' . $member['user_name'] . '" ');
								}
							}
							echo(' >');
							echo('<div class="sidebar-user-left">');
							echo('<div class="sidebar-user-wrapper">');

							if (!isset($member['avatar']) || $member['avatar'] == null || $member['avatar'] == '' || $member['avatar'] == 'undefined' || $member['sns_type_cd'] == 'fb') {
								if ($member['mobile'] == '1') {
									$avatar_url = "/assets/common/images/icon_round_mobile.svg";
								} else {
									$avatar_url = "/assets/common/images/icon_round_comp.svg";
								}
							}
							else {
								$avatar_url = $member['avatar'];
							}
							echo('<img class="sidebar-user-avatar" src="' . $avatar_url . '" height=30 width=30 />');

							echo('<div class="sidebar-user-sns">');
							if ($member['sns_type_cd'] == 'fb') { 
								echo('<img src="/assets/common/images/icon_round_fb.svg" alt="facebook icon" height=16 width=16 />');
							} else if ($member['sns_type_cd'] == 'ln') { 
								echo('<img src="/assets/common/images/icon_round_ln.svg" alt="line icon" height=16 width=16 />');
							} else if ($member['sns_type_cd'] == 'wc') { 
								echo('<img src="/assets/common/images/icon_round_wc.svg" alt="wechat icon" height=16 width=16 />');
							} else if ($member['sns_type_cd'] == 'wb') { 
								echo('<img src="/assets/common/images/icon_round_wb.svg" alt="web icon" height=16 width=16 />');
								if ($member['name'] == '' || $member['name'] == $member_id) $member['name'] = __('admin.chatlist.web_user');
							}
							echo('</div>');
							echo('</div>');
							if ($member['is_tester'] == 1) {
								$member['name'] = "※tester";
							}

							$online_style = 'style="margin-right: 2px"';
							if ($member['chat_online_status'] == 1) {
								$online_style = 'style="margin-right: 10px"';
							}

							$chat_mode_badge = '';
							if (($member['chat_mode'] == 1 || $member['chat_mode'] == 2) && $member['user_id'] != '') {
								$chat_mode_badge_color = $user_me ? "sidebar-green" : "sidebar-gray"; 
								$chat_mode_badge = '<span id="chat_mode_badge" class="sidebar-status-badge ' . $chat_mode_badge_color . '">' . __('admin.chatlist.responding') . '</span>';
							}

							$request_badge = '';
							if ($member['help'] == 1 && $member['user_id'] == '') {
								$request_badge = '<span class="sidebar-status-badge sidebar-orange">' . __('admin.chatlist.request') . '</span>';
							}

							echo('<div class="sidebar-user-name"' . $online_style . '>');				
							echo($member['name']);
							if (array_key_exists('country_cd', $member) && array_key_exists($member['country_cd'], $country) && 
								$member['country_cd'] != 'JP' && $member['country_cd'] != 'KS' && $member['country_cd'] != '') {
								echo('<img src="https://cdn.talkappi.com/common/country-flags/svg/' . strtolower($member['country_cd']) . '.svg" title="' . $country[$member['country_cd']] . '" style="margin-left:4px;width:24px;" onerror="this.src=\'\'">');
							} 
							if ($member['chat_online_status'] == 1) {
								echo('<span class="sidebar-user-online"></span>');
							}
							echo('</div>');
							echo($chat_mode_badge);
							echo($request_badge);
							if ($member['unanswer'] > 0) {
								echo('<span class="sidebar-status-badge sidebar-red" data-span="unanswer">');
								echo($member['unanswer']);
								echo('</span>');
							}
							if ($member['member_tag'] != '') {
								echo('<span class="sidebar-status-badge sidebar-yellow">' . $member['member_tag'] . '</span>');
							}
							if ($member['chat_mode'] != 1 && $member['chat_mode'] != 2) {
								echo('<span class="sidebar-user-time">' . $member['wait_time'] . '</span>');							
							}
							echo('</div>');
							echo('</a>');
						echo('</li>');
					} ?>
					</ul>
				</li>
				<?php }?>
				<input type="hidden" name="new_flg" id="new_flg" value="<?php echo($new_unanswer)?>" />
				<input type="hidden" name="new_tags" id="new_tags" value='<?php echo($new_tags)?>' />
				<input type="hidden" name="chat_member_exist" id="chat_member_exist" value="<?php echo($chat_member_exist)?>" />