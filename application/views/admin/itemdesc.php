<style>
	.talkappi-pulldown.js-translation-type.auto-translation .talkappi-dropdown-container.pulldown{
		background-color: #FFE6D6;
	}
	.talkappi-pulldown.js-translation-type .talkappi-dropdown-container.pulldown{
		background-color: #E3E5E8;
	}
	.popover>.arrow, .popover>.arrow:after {
		display: none;
	}
	#js-comment-count-popover {
		display: flex;
		min-width: 400px;
		padding: 8px 12px;
		flex-direction: column;
		justify-content: center;
		align-items: flex-start;
		gap: 4px;
		border-radius: 4px;
		background: #FFF;
		box-shadow: 1px 2px 8px 0px rgba(61, 63, 69, 0.24);
	}
	#js-comment-count-popover .comment-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		gap: 10px;
		width: 100%;
	}
	#js-comment-count-popover .comment-header .upd-info {
		font-size: 14px;
		font-weight: 600;
	}
	#js-comment-count-popover .comment-header .version {
		color: #1CA38B;
	}
	#js-comment-count-popover .comment-body{
		font-size: 12px;
	}
	.history-record-container{
		margin-bottom: 8px;
		width: 400px;
	}
	.history-record-container .history-record-title {
		display: flex;
		padding: 6px 12px;
		align-items: center;
		gap: 12px;
		align-self: stretch;
		border-radius: 999px;
		background: #EBEDF2;
		cursor: pointer;
	}
	.history-record-container .history-record-items {
		display: none;
		width: 400px;
		position: absolute;
		top: 0;
		border-radius: 12px;
		border: 1px solid #E3E5E8;
		box-shadow: 1px 2px 8px 0px rgba(61, 63, 69, 0.24);
		background-color: #FFFFFF;
		overflow-y: auto;
		max-height: 567px;
	}
	.history-record-container .history-record-items .history-record-item {
		display: flex;
		padding: 8px 12px;
		flex-direction: column;
		justify-content: center;
		align-items: flex-start;
		gap: 4px;
		background: #FFF;
		cursor: pointer;
	}
	.history-record-container .history-record-items .history-record-item:nth-child(2n-1) {
		background: #F6F7F9;
	}
	.history-record-container .history-record-items .history-record-item .history-record-item-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		gap: 12px;
		width: 100%;
	}
	.history-record-item .history-record-item-header .history-record-item-info {
		color: #000;
		font-size: 12px;
		font-weight: 600;
	}
	.history-record-item .history-record-item-header .history-record-item-version {
		color: #1CA38B;
		font-family: "Hiragino Sans";
		font-size: 12px;
		font-weight: 400;
	}
	.history-record-item .history-record-item-note {
		color: #77797D;
		font-size: 12px;
		font-weight: 300;
	}
	.alert-success.disabled {
		background-color: #eeeeee;
		color: #77797D;
		cursor: not-allowed !important;
	}
	.talkappi-upload.disabled {
		pointer-events: none;
		color: #77797D;
		cursor: not-allowed !important;
	}
	.talkappi-upload.disabled .talkappi-show-container {
		background-color: #eeeeee;
	}
	@media screen and (max-width: 991px) {
		.history-record-container {
			display: none;
		}
	}
</style>

<script type="text/javascript">
	const _preview_data = <?php echo json_encode($preview_data, JSON_UNESCAPED_UNICODE) ?>;
	const _lang_edit = '<?php echo $lang_edit ?>';
	const _native_support_lang = <?php echo json_encode((isset($native_support_lang) && is_array($native_support_lang)) ? $native_support_lang : [], JSON_UNESCAPED_UNICODE) ?>;
	const _item_id = '<?php echo $item_id?>';
	let _translation_type = '<?php echo $post['translation_type'] ?>';
	const _item_data = <?php echo json_encode($item_data, JSON_UNESCAPED_UNICODE) ?>;
	<?php 
		if ($temp_bot_id !== NULL) {
			echo('const _temp_bot_id = ' . $temp_bot_id . ';');
		}
	?>
	<?php if ($temp_bot_lang !== null){ ?>
		const _bot_id_itemdesc = '<?php echo $temp_bot_lang; ?>';
	<?php } else { ?>
		const _bot_id_itemdesc = '<?php echo $_bot_id; ?>';
	<?php } ?>
</script>

<input type="hidden" id="reflect_image_flg" name="reflect_image_flg" value='' />
<?php 
	if($temp_bot_lang){
		$_bot_lang = $temp_bot_lang;
	}
?>

<!-- BEGIN PAGE CONTENT-->
<?php echo $menu ?>
<input type="hidden" name="mail_users" id="mail_users" value="">
<input type="hidden" name="item_data" id="item_data" value="">
<div class="content-container white border">
	<div class="section-container">
		<div class="row">
			<div class="col-md-8">
					<?php 
						$label_message = '';
						if ($is_in_edit) {
							$label_message = __('admin.item.itemdesc.version.temporary_save');
						} elseif ($item_data['crt_time'] !== NULL) {
							if ($version == 'current' || $version == NULL) {
								$label_message = __('admin.item.itemdesc.version.current');
							} else {
								$label_message = __('admin.item.itemdesc.version.previous');
							}
						}
						if ($label_message !== '') {
							$label_message .= '・' . $item_data['upd_user'] . ' - ' . substr($item_data['upd_time'], 0, 16);
						}
					?>
					<?php if ($label_message !== '') { ?>
					<div class="flex" style="padding:12px 0;margin-bottom:24px;justify-content:space-between;align-items:center">
						<div class="update-information" style="font-size:14px;font-weight:600">
							<?php echo $label_message ?>
						</div>
						<div style="display:flex;align-items:center;gap:22px;">
							<?php 
								if ($version == 'current' || $version == NULL) {
									if ($is_in_edit && $item_data['crt_time'] !== NULL) {
										echo '<div class="btn round image edit js-goto-version" data-version="current">' . __('admin.item.itemdesc.go_to_current') . '</div>';
									} else {
										if ($has_edit_data) {
											echo '<div>' . __('admin.item.itemdesc.in_edit') . '</div>';
											echo '<div class="btn round image edit js-goto-version" data-version="">' . __('admin.item.itemdesc.back_to_edit') . '</div>';
										}
									}
								} else {
									if ($mode === 'read') {
										echo '<div>' . __('admin.item.itemdesc.readonly') . '</div>';
										echo '<div class="btn round image edit js-edit-from">' . __('admin.item.itemdesc.edit_from_the_version') . '</div>';
									}
								}
							?>
						</div>
					</div>
					<?php } ?>
					<input type="hidden" name="message" id="message" value="<?php echo ($message) ?>" />
					<input type="hidden" name="lang" value="<?php echo ($lang_edit) ?>" />
					<div style="display:flex;justify-content:space-between;align-items:flex-start;margin-bottom:20px;gap:20px;">
						<nav class="line-tab" style="padding-top: 0;">
							<ul class="flexbox" style="margin: 0; padding-inline-start: 0px;flex-flow: wrap; gap: 6px 0px;">
							<?php
								$display_lang_arr = explode(',', $display_lang);
								foreach($_bot_lang as $lang_cd=>$lang_name) {
									if ($lang_cd==$lang_edit) {
										echo('<li class="active">');
									}
									else {
										echo('<li>');
									}
									if ($display_lang !== null && !in_array($lang_cd, $display_lang_arr)) $lang_name = $lang_name . __('admin.itme.itemmeu.non_display');
									if ($is_admin){
										echo('<a class="func-menu" href="/admin/itemdesc?id='. $item_id .'&lang='. $lang_cd . '&admin=1">' . $lang_name . '</a></li>');
									} else {
										echo('<a class="func-menu" href="/admin/itemdesc?id='. $item_id .'&lang='. $lang_cd . '">' . $lang_name . '</a></li>');
									}
								}
							?>
							</ul>
						</nav>
						<?php if ($item_data['crt_time'] !== NULL) { ?>
						<?php if (($lang_edit != 'ja' && ($version === 'current' || $is_in_edit === false))) {
								$translate_type = $item_data['translate_type'] ?? 0;
						?>
							<div class="talkappi-pulldown js-translation-type <?php echo $translate_type == '1' ? "auto-translation" : "" ?>" data-value="<?php echo $translate_type ?>" data-source='[{"code":"0","text":"<?php echo __('admin.common.label.native_translation') ?>"},{"code":"1","text":"<?php echo __('admin.common.label.auto_translation') ?>"}]'></div>
						<?php } ?>
						<?php } ?>
					</div>
					<?php if ($lang_edit != 'ja' && $ref_ja_flg === true) { ?>
						<div class="form-group">
							<label class="label label-success"><?php echo __('admin.item.itemdesc.multilang.message') ?></label>
						</div>
					<?php } ?>
					<div class="form-group">
						<label class="control-label col-md-2"><?php echo __('admin.common.label.name') ?></label>
						<div class="col-md-8">
							<div class="input-icon right">
								<textarea name="item_name" id="item_name" <?php echo $mode === 'read' ? 'readonly' : '' ?> class="form-control talkappi-textinput" data-max-input="80" rows="2" placeholder=""><?php if ($item_data != NULL) echo ($item_data['item_name']) ?></textarea>
							</div>
						</div>
					</div>
					<div class="form-group">
						<label class="control-label col-md-2"><?php echo __('admin.common.label.common_description') ?>
						<span class="icon-detail" title=<?php echo __('admin.common.label.common_description_placeholder') ?>></span>
						</label>
						<div class="col-md-8">
							<div class="input-icon right">
								<textarea name="sell_point" id="sell_point" class="form-control talkappi-textinput" <?php echo $mode === 'read' ? 'readonly' : '' ?> data-max-input="80" rows="4" placeholder=<?php echo __('admin.common.label.common_description_placeholder') ?>><?php if ($item_data != NULL) echo ($item_data['sell_point']) ?></textarea>
							</div>
						</div>
					</div>
					<div class="form-group">
						<label class="control-label col-md-2"><?php echo __('admin.common.label.extra_description') ?>
						<span class="icon-detail" title=<?php echo __('admin.common.label.extra_description_placeholder') ?>></span>
						</label>
						<div class="col-md-8">
							<textarea name="item_description" class="form-control talkappi-textinput" <?php echo $mode === 'read' ? 'readonly' : '' ?> data-max-input="2000" rows="8" placeholder=<?php echo __('admin.common.label.extra_description_placeholder') ?>><?php if ($item_data != NULL) echo ($item_data['description']) ?></textarea>
						</div>
					</div>
					<div class="form-group last">
						<label class="control-label col-md-2"><?php echo __('admin.common.label.photo') ?></label>
						<div class="col-md-8 flexbox-x-axis" style="gap: 8px;">
							<div class="talkappi-upload <?php echo $mode === 'read' ? 'disabled' : '' ?>" style="max-width:320px" data-name="image_base64" data-type="img" data-label="<?php if ($item_data != NULL) echo $item_data['item_image'] ?>" data-url="<?php if ($item_data != NULL) echo $item_data['item_image'] ?>" data-max-size="2"></div>
							<div class="checkbox-label reflect_image-container" style="display: flex; align-items: center; width: 50%;">
								<input type="checkbox" name="reflect_image" id="reflect_image" style="display: none;" <?php if ($mode === 'read') echo 'disabled' ?>>
								<label for="reflect_image" style="display: flex; align-items: center;  cursor: pointer;" >
									<?php echo __('admin.item.itemdesc.reflact_image_to_other_lang') ?>
								</label>
							</div>
						</div>
					</div>
					<div class="form-group">
						<label class="control-label col-md-2"><?php echo __('admin.common.label.photo_action') ?></label>
						<div class="col-md-8">
							<div class="input-icon right">
								<input name="url" id="url" type="text" maxlength="1001" class="form-control" <?php echo $mode === 'read' ? 'readonly' : '' ?> placeholder="" value="<?php if ($item_data != NULL) echo ($item_data['url']) ?>">
							</div>
						</div>
					</div>
					<div class="form-group" style="display:none;">
						<label class="control-label col-md-2"><?php echo __('admin.common.label.phone_number') ?></label>
						<div class="col-md-6">
							<div class="input-icon right">
								<input name="tel" id="tel" type="text" class="form-control" <?php echo $mode === 'read' ? 'readonly' : '' ?> placeholder="" value="<?php if ($item_data != NULL) echo ($item_data['tel']) ?>">
							</div>
						</div>
					</div>
					<div class="form-group" style="display:none;">
						<label class="control-label col-md-2"><?php echo __('admin.common.label.address') ?></label>
						<div class="col-md-10">
							<div class="input-icon right">
								<textarea name="address" class="form-control" <?php echo $mode === 'read' ? 'readonly' : '' ?> maxlength="225" rows="2" placeholder=""><?php if ($item_data != NULL) echo ($item_data['address']) ?></textarea>
							</div>
						</div>
					</div>
					<div class="form-group" style="display:none;">
						<label class="control-label col-md-2"><?php echo __('admin.common.label.map_url') ?></label>
						<div class="col-md-10">
							<div class="input-icon right">
								<input name="map_url" id="map_url" type="text" maxlength="1001" class="form-control" <?php echo $mode === 'read' ? 'readonly' : '' ?> placeholder="" value="<?php if ($item_data != NULL) echo ($item_data['map_url']) ?>">
							</div>
						</div>
					</div>
					<div class="form-group">
						<label class="control-label col-md-2"><?php echo __('admin.common.label.mobile') ?></label>
						<div class="col-md-2">
							<input type="checkbox" name="flg_mobile" id="flg_mobile" value="1" <?php if ($mobile_flg == 1) echo ('checked'); ?> class="make-switch" <?php echo $mode === 'read' ? 'readonly' : '' ?> data-on-color="success" data-off-color="warning">
						</div>
					</div>
					<?php 
					for ($i = 1; $i <= 3; $i++) {
						$btn = NULL;
						if ($item_data != NULL && is_array($item_data['btns']) && isset($item_data['btns'][$i - 1]))	{
							$btn = $item_data['btns'][$i - 1];
						}
						$disabled_attribute = $mode === 'read' ? ['disabled' => 1] : [];
					?>
						<div class="form-group">
							<label class="control-label col-md-2"><?php echo __('admin.common.label.button') ?><?php echo $i ?></label>
							<div class="col-md-2" style="padding-right: 0px;width:100px;">
								<?php echo Form::select('btn' . $i . '_name', $btn_select, $btn['btn_name'] ?? '', array_merge(['class' => 'form-control btn_type', 'id' => 'btn' . $i . '_name'], $disabled_attribute)) ?>
							</div>
							<div class="col-md-2" style="padding-right: 0px;width:90px;">
								<?php echo Form::select('btn' . $i . '_url_lang_cd', $url_lang_cd, $btn['btn_url_lang_cd'] ?? '', array_merge(['class' => 'form-control btn_lang', 'id' => 'btn' . $i . '_url_lang_cd'], $disabled_attribute)) ?>
							</div>
							<div class="col-md-6">
								<input name="btn<?php echo $i ?>_url" type="text" maxlength="1001" class="form-control pc" <?php echo $mode === 'read' ? 'readonly' : '' ?> placeholder="" value="<?php if ($btn != NULL) { echo (htmlspecialchars($btn['btn_url'])); } else { echo ''; } ?>">
								<div class="input-group" id="btn<?php echo $i ?>_url_sp" style="margin-top: 2px;<?php if ($mobile_flg == 0) echo ('display:none;'); ?>">
									<span class="input-group-addon">
										<i class="fa fa-mobile-alt"></i>
									</span>
									<input name="btn<?php echo $i ?>_url_sp" type="text" maxlength="1001" class="form-control" <?php echo $mode === 'read' ? 'readonly' : '' ?> placeholder='<?php echo __('admin.item.itemdesc.mobile.url') ?>' value="<?php if ($btn != NULL) { echo (htmlspecialchars($btn['btn_url_sp'])); } else { echo ''; } ?>">
								</div>
								<div id="btn<?php echo $i ?>_url_show" class="skill" style="line-height:2">
									<?php
									$disabled = $mode === 'read' ? 'disabled' : '';
									if (array_key_exists("btn" . $i . '_url_show', $skills)) {
										foreach ($skills["btn" . $i . "_url_show"] as $skill) {
											// preg_match('/^UNSUPPORT -- \{"cmd":"http.+/', $skill, $matches) is used to find pattern like 『UNSUPPORT -- {"cmd":"http:\/\/www.google.com"}』, if it is the case, Database only have url for button to redirect, do not show skill.
											if ($skill != '' && strpos($skill, 'http') !== 0 && !preg_match('/^UNSUPPORT -- \{"cmd":"http.+/', $skill, $matches)) 
												echo ('<span class="alert alert-success alert-dismissable ' . $disabled . '" style="padding:0px 5px;cursor:pointer;word-break: break-all;">' . $skill . '</span>');
										}
									}
									?>
									<span class="alert alert-success alert-dismissable <?php echo $disabled ?>" style="padding:0px 5px;cursor:pointer;word-break: break-all;">+</span>
								</div>
							</div>
						</div>
					<?php } ?>
					<!-- 翻訳 -->
					<?php if (count($lang_display) > 1 && count($_bot_lang) > 1 && $mode !== 'read') { ?>
						<div class="form-group" style="display:flex;">
							<label	class="control-label col-md-2 edit-answer-title"><?php echo __('admin.common.label.reflect.all_lang') ?><?php echo($_bot_lang[$lang_edit]) ?></label>
							<div>	
								<div class="checkbox-label">
									<input type="radio" name="translate" id="translate_no" value='translate_no'>
									<label for="translate_no" id="translate_no_label"><?php echo __('admin.common.label.not_reflect') ?></label>
								</div>
								<div class="checkbox-label">
									<input type="radio" name="translate" id="translate_copy" value='translate_copy'>
									<label for="translate_copy"><?php echo __('admin.common.label.reflect.not.translate') ?></label>
								</div>
								<div class="checkbox-label js-auto-translate">
									<input type="radio" name="translate" id="translate_auto" value='translate_auto'>
									<label for="translate_auto"><?php echo __('admin.common.label.reflect.translate') ?></label>
								</div>
								<?php 
								if ($flg_native_translate == "1" && $lang_edit == 'ja') { ?>
									<div class="checkbox-label js-request">
										<input type="radio" name="translate" id="translate_native" value='translate_native'>
										<label for="translate_native"><?php echo __('admin.common.label.reflect.native.translate') ?></label>
									</div>
								<?php } ?>
								<div style="font-size:10px;margin-left:18px;"><?php echo __('admin.item.itemdesc.tranlate.message') ?></div>
							</div>
						</div>
						<div class="form-group js-auto_trans" style="display:none;">
							<label	class="control-label col-md-2 edit-answer-title"></label>
							<div class="edit-menu-container" style="width: auto; display: flex; gap: 8px; flex-direction: column; font-weight:300; margin-bottom:12px;">
								<div>
									<p><?php echo __('admin.common.auto.translation.desc') ?></p>
								</div>
								<div class="flexbox-x-axis" style="gap: 12px;">
									<div class="flexbox-x-axis" style="gap: 8px;">
										<div class="checkbox-label translate-lang flexbox-x-axis">
											<input type="checkbox" name="auto_translate_item_name" id="auto_translate_item_name" checked="true">
											<label for="auto_translate_item_name" style="margin: 0;"><?php echo __('admin.common.label.name') ?></label>
										</div>
									</div>
									<div class="flexbox-x-axis" style="gap: 8px;">
										<div class="checkbox-label translate-lang flexbox-x-axis">
											<input type="checkbox" name="auto_translate_sell_point" id="auto_translate_sell_point" checked="true">
											<label for="auto_translate_sell_point" style="margin: 0;"><?php echo __('admin.common.label.common_description') ?></label>
										</div>
									</div>	
									<div class="flexbox-x-axis" style="gap: 8px;">
										<div class="checkbox-label translate-lang flexbox-x-axis">
											<input type="checkbox" name="auto_translate_item_description" id="auto_translate_item_description" checked="true">
											<label for="auto_translate_item_description" style="margin: 0;"><?php echo __('admin.common.label.extra_description') ?></label>
										</div>
									</div>	
								</div>
							</div>
						</div>
						<div class="form-group js-trans-content" style="display:none;">
							<label	class="control-label col-md-2 edit-answer-title"></label>
							<div class="edit-menu-container" style="width: auto; display: flex; gap: 8px; flex-direction: column; font-weight:300; margin-bottom:12px;">
								<div>
									<p><?php echo __('admin.common.native.translation.desc') ?></p>
                        			<p><?php echo __('admin.common.native.translation.explain') ?></p>
								</div>
								<div class="flexbox-x-axis" style="gap: 12px;">
									<p><?php echo __('admin.common.label.expect.lang') ?></p>
									<div class="flexbox-x-axis" style="gap: 8px;">
										<?php
										foreach ($_bot_lang as $k => $v) {
											if (!in_array($k, $lang_display)) continue;
											$style = '';
											if ($k == $lang_edit) $style = 'display: none;';
											if (!in_array($k, $native_support_lang)) {
												$style='display: none;';
											}
											echo ('<div class="checkbox-label translate-lang flexbox-x-axis" style="' . $style . '">');
											echo ('<input type="checkbox" value="' . $k . '" name="translate_lang[]" id="translate_to_' . $k . '">');
											echo ('<label for="translate_to_' . $k . '" style="margin: 0;">' . $_bot_lang[$k] . '</label>');
											echo ('</div>');
										}
										?>
									</div>
								</div>
								<div style="display:flex">
									<div class="talkappi-pulldown js-trans-priority" style="width: fit-content;" data-name="trans-priority" data-value="2" data-source='{"4":"<?php echo __('admin.common.label.priority') ?>：<?php echo __('admin.common.label.priority.high') ?>", "2":"<?php echo __('admin.common.label.priority') ?>：<?php echo __('admin.common.label.priority.normal') ?>", "1":"<?php echo __('admin.common.label.priority') ?>：<?php echo __('admin.common.label.priority.low') ?>"}'></div>
									<div style="align-self: center; margin-left: 1em;" class="js-trans-estimate"></div>
								</div>
								<textarea class="fullwidth-textarea menu-text" style="white-space: pre-wrap;word-wrap: break-word;display:block;max-width:100%;" name="translate_comment" placeholder="<?php echo __('admin.common.native.translation.placeholder') ?>"></textarea>
							</div>
						</div>
					<?php } ?>
					<!-- アラート -->
					<?php if ($mode !== 'read') { ?>
						<div class="form-group js-alert-radio">
							<label class="control-label col-md-2" style="white-space: nowrap"><?php echo __('admin.common.label.alert.mail') ?></label>
							<div class="talkappi-radio js-alert" 
							data-day="<?php echo $remind_info ? substr($remind_info['remind_time'], 0, 10) : '' ?>"
							data-time="<?php echo $remind_info ? substr($remind_info['remind_time'], 11, 5) : '12:00' ?>"
							data-users="<?php echo $remind_info ? $remind_info['mail_users'] : '' ?>"
							data-upd_user="<?php echo $remind_info ? $remind_info['upd_user'] : '' ?>"
							data-remark="<?php echo $remind_info ? json_decode($remind_info['mail_params'], JSON_UNESCAPED_UNICODE)['remark'] : ''?>"
							data-name="alert" 
							data-value="<?php echo $remind_info ? '02' : '01'; ?>" 
							data-source='{"01":"<?php echo __('admin.inquiry.label.answer_limit_no')?>", "02":"<?php echo __('admin.inquiry.label.answer_limit_yes')?>"}'></div>
						</div>
						<!-- コメント -->
						<div class="comment-container" style="margin-top:24px">
							<div class="flex" style="justify-content:space-between;padding:12px 0;box-shadow:0px 1px 0px 0px #EBEDF2 inset">
								<div style="font-size:14px;font-weight:600"><?php echo __('admin.item.itemdesc.comment_label') ?></div>
								<div class="comment-count js-comment-count" style="cursor:pointer"><?php echo __('admin.item.itemdesc.existed_comment') ?>(<?php if ($item_data != NULL) { echo count($item_data['notes']); } else { echo 0; } ?>)</div>
							</div>
							<input class="form-control" placeholder="<?php echo __('admin.item.itemdesc.comment_placeholder') ?>" name="item_note" />
						</div>
					<?php } ?>
				<div class="actions-container" style="margin: 60px 0 0 140px;">
					<?php if ($mode === 'read') { ?>
						<div class="btn-larger btn-white js-goto-current"><?php echo __('admin.item.itemdesc.back_to_current') ?></div>
					<?php } else { ?>
						<?php if ($permission > 0) { ?>
							<button type="button" class="btn-larger btn-blue js-action-public"><?php echo __('admin.common.label.save_publish') ?></button>
							<div class="btn-larger btn-gray-black js-action-tmp-save"><?php echo __('admin.common.label.temporary_save') ?></div>
						<?php } ?>
					<?php } ?>
					
					<button type="button" onclick="top.location='/admin/items'" class="btn-larger btn-white js-action-back"><?php echo __('admin.common.button.return_to_list') ?></button>
				</div>
				
			</div>
			<div id="msg-preview" class="col-md-4 preview-sticky">
				<?php if (count($history_records) > 1) { ?>
				<div class="history-record-container">
					<div class="history-record-title js-history-record"><?php echo $current_record ?></div>
					<div class="history-record-items js-history-pulldown">
						<?php foreach ($history_records as $record) { ?>
							<div class="history-record-item js-history-record-item" data-version="<?php echo $record['no'] ?>">
								<div class="history-record-item-header">
									<div class="history-record-item-info">
										<?php if ($record['translate_type'] === 1) echo __('admin.item.itemdesc.auto_translate') . ' - ' ?>
										<?php echo $record['upd_user'] ?> - <?php echo $record['upd_time'] ?>
									</div>
									<?php if ($record['no'] == 'current' || $record['no'] == 'temp') { 
										$version = $record['no'] == 'current' ? __('admin.item.itemdesc.version.current') : __('admin.item.itemdesc.version.temporary_save');
									?>
										<div class="history-record-item-version"><?php echo $version ?></div>
									<?php }  ?>
								</div>
								<div class="history-record-item-note">
									<?php echo htmlspecialchars($record['note']) ?? __('admin.item.itemdesc.no_comment') ?>
								</div>
							</div>
						<?php } ?>
					</div>
				</div>
				<?php } ?>
				<div class="talkappi-preview js-msg-preview" data-type="message" style="margin:0; flex-basis: 30%; max-width:320px;"></div>
			</div>
		</div>
	</div>
</div>
<!-- END PAGE CONTENT-->
<div id="item_div" style="display:none;"><?php echo $item_div ?></div>
<div id="item_class_cd" style="display:none;"><?php echo $item->class_cd ?></div>
<?php echo $skillbox ?>