<style>
    .page-content-header {
        display: block !important;
    }
    .nav>li>a {
        padding: 0 10px !important;
    }
    .menu-toggler.responsive-toggler {
        display: none !important;
    }
    @media (max-width: 767px) {
        .page-content-wrapper .page-content {
            padding: 20px 0px 10px 0px !important;
            overflow: visible !important;
        }
        .navbar-nav {
            margin: 0 -15px !important;
        }
    }
    .section {
        margin-bottom: 36px;
    }
    .setting-group {
        border: 1px solid #EBEDF2;
        border-radius: 4px;
        padding: 16px;
        margin-bottom: 16px;
        background-color: #fff;
    }
    .setting-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
    }
    .preview-label {
        color: #77797D;
        margin-bottom: 8px;
    }
    .setting-content {
        background-color: #F6F7F9;
        padding: 10px;
        border-radius: 4px;
        margin-bottom: 8px;
        border: none;
    }
    .button-group {
        display: flex;
        gap: 8px;
    }
    .button {
        padding: 5px 12px;
        border-radius: 4px;
        cursor: pointer;
        box-sizing: border-box;
    }        
    .edit-button {
        background-color: #fff;
        border: 1px solid #ccc;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #333 !important;
    }
    .edit-button:hover {
        color: #333;
    }
    .separate-border {
        border: 0.5px solid #EBEDF2;
        width: 100%;
    }   
</style>
<?php if ($show_bot_box == true) { ?>
<script>
const _show_bot_box = <?php echo $show_bot_box ?>
</script>
<?php } else { ?>
<script>
const _is_chatbot_use = <?php echo json_encode($is_chatbot_use); ?>;
</script>
<div class="content-container white" style="position:relative;">
    <?php if ($is_disaster_mode) { ?>
        <h2 class="disaster-mode-notice" style="color: red;">
        <span><?php echo $bot_name ?></span>
        <span style="display: inline-block; width: 1ch;"></span>
        <span><?php echo __('admin.disaster.label.disaster_mode'); ?></span>
        </h2>
    <?php } ?>
    <div class="section">
        <h2 class="section-title"><?php echo __('admin.disaster.label.display_setup'); ?></h2>
        <?php
            $display_value = (isset($setting['greeting']) && $setting['greeting'] === 'disaster') || (isset($setting['welcome']) && $setting['welcome'] === 'disaster') ? '1' : '0';
            $display_text = $display_value === '1' ? __('admin.disaster.label.displaying') : __('admin.disaster.label.not_displaying');
        ?>
        <span>
            <span style="padding-right:6px"><?php echo $display_text; ?></span>
            <span class="talkappi-switch js-change_display" data-name="display" data-value="<?php echo $display_value; ?>"></span>
        </span>
    </div>

    <!-- ボーダー -->
    <div class="separate-border"></div>

    <div class="section">
        <h2 class="section-title"><?php echo __('admin.disaster.label.bubble_setup'); ?></h2>
        <div class="setting-group">
            <div class="setting-item">
                <span><?php echo __('admin.disaster.label.bubble_disaster_mode'); ?></span>
            </div>
            <div class="preview-label"><?php echo __('admin.disaster.label.preview_content'); ?></div>
            <?php foreach ($talkappi_greeting_disaster_message as $message): ?>
                <div class="setting-content">
                    <?php echo htmlspecialchars($message['content'], ENT_QUOTES, 'UTF-8'); ?>
                </div>
            <?php endforeach; ?>
            <div class="button-group">
                <a class="button edit-button" href="/admin/disaster?id=<?php echo $talkappi_greeting_disaster_message[0]['msg_id']; ?>&lang_cd=ja">
                    <span class="icon-edit" style="margin-right: 8px;"></span>
                    <?php echo __('admin.disaster.label.edit'); ?>
                </a>
            </div>
        </div>
    </div>

    <!-- ボーダー -->
    <div class="separate-border"></div>

    <div class="section">
        <h2 class="section-title"><?php echo __('admin.disaster.label.welcome_setup'); ?></h2>
        <div class="setting-group">
            <div class="setting-item">
                <span><?php echo __('admin.disaster.label.bot_introduction_disaster_mode'); ?></span>
            </div>
            <div class="preview-label"><?php echo __('admin.disaster.label.preview_content'); ?></div>
            <?php foreach ($welcome_disaster_message as $message): ?>
                <div class="setting-content">
                    <?php echo htmlspecialchars($message['content'], ENT_QUOTES, 'UTF-8'); ?>
                </div>
            <?php endforeach; ?>
            <div class="button-group">
                <a class="button edit-button" href="/admin/disaster?id=<?php echo $welcome_disaster_message[0]['msg_id']; ?>&lang_cd=ja">
                    <span class="icon-edit" style="margin-right: 8px;"></span>
                    <?php echo __('admin.disaster.label.edit'); ?>
                </a>
            </div>
        </div>
    </div>
</div>
<?php } ?>