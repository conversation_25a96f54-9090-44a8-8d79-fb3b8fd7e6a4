<style type="text/css">
.blue.btn {
    color: #FFFFFF;
    background-color: #aaa;
    border-color: "";
}
</style>
<script type="text/javascript">
var service_status = <?php echo(json_encode($status))?>;
</script>
<div class="modal fade" id="talk_box" tabindex="-1" role="basic" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
				<h4 class="modal-title" id="talkbox_title">メッセージ送信</h4>
			</div>
			<div class="modal-body">
				<div id="content" style="display:none;"></div><div id="content_02" style="display:none;"></div>
				<div class="row" id="change_seat_label" style="padding: 10px;display:none;">		
					調整したい座席を選択ください。
				</div>
				<div class="row" id="change_seat_select" style="padding: 10px;display:none;">
				</div>
				<div class="row" style="padding: 10px;">
					<textarea id="talkbox_msg" class="form-control" rows="10" readonly></textarea>
				</div>
				<div class="row" style="padding: 10px;">
					上記は日本語で表示していますが、お客様の言語へ変換されて送信されます。
				</div>
				<div class="row" style="padding: 10px;display:none;">					
					<div class="btn-group" data-toggle="buttons">
					<?php
					if ($translate == '0') {
					?>
						<label id="translate-off" class="btn blue active">
						<input type="radio" name="translate" class="toggle" checked="checked" value="0"> 翻訳 OFF </label>
						<label id="translate-on" class="btn blue">
						<input type="radio" name="translate" class="toggle" value="1"> ON </label>
					<?php
						}
						else {
					?>
						<label id="translate-off" class="btn blue">
						<input type="radio" name="translate" class="toggle" value="0"> 翻訳 OFF </label>
						<label id="translate-on" class="btn blue active">
						<input type="radio" name="translate" class="toggle" checked="checked" value="1"> ON </label>
					<?php 		
						}
					?>
					</div>					
				</div>				
				<div class="row" style="padding: 10px;">
					<select id="msg_class" class="form-control" style="display:none;">
						<option value="">--- 業務 ---</option>
						<?php 
						foreach($phrase_types as $key=>$name) {
							echo("<option value=\"$key\">$name</option>");
						}
						?>
					</select>				
					<select id="msg_content" class="form-control" style="margin-top: 10px;">
					</select>
				</div>				
			</div>
			<div class="modal-footer">
				<button type="button" class="btn default" data-dismiss="modal">キャンセル</button>
				<button type="button" class="btn green" id="talkbox_send" sid="" act="" sns_type_cd="" lang_cd="" member_id="" member_name="" member_mail="" bot_name="">送信</button>
			</div>
		</div>
		<!-- /.modal-content -->
	</div>
	<!-- /.modal-dialog -->
</div>
