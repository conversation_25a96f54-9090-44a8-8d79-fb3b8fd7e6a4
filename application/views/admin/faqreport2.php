			<!-- BEGIN PAGE HEADER-->
			<!-- BEGIN PAGE HEAD -->
			<div class="page-head">
				<!-- BEGIN PAGE TITLE -->
				<div class="page-title">
					<h1>FAQページ利用状況レポート<small></small></h1>
				</div>
				<!-- END PAGE TITLE -->
			</div>
			<!-- END PAGE HEAD -->
			<!-- END PAGE HEADER-->
			<!-- BEGIN PAGE CONTENT-->
			<div class="row">
				<div class="col-md-12">
			        <!-- Page Content -->
			        <div id="page-wrapper">
			        <div class="portlet light">
					<?php echo $reportmenu ?> 
						<div class="portlet box">
							<div class="portlet-body">
								<div class="form-body">		
									<div class="form-group">
										<label class="control-label col-md-1"><?php echo __('admin.common.label.year') ?></label>
										<div class="col-md-2">
											<input name="select_year" id="select_year" value="<?php echo($select_year)?>" class="form-control form-control-inline input-small date-picker" size="16" data-date-format="yyyy"  type="text"/>
										</div>
										<label class="control-label col-md-1"><?php echo __('admin.common.label.display_zero') ?></label>
										<div class="col-md-2">
											<input type="checkbox" id="utf-8" name="zero_flg" <?php if ($zero=='0') echo('checked')?> value="1" class="make-switch" data-on-color="success" data-off-color="warning">
										</div>
									</div>
									<label class="control-label col-md-1"><?php echo __('admin.common.label.lang') ?></label>
									<div class="form-group">
										<div class="col-md-2">
											<?php echo Form::select('lang_cd', $lang, $lang_cd, array('id'=>'lang_cd','class'=>'form-control'))?>
										</div>
									</div>
									<div class="form-group">
										<label class="control-label col-md-1"><?php echo __('admin.common.label.user_flow') ?></label>
										<?php echo $botcond ?>
										<div class="col-md-1">
											<button type="button" id="searchButton" class="btn yellow" disabled="disabled">
											<i class="fa fa-search mr10"></i><?php echo __('admin.common.button.search') ?></button>
										</div>	
										<div id="scene_use_flg" style="display:none;">use_faq_flg</div>																	
										<div class="col-md-1" style="margin-left: 25px;">
											<button type="button" data-tableId="report2table" data-title="利用状況（月別）" class="btn green exportCsv">
												<?php echo __('admin.common.button.csv_export') ?>
											</button>
										</div>
									</div>
									<div>
										<?php echo __('admin.common.label.mobile_annotation') ?>
									</div>									
								</div>												
							<table class="table table-striped table-bordered table-hover js-data-table" id="report2table">
							<thead>
							<tr>
								<th rowspan="2" style="text-align: center; vertical-align: middle;"><?php echo __('admin.common.label.month') ?></th>
								<th rowspan="2" style="text-align: center; vertical-align: middle;"><?php echo __('admin.common.label.number_of_users') ?></th>
								<th colspan="6" style="border-bottom: 1px solid #ddd;"><?php echo __('admin.common.label.number_of_user_operations') ?></th>																					
							</tr>
							<tr>
								<th><?php echo __('admin.faqreportmenu.search_window_input') ?></th>
								<th><?php echo __('admin.faqreportmenu.keyword_selection') ?></th>
								<th><?php echo __('admin.faqreportmenu.category_selection') ?></th>
								<th><?php echo __('admin.faqreportmenu.question_selection') ?></th>	
								<th><?php echo __('admin.faqreportmenu.question_display') ?></th>
								<th><?php echo __('admin.faqreportmenu.related_question_display') ?></th>										
							</tr>							
							</thead>

							<tbody>
							<?php 
							for($i=1; $i<=12;$i++) {
								echo('<tr class="gradeX odd" role="row">');
								echo('<td>');
								echo($i);
								echo('</td>');
								echo('<td style="text-align:right">');
								if (array_key_exists($i, $logs_member)) {
									$total = 0;
									if (array_key_exists('_0', $logs_member[$i])) {
										$total = $total + $logs_member[$i]['_0'];
									}
									if (array_key_exists('_1', $logs_member[$i])) {
										$total = $total + $logs_member[$i]['_1'];
									}
									if (array_key_exists('_2', $logs_member[$i])) {
										$total = $total + $logs_member[$i]['_2'];
									}
									echo($total);
									if (array_key_exists('_1', $logs_member[$i])) {
										echo('(' . $logs_member[$i]['_1'] . ')');
									}
									else {
										if ($zero == '0') echo('(' . $zero . ')');
									}
								}
								else {
									if ($zero == '0') echo($zero . '(' . $zero . ')');
								}
								echo('</td>');
								if (array_key_exists($i, $logs)) {
									$search_div_list = ['1','4','2','3','5','6'];
									foreach($search_div_list as $div) {
										echo('<td style="text-align:right">');
										if (array_key_exists($div . '_0', $logs[$i]) || array_key_exists($div . '_1', $logs[$i]) || array_key_exists($div . '_2', $logs[$i])) {
											$total = 0;
											if (array_key_exists($div . '_0', $logs[$i])) {
												$total = $total + $logs[$i][$div . '_0'];
											}
											if (array_key_exists($div . '_2', $logs[$i])) {
												$total = $total + $logs[$i][$div . '_2'];
											}
											if (array_key_exists($div . '_1', $logs[$i])) {
												$total = $total + $logs[$i][$div . '_1'];
												$mobile = $logs[$i][$div . '_1'];
											}
											else {
												$mobile = 0;
											}
											echo($total);
											if ($mobile > 0) {
												echo('(' . $mobile . ')');
											}
											else {
												if ($zero == '0') echo('(' . $zero . ')');
											}
										}
										else {
											if ($zero == '0') echo($zero . '(' . $zero . ')');
										}
										echo('</td>');
									}
								}
								else {
									if ($zero == '0') {
										echo('<td style="text-align:right">0(0)</td><td style="text-align:right">0(0)</td><td style="text-align:right">0(0)</td><td style="text-align:right">0(0)</td><td style="text-align:right">0(0)</td><td style="text-align:right">0(0)</td>');
									}
									else {
										echo('<td></td><td></td><td></td><td></td><td></td><td></td>');
									}
								}
								echo('</tr>');
							}
							?>

							</tbody>
							</table>
							</div>
						</div>
					</div>
			        </div>
			        <!-- /#page-wrapper -->					
				</div>
			</div>
			<!-- END PAGE CONTENT-->
