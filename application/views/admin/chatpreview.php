<div class="mobile-preview">
	<header class="header-container">
		<h4 style="margin: 0 auto 0 0;">プレビュー(スマホのみ)</h4>
	</header>
	<div class="main-container js-mobile-preview-main-container">
		<?php 
		$model = Model::factory('adminmodel');
		if (!isset($msg_desc_lists)) {
            $msg_desc_lists[] = $msg_desc_list;
        }
		foreach($msg_desc_lists as $msg_desc_list) {
			$image_postfix = '?' . time();
			if (($msg_type_cd == 'btn' || $msg_type_cd == 'lst'  || $msg_type_cd == 'mnu') && count($msg_desc_list) > 0) {
				$message = '<div class="answer">';
				$message = $message . nl2br($msg_desc_list[0]['title']) . '<br/>';
				$message = $message . '<div class="button-container">';
				foreach ($msg_desc_list as $item) {
					if ($item['content'] == '') continue;
					if (strpos($item['content'], "BTN_") === 0) {
						$item['content'] = $button_list[$item['content']];
					}
					$item_type = "postback";
					if (isset($item['type'])) {
						$item_type = $item['type'];
					}
					$message = $message . '<a href="javascript:;" class="webchat-list" type="postback" lang_cd="' . $item['lang_cd'] . '"' . ' no="' . $item['no'] . '" title="' . $item['content']. '" content="' . $item['content'] . '">';
					if ($msg_type_cd == 'lst') {
						$message = $message . '<span class="button">' . nl2br($item['content']) . '</span></a>';
						$message = $message .'<br/>';
					}
					else {
						$message = $message .'<span class="button">' . nl2br($item['content']) . '</span></a>';
					}
				}
				$message = $message . '</div></div>';
				echo($message);
			}
			else if ($msg_type_cd == 'txt' || $msg_type_cd == 'mal' || $msg_type_cd == 'tpl') {
				foreach ($msg_desc_list as $item) {
					if ($item['content'] != '') {
						echo('<div class="answer">');
						echo($model->format_url($item['content']));
						echo('</div>');
					}
				}
			}
			else if ($msg_type_cd == 'img') {
				foreach ($msg_desc_list as $item) {
					if (strpos($item['msg_image'], 'http') === 0) {
						$image_url = $item['msg_image'];
					}
					else {
						$image_url = $item['url'];
					}
					echo('<div class="answer">');
					echo(nl2br($item['title']) . '<br>');
					if (isset($s3_url)) {
						$image_title = 'S3写真';
						$image_url = str_replace('https://cdn.talkappi.com/', 'https://s3-ap-northeast-1.amazonaws.com/contents.talkappi.com/', $image_url);
					}
					else {
						$image_title = 'CDN写真';
					}
					echo('<a href="javascript:void();" class="cdn_image"><img title="' .  $image_title . '" no="' . $item['no'] . '" class="talkappi-img-circle" style="max-width:200px;" alt="" src="' . $image_url . $image_postfix . '"></a>');
					echo('</div>');
				}
			}
			else if ($msg_type_cd == 'mov') {
				foreach ($msg_desc_list as $item) {
					echo('<div class="answer">');
					echo('<video controls="controls" width="200px;" src="' . $item['msg_image'] . '" />');
					echo('</div>');
				}
			}
			else if ($msg_type_cd == 'rcm') {
				foreach ($msg_desc_list as $item) {						
					if (isset($s3_url)) {
						$image_title = 'S3写真';
						$item['msg_image'] = str_replace('https://cdn.talkappi.com/', 'https://s3-ap-northeast-1.amazonaws.com/contents.talkappi.com/', $item['msg_image']);
					}
					else {
						$image_title = 'CDN写真';
					}							
					echo('<a href="javascript:void();" class="cdn_image"><img class="image fileimage" style="width:200px;" title="' .  $image_title . '" src="' . $item['msg_image'] . $image_postfix . '" /></a>');
					//echo('<span class="body" style="margin-bottom:10px;">' . '<img class="talkappi-img-circle" style="width:200px;" alt="" src="' . $item['msg_image'] . $image_postfix . '"></span>');
					break;
				}
			}
			else if ($msg_type_cd == 'faq') {
				$has_skill = false;
				$has_summary = false;
				$line = '';
				for($i=1; $i<=$max_no; $i++) {
					if ($faq['answer' . $i . '_type_cd'] == 'img' && $faq['user_answer' . $i] == '') {
						echo('<span class="body" style="margin-bottom:10px;"><img no="' . $i . '" class="talkappi-img-circle" style="max-width:200px;" alt="" src=""></span>');
					}
					else if ($faq['user_answer' . $i] != '') {
						if ($faq['answer' . $i . '_summary'] != '') {
							if ($faq['answer_summary_title'] != '') {
								echo('<span class="body" style="text-align:left;">' . $faq['answer_summary_title'] . '<br>');
								$faq['answer_summary_title'] = '';
								$has_summary = true;
							}
							echo('<a class="webchat-list" title="' . $faq['answer' . $i . '_summary'] . '" href="javascript:;" type="postback"');
							echo(' content="INQUIRY_DETAIL -- ' . $intent_cd . ' -- ' . $i . ' -- ' . $sub_intent_cd . ' -- ' . $area_cd . '"><div class="badge badge-success button-item">' . $faq['answer' . $i . '_summary'] . '</div></a>');
						}
						if ($faq['answer' . $i . '_type_cd'] == 'car') {
							$has_skill = true;
						}
						else {
							if (!$has_summary) {
								if ($faq['answer' . $i . '_type_cd'] == 'txt' || $faq['answer' . $i . '_type_cd'] == 'url') {
									echo('<span class="body" style="margin-bottom:10px;">');
									$message = $model->format_url($faq['user_answer' . $i]);
									echo($message);
									echo('</span>');
								}
								if ($faq['answer' . $i . '_type_cd'] == 'img') {
									echo('<span class="body" style="margin-bottom:10px;"><a href="javascript:void();" class="cdn_image"><img no="' . $i . '" class="talkappi-img-circle" style="max-width:200px;" alt="" src="' . $faq['user_answer' . $i] . $image_postfix . '"></a></span>');
								}
								if ($faq['answer' . $i . '_type_cd'] == 'mov') {
									echo('<span class="body" style="margin-bottom:10px;"><video controls="controls" width="200px;" src="' . $faq['user_answer' . $i] . '" /></span>');
								}
								echo('<br />');
							}
						}
					}
				}
				if ($has_summary) echo('</span>');
			}
			else if ($msg_type_cd == 'car') {
				echo('<div class="xscroll-wrapper">');
				echo('<div class="card">');
				if (isset($item)) {
					if (!isset($show_image) || $show_image == 1) {
						if ($item['image'] == '') {
							echo('<a href="javascript:void(0);" class="cdn_image"><img class="image fileimage" title="CDN写真" src="https://cdn.talkappi.com/0/common/no_pic.jpg" /></a>');
						}
						else {
							if (isset($s3_url)) {
								$image_title = 'S3写真';
								$item['image'] = str_replace('https://cdn.talkappi.com/', 'https://s3-ap-northeast-1.amazonaws.com/contents.talkappi.com/', $item['image']);
							}
							else {
								$image_title = 'CDN写真';
							}
							echo('<a href="javascript:void(0);" class="cdn_image"><img class="image fileimage" title="' .  $image_title . '" src="' . $item['image'] . $image_postfix . '" /></a>');
						}
					}
					echo('<div class="item-caption">' . nl2br($item['title']) . '</div>');
					echo('<div class="item-description">' . $model->format_url($item['content']) . '</div>');
					echo('<div class="item-button-horizontal">');
					if (array_key_exists('buttons', $item))
					foreach ($item['buttons'] as $button) {
						if (!array_key_exists('url', $button) || $button['url'] == '') $button['url'] = 'javascript:void(0);';
						if (strpos($button['title'], "BTN_") === 0) {
							$button['title'] = $button_list[$button['title']];
						}
						echo('<a class="button" href="' . $button['url'] . '" target="blank">' . $button['title'] . '</a>');
					}
					echo('</div>');
				}
				echo('</div>');
				echo('</div>');
			}
        }
		?>
	</div>
	<footer class="footer-container">
		<?php 
		if (isset($preview) && $preview == 1) {
			if (isset($public)) {
				echo('こちらはプレビューです。<br />「行き方」ボタンはご入力住所により自動生成されます。');
			}
			else {
				echo('※プレビュー結果です。');
			}
		}
		else if (!isset($public) && ($msg_type_cd == 'img' || $msg_type_cd == 'mov' || $msg_type_cd == 'rcm' || $msg_type_cd == 'car')) {
			echo('※CDNへの反映は最大24時間かかります<br/>更新後即時の確認は写真をクリックしてください。');
		}?>
	</footer>
</div>
