			<!-- B<PERSON>IN PAGE HEADER-->
			<!-- BEGIN PAGE HEAD -->
			<div class="page-head">
				<!-- BEGIN PAGE TITLE -->
				<div class="page-title">
					<h1>利用状況レポート<small></small></h1>
				</div>
				<!-- END PAGE TITLE -->
			</div>
			<!-- END PAGE HEAD -->
			<!-- END PAGE HEADER-->
			<!-- BEGIN PAGE CONTENT-->
			<div class="row">
				<div class="col-md-12">
			        <!-- Page Content -->
			        <div id="page-wrapper">
			        <div class="portlet chatbot light">			        
					<?php echo $reportmenu ?>
						<div class="portlet chatbot box">
							<div class="portlet-body">
								<div class="form-body">		
									<div class="form-group">
										<label class="control-label col-md-1" style="padding-left:15px; padding-right:15px;"><?php echo __('admin.common.label.period') ?></label>
										<div class="col-md-3" style="width: 265px;">
											<input name="start_date" id="start_date" value="<?php echo($start_date)?>" style="float:left;" class="form-control form-control-inline input-small date-picker" size="16" data-date-format="yyyy-mm-dd"  type="text"/>
											<input name="end_date" id="end_date" value="<?php echo($end_date)?>" style="float:left; margin-left:10px;" class="form-control form-control-inline input-small date-picker" size="16" data-date-format="yyyy-mm-dd"  type="text"/>
										</div>
										<label class="control-label col-md-3" style="width: 112px;"><?php echo __('admin.common.label.number_of_repeaters_and_unasked questions') ?></label>
										<div class="col-md-2">
											<input type="checkbox" id="utf-8" name="repeat_flg" <?php if ($repeat_flg==1) echo('checked')?> value="1" class="make-switch" data-on-color="success" data-off-color="warning">
										</div>
										<label class="control-label col-md-1" style="width: 100px;"><?php echo __('admin.common.label.display_zero') ?></label>
										<div class="col-md-2">
											<input type="checkbox" id="utf-8" name="zero_flg" <?php if ($zero=='0') echo('checked')?> value="1" class="make-switch" data-on-color="success" data-off-color="warning">
										</div>										
									</div>
									<div class="form-group">
										<label class="control-label col-md-1" style="padding-left:15px; padding-right:15px;"><?php echo __('admin.common.label.user_flow') ?></label>
										<?php echo $botcond ?>	
										<div class="col-md-1">
											<button type="button" id="searchButton" class="btn yellow" disabled="disabled">
											<i class="fa fa-search mr10"></i><?php echo __('admin.common.button.search') ?></button>
										</div>											
									</div>
								</div>							

							<table class="table table-striped table-bordered table-hover" id="sample_3">
							<thead>
							<tr>
								<th style="width:200px;text-align:center;font-size:10px;"><?php echo __('admin.common.label.new_user') ?><?php if ($repeat_flg==1) echo __('admin.common.label.repeaters_unasked_questions') ?>	 
								</th>	
								<?php 
								$display_lang = explode(',', $_bot->lang_cd);
								$sns_array = explode(',', $_bot->sns_cd);
								foreach($sns_array as $sns_type_cd) {
									echo('<th style="width:200px;text-align:center">');
									echo($_codes['08'][$sns_type_cd]);
									echo('</th>');
								}
								?>							
								<th style="width:200px;text-align:center">
									 <?php echo __('admin.common.label.total') . '(' . __('admin.common.label.ratio')  . ')' ?>
								</th>																											
							</tr>
							</thead>
							<?php
							$sns_mnt = [];
							$sns_leave_mnt = [];
							$sns_repeat_mnt = [];
							$show_total = $total;
							$show_leave_total = 0;
							$show_repeat_total = 0;
							if ($total == 0) $total = 1;
							foreach($display_lang as $k) {
								echo('<tr>');
								echo('<td style="text-align:center;font-weight:bold;">' . $lang_array[$k] . '</td>');
								$sum = 0;
								$leave_sum = 0;
								$repeat_sum = 0;
								foreach($sns_array as $sns_type_cd) {
									echo('<td style="text-align:center;">');
									if (array_key_exists($sns_type_cd . $k, $month_result)) {
										echo($month_result[$sns_type_cd . $k]);
										$sum = $sum + $month_result[$sns_type_cd . $k];
										if (array_key_exists($sns_type_cd, $sns_mnt)) {
											$sns_mnt[$sns_type_cd] = $sns_mnt[$sns_type_cd] + $month_result[$sns_type_cd . $k];
										}
										else {
											$sns_mnt[$sns_type_cd] = $month_result[$sns_type_cd . $k];
										}
									}
									else {
										$month_result[$sns_type_cd . $k] = 0;
										echo($zero);
									}
									if ($repeat_flg == 1) {
										echo('[');
										if (array_key_exists($sns_type_cd . $k, $month_repeat_result) && $month_repeat_result[$sns_type_cd . $k] > 0) {
											$repeat_sum =  $repeat_sum + $month_repeat_result[$sns_type_cd . $k];
											echo($month_repeat_result[$sns_type_cd . $k]);
											if (array_key_exists($sns_type_cd, $sns_repeat_mnt)) {
												$sns_repeat_mnt[$sns_type_cd] = $sns_repeat_mnt[$sns_type_cd] + $month_repeat_result[$sns_type_cd . $k];
											}
											else {
												$sns_repeat_mnt[$sns_type_cd] = $month_repeat_result[$sns_type_cd . $k];
											}
											$show_repeat_total = $show_repeat_total + $month_repeat_result[$sns_type_cd . $k];
										}
										else {
											echo($zero);
										}
										echo('・');
										if (array_key_exists($sns_type_cd . $k, $leave_result)) {
											$show_leave_total = $show_leave_total + $leave_result[$sns_type_cd . $k];
											if ($leave_result[$sns_type_cd . $k] > 0) {
												echo($leave_result[$sns_type_cd . $k]);
											}
											else {
												echo($zero);
											}
											$leave_sum = $leave_sum + $leave_result[$sns_type_cd . $k];
											if (array_key_exists($sns_type_cd, $sns_leave_mnt)) {
												$sns_leave_mnt[$sns_type_cd] = $sns_leave_mnt[$sns_type_cd] + $leave_result[$sns_type_cd . $k];
											}
											else {
												$sns_leave_mnt[$sns_type_cd] = $leave_result[$sns_type_cd . $k];
											}
										}
										else {
											echo($zero);
										}
										echo(']');
									}
									echo('</td>');
								}
								echo('<td style="text-align:center;font-weight:bold;">' . $sum . ' (' . round($sum/$total * 100, 2) . '%)');
								if ($repeat_flg == 1) {
									echo('[');
									if ($repeat_sum <= 0) {
										echo($zero);
									}
									else {
										echo($repeat_sum);
									}
									echo('・');
									if ($leave_sum <= 0) {
										echo($zero);
									}
									else {
										echo($leave_sum);
									}
									echo(']');
								}
								echo('</td></tr>');
							}
							echo('<tr>');
							echo('<td></td>');
							foreach($sns_array as $sns_type_cd) {
								echo('<td style="text-align:center;font-weight:bold;">');
								if (array_key_exists($sns_type_cd, $sns_mnt)) {
									echo($sns_mnt[$sns_type_cd] . ' (' . round($sns_mnt[$sns_type_cd] /$total * 100, 2) . '%)');
								}
								else {
									echo($zero);
								}
								if ($repeat_flg == 1) {
									echo('[');
									if (array_key_exists($sns_type_cd, $sns_repeat_mnt) && $sns_repeat_mnt[$sns_type_cd] > 0) {
										echo($sns_repeat_mnt[$sns_type_cd]);
									}
									else {
										echo($zero);
									}
									echo('・');
									if (array_key_exists($sns_type_cd, $sns_leave_mnt) && $sns_leave_mnt[$sns_type_cd] > 0) {
										echo($sns_leave_mnt[$sns_type_cd]);
									}
									else {
										echo($zero);
									}
									echo(']');
								}
								echo('</td>');
							}
							echo('<td style="text-align:center;font-weight:bold;font-size:16px;">' . $show_total);
							if ($repeat_flg == 1) {
								echo('[');
								if ($show_repeat_total > 0) {
									echo($show_repeat_total);
								}
								else {
									echo($zero);
								}
								echo('・');
								if ($show_leave_total > 0) {
									echo($show_leave_total);
								}
								else {
									echo($zero);
								}
								echo(']');
							}
							
							echo('</td></tr>');
							?>																
							</tbody>
							</table>
							
							<table class="table table-striped table-bordered table-hover" id="sample_2" style="width:auto;">
							<thead>
							<tr>
								<th style="width:70px;text-align:center">
								<button type="button" id="csvButton" data-tableId="sample_2" data-title="チャンネル別ユーザ数" class="btn green exportCsv"><?php echo __('admin.common.button.csv_export') ?></button>
								</th>								
							<?php
							foreach($sns_array as $sns_type_cd) {
								echo('<th style="width:200px;text-align:center" colspan="' . count($display_lang) . '">');
								echo($_codes['08'][$sns_type_cd]);
								echo('</th>');
							}
							?>
							<th style="width:200px;text-align:center"><?php echo __('admin.common.label.total') ?></th>																									
							</tr>						
							</thead>							
							<tbody>	
							<tr>
							<?php 
							echo('<tr class="gradeX odd" role="row">');
							echo('<td>');
							echo('</td>');
							for($i=0; $i<count($sns_array); $i++) {
								foreach($display_lang as $k) {
									echo('<th style="width:40px;text-align:center">');
									echo($lang_array[$k]);
									echo('</th>');
							}
							}?>
							<th style="text-align:center;"><small><?php echo __('admin.common.label.new_user') ?><?php if ($repeat_flg==1) echo __('admin.common.label.repeaters_unasked_questions') ?></small></th>																																		
							</tr>	
							<?php 
							$sum_new_members = [];
							$sum_repeaters = [];
							$sum_leave_members = [];
							foreach($sns_array as $sns_type_cd) {
								foreach($display_lang as $k) {
									$sum_new_members[$sns_type_cd . $k] = 0;
									$sum_repeaters[$sns_type_cd . $k] = 0;
									$sum_leave_members[$sns_type_cd . $k] = 0;
								}
							}
							for($i=0; ;$i++) {
								$cur_day = date("Y-m-d",strtotime("+" . $i ." day",strtotime($start_date)));
								if ($cur_day > $end_date) break;
								echo('<tr class="gradeX odd" role="row" style="text-align:center;">');
								echo('<td>');
								echo(substr($cur_day, 2));
								echo('</td>');
								$total_day = 0;
								$total_repeater_day = 0;
								$total_leave_day = 0;
								$total_repeater = 0;

								foreach($sns_array as $sns_type_cd) {
									foreach($display_lang as $k) {
										echo('<td>');
										$key = $cur_day . $sns_type_cd . $k;
										if (array_key_exists($key, $repeat) || array_key_exists($key, $day_result)) {
											if (array_key_exists($key, $day_result)) {
												echo($day_result[$key]);
												$sum_new_members[$sns_type_cd . $k] = $sum_new_members[$sns_type_cd . $k] + $day_result[$key];
												$total_day = $total_day + $day_result[$key];
												$result_new = $day_result[$key]; 
											}
											else {
												echo($zero);
												$result_new = 0;
											}
											if ($repeat_flg == 1) {
												echo('[');
												if (array_key_exists($key, $repeat)) {
													$repeat_temp = $repeat[$key] - $result_new;
													if ($repeat_temp > 0) {
														echo($repeat_temp);
														$sum_repeaters[$sns_type_cd . $k] = $sum_repeaters[$sns_type_cd . $k] + $repeat_temp;
														$total_repeater_day = $total_repeater_day + $repeat_temp;
													}
													else {
														echo($zero);
													}
												}
												else {
													echo($zero);
												}
												echo('・');
												if (array_key_exists($key, $leave_result_day)) {
													if ($leave_result_day[$key] <= 0) {
														echo($zero);
													}
													else {
														echo($leave_result_day[$key]);
														$sum_leave_members[$sns_type_cd . $k] = $sum_leave_members[$sns_type_cd . $k] + $leave_result_day[$key];
													}
													$total_leave_day = $total_leave_day + $leave_result_day[$key];
												}
												else {
													echo($zero);
												}
												echo(']');		
											}
										}
										else {
											if ($zero == '0') {
												echo('0[0・0]');	
											}
										}
										echo('</td>');
									}
								}
								echo('<td>');
								if ($cur_day <= date('Y-m-d') && 
										($total_day > 0 || $total_repeater_day >0 || $total_leave_day >0)) {
									if ($total_day > 0) {
										echo($total_day);
									}
									else {
										echo($zero);
									}
									if ($repeat_flg == 1) {
										if ($total_repeater_day > 0) {
											echo(' [' . $total_repeater_day . '・');
										}
										else {
											echo(' [' . $zero . '・');
										}
										if ($total_leave_day > 0) {
											echo($total_leave_day . ']');
										}
										else {
											echo($zero . ']');
										}
									}
								}
								else {
									if ($zero == '0') {
										echo('0[0・0]');
									}
								}
								echo('</td>');
								echo('</tr>');
							}
							echo('<tr class="gradeX odd" role="row" style="text-align:center;font-weight:bold;">');
							echo('<td>' . __('admin.common.label.total') . '</td>');
							$total_sum_members = 0;
							$total_sum_repeaters = 0;
							$total_sum_leave = 0;
							
							foreach($sns_array as $sns_type_cd) {
								foreach($display_lang as $k) {
									$key = $sns_type_cd . $k;
									echo('<td>');
									if ($sum_new_members[$key] > 0) {
										echo($sum_new_members[$key]);
										$total_sum_members = $total_sum_members + $sum_new_members[$key];
									}
									else {
										echo($zero);
									}
									if ($repeat_flg == 1) {
									echo('[');
										if ($sum_repeaters[$key] > 0) {
											echo($sum_repeaters[$key]);
											$total_sum_repeaters = $total_sum_repeaters + $sum_repeaters[$key];
										}
										else {
											echo($zero);
										}
										echo('・');
										if ($sum_leave_members[$key] > 0) {
											echo($sum_leave_members[$key]);
											$total_sum_leave = $total_sum_leave + $sum_leave_members[$key];
										}
										else {
											echo($zero);
										}
										echo(']');
									}
									echo('</td>');
								}
							}
							
							echo('<td>' . $total_sum_members);
							if ($repeat_flg == 1) {
								echo('[' . $total_sum_repeaters . '・' . $total_sum_leave . ']');
							}
							echo('</td>');
							echo('</tr>');
							?>
							</tbody>
							</table>							
													
							</div>
						</div>
					</div>
			        </div>
			        <!-- /#page-wrapper -->					
				</div>
			</div>
			<!-- END PAGE CONTENT-->
