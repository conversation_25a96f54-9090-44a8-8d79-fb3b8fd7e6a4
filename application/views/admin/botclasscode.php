			<!-- BEGIN PAGE HEADER-->
			<!-- BEGIN PAGE HEAD -->
			<div class="page-head">
				<!-- BEGIN PAGE TITLE -->
				<div class="page-title">
					<h1><?php echo($_active_menu_name)?><small></small></h1>
				</div>
				<!-- <PERSON>ND PAGE TITLE -->
			</div>
			<!-- END PAGE HEAD -->
			<!-- END PAGE HEADER-->
			<!-- BEGIN PAGE CONTENT-->
			        <!-- Page Content -->
			        <div id="page-wrapper">
						<?php 
							if (strlen($_active_menu) > 4) {
								$tab_menu = View::factory ('admin/menutab');
								echo($tab_menu);
							}
						?>
					<div class="edit-container">
						<div class="settings-container">
							<input type="hidden" name="code_div_sel" id="code_div_sel" value="<?php if ($post != NULL) echo($post['code_div_sel'])?>" />
							<input type="hidden" name="code_div_copy" id="code_div_copy" value="<?php if ($post != NULL) echo($post['code_div_copy'])?>" />
							<input type="hidden" id="isedit" value="<?php echo($isEdit)?>" />
							<input type="hidden" name="message" id="message" value="<?php echo($message)?>" />
							<table class="table table-striped table-bordered table-hover js-data-table">
							<thead>
							<tr>
								<th style="width: 160px;">CODE DIV</th>
								<th>
									名前
								</th>
								<th style="width: 200px;">
									種類
								</th>
								<th style="width: 200px;">
									 編集操作
								</th>
							</tr>
							</thead>

							<tbody>
							<?php
								foreach ($data as $item) {
							?>
							<tr class="gradeX odd" role="row">
								<td>
									 <?php echo($item->code_div)?>
								</td>
								<td>
								<?php if ($item->div_class === '99' ) { ?>
									<a href="/admin/botclasscodeedit?id=<?php echo($item->code_div)?>"><?php echo($item->title)?></a>
								<?php } else { ?>
									<a href="/admin/botclasscodeeditnew?id=<?php echo($item->code_div)?>"><?php echo($item->title)?></a>
								<?php } ?>
								</td>
								<td>
									<?php echo($_codes['30'][$item->div_class])?>
								</td>
								<td>
									<button type="button" class="btn blue action" act="03" code_div="<?php echo($item->code_div)?>">コピー</button>										
								<?php if ($_bot_id == 0 || $_bot_id == $item->bot_id) { ?>
									<button type="button" class="btn yellow action" act="01" code_div="<?php echo($item->code_div)?>">編集</button>							
									<button type="button" class="btn red action" act="02" code_div="<?php echo($item->code_div)?>">削除</button>
								<?php }?>
								</td>
							</tr>
							<?php } ?>
							</tbody>
							</table>
							<br/>
							<button type="button" class="btn green-meadow action"  act="00"><i class="fa fa-file mr10"></i>新規</button>
							<?php if ($isEdit) { ?>
							<h4 class="form-section"></h4>
								<div class="form-body">
									<div class="form-group">
										<label class="control-label col-md-2">CODE DIV</label>
										<div class="col-md-3">
											<input name="code_div" id="code_div" <?php if ($post['code_div_sel']!='') echo(' readonly'); ?> type="text" class="form-control" value="<?php if ($post != NULL) echo($post['code_div'])?>">
										</div>
									</div>								
									<div class="form-group">
										<label class="control-label col-md-2">名前</label>
										<div class="col-md-3">
											<input name="title" id="title" type="text" class="form-control" value="<?php if ($post != NULL) echo($post['title'])?>">
										</div>
									</div>
									<div class="form-group">
										<label class="control-label col-md-2">種類</label>
										<div class="col-md-3">
												<?php 
												$code_array = $_codes['30'];
												if ($_bot_id != 0) {
													unset($code_array['90']);
													unset($code_array['99']);
												}
												echo Form::select('div_class', $code_array, $post['div_class'], array('id'=>'div_class','class'=>'form-control'))?>
										</div>
									</div>
									<div class="form-group">
										<label class="control-label col-md-2">説明</label>
										<div class="col-md-8">
											<div class="input-icon right">
												<input name="description" id="description" type="text" class="form-control" placeholder="" value="<?php if ($post != NULL) echo($post['description'])?>">
											</div>
										</div>
									</div>
								</div>
								<div class="form-actions">
									<div class="row">
										<div class="col-md-offset-2 col-md-9">
											<button type="button" id="saveBaseButton" class="btn blue mr10">
											<i class="fa fa-save mr10"></i>保存</button>
											<button type="reset" class="btn gray">リセット</button>
										</div>
									</div>
								</div>
								<?php } ?>
							</div>
						</div>
			        </div>
			        <!-- /#page-wrapper -->
