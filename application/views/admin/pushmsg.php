			<!-- BEGIN PAGE HEADER-->
			<!-- BEGIN PAGE HEAD -->			
			<div class="page-head">
				<!-- BEGIN PAGE TITLE -->
				<div class="page-title">
					<h1>プッシュ配信<small></small></h1>
				</div>
				<!-- END PAGE TITLE -->
			</div>
			<!-- END PAGE HEAD -->
			<!-- END PAGE HEADER-->
			<!-- BEGIN PAGE CONTENT-->
			<div class="row">
				<div class="col-md-12">
			        <!-- Page Content -->
			        <div id="page-wrapper">
						<div class="top-nav">
							<ul>
								<li>
									<a href="/admin/pushmsgmember">
									配信対象者選択(<?php echo($member_count)?>)</a>
								</li>
								<li class="active">
									<a href="/admin/pushmsg">
									配信コンテンツ選択</a>
								</li>
								<li>
									<a href="/admin/pushmsgmembertasks?type=02">
									指定対象者配信一覧</a>
								</li>
							</ul>
						</div>
						<div class="edit-container">
							<div class="settings-container">
							<input type="hidden" id="act" name="act" value="" />
							<input type="hidden" name="msg_cd" id="msg_cd" value="" />
							<table class="table table-striped table-bordered table-hover js-data-table">
							<thead>
							<tr>
								<th>
									コンテンツコード
								</th>
								<th>
									コンテンツ名称
								</th>
								<th>
									 タイプ
								</th>
								<th style="display: none;">
									 分類
								</th>
								<th>
									 配信
								</th>

							</tr>
							</thead>

							<tbody>
							<?php
								foreach ($msgs as $msg) {
							?>
							<tr class="gradeX odd" role="row">
								<td class="sorting_1">
									 <?php echo($msg->msg_cd)?>
								</td>
								<td>
									<a href="/admin/msgnew?id=<?php echo($msg->msg_id)?>"><?php echo($msg->msg_name)?></a>
								</td>
								<td>
									<?php echo($msg_type[$msg->msg_type_cd])?>
								</td>
								<td style="display: none;">
									<?php if (array_key_exists($msg->msg_class_cd, $msg_class)) echo($msg_class[$msg->msg_class_cd])?>
								</td>
								<td style="text-align: center;">
									<button type="button" class="btn green-meadow action pushnow" sid="<?php echo($msg->msg_cd)?>">即時配信</button>
								</td>
							</tr>
							<?php } ?>
							</tbody>
							</table>
							<div class="form-group" style="display:none;">
								<label class="control-label col-md-2">複数メッセージ選択</label>
								<div class="col-md-8">
									<input type="hidden" name="msg_cd_list" id="select2_sample5" class="form-control select2" value="">
								</div>
							</div>
							<div class="form-group" style="display:none;">
								<div class="col-md-offset-2 col-md-9">
									<button type="button" class="btn green-meadow action pushallnow">すぐ送信</button>
									<button type="button" class="btn yellow action pushalltask">送信予約</button>
								</div>
							</div>											
							</div>
						</div>
			        </div>
			        <!-- /#page-wrapper -->
				</div>
			</div>
			<!-- END PAGE CONTENT-->
