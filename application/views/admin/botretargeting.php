<style type="text/css">
.btn.default:active, .btn.default.active {
  background-image: none;
  background-color: #45b6af;
  color: #fff;
}
</style>
			<!-- BEGIN PAGE HEADER-->
			<!-- BEGIN PAGE HEAD -->
			<div class="page-head">
				<!-- BEGIN PAGE TITLE -->
				<div class="page-title">
					<h1>リターゲティング<small></small></h1>
				</div>
				<!-- END PAGE TITLE -->
			</div>
			<!-- END PAGE HEAD -->
			<!-- END PAGE HEADER-->
			<!-- BEGIN PAGE CONTENT-->
			        <!-- Page Content -->
			        <div id="page-wrapper">
				        <div class="portlet light">
							<div class="portlet box">
								<div class="portlet-body">
									<div class="row">
									<div class="form-group">
										<div class="row">
											<label class="control-label col-md-4">離脱箇所</label>
											<label class="control-label col-md-2" style="text-align: left;">リターゲティング時間（秒）</label>
											<label class="control-label col-md-5" style="text-align: left;">リターゲティング内容（メッセージコード）</label>
										</div>
									</div>
								<!-- BEGIN FORM-->
									<?php
									foreach($action_status as $orm)
									{
										echo('<div class="form-group">');
										echo('<div class="row">');
										echo('<label class="control-label col-md-4">' . $orm->description_ja . '</label>');
										echo('<div class="col-md-2">');
										$key = $orm->action . '#' . $orm->status;
										if (array_key_exists($key, $retargeting)) {
											$retarget_eclipse_time = $retargeting[$key]['retarget_eclipse_time'];
											$message_cd = $retargeting[$key]['message_cd'];
										}
										else {
											$retarget_eclipse_time = '';
											$message_cd = '';
										}
										echo('<input type="text" class="form-control" name="' . $key . '#retarget_eclipse_time' . '" value="' . $retarget_eclipse_time . '" >');
										echo('</div>');
										echo('<div class="col-md-5">');
										echo('<input type="text" class="form-control" name="' . $key . '#message_cd' . '" value="' . $message_cd . '" >');
										echo('</div>');
										echo('</div>');
										echo('</div>');
									}
									?>
									<div class="form-actions">
										<div class="row">
											<div class="col-md-offset-4 col-md-6">
												<button type="submit" id="saveButton" class="btn green mr10">
												<i class="fa fa-save mr10"></i>保存</button>
												<button type="reset" id="resetButton" class="btn gray">リセット</button>
											</div>
										</div>
									</div>
							</div>
							</div>
							</div>
						</div>
			        </div>
			        <!-- /#page-wrapper -->
			<!-- END PAGE CONTENT-->


