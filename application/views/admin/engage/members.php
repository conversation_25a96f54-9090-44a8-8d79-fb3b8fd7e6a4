<style>
td {
  position: relative;
}
.align-bottom {
  position: absolute;
  bottom: 5px;
  width: 100%;
}
a {
  text-decoration: underline;
}
.unregistered {
  color: grey;
  text-decoration: underline;
}
.support-history-items {
  display: flex;
  flex-direction: column;
  gap: 4px;
}
.support-item {
  border-radius: 4px;
  padding: 4px;
  background-color: #EBEDF2;
  width: 100%;
}
.additional-info-td {
  position: relative;
}
.additional-info-content {
  padding-bottom: 15px;
}
.additional-info-link {
  position: absolute;
  bottom: 5px;
  width: 100%;
}
.filter-modal {
  overflow: hidden !important;
}
</style>

<input name="lang_cd" id="lang_cd" hidden value="<?php echo($post['lang_cd'])?>">
<input name="page" id="page" hidden value="<?php echo($page)?>">
<input name='filter_condition' id='filter_condition' hidden value="<?php echo htmlspecialchars(json_encode($filter), ENT_QUOTES, 'UTF-8') ?>"/>
<input type="hidden" id="member_filter_condition" name="member_filter_condition" value="<?php echo htmlspecialchars(json_encode($filterCondition), ENT_QUOTES, 'UTF-8') ?>" />

<div class="flex" style="display:flex; flex-direction: column; gap:10px; justify-content: center; flex-wrap:wrap; background-color: #F6F7F9; padding: 12px">
    <div>
        <div class="" style="display:flex; align-items:center;">
            <label style="flex:none; margin-bottom:0px; font-weight:600; margin-right: 24px"><?php echo __('member.members.label.visit_period') ?></label>
            <div style="display: flex" class="talkappi-datepicker-range">
                <input name="start_date" id="start_date" value="<?php echo(substr($post['start_date'], 0, 10))?>"/>
                <p style="margin-right: 10px;">〜</p>
                <input name="end_date" id="end_date" value="<?php echo(substr($post['end_date'], 0, 10))?>"/>
            </div>
            <div class="action-button section btn-blue js-button-ok" id='member_filter_date_btn'><?php echo __('admin.common.button.search') ?></div>
        </div>
    </div>
    <div>
        <div class="" style="display:flex; align-items:center;">
            <label style="flex:none; margin-bottom:0px; font-weight:600; margin-right: 24px"><?php echo __('member.members.label.additional_conditions') ?></label>
            <div class="filter-condition-container" style="margin-right: 20px">
              <?php
                $lang_cd = 'ja';
                if ($post['lang_cd'] == 'en') {
                  $lang_cd = 'en';
                }
                $filter_string = "";
                foreach ($filter as $key => $subArray) {
                  if (!empty($subArray)) {
                      if (in_array("checkbox_age_all", $subArray) || in_array("checkbox_area_all", $subArray) || in_array("checkbox_car_all", $subArray) || in_array("checkbox_member_type_all", $subArray) || in_array("checkbox_purpose_all", $subArray) || in_array("checkbox_reservation_channel_all", $subArray)) {
                        switch ($key) {
                          case "age":
                            $filter_string = $filter_string . " " . ($lang_cd == 'en' ? "All Age Group," : "すべての年代,");
                            break;
                          case "area":
                            $filter_string = $filter_string . " " . ($lang_cd == 'en' ? "All Area," : "すべての地域,");
                            break;
                          case "car":
                            $filter_string = $filter_string . " " . ($lang_cd == 'en' ? "All Car," : "すべての車状態,");
                            break;
                          case "member_type":
                            $filter_string = $filter_string . " " . ($lang_cd == 'en' ? "All Memeber Type," : "すべての会員状態,");
                            break;
                          case "purpose":
                            $filter_string = $filter_string . " " . ($lang_cd == 'en' ? "All Purpose," : "すべての利用目的,");
                            break;
                          case "reservation_channel":
                            $filter_string = $filter_string . " " . ($lang_cd == 'en' ? "All Reservation Channel," : "すべての予約チャンネル,");
                            break;
                        }
                      } else {
                        foreach ($subArray as $value) {
                          $filter_string = $filter_string . " " . $filterCondition[$key][$value][$lang_cd] . ",";
                        }
                      }
                  }
                }
                echo  substr($filter_string, 0, -1);
              ?>
            </div>
            <div>
                <div id="filter_condition" name="filter_condition"></div>
                <button type="button" id="filter_button" name="filter_button" class="btn round light-blue image add" style="height: 24px"><?php echo __('member.members.label.check_all_conditions') ?></button>
            </div>
        </div>
    </div>
</div>

<div class="content-container" style="padding-left: 0;">
  <div class="flex-x-between">
    <div><?php echo __('member.members.label.members') ?></div>
  </div>
</div>

<div class="content-container white">
  <table class="table table-striped table-bordered table-hover js-data-table" id="contracts">
    <thead>
      <tr>
          <th><?php echo __('member.members.label.basic_info') ?></th>
          <th><?php echo __('member.members.label.member_info') ?></th>
          <th><?php echo __('member.members.label.contact_info') ?></th>
          <th><?php echo __('member.members.label.add_info') ?></th>
          <th><?php echo __('member.members.label.visit_history') ?></th>
          <th><?php echo __('member.members.label.meal_service') ?></th>
          <th><?php echo __('member.members.label.staff_service_history') ?></th>
      </tr>
    </thead>
    <tbody>
      <?php foreach ($members as $member): ?>
        <tr>
          <!-- 基本情報 -->
          <td>
              <a class="link-animate" href="/engage/memberdetail?id=<?php echo $member['basic_info']['customer_id'] ?>"><?php echo htmlspecialchars($member['basic_info']['name']); ?></a>
              <br>
              <?php
                $info_parts = [];
                if (!empty($member['basic_info']['age_group'])) {
                    $info_parts[] = htmlspecialchars($member['basic_info']['age_group']);
                }
                if (!empty($member['basic_info']['prefecture'])) {
                    $info_parts[] = htmlspecialchars($member['basic_info']['prefecture']);
                }
                if (!empty($member['basic_info']['transportation'])) {
                    $info_parts[] = htmlspecialchars($member['basic_info']['transportation']);
                }
                echo implode(' | ', $info_parts);
              ?><br>
              <div class="align-bottom">
                <?php echo __('member.members.label.member_id') ?> : <br>
                <?php echo htmlspecialchars($member['basic_info']['customer_id']); ?>
              </div>
          </td>
          <!-- 会員情報 -->
          <td>
            <?php echo !empty($member['membership_info']) ? $member['membership_info'] : '-'; ?>
            <div class="align-bottom">
              <!-- <a href="#"><?php echo __('member.members.label.detail') ?><br></a> -->
            </div>
          </td>
          <!-- 連絡情報 -->
          <td>
            □<?php echo __('member.members.label.mail') ?><br>
            <?php echo !empty($member['contact_info']['email']) ? htmlspecialchars($member['contact_info']['email']) : '-'; ?><br>
            □<?php echo __('member.members.label.phone') ?><br>
            <?php echo !empty($member['contact_info']['tel']) ? htmlspecialchars($member['contact_info']['tel']) : '-'; ?><br>
            □<?php echo __('member.members.label.address') ?><br>
            <?php echo !empty($member['contact_info']['address']) ? htmlspecialchars($member['contact_info']['address']) : '-'; ?>
           </td>
          <!-- 付加情報 -->
          <td class="additional-info-td">
            <div class="additional-info-content">
              <?php
                if (!empty($member['additional_info'])) {
                  foreach ($member['additional_info'] as $info) {
                    $title = htmlspecialchars($info['title']);
                    $detail = htmlspecialchars($info['detail']);
                    $note = htmlspecialchars($info['note']);
                    echo "□{$title} | {$detail}";
                    if (!empty($note)) {
                      echo " (" . __('member.members.label.notes') . ": {$note})";
                    }
                    echo "<br>";
                  }
                } else {
                    echo '-';
                }
              ?>
            </div>
            <div class="additional-info-link">
              <!-- <a href="#"><?php echo __('member.members.label.add') ?></a> -->
            </div>
          </td>

          <!-- 来店履歴 -->
          <td>
              <?php if (isset($member['visit_history']['visit_count'])): ?>
                  <strong><?php echo __('member.members.label.visits'); ?><?php echo htmlspecialchars($member['visit_history']['visit_count']); ?><?php echo __('member.members.label.times'); ?></strong><br>
              <?php endif; ?>
              <?php if (isset($member['visit_history']['last_visit_date'])): ?>
                  <?php echo htmlspecialchars(date("Y/m/d", strtotime($member['visit_history']['last_visit_date']))); ?><br>
              <?php endif; ?>
              <?php if (isset($member['visit_history']['room_assignment'])): ?>
                  □<?php echo __('member.members.label.room_assignment'); ?><br>
                  <?php echo !empty($member['visit_history']['room_assignment']) ? htmlspecialchars($member['visit_history']['room_assignment']) : '-'; ?><br>
              <?php endif; ?>
              <?php if (isset($member['visit_history']['purpose'])): ?>
                  □<?php echo __('member.members.label.purpose'); ?><br>
                  <?php if (!empty($member['visit_history']['purpose']['kind'])): ?>
                      <?php echo htmlspecialchars($member['visit_history']['purpose']['kind']); ?><br>
                  <?php endif; ?>
                  <?php if (!empty($member['visit_history']['purpose']['support'])): ?>
                      (<?php echo __('member.members.label.support'); ?>: <?php echo htmlspecialchars($member['visit_history']['purpose']['support']); ?>)<br>
                  <?php endif; ?>
                  <?php if (!empty($member['visit_history']['purpose']['note'])): ?>
                      (<?php echo __('member.members.label.notes'); ?>: <?php echo htmlspecialchars($member['visit_history']['purpose']['note']); ?>)<br>
                  <?php endif; ?>
              <?php endif; ?>
              <?php if (isset($member['visit_history']['reservation_channel'])): ?>
                  □<?php echo __('member.members.label.reservation_channel'); ?><br>
                  <?php echo !empty($member['visit_history']['reservation_channel']) ? htmlspecialchars($member['visit_history']['reservation_channel']) : '-'; ?>
              <?php endif; ?>
          </td>
          <!-- 食事対応 -->
          <td>
            <?php
              if (!empty($member['meal_service'])) {
                  echo htmlspecialchars($member['meal_service']);
              } else {
                  echo '<span class="unregistered">' . __('member.members.label.unregistered') . '</span>';
              }
            ?>
            <div class="align-bottom">
              <!-- <a href="#"><?php echo __('member.members.label.add') ?></a> -->
            </div>
          </td>
          <!-- スタッフ対応履歴 -->
          <td>
              <div class="support-history-items">
                  <?php if (!empty($member['support_history'])): ?>
                      <?php foreach ($member['support_history'] as $support): ?>
                          <div class="support-item">
                              <?= htmlspecialchars(date('Y/m/d', strtotime($support['date'])) . ' ' . $support['item']); ?>
                          </div>
                      <?php endforeach; ?>
                  <?php else: ?>
                      <div class="unregistered"><?= __('member.members.label.unregistered'); ?></div>
                  <?php endif; ?>
                  <div class="align-bottom">
                      <!-- <a href="#"><?php echo __('member.members.label.add') ?></a> -->
                  </div>
              </div>
          </td>
        </tr>
      <?php endforeach; ?>
    </tbody>
  </table>
  <!-- ページネーション -->
  <div class="row">
    <div class="col-md-5 col-sm-12">
    <div class="dataTables_info" id="itemtable_info" role="status" aria-live="polite">
        <?php echo $page; ?>/<?php echo $totalPages; ?><?php echo __('member.members.label.page') ?>
      </div>
    </div>
    <div class="col-md-7 col-sm-12">
      <div class="dataTables_paginate paging_simple_numbers" id="itemtable_paginate">
        <ul class="pagination">
          <li class="paginate_button previous <?php echo ($page <= 1) ? 'disabled' : ''; ?>" id="DataTables_Table_0_previous">
            <?php if ($page > 1): ?>
                <a id="pervious_page" value="<?php echo ($page - 1); ?>"><i class="fa fa-angle-left"></i></a>
            <?php else: ?>
                <span><i class="fa fa-angle-left"></i></span>
            <?php endif; ?>
          </li>
          <?php for ($i = 1; $i <= $totalPages; $i++): ?>
              <?php if ($i == 1 || $i == $totalPages || ($i >= $page - 2 && $i <= $page + 2)): ?>
                  <li class="paginate_button <?php echo ($page == $i) ? 'active' : ''; ?>">
                      <a id="<?php echo "page" . $i; ?>" class='goto_page' value="<?php echo $i; ?>"><?php echo $i; ?></a>
                  </li>
              <?php elseif (($i < $page - 2 && $i == 2) || ($i > $page + 2 && $i == $totalPages - 1)): ?>
                <li class="paginate_button disabled"><span>…</span></li>
              <?php endif; ?>
          <?php endfor; ?>
          <li class="paginate_button next <?php echo ($page >= $totalPages) ? 'disabled' : ''; ?>" id="DataTables_Table_0_next">
            <?php if ($page < $totalPages): ?>
                <a id="next_page" value="<?php echo ($page + 1); ?>"><i class="fa fa-angle-right"></i></a>
            <?php else: ?>
                <span><i class="fa fa-angle-right"></i></span>
            <?php endif; ?>
          </li>
        </ul>
      </div>
    </div>
  </div>
</div>