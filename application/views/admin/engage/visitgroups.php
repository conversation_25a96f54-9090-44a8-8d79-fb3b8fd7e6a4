<!-- styles -->
<style>
  .start-item {
    display: flex;
    align-items: center;
    gap: 12px;
  }
  .start-item .filter-category-wrapper {
    width: 192px;
  }
  .start-item .filter-search-bar-wrapper {
    height: 28px;
    background-color: #FFFFFF;
    border: 1px solid #E3E5E8;
    border-radius: 4px;
    box-sizing: border-box;
    width: 228px;
  }
  .filter-search-bar-wrapper .search {
    height: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 12px;
  }
  .filter-search-bar-wrapper .search input {
    width: 100%;
    border: none;
  }
  .filter-search-bar-wrapper .search img {
    width: 12px;
    height: 12px;
  }
  .end-item  {
    display: flex;
    align-items: center;
    gap: 12px;
  }
  .end-item label {
    margin-bottom: 0;
  }
  .end-item .sort-wrapper {
    width: 192px;
  }
  .date-change {
    display: flex;
    align-items: center;
    gap: 12px;
  }
  .arrow-icon {
    width: 24px;
    height: 24px;
    color: #3D3F45;
  }
  .top-date {
    color: #000;
    font-family: "Hiragino Sans";
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
  }
  /* toggle */
  .ref-switch {
    width: 36px;
    height: 20px;
    cursor: pointer;
  }
  .ref-switch .ref-switch-container {
    border-radius: 99px;
    overflow: hidden;
    width: 100%;
    height: 100%;
    padding: 1px;
    display: flex;
  }
  .ref-switch .ref-switch-container.ref-switch-off  {
    background-color: #C8CACE;
    justify-content: flex-start;
  }
  .ref-switch .ref-switch-container.ref-switch-on {
    background-color: #245BD6;
    justify-content: flex-end;
  }
  .ref-switch .ref-switch-knot {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background-color: #FFFFFF;
  }
</style>
<link rel="stylesheet" href="/assets/admin/engage/visitgroups.css">
<script>
  const _date = "<?php echo $date ?>";
  const _engage_room_number_order = <?php echo $engage_room_number_order ?>;
  const _admin_service_url = "<?php echo $admin_service_url ?>";
</script>

<div class="content-container" style="padding-left: 0;">
  <div class="flex-x-between">
    <div><a class="link-animate" href="/engage/visits"><?php echo __('member.visits.label.visits_management') ?></a></div>
  </div>
  <div class="date-change" style="display: flex; justify-content: flex-end; align-items: center;">
    <div style="display: flex; align-items: center; margin-right: auto;">
      <?php
      $currentDate = strtotime($date);
      $prevDay = date('Y-m-d', strtotime('-1 day', $currentDate));
      $nextDay = date('Y-m-d', strtotime('+1 day', $currentDate));
      ?>
      <a class="link-animate" href="/engage/visitgroups?date=<?php echo $prevDay; ?>">
          <img src="/assets/admin/css/img/Icon-Arrow-previous_page_off.svg" />
      </a>
      <span class="top-date"><?php echo date("Y年m月d日", $currentDate); ?></span>
      <a class="link-animate" href="/engage/visitgroups?date=<?php echo $nextDay; ?>">
          <img src="/assets/admin/css/img/Icon-Arrow-next_page.svg" />
      </a>
    </div>
    <div style="display: flex; justify-content: space-between; align-items: center; margin-right: 10px;">
      <?php echo __('member.visits.label.ref_manshitsuonrei') ?>
      <div class="ref-switch" style="margin-left: 10px;">
        <div class="ref-switch-container ref-switch-off">
          <div class="ref-switch-knot"></div>
        </div>
      </div>
    </div>
  </div>
  <div class="flex-x-between" style="margin-top:12px;">
    <div class="start-item">
      <div class="filter-category-wrapper">
        <div class="talkappi-pulldown js-filter-category" data-name="filter-category" data-value="01" data-source='{"01":"すべて", "02":"客室対応", "03":"料理対応"}'></div>
      </div>
      <div class="filter-search-bar-wrapper">
        <div class="search">
          <input class="searchbox js-search" placeholder="検索">
          <img src="/assets/admin/css/img/icon-search.svg" class="icon">
        </div>
      </div>
    </div>

    <div class="end-item">
      <label>並べ替え</label>
      <div class="sort-wrapper">
        <div class="talkappi-pulldown js-sort" data-name="sort" data-value="03" data-source='{"01":"到着時間早い順", "02":"到着時間遅い順", "03":"部屋番号順"}'></div>
      </div>
    </div>
  </div>
</div>

<div class="content-container" style="padding-left:0;">
  <div class="visitgroups" id="visitgroups">
  </div>
</div>

<script type="text/javascript" src="/assets/admin/engage/locale/member-locale-<?php echo $_lang_cd ?>.js"></script>