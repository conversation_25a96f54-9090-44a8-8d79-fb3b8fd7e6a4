<style>
h2 {
  margin-bottom: 10px !important;
}
.row {
  margin-left: 10px;
}
.info-group {
  margin-bottom: 40px;
}
.content-container {
  width: 100%;
  margin: 0 auto;
}
.info-group table {
    width: 100%;
    border-collapse: collapse;
}
.info-tag {
  border-radius: 2px;
  background: #EBEDF2;
  color: #333;
  padding: 3px 6px;
  margin: 2px;
  display: inline-block;
}
table th {
  font-weight: normal;
}
#membership-rank-table td {
  border: none;
  text-align: center;
  padding: 10px;
  background: #EFF1F4;
}
#add-info-table {
  margin-top:10px;
}
#support-history-table {
  margin-top:10px;
}
#add-info-table th, #add-info-table td {
  border: 1px solid #E3E5E8;
  text-align: left; 
  text-align: left;
  padding: 8px;
}
#support-history-table th, #support-history-table td {
  border: 1px solid #E3E5E8;
  text-align: left;
  padding: 8px;
}
#support-history-table th {
  background: #EFF1F4;
}
.status-tag {
  display: inline-block;
  padding: 1px 4px;
  border-radius: 4px;
  margin-left: 4px;
}
.status-tag.status-accept {
  background-color: #FFE6D6;
}
.status-tag.status-dealing {
  background-color: #D3EEFF;
}
.status-tag.status-complete {
  background-color: #CFF2D7;
}
.status-tag.status-cancel {
  background-color: #FFDCE5;
}
</style>

<div class="content-container white">
  <div class="content-container">
    <?php if (empty($member)): ?>
      <p><?php echo __('member.memberdetail.label.no_data'); ?></p>
    <?php else: ?>
      <!-- 基本情報 -->
      <div class="info-group">
        <h2><?php echo __('member.memberdetail.label.basic_info') ?></h2><br>
        <div class="row">
          <div class="col-md-3">
            <div class="form-group">
              <label for="customer_id"><?php echo __('member.memberdetail.label.customer_id') ?></label><br>
              <input type="text" class="form-control" id="customer_id" value="<?php echo $member['basic_info']['customer_id']; ?>" disabled>
            </div>
          </div>
          <div class="col-md-3">
            <div class="form-group">
              <label for="name"><?php echo __('member.memberdetail.label.name') ?></label><br>
              <input type="text" class="form-control" id="name" value="<?php echo $member['basic_info']['name']; ?>">
            </div>
          </div>
          <div class="col-md-3">
            <div class="form-group">
              <label for="furigana"><?php echo __('member.memberdetail.label.furigana') ?></label><br>
              <input type="text" class="form-control" id="furigana" value="<?php echo $member['basic_info']['furigana']; ?>">
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-md-3">
            <div class="form-group">
              <label for="region"><?php echo __('member.memberdetail.label.region') ?></label><br>
              <input type="text" class="form-control" id="region" value="<?php echo $member['basic_info']['prefecture_name']; ?>">
            </div>
          </div>
          <div class="col-md-3">
            <div class="form-group">
              <label for="age_group"><?php echo __('member.memberdetail.label.age_group') ?></label><br>
              <input type="text" class="form-control" id="age_group" value="<?php echo $member['basic_info']['age_group']; ?>">
            </div>
          </div>
          <div class="col-md-6">
            <div class="form-group">
              <label for="car_info"><?php echo __('member.memberdetail.label.notice') ?></label><br>
              <input type="text" class="form-control" id="car_info" value="<?php echo $member['basic_info']['latest_customer_notes']; ?>">
            </div>
          </div>
        </div>
      </div>
      <!-- 会員情報 -->
      <div class="info-group">
        <h2><?php echo __('member.memberdetail.label.member_info') ?></h2><br>
        <div class="row">
          <div class="col-md-3">
            <div class="form-group">
              <label for="phone"><?php echo __('member.memberdetail.label.tel') ?></label><br>
              <input type="text" class="form-control" id="tel" value="<?php echo $member['membership_info']['phone']; ?>">
            </div>
          </div>
          <div class="col-md-6">
            <div class="form-group">
              <label for="email"><?php echo __('member.memberdetail.label.email') ?></label><br>
              <input type="email" class="form-control" id="email" value="<?php echo $member['membership_info']['email']; ?>">
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-md-3">
            <div class="form-group">
              <label for="birthday"><?php echo __('member.memberdetail.label.birthday') ?></label><br>
              <input type="date" class="form-control" id="birthday" value="<?php echo $member['membership_info']['birthday']; ?>">
            </div>
          </div>
          <div class="col-md-6">
            <div class="form-group">
              <label for="address"><?php echo __('member.memberdetail.label.address') ?></label><br>
              <input type="text" class="form-control" id="address" value="<?php echo $member['membership_info']['address']; ?>">
            </div>
          </div>
        </div>
        <?php if (!empty($member['membership_info']['membership_rank'])): ?>
          <?php foreach ($member['membership_info']['membership_rank'] as $membership_rank): ?>
            <div class="row">
              <div class="col-md-12">
                <table id="membership-rank-table">
                  <thead>
                  </thead>
                  <tbody>
                    <tr>
                      <td class="col-md-2"><?php echo htmlspecialchars($membership_rank['membership_type']); ?></td>
                      <?php if ($membership_rank['join_status']): ?>
                        <td class="col-md-2"><?php echo htmlspecialchars($membership_rank['join_status']); ?></td>
                      <?php else: ?>
                        <td class="col-md-2"><?php echo '-' ?></td>
                      <?php endif; ?>
                      <td class="col-md-2">
                      <?php
                      if (!empty($membership_rank['join_date']) && strtotime($membership_rank['join_date']) !== false) {
                          $joinDate = new DateTime($membership_rank['join_date']);
                          echo htmlspecialchars($joinDate->format('Y/m/d').'〜');
                      } else {
                          echo '-';
                      }
                      ?>
                      </td>
                      <?php if ($membership_rank['allow_dm'] == 1): ?>
                        <td class="col-md-2"><?php echo __('member.memberdetail.label.dm_ok') ?></td>
                      <?php elseif ($membership_rank['allow_dm'] == 0): ?>
                        <td class="col-md-2"><?php echo __('member.memberdetail.label.dm_ng') ?></td>
                      <?php else: ?>
                        <td class="col-md-2"><?php echo '-' ?></td>
                      <?php endif; ?>
                      <td class="col-md-1"><?php echo __('member.memberdetail.label.expand') ?></td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          <?php endforeach; ?>
        <?php endif; ?>
      </div>
      <!-- 付加情報 -->
      <div class="info-group">
        <h2><?php echo __('member.memberdetail.label.add_info') ?></h2>
        <div class="row">
          <div class="col-md-12">
            <?php if (!empty($member['additional_info']) && is_array($member['additional_info'])): ?>
              <table id="add-info-table">
                <?php if (!empty($member['additional_info']) && is_array($member['additional_info'])): ?>
                  <tr>
                    <?php foreach ($member['additional_info'] as $title => $details): ?>
                      <th><?php echo __('member.memberdetail.label.additional_label.' . $title) ?></th>
                    <?php endforeach; ?>
                  </tr>
                  <tr>
                    <?php foreach ($member['additional_info'] as $details): ?>
                      <td>
                        <?php foreach ($details as $detail): ?>
                          <span class="info-tag"><?php echo htmlspecialchars($detail); ?></span>
                        <?php endforeach; ?>
                      </td>
                    <?php endforeach; ?>
                  </tr>
                <?php endif; ?>
              </table>
            <?php else: ?>
              <p><?php echo __('member.memberdetail.label.no_data'); ?></p>
            <?php endif; ?>
          </div>
        </div>
      </div>
      <!-- 来店履歴 -->
      <div class="info-group">
        <h2><?php echo '来店履歴' ?></h2>
        <div id="react-member-visithistory" style="display: flex; gap: 12px; flex-direction: column;"></div>

      </div>
      <!-- スタッフ対応履歴 -->
      <div class="info-group">
        <h2><?php echo __('member.memberdetail.label.support_history') ?></h2>
        <div class="row">
          <div class="col-md-12">
            <?php if (!empty($member['support_history'])): ?>
              <table id="support-history-table">
                <thead>
                  <tr>
                    <th><?php echo __('member.memberdetail.label.visit_count'); ?></th>
                    <th><?php echo __('member.memberdetail.label.date'); ?></th>
                    <th><?php echo __('member.memberdetail.label.task'); ?></th>
                    <th><?php echo __('member.memberdetail.label.staff'); ?></th>
                    <th><?php echo __('member.memberdetail.label.response'); ?></th>
                    <th><?php echo __('member.memberdetail.label.arrangement'); ?></th>
                    <th><?php echo __('member.memberdetail.label.notes'); ?></th>
                    <th><?php echo __('member.memberdetail.label.action'); ?></th>
                  </tr>
                </thead>
                <tbody>
                  <?php if (!empty($member['support_history'])): ?>
                    <?php foreach ($member['support_history'] as $history): ?>
                      <tr>
                        <td><?php echo htmlspecialchars($history['visit_count']); ?></td>
                        <td><?php echo htmlspecialchars($history['date']); ?></td>
                        <td><?php echo __('member.memberdetail.label.support_arrange.' . $history['task']) ?></td>
                        <td><?php echo htmlspecialchars($history['staff']); ?></td>
                        <td><?php echo htmlspecialchars($history['response']); ?></td>
                        <td><?php 
                          if ($history['arrangement_destination'] != '-') {
                            echo htmlspecialchars($history['arrangement_destination']);
                          }
                          if ($history['arrangement_status'] != '-') {
                            echo '<span class="status-tag status-' . $history['arrangement_status'] . '">' . __('member.memberdetail.label.support_arrange_status.' . $history['arrangement_status']) . '</span>';
                          } else {
                            echo htmlspecialchars($history['arrangement_status']); 
                          }
                        ?></td>
                        <td><?php echo htmlspecialchars($history['notes']); ?></td>
                        <td><?php echo __('member.memberdetail.label.detail') ?> <?php echo __('member.memberdetail.label.delete') ?></td>
                      </tr>
                    <?php endforeach; ?>
                  <?php else: ?>
                    <tr>
                      <td colspan="8"><?php echo __('member.memberdetail.label.no_data'); ?></td>
                    </tr>
                  <?php endif; ?>
                </tbody>
              </table>
            <?php else: ?>
              <p><?php echo __('member.memberdetail.label.no_data'); ?></p>
            <?php endif; ?>
          </div>
        </div>
      </div>
      <!-- 来店評価 -->
    <?php endif; ?>
  </div>
</div>

<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<script src="/assets/common/react/pages/engage/components/visithistory.bundle.js"></script>
<script type="text/javascript">
  document.addEventListener("DOMContentLoaded", function(){
    window.window.talkappi_member_setupVisitHistory({
      historyData: <?php echo json_encode($histories) ?>
        });
  })
</script>