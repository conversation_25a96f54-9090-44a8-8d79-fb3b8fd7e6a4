<style>
  .icon-import {
    background: url("/assets/admin/css/img/icon-import-blue.svg") no-repeat;
    height: 12px;
    width: 12px;
  }

  .csv_import_float_panel {
    position: relative;
  }

  .container_item_in_float_panel_frame {
    /* flex-direction: column; */
    /* align-items: flex-start; */
    padding: 10px;
    /* gap: 6px; */

    /* height: 52px; */
    /* Inside auto layout */
    /* flex: none; */
    /* align-self: stretch; */
  }
</style>

<script>
  var visitsgroups = <?php echo json_encode($visitsgroups); ?>;
  var _path = "<?php echo $_path; ?>";
  const _admin_service_url = "<?php echo $admin_service_url ?>";
</script>
<script type="text/javascript" src="/assets/admin/engage/locale/member-locale-<?php echo $_lang_cd ?>.js"></script>

<!-- hidden/float panel BEGIN -->
<div class="modal fade" id="boxCSVImport" tabindex="-1" role="basic" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="csv_import_float_panel">
        <div class="container_item_in_float_panel_frame">
            <div class="txt_title_name_upload_label_count_float"><?php echo __('admin.common.label.upload.csv') ?></div>
            <div id="upload_csv" class="talkappi-upload js-upload" data-name="csv_file" 
                data-label="<?php echo __('admin.common.upload.csv.placeholder') ?>" data-type="csv" data-max-size="10">
            </div>
            <!-- <input type="file" name="csv_file" id="upload_csv"> -->
        </div>
			</div>
    </div>
  </div>
</div>

<!-- Page Content -->
<div class="content-container" style="padding-left:0;padding-right:0">
  <div class="flex-x-between">
    <div><?php echo __('member.visits.label.visits_management') ?></div>
    <div class="flex">
      <div class="talkappi-datepicker-range">
        <input name="start_date" value="<?php echo ($post['start_date']) ?>"/><p>〜</p>
        <input name="end_date" value="<?php echo ($post['end_date']) ?>"/>
      </div>
      <button style="margin-left:0" type="button" id="searchButton" class="btn-smaller btn-blue"><span class="icon-filter"></span><?php echo __('admin.common.label.narrowdown'); ?></button>
      <button style="" type="button" id="import" class="btn-smaller btn-gray-black"><span class="icon-import"></span><?php echo __('admin.common.button.import.csv'); ?></button>
    </div>
  </div>
</div>

<div class="content-container white">
  <table class="table table-striped table-bordered table-hover js-data-table">
    <thead>
        <tr>
          <th><?php echo __('member.visits.label.checkin_date') ?></th>
          <th><?php echo __('member.visits.label.reservation_count') ?></th>
          <th><?php echo __('member.visits.label.room_list') ?></th>
          <th><?php echo __('member.visits.label.kitchen_remarks') ?></th>
          <th><?php echo __('admin.common.label.operation') ?></th>
        </tr>
    </thead>
      <tbody>
      </tbody>
  </table>
</div>
