<style>
/* To avoid 100vh is not equal to window.innerHeight on iPad */
@supports (-webkit-touch-callout: none) {
  body {
    /* The hack for Safari */
    height: -webkit-fill-available;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }
}
/* Full screen view  */
.page-content-wrapper .page-content {
  padding: 0 !important;
  margin-left: unset !important;
}

.page-content-header,
.page-sidebar-wrapper {
  display: none;
}

.talkappibot {
  display: none;
}
.page-footer {
  display: none;
}
.full-view {
  width: 100%;
  background-color: white;
  padding: 0px;
  display: flex;
  flex-direction: column;
}
</style>
<script>
const _admin_service_url = "<?= $admin_service_url ?>";
const _header_info = <?= json_encode($header_info, JSON_UNESCAPED_UNICODE) ?>;
</script>

<div id="react-graphs-container" class="full-view"></div>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/1.12.4/jquery.min.js"></script>
<script type="text/javascript" src="/assets/common/react/pages/engage/visit.bundle.js"></script>
<script type="text/javascript">
    jQuery(document).ready(function($) {
        const urlParams = new URLSearchParams(window.location.search);
        const status = urlParams.get('status');
        const visitId = urlParams.get('id');
        window.talkappi_visitorSupport_setupFunctionsGraph({
            headerInfo: <?php echo json_encode($header_info) ?>,
            status: status,
            visitId: visitId,
            importantItemsData: <?php echo json_encode($important_items) ?>,
        });
    });
</script>