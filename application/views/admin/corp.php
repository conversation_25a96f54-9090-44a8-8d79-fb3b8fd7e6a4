<style type="text/css">
.btn.default:active, .btn.default.active {
  background-image: none;
  background-color: #45b6af;
  color: #fff;
}
</style>
<input type="hidden" name="act" id="act" value="" />
<input type="hidden" name="corp_ids" id="corp_ids" value="<?php echo $corp_ids ?>" />
<input type="hidden" name="corp" id="corp" value="<?php echo $corp->corp_id; ?>" />
			<!-- BEGIN PAGE HEADER-->
			<!-- BEGIN PAGE HEAD -->
			<div class="page-head">
				<!-- BEGIN PAGE TITLE -->
				<div class="page-title">
					<h1><?php echo($_active_menu_name)?><small></small></h1>
				</div>
				<!-- END PAGE TITLE -->
			</div>
			<!-- END PAGE HEAD -->
			<!-- END PAGE HEADER-->
			<!-- BEGIN PAGE CONTENT-->
			        <!-- Page Content -->
			        <div id="page-wrapper">
                        <h5>会員一覧 > 法人会員詳細</h5>
				        <div class="portlet light">
							<div class="portlet box">
								<div class="portlet-body">
								<input type="hidden" name="error_message" id="error_message" value="<?php echo ($error_message) ?>" />
								<input type="hidden" name="success_message" id="success_message" value="<?php echo ($success_message) ?>" />
                                    <div class="form-body">
                                        <div class="form-group">
                                            <label class="control-label col-md-2"><span class="badge badge-danger">必須</span> 会員No</label>
                                            <div class="col-md-3">
											    <div class="input-icon right">
												    <input name="corp_id" id="corp_id" type="text" class="form-control" value="<?php if ($post != NULL) echo($post['corp_id'])?>" placeholder="">
											    </div>
										    </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="control-label col-md-2"><span class="badge badge-danger">必須</span> 法人パスワード</label>
                                            <div class="col-md-3">
											    <div class="input-icon right">
												    <input name="corp_password" id="corp_password" type="text" class="form-control" value="<?php if ($post != NULL) echo($post['corp_password'])?>" placeholder="">
											    </div>
										    </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="control-label col-md-2"><span class="badge badge-danger">必須</span> 会員名</label>
                                            <div class="col-md-5">
											    <div class="input-icon right">
												    <input name="corp_name" id="corp_name" type="text" class="form-control" value="<?php if ($post != NULL) echo($post['corp_name'])?>" placeholder="〇〇株式会社">
											    </div>
										    </div>
                                        </div>
                                        <hr>
                                        <div class="form-group">
                                            <label class="control-label col-md-2">有効期間</label>
                                            <div class="col-md-5">
                                                <input name="contract_date_start" id="start_date" value="<?php if ($post != NULL)echo($post['contract_date_start'])?>" style="float:left;" class="form-control form-control-inline input-small date-picker" size="16" data-date-format="yyyy-mm-dd"  type="text" placeholder="契約開始日"/>
											    <input name="contract_date_end" id="end_date" value="<?php if ($post != NULL) echo($post['contract_date_end'])?>" style="float:left; margin-left:10px;" class="form-control form-control-inline input-small date-picker" size="16" data-date-format="yyyy-mm-dd"  type="text" placeholder="契約終了日"/>
										    </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="control-label col-md-2">会社詳細情報(郵便番号)</label>
                                            <div class="col-md-3">
											    <div class="input-icon right">
												    <input name="postal_code" id="postal_code" type="text" class="form-control" value="<?php if ($post != NULL) echo($post['postal_code'])?>" placeholder="ハイフンありで郵便番号入力">
											    </div>
										    </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="control-label col-md-2">会社詳細情報(住所)</label>
                                            <div class="col-md-5">
											    <div class="input-icon right">
												    <input name="address" type="text" class="form-control" value="<?php if ($post != NULL) echo($post['address'])?>" placeholder="">
											    </div>
										    </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="control-label col-md-2">会社詳細情報(電話番号)</label>
                                            <div class="col-md-3">
											    <div class="input-icon right">
													<input name="tel" id="tel" type="text" class="form-control" value="<?php if ($post != NULL) echo($post['tel'])?>" placeholder="ハイフンなしで電話番号入力">
											    </div>
										    </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="control-label col-md-2">持ち物</label>
                                            <div class="col-md-5">
											    <div class="input-icon right">
													<textarea name="bring_list" class="form-control" rows="10"><?php if ($post != NULL) echo($post['bring_list'])?></textarea>
												</div>
										    </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="control-label col-md-2">追加情報</label>
                                            <div class="col-md-5">
											    <div class="input-icon right">
												    <input name="extra_info" type="text" class="form-control" value="<?php if ($post != NULL) echo($post['extra_info'])?>" placeholder="複数の場合は「,」区切りで追加情報を入力">
											    </div>
										    </div>
                                        </div>
										<div class="form-group">
                                            <label class="control-label col-md-2">受付不可</label>
                                            <div class="col-md-3">
												<div class="btn-group" id="is_available" data-toggle="buttons">
													<input type="checkbox" name="is_available" <?php if ($post['is_available'] === 0) echo('checked')?> value="0" class="make-switch" data-on-color="success" data-off-color="default">
											    </div>
										    </div>
                                        </div>
										<div class="form-group">
                                            <label class="control-label col-md-2">OB会員</label>
                                            <div class="col-md-3">
												<div class="btn-group" id="is_ob" data-toggle="buttons">
													<input type="checkbox" name="is_ob" <?php if ($post['is_ob'] === 1) echo('checked')?> value="1" class="make-switch" data-on-color="success" data-off-color="default">
											    </div>
										    </div>
                                        </div>
										<div class="form-group" id="ob_corp_id">
                                            <label class="control-label col-md-2">OB会員No</label>
                                            <div class="col-md-3">
											    <div class="input-icon right" >
												    <input name="ob_corp_id" type="text" class="form-control" value="<?php if ($post != NULL) echo($post['ob_corp_id'])?>" placeholder="OB会員がある場合は、会員Noを入力">
											    </div>
										    </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="control-label col-md-2">最終編集者</label>
                                            <div class="col-md-3" style="margin-top: 10px;">
												<?php if ($post != NULL) echo($post['upd_time'] . '  ' . $post['upd_user']); ?>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-actions" style="margin-top: 20px;">
									    <div class="row">
										    <div class="col-md-offset-2 col-md-9">
											    <button type="button" id="saveBaseButton" class="btn blue mr10">
											    <i class="fa fa-save mr10"></i>保存</button>
											    <button type="button" id="deleteButton" class="btn red mr10">削除</button>
                                                <button type="button" onclick="top.location='/admin/corps'" class="btn grey-steel">戻る</button>
										    </div>
									    </div>
								    </div>
							    </div>
							</div>
						</div>
			        </div>
			        <!-- /#page-wrapper -->
			<!-- END PAGE CONTENT-->


