<style>
.mobile-preview .main-container {
	display:flex;
	height: 400px;
	gap: 10px;
	border: 1px solid #D8D8D8;
}
.js-selected-table tbody {
	position: relative;
}
.js-selected-cd {
	cursor: move;
	position: relative;
}
.js-selected-cd.all_backrandom td:first-child img {
	display: none;
}
.js-selected-cd .name-part {
	display: flex;
	align-items: center;
	gap: 6px;
}
.js-selected-cd .name-part.current {
	color: #245BD6;
}
.js-selected-cd.ui-sortable-helper {
	background-color: rgba(255, 255, 255, 0.8);
	height: 40px;
	width: 100%;
	display: flex;
}
.js-selected-cd.ui-sortable-placeholder {
	visibility: visible !important;
}
.js-selected-cd.ui-sortable-placeholder::after {
	content: '';
	display: block;
	width: 100%;
	height: 2px;
	background-color: #245BD6;
	position: absolute;
	left: 0;
	bottom: 0;
	right: 0;
	z-index: 100;
}
.js-selected-cd td:first-child {
	width: 107px;
}
.js-selected-cd td:last-child {
	width: calc(100% - 107px);
}
.cursor-not-allow {
	cursor:not-allowed !important;
}
</style>
<!-- BEGIN PAGE CONTENT-->
<?php echo $menu ?>

<input type="hidden" id="class_cd" name="class_cd" value="<?php echo $current_class_cd; ?>">
<input type="hidden" name="save_data" id="save_data" value="" >
<input type="hidden" name="is_set_allbackrandom" value="<?php echo $isSetAllBackRandom ?>">

<div class="content-container white border">
	<div class="section-container">
	<div class="row">
		<div class="col-md-8">						
			<div class="form-body">
				<div class="form-group">
					<label class="col-md-2"><?php echo __('admin.item.itemdisplay.name') ?></label>
					<div class="col-md-9">
						<input type="text" class="form-control" style="width:500px;" readonly value="<?php echo $post['item_name'] ?>">
					</div>
				</div>

				<div class="form-group">
					<label class="col-md-2"><?php echo __('admin.common.label.classification') ?></label>
					<div class="col-md-9">
						<?php if (count($class_cd_array) == 0) { ?>
							<p style="color:#A1A4AA"><?php echo __('admin.item.itemdisplay.cate_not_setting') ?></p>
						<?php } else { ?>
						<nav class="button-tab" style="padding:0">
							<ul class="">
							<?php foreach ($class_cd_array as $key => $class_cd) { ?>
								<li style="margin-bottom:10px" class="<?php if ($class_cd['class_cd'] == $current_class_cd) echo 'active' ?>" data-class_cd="<?php echo $class_cd['class_cd'] ?>"><?php echo $class_cd['class_name'] ?></li>
							<?php } ?>
							</ul>
            </nav>
						<?php echo __('admin.item.itemdisplay.cate_tips') ?>
						<?php } ?>
					</div>
				</div>
				<!-- 分類がない場合は、ランダム表示の設定ができない -->
				<?php if (count($class_cd_array) != 0) { ?>
				<div class="form-group">
					<label class="col-md-2"><?php echo __('admin.item.itemdisplay.random_display') ?></label>
					<div class="col-md-9">
						<div class="checkbox-label">
							<input type="checkbox" id="all_backrandom_chx" <?php if ($isSetAllBackRandom) echo 'checked' ?> value="<?php echo intval($isSetAllBackRandom) ?>">
							<label for="all_backrandom_chx"><?php echo __('admin.item.itemdisplay.all_backrandom') ?></label>
						</div>
					</div>
				</div>
				<?php } ?>

				<div style="margin-bottom:14px">「<?php echo $class_cd_cond_name ?>」<?php echo __('admin.items.item_order.order_title') ?></div>
				<div style="margin-bottom:30px">
					<table class="table table-bordered js-selected-table">
            <thead style="background-color:#F6F7F9;">
              <tr>
                <th style="text-align:end;">#</th>
                <th style="text-align:start;"><?php echo __('admin.common.label.name') ?></th>
              </tr>
            </thead>
						<?php if ($items_count == 0) { ?>
						<tbody>
							<tr>
								<td colspan="2" style="color:#D8D8D8;">
								<div style="display:flex;flex-direction:column;justify-content:center;align-items:center;font-size:12px;gap:6px;">
									<?php echo __('admin.item.itemdisplay.add_cate_info') ?>
									<a href="<?php echo ('/admin/item?id='.$item_id) ?>"><?php echo __('admin.item.itemdisplay.add_cate_guide') ?></a>
								</div>
								</td>
							</tr>
						</tbody>
						<?php } else { ?>
            <?php foreach ($items_by_type as $type => $items) { ?>
              <tbody class="js-sortable-tbody" id="sortable-items-<?php echo $type ?>" data-type="<?php echo $type ?>">
              <?php foreach($items as $key => $item) { ?>
								<?php $is_self = $item['item_id'] == $item_id; ?>
								<tr class="js-selected-cd" data-id="<?php echo $item['item_id'] ?>" data-order1="<?php echo ($type == '1') ? ($key + 1) : $type ?>" data-order2="<?php echo ($type != "1") ? ($key + 1) : '' ?>">
									<td style="vertical-align:middle;">
										<div class="wrapper" style="display:flex;justify-content:space-between;align-items:center">
											<img src="/assets/admin/css/img/icon-drag.svg">
											<span><?php echo ($type == '1') ? ($key + 1) : '-' ?></span>
										</div>
									</td>
									<td>
										<div class="wrapper" style="display:flex;justify-content:space-between;align-items:center;">
											<span class="name-part <?php if ($is_self) echo 'current' ?>">
												<?php echo $item['item_id'] . ' ' . $item['item_name'] ?>
												<?php if ($item['recommend'] == 1) { ?>
													<img src="/assets/admin/images/icon-recommend.svg" alt="">
												<?php } ?>
												<?php if ($item['is_all_lang_support']) {?>
													<img src="/assets/admin/images/icon-language-all.svg" alt="">
												<?php }?>
											</span>
											<div class="random-setting-part">
												<?php
													$random_data_source = [
														"-1" => __('admin.items.item_order.random_type_-1'),
														"1" => __('admin.items.item_order.random_type_1'), 
														"0" => __('admin.items.item_order.random_type_0')
													]
												?>
												<div class="talkappi-pulldown js-random-type" data-value="<?php echo $type ?>" data-source='<?php echo json_encode($random_data_source, JSON_UNESCAPED_UNICODE) ?>'></div>
											</div>
										</div>
									</td>
								</tr>
								<?php } ?>
							</tbody>
						<?php } } ?>
          </table>
				</div>

				<div class="form-group" style="display: none;">
					<label class="col-md-2"><?php echo __('admin.item.itemdisplay.recommend') ?> <img style="margin-left:6px;margin-top:-6px;" src="/assets/admin/images/icon-recommend.svg" alt=""></label>
					<div class="col-md-9" style="display:flex;gap:8px;align-items:center">
						<div class="talkappi-switch" data-name="recommend" data-value="<?php echo $post['recommend'] == '1' ? '1' : '0' ?>"></div>
						<?php echo __('admin.item.itemdisplay.recommend_tips') ?>
					</div>
				</div>																							
				<div class="form-group">
					<label class="col-md-2"><?php echo __('admin.item.itemdisplay.multilingual') ?></label>
					<div class="col-md-9">
						<div class="btn-group" data-toggle="buttons">
						<?php
						$langs = explode(',', $post['lang_display']);
						foreach($_bot_lang as $lang_cd=>$lang_name) {
							if (in_array($lang_cd, $langs)) {
								echo('<label class="btn default active">');
								echo('<input name="lang_display[]" type="checkbox" checked="true" value="' . $lang_cd . '" class="toggle">' . $lang_name. '</label>');
							}
							else {
								echo('<label class="btn default">');
								echo('<input name="lang_display[]" type="checkbox" value="' . $lang_cd . '" class="toggle">' . $lang_name. '</label>');
							}
						}
						?>
						</div>
					</div>
				</div>															
			</div>
			<div class="actions-container" style="margin: 60px 0 0 140px;">
				<button type="button" class="btn-larger btn-blue js-action-save"><?php echo __('admin.common.button.save') ?></button>
				<button type="button" onclick="top.location='/admin/items'" class="btn-larger btn-white"><?php echo __('admin.common.button.back') ?></button>
			</div>
		</div>
		<div class="col-md-4">
			<div class="talkappi-preview js-msg-preview preview-sticky" data-type="message" style="margin:30px 0 0 20px; flex-basis: 30%; max-width:320px;"></div>
		</div>
	</div>
</div>								
</div>
<!-- END PAGE CONTENT-->

<script>
const _item_id = '<?php echo $item_id ?>';
const _items_by_type = <?php echo json_encode($items_by_type, JSON_UNESCAPED_UNICODE) ?>;
const _preview_item_datas = <?php echo json_encode($preview_item_datas, JSON_UNESCAPED_UNICODE) ?>;
const _lang_edit = '<?php echo $lang_edit ?>';
</script>



