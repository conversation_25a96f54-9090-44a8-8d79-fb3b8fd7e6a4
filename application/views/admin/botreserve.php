<style type="text/css">
	.btn.default {
		margin-right: 2px;
	}

	.btn.default.active {
		background-image: none;
		background-color: #245BD6;
		color: #fff;
		margin-right: 2px;
	}
</style>
<script type="text/javascript">
var _cancel_policy_list = <?php echo(json_encode($cancel_policy))?>;
</script>
<!-- BEGIN PAGE HEADER-->
<!-- BEGIN PAGE HEAD -->
<div class="page-head">
	<!-- BEGIN PAGE TITLE -->
	<div class="page-title">
		<h1><?php echo ($_active_menu_name) ?><small></small></h1>
	</div>
	<!-- END PAGE TITLE -->
</div>
<!-- END PAGE HEAD -->
<!-- END PAGE HEADER-->
<!-- BEGIN PAGE CONTENT-->
<!-- Page Content -->
<div id="page-wrapper">
	<div class="portlet light">
		<?php
		if (strlen($_active_menu) > 4) {
			$tab_menu = View::factory('admin/menutab');
			echo ($tab_menu);
		}

		$arrayrooms[''] = '';
		for ($i = 1; $i <= 15; $i++) {
			$arrayrooms[$i] = $i;
		}

		$arraypeople[''] = '';
		for ($i = 1; $i <= 15; $i++) {
			$arraypeople[$i] = $i;
		}

		$arraydays[''] = '';
		for ($i = 0; $i <= 31; $i++) {
			$arraydays[$i] = $i;
		}

		$arraypecents[''] = '';
		for ($i = 0; $i <= 100; $i = $i + 5) {
			$arraypecents[$i] = $i;
		}
		?>
		<div class="portlet box">
			<div class="portlet-body">
				<input type="hidden" name="message" id="message" value="<?php echo ($message) ?>" />
				<input type="hidden" name="key" id="key" value="<?php echo($key) ?>" />
				<input type="hidden" name="act" id="act" />
				<input type="hidden" name="cancel_policy" id="cancel_policy" value="" />
				<input type="hidden" name="child_price_remark" id="child_price_remark" value="" />
				<nav class="line-tab">
				<ul class="">
					<?php foreach($key_list as $k) {
						$show = $k;
						if ($show == '') $show = '未命名';
						$active = '';
						if ($key == $k) $active = 'active';
						echo('<li class="' . $active . '"><a class="func-menu" href="/admin/botreserve?key=' . $k . '">' . $show . '</a></li>');
					}
					?>
				</ul>
			</nav>
				<div class="row">
					<div class="col-md-10">
						<div class="form-body">
						<?php if (count($key_list) > 1) { ?>
							<div class="form-group">
								<label class="control-label col-md-2" style="font-weight: 600;">【識別キー】</label>
								<div class="col-md-2">
									<input type="text" name="key_input" class="form-control" value="<?php echo $key ?>">
								</div>
							</div>
						<?php } ?>
							<br/>					
							<div class="form-group">
								<label class="control-label col-md-2" style="font-weight: 600;">【在庫連携設定】</label>
								<div class="col-md-8">
									<hr>
								</div>
							</div>
							<div class="form-group">
								<label class="control-label col-md-3">在庫管理</label>
								<div class="col-md-3">
									<?php echo Form::select('type', $site_controllers, $type, array('id' => 'type', 'class' => 'form-control')) ?>
								</div>
							</div>
							<div class="form-group">
								<label class="control-label col-md-3">連携情報</label>
								<div class="col-md-2">
									<input name="hotel_code" id="hotel_code" maxlength="32" type="text" class="form-control" value="<?php echo $hotel_code ?>" placeholder="施設ID">
								</div>
								<div class="col-md-2">
									<input name="user" id="user" maxlength="32" type="text" class="form-control" value="<?php echo $user ?>" placeholder="連携ID">
								</div>
								<div class="col-md-2">
									<input name="password" id="password" maxlength="32" type="text" class="form-control" value="<?php echo $password ?>" placeholder="連携Password">
								</div>
							</div>
							<div class="form-group" id="reserve-setting" <?php if($type === 'tl') echo('style="display:none;"') ?>>
								<label class="control-label col-md-3">在庫設定</label>
								<div class="col-md-5">
									<div class="btn-larger btn-blue" style="width: 180px;height: 28px;margin-left:0px;">
										<a class="flexbox-center height-100" href="<?php echo $admin_url . 'reserveadmin/?mode=admin'?>" target="_blank" style="color: #fff;" onfocus="this.blur();">部屋・プランを管理する</a>
									</div>
								</div>
							</div>
							<div class="form-group" id="tl-reserve-setting" <?php if($type !== 'tl') echo('style="display:none;"') ?>>
								<label class="control-label col-md-3">在庫設定</label>
								<div class="col-md-7">
									<label class="control-label">TLブッキング側で部屋とプランの設定をお願いします</label>
								</div>
							</div>
							<br><br>

							<div class="form-group">
								<label class="control-label col-md-2" style="font-weight: 600;">【法人会員予約】</label>
								<div class="col-md-8">
									<hr>
								</div>
							</div>

							<div class="form-group">
								<label class="control-label col-md-3">法人会員予約機能</label>
								<div class="col-md-3">
									<div class="talkappi-radio" data-name="corp_func" data-value="<?php echo $corp_func ?>" data-source='{"1":"利用する","0":"利用しない"}'></div>
								</div>
							</div>
							<div class="form-group">
								<label class="control-label col-md-3">プライバシーURL</label>
								<div class="col-md-8">
									<input name="privacy_url" id="privacy_url" maxlength="500" type="text" class="form-control" value="<?php echo $privacy_url ?>" placeholder="https://talkappi.com/privacy/">
								</div>
							</div>
							<br><br>

							<div class="form-group">
								<label class="control-label col-md-2" style="font-weight: 600;">【予約基本設定】</label>
								<div class="col-md-8">
									<hr>
								</div>
							</div>
							<?php if(isset($parent_bot_setting) && $parent_bot_setting['setting']){?>
								<div class="form-group">
									<label class="control-label" style="font-weight: 600; margin: 0 0 20px 50px; color: #E53361;">現在は{<?php echo $parent_bot_setting['name']?>}(ID:<?php echo $parent_bot_setting['id']?>)の予約販売設定を参照しています</label>
								</div>
							<?php }?>
							<div class="form-group">
								<label class="control-label col-md-3">予約可能期間</label>
								<div class="col-md-2">
									<input name="reservable_months" id="reservable_months" maxlength="2" type="number" min="1" max="18" class="form-control" value="<?php echo $reservable_months ?>">
								</div>
								<div class="col-md-1">
									<label class="control-label">ヶ月間</label>
								</div>
							</div>
							<div class="form-group">
								<label class="control-label col-md-3">最大予約部屋数</label>
								<div class="col-md-2">
									<?php echo Form::select('select_rooms', $arrayrooms, $select_rooms, array('id' => '$select_rooms', 'class' => 'form-control')) ?>
								</div>
								<div class="col-md-2">
									<label class="control-label">部屋まで</label>
								</div>
							</div>
							<div class="form-group flex">
								<label class="control-label col-md-3">予約可能な期限</label>
								<div class="col-md-1" style="width: 58px;">
									<label class="control-label">前日</label>
								</div>
								<div class="col-md-2">
									<input name="last_rev_time" id="last_rev_time" maxlength="2" type="number" min="0" class="form-control" value="<?php echo $last_rev_time ?>">
								</div>
								<div class="col-md-6">
									<label class="control-label">時まで（例:当日の朝5時までに可能な場合 → 前日の29時に設定）</label>
								</div>
							</div>
							<div class="form-group">
								<label class="control-label col-md-3">デフォルト人数（大人）</label>
								<div class="col-md-2">
									<div class="talkappi-radio" data-name="default_adults" data-value="<?php echo $default_adults ?>" data-source='{"1":"1名","2":"2名"}'></div>
								</div>
								<div class="col-md-6">
									<label class="control-label">※プランごとの設定は「プラン・部屋設定」にて設定可能です。</label>
								</div>
							</div>
							<div class="form-group">
								<label class="control-label col-md-3">最大人数/部屋</label>
								<div class="col-md-1" style="width: 58px;">
									<label class="control-label">大人</label>
								</div>
								<div class="col-md-2">
									<?php echo Form::select('adults_max', $arraypeople, $adults_max, array('id' => '$adults_max', 'class' => 'form-control')) ?>
								</div>
								<div class="col-md-6">
									<label class="control-label">※設定値は全プランに適用される</label>
								</div>
							</div>
							<div class="form-group">
								<label class="control-label col-md-3"></label>
								<div class="col-md-1" style="width: 58px;">
									<label class="control-label">子供</label>
								</div>
								<div class="col-md-2">
									<?php echo Form::select('children_max', $arraypeople, $children_max, array('id' => '$children_max', 'class' => 'form-control')) ?>
								</div>
								<div class="col-md-6">
									<label class="control-label">※設定値は全プランに適用される</label>
								</div>
							</div>
							<div class="form-group">
								<label class="control-label col-md-3">男女人数の入力</label>
								<div class="col-md-3">
									<div class="talkappi-radio" data-name="sex_diff" data-value="<?php echo $sex_diff ?>" data-source='{"1":"有効","0":"無効"}'></div>
								</div>
							</div>
							<div class="form-group">
								<label class="control-label col-md-3">喫煙・禁煙</label>
								<div class="col-md-6">
									<div class="talkappi-radio" data-name="facility_smoking" data-value="<?php echo $facility_smoking ?>" data-source='{"0":"全館禁煙","1":"全館喫煙","2":"禁煙・喫煙選択"}'></div>
								</div>
							</div>
							<div class="form-group">
								<label class="control-label col-md-3">予約者の漢字入力</label>
								<div class="col-md-3">
									<div class="talkappi-radio" data-name="kanji_name" data-value="<?php echo $kanji_name ?>" data-source='{"1":"有効","0":"無効"}'></div>
								</div>
							</div>
							<br><br>

							<div class="form-group">
								<label class="control-label col-md-2" style="font-weight: 600;">【通知関連設定】</label>
								<div class="col-md-8">
									<hr>
								</div>
							</div>
							<div class="form-group">
								<label class="control-label col-md-3">予約完了メールの転送先(BCC)</label>
								<div class="col-md-8">
									<input name="complete_mail_bcc" id="complete_mail_bcc" type="text" class="form-control" value="<?php echo $complete_mail_bcc ?>" placeholder="転送先メールアドレス、複数指定の場合,区切り">
								</div>
							</div>

							<br><br>

							<div class="form-group">
								<label class="control-label col-md-2" style="font-weight: 600;">【決済関連設定】</label>
								<div class="col-md-8">
									<hr>
								</div>
							</div>
							<div class="form-group">
								<label class="control-label col-md-3">利用可能な決済方法</label>
								<div class="col-md-6">
									<div class="talkappi-checkbox js-payment-support" data-name="payment_support" data-value='<?php echo json_encode($payment_support)?>' data-source='<?php echo json_encode($_codes['38'], JSON_UNESCAPED_UNICODE) ?>'></div>
								</div>
							</div>
							<div class="form-group">
								<label class="control-label col-md-3">デフォルトの決済方法</label>
								<div class="col-md-1" style="white-space: nowrap; width: 90px;">
									<label class="control-label">国内（日本語）</label>
								</div>
								<div class="col-md-6">
									<div class="talkappi-checkbox js-payment-setting-jp" data-name="payment_setting_jp" data-value='<?php echo json_encode($payment_setting['jp'])?>' data-source='<?php echo json_encode($_codes['38'], JSON_UNESCAPED_UNICODE) ?>'></div>
								</div>
							</div>
							<div class="form-group">
								<label class="control-label col-md-3"></label>
								<div class="col-md-1" style="white-space: nowrap; width: 90px;">
									<label class="control-label">海外（外国語）</label>
								</div>
								<div class="col-md-6">
									<div class="talkappi-checkbox js-payment-setting-foreign" data-name="payment_setting_foreign" data-value='<?php echo json_encode($payment_setting['foreign'])?>' data-source='<?php echo json_encode($_codes['38'], JSON_UNESCAPED_UNICODE) ?>'></div>
								</div>
							</div>
							<div class="form-group" id="card-provider-setting">
								<label class="control-label col-md-3">プロバイダー</label>
								<div class="col-md-3">
									<?php echo Form::select('card_provider', $card_payments, $card_provider, array('id' => 'card_provider', 'class' => 'form-control')) ?>
								</div>
							</div>
							<div class="form-group" id="card-provider-setting-detail">
								<label class="control-label col-md-3">JTB Book&Pay設定</label>
								<div class="col-md-2" style="width:190px;">
									<input name="talkappi_facility_code" id="talkappi_facility_code" maxlength="32" type="text" class="form-control" value="<?php echo $talkappi_facility_code ?>" placeholder="talkappi連携用施設コード">
								</div>
								<div class="col-md-2">
									<input name="jtb_facility_code" id="jtb_facility_code" maxlength="32" type="text" class="form-control" value="<?php echo $jtb_facility_code ?>" placeholder="JTB施設コード">
								</div>
								<div class="col-md-2">
									<input name="jtb_user" id="jtb_user" maxlength="32" type="text" class="form-control" value="<?php echo $jtb_user ?>" placeholder="ログインID">
								</div>
								<div class="col-md-2">
									<input name="jtb_password" id="jtb_password" maxlength="32" type="text" class="form-control" value="<?php echo $jtb_password ?>" placeholder="ログインPassword">
								</div>
							</div>

							<br><br>

							<div class="form-group">
								<label class="control-label col-md-2" style="font-weight: 600;">【キャンセル設定】</label>
								<div class="col-md-8">
									<hr>
								</div>
							</div>
							<div class="form-group js-cancel-policy-label">
								<label class="control-label col-md-3"></label>
								<div class="col-md-7">
									<a class="js-cancel-policy-add"><label class="control-label">キャンセルポリシー追加</label></a>
								</div>
							</div>
							<div class="form-group flex">
								<label class="control-label col-md-3">キャンセル</label>
								<div class="col-md-9 js-reserve-cancel" style="display: flex;">
									<div class="talkappi-radio js-cancel-ontheday" data-name="cancel_ontheday" data-value="<?php echo $cancel_ontheday ?>" data-source='{"0":"不可","1":"可能"}'></div>
									<label class="control-label" style="margin-left:24px;">チェックインの</label>
									<!-- <input class="form-control js-cancel-before-day" style="width:60px;margin-left:8px;" maxlength="2" type="number" min="0" value=""> -->
									<select class="form-control js-cancel-before-day" style="width:80px;margin-left:8px;"></select>
									<label class="control-label" style="margin-left:4px;margin-right:8px;">日</label>
									<input type="text" class="talkappi-timepicker js-cancel-before-time" value="12:00" data-minuteStep="60"/>
									<label class="control-label" style="margin-left:4px;">まで</label>
									<input name="last_cancel_time" id="last_cancel_time" type="hidden" value="<?php echo $last_cancel_time ?>">
								</div>
							</div>
							<div class="form-group">
								<label class="control-label col-md-3">キャンセル料発生後のキャンセル</label>
								<div class="col-md-3">
									<div class="talkappi-radio" data-name="cancel_afterneedpay" data-value="<?php echo $cancel_afterneedpay ?>" data-source='{"0":"不可","1":"可能"}'></div>
								</div>
							</div>
							<br><br>
							<div class="form-group">
								<label class="control-label col-md-2" style="font-weight: 600;">【予約変更設定】</label>
								<div class="col-md-8">
									<hr>
								</div>
							</div>
							<div class="form-group flex">
								<label class="control-label col-md-3">予約変更</label>
								<div class="col-md-9 js-reserve-modify" style="display: flex;">
									<div class="talkappi-radio js-cancel-ontheday" data-name="modifiable_flg" data-value="<?php echo $modifiable_flg ?>" data-source='{"0":"不可","1":"可能"}'></div>
									<label class="control-label" style="margin-left:24px;">チェックインの</label>
									<!-- <input class="form-control js-cancel-before-day" style="width:60px;margin-left:8px;" maxlength="2" type="number" min="0" value=""> -->
									<select class="form-control js-cancel-before-day" style="width:80px;margin-left:8px;"></select>
									<label class="control-label" style="margin-left:4px;margin-right:8px;">日</label>
									<input type="text" class="talkappi-timepicker js-cancel-before-time" value="12:00" data-minuteStep="60"/>
									<label class="control-label" style="margin-left:4px;">まで</label>
									<input name="edit_ontheday" id="edit_ontheday" type="hidden" min="0" value="<?php echo $edit_ontheday ?>">
								</div>
							</div>
							<div class="form-group">
								<label class="control-label col-md-3">宿泊日変更</label>
								<div class="col-md-3">
									<div class="talkappi-radio" data-name="modify_checkin" data-value="<?php echo $modify_checkin ?>" data-source='{"0":"不可","1":"可能"}'></div>
								</div>
							</div>
							<div class="form-group">
								<label class="control-label col-md-3">部屋タイプ変更</label>
								<div class="col-md-3">
									<div class="talkappi-radio" data-name="modify_room_type" data-value="<?php echo $modify_room_type ?>" data-source='{"0":"不可","1":"可能"}'></div>
								</div>
							</div>
							<br><br>
							<div class="form-group">
								<label class="control-label col-md-2" style="font-weight: 600;">【子供料金設定】</label>
								<div class="col-md-8">
									<hr>
								</div>
							</div>
							<div class="form-group">
								<label class="control-label col-md-3">TLリンカーン</label>
								<div class="col-md-2"></div>
								<div class="col-md-2">
									<label class="control-label">talkappi</label>
								</div>
							</div>
							<div class="form-group">
								<label class="control-label col-md-3">53:子供A1(小学生 高学年)</label>
								<div class="col-md-2">
									<input name="elementary_high_age" id="elementary_high_age" maxlength="8" type="text" class="form-control" value="<?php echo $elementary_high_age ?>" placeholder="10ｰ12">
								</div>
								<div class="col-md-2" style="width:80px">
									<label class="control-label">日本語名</label>
								</div>
								<div class="col-md-4">
									<input name="elementary_high_label" id="elementary_high_label" maxlength="30" type="text" class="form-control" value="<?php echo $elementary_high_label ?>">
								</div>
							</div>
							<div class="form-group">
								<label class="control-label col-md-3">54:子供A2(小学生 低学年)</label>
								<div class="col-md-2">
									<input name="elementary_low_age" id="elementary_low_age" maxlength="8" type="text" class="form-control" value="<?php echo $elementary_low_age ?>" placeholder="6ｰ9">
								</div>
								<div class="col-md-2" style="width:80px">
									<label class="control-label">日本語名</label>
								</div>
								<div class="col-md-4">
									<input name="elementary_low_label" id="elementary_low_label" maxlength="30" type="text" class="form-control" value="<?php echo $elementary_low_label ?>">
								</div>
							</div>
							<div class="form-group">
								<label class="control-label col-md-3">55:子供B1(幼児 食事・布団あり)</label>
								<div class="col-md-2">
									<input name="infant1_age" id="infant1_age" maxlength="8" type="text" class="form-control" value="<?php echo $infant1_age ?>" placeholder="3ｰ5">
								</div>
								<div class="col-md-2" style="width:80px">
									<label class="control-label">日本語名</label>
								</div>
								<div class="col-md-4">
									<input name="infant1_label" id="infant1_label" maxlength="30" type="text" class="form-control" value="<?php echo $infant1_label ?>">
								</div>
							</div>
							<div class="form-group">
								<label class="control-label col-md-3">56:子供B2(幼児 食事あり)</label>
								<div class="col-md-2">
									<input name="infant2_age" id="infant2_age" maxlength="8" type="text" class="form-control" value="<?php echo $infant2_age ?>" placeholder="3ｰ5">
								</div>
								<div class="col-md-2" style="width:80px">
									<label class="control-label">日本語名</label>
								</div>
								<div class="col-md-4">
									<input name="infant2_label" id="infant2_label" maxlength="30" type="text" class="form-control" value="<?php echo $infant2_label ?>">
								</div>
							</div>
							<div class="form-group">
								<label class="control-label col-md-3">57:子供C(幼児 布団あり)</label>
								<div class="col-md-2">
									<input name="baby1_age" id="baby1_age" maxlength="8" type="text" class="form-control" value="<?php echo $baby1_age ?>" placeholder="0ｰ2">
								</div>
								<div class="col-md-2" style="width:80px">
									<label class="control-label">日本語名</label>
								</div>
								<div class="col-md-4">
									<input name="baby1_label" id="baby1_label" maxlength="30" type="text" class="form-control" value="<?php echo $baby1_label ?>">
								</div>
							</div>
							<div class="form-group">
								<label class="control-label col-md-3">58:子供D(幼児 食事・布団なし)</label>
								<div class="col-md-2">
									<input name="baby2_age" id="baby2_age" maxlength="8" type="text" class="form-control" value="<?php echo $baby2_age ?>" placeholder="0ｰ2">
								</div>
								<div class="col-md-2" style="width:80px">
									<label class="control-label">日本語名</label>
								</div>
								<div class="col-md-4">
									<input name="baby2_label" id="baby2_label" maxlength="30" type="text" class="form-control" value="<?php echo $baby2_label ?>">
								</div>
							</div>
							<div class="form-group">
								<label class="control-label col-md-2" style="font-weight: 600;">【子供料金説明】</label>
								<div class="col-md-8">
									<hr>
								</div>
							</div>
							<div class="form-group">
								<label class="control-label col-md-3">説明内容</label>
								<div class="col-md-8">
									<div class="talkappi-multitext js-input-remark" title="子供料金説明" data-language='<?php echo json_encode(explode(',', $_bot->support_lang), JSON_UNESCAPED_UNICODE)?>' data-max-input="200" data-value='<?php echo(json_encode($child_price_remark, JSON_UNESCAPED_UNICODE)); ?>'></div>
								</div>
							</div>
							<div class="form-group">
								<label class="control-label col-md-2" style="font-weight: 600;"></label>
								<div class="col-md-8">
									<hr>
								</div>
							</div>
							<div class="form-actions">
								<div class="row">
									<div class="col-md-offset-3 col-md-10" style="display:flex;">
										<button type="button" id="saveButton" class="btn-larger btn-blue"><i class="fa fa-save mr10"></i>保存</button>
										<button type="button" id="copyButton" class="btn-larger btn-yellow">コピー</button>
										<?php if (count($key_list) > 1) { ?>
										<button type="button" id="deleteButton" class="btn-larger btn-red">削除</button>
										<?php } ?>
									</div>
								</div>
							</div>
						</div>
						<div class="col-md-2">

						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<!-- END PAGE CONTENT-->