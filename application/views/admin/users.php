			<!-- BEGIN PAGE HEADER-->
			<!-- BEGIN PAGE HEAD -->
			<div class="page-head">
				<!-- BEGIN PAGE TITLE -->
				<div class="page-title">
					<h1><?php echo($_active_menu_name)?><small></small></h1>
				</div>
				<!-- <PERSON>ND PAGE TITLE -->
			</div>
			<!-- END PAGE HEAD -->
			<!-- END PAGE HEADER-->
			<!-- BEGIN PAGE CONTENT-->
			<div class="row">
				<div class="col-md-12">
			        <!-- Page Content -->
			        <div id="page-wrapper">
				        <div class="">
						<?php 
							if (strlen($_active_menu) > 4) {
								$tab_menu = View::factory ('admin/menutab');
								echo($tab_menu);
							}
						?>
						<div class="edit-container">
							<div class="settings-container">				
							<input type="hidden" name="user_id" id="user_id" value="" />
							<input type="hidden" name="act" id="act" value="" />							
							<table class="table table-striped table-bordered table-hover js-data-table">
							<thead>
							<tr>
								<th>
								<?php echo __('admin.account.th.account_name') ?>
								</th>
								<th>
								<?php echo __('admin.account.th.user_role') ?>
								</th>
								<th>
								<?php echo __('admin.account.th.login_id') ?>
								</th>
								<th>
								<?php echo __('admin.account.th.available_lang') ?>
								</th>
								<th>
								<?php echo __('admin.account.th.last_login') ?>
								</th>
								<th <?php if ($_user->role_cd != "99" && $_user->role_cd != "09" && $_user->role_cd != "79") echo('style="display:none;"') ?>>
								<?php echo __('admin.common.label.operation') ?>
								</th>								
							</tr>
							</thead>

							<tbody>
							<?php
								foreach ($users as $user) {
							?>	
							<tr class="gradeX odd" role="row">
								<td class="sorting_1" style="text-align:center;vertical-align: middle;">
									 <a href="/admin/user?id=<?php echo($user['user_id'])?>"><?php echo($user['name'])?></a><br>
								</td>
								<td style="vertical-align: middle;">
									 <?php  
									 if ($user['role_cd'] == "99") {
									 	echo('<span class="badge badge-danger" style="margin-left:5px;">' . $role_kv[$user['role_cd']] . '</span>');
									 }
									 else if  ($user['role_cd'] == '80') {
									 	echo('<span class="badge badge-warning" style="margin-left:5px;">' . $role_kv[$user['role_cd']] . '</span>');
									 }
									 else {
										if (isset($role_kv[$user['role_cd']])) {
											echo('<span class="badge badge-primary" style="margin-left:5px;">' . $role_kv[$user['role_cd']] . '</span>');
										}
										// else echo('削除されたRole['.$user['role_cd'].']');
									 }
									 ?>
								</td>
								<td style="vertical-align: middle;">
									<?php echo($user['email']);
										if ($flg_operator_mode_control == 1)
										echo('<br><span class="badge badge-' . $tag_color[$_codes['23'][$user['chat_status_cd']]] . '" style="margin-left:5px;">' . $_codes['23'][$user['chat_status_cd']] . '</span>');
										if ($user['login_error_times'] >= 5) {
											echo('<span class="badge badge-success reactive" style="margin-left:5px;" sid="' . $user['user_id'] .'">'.__('admin.account.label.unfreeze').'</span>');
										}
										if ($user['email_flg'] == 1) {
											echo('<span class="badge badge-success" style="margin-left:5px;">'.__('admin.account.label.receiving_emails').'</span>');
									 	}
									 ?>
								</td>							
								<td style="vertical-align: middle;">
									<?php
									$user_langs = explode(',', $user['chat_available_lang']);
									$chat_langs = explode(',', $user['chat_lang']);
									foreach($_bot_lang as $lang_cd=>$lang_name) {
										if (in_array($lang_cd, $user_langs)) {
											if (in_array($lang_cd, $chat_langs)) {
												echo('<span class="badge badge-success" style="margin-left:5px;">' . $lang_name . '</span>');
											}
											else {
												echo('<span class="badge badge-warning" style="margin-left:5px;">' . $lang_name . '</span>');
											}
										}
										else {
											echo('<span class="badge badge-default" style="margin-left:5px;">' . $lang_name . '</span>');
										}
									}
									?>
								</td>
								<td style="vertical-align: middle; white-space: nowrap;">
									<?php 
										if (isset($user['last_login_date'])) {
											echo($user['last_login_date']);
										}
									?>
								</td>									
								<td style="text-align:center;vertical-align: middle;<?php if ($_user->role_cd != "99" && $_user->role_cd != "09" && $_user->role_cd != "19" && $_user->role_cd != "79") echo("display:none;") ?>">
									<button type="button" class="btn red action" sid="<?php echo $user['user_id'] ?>"><?php echo __('admin.common.button.delete') ?></button>
								</td>														
							</tr>
							<?php } ?>
							</tbody>
							</table>
							</div>
						</div>
					</div>
			        </div>
			        <!-- /#page-wrapper -->					
				</div>
			</div>
			<!-- END PAGE CONTENT-->
