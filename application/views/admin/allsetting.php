			<!-- B<PERSON>IN PAGE HEADER-->
			<!-- BEGIN PAGE HEAD -->
			<div class="page-head">
				<!-- BEGIN PAGE TITLE -->
				<div class="page-title">
					<h1>ボット設定マトリックス<small></small></h1>
				</div>
				<!-- <PERSON>ND PAGE TITLE -->
			</div>
			<!-- END PAGE HEAD -->
			<!-- END PAGE HEADER-->
			<!-- BEGIN PAGE CONTENT-->
			<div class="row">
				<div class="col-md-12">
			        <!-- Page Content -->
			        <div id="page-wrapper">
			        <div class="portlet light">
						<div class="portlet box">
							<div class="portlet-body">
							<div class="form-body">		
								<div class="form-group">
									<div class="col-md-2">
										<?php echo Form::select('bot_class_cd', $bot_class_array, $post['bot_class_cd'], array('id'=>'bot_class_cd','class'=>'form-control'))?>
									</div>
									<div class="col-md-4">
										<?php echo Form::select('bot_id', $bots, $post['bot_id'], array('id'=>'bot_id','class'=>'form-control'))?>
									</div>
									<div class="col-md-1">
										<button type="submit" id="searchButton" class="btn yellow">
										<i class="fa fa-search mr10"></i>検索</button>
									</div>											
								</div>
							</div>							
							<table class="table table-striped table-bordered table-hover js-data-table" style="width:auto;">
							<thead>
							<tr>
							<th style="width:120px;"></th>
							<?php 
							foreach($setting_title as $k=>$v) {
								echo('<th style="width:70px;height:auto;text-align:center">');
								echo($v);
								echo('</th>');
							}
							?>												
							</thead>							
							<tbody>	
							<tr>
							<?php 
							foreach($bots as $k=>$v) {
								if ($k==0) continue;
								if (!array_key_exists($k, $settings)) continue;
								echo('<tr class="gradeX odd" style="height:200px;" role="row">');
								echo('<td>');
								echo($v);
								echo('</td>');
								foreach($setting_title as $s=>$t) {
									if (array_key_exists($s, $settings[$k])) {
										echo('<td style="color:blue;">');
										$val = $settings[$k][$s];
									}
									else {
										echo('<td>');
										$val = $settings['0'][$s];
									}
									if (is_array(json_decode($val, true))) {
										echo('<span title="' . htmlspecialchars($val) . '">json</span>');
									}
									else if (strpos($s, 'flg_') === 0) {
										if ($val == 1) {
											echo('●');
										}
										else {
											echo('✕');
										}
									}
									else {
										echo($val);
									}
									echo('</td>');
								}
								echo('</tr>');
							}
							?>
							</tbody>
							</table>							
													
							</div>
						</div>
					</div>
			        </div>
			        <!-- /#page-wrapper -->					
				</div>
			</div>
			<!-- END PAGE CONTENT-->
