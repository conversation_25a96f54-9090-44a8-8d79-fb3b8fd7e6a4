							<style>
								.add.image.round.btn:before { 
									background: url(/assets/admin/css/img/icon-filling.svg) no-repeat;
								}
							</style>
							<input type="hidden" name="new_flg" id="new_flg" value="<?php echo($new_flg)?>" /> 
							<table class="table table-striped table-bordered table-hover js-data-table">
							<thead>
							<tr>
								<th style="width:110px;">
									<?php echo __('admin.servicetable.title.date') ?>
								</th>
								<th style="width:80px;">
									<?php echo __('admin.common.label.user') ?>
								</th>
								<th>
									<?php echo __('admin.servicetable.title.type') ?>
								</th>
								<th style="display:none;"></th>
								<th style="min-width:200px;">
									<?php echo __('admin.servicetable.title.details') ?>
								</th>
								<th style="display:none;"></th>
								<th style="display:none;"></th>
								<th style="width:60px;">
									<?php echo __('admin.servicetable.title.contact') ?>
								</th>
								<th style="width:100px;">
									<?php echo __('admin.servicetable.title.status') ?>
								</th>
								<th style="min-width:120px;">
									<?php echo __('admin.servicetable.title.assignee') ?>
								</th>
							</tr>
							</thead>
							<tbody id="service-body">
							
							<?php
								$width = 'display:none;';
								if ($bot_class_cd == '01' || $bot_class_cd == '03' ) {
									$width = 'width:40px;';
								}
								foreach ($services as $service) {
									if ($service['delete_flg'] == 1 && $delete_flg == 0) continue;
							?>	
							<tr class="gradeX odd" role="row">
								<td class="sorting_1" style="word-break: normal;">
								<?php echo(date('Y/m/d H:i', strtotime($service['log_time']))); ?>
									 <br/>
									 <?php echo __('admin.servicetable.label.reception_id') ?><?php echo($service['service_id']);?>
									 <br/>
									 <?php 
									 if ($service['delete_flg'] == 0) {
									 	echo('<span sid="' . $service['service_id'] . '" class="btn round light-red js-service-delete" style="margin: 5px 0;">' . __('admin.common.button.delete.full') . '</span>');
									 }
									 ?>
								</td>
								<td>
									 <img src="/assets/common/images/chat_<?php echo $service['lang_cd']?>.png" style="margin:5px;width:24px;"/>
									 <img src="/assets/common/images/icon_<?php echo $service['sns_type_cd']?>.png" style="margin:5px;width:28px;"/>
									 <br>
									 <?php 
									 if ($_user->role_cd == '99' || $_user->role_cd == '01' || $_user->role_cd == '07' || $_user->role_cd == '09')
										 echo ('<label class="badge badge-info js-memberinfo" style="cursor:pointer;" data-member-id="' . $service['member_id'] .'">' . __('admin.common.label.journey')  . '</label>');
									 ?>
								</td>
								<td>
									<?php echo($service['intent_name'])?>
								</td>
								<td style="display:none;">
									<?php echo($service['template_msg'])?>
								</td>
								<td style="word-break:break-all;">
									<?php
									echo($service['bot_name'] . '<br />');
									if ($service['reserve1']!='') {
										echo("部屋番号：" . $service['reserve1']);
										echo("<br/>");
										echo("お客様：" . $service['reserve2']);
										echo("<br/>");
									}
									?>
									<?php echo(nl2br($service['staff_msg']))?>
								</td>
								<td style="display:none;">
									<?php echo($service['member_msg'])?>
								</td>
								<td style="display:none;">
									<?php echo($service['service_data'])?>
								</td>
								<td>
									<?php
									$member_name = '';
									if ($service['sns_type_cd'] == 'wb') {
										$member_name = "Webユーザ";
									}
									else {
										if ($service['last_name'] . $service['first_name'] !='') {
											$member_name = $service['last_name'] . $service['first_name'];
										}
									}
									?>
									<a class="pop_adminchat" member_id="<?php echo$service['member_id'] ?>">
									<span class="badge badge-primary" style="margin-bottom: 5px;" ><?php echo __('admin.servicetable.label.chat') ?></span> </a>
									<br/>
									<a class="pop_talkbox" sid="<?php echo($service['service_id'])?>" lang_cd='<?php echo($service['lang_cd'])?>' sns_type_cd='<?php echo($service['sns_type_cd'])?>' member_id='<?php echo($service['member_id'])?>' member_name='<?php echo($member_name)?>'>
									<span class="badge badge-success" style="margin: 5px;display:none;" ><?php echo __('admin.servicetable.label.fixed_phase') ?></span> </a>
									<br/>
									<?php if ($service['service_data'] != '' && $bot_class_cd != '10' && false) {?>
				 				 	<a class="pop_servicebox" sid="<?php echo($service['service_id'])?>"><span class="badge badge-warning" style="margin: 5px;" ><?php echo __('admin.servicetable.title.edit') ?></span></a>
				 				 	<?php }?>
									<?php if ($has_delete == 1) {?>
										<a class="deletelog" service_id=<?php echo $service['service_id'] ?>><span class="badge badge-danger"><?php echo __('admin.servicetable.label.delete') ?></span> </a>
				 				 	<?php }?>
								</td>
								</td>
								<td>
								<div style="display: flex; flex-direction: column;" id='service-support-buttons-<?php echo($service['service_id'])?>'>
								<?php foreach($buttons as $key=>$value) {
									if ($key == '05') continue;
									$disabled = "";
									if (($service['delete_flg']== 0 && $service['service_status_cd'] == $key) || 
										($service['delete_flg']== 1 && $key == '05')) {
										$disabled = "disabled";
										$btncolor = $button_color[$value];		
									}
									else {
										$btncolor = "gray";	
									}		
								?>	
									<button type="button" <?php echo $disabled ?> class="btn btn-talkbox <?php echo($btncolor)?> action" style="margin-bottom: 10px; opacity: 1;" act="<?php echo($key)?>" sid="<?php echo($service['service_id'])?>"><?php echo($value)?></button>
									<?php }?>											
								</div>
								</td>
								<td>				
									<div class="btn round image add js-memo" service_id="<?php echo($service['service_id'])?>"><?php echo __('admin.servicetable.label.memo') ?></div>
									<?php
										echo('<div class="support-tags-' . $service['service_id'] . '">');
										$was_updated = false;
										foreach($service['support'] as $support) {
											if ($support['support_type_cd']) {
												$was_updated = true;
												$labelcolor = '#d84a38';
												if ($support['support_type_cd'] == '02') {
													$labelcolor = '#FFB848';
												} elseif ($support['support_type_cd'] == '03') {
													$labelcolor = '#1BBC9B';
												} elseif ($support['support_type_cd'] == '04') {
													$labelcolor = '#95A5A6';
												}
												$comment = '';
												if (in_array($support['support_type_cd'], ['send_mail', 'received_mail'], true)) {
													$comment = '<div style="width: calc(100% - 20px);font-size:11px;" class="js-memo-mail memo-mail" no="'. $support['id'] .'" lang_cd="' . $service['lang_cd'] . '" result_id="' . $support['result_id'] . '" mail_sender_alias="' . $support['mail_sender_alias'] . '">'. date('Y/m/d H:i', strtotime($support['upd_time']))  . ' ' . $support['title'] . '<br/>' . $memo .'</div>';
													if($support['support_type_cd'] == 'send_mail'){
														$comment .= '<div class="icon-preview-mail-sent js-memo-mail" no="' . $support['id'] . '" lang_cd="' . $service['lang_cd'] . '" style="margin: 0 0 0 auto;" result_id="' . $support['result_id'] . '" mail_sender_alias="' . $support['mail_sender_alias'] . '"></div>';
													} else if($support['support_type_cd'] == 'received_mail') {
														$comment .= '<div class="icon-preview-mail js-memo-mail" no="' . $support['id'] . '" lang_cd="' . $service['lang_cd'] . '" style="margin: 0 0 0 auto;" result_id="' . $support['result_id'] . '" mail_sender_alias="' . $support['mail_sender_alias'] . '"></div>';
													}
												} else {
													$support_label = '<span class="label" style="background-color: ' . $labelcolor . '; color: #FFF;">' . $buttons[$support['support_type_cd']] . '</span>';
													$comment = '<div style="width: 100%; font-size:11px;">'. date('Y/m/d H:i', strtotime($support['upd_time'])) . ' ' . $support['name'] . ' ' . $support_label .'</div>';
												}
												echo('<div class="small-table-pannel" style="display: flex;padding: 8px; background-color: #EFEFEF;">' . $comment . '</div>');
											} else {
												$close = '<div class="icon-cancel-small js-memo-delete" no="' . $support['no'] . '" style="margin: 0 0 0 auto; cursor: pointer"></div>';
												$comment = '<div style="width: calc(100% - 18px); font-size:11px;">'. date('Y/m/d H:i', strtotime($support['upd_time'])) . ' ' . $support['name'] . '<br/>' . nl2br($support['memo']) .'</div>';
												echo('<div class="small-table-pannel" style="display: flex;padding: 8px;">' . $comment . $close . '</div>');
											}
										}
										/*
										// display previously updated status
										// if new status is updated this record will not be displayed
										$labelcolor = '#d84a38';
										if ($service['service_status_cd'] == '02') {
											$labelcolor = '#FFB848';
										} elseif ($service['service_status_cd'] == '03') {
											$labelcolor = '#1BBC9B';
										} elseif ($service['service_status_cd'] == '04') {
											$labelcolor = '#95A5A6';
										}
										if ($was_updated == false AND $service['name'] AND $service['service_status_cd']) {
											echo('<div class="small-table-pannel" style="font-size: 11px; padding: 8px; background-color: #EFEFEF;">' . date('Y/m/d H:i', strtotime($service['upd_time'])) . ' ' . $service["name"] . ' ' . '<span class="label" style="background-color: ' . $labelcolor . '; color: #FFF;">' . $buttons[$service['service_status_cd']] . '</span>' . '</div>');
										}
											*/
										echo('</div>');
									?>	
								</td>								
							</tr>
							<?php } ?>							

							</tbody>
							</table>
