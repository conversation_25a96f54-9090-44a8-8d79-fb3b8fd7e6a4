<style>
	.talkappi-colorpicker {
		width:220px;
	}
</style>
<div class="category-separator">
	<div class="setting-header">アイコン（表示設定）</div>
</div>
<div class="lines-container" style="display:none;">
	<div class="basic-label">閉じるアイコンサイズ</div>
	<div class="col-md-1" style="padding-right:0px;">
		<input name="close-icon-size" type="text" class="form-control" placeholder="" value="<?php echo ($post['close-icon-size']) ?>">
	</div>
	<label class="control-label col-md-2" style="text-align:left;">※　0を設定する場合、非表示</label>
	<div class="basic-label">背景色</div>
	<div class="">
		<div class="talkappi-colorpicker js-theme-color" data-name="close-icon-bkcolor" data-color-format="rgba" data-value="<?php if ($post != NULL) echo ($post['close-icon-bkcolor']) ?>"></div>
	</div>
</div>
<div class="lines-container">
	<div class="basic-label">z-index</div>
	<div class="col-md-2" style="padding-right:0px;">
		<input name="z-index" type="text" class="form-control z-index-container" placeholder="" value="<?php if ($post != NULL) echo ($post['z-index']) ?>">
	</div>
</div>
<div class="lines-container">
	<div class="basic-label" style="display:none;">通知件数表示</div>
	<div class="col-md-2" style="display:none;">
		<input type="checkbox" name="show_notification" <?php if ($post['show_notification'] == 1) echo ('checked ') ?> value="1" class="make-switch" data-on-color="success" data-off-color="warning">
	</div>
	<div class="basic-label">HPを表示してから</div>
	<div class="col-md-6">
		<input name="delay_show" id="label" type="number" class="select-number-container" value="<?php echo ($post['delay_show']) ?>" placeholder="">
		<span> 秒 後にアイコンを表示する</span>
	</div>
</div>
<div class="lines-container">
	<div class="basic-label">ページを</div>
	<div class="col-md-6">
		<input name="scroll_show" id="label" type="number" class="select-number-container" value="<?php echo ($post['scroll_show']) ?>" placeholder="">
		<span> px(ピクセル) スクロール後にアイコンを表示する</span>
	</div>
</div>
<div class="lines-container">
	<div class="basic-label">アイコンを閉じたら</div>
	<div class="col-md-6">
		<input name="talkappi_reshow" id="label" type="number" class="select-number-container" value="<?php echo ($post['talkappi_reshow']) ?>" placeholder="分">
		<span> 分 後にアイコンを再表示する　<b>← パソコンの場合</b></span>
	</div>
</div>
<div class="lines-container">
	<div class="basic-label">アイコンを閉じたら</div>
	<div class="col-md-6">
		<input name="talkappi_reshow_mobile" id="label" type="number" class="select-number-container" value="<?php echo ($post['talkappi_reshow_mobile']) ?>" placeholder="分">
		<span> 分 後にアイコンを再表示する　<b>← モバイルの場合</b></span>
	</div>
</div>
<div class="category-separator">
	<div class="setting-header">アイコン（スタイル）</div>
</div>
<div class="lines-container" style="align-items: center;">
	<div class="basic-label">枠線の色</div>
	<div class="col-md-6">
		<div class="talkappi-colorpicker js-theme-color" data-color-format="rgba" data-name="icon-bdcolor" data-value="<?php if ($post != NULL) echo ($post['icon-bdcolor']) ?>"></div>
	</div>
</div>
<div class="lines-container">
	<div class="basic-label">角丸</div>
	<div class="col-md-6">
		<input name="radius" type="number" style="width:100px;" class="select-number-container" placeholder="0～100" value="<?php echo ($post['radius']) ?>">
		<span> %</span>
	</div>
</div>
<div class="lines-container">
	<div class="basic-label" style="width: 250px;">吹出を閉じたらアイコンも一緒に閉じる</div>
	<div class="col-md-1">
		<div class="talkappi-switch" data-name="close_icon_with_greeting" data-value="<?php echo $post['close_icon_with_greeting'] ?>"></div>
	</div>
</div>
<div class="lines-container">
	<div class="basic-label" style="width: 250px;">アイコンにアニメーションを追加する</div>
	<div class="col-md-1">
		<div class="talkappi-switch" data-name="chatbot_icon_animation" data-value="<?php echo $post['chatbot_icon_animation'] ?>"></div>
	</div>
</div>
<div class="flexbox" style="flex-wrap:wrap">
	<div class="device-setting-container flexbox-space-top">
		<div class="font-standard font-family-v3 device-setting-container-title">パソコン</div>
		<div class="flexbox-x-axis flexbox-space-top">
			<div class="basic-label">非表示</div>
			<div class="talkappi-switch" data-name="not_show" data-value="<?php echo $post['not_show'] ?>"></div>
		</div>
		<div class="flexbox-x-axis flexbox-space-top">
			<div class="basic-label">アイコン</div>
			<div style="width: calc(100% - 109px);" class="talkappi-upload" data-name="talkappi_file_base64" data-label="<?php echo basename($post['talkappi-file']) ?>" data-type="img" data-ratio="1:1" data-url="<?php echo ($post['talkappi-file']) ?>"></div>
		</div>
		<div style="margin-left: 132px; margin-top:16px">
			<?php if ($post['talkappi-file'] != '') echo ('<img id="talkappi_file_base64_prev" class="round-prev large" src="' . $post['talkappi-file'] . '?t=' . time() . '"style="max-width:100px;">'); ?>
		</div>
		<div class="flexbox-x-axis  flexbox-space-top">
			<div class="basic-label">サイズ(px)</div>
			<div class="device-setting-size-container">
				<input name="width" type="text" class="device-setting-size-input" placeholder="幅" value="<?php echo ($post['width']) ?>">
				<p class="device-setting-size-letter">W</p>
			</div>
			<div class="device-setting-size-container">
				<input name="height" type="text" class="device-setting-size-input" placeholder="高さ" value="<?php echo ($post['height']) ?>">
				<p class="device-setting-size-letter">H</p>
			</div>
		</div>
		<div class="flexbox-x-axis  flexbox-space-top">
			<div class="basic-label">表示位置</div>
			<div class="device-setting-size-container">
				<input name="bottom" type="text" class="device-setting-size-input" placeholder="下" value="<?php echo ($post['bottom']) ?>">
				<p class="device-setting-size-letter">Y</p>
			</div>
			<div class="device-setting-size-container">
				<input name="right" type="text" class="device-setting-size-input" placeholder="右" id="right" value="<?php echo ($post['right']) ?>">
				<p class="device-setting-size-letter">X</p>

			</div>
			<div class="col-md-1" style="padding-right:0px;">
				<?php echo Form::select('icon-posi', $posilist, $post['icon-posi'], array('style' => 'width:80px;', 'class' => 'form-control', 'id' => 'icon-posi')) ?>
			</div>
		</div>
	</div>
	<div class="device-setting-container flexbox-space-top">
		<div class="font-standard font-family-v3 device-setting-container-title">モバイル</div>
		<div class="flexbox-x-axis flexbox-space-top">
			<div class="basic-label">非表示</div>
			<div class="talkappi-switch" data-name="not_show_mobile" data-value="<?php echo $post['not_show_mobile'] ?>"></div>
		</div>
		<div class="flexbox-x-axis flexbox-space-top">
			<div class="basic-label">アイコン</div>
			<div style="width: calc(100% - 109px);" class="talkappi-upload" data-name="talkappi_m_file_base64" data-label="<?php echo basename($post['talkappi-mobile-file']) ?>" data-type="img" data-ratio="1:1" data-url="<?php echo ($post['talkappi-mobile-file']) ?>"></div>
		</div>
		<div style="margin-left:132px; margin-top:16px">
			<?php if ($post['talkappi-mobile-file'] != '') echo ('<img id="talkappi_m_file_base64_prev" class="round-prev large" src="' . $post['talkappi-mobile-file'] . '?t=' . time() . '"style="max-width:100px;">'); ?>
		</div>
		<div class="flexbox-x-axis flexbox-space-top">
			<div class="basic-label">サイズ(px)</div>
			<div class="device-setting-size-container">
				<input name="width-m" type="text" class="device-setting-size-input" placeholder="幅" value="<?php echo ($post['width-m']) ?>">
				<p class="device-setting-size-letter">W</p>
			</div>
			<div class="device-setting-size-container">
				<input name="height-m" type="text" class="device-setting-size-input" placeholder="高さ" value="<?php echo ($post['height-m']) ?>">
				<p class="device-setting-size-letter">H</p>
			</div>
		</div>
		<div class="flexbox-x-axis  flexbox-space-top">
			<div class="basic-label">表示位置</div>
			<div class="device-setting-size-container">
				<input name="bottom-m" type="text" class="device-setting-size-input" placeholder="下" value="<?php echo ($post['bottom-m']) ?>">
				<p class="device-setting-size-letter">Y</p>
			</div>
			<div class="device-setting-size-container">
				<input name="right-m" type="text" class="device-setting-size-input" placeholder="右" id="right-m" value="<?php echo ($post['right-m']) ?>">
				<p class="device-setting-size-letter">X</p>
			</div>
			<div class="col-md-1" style="padding-right:0px;">
				<?php echo Form::select('icon-posi-m', $posilist, $post['icon-posi-m'], array('style' => 'width:80px;', 'class' => 'form-control', 'id' => 'icon-posi-m')) ?>
			</div>
		</div>
	</div>
</div>

<div class="form-group" style="display:none;">
	<div class="basic-label">アイコンの影</div>
	<div class="col-md-1" style="padding-right:0px;">
		<input name="h-shadow" type="text" class="form-control" placeholder="0" value="<?php echo ($post['h-shadow']) ?>">
	</div>
	<div class="col-md-1" style="padding-right:0px;">
		<input name="v-shadow" type="text" class="form-control" placeholder="2" value="<?php echo ($post['v-shadow']) ?>">
	</div>
	<div class="col-md-1" style="padding-right:0px;">
		<input name="blur" type="text" class="form-control" placeholder="8" value="<?php echo ($post['blur']) ?>">
	</div>
	<div class="col-md-1" style="padding-right:0px;">
		<input name="spread" type="text" class="form-control" placeholder="1" value="<?php echo ($post['spread']) ?>">
	</div>
</div>


<div class="category-separator">
	<div class="setting-header">アイコン（最小化設定）</div>
</div>
<div class="lines-container">
	<div class="basic-label" style="width: 150px;">アイコン最小化可能</div>
	<div class="col-md-1">
		<div class="talkappi-switch js-minimized-on" data-name="minimized-on" data-value="<?php echo $post['minimized-on'] ?>"></div>
	</div>
</div>
<div class="js-minimized" style="display:none;">
<div class="lines-container">
	<div class="basic-label" style="width: 150px;">最小化時のスタイル</div>
	<div class="line-content">
		<div class="talkappi-radio" data-name="minimized-icon" data-value="<?php echo $post['minimized-icon'] ?>" data-source='{"0":"文字のみ", "1":"会話アイコン + 文字", "2":"チャットアイコン + 文字", "3":"ロボットアイコン + 文字"}'></div>
	</div>
</div>

<div class="lines-container">
	<div class="basic-label" style="width: 150px;">最小化時の表示文字</div>
	<div class="">
		<div class="talkappi-multitext" data-type="input" data-name="minimized-text" data-language='<?php echo json_encode(explode(',', $_bot->support_lang), JSON_UNESCAPED_UNICODE)?>' data-value='<?php echo $post['minimized-text'] ?>' data-max-input="20" title=""></div>
	</div>
</div>
<div class="lines-container" style="align-items: center;">
	<div class="basic-label" style="width: 150px;">文字とアイコンの色</div>
	<div class="">
		<div class="talkappi-colorpicker" data-name="minimized-color" data-color-format="rgba" data-value="<?php if ($post != NULL) echo ($post['minimized-color']) ?>"></div>
	</div>
</div>
<div class="lines-container" style="align-items: center;">
	<div class="basic-label" style="width: 150px;">背景色</div>
	<div class="">
		<div class="talkappi-colorpicker" data-name="minimized-bkcolor" data-color-format="rgba" data-value="<?php if ($post != NULL && $post['minimized-bkcolor'] !== "") {echo ($post['minimized-bkcolor']);} else {echo ($post['close-icon-color']);} ?>"></div>
	</div>
</div>
<div class="flexbox" style="flex-wrap:wrap">
	<div class="device-setting-container flexbox-space-top">
		<div class="font-standard font-family-v3 device-setting-container-title">パソコン</div>
		<div class="flexbox-x-axis flexbox-space-top">
				<div class="basic-label" style="width: 140px;">最小化時の表示位置</div>
				<div class="col-md-1">
					<?php echo Form::select('minimized-icon-posi', $posilist5, $post['minimized-icon-posi'], array('style' => 'width:80px;', 'class' => 'form-control', 'id' => 'minimized-icon-posi')) ?>
				</div>
				<div class="device-setting-size-container" style="margin-left:60px;">
					<input name="minimized-icon-offset" type="text" class="device-setting-size-input" placeholder="右" value="<?php echo ($post['minimized-icon-offset']) ?>">
					<p class="device-setting-size-letter">Y</p>
				</div>
		</div>
		<div class="flexbox-x-axis flexbox-space-top">
				<div class="basic-label" style="width: 140px;">表示時に最小化する</div>
				<div class="col-md-1">
					<div class="talkappi-switch" data-name="minimized-default" data-value="<?php echo $post['minimized-default'] ?>"></div>
				</div>
		</div>
	</div>
	<div class="device-setting-container flexbox-space-top">
		<div class="font-standard font-family-v3 device-setting-container-title">モバイル</div>
		<div class="flexbox-x-axis  flexbox-space-top">
			<div class="basic-label" style="width: 140px;">最小化時の表示位置</div>
			<div class="col-md-1" style="padding-right:0px;">
				<?php echo Form::select('minimized-icon-posi-m', $posilist5, $post['minimized-icon-posi-m'], array('style' => 'width:80px;', 'class' => 'form-control', 'id' => 'minimized-icon-posi-m')) ?>
			</div>
			<div class="device-setting-size-container" style="margin-left:60px;">
				<input name="minimized-icon-offset-m" type="text" class="device-setting-size-input" placeholder="右" value="<?php echo ($post['minimized-icon-offset-m']) ?>">
				<p class="device-setting-size-letter">Y</p>
			</div>
		</div>
		<div class="flexbox-x-axis flexbox-space-top">
				<div class="basic-label" style="width: 140px;">表示時に最小化する</div>
				<div class="col-md-1">
					<div class="talkappi-switch" data-name="minimized-default-m" data-value="<?php echo $post['minimized-default-m'] ?>"></div>
				</div>
		</div>
	</div>
</div>
</div>

<div class="category-separator">
	<div class="setting-header">チャットウィンドウ（表示設定）
	</div>
</div>
<div class="lines-container">
	<div class="basic-label">表示位置</div>
	<div class="device-setting-size-container" style="margin-left:45px;">
		<input name="w-bottom" type="text" class="device-setting-size-input" placeholder="下" value="<?php if ($post != NULL) echo ($post['w-bottom']) ?>">
		<p class="device-setting-size-letter">Y</p>
	</div>
	<div class="device-setting-size-container">
		<input name="w-right" type="text" class="device-setting-size-input" placeholder="右" value="<?php if ($post != NULL) echo ($post['w-right']) ?>">
		<p class="device-setting-size-letter">X</p>
	</div>
	<div class="col-md-1" style="padding-right:0px;">
		<?php echo Form::select('t-w-right', $posilist2, $post['t-w-right'], array('style' => 'width:80px;', 'class' => 'form-control', 'id' => 't-w-right')) ?>
	</div>
</div>
<div class="lines-container">
	<div class="basic-label">ウィンドウのサイズ</div>
	<div class="device-setting-size-container" style="margin-left:45px;">
		<input name="w-width" type="text" class="device-setting-size-input" placeholder="幅" value="<?php if ($post != NULL) echo ($post['w-width']) ?>">
		<p class="device-setting-size-letter">W</p>
	</div>
	<div class="device-setting-size-container">
		<input name="w-height" type="text" class="device-setting-size-input" placeholder="高" value="<?php if ($post != NULL) echo ($post['w-height']) ?>">
		<p class="device-setting-size-letter">H</p>
	</div>
</div>
<div class="form-group" style="display:none;">
	<div class="basic-label">JSコード</div>
	<div class="col-md-8">
		<div id="jscode" class="alert alert-success">
		</div>
		上記コードをお客様のホームページに埋込ください。
	</div>
</div>

<!-- 吹き出し -->
<div style="display:none;">
    <input type="hidden" name="greeting-color" value="<?php if ($post != NULL) echo ($post['greeting-color']) ?>">
    <input type="hidden" name="greeting-bkcolor" value="<?php if ($post != NULL) echo ($post['greeting-bkcolor']) ?>">
    <input type="hidden" name="greeting-bdcolor" value="<?php if ($post != NULL) echo ($post['greeting-bdcolor']) ?>">
    <input type="hidden" name="close-icon-color" value="<?php if ($post != NULL) echo ($post['close-icon-color']) ?>">
    <input type="hidden" name="greeting-font-size" value="<?php echo ($post['greeting-font-size']) ?>">
    <input type="hidden" name="greeting-bottom" value="<?php echo ($post['greeting-bottom']) ?>">
    <input type="hidden" name="greeting-right" value="<?php echo ($post['greeting-right']) ?>">
    <input type="hidden" name="greeting-posi" value="<?php echo ($post['greeting-posi']) ?>">
    <input type="hidden" name="greeting-font-size-m" value="<?php echo ($post['greeting-font-size-m']) ?>">
    <input type="hidden" name="greeting-bottom-m" value="<?php echo ($post['greeting-bottom-m']) ?>">
    <input type="hidden" name="greeting-right-m" value="<?php echo ($post['greeting-right-m']) ?>">
    <input type="hidden" name="greeting-posi-m" value="<?php echo ($post['greeting-posi-m']) ?>">
    <input type="hidden" name="greeting-width" value="<?php echo ($post['greeting-width']) ?>">
    <input type="hidden" name="greeting-h-shadow" value="<?php echo ($post['greeting-h-shadow']) ?>">
    <input type="hidden" name="greeting-v-shadow" value="<?php echo ($post['greeting-v-shadow']) ?>">
    <input type="hidden" name="greeting-blur" value="<?php echo ($post['greeting-blur']) ?>">
    <input type="hidden" name="greeting-spread" value="<?php echo ($post['greeting-spread']) ?>">
    <input type="hidden" name="greeting-radius" value="<?php echo ($post['greeting-radius']) ?>">
	<input type="hidden" name="otaprice-greeting-txtcolor" value="<?php if ($post != NULL) echo ($post['otaprice-greeting-txtcolor']) ?>">
    <input type="hidden" name="otaprice-greeting-bkcolor" value="<?php if ($post != NULL) echo ($post['otaprice-greeting-bkcolor']) ?>">
    <input type="hidden" name="otaprice-greeting-bdcolor" value="<?php if ($post != NULL) echo ($post['otaprice-greeting-bdcolor']) ?>">
    <input type="hidden" name="otaprice-close-icon-color" value="<?php if ($post != NULL) echo ($post['otaprice-close-icon-color']) ?>">
    <input type="hidden" name="otaprice-header-bkcolor" value="<?php if ($post != NULL) echo ($post['otaprice-header-bkcolor']) ?>">
    <input type="hidden" name="otaprice-greeting-width" value="<?php echo ($post['otaprice-greeting-width']) ?>">
    <input type="hidden" name="otaprice-greeting-height" value="<?php echo ($post['otaprice-greeting-height']) ?>">
    <input type="hidden" name="otaprice-greeting-bottom" value="<?php echo ($post['otaprice-greeting-bottom']) ?>">
    <input type="hidden" name="otaprice-greeting-right" value="<?php echo ($post['otaprice-greeting-right']) ?>">
    <input type="hidden" name="otaprice-greeting-posi" value="<?php echo ($post['otaprice-greeting-posi']) ?>">
    <input type="hidden" name="otaprice-greeting-width-m" value="<?php echo ($post['otaprice-greeting-width-m']) ?>">
    <input type="hidden" name="otaprice-greeting-height-m" value="<?php echo ($post['otaprice-greeting-height-m']) ?>">
    <input type="hidden" name="otaprice-greeting-bottom-m" value="<?php echo ($post['otaprice-greeting-bottom-m']) ?>">
    <input type="hidden" name="otaprice-greeting-right-m" value="<?php echo ($post['otaprice-greeting-right-m']) ?>">
    <input type="hidden" name="otaprice-greeting-posi-m" value="<?php echo ($post['otaprice-greeting-posi-m']) ?>">
</div>
