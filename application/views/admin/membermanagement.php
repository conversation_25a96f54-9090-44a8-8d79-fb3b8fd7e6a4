<script>
	const _program_id = <?php echo !empty($program_id) ? '"' . $program_id . '"' : 'null'; ?>;
	const _ranks = <?php echo !empty($ranks) ? json_encode(array_column($ranks, 'name', 'rank_id')) : '[]'; ?>;
	const _display_old_member = <?php echo !empty($post['display_old_member']) ? $post['display_old_member'] : 0; ?>;
</script>

<style>
	table.table thead tr th {
		font-size: 14px;
		font-weight: 600;
		font-stretch: normal;
		font-style: normal;
		line-height: normal;
		letter-spacing: normal;
		-webkit-font-smoothing: auto;
	}

	.js-multi-select-bot-container .bs-select .dropdown-menu {
		width: fit-content;
	}

	.check-container {
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.icon-check {
		cursor: pointer;
		margin: 0;
		padding: 0;
	}

	.icon-check.checked {
		background: url("/assets/admin/css/img/icon-check.svg") no-repeat center;
		background-size: contain;
		border: none;
	}

	.member-name-link, .member-point-link {
		color: #245BD6;
		cursor: pointer;
	}

	/* mobile */
	@media screen and (max-width: 768px) {
		#add-member,
		#export-csv {
			display: none;
		}

		#members-table {
			position: relative;
		}

		#members-table th,
		#members-table td {
			padding: 8px 4px;
			font-size: 12px;
		}

		#members-table.show-old-members th.checkbox-column,
		#members-table.show-old-members td.checkbox-column {
			display: none;
		}

		#members-table th.member-type-column,
		#members-table td.member-type-column {
			position: sticky;
			left: 0;
			background: #fff;
			min-width: 40px;  /* 新旧会員列 */
			white-space: normal;
			z-index: 1;
		}

		#members-table th.name-column,
		#members-table td.name-column {
			position: sticky;
			left: 40px;
			background: #fff;
			max-width: 60px;  /* 名前列 */
			min-width: 60px !important;
			z-index: 1;
		}

		#members-table th:nth-child(4),
		#members-table td:nth-child(4) {
			min-width: 70px;  /* 会員タイプ列 */
		}

		#members-table th:nth-child(5),
		#members-table td:nth-child(5) {
			min-width: 150px ;  /* 会員ID/メールアドレス列 */
		}
		#members-table th:nth-child(8),
		#members-table td:nth-child(8) {
			max-width: 80px;  /* 住所列 */
		}

		#members-table th:nth-child(10),
		#members-table td:nth-child(10) {
			max-width: 70px;  /* ポイント列 */
		}

		#members-table th:nth-child(11),
		#members-table td:nth-child(11) {
			min-width: 50px;  /* ポイント統合列 */
		}
	}

	/* PC用の固定列設定 */
	@media screen and (min-width: 769px) {
		#members-table {
			position: relative;
			border-collapse: collapse;
		}

		/* チェックボックス列が表示される場合 */
		#members-table.show-old-members th.checkbox-column,
		#members-table.show-old-members td.checkbox-column {
			position: sticky;
			left: 0;
			width: 35px;
		}

		#members-table.show-old-members th.member-type-column,
		#members-table.show-old-members td.member-type-column {
			position: sticky;
			left: 28px;
		}

		#members-table.show-old-members th.name-column,
		#members-table.show-old-members td.name-column {
			position: sticky;
			left: 83px;
		}

		/* チェックボックス列が非表示の場合 */
		#members-table:not(.show-old-members) th.member-type-column,
		#members-table:not(.show-old-members) td.member-type-column {
			position: sticky;
			left: 0;
		}

		#members-table:not(.show-old-members) th.name-column,
		#members-table:not(.show-old-members) td.name-column {
			position: sticky;
			left: 50px;
		}

		/* 固定列の背景色設定 */
		#members-table th.checkbox-column,
		#members-table th.member-type-column,
		#members-table th.name-column {
			background: #fff;
		}

		#members-table tr:nth-child(even) td.checkbox-column,
		#members-table tr:nth-child(even) td.member-type-column,
		#members-table tr:nth-child(even) td.name-column {
			background: #fff;
		}

		#members-table tr:nth-child(odd) td.checkbox-column,
		#members-table tr:nth-child(odd) td.member-type-column,
		#members-table tr:nth-child(odd) td.name-column {
			background: #f9f9f9;
		}
	}

	/* 会員詳細モーダル */
	.two-column-container {
		display: flex;
		gap: 30px;
	}

	.column {
		flex: 1;
	}

	.modal-label {
		display: flex;
		align-items: center;
		margin-bottom: 10px;
	}

	.modal-label .control-label {
		width: 100px;
		margin-right: 5px;
	}

	.modal-label input,
	.modal-label textarea {
		flex: 1;
	}

	.address-field {
		margin-bottom: 8px !important;
	}

	.address-field label {
		margin-bottom: 2px;
	}

	.address-fields {
		margin-top: 5px;
	}

</style>
<?php $page_type = substr($_action, 0, strlen($_action) - 1);?>
<input type="hidden" id="latest_member_id" name="latest_member_id" value="">
<input type="hidden" id="old_member_id" name="old_member_id" value="">
<input type="hidden" id="page" name="page" value="<?php echo isset($page) ? $page : ''?>">
<input type="hidden" id="display_old_member" name="display_old_member" value="<?php echo isset($post['display_old_member']) ? $post['display_old_member'] : ''?>">
<input type="hidden" id="display_withdrawal" name="display_withdrawal" value="<?php echo isset($post['display_withdrawal']) ? $post['display_withdrawal'] : ''?>">
<input type="hidden" id="display_merge_failed" name="display_merge_failed" value="<?php echo isset($post['display_merge_failed']) ? $post['display_merge_failed'] : ''?>">
<input type="hidden" id="sort_order" name="sort_order" value="<?php echo isset($post['sort_order']) ? $post['sort_order'] : ''?>">

<div class="content-container light-gray">
    <div class="form-group">
        <label class="control-label col-md-1">登録日</label>
        <div class="col-md-5">
            <div class="talkappi-datepicker-range">
                <input name="start_date" value="<?php echo htmlspecialchars($post['start_date'] ?? ''); ?>"/><p>〜</p>
                <input name="end_date" value="<?php echo htmlspecialchars($post['end_date'] ?? ''); ?>"/>
            </div>
        </div>
    </div>
    <div class="form-group">
		<label class="control-label col-md-1" style="white-space: nowrap;">会員ランク</label>        
        <div class="col-md-5">
            <div class="">
                <div class="talkappi-checkbox js-ranks" data-name="ranks" data-value='<?php echo json_encode($post['ranks'] ?? []); ?>' data-source='<?php echo json_encode(array_column($ranks, 'name', 'rank_id')); ?>'></div>
            </div>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-md-1" style="white-space: nowrap;">キーワード</label>
		<div class="col-md-5">
			<input type="text" name="keyword" data-name="keyword" class="form-control" style="width: 500px;" value="<?php echo htmlspecialchars($post['keyword'] ?? ''); ?>">
			<div class="search-hint" style="color: #999; font-size: 10px; margin-top: 4px; white-space: nowrap;">
				名前・ID・電話番号(ハイフンなし)・住所(都道府県・市区郡などの結合不可)から検索
			</div>
		</div>
        <div class="col-md-6" style="display: flex; justify-content: flex-end; align-items: center;">
            <span id="search-button" class="btn-smaller btn-blue"><i class="fa fa-search mr10"></i><?php echo __('admin.common.button.search') ?></span>
        </div>
    </div>
</div>

<div class="flex-x-between" style="margin-top:10px;">
	<div>会員一覧</div>
	<!-- <div><a href="/admin/memberprograms">プログラム一覧</a> > 会員一覧</div> -->
	<div class="flex-x-between">
		<button type="button" id="add-member" class="btn-smaller btn-blue"><span class="icon-add-white"></span>会員追加</button>
		<button type="button" id="export-csv" class="btn-smaller btn-white"><span class="icon-export"></span>csv出力</button>
	</div>
</div>

<div class="" style="margin-top:10px;margin-bottom:10px;white-space:nowrap;">
	<div class="selected-members-action" style="display: none;">
        <div style="display: flex; align-items: center;">
            選択した<span class="selected-count">0</span>名の
            <button type="button" id="merge-points-btn" class="btn-smaller btn-blue">
                <span class="icon-import-white" style="margin-right: 5px;"></span>ポイントを統合
            </button>
        </div>
    </div>
</div>

<div class="content-container white" style="position: relative;">    
    <div class="dataTables_wrapper">
        <div class="dataTables_header_custom" style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 10px;">
            <div style="display: flex; align-items: center;">
                <div style="margin-right: 20px; display: flex; align-items: center;">
                    <label style="margin-right: 5px;">旧会員を表示</label>
                    <span class="talkappi-switch js-display_old_member" data-name="display_old_member" data-value="<?php echo $post['display_old_member'] ?? 0; ?>"></span>
                </div>
                <div style="margin-right: 20px; display: flex; align-items: center;">
                    <label style="margin-right: 5px;">退会者を表示</label>
                    <span class="talkappi-switch js-display_withdrawal" data-name="display_withdrawal" data-value="<?php echo $post['display_withdrawal'] ?? 0; ?>"></span>
                </div>
                <?php if (($post['display_old_member'] ?? 0) == 1): ?>
                    <div style="margin-right: 20px; display: flex; align-items: center;">
                        <label style="margin-right: 5px;">自動統合失敗者を表示</label>
                        <span class="talkappi-switch js-display_merge_failed" data-name="display_merge_failed" data-value="<?php echo $post['display_merge_failed'] ?? 0; ?>"></span>
                    </div>
                <?php else: ?>
                    <input type="hidden" name="display_merge_failed" value="0">
                <?php endif; ?>
            </div>
            <div style="display: flex; align-items: center;">
                <span class="talkappi-pulldown js-sort_order" data-name="sort_order" data-value="<?php echo $post['sort_order'] ?? 'date'; ?>" data-source='{"date":"新規登録順","name":"名前順"}'></span>
            </div>
        </div>
    </div>

    <table id="members-table" class="table table-striped table-bordered table-hover js-data-table <?php echo ($post['display_old_member'] ?? 0) == 1 ? 'show-old-members' : ''; ?>">
		<thead>
			<tr>
				<?php if (($post['display_old_member'] ?? 0) == 1): ?>
					<th class="checkbox-column" style="width: 60px;"></th>
				<?php endif; ?>
				<th class="member-type-column" style="max-width: 50px;">新旧<br>会員</th>
				<th class="name-column" style="min-width: 120px;">名前</th>
				<th>会員タイプ</th>
				<th>会員ID/<br>メールアドレス</th>
				<th>生年月日</th>
				<th>電話番号</th>
				<th style="min-width: 150px;">住所</th>
				<th>登録日</th>
				<th>保有ポイント</th>
				<?php if (($post['display_old_member'] ?? 0) == 1): ?>
					<th>ポイントの統合</th>
				<?php endif; ?>
				<th class="memo-column" style="min-width: 120px;">備考</th>
				<th class="last_name_furigana_col" style="display: none;"></th>
				<th class="first_name_furigana_col" style="display: none;"></th>
			</tr>
		</thead>
		<tbody>
			<?php foreach ($members as $member): ?>
				<?php 
					$is_old_member = $member['old_member_flg'] == 1;
					if ((($post['display_old_member'] ?? 0) == 1 || !$is_old_member) && 
						(($post['display_withdrawal'] ?? 0) == 1 || $member['member_status'] != "99")): 
				?>
					<tr>
						<?php if (($post['display_old_member'] ?? 0) == 1): ?>	
							<td class="checkbox-column" style="text-align: center; vertical-align: middle;">
								<?php if ($member['member_status'] != '98'): ?>
									<div class="check-container js-check-container" style="display: flex; justify-content: center; align-items: center;">
										<span class="icon-check" data-member-id="<?php echo $member['member_id']; ?>"></span>
									</div>
								<?php endif; ?>
							</td>
						<?php endif; ?>
						<td class="member-type-column"><?php echo $is_old_member ? '旧会員' : '新会員'; ?></td>
						<td class="name-column">
							<span class="member-name-link js-member-detail" 
									data-member-id="<?php echo $member['member_id']; ?>" 
									data-member-type="<?php echo $is_old_member ? 'old' : 'new'; ?>">
								<?php echo htmlspecialchars($member['last_name'] . ' ' . $member['first_name']); ?>
							</span>
						</td>
						<td><?php echo htmlspecialchars($member['rank']['name'] ?? ''); ?></td>
						<td>
							<?php if ($is_old_member): ?>
								<div style="white-space: nowrap;">
									<div><?php echo htmlspecialchars($member['old_member_cd'] ?: '-'); ?></div>
									<div><?php echo !empty($member['email']) ? htmlspecialchars($member['email']) : '-'; ?></div>
								</div>
							<?php else: ?>
								<div style="white-space: nowrap;">
									<div><?php echo htmlspecialchars(($program_id . $member['member_cd']) ?: '-'); ?></div>
									<?php if (empty($member['old_member_cd']) && !empty($member['input_old_member_cd'])): ?>
										<div style="color: red;"><?php echo htmlspecialchars($member['input_old_member_cd']); ?>（自動統合失敗）</div>
									<?php else: ?>
										<div><?php echo !empty($member['old_member_cd']) ? htmlspecialchars($member['old_member_cd']) : '-'; ?></div>
									<?php endif; ?>
									<div><?php echo !empty($member['email']) ? htmlspecialchars($member['email']) : '-'; ?></div>
								</div>
							<?php endif; ?>
						</td>
						<td style="white-space: nowrap;"><?php echo !empty($member['birthday']) ? htmlspecialchars(date('Y/m/d', strtotime($member['birthday']))) : '-'; ?></td>
						<td style="white-space: nowrap;"><?php 
							if (!empty($member['phone'])) {
								$phone = $member['phone'];
								$formatted_phone = substr($phone, 0, 3) . '-' . substr($phone, 3, 4) . '-' . substr($phone, 7);
								echo htmlspecialchars($formatted_phone);
							} else {
								echo '-';
							}
						?></td>
						<td><?php 
							$formatted_post_cd = substr($member['post_cd'] ?? '', 0, 3) . '-' . substr($member['post_cd'] ?? '', 3);
							$full_address = sprintf('%s %s%s%s%s',
								$formatted_post_cd,'<br>',
								htmlspecialchars($member['address']['prefecture'] ?? ''),
								htmlspecialchars($member['address']['city'] ?? ''),
								htmlspecialchars($member['address']['street'] ?? ''),
								($member['address']['building'] ?? '') ? htmlspecialchars($member['address']['building']) : ''
							);
							echo $full_address;
						?></td>
						<td style="white-space: nowrap;"><?php 
							if ($member['member_status'] == '99') {
								// 退会済み会員の場合
								$join_date = $is_old_member ? $member['join_date'] : $member['join_time'];
								echo !empty($join_date) ? htmlspecialchars(date('Y/m/d', strtotime($join_date))) : '-';
								echo '<br>';
								echo '→';
								echo !empty($member['upd_time']) ? htmlspecialchars(date('Y/m/d', strtotime($member['upd_time']))) : '-';
								echo ' 退会済み';
							} else {
								// 通常の会員の場合
								$join_date = $is_old_member ? $member['join_date'] : $member['join_time'];
								echo !empty($join_date) ? htmlspecialchars(date('Y/m/d', strtotime($join_date))) : '-';
							}
						?></td>
						<td>
							<?php if ($is_old_member && $member['member_status'] == '98'): ?>
								-
							<?php else: ?>
								<span class="member-point-link js-point-detail" data-member-id="<?php echo $member['member_id']; ?>">
									<span style="display: none;"><?php echo sprintf('%012d', $member['point_balance'] + 1000000000); ?></span>
									<?php echo number_format($member['point_balance'] ?? 0) . ' pt'; ?>
								</span>
							<?php endif; ?>
						</td>
						<?php if (($post['display_old_member'] ?? 0) == 1): ?>
							<td>
								<?php if ($is_old_member): ?>
									<?php if ($member['member_status'] == '98'): ?>
										<div style="margin-top: 2px; cursor: default;" class="btn round light-green">統合済み</div>
										<?php if (isset($member['merge_info']) && 
													isset($member['merge_info']['merge_time']) && 
													strtotime($member['merge_info']['merge_time']) > 0): ?>
											<div style="font-size: 10px;">
												<?php echo date('Y/m/d H:i', strtotime($member['merge_info']['merge_time'])); ?><br><?php echo $member['merge_info']['merge_user']['name']; ?>
											</div>
										<?php endif; ?>
									<?php else: ?>
										<div style="margin-top: 2px; cursor: default;" class="btn round light-red">未統合</div>
									<?php endif; ?>
								<?php endif; ?>
							</td>
						<?php endif; ?>
						<td class="memo-column">
							<?php 
								if (!empty($member['memo_data']['memo1'])) {
									$memo = $member['memo_data']['memo1'];
									echo htmlspecialchars(mb_strlen($memo) > 30 ? mb_substr($memo, 0, 30) . '...' : $memo);
								} else {
									echo '-';
								}
							?>
						</td>
						<td class="last_name_furigana_col" style="display: none;"><?php echo $member['last_name_furigana']; ?></td>
						<td class="first_name_furigana_col" style="display: none;"><?php echo $member['first_name_furigana']; ?></td>
					</tr>
				<?php endif; ?>
			<?php endforeach; ?>
		</tbody>
	</table>

    <div class="row" style="margin-top: 20px;">
        <div class="col-md-5 col-sm-12">
            <div class="dataTables_info">
                全<?php echo number_format($total_count); ?>件中 
                <?php echo number_format(($current_page - 1) * $per_page + 1); ?>から
                <?php echo number_format(min($current_page * $per_page, $total_count)); ?>件を表示
            </div>
        </div>
        <div class="col-md-7 col-sm-12">
            <div class="dataTables_paginate paging_simple_numbers">
                <ul class="pagination">
                    <li class="paginate_button previous <?php echo ($current_page <= 1) ? 'disabled' : ''; ?>">
                        <?php if ($current_page > 1): ?>
                            <a class="page-link" data-page="<?php echo ($current_page - 1); ?>">
                                <i class="fa fa-angle-left"></i>
                            </a>
                        <?php else: ?>
                            <span class="page-link"><i class="fa fa-angle-left"></i></span>
                        <?php endif; ?>
                    </li>

                    <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                        <?php if ($i == 1 || $i == $total_pages || ($i >= $current_page - 2 && $i <= $current_page + 2)): ?>
                            <li class="paginate_button <?php echo ($current_page == $i) ? 'active' : ''; ?>">
                                <a class="page-link" data-page="<?php echo $i; ?>"><?php echo $i; ?></a>
                            </li>
                        <?php elseif (($i < $current_page - 2 && $i == 2) || ($i > $current_page + 2 && $i == $total_pages - 1)): ?>
                            <li class="paginate_button disabled"><span class="page-link">…</span></li>
                        <?php endif; ?>
                    <?php endfor; ?>

                    <li class="paginate_button next <?php echo ($current_page >= $total_pages) ? 'disabled' : ''; ?>">
                        <?php if ($current_page < $total_pages): ?>
                            <a class="page-link" data-page="<?php echo ($current_page + 1); ?>">
                                <i class="fa fa-angle-right"></i>
                            </a>
                        <?php else: ?>
                            <span class="page-link"><i class="fa fa-angle-right"></i></span>
                        <?php endif; ?>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
