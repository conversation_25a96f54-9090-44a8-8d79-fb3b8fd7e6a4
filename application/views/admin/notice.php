<link href="/assets/admin/pages/css/blog.css" rel="stylesheet"/>
<link rel="stylesheet" type="text/css" href="/assets/global/plugins/bootstrap-wysihtml5/bootstrap-wysihtml5.css"/>
<link rel="stylesheet" type="text/css" href="/assets/global/plugins/bootstrap-markdown/css/bootstrap-markdown.min.css">
<style>
.basic-label {
    width: 12em !important;
}
</style>

<!-- BEGIN PAGE HEADER-->
<!-- BEGIN PAGE HEAD -->
<div class="page-head">
	<!-- BEGIN PAGE TITLE -->
	<div class="page-title">
		<h1><?php echo($_active_menu_name)?><small></small></h1>
	</div>
	<!-- END PAGE TITLE -->
</div>
<!-- END PAGE HEAD -->
<!-- END PAGE HEADER-->
<!-- BEGIN PAGE CONTENT-->

<input type="hidden" id="notice_tag_cd" name="notice_tag_cd" value="<?php echo $post['notice_tag_cd'] ?>" />
<input type="hidden" id="notice_type_cd" name="notice_type_cd" value="<?php echo $post['notice_type_cd'] ?>" />
<input type="hidden" id="notice_status_cd" name="notice_status_cd" value="<?php echo $post['notice_status_cd'] ?>" />
<input type="hidden" id="range" name="range" value="" />
<!-- <input type="hidden" id="notice_id" name="notice_id" value="<?php echo $post['notice_id'] ?>" /> -->

<nav class="top-nav">
	<ul>
		<li class="active">
			<a><?php echo __('admin.notice.basic_setting') ?></a>
		</li>
		<?php if ($notice_id != NULL) { ?>
			<li>
				<a href="/<?php echo $_path?>/noticedesc?id=<?php echo $notice_id?>&lang=<?php echo $lang_edit ?>"><?php echo __('admin.notice.contents') ?></a>
			</li>
		<?php }?>
	</ul>
</nav>

<div class="content-container white border">
	<!-- 基本設定 -->
	<div class="section-container">
		<!-- お知らせ名 -->
		<div class="lines-container">
			<div class="basic-label"><?php echo __('admin.notice.name') ?></div>
			<div class="col-md-8">
				<input type="text" id="notice_name" name="notice_name" value="<?php echo htmlspecialchars($post['notice_name'])?>" class="text-input-longer">
				<p style="color: #245BD6;"><?php echo __('admin.notice.name.explanation') ?></p> 
			</div>
		</div>
		<!-- 実施期間 -->
		<div class="lines-container">
			<div class="basic-label"><?php echo __('admin.common.label.listing_period'); ?></div>
			<div class="col-md-8">
				<div class="talkappi-datepicker-range" data-value='<?php echo json_encode($post['range']) ?>' data-time-format="hh:mm"></div>
			</div>
		</div>
		<!-- 表示方法 -->
		<div class="lines-container">
			<label class="basic-label"><?php echo __('admin.common.label.template') ?></label>
			<div class="template-select-radio col-md-8">
			<input type="radio" name="notice_type_cd" value="02" id="02" <?php if ($post['notice_type_cd'] == "02") echo 'checked'?>>
				<label for="02">
					<img src="../../assets/apps/notice/template_notice.svg" width="160" height="224">
				</label>
			<input type="radio" name="notice_type_cd" value="01" id="01" <?php if($post['notice_type_cd']=="01") echo 'checked'?>>
				<label for="01">
					<img src="../../assets/apps/notice/template_half_modal.svg" width="160" height="224">
				</label>
			</div>
		</div>
		<!-- ボタン -->
		<div class="actions-container" style="margin: 60px 0 0 140px;">
			<div class="btn-larger btn-blue x-first js-action-save"><?php echo __('admin.common.button.save'); ?></div>
			<div class="btn-larger btn-white js-action-back"><?php echo __('admin.common.button.return_to_list'); ?></div>
			<?php if ($notice_id != NULL) { ?>
				<div class="btn-larger btn-red-border js-action-delete">
					<span class="icon-delete"></span>
				</div>
			<?php }?>
		</div>
	</div>
</div>
