<?php 
$model = new Model_Adminmodel();
?>
<input type="hidden" id="message" value="<?php echo $message?>" />
<input type="hidden" id="act" name="act" value="" />
<nav class="top-nav">
	<ul class="">
		<li class="active">
			<a class="func-menu" href="/admin/<?php echo $msg_action_type?>msg?id=<?php echo($msg_id)?>&back=<?php echo $back_action?>">
				<?php echo __('admin.itme.itemmeu.info') ?>
			</a>
		</li>
		<?php if ($msg_id != NULL) foreach($_bot_lang as $lang_cd=>$lang_name) {
			echo('<li>');
			echo('<a class="func-menu" href="/admin/' . $msg_action_type . 'msgdesc?lang='. $lang_cd . '">' . $lang_name . '</a></li>');
		}?>
		<li class="">
			<a class="func-menu" href="/admin/<?php echo $msg_action_type?>msglist">
				<?php echo __('admin.itme.itemmeu.back') ?>
			</a>
		</li>							
	</ul>
</nav>
<div class="content-container white border">
	<div class="form-body">
		<?php if ($_bot_id == 0) { ?>
		<div class="form-group">
			<label class="control-label col-md-2 label-fix-6"><?php echo __('admin.common.label.industry') ?></label>
			<div class="col-md-3">
				<?php 
				echo Form::select('bot_class_cd', $bot_class, $post['bot_class_cd'], array('id'=>'bot_class_cd','class'=>'form-control'))
				?>
			</div>
		</div>	
		<?php }?>
		<div class="form-group">
			<label class="control-label col-md-2 label-fix-6"><?php echo __('admin.common.label.type') ?></label>
			<div class="col-md-3">
				<div class="talkappi-pulldown" data-name="msg_type_cd" data-value="<?php echo $msg_type_cd?>" data-source='<?php echo json_encode($msg_type_list, JSON_UNESCAPED_UNICODE) ?>'></div>
			</div>
		</div>
		<div class="form-group">
			<label class="control-label col-md-2 label-fix-6"><?php echo __('admin.common.label.classification') ?></label>
			<div class="col-md-3">
				<?php 
					if ($msg_id == NULL && $_bot_id > 0) {
						unset($msg_class['98']);
						unset($msg_class['99']);
					}
				?>
				<div class="talkappi-pulldown" data-name="msg_class_cd" data-value="<?php echo $post['msg_class_cd']?>" data-source='<?php echo json_encode($msg_class, JSON_UNESCAPED_UNICODE) ?>'></div>
			</div>
		</div>
		<?php if ($_user->role_cd == '99') { ?>							
		<div class="form-group">
			<label class="control-label col-md-2 label-fix-6"><?php echo __('admin.common.label.code') ?></label>
			<div class="col-md-3">
				<input name="msg_cd" id="msg_cd" maxlength="60" type="text" class="form-control" style="width:300px;" placeholder="" <?php if ($mode != 'self' && $_bot_id > 0) echo('readonly');?> value="<?php if ($post != NULL) echo($post['msg_cd'])?>">
			</div>
		</div>
		<?php } ?>
		<div class="form-group">
			<label class="control-label col-md-2 label-fix-6"><?php echo __('admin.common.label.name') ?></label>
			<div class="col-md-3">
				<input name="msg_name" id="msg_name" type="text" class="form-control" style="width:400px;" placeholder="" <?php if ($mode != 'self' && $_bot_id > 0) echo('readonly');?> value="<?php if ($post != NULL) echo($post['msg_name'])?>">
			</div>
		</div>
		<div class="form-group" <?php if ($msg_menu_cd == 'sys') echo ('style="display:none;"');?>>
			<label class="control-label col-md-2 label-fix-6"><?php echo __('admin.common.label.listing_period') ?></label>
			<div class="col-md-4">
				<div class="talkappi-datepicker-range" data-name="range" data-value='<?php echo json_encode($post['range']) ?>' data-time-format="hh:mm"></div>
			</div>																				
		</div>
		<?php 
		if ($msg_type_cd != 'rcm') {
			foreach($item_data_def as $key=>$def) {
				if ($def['type'] == 'sel' || $def['type'] == 'opt' || $def['type'] == 'chk') {
					$def['list'] = $model->get_customize_list($def);
					if (count($def['list']) == 0) continue;
				}
				echo('<div class="form-group">');
				echo('<label class="control-label col-md-2 label-fix-6">' . $def['title'] . '</label>');
				echo('<div class="col-md-8">');
				if (!array_key_exists($key, $item_data)) $item_data[$key] = '';
				if ($def['type'] == 'opt') {
					$radio_value = "";
					if (array_key_exists('normal', $def['list'])) {
						$radio_value = array_key_exists($item_data[$key], $def['list']) ? $item_data[$key] : 'normal';
					} else {
						$radio_value = array_key_exists($item_data[$key], $def['list']) ? $item_data[$key] : '';
					}
					echo('<div class="talkappi-radio" data-name="' . $key . '" data-value="' . $radio_value . '" data-source=\'' .  json_encode($def['list'], JSON_UNESCAPED_UNICODE) .'\'></div>');
				} 
				else if ($def['type'] == 'sel') {
					echo('<div class="talkappi-pulldown" data-name="' . $key . '" data-value="' . $item_data[$key] . '" data-source=\'' .  json_encode($def['list'], JSON_UNESCAPED_UNICODE) .'\'></div>');
				}
				else if ($def['type'] == 'chk') {
					echo('<div class="talkappi-checkbox" data-name="' . $key . '" data-value=\'' . json_encode($item_data[$key]) . '\' data-source=\'' .  json_encode($def['list'], JSON_UNESCAPED_UNICODE) .'\'></div>');
				}
				else if ($def['type'] == 'num' || $def['type'] == 'txt') {
					$style = '';
					if ($def['type'] == 'num') {
						$style = 'style="width:100px;text-align: right;"';
					}
					if (array_key_exists($key, $item_data)) {
						$value = $item_data[$key];
					}
					else {
						$value = '';
					}
					echo('<input name="' . $key . '" type="text" class="form-control" value="' .  $value . '"' . $style . ' >');
				}
				echo('</div>');
				echo('</div>');
			}
		}
		else {							
			if ($post['msg_data']!= '' && $_user->role_cd=='99') {?>
			<div class="form-group">
				<label class="control-label col-md-2 label-fix-6"><?php echo __('admin.common.label.related_data') ?>※</label>
				<label class="control-label col-md-10" style="text-align:left;"><?php echo $post['msg_data']?></label>
			</div>
			<?php 
			}
		}?>
	</div>
	<div class="form-actions">
		<div class="row">
			<label class="control-label col-md-2 label-fix-6"></label>
			<div class="col-md-10 flex">
				<?php 
					if ($_bot_id == 0) {
						echo('<button type="button" class="btn-larger btn-blue js-action-save">' . __('admin.common.button.save') . '</button>');
					}
					else {
						if ($mode == 'inherit') {
							echo('<button type="button" class="btn-larger btn-blue js-action-save">' . __('admin.common.button.customize') . '</button>');
						}
						else {
							echo('<button type="button" class="btn-larger btn-blue js-action-save">' . __('admin.common.button.save') . '</button>');
						}
					}
					if ($msg_type_cd == 'rcm' && $msg_id != null && $mode != 'inherit') {
						echo('<button type="button" class="btn-larger btn-white js-action-line">Lineメニュー作成</button>');
						echo('<button type="button" class="btn-larger btn-white js-action-default">デフォルトメニュー設定</button>');
					}
				?>
				<button type="button" onclick="top.location='/admin/<?php echo $back_action?>'" class="btn-larger btn-white js-action-back"><?php echo __('admin.common.button.back') ?></button>
			</div>
		</div>
	</div>
</div>
