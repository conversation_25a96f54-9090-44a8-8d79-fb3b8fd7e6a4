<!-- BEGIN PAGE HEADER-->
<!-- BEGIN PAGE HEAD -->
<div class="page-head">
	<!-- BEGIN PAGE TITLE -->
	<div class="page-title">
		<h1><?php echo($_active_menu_name)?><small></small></h1>
	</div>
	<!-- <PERSON>ND PAGE TITLE -->
</div>
<!-- END PAGE HEAD -->
<!-- END PAGE HEADER-->
<!-- BEGIN PAGE CONTENT-->

<input type="hidden" id="notice_id" name="notice_id" value="" />

<div style="margin: 10px;">
    <div class="flex-x-between">
        <div></div>
        <span class="btn-smaller btn-blue js-new-notice">
            <span class="icon-add-white"></span>
            <?php echo __('admin.common.button.create_new') ?>
        </span>
    </div>
    <p>※<?php echo __('admin.notice.explanation') ?></p>
</div>

<div class="content-container white">
    <table class="table table-striped table-bordered table-hover js-data-table">
        <thead>
            <tr>
                <th>ID</th>
                <th><?php echo __('admin.notice.name') ?></th>
                <th><?php echo __('admin.notices.very.display') ?></th>
                <th><?php echo __('admin.common.label.listing_period') ?></th>
                <th><?php echo __('admin.common.label.listing_type') ?></th>
                <th><?php echo __('admin.common.label.multilingual_data') ?></th>
                <th><?php echo __('admin.common.label.registration_date') ?></th>
                <th><?php echo __('admin.common.label.last_update') ?></th>
                <th><?php echo __('admin.common.label.operation') ?></th>
            </tr>
        </thead>
        <tbody>
            <?php
            foreach ($notices as $notice) {
                ?>
                <?php $isTemplate = (bool) ($notice['notice_id'] == 0); ?>
                <tr class="gradeX odd" role="row">
                    <td><?php echo ($notice['notice_id']);?></td>
                    <!-- お知らせ名 -->
                    <td>
                        <?php if ($isTemplate && $_bot->bot_id !== 0) { ?>
                            <?php echo $notice['notice_name'] ?>
                        <?php } else { ?>
                            <a href="/<?php echo $_path?>/notice?id=<?php echo ($notice['notice_id']) ?>"><?php echo $notice['notice_name'] ?></a>
                        <?php } ?>
                    </td>
                    <!-- VERYで表示 -->
                    <td>
                        <div style="display:flex;flex-wrap: wrap;margin-bottom:8px;gap: 4px;">
                            <?php
                            $foundMatch = false;
                            foreach ($very_support_lang as $lang) {
                                if (isset($very_displayd_notices[$lang]) && in_array($notice['notice_id'], $very_displayd_notices[$lang])) {
                                    $foundMatch = true;
                                    echo '<label class="btn light-blue">' . $_codes['02'][$lang] . '</label>';
                                }
                            }
                            if (!$foundMatch) {
                                echo __('admin.common.label.undisplay');
                            }
                            ?>
                        </div>
                    </td>
                    <!-- 表示期間 -->
                    <td style="white-space: nowrap;">
                      <?php
                      if (!empty($notice['display_start_date']) && !empty($notice['display_end_date'])) {
                        echo '<p>' . substr($notice['display_start_date'], 0, 10) . '～</p>';
                        echo '<p>' . substr($notice['display_end_date'], 0, 10) . '</p>';
                      } else {
                          echo "-";
                      }
                      ?>
                    </td>
                    <!-- 表示タイプ -->
                    <td>
                        <?php
                        if ($notice['notice_type_cd'] == '01') {
                            echo '<label class="btn light-purple">' . __('admin.notices.half.modal') . '</label>';
                        } else {
                            echo '<label class="btn light-purple">' . __('admin.notices.notice.area') . '</label>';
                        }
                        ?>
                    <!-- 多言語登録 -->
                    <td>
                        <?php
                        foreach($_bot_lang as $cd=>$lang) {
                            //veryの対応言語のみ表示
                            if (!in_array($cd, $very_support_lang)) continue;
                            $editedLang = $notice['languages'] ? explode(',', $notice['languages']) : [];
                            $color = in_array($cd, $editedLang) ? 'light-green' : 'light-gray';
                            if ($isTemplate && $_bot->bot_id !== 0) {
                                echo '<label class="btn round ' . $color . '" style="display: inline-block;margin-bottom:8px;">' . $lang . '</label>';
                            } else{
                                echo '<a class="func-menu" href="noticedesc?id=' . $notice['notice_id'] . '&lang=' . $cd . '" style="display: inline-block;margin-bottom:8px;">';
                                echo '<label class="btn round ' . $color . '">' . $lang . '</label>';
                                echo '</a>';
                            }
                        }
                        ?>
                    </td>
                    <!-- 登録日 -->
                    <td style="white-space: nowrap;">
                      <?php echo substr($notice['create_time'], 0, 10);?>
                    </td>
                    <!-- 更新日時 -->
                    <td style="white-space: nowrap;">
                    
                    <?php
                        // 日付のみを表示
                        if (!empty($notice['upd_time'])) {
                            echo '<p>' . substr($notice['upd_time'], 0, 16) . '</p>';
                        }
                        if (!empty($notice['name']) && !$isTemplate) {
                            echo '<p>' . $notice['name'] . '</p>';
                        }
                    ?>
                    </td>
                    <td>
                    <div style="margin-top: 2px;" class="btn round image copy js-copy" data-notice_id="<?php echo ($notice['notice_id'] ) ?>"><?php echo __('admin.common.button.clone') ?></div>
                    </td>
                </tr>
            <?php } ?>
        </tbody>
    </table>
</div>