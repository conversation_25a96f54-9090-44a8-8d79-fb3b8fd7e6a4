<style>
    .shifted-label {
	  padding-top: 5px;
	  width: 45px;
    }
</style>

<script type="text/javascript">
const _reserve_info = <?php echo(json_encode($reserve_info, JSON_UNESCAPED_UNICODE))?>;
const api_server = '<?php echo $engine_url?>components/booking/';
</script>
<?php echo $menu?>
<div class="content-container white border">
	<div class="form-body">
		<div class="form-group">								
		<label class="control-label col-md-1">ステータス</label>
		<label class="control-label col-md-3" style="text-align:left;color: #245BD6;">
		<?php 
			if ($order->order_status_cd == '04') $order->order_status_cd = '01';
			if ($order->order_status_cd == '05') $order->order_status_cd = '02';
			echo($_codes['25'][$order->order_status_cd]);
		?>
		</label>
		<label class="control-label col-md-1">お客様情報</label>
		<label class="control-label col-md-7" style="text-align:left;color: #245BD6;"><?php echo('<i class="fas fa-user"></i>　'. $order->checkin_name)?>　<?php echo('<i class="fas fa-phone-alt"></i>　'. $order->checkin_tel)?>　<?php echo('<i class="fas fa-envelope"></i>　' . $order->checkin_mail)?></label>				
	</div>

	<div class="form-group">								
		<label class="control-label col-md-1">予約番号</label>
		<label class="control-label col-md-3" style="text-align:left;color: #245BD6;"><?php echo($order->order_no)?></label>
		<label class="control-label col-md-1">宿泊期間</label>
		<label class="control-label col-md-3" style="text-align:left;color: #245BD6;"><?php echo($order->checkin_date)?>～<?php echo($order->checkout_date)?></label>	
	</div>

	<div class="form-group">
		<label class="control-label col-md-1">宿泊プラン</label>
		<label class="control-label col-md-3" style="text-align:left;color: #245BD6;"><?php echo($order_data['rate_plan_name'])?></label>
		<label class="control-label col-md-1">部屋タイプ</label>
		<label class="control-label col-md-3" style="text-align:left;color: #245BD6;"><?php echo($order_data['room_type_name'])?></label>																
	</div>
	<?php if (array_key_exists('remark', $order_data)) {?>
	<div class="form-group">
		<label class="control-label col-md-1">確認事項</label>
		<label class="control-label col-md-7" style="text-align:left;color: #245BD6;"><?php echo(nl2br($order_data['remark']))?></label>													
	</div>
	<?php }?>
	</div>	
	<input type="hidden" name="message" id="message" value="<?php echo($message)?>" />
	<input type="hidden" name="order_id" id="order_id" value="<?php echo($order->order_id)?>" />
	<input type="hidden" name="room_list" id="room_list" value="" />
	<table class="table table-striped table-bordered table-hover js-data-table">
		<thead>
			<tr>
				<th>処理番号</th>
				<th>処理日時</th>
				<th>連携ID</th>
				<th style="width: 150px;">処理データ</th>
			</tr>
		</thead>
		<tbody>
			<?php
				foreach ($order_history as $item) {
			?>
			<tr class="gradeX odd" role="row">
				<td><?php echo($item->no)?></td>
				<td><?php echo($item->order_date)?></td>
				<td>
				<?php 
					echo($item->link_id);
					if ($item->cancel_link_id != NULL) {
						echo('<span style="color:red;margin-left:5px;">取消');
						echo($item->cancel_link_id);
						echo('</span>');
					}
				?>
				</td>
				<td></td>																								
			</tr>
			<?php } ?>
		</tbody>
	</table>
	<br/>
	
	<div class="form-group">
		<label class="control-label col-md-1">宿泊期間</label>
		<div class="col-md-6 flex">
			<div class="datepicker-wrapper" style="text-decoration: underline; cursor: pointer;">
				<v-date-picker is-expanded style="width: 100%" mode='range' locale='ja-jp' v-model='selectedDate' :popover="{ placement: 'bottom', visibility: 'click' }" :available-dates='availableDate' :input-props='{readonly: "readonly"}' @drag="dragEvent($event)" @input="inputEvent($event)">
                    <div class="bold text-search" style="width: calc(100% - 0px);font-size:14px;color:#245BD6;"><?php echo($order_data['checkin_date']);?>～<?php echo($order_data['checkout_date']);?></div>
                </v-date-picker>
            </div>
			<div style="display:none">
			<input name="checkin_date" id="checkin_date" value="<?php echo($order_data['checkin_date']);?>" class="form-control form-control-inline input-small date-picker" size="16" data-date-format="yyyy-mm-dd"  type="text"/>
			<input name="checkout_date" id="checkout_date" value="<?php echo($order_data['checkout_date']);?>" class="form-control form-control-inline input-small date-picker" size="16" data-date-format="yyyy-mm-dd"  type="text"/>
			</div>
		</div>									
	</div>
	<div class="form-group">
		<label class="control-label col-md-1">部屋数</label>
		<div class="col-md-1">
			<?php echo Form::select('rooms_count', $rooms, count($order_data['room_list']), array('id'=>'rooms_count','class'=>'form-control'))?>
		</div>
	</div>
	<div class="form-group" id="overlay">
		<label class="control-label col-md-1">名前</label>
		<div class="col-md-5">
			<input name="name" type="text" class="form-control" value="<?php echo($order_data['checkin_name'])?>">
		</div>									
	</div>
	<div class="form-group">
		<label class="control-label col-md-1">メールアドレス</label>
		<div class="col-md-5">
			<input name="email" type="text" class="form-control" value="<?php echo($order_data['checkin_mail'])?>">
		</div>									
	</div>
	<div class="form-group">
		<label class="control-label col-md-1">電話番号</label>
		<div class="col-md-5">
			<input name="phone" type="text" class="form-control" value="<?php echo($order_data['checkin_tel'])?>">
		</div>									
	</div>								
	<div class="form-group">
		<div class="col-md-offset-1 col-md-10">
			<?php 
			//'01' => '新規'
			if ($order->order_status_cd == '01' || $order->order_status_cd == '02') {
				if ($order->payment_type_div == '2') {
					echo('<button type="button" id="goCreditButton" class="btn yellow action mr10"><i class="far fa-calendar-times mr10"></i>変更/取消</button>');
				}
				else {
					echo('<button type="button" id="modifyButton" class="btn yellow action mr10"><i class="far fa-calendar-times mr10"></i>変更</button>');
				}
			}
			//'03' => '取消'											
			if ($order->order_status_cd != '03') {
				if ($order->payment_type_div== '2') {
					echo('<button type="button" id="goCreditButton" class="btn red action mr10"><i class="far fa-trash-alt mr10"></i>変更/取消</button>');
				}
				else {
					echo('<button type="button" id="cancelButton" class="btn red action mr10"><i class="far fa-trash-alt mr10"></i>取消</button>');
				}
			}?>
			<button type="button" onclick="top.location='/admin/orders'" class="btn grey-steel">戻る</button>
		</div>
	</div>
	</div>

<div id="lang_cd" style="display:none;"><?php echo $order->lang_cd?></div>


