<style>
  .page-content-wrapper .page-content {
    padding: 0 !important;
    margin-left: unset !important;
  }
  /* ヘッダーとメニューサイドバーを非表示 */
  .page-content-header {
   display: none; 
  }
  .page-sidebar-wrapper {
   display: none; 
  }

  .sidebar-container {
    display: block;
  }

  .modal_time_input {
    width: 100%;
    width: 100px;
    height: 28px;
    padding: 8px 12px;
    border-radius: 4px;
    border: solid 1px #e3e5e8;
    font-size: 12px;
    background: #fff;
    outline: none;
  }

</style>
<input type="hidden" name="act" id="act" value="">
<input type="hidden" name="restrict_time" id="restrict_time" value="">
<input type="hidden" name="item_id" value="<?php echo $item_id ?>">
<div class="congestion-container">
  <section class="current-congestion flexbox-center" style="flex-direction: column; background-image:url(<?php echo $item_pic ?>);background-blend-mode:darken;">
    <h1 class="congestion-place"><?php echo($result['item_name']) ?></h1>
    <p class="congestion-sub-title"><?php echo __('admin.congestion.label.current_status') ?></p>
    <div type="button" class="congestion-button crowded-<?php echo $result['status_cd'] ?> flexbox-center">
    <?php if ($result['status_cd']!=null) {

      echo($_codes['39'][$result['status_cd']]);
      if ($result['status_cd'] === '4') {
        echo(($result['restrict_time'] != '') ? ' 〜' . $result['restrict_time'] : ' ' . __('admin.congestion.label.restrict_time_undecided'));
      }
    } 
    ?>
    </div>
    <p class="congestion-his"><?php echo __('admin.congestion.label.last_update') ?> <?php if ($result['upd_time']!=null) echo ($result['name'] . ' ' . $result['upd_time']) ?></p>
  </section>
  <section class="congestion-setting flexbox-center" style="flex-direction: column;">
    <h1 class="congestion-sub-title"><?php echo __('admin.congestion.label.congestion_change') ?></h1>
    <div type="button" class="congestion-button flexbox-center crowded js-congestion-button" data-status="3"><?php echo __('admin.congestion.label.congestion_status.clowded') ?></div>
    <div type="button" class="congestion-button flexbox-center slightly-crowded js-congestion-button" data-status="2"><?php echo __('admin.congestion.label.congestion_status.slightly_clowded') ?></div>
    <div type="button" class="congestion-button flexbox-center vacant js-congestion-button" data-status="1"><?php echo __('admin.congestion.label.congestion_status.vacant') ?></div>
    <div type="button" class="congestion-button flexbox-center restricted js-congestion-button" data-status="4"><?php echo __('admin.congestion.label.congestion_status.restricted') ?></div>
  </section>
</div>


<div class="talkappi-modal js-restrict-modal" style="display:none;">
<div class="talkappi-modal-background js-talkappi-modal-background"></div>
  <div class="talkappi-modal-container js-modal-container">
    <div class="modal-small-title-container">
      <h4 class="modal-small-title"><?php echo __('admin.congestion.label.restrict_setting_question') ?></h4>
      <svg class="modal-close-button js-close-button" data-type="close" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
        <g fill="none" fill-rule="evenodd" stroke-linecap="round">
          <g stroke="#3D3F45" stroke-width="2">
            <g>
              <path d="M16 16L0 0" transform="translate(4 4)"></path>
              <path d="M16 16L0 0" transform="translate(4 4) matrix(-1 0 0 1 16 0)"></path>
            </g>
          </g>
        </g>
      </svg>
    </div>
    <div style="margin:20px 0 0 10px;"><?php echo __('admin.congestion.label.restrict_setting_tips') ?></div>
    <div>
      <label style="margin: 20px 0 0 10px;"><?php echo __('admin.congestion.label.estimated_release_time') ?></label>
      <input class="modal_time_input js_restrict_time" type="time" step="300"/>
    </div>

    
    <div class="modal-btn-container">
      <div class="btn-larger btn-red js-restrict-ok-button" data-type="confirm">OK</div>
      <div class="btn-larger btn-white js-restrict-cancel-button" data-type="close"><?php echo __('admin.common.button.cancel') ?></div>
    </div>
	</div>
</div>