<style type="text/css">
.btn.default:active, .btn.default.active {
  background-image: none;
  background-color: #45b6af;
  color: #fff;
}
</style>

<script type="text/javascript">
var bot_msgs = [<?php echo $bot_msgs ?>];
</script>
			<!-- BEGIN PAGE HEADER-->
			<!-- BEGIN PAGE HEAD -->
			<div class="page-head">
				<!-- BEGIN PAGE TITLE -->
				<div class="page-title">
					<h1>メールリンク管理<small></small></h1>
				</div>
				<!-- END PAGE TITLE -->
			</div>
			<!-- END PAGE HEAD -->
			<!-- END PAGE HEADER-->
			<!-- BEGIN PAGE CONTENT-->

			        <!-- Page Content -->
			        <div id="page-wrapper">
				        <div class="portlet light">
							<div class="tabbable-line">
							<ul class="nav nav-tabs ">
								<li class="">
									<a class="func-menu" href="/admin/linklogs">
									リンク一覧</a>
								</li>							
								<li class="active">
									<a class="func-menu" href="/admin/linklog?id=<?php echo($link_id)?>">
									リンク管理</a>
								</li>					
							</ul>
							</div>
							<input type="hidden" id="act" name="act" value="" />
							<input type="hidden" name="message" id="message" value="<?php echo($message)?>" />					
							<div class="portlet box">
								<div class="portlet-body">
								<div class="row">
								<div class="col-md-8">				
									<div class="form-body">
										<div class="form-group">
											<label class="control-label col-md-3">分類</label>
											<div class="col-md-4">
												<?php echo Form::select('link_type_cd', $link_types, $post['link_type_cd'], array('id'=>'link_type_cd','class'=>'form-control'))?>
											</div>
										</div>																										
										<?php foreach($ext_data as $k=>$v) {?>
										<div class="form-group">
											<label class="control-label col-md-3"><?php echo $v?></label>
											<?php 
											if ($k == 'sns_type_cd') {
												echo('<div class="col-md-9">');
												echo('<div class="btn-group" data-toggle="buttons">');
												$bot_snses = explode(',', $_bot->sns_cd);
												foreach($_codes['16'] as $sns_cd=>$sns_name) {
													if (!in_array($sns_cd, $bot_snses)) continue;
													$select_sns = explode(',', $post[$k]);
													if (in_array($sns_cd, $select_sns) || $link_id==null) {
														echo('<label class="btn default active">');
														echo('<input name="' . $k . '[]"  type="checkbox" checked="true" value="' . $sns_cd . '" class="toggle">' . $sns_name. '</label>');
													}
													else {
														echo('<label class="btn default">');
														echo('<input name="' . $k . '[]" type="checkbox" value="' . $sns_cd . '" class="toggle">' . $sns_name . '</label>');
													}
												}
												echo('</div>');
												echo('</div>');
												//echo Form::select('sns_type_cd',[''=>'-'] +  $_codes['08'], $post[$k], array('id'=>'sns_type_cd','class'=>'form-control'));
											}
											else if ($k == 'lang_cd') {
												echo('<div class="col-md-9">');
												echo('<div class="btn-group" data-toggle="buttons">');
												$bot_langs = explode(',', $_bot->lang_cd);
												foreach($_bot_lang as $lang_cd=>$lang_name) {
													if (!in_array($lang_cd, $bot_langs)) continue;
													$select_lang = explode(',', $post[$k]);
													if (in_array($lang_cd, $select_lang) || $link_id==null) {
														echo('<label class="btn default active">');
														echo('<input name="' . $k . '[]" type="checkbox" checked="true" value="' . $lang_cd . '" class="toggle">' . $lang_name. '</label>');
													}
													else {
														echo('<label class="btn default">');
														echo('<input name="' . $k . '[]" type="checkbox" value="' . $lang_cd . '" class="toggle">' . $lang_name. '</label>');
													}
												}
												echo('</div>');
												echo('</div>');
												//echo Form::select('lang_cd',[''=>'-'] +  $_bot_lang, $post[$k], array('id'=>'lang_cd','class'=>'form-control'));
											}
											else if (strpos($k, 'time')!==false || strpos($k, 'date')!==false) {
												echo('<div class="col-md-2">');
												echo('<input name="' . $k . '" value="' . $post[$k] .'" class="form-control form-control-inline input-small date-picker" size="16" data-date-format="yyyy-mm-dd" type="text"/>');
												echo('</div>');
												if (strpos($k, 'time')!==false) {
													echo('<div class="col-md-2">');
													echo('<input name="' . $k . '_time" type="text" class="form-control timepicker timepicker-24" size="10" value="' . $post[$k . '_time'] .'">');
													echo('</div>');
												}
											}
											else {
												echo('<div class="col-md-9">');
												echo('<input name="' . $k . '" type="text" class="form-control" value="' . $post[$k] .'">');
												echo('</div>');
											}
										echo('</div>');
									 }?>
										<div class="form-group">
											<label class="control-label col-md-3">実行予定時刻</label>
											<div class="col-md-2">
												<input name="valid_date" id="valid_date" value="<?php if ($post != NULL)echo($post['valid_date'])?>" class="form-control form-control-inline input-small date-picker" size="16" data-date-format="yyyy-mm-dd"  type="text"/>
											</div>
											<div class="col-md-2">
												<div class="input-group">
													<input name="valid_time" id="valid_time" type="text" class="form-control timepicker timepicker-24" value="<?php if ($post != NULL) echo($post['valid_time'])?>">
													<span class="input-group-btn">
													<button class="btn default" type="button"><i class="far fa-clock"></i></button>
													</span>
												</div>
											</div>																	
										</div>									 
									</div>																					
									<div class="form-actions">
										<div class="row">
											<div class="col-md-offset-3 col-md-9">
												<button type="button" id="saveBaseButton" class="btn blue mr10">
												<i class="fa fa-save mr10"></i>保存</button>
												<button type="button" id="deleteButton" class="btn red mr10">削除</button>
												<button type="button" onclick="top.location='/admin/linklogs'" class="btn grey-steel">戻る</button>
											</div>
										</div>
									</div>
								
								</div>
								</div>
								
								</div>
							</div>
						</div>
			        </div>
			        <!-- /#page-wrapper -->
				</div>
			
			<!-- END PAGE CONTENT-->



