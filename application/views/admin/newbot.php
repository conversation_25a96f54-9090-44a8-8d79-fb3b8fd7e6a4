			<!-- BEGIN PAGE HEADER-->
			<!-- BEGIN PAGE HEAD -->
			<div class="page-head">
				<!-- BEGIN PAGE TITLE -->
				<div class="page-title">
					<h1>ボット作成<small></small></h1>
				</div>
				<!-- <PERSON>ND PAGE TITLE -->
			</div>
			<!-- END PAGE HEAD -->
			<!-- END PAGE HEADER-->
			<!-- BEGIN PAGE CONTENT-->
			<div class="row">
				<div class="col-md-12">
			        <!-- Page Content -->
			        <div id="page-wrapper">
				        <div class="portlet light">
							<div class="tabbable-line">
								<ul class="nav nav-tabs ">
									<li class="">
										<a href="/admin/bottheme">
										ボット作成</a>
									</li>
								</ul>
							</div>
							<div class="portlet box">
								<div class="portlet-body">
									<div class="row">
										<div class="col-md-10">
										<form id="itemForm" action="/admin/newbot" class="form-horizontal" role="form" method="post" enctype="multipart/form-data">
											<div class="form-body">
												<div class="form-group">
													<label class="control-label col-md-2">ボットID</label>
													<div class="col-md-3">
														<div class="input-group">
															<input name="bot_id" maxlength="20" type="text" class="form-control" style="width:160px;" placeholder="" value="<?php if ($post != NULL) echo($post['bot_id'])?>">
														</div>
													</div>
												</div>
												<div class="form-group">
													<label class="control-label col-md-2">施設ID</label>
													<div class="col-md-3">
														<div class="input-group">
															<input name="item_id" maxlength="20" type="text" class="form-control" style="width:160px;" placeholder="" value="<?php if ($post != NULL) echo($post['item_id'])?>">
														</div>
													</div>
												</div>												
												<div class="form-group">
													<label class="control-label col-md-2">名前</label>
													<div class="col-md-3">
														<input name="bot_name" type="text" class="form-control" style="width:400px;" placeholder="" value="<?php if ($post != NULL) echo($post['bot_name'])?>">
													</div>
												</div>		
												<div class="form-group">
													<label class="control-label col-md-2">法人</label>
													<div class="col-md-3">
														<input name="company_name" type="text" class="form-control" style="width:400px;" placeholder="" value="<?php if ($post != NULL) echo($post['company_name'])?>">
													</div>
												</div>		
												<div class="form-group">
													<label class="control-label col-md-2">メール</label>
													<div class="col-md-3">
														<input name="mail" type="text" class="form-control" style="width:400px;" placeholder="" value="<?php if ($post != NULL) echo($post['mail'])?>">
													</div>
												</div>																							
												<div class="form-group">
													<label class="control-label col-md-2">業界</label>
													<div class="col-md-3">
														<?php echo Form::select('bot_class_cd', $_codes['10'], $post['bot_class_cd'], array('id'=>'bot_class_cd','class'=>'form-control'))?>
													</div>
												</div>					
												<div class="form-group">
													<label class="control-label col-md-2">ステータス</label>
													<div class="col-md-3">
														<?php echo Form::select('bot_status_cd', $_codes['04'], $post['bot_status_cd'], array('id'=>'bot_status_cd','class'=>'form-control'))?>
													</div>
												</div>		
												<div class="form-group">
													<label class="control-label col-md-2">利用開始日</label>
													<div class="col-md-3">
														<input name="start_date" value="<?php if ($post != NULL) echo($post['start_date'])?>" class="form-control form-control-inline input-medium date-picker" data-date-format="yyyy-mm-dd" size="16" type="text"/>
													</div>
												</div>																																																																		
											</div>
											<div class="form-actions">
												<div class="row">
													<div class="col-md-offset-2 col-md-6">
														<button type="submit" id="saveButton" class="btn blue">
														<i class="fa fa-save"></i>&nbsp;&nbsp;保存</button>
													</div>
												</div>
											</div>
										</form>
										</div>
										<div class="col-md-2">

										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
			    </div>
			</div>

			<!-- END PAGE CONTENT-->



