<style type="text/css">
.btn.default:active, .btn.default.active {
  background-image: none;
  background-color: #45b6af;
  color: #fff;
}
</style>	
			<!-- BEGIN PAGE HEADER-->
			<!-- BEGIN PAGE HEAD -->
			<div class="page-head">
				<!-- BEGIN PAGE TITLE -->
				<div class="page-title">
					<?php 
						if ($item_div == 1) {
							$title = '施設コンテンツ管理';
						}
						else if ($item_div == 2) {
							$title = '広域コンテンツ管理';
						}
						else if ($item_div == 3) {
							$title = 'グループ施設管理';
						}
						else if ($item_div == 4) {
							$title = 'メディア管理';
						}
					?>					
					<h1><?php echo $title?><small>・<?php echo $post['item_name']?></small></h1>
				</div>
				<!-- END PAGE TITLE -->
			</div>
			<!-- END PAGE HEAD -->
			<!-- END PAGE HEADER-->
			<!-- BEGIN PAGE CONTENT-->
			<div class="row">
				<div class="col-md-12">
			        <!-- Page Content -->
			        <div id="page-wrapper">
				        <div class="portlet light">
						<?php echo $menu ?>
						<div class="portlet box">
							<div class="portlet-body">
								<input type="hidden" name="class_cd_hidden" id="class_cd_hidden" value="<?php echo $post['class_cd']?>" />
								<table class="table table-striped table-bordered table-hover" id="sample_3">
								<thead>
								<tr>
									<th>
										区分名
									</th>
									<th>
										区分
									</th>
									<th>
										 コード・コード名
									</th>
									<th style="width: 150px;">
										 編集操作
									</th>
								</tr>
								</thead>
	
								<tbody>
								<?php
									foreach ($item_codes as $code) {
								?>
								<tr class="gradeX odd" role="row">
									<td>
									 <?php echo($code['title']);?>	 
									</td>
									<td>
									 <?php echo($code['code_div']);?>	 
									</td>
									<td>
									 <?php echo($code['class_cd'] . ' ' . $code['name']);?>	 
									</td>
									<td>
									<button type="button" class="btn yellow action" act="01" code_div="<?php echo($code['code_div'])?>">編集</button>
									<button type="button" class="btn red action" act="02" code_div="<?php echo($code['code_div'])?>">削除</button>
									</td>																											
								</tr>								
								<?php }?>
								</tbody>
								</table>
								<br/>
								<button type="button" class="btn green-meadow action" act="00"><i class="fa fa-file mr10"></i>新規</button>
								<?php if ($post['action'] == '00' || $post['action'] == '01') { ?>													
								<div class="form-body">
									<div class="form-group">
										<label class="control-label col-md-2">区分</label>
										<div class="col-md-3">
											<?php 
											if ($post['action'] == '00') {
												echo Form::select('code_div', $code_div_array, '', array('id'=>'code_div','class'=>'form-control'));
											}
											else {
												echo Form::select('code_div', $code_div_array, $post['code_div'], array('readonly'=>'readonly', 'id'=>'code_div','class'=>'form-control'));
											}
											?>
										</div>
									</div>									
									<?php echo($classbox);?>																					
									<div class="form-actions">
										<div class="row">
											<div class="col-md-offset-2 col-md-9">
												<button type="button" id="saveBaseButton" class="btn blue mr10">
												<i class="fa fa-save mr10"></i>保存</button>
												<button type="reset" class="btn gray">リセット</button>
											</div>
										</div>
									</div>
								<?php } ?>									
								</div>
							</div>
						</div>
						</div>
			        </div>
			        <!-- /#page-wrapper -->
				</div>
			</div>
			<!-- END PAGE CONTENT-->