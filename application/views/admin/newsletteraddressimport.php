<link rel="stylesheet" type="text/css" href="/assets/admin/css/newsletter.css"/>
<style>
.content-container .content-wrapper {
  padding: 12px 6%;
  display:flex;
  flex-direction:column;
  gap:24px;
}

#filter-conditions span{
    text-decoration: underline;
    cursor: pointer;
}
#filter-conditions.disabled span {
    color: #A1A4AA;
    text-decoration: unset;
}
#filter-conditions .inquiry-branch {
    color: #245BD6;
}

div.js-entry-condition-title-wrapper {
    display: none;
}

.icon-undo {
  height: 12px;
  width: 12px;
}

</style>

<div class="content-container white">
  <div class="content-wrapper">
    <!-- 案件所属 -->
    <?php if($is_newsletter_project_enabled && $project_id && is_numeric($project_id) ) : ?>
      <div style="display:flex;gap:6px;">
        <span><?php echo __('admin.newsletterproject.label.name') ?></span>
        <span style="font-weight: 600;"><?php echo $project_name ?></span>
      </div>
    <?php endif; ?>

    <!-- 言語選択 -->
    <div style="display:flex;flex-direction:column;gap:6px;">
      <div><?php echo __('admin.newsletterimport.label.select_language') ?></div>
      <div class="talkappi-pulldown js-support_lang_cd" data-name="support_lang_cd" data-value='-' data-source='<?php echo json_encode($support_lang_cds, JSON_UNESCAPED_UNICODE) ?>'></div>
    </div>

    <!-- フォーム選択 -->
    <div style="display:flex;flex-direction:column;gap:6px;">
      <div><?php echo __('admin.newsletterimport.label.select_inquiry') ?></div>
      <div class="talkappi-pulldown js-inquiry_id" data-name="inquiry_id" data-value='' data-source='{}'></div>
    </div>

    <!-- ラベル指定 -->
    <div id="label-select" class="content-container light-gray hide" style="display:flex;flex-direction:column;gap:6px;">
      <div style="display:flex;align-items:center;">
        <label style="width:120px;margin-bottom:0;"><?php echo __('admin.common.label.mail.address') ?></label>
        <div style="width:100%" class="talkappi-pulldown js-mail_no" data-name="mail_no"></div>
      </div>
      <div style="display:flex;align-items:center;">
        <label style="width:120px;margin-bottom:0;"><?php echo __('admin.common.label.name.human') ?></label>
        <div style="width:100%" class="talkappi-pulldown js-name_no" data-name="name_no"></div>
      </div>
    </div>

    <div class="form-control flex-x-between disabled" id="filter-conditions" style="max-width:350px; display: none;">
      <span class="js-set-conditions"></span>
      <!-- only clear conditions(without apply filtering automatically, need to click preview button again) button for now-->
      <div class="inquiry-branch pointer js-reset-conditions"><?php echo __('admin.common.button.clear'); ?></div>
    </div>
    
    <div style="display:flex;">
      <button class="btn-smaller page btn-blue js-import" style="margin-left:0"><?php echo __('admin.newsletterimport.label.import_inquiry') ?></button>
    </div>

    <div style="display:flex;flex-direction:column;gap:6px;">
      <div class="result_data_count">
        <?php echo __('admin.common.label.total') ?><span></span>
        <?php echo __('admin.newsletterimport.label.pcount') ?></div>
      <div>
        <table class="table table-striped table-bordered table-hover js-data-table">
          <thead>
            <tr>
              <th><?php echo __('admin.common.label.mail.address') ?></th>
              <th><?php echo __('admin.common.label.name.human') ?></th>
              <th><?php echo __('admin.newsletterimport.label.tag') ?></th>
              <th style="width:40px!important"><?php echo __('admin.newsletterimport.button.exclude') ?></th>
            </tr>
          </thead>
          <tbody></tbody>
        </table>
      </div>
    </div>

    <div class="flex">
      <button class="action-button page btn-blue js-export-to" style="margin-left:0"><?php echo __('admin.newsletterimport.label.export') ?></button>
      <?php echo HTML::anchor($navi_parent_url, __('admin.common.button.return_to_list'), ['class' => 'action-button page btn-white js-action-back']) ?>
    </div>
  </div>
</div>

<script>
const _inquirys_by_lang_obj = <?php echo $inquirys_by_lang ?>;
let _results_data_array = <?php echo $result_datas ?>;

const _project_id = <?php echo isset($project_id) && $project_id && is_numeric($project_id) ? $project_id : 'null'; ?>;
const _is_newsletter_project_enabled = <?php echo $is_newsletter_project_enabled ? 'true' : 'false'; ?>;
</script>