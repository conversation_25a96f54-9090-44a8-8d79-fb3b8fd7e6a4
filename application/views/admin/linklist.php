
			<!-- B<PERSON>IN PAGE HEADER-->
			<!-- BEGIN PAGE HEAD -->
			<div class="page-head">
				<!-- BEGIN PAGE TITLE -->
				<div class="page-title">
					<h1><?php echo($_active_menu_name)?><small></small></h1>
				</div>
				<!-- <PERSON>ND PAGE TITLE -->
			</div>
			<!-- END PAGE HEAD -->
			<!-- END PAGE HEADER-->
			<!-- BEGIN PAGE CONTENT-->
			<div class="row">
				<div class="col-md-12">
			        <!-- Page Content -->
			        <div id="page-wrapper">
			        <div class="portlet light">
							<div class="form-body">
								<div class="row">
								<br/>
								</div>
								<div class="row">
									<label class="control-label col-md-1" style="padding-left:15px; padding-right:15px;">期間</label>
									<div class="col-md-3">
										<input name="start_date" id="start_date" value="<?php echo($post['start_date'])?>" style="float:left;" class="form-control form-control-inline input-small date-picker" size="16" data-date-format="yyyy-mm-dd"  type="text"/>
										<input name="end_date" id="end_date" value="<?php echo($post['end_date'])?>" style="float:left; margin-left:10px;" class="form-control form-control-inline input-small date-picker" size="16" data-date-format="yyyy-mm-dd"  type="text"/>
									</div>
									<div class="col-md-1">
										<?php echo Form::select('link_class_cd', $link_class, $post['link_class_cd'], array('id'=>'link_class_cd','class'=>'form-control'))?>
									</div>
									<div class="col-md-1">
										<?php echo Form::select('link_status_cd', $link_status, $post['link_status_cd'], array('id'=>'link_status_cd','class'=>'form-control'))?>
									</div>																		
									<div class="col-md-2">
										<?php echo Form::select('link_type_cd', $link_type, $post['link_type_cd'], array('id'=>'link_type_cd','class'=>'form-control'))?>
									</div>									
									<div class="col-md-2">
										<?php echo Form::select('bot_id', $bots, $post['bot_id'], array('id'=>'bot_id','class'=>'form-control'))?>
									</div>
									<div class="col-md-1">
										<div class="form-group">
											<button type="submit" class="btn yellow">
											<i class="fa fa-search mr10"></i>検索</button>
										</div>
									</div>																		
								</div>
							</div>						
						<div class="portlet box">
							<div class="portlet-body">
							<table class="table table-striped table-bordered table-hover js-data-table">
							<thead>
							<tr>
								<th style="width:100px;">
									日時
								</th>
								<th style="width:200px;">
									  ボット
								</th>
								<th style="width:100px;">
									 連携タイプ
								</th>			
								<th style="width:100px;">
									 ステータス
								</th>	
								<th style="width:100px;">
									 作業タイプ
								</th>																									
								<th>
									 タイトル
								</th>
								<th>
									 内容
								</th>																																							
							</tr>
							</thead>

							<tbody>
							<?php
								foreach ($link_list as $log) {
							?>	
							<tr class="gradeX odd" role="row">
								<td class="sorting_1">
									 <?php echo(substr($log['upd_time'], 5, 11))?><br/>
									 <?php echo($log['name'])?>
								</td>
								<td>
									<?php echo($log['bot_name'])?>
								</td>
								<td>				
									<?php 
									if ($log['link_class_cd'] == '01') {
										echo('<a class="redmine-link" target="_blank" href="' . $redmine_url . 'issues/' . $log['ticket_id'] . '">');
										echo('<span class="badge badge-success" style="margin: 5px;" >' . $link_class[$log['link_class_cd']] . '</span> </a>');
									}
									else if ($log['link_class_cd'] == '02') {
										echo('<a class="redmine-link" target="_blank" href="https://support.activalues.com/issues/' . $log['ticket_id'] . '">');
										echo('<span class="badge badge-danger" style="margin: 5px;" >' . $link_class[$log['link_class_cd']] . '</span> </a>');
									}
									else {
										echo('<a class="redmine-link" target="_blank" href="' . $kintone_url . 'show#record=' . $log['ticket_id'] . '">');
										echo('<span class="badge badge-warning" style="margin: 5px;" >' . $link_class[$log['link_class_cd']] . '</span> </a>');										
									}
									?>
								</td>
								<td>				
									<?php echo($link_status[$log['link_status_cd']])?>					
								</td>								
								<td>				
									<?php echo($link_type[$log['link_type_cd']])?>					
								</td>																				
								<td>					
									<?php echo($log['title'])?>				
								</td>	
								<td>				
									<?php echo(nl2br($log['description']))?>					
								</td>																											
							</tr>
							<?php } ?>
							</tbody>
							</table>
							</div>
						</div>
					</div>
			        </div>
			        <!-- /#page-wrapper -->					
				</div>
			</div>
			<!-- END PAGE CONTENT-->
