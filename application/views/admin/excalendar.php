
			<!-- BEGIN SAMPLE PORTLET CONFIGURATION MODAL FORM-->
			<div class="modal fade" id="portlet-config" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
				<div class="modal-dialog">
					<div class="modal-content">
						<div class="modal-header">
							<button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
							<h4 class="modal-title">Modal title</h4>
						</div>
						<div class="modal-body">
							 Widget settings form goes here
						</div>
						<div class="modal-footer">
							<button type="button" class="btn blue">Save changes</button>
							<button type="button" class="btn default" data-dismiss="modal">Close</button>
						</div>
					</div>
					<!-- /.modal-content -->
				</div>
				<!-- /.modal-dialog -->
			</div>
			<!-- /.modal -->
			<!-- <PERSON><PERSON> SAMPLE PORTLET CONFIGURATION MODAL FORM-->
			<!-- BEGIN PAGE HEADER-->
			<!-- BEGIN PAGE HEAD -->
			<div class="page-head">
				<!-- BEGIN PAGE TITLE -->
				<div class="page-title">
				<?php 
					$next_year = $year;
					$next_month = $month+1;
					$last_year = $year;
					$last_month = $month-1;
					if ($month==12) {
						$next_year = $year + 1;
						$next_month = "1";
					}
					else if ($month==1) {
						$last_year = $year - 1;
						$last_month = "12";
					}
				?>
					<h1><?php echo($experience_name) ?> <small style="margin-left: 40px;">
					<a href="/admin/excalendar?id=<?php echo($experience_id)?>&&year=<?php echo($last_year)?>&&month=<?php echo($last_month)?>" style="margin-right: 20px;">前月</a>
					<?php echo($year) ?>年<?php echo($month) ?>月
					<a href="/admin/excalendar?id=<?php echo($experience_id)?>&&year=<?php echo($next_year)?>&&month=<?php echo($next_month)?>" style="margin-left: 20px;">次月</a>
					<a href="/admin/experiences" style="margin-left: 40px;">一覧に戻る</a>
					</small></h1>
				</div>
			</div>
			<!-- END PAGE HEAD -->
			<!-- END PAGE HEADER-->
			<!-- BEGIN PAGE CONTENT-->
			<div class="row">
				<div class="col-md-12">
					<form id="servicesForm" action="/admin/excalendar" class="form-horizontal" role="form" method="post">
						<input type="hidden" name="experience_id" id="experience_id" value="<?php echo($experience_id) ?>">
						<input type="hidden" name="year" id="year" value="<?php echo($year) ?>">
						<input type="hidden" name="month" id="month" value="<?php echo($month) ?>">
						<input type="hidden" name="day" id="day" value="">
						<input type="hidden" name="operation" id="operation" value="">
						<div class="form-body">
							<div class="portlet box">
							<div class="portlet-body">
							<table class="table table-striped table-bordered table-hover js-data-table">
								<thead>
									<tr>
									<th>曜日</th>
									<th>予約状況</th>
									<th>操作</th></tr>
								</thead>	
								<tbody>				
								<?php
									$last_day = date('t', mktime(0, 0, 0, $month + 1, 0, $year));
									// 月末日までループ
									for ($i = 1; $i < $last_day + 1; $i++) {
										$day = str_pad($i, 2, "0", STR_PAD_LEFT);
										$desc = "";
										$status_cd = "01";
										if (array_key_exists($day, $stocks)) {
											$desc = $stocks[$day]->description;
											$status_cd = $stocks[$day]->stock_status_cd;
										}
										// 曜日を取得
										$week = date('N', mktime(0, 0, 0, $month, $i, $year));
										
										if ($week == 7) $dayweek = $i . "(日)";
										if ($week == 1) $dayweek = $i . "(月)";
										if ($week == 2) $dayweek = $i . "(火)";
										if ($week == 3) $dayweek = $i . "(水)";
										if ($week == 4) $dayweek = $i . "(木)";
										if ($week == 5) $dayweek = $i . "(金)";
										if ($week == 6) $dayweek = $i . "(土)";
										
										if ($week == 7 || $week == 6) {
											echo('<tr class="warning">');
										}
										else {
											echo('<tr>');
										}
										echo("<td width=50>$dayweek</td>");
										echo("<td>" .  $desc . "</td>");
										if (date('Y-m-d', mktime(0, 0, 0, $month, $i, $year)) <= date('Y-m-d')) {
											if ($status_cd == "01") {
												echo('<td width=100>取消</td></tr>');
											}
											else if($status_cd == "02") {
												echo('<td width=100>確定</td></tr>');
											}
										}
										else {
											if ($status_cd == "01") {
												echo('<td width=100><button type="button" class="btn green-meadow action" operation="02" day="' . $i . '">確定</button></td></tr>');
											}
											else if($status_cd == "02") {
												echo('<td width=100><button type="button" class="btn gray action" operation="01" day="' . $i . '">取消</button></td></tr>');
											}
										}
									}
								?>
								</tbody>
							</table>																
							</div>
							</div>
						</div>
					</form>
				</div>
			</div>

