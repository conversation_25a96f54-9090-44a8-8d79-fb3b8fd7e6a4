<link rel="stylesheet" type="text/css" href="/assets/admin/css/maximum_screen.css"/>
<script>
	const _maximum_id = <?php echo json_encode($maximum_id, JSON_UNESCAPED_UNICODE)?>;
  const _num_in_page = <?php echo json_encode($num_in_page, JSON_UNESCAPED_UNICODE)?>;
  const _promotion_images = <?php echo json_encode($promotion_images, JSON_UNESCAPED_UNICODE)?>;
</script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.24.0/moment.min.js" defer></script>
<?php
  $url = $_SERVER['REQUEST_URI'];
?>
<div class="maximum-screen-main-container">
  <div class="maximum-screen-main-left">
    <!-- Header（時間・日付・タイトル） -->
    <div class="maximum-screen-header-container">
      <div>      
        <p class="time-label maximum-screen-clock"></p>
        <p class="date-label maximum-screen-date"></p>
      </div>
      <h1 class="maximum-screen-title"><?php echo $maximum_screen_title ?></h1>
    </div>

    <div class="maximum-screen-content-wrapper">
      <!-- 繁忙期 -->
      <?php if($is_busy_period): ?>
        <!-- 今日のデータ -->
        <div class="maximum-screen-content-container" id="slide_1">
          <?php generate_content($results, 0, $is_busy_period); ?>
        </div>
        <!-- 明日のデータ -->
        <div class="maximum-screen-content-container" id="slide_2">
          <?php generate_content($results, 1, $is_busy_period); ?>
        </div>
      <?php else: ?>
      <!-- 閑散期 -->
        <!-- 今日と明日のデータ -->
        <div class="maximum-screen-content-container" id="slide_1">
          <?php generate_content($results, 0, $is_busy_period); ?>
          <?php generate_content($results, 1, $is_busy_period); ?>
        </div>
        <!-- 明後日と明明後日のデータ -->
        <div class="maximum-screen-content-container" id="slide_2">
          <?php generate_content($results, 2, $is_busy_period); ?>
          <?php generate_content($results, 3, $is_busy_period); ?>
        </div>
      <?php endif; ?>
    </div>
    
    <!-- インジケータ -->
    <div class="indicator-container <?php echo $is_busy_period ? 'busy-period' : 'off-peak-period'; ?>">
      <span id="indicator-1" class="indicator active"></span>
      <span id="indicator-2" class="indicator"></span>
    </div>
  </div>
  <!-- 右側写真 -->
  <div class="promotion-image-container">
    <?php 
      if (count($promotion_images) > 0) {
        foreach($promotion_images as $key => $image) {
          echo ('<img data-id="' . $key . '" src="' . $image['msg_image'] . '?' . time() . '" alt="Promotion Image" id="promotion-image-' . $key . '" class="promotion-image" />');
        }
      } else {
        echo ('画像が登録されていません');
      }
    ?>
  </div>
</div>

<?php
function generate_content($results, $day, $is_busy_period) {
  $target_date = date('Y-m-d', strtotime("+$day days"));
  ?>
  <div class="date-slot-container">
    <!-- 日付 -->
    <div class="date-container">
      <p class="day">
        <?php if ($day === 0): ?>
          <?php echo "本日"; ?><br>Today<br><span style="color: gray; font-size: smaller;"><?php echo date('j日', strtotime($target_date)) . date('(D)', strtotime($target_date)); ?></span>
        <?php else: ?>
            <?php echo date('j日', strtotime($target_date)); ?><br><span style="color: gray; font-size: smaller;"><?php echo date('(D)', strtotime($target_date)); ?></span>
        <?php endif; ?>
      </p>
    </div>
    <!-- 枠 -->
    <div class="slots-container">
      <?php foreach ($results[$day] as $result_list): ?>
        <?php foreach ($result_list as $result): ?>
          <div class="slot <?php echo $is_busy_period ? '' : 'small-slot'; ?> <?php echo $result['is_past'] ? 'past-slot' : 'future-slot'; ?>" style="<?php echo $is_busy_period ? 'flex: 0 0 calc(33.333% - 15px);' : 'flex: 0 0 calc(20% - 15px);' ?>">
          <div class="slot-time">
            <?php echo substr($result['time'], 0, strpos($result['time'], "-")); ?>
          </div>
          <div class="slot-availability">
            <?php if ($result['is_past']): ?>
              <div class="availability">ー</div>
            <?php else: ?>
              <?php if ($result['is_no_stock']): ?>
                <div class="no-availability">&#10060;</div>
              <?php elseif ($result['is_low_stock']): ?>
                <div class="low-availability">&#9651;</div>
              <?php else: ?>
                <div class="availability">
                    <svg height="20" width="20">
                        <circle cx="10" cy="10" r="6" stroke="#0f0" stroke-width="3" fill="transparent" />
                    </svg>
                </div>
              <?php endif; ?>
            <?php endif; ?>
          </div>
        </div>
        <?php endforeach; ?>
      <?php endforeach; ?>
    </div>
  </div>
  <div class="grey-bar"></div>
  <?php } ?>