<script>
const _paging = <?php echo json_encode($post['paging']) ?>;
</script>
<!-- BEGIN PAGE HEADER-->
			<!-- BEGIN PAGE HEAD -->
			<style>
				.selectmember:link{
					text-decoration: none;
					color: #333;					
				}
				.selectmember:hover{
					text-decoration: none;
					color: #333;					
				}				
				.selectmember:visited{
					text-decoration: none;
					color: #333;					
				}				
				
				.selected {
					font-weight: bolder;
				}
			</style>
			<div class="page-head">
				<!-- BEGIN PAGE TITLE -->
				<div class="page-title">
					<h1>チャット履歴検索<small></small></h1>
				</div>
				<!-- END PAGE TITLE -->
			</div>
			<!-- END PAGE HEAD -->
			<!-- END PAGE HEADER-->
			<!-- BEGIN PAGE CONTENT-->
			<div class="row">
				<div class="col-md-12">
					<div id="page-wrapper">
					  <?php echo $logmenu ?>				
						<div class="edit-container">
							<div class="settings-container">
							<input type="hidden" name="message" id="message" value="<?php echo ($message) ?>" />
							<input type="hidden" name="act" id="act" value="" />
							<input type="hidden" name="log_id" id="log_id" value="" />
							<input type="hidden" name="bot_id" id="bot_id" value="" />
							<div class="form-body">							
								<div class="form-group">
									<label class="control-label col-md-1" style="padding-left:15px; padding-right:15px;"><?php echo __('admin.common.label.conversation_date') ?></label>
									<div class="col-md-4">
										<input name="start_date" id="start_date" value="<?php echo($post['start_date'])?>" style="float:left;" class="form-control form-control-inline input-small date-picker" size="16" data-date-format="yyyy-mm-dd"  type="text"/>
										<input name="end_date" id="end_date" value="<?php echo($post['end_date'])?>" style="float:left; margin-left:10px;" class="form-control form-control-inline input-small date-picker" size="16" data-date-format="yyyy-mm-dd"  type="text"/>
									</div>
								</div>
								<div class="form-group">
									<label class="control-label col-md-1" style="padding-left:15px; padding-right:15px;"><?php echo __('admin.common.label.user_flow') ?></label>
									<?php echo $botcond ?>	
								</div>
								<div class="form-group">
									<label class="control-label col-md-1"><?php echo __('admin.common.label.chat_contents') ?></label>
									<div class="col-md-4">
										<input name="keyword" id="keyword" placeholder="<?php echo __('admin.common.label.chat_example') ?>" value="<?php echo($post['keyword'])?>" class="form-control" />
									</div>
									<label class="control-label col-md-2" style="width: auto;"><?php echo __('admin.common.label.user_chat_only') ?></label>
									<div class="col-md-2">
										<input type="checkbox" name="member_only" value="1" <?php if ($post['member_only'] == true) echo("checked")?> class="make-switch" data-on-color="success" data-off-color="warning">
									</div>		
									<div class="col-md-2" style="width: auto;">
										<button type="button" id="searchButton" class="btn yellow" disabled="disabled" style="margin: 5px;"> <i class="fa fa-search mr10"></i><?php echo __('admin.common.button.search') ?></button>
										<button type="button" id="csvexport" class="btn green" style="margin: 5px;"><?php echo __('admin.common.button.csv_export') ?></button>
									</div>
								</div>
							</div>
							<table class="table table-striped table-bordered table-hover js-data-table">
							<thead>
							<tr>
								<th style="width: 100px;"><?php echo __('admin.common.label.username') ?></th>
								<th><?php echo __('admin.common.label.sns') ?></th>
								<th style="width: 160px;"><?php echo __('admin.common.label.conversation_date') ?></th>
								<th><?php echo __('admin.common.label.user_flow') ?></th>
								<th><?php echo __('admin.common.label.content') ?></th>
							</tr>
							</thead>
							<tbody>							
								<?php
								if ($result != NULL)
								foreach ($result as $log) {
									$is_tester = 'badge badge-success';
									if ($log['is_tester'] == 1) $is_tester = 'badge badge-warning';
								?>	
								<tr class="gradeX odd" role="row">
									<td>							
										 <?php 
										 if ($log['sns_type_cd']== 'wb') {
										 	echo('No:' . $log['member_no']);

										 }
										 else {
										 	echo($log['last_name'] . ' ' . $log['first_name']);
										 }
										 if ($_user->role_cd == '99' || $_user->role_cd == '01' || $_user->role_cd == '07' || $_user->role_cd == '09')
										 echo ('<label class="badge badge-info js-memberinfo" style="cursor:pointer;" data-member-id="' . $log['member_id'] .'">' . __('admin.common.label.journey')  . '</label>');
										 ?>
									</td>
									<td>
										<img src="/assets/common/images/chat_<?php echo $log['lang_cd']?>.png" style="margin:5px;width:24px;"/>
										<img src="/assets/common/images/icon_<?php echo $log['sns_type_cd']?>.png" style="margin:5px;width:24px;"/>
									</td>
									<td>
										<?php
											 $display_time = date('Y-m-d H:i:s', strtotime($log['log_time']));
											 echo $display_time;
										?>
										<a class="pop_adminchat" member_id=<?php echo $log['member_id'] ?> bot_id=<?php echo $log['bot_id'] ?> ><span class="<?php echo($is_tester)?>" style="margin: 5px;" ><?php echo __('admin.common.button.showlog') ?></span> </a>
										<a class="deletelog" log_id=<?php echo $log['log_id'] ?>  bot_id=<?php echo $log['bot_id']?> ><span class="badge badge-danger"><?php echo __('admin.common.button.deletelog') ?></span> </a>
									</td>
									<td>
										<?php echo($log['label'])?>
									</td>
									<td>
										<?php if($log['member_msg'] <> '') echo($log['member_msg'])?>
										<?php if($log['bot_msg'] <> '') echo($log['bot_msg'])?>
									</td>
								</tr>
								<?php } ?>
								</tbody>
							</table>
							</div>
						</div>
			        </div>
			        <!-- /#page-wrapper -->				
				</div>
			</div>
			<!-- END PAGE CONTENT-->

			