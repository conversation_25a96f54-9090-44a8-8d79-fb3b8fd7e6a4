<?php 
	$to_lang_cds = [];
	foreach ($_bot_lang as $k => $v) {
		if (in_array($k, $faq_langs))	{
			$to_lang_cds[] = [
				"lang_cd" => $k,
				"lang" => $v
			];
		}
	}
?>

<script type="text/javascript">
	const _talk_data = <?php echo json_encode($talk_data, JSON_UNESCAPED_UNICODE)?>;
	const _talk_data_ja = <?php echo json_encode($talk_data_ja, JSON_UNESCAPED_UNICODE)?>;
	const _talk_skills = <?php echo json_encode($talk_skills, JSON_UNESCAPED_UNICODE)?>;
	const _sub_bots = <?php echo json_encode($sub_bots, JSON_UNESCAPED_UNICODE)?>;
	const _edit_bot_id = '<?php echo $bot_id ?>';
	const _native_support_lang = <?php echo json_encode((isset($native_support_lang) && is_array($native_support_lang)) ? $native_support_lang : [], JSON_UNESCAPED_UNICODE) ?>;
	const _faq_level = '<?php if ($item_talk == null) { echo ''; } else { echo $item_talk['level']; } ?>';
	<?php 
	if ($temp_bot_id !== NULL) {
		echo('const _temp_bot_id = ' . $temp_bot_id . ';');
	}
	?>
	// for multilingualreflect component
	const _from_lang_cd = '<?php echo $lang_cd ?>';
	const _to_lang_cds = '<?php echo json_encode($to_lang_cds, JSON_UNESCAPED_UNICODE) ?>';
	let _translation_type = '<?php echo $public_flg == 1 ? htmlspecialchars($translation_type) : 0 ?>';
	const _datas = <?php echo json_encode($talk_data, JSON_UNESCAPED_UNICODE)?>;
	const _mode = '<?php echo $mode ?>';
	const _faq_updated_by_ai_data = <?php echo json_encode($faq_updated_by_ai_data, JSON_UNESCAPED_UNICODE)?>;
	const _flg_update_faq_by_gpt = <?php echo $flg_update_faq_by_gpt ?>;
	const _flg_create_faq_by_gpt = <?php echo $flg_create_faq_by_gpt ?>;
	const _is_faq_updating_by_ai = '<?php echo $is_faq_updating_by_ai ?? '0' ?>';
	const _is_faq_creating_by_ai = '<?php echo $is_faq_creating_by_ai ?? '0' ?>';	
	const _can_update_this_faq_by_gpt = '<?php echo $can_update_this_faq_by_gpt ? '1': '0' ?>';
	const _role_cd = <?php echo $_user->role_cd; ?>;
	const _can_create_this_faq = '<?php echo $can_create_this_faq ? '1': '0' ?>';
	const _is_this_faq_can_update_by_gpt = <?php echo json_encode($is_this_faq_can_update_by_gpt, JSON_UNESCAPED_UNICODE)?>;
</script>

<input type="hidden" id="talk_data" name="talk_data" value='' />
<input type="hidden" name="act" id="act" value="" />
<input type="hidden" name="cd" id="cd" value="<?php echo($intent_cd)?>" />
<input type="hidden" name="intent_id" id="intent_id" value="<?php echo($intent_id)?>" />
<input type="hidden" name="sub_cd" id="sub_cd" value="<?php echo($sub_intent_cd)?>" />			
<input type="hidden" name="lang" id="lang" value="<?php echo($lang_cd)?>" />
<input type="hidden" name="question" id="question" value="<?php echo($item_talk['question'])?>" />
<input type="hidden" name="question_input" id="question_input" value="<?php echo($item_talk['question'])?>" />
<input type="hidden" id="class_div" name="class_div" value="<?php echo $faq_div?>" />
<input type="hidden" name="class_cd_hidden" id="class_cd_hidden" value="<?php echo $intent_type_cd?>" />
<input type="hidden" id="mode" name="mode" value="<?php echo $mode?>" />
<input type="hidden" id="copy_bots" name="copy_bots" value="" />
<input type="hidden" id="flg_native_translate" name="flg_native_translate" value="<?php echo($flg_native_translate)?>" />
<input type="hidden" id="level" name="level" value="<?php echo($item_talk['level'])?>" />
<?php $url = htmlspecialchars($_SERVER['REQUEST_URI'], ENT_QUOTES, 'UTF-8');?>

<style>
.js-filename {
	text-overflow: ellipsis;
	width: 100%;
	white-space: nowrap;
	overflow: hidden;
}

.menu-html {
	max-width: 500px;
	white-space: pre-wrap;
	word-wrap: break-word;
	display: block;
}

.note-table.dropdown-menu {
	width: fit-content;
}

.note-editable:focus {
  box-shadow: none;
  outline: none;
}

/* summernote 暫定対応 */
.js-summernote-modal.rich-text-editor .modal-body label {
	padding-left: 20px;
}

.modal-select-container .select .list {
	display: block;
}
.btn-larger {
	min-width: fit-content;
}
.js-parent-user {
	display: none;
}
.multilingual-reflect-container {
	margin-top: 16px;
}
.talkappi-pulldown.js-translation-type.auto-translation .talkappi-dropdown-container.pulldown{
	background-color: #FFE6D6;
}
.talkappi-pulldown.js-translation-type .talkappi-dropdown-container.pulldown{
	background-color: #E3E5E8;
}
.translation-tips {
	font-size: 0.8em;
	color: #77797D;
}
</style>
<!-- BEGIN PAGE HEADER　-->
<div id="page_type" style="display:none;"><?php echo $_action?></div>
<!-- END PAGE HEAD -->
<!-- END PAGE HEADER-->
<!-- BEGIN PAGE CONTENT-->
<!-- Page Content -->
<div class="content-container white border">
	<div style="display:flex;justify-content:space-between;align-items:center;width: calc(100% - 424px);">
		<nav class="line-tab">
			<ul class="flexbox" style="margin: 0; padding-inline-start: 0px;flex-flow: wrap; gap: 6px 0px;">
			<?php
			foreach($_bot_lang as $k=>$v) {
				if (!in_array($k, $faq_langs)) continue;
				if ($k == $lang_cd) {
					echo('<li class=" active">');
				}
				else {
					echo('<li>');
				}
				$lang_style = '';
				if (!in_array($k, $faq_regist_langs)) {
					$lang_style = ' style="color: gray;"';
				}

				if ($mode == 'new') {
					echo('<a class="func-menu"' . $lang_style . ' href="/admin/' . $_action . '?mode=new&class_cd='. $class_cd .  '&lang=' . $k . '">');
				}
				else {
					if ($is_admin && $faq_id != NULL) {
						echo('<a class="func-menu"' . $lang_style . ' href="/admin/'  . $_action . '?id=' . $faq_id . '&lang=' . $k . '&admin=1">');
					} else {
						if (isset($bot_id)) {
							echo('<a class="func-menu"' . $lang_style . ' href="/admin/'  . $_action . '?cd=' . $intent_cd . '&lang=' . $k . '&sub_cd=' . $sub_intent_cd . '&bot_id=' . $bot_id . '">');
						}
						else {
							echo('<a class="func-menu"' . $lang_style . ' href="/admin/'  . $_action . '?cd=' . $intent_cd . '&lang=' . $k . '&sub_cd=' . $sub_intent_cd . '">');
						}
					}
				}
				echo("$_bot_lang[$k]");
				echo('</a></li>');
				$answer_types = ['txt'=>'テキスト','img'=>'画像','mov'=>'動画','car'=>'高度な設定','url'=>'URL'];
			}
			?>
			</ul>
		</nav>
	</div>
	<main class="edit-container faq-edit-container" style="display:none;">
		<!-- left container 入力画面 -->
		<section class="left-container">
			<!-- FAQ質問文 -->
			<div class="edit-faq-main-container">
				<?php if (count($sub_bots) > 0 && isset($bot_id)) { ?>
				<div class="talkappi-pulldown js-select-bot" style="margin-bottom:20px;width:400px;" data-name="bot_id" data-value=<?php echo $bot_id?> data-source='{}'></div>
				<?php }?>
				<div class="flexbox-x-axis" style="display: flex;align-items: center;">
					<h1 class="font-standard font-family-v5" style="margin: 0 2px 0 0;width: 36px;"><?php echo __('admin.common.label.question') ?></h1>
					<label class="btn-mini-square font-standard font-family-v1 js-answer-flag" style="margin: 0 36px 0 0;width: 64px;text-wrap: nowrap;<?php if ($public_flg == 1) {echo('background:#cff2d7');} else if ($public_flg == 0) {echo('color:white;background:#FFB848');} else {echo('background:#E5E5E5');} ?>">
						<?php if ($public_flg == 1) {echo __('admin.common.label.published');} else if($public_flg == 0) {echo __('admin.common.label.editing');} else {echo __('admin.common.label.not_answered');} ?>
					</label>
					<div class="" style="width: 100%; margin-right: 36px;">
						<?php if ($item_talk == null || $item_talk['level'] == 4) {?>
							<div>
								<input name="question_input" type="text" maxlength="500" class="form-control question_input" placeholder="" value="<?php if (isset($item_talk['question'])) { echo $item_talk['question']; } else { echo ''; } ?>">
								<?php if ($item_talk == null) {?>
									<div class="js-caution-container" style="background:#FFF0BB;display: flex;align-items: flex-start; padding: 0.5rem 1rem; margin: 0.5rem 0 0 0;border-radius: 4px;gap: 1rem;">
										<div>
											<p style="font-weight: 400;"><?php echo __('admin.faqnew.message.note_1') ?></p>
											<p style="font-weight: 600;"><?php echo __('admin.faqnew.message.note_2') ?></p>
										</div>
										<img class="js-close-caution" src="/assets/admin/css/img/icon-cancel-small.svg" alt="close">
									</div>
								<?php }?>
							</div>
						<?php } else {?>
							<p class="font-standard font-size-v3 font-family-v1">
								<?php
									if ($item_talk['facility_question_title'] != NULL && $item_talk['facility_question_title'] != '') {
										echo($item_talk['facility_question_title']);
									}
									else {
										echo($item_talk['question']);
									}
								?>
							</p>
						<?php }?>
					</div>
					<?php 
						$hide_translation_type = false;
						if ($public_flg != 1 || $lang_cd == 'ja') { 
							$hide_translation_type = true;
						} 
					?>
					<div class="talkappi-pulldown js-translation-type <?php echo $translation_type == '1' ? "auto-translation" : "" ?> <?php echo $hide_translation_type ? 'hide' : '' ?>" data-name="translation_type" data-value="<?php echo $translation_type ?>" data-source='[{"code":"0","text":"<?php echo __('admin.common.label.native_translation') ?>"},{"code":"1","text":"<?php echo __('admin.common.label.auto_translation') ?>"}]'></div>
				</div>
				<div class="lines-container category-container" style="<?php if ($item_talk == null || $item_talk['level'] == 4 || $code_div != "900101") { echo 'display: flex;';} else { echo 'display: none;';} ?>">
					<div class="talkappi-category-select js-faq-categorys" data-title=<?php echo __('admin.common.label.classification.add')?> data-name="class_cd" data-div='<?php echo $code_div ?>'
						data-value='<?php if ($class_cd == '') {echo('[]');} else {echo(json_encode(explode(' ', $class_cd)));}?>'>
					</div>
				</div>
			</div>
			<main class="">
				<!-- 回答を追加する -->
				<div class="btn-add-survey js-add-answer" style="margin: 46px 0 24px 0;">
					<img src="./../assets/admin/css/img/icon-add.svg" width="12" height="12">
					<span><?php echo __('admin.common.label.add_answer') ?></span>
				</div>
				<!-- メニュー -->
				<div class="edit-faq-menu js-edit-faq-menu" style="display: none;">
					<div class="flexbox-x-axis">
						<h1 class="font-standard font-family-v1 left-aligned"><?php echo __('admin.common.label.menu_answer') ?></h1>
						<div class="flexbox-x-axis js-delete-menu pointer">
							<span class="icon-delete"></span>
							<span class="font-standard font-color-v3" style="padding: 0 0 0 8px;"><?php echo __('admin.common.button.delete') ?></span>
						</div>
					</div>
					<!-- メニュータイトル -->
					<div class="flexbox js-menu-title-container" style="margin: 24px 0 0 0;">
						<h1 class="font-standard font-family-v1 survey-space-top-bottom-1 edit-answer-title"><?php echo __('admin.common.label.menu_title') ?></h1>
						<div class="" style="width: 100%;">
							<textarea class="text-input-longer js-menu-main-title edit-answer-title-input"style="white-space: pre-wrap;word-wrap: break-word;display:block;max-width:100%;"></textarea>
						</div>
					</div>
					<!-- メニュー編集 -->
					<div class="js-edit-faq-menu-wrap" style="margin: 16px 0 0 0;">
						<div class="flexbox-x-axis">
							<h1 class="font-standard font-family-v1 survey-space-top-bottom-1 edit-answer-title"><?php echo __('admin.common.label.menu_edit') ?></h1>
							<!-- 追加済み選択肢 -->
							<div class="flexbox-x-axis" style="flex-wrap: wrap;"id="sortable">
								<p class="js-menu-add-selection icon-round-corners-middle background-pale-gray font-color-v5" style="margin: 8px 10px 0 0 !important;"><span class="icon-add"></span><span class="js-add-text"><?php echo __('admin.common.label.add_button') ?></span></p>
							</div>
						</div>
					</div>
				</div>
				<!-- メニューを追加する -->
				<div class="btn-add-survey js-add-menu" style="margin: 46px 0 24px 0;">
					<img src="./../assets/admin/css/img/icon-menu.svg" width="12" height="12">
					<span><?php echo __('admin.common.label.add_menu') ?></span>
				</div>
				<!-- AIのおすすめ回答 -->
				<div style="box-shadow: 0px 1px 0px 0px #EBEDF2 inset; margin-top: 10px; padding: 12px 0px;">
					<div id="react-textareadiff"></div>
				</div>
				<!-- 翻訳 -->
				<?php if(count($faq_langs) > 1 && count($_bot_lang) > 1) { ?>
				<div id="react-multilingualreflect"></div>
				<?php }?>
				<!-- アラート -->
				<div class="flexbox-x-axis" style="margin: 25px 0 0 0;">
					<label class="font-standard font-family-v1 edit-answer-title flexbox-x-axis"><?php echo __('admin.common.label.alert.mail') ?></label>
					<div class="talkappi-radio js-alert" data-name="alert" data-value="<?php if(!isset($reminder[0])) { echo "01";} else { echo "02";}?>" data-source='{"01":"<?php echo __('admin.inquiry.label.answer_limit_no')?>", "02":"<?php echo __('admin.inquiry.label.answer_limit_yes')?>"}'></div>
				</div>
				<div class="flexbox-x-axis js-alert-content" style="margin: 16px 0px 0px 110px; <?php if(!isset($reminder[0])) echo 'display: none;'?>">
					<div class="edit-menu-container" style="width: auto;">
						<p class="flexbox-x-axis" style="height: 24px; padding:0 16px; background: #E3E5E8; border-radius: 13px; width: fit-content;
							<?php if (!array_key_exists(0, $reminder) || ($reminder[0]['upd_user'] !== "0")) echo 'display: none;'?>">
							<?php echo __('admin.talknew.label.previous.alert')?>：<span><?php if(array_key_exists(0,$reminder)) echo $reminder[0]['remind_time'] ?></span><?php echo __('admin.talknew.label.alert.done')?><span class="icon-success"></span>
						</p>
						<div class="flexbox-x-axis" style="margin: 12px 0 0 0;">
							<h1 class="font-standard font-family-v1 menu-edit-title" style="margin: 0 26px 0 0;"><?php echo __('admin.common.label.alert.date') ?></h1>
							<div class="survey-period-date-container" style="background: #fff;">
								<span class="icon-calender"></span>
								<input name="alert_day" id="alert_day" placeholder="<?php echo date('Y-m-d', strtotime('+1 day'));?>" value="<?php if(isset($reminder[0]) && array_key_exists('remind_time', $reminder[0]) && $reminder[0]['upd_user']!=="0") echo substr($reminder[0]['remind_time'], 0, 10)?>" class="date-picker line-none" style="width: 100%;" size="16" data-date-format="yyyy-mm-dd" type="text" autocomplete="off" />
							</div>
							<input name="alert_time" id="alert_time" type="text" style="width:64px;height:28px;" class="form-control timepicker timepicker-24" placeholder="12:00" value="<?php if(isset($reminder[0]) && array_key_exists('remind_time', $reminder[0]) && $reminder[0]['upd_user']!=="0") echo substr($reminder[0]['remind_time'], 11, 5)?>" />
						</div>
						<div class="flexbox-x-axis" style="margin: 12px 0 0 0;">
							<div class="flexbox-x-axis js-select-user-container">
								<h1 class="font-standard font-family-v1 menu-edit-title" style="margin: 0 26px 0 0;"><?php echo __('admin.common.label.mail.destination') ?></h1>
								<div class="js-add-user-container" style="display:flex; flex-wrap: wrap;gap: 8px;">
									<ul class="btn round light-blue pointer js-selected-users selected-users" style="list-style:none;display:none;padding: 3px 11px;flex-wrap: wrap;    height: fit-content;"></ul>
									<div class="btn round light-blue pointer"><img src="./../assets/admin/css/img/icon-add.svg"><?php echo __('admin.common.label.mail.destination') ?></div>
								</div>
								<input type="hidden" name="mail_users" id="mail_users" value="<?php if(isset($reminder[0]) && array_key_exists('mail_users', $reminder[0]) && $reminder[0]['upd_user']!=="0") echo $reminder[0]['mail_users'] ?>">
							</div>
						</div>
						<div class="flexbox-x-axis" style="margin: 12px 0 0 0;">
						<h1 class="font-standard font-family-v1 menu-edit-title" style="margin: 0 26px 0 0;"><?php echo __('admin.common.label.note') ?></h1>
							<textarea class="fullwidth-textarea menu-text"style="white-space: pre-wrap;word-wrap: break-word;display:block;max-width:100%;" name="alert_text"><?php if(isset($reminder[0]) && array_key_exists('mail_params', $reminder[0]) && $reminder[0]['upd_user']!=="0") echo json_decode($reminder[0]['mail_params'], JSON_UNESCAPED_UNICODE)['remark']?></textarea>
						</div>
					</div>
				</div>
				<!-- SUBMITボタン -->
				<div class="submit-btn-container" style="margin: 44px 0 0 0;">
					<div class="btn-larger btn-blue js-publish-button" id=""><?php echo __('admin.common.label.save_publish') ?></div>
					<div class="btn-larger btn-gray-black js-tmp-save-button"><?php echo __('admin.common.label.temporary_save') ?></div>
					<?php if($bot_grp_id!=-1) { ?>
						<div class="btn-larger btn-gray-black js-copy-button"><?php echo __('admin.common.label.copy_group_facilities') ?></div>
					<?php }?>
					<?php if ($item_talk['level'] == 4 && $mode!= 'cate') {?>
						<button type="button" id="deleteButton" class="btn-larger btn-red-border">
							<span class="icon-delete" style="margin: 0;"></span>
						</button>
					<?php }?>
					<?php if ($mode != 'new' && $real_bot_id == $bot_id && $_action === 'talknew' && $lang_cd == 'ja') {?>
						<button type="button" id="deleteChatbotFAQButton" class="btn-larger btn-red-border">
							<span class="icon-delete" style="margin-right: 8px !important;"></span> <span style="color: black;"><?php echo __('admin.common.label.del_faq_all_lang_data') ?></span>
						</button>
					<?php }?>
					<?php if(strpos($_action,'talk') === false) {?>
						<div class="" id=""><a href="/admin/faqs" class="btn-larger btn-white"><?php echo __('admin.common.button.return_to_list') ?></a></div>
						<?php } else {?>
							<div class="" id=""><a href="/admin/talks" class="btn-larger btn-white"><?php echo __('admin.common.button.return_to_list') ?></a></div>
					<?php }?>
				</div>
			</main>
		</section>
		<!-- right container プレビュー画面 -->
		<section class="right-container" style="margin: 17px 37px 0 0;">
			<!-- 親ボットのみ -->
		  <?php
		  	$model = new Model_Adminmodel(); 
		  	if($model->get_grp_bot_id($bot_id) == 0) { // 親ボットの場合
				?> 
				<div>
					<div class="checkbox-label">
					<input type="checkbox" name="inherit" id="inherit" <?php if($item_talk['inherit']==1||$item_talk['inherit']==null) echo 'checked'?>>
						<label for="inherit"><?php echo __('admin.talknew.button.inherit_child_bots') ?></label>
					</div>
				</div>
				<div class="js-select-inherit-container" style="border-bottom: 1px solid #e3e5e8;padding: 12px 0; margin:0 0 20px 0;">	
					<div class="js-inherit-bot" style="display:flex;">
						<ul class="btn round light-blue pointer js-selected-inherit-container" 
							style="list-style: none; max-width: 320px; display: flex; flex-wrap: wrap; height: auto; flex-direction: column; align-items: center; padding: 4px 8px;
							<?php if(!$item_talk['inherit_exception']) echo 'display: none;'?>"></ul>
						<div class="btn round light-blue pointer js-select-inherit"><img src="./../assets/admin/css/img/icon-add.svg">例外施設選択</div>
					</div>
					<input type="hidden" name="inherit_exception" value="<?php echo $item_talk['inherit_exception'] ?>">
				</div>
			<?php } ?>
			<!-- 最終編集 -->
			<div class="pointer">
				<?php
					if (count($history_list) > 0) { ?>
					<div class="">
					<select name="history_no" id="history_no" class="faq-edit-history flexbox-center line-none">
						<option value="" disabled selected hidden><?php echo $history_list_display ?></option>
						<?php foreach ($history_list as $key => $value) { ?>
							<option value="<?php echo $key; ?>" <?php if ($key == $history_no) { echo 'selected'; } ?>><?php echo $value; ?></option>
						<?php } ?>
					</select>
					</div>
				<?php } else { ?>
					<div class="faq-edit-history flexbox-center">
						<span class="faq-edit-history-title"><?php echo __('admin.talknew.label.edit_history_none') ?></span>
						<span class="icon-drop-down-close right-aligned"></span>
					</div>
				<?php } ?>
			</div>
			<!-- プレビュー画面 -->
			<div class="faq-preview-container" style="margin: 12px 0 0 0;position: sticky; top: 0;">
				<!-- ヘッダー -->
				<header class="faq-preview-header">
					<h1 class="font-standard font-family-v2 font-weight-v1"><?php echo __('admin.common.label.preview') ?></h1>
					<?php if(!strpos($url, "ja")) { ?>
						<p class="flexbox-x-axis js-toggle-trans-ref pointer" style="margin:0 0 0 auto;">
							<span class="icon-check"></span>
							<span class=""><?php echo __('admin.common.label.japanese_reference') ?></span>
						</p>
					<?php }?>
				</header>
				<!-- メイン(画面) -->
				<div class="faq-preview-main js-faq-preview-main" id="js-faq-preview-main">
					<!-- 質問 -->
					<p class="faq-preview-faq-title font-standard font-family-v1 font-color-v4">
						<?php
							if ($item_talk['facility_question_title'] != NULL && $item_talk['facility_question_title'] != '') {
								echo($item_talk['facility_question_title']);
							}
							else {
								echo($item_talk['question']);
							}
						?>
					</p>
					<!-- メニューアイコン -->
					<div class="js-preview-faq-menu preview-faq-main-title" style='display: none;'>
						<p class="js-preview-faq-menu-main-title preview-faq-menu-main-title"></p>
						<div class="js-preview-faq-icon-container preview-faq-icon-container">
						</div>
					</div>
					<!-- メニュー内容 -->
				</div>
				<!-- 多言語　日本語表示 -->
				<div class="faq-preview-main faq-preview-ref js-ref-main-container" style="display: none;">
					<div class="js-ref-container ref-container clone" style="display:none;">
						<div class="font-standard font-family-v1 font-weight-v1">
							<!-- 回答、メニュー回答、選択肢 -->
							<span class="js-ref-type font-color-v2"></span>
							<!-- 数字 -->
							<span class="js-ref-num"></span>
						</div>
						<h1 class="js-ref-title ref-title font-standard font-family-v1">
							<?php echo __('admin.common.label.title') ?>：<span class=""></span>
						</h1>
						<p class="js-ref-text font-standard font-family-v1">
							<span class="js-ref-text-title"><?php echo __('admin.common.label.body') ?></span>：
							<span class="js-ref-text-main ref-text-main font-color-v2"></span>
						</p>
					</div>
					<div class="js-ref-menu-title-container ref-container clone"  style="display:none;">
						<div class="font-standard font-family-v1 font-weight-v1">
							<span class="font-color-v2"><?php echo __('admin.common.label.menu_answer') ?></span>
							<p class="js-ref-menu-title font-standard font-family-v1">
								<span><?php echo __('admin.common.label.menu_title') ?></span>：
								<span class="js-ref-menu-title-text font-color-v2"></span>
						</p>
						</div>
					</div>
				</div>
			</div>
		</section>
	</main>
</div>

<!-- template -->
<!-- 回答編集 -->
<div class="edit-faq-answer js-edit-faq-answer js-faq-item clone" style="display: none;">
	<div class="flexbox-x-axis">
		<h1 class="font-standard font-family-v1 left-aligned" style="text-decoration: underline;">
			<?php echo __('admin.common.label.answer') ?>
			<span class="js-answer-num"></span>
		</h1>
		<div class="flexbox-x-axis js-delete-answer pointer">
			<span class="icon-delete"></span>
			<span class="font-standard font-color-v3" style="padding: 0 0 0 8px;"><?php echo __('admin.common.button.delete') ?></span>
		</div>
	</div>
	<div class="js-edit-contents-container cloned">
		<!-- 回答形式 -->
		<div class="flexbox-x-axis" style="margin: 16px 0 0 0;">
			<h1 class="font-standard font-family-v1 survey-space-top-bottom-1 edit-answer-title"><?php echo __('admin.common.label.answer_format') ?></h1>
			<div class="checkbox-v2-container js-checkbox-v2-container flexbox-x-axis pointer">
				<div class="checkbox-v2-options flexbox-x-axis js-select-txt inner-space-3" id="txt"><?php echo __('admin.common.label.text') ?></div>
				<div class="flexbox-x-axis js-select-img inner-space-3" id="img"><?php echo __('admin.common.label.image') ?></div>
				<div class="flexbox-x-axis js-select-mov inner-space-3" id="mov"><?php echo __('admin.common.label.video') ?></div>
				<?php if ($item_talk == null || $item_talk['level'] == 4) {?>
					<div class="flexbox-x-axis js-select-htm inner-space-3" id="htm">HTML</div>
				<?php }?>
				<div class="flexbox-x-axis js-select-car inner-space-3" id="car"><?php echo __('admin.common.label.advanced_settings') ?></div>
			</div>
		</div>
		<!-- 本文 -->
		<div class="flexbox js-faq-answer-input" style="margin: 12px 0 0 0;">
			<h1 class="font-standard font-family-v1 survey-space-top-bottom-1 edit-answer-title" style="margin: 10px 0 0 0;"><?php echo __('admin.common.label.body') ?></h1>
			<div class="" style="width: 100%;">
				<textarea name="" id="" class="fullwidth-textarea js-faq-answer"style="white-space: pre-wrap;word-wrap: break-word;display:block;max-width:100%;"></textarea>
			</div>
		</div>
		<!-- 画像、動画、高度な設定 -->
		<div class="flexbox-x-axis js-menu-add-option-container" style="display: none; margin: 12px 0 0 0;">
			<h1 class="font-standard font-family-v1 menu-edit-title" style="margin: 0 26px 0 0;"><span class="js-menu-add-option-title"></span></h1>
			<!-- 画像、動画 -->
			<div class="talkappi-upload" data-name="image_base64" data-type="img" data-label="" data-max-size="2"></div>
			<!-- 高度な設定 -->
			<div class="talkappi-skill-select js-car-container" data-value="" data-mode="skill" <?php if ($temp_bot_id !== NULL) echo 'data-bot_id="' . $temp_bot_id . '"' ?>></div>
		</div>
		<!-- HTML -->
		<div class="flexbox js-faq-answer-html" style="display: none; margin: 12px 0 0 0;">
			<h1 class="font-standard font-family-v1 menu-edit-title" style="margin: 0 26px 0 0;">HTML</h1>
			<div class="" style="width: 100%;position: relative;">
				<div class="fullwidth-textarea js-menu-html menu-html" style="max-width:100%;"></div>
				<div class="js-to-open-summernote-editor to-open-summernote-editor flexbox-x-axis">
					<span class="icon-form-zoom-in"></span>
					<span><?php echo __('admin.common.label.edit_html') ?></span>
				</div>
			</div>
		</div>
	</div>
</div>
<!-- template -->
<!-- メニュー編集 -->
<div class="edit-menu-container js-faq-item js-edit-menu-container clone" style="margin: 16px 0 0 110px; display: none; width: auto;">
	<div class="flexbox-x-axis">
		<h1 class="font-standard font-family-v1 menu-edit-title" style="margin: 0 26px 0 0;"><?php echo __('admin.common.label.button_title') ?></h1>
		<input type="text" class="text-input-longer js-menu-title" style="max-width: 100%;">
		<span class="icon-delete js-delete-menu-container pointer" style="padding: 0 10px; margin: 0 0 0 10px;"></span>
	</div>
	<div class="js-edit-contents-container cloned">
		<div class="flexbox-x-axis" style="padding: 12px 0 0 0; margin: 12px 0 0 0; box-shadow: inset 0px 1px 0px #ebedf2;">
			<h1 class="font-standard font-family-v1 menu-edit-title" style="margin: 0 26px 0 0;"><?php echo __('admin.common.label.message') ?>
				<span class="js-menu-message-num"></span>
			</h1>
			<div class="flexbox-x-axis js-delete-menu-message pointer" style="margin:0 0 0 auto;">
				<span class="icon-delete"></span>
				<span class="font-standard font-color-v3" style="padding: 0 0 0 8px;"><?php echo __('admin.common.button.delete') ?></span>
			</div>
		</div>
		<!-- 回答形式 -->
		<div class="flexbox-x-axis" style="margin: 12px 0 0 0;">
			<h1 class="font-standard font-family-v1 menu-edit-title" style="margin: 0 26px 0 0;"><?php echo __('admin.common.label.answer_format') ?></h1>
			<div class="checkbox-v2-container js-checkbox-v2-container flexbox-x-axis pointer">
				<div class="checkbox-v2-options flexbox-x-axis js-select-txt inner-space-3" id="txt"><?php echo __('admin.common.label.text') ?></div>
				<div class="flexbox-x-axis js-select-img inner-space-3" id="img"><?php echo __('admin.common.label.image') ?></div>
				<div class="flexbox-x-axis js-select-mov inner-space-3" id="mov"><?php echo __('admin.common.label.video') ?></div>
				<?php if ($item_talk == null || $item_talk['level'] == 4) {?>
					<div class="flexbox-x-axis js-select-htm inner-space-3" id="htm">HTML</div>
				<?php }?>
				<div class="flexbox-x-axis js-select-car inner-space-3" id="car"><?php echo __('admin.common.label.advanced_settings') ?></div>
			</div>
		</div>
		<!-- 本文 -->
		<div class="flexbox js-faq-answer-input" style="margin: 12px 0 0 0;">
			<h1 class="font-standard font-family-v1 menu-edit-title" style="margin: 0 26px 0 0;"><?php echo __('admin.common.label.body') ?></h1>
			<div class="" style="width: 100%;">
				<textarea class="fullwidth-textarea js-menu-text menu-text"style="white-space: pre-wrap;word-wrap: break-word;display:block;max-width:100%;"></textarea>
			</div>
		</div>
		<!-- 画像、動画、高度な設定 -->
		<div class="flexbox-x-axis js-menu-add-option-container" style="display: none; margin: 12px 0 0 0;">
			<h1 class="font-standard font-family-v1 menu-edit-title" style="margin: 0 26px 0 0;"><span class="js-menu-add-option-title"></span></h1>
			<!-- 画像、動画 -->
			<div class="talkappi-upload" data-name="image_base64" data-type="img" data-label="" data-max-size="2"></div>
			<!-- 高度な設定 -->
			<div class="talkappi-skill-select js-car-container" data-value="" data-mode="skill" <?php if ($temp_bot_id !== NULL) echo 'data-bot_id="' . $temp_bot_id . '"' ?>></div>
		</div>
		<!-- HTML -->
		<div class="flexbox js-faq-answer-html" style="display: none; margin: 12px 0 0 0;">
			<h1 class="font-standard font-family-v1 menu-edit-title" style="margin: 0 26px 0 0;">HTML</h1>
			<div class="" style="width: 100%;position: relative;">
				<div class="fullwidth-textarea js-menu-html menu-html" style="max-width:100%;"></div>
				<div class="js-to-open-summernote-editor to-open-summernote-editor flexbox-x-axis">
					<span class="icon-form-zoom-in"></span>
					<span><?php echo __('admin.common.label.edit_html') ?></span>
				</div>
			</div>
		</div>
	</div>
	<div class="btn-add-survey js-add-menu-contents" style="margin: 32px 0 0 0;">
		<img src="./../assets/admin/css/img/icon-add.svg" width="12" height="12">
		<span><?php echo __('admin.common.label.add_message') ?></span>
	</div>
</div>
<!-- templete -->
<!-- メニューコンテンツ(回答形式、本文など入力部分) -->
<div class="js-edit-contents-container clone" style="display: none;">
	<div class="flexbox-x-axis" style="padding: 12px 0 0 0; margin: 12px 0 0 0; box-shadow: inset 0px 1px 0px #ebedf2;">
		<h1 class="font-standard font-family-v1 menu-edit-title" style="margin: 0 26px 0 0;"><?php echo __('admin.common.label.message') ?>
			<span class="js-menu-message-num"></span>
		</h1>
		<div class="flexbox-x-axis js-delete-menu-message pointer" style="margin:0 0 0 auto;">
			<span class="icon-delete"></span>
			<span class="font-standard font-color-v3" style="padding: 0 0 0 8px;"><?php echo __('admin.common.button.delete') ?></span>
		</div>
	</div>
	<!-- 回答形式 -->
	<div class="flexbox-x-axis" style="margin: 12px 0 0 0;">
		<h1 class="font-standard font-family-v1 menu-edit-title" style="margin: 0 26px 0 0;"><?php echo __('admin.common.label.answer_format') ?></h1>
		<div class="checkbox-v2-container js-checkbox-v2-container flexbox-x-axis pointer">
			<div class="checkbox-v2-options flexbox-x-axis js-select-txt inner-space-3" id="txt"><?php echo __('admin.common.label.text') ?></div>
			<div class="flexbox-x-axis js-select-img inner-space-3" id="img"><?php echo __('admin.common.label.image') ?></div>
			<div class="flexbox-x-axis js-select-mov inner-space-3" id="mov"><?php echo __('admin.common.label.video') ?></div>
			<?php if ($item_talk == null || $item_talk['level'] == 4) {?>
				<div class="flexbox-x-axis js-select-htm inner-space-3" id="htm">HTML</div>
			<?php }?>
			<div class="flexbox-x-axis js-select-car inner-space-3" id="car"><?php echo __('admin.common.label.advanced_settings') ?></div>
		</div>
	</div>
	<!-- 本文 -->
	<div class="flexbox js-faq-answer-input" style="margin: 12px 0 0 0;">
		<h1 class="font-standard font-family-v1 menu-edit-title" style="margin: 0 26px 0 0;"><?php echo __('admin.common.label.body') ?></h1>
		<div class="" style="width: 100%;">
			<textarea class="fullwidth-textarea js-menu-text menu-text"style="white-space: pre-wrap;word-wrap: break-word;display:block;max-width:100%;"></textarea>
		</div>
	</div>
	<!-- 画像、動画、高度な設定 -->
	<div class="flexbox-x-axis js-menu-add-option-container" style="display: none; margin: 12px 0 0 0;">
		<h1 class="font-standard font-family-v1 menu-edit-title" style="margin: 0 26px 0 0;"><span class="js-menu-add-option-title"></span></h1>
		<!-- 画像、動画 -->
		<div class="talkappi-upload" data-name="image_base64" data-type="img" data-label="" data-max-size="2"></div>
		<!-- 高度な設定 -->
		<div class="talkappi-skill-select js-car-container" data-value="" data-mode="skill" <?php if ($temp_bot_id !== NULL) echo 'data-bot_id="' . $temp_bot_id . '"' ?>></div>
	</div>
	<!-- HTML -->
	<div class="flexbox js-faq-answer-html" style="display: none; margin: 12px 0 0 0;">
		<h1 class="font-standard font-family-v1 menu-edit-title" style="margin: 0 26px 0 0;">HTML</h1>
		<div class="" style="width: 100%;position: relative;">
			<div class="fullwidth-textarea js-menu-html menu-html" style="max-width:100%;"></div>
			<div class="js-to-open-summernote-editor to-open-summernote-editor flexbox-x-axis">
				<span class="icon-form-zoom-in"></span>
				<span><?php echo __('admin.common.label.edit_html') ?></span>
			</div>
		</div>
	</div>
</div>
<!-- template -->
<!-- リッチテキストエディタ -->
<div class="survey-branch-modal-container js-summernote-modal rich-text-editor" style="display: none;">
	<div class="flexbox flexbox-baselines">
		<h1 class="font-standard font-size-v5 font-family-v4"style="margin:0 auto 24px 0;"><?php echo __('admin.common.label.title_edit') ?></h1>
		<span class="icon-cancel-large pointer js-cancel"></span>
	</div>
	<div class="js-summernote-editors" style="display: none;"></div>
	<div class="submit-btn-container" style="position: fixed; bottom: 12px;">
		<div class="btn-larger btn-blue js-ok">OK</div>
		<div class="btn-larger btn-white js-cancel"><?php echo __('admin.common.button.cancel') ?></div>
	</div>
</div>
<!-- template -->
<!-- モーダル背景 -->
<div class="modal-background js-modal-background" style="display: none;"></div>
<!-- template -->
<!-- メニューアイコン削除モーダル -->
<div class="js-delete-icon-modal sdelete-icon-modal" style="display: none;">
	<div style="display: grid; height: 100%; width: 100%;">
		<h1 class="font-standard font-size-v5 font-family-v1" style="margin: 36px auto 0;text-align: center;">
		<span class="js-answer-type-title"></span>
		<span class="js-title-text"></span>
		<span class="js-modal-answer-num"></span>
		</h1>
		<p class="font-standard font-size-v3 font-family-v1" style="line-height: 1.71; margin: 0px auto 0; width: 312px;"><?php echo __('admin.common.message.action_cannot_undone') ?></p>
		<div class="flexbox-x-axis"  style="margin: 0 auto;">
			<div class="js-menu-modal-cancel-button font-standard font-size-v3 font-family-v1 font-color-v2 menu-modal-cancel-button pointer"><?php echo __('admin.common.button.cancel') ?></div>
			<div class="js-delete-icon font-standard font-size-v3 font-family-v1 font-color-v6 pointer"><?php echo __('admin.common.message.confirm_to_delete') ?></div>
		</div>
	</div>
</div>
<?php echo $skillbox ?>

<script src="/assets/common/react/components/blocks/multilingualreflect.bundle.js"></script>
<script src="/assets/common/react/components/atoms/faqupdatetextareadiff.bundle.js"></script>