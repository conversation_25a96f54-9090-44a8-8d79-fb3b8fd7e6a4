<div class="top-nav">
	<?php echo $menu ?>
</div>
<div class="content-container white border">
	<input type="hidden" id="item_id" value="<?php echo $product_id?>" />
	<input type="hidden" name="no" id="no" value="" />
	<div class="section-container">
		<h2>アップロード済みの画像</h2>
		<table class="table table-striped table-bordered table-hover">
			<thead>
			<tr>
				<th></th>											
				<th>URL</th>							
				<th></th>
			</tr>
			</thead>
			<tbody>
				<?php
				$no = 0;
				$tl_config = $config['tl_config'];
				foreach ($product_images as $image) {
					echo('<tr class="gradeX odd" role="row">');	
					echo('<td>');
					$image['url'] = str_replace($tl_config['original_image_url'], $tl_config['mapping_image_url'], $image['url']);
					echo('<img style="width:120px;border-radius:5px;" src="'. $image['url'] . '" />');
					echo('</td>');
					echo('<td>');
					echo($image['url']);
					echo('</td>');
					echo('<td>');
					echo('<a href="javascript:void(0);" class="btn round image edit js-main" no="' . $no . '">メイン画像にする</a>');
					echo('<a href="javascript:void(0);" class="btn round image delete js-delete" no="' . $no . '">削除</a>');
					echo('<a href="' . $image['url'] . '" target="_blank" class="btn round image copy">表示</a>');
					echo('<a href="javascript:void(0);" class="btn round js-move-up" no="' . $no . '"> ↑ </a>');
					echo('<a href="javascript:void(0);" class="btn round js-move-down" no="' . $no . '"> ↓ </a>');
					echo('</td>');
					echo('</tr>');
					$no++;
				}?>
			</tbody>
		</table>													
	</div>
	<div class="section-container">
		<h2>画像追加</h2>
		<div class="talkappi-upload" data-name="image_base64" data-type="img" data-label="" data-url="" data-max-size="2"></div>
	</div>

	<div class="form-actions">
		<div class="row">
			<div class="col-md-offset-2 col-md-9 flex">
				<button type="submit" class="btn-larger btn-blue js-action-save">保存</button>
				<button type="button" onclick="top.location='/admin/<?php echo $func?>s'" class="btn-larger btn-white js-action-back">戻る</button>
			</div>
		</div>
	</div>								
</div>
