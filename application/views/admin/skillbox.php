<div class="modal fade" id="skill_box" tabindex="-1" role="basic" aria-hidden="true">
	<div class="modal-dialog" style="width:720px">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
				<h4 class="modal-title" id="skill_title">SKILL設定</h4>
			</div>
			<input type="hidden" id="class_div" value="" />
			<div id="intent_cd" style="display:none;"></div>
			<div id="sub_intent_cd" style="display:none;"></div>
			<div id="skill_data" style="display:none;"></div>
			<div id="param_data" style="display:none;"></div>
			<div id="param" style="display:none;"></div>
			<div class="form-group" style="margin:10px;">
				<?php echo Form::select('skill_kind', $skill_kinds, '', array('id'=>'skill_kind','class'=>'form-control'))?>
			</div>			
			<div class="form-group" style="margin:10px;">
				<?php echo Form::select('skill', [], '', array('id'=>'skill','class'=>'form-control'))?>
			</div>
			<div class="modal-body" id="skill_body">
		
			</div>
			<div class="modal-footer">
				<?php if ($all_language !== NULL) { ?>
				<label style="float:left;" class="control-label col-md-2"><?php echo __('admin.common.label.applies_to_all_languages') ?></label>
				<div class="col-md-2" style="float:left;">
					<input type="checkbox" name="flg_apply_all_lang" <?php if (true || $all_language == 1) echo('checked')?> value="1" class="make-switch" data-on-color="success" data-off-color="warning">
				</div>
				<?php }?>
				<?php if (isset($default_btn)) { ?>
				<button type="button" class="btn yellow act" id="default_skill"><?php echo __('admin.common.button.return_to_default') ?></button>
				<?php }?>
				<button type="button" class="btn default" data-dismiss="modal"><?php echo __('admin.common.button.cancel') ?></button>
				<button type="button" class="btn red act" id="delete_skill"><?php echo __('admin.common.button.delete') ?></button>
				<button type="button" class="btn green act" id="save_skill"><?php echo __('admin.common.button.confirm') ?></button>
			</div>
		</div>
		<!-- /.modal-content -->
	</div>
	<!-- /.modal-dialog -->
</div>