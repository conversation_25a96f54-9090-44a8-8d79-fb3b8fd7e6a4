<link href="/assets/admin/pages/css/blog.css" rel="stylesheet"/>
<link rel="stylesheet" type="text/css" href="/assets/global/plugins/bootstrap-wysihtml5/bootstrap-wysihtml5.css"/>
<link rel="stylesheet" type="text/css" href="/assets/global/plugins/bootstrap-markdown/css/bootstrap-markdown.min.css">
<link rel="stylesheet" type="text/css" href="/assets/global/plugins/bootstrap-summernote/summernote.css">

<!-- BEGIN PAGE HEADER-->
<!-- BEGIN PAGE HEAD -->
<div class="page-head">
	<!-- BEGIN PAGE TITLE -->
	<div class="page-title">
		<h1><?php echo($_active_menu_name)?><small></small></h1>
	</div>
	<!-- END PAGE TITLE -->
</div>
<!-- <PERSON><PERSON> PAGE HEAD -->
<!-- END PAGE HEADER-->
<!-- BEGIN PAGE CONTENT-->
<div class="row">
	<div class="col-md-12">
        <!-- Page Content -->
        <div id="page-wrapper">
	        <!-- <div class="portlet light"> -->
			<?php 
				if (strlen($_active_menu) > 4) {
					$tab_menu = View::factory ('admin/menutab');
					echo($tab_menu);
				}
			?>
		<div class="edit-container">
        	<div class="settings-container">
			<div class="portlet box">
				<div class="portlet-body">
					<input type="hidden" name="ticket_id" id="ticket_id" value="<?php echo $ticket_id?>"/>
					<input type="hidden" name="no" id="no" value="<?php echo $no?>"/>
					<input type="hidden" name="content" id="content" value=""/>
					<input type="hidden" name="display_flg" id="display_flg" value="<?php echo $post['display_flg'] ?>">
					<input type="hidden" name="notify_flg" id="notify_flg" value="<?php echo $post['notify_flg'] ?>">
					<input type="hidden" name="act" id="act" value="" />
					<div class="form-body">	
					<?php if ($no == NULL) {		?>						
						<div class="form-group">
							<label class="control-label col-md-2">タイトル</label>
							<div class="col-md-10">
								<input name="title" id="title" type="text" class="form-control" style="width:100%;" placeholder="" value="<?php echo($post['title'])?>">
							</div>
						</div>
						<div class="form-group">
							<label class="control-label col-md-2">分類</label>
							<div class="col-md-2">
								<div class="input-group">
									<span class="input-group-addon">
									<i class="fa fa-filter"></i>
									</span>
									<?php echo Form::select('ticket_tag_cd', $ticket_tag_cd, $post['ticket_tag_cd'], array('id'=>'ticket_tag_cd','class'=>'form-control','style'=>'width:120px'))?>
								</div>
							</div>
						</div>							
						<div class="form-group" style="display: none;">
							<label class="control-label col-md-2">タイプ</label>
							<div class="col-md-2">
								<div class="input-group">
									<span class="input-group-addon">
									<i class="fa fa-filter"></i>
									</span>
									<?php echo Form::select('ticket_type_cd', $ticket_type, $post['ticket_type_cd'], array('id'=>'ticket_type_cd','class'=>'form-control','style'=>'width:120px'))?>
								</div>
							</div>
						</div>							
						<div class="form-group" style="display: none;">
							<label class="control-label col-md-2">ステータス</label>
							<div class="col-md-2">
								<div class="input-group">
									<span class="input-group-addon">
									<i class="fa fa-filter"></i>
									</span>
									<?php echo Form::select('ticket_status_cd', $ticket_status, $post['ticket_status_cd'], array('id'=>'ticket_status_cd','class'=>'form-control','style'=>'width:120px'))?>
								</div>
							</div>
						</div>		
					<?php }?>	
						<div class="form-group">
							<label class="control-label col-md-2">内容</label>
							<div class="col-md-10">
								<div id="summernote"> <?php echo $post['content']?></div>
							</div>
						</div>
						<div class="form-group">
							<label class="control-label col-md-2">表示状態</label>
							<div class="col-md-2" style="padding-top:7px;">
								<div class="talkappi-switch switch-display" data-value=<?php echo $post['display_flg'] == 1 ? "1" : "0" ?> style="display:inline-block;"></div>
								<span style="margin-left:10px;">表示する</span>
							</div>
						</div>
						<div class="form-group">
							<label class="control-label col-md-2">ポップアップ表示</label>
							<div class="col-md-2" style="padding-top:7px;">
								<div class="talkappi-switch  switch-notify" data-value=<?php echo $post['notify_flg'] == 1 ? "1" : "0" ?> style="display:inline-block;"></div>
								<span style="margin-left:10px;">表示する</span>
							</div>
						</div>
					</div>
					<div class="form-actions">
						<div class="row">
							<div class="col-md-offset-2 col-md-9">
								<button type="button" id="saveButton" class="btn blue mr10">
								<i class="fa fa-save mr10"></i>確定</button>
								<?php if ($ticket_id !== null) { ?>
									<button type="button" id="deleteButton" class="btn red mr10">削除</button>
								<?php }?>					
								<button type="button" onclick="top.location='<?php echo $back_url?>'" class="btn grey-steel mr10">戻る</button>
							</div>
						</div>
					</div>
				</div>
			</div>
			</div>
		</div>
</div>
</div>
</div>
<!-- END PAGE CONTENT-->