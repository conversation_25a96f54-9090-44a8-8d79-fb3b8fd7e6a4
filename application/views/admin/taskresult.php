<style type="text/css">
.btn.default:active, .btn.default.active {
  background-image: none;
  background-color: #45b6af;
  color: #fff;
}
</style>	
			<!-- BEGIN PAGE HEADER-->
			<!-- BEGIN PAGE HEAD -->
			<div class="page-head">
				<!-- BEGIN PAGE TITLE -->
				<div class="page-title">
					<h1>タスク管理<small></small></h1>
				</div>
				<!-- END PAGE TITLE -->
			</div>
			<!-- END PAGE HEAD -->
			<!-- END PAGE HEADER-->
			<!-- BEGIN PAGE CONTENT-->
			<div class="row">
				<div class="col-md-12">
			        <!-- Page Content -->
			        <div id="page-wrapper">
						<?php echo $menu ?>
						<div class="edit-container">
						<div class="settings-container">
								<input type="hidden" id="act" name="act" value="" />
								<div class="form-body">
									<div class="form-group" style="display:none;">
										<label class="control-label col-md-2">送信結果</label>
										<div class="col-md-3">
											<div class="input-group">
												<span class="input-group-addon">
												<i class="fa fa-filter"></i>
												</span>
												<?php //echo Form::select('result', [''=>'-'] + $_codes['15'], $post['result'], array('id'=>'result','class'=>'form-control'))?>
											</div>
										</div>
									</div>
							<table class="table table-striped table-bordered table-hover js-data-table">
							<thead>
							<tr>
								<th>
								配信時刻
								</th>
								<th>
								お客様
								</th>								
								<th>
								結果
								</th>														
							</tr>
							</thead>
							<tbody>
							<?php
								foreach ($results as $log) {
							?>	
							<tr class="gradeX odd" role="row">
								<td>
									 <?php echo($log['process_time'])?>
								</td>
								<td>
									 <?php 
									 if ($log['name'] != NULL) {
									 	echo($member['name']);
									 }
									 else if ($log['sns_type_cd'] == 'wb') {
									 	echo('Webユーザ');
									 }
									 else if ($log['first_name'] != NULL && $log['first_name'] != '') {
									 	echo($log['last_name'] . ' ' . $log['first_name']);
									 }
									 

									 if ($log['sns_type_cd']== 'fb') {
									 	echo('<span class="label label-primary" style="margin-left: 5px;" >Facebook</span>');
									 }
									 if ($log['sns_type_cd']== 'ln') {
									 	echo('<span class="label label-success" style="margin-left: 5px;" >Line</span>');
									 }
									 if ($log['sns_type_cd']== 'wc') {
									 	echo('<span class="label label-success" style="margin-left: 5px;" >WeChat</span>');
									 }
									 if ($log['sns_type_cd']== 'wb') {
									 	echo('<span class="label label-warning" style="margin-left: 5px;" >Web</span>');
									 }

									 ?>
									 <a class="pop_adminchat" member_id="<?php echo $log['member_id'] ?>">
									<span class="badge badge-primary" style="margin: 5px;" >チャット</span> </a>
								</td>			
								<td>
									 <?php echo($_codes['15'][$log['process_result']])?>
								</td>															
							</tr>
							<?php } ?>
							</tbody>
							</table>																										
								</div>
								<div class="form-actions">
									<div class="row">
										<div class="col-md-offset-2 col-md-9">
											<button type="button" onclick="top.location='/admin/tasks'" class="btn grey-steel">戻る</button>
										</div>
									</div>
								</div>
							</div>
						</div>
			        </div>
			        <!-- /#page-wrapper -->
				</div>
			</div>
			<!-- END PAGE CONTENT-->



