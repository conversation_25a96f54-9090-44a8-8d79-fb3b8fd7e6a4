<style type="text/css">
.btn.default:active, .btn.default.active {
  background-image: none;
  background-color: #45b6af;
  color: #fff;
}
</style>
			<!-- BEGIN PAGE HEADER-->
			<!-- BEGIN PAGE HEAD -->
			<div class="page-head">
				<!-- BEGIN PAGE TITLE -->
				<div class="page-title">
					<h1><?php echo($_active_menu_name)?><small></small></h1>
				</div>
				<!-- END PAGE TITLE -->
			</div>
			<!-- END PAGE HEAD -->
			<!-- END PAGE HEADER-->
			<!-- BEGIN PAGE CONTENT-->
				<!-- Page Content -->
				<div id="page-wrapper">
					<?php 
						if (strlen($_active_menu) > 4) {
							$tab_menu = View::factory ('admin/menutab');
							echo($tab_menu);
						}
					?>
					<div class="edit-container">
					<div class="settings-container">
						<input type="hidden" name="context_id" id="context_id" value="<?php echo($post['context_id'])?>" />
						<table class="table table-striped table-bordered table-hover js-data-table">
						<thead>
						<tr>
							<th>
								コンテキスト名
							</th>
							<th>
								AIエンジン
							</th>
							<th>
									FAQ範囲
							</th>
							<th style="width: 150px;">
									編集操作
							</th>
						</tr>
						</thead>

						<tbody>
						<?php
						foreach ($results as $row) {
						?>
						<tr class="gradeX odd" role="row">
							<td>
									<?php echo($row->context_name)?>
							</td>
							<td>
								<?php echo($row->engine)?>
							</td>
							<td>
							<?php 
							$arr = explode(',', $row->intent_type_cd);
							foreach($arr as $cd) {
								if ($cd != '' && isset($class_types[$cd])) echo('<label class="btn default active">' .  $class_types[$cd] . '</label>');
							}
							?>
							</td>
							<td>
								<button type="button" class="btn yellow action" act="01" context_id="<?php echo($row->context_id)?>">編集</button><br/>
								<button type="button" class="btn red action" style="margin-top:5px;" act="02" context_id="<?php echo($row->context_id)?>">削除</button>
							</td>
						</tr>
						<?php } ?>
						</tbody>
						</table>
						<br/>
						<button type="button" class="btn green-meadow action" act="00"><i class="fa fa-file mr10"></i>新規</button>
						<?php if ($isEdit) { ?>
						<h4 class="form-section"></h4>
							<div class="form-body">
								<div class="form-group">
									<label class="control-label col-md-2">コンテキスト名</label>
									<div class="col-md-5">
										<input name="context_name" id="context_name" type="text" class="form-control" value="<?php echo($post['context_name'])?>">
									</div>
								</div>								
								<div class="form-group">
									<label class="control-label col-md-2">AIエンジン</label>
									<div class="col-md-5">
										<input name="engine" id="engine" type="text" class="form-control" value="<?php echo($post['engine'])?>">
									</div>
								</div>
								<div class="form-group">
									<label class="control-label col-md-2">AIエンジン</label>
									<div class="col-md-8">
										<?php
										$i = 0;
										$arr = explode(',', $post['intent_type_cd']);
										//if ($bot->class_type_cd == '') echo('なし');
										foreach($class_types as $type_cd=>$type_name) {
											if ($i!=0 && $i%10==0) echo('</div></br></br>');
											if ($i%10==0) {
												echo('<div class="btn-group" data-toggle="buttons">');
											}
											if (in_array($type_cd, $arr)) {
												echo('<label class="btn default active">');
												echo('<input name="classes[]" type="checkbox" checked="true" value="' . $type_cd . '" class="toggle">' . $type_name . '</label>');
											}
											else {
												echo('<label class="btn default">');
												echo('<input name="classes[]" type="checkbox" value="' . $type_cd . '" class="toggle">' . $type_name . '</label>');
											}
											$i++;
										}
										?>
									</div>
								</div>																
							</div>
							<div class="form-actions">
								<div class="row">
									<div class="col-md-offset-2 col-md-9">
										<button type="button" id="saveBaseButton" class="btn blue mr10">
										<i class="fa fa-save mr10"></i>保存</button>
										<button type="reset" class="btn gray">リセット</button>											
									</div>
								</div>
							</div>
							<?php } ?>
						</div>
						</div>
			        </div>
			        <!-- /#page-wrapper -->
			<!-- END PAGE CONTENT-->
