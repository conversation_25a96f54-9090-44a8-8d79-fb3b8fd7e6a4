<input type="hidden" name="no" id="no" value="" />
<div id="page-wrapper">
    <div class="portlet light">
		<div class="portlet box">  
            <div class="portlet-body">
				<h5><?php echo __('admin.uploadfiles.h5') ?></h5>
                <table class="table table-striped table-bordered table-hover" id="sample_2">
					<thead>
					<tr>
						<th><?php echo __('admin.common.label.title') ?></th>											
						<th><?php echo __('admin.common.label.type') ?></th>							
						<th>URL</th>
						<th style="width:120px;"><?php echo __('admin.common.label.operation') ?></th>
					</tr>
					</thead>
					<tbody>
						<?php
						$no = 0;
						foreach ($files as $f) {
							echo('<tr class="gradeX odd" role="row">');	
							echo('<td>');
							echo($f['title']);
							echo('</td>');
							echo('<td>');
							switch ($f['file_type_cd']) {
								case 'pdf':
									echo __('PDF');
									break;
								case 'img':
									echo __('admin.common.label.image');
									break;
								case 'mov':
									echo __('admin.common.label.video');
									break;
								case 'ttf':
									echo __('admin.common.label.font');
									break;
								default:
									echo('');
									break;
							}
							echo('</td>');
							echo('<td>');
							echo('<a href="' . $f['url'] . '" target="_blank">' .$f['url'] . '</a>');
							echo('</td>');
							echo('<td>');
							echo('<a href="javascript:void(0);" class="urlcopy icon-round-corners-small icon-background-pale-blue survey-space-all-around-2 flexbox-x-axis" data-clipboard-text="' . $f['url'] . '">');
							echo('<span class="font-standard font-family-v1">');
							echo('<img src="./../assets/admin/css/img/icon-copy.svg" width="12" height="12" class="surveys-details-icon-img">' . __('admin.common.button.copy_url'));
							echo('</span>');
							echo('</a>');
							echo('<a href="javascript:void(0);" no="' .$f['id'] . '" class="delete icon-round-corners-small icon-background-pale-blue survey-space-all-around-2 flexbox-x-axis" data-clipboard-text="' . $f['url'] . '">');
							echo('<span class="font-standard font-family-v1">');
							echo('<img src="./../assets/admin/css/img/icon-delete.svg" width="12" height="12" class="surveys-details-icon-img">' . __('admin.common.button.delete'));
							echo('</span>');
							echo('</a>');
							echo('</td>');
							echo('</tr>');
							$no++;
						 }?>
					</tbody>
				</table>

				<div class="form-group">
					<label class="control-label col-md-3" style="font-weight: bold;"><?php echo __('admin.common.label.upload_file') ?></label>
				</div>
				<div class="form-group">
					<label class="control-label col-md-2"><?php echo __('admin.common.label.title') ?></label>
					<div class="col-md-6">
					<input name="title" type="text" class="form-control" placeholder="">
					</div>
				</div>
				<div class="form-group">						
					<label class="control-label col-md-2"><?php echo __('admin.common.label.file') ?></label>
					<div class="col-md-6">
						<div class="talkappi-upload" data-name="image_base64" data-type="all" data-label="" data-url="" data-max-size="5"></div>
					</div>
				</div>
				<input type="hidden" id="upload_file_name" name="upload_file_name" value=""/>
				<div class="actions-container" style="margin: 60px 0 0 140px;">
					<button type="submit" id="saveBaseButton" class="btn-larger btn-blue"><?php echo __('admin.common.button.save') ?></button>
				</div>
			</div>
		</div>
		<!-- /#page-wrapper -->
	</div>
</div>
<!-- END PAGE CONTENT-->



