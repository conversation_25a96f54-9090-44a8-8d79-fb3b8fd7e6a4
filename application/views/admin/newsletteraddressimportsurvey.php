<link rel="stylesheet" type="text/css" href="/assets/admin/css/newsletter.css"/>
<input type="hidden" name="branches" id="branchDatas" value="[]" />
<style>
.content-container .content-wrapper {
  padding: 12px 6%;
  display:flex;
  flex-direction:column;
  gap:24px;
}
#filter-conditions span{
    text-decoration: underline;
    cursor: pointer;
}
#filter-conditions.disabled span {
    color: #A1A4AA;
    text-decoration: unset;
}
#filter-conditions .inquiry-branch {
    color: #245BD6;
}
.icon-undo {
  height: 12px;
  width: 12px;
}

.js-support_lang_cd {
  max-width: 200px;
}

</style>

<div class="content-container white">
  <div class="content-wrapper">
    <!-- 案件所属 -->
    <?php if($is_newsletter_project_enabled && $project_id && is_numeric($project_id) ) : ?>
      <div style="display:flex;gap:6px;">
        <span><?php echo __('admin.newsletterimport.label.project') ?></span>
        <span><?php echo $project_name ?></span>
      </div>
    <?php endif; ?>

    <!-- 言語選択 -->
    <div style="display:flex;flex-direction:column;gap:6px;">
      <div><?php echo __('admin.newsletterimport.label.select_language_survey') ?></div>
      <div class="talkappi-pulldown js-support_lang_cd" data-name="support_lang_cd" data-value='-' data-source='<?php echo json_encode($support_lang_cds, JSON_UNESCAPED_UNICODE) ?>'></div>
    </div>

    <!-- フォーム選択 -->
    <div style="display:flex;flex-direction:column;gap:6px;">
      <div><?php echo __('admin.newsletterimport.label.select_survey') ?></div>
      <select class="form-control select2me" id="survey_select" data-placeholder="Select..."></select>
    </div>

    <!-- 項目選択 -->
    <div id="entry-select" class="content-container light-gray hide" style="display:flex;flex-direction:column;gap:6px;">
      <div><?php echo __('admin.newsletterimport.label.select_entry') ?></div>
      <div style="display:flex;align-items:center;">
        <div style="flex: 0 0 150px;"><?php echo __('admin.common.label.mail.address') ?></div>
        <div style="width:100%; margin-right: 35px;" class="talkappi-pulldown js-mail_no" data-name="mail_no"></div>
      </div>
      <div style="display:flex;align-items:center;">
        <div style="flex: 0 0 150px;"><?php echo __('admin.common.label.name.human') ?></div>
        <div style="width:100%; margin-right: 35px;" class="talkappi-pulldown js-name_no" data-name="name_no"></div>
      </div>
      <div class="js-add-entry-container" style="display: flex; justify-content: center; align-items: center;">
        <div id="js-add-entry">
          <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12">
            <g fill="none" fill-rule="evenodd" stroke-linecap="round" stroke-linejoin="round">
              <g stroke="#245BD6" stroke-width="2">
                <g>
                  <path d="M6 10L6 2" transform="translate(-997 -453) translate(997 453) matrix(1 0 0 -1 0 12)"></path>
                  <path d="M6 10L6 2" transform="translate(-997 -453) translate(997 453) matrix(0 -1 -1 0 12 12)"></path>
                </g>
              </g>
            </g>
          </svg>
          <?php echo __('admin.newsletterimport.label.add_extend_entry') ?>
        </div>
      </div>
      
    </div>

    <div class="form-control flex-x-between disabled" id="filter-conditions" style="max-width:350px; display: none;">
      <span class="js-set-conditions"></span>
      <!-- only clear conditions(without apply filtering automatically, need to click preview button again) button for now-->
      <div class="inquiry-branch pointer js-reset-conditions"><?php echo __('admin.common.button.clear'); ?></div>
    </div>

    <div style="display:flex;">
      <button class="btn-smaller page btn-blue js-import" style="margin-left:0"><?php echo __('admin.newsletterimport.label.import_survey') ?></button>
    </div>

    <div style="display:flex;flex-direction:column;gap:6px;">
      <div class="result_data_count">
        <?php echo __('admin.common.label.total') ?><span></span>
        <?php echo __('admin.newsletterimport.label.pcount') ?></div>
      <div>
        <table class="table table-striped table-bordered table-hover js-data-table">
          <thead>
            <tr>
              <th><?php echo __('admin.common.label.mail.address') ?></th>
              <th><?php echo __('admin.common.label.name.human') ?></th>
              <th><?php echo __('admin.newsletterimport.label.tag') ?></th>
              <th style="width:40px!important"><?php echo __('admin.newsletterimport.button.exclude') ?></th>
            </tr>
          </thead>
          <tbody></tbody>
        </table>
      </div>
    </div>

    <div class="flex">
      <button class="action-button page btn-blue js-export-to" style="margin-left:0"><?php echo __('admin.newsletterimport.label.export') ?></button>
      <?php echo HTML::anchor($navi_parent_url, __('admin.common.button.return_to_list'), ['class' => 'action-button page btn-white js-action-back']) ?>
    </div>
  </div>
</div>

<script>
const _surveys_by_lang = '<?php echo $surveys_by_lang ?>';
const _result_datas = '<?php echo $result_datas ?>';
const _extends = '<?php echo $extends ?>';

const _project_id = <?php echo isset($project_id) && $project_id && is_numeric($project_id) ? $project_id : 'null'; ?>;
const _is_newsletter_project_enabled = <?php echo $is_newsletter_project_enabled ? 'true' : 'false'; ?>;
</script>