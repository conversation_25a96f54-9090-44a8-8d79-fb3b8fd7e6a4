<script>
    const _labels = <?php echo json_encode($labels,JSON_UNESCAPED_UNICODE)?>;
    const _selectedLabels = <?php echo json_encode($selected_labels,JSON_UNESCAPED_UNICODE)?>;
	const _labelcolor = <?php echo json_encode($labelcolor) ?>;
	const _selectDates = <?php echo json_encode($post['select_dates'],JSON_UNESCAPED_UNICODE)?>;
</script>
<input type="hidden" name="maximum_id" id="maximum_id" value="<?php echo $post['maximum_id'] ?>" />
<input type="hidden" name="order_id" id="order_id" value="" />
<input type="hidden" name="order_info" id="order_info" value="" />
<input type="hidden" id="message" value="<?php echo $message ?>" />
<input type="hidden" id="label_id" name="label_id" value="<?php echo $label_id ?>" />
<input type="hidden" id="field_setting" name="field_setting" value="" />
<input type="hidden" name="status_cd" id="status_cd" value="<?php echo $post['status_cd'] ?>" />
<input type="hidden" id="support_type_cd" name="support_type_cd" value="<?php echo $post['support_type_cd'] ?>" />
<style>
	.dropdown-menu {
		font-size: 12px;
	}
	.setting-item-label{
		font-weight: 500;
        text-align: right;
        width: 100px;
    }
	label {
		margin: 0;
	}
	.scrolling-wrapper {
		overflow-x: auto;
    overflow-y: hidden;
    height: 15px;
    margin-bottom: 5px;
    width: 100%;
    margin-bottom: -11px;
    margin-top: 8px;
    border-top: 1px solid #ddd;
	}

	.scrolling-wrapper div {
		height: 1px;
	}
	.content-container {
    overflow: hidden;
	}
	#data-table_wrapper {
		overflow: hidden;
	}
	.table-scrollable {
		max-height: calc(100vh - 160px);
    overflow: auto;
	}
	.js-data-table thead {
		font-size: 8px;
    position: sticky;
    left: 0;
    top: 0;
    right: 0;
    background: white;
    z-index: 10;
	}
	.table-bordered>thead>tr>th {
    border-bottom: 0.5px solid #dddddd !important;
	}
	.js-data-table tfoot {
		position: sticky;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 10;
    background: white;
	}
</style>

<?php echo $menu ?>	
<div class="content-container white border">
	<div class="col-md-2" style="display:none;">
		<?php echo Form::select('date_cond', ['reserve'=>'予約日','check'=>'利用期間'], $post['date_cond'], array('id'=>'status_cd','class'=>'form-control'))?>
	</div>
	<div class="setting-item flex" style="display: flex; flex-direction: column; gap: 12px;margin: 0 0 12px 0;">
		<div class="flexbox-x-axis" style="justify-content: flex-start; gap: 24px;">
			<!-- フォーム名 -->
			<label class="setting-item-label"><?php echo __('admin.maximums.stock_name') ?></label>
			<div style="line-height:24px;"><?php echo $maximum->name ?></div>
		</div>
		<?php
			// 掲載フォーム
			if (count($inquiry_dict) > 0) {
				echo('<div class="flexbox-x-axis" style="justify-content: flex-start; gap: 24px;">');
					echo('<label class="setting-item-label">' . __('admin.common.label.form') . ' (' . count($inquiry_dict) . ') </label>');
				if (count($inquiry_dict) > 1) {
					echo('<div class="talkappi-pulldown" style="min-width: 300px;" id="inquiry_id" data-name="inquiry_id" data-blank="1" data-blank-text="' . __('admin.common.label.all') . ' " data-value="' . $post['inquiry_id'] . '" data-source=\'' . json_encode($inquiry_dict, JSON_UNESCAPED_UNICODE) . '\'></div>');
				}
				else {
					echo('<div class="talkappi-pulldown" style="min-width: 300px;" id="inquiry_id" data-name="inquiry_id" data-value="' . $post['inquiry_id'] . '" data-source=\'' . json_encode($inquiry_dict, JSON_UNESCAPED_UNICODE) . '\'></div>');
				}
					echo(
						'<div style="position: relative;">
							<a href="javascript:void(0);" class="js-goto-form">' . __('admin.common.button.go_to_form') . '</a>
							<span class="icon-detail" style="margin: 0 0 0 0.5rem;" title="' . __('admin.inquiry.label.form_link_hint') . '"></span>
						</div>'
					);
				echo('</div>');
			}
		?>
		<div class="flexbox-x-axis" style="justify-content: flex-start; gap: 24px;">
			<div class="flexbox-x-axis" style="justify-content: flex-start; gap: 24px;">
				<!-- 状態 -->
				<label class="setting-item-label" style="min-width: fit-content;"><?php echo __('admin.common.label.status') ?></label>
				<div style="width:200px;">
					<select class="status-select form-control" multiple>
						<?php 
							foreach($status_array as $k=>$v) {
								echo('<option value="' . $k . '">' . $v .'</option>');
							}
						?>
					</select>
				</div>
			</div>
			<div class="flexbox-x-axis" style="justify-content: flex-start; gap: 24px; min-width:200px;">
				<!-- 対応ステータス -->
				<label class="setting-item-label" style="min-width: fit-content;"><?php echo __('admin.inquiryresult.label.response_status') ?></label>
				<div style="width:200px;">
					<select class="bs-select form-control" multiple>
						<?php 
							echo('<option value="00">' . __('admin.service.dropdown.no_status') .'</option>');
							if(is_array($buttons)){
								foreach($buttons as $k=>$v) {
									echo('<option value="' . $k . '">' . $v .'</option>');
								}
							}
						?>
					</select>
				</div>
			</div>
			<div class="flexbox-x-axis" style="justify-content: flex-start; gap: 24px; min-width:200px;">
				<!-- 分割 -->
				<label class="setting-item-label" style="min-width: fit-content;width:60px;"><?php echo __('admin.maximumorders.label.result_type') ?></label>
				<div class="talkappi-radio" data-name="result_type"
					data-value='<?php echo $post['result_type'] ?>'
					data-source='{"0":"<?php echo __('admin.maximumorders.label.split')?>", "1":"<?php echo __('admin.maximumorders.label.summary')?>"}'>
				</div>
			</div>			
		</div>
		<div class="flexbox-x-axis" style="justify-content: flex-start; gap: 24px;">
			<!-- 枠期間 -->
			<div class="flexbox-x-axis" style="gap: 24px;">
				<label class="setting-item-label" style="min-width: fit-content;"><?php echo __('admin.maximums.stock_period') ?></label>	
				<div class="talkappi-radio js-select-date-div" data-name="select_date_div"
					data-value='<?php echo ($post['select_date_div']) ?>'
					data-source='{"0":"<?php echo __('admin.maximumorders.label.today')?>", "1":"<?php echo __('admin.maximumorders.label.tomorrow')?>", "2":"<?php echo __('admin.maximumorders.label.this_month')?>", "3":"<?php echo __('admin.maximumorders.label.next_month')?>"}'>
				</div>
				<div style="width:210px;">	
					<div class="talkappi-datepicker-range">
						<input name="start_date" value="<?php echo ($post['start_date']) ?>"/><p>〜</p>
						<input name="end_date" value="<?php echo ($post['end_date']) ?>"/>
					</div>
				</div>
			</div>
		</div>
		<div class="flexbox-x-axis">
			<!-- 検索・出力項目設定・CSV・印刷 -->
			<label class="setting-item-label" style="min-width: fit-content;"></label>
			<div class="col-md-5 flex">
				<button class="btn-smaller btn-yellow js-block-ui js-search"><i class="fa fa-search mr10"></i><?php echo __('admin.common.button.search') ?></button>
				<div class="btn-smaller btn-blue js-field-setting"><?php echo __('admin.maximums.csv_field_setting') ?></div>
				<button type="button" class="btn-smaller btn-white exportCsv" data-tableId="data-table" data-exclude='[]' data-title="<?php echo __('admin.maximums.stock_usage') ?>"><span class="icon-export"></span><?php echo __('admin.common.button.csv_export') ?></button>
				<button type="button" class="btn-smaller btn-white printCsv" data-tableId="data-table" data-exclude='[]' data-title="<?php echo __('admin.maximums.stock_usage') ?>"><span class="icon-export"></span><?php echo __('admin.common.button.csv_print') ?></button>
			</div>
		</div>
	</div>

	<table class="table table-striped table-bordered table-hover js-data-table" <?php if ($post['result_type'] == '1') echo('style="table-layout:fixed;word-wrap:break-word;"') ?> id="data-table">
	<thead style="font-size:8px;">
	<tr>
		<?php 
		$has_not_default = false;
		$cols = [];
		$cols_width = [];
		foreach($maximum_labels as $maximum_label) {
			$code = intval($maximum_label['no']);
			switch ($code) {
				case $code > 0:
					if ($post['result_type'] == '1') break;
					$has_not_default = true;
					$cols[] = $maximum_label['label'];
					$cols_width[] = '200';
					break;
				case -1:
					$cols[] = __('admin.maximums.reservation_date');
					$cols_width[] = '100';
					break;
				case -2:
					$cols[] = __('admin.maximums.reception_id');
					$cols_width[] = '60';
					break;
				case -3:
					$cols[] = __('admin.maximums.stock_date');
					$cols_width[] = '80';
					break;
				case -4:
					$cols[] = __('admin.maximums.stock_time');
					$cols_width[] = '80';
					break;
				case -5:
					$cols[] = __('admin.maximums.number_slots_groups');
					$cols_width[] = '40';
					break;
				case -6:
					$cols[] = __('admin.common.label.status');
					$cols_width[] = '60';
					break;
				case -7:
					$cols[] = __('admin.common.label.operation');
					$cols_width[] = '160';
					break;
				case -8:
					if ($post['result_type'] == '1') break;
					$cols[] = __('admin.maximums.reservation_lead');
					$cols_width[] = '100';
					break;
				case -9:
					$cols[] = __('admin.maximums.total_price');
					$cols_width[] = '40';
					break;
				case -10:
					$cols[] = __('admin.inquiryresult.label.response_status');
					$cols_width[] = '80';
					break;
				case -11:
					$cols[] = __('admin.inquiryresult.label.response_history');
					$cols_width[] = '200';
					break;
				case -12:
					if ($post['result_type'] == '1') break;
					$cols[] = __('admin.maximums.stock_range_start');
					$cols_width[] = '60';
					break;
				case -13:
					if ($post['result_type'] == '1') break;
					$cols[] = __('admin.maximums.stock_range_end');
					$cols_width[] = '60';
					break;
				case -14:
					if ($post['result_type'] == '1') break;
					if ($maximum->extra_data != NULL) $extra_data = json_decode($maximum->extra_data, true);
					if (isset($extra_data['category']) && count($extra_data['category']) > 0) {
						$cols[] = __('admin.maximums.breakdown');
						foreach ($extra_data['category'] as $v) {
							$cols[] = $v['title']['ja'] . '<br/>数量';
							$cols_width[] = '80';
							$cols[] = $v['title']['ja'] . '<br/>料金';
							$cols_width[] = '80';
						}
					}
					break;
				default:
					break;
			}
		} 
		if (count($maximum_labels) > 0 && !$has_not_default && $post['result_type'] == 0)  {
			$result_array = [];
			$result_no_array = [];
			$c = 1;
			foreach($entry_array as $k=>$v) {
				$cols[] = $v;
				if (array_key_exists($v, $result_array)) {
					$result_array[$v . $c++] = '';
				}
				else {
					$result_array[$v] = '';
				}
				$result_no_array[$k] = '';
			}
		}
		for($i=0; $i<count($cols); $i++) {
			if (!isset($cols_width[$i]) || $cols_width[$i] == '' || $post['result_type'] == '0') {
				echo '<th>' . $cols[$i] . '</th>';
			}
			else {
				echo '<th style="width:' . $cols_width[$i] . 'px">' . $cols[$i] . '</th>';
			}
		}
		if ($post['result_type'] == '1') echo '<th>' . __('admin.inquiryresult.label.result') . '</th>';
		?>
	</tr>
	</thead>
	<tbody>
	<?php
		$sum_total_amount = 0;
		foreach ($orders as $order) {
			$result_data = json_decode($order['result_data'], true);
			if (isset($result_data['error_code'])) continue;
			if (count($maximum_labels) > 0) {
				$order_data = json_decode($order['order_data'], true);
				$order_array = json_decode($order["order_info"], true);
				$value_dict = [];
				foreach($order_array as $k=>$v) {
					$value_dict[$v['no']] = $v['value'];
				}
				echo '<tr class="gradeX odd" role="row">';
			} else {
				continue;
			}
			foreach($maximum_labels as $label) {
				$no = intval($label['no']);
				if ($no > 0) {
					if ($post['result_type'] == '0') {
						if (isset($inquiry_label_dict[$order['inquiry_id']][$order['lang_cd']][$no]) && 
							isset($value_dict[$inquiry_label_dict[$order['inquiry_id']][$order['lang_cd']][$no]])
						) {
							echo '<td>';
								echo $value_dict[$inquiry_label_dict[$order['inquiry_id']][$order['lang_cd']][$no]];
							echo '</td>';
						}
						else {
							echo('<td></td>');
						}
					}
				} else {
					if ($no == -1) {
						// 予約日
						echo '<td style="white-space: nowrap;">' . ($order['order_date'] != NULL ? substr($order['order_date'], 2, 14) : '');
						echo '<br/>';
						$member_name = '';
						if ($order['name'] != NULL) { 
							$member_name = $order['name'];
						} else {
							if ($order['sns_type_cd'] == 'wb') {
								$member_name = __('admin.common.label.web_user');
							}
							else {
								if ($order['last_name'] . $order['first_name'] !='') {
									$member_name = $order['last_name'] . $order['first_name'];
								}
							}
						}
						echo(nl2br($member_name));
						if ($_user->role_cd == '99' || $_user->role_cd == '01' || $_user->role_cd == '07' || $_user->role_cd == '09') {
							echo ('<br><label class="badge badge-info js-memberinfo" style="cursor:pointer;" data-member-id="' . $order['member_id'] .'">' . __('admin.common.label.journey')  . '</label>');
						}
						echo '</td>';
					} else if ($no == -2) {
						// 受付ID
						echo '<td>';
						if (!isset($order['result_cd']) || $order['result_cd'] == null) {
							$result_cd = $order['link_id'];
						}
						else {
							$result_cd = $order['result_cd'];
						}
						if ($_bot_id == 401005 || $_bot_id == 401006) {
							echo('<a href="/admin/maximumorder?id=' . $order['order_id'] .'">' . $result_cd . '</a>');
						}
						else {
							echo($result_cd);
						}
						// 受付詳細へ
						if (isset($result_cd)) {
							echo '<br/><a href="/admininquiry/inquiryresultdetail?id=' . $result_cd . '" class="btn round light-blue" style="margin: 5px 0;">' . __('admin.inquiryresult.label.resultdetail') . '</a>';
						}
						echo '</td>';
					} else if ($no == -3) {
						// 枠（日付）
						echo '<td style="white-space: nowrap;">';
						if($maximum->span === '1d' || $maximum->span === '1h') {
							echo $order['day'];
						} else {
							echo '-';
						}
						echo '</td>';
					} else if ($no == -4) {
						// 枠（時間帯）
						echo '<td style="white-space: nowrap;">';
						if($maximum->span === '1h') {
							$time_arr = explode(',', $order['time']);
							foreach($time_arr as $t) {
								echo $t . '<br>';
							}
						} else {
							echo '-';
						}
						echo '</td>';
					} else if ($no == -5) {
						// 枠数
						echo '<td style="text-align:center;">';
						echo '<span style="margin-left:5px;font-weight:bold">';
						echo $order['num']== NULL ? $order['num2'] : $order['num'];
						echo '</span>';
						echo '</td>';
					} else if ($no == -6) {
						// 状態
						echo '<td style="white-space: nowrap;">';
						if ($order['order_status_cd'] == '04') $order['order_status_cd'] = '01';
						if ($order['order_status_cd'] == '05') $order['order_status_cd'] = '02';
						if ($order['order_status_cd'] == '03') echo('<span style="color:red;font-weight:bold;">');
						if ($order['order_status_cd'] == '01') echo('<span style="color:blue;">');
						if ($order['upd_user'] === '0') echo('自動');
						echo($codes['25'][$order['order_status_cd']]);
						if ($order['order_status_cd'] == '03' || $order['order_status_cd'] == '01') {
							echo('</span>');
						}
						if ($order['order_status_cd'] == '03' || $order['order_status_cd'] == '02') {
							echo('<br/><span style="font-size:5px;">' . substr($order['upd_time'], 5, 11) . '<br/>' . ((isset($order['user_name']) && $order['user_name'] != NULL) ? $order['user_name'] : '') . '</span>');
						}
						echo '</td>';
					} else if ($no == -7) {
						// 操作
						echo '<td>';
							echo '<div style="text-align: center;display: flex; flex-direction:column; justify-content: center; align-items: center; gap: 5px">';
								if ($order['order_status_cd'] == '00' || $order['order_status_cd'] == '01' || $order['order_status_cd'] == '02') {
									echo '<div class="btn round image delete js-cancel" data-order-id="' . $order['order_id'] . '">' . __('admin.common.button.cancel') . '</div>';
								}
								if ($order['order_status_cd'] == '01' || $order['order_status_cd'] == '02') {
									echo '<div class="btn round js-confirm-mail" data-order-id="' . $order['order_id'] . '">' . __('admin.maximums.resend_reservation_email') . '</div>';
								}
								if ($order['order_status_cd'] == '03') {
									echo '<div class="btn round js-cancel-mail" data-order-id="' . $order['order_id'] . '">' . __('admin.maximums.resend_cancel_email') . '</div>';
								}
								if ($order['order_status_cd'] == '01' || $order['order_status_cd'] == '02') {
									echo('<div class="btn round light-yellow js-inquiry-modify" data-result-id="' . $order['link_id'] . '">' . __('admin.common.label.change_by_admin') . '</div><br/>');
								}
							echo '</div>';
						echo '</td>';
					} else if ($no == -8) {
						// 予約導線
						if ($post['result_type'] == '0') {
							echo '<td style="white-space: nowrap;">';
								echo(nl2br($order['bot_name']));
								if (array_key_exists('link_key_name', $order)) echo("(" . $order['link_key_name'] . ')');
							echo '</td>';
						}
					} else if ($no == -9) {
						// 金額
						echo '<td style="text-align:right;">';
						if ($has_amount) {
							if (isset($order_data['items'])) {
								$order_amount = 0;
								foreach($order_data['items'] as $item) {
									if (isset($item['price'])) {
										$num = isset($item['num'])?$item['num']:1;
										if (isset($item['discount_price'])) {
											$order_amount += ($item['price'] - $item['discount_price']) * $num;
										}
										else {
											$order_amount += $item['price'] * $num;
										}
									}
								}
								$sum_total_amount += $order_amount;
								echo(number_format($order_amount));
							}
							else if (isset($order_data['price'])) {
								$num = isset($order_data['num'])?$order_data['num']:1;
								$order_amount = $order_data['price'] * $num;
								$sum_total_amount += $order_amount;
								echo(number_format($order_amount));
							}
						} else {
							echo '-';
						}
						echo '</td>';
					} else if ($no == -10) {
						// 対応ステータス
						echo '<td style="white-space: nowrap;">';
							echo '<div style="text-align: center;display: flex; flex-direction:column; justify-content: center; align-items: center; gap: 5px">';
								$attr = '';
								foreach($buttons as $key=>$value) {
									if ($key == '04' && $result['status_cd'] == '03') continue;
									if ($key == '04' && $result['status_cd'] != '00' && $pay_vender == 'talkappi' && date('Y-m-d', strtotime($result['end_time'] . ' +365 day')) < date('Y-m-d')) {
										$attr = ' data-cancel="overtime" ';
									}
									$style = "";
									foreach ($order['support_info'] as $support_info_key => $support_info) {
										if($support_info['support_type_cd'] !== ""){
											if ($support_info['support_type_cd'] == $key) {
												$style = 'style="margin-bottom:5px;background-color: ' . $labelcolor[$support_info['support_type_cd']] . '; color: #FFF;"';
											}
											else {
												$style='style="margin-bottom:5px;"';
											}
										}
									}
									echo('<span ' . $attr . ' class="btn round js-support" '. $style . ' support_type_cd="' .  $key . '" result_id="' . $order['link_id'] . '">' . $value . '</span>');
									$attr = '';
								}
							echo '</div>';
						echo '</td>';
					} else if ($no == -11) {
						// 対応履歴
						echo '<td>';
							echo '<div class="btn round image add js-memo" result_id="' . $order['link_id'] .'">' . __('admin.inquiryresult.label.add_memo') . '</div>';
							echo '<div class="js-td-memo">';
									foreach($order['support_info'] as $support) {
										if ($support['support_type_cd'] != '') {
											if (isset($buttons[$support['support_type_cd']])) {
												$support_label = '<span class="label" style="color:#FFF;background-color:' . $labelcolor[$support['support_type_cd']] . '">' . $buttons[$support['support_type_cd']] . '</span><br/>';
											}
											else {
												$support_label = '';
											}
											$close = '';
										}
										else {
											$support_label = '';
											$close = '<div class="icon-cancel-small js-memo-delete" no="' . $support['no'] . '" style="margin: 0 0 0 auto;"></div>';
										}
										$memo = '';
										if ($support['memo'] != null) $memo = nl2br($support['memo']);
										$comment = '<div style="width: calc(100% - 20px);font-size:11px;">'. date('Y/m/d H:i', strtotime($support['upd_time']))  . ' ' . $order['support_info'][1]['name'] . $support_label . '<br/>' . $memo .'</div>';
										echo('<div class="small-table-pannel" style="display: flex;padding:8px;width:200px">' . $comment . $close . '</div>');
									}
							echo '</div>';
						echo '</td>';
					} else if ($no == -12) {
						// 利用開始
						if ($post['result_type'] == '0') {
							echo '<td style="white-space: nowrap;">';
							echo isset($order_data['range']['start']) ? $order_data['range']['start'] : '-';
							echo '</td>';
						}
					} else if ($no == -13) {
						// 利用終了
						if ($post['result_type'] == '0') {
							echo '<td style="white-space: nowrap;">';
							echo isset($order_data['range']['end']) ? $order_data['range']['end'] : '-';
							echo '</td>';
						}
					} else if ($no == -14) {
						// 内訳
						if ($post['result_type'] == '0') {
							if ($maximum->extra_data != NULL) $extra_data = json_decode($maximum->extra_data, true);
							if (isset($extra_data['category']) && count($extra_data['category']) > 0) {
								$category_price = $extra_data['category'];
								echo '<td>' . trim($order['detail'], '\'"') . '</td>';
								$category_result = '';
								if ($order['category'] != NULL) {
									$category_result = json_decode($order['category'], true);
								}
								if ($category_result === '' && $order['choice'] != NULL) {
									$category_result = json_decode($order['choice'], true);
								}
								foreach($category_price as $v) {
									$fit = false;
									if (is_array($category_result)) {
										foreach($category_result as $cr) {
											$price = intval($cr['price']);
											$item_total = isset($cr['item_total']) ? $cr['item_total'] : intval($cr['num']) * $price;
											if (isset($cr['price_id'])) {
												if ($cr['price_id'] == $v['id']) {
													echo('<td style="text-align:right;">' . $cr['num'] . '</td>');
													echo('<td style="text-align:right;">' . $item_total . '</td>');
													$fit = true;
													break;
												}
											}
											else {
												echo('<td style="text-align:right;">' . $cr['num'] . '</td>');
												echo('<td style="text-align:right;">' . $item_total . '</td>');
												$fit = true;
												break;
											}
										}
									}
									if ($fit == false) {
										echo('<td></td><td></td>');
									}
								}
							} else {

							}
						}
					}
				}
			}
			if (count($maximum_labels) > 0 && !$has_not_default && $post['result_type'] == '0') {
				$record_array = $result_array;
				if (is_array($order_array)) {
					$first = true;
					foreach($order_array as $k=>$v) {
						if (array_key_exists('no', $v)) {
							if ($first) {
								$record_array = $result_no_array;
								$first = false;
							}
							if (array_key_exists($v['no'], $record_array)) $record_array[$v['no']] = $v['value'];
						}
						else {
							$record_array[$v['title']] = $v['value'];
						}
					}
				}
				echo('<td>' . implode('</td><td>', $record_array) . '</td>');
			}
			if ($post['result_type'] == '1') {
				$entry_results = '<p style="font-weight:bold">' . $inquiry_dict[$order['inquiry_id']] . '</p>';
				$pp = 1;
				foreach($order_array as $k=>$v) {
					$entry_results .= ($pp++) . '.' . $v['value'] . '<br>';
				}
				echo '<td>' . nl2br($entry_results) . '</td>';
			}
			if (count($maximum_labels) > 0) {
				echo '</tr>';
			}
		}	
	?>
	</tbody>
	<tfoot>
		<?php
			$sum_maximum_order = 0;
			$sum_maximum_order2 = 0;
			foreach ($orders as $order) {
				$num = ($order["num"] == '')?$order["num2"]:$order["num"];
				$sum_maximum_order = $sum_maximum_order + $num;
			}
			echo '<tr class="gradeX odd" role="row" style="text-align:center;font-weight:bold;">';
			foreach ($maximum_labels as $key => $maximum_label) {
				$code = intval($maximum_label['no']);
				if ($key == 0) {
					echo '<td>' . __('admin.common.label.total') . '</td>';
				} else {
					if ($code == -5) {
						echo '<td>' . $sum_maximum_order . '</td>';
					} else if ($code == -9) {
						echo '<td>' . number_format($sum_total_amount) . '</td>';
					} else if ($code == -14 && $post['result_type'] == '0') {
						if ($maximum->extra_data != NULL) $extra_data = json_decode($maximum->extra_data, true);
						if (isset($extra_data['category']) && count($extra_data['category']) > 0) {
							echo '<td></td>';
							foreach ($extra_data['category'] as $data) {
								echo '<td></td><td></td>';
							}
						}
					} else if (($code == -8 || $code == -12 || $code == -13 || $code > 0) && $post['result_type'] == '1') {
					} else {
						echo '<td></td>';
					}
				}
			}
			if (count($maximum_labels) > 0 && !$has_not_default && $post['result_type'] == '0') {
				for($i=0; $i<count($entry_array); $i++) {
					echo('<td></td>');
				}
			}
			echo '</tr>';
		?>
	</tfoot>
	</table>   				
</div>
<?php echo($memo_box);?>