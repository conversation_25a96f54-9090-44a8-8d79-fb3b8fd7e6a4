<script src="/assets/admin/newsletter-api.js"></script>
<link rel="stylesheet" type="text/css" href="/assets/admin/css/newsletter.css"/>

<style>
    .icon-add {
        background: url("/assets/admin/css/img/icon-add-white.svg") no-repeat;
        height: 12px;
        width: 12px;
    }
    .ico-success {
        background: url("/assets/admin/css/img/icon-status-success.svg") no-repeat;
        height: 14px;
        width: 14px;
    }
    .ico-limited {
        background: url("/assets/admin/css/img/icon-status-limited.svg") no-repeat;
        height: 14px;
        width: 14px;
    }
    .ico-fail {
        background: url("/assets/admin/css/img/icon-status-fail.svg") no-repeat;
        height: 14px;
        width: 14px;
    }
    .order-display {
        display: flex;
        gap: 12px;
    }
    .order_label {
        flex: none;

    }
    .task-name {
        display: flex;
    }
    .task-title {
        margin-bottom: 8px;
		cursor: pointer;
    }

    .color_label_status_border_diff {
        border: 1px solid #E3E5E8;
    }

    td.negative {
        color: #E53361;
    }
    td.pct {
        font-family: roboto;
        font-weight: 600;
        font-size: 11px;
    }
</style>

<div class="content-container light-gray">
    <div class="form-group">
        <label class="control-label col-md-1"><?php echo __('admin.common.label.narrowdown') ?></label>

        <!-- 案件 -->
        <?php if ($is_newsletter_project_enabled): ?>
            <div class="col-md-2">
            <?php echo Form::select('project', array_column($projects, 'project_name', 'project_id'), $project_id, array('id'=>'filter_project','class'=>'form-control project',
            'style' => 'background: #FFFFFF;'));
            ?>   
            </div>
        <?php endif; ?>
        
        <div class="col-md-2">
        <?php echo Form::select('status', $status_set, $filter_status, array('id'=>'filter_status','class'=>'form-control task_status',
            'style' => 'background: #FFFFFF;'))
        ?>   
        </div>

        <?php if (!$is_newsletter_project_enabled): ?>
            <div class="col-md-2">
            <?php echo Form::select('upd_user', $user_names, $filter_upd_user, array('id'=>'filter_upd_user','class'=>'form-control upd_user',
                'style' => 'background: #FFFFFF;'))
            ?>
            </div>
        <?php endif; ?>
    
        <div class="col-md-3">
        <div id="filter_date_range" class="talkappi-datepicker-range" data-value='<?php echo json_encode($filter_date_range) ?>'>
        </div>
        </div>
        
    </div>
</div>



<div class="content-container">
    
    <div class="flex-x-between">
        <div></div>
        <span id="newtask" class="btn-smaller btn-blue js-new-survey">
            <span class="icon-add-white"></span>
            <?php echo __('admin.common.button.create_new') ?>
        </span>
    </div>

</div>

<!--  hidden -->
<div class="content-container white" >
   

    <table id="tasks" class="table table-striped table-bordered dataTable table-hover js-data-table0">

        <!-- custom header -->

        <thead>
            <tr>
                <!-- table heads -->
                <th><?php echo __('admin.send.task.label.name') ?></th>
                <th><?php echo __('admin.send.task.label.address') ?></th>
                <th><?php echo __('admin.send.task.label.last_update') ?></th>
                <th><?php echo __('admin.send.task.th.delivery_rate') ?></th>
                <th><?php echo __('admin.send.task.th.open_rate') ?></th>
                <th><?php echo __('admin.send.task.th.click_rate') ?></th>
                <th><?php echo __('admin.send.task.th.bounce_rate') ?></th>
                <th><?php echo __('admin.send.task.th.unsubscribe_rate') ?></th>
                <th><?php echo __('admin.common.label.operation') ?></th>

            </tr>
        </thead>
        <!-- table grids -->
        <tbody>
            <!-- Cell contents read from data -->
            <!-- PHP loop echo BEGIN -->
            <?php
                $index = 0;
                foreach($tasks as $task) {
                    $tr_attrs = ['id' => $task->mail_task_id, 'class' => 'gradeX odd', 'role' => 'row'];
                    $mail_task_data = json_decode($task->mail_task_data);
                    $task_name = $task->mail_task_name;
                    if ($task_name == NULL || $task_name == '') {
                        if ( isset($task->title) && ! empty($task->title) ) {
                            $task_name = '[件名]'.$task->title;
                        } else {
                            $task_name = '[タスク#'.$task->mail_task_id.']';
                            if ($task->status == 0){
                                $task_name = '[タスク下書き #'.$task->mail_task_id.']';
                            }
                        }
                    }

                    
                    $display_status = $task->status;
                    $css_border = '';
                    if ( isset($status_settings[$display_status]['border']) ) {    // && $status_settings[$task->status]['border']
                        $css_border = ' color_label_status_border_diff';
                    }

                    $div_status_icon = '';
                    $css_w_ico_diff = '';
                    if ( isset($status_settings[$display_status]['ico']) ) {    // && $status_settings[$task->status]['border']
                        $div_status_icon = '<span class="ico-'.$status_settings[$display_status]['ico'].'"></span>';
                        $css_w_ico_diff = 'padding: 1px 3px 1px 6px;gap: 3px;';
                    }

                    $status_css_style = 'background: '.$status_settings[$display_status]['color'].';'.$css_w_ico_diff;

                    $task_name_html = $task_name;
                    //  using kohana HTML::anchor
                    if ($is_newsletter_project_enabled) {
                        $href_edit = '/'.$_path.'/newsletternewtask?id='.$task->mail_task_id.'&proj='.$project_id;
                    } else {
                        $href_edit = '/'.$_path.'/newsletternewtask?id='.$task->mail_task_id;
                    }
                    
                    // change to 確認check/review button if not editable
                    $btn_edit_html = HTML::anchor($href_edit, '<span class="btn round image review" style="margin-bottom: 4px;">'.__('admin.common.button.review').'</span>', ['class' => 'link-animate']);


                    // $css_result_disabled = '';
                    $btn_result_html = '<span class="btn round image result disabled" style="margin-bottom: 4px;">'.__('admin.common.label.result').'</span>';
                    $btn_report_html = '<span class="btn round image report disabled" style="margin-bottom: 4px;">'.__('admin.common.button.report').'</span>';
                    $btn_delete_html = '<span class="btn round image delete disabled" style="margin-bottom: 4px;">'.__('admin.common.button.delete').'</span>';
                    $href_result = '/'.$_path.'/newslettertaskresult?id='.$task->mail_task_id;
                    $href_report = '/'.$_path.'/newslettertaskreport?id='.$task->mail_task_id;
                    $ico_dashboard = 'icon-navi-dashboard-grey';
                    $ico_edit = 'icon-edit';
                    $mail_task_data = json_decode($task->mail_task_data);
                    $report = false;
                    if ($task->status > 2) {    //  3,4,5   完了
                        // $css_edit_disabled = ' disabled';
                        $btn_result_html = HTML::anchor($href_result, '<span class="btn round image result" style="margin-bottom: 4px;">'.__('admin.common.label.result').'</span>', ['class' => 'link-animate']);
                        $task_name_html = HTML::anchor($href_result, $task_name, ['class' => 'link-animate']);
                        if ( isset($mail_task_data->is_track_event) && $mail_task_data->is_track_event != NULL && $mail_task_data->is_track_event == 1 ){
                            // report enable
                            $btn_report_html = HTML::anchor($href_report, '<span class="btn round image report" style="margin-bottom: 4px;">'.__('admin.common.button.report').'</span>', ['class' => 'link-animate']);
                            $task_name_html = HTML::anchor($href_report, $task_name, ['class' => 'link-animate']);
                            $report = true;
                        } 
                        // else echo '';
                        $ico_edit = 'icon-edit-grey';
                        $ico_dashboard = 'icon-navi-dashboard-blue';
                    } else if ($task->status == 2){
                        // $css_edit_disabled = ' disabled';
                        // $css_result_disabled = ' disabled';
                        $ico_edit = 'icon-edit-grey';
                    } else {    // 0,1  //  新規or下書き
                        $btn_edit_html = HTML::anchor($href_edit, '<span class="btn round image edit" style="margin-bottom: 4px;">'.__('admin.common.button.edit').'</span>', ['class' => 'link-animate']);
                        $btn_delete_html = '<span class="btn round image delete" style="margin-bottom: 4px;">'.__('admin.common.button.delete').'</span>';
                        $task_name_html = HTML::anchor($href_edit, $task_name, ['class' => 'link-animate']);
                        // $css_result_disabled = ' disabled';
                    }

                    if ($is_newsletter_project_enabled) {
                        $href_copy = '/'.$_path.'/newsletternewtask?cp_id='.$task->mail_task_id.'&proj='.$project_id;
                    } else {
                        $href_copy = '/'.$_path.'/newsletternewtask?cp_id='.$task->mail_task_id;
                    }
                    $btn_copy_html = HTML::anchor($href_copy, '<span class="btn round image copy" style="margin-bottom: 4px;">'.__('admin.common.label.copy').'</span>');

                    $task_member_count = 0;
                    $member_count_txt = '';
                    $tag_member_count_text = '';
                    if ( isset($task_member_counts[$task->mail_task_id]) ) {
                        $task_member_count = $task_member_counts[$task->mail_task_id];

                        $excluded_member_count = 0;
                        $member_count_txt = str_replace( "{target_count}", ' <b class="selected_count">'.$task_member_count.'</b> ', __('admin.send.task.count.context') );
                        if ( isset($mail_task_data->excluded_count) && $mail_task_data->excluded_count != NULL ){
                            $excluded_member_count = $mail_task_data->excluded_count;
                            $member_count_txt .= str_replace( "{exclude_count}", '<span class="excluded_count">'.$excluded_member_count.'</span>', __('admin.send.task.count.context.exclude') );
                        }

                        if ( $excluded_member_count == 0 )
                            $tag_member_count_text = __('admin.send.task.label.all_addr');  // 全ての連絡先
                        if ( isset($tag_member_counts[$task->mail_task_id]) ) {
                            $tag_member_count = $tag_member_counts[$task->mail_task_id];
                            if ( is_array($tag_member_count) && count($tag_member_count) > 0 ) {
                                $tag_member_count_text = '';
                                foreach ( $tag_member_count as $tag_no=>$tag_info ) {
                                    $tag_text = $tag_info['name'].'('. $tag_info['count'].')';
                                    if ( $tag_member_count_text != '' ) {
                                        $tag_text = '; '.$tag_text;
                                    }
                                    $tag_member_count_text .= $tag_text;
                                }
                            }
                        }
                    }
                    
                    if ($task->status < 2) {
                        $date_title = __('admin.send.task.label.plan').': ';
                        if ( $task->schedule_time == null ) {
                            $date_content = '-';
                        } else {
                            $date_content = $task->schedule_time;
                        }
                    } else {
                        $date_title = __('admin.send.label.date').': ';
                        $date_content = $task->upd_time;
                    }

                    $delivery_rate = '-';
                    $open_rate = '-';
                    $click_rate = '-';
                    $bounce_rate = '-';
                    $unsubscribe_rate = '-';
                    $unsub_color_class = '';

                    if ( ($task->status == 3 || $task->status == 4) ) { //追跡あり、算出可能
                        if ( $report && isset($task_report_member_counts[$task->mail_task_id]) ) { 
                            $task_event_counts = $task_report_member_counts[$task->mail_task_id];
                            //delivery rate
                            if ( isset($task_event_counts['Delivery']) ) {
                                $delivery_rate = number_format(100 * $task_event_counts['Delivery']/$task_member_count, 1, '.', '').'%';
                                if ( $task_event_counts['Delivery'] > 0 ) {
                                    if (isset($task_event_counts['Open'])) {
                                        // open rate
                                        if ($task_event_counts['Open'] > 0 && $task_event_counts['Open'] > $task_event_counts['Delivery']) {
                                            // handle exceptional data
                                            $open_rate = '100%';
                                        } else {
                                            $open_rate = number_format(100 * $task_event_counts['Open']/$task_member_count, 1, '.', '').'%';
                                        }
                                        
                                        if ( $task_event_counts['Open'] > 0 && isset($task_event_counts['Click']) ) {
                                            if (isset($task_event_counts['Click'])) {
                                                // click rate
                                                if ($task_event_counts['Click'] > 0 && $task_event_counts['Click'] > $task_event_counts['Open']) {
                                                    // 2024.10.22 handle exceptional data
                                                    $click_rate = '100%';
                                                } else {
                                                    $click_rate = number_format(100 * $task_event_counts['Click']/$task_event_counts['Open'], 1, '.', '').'%';
                                                }
                                            }
                                            // unsubscribe rate
                                            if ( isset($mail_task_data->unsubscribe_enable) && $mail_task_data->unsubscribe_enable != NULL && $mail_task_data->unsubscribe_enable == 1 ){
                                                if ($unsub_counts && key_exists($task->mail_task_id, $unsub_counts)) {
                                                    $unsub_count = $unsub_counts[$task->mail_task_id];
                                                    if ($unsub_count > $task_event_counts['Open']) {
                                                        // handle exceptional data
                                                        $unsubscribe_rate = '100%';
                                                    } else {
                                                        $unsubscribe_rate = number_format(100 * $unsub_count/$task_event_counts['Open'], 1, '.', '').'%';
                                                    }
                                                    if ($unsub_count > 0){
                                                        $unsub_color_class = 'negative';
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    
                                } 
                            }
                            if ( isset($task_event_counts['Bounce']) ) {
                                // bounce rate
                                $bounce_rate = number_format(100 * $task_event_counts['Bounce']/$task_member_count, 1, '.', '').'%';
                            }
                        }
                    }

                    echo('
            <tr '.HTML::attributes($tr_attrs).'>
                <td>
                    <div style="display: flex; justify-content: space-between;gap: 10px; ">
                    <div style="width: 300px;">
                        <div class="task-title">'.$task_name_html.'</div>
                        <div>'.$date_title.$date_content.'</div>
                    </div>
                    <div>
                        <div class="color_label_status'.$css_border.'" style="'.$status_css_style.'">'.$status_settings[$display_status]['txt'].''.$div_status_icon.'</div>

                    </div>
                    </div>
                </td>
                <td>
                    <!-- $sending_target_count -->
                    <div>'.$member_count_txt.'</div>
                    <!-- $sending_target_group_array -->
                    <div style="word-break: keep-all;">'.$tag_member_count_text.'</div>
                </td>
                <!-- -->
                <td>
                    <div>'.date("Y-m-d H:i", strtotime($task->upd_time)).'</div>
                </td>
                <td class="pct">
                    <b>'.$delivery_rate.'</b>
                </td>
                <td class="pct">
                    <b>'.$open_rate.'</b>
                </td>
                <td class="pct">
                    <b>'.$click_rate.'</b>
                </td>
                <td class="pct">
                    <b>'.$bounce_rate.'</b>
                </td>
                <td class="pct '.$unsub_color_class.'">
                    <b>'.$unsubscribe_rate.'</b>
                </td>
                <td>'
                    .$btn_edit_html.$btn_copy_html.$btn_result_html.$btn_report_html.$btn_delete_html.
                '</td>
            </tr>
                    ');     
                    $index ++;               
                }
            ?>
        <!-- PHP loop echo END -->
        </tbody>
    </table>
</div>

<script>
const _project_id = <?php echo isset($project_id) && $project_id && is_numeric($project_id) ? $project_id : 0; ?>;
const _is_newsletter_project_enabled = <?php echo $is_newsletter_project_enabled ? true : 0; ?>;
</script>
