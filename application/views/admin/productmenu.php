								<div class="top-nav">
									<ul>
									<li class="<?php if ($type=='product') echo('active')?>">
										<a class="func-menu" href="/admin/<?php echo $func ?>?id=<?php echo($product_id)?>">
										基本情報</a>
									</li>
									<?php if ($product_id != NULL) {
										$orm = ORM::factory('product', $product_id);
										$upd_arr = DB::select('lang_cd', 'upd_time')->from('t_product_description')->where('product_id', '=', $product_id)->execute()->as_array('lang_cd', 'upd_time');
										$display = ORM::factory('itemdisplay')->where('item_id', '=', $product_id)->where('item_div', '=', $orm->item_div)->find();
										$display_lang_arr = explode(',', $display->lang_display);
										if ($type=='productdesc') {
											echo('<li class="active">');
										}
										else {
											echo('<li>');
										}
										echo('<a class="func-menu" href="/admin/' . $func . 'desc?lang='. $bot_local_lang . '">' . __('admin.itme.itemmeu.description') . '</a></li>');
										if ($orm->link_type_cd != '') {
											if ($type=='linkdata') {
												echo('<li class="active">');
											}
											else {
												echo('<li>');
											}
											echo('<a class="func-menu" href="/admin/productlinkdata">連携情報</a>');
											echo('</li>');
										}
										if ($type=='keyword') {
											echo('<li class="active">');
										}
										else {
											echo('<li>');
										}
										echo('<a class="func-menu" href="/admin/' . $func . 'keyword">キーワード</a>');
										echo('</li>');
										if ($type=='display') {
											echo('<li class="active">');
										}
										else {
											echo('<li>');
										}
										echo('<a class="func-menu" href="/admin/' . $func . 'display">写真管理</a>');
										echo('</li>');
									} ?>
									<li class="">
										<a class="func-menu" href="/admin/<?php echo $func ?>s">
										一覧に戻る</a>
									</li>
								</ul>
							</div>