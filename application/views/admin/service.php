			<!-- BEGIN PAGE HEADER-->
			<!-- BEGIN PAGE HEAD -->
			<div class="page-head">
				<!-- BEGIN PAGE TITLE -->
				<div class="page-title">
					<h1><?php echo __('admin.service.label.servicelist') ?><small></small></h1>
				</div>
				<!-- END PAGE TITLE -->
			</div>
			<!-- END PAGE HEAD -->
			<!-- END PAGE HEADER-->
			<!-- BEGIN PAGE CONTENT-->
			<div class="row">
				<div class="col-md-12">
			        <!-- Page Content -->
			        <div id="page-wrapper">
			        <div class="portlet light">
						<div class="tabbable-line"> 
							<ul class="nav nav-tabs ">							
								<li class="<?php if ($_action == 'servicelist') echo('active'); ?>">
									<a href="/admin/servicelist">
									<?php echo __('admin.service.label.reception_list') ?></a>
								</li>
								<li class="<?php if ($_action == 'service') echo('active'); ?>">
									<a href="/admin/service">
									<?php echo __('admin.service.label.reception_application') ?></a>
								</li>
							</ul>
						</div>
							<div class="form-body">		
								<div class="form-group">
									<label class="control-label col-md-3"><?php echo __('admin.service.label.date') ?></label>
									<div class="col-md-3">
										<input name="service_date" value="<?php echo($service_date)?>" class="form-control form-control-inline input-small date-picker" data-date-format="yyyy-mm-dd" size="16" type="text"/>
									</div>
								</div>								
								<div class="form-group">
									<label class="control-label col-md-3"><?php echo __('admin.service.label.time') ?></label>
									<div class="col-md-3">
										<div class="input-group">
											<input name="service_time" type="text" class="form-control timepicker timepicker-24">
											<span class="input-group-btn">
											<button class="btn default" type="button"><i class="fa fa-clock-o"></i></button>
											</span>
										</div>
									</div>
								</div>								
								<div class="form-group">
									<label class="control-label col-md-3"><?php echo __('admin.service.label.type') ?></label>
									<div class="col-md-3">
										<?php echo Form::select('intent_cd', $service_intent, $intent_cd, array('id'=>'intent_cd','class'=>'form-control'))?>
									</div>
								</div>
								<div class="form-group">
									<label class="control-label col-md-3"><?php echo __('admin.service.label.details') ?></label>
									<div class="col-md-3">
										<input name="description" type="text" class="form-control" style="width:600px;" placeholder="">
									</div>
								</div>
								<div class="form-group">
									<label class="control-label col-md-3"><?php echo __('admin.service.label.room_number') ?></label>
									<div class="col-md-3">
										<input name="room" type="text" class="form-control" placeholder="">
									</div>
								</div>																								
								<div class="form-group">
									<label class="control-label col-md-3"><?php echo __('admin.service.label.name') ?></label>
									<div class="col-md-3">
										<input name="customer" type="text" class="form-control" placeholder="">
									</div>
								</div>			
							</div>
							<div class="form-actions">
								<div class="row">
									<div class="col-md-offset-3 col-md-9">
										<button type="button" id="saveButton" class="btn blue">
										<i class="fa fa-save"></i><?php echo __('admin.service.label.accept') ?></button>
										<button type="button" onclick="top.location='/admin/servicelist'" class="btn grey-steel"><?php echo __('admin.service.label.back') ?></button>
									</div>
								</div>
							</div>					
					</div>
			        </div>
			        <!-- /#page-wrapper -->					
				</div>
			</div>
			<!-- END PAGE CONTENT-->
