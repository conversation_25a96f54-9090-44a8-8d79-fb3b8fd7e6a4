<style>
  .table thead tr th {
    font-size: 12px;
    font-weight: 600;
    /* border-bottom: 0.5px solid #ddd !important; */
  }

  .report-menu {
    height: 140px;
  }

  .form-group {
    margin-left: 10px !important;
  }

  .stats-container {
    flex: 1;
    background-color: #fff;
    padding: 10px;
  }

  .stats-title {
    margin: 0;
    font-size: 36px;
    font-weight: 700;
    padding-bottom: 15px;
  }

  .stats-description {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 5px;
  }

  .stats-name {
    padding-top: 10px;
    border-top: 1px solid #EBEDF2;
  }

  .dropdown-menu.open {
    width: fit-content;
  }
  .surveys_donutchart_container {
    width: 50%;
    padding: 0 64px;
  }
  .surveys_donutchart_container .chart-title {
    width:156px;
    position:absolute;
    top:50%;
    left:50%;
    transform:translate(-50%,-45%);
    text-align:center;
  }
  .surveys_donutchart_container .chart-title p {
    font-size: 14px;
  }
  .surveys_donutchart_container .chart-title strong {
    font-size: 32px;
    margin-top: 4px;
    display: block;
  }
  .react-donut-chart .dc-graph-container {
    display: flex;
    align-items: center;
    gap: 30px;
    position: relative;
  }
  .react-donut-chart .dc-graph-container .legend {
    position: relative;
    inset: 0;
  }
  .react-donut-chart .dc-graph-container .visx-legend-label {
    margin-right: 0 !important;
    word-break: keep-all !important;
    font-size: 12px;
  }
</style>

<script>
	var _grp_bot_id = <?php echo $grp_bot_id?>;
	var _flg_faq_site = <?php echo isset($flg_faq_site) ? $flg_faq_site : 0; ?>;
</script>

<input type="hidden" id="class_cd_cond" name="class_cd_cond" value="<?php echo $scene_cd ?>" />
<input type="hidden" id="multiple" name="multiple" value="true" />

<div class="report-menu" style="margin-left:0px; background-color: #f6f7f9;">
  <div class="report-menu-tub row menu-row">
    <div class="form-body" style="margin-top:10px;">
      <div class="form-group">
        <label class="control-label col-md-1" style="min-width:100px;"><?php echo __('admin.common.label.status_kana') ?></label>
        <?php echo $reportmenu ?>
      </div>
    </div>
  </div>
  <div class="report-menu-edit row menu-row">
    <div class="form-body">
      <div class="form-group js-code">
        <label class="control-label col-md-1" style="min-width:100px;"><?php echo __('admin.common.label.user_flow') ?></label>
        <?php echo $botcond ?>
      </div>
      <div class="form-group">
        <label class="control-label col-md-1" style="min-width:100px;"><?php echo __('admin.common.label.period') ?></label>
        <div class="col-md-6" style="background-color: #f6f7f9;display:flex;">
          <div class="survey-period-date-container" style="width: 150px; background-color:white;">
            <span class="icon-calender" style="margin:8px 8px 8px 3px;"></span>
            <input name="start_date" id="start_date" value="<?php echo ($start_date) ?>" class="date-picker line-none" style="width: 100%;" size="12" data-date-format="yyyy-mm-dd" type="text" autocomplete="off" />
          </div>
          <p>〜</p>
          <div class="survey-period-date-container" style="width: 150px; background-color:white; margin-left: 5px; margin-right:12px; ">
            <span class="icon-calender" style="margin:8px 10px 8px 5px;"></span>
            <input name="end_date" id="end_date" value="<?php echo ($end_date) ?>" class="date-picker line-none" style="width: 100%;" size="12" data-date-format="yyyy-mm-dd" type="text" autocomplete="off" />
          </div>
          <button type="button" id="searchButton" class="btn-smaller btn-blue">
            <i class="fa fa-search mr10"></i><?php echo __('admin.common.button.search') ?>
          </button>
          <button type="button" id="clearButton" class="btn-smaller btn-gray-black"><?php echo __('admin.common.button.reset') ?></button>
        </div>
      </div>
    </div>
  </div>
</div>
<div style="margin-top:10px;display:flex;">
  <div>
  <?php echo Form::select('lang_cd', $lang, $lang_cd, array('id' => 'lang_cd', 'class' => 'form-control', 'style' => "height:28px;'")) ?>
  </div>
</div>
<div style="display:flex; flex-direction:column; padding-top:10px; gap:14px;">
  <div style="display:flex; gap:24px;">
    <div class="stats-container">
      <?php
        $surveys_distributed_diff = $surveys_distributed - $prev_surveys_distributed;
        $surveys_distributed_diff_percentage = null;
        if ($prev_surveys_distributed != 0) {
          $surveys_distributed_diff_percentage = number_format(abs($surveys_distributed_diff / $prev_surveys_distributed) *100, 1);
        }
      ?>
      <h4 class="stats-title"><?= $surveys_distributed ?></h4>
      <div class="stats-description">
        <p><?= __('admin.talkreport1.label.surveys_distributed') ?></p>
        <?php
          $background_color = $surveys_distributed_diff_percentage == 0 ? '#EBEDF2' : ($surveys_distributed_diff_percentage < 0 ? '#FFDCE5' : '#D3EEFF');
          $arrow = $surveys_distributed_diff_percentage == 0 ? 'arrow-stay' : ($surveys_distributed_diff_percentage < 0 ? 'arrow-down' : 'arrow-up');
          echo ('<span style="display:flex; align-items:flex-start; padding:5px; padding-left:10px; border-radius:20px; background-color:' . $background_color . '">');
          echo ($surveys_distributed_diff_percentage ?? '-') . '%';
          echo ('<img style="width: 15px;" alt="arrow" src="/assets/admin/images/' . $arrow . '.svg" />');
          echo ('</span>');
        ?>
      </div>
      <p class="stats-name">Surveys distributed count</p>
    </div>
    <div class="stats-container">
    <?php
      $survey_response_diff = $survey_response - $prev_survey_response;
      $survey_response_diff_percentage = null;
      if ($prev_survey_response != 0) {
        $survey_response_diff_percentage = number_format(abs($survey_response / $prev_survey_response) *100,1);
      }
    ?>
      <h4 class="stats-title"><?= $survey_response ?></h4>
      <div class="stats-description">
        <p><?= __('admin.talkreport1.label.surveys_response') ?></p>
        <?php
          $background_color = $survey_response_diff_percentage == 0 ? '#EBEDF2' : ($survey_response_diff_percentage < 0 ? '#FFDCE5' : '#D3EEFF');
          $arrow = $survey_response_diff_percentage == 0 ? 'arrow-stay' : ($survey_response_diff_percentage < 0 ? 'arrow-down' : 'arrow-up');
          echo ('<span style="display:flex; align-items:flex-start; padding:5px; padding-left:10px; border-radius:20px; background-color:' . $background_color . '">');
          echo ($survey_response_diff_percentage ?? '-') . '%';
          echo ('<img style="width: 15px;" alt="arrow" src="/assets/admin/images/' . $arrow . '.svg" />');
          echo ('</span>');
        ?>
      </div>
      <p class="stats-name">Survey response count</p>
    </div>
  </div>
  <?php if ($surveys_distributed > 0) { ?>
  <div style="padding:24px 60px; background-color:#fff;">
    <div style="display:flex;align-items:center;justify-content:center;gap:30px">
      <div class="surveys_donutchart_container">
        <div class="react-donut-chart" data-size="254" data-colors="<?= htmlspecialchars(json_encode($survey_distributed_datas_colors)) ?>" data-lang_cd="ja" data-datas="<?php echo htmlspecialchars(json_encode($surveys_distributed_datas, JSON_UNESCAPED_UNICODE)) ?>"></div>
        <div class="chart-title" style="display:none;">
          <p><?= __('admin.talkreport1.label.chart_title.satisfaction') ?></p>
          <p><?= __('admin.talkreport1.label.chart_title.surveys') ?></p>
          <p><?= __('admin.talkreport1.label.chart_title.distributed') ?></p>
          <strong><?= $surveys_distributed ?></strong>
        </div>
      </div>
      <?php if ($survey_response > 0) { ?>
      <div class="surveys_donutchart_container">
        <div class="react-donut-chart" 
          data-size="254" 
          data-colors="<?= htmlspecialchars(json_encode($surveys_response_datas_colors)) ?>" 
          data-lang_cd="ja" 
          data-datas="<?php echo htmlspecialchars(json_encode($surveys_response_datas, JSON_UNESCAPED_UNICODE)) ?>"
        >
        </div>
        <div class="chart-title" style="display:none;">
          <p><?= __('admin.talkreport1.label.chart_title.satisfaction') ?></p>
          <p><?= __('admin.talkreport1.label.chart_title.surveys') ?></p>
          <p><?= __('admin.talkreport1.label.chart_title.response') ?></p>
          <strong><?= $survey_response ?></strong>
        </div>
      </div>
      <?php } ?>
    </div>
    <div style="color: #3D3F45;font-size:12px;margin-top:30px"><?= __('admin.talkreport1.label.chart_description') ?></div>
  </div>
  <?php } ?>
</div>
<div id="page-wrapper">
  <div class="form-body" style="margin-top: 12px;margin-bottom: 2px;">
    <div class="form-group">
      <div style="float:right; margin-right: 18px">
        <button type="button" id="csvoutput" class="btn-smaller btn-white"><span class="icon-export"></span><?php echo __('admin.talkreport1.label.csv_export_inadequacy') ?></button>
      </div>
      <div style="float:right; margin-right:12px;">
        <button type="button" id="csvoutputall" data-tableId="faqreport" data-title="よく聞かれる質問" class="btn-smaller btn-white"><span class="icon-export"></span><?php echo __('admin.talkreport1.label.csv_export_all') ?></button>
      </div>
    </div>
  </div>

  <div class="portlet light">
    <div class="portlet box">
      <div class="portlet-body" style="position: relative;">
        <table class="table table-bordered table-hover js-data-table" id="faqreport">
          <thead style="background-color: #f6f7f9;">
          <tr>
            <?php 
            list($intent_type_dict, $intent_type_level) = $intent_class;
            for($i=0;$i<$intent_type_level;$i++) {
              echo('<th rowspan="2" style="vertical-align: middle;">' .  __('admin.talkreport1.label.category') . ($i+1) . '</th>');
            }
            ?>
            <th rowspan="2" style="vertical-align: middle;"><?php echo __('admin.talkreport1.label.question') ?></th>
            <th rowspan="2" style="vertical-align: middle;"><?php echo __('admin.talkreport1.label.num_asked') . "<br>" . __('admin.talkreport1.label.ai_chatbot_parentheses'); ?></th>
            <?php if ($flg_faq_site == 1) { ?>
              <th rowspan="2" style="vertical-align: middle;"><?php echo __('admin.talkreport1.label.num_displayed') . "<br>" . __('admin.talkreport1.label.talkappi_faq_parentheses'); ?></th>
            <?php } ?>
            <th colspan="3" style="border-bottom: 1px solid #ddd;"><?php echo __('admin.talkreport1.label.satisfaction_survey') . "<br>" . __('admin.talkreport1.label.ai_chatbot_parentheses'); ?></th>
            <?php if ($flg_faq_site == 1) { ?>
              <th colspan="3" style="border-bottom: 1px solid #ddd;"><?php echo __('admin.talkreport1.label.satisfaction_survey') . "<br>" . __('admin.talkreport1.label.talkappi_faq_parentheses'); ?></th>
            <?php } ?>
          </tr>
            <tr>
              <th><?php echo __('admin.talkreport1.label.adequate') ?></th>
              <th><?php echo __('admin.talkreport1.label.inadequate') ?></th>
              <th><?php echo __('admin.talkreport1.label.insufficient_ratio') ?></th>
              
              <?php if ($flg_faq_site == 1) { ?>
                <th><?php echo __('admin.talkreport1.label.adequate') ?></th>
                <th><?php echo __('admin.talkreport1.label.inadequate') ?></th>
                <th><?php echo __('admin.talkreport1.label.insufficient_ratio') ?></th>
              <?php } ?>
              <th style="width:40px; vertical-align: middle;display:none;"><?php echo __('admin.common.label.ai_recognition_rate') ?></th>
            </tr>
          </thead>
          <tbody>
            <?php
            foreach ($data as $k => $v) {
              if ($v['question'] == '') continue;
              list($intent_cd, $sub_intent_cd, $area_cd) = explode('#', $k);
              echo ('<tr>');
              if ($intent_type_level >0) {
                foreach($intent_type_dict[$k] as $c) {
                  echo ('<td>' . $c . '</td>');
                }
                for($i=0;$i<$intent_type_level-count($intent_type_dict[$k]);$i++) {
                  echo ('<td>' . $c . '</td>');
                }
              }
              // 質問
              echo ('<td>');
              echo ($v['question']);
              echo ('</td>');
              // 質問された回数（AIチャットボット）
              echo ('<td style="text-align:center;">');
              if ($v['count'] > 0) {
                echo ('<a class="link-animate" href="/admin/talkreport1_1?intent_cd=' . $intent_cd . '&sub_intent_cd=' . $sub_intent_cd . '&area_cd=' . $area_cd . '"><span class="badge-talk" style="background-color: #d3eeff">');
                echo ($v['count']);
                echo ('</span></a>');
              }
              if ($flg_faq_site == 1) {
                  // 表示回数（talkappi FAQ）
                  echo ('<td style="text-align:center;">');
                  echo ($v['count_faq']);
                  echo ('</td>');
                }  
              echo ('</td>');
              // 満足（AIチャットボット）
              echo ('<td style="text-align:center;">');
              if ($v['survey_yes'] > 0) {
                echo ('<a class="link-animate" href="/admin/talkreport1_2?intent_cd=' . $intent_cd . '&sub_intent_cd=' . $sub_intent_cd . '&area_cd=' . $area_cd . '&survey=1"><span class="badge-talk" style="background-color: #cff2d7">');
                echo ($v['survey_yes']);
                echo ('</span></a>');
              }
              echo ('</td>');
              // 不満足（AIチャットボット）
              echo ('<td style="text-align:center;">');
              if ($v['survey_no'] > 0) {
                if (is_array($v['reason']) && count($v['reason']) > 0) {
                  echo ('<a class="link-animate" href="/admin/talkreport1_2?intent_cd=' . $intent_cd . '&sub_intent_cd=' . $sub_intent_cd . '&area_cd=' . $area_cd . '"><span class="badge-talk" style="background-color: #ffdce5; color:#E53361;">');
                } else {
                  echo ('<a class="link-animate" href="/admin/talkreport1_2?intent_cd=' . $intent_cd . '&sub_intent_cd=' . $sub_intent_cd . '&area_cd=' . $area_cd . '"><span class="badge-talk" style="background-color: #ffdce5">');
                }
                echo ($v['survey_no']);
                echo ('</span></a>');
              }
              echo ('</td>');
              // 不満足割合（AIチャットボット）
              echo ('<td style="text-align:right;">');
              echo ($v['ratio']);
              echo ('</td>');
              if ($flg_faq_site == 1) {
                // 満足（talkappi FAQ）
                echo ('<td style="text-align:center;">');
                if ($v['survey_yes_faq'] > 0) {
                  echo ('<a class="link-animate" href="/admin/talkreport1_2?faq=1&intent_cd=' . $intent_cd . '&sub_intent_cd=' . $sub_intent_cd . '&area_cd=' . $area_cd . '&survey=1"><span class="badge-talk" style="background-color: #cff2d7">');
                  echo ($v['survey_yes_faq']);
                  echo ('</span></a>');
                }
                echo ('</td>');
                // 不満足（talkappi FAQ）
                echo ('<td style="text-align:center;">');
                if ($v['survey_no_faq'] > 0) {
                  if (is_array($v['reason_faq']) && count($v['reason_faq']) > 0) {
                    echo ('<a class="link-animate" href="/admin/talkreport1_2?faq=1&intent_cd=' . $intent_cd . '&sub_intent_cd=' . $sub_intent_cd . '&area_cd=' . $area_cd .  '"><span class="badge-talk" style="background-color: #ffdce5; color:#E53361;">');
                  } else {
                    echo ('<a class="link-animate" href="/admin/talkreport1_2?faq=1&intent_cd=' . $intent_cd . '&sub_intent_cd=' . $sub_intent_cd . '&area_cd=' . $area_cd .  '"><span class="badge-talk" style="background-color: #ffdce5">');
                  }
                  echo ($v['survey_no_faq']);
                  echo ('</span></a>');
                }
                // 不満足割合（talkappi FAQ）
                echo ('</td>');
                echo ('<td style="text-align:right;">');
                echo ($v['ratio_faq']);
                echo ('</td>');
              }
              echo ('<td style="display:none;">');
              if (isset($v['score']) && $v['score'] != "") echo (round($v['score'], 2));
              echo ('</td>');
              echo ('</tr>');
            }
            ?>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>

<script type="text/javascript" src="/assets/common/react/components/blocks/linechart.bundle.js"></script>
<script type="text/javascript" src="/assets/common/react/components/blocks/donutchart.bundle.js"></script>