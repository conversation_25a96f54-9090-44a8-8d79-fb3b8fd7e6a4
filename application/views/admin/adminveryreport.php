<!-- BEGIN PAGE HEADER-->
<!-- BEGIN PAGE HEAD -->
<div class="page-head">
    <!-- BEGIN PAGE TITLE -->
    <div class="page-title">
        <h1><?php echo __('admin.adminveryreport.title') ?><small></small></h1>
    </div>
    <!-- END PAGE TITLE -->
</div>
<!-- END PAGE HEAD -->
<!-- END PAGE HEADER-->
<!-- BEGIN PAGE CONTENT-->
<nav class="top-nav">
    <ul class="">
        <li class="<?php if ($tab == 'day') echo ('active'); ?>">
            <a href="/admin/adminveryreport?tab=day"><?php echo __('admin.adminveryreport.daily') ?></a>
        </li>
        <li class="<?php if ($tab == 'month') echo ('active'); ?>">
            <a href="/admin/adminveryreport?tab=month"><?php echo __('admin.adminveryreport.monthly') ?></a>
        </li>
    </ul>
</nav>
<input type="hidden" name="tab" value="<?php echo ($tab) ?>">
<div class="content-container white border">
    <div class="form-body">
        <div class="form-group">
            <label class="control-label col-md-1"><?php echo __('admin.common.label.period') ?></label>
            <div class="col-md-4" style="display: flex;align-items: center;">
                <?php
                $start_date_after = substr($start_date, 0, 10);
                $end_date_after = substr($end_date, 0, 10);
                $date_format = 'yyyy-mm-dd';
                $max_length = 10;
                if ($tab == 'day') {
                    $start_date_after = substr($start_date, 0, 10);
                    $end_date_after = substr($end_date, 0, 10);
                } else if ($tab == 'month') {
                    $start_date_after = substr($start_date, 0, 7);
                    $end_date_after = substr($end_date, 0, 7);
                    $date_format = 'yyyy-mm';
                    $max_length = 7;
                }
                ?>
                <input name="start_date" class="talkappi-datepicker" id="start_date" value="<?php echo ($start_date_after) ?>" data-date-format="<?php echo ($date_format) ?>" placeholder="<?php echo ($date_format) ?>" max-length="<?php echo ($max_length); ?>" />
                <p style="margin-right: 10px;">〜</p>
                <input name="end_date" class="talkappi-datepicker" id="end_date" value="<?php echo ($end_date_after) ?>" data-date-format="<?php echo ($date_format) ?>" placeholder="<?php echo ($date_format) ?>" max-length="<?php echo ($max_length); ?>" />
            </div>
            <div class="col-md-1">
                <button type="submit" id="searchButton" class="btn blue">
                    <i class="fa fa-search mr10"></i><?php echo __('admin.common.button.search') ?></button>
            </div>
        </div>
    </div>
    <table class="table table-striped table-bordered table-hover js-data-table">
        <thead>
            <tr>
                <th><?php echo __('admin.adminveryreport.bot_name') ?></th>
                <th><?php echo __('admin.adminveryreport.unique_user_count') ?></th>
                <th><?php echo __('admin.adminveryreport.operation_count') ?></th>
            </tr>
        </thead>
        <tbody>
            <?php
            foreach ($result as $item) {
                echo ('<tr class="gradeX odd" role="row">');
                echo ('<td>' . $item['botName'] . '</td>');
                echo ('<td>' . $item['userCount'] . '</td>');
                echo ('<td>' . $item['operateCount'] . '</td>');
                echo ('</tr>');
            }
            ?>
        </tbody>
    </table>
</div>
<!-- END PAGE CONTENT-->