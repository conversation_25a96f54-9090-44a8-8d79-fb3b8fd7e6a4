<link rel="stylesheet" type="text/css" href="/assets/admin/css/maximumcalendar.css"/>

<div class="content-container" style="padding-left: 0;">
  <div class="flex-x-between">
      <div><?php echo __('admin.maximumcalendar.title') ?></div>
      <span class="btn-smaller btn-blue js-new-maximumcalendars">
          <span class="icon-add-white"></span>
          <?php echo __('admin.common.button.create_new') ?>
      </span>
  </div>
</div>

<div class="content-container white border">
  <table class="table table-striped table-bordered table-hover js-data-table">
    <thead>
      <tr>
        <th><?php echo __('admin.maximumcalendar.name') ?></th>
        <th style="width:80px;"><?php echo __('admin.maximums.stock_type') ?></th>
        <th style="width:220px;"><?php echo __('admin.common.label.last_update') ?></th>
        <th style="width:240px;"><?php echo __('admin.common.label.details') ?></th>
      </tr>
    </thead>
    <tbody>
      <?php
        foreach ($items as $item) {
      ?>
        <tr class="gradeX odd" role="row">
          <!-- 枠名称 -->
          <td>
            <a href="/<?php echo $_path?>/maximumcalendar?id=<?php echo ($item['id']) ?>" class="js-calendar link-animate"><?php echo($item['title']) ?></a>
          </td>
          <!-- 枠タイプ -->
          <td class="span <?php echo($item['span']) ?>">
            <?php echo($span_codes[$item['span']]) ?>
          </td>
          <!-- 最終更新 -->
          <td class="">
            <span class="">
              <?php echo($item['upd_time']) ?>
              <?php echo($item['upd_user_name']) ?>
            </span>
          </td>
          <!-- 詳細 -->
          <td class="flex">
            <?php if($item['span'] !== 'all'){ ?>
              <a href="/<?php echo $_path?>/maximumcalendar?id=<?php echo ($item['id']) ?>&fullview=true" target="_blank" rel="noopener noreferrer"><div class="btn round image calendar js-calendar"><?php echo __('admin.common.label.calendar') ?></div></a>
            <?php }?>
            <div class="btn round image edit js-edit" data-maximumcalendar-id=<?php echo ($item['id']) ?> ><?php echo __('admin.common.button.edit') ?></div>
            <div class="btn round image copy js-copy" data-maximumcalendar-id=<?php echo ($item['id']) ?> ><?php echo __('admin.common.label.copy') ?></div>
          </td>
        </tr>
      <?php } ?>
    </tbody>
  </table>
</div>

<input type="hidden" name="calendar_id" id="calendar_id" value="" />