<link rel="stylesheet" type="text/css" href="/assets/admin/css/congestion_user.css"/>
<script>
	const _status = <?php echo json_encode($congestions, JSON_UNESCAPED_UNICODE)?>;
  const _congestion_numbers = <?php echo json_encode($congestion_numbers, JSON_UNESCAPED_UNICODE)?>;
	const _bot_id = <?php echo json_encode($facility->bot_id, JSON_UNESCAPED_UNICODE)?>;
  const _bot_lang_cd = <?php echo json_encode(explode(",",$facility->lang_cd), JSON_UNESCAPED_UNICODE)?>;
  const _congestion_user_page_def = <?php echo json_encode($congestion_user_page_def, JSON_UNESCAPED_UNICODE)?>;
  const _bus_schedule = <?php echo json_encode($bus_schedule) ?>;
  const _promotion_images = <?php echo json_encode($promotion_images) ?>;
  const _holidays = <?php echo json_encode($holidays) ?>;
  let _display_lang = <?php echo json_encode( 'ja', JSON_UNESCAPED_UNICODE)?>;
</script>
<link
  rel="stylesheet"
  href="https://cdn.jsdelivr.net/npm/swiper@8/swiper-bundle.min.css"
/>
<script src="https://cdn.jsdelivr.net/npm/swiper@8/swiper-bundle.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.24.0/moment.min.js" defer></script>
<?php
  $url = $_SERVER['REQUEST_URI'];
  $full_screen = strpos($url, "screen=1");
  $screen_2 = strpos($url, "screen=2");
?>
<div class="full-view">
  <main class="congestion-main-container">
    <div class="congestion-main-left" <?php if ($full_screen == true) echo 'style="width:62.5%;"' ?>>
      <!-- Header（時間とQRコード） -->
      <section class="congestion-header-container" <?php if ($full_screen == true) echo 'style="padding: 12px; padding-right: 0;"' ?>>
        <div>
          <p class="date-label congestion-date"></p>
          <p class="time-label congestion-clock" <?php if ($full_screen == true) echo 'style="font-size:36px;"' ?>></p>
          <p class="congestion-page-title congestion-page-header" <?php if ($full_screen == true) echo 'style="font-size:24px;"' ?>>混雑状況</p>
        </div>
        <div class="congestion-qr-container">
          <div class="congestion-qr-image-container">
            <div id="img-qr" class="congestion-qr-image"></div>
          </div>
          <div class="congestion-qr-text-container">
            <h1 class="congestion-qr-text-title"><?php echo $facility->bot_name ?></h1>
            <p class="qr-description-1">滞在中ウェブアプリ(ダウンロード不要)</p>
            <p class="qr-description-2">各施設の混雑状況をスマホでリアルタイムで確認できます。<br>館内・周辺案内など豊富な情報を掲載中！ぜひお試しください。</p>
          </div>
        </div>
      </section>
      <!-- 混雑状況のswiper -->
      <section class="congestion-panels-container" <?php if ($full_screen == true) echo 'style="padding:12px; padding-right:0; height:58%;"' ?>>
        <div class="swiper">
        <!-- Additional required wrapper -->
          <div class="swiper-wrapper" id="swiper-wrapper">
            <!-- Slides -->
            <?php
              foreach ($congestions as $c) {
            ?>
              <article class="swiper-slide <?php if ($screen_2 == true) echo 'swiper-slide2' ?>" <?php if ($full_screen == true) echo 'style="width: 250px !important"' ?>>
                <div class="panel-top <?php if ($screen_2 == true) echo 'panel-top2' ?>">
                  <?php if($c['item_image']) {?>
                    <div class="panel-image <?php if ($screen_2 == true) echo 'panel-image2' ?>" style="background: url(<?php echo $c['item_image'] ?>); background-position: 50% 50%;"></div>
                  <?php } ?>
                  <div class="panel-top-main">
                    <h2 class="panel-top-title <?php if ($screen_2 == true) echo 'panel-top-title2' ?> js-<?php echo $c['item_id']?>-title" <?php if ($full_screen == true) echo 'style="height: 45px; -webkit-line-clamp: 2; margin-top: 16px;"' ?>><?php echo $c['item_name'] ?></h2>
                    <p class="js-congestion-container-title panel-congestion-current <?php if ($screen_2 == true) echo 'panel-congestion-current2' ?> js-<?php echo $c['item_id']?>">現在の混雑状況</p>
                    <div class="panel-congestion-container <?php if ($screen_2 == true) echo 'panel-congestion-container2' ?> js-congestion-status js-<?php echo $c['item_id']?>">
                      <p class="panel-congestion-title <?php if ($screen_2 == true) echo 'panel-congestion-title2' ?> js-congestion_status js-<?php echo $c['item_id']?>"></p>
                    </div>
                    <div class="js-upd-container panel-congestion-updated-container <?php if ($screen_2 == true) echo 'panel-congestion-updated-container2' ?>">
                      <p class="js-congestion_upd-title panel-congestion-updated-color js-<?php echo $c['item_id']?>">最終更新</p>
                      <p class="js-congestion_upd panel-congestion-updated-color js-<?php echo $c['item_id']?>"></p>
                    </div>
                  </div>
                </div>
                <div class="panel-bottom <?php if ($screen_2 == true) echo 'panel-bottom2' ?>">
                  <div class="bg-white p-3">
                    <ul class="congestion-panel-tags">
                      <?php if ($c['item_status_cd'] != '01'){
                        $red_tag = $c['item_status_cd'] == '03' ? 'red' : '';
                      ?>
                        <li class="congestion-panel-tag <?php if ($screen_2 == true) echo 'congestion-panel-tag2' ?> <?php echo $red_tag?>" data-item-status="<?php echo $c['item_status_cd'] ?>"><?php echo $c['item_status']?></li>
                      <?php } else if($c['item_class']){?>
                        <?php foreach (explode(' ', $c['item_class']) as $index => $class_name){ ?>
                        <li class="congestion-panel-tag <?php if ($screen_2 == true) echo 'congestion-panel-tag2' ?>" data-item-class="<?php echo explode(' ',$c['class_cd'])[$index] ?>"><?php echo $class_name ?></li>
                        <?php }?>
                    <?php }?>
                    </ul>
                  </div>
                  <p class="congestion-panel-text <?php if ($screen_2 == true) echo 'congestion-panel-text2' ?> js-<?php echo $c['item_id']?>" <?php if ($full_screen == true) echo 'style="-webkit-line-clamp: 4;"' ?>>
                    <?php echo nl2br($c['sell_point'])?>
                  </p>
                </div>
              </article>
            <?php } ?>
          </div>
        </div>
      </section>
      <?php 
      if ($full_screen == true) {
      ?>
        <section class="congestion-bus-container">
          <div class="congestion-bus-wrapper">
            <h3 class="congestion-bus-title congestion-bus-schedule-title">バス時刻　ホテル駅
            </h3>
            <div class="next-bus-wrapper">
              <div class="next-bus" style="display: none;" id="next-bus-1">
                <p class="bottom-spacing-sm bus-text-small" id="next-bus-1-time-to"></p>
                <p id="next-bus-1-time" class="congestion-bus-title bottom-spacing-sm"></p>
                <p class="bus-text-small" id="next-bus-1-destination"></p>
              </div>
              <div class="next-bus" style="display: none;" id="next-bus-2">
                <p class="bottom-spacing-sm bus-text-small" id="next-bus-2-time-to"></p>
                <p id="next-bus-2-time" class="congestion-bus-title bottom-spacing-sm"></p>
                <p class="bus-text-small" id="next-bus-2-destination"></p>
              </div>
              <div class="next-bus" style="display: none;" id="next-bus-3">
                <p class="bottom-spacing-sm bus-text-small" id="next-bus-3-time-to"></p>
                <p id="next-bus-3-time" class="congestion-bus-title bottom-spacing-sm"></p>
                <p class="bus-text-small" id="next-bus-3-destination"></p>
              </div>
            </div>
          <div>
        </section>
      <?php
      }
    ?>
    </div>
    <?php 
      if ($full_screen == true) {
    ?>
    <!-- 混雑一覧 -->
    <section class="congestion-list-container">
      <?php if (count($promotion_images) > 0) {
        foreach($promotion_images as $key=>$image) {
          echo ('<img data-id="' . $key . '" src="' . $promotion_images[$key]['msg_image'] . "?". time() . '" alt="Promotion Image" id="promotion-image-' . $key . '" class="promotion-image" style="display: none;" />');
        }
      }
      ?>
        <div id="congestion-list-data" class="congestion-list-wrapper">
          <h2 class="congestion-list-header">混雑状況一覧</h2>
          <?php
            foreach ($congestions as $c) {
          ?>
          <div class="congestion-list-item">
            <div class="congestion-item-left">
              <h3 class="congestion-list-title js-<?php echo $c['item_id']?>-title"><?php echo $c['item_name'] ?></h3>
              <ul class="congestion-panel-tags congestion-list-tags">
                <?php if ($c['item_status_cd'] != '01'){
                  $red_tag = $c['item_status_cd'] == '03' ? 'red' : '';
                ?>
                  <li class="congestion-panel-tag <?php echo $red_tag?>" data-item-status="<?php echo $c['item_status_cd'] ?>"><?php echo $c['item_status']?></li>
                <?php } else if($c['item_class']){?>
                  <?php foreach (explode(' ', $c['item_class']) as $index => $class_name){ ?>
                    <li class="congestion-panel-tag" data-item-class="<?php echo explode(' ',$c['class_cd'])[$index] ?>"><?php echo $class_name ?></li>
                    <?php }?>
                  <?php }?>
              </ul>
            </div>
            <div class="congestion-item-right">
              <p class="congestion-list-status js-congestion_status js-congestion-color-text js-<?php echo $c['item_id']?>"></p>
              <div class="congestion-list-time">
                <p class="js-congestion_upd js-<?php echo $c['item_id']?>"></p>
                <p class="js-congestion_upd-title js-<?php echo $c['item_id']?>">最終更新</p>
              </div>
            </div>
          </div>
        <?php
          }
        ?>
      </div>
    </section>
    <?php
      }
    ?>
  </main>
</div>