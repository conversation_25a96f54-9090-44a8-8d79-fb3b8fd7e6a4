<script>
const _paging = <?php echo json_encode($post['paging']) ?>;
</script>

<!-- BEGIN PAGE HEAD -->
<div class="page-head">
    <!-- BEGIN PAGE TITLE -->
    <div class="page-title">
        <?php
		if ($item_div == 1) {
			$title = '施設コンテンツ';
		} else if ($item_div == 2) {
			$title = '広域コンテンツ';
		} else if ($item_div == 3) {
			$title = 'グループ施設';
		} else if ($item_div == 4) {
			$title = 'メディア';
		}
		?>
        <h1 style="float:left;margin-right:20px;">
            <?php echo $title ?>
        </h1>
    </div>
    <!-- END PAGE TITLE -->
</div>
<!-- END PAGE HEAD -->
<!-- END PAGE HEADER-->

<!-- 検索条件欄 -->
<div class="content-container light-gray">
    <?php echo $classbox ?>
    <?php if ($div_item_area != '') {
		echo ($areabox);
	} ?>
    <div class="form-group">
        <!-- 営業状態 -->
        <label class="control-label col-md-1" style="padding-left:15px; padding-right:15px;">
            <?php echo __('admin.common.label.lang') ?>
        </label>
        <div class="col-md-2" style="width:135px;">
            <?php echo Form::select('lang_cd', $lang, $post['lang_cd'], array('id' => 'lang_cd', 'class' => 'form-control')) ?>
        </div>
        <label class="control-label col-md-1" style="width:78px; padding-left:15px; padding-right:15px;">
            <?php echo __('admin.common.label.operating_conditions') ?>
        </label>
        <div class="col-md-2">
            <?php echo Form::select('items_status_cd', ['' => __('admin.common.label.all')] + $_codes['11'], $post['items_status_cd'], array('id' => 'items_status_cd', 'class' => 'form-control')) ?>
        </div>
        <!-- 公開中 -->
        <label class="control-label col-md-1" style="width:56px;">
            <?php echo __('admin.common.label.show_published_only') ?>
        </label>
        <div class="col-md-2" style="width:100px;">
            <input type="checkbox" name="show_flg" value="1" class="make-switch" <?php if ($post['show_flg'] == 1)
				echo ("checked") ?> data-on-color="success" data-off-color="warning">
        </div>
        <?php if ($item_div == 1 || $item_div == 2) { ?>
        <!-- 混雑状況設定あり -->
        <label class="control-label col-md-1" style="width:80px; white-space: nowrap; margin: 0 15px;">
            <?php echo __('admin.common.label.congestion_situation') ?>
        </label>
        <div class="col-md-2" style="width:100px; margin: 0 15px;">
                <input type="checkbox" name="congestion_flg" value="1" class="make-switch" <?php if ($post['congestion_flg'] == 1)
					echo ("checked") ?> data-on-color="success" data-off-color="warning">
        </div>
        <?php } ?>
        <div class="col-md-1 ml20">
            <span class="btn-smaller btn-yellow" id="searchButton"><i class="fa fa-search mr10"></i>
                <?php echo __('admin.common.button.search') ?>
            </span>
        </div>
    </div>
</div>

<div class="content-container" style="padding-left: 0;">
    <div style="display: flex;justify-content: end;">
        <span class="btn-smaller btn-blue js-new">
            <span class="icon-add-white"></span>
            <?php echo __('admin.common.button.create_new') ?>
        </span>
    </div>
</div>

<!-- BEGIN PAGE CONTENT-->
<div class="row">
    <div class="col-md-12">
        <!-- Page Content -->
        <div id="page-wrapper">
            <div class="top-nav font-standard">
                <ul>
                    <li class="active">
                        <a class="link-animate" href="/admin/items?div=<?php echo $item_div ?>">
                            <?php echo __('admin.common.label.list_display') ?>
                        </a>
                    </li>
                    <?php if ($item_div == 2 && $_bot_id != 9999999) { ?>
                    <li class="">
                        <a class="link-animate" href="/admin/itemlink">
                            <?php echo __('admin.common.label.list_link') ?>
                        </a>
                    </li>
                    <?php } ?>
                    <li class="">
                        <a class="link-animate" href="/admin/itemsorder">
                            <?php echo __('admin.items.item_order.edit') ?>
                        </a>
                    </li>
                </ul>
            </div>
            <div class="edit-container">
                <div class="settings-container">
                    <input type="hidden" id="item_div" name="item_div" value="<?php echo $item_div ?>" />
                    <input type="hidden" id="class_cd_cond" name="class_cd_cond"
                        value="<?php echo $post['class_cd_cond'] ?>" />
                    <input type="hidden" id="area_cd_cond" name="area_cd_cond"
                        value="<?php echo $post['area_cd_cond'] ?>" />
                    <input type="hidden" id="sort_flg" name="sort_flg" value="<?php echo $sort_flg ?>" />
                    <table class="table table-striped table-bordered table-hover js-data-table" id="itemtable">
                        <thead>
                            <tr>
                                <th style="width:100px;">
                                    <?php echo __('admin.common.label.classification') ?>
                                </th>
                                <th>
                                    <?php echo __('admin.common.label.name') ?>
                                </th>
                                <th>
                                    <?php echo __('admin.common.label.description') ?>
                                </th>
                                <?php
								echo ('<th style="width:80px;">' . __('admin.common.label.display_information') . '</th>');
								?>
                                <th style="width:80px;">
                                    <?php echo __('admin.common.label.last_update') ?>
                                </th>
                                <!-- <th style="width:60px;">
                                    <?php echo __('admin.common.label.note') ?>
                                </th> -->
                            </tr>
                        </thead>
                        <tbody>
                            <?php
							foreach ($items as $item) {
								?>
                            <tr class="gradeX odd" role="row">
                                <td>
                                    <?php
										$can_modify_status = true;
										$class_cd = explode(' ', $item['class_cd']);
										foreach ($class_cd as $cd) {
											foreach ($item_statusless_config as $sc) {
												if ($sc != '' && strpos($cd, $sc) === 0) {
													$can_modify_status = false;
													break;
												}
											}
											$name = '';
											$names = [];
											for ($i = 1; $i <= strlen($cd) / 2; $i++) {
												$key = substr($cd, 0, $i * 2);
												if (array_key_exists($key, $code_div_dict))
													$names[] = $code_div_dict[$key];
											}
											if (count($names) > 2)
												$names = array_slice($names, count($names) - 2);
											$name = implode(' > ', $names);
											echo ($name . '<br>');
										}
										?>
                                </td>
                                <td>
                                    <?php echo ($item['item_cd'] . '<br>') ?>
                                    <a class="link-animate" href="/admin/item?id=<?php echo ($item['item_id']) ?>">
                                        <?php if ($item['item_name'] == NULL)
												echo (__('admin.common.label.untitled_content'));
											else
												echo ($item['item_name']) ?>
                                    </a>
                                    <?php if ($can_modify_status) {
												echo ('<br />');
												echo Form::select('status_cd', $_codes['11'], $item['item_status_cd'], array('class' => 'form-control status_cd', 'style' => 'width:160px;', 'item_id' => $item['item_id']));
											}
											?>
                                </td>
                                <td>
                                    <?php echo ($item['sell_point']) ?>
                                </td>
                                <?php
									echo ('<td style="text-align:center;vertical-align: middle;line-height:26px;">');
									echo '<div style="display:flex;flex-wrap:wrap;gap:4px;justify-content:flex-start;">';
									if ($_user->role_cd == '80' || $_user->role_cd == '99') {
										if ($item['recommend'] == 1)
											echo ('<a class="link-animate" href="/admin/itemdisplay?id=' . $item['item_id'] . '"><i class="fas fa-thumbs-up" style="float:right;color: #fecb81"></i></a>');
										if ($item['lang_display'] === '')
											echo ('<i class="fas fa-eye-slash" style="float:right;color: #aaa"></i>');
                                    }
                                    if ($item_div != 3 && $item['bot_id'] == $_bot_id) {
                                        if ($item['keyword0'] == '' && $item['keyword2'] == '') {
                                            echo ('<a class="link-animate" href="/admin/itemkeyword?id=' . $item['item_id'] . '"><span class="btn round light-gray">');
                                        } else {
                                            echo ('<a class="link-animate" href="/admin/itemkeyword?id=' . $item['item_id'] . '"><span class="btn round light-blue">');
                                        }
                                        echo (__('admin.common.label.keyword'));
                                        echo ('</span></a>');
                                    }
									// if ($item['lang_display'] != '') {
									// 	$lang_display = explode(',', $item['lang_display']);
									// 	foreach($lang_display as $la) {
									// 		echo('<a href="/admin/itemdisplay?id=' . $item['item_id'] . '">');
									// 		echo ('<span class="label label-success">' . $_codes['02'][$la] . '</span>');
									// 		echo('</a>');
									// 	}
									// }
								
									if ($item['bot_id'] != $_bot_id) {
										echo ('<a href="javascript:void();" class="unlink" sid="' . $item['item_id'] . '"><span class="btn btn-red" style="color: white; border-radius: 4px; padding: 3px 6px;"><span class="icon-minus-white"></span>' . __('admin.common.label.unlink') . '</span>');
									}

									if ($item['public_flg'] === 0) {
										echo ('<span class="label label-danger">非公開</span>');
									} else {
										if ($item['sort_no1'] == 0 || $item['sort_no1'] == '-1') { // 前ランダムと後ランダム
											echo ('<a class="link-animate" href="/admin/itemdisplay?id=' . $item['item_id'] . '"><span class="btn round light-gray">');
										} else {
											echo ('<a class="link-animate" href="/admin/itemdisplay?id=' . $item['item_id'] . '"><span class="btn round light-blue">');
										}
										echo __('admin.common.label.display_order');
										echo ('</span></a>');
									}
									echo '</div>';
									echo ('</td>');
									?>
                                <td style="font-size: 10px;">
                                    <?php if ($item['upd_time'] != NULL) {
											echo date('Y/m/d H:i', strtotime($item['upd_time'])) . '<br/>' . $item['name'];
										} ?>
                                </td>
                                <!-- <td>
                                    <?php
										$view_text = '';
										$reqing_msgs = [];
										if (array_key_exists($item['item_id'], $req_tasks)) {
											foreach ($req_tasks[$item['item_id']] as $req) {
												$req_task_view = View::factory('admin/reqtaskview');
												$req_task_view->req = $req;
												$view_text = $view_text . $req_task_view;
												$req_data = json_decode($req['req_data'], true);
												if (is_array($req_data) && array_key_exists('msg_cd', $req_data)) {
													if (!in_array($req_data['msg_cd'], $reqing_msgs)) {
														$reqing_msgs[] = $req_data['msg_cd'];
													}
												}
											}
										}
										if ($_user->role_cd == '80' || $_user->role_cd == '99') {
											echo ('<a href="javascript:void(0);" class="reqinput" item_id="' . $item['item_id'] . '"><span class="badge badge-success" style="margin: 4px;">' . __('admin.common.label.request_input') . '</span></a>');
											//　2022/11/16　陳さんと話して、「FAQ修正依頼ボタン」は、現在使用していないのと汎用的な仕組みではないので、コメントアウト
											// foreach($req_task_msgs as $msg) {
											// 	if (!in_array($msg['msg_cd'], $reqing_msgs)) {
											// 		echo('<a href="javascript:void(0);" class="reqtask" msg_cd="' . $msg['msg_cd'] . '" item_id="' . $item['item_id'] . '"><span class="badge badge-primary" style="margin: 4px;">' . $msg['msg_name'] . '</span></a>');
											// 	}
											// }
										}
										echo ($view_text);
										?>
                                </td> -->
                            </tr>
                            <?php } ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <!-- /#page-wrapper -->
    </div>
</div>
<!-- END PAGE CONTENT-->
<?php echo ($req_box); ?>