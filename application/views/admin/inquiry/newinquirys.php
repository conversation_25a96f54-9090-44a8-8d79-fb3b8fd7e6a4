<input type="hidden" id="inquiry_id" name="inquiry_id" value="" />
<!-- Page Content -->
<style>
.new-inquiry {
    font-size: 10px;
    padding: 4px;
    margin-left:4px;
    background-color: lightgoldenrodyellow;
    cursor: pointer;
    width: fit-content;
}

.new-inquiry.time {
    background-color: lightcyan;
}

.new-inquiry.payment {
    background-color: #cff2d7;
    padding: 2px;
    margin-top:4px;
    margin-left:0px;
}

</style>

<div class="content-container white">
    <table class="table table-striped table-bordered table-hover js-data-table">
        <thead>
            <tr>
                <th style="width: 60px;">ID</th>
                <th><?php echo __('admin.inquirys.label.inquiry_name') ?></th>
                <th style="width: 85px;"><?php echo __('admin.inquirys.label.period') ?></th>
                <th>施設</th>
                <th>枠</th>
                <th>決済</th>
                <th style="width: 72px;"><?php echo __('admin.inquirys.label.answers_count') ?><br/><?php echo __('admin.inquirys.label.awaiting_response') ?></th>
                <th style="width:140px;"><?php echo __('admin.inquirys.label.last_update') ?></th>
                <th style="width: 100px;"><?php echo __('admin.common.label.operation') ?></th>
            </tr>
        </thead>
        <tbody>
            <?php
            foreach ($items as $item) {
                if ($item['scene_cd'] == '') $item['scene_cd'] =  $_bot->facility_cd;
                if ($item['renew_time'] == null) {
                    $public_url = $verify_url . '?f=' . $item['scene_cd'] . '&id=' . $item['inquiry_id'];
                }
                else {
                    $public_url = $verify_url . '?f=' . $item['scene_cd'] . '&id=' . $item['inquiry_cd'];
                }
            ?>
                <tr class="gradeX odd" role="row">
                    <!-- コード -->
                    <td data-order="<?php echo ($item['inquiry_id']) ?>">
                        <?php if ($item['base_ver_id'] != null) echo ('<a href="inquiry?id=' . $item['base_ver_id'] . '"><span class="badge badge-warning">' . $item['base_ver_id'] . __('admin.inquirys.label.revision') . '</span></a><br>'); ?>
                        <?php
                        echo ($item['inquiry_id'] . '<br>');
                        ?><br />
                    </td>
                    <!-- 問合せフォーム名 -->
                    <td>
                        <a href="/<?php echo $_path?>/inquiry?id=<?php echo ($item['inquiry_id']) ?>"><?php echo($item['inquiry_name']) ?></a>
                        <?php if ($_user->role_cd == '99') {
                            if ($item['renew_time'] == null) {
                                echo('<span class="new-inquiry js-switch-new-inquiry" data-inquiry_id="' . $item['inquiry_id'] . '">※新版に</span>');
                            }
                            else {
                                echo('<span class="new-inquiry time">※' . $item['renew_time'] . '</span>');
                            }
                        }
                        ?>
                    </td>
                    <!-- 実施時間 -->
                    <td style="text-align:left;">
                        <?php
                            if ($item['start_date'] != null) {
                                echo (substr($item['start_date'], 0, 10));
                            }
                            echo('<br>～<br>');
                            if ($item['end_date'] != null) {
                                echo (substr($item['end_date'], 0, 10));
                            }
                         ?>
                    </td>
                    <!-- 施設 -->
                    <td>
                        <?php 
                            echo($item['bot_name']);
                        ?>
                    </td>
                    <td>
                        <?php 
                            if (isset($entries[$item['inquiry_id']])) {
                                $existed = [];
                                if (isset($entries[$item['inquiry_id']]['maximum'])) {
                                    if (!in_array($entries[$item['inquiry_id']]['maximum']['maximum_id'], $existed)) {
                                        echo('<p style="background-color:#e3e5e8;">' . $entries[$item['inquiry_id']]['maximum']['maximum_id'] . ' ' . $entries[$item['inquiry_id']]['maximum']['span'] . '</p>');
                                        $existed[] = $entries[$item['inquiry_id']]['maximum']['maximum_id'];
                                    }
                                }
                                if (isset($entries[$item['inquiry_id']]['maximums'])) {
                                    foreach($entries[$item['inquiry_id']]['maximums'] as $e) {
                                        if (!in_array($e['maximum_id'], $existed)) {
                                            echo('<p>' . $e['maximum_id'] . ' ' . $e['span'] . '</p>');
                                            $existed[] = $e['maximum_id'];
                                        }
                                    }
                                }
                            }
                        ?>
                    </td>
                    <td>
                        <?php 
                            if (isset($entries[$item['inquiry_id']])) {
                                if (isset($entries[$item['inquiry_id']]['payment'])) {
                                    echo('<p>' . json_encode($entries[$item['inquiry_id']]['payment']) . '</p>');
                                }
                            }
                            $inquiry_data = json_decode($item['inquiry_data'], true);
                            if (isset($inquiry_data['cancelable']) && $inquiry_data['cancelable'] == 1) {
                                echo('<p class="new-inquiry payment">キャンセル可能' . '</p>');
                            }
                            if (isset($inquiry_data['modifiable']) && $inquiry_data['modifiable'] == 1) {
                                echo('<p class="new-inquiry payment">変更可能' . '</p>');
                            }
                        ?>
                    </td>
                    <!-- 回答件数 -->
                    <td style="text-align:center;" data-order="<?php if (isset($results[$item['inquiry_id']])) {echo ($results[$item['inquiry_id']]);} else {echo 0;} ?>">
                    <?php 
                        $answered = 0;
                        if (array_key_exists($item['inquiry_id'], $results)) {
                            $answered = $results[$item['inquiry_id']]; ?>
                            <a href="javascript:void(0);" class="js-result" data-inquiry_id="<?php echo $item['inquiry_id'] ?>">
                                <span class="btn round"><?php echo ($results[$item['inquiry_id']]) ?></span>
                            </a>
                        <?php }?>
                    </td>
                    <!-- 最終更新 -->
                    <td class="">
                        <span class="">
                        <?php echo($item['upd_time']) ?><br>
                        <?php echo($item['upd_user_name']) ?>
                        </span>
                    </td>
                    <!-- 詳細 -->
                    <td>
                        <div style="margin-top: 2px;" class="btn round image preview js-preview" data-url="<?php echo ($public_url) ?>"><?php echo __('admin.common.label.demo') ?></div>
                    </td>
                </tr>
            <?php } ?>
        </tbody>
    </table>
</div>
