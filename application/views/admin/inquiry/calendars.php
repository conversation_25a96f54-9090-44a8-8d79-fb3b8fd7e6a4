<link rel="stylesheet" type="text/css" href="/assets/admin/css/inquirycalendar.css"/>

<div class="content-container" style="padding-left: 0;margin-top: -41px;">
  <div class="flex-x-between">
      <div></div>
      <span class="btn-smaller btn-blue js-new-inquirycalendar">
          <span class="icon-add-white"></span>
          <?php echo __('admin.common.button.create_new') ?>
      </span>
  </div>
</div>

<div class="content-container white border">
  <table class="table table-striped table-bordered table-hover js-data-table">
    <thead>
      <tr>
        <th><?php echo __('admin.inquirycalendars.th.name') ?></th>
        <th style="width:220px;"><?php echo __('admin.common.label.last_update') ?></th>
        <th style="width:240px;"><?php echo __('admin.common.label.details') ?></th>
      </tr>
    </thead>
    <tbody>
      <?php
        foreach ($calendars as $calendar) {
          $id = $calendar['id'];
          $upd_user_name = $calendar['upd_user_name'];
          $json_title = $calendar['title'];
          $obj_title = json_decode($json_title, true);
          $title = $obj_title[$_lang_cd];
          if (!$title) {
            foreach ($obj_title as $key => $value) {
              if ($value) {
                $title = $value;
                break;
              }
            }
          }
          // https://inquiry2.talkappi.com/calendar?id=96
          $public_url = $public_url_prefix.$id;
      ?>
        <tr class="gradeX odd" role="row">
          <!-- 枠名称 -->
          <td>
            <a href="/<?php echo $_path?>/calendar?id=<?php echo ($id) ?>" class="js-calendar link-animate"><?php echo($title) ?></a>
          </td>
          <!-- 最終更新 -->
          <td class="">
            <span class="">
              <?php echo($upd_user_name) ?>
              <?php echo($calendar['upd_time']) ?>
            </span>
          </td>
          <!-- 詳細 -->
          <td class="flex">
            <a class="link-animate" href="/<?php echo $_path?>/calendar?id=<?php echo ($id) ?>">
              <div class="btn round image edit"><?php echo __('admin.common.button.edit') ?></div>
            </a>
            <!-- コピー機能はまだないので、一旦非表示に -->
            <!-- <div class="btn round image copy js-copy" data-calendar-id=<?php echo ($id) ?> ><?php echo __('admin.common.label.copy') ?></div> -->
            <div class="btn round image copy js-public" @click="copyUri" data-clipboard-action="copy" data-clipboard-text="<?php echo htmlspecialchars($public_url) ?>"><?php echo __('admin.inquirycalendars.label.public_url') ?></div>
          </td>
        </tr>
      <?php } ?>
    </tbody>
  </table>
</div>

<input type="hidden" name="calendar_id" id="calendar_id" value="" />
<script type="text/javascript">
  const _inquiry_options = <?php echo ($inquiry_options) ?>;
  const _all_maximum_options = <?php echo ($all_maximum_options) ?>;
  const _inquiry_maximum_mapping = <?php echo ($inquiry_maximum_mapping) ?>;
  const _display_langs = '<?php echo json_encode(array_keys($bot_lang), JSON_UNESCAPED_UNICODE) ?>';
  const _lang_cd = '<?php echo $_lang_cd ?>';
</script>