
<ol class="survey-details-breadcrumbs-list" style="display: none;">
	<?php if ($inquiry_id == NULL) { ?>
		<li class="<?php if ($_action == 'inquirys') echo('active'); ?>">
			<a href="/<?php echo $_path?>/inquirys">
			<?php echo __('admin.inquirymenu.label.inquirys_list'); ?></a>
		</li>
	<?php }?>
	<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16">
		<g fill="none" fill-rule="evenodd" stroke-linecap="round" stroke-linejoin="round">
			<g stroke="#3D3F45">
				<g>
					<g>
						<path d="M6 4L10 8.105 6 12" transform="translate(-572 -829) translate(419 506) translate(153 323)"/>
					</g>
				</g>
			</g>
		</g>
	</svg>
  <li>
    <a>
      <span itemprop="name"><?php echo __('admin.inquirymenu.label.inquiry_detal'); ?></span>
    </a>
  </li>
</ol>

<nav class="top-nav">
	<ul class="">
		<?php if ($_user->role_cd == '74') {?>
			<li class="<?php if ($_action == 'inquiryresult') echo('active'); ?>">
				<?php 
					echo('<a href="/' . $_path . '/inquiryresult?fullview=true&id=' . $inquiry_id . '">');
					echo __('admin.inquirymenu.label.inquiry_result');
					echo('</a>');
				?>
			</li>
			<!--
			<li class="<?php if ($_action == 'inquiryrefer') echo('active'); ?>">
				<?php 
					echo('<a href="/' . $_path . '/inquiryrefer?fullview=true&id=' . $inquiry_id . '">');
					echo  __('admin.inquirymenu.label.inquiry_refer');
					echo('</a>');
				?>
			</li>
		-->
		<?php } else { ?>
		<?php if ($inquiry_id == NULL) { ?>
			<li class="<?php if ($_action == 'inquiry') echo('active'); ?>">
				<a href="/<?php echo $_path?>/inquiry">
				<?php echo __('admin.inquirymenu.label.add_new'); ?></a>
			</li>
			<li class="<?php if ($_action == 'inquirys') echo('active'); ?>">
				<a href="/<?php echo $_path ?>/inquirys">
				<?php echo __('admin.inquirymenu.label.inquirys_list'); ?></a>
			</li>
		<?php }?>
		<?php if ($inquiry_id != NULL) { 
			$menu_path = ($inquiry_div == NULL || $inquiry_div == 9) ? 'admininquiry' : 'adminorder';
			$model = new Model_Adminmodel();
			echo $model->tab_menu_authority($_path, $_action, 'id=' . $inquiry_id, $menu_path, 'inquiry', __('admin.inquirymenu.label.basic_settings'));
			echo $model->tab_menu_authority($_path, $_action, 'id=' . $inquiry_id, $menu_path, 'inquirydesc', __('admin.inquirymenu.label.summary_settings'));
			echo $model->tab_menu_authority($_path, $_action, 'id=' . $inquiry_id, $menu_path, 'inquiryentry', __('admin.inquirymenu.label.create_inquiry_form'));
			echo $model->tab_menu_authority($_path, $_action, 'id=' . $inquiry_id . '&type=detail', $menu_path, 'inquiryresult', '' . __('admin.inquirymenu.label.inquiry_result') .'');
			echo $model->tab_menu_authority($_path, $_action, 'id=' . $inquiry_id, $menu_path, 'inquiryrefer', __('admin.inquirymenu.label.inquiry_refer'));
			// inquiryordersの仕様について要確認のためコメントアウト
			// echo $model->tab_menu_authority($_path, $_action, 'id=' . $inquiry_id, 'admininquiry', 'inquiryorders', 'クーポン利用状況');
			?>
			<li class="<?php if ($_action == 'inquirys') echo 'active'; ?>">
				<a href="/<?php echo $_path ?>/inquirys">
					<?php echo __('admin.common.button.return_to_list'); ?>
				</a>
			</li>
		<?php }?>
		<?php }?>
	</ul>
</nav>