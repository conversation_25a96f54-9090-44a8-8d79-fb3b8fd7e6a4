<div class="content-container" style="padding-left: 0;">
  <div class="flex-x-between">
    <div></div>
    <span class="btn-smaller btn-blue js-new-label">
      <span class="icon-add-white"></span>
      <?php echo __('admin.common.button.create_new') ?>
      </button>
  </div>
</div>
<input type="hidden" name="label_id" id="label_id" />
<div class="content-container white">
  <table class="table table-striped table-bordered table-hover js-data-table">
    <thead>
      <tr>
        <th style="width:80px;"><?php echo __('admin.common.label.code') ?></th>
        <th style="width:80px;"><?php echo __('admin.label.title.div') ?></th>
        <th><?php echo __('admin.label.title.name') ?></th>
        <th style="width:80px;"><?php echo __('admin.label.title.relation') ?></th>
        <th style="width:140px;"><?php echo __('admin.common.label.last_update') ?></th>
        <th style="width:120px;"><?php echo __('admin.common.label.operation') ?></th>
      </tr>
    </thead>
    <tbody>
      <?php
      foreach ($items as $item) {
      ?>
        <tr class="gradeX odd" role="row">
          <!-- コード -->
          <td><?php echo ($item['label_id']) ?></td>
          <!-- タイプ -->
          <td class="span <?php echo ($item['label_div']) ?>">
            <?php echo strtoupper($item['label_div']) ?>
          </td>
          <!-- 名称 -->
          <td>
            <a href="/<?php echo $_path ?>/label?id=<?php echo ($item['label_id']) ?>" class="js-name link-animate"><?php echo ($item['label_name']) ?></a>
          </td>
          <!-- 関連利用 -->
          <td style="text-align:center;">
            <?php if (isset($results[$item['label_id']])) {
              echo $results[$item['label_id']];
            } ?>
          </td>
          <!-- 最終更新 -->
          <td class="">
            <span class="">
              <?php echo ($item['upd_time']) ?><br>
              <?php echo ($item['upd_user_name']) ?>
            </span>
          </td>
          <!-- 詳細 -->
          <td class="flex">
            <a class="link-animate" href="/<?php echo $_path ?>/label?id=<?php echo ($item['label_id']) ?>">
              <div class="btn round image edit js-memo"><?php echo __('admin.common.label.edit') ?></div>
            </a>
            <?php if (!isset($results[$item['label_id']])) { ?>
              <div class="btn round image delete js-delete" data-label_id="<?php echo ($item['label_id']) ?>"><?php echo __('admin.common.button.delete') ?></div>
            <?php } ?>
          </td>
        </tr>
      <?php } ?>
    </tbody>
  </table>
</div>