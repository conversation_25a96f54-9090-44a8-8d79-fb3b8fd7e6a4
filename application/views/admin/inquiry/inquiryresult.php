<script>
const _all_language_same = <?php echo $all_language_same ?>;
const _maximums = <?php echo json_encode($maximums, JSON_UNESCAPED_UNICODE) ?>;
const _status_list = '<?php echo $post['status_cd'] ?>';
const _support_type_list = '<?php echo $post['support_type_cd'] ?>';
const _labelcolor = <?php echo json_encode($labelcolor) ?>;
const _paging = <?php if (isset($post['paging'])) { echo json_encode($post['paging']); } else { echo 'null'; } ?>;
const _result_id = <?php if (isset($post['result_id']) && $post['result_id'] != null) { echo $post['result_id']; } else { echo 'null'; } ?>;
const _user_id = <?php echo $_user->user_id; ?>;
const _senderAlias = '<?php echo $mail_sender_alias; ?>';
const _clink_url = '<?php echo $clink_url; ?>';
const _inquiry_setting = <?php echo json_encode($inquiry_setting, JSON_UNESCAPED_UNICODE) ?>;
const _mail_signature = <?= json_encode($mail_signature, JSON_UNESCAPED_UNICODE) ?>;
const _mail_template = <?= json_encode($mail_template, JSON_UNESCAPED_UNICODE) ?>;
const _inquiry_div = "<?php echo $inquiry_div ?>";
</script>

<?php echo $menu ?>
<input type="hidden" id="start_time" value="<?php echo(date('Y-m-d H:i:s')) ?>" />
<input type="hidden" id="refresh_intval" value="<?php if (isset($inquiry_setting['result_refresh_intval'])) { echo $inquiry_setting['result_refresh_intval']; } else { echo '0'; } ?>" />
<input type="hidden" name="filter_entry_delete" id="filter_entry_delete" value="" />
<input type="hidden" name="filter_entry_answer_delete" id="filter_entry_answer_delete" value="" />
<input type="hidden" name="member_id" id="member_id" value="" />
<input type="hidden" name="bot_id" id="bot_id" value="" />
<input type="hidden" name="inquiry_id" id="inquiry_id" value="<?php echo $inquiry_id ?>" />
<input type="hidden" name="result_id" id="result_id" value="" />
<input type="hidden" name="type" id="type" value="<?php echo $post['type'] ?>" />
<input type="hidden" name="is_blocked" id="is_blocked" value="<?php echo $post['is_blocked'] ?>" />
<input type="hidden" name="status_cd" id="status_cd" value="<?php echo $post['status_cd'] ?>" />
<input type="hidden" name="support_type_cd" id="support_type_cd" value="<?php echo $post['support_type_cd'] ?>" />
<input type="hidden" name="inquiry_entry_answer" id="inquiry_entry_answer" value="" />
<input type="hidden" name="branches" id="branchDatas" value="<?php echo htmlspecialchars($inquiryFilters) ?>" />
<input type="hidden" name="condition_title" id="condition_title" value="<?php echo isset($post['condition_title']) ? $post['condition_title']: "" ?>"/>
<input type="hidden" name="condition_no" id="condition_no" value="<?php echo isset($post['condition_no']) ? $post['condition_no']: "" ?>"/>
<style>
    .add.image.round.btn:before { 
        background: url(/assets/admin/css/img/icon-filling.svg) no-repeat;
    }
    .filter-conditions span{
        text-decoration: underline;
        cursor: pointer;
    }
    .filter-conditions.disabled span {
        color: #A1A4AA;
        text-decoration: unset;
    }
    .filter-conditions .inquiry-branch {
        color: #245BD6;
    }
    .maximums-wrapper {
        display: flex;
        align-items: center;
        margin-right: 0;
        float: right;
        gap: 12px;
        justify-content: flex-end;
    }
    .line-tab {
        padding-bottom: 0px;
    }
    .form-group-wrapper {
        padding: 12px;
        background-color: #F6F7F9;
    }
    .js-maximums {
        width: 50%;
    }
    .js-maximums .talkappi-dropdown-container.pulldown {
        max-width: none;
    }
    .js-goto-maximum::after {
        content: '';
        display: inline-block;
        width: 12px;
        height: 12px;
        background-image: url(/assets/common/images/icon_link.svg);
        background-position: center;
        background-repeat: no-repeat;
        background-size: contain;
        margin-left: 6px;
    }
    .js-csv-export {
        margin-left: 10px;
    }
    .icon-detail-box {
        left: 0;
    }
    .pressed {
        font-weight: bold !important;
    }
    .dropdown-menu {
		font-size: 12px;
	}
    .memo-mail {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2; /* 2行で省略 */
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: normal; /* 必須: 折り返しを許可 */
        word-break: break-word; /* 単語が長すぎても折り返す */
    }

</style>
<div class="edit-container">
    <!-- Page Content -->
    <div class="page-container">
        <div class="left-container">
            <!-- 検索条件 -->
            <?php if ($post['type'] == 'detail' && isset($post['result_id'])) {
                $buttons = $buttons + $buttons_fixed;
            ?>    
            <div style="display: flex; justify-content: space-between; align-items: flex-start; align-self: stretch; margin-bottom: 6px;">
                <div style="display: flex; width: 576px; align-items: flex-start; gap: 12px;">
                    <a href="/admininquiry/inquiryresult?id=<?php echo $inquiry_id; ?>&type=detail" class="btn-smaller btn-white" style="text-decoration: none; display: inline-flex; cursor: pointer; padding: 0; margin:0;">
                        <span style="padding: 0 12px; display: flex; align-items: center; white-space: nowrap;">
                            <img src="/assets/admin/css/img/icon-task.svg" height="12" width="12" style="margin-right: 6px;">
                            <?php echo __('admin.common.label.all_results'); ?>
                        </span>
                    </a>
                    <button id="copyUrlButton" class="btn-smaller btn-white" style="text-decoration: none; display: inline-flex; cursor: pointer; padding: 0; align-items: center; border: 1px solid #cccccc; border-radius: 5px; background-color: #ffffff; color: #000000; margin:0;">
                        <span style="padding: 0 12px; display: flex; align-items: center; white-space: nowrap;">
                            <img src="/assets/admin/css/img/icon-url-link.svg" height="12" width="12" style="margin-right: 6px;">
                            <?php echo __('admin.common.label.share'); ?>
                        </span>
                    </button>
                </div>
                <div style="display: flex; align-items: flex-start; gap: 12px;">
                    <?php if (isset($prev_result_id)): ?>
                        <a href="/admininquiry/inquiryresultdetail?id=<?php echo $prev_result_id; ?>" class="btn-smaller btn-white" style="text-decoration: none; display: inline-flex; cursor: pointer; padding: 0; margin:0;">
                    <?php else: ?>
                        <a class="btn-smaller btn-white button-disabled" style="text-decoration: none; display: inline-flex; cursor: pointer; padding: 0; margin:0; pointer-events: none; opacity: 0.5;">
                    <?php endif; ?>
                            <span style="padding: 0 12px 0 6px; display: flex; align-items: center; white-space: nowrap;">
                                <img src="/assets/admin/css/img/Icon-Arrow-previous_page_off.svg" height="12" width="12" style="margin-right: 6px;">
                                <?php echo __('admin.common.label.prev_result'); ?>
                            </span>
                        </a>
                    <?php if (isset($next_result_id)): ?>
                        <a href="/admininquiry/inquiryresultdetail?id=<?php echo $next_result_id; ?>" class="btn-smaller btn-white" style="text-decoration: none; display: inline-flex; cursor: pointer; padding: 0; margin:0;">
                    <?php else: ?>
                        <a class="btn-smaller btn-white button-disabled" style="text-decoration: none; display: inline-flex; cursor: pointer; padding: 0; margin:0; pointer-events: none; opacity: 0.5;">
                    <?php endif; ?>
                            <span style="padding: 0 6px 0 12px; display: flex; align-items: center; white-space: nowrap;">
                                <?php echo __('admin.common.label.next_result'); ?>
                                <img src="/assets/admin/css/img/Icon-Arrow-next_page.svg" height="12" width="12" style="margin-left: 6px;">
                            </span>
                        </a>
                </div>
            </div>
            <?php 
            }
            else { ?>
            <div class="form-body">
                <div style="margin:0 -15px 15px;display:flex;align-items:center;justify-content:space-between;;">
                    <div style="width:50%;max-width:100%;">
                        <div style="text-align:left;font-size:13px;font-weight:400;display:flex;gap:10px;align-items:center;">
                            <?php echo __('admin.inquiryresult.lable.inquiry_name') ?> <a href=" <?php echo $verify_url ?>" target="_blank" rel="noopener noreferrer"> <?php echo ($inquiry->inquiry_name); ?> </a>
                        </div>
                    </div>
                    <?php if (count($maximums) > 0) { ?>
                    <div class="maximums-wrapper"  style="width:50%;max-width:100%;">
                        <div class="talkappi-pulldown js-maximums" id="maximum_id" data-name="maximum_id" data-value="" data-source="{}"></div>
                        <div><a href="javascript:void(0);" class="js-goto-maximum"><?php echo __('admin.inquiryresult.label.to_usage_status') ?></a></div>
                    </div>
                    <?php } ?>
                    <div class="">
                        <?php 
                            $csv_type_texts = [];
                            foreach ($csv_type as $key => $value) {
                                $csv_type_texts[$key] = __('admin.inquiryresult.label.csv_type_cd_'.$key);
                            }
                        ?>
                        <div class="talkappi-pulldown js-csv-export" id="csv_type_cd" data-name="csv_type_cd" data-blank-text="<?php echo __('admin.common.button.csv_export') ?>" data-value="" data-source="<?php echo htmlspecialchars(json_encode($csv_type_texts, JSON_UNESCAPED_UNICODE)) ?>"></div>
                    </div>
                </div>
                <nav class="line-tab">
                    <ul class="">
                        <li class="<?php if ($post['type'] == 'total') echo ('active'); ?>"><a class="func-menu" href="/admininquiry/inquiryresult?type=total&id=<?php echo $inquiry_id ?>"><?php echo __('admin.inquiryresult.label.type_total'); ?></a></li>
                        <li class="<?php if ($post['type'] == 'detail') echo ('active'); ?>"><a class="func-menu" href="/admininquiry/inquiryresult?type=detail&id=<?php echo $inquiry_id ?>"><?php echo __('admin.inquiryresult.label.type_detail'); ?></a></li>
                        <?php if ($inquiry_id == 2260040003 || $inquiry_id == 2260060004) { ?>
                        <li class="">
                            <label style="color:#245BD6;" for="file-csv">CSVインポート</label>
                            <input type="file" id="file-csv" class="js-file-input" style="display:none;height:0 !important;" accept="*">
                            <input type="hidden" class="js-csv-file" name="csv_file" value="" />
                            <input type="hidden" class="js-csv-file-name" name="csv_file_name" value="" />
                        </li>
                        <li class="">
                            <a class="js-delete-import">インポート削除</a>
                        </li>
                        <?php } ?>
                    </ul>
                </nav>
                <div class="form-group-wrapper">
                    <div class="form-group" style="margin:0;display:flex;">
                        <div class="" style="width:120px;margin-right:10px;">
                            <?php 
                                $lang_cd_list_after = [];
                                foreach ($lang_cd_list as $key => $value) {
                                    if ($key == '') {
                                        $lang_cd_list_after[$key] = __('admin.inquiryresult.label.language_default');
                                    } else {
                                        $lang_cd_list_after[$key] = $value;
                                    }
                                }
                            ?>
                            <?php echo Form::select('lang_cd', $lang_cd_list_after, $post['lang_cd'], array('id' => 'lang_cd', 'class' => 'form-control')) ?>                    
                        </div>     
                        <div class="" style="display:flex;align-items:center;width:220px;margin-right:10px;">
                            <div class="talkappi-datepicker-range">
                                <input name="start_date" id="start_date" value="<?php echo(substr($post['start_date'], 0, 10)) ?>"/>
                                <p>〜</p>
                                <input name="end_date" id="end_date" value="<?php echo(substr($post['end_date'], 0, 10)) ?>"/>
                            </div>
                        </div>
                        <div class="" style="width:210px;margin-right:10px;">
                            <select class="status-select form-control" multiple>
                            <?php 
                            foreach($status as $k=>$v) {
                                echo('<option value="' . $k . '">' . $v .'</option>');
                            }
                            ?>
                            </select>
                        </div>
                        <div class="" style="width:210px;margin-right:10px;">
                            <select class="bs-select form-control" multiple>
                            <?php 
                            echo('<option value="00">' . __('admin.service.dropdown.no_status') .'</option>');
                            foreach($buttons as $k=>$v) {
                                echo('<option value="' . $k . '">' . $v .'</option>');
                            }
                            foreach($buttons_fixed as $k=>$v) {
                                echo('<option value="' . $k . '">' . $v .'</option>');
                            }
                            $buttons = $buttons + $buttons_fixed;
                            ?>
                            </select>
                        </div>
                        <?php if ($versions_count == 1) { ?>
                        <button type="button" id="searchButton" class="btn-smaller btn-blue"><span class="icon-filter"></span><?php echo __('admin.common.label.narrowdown'); ?></button>
                        <?php } ?>
                    </div>
                    <?php if ($versions_count > 1) { ?>
                    <!-- 改版がある場合 -->
                    <div class="form-group" style="margin:12px 0 0 0;display:flex;">
                        <?php if ($post['type'] == 'detail') { ?>
                        <div class="js-checkbox-container" style="height:28px;display:flex;align-items:center;margin-right:10px;">
                            <span><?php echo __('inquiry.common.label.all_versions') ?></span>
                            <div class="talkappi-switch js-show-allversion" style="margin-left:5px;" data-name="show_allversion" data-value="<?php echo $post["show_allversion"] ? 1 : 0 ?>"></div>
                        </div>
                        <?php } ?>
                        <div class="current-version-pulldown-wrapper" style="margin-right:10px; display:<?php if ($post['show_allversion']) { echo 'none'; } else { echo 'block'; } ?>">
                            <div style="width:300px;" class="talkappi-pulldown js-current-version" data-name="current_version" data-value="<?php if ($post['current_version'] == NULL) { echo $inquiry_id; } else { echo $post['current_version']; } ?>" data-source="<?php echo htmlspecialchars(json_encode($all_version_options, JSON_UNESCAPED_UNICODE)) ?>"></div>
                        </div>
                        <div class="js-checkbox-container" style="height:28px;display:flex;align-items:center;margin-right:10px;">
                        <span><?php echo __('admin.inquiryresult.label.deleted_display'); ?></span>
                        <div class="talkappi-switch" style="margin-left:5px;" data-name="is_deleted" data-value="<?php echo $post["is_deleted"]?>"></div>
                        </div>
                        <div class="js-checkbox-container" style="height:28px;display:flex;align-items:center;">
                        <span><?php echo __('admin.inquiryresult.label.spam_display'); ?></span>
                        <div class="talkappi-switch" style="margin-left:5px;" data-name="is_blocked" data-value="<?php echo $post["is_blocked"]?>"></div>
                        </div>
                        <?php if (isset($inquiry_setting['result_refresh_intval'])) { ?>
                        <div class="" style="height:28px;display:flex;align-items:center;margin-left:20px;">
                        <span><?php echo __('admin.inquiryresult.label.auto_refresh'); ?></span>
				        <div class="talkappi-radio js-auto-refresh" data-name="auto_refresh" data-value='0' data-source='{"0":"OFF", "1":"ON"}'></div>
                        </div>
                        <?php } ?>
                        <button type="button" id="searchButton" class="btn-smaller btn-blue"><span class="icon-filter"></span><?php echo __('admin.common.label.narrowdown'); ?></button>
                    </div>
                    <?php } ?>
                    <?php if ($versions_count == 1 || $post['show_allversion'] == false) { ?>
                    <div class="form-group" style="margin:12px 0px 0px;display:flex;<?php if ($_user->role_cd == '74') echo('display:none;') ?>">
                        <div class="form-control flex-x-between filter-conditions disabled" id="filter-conditions" style="max-width:350px;margin-right:10px; ">
                            <span class="js-set-conditions"></span>
                            <div class="inquiry-branch pointer js-reset-conditions"><?php echo __('admin.common.button.clear'); ?></div>
                        </div>
                        <?php if ($versions_count == 1) { ?>
                        <div class="js-checkbox-container" style="height:28px;display:flex;align-items:center;margin-right:10px;">
                        <span><?php echo __('admin.inquiryresult.label.deleted_display'); ?></span>
                        <div class="talkappi-switch" style="margin-left:5px;" data-name="is_deleted" data-value="<?php echo $post["is_deleted"]?>"></div>
                        </div>                            
                        <div class="js-checkbox-container" style="height:28px;display:flex;align-items:center;">
                        <span><?php echo __('admin.inquiryresult.label.spam_display'); ?></span>
                        <div class="talkappi-switch" style="margin-left:5px;" data-name="is_blocked" data-value="<?php echo $post["is_blocked"]?>"></div>
                        </div>
                        <?php if (isset($inquiry_setting['result_refresh_intval'])) { ?>
                        <div class="" style="height:28px;display:flex;align-items:center;margin-left:20px;">
                        <span><?php echo __('admin.inquiryresult.label.auto_refresh'); ?></span>
				        <div class="talkappi-radio js-auto-refresh" data-name="auto_refresh" data-value='0' data-source='{"0":"OFF", "1":"ON"}'></div>
                        </div>
                        <?php } ?>
                        <?php } ?>
                    </div>
                    <?php 
                    $style = ($post['lang_cd'] && $_user->role_cd != '74') ? 'display:flex' : 'display:none';
                    echo "<div class='form-group js-saved-result-conditions' style='margin:12px 0px 0px; {$style}'>"
                    ?>
                        <div style="display: flex;flex-wrap: wrap;gap: 0.5em;align-items: center;">
                        <span><?php echo __('admin.inquiryresult.label.saved_search_conditions'); ?> : </span>
                        <?php 
                        $isLangCdMatched = false;
                        if (!empty($inquiry_result_condtions)) {
                            foreach ($inquiry_result_condtions as $condition) {
                                $conditionData = json_decode($condition['condition_data'], true);
                                $title = $conditionData['title'] ? $conditionData['title'] : __('admin.inquiryresult.label.untitled_condition');
                                $conditions = isset($conditionData['conditions']) ? $conditionData['conditions'] : '';
                                $no = $condition['no'];
                                $langCd = $condition['lang_cd'];
                            
                                // 条件が選択されている場合、一致するボタンをハイライトする
                                $selectedConditionNo = isset($post['condition_no']) ? $post['condition_no'] : null;
                                $btnClass = ($no == $selectedConditionNo) ? 'light-blue' : 'light-gray';
                                // 言語コードの一致する条件だけを表示する
                                if ($langCd === $post['lang_cd']) {
                                    $isLangCdMatched = true;
                                    $style = '';
                                } else {
                                    $style = 'display:none;';
                                }
                                echo "<label class='btn round {$btnClass} js-inquiry-result-condition' data-no='{$no}' data-lang='{$langCd}' data-conditions='{$conditions}' style='{$style}'>{$title}</label>";
                            }
                        }
                        $spanStyle = $isLangCdMatched ? 'display:none;' : '';
                        echo "<span class='js-inquiry-result-condition-message' style='{$spanStyle}'>";
                        echo __('admin.inquiryresult.label.no_saved_search_conditions');
                        echo "</span>";
                        ?>
                        </div>
                    </div>
                    <?php } ?>
                </div>
            </div>
            <?php } ?>
            
            <?php
            $index = 1;
            ?>
            <?php if ($post['type'] == 'total') { ?>
                <div id="result-total">
                    <?php
                    foreach ($section as $no) {
                    	$entry = $inquiry_result_total[$no];
                        //if ($post['inquiry_entry'] != '' && $post['inquiry_entry'] != $no) continue;
                        echo ('<div style="width:75%"><span class="result-title">' . strip_tags($entry['title']) . '</span>');
                        echo ('<span class="label inquiry-kind-label" style="margin-left:10px;">' . $_codes['10'][$entry['entry_type_cd']] . '</span>');
                        if ($entry['required'] == 1) echo ('<span class="label inquiry-required-label" style="margin-left:10px;">' . __('inquiry.common.label.required') . '</span>');
                        if ($entry['entry_type_cd'] == 'fup') echo('<button type="button" style="float:right;" class="btn blue fup-download" entry_no="' . $no . '">' . __('admin.inquiryresult.label.fup_download') . '</button>');
                        echo ('</div>');
                        if ($entry['entry_type_cd'] == 'txt' || $entry['entry_type_cd'] == 'txa' || $entry['entry_type_cd'] == 'fup' || $entry['entry_type_cd'] == 'txq') {
                            if ($entry['entry_type_cd'] == 'txt' || $entry['entry_type_cd'] == 'txa' || $entry['entry_type_cd'] == 'txq') {
                                echo ('<table class="table paginateTbl' . $index . ' table-striped table-bordered table-hover" style="width:75%; margin-top:15px;">');
                                $total = 0;
                                foreach ($entry['entry_count'] as $ec) {
                                    if ($ec['answer'] != '') {
                                        $total = $total + 1;
                                    }
                                }
                                echo ('<thead><tr><th style="width: 300px;">' . __('admin.common.label.member_id') . '</th><th>' . __('admin.common.label.answer') . '(' . $total . ')</th></tr></thead>');
                                echo ('<tbody class="paginateBody' . $index . '">');
                                foreach ($entry['entry_count'] as $ec) {
                                    if ($ec['answer'] != '') {
                                        echo ('<tr>');
                                        echo ('<td>' . $ec['uid'] . '</td>');
                                        echo ('<td>' . $ec['answer'] . '</td>');
                                        echo ('</tr>');
                                    }
                                }
                                echo ('</tbody></table>');
                            } else if ($entry['entry_type_cd'] == 'fup') {
                                echo ('<table class="table paginateTbl' . $index . ' table-striped table-bordered table-hover" style="width:75%; margin-top:15px;">');
                                $total = 0;
                                foreach ($entry['entry_count'] as $ec) {
                                    if ($ec['answer'] != '') {
                                        $total = $total + 1;
                                    }
                                }
                                echo ('<thead><tr><th style="width: 300px;">UID</th><th>' . __('admin.common.label.file') . '(' . $total . ')</th></tr></thead>');
                                echo ('<tbody class="paginateBody' . $index . '">');
                                foreach ($entry['entry_count'] as $ec) {
                                    if ($ec['answer'] != '') {
                                        echo ('<tr>');
                                        echo ('<td>' . $ec['uid'] . '</td>');
                                        echo ('<td>');
                                        $file_info = json_decode($ec['answer'], true);
                                        if ($file_info != null) {
                                            foreach ($file_info as $f) {
                                                $parts = explode('/', $f['url']);
                                                echo ('<a href="' . $f['url'] . '" target="_blank" rel="noopener noreferrer">' . $parts[count($parts) - 1] . '</a>');
                                            }
                                        }
                                        echo ('</td>');
                                        echo ('</tr>');
                                    }
                                }
                                echo ('</tbody></table>');
                            }
                            $index++;
                        }
                        else if ($entry['entry_type_cd'] == 'spl') {
                            echo ('<table class="table paginateTbl' . $index . ' table-striped table-bordered table-hover" style="width:75%; margin-top:15px;">');
                            $total = 0;
                            foreach ($entry['entry_count'] as $ec) {
                                if ($ec['answer'] != '') {
                                    $total = $total + 1;
                                }
                            }
                            echo ('<thead><tr><th style="width: 300px;">UID</th><th>' . __('admin.common.label.answer') . '(' . $total . ')</th></tr></thead>');
                            echo ('<tbody class="paginateBody' . $index . '">');
                            foreach ($entry['entry_count'] as $ec) {
                                if ($ec['answer'] != '') {
                                    echo ('<tr>');
                                    echo ('<td>' . $ec['uid'] . '</td>');
                                    echo ('<td>' . $ec['answer'] . '</td>');
                                    echo ('</tr>');
                                }
                            }
                            echo ('</tbody></table>');
                            $index++;
                        } else {
                            $total = 0;
                            foreach ($entry['entry_count'] as $ec) {
                                $total = $total + $ec['count'];
                                //array_push($titles, $ec['title']);   compile error
                            }
                            echo ('<table class="table table-striped table-bordered table-hover" style="width:75%; margin-top:15px;">');
                            echo ('<thead><tr><th style="width: 100px;">' . __('admin.common.label.answer') . '</th><th style="width: 60px;">' . __('admin.inquiryresult.label.ratio') . '</th><th style="width: 60px;">' . __('inquiry.index.label.count') . '(' . $total . ')</th></tr></thead>');
                            echo ('<tbody>');
                            foreach ($entry['entry_count'] as $ec) {
                                echo ('<tr>');
                                if ($ec['title'] == __('inquiry.common.label.other'). "*..." || $ec['title'] == __('inquiry.common.label.other'). "...") {
                                    echo ('<td>' . __('inquiry.common.label.other') . '</td>');
                                } else {
                                    echo ('<td>' . $ec['title'] . '</td>');
                                }
                                if ($total > 0) {
                                    echo ('<td style="text-align:center;">' . round($ec['count'] * 100 / $total, 1) . '%</td>');
                                } else {
                                    echo ('<td style="text-align:center;">-%</td>');
                                }
                                echo ('<td style="text-align:center;">' . $ec['count'] . '</td>');
                                echo ('</tr>');
                            }
                            echo ('</tbody>');
                            echo ('</table>');

                            if ($total > 0) {
                                echo ('<div class="graph-btn-wrapper">');
                                echo ('<div class="graph-btn graph-btn-selected js-table-toggle">' . __('admin.inquiryresult.label.table') . '</div>');
                                    echo ('<input type="radio" class="hide btn-check" name="options" id="option' . $no . '-1" autocomplete="off">');
                                    echo ('<label class="btn btn-secondary graph-btn" for="option' . $no . '-1">' . __('admin.inquiryresult.label.pie_chart') . '</label>');
                                    echo ('<input type="radio" class="hide btn-check" name="options" id="option' . $no . '-2" autocomplete="off">');
                                    echo ('<label class="btn btn-secondary graph-btn"  for="option' . $no . '-2">' . __('admin.inquiryresult.label.vertical_bar_chart') . '</label>');
                                    echo ('<input type="radio" class=" hide btn-check" name="options" id="option' . $no . '-3" autocomplete="off">');
                                    echo ('<label class="btn btn-secondary graph-btn" for="option' . $no . '-3"> ' . __('admin.inquiryresult.label.horizontal_bar_chart') . ' </label>');
                                    echo ('<input type="radio" class=" hide btn-check" name="options" id="option' . $no . '-4" autocomplete="off">');
                                    echo ('<label class="btn btn-secondary graph-btn" for="option' . $no . '-4">' . __('admin.inquiryresult.label.other_input') . '</label>');
                                    echo ('</div>');
                                    echo ('<div id="piechart' . $no  . '"></div>');
                                    echo ('<div id="columnchart' . $no  . '"></div>');
                                    echo ('<div id="barchart' . $no  . '"></div>');
                            }
                            echo('<div class id="otherTable'.$no.'">');
                            echo ('<table class="table table-striped  table-bordered table-hover paginateTbl-other'.$no.'" style="width:75%; margin-top:15px; position:relative">');
                            echo ('<thead><tr><th style="width: 100px;">UID</th><th style="width: 120px;">' . __('admin.inquiryresult.label.other') . '</th></tr></thead>');
                            echo ('<tbody class="paginateBody-other' . $no . '">');
                            foreach ($inquiry_result_entry_dict as $eds) {
                                foreach ($eds as $ed) {
                                    if ($ed["no"] == $no) {
                                        if (strpos($ed["entry_data"], __('inquiry.common.label.other') . '(') !== false) {
                                            echo ('<tr>');
                                            echo ('<td>' . $ed["member_id"] . '</td>');
                                            $text = $ed["entry_data"];
                                            $pattern = "{\((.*)\)}";
                                            preg_match($pattern, $text, $matchedText);
                                            echo ('<td style="text-align:center;">' . $matchedText[1] . '</td>');
                                            echo ('</tr>');
                                        }
                                    }
                                }
                            }
                            echo ('</tbody>');
                            echo ('</table>');
                            echo ('</div>');
                        }
                        $entryLength = count($inquiry_entry);
                        if ($entryLength > $no) {
                            echo ("<div class='q-separator'></div>");
                        }
                    }
                    ?>
                </div>
            <?php } else {
            	$inquiry_entry_arr = [];
            	foreach($inquiry_entry as $ie) {
            		$inquiry_entry_arr[$ie->no] = $ie;
            	}
            	?>
                <div id="result-detail">
                    <div class="flex-x-between" style="align-items:center;margin-top:-15px;margin-bottom:10px;">
                        <?php 
                        if ($total > 0) echo('<p style="color:green;font-size:14px;font-weight:bold;">' . __('admin.inquiryresult.label.sales_amount') . '￥' . number_format($total) . '</p>');
                    ?>
                    </div>
                    <table class="table table-striped table-bordered table-hover js-data-table">
                        <thead>
                            <tr>
                                <th style="width: 160px;"><?php echo __('admin.common.label.reception'); ?></th>
                                <th style="width: 100px;"><?php echo __('admin.common.label.user'); ?></th>
                                <th><?php echo __('admin.common.label.reception_detail'); ?></th>
                                <th style="width: 80px;"><?php echo __('admin.inquiryresult.label.contact'); ?></th>
                                <th style="width: 80px;"><?php echo __('admin.inquiryresult.label.response_status'); ?></th>
                                <th><?php echo __('admin.inquiryresult.label.response_history'); ?></th>
                            </tr>
                        </thead>
                        <tbody class="js-tbody">
                            <?php
                            if ($post['support_type_cd'] == '') {
                                $support_type_list = null;
                            }
                            else {
                                $support_type_list = explode(',', $post['support_type_cd']);
                            }
                            if ($post['status_cd'] == '') {
                                $status_list = null;
                            }
                            else {
                                $status_list = explode(',', $post['status_cd']);
                            }
                            $result_base_id = array_column($inquiry_result, 'base_id');
                            foreach ($inquiry_result as $result) {
                                // if ($result['status_cd'] == '00') continue;
                                if ($result['status_cd'] == '03' && $result['upd_user'] === '0') continue;
                                if ($status_list) {
                                    if (!in_array($result['status_cd'], $status_list)) continue;
                                }
                                if (isset($inquiry_result_support_dict[$result['id']])) {
                                    $inquiry_result_support = $inquiry_result_support_dict[$result['id']];
                                }
                                else {
                                    $inquiry_result_support = [];
                                }
                                $support_type_cd = '00';
                                foreach($inquiry_result_support as $support) {
                                    if ($support['support_type_cd'] != '') $support_type_cd = $support['support_type_cd'];
                                }
                                if ($support_type_list) {
                                    if (!in_array($support_type_cd, $support_type_list)) continue;
                                }
                                $result_data = json_decode($result['result_data'], true);
                                if (isset($result_data['error_code'])) continue;
                                $row = View::factory('admin/inquiry/inquiryresultrow');
                                $row->result = $result;
                                $row->inquiry = $inquiry;
                                $row->post = $post;
                                $row->inquiry_result_support = $inquiry_result_support;
                                $row->buttons = $buttons;
                                $row->labelcolor = $labelcolor;
                                $row->inquiry_data = $inquiry_data;
                                $row->result_base_id = $result_base_id;
                                echo $row;
                            } ?>
                        </tbody>
                    </table>
                </div>
            <?php } ?>
        </div>
    </div>
</div>
<?php echo($memo_box);?>
<?php
    if ($post['type'] == 'total') {
        foreach ($inquiry_result_total as &$each) {
            foreach ($each["entry_count"] as &$eachOpt) {
                if (array_key_exists('title', $eachOpt) && ($eachOpt["title"] == __('inquiry.common.label.other') . "*..." || $eachOpt["title"] == __('inquiry.common.label.other') . "...")) {
                    $eachOpt["title"] = __('inquiry.common.label.other');
                }
            }
            unset($eachOpt);
        }
        unset($each);
        $js_result_total = [];
        foreach ($section as $no) {
            $inquiry_result_total[$no]['no'] = $no;
            array_push($js_result_total, $inquiry_result_total[$no]);
        };
        $js_result_total_str = json_encode($js_result_total, JSON_UNESCAPED_UNICODE);
        if ($js_result_total_str == '') $js_result_total_str = '[]';
    }
?>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/1.12.4/jquery.min.js"></script>
<script type="text/javascript" src="/assets/common/paginathing.js"></script>

<script type="text/javascript">
    const _inquiry_branch_entries = <?php echo json_encode($inquiry_branch_entries, JSON_UNESCAPED_UNICODE) ?>;
    const _maximum_category_price = <?php echo json_encode($maximum_category_price, JSON_UNESCAPED_UNICODE) ?>;
    const _result_total = <?php if(isset($js_result_total_str)) { echo $js_result_total_str; } else { echo '[]'; } ?>;
    jQuery(document).ready(function($) {
        $("tbody[class^=paginateBody]").each(function(i, v) {
            if ($(`.paginateBody${i + 1}`).children().length > 0) {
                $(`.paginateBody${i + 1}`).paginathing({
                    perPage: 10,
                    insertAfter: `.paginateTbl${i + 1}`,
                    pageNumbers: true
                });
            };
        });
        $("tbody[class^=paginateBody-other]").each(function(index, value) {
            if ($(`.paginateBody-other${index + 1}`).children().length > 0) {
                $(`.paginateBody-other${index + 1}`).paginathing({
                    perPage: 10,
                    insertAfter: `.paginateTbl-other${index + 1}`,
                    pageNumbers: true
                });
            };
        });
    });
</script>

<!-- 支払金額モーダル -->
<div class="modal-image-container js-modal-amount-container" style="display: none;height: 176px; min-height: 176px;">
	<div class="relative" style="min-height: 100%;">
		<!-- タイトル -->
		<div class="flexbox relative">
			<h4 class="font-standard font-size-v5 font-family-v2" style="margin: 0;"><?php echo __('admin.inquiryresult.dialog.amount.title') ?></h4>
			<span class="js-close-modal icon-cancel-large survey-position-absolute-v2 pointer"></span>
		</div>
        <!-- 金額 -->
        <div class="survey-space-top-2 flexbox-x-axis">
            <div class="width-96"><?php echo __('admin.inquiryresult.dialog.amount.label') ?></div>
            <input type="number" class="survey-period-date-container js-amount-input" style="width:100px;padding:0 12px;" placeholder="">
        </div>
		<!-- SUBMIT -->
		<div class="submit-btn-container modal-image-button">
			<div class="flexbox-center btn-smaller btn-blue js-save-amount" style="margin:0 12px 0 0;" type="button">OK</div>
			<div class="flexbox-center btn-smaller btn-white js-close-modal" type="button"><?php echo __('admin.common.button.cancel') ?></div>
		</div>
	</div>
</div>
<script src="/assets/common/react/components/blocks/ResultSupportMailModal.bundle.js"></script>
