<input type="hidden" id="act" name="act" value="" />
<input type="hidden" id="item_div" name="item_div" value="<?php echo $item_div ?>" />
<input type="hidden" id="class_div" name="class_div" value="<?php echo $div_item_class ?>" />
<input type="hidden" id="class_cd_cond" name="class_cd_cond" value="<?php echo $post['class_cd_cond'] ?>" />
<input type="hidden" id="inquiry_id" name="inquiry_id" value="" />
<input type="hidden" id="from_inquiry_id" name="from_inquiry_id" value="" />

<script>
    const _template_bot_id = <?php echo $template_bot_id ?>;
    const _inquiry_div = <?php echo $inquiry_div ?>;
</script>

<style>
.new-inquiry {
    font-size: 10px;
    padding: 4px;
    margin-left:4px;
    background-color: lightgoldenrodyellow;
    cursor: pointer;
    width: fit-content;
}

.new-inquiry.time {
    background-color: lightcyan;
}

.new-inquiry.maximum {
    background-color: #cff2d7;
    padding: 2px;
    margin-top:4px;
    margin-left:0px;
}

.new-inquiry.payment {
    background-color: #cff2d7;
    padding: 2px;
    margin-top:4px;
    margin-left:0px;
}

.inquiry-maximum {
    font-size:11px;
    color: #555;
    cursor: pointer;
}

.inquiry-maximum.disabled {
    font-size:11px;
    color: #aaa;
    cursor: default;
}
</style>
<!-- Page Content -->
<div class="content-container light-gray">
    <div class="form-group">
    <label class="control-label col-md-1" <?php if ($json_next_inquiry_setting['user_in_charge_required'] != 1) echo('style="display:none;"')?>><?php echo __('admin.common.label.person_in_charge') ?></label>
        <div class="col-md-3" <?php if ($json_next_inquiry_setting['user_in_charge_required'] != 1) echo('style="display:none;"')?>>
        <?php 
            $user_list_after = [];
            foreach($user_list as $k => $v) {
                if ($v == '- 担当者 -') {
                    $user_list_after[$k] = '- ' . __('admin.inquirys.label.person_in_charge') . ' -';
                } else {
                    $user_list_after[$k] = $v;
                }
            }
            $template_list_after = [];
            foreach ($template_list as $k => $v) {
                if ($v == '- テンプレート -') {
                    $template_list_after[$k] = __('admin.inquirys.label.template_list_default');
                } else {
                    $template_list_after[$k] = $v;
                }
            }
        ?>
        <?php echo Form::select('user_in_charge', $user_list_after, $post['user_in_charge'], array('id'=>'user_in_charge','class'=>'form-control select2me'))?>
        </div>
        <label class="control-label col-md-1" style="white-space: nowrap;"><?php echo __('admin.common.label.template') ?></label>
        <div class="col-md-3">
            <?php echo Form::select('template_cd', $template_list_after, $post['template_cd'], array('id'=>'template_cd','class'=>'form-control'))?>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-md-1"><?php echo __('admin.common.label.classification_tags') ?></label>
		<div class="col-md-3">
			<select name="class_cd" class="bs-select form-control" multiple>
			<?php foreach($code_div_dict as $k=>$v) {
				echo('<option value="' . $k . '">' . $v .'</option>');
			}?>
			</select>
		</div>   
        <label class="control-label col-md-1"><?php echo __('admin.common.label.period') ?></label>          
        <div class="col-md-3">
            <div class="talkappi-datepicker-range">
                <input name="start_date" value="<?php echo ($post['start_date']) ?>"/><p>〜</p>
                <input name="end_date" value="<?php echo ($post['end_date']) ?>"/>
            </div>
        </div>
        <div class="col-md-1">
            <span class="btn-smaller btn-yellow js-search"><i class="fa fa-search mr10"></i><?php echo __('admin.common.button.search') ?></span>
        </div>	
    </div>
</div>

<?php 
	$model = new Model_Adminmodel();
    $authority = $model->check_authority('admininquiry/inquiryentry');
?>

<div class="content-container" style="padding-left: 0;">
    <div class="flex-x-between">
        <div></div>
        <?php if ($authority) { 
            if ($_user->role_cd == '99') { ?>
                <div class="talkappi-pulldown js-new-inquiry" data-type="menu" data-action="<?php echo __('admin.common.button.create_new') ?>" style="width:160px;" data-value="" data-source='{"1":"<?php echo __('admin.inquirys.label.copyfromtemplate') ?>","2":"<?php echo __('admin.inquirys.label.newinquiry') ?>"}'>
                </div>    
            <?php } 
            else { ?>
                <span class="btn-smaller btn-blue js-new-inquiry-button">
                    <span class="icon-add-white"></span>
                    <?php echo __('admin.common.button.create_new') ?>
                </span>
        <?php 
            }
        }?>
    </div>
</div>

<div class="content-container white">
    <table class="table table-striped table-bordered table-hover js-data-table">
        <thead>
            <tr>
                <th style="width: 60px;">ID</th>
                <th><?php echo __('admin.inquirys.label.inquiry_name') ?></th>
                <th style="width: 85px;"><?php echo __('admin.inquirys.label.period') ?></th>
                <th><?php echo __('admin.inquirys.label.class_cd') ?></th>
                <th style="width: 72px;"><?php echo __('admin.inquirys.label.answers_count') ?><br/><?php echo __('admin.inquirys.label.awaiting_response') ?></th>
                <th><?php echo __('admin.inquirys.label.access_count') ?><br><?php echo __('admin.inquirys.label.conversion_rate') ?></th>
                <th <?php if ($json_next_inquiry_setting['user_in_charge_required'] != 1) echo('style="display:none;"')?>><?php echo __('admin.inquirys.label.person_in_charge') ?></th>
                <th style="width:140px;"><?php echo __('admin.inquirys.label.last_update') ?></th>
                <?php if ($authority) { ?><th style="width: 100px;"><?php echo __('admin.common.label.operation') ?></th> <?php } ?>
            </tr>
        </thead>
        <tbody>
            <?php
            foreach ($items as $item) {
                if ($item['scene_cd'] == '') $item['scene_cd'] =  $_bot->facility_cd;
                if ($item['renew_time'] == null) {
                    $public_url = $verify_url . '?f=' . $item['scene_cd'] . '&id=' . $item['inquiry_id'];
                }
                else {
                    $public_url = $verify_url . '?f=' . $item['scene_cd'] . '&id=' . $item['inquiry_cd'];
                }
            ?>
                <tr class="gradeX odd" role="row">
                    <!-- コード -->
                    <td data-order="<?php echo ($item['inquiry_id']) ?>">
                        <?php if ($item['base_ver_id'] != null) echo ('<a href="inquiry?id=' . $item['base_ver_id'] . '"><span class="badge badge-warning">' . $item['base_ver_id'] . __('admin.inquirys.label.revision') . '</span></a><br>'); ?>
                        <?php
                        echo ($item['inquiry_id'] . '<br>');
                        if ($item['present'] != null) echo ('<span class="badge badge-success">' . __('admin.inquirys.label.has_coupon') . '</span>');
                        ?><br />
                    </td>
                    <!-- 問合せフォーム名 -->
                    <td>
                        <?php if ($authority) { ?>
                        <a class="link-animate" href="/<?php echo $_path?>/inquiry?id=<?php echo ($item['inquiry_id']) ?>"><?php echo($item['inquiry_name']) ?></a>
                        <?php } else { 
                            echo($item['inquiry_name']); 
                        } ?>
                        <?php 
                            /*
                            if ($_user->role_cd == '99') {
                                if ($item['renew_time'] == null) {
                                    echo('<span class="new-inquiry js-switch-new-inquiry" data-inquiry_id="' . $item['inquiry_id'] . '">※新版に</span>');
                                }
                                else {
                                    echo('<span class="new-inquiry time">※' . $item['renew_time'] . '</span>');
                                    echo('<span class="new-inquiry time js-switch-old-inquiry" data-inquiry_id="' . $item['inquiry_id'] . '">※旧版に戻る</span>');
                                }
                            }
                            */
                            if (isset($entries[$item['inquiry_id']])) {
                                if (isset($entries[$item['inquiry_id']]['maximums'])) {
                                    echo('<br><span class="new-inquiry maximum">枠あり</span><br>');
                                    foreach($entries[$item['inquiry_id']]['maximums'] as $e) {
                                        if (isset($maximums[$e])) {
                                            echo('<p class="inquiry-maximum js-maximum" data-maximum_id="' . $e . '">' . $maximums[$e] . '</p>');
                                        }
                                        else {
                                            echo('<p class="inquiry-maximum disabled">' . $e . '(削除済み)</p>');
                                        }
                                    }
                                }
                            }
                            if (isset($entries[$item['inquiry_id']])) {
                                if (isset($entries[$item['inquiry_id']]['payment'])) {
                                    echo('<br>');
                                    if ($_user->role_cd == '99') {
                                        $title = 'title="' . htmlspecialchars(json_encode($entries[$item['inquiry_id']]['payment'])) . '"';
                                    }
                                    else {
                                        $title = '';
                                    }
                                    echo('<p class="new-inquiry payment" ' . $title . '>' . '決済あり' . '</p>');
                                }
                            }
                            $inquiry_data = json_decode($item['inquiry_data'], true);
                            if (isset($inquiry_data['cancelable']) && $inquiry_data['cancelable'] == 1) {
                                echo('<p class="new-inquiry payment">キャンセル可能' . '</p>');
                            }
                            if (isset($inquiry_data['modifiable']) && $inquiry_data['modifiable'] == 1) {
                                echo('<p class="new-inquiry payment">変更可能' . '</p>');
                            }
                        ?>
                    </td>
                    <!-- 実施時間 -->
                    <td style="text-align:left;">
                        <?php
                            if ($item['start_date'] != null) {
                                echo (substr($item['start_date'], 0, 10));
                            }
                            echo('<br>～<br>');
                            if ($item['end_date'] != null) {
                                echo (substr($item['end_date'], 0, 10));
                            }
                         ?>
                    </td>
                    <!-- 分類タグ -->
                    <td>
                        <?php 
                        foreach(explode(' ', $item['class_cd']) as $class_cd) {
                            if ($class_cd !='') {
                                if (array_key_exists($class_cd, $code_div_dict)) {
                                    echo('<span class="badge badge-success category-tag">' . $code_div_dict[$class_cd] . ' </span>');
                                }
                                else {
                                    echo('<span class="badge badge-success category-tag">' . __('admin.common.label.unknown') . ' </span>');
                                }
                            }
                        }
                        ?>
                    </td>
                    <!-- 回答件数 -->
                    <td style="text-align:center;" data-order="<?php if (isset($results[$item['inquiry_id']])) {echo ($results[$item['inquiry_id']]);} else {echo 0;} ?>">
                    <?php 
                        $answered = 0;
                        if (array_key_exists($item['inquiry_id'], $results)) {
                            $answered = $results[$item['inquiry_id']]; ?>
                            <a href="/<?php echo $_path?>/inquiryresult?type=detail&id=<?php echo ($item['inquiry_id']) ?>">
                                <span class="btn round"><?php echo ($results[$item['inquiry_id']]) ?></span>
                            </a>
                        <?php }?>
                        <br/>
                        <?php if (array_key_exists($item['inquiry_id'], $unsupports) && $unsupports[$item['inquiry_id']] > 0) {?>
                            <a href="/<?php echo $_path?>/inquiryresult?type=detail&id=<?php echo ($item['inquiry_id']) ?>">
                                <span class="btn round light-red" style="margin-top:5px;"><?php echo ($unsupports[$item['inquiry_id']]) ?></span>
                            </a>
                        <?php }?>
                    </td>
                    <!-- PV数・CVR -->
                    <td style="text-align:center;" data-order="<?php if (isset($access_count[$item['inquiry_id']])) {echo ($access_count[$item['inquiry_id']]);} else {echo 0;} ?>">
                        <?php if (array_key_exists($item['inquiry_id'], $access_count) && $access_count[$item['inquiry_id']] > 0) {
                            echo($access_count[$item['inquiry_id']] . '<br>');
                            echo('<span style="color:blue;">' . round(intval($answered) * 100 / $access_count[$item['inquiry_id']], 2) . '%</span>');
                        }?>
                    </td>
                    <!-- 担当者 -->
                    <td <?php if ($json_next_inquiry_setting['user_in_charge_required'] != 1) echo('style="display:none;"')?>>
                        <?php echo ($item['user_name']) ?>
                    </td>
                    <!-- 最終更新 -->
                    <td class="">
                        <span class="">
                        <?php echo($item['upd_time']) ?><br>
                        <?php echo($item['upd_user_name']) ?>
                        </span>
                    </td>
                    <!-- 詳細 -->
                    <?php if ($authority) { ?>
                    <td>
                        <a class="link-animate" href="/<?php echo $_path?>/inquiryentry?id=<?php echo ($item['inquiry_id']) ?>"><div style="margin-top: 2px;" class="btn round image edit js-memo"><?php echo __('admin.common.button.modify') ?></div></a>
                        <div style="margin-top: 2px;" class="btn round image copy js-copy" data-inquiry_id="<?php echo ($item['inquiry_id']) ?>"><?php echo __('admin.common.button.clone') ?></div>
                        <div style="margin-top: 2px;" class="btn round image add js-version" data-inquiry_id="<?php echo ($item['inquiry_id']) ?>"><?php echo __('admin.inquirys.label.revision') ?></div>
                        <div style="margin-top: 2px;" class="btn round image link js-public" data-clipboard-text="<?php echo ($public_url) ?>"><?php echo __('admin.common.button.copy_public_url') ?></div>
                        <div style="margin-top: 2px;" class="btn round image preview js-preview" data-url="<?php echo ($public_url) ?>"><?php echo __('admin.common.button.preview') ?></div>
                    </td>
                    <?php } ?>
                </tr>
            <?php } ?>
        </tbody>
    </table>
</div>
