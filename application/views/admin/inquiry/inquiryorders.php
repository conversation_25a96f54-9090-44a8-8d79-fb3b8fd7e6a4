<?php echo $menu?>

<div class="edit-container">
	<!-- Page Content -->
	<div class="page-container">
		<div class="left-container">
			<input type="hidden" name="inquiry_id" value="<?php echo $inquiry_id?>" />
			<div class="form-body">
				<div class="form-group">
					<div class="row">
						<br />
					</div>
					<div class="row">
						<label class="control-label col-md-1">取得期間:</label>
						<div class="col-md-4">
							<input name="start_date" id="start_date" value="<?php echo ($start_date) ?>" style="float:left;" class="form-control form-control-inline input-small date-picker" size="16" data-date-format="yyyy-mm-dd" type="text" />
							<input name="end_date" id="end_date" value="<?php echo ($end_date) ?>" style="float:left; margin-left:10px;" class="form-control form-control-inline input-small date-picker" size="16" data-date-format="yyyy-mm-dd" type="text" />
						</div>
						<div class="col-md-1">
							<button type="submit" id="searchButton" class="btn" style="color: #FFFFFF; background-color: #4B8DF8;">
								<i class="fa fa-search mr10"></i>検索</button>
						</div>
					</div>
				</div>
			</div>
			<div class="portlet box">
				<div id="service-table" class="portlet-body">
					<table class="table table-striped table-bordered table-hover js-data-table">
						<thead>
							<tr>
								<th style="width: 150px;">
									クーポン
								</th>
								<th style="width: 80px;">
									お名前
								</th>
								<th style="width: 80px;">
									チャンネル
								</th>
								<th style="width: 100px;">
									取得日時
								</th>
								<th style="width: 150px;">
									利用状況
								</th>
							</tr>
						</thead>
						<tbody id="experience-body">

							<?php
							foreach ($services as $service) {
							?>
								<tr class="gradeX odd" role="row">
									<td>
										<?php echo (nl2br($service['product_name'])) ?>
										<br />
									</td>
									<td>
										<?php
										$member_name = '';
										if ($service['name'] != NULL) {
											$member_name = $service['name'];
										} else {
											if ($service['sns_type_cd'] == 'wb') {
												$member_name = "Webユーザ";
											} else {
												if ($service['last_name'] . $service['first_name'] != '') {
													$member_name = $service['last_name'] . $service['first_name'];
												}
											}
										}
										?>
										<?php echo (nl2br($member_name)) ?>
										<br />
										<?php if ($service['sns_type_cd'] != null) { ?>
											<a class="pop_adminchat" member_id="<?php echo $service['member_id'] ?>">
												<span class="badge badge-primary" style="margin: 5px;">チャット</span> </a>
											<br />
											<?php if ($service['order_data'] != '' && false) { ?>
												<a class="pop_servicebox" sid="<?php echo ($service['order_id']) ?>"><span class="badge badge-warning" style="margin: 5px;">用件編集</span></a>
											<?php } ?>
										<?php } else {
											echo ('非会員');
										} ?>
									</td>
									<td>
										<span class="badge badge-warning" style="display:none;margin-top: 5px;"><?php echo ($_bot_lang[$service['lang_cd']]) ?></span>
										<?php if ($service['sns_type_cd'] == null) { ?>
											<img src="/assets/common/images/chat_<?php echo $service['order_lang_cd'] ?>.png" style="margin:5px;width:24px;" />
											<img src="/assets/common/images/icon_wb.png" style="margin:5px;width:28px;" />
										<?php } else { ?>
											<img src="/assets/common/images/chat_<?php echo $service['lang_cd'] ?>.png" style="margin:5px;width:24px;" />
											<img src="/assets/common/images/icon_<?php echo $service['sns_type_cd'] ?>.png" style="margin:5px;width:28px;" />
										<?php } ?>
										<span class="badge badge-primary" style="display:none;margin-top: 5px;"><?php echo ($_codes['08'][$service['sns_type_cd']]) ?></span>
										<br />
									</td>
									<td style="text-align:center;">
										<?php
										echo (substr($service['order_date'], 0, 16));
										?>
									</td>
									<td>
										最後利用時間：<?php echo (substr($service['use_date'], 0, 16)) ?>
										<br />利用回数：<span style="margin-left:5px;font-weight:bold"><?php echo ($service['num']) ?></span>
									</td>
								</tr>
							<?php } ?>


						</tbody>
					</table>
				</div>
			</div>
		</div>
	</div>
	<!-- /#page-wrapper -->
</div>

<?php echo $talkbox ?>

<!-- END PAGE CONTENT-->