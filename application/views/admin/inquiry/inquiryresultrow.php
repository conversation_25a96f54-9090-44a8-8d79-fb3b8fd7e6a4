<?php 
if (isset($inquiry_data['payment']) && isset($inquiry_data['payment']['2_step']) && $inquiry_data['payment']['2_step'] == 1) {
    $pay_2_step = 1;
}
else {
    $pay_2_step = 0;
}

if (is_array($result['result_data'])) {
    $result_data = $result['result_data'];
}
else {
    $result_data = json_decode($result['result_data'], true);
}
$service_id = '';
if ($result_data != null && array_key_exists('service_id', $result_data)) {
    $service_id = $result_data['service_id'];
}
$amount = '';
$pay_vender = '';
if ($result_data != null && isset($result_data['payment']) && isset($result_data['payment']['amount'])) {
    $amount = $result_data['payment']['amount'];
    if (isset($result_data['payment']['vender'])) {
        $pay_vender = $result_data['payment']['vender'];
    }
}
if ($result_data != null && isset($result_data['payment']) && isset($result_data['payment']['TALKAPPI_PAYMENT'])) {
    if (isset($result_data['payment']['TALKAPPI_PAYMENT']['vender'])) {
        $pay_vender = $result_data['payment']['TALKAPPI_PAYMENT']['vender'];
    }
}
if (isset($result_data['receipt_url'])) {
    $link_id = substr($result_data['receipt_url'], -32);
    $link = ORM::factory('link', $link_id);
    $receipt_data = json_decode($link->param1, true);
}
else {
    $receipt_data = [];
}
//base_idの受付IDを取得する
$base_result_cd = '';
$base_result_id = '';
foreach ($inquiry_result as $rst) {
    if ($rst['id'] == $result['base_id']) {
        if($rst['result_cd']) {
            $base_result_cd = $rst['result_cd'];
        }
        else {
            $base_result_cd = $result['base_id'];
        }
        $base_result_id = $result['base_id'];
        break;
    }
}
//変更によるキャンセルの場合
if ($base_result_cd == '') {
    if ($result['status_cd'] == '03') {
        if (in_array($result['id'], $result_base_id)) {
            $base_result_cd = $result['id'];
            $base_result_id = $result['id'];
        }
    }
}
?>
<tr class="gradeX odd js-result-row" role="row" data_id="<?php echo($result['id'])?>" data-base_id="<?php echo $base_result_id ?>">
    <!-- 受付情報 -->
    <td class="js-td-base-info">
        <?php echo(date('Y/m/d H:i', strtotime($result['end_time']))); ?>
        <br>
        <?php
        if ($result['delete_flg'] == 1) {
            echo('<span class="btn light-red js-status-cd" style="margin: 5px 0;">' . __('admin.inquiryresult.label.deleted') . '</span>');
        }
        else {
            if ($result['status_cd'] == '00') echo('<span class="btn light-yellow js-status-cd" style="margin: 5px 0;">' . __('admin.inquiryresult.label.pre_done') . '</span>');
            if ($result['status_cd'] == '01') echo('<span class="btn light-green js-status-cd" style="margin: 5px 0;">' . __('admin.inquiryresult.label.done') . '</span>');
            if ($result['status_cd'] == '02') {
                echo('<span class="btn light-yellow js-status-cd" style="margin: 5px 0;">' . __('admin.inquiryresult.label.modified') . '</span>');
                if ($base_result_cd != '')
                {
                    echo('<span class="btn round light-yellow js-status-cd js-modify-result-span" style="margin: 5px 0;">' . $base_result_cd . __('admin.inquiryresult.label.modified_history') . '</span>');
                }
            }
            if ($result['status_cd'] == '03') {
                if ($result['base_id'] != NULL || is_array($result_base_id) && in_array($result['id'], $result_base_id)) {
                    echo('<span class="btn light-red js-status-cd" style="margin: 5px 0;">' . __('admin.inquiryresult.label.modifycancel') . '</span>');
                    if ($base_result_cd != '')
                    {
                        echo('<span class="btn light-yellow js-status-cd js-modify-result-span" style="margin: 5px 0;">' . $base_result_cd . __('admin.inquiryresult.label.modified_history') . '</span>');
                    }
                }
                else {
                    echo('<span class="btn light-red js-status-cd" style="margin: 5px 0;">' . __('admin.inquiryresult.label.cancel_done') . '</span>');
                }
            }
        }
        ?>
        <br>
        <?php echo __('admin.inquiryresult.label.reception_id'); ?>: <span title="<?php echo($result['id'])?>"><?php 
        if ($result['result_cd'] == null) {
            $id_label = $result['id'];
        }
        else {
            $id_label = $result['result_cd'];
        }
        if (false && isset($inquiry->renew_time) && $inquiry->renew_time != null && $result['start_time'] > $inquiry->renew_time) {
            echo('<a href="/admininquiry/inquiryresultedit?id=' . $result['id'] .'">' . $id_label . '</a>');
        }
        else {
            echo($id_label);
        }

        ?>
        </span>
        <?php
        if ($service_id != '') {
            echo('<br/>' . __('admin.inquiryresult.label.service_id') . ':' . $service_id );
        } 
        ?>
        <br/>
        <?php echo __('admin.inquiryresult.label.conductor'); ?>: <?php echo($result['label'])?>
        <br/>
        <?php 
        if (isset($result['id']) && !isset($post['result_id'])) {
            echo '<br/><a href="/admininquiry/inquiryresultdetail?id=' . $result['id'] . '" class="btn round light-blue" style="margin: 5px 0; text-decoration: none; color: inherit;">' . __('admin.inquiryresult.label.resultdetail') . '</a>';
        }
        if (isset($post['result_id']) && $result['delete_flg'] == 0) {
            echo '<div class="btn round light-red js-result-delete" style="margin-top:5px;" result_id="' . $result['id'] . '">' . __('admin.common.button.delete.full') . '</div>';
        }
        ?>
    </td>
    <!-- ユーザー情報 -->
    <td style="text-align: center;">
        <?php echo __('admin.common.label.user_id'); ?>: <?php echo($result['member_no'])?>
        <br/>
        <span class="badge badge-warning" style="display:none;margin-top: 5px;" ><?php echo($_bot_lang[$result['lang_cd']])?></span>
        <img src="/assets/common/images/chat_<?php echo $result['lang_cd']?>.png" style="margin:5px;width:24px;"/>
        <img src="/assets/common/images/icon_<?php echo $result['sns_type_cd']?>.png" style="margin:5px;width:24px;"/>
        <span class="badge badge-primary" style="display:none;margin-top: 5px;" ><?php echo($_codes['08'][$result['sns_type_cd']])?></span>
        <br/>
        <?php 
            if ($_user->role_cd == '99' || $_user->role_cd == '01' || $_user->role_cd == '07' || $_user->role_cd == '09')
            echo ('<label class="badge badge-info js-memberinfo" style="cursor:pointer;" data-member-id="' . $result['member_id'] .'">' . __('admin.common.label.journey')  . '</label>');
        ?>
        <br/>
        <br/>
            <?php
            if($result['is_blocked'] === '0') {
                echo ('<label class="btn round js-block-user" style="height: 20px;font-size: 10px;color: #939393;background-color: #f2f2f2;" member_id="' . $result['member_id'] .'" is_blocked="' . $result['is_blocked'] . '">' . __('admin.inquiryresult.label.spam_user') . '</label>');
            }
            else {
                echo ('<label class="btn round js-block-user" style="height: 20px;font-size: 10px;color: #000000;background-color: #FFDCE5;" member_id="' . $result['member_id'] .'" is_blocked="' . $result['is_blocked'] . '">' . __('admin.inquiryresult.label.unspam_user') . '</label>');
            }
            ?>
    </td>
    <!-- 受付情報 -->
    <td>
        <?php 
        if (isset($result['answer_html'])) {
            echo(nl2br($result['answer_html']));
            // 自動翻訳ボタン
            if ($result['lang_cd'] !== $_lang_cd) {
                echo '<br/><div class="btn round js-translate-answer" style="height: 20px;font-size: 10px;margin-top:5px;color:#939393;background-color: #f2f2f2;" data-result-id="' . $result['id'] . '" data-from-lang="' . $result['lang_cd'] . '" data-to-lang="' . $_lang_cd . '" data-answer="' . htmlspecialchars($result['answer_html'], ENT_QUOTES) . '">' . __('admin.inquiryresult.label.auto_translate') . '</div>';
            }
        }
        if (isset($receipt_data['issue_time'])) echo('<br>領収書発行済：' . $receipt_data['issue_time']);
        ?>
    </td>
    <!-- 連絡 -->
    <td class="js-td-contact" style="text-align: center;" amount="<?php echo $amount ?>">
        <?php 
        if ($result['status_cd'] == '03') {
            $mail_type = 'cancel';
            $resend = 'cancel_resend';
            $btn_label = 'admin.inquiryresult.label.cancel_mail';
            $btn_label2 = 'admin.inquiryresult.label.cancel_mail_admin';
        }
        else {
            $mail_type = 'confirm';
            $resend = 'complete_resend';
            $btn_label = 'admin.inquiryresult.label.confirm_mail';
            $btn_label2 = 'admin.inquiryresult.label.confirm_mail_admin';
        }
        if ($_bot_setting['flg_ai_bot'] == 0) { ?>
        <a class="pop_adminchat" member_id="<?php echo $result['member_id'] ?>"><span class="btn round light-blue" style="margin: 5px;"><?php echo __('admin.common.label.chat'); ?></span></a>
        <?php }?>
        <div class="btn round js-confirm-mail" style="margin-top:5px;" data-result-id="<?php echo($result['id'])?>" data-type="<?php echo($mail_type)?>" data-title="<?php echo($resend)?>" data-div="1"><?php echo __($btn_label); ?></div><br/>
        <div class="btn round js-confirm-mail" style="margin-top:5px;" data-result-id="<?php echo($result['id'])?>" data-type="<?php echo($mail_type)?>" data-title="<?php echo($resend)?>" data-div="2"><?php echo __($btn_label2); ?></div><br/>
        <?php 
            if ($pay_2_step == 1 && $result['status_cd'] == '00') {
                echo('<div class="btn round light-green js-2-step-confirm-mail" style="margin-top:5px;" result_id="' . $result['id'] . '">' . __('admin.inquiryresult.label.2_step_pay_confirm') . '</div><br/>');
                echo('<div class="btn round light-red js-2-step-refuse-mail" style="margin-top:5px;" result_id="' . $result['id'] . '">' . __('admin.inquiryresult.label.2_step_pay_refuse') . '</div><br/>');                      
            }
            if ($result['status_cd'] == '01' || $result['status_cd'] == '02') {
                echo('<div class="btn round light-yellow js-inquiry-modify" style="margin-top:5px;" data-result-id="' . $result['id'] . '">' . __('admin.common.label.change_by_admin') . '</div><br/>');
            }
            if ($_user->role_cd == '99' && ($_user->user_id == 1 || $_user->user_id == 1216)) {
                echo('<div class="btn round light-red js-cancel-payment" style="margin-top:5px;" result_id="' . $result['id'] . '">※決済キャンセル</div>'); 
            }
        ?>
    </td>
    <!-- 対応状態 -->
    <td class="js-td-support" >
    <div style="display: grid;place-items: center;">
        <?php 
        $support_type_cd = '';
        foreach($inquiry_result_support as $support) {
            if ($support['support_type_cd'] != '') $support_type_cd = $support['support_type_cd'];
        }
        $attr = '';
        foreach($buttons as $key=>$value) {
            if ($key == '04' && $result['status_cd'] == '03') continue;
            if ($key == '04' && $result['status_cd'] != '00' && isset($inquiry_data['cancel_policy']) && isset($inquiry_data['cancel_policy']['policy'])) {
                $attr = ' data-cancel="policy" ';
            }
            if ($key == '04' && $result['status_cd'] != '00' && $pay_vender == 'talkappi' && date('Y-m-d', strtotime($result['end_time'] . ' +365 day')) < date('Y-m-d')) {
                $attr = ' data-cancel="overtime" ';
            }
            if ($support_type_cd == $key) {
                $style = 'style="margin-bottom:5px;background-color: ' . $labelcolor[$support_type_cd] . '; color: #FFF;"';
            }
            else {
                $style='style="margin-bottom:5px;"';
            }
            echo('<span ' . $attr . ' class="btn round js-support" '. $style . ' support_type_cd="' .  $key . '" result_id="' . $result['id'] . '">' . $value . '</span>');
            $attr = '';
        }
    ?>
    </div>
    </td>
    <!-- 対応履歴 -->
    <td>
    <div class="btn round image add js-memo" result_id="<?php echo($result['id'])?>"><?php echo __('admin.inquiryresult.label.add_memo'); ?></div>
    <div class="js-td-memo" result_id="<?php echo($result['id'])?>">
    <?php 
    foreach($inquiry_result_support as $support) {
        if ($support['support_type_cd'] != '') {
            if (isset($buttons[$support['support_type_cd']])) {
                $support_label = '<span class="label" style="color:#FFF;background-color:' . $labelcolor[$support['support_type_cd']] . '">' . $buttons[$support['support_type_cd']] . '</span><br/>';
            }
            else {
                $support_label = '';
            }
            $close = '';
        }
        else {
            $support_label = '';
            $close = '<div class="icon-cancel-small js-memo-delete" no="' . $support['no'] . '" style="margin: 0 0 0 auto;"></div>';
        }
        $memo = '';
        if(in_array($support['support_type_cd'], ['send_mail', 'received_mail'], true)){
            $comment = '<div style="width: calc(100% - 20px);font-size:11px;" class="js-memo-mail memo-mail" no="'. $support['id'] .'">'. date('Y/m/d H:i', strtotime($support['upd_time']))  . ' ' . $support['title'] . '<br/>' . $memo .'</div>';
            if($support['support_type_cd'] == 'send_mail'){
                $close = '<div class="icon-preview-mail-sent js-memo-mail" no="' . $support['id'] . '" lang_cd="' . $result['lang_cd'] . '" style="margin: 0 0 0 auto;"></div>';
            } else if($support['support_type_cd'] == 'received_mail') {
                $close = '<div class="icon-preview-mail js-memo-mail" no="' . $support['id'] . '" lang_cd="' . $result['lang_cd'] . '" style="margin: 0 0 0 auto;"></div>';
            }
        } else {
            if ($support['memo'] != null) $memo = nl2br($support['memo']);
            $comment = '<div style="width: calc(100% - 20px);font-size:11px;">'. date('Y/m/d H:i', strtotime($support['upd_time']))  . ' ' . $support['name'] . $support_label . '<br/>' . $memo .'</div>';
        }
        echo('<div class="small-table-pannel" style="display: flex;padding: 8px;">' . $comment . $close . '</div>');
    }
    ?>
    </div>
    </td>
</tr>