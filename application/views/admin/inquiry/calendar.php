<link rel="stylesheet" type="text/css" href="/assets/admin/css/inquirycalendar.css"/>
<style>
  .note-editor { 
    margin-bottom: 0;
  }
</style>
<script>
  const _inquiry_options = <?php echo ($inquiry_options) ?>;
  const _all_maximum_options = <?php echo ($all_maximum_options) ?>;
  const _inquiry_maximum_mapping = <?php echo ($inquiry_maximum_mapping) ?>;
  const _display_langs = '<?php echo json_encode(array_keys($bot_lang), JSON_UNESCAPED_UNICODE) ?>';
  const _bot_langs = <?php echo json_encode($bot_lang, JSON_UNESCAPED_UNICODE) ?>;
  const _lang_cd = '<?php echo $_lang_cd ?>';
  const _calendar_data = <?php echo $calendar_data ?>;
  const _calendar_title = <?php echo ($calendar->title) ?>;
  const _calendar_remark = <?php echo (json_encode($remarks, JSON_UNESCAPED_UNICODE)) ?>;
</script>
  <div class="content-container white border">
    <div class="section-container bottom-line">
      <h2 style="margin-top:0;"><?php echo __('admin.inquirycalendar.label.calendar_setting') ?></h2>
      <div class="form-body">
        <div class="form-group">
          <label class="control-label col-md-3"><?php echo __('admin.inquirycalendar.label.calendar_name') ?></label>
          <div class="col-md-6">
          <div class="readonly-input flex-x-between">
              <span><?php echo $calendar_title ?></span>
              <a class="js-edit edit-cal-input-button" data-inquirycalendar-id=<?php echo ($calendar->id) ?>  href="#"><?php echo __('admin.common.button.edit') ?></a>
          </div>
          </div>
        </div>
        <div class="form-group">
          <label class="control-label col-md-3"><?php echo __('admin.inquirycalendar.label.remark') ?></label>
          <div class="col-md-6">
            <div class="readonly-input flex-x-between">
              <span><?php echo is_null($calendar_remark) ? 'なし' : $calendar_remark ?></span>
              <a class="js-edit-remark" data-inquirycalendar-id=<?php echo ($calendar->id) ?>  href="#"><?php echo __('admin.common.button.edit') ?></a>
            </div>
          </div>
        </div>
        <div class="form-group">
          <label class="control-label col-md-3"><?php echo __('admin.inquirycalendar.label.add_edit') ?></label>
          <div class="col-md-6">
            <div class="readonly-input flex-x-between">
                <span><?php echo $reservations_text ?></span>
                <a class="js-edit edit-cal-input-button" data-inquirycalendar-id=<?php echo ($calendar->id) ?>  href="#"><?php echo __('admin.common.button.edit') ?></a>
            </div>
          </div>
        </div>
        <div class="form-group">
          <label class="control-label col-md-3"><?php echo __('admin.inquirycalendar.label.public_iframe_code') ?></label>
          <div class="col-md-6">
            <div class="readonly-input flex-x-between">
                <span><?php echo htmlspecialchars($embed_code) ?></span>
                <a class="js-copy copy-cal-input-button" @click="copyUri" data-clipboard-action="copy" data-clipboard-text="<?php echo htmlspecialchars($embed_code) ?>"  href="#"><?php echo __('admin.common.label.copy') ?></a>
            </div>
          </div>
        </div>
        <div class="form-group">
          <label class="control-label col-md-3"><?php echo __('admin.inquirycalendars.label.public_url') ?></label>
          <div class="col-md-6 flex" style="flex-flow:column; gap:8px;">
            <div class="readonly-input flex-x-between">
                <span><?php echo $public_url ?></span>
                <a class="js-copy copy-cal-input-button" @click="copyUri" data-clipboard-action="copy" data-clipboard-text="<?php echo htmlspecialchars($public_url) ?>"  href="#"><?php echo __('admin.common.label.copy') ?></a>
            </div>
            <div class="flex public-url-locales">
              <?php foreach ($bot_lang as $lang_cd=>$lang) : ?>
              <a class="action-button section btn-grey js-copy" @click="copyUri" data-clipboard-action="copy" data-clipboard-text="<?php echo $public_url.'&lang_cd='.$lang_cd ?>" style="margin:0; width:fit-content; color: black;"><span class="copy-icon"></span><?php echo $lang ?></a>
              <?php endforeach ?>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
