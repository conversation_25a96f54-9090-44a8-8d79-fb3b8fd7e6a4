<style>
    div.setting-title{
        padding: 12px 0px 12px 0px;
    }
    div.setting-title>div.title-text{
        white-space: nowrap;
        height: 18px;

        font-family: Hiragino Sans;
        font-size: 12px;
        font-weight: 500;
        line-height: 18px;
        text-align: left;
    }
    div.setting-title>div.title-border{
        width: calc(100% - 120px);
        align-self: center;
        height: 0;
        border: 0.5px solid #E3E5E8
    }

    .setting-item {
        padding: 16px 24px 24px 24px;
        gap: 24px;
        justify-content: flex-start;
    }

    .setting-item-label{
        padding: 5px 0px 5px 0px;
        text-align: right;
        width: 200px;
    }

    div.setting-container{
        flex-direction: column;
        gap: 12px;
    }
    div.setting-input-button{
        gap: 12px;
    }
    input {
        height: 28px;
        font-family: 'Hiragino Sans';
        font-weight: 300;
        font-size: 12px;
        cursor: default;
        padding: 0px 5px 0px 5px;
        border-width: 1px;
        border-color: rgba(227, 229, 232, 1);
        border-radius: 4px;
        border-style: solid;
    }
    div.setting-input-button>button{
        border: none;
        font-family: Hiragino Sans;
        font-size: 12px;
        font-weight: 400;
        line-height: 18px;
        text-align: center;
    }
    .setting-detail-container{
        width: 695px;
        padding: 12px;
        gap: 12px;
        border-radius: 4px 0px 0px 0px;
        border-width: 1px;
        border-color: rgba(227, 229, 232, 1);
        border-style: solid;
        flex-direction: column;
    }
    .setting-detail-top{
        justify-content: space-between;
        width: 100%;
    }
    .setting-detail-top>button{
        border: none;
        font-family: Hiragino Sans;
        font-size: 12px;
        font-weight: 400;
        line-height: 18px;
        text-align: center;
    }

    .setting-detail-container>table td, .setting-detail-container>table th{
        text-align: center;
        vertical-align: middle;
    }

    .setting-detail-container>table>thead {
        background: rgba(246, 247, 249, 1);
    }

    .setting-detail-container>table>thead>tr>th {
        font-size: 12px;
        font-weight: 300;
    }

    .setting-detail-container>table td:first-child, .setting-detail-container>table th:first-child {
        width: 120px;
    }
    .setting-detail-container>table td:last-child, .setting-detail-container>table th:last-child {
        width: 60px;
    }
    .setting-detail-container>table td:last-child>a {
        text-decoration: underline;
    }
    .talkappi-pulldown[data-name="mail_signature"] .talkappi-dropdown-options {
		top: unset;
		bottom: 30px;
	}
</style>

<input type="hidden" name="message" id="message" value="<?php echo ($message) ?>" />
<div class="edit-container">
    <div class="settings-container">
        <div class="settings-segment">
            <div class="setting-title flex"><div class="title-text">【<?php echo __('admin.inquirysetting.user_in_charge.setting')?>】</div><div class="title-border"></div></div>
            <div class="setting-item flex" style="display: flex; flex-direction: column; gap: 12px;">
                <div class="flexbox-x-axis" style="justify-content: flex-start; gap: 24px;">
                    <label class="setting-item-label"><?php echo __('admin.inquirysetting.user_in_charge')?></label>
                    <div class="talkappi-radio" data-name="user_in_charge_required"
                        data-value='<?php echo isset($inquiry_setting['user_in_charge_required']) ? $inquiry_setting['user_in_charge_required'] : ''; ?>'
                        data-source='{"0":"しない", "1":"する"}'>
                    </div>
                </div>
            </div>
        </div>
        <?php if ($inquiry_div != 9) { ?>
        <div class="settings-segment">
            <div class="setting-title flex"><div class="title-text">【<?php echo __('admin.inquiry.label.receipt_setting')?>】</div><div class="title-border"></div></div>
            <div class="setting-item flex" style="display: flex; flex-direction: column; gap: 12px;">
                <div class="flexbox-x-axis" style="justify-content: flex-start; gap: 24px;">
                    <label class="setting-item-label"><?php echo __('admin.inquirysetting.corporation')?></label>
                    <input name="corporation" type="text" class="" style="width:400px;" placeholder="" value="<?php echo isset($receipt_setting['corporation']) ? $receipt_setting['corporation'] : ''; ?>">
                </div>
                <div class="flexbox-x-axis" style="justify-content: flex-start; gap: 24px;">
                    <label class="setting-item-label"><?php echo __('admin.inquirysetting.address')?></label>
                    <input name="address" type="text" class="" style="width:400px;" placeholder="" value="<?php echo isset($receipt_setting['address']) ? $receipt_setting['address'] : ''; ?>">
                </div>
                <div class="flexbox-x-axis" style="justify-content: flex-start; gap: 24px;">
                    <label class="setting-item-label"><?php echo __('admin.inquirysetting.tel')?></label>
                    <input name="tel" type="text" class="" style="width:400px;" placeholder="" value="<?php echo isset($receipt_setting['tel']) ? $receipt_setting['tel'] : ''; ?>">
                </div>
                <div class="flexbox-x-axis" style="justify-content: flex-start; gap: 24px;">
                    <label class="setting-item-label"><?php echo __('admin.inquirysetting.regist_no')?></label>
                    <input name="regist_no" type="text" class="" style="width:400px;" placeholder="" value="<?php echo isset($receipt_setting['regist_no']) ? $receipt_setting['regist_no'] : ''; ?>">
                </div>
                <div class="flexbox-x-axis" style="justify-content: flex-start; gap: 24px; display: none;">
                    <label class="setting-item-label"><?php echo __('admin.inquirysetting.template')?></label>
                    <input name="template" type="text" class="" style="width:400px;" placeholder="receipt.inquiry.pdf1" value="receipt.inquiry.pdf1" readonly>
                </div>
                <div class="flexbox-x-axis" style="justify-content: flex-start; gap: 24px;">
                    <label class="setting-item-label"><?php echo __('admin.inquirysetting.tax_rate')?></label>
                    <input name="tax_rate" type="number" class="" style="width:200px;" placeholder="10" value="<?php echo isset($receipt_setting['tax_rate']) ? $receipt_setting['tax_rate'] : '10'; ?>">％
                </div>
            </div>
        </div>
        <?php } ?>
        <div class="settings-segment">
            <div class="setting-title flex"><div class="title-text">【<?php echo __('admin.inquirysetting.label.sender_setting') ?>】</div><div class="title-border"></div></div>
            <div class="setting-item flex">
                <label class="setting-item-label"><?php echo __('admin.inquirysetting.label.sender_setting') ?></label>
                <div class="flex setting-container">
                    <div class="setting-input-button flex">
                        <input name="domain" type="text" class="" style="width:400px;" placeholder="<?php echo __('admin.inquirysetting.placeholder.input_domain') ?>" value="<?php if ($post != NULL) echo ($post['domain']) ?>">
                        <button class="btn-smaller btn-grey-black js-acquire-dns-id"><?php echo __('admin.inquirysetting.button.get_dns') ?></button>
                    </div>
                    <div id="dns" class="flex setting-detail-container" style="display:none;">
                        <div class="flex setting-detail-top">
                            <label class=""><?php echo __('admin.inquirysetting.label.dns_record_details') ?></label>
                        </div>
                        <table class="table table-bordered table-hover" id="dns-record-detail">
                            <thead>
                                <tr>
                                    <th>レコードタイプ （Type）</th>
                                    <th>ホスト名（Host）</th>
                                    <th>値（Value）</th>
                                    <th><?php echo __('admin.common.label.operation') ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr id="1" class="gradeX domain cname" role="row">
                                    <td name="type">CNAME</td>
                                    <td name="host"></td>
                                    <td name="value"></td>
                                    <td name="operation"><a class="js-copy" data-clipboard-action="copy"><?php echo __('admin.common.button.copy') ?></a></td>
                                </tr>
                                <tr id="2" class="gradeX domain cname" role="row">
                                    <td name="type">CNAME</td>
                                    <td name="host"></td>
                                    <td name="value"></td>
                                    <td name="operation"><a class="js-copy" data-clipboard-action="copy"><?php echo __('admin.common.button.copy') ?></a></td>
                                </tr>
                                <tr id="3" class="gradeX domain cname" role="row">
                                    <td name="type">CNAME</td>
                                    <td name="host"></td>
                                    <td name="value"></td>
                                    <td name="operation"><a class="js-copy" data-clipboard-action="copy"><?php echo __('admin.common.button.copy') ?></a></td>
                                </tr>
                                <tr class="gradeX domain mx" role="row">
                                    <td name="type">MX</td>
                                    <td name="host"></td>
                                    <td name="value">10 feedback-smtp.ap-northeast-1.amazonses.com</td>
                                    <td name="operation"><a class="js-copy" data-clipboard-action="copy"><?php echo __('admin.common.button.copy') ?></a></td>
                                </tr>
                                <tr class="gradeX domain txt" role="row">
                                    <td name="type">TXT</td>
                                    <td name="host"></td>
                                    <td name="value">v=spf1 include:amazonses.com ~all</td>
                                    <td name="operation"><a class="js-copy" data-clipboard-action="copy"><?php echo __('admin.common.button.copy') ?></a></td>
                                </tr>
                                <tr class="gradeX domain txt dmarc" role="row">
                                    <td name="type">TXT</td>
                                    <td name="host"></td>
                                    <td name="value">v=DMARC1; p=none;</td>
                                    <td name="operation"><a class="js-copy" data-clipboard-action="copy"><?php echo __('admin.common.button.copy') ?></a></td>
                                </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <div class="settings-segment">
            <div class="setting-title flex"><div class="title-text">【<?php echo __('admin.send.label.signature')?>】</div><div class="title-border"></div></div>
            <div class="setting-item flex" style="display: flex; flex-direction: column; gap: 12px;">
                <div class="flexbox-x-axis" style="justify-content: flex-start; gap: 24px;">
                    <label class="setting-item-label"><?= __('admin.send.label.signature') ?></label>
                    <div style="display: flex; gap: 12px;align-items: center;">
                        <div style="width:300px" class="talkappi-pulldown" data-name="mail_signature" data-value="<?= $inquiry_setting['mail_signature'] ?? 0 ?>" data-source='<?= htmlspecialchars($mail_signature_options) ?>'></div>
                        <span class="js-edit-signature pointer" style="color:#245BD6;"><?= __('admin.send.label.signature_edit') ?></span>
                    </div>
                </div>
            </div>
        </div>
        <div class="submit-btn-container" style="margin: 60px 0 0 134px;">
            <div class="btn-larger btn-blue js-action-save"><?php echo __('admin.common.button.save'); ?></div>
	    </div>
    </div>
</div>