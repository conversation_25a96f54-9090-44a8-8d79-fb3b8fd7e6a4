<?php 
	$to_lang_cds = [];
	foreach ($support_lang_cd as $k => $v) {
		$to_lang_cds[] = [
			"lang_cd" => $k,
			"lang" => $v
		];
	}
?>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/1.12.4/jquery.min.js"></script>
<script src="/assets/common/react/components/blocks/adminlangtabs.bundle.js"></script>
<script src="/assets/common/react/components/blocks/multilingualreflect.bundle.js"></script>
<script type="text/javascript">
	<?php 
	$model = new Model_Adminmodel();
	if ($post['page'] == 'complete') {
		if (isset($post['complete_area'])) {
			$complete_area = json_encode($post['complete_area'], JSON_UNESCAPED_UNICODE);
		} else {
			$complete_area = '';
		}
		echo("var complete_area='" . $complete_area. "';");
		if (isset($post['actions'])) {
			$actions = json_encode($post['actions'], JSON_UNESCAPED_UNICODE);
		} else {
			$actions = '';
		}
		echo("var actions='" . $actions . "';");
	}
	else {
		$complete_area = '';
		$actions = '';
	}
	$_item_upd_timestamp = str_replace(['-',' ',':'], '', $item->upd_time);
	?>
	const _inquiry_div = "<?php echo $inquiry_div ?>";
	const _main_pic = <?php echo json_encode($post['inquiry_image'], JSON_UNESCAPED_UNICODE); ?>;
	const _item_upd_timestamp = '<?php echo $_item_upd_timestamp ?>';
	const _from_lang_cd = '<?php echo $lang_cd ?>';
	const _to_lang_cds = <?php echo json_encode($to_lang_cds, JSON_UNESCAPED_UNICODE) ?>;

	jQuery(document).ready(function($) {
        // 表示言語
        const displayedLangs = Object.values(<?php echo json_encode($lang_display) ?>);
        // サポート言語
        const allLangs = Object.keys(<?php echo json_encode($support_lang_cd) ?>);
        // 非表示言語
        const undisplayedLangs = allLangs.filter(key => !displayedLangs.includes(key));

        window.talkappi_admin_setupAdminLangTabs({
            displayed: displayedLangs,
            undisplayed: undisplayedLangs,
            url: `/admininquiry/inquirydesc?id=<?php echo $inquiry_id ?>&lang=`,
            active: '<?php echo $lang_cd ?>',
        });
    });
</script>

<?php echo $menu?>

<input type="hidden" name="description_extra" id="description_extra" value="" />
<input type="hidden" name="description" id="description" value="" />
<input type="hidden" name="message" id="message" value="<?php echo($message)?>" />
<input type="hidden" name="lang" id="lang" value="<?php echo($lang_edit)?>" />
<input type="hidden" name="page" id="page" value="<?php echo($post['page'])?>" />
<input type="hidden" name="inquiry_id" value="<?php echo $inquiry_id?>" />
<input type="hidden" name="complete_area" id="complete_area" value='<?php echo($complete_area)?>' />
<input type="hidden" name="actions" id="actions" value='<?php echo($actions)?>' />
<input type="hidden" name="inquiry_image" id="inquiry_image" value=<?php echo $post['inquiry_image']?> />

<style>
	body {
		font-size: 13px;
	}
	.container {
    height: 32px;
    margin-top: 16px;
	}
	.breadcrumb {
	width: 100%;
	position: relative;
	height:32px;
	display: inline-block;
	overflow: hidden;
	border-radius: 8px 8px 0 0;
	text-align: center;
	padding-left: 0px;
	list-style: none;
	overflow: hidden;
	font-size: 1.2rem;
	border-radius: 8px 8px 0 0;
	margin-bottom: 0px;
	}

	@media screen and (max-width: 670px) {
		.breadcrumb {
			font-size: 1.1rem;
		}
	}

	.breadcrumb .item {
		position: relative;
		display: block;
		float: left;
		width: 33.333333333333%;
		background: #f6f7f9;
		color: #a1a4aa;
		text-decoration: none;
		outline: none;
		line-height: 32px;
		white-space: nowrap;
	}

	.breadcrumb .arrow {
		position: absolute;
		top: 16px;
		right: 1px;
		z-index: 2;
	}
	@media screen and (max-width: 767px) {
		.breadcrumb .arrow {
		top: 16px;
		}
	}
	.breadcrumb .arrow:after, .breadcrumb .arrow:before {
		content: " ";
		position: absolute;
		height: 0;
		width: 0;
		border: solid transparent;
	}

	.breadcrumb .arrow:after {
		margin-top: -22px;
		right: -12px;
		border-width: 22px 0 22px 12px;
	}

	.breadcrumb .arrow:before {
		margin-top: -26px;
		border-width: 26px 0 26px 15px;
	}

	.breadcrumb .active {
		background-color: #231c19 !important;
		color: #fff !important;
	}
	.breadcrumb .item:hover .arrow:after {
		border-left-color: #f6f7f9;
	}
	.breadcrumb .active {
		line-height: 32px;
	}

	.breadcrumb .active .arrow {
		top: 16px;
	}
	@media screen and (max-width: 767px) {
		.breadcrumb .active .arrow {
		top: 16px;
		}
	}
	.breadcrumb .active .arrow:before {
		border-left-color: #231c19!important;
		
	}
	.breadcrumb .active .arrow:after {
		border-left-color: #231c19!important;
	}

	.breadcrumb .item.completed {
		color: #3d3f45 !important;
	}
	.breadcrumb .item .arrow:before {
		border-left-color: #e3e5e8;

	} 
	.breadcrumb .item .arrow:after {
		border-left-color: #f6f7f9;
	}
	.breadcrumb .item.completed:after {
		box-shadow: 2px -2px 0 2px #444, 3px -3px 0 2px #444;
	}

	.inquiry-content-complete {
		background-color: white;
		border: solid 1px #e3e5e8;
		padding-top: 55px;
		color: #3d3f45;
		border-radius: 0 0 8px 8px;
		display: block;
		text-align: center;
	}

	.inquiry-content-complete p {
		margin-bottom: 15px;
	}

	.complete-message {
		max-width: 351px;
		margin: 0 auto;
		margin-top: 31px;
		color: #3d3f45;
		font-size: 1.3rem;
		line-height: 1.54;
		margin-bottom: 54px;
		white-space: pre-wrap;
		word-wrap: break-word;
	}
	.complete-btn {
		width: 100%;
		height: 50px;
		border-radius: 4px;
		background-color: #231c19;
		color: #fff;
		display: -ms-flexbox;
		display: flex;
		justify-content: center;
		-ms-flex-align: center;
		margin: 0 auto;
		align-items: center;
		-ms-flex-align: center;
		max-width: 222px;
		margin-bottom: 16px;
	}
	.single-img {
	margin-bottom: 24px;
	}

	.js-preview-main-pic {
	margin: 0 14px;
	}

	.swiper-carousel {
	margin-bottom: 12px;
	}

	.swiper-thumbnail {
	margin-bottom: 24px;
	}

	span.swiper-pagination-bullet.swiper-pagination-bullet-active {
	background-color: rgba(255, 255, 255, 0.7);
	}

	span.swiper-pagination-bullet {
	background: #000;
	opacity: 70%;
	}

	/* Remove default navigation arrows */
	.swiper-button-next::after, .swiper-button-prev::after {
	content: "" !important;
	}

	.swiper-button-next, .swiper-button-prev {
	background-color: rgba(0,0,0,0.6);
	border-radius: 6px;
	}
	.swiper-slide-thumb-active > img {
	border: 2px solid #245BD6;
	}
	.single-img {
		margin-bottom: 30px;
	}

	.preview-description-container {
		box-shadow: inset 0 1px 0 0 #e3e5e8;
		margin: 13px 0 0 0;
	}

	.preview-description-container:nth-of-type(1) {
		box-shadow:none;
	}

	.rotate-fold-icon {
		transform:rotate(180deg);
	}

	.description-wrap img {
		max-width: 100% !important;
		height: fit-content !important;
	}
	.suevey-languages-info-container .main-img {
		width: 100%;
    margin: 0 auto;
    border-radius: 8px;
    border: solid 1px #e3e5e8;
    display: block;
    justify-content: center;
    object-fit: cover;
	}
	.secondary-img {
		width: 100%;
    height: 66.67px;
    object-fit: cover;
	}
	.description-section-main-container .description-section-container:nth-of-type(2) {
		padding-top: 0;
	}
	.description-section-main-container .description-section-container:nth-of-type(2)::before {
		display: none;
	}
</style>
<link
  rel="stylesheet"
  href="https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.css"
/>
<script src="https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.js"></script>
<div class="edit-container">
	<!-- 多言語情報 -->
	<div class="suevey-languages-info-container flexbox js-suevey-languages-info-container">
		<!-- 左側 -->
		<div class="left-container">
			<div class="flex-x-between">
				<!-- 言語選択 -->
					<div id="react-adminlangtabs"></div>
				</nav>
			</div>
			<nav class="line-tab">
				<ul class="pointer" style="background: #ebedf2; width: fit-content;border-radius: 4px;">
					<li class="btn-smaller-square flexbox-center <?php if ($post['page'] == 'input') echo ('active'); ?>"><a class="func-menu font-standard font-family-v4 font-color-v2" href="/admininquiry/inquirydesc?page=input&lang=<?php echo($lang_edit)?>&id=<?php echo $inquiry_id ?>"><?php echo __('admin.inquirydesc.label.input_page'); ?></a></li>
					<?php if ($post['new'] == 0) {?>
						<li class="btn-smaller-square flexbox-center <?php if ($post['page'] == 'confirm') echo ('active'); ?>"><a class="func-menu font-standard font-family-v4 font-color-v2" href="/admininquiry/inquirydesc?page=confirm&lang=<?php echo($lang_edit)?>&id=<?php echo $inquiry_id ?>"><?php echo __('admin.inquirydesc.label.confirm_page'); ?></a></li>
						<li class="btn-smaller-square flexbox-center <?php if ($post['page'] == 'complete') echo ('active'); ?> js-select-complete"><a class="func-menu font-standard font-family-v4 font-color-v2" href="/admininquiry/inquirydesc?page=complete&lang=<?php echo($lang_edit)?>&id=<?php echo $inquiry_id ?>"><?php echo __('admin.inquirydesc.label.complete_page'); ?></a></li>
					<?php } else { ?>
						<li class="btn-smaller-square flexbox-center" style="font-weight:100;color:#231c19;cursor:default;"><?php echo __('admin.inquirydesc.label.confirm_page'); ?></li>
						<li class="btn-smaller-square flexbox-center" style="font-weight:100;color:#231c19;cursor:default;"><?php echo __('admin.inquirydesc.label.complete_page'); ?></li>
					<?php } ?>
				</ul>
			</nav>
			<!-- 完了画面 -->
			<?php if ($post['page'] == 'complete') { ?>
				<div class="background-pale-gray desc-header-container js-desc-header-container" style="padding: 17px 12px 16px 16px;">
				<!-- 基本情報　送信完了 -->
					<div class="js-submited-title-container flexbox-x-axis">
						<p class="font-standard font-family-v3"><?php echo __('admin.inquirydesc.label.basic_info_complete'); ?></p>
						<p class="font-standard font-family-v1 font-color-v2"style="margin: 0 0 0 38px;"><?php echo __('admin.inquirydesc.label.basic_info_hint'); ?></p>
						<div class="right-aligned pointer flexbox-x-axis">
							<p class="js-fold-title font-standard font-family-v1 font-color-v1"><?php echo __('admin.inquirydesc.label.extend'); ?></p>
							<span class="icon-drop-down-close"></span>
						</div>
					</div>
					<?php } ?>
					<div class="title-setting-container js-title-setting-container">
						<!-- 基本情報　送信確認 -->
						<?php if ($post['page'] == 'confirm') { ?>
							<div class="flexbox-x-axis">
								<div class="setting-header"><?php echo __('admin.inquirydesc.label.basic_info_confirm'); ?></div>
								<p class="font-standard font-family-v1 font-color-v2" style="margin: 0 0 0 52px;"><?php echo __('admin.inquirydesc.label.basic_info_hint'); ?></p>
							</div>
						<?php } ?>
						<?php if ($post['page'] == 'complete' || $post['page'] == 'confirm') { ?>
							<div class="flexbox-x-axis survey-title-container" style="display: none;">
								<h4 class="font-standard font-family-v1 survey-space-top-bottom-1 survey-space-top-bottom-2"><?php echo __('admin.inquirydesc.label.inquiry_name'); ?></h4>
								<textarea name="title" class="text-input-longer js-survey-title-input"><?php echo($post['title'])?></textarea>
							</div>
						<?php }?>
						<!-- 基本情報　回答画面-->
						<?php if($post['page'] !== 'complete' && $post['page'] !== 'confirm') {?>
							<div class="flexbox-x-axis">
								<div class="setting-header"><?php echo __('admin.inquirydesc.label.basic_info'); ?></div>
							</div>
							<div class="flexbox-x-axis survey-title-container">
								<h4 class="font-standard font-family-v1 survey-space-top-bottom-1 survey-space-top-bottom-2"><?php echo __('admin.inquirydesc.label.inquiry_title'); ?></h4>
								<textarea name="title" class="text-input-longer js-survey-title-input"><?php echo($post['title'])?></textarea>
							</div>
						<?php }?>
						<?php if (isset($post['description']) && $post['description']) { ?>
						<div class="flexbox-x-axis width-100 flexbox-space-top relative js-input-des-section-parent">
							<h4 class="font-standard font-family-v1 survey-space-top-bottom-1 survey-space-top-bottom-2"><?php echo __('admin.inquirydesc.label.inquiry_description'); ?></h4>
							<div class="summernote-edit js-survey-summary-textarea" data-value='<?php echo $model->summernote_encode($post['description']) ?>' title="<?php echo __('admin.inquirydesc.label.inquiry_description'); ?>" data-upload-image="1">
								<textarea rows="2" class="form-control" value='tip position bottom with image upload' ></textarea>
							</div>
						</div>
						<?php } ?>
						<?php if ($post['page'] !== 'complete' && $post['page'] !== 'confirm') { ?>
							<div class="flexbox-x-axis flexbox-space-top js-survey-header-image-container flexbox-space-top">
								<h4 class="font-standard font-family-v1 survey-space-top-bottom-1 survey-space-top-bottom-2"><?php echo __('admin.inquirydesc.label.main_picture'); ?></h4>
								<div id="sortable" class="js-main-pic-container main-pic-container" style="width: 100%;">
								</div>				
							</div>
						<?php } ?>
					</div>
					<!-- 説明セクション -->
					<div class="description-section-main-container js-description-section-main-container">
						<div class="description-section-header flexbox-x-axis">
							<div class="setting-header"><?= __('admin.inquirydesc.label.description_section') ?></div>
							<?php if ($post['page'] !== 'input') { ?>
								<p class="font-standard font-family-v1 font-color-v2"><?= __('admin.inquirydesc.label.section_info_hint') ?></p>
							<?php } ?>
						</div>
						<?php if($post['description_extra'] != null) { ?>
							<?php foreach($post['description_extra'] as $key => $desc) { ?>
							<div class="description-section-container js-description-section-container">
								<div class="description-section-title-container flexbox-x-axis" style="max-width: 650px;">
									<h2 class="font-standard font-family-v3 survey-space-top-bottom-1" style="margin: 24px auto 24px 0;"><?= __('survey.inquiry.common.label.section') . ($key + 1)	 ?></h2>
									<svg class="js-delete-section delete-section pointer" style="margin:24px 0 24px 0;" xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12">
										<g fill="none" fill-rule="evenodd" stroke-linecap="round">
											<g stroke="#E53361" stroke-width="2">
												<path d="M1 2.5L11 2.5M6 2L6 1M6 8L6 6"/>
												<path stroke-linejoin="round" d="M2 6L2 11 10 11 10 6"/>
											</g>
										</g>
									</svg>
								</div>
								<div class="flexbox-x-axis flexbox-space-top">
									<h4 class="font-standard font-family-v1 survey-space-top-bottom-1 survey-space-top-bottom-2"><?php echo __('admin.inquirydesc.label.description_section_title'); ?></h4>
									<input type="text" class="text-input-longer js-survey-description-section-input" value="<?php echo $desc['title'] ?>">
								</div>
								<div class="flexbox-x-axis survey-contents-container flexbox-space-top relative js-input-des-section-parent">
									<h4 class="font-standard font-family-v1 survey-space-top-bottom-1 survey-space-top-bottom-2"><?php echo __('admin.inquirydesc.label.description_section_content'); ?></h4>
									<div class="summernote-edit js-survey-section-contents-textarea" data-value='<?php echo $model->summernote_encode($desc['description']) ?>' title="<?php echo __('admin.inquirydesc.label.inquiry_description'); ?>" data-upload-image="1">
										<textarea rows="2" class="form-control" value='tip position bottom with image upload' ></textarea>
									</div>
								</div>
								<div class="flexbox-x-axis flexbox-space-top">
									<label class="survey-space-top-bottom-2"><?php echo __('admin.inquirydesc.label.initialization'); ?></label>
									<div>
										<div class="talkappi-radio" data-value="<?php echo ($desc['fold'] ? $desc['fold']: "show") ?>" data-source='{"show":"<?php echo __('admin.inquirydesc.label.initialization_show'); ?>", "fold":"<?php echo __('admin.inquirydesc.label.initialization_fold'); ?>"}'></div>
									</div>
								</div>
							</div>
							<?php } ?>
						<?php } ?>
						<!-- 説明セクション コピー用 -->
						<div class="description-section-container js-description-section-container for-clone display-none">
							<div class="description-section-title-container flexbox-x-axis" style="max-width: 650px;">
								<h2 class="font-standard font-family-v3 survey-space-top-bottom-1" style="margin:24px auto 24px 0;"><?php echo __('admin.inquirydesc.label.description_section'); ?></h2>
								<svg class="js-delete-section delete-section pointer" style="margin:24px 0 24px 0;" xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12">
									<g fill="none" fill-rule="evenodd" stroke-linecap="round">
										<g stroke="#E53361" stroke-width="2">
											<path d="M1 2.5L11 2.5M6 2L6 1M6 8L6 6"/>
											<path stroke-linejoin="round" d="M2 6L2 11 10 11 10 6"/>
										</g>
									</g>
								</svg>
							</div>
							<div class="flexbox-x-axis survey-title-container">
								<h4 class="font-standard font-family-v1 survey-space-top-bottom-1 survey-space-top-bottom-2"><?php echo __('admin.inquirydesc.label.description_section_title'); ?></h4>
								<input type="text" class="text-input-longer js-survey-description-section-input">
							</div>
							<div class="flexbox-x-axis survey-contents-container flexbox-space-top relative js-input-des-section-parent">
								<h4 class="font-standard font-family-v1 survey-space-top-bottom-1 survey-space-top-bottom-2"><?php echo __('admin.inquirydesc.label.description_section_content'); ?></h4>
								<div class="summernote-edit js-survey-section-contents-textarea" data-value='' title="<?php echo __('admin.inquirydesc.label.inquiry_description'); ?>" data-upload-image="1">
									<textarea rows="2" class="form-control" value='tip position bottom with image upload' ></textarea>
								</div>
							</div>
							<div class="flexbox-x-axis flexbox-space-top">
								<label class="survey-space-top-bottom-2"><?php echo __('admin.inquirydesc.label.initialization'); ?></label>
								<div>
									<div class="talkappi-radio" data-value="show" data-source='{"show":"<?php echo __('admin.inquirydesc.label.initialization_show'); ?>", "fold":"<?php echo __('admin.inquirydesc.label.initialization_fold'); ?>"}'></div>
								</div>
							</div>							
						</div>
					</div>
					<!-- 「説明セクションを追加する」 -->
					<div class="flexbox-x-axis js-survey-add-section survey-space-top-bottom-4 pointer">
						<svg class="add-icon" xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12">
							<g fill="none" fill-rule="evenodd" stroke-linecap="round" stroke-linejoin="round">
								<g stroke="#245BD6" stroke-width="2">
									<g>
										<path d="M6 10L6 2" transform="translate(-997 -453) translate(997 453) matrix(1 0 0 -1 0 12)"/>
										<path d="M6 10L6 2" transform="translate(-997 -453) translate(997 453) matrix(0 -1 -1 0 12 12)"/>
									</g>
								</g>
							</g>
						</svg>
						<p class="font-standard font-family-v2"><?php echo __('admin.inquirydesc.label.description_section_add'); ?></p>
					</div>
					<?php if ($post['page'] == 'complete') { ?>
				</div>
				<!-- 基本情報 -->
				<div class="title-setting-container js-title-setting-container">
					<!-- タイトル -->
					<div class="flexbox-x-axis survey-title-container" style="margin: 24px 0 0 0;">
						<h4 class="font-standard font-family-v1 survey-space-top-bottom-1 survey-space-top-bottom-2" style="margin: 0 0 0 10px !important;"><?php echo __('admin.inquirydesc.label.add_description_section_title'); ?></h4>
						<textarea class="text-input-longer js-complete-title"><?php echo $post['complete_area']['title'] ?></textarea>
					</div>
					<!-- 画像 -->
					<div class="flexbox-x-axis flexbox-space-top js-survey-header-image-container flexbox-space-top">
						<h4 class="font-standard font-family-v1 survey-space-top-bottom-1 survey-space-top-bottom-2"><?php echo __('admin.inquirydesc.label.add_description_section_picture'); ?></h4>
						<div class="talkappi-upload" data-name="image_base64" data-type="img" data-label="<?php echo ($post['complete_area']['icon'])?>" data-url="<?php echo $post['complete_area']['icon']?>" data-max-size="2"></div>
					</div>
					<!-- 概要 -->
					<div class="flexbox-x-axis width-100 flexbox-space-top relative js-input-des-section-parent">
						<h4 class="font-standard font-family-v1 survey-space-top-bottom-1 survey-space-top-bottom-2"><?php echo __('admin.inquirydesc.label.add_description_section_desc'); ?></h4>
						<textarea class="survey-summary-textarea js-complete-summary-textarea js-input-des-section"><?php echo $post['complete_area']['description'] ?></textarea>
					</div>
				</div>
				<!-- アクション -->
				<div class="description-section-main-container js-description-section-main-container js-description-action-main-container" style="<?php if(!$displayAction) echo 'display:none;' ?>">
					<div class="description-section-container js-description-action-container cloned">
						<div class="description-section-title-container flexbox-x-axis" style="max-width: 650px;">
							<h2 class="font-standard font-family-v3 survey-space-top-bottom-1" style="margin: 24px auto 24px 0;"><?php echo __('admin.inquirydesc.label.actions'); ?></h2>
							<svg class="js-delete-action delete-section pointer" style="margin:24px 0 24px 0;" xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12">
								<g fill="none" fill-rule="evenodd" stroke-linecap="round">
									<g stroke="#E53361" stroke-width="2">
										<path d="M1 2.5L11 2.5M6 2L6 1M6 8L6 6"/>
										<path stroke-linejoin="round" d="M2 6L2 11 10 11 10 6"/>
									</g>
								</g>
							</svg>
						</div>
						<div class="flexbox-x-axis flexbox-space-top">
							<h4 class="font-standard font-family-v1 survey-space-top-bottom-1 survey-space-top-bottom-2"><?php echo __('admin.inquirydesc.label.display_text'); ?></h4>
							<input type="text" class="text-input-longer js-action-input" value='<?php if(isset($post['actions']) && isset($post['actions'][0]) && isset($post['actions'][0]['title'])) echo $post['actions'][0]['title']; ?>'>
						</div>
						<div class="flexbox-x-axis flexbox-space-top">
							<h4 class="font-standard font-family-v1 survey-space-top-bottom-1 survey-space-top-bottom-2"><?php echo __('admin.inquirydesc.label.destination_url'); ?></h4>
							<input type="text" class="text-input-longer js-description-action-input" value='<?php if(isset($post['actions']) && isset($post['actions'][0]) && isset($post['actions'][0]['url'])) echo $post['actions'][0]['url']; ?>' placeholder="<?php echo __('admin.inquirydesc.label.destination_url_placeholder'); ?>">
						</div>
					</div>
				</div>
				<!-- 「アクションを追加する」 -->
				<div class="flexbox-x-axis js-add-action survey-space-top-bottom-4 pointer" style="<?php if($displayAction) echo 'display:none;'?>">
					<svg class="add-icon" xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12">
						<g fill="none" fill-rule="evenodd" stroke-linecap="round" stroke-linejoin="round">
							<g stroke="#245BD6" stroke-width="2">
								<g>
									<path d="M6 10L6 2" transform="translate(-997 -453) translate(997 453) matrix(1 0 0 -1 0 12)"/>
									<path d="M6 10L6 2" transform="translate(-997 -453) translate(997 453) matrix(0 -1 -1 0 12 12)"/>
								</g>
							</g>
						</g>
					</svg>
					<p class="font-standard font-family-v2"><?php echo __('admin.inquirydesc.label.action_add'); ?></p>
				</div>
			<!-- 確認画面 -->
			<?php } else if($post['page'] == 'confirm') { ?>
			<?php } ?>

			<!-- 基本情報　回答画面-->
			<?php if($post['page'] !== 'complete' && $post['page'] !== 'confirm') {?>
				<div class="description-section-container js-description-section-container">
					<div class="description-section-title-container flexbox-x-axis" style="max-width: 650px;">
						<h2 class="font-standard font-family-v3 survey-space-top-bottom-1" style="margin: 24px auto 24px 0;"><?php echo __('admin.inquirydesc.label.brief'); ?></h2>
					</div>
					<div class="flexbox-x-axis survey-title-container flexbox-space-top" style="margin-top:-10px;">
						<h4 class="font-standard font-family-v1 survey-space-top-bottom-1 survey-space-top-bottom-2" style="position:relative;"></h4>
						<div class="line-content">
							<div class="talkappi-radio js-brief" data-name="use_brief" data-value="<?php echo $post['use_brief']?>" data-source='{"0":"<?php echo __('admin.inquiry.label.support_flg_no'); ?>", "1":"<?php echo __('admin.inquiry.label.support_flg_yes'); ?>"}'></div>
						</div>
					</div>
					<div class="flexbox-x-axis survey-title-container flexbox-space-top">
						<h4 class="font-standard font-family-v1 survey-space-top-bottom-1 survey-space-top-bottom-2" style="position:relative;"></h4>
						<div class="line-content">
							<?php echo __('admin.inquirydesc.label.brief_tip'); ?>
						</div>
					</div>
					<div class="flexbox-x-axis survey-title-container flexbox-space-top js-sell-point" <?php if ($post['sell_point'] == '') echo('style="display:none"');  ?>>
						<h4 class="font-standard font-family-v1 survey-space-top-bottom-1 survey-space-top-bottom-2"></h4>
						<textarea class="survey-summary-textarea" name="sell_point"><?php echo $post['sell_point'] ?></textarea>
					</div>
				</div>
			<?php }?>
			<!-- 翻訳 -->
			<?php if (count($support_lang_cd) > 1) { ?>
			<div id="react-multilingualreflect" style="margin-top:32px"></div>
			<?php } ?>			
			<!-- ボタン　コンテナ -->
			<div class="submit-btn-container" style="margin: 60px 0 0 134px;">
				<div type="button" class="btn-larger btn-blue" id="saveButton"><?php echo __('admin.common.button.save'); ?></div>
				<div class="btn-larger btn-gray-black" id="previewButton">
					<a class="flexbox-center height-100 width-100" href="<?php echo $verify_url ?>" target="_blank" style="color: #000;" onfocus="this.blur();"><?php echo __('admin.common.button.verify'); ?></a>
				</div>
				<div class="btn-larger btn-white js-back-to-inquirys"><?php echo __('admin.common.button.return_to_list'); ?></div>
				<?php //echo $debug ?>
			</div>
		</div>
		<!-- 右側 -->
		<div class="right-container">
			<header class="preview-sm-header flexbox-x-axis">
				<h4 class="font-standard font-family-v2" style="margin: 0 auto 0 0;"><?php echo __('admin.inquirydesc.label.preview'); ?></h4>
			</header>
			<main class="preview-sm-main" style="width: 320px;margin: 0;border: 1px solid #e3e5e8;height: 550px;overflow-y: scroll;">
				<div style="background-color: <?php echo $config['theme-bk-color']?>"></div>
				<!-- ロゴ、タイトル -->
				<div style="margin:0 14px;">
					<div class="hor-center"><img src="<?php echo $logo_url . "?" . $_item_upd_timestamp?>" alt="" class="desc-preview-logo" style="width: 44px; height: 44px; margin:14px 0 7px 0;border: 1px solid #C8CACE;"></div>
					<h4 class="font-standard font-family-v3 font-size-v2 js-preview-title hor-center" style="margin:7px 0 7px 0;white-space: pre-wrap;word-wrap: break-word;"><?php echo $post['title'] ?></h4>
				</div>
				<!-- メイン画像 -->
				<div style="text-align: center;" class="js-preview-main-pic">
					<div class="swiper swiper-carousel">
						<div class="swiper-wrapper js-swiper-wrapper">
						<?php
						$image_array = [];
						if ($post['inquiry_image'] != null && $post['inquiry_image'] != '[]') {
							// Check if there are multiple images or only one
							$image_array = json_decode($post['inquiry_image'], true);
							if ($image_array==null) $image_array=$post['inquiry_image'];
							if (!is_array($image_array)) $image_array = explode("あ",$image_array);
							$main_image_src = $image_array[0];
						}
						?>
						<?php 
							foreach ($image_array as $image) {
								echo ('<div class="swiper-slide">');
								echo ('<img style="width: 100%; margin: 0;" class="main-img" src="' . $image . '?' . $_item_upd_timestamp . '">');
								echo('</div>');
							}
						?>
						</div>
						<div class="swiper-button-next js-swiper-button-next" style="display: none;"><img class="carousel-icon" src="/assets/admin/images/icon-arrow-next.svg" /></div>
						<div class="swiper-button-prev js-swiper-button-prev" style="display: none;"><img class="carousel-icon" src="/assets/admin/images/icon-arrow-prev.svg" /></div>
						<div class="swiper-pagination"></div>
					</div>
					<div thumbsSlider="" class="swiper swiper-thumbnail">
						<div class="swiper-wrapper js-swiper-wrapper js-preview-swiper-small">
						<?php
						foreach ($image_array as $image) {
							echo('<div class="swiper-slide">');
							echo('<img class="secondary-img" src="' . $image . '?' . $_item_upd_timestamp . '">');
							echo('</div>');
						}
						?>
						</div>
					</div>
				</div>
				<!-- 概要以下 -->
				<section class="description-wrap js-preview-main-container" style="margin: 0 14px;">
					<div class="preview-description-container js-preview-main-desc" style="<?php if(!trim($post['description'])) echo "display: none;"?>">
						<div class="flexbox-x-axis" style="height: 38px;">
							<h4 class="font-standard font-size-v3 font-family-v4"><?php echo __('survey.index.label.content') ?></h4>
							<span class="js-preview-fold icon-more-info-on right-aligned"></span>
						</div>
						<div class="font-standard font-size-v4 font-family-v1 font-color-v2 survey-space-top-3 js-preview-summary-contents"style="white-space: pre-wrap;word-wrap: break-word;"><?php echo $post['description'] ?></div>
					</div>
					<?php if ($post['description_extra'] != null) {
						foreach($post['description_extra'] as $desc) { ?>
							<div class="preview-description-container js-preview-description-container">
								<div class="flexbox-x-axis" style="height: 38px;">
									<h4  class="font-standard font-size-v3 font-family-v4 js-preview-description-title"><?php echo $desc['title'] ?></h4>
									<span class="js-preview-fold icon-more-info-on right-aligned <?php if($desc['fold']=="fold") echo "rotate-fold-icon" ?>" ></span>
								</div>
								<div class="font-standard font-size-v4 font-family-v1 font-color-v2 survey-space-top-3 js-preview-description-contents preview-description-contents" style="white-space: pre-wrap;word-wrap: break-word;<?php echo ($desc['fold']=="fold" ? "display:none;" :"") ?>"><?php echo($desc['description'])?></div>
							</div>
					<?php }}?>
				</section>
				<div class="preview-description-container js-preview-description-container for-clone display-none">
					<div class="flexbox-x-axis" style="height: 38px;">
						<h4  class="font-standard font-size-v3 font-family-v4 js-preview-description-title"></h4>
						<span class="js-preview-fold icon-more-info-on right-aligned"></span>
					</div>
					<div class="font-standard font-size-v4 font-family-v1 font-color-v2 survey-space-top-3 js-preview-description-contents preview-description-contents" style="white-space: pre-wrap;word-wrap: break-word;"></div>
				</div>
				<!-- 完了画面 -->
				<?php if ($post['page'] == 'complete') { ?>
					<!-- 進捗リスト -->
					<div class="inquiry-content-wrapper">
						<div class="container" style="width: 100%;padding: 0;">
							<div class="breadcrumb">
								<a href="#" class="item completed">
									<span class="arrow"></span>
									<!-- <span class="hide-mobile"><span class="ste ps-text">&nbsp;</span><?php echo  __('inquiry.common.tab.step1')  ?></span> -->
									<span class="show-mobile"><span class="steps-text">&nbsp;</span><?php echo  __('inquiry.common.tab.step1')  ?></span>
								</a>
								<a href="#" class="item completed">
									<span class="arrow"></span>
									<!-- <span class="hide-mobile"><span class="steps-text">&nbsp;</span><?php echo  __('inquiry.common.tab.step2')  ?></span> -->
									<span class="show-mobile"><span class="steps-text">&nbsp;</span><?php echo  __('inquiry.common.tab.step2')  ?></span>
								</a>
								<a href="#" class="item active">
									<!-- <span class="hide-mobile"><span class="steps-text">&nbsp;</span><?php echo  __('inquiry.common.tab.step3')  ?></span> -->
									<span class="show-mobile"><span class="steps-text">&nbsp;</span><?php echo  __('inquiry.common.tab.step3')  ?></span>
								</a>
							</div>
						</div>
						<!-- 受付完了メッセージ -->
						<div class="inquiry-content-complete" style="border: none;">
							<p class="js-preview-complete-title" style="white-space: pre-wrap;word-wrap: break-word;"><?php echo $post['complete_area']['title']; ?></p>
							<div class="js-preview-complete-image">
								<img class = "js-preview-complete-image-img" src="<?php echo $post['complete_area']['icon']?>" alt="" style="width:85px;height:85px;">
							</div>
							<div class="complete-message js-preview-complete-summary-contents" style="padding:0 34px;white-space: pre-wrap;word-wrap: break-word;text-align: center;"><?php echo $post['complete_area']['description'];?></div>
							<a style="text-decoration: none !important; <?php if(!$displayAction) echo 'display:none;' ?>" href='<?php if(isset($post['actions']) && isset($post['actions'][0]) && isset($post['actions'][0]['url'])) echo $post['actions'][0]['url'] ?>'class="js-complete-btn cloned">
								<div class="complete-btn primary-btn complete-submit-button js-preview-action-btn">
									<?php if(isset($post['actions']) && isset($post['actions'][0]) && isset($post['actions'][0]['title'])) echo $post['actions'][0]['title'] ?>
								</div>
							</a>
							<a style="text-decoration: none !important;" href="" class="js-complete-btn for-clone display-none">
								<div class="complete-btn primary-btn complete-submit-button js-preview-action-btn"></div>
							</a>
						</div>
						<!-- 確認画面 -->
					<?php } else if($post['page'] == 'confirm') { ?>
						<!-- 進捗リスト -->
						<div class="inquiry-content-wrapper">
							<div class="container" style="width: 100%;padding: 0;">
								<div class="breadcrumb">
									<a href="#" class="item completed">
										<span class="arrow"></span>
										<!-- <span class="hide-mobile"><span class="steps-text">&nbsp;</span><?php echo  __('inquiry.common.tab.step1')  ?></span> -->
										<span class="show-mobile"><span class="steps-text">&nbsp;</span><?php echo  __('inquiry.common.tab.step1')  ?></span>
									</a>
									<a href="#" class="item active">
										<span class="arrow"></span>
										<!-- <span class="hide-mobile"><span class="steps-text">&nbsp;</span><?php echo  __('inquiry.common.tab.step2')  ?></span> -->
										<span class="show-mobile"><span class="steps-text">&nbsp;</span><?php echo  __('inquiry.common.tab.step2')  ?></span>
									</a>
									<a href="#" class="item completed">
										<!-- <span class="hide-mobile"><span class="steps-text">&nbsp;</span><?php echo  __('inquiry.common.tab.step3')  ?></span> -->
										<span class="show-mobile"><span class="steps-text">&nbsp;</span><?php echo  __('inquiry.common.tab.step3')  ?></span>
									</a>
								</div>
							</div>
						</div>
					</div>
				<?php } ?>
			</main>
		</div>
	</div>
</div>