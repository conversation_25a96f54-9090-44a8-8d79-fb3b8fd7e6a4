<?php echo $menu ?>
<input type="hidden" name="inquiry_id" id="inquiry_id" value="<?php echo $inquiry_id ?>" />
<div class="content-container white border">
	<div class="form-body">
        <div class="form-group">
			<div class="col-md-2" style="max-width: 150px;">
				<?php 
					$lang_cd_list_after = [];
					foreach ($lang_cd_list as $key => $value) {
						if ($key == '') {
							$lang_cd_list_after[$key] = __('admin.inquiryresult.label.language_default');
						} else {
							$lang_cd_list_after[$key] = $value;
						}
					}
				?>
				<?php echo Form::select('lang_cd', $lang_cd_list_after, $post['lang_cd'], array('id' => 'lang_cd', 'class' => 'form-control')) ?>                    
			</div>     
			<div class="col-md-6" style="display: flex;align-items: center; max-width: 300px;">
				<input name="start_date" class="talkappi-datepicker" id="start_date" value="<?php echo(substr($post['start_date'], 0, 10))?>"/>
				<p style="margin-right: 10px;">〜</p>
				<input name="end_date" class="talkappi-datepicker" id="end_date" value="<?php echo(substr($post['end_date'], 0, 10))?>"/>
			</div>
			<div class="col-md-4">
				<button type="submit" class="btn-smaller btn-blue js-action-search"><span class="icon-filter"></span><?php echo __('admin.common.label.narrowdown'); ?></button>
			</div>
		</div>
	</div>
	<div class="section-container bottom-line">
		<h2><?php echo __('admin.inquiryrefer.label.inflow_route'); ?></h2>
		<table class="table table-striped table-bordered table-hover js-data-table" id="domain-refer-url">
			<thead>
				<th><?php echo __('admin.inquiryrefer.label.referent'); ?></th>
				<th><?php echo __('admin.inquiryrefer.label.answer_start'); ?></th>
				<th><?php echo __('admin.inquiryrefer.label.answer_end'); ?></th>
			</thead>
			<tbody>
			<?php
				foreach($domain_refers as $k=>$v) {
					if ($k == '') $k = __('admin.inquiryrefer.label.not_set');
					echo('<tr><td>' . $k . '</td>');
					echo('<td>' . $v['input'] . '</td>');
					echo('<td>' . $v['complete'] . '</td></tr>');
				}
			?>
			</tbody>
		</table>
	</div>
	<div class="section-container bottom-line">
		<h2><?php echo __('admin.inquiryrefer.label.inflow_url'); ?></h2>
		<table class="table table-striped table-bordered table-hover js-data-table" id="refer-url">
			<thead>
				<th><?php echo __('admin.inquiryrefer.label.referent.url'); ?></th>
				<th><?php echo __('admin.inquiryrefer.label.answer_start'); ?></th>
				<th><?php echo __('admin.inquiryrefer.label.answer_end'); ?></th>
			</thead>
			<tbody>
			<?php
			foreach($refers as $k=>$v) {
				if ($k == '') $k = __('admin.inquiryrefer.label.not_set');
				echo('<tr><td>' . $k . '</td>');
				echo('<td>' . $v['input'] . '</td>');
				echo('<td>' . $v['complete'] . '</td></tr>');
			}
			?>
			</tbody>
		</table>
	</div>
	<div class="section-container bottom-line">
		<h2><?php echo __('admin.common.label.user'); ?></h2>
		<table class="table table-striped table-bordered table-hover js-data-table" id="refer-city">
			<thead>
				<th><?php echo __('admin.inquiryrefer.label.location_detail'); ?></th>
				<th><?php echo __('admin.inquiryrefer.label.answer_start'); ?></th>
				<th><?php echo __('admin.inquiryrefer.label.answer_end'); ?></th>
			</thead>
			<tbody>
			<?php
			foreach($citys as $k=>$v) {
				if ($k == '') $k = __('admin.inquiryrefer.label.not_set');
				echo('<tr><td>' . $k . '</td>');
				echo('<td>' . $v['input'] . '</td>');
				echo('<td>' . $v['complete'] . '</td></tr>');
			}
			?>
			</tbody>
		</table>
	</div>
</div>

