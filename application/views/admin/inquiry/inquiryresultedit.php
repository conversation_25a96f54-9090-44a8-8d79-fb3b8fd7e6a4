<input type="hidden" id="result_id" name="result_id" value="<?php echo ($result_id) ?>" />
<input type="hidden" id="order_info" name="order_info" value="" />
<?php echo $menu ?>
<div class="content-container white border">
	<div class="section-container">
		<?php
		$ptr = 1;
		foreach ($inquiry_entry as $entry) {
			if (!isset($inquiry_result_kv[$entry->no])) continue;
			echo ('<div style="font-size:12px;margin-bottom:4px;">' . $ptr++ . '. ' . $entry->title . '</div>');
			echo ('<div class="js-entry-result" data-entry-no="' . $entry->no . '" style="color:royalblue;font-size:14px;margin-bottom:8px;">');
			if ($entry->entry_type_cd == 'txt' || $entry->entry_type_cd == 'spl') {
				$input_rules = json_decode($entry->input_rules, true);
				if (!is_array($input_rules)) $input_rules = [];
				if ($input_rules['type'] == 'tel') {
					echo ('<input type="text" class="form-control" pattern="(?:\+?\d+-{0,1})?\d+(?:-{0,1}\d+){2}$|^\+?\d+" data-title="tel" value="' . $inquiry_result_kv[$entry->no]->entry_data . '" style="width:200px;" />');
				} else if ($input_rules['type'] == 'mail') {
					echo ('<input type="email" class="form-control" data-title="mail" value="' . $inquiry_result_kv[$entry->no]->entry_data . '" style="width:400px;" />');
				} else if ($input_rules['type'] == 'name_separate') {
					$v = json_decode($inquiry_result_kv[$entry->no]->entry_result, true);
					echo ('<div style="display:flex;">');
					echo ('<input type="text" class="form-control" data-title="last_name" value="' . $v['last_name'] . '" style="width:200px;" />');
					echo ('<input type="text" class="form-control" data-title="first_name" value="' . $v['first_name'] . '" style="width:200px;" />');
					echo ('</div>');
					if ($lang_cd == 'ja') {
						echo ('<div style="display:flex;">');
						echo ('<input type="text" class="form-control" data-title="last_name_kana" value="' . $v['last_name_kana'] . '" style="width:200px;" />');
						echo ('<input type="text" class="form-control" data-title="first_name_kana" value="' . $v['first_name_kana'] . '" style="width:200px;" />');
						echo ('</div>');
					}
				} else if ($input_rules['type'] == 'name_full') {
					$v = json_decode($inquiry_result_kv[$entry->no]->entry_result, true);
					echo ('<div style="display:flex;">');
					echo ('<input type="text" class="form-control" data-title="full_name" value="' . $v['full_name'] . '" style="width:200px;" />');
					echo ('</div>');
					if ($lang_cd == 'ja') {
						echo ('<div style="display:flex;">');
						echo ('<input type="text" class="form-control" data-title="furigana" value="' . $v['furigana'] . '" style="width:200px;" />');
						echo ('</div>');
					}
				} else if ($input_rules['type'] == 'address') {
					$v = json_decode($inquiry_result_kv[$entry->no]->entry_result, true);
					if ($lang_cd != 'ja') {
						echo ('<div style="display:flex;">');
						echo ('<input type="text" class="form-control" data-title="country" value="' . $v['country'] . '" style="width:200px;" />');
						echo ('</div>');
					}
					echo ('<div style="display:flex;">');
					echo ('<input type="text" class="form-control" data-title="postcode" value="' . $v['postcode'] . '" style="width:200px;" />');
					echo ('<input type="text" class="form-control" data-title="address1" value="' . $v['address1'] . '" style="width:200px;" />');
					echo ('</div>');
					echo ('<div style="display:flex;">');
					echo ('<input type="text" class="form-control" data-title="address2" value="' . $v['address2'] . '" style="width:200px;" />');
					echo ('<input type="text" class="form-control" data-title="address3" value="' . $v['address3'] . '" style="width:200px;" />');
					echo ('</div>');
					echo ('<div style="display:flex;">');
					echo ('<input type="text" class="form-control" data-title="address4" value="' . $v['address4'] . '" style="width:400px;" />');
					echo ('</div>');
				} else {
					echo ($inquiry_result_kv[$entry->no]->entry_data);
				}
			} else {
				echo ($inquiry_result_kv[$entry->no]->entry_data);
			}
			echo ('</div>');
		}
		?>
	</div>
	<div class="actions-container" style="margin: 60px 0 0 140px;">
		<div class="btn-larger btn-blue x-first js-action-save">保存</div>
		<div class="btn-larger btn-gray-black js-action-reset">画面内容リセット</div>
		<div class="btn-larger btn-white js-action-back">一覧に戻る</div>
	</div>
</div>