<!-- 共通：ページ遷移タブ(基本設定、多言語情報、問合せフォーム質問) -->
<?php 
	$to_lang_cds = [];
	foreach ($support_lang_cd as $k => $v) {
		$to_lang_cds[] = [
			"lang_cd" => $k,
			"lang" => $v
		];
	}
?>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/1.12.4/jquery.min.js"></script>
<script src="/assets/common/react/components/blocks/adminlangtabs.bundle.js"></script>
<script src="/assets/common/react/components/blocks/multilingualreflect.bundle.js"></script>
<script>
	const _inquiry_div = "<?php echo $inquiry_div ?>";
	const _inquiry_data = <?php echo $inquiry->inquiry_data ?>;
	var _user_template_flg = <?php echo $user_template_flg?>;
	var _inquiry_entries = <?php echo $entries?>;
	var _inquiry_sections = <?php echo $sections?>;
	var _inquiry_branchs = <?php echo $branchs?>;
	var _coupons = <?php echo json_encode($coupons, JSON_UNESCAPED_UNICODE)?>;
	var _maximums_all = <?php echo json_encode($maximum_all, JSON_UNESCAPED_UNICODE)?>;
	var _maximums_1d1h = <?php echo json_encode($maximum_1d1h, JSON_UNESCAPED_UNICODE)?>;
	var _maximums_1d = <?php echo json_encode($maximum_1d, JSON_UNESCAPED_UNICODE)?>;
	var _maximums_1h = <?php echo json_encode($maximum_1h, JSON_UNESCAPED_UNICODE)?>;
	var _payment_display = <?php echo json_encode($payment_display, JSON_UNESCAPED_UNICODE)?>;
	var _json_payment_service = <?php echo json_encode($json_payment_service, JSON_UNESCAPED_UNICODE)?>;
	var all_users = <?php echo($post['all_users']) ?>;
	var _label_id = <?php echo $label_id ?>;
	var _labels = <?php echo json_encode($labels, JSON_UNESCAPED_UNICODE | JSON_HEX_APOS | JSON_HEX_QUOT) ?>;
	var _inquiry_res = <?php echo $inquiry_res->labels ?>;
	var _inquiry_actions = <?php echo json_encode($entry_action) ?>;
	var _user_mail_template_list = <?php echo json_encode($user_mail_template_list, JSON_UNESCAPED_UNICODE) ?>;
	var _user_mail_template = "<?php echo $inquiry->user_mail_template ?>";
	const _maximum_category_price = <?php echo json_encode($maximum_category_price, JSON_UNESCAPED_UNICODE)?>;
	const _item_upd_timestamp = '<?php echo str_replace(['-',' ',':'], '', $inquiry->upd_time) ?>';
	const _from_lang_cd = '<?php echo $lang_cd ?>';
	const _to_lang_cds = <?php echo json_encode($to_lang_cds, JSON_UNESCAPED_UNICODE) ?>;
	const _is_order = <?= $inquiry->class_cd == '04' ? 'true' : 'false' ?>;
	jQuery(document).ready(function($) {
        // 表示言語
        const displayedLangs = Object.values(<?php echo json_encode($lang_display) ?>);
        // サポート言語
        const allLangs = Object.keys(<?php echo json_encode($support_lang_cd) ?>);
        // 非表示言語
        const undisplayedLangs = allLangs.filter(key => !displayedLangs.includes(key));

        window.talkappi_admin_setupAdminLangTabs({
            displayed: displayedLangs,
            undisplayed: undisplayedLangs,
            url: `/admininquiry/inquiryentry?id=<?php echo $inquiry_id ?>&lang=`,
            active: '<?php echo $lang_cd ?>',
        });
    });
</script>
<style>
	.limit-mng-layer {
		margin: 10px 0 0 16px !important;
	}

	.js-action-select-container .dropdown-connection {
		cursor: default;
		pointer-events: none;
	}
	.js-action-select-container .dropdown-connection img {
		display: none;
	}

	.js-action-select-container:nth-of-type(2) .dropdown-connection {
		cursor: pointer;
		pointer-events: auto;
	}
	.js-action-select-container:nth-of-type(2) .dropdown-connection img {
		display: inline-block;
	}

	.js-action-select-container:nth-of-type(1) .dropdown-connection {
		display: none;
	}

	/* #3D3F45 */
	.js-action-select-container .dropdown-connection .dropdown-selected-text {
		color: #3D3F45;
	}

	.action-modal-background {
		height: 100vh;
		width: 100vw;
		background: rgba(61, 63, 69, 0.72);
		z-index: 990;
		position: fixed;
		top: 0;
		left: 0;
	}
	.action-setting-container .js-action-conditions-dropdown,
	.action-setting-container .js-dropdown-conditions{
		background: #E3E5E8;
	}

	.action-setting-container .js-action-conditions-dropdown,
	.action-setting-container .js-dropdown-conditions{
		background: #E3E5E8;
	}

	.branch-select-num {
		min-width: 85px;
	}

	.long-text-container {
		display: flex;
		align-items: center;
	}

	.container-padding {
		padding: 0 24px;
	}

	.modal-container-context {
		height: 100%;
		padding: 0;
		display: flex;
		flex-direction: column;
		gap: 8px;
	}
	.survey-branch-container {
		margin: 0;
	}

	.add-branch-pulldown-container {
		gap: 8px;
	}

	.btn-add-branch {
		margin: 0;
	}

	.btn-smaller.js-add-button {
		margin: 0;
	}

	.js-dropdown-option[data-value="-1"]{
		display: none;
	}

	.survey-branch-container .talkappi-dropdown-options {
		width: auto; 
	}

	.common-modal-container {
		height: 100%;
	}

	.common-modal-content {
		height: 100%;
		max-height: calc(100% - 55px);
	}
	.modal-select-container {
		height: 100%;
	}
	.modal-select-container .select .list {
		max-height: calc(100% - 87px);
	}

	.survey-branch-container:first-child .branch-or-title{
		display: none;
	}
	.survey-branch-container input{
		margin: 12px 0 0 0;
		border-radius: 0px 4px 4px 0;
	}
	.survey-branch-container input:first-child{
		margin: 12px 0 0 12px;
		border-radius: 4px;
	}

	.js-action-modal .modal-container-context::-webkit-scrollbar {
		display: block;
		width: 10px;
	}

	.js-action-modal .modal-container-context::-webkit-scrollbar-thumb {
		border-radius: 8px;
	}
	.chk-limit-num div {
		margin: 0 6px 0 0;
	}
	.js-dropdown-option[data-value="-1"]{
		display: none;
	}
	.inquiry-action-mail-row {
		display: flex;
		align-items: flex-start;
	}
	.inquiry-action-mail-title {
		background: #A1A4AA;
		color: #FFF;
		width: fit-content;
		padding: 1px 7px;
		border-radius: 3px;
		min-width: 90px;
		display: flex;
		justify-content: center;
	}
</style>

<input type="hidden" id="inquiry_entries" name="inquiry_entries" value='' />
<input type="hidden" id="inquiry_sections" name="inquiry_sections" value='' />
<input type="hidden" id="inquiry_branchs" name="inquiry_branchs" value='' />
<input type="hidden" id="lang_cd" name="lang_cd" value='<?php echo $lang_cd?>' />
<input type="hidden" id="message" name="message" value='<?php echo $message?>' />
<input type="hidden" id="inquiry_id" name="inquiry_id" value="<?php echo $inquiry_id?>" />
<input type="hidden" id="ex_labels" name="ex_labels" value='<?php echo json_encode($labels, JSON_UNESCAPED_UNICODE | JSON_HEX_APOS | JSON_HEX_QUOT) ?>' />

<!-- summernote -->
<link rel="stylesheet" type="text/css" href="/assets/global/plugins/bootstrap-wysihtml5/bootstrap-wysihtml5.css"/>
<link rel="stylesheet" type="text/css" href="/assets/global/plugins/bootstrap-markdown/css/bootstrap-markdown.min.css">

<?php echo $menu?>
<?php 
	$is_order = false;
	if($inquiry->class_cd == '04'){
		$is_order = true;
	}
?>

<div class="edit-container">
	<!-- 問合せフォーム質問 -->
	<div class="survey-survey-questions-container flexbox js-survey-survey-questions-container">
		<div class="left-container js-left-container sectionSortable"data-fold-survey="false">
			<div class="flex-x-between">
				<div id="react-adminlangtabs"></div>
			</div>
			<div class="survey-space-top-1">
				<!-- 問合せフォーム名 -->
				<div class="survey-survey-questions-lines-container">
					<h4 class="font-standard font-family-v1 survey-space-top-bottom-1" style="width: 109px;"><?php echo __('admin.inquirydesc.label.inquiry_name') ?></h4>
					<div class="survey-title-input-container relative">
						<p type="text" class="survey-title-input flexbox-x-axis js-survey-title-input"><?php if (isset($inquiry_title)) echo($inquiry_title) ?></p>
						<a class="font-standard font-family-v2 font-color-v1 survey-position-absolute-v1 js-edit-desc"><?php echo __('admin.common.label.edit') ?></a>
					</div>
				</div>
				<!-- 問合せフォーム概要 -->
				<?php if (isset($inquiry_description)) { ?>
					<div class="survey-survey-questions-lines-container">
						<h4 class="font-standard font-family-v1 survey-space-top-bottom-1" style="width: 109px;"><?php echo __('admin.inquirydesc.label.inquiry_description') ?></h4>
						<div class="survey-summary-textarea-container flexbox-x-axis relative"style="white-space: pre-wrap;word-wrap: break-word;">
							<div name=""  cols="" rows="" class="survey-summary-textarea" style="min-height: 28px;"><?php if (isset($inquiry_description)) echo($inquiry_description) ?></div>
							<a class="font-standard font-family-v2 font-color-v1 survey-position-absolute-v1 js-edit-desc"><?php echo __('admin.common.label.edit') ?></a>
						</div>
					</div>
				<?php } ?>
				<div class="survey-questions-border"></div>
			</div>
			<!-- 質問編集セクション -->
			<div class="survey-editing-title-container js-survey-editing-title-container flexbox-x-axis survey-space-around-5">
				<h4 class="font-standard font-family-v4 left-aligned"><?php echo __('survey.inquiry.common.edit') ?></h4>
				<div class="js-icon-fold-survey flexbox-x-axis pointer survey-space-around-4">
					<div class="survey-fold-icon">
						<span class="icon-fold-open"></span>
					</div>
					<span class="font-standard font-family-v1"><?php echo __('survey.inquiry.common.list') ?></span>
				</div>
			</div>
			<!-- セクション追加ボタン -->
			<div class="survey-add-container relative js-survey-add-container not-sortable" style="position:relative;">
				<div class="btn-add-section js-add-section-button">
					<svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12">
						<g fill="none" fill-rule="evenodd" stroke-linecap="round" stroke-linejoin="round">
							<g stroke="#245BD6" stroke-width="2">
								<g>
									<path d="M5 6L11 6M5 2L11 2M1 2L2 2M1 6L2 6M1 10L2 10M5 10L11 10" transform="translate(-391 -995) translate(391 995)"/>
								</g>
							</g>
						</g>
					</svg>
					<span><?php echo __('survey.inquiry.common.label.section.add') ?></span>
				</div>
			</div>
			<!-- 翻訳 -->
			<?php if (count($support_lang_cd) > 1) { ?>
			<div id="react-multilingualreflect" style="margin-top:32px"></div>
			<?php } ?>
			<!-- submit ボタン -->
			<div class="submit-btn-container" style="margin: 44px 0 0 110px;">
				<div class="btn-larger btn-blue js-survey-entry-save-button" id="saveBaseButton"><?php echo __('admin.common.button.save') ?></div>
				<div class="survey-preview-button btn-larger btn-gray-black">
					<a class="flexbox-center height-100 width-100" href="<?php echo $verify_url ?>" target="_blank" style="color: #000;" onfocus="this.blur();"><?php echo __('admin.common.button.verify')?></a>
				</div>
				<div class="btn-larger btn-white js-back-btn"><?php echo __('admin.common.button.return_to_list') ?></div>
			</div>
		</div>
	</div>
</div>
<!-- template -->
<!-- 税・サ込　設定 -->
<div class="flexbox-x-axis survey-space-around-2 survey-height-32 limit-mng-layer js-add-price-container clone" style="display: none; height: auto;">
	<div style="display: flex;">
		<!-- 税・サ -->
		<div class="flexbox-x-axis dropdown-container" style="padding:0 4px; width:100%; margin: 0 12px 0 0;">
			<!-- 税込の選択プルダウン -->
			<div class="dropdown-container js-dropdown-container js-with-price-container" style="padding: 0 7px;background:#e3e5e8;height:20px;">
				<div class="dropdown-selected">
					<span class="dropdown-selected-text" data-value="tax-service"><?php echo __('inquiry.common.label.order.tax.service.included') ?></span>
					<svg xmlns="http://www.w3.org/2000/svg" width="8" height="5" viewBox="0 0 8 5">
						<g fill="none" fill-rule="evenodd" stroke-linecap="round" stroke-linejoin="round">
							<g stroke="#3D3F45" stroke-width="2">
								<g>
									<g>
										<path d="M3 5L6 8 9 5" transform="translate(-756 -300) translate(496 288) translate(258 8)"/>
									</g>
								</g>
							</g>
						</g>
					</svg>
				</div>
				<ul class="dropdown-options dropdown-middle" style="top: -10px; width:fit-content; width: -moz-fit-content;">
					<!-- 税・サ込 -->
					<li class="dropdown-option" data-value="tax-service"><?php echo __('inquiry.common.label.order.tax.service.included') ?></li>
					<!-- 税込・サ抜 -->
					<li class="dropdown-option" data-value="tax"><?php echo __('inquiry.common.label.order.tax.included') ?></li>
					<!-- 非課税 -->
					<li class="dropdown-option" data-value="tax-none"><?php echo __('inquiry.common.label.order.tax.service.none') ?></li>
				</ul>
			</div>
			<div class="flexbox-x-axis">
				<p style="padding:0 2px;">¥</p>
				<input type="text" class="js-num-only js-with-price text-input-mini line-none" style="text-align:right;width:100%;">
			</div>
		</div>
		<!-- 数量 -->
		<div class="flexbox-x-axis dropdown-container" style="padding:0 4px; width:100%;">
			<!-- 税込の選択プルダウン -->
			<div class="dropdown-container js-dropdown-container dropdown-mini" style="padding: 0 7px;background:#e3e5e8;height:20px;">
				<div class="dropdown-selected">
					<span class="dropdown-selected-text" style="margin: 0 auto;"><?php echo __('inquiry.common.label.order.limit') ?></span>
				</div>
			</div>
			<div class="flexbox-x-axis">
				<input type="text" class="js-num-only js-with-max-num text-input-mini line-none" style="text-align:right;width:100%;" value="1">
			</div>
		</div>
	</div>
	<?php
	$inquiry_data = json_decode($inquiry->inquiry_data, true);
	if (isset($inquiry_data['pms'])):
	?>
		<div style="display: flex;">
			<!-- 商品コード（PMS連携） -->
			<div class="flexbox-x-axis dropdown-container" style="padding:0 4px; width:100%; margin-top: 4px;">
				<div class="dropdown-container js-dropdown-container dropdown-mini" style="background:#e3e5e8;height:20px; display: flex; align-items: center; justify-content: center;">
					<div class="dropdown-selected">
						<span class="dropdown-selected-text" style="margin: 0 auto; white-space: nowrap; overflow: visible; text-align: center;"><?php echo __('inquiry.common.label.product.code') ?></span>
					</div>
				</div>
				<div class="flexbox-x-axis" style="flex: 1;">
					<input type="text" class="js-with-product-code text-input-mini line-none" style="text-align:right;width:100%;" value="">
				</div>
			</div>
		</div>
	<?php endif; ?>
	<!-- 在庫管理　枠管理　枠選択 -->
	<div class="js-limit-mng-container flexbox-x-axis limit-mng-layer">
		<h1 class="font-standard font-color-v2" style="min-width: 48px;"><?php echo __('inquiry.common.maximum.control') ?></h1>
	</div>
</div>
<!-- template -->
<!-- 画像追加 選択肢表示-->
<div class="flexbox-center add-image-container js-add-image-container clone"style="min-width: 144px; height: 108px;display: none;position: relative;">
	<div class="dropdown-container">
		<span class="icon-form-photo" style="margin:0 10px 0 0;"></span><?php echo __('inquiry.input.text.fup') ?>
	</div>
</div>
<!-- template -->
<!-- 画像追加モーダル -->
<div class="modal-image-container js-modal-image-container clone" style="display: none;">
	<div class="relative" style="min-height: 100%;">
		<!-- タイトル -->
		<div class="flexbox relative">
			<h4 class="font-standard font-size-v5 font-family-v2" style="margin: 0;"><?php echo __('inquiry.input.text.fup') ?></h4>
			<span class="js-close-modal-image icon-cancel-large survey-position-absolute-v2 pointer"></span>
		</div>
		<!-- アップロード -->
		<div class="" style="margin: 22px 0 0 24px;">
			<label class="survey-drag-or-click flexbox-center">
			<svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12">
				<g fill="none" fill-rule="evenodd" stroke-linecap="round" stroke-linejoin="round">
					<g stroke="#245BD6" stroke-width="2">
						<g>
							<path d="M1 8L1 11 11 11 11 8" transform="translate(-512 -298) translate(512 298)"/>
							<path d="M4 2L6 4 8 2" transform="translate(-512 -298) translate(512 298) matrix(1 0 0 -1 0 6)"/>
							<path d="M6 8L6 2" transform="translate(-512 -298) translate(512 298) matrix(1 0 0 -1 0 10)"/>
						</g>
					</g>
				</g>
			</svg>
				<input type="file" name="image" id="myImage" accept="image/*" style="display:none;">
				<span class="font-standard font-family-v2 font-color-v3"><?php echo __('admin.common.modal.image.flie.upload') ?></span>
			</label>
			<p class="font-standard font-family-v2 survey-space-top-4"><?php echo __('admin.common.modal.image.flie.category') ?></p>
			<div class="js-modal-image-preview modal-image-preview flexbox" style="display: none;">
				<div>
					<img src="" class="" style="width: 300px;height: 200px;object-fit: cover;" id="preview">
				</div>
				<div>
					<div class="flexbox-x-axis survey-width-430">
					</div>
				</div>
			</div>
		</div>
		<!-- SUBMIT -->
		<div class="submit-btn-container modal-image-button" style="margin: 0 0 0 25px;">
			<div class="btn-larger btn-gray-white js-save-modal-image" type="button"><?php echo __('admin.common.button.confirm')?></div>
			<div class="btn-larger btn-white js-cancel-modal-image" type="button"><?php echo __('admin.inquiry.label.cancel')?></div>
		</div>
	</div>
</div>
<!-- template -->
<!-- 写真削除 -->
<div class="modal-smaller-container js-modal-delete-image" style="display: none;">
	<div class="modal-small-title-container">
		<h4 class="modal-small-title"><?php echo __('survey.inquiry.common.item.image.confirm.delete') ?></h4>
		<svg class="survey-modal-close-button js-close-modal-image" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
			<g fill="none" fill-rule="evenodd" stroke-linecap="round">
				<g stroke="#3D3F45" stroke-width="2">
					<g>
						<path d="M16 16L0 0" transform="translate(4 4)"/>
						<path d="M16 16L0 0" transform="translate(4 4) matrix(-1 0 0 1 16 0)"/>
					</g>
				</g>
			</g>
		</svg>
	</div>
	<p style="margin: 32px 0 0 10px;"><?php echo __('admin.common.modal.delete.confirm') ?></p>
	<div class="submit-btn-container" style="position: fixed; bottom: 12px;">
		<div class="btn-larger btn-red js-modal-delete-image-btn"><?php echo __('admin.common.button.delete') ?></div>
		<div class="btn-larger btn-white js-modal-close-image-btn"><?php echo __('admin.inquirydesc.label.modal_cancel_button') ?></div>
	</div>
</div>
<!-- template -->
<!-- 時間帯 -->
<div class="survey-branch-modal-container js-time-setting-container" style="max-height: 340px; min-height: 340px;min-width:708px;display: none;">
	<h1 class="font-standard font-size-v5 font-family-v4 js-date-setting-title"><?php echo __('inquiry.common.label.detailed.settings') ?></h1>
	<div class="survey-space-top-1 js-time-detail-setting inner-space-1">
		<div class="">
			<!-- 利用時間帯 -->
			<div class="flexbox">
				<div class="width-96 flexbox-x-axis"><?php echo __('inquiry.common.label.time.use') ?></div>
				<div class="flexbox-x-axis">
					<input type="text" placeholder="　:　" class="survey-period-date-container js-possible-time-start" style="width:121px;padding:0 12px; margin:0 12px;">
					<span>〜</span>
					<input type="text" placeholder="　:　" class="survey-period-date-container js-possible-time-end" style="width:121px;padding:0 12px; margin:0 12px;">
				</div>
			</div>
			<!-- 時間枠 -->
			<div class="flexbox" style="margin:16px 0 0 0;">
				<div class="width-96 flexbox-x-axis"><?php echo __('inquiry.common.label.time.schedule') ?></div>
				<div class="dropdown-container js-dropdown-container dropdown-middle pointer" style="width: 278px; min-width: 278px;margin:0 0 0 12px;">
					<div class="dropdown-selected">
						<span class="dropdown-selected-text" data-span="60"><?php echo (str_replace('{num}', "60", __('inquiry.common.label.time.every.minute'))) ?></span>
						<svg xmlns="http://www.w3.org/2000/svg" width="8" height="5" viewBox="0 0 8 5">
							<g fill="none" fill-rule="evenodd" stroke-linecap="round" stroke-linejoin="round">
								<g stroke="#3D3F45" stroke-width="2">
									<g>
										<g>
											<path d="M3 5L6 8 9 5" transform="translate(-756 -300) translate(496 288) translate(258 8)"/>
										</g>
									</g>
								</g>
							</g>
						</svg>
					</div>
					<ul class="dropdown-options dropdown-middle" style="top: -10px; width:fit-content; width: -moz-fit-content;padding-inline-start: 0;    z-index: 99999;min-width: 278px;">
						<li class="dropdown-option" data-span="15"><?php echo (str_replace('{num}', "15", __('inquiry.common.label.time.every.minute'))) ?></li>
						<li class="dropdown-option" data-span="30"><?php echo (str_replace('{num}', "30", __('inquiry.common.label.time.every.minute'))) ?></li>
						<li class="dropdown-option" data-span="45"><?php echo (str_replace('{num}', "45", __('inquiry.common.label.time.every.minute'))) ?></li>
						<li class="dropdown-option" data-span="60"><?php echo (str_replace('{num}', "60", __('inquiry.common.label.time.every.minute'))) ?></li>
						<li class="dropdown-option" data-span="120"><?php echo (str_replace('{num}', "120", __('inquiry.common.label.time.every.minute'))) ?></li>
					</ul>
				</div>
			</div>
			<!-- 除外時間帯 -->
			<div class="flexbox" style="margin:16px 0 0 0;">
				<div class="width-96 flexbox-x-axis"><?php echo __('inquiry.common.label.excluded.time.zone') ?></div>
				<div class="flexbox-x-axis">
					<input type="text" placeholder="　:　" class="survey-period-date-container js-except-time-start" style="width:121px;padding:0 12px; margin:0 12px;">
					<span>〜</span>
					<input type="text" placeholder="　:　" class="survey-period-date-container js-except-time-end" style="width:121px;padding:0 12px; margin:0 12px;"> 
				</div>
			</div>
			<div class="flexbox" style="margin:16px 0 0 0;">
				<div class="width-96"><?php echo __('inquiry.common.label.maximum.display.format') ?></div>
				<div class="flexbox js-peroid-display-mode-container" style="flex-direction: column;">
					<div class="flexbox-x-axis js-peroid-display-mode js-peroid-display-mode-time">
						<span class="icon-form-single-option-off survey-space-right-4"></span>
						<span>10:00</span>
					</div>
					<div class="flexbox-x-axis survey-space-top-5 js-peroid-display-mode js-peroid-display-mode-peroid">
						<span class="icon-form-single-option-off survey-space-right-4"></span>
						<span>10:00〜10:30</span>
					</div>
				</div>
			</div>
			<!-- 表示形式 -->
			
		</div>
	</div>
	<div class="submit-btn-container modal-button-container">
		<div class="btn-larger btn-blue active js-time-setting-ok"><?php echo __('admin.common.button.confirm')?></div>
		<div class="btn-larger btn-white js-time-setting-cancel"><?php echo __('admin.common.button.cancel')?></div>
		<div class="btn-larger btn-red-border flexbox-center js-time-setting-delete-button">
			<span class="icon-delete space-0"></span>
		</div>
	</div>
</div>
<!-- template -->
<!-- 写真削除 -->
<div class="modal-smaller-container js-modal-hide-image-input" style="display: none;">
	<div class="modal-small-title-container">
		<h4 class="modal-small-title"><?php echo __('survey.inquiry.common.item.image.confirm.display_none') ?></h4>
		<svg class="survey-modal-close-button js-close-modal-image" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
			<g fill="none" fill-rule="evenodd" stroke-linecap="round">
				<g stroke="#3D3F45" stroke-width="2">
					<g>
						<path d="M16 16L0 0" transform="translate(4 4)"/>
						<path d="M16 16L0 0" transform="translate(4 4) matrix(-1 0 0 1 16 0)"/>
					</g>
				</g>
			</g>
		</svg>
	</div>
	<p style="margin: 32px 0 0 10px;"><?php echo __('survey.inquiry.common.item.image.confirm.delete.title') ?></p>
	<div class="submit-btn-container" style="position: fixed; bottom: 12px;">
		<div class="btn-larger btn-red js-modal-hide-image-input-btn"><?php echo __('admin.common.button.delete') ?></div>
		<div class="btn-larger btn-white js-modal-close-image-btn"><?php echo __('admin.inquirydesc.label.modal_cancel_button') ?></div>
	</div>
</div>
<!-- template -->
<!-- アクション追加のモーダルウィンドウ -->
<div class="survey-branch-modal-container js-action-modal" style="display: none;height: 80%;">
	<h4 class="font-standard font-size-v5 font-family-v4">「<span></span>」<?php echo __('inquiry.common.label.action.setting.notify.title') ?>
	</h4>
	<p class="font-standard font-size-v3 font-family-v4 font-color-v1 survey-space-all-around"><?php echo __('survey.inquiry.common.action.destination') ?></p>
	<div class="modal-container-context" style="padding:0; height: calc(100% - 150px);">

		<div class="btn-add-branch js-add-action-in-modal">
			<p>
			<span class="font-standard font-size-v3 font-family-v3 font-color-v1 survey-space-around-4"><span class="icon-add"></span></span>
			<?php echo __('inquiry.common.label.select.add.notification_condition') ?></p>
		</div>
	</div>
	<div class="submit-btn-container" style="margin: 20px 0 12px 0;">
		<div class="btn-larger btn-blue js-survey-modal-add-button active"><?php echo __('admin.common.button.confirm')?></div>
		<div class="btn-larger btn-white js-survey-modal-cancel-button"><?php echo __('admin.inquiry.label.cancel')?></div>
		<div class="btn-larger btn-red-border js-delete-all-modal-action">
			<span class="icon-delete"></span>
		</div>
	</div>
</div>
<!-- template -->
<!-- アクションを追加 -->
<div class="survey-branch-container js-action-setting-container action-setting-container" style="display:none;">
	<div class="flexbox-x-axis js-action-setting-title">
		<input type="text" class="text-input-longer survey-space-all-around-5 js-branch-setting-title" placeholder="<?php echo __('inquiry.common.placeholder.notification_conditions_title') ?>">
		<span class="icon-delete right-aligned js-delete-icon" style="width: 50px; cursor: pointer;"></span>
	</div>
	<div class="js-add-branch-container" style="margin:16px 36px 0 36px;">
		<h4 class="font-standard font-family-v4"><?php echo __('inquiry.common.label.action.notification_conditions') ?></h4>
		<div class="add-branch-pulldown-container js-add-pulldown-container" style="display: block;">
		<div class="js-action-slects">
		<div class="flexbox-x-axis js-action-select-container" style="margin: 0 0 10px 0;">
			<div class="dropdown-container dropdown-longer js-dropdown-container dropdown-connection js-dropdown-conditions" style="min-width: 70px;">
				<div class="dropdown-selected">
					<span class="dropdown-selected-text" data-option="and">AND</span>
					<img src="./../assets/admin/css/img/icon-drop-down-close.svg" width="12" height="12">
				</div>
				<ul class="dropdown-options survey-width-fit dropdown-longer"style="min-width: auto;padding: 0;">
					<li class="dropdown-option" data-option="and">AND</li>
					<li class="dropdown-option" data-option="or">OR</li>
				</ul>
			</div>
			<div class="dropdown-container dropdown-longer js-question-dropdown js-dropdown-container" style="width: 100%;">
				<div class="dropdown-selected">
					<span class="dropdown-selected-text"><?php echo __('inquiry.common.label.action.select') ?></span>
					<img src="./../assets/admin/css/img/icon-drop-down-close.svg" width="12" height="12">
				</div>
				<ul class="dropdown-options survey-width-fit dropdown-longer" style="padding: 0;"></ul>
			</div>
			<div class="dropdown-container dropdown-longer js-dropdown-container js-action-conditions-dropdown" style="min-width: 110px;width: 110px;">
				<div class="dropdown-selected">
					<span class="dropdown-selected-text" data-option="equal"><?php echo __('inquiry.common.label.branch.equal') ?></span>
					<img src="./../assets/admin/css/img/icon-drop-down-close.svg" width="12" height="12">
				</div>
				<ul class="dropdown-options survey-width-fit dropdown-longer"style="min-width: auto;padding: 0;">
					<li class="dropdown-option" data-option="equal"><?php echo __('inquiry.common.label.branch.equal') ?></li>
					<li class="dropdown-option" data-option="not_equal"><?php echo __('inquiry.common.label.branch.not_equal') ?></li>
					<li class="dropdown-option" data-option="include"><?php echo __('inquiry.common.label.branch.include') ?></li>
					<li class="dropdown-option" data-option="not_include"><?php echo __('inquiry.common.label.branch.not_include') ?></li>
					<li class="dropdown-option" data-option="greater"><?php echo __('inquiry.common.label.branch.greater') ?></li>
					<li class="dropdown-option" data-option="smaller"><?php echo __('inquiry.common.label.branch.smaller') ?></li>
					<li class="dropdown-option" data-option="equal_or_greater"><?php echo __('inquiry.common.label.branch.equal_or_greater') ?></li>
					<li class="dropdown-option" data-option="equal_or_smaller"><?php echo __('inquiry.common.label.branch.equal_or_smaller') ?></li>
				</ul>
			</div>
			<img src="./../assets/admin/css/img/icon-cancel-small.svg" width="12" height="12" class="js-delete-icon">
		</div>
		</div>
			<div class="btn-smaller btn-blue js-add-button" style="width: 66px;">
				<span class="icon-add-white icon-add-white-svg"></span>
				<?php echo __('admin.common.button.add') ?>
			</div>
		</div>
		<div>
			<!-- <span class="font-standard font-family-v1 js-text"><?php echo __('admin.inquiry.label.action') ?></span> -->
			<div style="display:flex; flex-direction:column; gap:10px; padding-top:10px;">
				<span class="font-standard font-family-v1 js-text"><?php echo __('admin.inquiry.label.notify.admin') ?></span>
				<div class="inquiry-action-mail-row">
					<div class="inquiry-action-mail-title"><?php echo __('admin.inquiry.label.mail.admin') ?></div>
					<div class="js-action-add-user" style="display:flex; width: fit-content;width: -moz-fit-content;">
						<div class="js-show-selected-user pointer show-selected-user" style="background:none; gap: 10px; margin:0px;">
							<li class="survey-add-dest-user pointer">
								<img src="./../assets/admin/css/img/icon-add.svg">
								<span><?php echo __('admin.inquiry.label.mail_users') ?></span>
							</li>
						</div>
					</div>
				</div>
				<span class="font-standard font-family-v1 js-text"><?php echo __('admin.inquiry.label.notify.user') ?></span>
				<div class="inquiry-action-mail-row">
					<div class="inquiry-action-mail-title"><?php echo __('admin.inquiry.label.mail.users') ?></div>
					<div style="padding:0px 12px; display:flex; align-items:center; gap:10px;">
						<div class="talkappi-pulldown" data-name="user_mail_template" data-value="" data-blank="1" data-blank-text="<?php echo __('admininquiry.baseinfo.not_notify') ?>" style="width: 300px;" data-source='<?php echo json_encode($user_mail_template_list, JSON_UNESCAPED_UNICODE|JSON_HEX_APOS) ?>'></div>
						<p class="js-edit edit_link"><?php echo __('admin.common.button.edit'); ?></p>
					</div>
				</div>
			</div>

			<div class="survey-space-all-around-3 flexbox-inline-x-axis icon-round-corners-small icon-background-light-blue js-added-branch-condition for-clone display-none">
				<span class="font-standard font-family-v1 flexbox-x-axis survey-space-right-1" style="overflow: hidden;"></span>
				<img src="./../assets/admin/css/img/icon-cancel-small.svg" class="js-delete-icon" width="12" height="12">
			</div>
			<span class="font-standard font-family-v1 font-color-v3 js-omit-next" style="display: none">･･･</span>
		</div>
	</div>
</div>
<!-- template -->
<!-- 選択追加 -->
<div class="flexbox-x-axis js-action-select-container" style="display: none; margin: 0 0 10px 0;">
	<div class="dropdown-container dropdown-longer js-dropdown-container dropdown-connection js-dropdown-conditions" style="min-width: 70px;">
		<div class="dropdown-selected">
			<span class="dropdown-selected-text" data-option="and">AND</span>
			<img src="./../assets/admin/css/img/icon-drop-down-close.svg" width="12" height="12">
		</div>
		<ul class="dropdown-options survey-width-fit dropdown-longer js-droop"style="min-width: auto;padding: 0;">
			<li class="dropdown-option" data-option="and">AND</li>
			<li class="dropdown-option" data-option="or">OR</li>
		</ul>
	</div>
	<div class="dropdown-container dropdown-longer js-question-dropdown js-dropdown-container" style="width: 100%;">
		<div class="dropdown-selected">
			<span class="dropdown-selected-text"><?php echo __('inquiry.common.label.action.select') ?></span>
			<img src="./../assets/admin/css/img/icon-drop-down-close.svg" width="12" height="12">
		</div>
		<ul class="dropdown-options survey-width-fit dropdown-longer" style="padding: 0;"></ul>
	</div>
	<div class="dropdown-container dropdown-longer js-dropdown-container js-action-conditions-dropdown" style="min-width: 110px;width: 110px;">
		<div class="dropdown-selected">
			<span class="dropdown-selected-text" data-option="equal"><?php echo __('inquiry.common.label.branch.equal') ?></span>
			<img src="./../assets/admin/css/img/icon-drop-down-close.svg" width="12" height="12">
		</div>
		<ul class="dropdown-options survey-width-fit dropdown-longer"style="min-width: auto;padding: 0;">
			<li class="dropdown-option" data-option="equal"><?php echo __('inquiry.common.label.branch.equal') ?></li>
			<li class="dropdown-option" data-option="not_equal"><?php echo __('inquiry.common.label.branch.not_equal') ?></li>
			<li class="dropdown-option" data-option="include"><?php echo __('inquiry.common.label.branch.include') ?></li>
			<li class="dropdown-option" data-option="not_include"><?php echo __('inquiry.common.label.branch.not_include') ?></li>
			<li class="dropdown-option" data-option="greater"><?php echo __('inquiry.common.label.branch.greater') ?></li>
			<li class="dropdown-option" data-option="smaller"><?php echo __('inquiry.common.label.branch.smaller') ?></li>
			<li class="dropdown-option" data-option="equal_or_greater"><?php echo __('inquiry.common.label.branch.equal_or_greater') ?></li>
			<li class="dropdown-option" data-option="equal_or_smaller"><?php echo __('inquiry.common.label.branch.equal_or_smaller') ?></li>
		</ul>
	</div>
	<img src="./../assets/admin/css/img/icon-cancel-small.svg" width="12" height="12" class="js-delete-icon">
</div>
<!-- template -->
<!-- summernote item desc -->
<div class="survey-branch-modal-container rich-text-editor js-item-desc-summernote-modal" style="display: none;">
	<div class="flexbox flexbox-baselines">
		<h1 class="font-standard font-size-v5 font-family-v4"style="margin:0 auto 24px 0;"><?php echo __('survey.inquiry.common.item.edit_HTML.title') ?></h1>
		<img src="./../assets/admin/css/img/icon-cancel-large.svg" width="24" height="24" class="js-cancel pointer">
	</div>
	<textarea type="text" class="survey-title js-survey-title-editor"></textarea>	
	<div class="submit-btn-container" style="position: fixed; bottom: 12px;">
		<div class="btn-larger btn-blue js-ok"><?php echo __('admin.common.button.confirm')?></div>
		<div class="btn-larger btn-white js-cancel"><?php echo __('admin.inquiry.label.cancel')?></div>
	</div>
</div>
<!-- template -->
<!-- 受付詳細設定 -->
<div class="survey-branch-modal-container js-limit-advanced-setting-modal" style="display: none;min-width: 600px; min-height: 550px;">
	<h1 class="font-standard font-size-v5 font-family-v4"><?php echo __('inquiry.common.label.detailed.settings.maximum') ?></h1>
	<div class="" style="height: 440px; overflow-y: scroll;">
		<!-- 開始日 -->
		<div class="flexbox" style="margin:22px 0 0 0;">
			<div class="width-96"><?php echo __('inquiry.common.label.maximum.start') ?></div>
			<div class="flexbox js-start-date-container" style="flex-direction: column;">
				<div class="flexbox-x-axis js-start-date-setting">
					<span class="icon-form-single-option-off survey-space-right-4"></span>
					<span class="js-start-date-from-label" style="margin: 0 8px 0 8px;">＋</span>
					<input type="number" class="survey-short-text-input js-start-date survey-space-right-4">
					<span><?php echo __('inquiry.common.label.maximum.days.after') ?></span>
				</div>
				<div class="flexbox-x-axis survey-space-top-5 js-start-date-setting">
					<span class="icon-form-single-option-off survey-space-right-4"></span>
					<div class="survey-period-date-container background-white">
						<span class="icon-calender"></span><input name="start_date" id="start_date" value="" class="date-picker js-start-date line-none survey-space-right-4" size="16" data-date-format="yyyy-mm-dd" type="text" autocomplete="off" style="width: 100px;">
					</div>
					<span>〜</span>
				</div>
			</div>
		</div>
		<!-- 終了日 -->
		<div class="flexbox" style="margin:22px 0 0 0;">
			<div class="width-96"><?php echo __('inquiry.common.label.maximum.end') ?></div>
			<div class="flexbox js-end-date-container" style="flex-direction: column;">
				<div class="flexbox-x-axis js-end-date-setting">
					<span class="icon-form-single-option-off survey-space-right-4"></span>
					<?php if($lang_cd === 'en') echo "<span><?php echo __('inquiry.common.label.up.to') ?></span>" ?>
					<span class="js-end-date-from-label" style="margin: 0 8px 0 8px;">＋</span>
					<input type="number" class="survey-short-text-input js-end-date survey-space-right-4">
					<span><?php echo __('inquiry.common.label.maximum.days.later') ?></span>
				</div>
				<div class="flexbox-x-axis survey-space-top-5 js-end-date-setting">
					<span class="icon-form-single-option-off survey-space-right-4"></span>
					<?php if($lang_cd === 'en') echo "<span><?php echo __('inquiry.common.label.up.to') ?></span>" ?>
					<div class="survey-period-date-container background-white">
						<span class="icon-calender"></span><input name="end_date" id="end_date" value="" class="date-picker js-end-date line-none survey-space-right-4" size="16" data-date-format="yyyy-mm-dd" type="text" autocomplete="off" style="width: 100px;">
					</div>
					<?php if($lang_cd === 'ja') echo "<span><?php echo __('inquiry.common.label.until.days') ?></span>" ?>
				</div>
			</div>
		</div>
		<!-- 予約可能時間帯(時間帯枠のみ) -->
		<div class="flex js-reservable-period-container" style="margin:22px 0 0 0">
			<div class="width-96"><?php echo __('inquiry.common.label.maximum.reservable_period') ?></div>
			<div class="flexbox js-reservable-period-setting-container">
				<div class="flexbox-x-axis js-reservable-period-setting">
					<input type="time" class="survey-period-date-container js-reservable-period-start-time" style="width:100px;padding:0 12px;"> 
					<span><?php echo __('inquiry.common.label.maximum.from') ?></span>
					<input type="time" class="survey-period-date-container js-reservable-period-end-time" style="width:100px;padding:0 12px;">
					<span><?php echo __('inquiry.common.label.maximum.to') ?></span>
				</div>
			</div>
		</div>
		<!-- 期限（開始） -->
		<div class="flexbox survey-space-top-2 js-limit-start-container">
			<div class="width-96"><?php echo __('inquiry.common.label.maximum.term.start') ?></div>
			<div class="flexbox js-limit-start-setting-container" style="flex-direction: column;">
				<div class="flexbox-x-axis js-limit-start-setting">
					<input type="number" class="survey-period-date-container js-limit-start-day" style="width:100px;padding:0 12px;">
					<span class="mr10"><?php echo __('inquiry.common.label.maximum.days.before') ?></span>
					<input type="time" class="survey-period-date-container js-limit-start-time" style="width:100px;padding:0 12px;"> 
					<span>〜</span>
				</div>
			</div>
		</div>
		<!-- 期限（終了） -->
		<div class="flexbox survey-space-top-2 js-limit-day-container">
			<div class="width-96"><?php echo __('inquiry.common.label.maximum.term.end') ?></div>
			<div class="flexbox js-limit-time-container" style="flex-direction: column;">
				<div class="flexbox-x-axis js-limit-time-setting">
					<span class="icon-form-single-option-off survey-space-right-4"></span>
					<input type="number" class="survey-period-date-container js-limit-date" style="width:100px;padding:0 12px;">
					<span class="mr10"><?php echo __('inquiry.common.label.maximum.days.before') ?></span>
					<input type="time" class="survey-period-date-container js-limit-hour" style="width:100px;padding:0 12px;"> 
				</div>
				<div class="flexbox-x-axis survey-space-top-5 js-limit-time-setting">
					<span class="icon-form-single-option-off survey-space-right-4"></span>
					<?php if($lang_cd === 'en') echo "<span><?php echo __('inquiry.common.label.up.to') ?></span>" ?>
					<input type="number" class="survey-period-date-container js-limit-hour" style="width:100px;padding:0 12px;"> 
					<span class="mr10"><?= __('inquiry.common.label.until.hours') ?></span>
					<?php
					$minutes_options[0] = 0;;
					for ($i = 0; $i <= 60; $i++) {
						$minutes_options[$i] = $i;
					}
					?>
					<?php echo Form::select('limit-minute', $minutes_options, '0', array('class' => 'form-control js-limit-minute', 'id' => 'limit-minute', 'style' => "width:100px;margin-right:8px")) ?>
					<span><?= __('inquiry.common.label.until.minutes') ?></span>
				</div>
				<div class="flexbox-x-axis survey-space-top-5 js-limit-time-setting">
					<span class="icon-form-single-option-off survey-space-right-4"></span>
					<div class="survey-period-date-container background-white">
						<span class="icon-calender"></span><input name="end_date" id="end_date" value="" class="date-picker js-end-date line-none survey-space-right-4" size="16" data-date-format="yyyy-mm-dd" type="text" autocomplete="off" style="width: 100px;">
					</div>
					<input type="time" class="survey-period-date-container js-limit-hour" style="width:100px;padding:0 12px;" value="00:00">
				</div>
			</div>
		</div>
		<!-- 数量ラベル名 -->
		<div class="flexbox survey-space-top-2 js-num-label-container">
			<div class="width-96"><?php echo __('inquiry.common.label.maximum.quantity') ?></div>
			<div class="flexbox js-num-label-setting-container" style="flex-direction: column;">
				<div class="flexbox-x-axis js-num-label-setting">
					<span class="mr10"><?php echo __('inquiry.common.label.maximum.type') ?></span>
					<?php echo Form::select('use_number', $use_number_config, 0, array('class' => 'form-control js-use-number', 'id' => 'use_number', 'style' => "width: fit-content;")) ?>
					<span style="margin:0px 0px 0px 10px;"><?php echo __('inquiry.common.label.maximum.num_label_select')?></span>
					<div class="talkappi-switch js-select-num-label" style="margin-left:2px; margin-top: 3px;" data-name="select_num_label" data-value="0"></div>
					<div class="js-label-none-toggle" style="display: none;">
						<span style="margin:0px 5px 0px 10px;"><?php echo __('inquiry.common.label.maximum.label') ?></span>
						<?php echo Form::select('num_label', $num_label_config, 'LABEL_NONE', array('class' => 'form-control js-num-label', 'id' => 'num_label', 'style' => "width: fit-content;")) ?>
					</div>
				</div>
			</div>
		</div>
		<!-- 数量指定 -->
		<div class="flexbox survey-space-top-2 js-limit-num-container" style="margin: 12px 0 0 0;">
			<div class="width-96"></div>
			<div class="flexbox js-limit-num-setting-container" style="flex-direction: column;">
				<div class="js-limit-num-setting">
					<div class='flexbox-x-axis'>
						<span class="mr10"><?php echo __('inquiry.common.label.maximum.limit') ?></span>
						<input type="number" class="survey-period-date-container js-limit-num" style="width:100px;padding:0 12px;">
						<span class="mr10"><?php echo __('inquiry.common.label.maximum.limit_min') ?></span>
						<input type="number" class="survey-period-date-container js-limit-num-min" style="width:100px;padding:0 12px;">
					</div>
					<div class='flexbox-x-axis' style="margin: 12px 0 0 0;">
						<span class="mr10"><?php echo __('inquiry.common.label.maximum.consecutive') ?></span>
						<input type="number" class="survey-period-date-container js-merge-num" style="width:100px;padding:0 12px;">
					</div>
				</div>
			</div>
		</div>
		<!-- 表示設定 -->
		<div class="flexbox survey-space-top-2 js-display-format-container">
			<div class="width-96"><?php echo __('inquiry.common.label.maximum.display.format') ?></div>
			<div class="flexbox" style="flex-direction: column;">
				<div class="flexbox-x-axis js-display-time-zone-setting">
					<span class="icon-form-single-option-off survey-space-right-4 active"></span>
					12:00 ~ 13:00 (<?php echo __('inquiry.common.label.maximum.default') ?>)
				</div>
				<div class="flexbox-x-axis survey-space-top-5 js-display-start-only-setting">
					<span class="icon-form-single-option-off survey-space-right-4"></span>
					12:00
				</div>
			</div>
		</div>
		<!-- 期間設定 -->
		<div class="flexbox survey-space-top-2 js-range-setting-container">
			<div class="width-96"><?php echo __('inquiry.common.label.maximum.range') ?></div>
			<div class="flexbox" style="flex-direction: column;">
				<div class="flexbox js-limit-num-setting-container" style="flex-direction: column;">
					<div class="flexbox-x-axis js-range-setting-enable">
						<span class="mr10"><?php echo __('inquiry.common.label.maximum.range_setting_enable') ?></span>
						<div class="talkappi-switch js-range-setting" style="margin:5px;" data-name="range_setting"></div>
					</div>
					<div class="flexbox-x-axis js-extra-crossday" style="display:none;">
						<span class="mr10"><?php echo __('inquiry.common.label.maximum.cross_day') ?></span>
						<div class="talkappi-switch js-cross_day" style="margin:5px;" data-name="cross_day"></div>
					</div>
					<div class="flexbox-x-axis js-reserve-setting" style="display:none;">
						<span class="mr10"><?php echo __('inquiry.common.label.maximum.reserve_setting') ?></span>
						<span class="mr10" style="margin:5px;"><?php echo __('inquiry.common.label.maximum.min_reserve_unit') ?></span>
						<input type="number" class="survey-short-text-input js-range-setting-min-span" style="margin:5px;">
						<span class="mr10" style="margin:5px;"><?php echo __('inquiry.common.label.maximum.max_reserve_unit') ?></span>
						<input type="number" class="survey-short-text-input js-range-setting-max-span" style="margin:5px;">
					</div>
					<div class="flexbox-x-axis js-reserve-span-setting" style="display:none">
						<span class="mr10"><?php echo __('inquiry.common.label.maximum.reserve_span_before') ?></span>
						<input type="number" class="survey-short-text-input js-range-setting-reserve-span-before" style="margin:5px;">
						<span class="mr10" style="margin:5px;"><?php echo __('inquiry.common.label.maximum.reserve_span_after') ?></span>
						<input type="number" class="survey-short-text-input js-range-setting-reserve-span" style="margin:5px;">
					</div>
					<div class="flexbox-x-axis js-reserve-time" style="display:none;">
						<span class="mr10"><?php echo __('inquiry.common.label.maximum.reserve_time') ?></span>
						<input type="time" class="survey-period-date-container js-range-setting-service-start"  style="margin:5px;width:100px;"> 
						<span class="mr10" style="margin:5px;"><?php echo __('inquiry.common.label.maximum.from') ?></span>
						<input type="time" class="survey-period-date-container js-range-setting-service-end"  style="margin:5px;width:100px;"> 
						<span class="mr10" style="margin:5px;"><?php echo __('inquiry.common.label.maximum.to') ?></span>
					</div>
					<div class="flexbox-x-axis js-out-service-reserve" style="display:none">
						<span class="mr10"><?php echo __('inquiry.common.label.maximum.out_service_reserve') ?></span>
						<div class="talkappi-switch js-out-service-reserve-setting" style="margin:5px;" data-name="out_service_reserve"></div>
					</div>
				</div>
			</div>
		</div>
		<!-- 新版UI(複数選択) -->
		<div class="flexbox survey-space-top-2 js-switch-chk-ui-container">
			<div class="width-96"><?php echo __('inquiry.common.label.maximum.ui_version') ?></div>
			<div class="talkappi-switch js-chk-ui-range-setting" style="margin:5px;" data-name="range_setting"></div>
		</div>
		<!-- 内訳単一選択に -->
		<div class="flexbox survey-space-top-2 js-category-single-select-container">
			<div class="width-96"><?php echo __('inquiry.common.label.maximum.category_single_select') ?></div>
			<div class="talkappi-switch js-category-single-select" style="margin:5px;"></div>
		</div>
		<!-- 切り替え時刻設定 -->
		<div class="flexbox survey-space-top-2 js-switch-time-container">
			<div class="width-96"><?php echo __('inquiry.common.label.maximum.switch_time') ?></div>
			<div class="flexbox js-switch-time-setting-container" style="flex-direction: column;">
				<div class="flexbox-x-axis js-switch-time-setting">
					<input type="time" class="survey-period-date-container js-switch-time" style="width:100px;padding:0 12px;">
				</div>
			</div>
		</div>
		<!-- 拡張設定 -->
		<div class="flexbox survey-space-top-2 js-extra-container">
			<div class="width-96"><?php echo __('inquiry.common.label.maximum.extension') ?></div>
			<div class="flexbox js-extra-setting-container" style="flex-direction: column;">
				<div class="flexbox-x-axis js-extra-setting">
					<textarea name="" id="" class="form-control" cols="30" rows="10"></textarea>
				</div>
			</div>
		</div>
		<!-- 割引設定 -->
		<div class="flexbox survey-space-top-2 js-limit-early-discount-container">
			<div class="width-96"><?php echo __('inquiry.common.label.maximum.discount') ?></div>
			<div class="flexbox js-limit-early-discount-setting-container" style="flex-direction: column;">
				<div class="flexbox-x-axis js-limit-early-discount-setting">
					<textarea name="" id="" class="form-control" cols="30" rows="10"></textarea>
				</div>
			</div>
		</div>
		
	</div>
	<div class="submit-btn-container modal-button-container">
		<div class="btn-larger btn-blue active js-limit-advanced-setting-ok"><?php echo __('admin.common.button.confirm')?></div>
		<div class="btn-larger btn-white js-limit-advanced-setting-cancel"><?php echo __('admin.inquiry.label.cancel')?></div>
		<div class="btn-larger btn-red-border flexbox-center js-limit-advanced-setting-delete">
			<span class="icon-delete space-0"></span>
		</div>
	</div>
</div>
<!-- template -->
<!-- モーダル背景 -->
<div class="modal-background js-modal-background" style="display: none;"></div>
<!-- template -->
<!-- 項目コンテナ -->
<div class="survey-input-container js-survey-input-container clone" style="display: none;">
	<div class="survey-editing-order-select flexbox-x-axis pointer js-fold-item">
		<span class="js-survey-editing-order-num left-aligned">1</span>
		<span class="icon-fold-open"></span>
	</div>
	<div class="survey-editing-input-container js-survey-editing-input-container">
		<!-- タイトルコンテナ -->
		<div class="title-container js-title-container js-survey-questions-title-focus" style="margin: 0 24px;position:relative;">
			<img src="./../assets/admin/css/img/icon-drag.svg" width="20" height="20" class="survey-icon-draggable">
			<input type="text" class="survey-title js-survey-title js-survey-title-input"
				placeholder="<?php if($is_order) echo __('survey.inquiry.common.item.placeholder.title.order'); else echo __('survey.inquiry.common.item.placeholder.title'); ?>"
			>
			<div class="preview-contents survey-title js-survey-title"></div>
			<div class="js-to-open-summernote-editor to-open-summernote-editor flexbox-x-axis display-none">
				<img src="./../assets/admin/css/img/icon-form-zoom-in.svg" width="12" height="12">
				<span><?php echo __('survey.inquiry.common.item.to_edit_HTML') ?></span>
			</div>
			<!-- リッチテキストエディタ -->
			<div class="survey-branch-modal-container js-survey-questions-input-title-modal rich-text-editor display-none">
				<div class="flexbox flexbox-baselines">
					<h1 class="font-standard font-size-v5 font-family-v4"style="margin:0 auto 24px 0;"><?php echo __('survey.inquiry.common.item.edit_HTML.title') ?></h1>
					<img src="./../assets/admin/css/img/icon-cancel-large.svg" width="24" height="24" class="js-survey-modal-cancel-button pointer">
				</div>
				<textarea type="text" class="survey-title js-survey-title-editor"></textarea>	
				<div class="submit-btn-container" style="position: fixed; bottom: 12px;">
					<div class="btn-larger btn-blue js-suevey-save-title js-survey-modal-add-button"><?php echo __('admin.common.button.confirm')?></div>
					<div class="btn-larger btn-white js-survey-modal-cancel-button"><?php echo __('admin.inquiry.label.cancel')?></div>
				</div>
			</div>
			<div class="modal-background js-survey-questions-input-title-modal-background display-none"></div>
			<!-- 質問形式の選択プルダウン -->
			<div class="dropdown-container js-dropdown-container dropdown-middle pointer">
				<div class="dropdown-selected">
					<span class="dropdown-selected-text">
						<?= $is_order ? __('survey.inquiry.common.ord') : __('survey.inquiry.common.opt') ?>
					</span>
					<svg xmlns="http://www.w3.org/2000/svg" width="8" height="5" viewBox="0 0 8 5">
						<g fill="none" fill-rule="evenodd" stroke-linecap="round" stroke-linejoin="round">
							<g stroke="#3D3F45" stroke-width="2">
								<g>
									<g>
										<path d="M3 5L6 8 9 5" transform="translate(-756 -300) translate(496 288) translate(258 8)"/>
									</g>
								</g>
							</g>
						</g>
					</svg>
				</div>
				<ul class="dropdown-options dropdown-middle" style="top: -10px; width:fit-content; width: -moz-fit-content;">
					<?php if ($is_order) { ?>
						<!-- オーダーの項目 -->
						<li class="dropdown-option js-dropdown-option" data-add-type-cd='ord' data-action-type='change' data-entry-type-detail='ord'>
							<span class="icon-form-single-option-on" style="margin: 0 8px 0 0;"></span><?= __('survey.inquiry.common.ord') ?>
						</li>
					<?php } else { ?>
						<!-- オーダー以外の項目 -->
						<!-- よく使う項目 -->
						<li class="dropdown-option js-pulldown-parent relative">
							<span class="icon-form-fleq" style="margin: 0 8px 0 0;"></span>
							<?php echo __('survey.inquiry.common.fleq') ?>
							<span class="pulldown-more"></span>
							<ul class="pulldown-list-children2">
								<li class="dropdown-option js-dropdown-option" data-add-type-cd='spl' data-action-type='change' data-entry-type-detail='name'>
									<span class="icon-edit-bar-add-q"></span><?php echo __('inquiry.common.label.name') ?>
								</li>
								<li class="dropdown-option js-dropdown-option" data-add-type-cd='spl' data-action-type='change' data-entry-type-detail='address'>
									<span class="icon-edit-bar-add-q"></span><?php echo __('admin.common.label.address') ?>
								</li>
								<?php if($inquiry_div != 9){ ?>
									<li class="dropdown-option js-dropdown-option" data-add-type-cd='opt' data-action-type='change' data-entry-type-detail='payment'>
										<span class="icon-edit-bar-add-q"></span><?php echo __('inquiry.common.label.payment') ?>
									</li>
								<?php } ?>
							</ul>
						</li>
						<!-- 選択 -->
						<li class="dropdown-option js-pulldown-parent relative">
							<span class="icon-form-options" style="margin: 0 8px 0 0;"></span>	
							<?php echo __('admin.common.button.select') ?>
							<span class="pulldown-more"></span>
							<ul class="pulldown-list-children2">
								<li class="dropdown-option js-dropdown-option" data-add-type-cd='opt' data-action-type='change' data-entry-type-detail='opt'>
									<span class="icon-form-single-option-on" style="margin: 0 8px 0 0;"></span>
									<?php echo __('survey.inquiry.common.opt') ?>
								</li>
								<li class="dropdown-option js-dropdown-option" data-add-type-cd='chk' data-action-type='change' data-entry-type-detail='chk'>
									<span class="icon-form-multi-option-on" style="margin: 0 8px 0 0;"></span><?php echo __('survey.inquiry.common.chk') ?>
								</li>
								<li class="dropdown-option js-dropdown-option" data-add-type-cd='sel' data-action-type='change'  data-entry-type-detail='sel'>
									<span class="icon-form-pulldown-option-on" style="margin: 0 8px 0 0;"></span><?php echo __('survey.inquiry.common.sel') ?>
								</li>
							</ul>
						</li>
						<!-- 長文 -->
						<li class="dropdown-option js-dropdown-option" data-add-type-cd='txa' data-action-type='change' data-entry-type-detail='txa'>
							<span class="icon-form-writing-text" style="margin: 0 8px 0 0;"></span><?php echo __('survey.inquiry.common.txa') ?>
						</li>
						<!-- 短文 -->
						<li class="dropdown-option js-dropdown-option" data-add-type-cd='txt' data-action-type='change' data-entry-type-detail='txt'>
							<span class="icon-form-writing-short-text" style="margin: 0 8px 0 0;"></span><?php echo __('survey.inquiry.common.txt') ?>
						</li>
						<?php if($inquiry_div != 9){ ?>
							<!-- 予約枠紐付け -->
							<li class="dropdown-option js-dropdown-option" data-add-type-cd='txt' data-action-type='change' data-entry-type-detail='maximum'>
								<span class="icon-calender-gray" style="margin: 0 8px 0 0;"></span><?php echo __('inquiry.common.maximum') ?>
							</li>
						<?php } ?>
						<!-- ファイルアップロード -->
						<li class="dropdown-option js-dropdown-option" data-add-type-cd='fup' data-action-type='change' data-entry-type-detail='fup'>
							<span class="icon-form-upload" style="margin: 0 8px 0 0;"></span><?php echo __('survey.inquiry.common.fup') ?>
						</li>
						<!-- フリースペース -->
						<li class="dropdown-option js-dropdown-option js-survey-type-freespace" data-add-type-cd='frs' data-action-type='change' data-entry-type-detail='frs'>
							<span class="icon-form-fq" style="margin: 0 8px 0 0;"></span><?php echo __('survey.inquiry.common.frs') ?>
						</li>
					<?php } ?>
				</ul>
			</div>
		</div>
		<?php if (!$is_order) { ?>
			<div class="title-container js-title-desc-container js-survey-questions-title-focus" style="position:relative;margin: 12px 24px 0 24px; width: calc(100% - 24px);">
				<img src="./../assets/admin/css/img/icon-drag.svg" width="20" height="20" class="survey-icon-draggable">
				<textarea type="text" class="survey-title js-survey-title js-survey-title-input js-title-desc" placeholder="<?php echo __('inquiry.common.item.placeholder.title.desc') ?>" style="max-width: initial;"></textarea>
				<div class="preview-contents survey-title js-survey-title" style="max-width: initial;"></div>
				<div class="js-to-open-summernote-editor to-open-summernote-editor flexbox-x-axis display-none">
					<img src="./../assets/admin/css/img/icon-form-zoom-in.svg" width="12" height="12">
					<span><?php echo __('inquiry.common.item.to_edit_HTML.title.desc') ?></span>
				</div>
				<!-- リッチテキストエディタ -->
				<div class="survey-branch-modal-container js-survey-questions-input-title-modal rich-text-editor display-none">
					<div class="flexbox flexbox-baselines">
						<h1 class="font-standard font-size-v5 font-family-v4"style="margin:0 auto 24px 0;"><?php echo __('inquiry.common.item.edit_HTML.title.desc') ?></h1>
						<img src="./../assets/admin/css/img/icon-cancel-large.svg" width="24" height="24" class="js-survey-modal-cancel-button pointer">
					</div>
					<textarea type="text" class="survey-title js-survey-title-editor"></textarea>	
					<div class="submit-btn-container" style="position: fixed; bottom: 12px;">
						<div class="btn-larger btn-blue js-suevey-save-title js-survey-modal-add-button"><?php echo __('admin.common.button.confirm')?></div>
						<div class="btn-larger btn-white js-survey-modal-cancel-button"><?php echo __('admin.inquiry.label.cancel')?></div>
					</div>
				</div>
				<div class="modal-background js-survey-questions-input-title-modal-background display-none"></div>
			</div>
		<?php } ?>
		<div class="js-options-main-container">
			<!-- 選択肢のコンテナ -->
			<div class="width-100 js-options-container display-none js-type-container">
				<!-- 選択肢1 -->
				<div class="flexbox-x-axis width-100 survey-space-top-1 js-option-container container-padding">
					<div class="js-opt-icon display-none">
						<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20">
							<g fill="none" fill-rule="evenodd">
								<g fill="#FFF" stroke="#C8CACE">
									<g transform="translate(-496 -679) translate(496 679)">
										<rect width="15" height="15" x="2.5" y="2.5" rx="7.5"/>
									</g>
								</g>
							</g>
						</svg>
					</div>
					<div class="js-chk-icon display-none">
						<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20">
							<g fill="none" fill-rule="evenodd">
								<g fill="#FFF" stroke="#C8CACE">
									<g transform="translate(-496 -455) translate(496 455)">
										<rect width="15" height="15" x="2.5" y="2.5" rx="3"/>
									</g>
								</g>
							</g>
						</svg>
					</div>
					<div class="pulldown-num js-sel-icon display-none" ></div>
					<div class="width-100">
						<div class="flexbox-x-axis">
							<div class="width-100">
								<div class="flexbox-x-axis">
									<input type="text" style="width:95%;"class="border-none survey-space-around-2 survey-height-32 js-option-input js-input-column" onpaste="return false" placeholder="<?php echo (str_replace('{num}', "1", __('survey.inquiry.common.item.option.placeholder'))) ?>">
									<div class="js-add-item-desc-btn btn round image add" style="display:none;"><?php echo __('inquiry.common.item.edit_HTML.product.desc') ?></div>
								</div>
								<div class="js-add-price-input-container"></div>
							</div>
							<div class="js-add-image-input-container"></div>
						</div>
						<!-- 商品説明 -->
						<div class="js-add-item-desc-container clone" style="margin:12px 0 0 0; display: none;">
							<div class="" style="position: relative;width: 96%;min-height: 30px;background: #F6F7F9;padding: 7px 36px 7px 12px;margin: 0 12px;">
							<div class="preview-item-desc" style="height: auto;"></div>
							<span class="icon-delete js-delete-desc pointer" style="position: absolute;right: 12px;top: 7px;"></span>
							<div class="js-open-item-summernote to-open-summernote-editor flexbox-x-axis" style="display: none;">
								<img src="./../assets/admin/css/img/icon-form-zoom-in.svg" width="12" height="12">
								<span><?php echo __('inquiry.common.item.to_edit_HTML.product.desc') ?></span>
							</div>
							</div>
						</div>
					</div>
					<input name="file" type="hidden" style="display:none;" class="js-input-image-to-save">
					<div class="delete-icon js-delete-icon js-delete-option pointer display-none">
						<svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 14 14">
							<g fill="none" fill-rule="evenodd">
								<g>
									<circle cx="7" cy="7" r="7" fill="#3D3F45" fill-opacity=".397"/>
									<g stroke="#FFF" stroke-linecap="round">
										<path d="M6 6L0 0" transform="translate(4 4)"/>
										<path d="M6 6L0 0" transform="translate(4 4) matrix(-1 0 0 1 6 0)"/>
									</g>
								</g>
							</g>
						</svg>
					</div>
				</div>
				<!-- 選択肢2 -->
				<div class="flexbox-x-axis width-100 survey-space-top-1 js-option-container container-padding">
					<div class="js-opt-icon display-none">
						<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20">
							<g fill="none" fill-rule="evenodd">
								<g fill="#FFF" stroke="#C8CACE">
									<g transform="translate(-496 -679) translate(496 679)">
										<rect width="15" height="15" x="2.5" y="2.5" rx="7.5"/>
									</g>
								</g>
							</g>
						</svg>
					</div>
					<div class="js-chk-icon display-none">
						<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20">
							<g fill="none" fill-rule="evenodd">
								<g fill="#FFF" stroke="#C8CACE">
									<g transform="translate(-496 -455) translate(496 455)">
										<rect width="15" height="15" x="2.5" y="2.5" rx="3"/>
									</g>
								</g>
							</g>
						</svg>
					</div>
					<div class="pulldown-num js-sel-icon display-none" ></div>
					<div class="width-100">
						<div class="flexbox-x-axis">
							<div class="width-100">
								<div class="flexbox-x-axis">
									<input type="text" style="width:95%;"class="border-none survey-space-around-2 survey-height-32 js-option-input js-input-column" onpaste="return false" placeholder="<?php echo (str_replace('{num}', "2", __('survey.inquiry.common.item.option.placeholder'))) ?>">
									<div class="js-add-item-desc-btn btn round image add" style="display:none;"><?php echo __('inquiry.common.item.edit_HTML.product.desc') ?></div>
								</div>
								<div class="js-add-price-input-container"></div>
							</div>
							<div class="js-add-image-input-container"></div>
						</div>
						<!-- 商品説明 -->
						<div class="js-add-item-desc-container clone" style="margin:12px 0 0 0; display: none;">
							<div class="" style="position: relative;width: 96%;min-height: 30px;background: #F6F7F9;padding: 7px 36px 7px 12px;margin: 0 12px;">
							<div class="preview-item-desc" style="height: auto;"></div>
							<span class="icon-delete js-delete-desc pointer" style="position: absolute;right: 12px;top: 7px;"></span>
							<div class="js-open-item-summernote to-open-summernote-editor flexbox-x-axis" style="display: none;">
								<img src="./../assets/admin/css/img/icon-form-zoom-in.svg" width="12" height="12">
								<span><?php echo __('inquiry.common.item.to_edit_HTML.product.desc') ?></span>
							</div>
							</div>
						</div>
					</div>
					<input name="file" type="hidden" style="display:none;" class="js-input-image-to-save">
					<div class="delete-icon js-delete-icon js-delete-option pointer display-none">
						<svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 14 14">
							<g fill="none" fill-rule="evenodd">
								<g>
									<circle cx="7" cy="7" r="7" fill="#3D3F45" fill-opacity=".397"/>
									<g stroke="#FFF" stroke-linecap="round">
										<path d="M6 6L0 0" transform="translate(4 4)"/>
										<path d="M6 6L0 0" transform="translate(4 4) matrix(-1 0 0 1 6 0)"/>
									</g>
								</g>
							</g>
						</svg>
					</div>
				</div>
				<!-- 選択肢　コピー用 -->
				<div class="flexbox-x-axis width-100 survey-space-top-1 js-option-container container-padding for-clone display-none">
					<div class="js-opt-icon display-none">
						<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20">
							<g fill="none" fill-rule="evenodd">
								<g fill="#FFF" stroke="#C8CACE">
									<g transform="translate(-496 -679) translate(496 679)">
										<rect width="15" height="15" x="2.5" y="2.5" rx="7.5"/>
									</g>
								</g>
							</g>
						</svg>
					</div>
					<div class="js-chk-icon display-none">
						<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20">
							<g fill="none" fill-rule="evenodd">
								<g fill="#FFF" stroke="#C8CACE">
									<g transform="translate(-496 -455) translate(496 455)">
										<rect width="15" height="15" x="2.5" y="2.5" rx="3"/>
									</g>
								</g>
							</g>
						</svg>
					</div>
					<div class="pulldown-num js-sel-icon display-none" ></div>
					<div class="width-100">
						<div class="flexbox-x-axis">
							<div class="width-100">
								<div class="flexbox-x-axis">
									<input type="text" style="width:95%;"class="border-none survey-space-around-2 survey-height-32 js-option-input js-input-column" onpaste="return false" placeholder="<?php echo (str_replace('{num}', "2", __('survey.inquiry.common.item.option.placeholder'))) ?>">
									<div class="js-add-item-desc-btn btn round image add" style="display:none;"><?php echo __('inquiry.common.item.edit_HTML.product.desc') ?></div>
								</div>
								<div class="js-add-price-input-container"></div>
							</div>
							<div class="js-add-image-input-container"></div>
						</div>
						<!-- 商品説明 -->
						<div class="js-add-item-desc-container clone" style="margin:12px 0 0 0; display: none;">
							<div class="" style="position: relative;width: 96%;min-height: 30px;background: #F6F7F9;padding: 7px 36px 7px 12px;margin: 0 12px;">
							<div class="preview-item-desc" style="height: auto;"></div>
							<span class="icon-delete js-delete-desc pointer" style="position: absolute;right: 12px;top: 7px;"></span>
							<div class="js-open-item-summernote to-open-summernote-editor flexbox-x-axis" style="display: none;">
								<img src="./../assets/admin/css/img/icon-form-zoom-in.svg" width="12" height="12">
								<span><?php echo __('inquiry.common.item.to_edit_HTML.product.desc') ?></span>
							</div>
							</div>
						</div>
					</div>
					<input name="file" type="hidden" style="display:none;" class="js-input-image-to-save">
					<div class="delete-icon js-delete-icon js-delete-option pointer display-none">
						<svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 14 14">
							<g fill="none" fill-rule="evenodd">
								<g>
									<circle cx="7" cy="7" r="7" fill="#3D3F45" fill-opacity=".397"/>
									<g stroke="#FFF" stroke-linecap="round">
										<path d="M6 6L0 0" transform="translate(4 4)"/>
										<path d="M6 6L0 0" transform="translate(4 4) matrix(-1 0 0 1 6 0)"/>
									</g>
								</g>
							</g>
						</svg>
					</div>
				</div>
				<!-- その他 -->
				<div class="flexbox-x-axis survey-space-top-1 js-other-container not-sortable display-none container-padding">
					<div class="js-opt-icon display-none">
						<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20">
							<g fill="none" fill-rule="evenodd">
								<g fill="#FFF" stroke="#C8CACE">
									<g transform="translate(-496 -679) translate(496 679)">
										<rect width="15" height="15" x="2.5" y="2.5" rx="7.5"/>
									</g>
								</g>
							</g>
						</svg>
					</div>
					<div class="js-chk-icon display-none">
						<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20">
							<g fill="none" fill-rule="evenodd">
								<g fill="#FFF" stroke="#C8CACE">
									<g transform="translate(-496 -455) translate(496 455)">
										<rect width="15" height="15" x="2.5" y="2.5" rx="3"/>
									</g>
								</g>
							</g>
						</svg>
					</div>
					<div class="pulldown-num js-sel-icon display-none" ></div>
					<p class="js-option-other survey-width-50 survey-space-around-2"><?php echo __('survey.common.label.other') ?></p>
					<div class="checkbox-small-v1-container js-survey-checkbox-container">
						<input type="text" class="js-placeholder-input placeholder-input" placeholder="<?php echo __('survey.common.label.other.input') ?>">
						<span class="right-aligned"><?php echo __('inquiry.common.label.required') ?></span>
						<label class="js-label checkbox-small-v1-label-off">
							<span class="js-span checkbox-small-v1-span-off"></span>
						</label>
						<input type="checkbox" name="" value="" class="display-none">
					</div>
					<div class="pointer js-delete-icon js-other-delete-icon display-none">
						<svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 14 14">
							<g fill="none" fill-rule="evenodd">
								<g>
									<circle cx="7" cy="7" r="7" fill="#3D3F45" fill-opacity=".397"/>
									<g stroke="#FFF" stroke-linecap="round">
										<path d="M6 6L0 0" transform="translate(4 4)"/>
										<path d="M6 6L0 0" transform="translate(4 4) matrix(-1 0 0 1 6 0)"/>
									</g>
								</g>
							</g>
						</svg>
					</div>
				</div>
				<!-- 「選択肢」または「その他」を追加する -->
				<div class="survey-space-top-1 flexbox-x-axis js-add-option-or-other-container not-sortable container-padding display-none">
					<div class="js-opt-icon display-none">
						<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20">
							<g fill="none" fill-rule="evenodd">
								<g fill="#FFF" stroke="#C8CACE">
									<g transform="translate(-496 -679) translate(496 679)">
										<rect width="15" height="15" x="2.5" y="2.5" rx="7.5"/>
									</g>
								</g>
							</g>
						</svg>
					</div>
					<div class="js-chk-icon display-none">
						<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20">
							<g fill="none" fill-rule="evenodd">
								<g fill="#FFF" stroke="#C8CACE">
									<g transform="translate(-496 -455) translate(496 455)">
										<rect width="15" height="15" x="2.5" y="2.5" rx="3"/>
									</g>
								</g>
							</g>
						</svg>
					</div>
					<div class="pulldown-num js-sel-icon display-none" ></div>
					<div class="flexbox-x-axis survey-space-left-1">
						<span class="font-standard font-family-v1 font-color-v1 pointer js-survey-link-add"><?php echo __('survey.inquiry.common.item.add.option') ?></span>
						<span class="font-standard font-family-v1 font-color-v2 survey-space-around-1 js-survey-text"><?php echo __('survey.inquiry.common.item.option.or') ?></span>
						<span class="font-standard font-family-v1 font-color-v1 pointer js-survey-link-other"><?php echo __('survey.inquiry.common.item.add.other') ?></span>
					</div>
				</div>
				<!-- 枠紐付けのプルダウン -->
				<?php if(!$is_order) {?>
					<div class="survey-space-top-1  width-100 container-padding js-maximum-type" style="display: none;">
						<div class="js-limit-advanced-setting-container flexbox-x-axis" style="margin: 10px 0 0 0">
							<p style="min-width: 48px;"><?php echo __('inquiry.common.label.detailed.settings') ?></p>
							<div class="flexbox-x-axis added-date-detailed-container js-added-limit-advanced-setting-container js-limit-advanced-setting js-maximum-setting pointer"
							style="margin: 0 0 0 12px; width: 100%; justify-content: flex-end; text-decoration: underline;"><?php echo __('admin.inquiryrefer.label.not_set')?></div>
						</div>
					</div>
				<?php } ?>
			</div>
			<!-- 短文のコンテナ -->
			<div class="survey-space-top-1 width-100 js-txt-container display-none container-padding js-type-container">
				<div class="flexbox-x-axis js-check-range pointer" style="margin:30px 0 16px 0; display:none;width: 100px;">
					<span class="icon-check js-icon-check"></span>
					<p>　<?php echo __('inquiry.common.txt.range') ?></p>	
				</div>
				<div class="flexbox-x-axis js-range-input-container" style="display: none;">
					<input type="text" class="short-text-input font-color-v3" placeholder="<?php echo __('inquiry.common.txt.sample.placeholder') ?>">
					<span style="margin:0 20px;">〜</span>
					<input type="text" class="short-text-input font-color-v3" placeholder="<?php echo __('inquiry.common.txt.sample.placeholder') ?>">
				</div>
				<input type="text" class="short-text-input js-short-text-input font-color-v3" placeholder="<?php echo __('inquiry.common.txt.sample.placeholder') ?>">
				<div class="survey-space-top-1 flexbox-x-axis width-100 js-pulldown-txt-type-container">
					<div class="right-aligned flexbox-x-axis js-short-text-count-container">
						<div class="survey-space-left-2 js-short-text-count js-short-text-count-min"><?php echo __('survey.inquiry.common.txa.min') ?>：<input class ="survey-short-text-input" type="text"></div>
						<div class="survey-space-left-2 js-short-text-count js-short-text-count-max"><?php echo __('survey.inquiry.common.txa.max') ?>：<input class ="survey-short-text-input" type="text"></div>
					</div>
					<div class="right-aligned flexbox-x-axis js-short-text-num-container" style="display: none;">
						<div class="survey-space-left-2 js-short-text-num js-short-text-num-min"><?php echo __('inquiry.common.txt.min') ?>：<input class ="survey-short-text-input" type="text"></div>
						<div class="survey-space-left-2 js-short-text-num js-short-text-num-max"><?php echo __('inquiry.common.txt.max') ?>：<input class ="survey-short-text-input" type="text"></div>
					</div>
					<div class="right-aligned flexbox-x-axis js-check-mail-confirm pointer" style="display: none;">
						<span class="icon-check js-icon-check"></span>
						<p> <?php echo __('survey.inquiry.common.txt.reconfirm.mail') ?></p>
					</div>
					<div class="right-aligned flexbox-x-axis js-send-mail pointer" style="display: none;">
						<span class="icon-check js-send-mail-check"></span>
						<p> <?php echo __('inquiry.common.txt.mail.sendmail') ?></p>
					</div>
					<div class="right-aligned flexbox-x-axis js-clock-container pointer" style="display: none;">
						<?php echo __('inquiry.common.txt.every_minute') ?>　<input class="survey-short-text-input" style="width: 120px;" type="number">
					</div>
					<div class="right-aligned js-coupon-container pointer" style="display: none;">
						<div class="survey-add-dest-user js-button-select-coupon" style="width: fit-content;">
							<img src="./../assets/admin/css/img/icon-add.svg">	
							<?php echo __('inquiry.common.txt.add.coupon') ?>
						</div>
					</div>
				</div>
				<?php if($inquiry_div != 9){ ?>
					<div class="right-aligned flexbox-x-axis js-check-unique-container pointer" style="display: none;width:fit-content;padding: 12px 10px 0 0;">
						<span class="icon-check js-icon-check"></span>
						<p style="padding: 0 10px 0 0;"><?php echo __('inquiry.common.txt.check.unique') ?></p>
						<input type="number" min="1" class="survey-period-date-container js-dayspan" style="width: 70px; padding: 0px 12px; color: rgb(0, 0, 0);">
						<span class="mr10" style="color: rgb(0, 0, 0);"><?php echo __('inquiry.common.label.days') ?></span>
						<input type="time" class="survey-period-date-container js-timespan-start" style="width: 62px; padding: 0px 12px; color: rgb(0, 0, 0);">
						<span class="mr10" style="color: rgb(0, 0, 0);">〜</span>
						<input type="time" class="survey-period-date-container js-timespan-end" style="width: 62px; padding: 0px 12px; color: rgb(0, 0, 0);">
						<p><?php echo __('inquiry.common.txt.check.unique.suffix') ?></p>
					</div>
				<?php } ?>
				<div class="flexbox-x-axis js-added-date-detailed-container" style="display: none; margin: 12px 0 0 0; gap: 12px;">
					<p class="" style="min-width: fit-content;"><?php echo __('inquiry.common.label.detailed.settings')?></p>
					<div class="js-added-detailed added-date-detailed-container flexbox-x-axis js-check-date-detail" style="padding: 8px; width: 100%; justify-content: flex-end; text-decoration: underline; cursor: pointer; overflow-wrap: anywhere;"><?php echo __('admin.inquiryrefer.label.not_set')?></div>
				</div>
				<div class="flexbox-x-axis js-added-time-detailed-container" style="display: none; margin: 12px 0 0 0; gap: 12px;">
					<p class="" style="min-width: fit-content;"><?php echo __('inquiry.common.label.detailed.settings')?></p>
					<div class="js-added-detailed-time added-date-detailed-container flexbox-x-axis js-check-time-detail" style="padding: 8px; width: 100%; justify-content: flex-end; text-decoration: underline; cursor: pointer;"><?php echo __('admin.inquiryrefer.label.not_set')?></div>
				</div>
				<div class="survey-branch-modal-container js-date-setting-container" style="<?php if($inquiry_div != 9){ echo 'min-height: 712px;'; } else { echo "min-height: 300px;"; } ?> display: none;">
					<h1 class="font-standard font-size-v5 font-family-v4 js-date-setting-title"><?php echo __('inquiry.common.label.detailed.settings') ?></h1>
					<div class="survey-space-top-1 js-date-detail-setting background-pale-gray inner-space-1">
						<div class="inner-space-2" style="<?php if($inquiry_div != 9){ echo ''; } else { echo "padding: 30px 0;"; } ?>">
							<!-- 時制 -->
							<div class="flexbox survey-space-top-1 js-mode-container">
								<div class="width-96" style="position:relative;display:flex;width:100px">
									<?php echo __('inquiry.common.txt.timing') ?>
									<span class="icon-detail" title="<?php echo htmlspecialchars(__('inquiry.common.txt.timing.description')) ?>"></span>
								</div>
								<div class="checkbox-v2-container flexbox-x-axis pointer">
									<div class="checkbox-v2-options flexbox-x-axis inner-space-3 js-future"><?php echo __('inquiry.common.txt.future') ?></div>
									<div class="flexbox-x-axis inner-space-3 js-past"><?php echo __('inquiry.common.txt.past') ?></div>
									<div class="flexbox-x-axis inner-space-3 js-all"><?php echo __('inquiry.common.txt.all_period') ?></div>
								</div>
							</div>
							<?php if($inquiry_div != 9){ ?>
								<!-- 開始日 -->
								<div class="flexbox" style="margin:22px 0 0 0;">
									<div class="width-96"><?php echo __('inquiry.common.txt.start.date') ?></div>
									<div class="flexbox js-start-date-container" style="flex-direction: column;">
										<div class="flexbox-x-axis js-start-date-setting">
											<span class="icon-form-single-option-off survey-space-right-4"></span>
											<span class="js-start-date-from-label" style="margin: 0 8px 0 8px;">＋</span>
											<input type="text" class="survey-short-text-input js-start-date survey-space-right-4 js-start-date-number">
											<span><?php echo __('inquiry.common.label.day')?></span>
										</div>
										<div class="flexbox-x-axis survey-space-top-5 js-start-date-setting">
											<span class="icon-form-single-option-off survey-space-right-4"></span>
											<div class="survey-period-date-container background-white">
												<span class="icon-calender"></span>
											</div>
										</div>
									</div>
								</div>
								<!-- 終了日 -->
								<div class="flexbox survey-space-top-2 js-end-date-container">
									<div class="width-96"><?php echo __('inquiry.common.txt.end.date') ?></div>
									<div class="flexbox js-end-date-container" style="flex-direction: column;">
										<div class="flexbox-x-axis js-end-date-setting">
											<span class="icon-form-single-option-off survey-space-right-4"></span>
											<span class="js-end-date-from-label" style="margin: 0 8px 0 8px;">＋</span>
											<input type="text" class="survey-short-text-input js-end-date survey-space-right-4 js-end-date-number">
											<span><?php echo __('inquiry.common.label.day')?></span>
										</div>
										<div class="flexbox-x-axis survey-space-top-5 js-end-date-setting">
											<span class="icon-form-single-option-off survey-space-right-4"></span>
											<div class="survey-period-date-container background-white">
												<span class="icon-calender"></span>
											</div>
										</div>
									</div>
								</div>
								<!-- 曜日 -->
								<div class="flexbox survey-space-top-2">
									<div class="width-96"><?php echo __('inquiry.common.label.specify.day') ?></div>
									<label class="js-select-day flexbox-center icon-round-corners-small icon-background-light-blue survey-space-right-3">
										<span><?php echo __('inquiry.common.label.sun') ?></span>
										<input type="checkbox" value="" class="display-none" style="display: none;">
									</label>
									<label class="js-select-day flexbox-center icon-round-corners-small icon-background-light-blue survey-space-right-3">
										<span><?php echo __('inquiry.common.label.mon') ?></span>
										<input type="checkbox" value="" class="display-none" style="display: none;">
									</label>
									<label class="js-select-day flexbox-center icon-round-corners-small icon-background-light-blue survey-space-right-3">
										<span><?php echo __('inquiry.common.label.tue') ?></span>
										<input type="checkbox" value="" class="display-none" style="display: none;">
									</label>
									<label class="js-select-day flexbox-center icon-round-corners-small icon-background-light-blue survey-space-right-3">
										<span><?php echo __('inquiry.common.label.wed') ?></span>
										<input type="checkbox" value="" class="display-none" style="display: none;">
									</label>
									<label class="js-select-day flexbox-center icon-round-corners-small icon-background-light-blue survey-space-right-3">
										<span><?php echo __('inquiry.common.label.thurs') ?></span>
										<input type="checkbox" value="" class="display-none" style="display: none;">
									</label>
									<label class="js-select-day flexbox-center icon-round-corners-small icon-background-light-blue survey-space-right-3">
										<span><?php echo __('inquiry.common.label.fri') ?></span>
										<input type="checkbox" value="" class="display-none" style="display: none;">
									</label>
									<label class="js-select-day flexbox-center icon-round-corners-small icon-background-light-blue survey-space-right-3">
										<span><?php echo __('inquiry.common.label.sat') ?></span>
										<input type="checkbox" value="" class="display-none" style="display: none;">
									</label>
								</div>
								<!-- 祝日 -->
								<div class="flexbox survey-space-top-1 js-holiday-container">
									<div class="width-96"><?php echo __('inquiry.common.label.holiday') ?></div>
									<div class="checkbox-v2-container flexbox-x-axis pointer">
										<div class="checkbox-v2-options flexbox-x-axis inner-space-3 js-include"><?php echo __('inquiry.common.label.including') ?></div>
										<div class="flexbox-x-axis inner-space-3 js-exclude"><?php echo __('inquiry.common.label.excluding') ?></div>
									</div>
								</div>
								<!-- 開催期間 -->
								<div class="flexbox survey-space-top-2 js-span-container">
									<div class="width-96"><?php echo __('inquiry.common.label.holding.period') ?></div>
									<div class="flexbox" style="flex-direction: column;">
										<div class="flexbox-x-axis js-span-setting">
											<span class="icon-form-single-option-off survey-space-right-4"></span>
											<input type="text" class="survey-short-text-input js-span survey-space-right-4">
											<span class="js-span-date"><?php echo __('inquiry.common.label.days') ?></span>
										</div>
										<div class="flexbox-x-axis survey-space-top-5 js-span-setting">
											<span class="icon-form-single-option-off survey-space-right-4"></span>
											<input type="text" class="survey-short-text-input js-span survey-space-right-4">
											<span class="js-span-month"><?php echo __('inquiry.common.label.months') ?></span>
										</div>
									</div>
								</div>
								<!-- 除外日 -->
								<div class="flexbox survey-space-top-2 js-excluding-day-container">
									<div class="width-96"><?php echo __('inquiry.common.label.excluding.day') ?></div>
									<input type="text" class="survey-period-date-container" style="width:100%;max-width: 470px;padding:0 12px;" placeholder="例)2021/9/20, 2021/10/1-2021/10/15">
								</div>
								<!-- 期限 -->
								<div class="flexbox survey-space-top-2 js-limit-day-container">
									<div class="width-96"><?php echo __('inquiry.common.label.term') ?></div>
									<div class="flexbox-x-axis">
										<?php if($lang_cd === 'en') echo "<span class='survey-space-right-4'><?php echo __('inquiry.common.label.up.to') ?></span>" ?>
										<input type="text" class="survey-period-date-container js-limit-date" style="width:100px;padding:0 12px;">
										<span class="survey-space-right-4"><?php echo __('inquiry.common.label.days.before') ?></span>
										<?php if($lang_cd === 'en') echo "<span class='survey-space-right-4'><?php echo __('inquiry.common.label.until') ?></span>" ?>
										<input type="text" class="survey-period-date-container js-limit-hour" style="width:100px;padding:0 12px;"> 
										<?php if($lang_cd === 'ja') echo "<span class='survey-space-right-4'><?php echo __('inquiry.common.label.until') ?></span>" ?>
									</div>
								</div>
							<?php } ?>
						</div>
					</div>
					<div class="submit-btn-container modal-button-container">
						<div class="btn-larger btn-blue active js-date-setting-ok"><?php echo __('admin.common.button.confirm')?></div>
						<div class="btn-larger btn-white js-date-setting-cancel"><?php echo __('admin.common.button.cancel')?></div>
						<div class="btn-larger btn-red-border flexbox-center js-date-setting-delete-button">
							<span class="icon-delete space-0"></span>
						</div>
					</div>
				</div>
			</div>
			<!-- 長文のコンテナ -->
			<div class="long-text-container js-txa-container container-padding display-none js-type-container">
				<input type="text" class="survey-space-top-1 font-color-v3 long-text-input">
				<?php if($inquiry->faq_scene != "" && $inquiry_div != 19) {?>
					<div class="js-survey-checkbox-container" style="width:220px; height: 32px; margin: 0 10px 0 auto; display: flex;align-items: center; margin: 0 0 0 12px;">
						<span class="right-aligned" style="width:fit-content; white-space:nowrap;"><?php echo __('inquiry.common.txa.faq') ?></span>
						<label class="js-label checkbox-small-v1-label-off">
							<span class="js-span checkbox-small-v1-span-off"></span>
						</label>
						<input type="checkbox" name="" value="" class="display-none">
					</div>
				<?php }?>
			</div>
			<!-- ファイルアップロードのコンテナ -->
			<div class="survey-attachment-container survey-space-top-1 js-fup-container display-none container-padding js-type-container">
				<p class="font-standard font-family-v1 font-color-v3 survey-space-top-1 flexbox-x-axis">
					<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20">
						<g fill="none" fill-rule="evenodd" stroke-linecap="round" stroke-linejoin="round">
							<g stroke="#C8CACE">
								<path fill="#FFF" d="M16 7v9c0 .552-.448 1-1 1H5c-.552 0-1-.448-1-1V4c0-.552.448-1 1-1h6.818L16 7z"/>
								<path d="M16 7L12 3 12 7z"/>
							</g>
						</g>
					</svg>
					<?php echo __('survey.inquiry.common.fup') ?>
				</p>
				<div style="display: flex;gap: 17px;padding: 1rem;">
					<?php echo $upload_limit_size . __('admin.common.label.file_upload_limit_size')?>,
					<?php echo __('admin.common.label.file_upload_extension') . " : " . $extensions ?>
				</div>
			</div>
			<!-- お名前：よくある質問のコンテナ メモ-->
			<div class="survey-name-container survey-space-top-1 js-name-container display-none focusout container-padding js-type-container">
				<div class="js-name-full-container name-full-container">
					<div class="flexbox-x-axis survey-space-top-1">
						<input type="text" class="survey-title js-full-name-label font-standard font-family-v1 js-input-column background-pale-gray">
						<input type="text" class="survey-title js-full-name-typehplder font-standard font-family-v1 font-color-v3 js-input-column">
					</div>
					<?php if($lang_cd === 'ja') { ?>
					<div class="flexbox-x-axis survey-space-top-1">
						<input type="text" class="survey-title js-full-furigana-label font-standard font-family-v1 js-input-column background-pale-gray">
						<input type="text" class="survey-title js-full-furigana-typehplder font-standard font-family-v1 font-color-v3 js-input-column">
					</div>
					<?php }?>
				</div>
				<div class="js-name-separate-container name-separate-container" style="display:none;">
					<div class="flexbox-x-axis survey-space-top-1">
						<input type="text" class="survey-title js-first-name-label font-standard font-family-v1 js-input-column background-pale-gray">
						<input type="text" class="survey-title js-first-name-typehplder font-standard font-family-v1 font-color-v3 js-input-column">
					</div>
					<div class="flexbox-x-axis survey-space-top-1">
						<input type="text" class="survey-title js-last-name-label font-standard font-family-v1 js-input-column background-pale-gray">
						<input type="text" class="survey-title js-last-name-typehplder font-standard font-family-v1 font-color-v3 js-input-column">
					</div>
					<?php if($lang_cd === 'ja') { ?>
					<div class="flexbox-x-axis survey-space-top-1">
						<input type="text" class="survey-title js-first-name-kana-label font-standard font-family-v1 js-input-column background-pale-gray">
						<input type="text" class="survey-title js-first-name-kana-typehplder font-standard font-family-v1 font-color-v3 js-input-column">
					</div>
					<div class="flexbox-x-axis survey-space-top-1">
						<input type="text" class="survey-title js-last-name-kana-label font-standard font-family-v1 js-input-column background-pale-gray">
						<input type="text" class="survey-title js-last-name-kana-typehplder font-standard font-family-v1 font-color-v3 js-input-column">
					</div>
					<?php }?>
				</div>
				<div class="flexbox-x-axis js-name-full pointer" style="width:fit-content;padding: 12px 0 0 0;">
					<span class="icon-check js-icon-check"></span>
					<p><?php echo __('inquiry.common.label.name.separate') ?></p>
				</div>
			</div>
			<!-- 住所：よくある質問のコンテナ -->
			<div class="survey-address-container survey-space-top-1 js-address-container display-none focusout container-padding js-type-container">
				<?php if($lang_cd !== 'ja'){?>
					<div class="flexbox-x-axis survey-space-top-1 country">
						<input type="text" class="survey-title js-input-column js-address-country-label font-standard font-family-v1 background-pale-gray">
						<input type="text" class="survey-title js-input-column js-address-country font-standard font-family-v1 font-color-v3">
					</div>
				<?php }?>
				<div class="flexbox-x-axis survey-space-top-1">
					<input type="text" class="survey-title js-input-column js-address-postcode-label font-standard font-family-v1 background-pale-gray">
					<input type="text" class="survey-title js-input-column js-address-postcode font-standard font-family-v1 font-color-v3">
				</div>
				<div class="flexbox-x-axis survey-space-top-1">
					<input type="text" class="survey-title js-input-column js-address-prefecture-label font-standard font-family-v1 background-pale-gray">
					<input type="text" class="survey-title js-input-column js-address-prefecture font-standard font-family-v1 font-color-v3">
				</div>
				<div class="flexbox-x-axis survey-space-top-1">
					<input type="text" class="survey-title js-input-column js-address-municipalities-label font-standard font-family-v1 background-pale-gray">
					<input type="text" class="survey-title js-input-column js-address-municipalities font-standard font-family-v1 font-color-v3">
				</div>
				<div class="flexbox-x-axis survey-space-top-1">
					<input type="text" class="survey-title js-input-column js-address-houseNumber-label font-standard font-family-v1 background-pale-gray">
					<input type="text" class="survey-title js-input-column js-address-houseNumber font-standard font-family-v1 font-color-v3">
				</div>
				<div class="flexbox-x-axis survey-space-top-1">
					<input type="text" class="survey-title js-input-column js-address-roomNumber-label font-standard font-family-v1 background-pale-gray">
					<input type="text" class="survey-title js-input-column js-address-roomNumber font-standard font-family-v1 font-color-v3">
				</div>
			</div>
			<!-- フリースペースのコンテナ -->
			<div class="survey-freespace-container survey-space-top-1 js-frs-container display-none js-type-container">
			</div>
			<!-- 予約管理のコンテナ -->
			<div class="js-maximum-container display-none container-padding js-type-container" style="margin:30px 0 0 0;">
				<div class="flexbox-x-axis js-maximum-time-container" style="display: none;">
					<input type="text" class="short-text-input font-color-v3" placeholder="yyyy-mm-dd" style="pointer-events: none;">
					<span style="margin:0 20px;"></span>
					<input type="text" class="short-text-input font-color-v3" placeholder="hh:mm" style="pointer-events: none;">
				</div>
				<input type="text" class="short-text-input js-maximum-text-input font-color-v3" placeholder="yyyy-mm-dd" style="pointer-events: none; display: none;">	
				<!-- 在庫管理　枠管理　枠選択 -->
				<div class="js-limit-mng-container js-1d-1h flexbox-x-axis" style="margin: 10px 0 0 0">
					<h1 class="font-standard font-color-v2" style="min-width: 48px;"><?php echo __('inquiry.common.maximum.control') ?></h1>
				</div>
				<div class="js-limit-advanced-setting-container flexbox-x-axis" style="margin: 10px 0 0 0">
					<p style="min-width: 48px;"><?php echo __('inquiry.common.label.detailed.settings') ?></p>
					<div class="flexbox-x-axis added-date-detailed-container js-added-limit-advanced-setting-container js-limit-advanced-setting js-maximum-setting pointer"
						style="margin: 0 0 0 12px; width: 100%; justify-content: flex-end; text-decoration: underline;"><?php echo __('admin.inquiryrefer.label.not_set')?></div>
				</div>
			</div>
		</div>
		<!-- 注釈 -->
		<div class="js-icon-form-description-container icon-form-description-container relative font-color-v2 container-padding">
			<textarea class="form-description-input font-standard font-color-v2 js-form-description-input survey-title" style="padding:10px 14px;"></textarea>
		</div>
		<!-- 削除、回答必須のコンテナ -->
		<div class="submit-btn-container js-button-container container-padding" style="margin:32px 0 0 auto; width: fit-content; width: -moz-fit-content;">
			<div class="survey-btn" title="<?php echo __('survey.inquiry.common.delete.item') ?>">
				<img src="./../assets/admin/css/img/icon-delete.svg" width="12" height="12" class="js-edidind-survey-delete-icon">
			</div>
			<?php if(!$is_order){?>
				<div class="survey-btn js-add-action-modal" style="display: none;" title="<?php echo __('inquiry.common.add.notify_on_selection') ?>" data-action=''> 
					<span class="icon-add-action js-add-action"></span>
				</div>
				<!-- 画像追加 -->
				<div class="survey-btn js-add-photo-icon" style="display: none;" title="<?php echo __('survey.inquiry.common.item.image.add.title') ?>">
					<img src="./../assets/admin/css/img/icon-photo.svg" width="12" height="12" class="js-icon-photo-active" style="display:none;">
					<img src="./../assets/admin/css/img/icon-photo-unactive.svg" width="12" height="12" class="js-icon-photo-unactive">
				</div>
				<?php if($inquiry_div != 9){ ?>
					<!-- 金額追加 -->
					<div class="survey-btn js-add-price-icon" style="display: none;" title="<?php echo __('inquiry.common.add.amount_quantity') ?>">
						<img src="./../assets/admin/css/img/icon-form-price.svg" width="12" height="12" class="js-icon-form-price-active" style="display:none;">
						<img src="./../assets/admin/css/img/icon-form-price-unactive.svg" width="12" height="12" class="js-icon-form-price-unactive">
					</div>
				<?php } ?>
				<div class="survey-btn js-jump-icon-container" title="<?php echo __('survey.inquiry.common.branch.add.and') ?>">
					<img src="./../assets/admin/css/img/icon-jump-unactive.svg" width="12" height="12" class="js-edidind-survey-jump-unactive-icon">
					<img src="./../assets/admin/css/img/icon-jump-active.svg" width="12" height="12" class="js-edidind-survey-jump-active-icon" style="display: none;">
				</div>
				<div class="survey-btn icon-form-description js-icon-form-description" title="<?php echo __('inquiry.common.add.item.desc') ?>"></div>
				<div class="js-survey-checkbox-container">
					<span class=""><?php echo __('survey.inquiry.common.label.required') ?></span>
					<label class="js-label checkbox-small-v1-label-off">
						<span class="js-span checkbox-small-v1-span-off"></span>
					</label>
					<input type="checkbox" name="" value="" class="display-none">
				</div>
			<?php } ?>
		</div>
		<!-- コンテナ削除時のモーダルウィンドウ -->
		<div class="modal-smaller-container js-survey-delete-container" style="display: none;">
			<div class="modal-small-title-container">
				<h4 class="modal-small-title"><?php echo __('survey.inquiry.common.delete.item.title') ?></h4>
				<svg class="survey-modal-close-button js-survey-modal-close-button" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
					<g fill="none" fill-rule="evenodd" stroke-linecap="round">
						<g stroke="#3D3F45" stroke-width="2">
							<g>
								<path d="M16 16L0 0" transform="translate(4 4)"/>
								<path d="M16 16L0 0" transform="translate(4 4) matrix(-1 0 0 1 16 0)"/>
							</g>
						</g>
					</g>
				</svg>
			</div>
			<p style="margin: 32px 0 0 10px;"><?php echo __('admin.common.modal.delete.confirm') ?></p>
			<div class="submit-btn-container" style="position: fixed; bottom: 12px;">
				<div class="btn-larger btn-red js-survey-modal-delete-button"><?php echo __('admin.common.button.delete') ?></div>
				<div class="btn-larger btn-white js-survey-modal-cancel-button"><?php echo __('admin.inquirydesc.label.modal_cancel_button') ?></div>
			</div>
		</div>
		<div class="modal-background js-survey-questions-modal-background display-none"></div>
	</div>
</div>
<!-- template -->
<!-- 項目を追加、編集するコンテナ -->
<section class="survey-editing-container js-survey-editing-container clone" id="sortable1" data-secno="1" data-fold-section="false" style="display: none;">
	<!-- セクションの番号 -->
	<div class="survey-section-num js-survey-section-num not-sortable display-none">
		<?php echo __('survey.inquiry.common.label.section') ?>
		<span class="js-section-numerator"></span>
		/
		<span class="js-section-denominator"></span>
	</div>
	<!-- セクションのタイトルコンテナ -->
	<section class="js-section-title-container flexbox not-sortable display-none">
		<input type="text" class="survey-space-all-around-5 text-input-longer js-section-title">
		<div class="js-num-of-surveys font-standard font-family-v4 survey-space-top-bottom-3" style="width: 200px;"></div>
		<div class="flexbox-x-axis js-section-icon-container pointer" style="margin:12px 0 0 auto;">
			<div class="survey-btn"	title="<?php echo __('survey.inquiry.common.section.delete') ?>">
				<img src="./../assets/admin/css/img/icon-delete.svg" width="12" height="12" class="delete-icon js-survey-delete-section-icon">
			</div>
			<div class="survey-btn js-jump-icon-container js-jump-section-icon-container"	title="<?php echo __('survey.inquiry.common.branch.add.and') ?>">
				<img src="./../assets/admin/css/img/icon-jump-unactive.svg" width="12" height="12" class="js-edidind-survey-jump-unactive-icon">
				<img src="./../assets/admin/css/img/icon-jump-active.svg" width="12" height="12" class="js-edidind-survey-jump-active-icon" style="display: none;">
			</div>
			<div class="js-survey-fold-section" style="width:auto;" title="<?php echo __('survey.inquiry.common.section.fold_up') ?>">
				<span class="icon-fold-section-open"></span>
			</div>
		</div>
		<!-- コンテナ削除時のモーダルウィンドウ -->
		<div class="modal-smaller-container js-survey-delete-container" style="display: none;">
			<div class="modal-small-title-container">
				<h4 class="modal-small-title"><?php echo __('survey.inquiry.common.section.delete.confirm') ?></h4>
				<svg class="survey-modal-close-button js-survey-modal-close-button" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
					<g fill="none" fill-rule="evenodd" stroke-linecap="round">
						<g stroke="#3D3F45" stroke-width="2">
							<g>
								<path d="M16 16L0 0" transform="translate(4 4)"/>
								<path d="M16 16L0 0" transform="translate(4 4) matrix(-1 0 0 1 16 0)"/>
							</g>
						</g>
					</g>
				</svg>
			</div>
			<p style="margin: 32px 0 0 12px;"><?php echo __('admin.common.modal.delete.confirm') ?></p>
			<div class="submit-btn-container" style="position: fixed; bottom: 12px;">
				<div class="btn-larger btn-red js-survey-modal-delete-section-button"><?php echo __('admin.common.button.delete') ?></div>
				<div class="btn-larger btn-white js-survey-modal-close-button"><?php echo __('admin.inquirydesc.label.modal_cancel_button') ?></div>
			</div>
		</div>
		<div class="modal-background js-survey-questions-modal-background display-none"></div>
	</section>
	<!-- 質問形式選択 -->
	<div class="survey-add-container relative js-survey-add-container not-sortable pointer">
		<div class="btn-add-survey js-add-survey-button js-add-survey-button-in-section">
			<svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12">
				<g fill="none" fill-rule="evenodd" stroke-linecap="round" stroke-linejoin="round">
					<g stroke="#245BD6" stroke-width="2">
						<g>
							<path d="M6 10L6 2" transform="translate(-997 -453) translate(997 453) matrix(1 0 0 -1 0 12)"/>
							<path d="M6 10L6 2" transform="translate(-997 -453) translate(997 453) matrix(0 -1 -1 0 12 12)"/>
						</g>
					</g>
				</g>
			</svg>
			<span><?php echo __('survey.inquiry.common.label.item.add') ?></span>
		</div>
		<ul class="dropdown-options dropdown-middle" style="top: -10px; width:fit-content; width: -moz-fit-content;">
			<?php if ($is_order) { ?>
				<!-- オーダーの項目 -->
				<li class="dropdown-option js-dropdown-option" data-add-type-cd='ord' data-action-type='add' data-entry-type-detail='ord'>
					<span class="icon-form-single-option-on" style="margin: 0 8px 0 0;"></span>旅ナカオーダー
				</li>
			<?php } else { ?>
				<!-- オーダー以外の項目 -->
				<!-- よく使う項目 -->
				<li class="dropdown-option js-pulldown-parent relative">
					<span class="icon-form-fleq" style="margin: 0 8px 0 0;"></span>	
					<?php echo __('survey.inquiry.common.fleq') ?>
					<span class="pulldown-more"></span>
					<ul class="pulldown-list-children2">
						<li class="dropdown-option js-dropdown-option" data-add-type-cd='spl' data-action-type='add' data-entry-type-detail='name'>
							<span class="icon-edit-bar-add-q"></span><?php echo __('inquiry.common.label.name') ?>
						</li>
						<li class="dropdown-option js-dropdown-option" data-add-type-cd='spl' data-action-type='add' data-entry-type-detail='address'>
							<span class="icon-edit-bar-add-q"></span><?php echo __('admin.common.label.address') ?>
						</li>
						<?php if($inquiry_div != 9){ ?>
							<li class="dropdown-option js-dropdown-option" data-add-type-cd='opt' data-action-type='add' data-entry-type-detail='payment'>
								<span class="icon-edit-bar-add-q"></span><?php echo __('inquiry.common.label.payment') ?>
							</li>
						<?php } ?>
					</ul>
				</li>
				<!-- 選択 -->
				<li class="dropdown-option js-pulldown-parent relative">
					<span class="icon-form-options" style="margin: 0 8px 0 0;"></span>	
					<?php echo __('admin.common.button.select') ?>
					<span class="pulldown-more"></span>
					<ul class="pulldown-list-children2">
						<li class="dropdown-option js-dropdown-option" data-add-type-cd='opt' data-action-type='add' data-entry-type-detail='opt'>
							<span class="icon-form-single-option-on" style="margin: 0 8px 0 0;"></span><?php echo __('survey.inquiry.common.opt') ?>
						</li>
						<li class="dropdown-option js-dropdown-option" data-add-type-cd='chk' data-action-type='add' data-entry-type-detail='chk'>
							<span class="icon-form-multi-option-on" style="margin: 0 8px 0 0;"></span><?php echo __('survey.inquiry.common.chk') ?>
						</li>
						<li class="dropdown-option js-dropdown-option" data-add-type-cd='sel' data-action-type='add' data-entry-type-detail='sel'>
							<span class="icon-form-pulldown-option-on" style="margin: 0 8px 0 0;"></span><?php echo __('survey.inquiry.common.sel') ?>
						</li>
					</ul>
				</li>
				<!-- 長文 -->
				<li class="dropdown-option js-dropdown-option" data-add-type-cd='txa' data-action-type='add' data-entry-type-detail='txa'>
					<span class="icon-form-writing-text" style="margin: 0 8px 0 0;"></span><?php echo __('survey.inquiry.common.txa') ?>
				</li>
				<!-- 短文 -->
				<li class="dropdown-option js-dropdown-option" data-add-type-cd='txt' data-action-type='add' data-entry-type-detail='txt'>
					<span class="icon-form-writing-short-text" style="margin: 0 8px 0 0;"></span><?php echo __('survey.inquiry.common.txt') ?>
				</li>
				<?php if($inquiry_div != 9){ ?>
					<!-- 予約枠紐付け -->
					<li class="dropdown-option js-dropdown-option" data-add-type-cd='txt' data-action-type='add' data-entry-type-detail='maximum'>
						<span class="icon-calender-gray" style="margin: 0 8px 0 0;"></span><?php echo __('inquiry.common.maximum') ?>
					</li>
				<?php } ?>
				<!-- ファイルアップロード -->
				<li class="dropdown-option js-dropdown-option" data-add-type-cd='fup' data-action-type='add' data-entry-type-detail='fup'>
					<span class="icon-form-upload" style="margin: 0 8px 0 0;"></span><?php echo __('survey.inquiry.common.fup') ?>
				</li>
				<!-- フリースペース -->
				<li class="dropdown-option js-dropdown-option" data-add-type-cd='frs' data-action-type='add' data-entry-type-detail='frs'>
					<span class="icon-form-fq" style="margin: 0 8px 0 0;"></span><?php echo __('survey.inquiry.common.frs') ?>
				</li>
			<?php } ?>
		</ul>
	</div>
</section>

<script type="text/javascript">
    const num_label_config = <?php echo json_encode($num_label_config); ?>;
	const use_number_config = <?php echo json_encode($use_number_config); ?>;
</script>

<?php 
    echo('<script type="text/javascript" src="/assets/admin/inquiry/locales/inquiryentry-' . $lang_cd . '.js"></script>');
?>