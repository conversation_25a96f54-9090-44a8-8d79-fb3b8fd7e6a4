<style>
	.icon-detail {
		width: 23px !important;
	}
	.basic-label {
		width: 15em !important;
	}
	.section-container {
		margin: 12px 12px 12px 12px;
    	padding: 0 0 12px 0px;
		border-bottom: 1px solid #EBEDF2;
		background: #fff;
	}
	.section-container.hide-section .setting-header-conteiner div {
		margin: 0 !important;
	}

	.talkappi-dropdown-selected-text {
		max-width: 350px;
	}
	.talkappi-pulldown[data-name="mail_signature"] .talkappi-dropdown-options {
		top: unset;
		bottom: 30px;
	}
	.parameter-url-label {
		width: 80px;
		text-align: right;
		margin: 12px;
	}
</style>

<script type="text/javascript">
	var _inquiry_entries = <?php echo $entries?>;
	const _inquiry_div = "<?php echo $inquiry_div ?>";
	var _bot_lang = <?php echo json_encode($_bot_lang, JSON_UNESCAPED_UNICODE|JSON_HEX_APOS)?>;
	var login_class_cd = <?php if ($login_class_cd == '') {echo('[]');} else {echo($login_class_cd);}?>;
	var _start_time = <?php if ($post['start_date'] == '') {echo('[]');} else {echo("['".substr($post['start_date'], 11, 5)."']");}?>;
	var _end_time = <?php if ($post['end_date'] == '') {echo('[]');} else {echo("['".substr($post['end_date'], 11, 5)."']");}?>;
	var _has_pms_linked_inquiryentry = <?php echo $has_pms_linked_inquiryentry ? 'true' : 'false'; ?>;
	var _member_mail_template_list = <?php echo json_encode($member_mail_template_list, JSON_UNESCAPED_UNICODE|JSON_HEX_APOS) ?>;
	var _user_mail_template_list = <?php echo json_encode($user_mail_template_list, JSON_UNESCAPED_UNICODE|JSON_HEX_APOS) ?>;
	const _inquiry_data = <?php echo json_encode($inquiry_data, JSON_UNESCAPED_UNICODE|JSON_HEX_APOS)?>;
	const _only_talkappipay = <?php echo $post['only_talkappipay']?>;
</script>

<?php echo $menu?>
<input type="hidden" name="act" id="act" value="" />
<input type="hidden" name="public_flg" value="1" />
<input type="hidden" name="inquiry_id" value="<?php echo $inquiry_id?>" />
<input type="hidden" name="cancel_policy" id="cancel_policy" value="" />
<input type="hidden" name="modify_policy" id="modify_policy" value="" />
<input type="hidden" name="remind" id="remind" value="" />
<input type="hidden" name="input_policy_opening" id="input_policy_opening" value="" />
<input type="hidden" id="param_login" value="<?php echo $json_next_inquiry_setting['param_login']?>" />
<input type="hidden" id="param_user_in_charge_required" value="<?php echo $json_next_inquiry_setting['user_in_charge_required']?>" />
<input type="hidden" id="default_tax_rate" value="<?php echo isset($post['default_tax_rate']) ? $post['default_tax_rate'] : '' ?>" />
<input type="hidden" name="inquiry_mail_signature" value="<?= $post['mail_signature'] ?>" />

<div class="content-container white border">
	<!-- 問合せフォーム設定　開始 -->
	<div class="section-container">
		<div class="setting-header"><?php echo __('admin.inquiry.label.inquiry_settings'); ?></div>
		<!-- コード -->
		<!--
		<div class="lines-container">
			<div class="basic-label"><?php echo __('admin.inquiry.label.inquiry_cd'); ?></div>
			<?php if ($_user->role_cd == '99') { ?>
				<input type="text" name="inquiry_cd" class="text-input-longer" style="width:200px;" value="<?php echo $post['inquiry_cd']?>" placeholder="<?php echo __('admin.inquiry.label.inquiry_cd_placeholder'); ?>">
			<?php } else {?>
				<input type="text" class="code-readonly" value="<?php echo $post['inquiry_cd']?>" placeholder="<?php echo __('admin.inquiry.label.inquiry_cd_placeholder'); ?>" readonly>
			<?php }?>
		</div>
		-->
		<!-- 問合せフォーム名 -->
		<div class="lines-container">
			<div class="basic-label"><?php echo __('admin.inquiry.label.inquiry_name'); ?></div>
			<input type="text" name="inquiry_name" value="<?php echo htmlspecialchars($post['inquiry_name'])?>" class="text-input-longer">
		</div>	
		<!-- 担当者 -->
		<div class="lines-container" <?php if ($json_next_inquiry_setting['user_in_charge_required'] != 1) echo('style="display:none;"')?>>
			<div class="basic-label"><?php echo __('admin.inquiry.label.user_in_charge'); ?></div>
			<div class="">
				<div 
					class="react-multi-select"
					data-items='<?php echo $user_list_for_react_select; ?>' 
					data-initial-selected-items='<?php echo $user_in_charge_list_for_react_select; ?>'
				></div>
				<input type="hidden" name="user_in_charge">
			</div>
		</div>
		<!-- 実施期間 -->
		<div class="lines-container">
			<div class="basic-label"><?php echo __('admin.inquiry.label.period'); ?></div>
			<div class="survey-lines-period-select flexbox-x-axis">
				<div class="survey-period-date-container">
					<span class="icon-calender"></span>
					<input name="start_date" id="start_date" value="<?php if($post['start_date'] != null) echo(substr($post['start_date'], 0, 10))?>" class="date-picker line-none" style="width: 100%;" size="16" data-date-format="yyyy-mm-dd" type="text" autocomplete="off" />
				</div>
					<input name="start_time" id="start_time" type="text" style="width:64px;height:28px;" class="form-control timepicker timepicker-24" value="<?php if($post['start_date'] != null) echo(substr($post['start_date'], 11, 5))?>" />			
				<span style="margin: 0 10px;">〜</span>
				<div class="survey-period-date-container">
					<span class="icon-calender"></span>
					<input name="end_date" id="end_date" value="<?php if($post['end_date'] != null) echo(substr($post['end_date'], 0, 10))?>" class="date-picker line-none" style="width: 100%;" size="16" data-date-format="yyyy-mm-dd" type="text" autocomplete="off" />
				</div>
					<input name="end_time" id="end_time" type="text" style="width:64px;height:28px;" class="form-control timepicker timepicker-24" value="<?php if($post['end_date'] != null) echo(substr($post['end_date'], 11, 5))?>" />
			</div>
		</div>
		<!-- サポート言語 -->
		<div class="lines-container">
			<div class="basic-label"><?php echo __('admin.inquiry.label.support_language'); ?></div>
			<div class="talkappi-checkbox js-support-language" data-name="support_lang_cd" data-value='<?php echo json_encode($post['support_lang_cd'])?>' data-source='<?php echo json_encode($_bot_lang, JSON_UNESCAPED_UNICODE|JSON_HEX_APOS) ?>'></div>
		</div>
		<!-- 表示言語 -->		
		<div class="lines-container">
			<div class="basic-label"><?php echo __('admin.inquiry.label.lang_display'); ?></div>
			<div class="talkappi-checkbox js-language" data-name="lang_display" data-value='<?php echo json_encode($post['lang_display'])?>' data-source='<?php echo json_encode($_bot_lang, JSON_UNESCAPED_UNICODE|JSON_HEX_APOS) ?>'></div>
		</div>
		<!-- 公開URL -->
		<div class="lines-container">
			<div class="basic-label"><?php echo __('admin.common.label.public_url'); ?></div>
			<div class="">
				<div class="talkappi-radio" data-name="public_url" data-value="0" data-source='{"0": "<?php echo __('admin.inquiry.label.url.standard'); ?>", "1": "<?php echo __('admin.inquiry.label.url.flow_path'); ?>" }'></div>
				<div class="js-simple-url">
					<p style="margin: 4px;"><?php echo __('admin.inquiry.label.url.standard.hint'); ?></p>
					<div class="public-url-area">
						<span class="public-url-link public-url-raw copy" @click="copyUri" data-clipboard-action="copy" data-clipboard-text="<?php echo htmlspecialchars($verify_url) ?>"><?php echo $verify_url ?></span>
						<span class="copy font-standard font-family-v1 font-color-v1 survey-copy-url" @click="copyUri" data-clipboard-action="copy" data-clipboard-text="<?php echo htmlspecialchars($verify_url) ?>"><?php echo __('admin.common.label.copy'); ?></span>
					</div>
				</div>
				<div class="js-parameter-url" style="display: none;">
					<p style="margin: 4px;"><?php echo __('admin.inquiry.label.url.flow_path.hint'); ?></p>
					<div style="border: 1px solid #E3E5E8; padding: 8px;">
						<p style="font-weight: bold; margin: 0 0 0 12px;"><?php echo __('admin.inquiry.label.url.flow_path'); ?></p>
						<?php foreach ($verify_url_inflow_type as $refer => $label) { ?>
							<div class="flexbox-x-axis" style="margin: 8px;">
								<div class="parameter-url-label"><?php echo $label; ?></div>
								<div class="public-url-area">
									<?php $verify_url_mail = $verify_url . '&refer=' . $refer; ?>
									<span class="public-url-link public-url-raw copy" @click="copyUri" data-clipboard-action="copy" data-clipboard-text="<?php echo htmlspecialchars($verify_url_mail) ?>"><?php echo $verify_url_mail ?></span>
									<span class="copy font-standard font-family-v1 font-color-v1 survey-copy-url" @click="copyUri" data-clipboard-action="copy" data-clipboard-text="<?php echo htmlspecialchars($verify_url_mail) ?>"><?php echo __('admin.common.label.copy'); ?></span>
								</div>
							</div>
						<?php }?>
					</div>
				</div>
			</div>
		</div>
	</div>

	<!-- 変更・キャンセル設定 開始-->
	<div class="section-container">
		<div class="flexbox setting-header-conteiner" style="justify-content: space-between;">
			<div class="setting-header"><?php echo __('admin.inquiry.label.cancel_modify_settings'); ?></div>
		</div>
		<div class="js-section-contents">
		<!-- キャンセル -->
		<div class="lines-container">
			<div class="basic-label"><?php echo __('admin.inquiry.label.cancel_inquiry'); ?></div>
			<div class="line-content">
				<div class="talkappi-radio" data-name="cancelable" data-value="<?php echo $post['inquiry_data']['cancelable'] ?>" data-source='{"0":"<?php echo __('admin.inquiry.label.cancel_no'); ?>", "1":"<?php echo __('admin.inquiry.label.cancel_yes'); ?>"}'></div>
				<div class="component-container js-cancelpolicy-group" style="margin-top:10px;width:600px;">
					<div style="margin:-10px 0 5px 5px;"><?php echo __('admin.inquiry.label.cancel_period'); ?></div>
					<div class="flexbox js-radio-group" style="flex-direction: column;">
						<div class="flexbox-x-axis js-span-setting <?php if ($post['inquiry_data']['cancel_policy'] === array()) echo 'active' ?>">
							<span class="icon-form-single-option-off survey-space-right-4 <?php if ($post['inquiry_data']['cancel_policy'] === array()) echo 'active' ?>" style="color: rgb(0, 0, 0);"></span>
							<span style="color: rgb(0, 0, 0);"><?php echo __('admin.inquiry.label.ok_whenever'); ?></span>
						</div>
						<div class="flexbox-x-axis survey-space-top-5 js-span-setting <?php if (array_key_exists('cancel_deadline_date', $post['inquiry_data']['cancel_policy'])) echo 'active' ?>">
							<span class="icon-form-single-option-off survey-space-right-4 <?php if (array_key_exists('cancel_deadline_date', $post['inquiry_data']['cancel_policy'])) echo 'active' ?>" style="color: rgb(0, 0, 0);"></span>
							<div class="survey-period-date-container background-white">
								<span class="icon-calender"></span><input data-prop="cancel_deadline_date" value="<?php if (array_key_exists('cancel_deadline_date', $post['inquiry_data']['cancel_policy'])) echo $post['inquiry_data']['cancel_policy']['cancel_deadline_date']?>" class="date-picker js-end-date line-none survey-space-right-4" size="16" data-date-format="yyyy-mm-dd" type="text" autocomplete="off" style="width: 100px;">
							</div>
							<input type="time" class="form-control" style="width:92px;" data-prop="cancel_deadline_time" value="<?php if (array_key_exists('cancel_deadline_time', $post['inquiry_data']['cancel_policy'])) { echo $post['inquiry_data']['cancel_policy']['cancel_deadline_time']; } else { echo "00:00"; }?>">
							<span style="color: rgb(61, 63, 69);"><?php echo __('admin.inquiry.label.limit_time'); ?></span>
						</div>
						<div class="flexbox-x-axis survey-space-top-5 js-span-setting <?php if (array_key_exists('before_day', $post['inquiry_data']['cancel_policy'])) echo 'active' ?>">
							<span class="icon-form-single-option-off survey-space-right-4 <?php if (array_key_exists('before_day', $post['inquiry_data']['cancel_policy'])) echo 'active' ?>" style="color: rgb(61, 63, 69);"></span>
							<span class=" survey-space-right-4" style="color: rgb(0, 0, 0);"><?php echo __('admin.common.label.usage_day'); ?></span>
							<input type="number" data-prop="before_day" class="survey-short-text-input survey-space-right-4" style="padding-right: 0px;" value="<?php if (array_key_exists('before_day', $post['inquiry_data']['cancel_policy'])) echo $post['inquiry_data']['cancel_policy']['before_day']?>">
							<span style="color: rgb(61, 63, 69);margin-right:10px;"><?php echo __('admin.inquiry.label.day_before'); ?></span>
							<input type="time" class="form-control" style="width:92px;" data-prop="limit_time" value="<?php if (array_key_exists('limit_time', $post['inquiry_data']['cancel_policy'])) { echo $post['inquiry_data']['cancel_policy']['limit_time']; } else { echo "00:00"; }?>">
							<div style="min-width: unset;width:fit-content !important; margin: 0 !important;" class="basic-label">
								<?php echo __('admin.inquiry.label.limit_time'); ?>
								<span class="icon-detail" style="margin: 0 0 0 0.5rem;" title="<?php echo __('admin.inquiry.label.usage_day_hint'); ?>"></span>
							</div>
						</div>
						<div class="flexbox-x-axis survey-space-top-5 js-span-setting <?php if (array_key_exists('before_hour', $post['inquiry_data']['cancel_policy'])) echo 'active' ?>">
							<span class="icon-form-single-option-off survey-space-right-4 <?php if (array_key_exists('before_hour', $post['inquiry_data']['cancel_policy'])) echo 'active' ?>" style="color: rgb(0, 0, 0);"></span>
							<span class=" survey-space-right-4"  style="color: rgb(0, 0, 0);"><?php echo __('admin.common.label.usage_time'); ?></span>
							<input type="number" data-prop="before_hour" class="survey-short-text-input survey-space-right-4" style="color: rgb(0, 0, 0);padding-right: 0px;" value="<?php if (array_key_exists('before_hour', $post['inquiry_data']['cancel_policy'])) echo $post['inquiry_data']['cancel_policy']['before_hour']?>">
							<div style="min-width: unset;width:fit-content !important; margin: 0 !important;" class="basic-label">
								<?php echo __('admin.inquiry.label.before_hour'); ?>
								<span class="icon-detail" style="margin: 0 0 0 0.5rem;" title="<?php echo __('admin.inquiry.label.usage_time_hint'); ?>"></span>
							</div>
						</div>
						<div class="flexbox-x-axis survey-space-top-5 js-span-setting js-rd-cancel-policy <?php if (array_key_exists('policy', $post['inquiry_data']['cancel_policy'])) echo 'active' ?>">
							<span class="icon-form-single-option-off survey-space-right-4 <?php if (array_key_exists('policy', $post['inquiry_data']['cancel_policy'])) echo 'active' ?>" style="color: rgb(0, 0, 0);"></span>
							<span style="color: rgb(0, 0, 0);"><?php echo __('admin.inquiry.label.cancel_policy'); ?></span>
						</div>
						<?php 
						$policy_style = '';
						if (!isset($post['inquiry_data']['cancel_policy']['policy'])) $policy_style = 'display:none;';
						?>
						<div class="talkappi-policy js-cancel-policy" style="margin-left:20px;padding-top:10px;<?php echo $policy_style ?>"></div>		
					</div>
				</div>
			</div>
		</div>
		<!-- 変更ポリシー -->
		<?php if (!isset($post['inquiry_data']['modifiable'])) {
				$post['inquiry_data']['modifiable'] = 0;
				$post['inquiry_data']['modify_policy'] = [];
			}
			else {
				if (!isset($post['inquiry_data']['modify_policy'])) $post['inquiry_data']['modify_policy'] = [];
			}
		?>
		<!-- 変更機能 -->
		<div class="lines-container">
			<div class="basic-label"><?php echo __('admin.inquiry.label.modify_inquiry'); ?></div>
			<div class="line-content">
				<div class="talkappi-radio" data-name="modifiable" data-value="<?php echo $post['inquiry_data']['modifiable'] ?>" data-source='{"0":"<?php echo __('admin.inquiry.label.cancel_no'); ?>", "1":"<?php echo __('admin.inquiry.label.cancel_yes'); ?>"}'></div>
				<div class="component-container js-modifypolicy-group" style="margin-top:10px;width:400px;">
					<div style="margin:-10px 0 5px 5px;"><?php echo __('admin.inquiry.label.modify_period'); ?></div>
					<div class="flexbox js-radio-group-modify" style="flex-direction: column;">
						<div class="flexbox-x-axis js-span-setting <?php if ($post['inquiry_data']['modify_policy'] === array()) echo 'active' ?>">
							<span class="icon-form-single-option-off survey-space-right-4 <?php if ($post['inquiry_data']['modify_policy'] === array()) echo 'active' ?>" style="color: rgb(0, 0, 0);"></span>
							<span style="color: rgb(0, 0, 0);"><?php echo __('admin.inquiry.label.ok_whenever'); ?></span>
						</div>
						<div class="flexbox-x-axis survey-space-top-5 js-span-setting <?php if (array_key_exists('deadline_date', $post['inquiry_data']['modify_policy'])) echo 'active' ?>">
							<span class="icon-form-single-option-off survey-space-right-4 <?php if (array_key_exists('deadline_date', $post['inquiry_data']['modify_policy'])) echo 'active' ?>" style="color: rgb(0, 0, 0);"></span>
							<div class="survey-period-date-container background-white">
								<span class="icon-calender"></span><input data-prop="deadline_date" value="<?php if (array_key_exists('deadline_date', $post['inquiry_data']['modify_policy'])) echo $post['inquiry_data']['modify_policy']['deadline_date']?>" class="date-picker js-end-date line-none survey-space-right-4" size="16" data-date-format="yyyy-mm-dd" type="text" autocomplete="off" style="width: 100px;">
							</div>
							<input type="time" class="survey-period-date-container" style="width:100px;padding:0 12px;" data-prop="deadline_time" value="<?php if (array_key_exists('deadline_time', $post['inquiry_data']['modify_policy'])) { echo $post['inquiry_data']['modify_policy']['deadline_time']; } else { echo "00:00"; }?>">
						</div>
						<div class="flexbox-x-axis survey-space-top-5 js-span-setting <?php if (array_key_exists('before_day', $post['inquiry_data']['modify_policy'])) echo 'active' ?>">
							<span class="icon-form-single-option-off survey-space-right-4 <?php if (array_key_exists('before_day', $post['inquiry_data']['modify_policy'])) echo 'active' ?>" style="color: rgb(61, 63, 69);"></span>
							<span class=" survey-space-right-4" style="color: rgb(0, 0, 0);"><?php echo __('admin.common.label.usage_day'); ?></span>
							<input type="number" data-prop="before_day" class="survey-short-text-input survey-space-right-4" style="padding-right: 0px;" value="<?php if (array_key_exists('before_day', $post['inquiry_data']['modify_policy'])) echo $post['inquiry_data']['modify_policy']['before_day']?>">
							<span style="color: rgb(61, 63, 69);margin-right:10px;"><?php echo __('admin.inquiry.label.day_before'); ?></span>
							<input type="time" class="survey-period-date-container" style="width:100px;padding:0 12px;" data-prop="limit_time" value="<?php if (array_key_exists('limit_time', $post['inquiry_data']['modify_policy'])) { echo $post['inquiry_data']['modify_policy']['limit_time']; } else { echo "00:00"; }?>">
							<div style="min-width: unset;width:fit-content !important; margin: 0 !important;" class="basic-label">
								<?php echo __('admin.inquiry.label.limit_time'); ?>
								<span class="icon-detail" style="margin: 0 0 0 0.5rem;" title="<?php echo __('admin.inquiry.label.usage_day_hint'); ?>"></span>
							</div>
						</div>

						<div class="flexbox-x-axis survey-space-top-5 js-span-setting <?php if (array_key_exists('before_hour', $post['inquiry_data']['modify_policy'])) echo 'active' ?>">
							<span class="icon-form-single-option-off survey-space-right-4 <?php if (array_key_exists('before_hour', $post['inquiry_data']['modify_policy'])) echo 'active' ?>" style="color: rgb(0, 0, 0);"></span>
							<span class=" survey-space-right-4"  style="color: rgb(0, 0, 0);"><?php echo __('admin.common.label.usage_time'); ?></span>
							<input type="number" data-prop="before_hour" class="survey-short-text-input survey-space-right-4" style="color: rgb(0, 0, 0);padding-right: 0px;" value="<?php if (array_key_exists('before_hour', $post['inquiry_data']['modify_policy'])) echo $post['inquiry_data']['modify_policy']['before_hour']?>">
							<div style="min-width: unset;width:fit-content !important; margin: 0 !important;" class="basic-label">
								<?php echo __('admin.inquiry.label.before_hour'); ?>
								<span class="icon-detail" style="margin: 0 0 0 0.5rem;" title="<?php echo __('admin.inquiry.label.usage_time_hint'); ?>"></span>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		</div>
	</div>

	<!-- 回答後メール設定 開始-->
	<div class="section-container">
		<div class="flexbox setting-header-conteiner" style="justify-content: space-between;">
			<div class="setting-header"><?php echo __('admin.inquiry.label.after_answer_settings'); ?></div>
		</div>
		<div class="js-section-contents">
			<!-- 送信者名 -->
			<div class="lines-container">
				<div class="basic-label">
					<?php echo __('admin.inquiry.label.member_mail_sender'); ?>
				</div>
				<div class="talkappi-radio" data-name="mail_sender" data-value='<?php echo $post['inquiry_data']['mail_sender'] ? $post['inquiry_data']['mail_sender']: 0?>' data-source='{"0":"<?php echo __('admin.inquiry.label.member_mail_sender_no'); ?>", "1":"<?php echo __('admin.inquiry.label.member_mail_sender_yes'); ?>"}'></div>
			</div>
			<!-- 送信者メールアドレス -->
			<div class="lines-container" style="align-items: center;">
				<div class="basic-label">
					<?php echo __('admin.inquiry.label.member_mail_from'); ?>
					<span class="icon-detail js-send-address" style="margin: 0 0 0 0.5rem;" title="<?php echo __('admin.inquiry.label.member_mail_form_hint'); ?>"></span>
					<span class="icon-success js-aws-varify" style="margin: 0 0 0 0.5rem;display: none;" title="Varified"></span>
				</div>
				<input class="text-input-longer js-mail-from" type="text" name="member_mail_from" value="<?php echo $post['member_mail_from']?>" placeholder="<?php echo __('admin.inquiry.label.member_mail_from_placeholder')?>" style="width: 400px;">
				<img src="./../assets/admin/css/img/status=Icon-limited.svg" class="js-aws-mail-error" style="margin: 0 0 0 8px;">
			</div>
			<div class="lines-container" style="margin-top:5px;min-height:unset;">
				<div class="basic-label"></div>
				<div  class="js-aws-mail-error" style="margin-left:5px;background: #FFE6D6;display: none;"><?php echo __('admin.inquiry.label.member_mail_from_error'); ?></div>
			</div>
			<!-- 返送先メールアドレス -->
			<div class="lines-container" style="align-items: center;">
				<div class="basic-label">
					<?php echo __('admin.inquiry.label.member_mail_replyto'); ?>
					<span class="icon-detail js-send-address" style="margin: 0 0 0 0.5rem;" title="<?php echo __('admin.inquiry.label.member_mail_replyto_hint'); ?>"></span>
				</div>
				<input class="text-input-longer" type="text" name="member_mail_replyto" value="<?php echo $post['member_mail_replyto']?>" placeholder="<?php echo __('admin.inquiry.label.member_mail_replyto_placeholder')?>" style="width: 400px;">
			</div>
			<!-- ユーザーへ送信 -->
			<div class="lines-container" style="display:flex;">
				<div class="basic-label">
					<?php echo __('admin.inquiry.label.member_mail_template'); ?>
					<span class="icon-detail" style="margin: 0 0 0 0.5rem;" title="<?php echo htmlspecialchars(__('survey.inquiry.common.info.member_mail_template')) ?>"></span>
				</div>
				<div class="talkappi-pulldown" data-name="member_mail_template" data-value="<?php echo $post['member_mail_template']?>" data-blank="1" data-blank-text="<?php echo __('admininquiry.baseinfo.not_send_mail') ?>" style="width: 400px;" data-source='<?php echo json_encode($member_mail_template_list, JSON_UNESCAPED_UNICODE|JSON_HEX_APOS) ?>'></div>
				<p style="margin-left:10px;" class="js-edit edit_link"><?php echo __('admin.common.button.edit'); ?></p>
			</div>
			<?php if($inquiry_div != 9){ ?>
				<!-- ユーザーリマインド -->
				<?php if (!isset($post['inquiry_data']['remind_flg'])) {
						$post['inquiry_data']['remind_flg'] = 0;
						$post['inquiry_data']['remind'] = [];
					}
					else {
						if (!isset($post['inquiry_data']['remind'])) $post['inquiry_data']['remind'] = [];
					}
				?>
				<div class="lines-container">
					<div class="basic-label">
						<?php echo __('admin.inquiry.label.member_mail_remind'); ?>
					</div>
					<div class="talkappi-radio" data-name="remind_flg" data-value='<?php echo $post['inquiry_data']['remind_flg'] ? $post['inquiry_data']['remind_flg']: 0?>' data-source='{"0":"<?php echo __('admin.inquiry.label.cancel_no'); ?>", "1":"<?php echo __('admin.inquiry.label.cancel_yes'); ?>"}'></div>
				</div>
				<div class="js-remind-group">
					<?php 
					$i = 1;
					$label_title = ['admin.common.label.first_time', 'admin.common.label.second_time'];
					if (!isset($post['inquiry_data']['remind']) || count($post['inquiry_data']['remind']) == 0) {
						$post['inquiry_data']['remind'] = [['remind_day'=>'', 'remind_time'=>'10:00'], ['remind_day'=>'', 'remind_time'=>'10:00']];
					}
					foreach($post['inquiry_data']['remind'] as $r) {
					?>
					<div class="lines-container" style="display:flex;">
						<div class="basic-label">
						</div>
						<span class="survey-space-right-5" style="color: rgb(0, 0, 0); font-weight: bold;"><?php echo __($label_title[$i-1]); ?></span>
						<span class="survey-space-right-4" style="color: rgb(0, 0, 0);"><?php echo __('admin.common.label.usage_day'); ?></span>
						<input type="number" data-prop="remind1_day" name="remind<?php echo $i ?>_day" min="0" max="365"  class="survey-short-text-input survey-space-right-4 js-remind-day" style="padding-right: 0px;" value="<?php  echo $r['remind_day']?>">
						<span style="color: rgb(61, 63, 69);margin-right:10px;"><?php echo __('admin.inquiry.label.day_before'); ?></span>
						<input type="time" class="survey-period-date-container js-remind-time" style="width:100px;padding:0 12px;" data-prop="remind<?php echo $i ?>_time" value="<?php echo $r['remind_time'];?>">
					</div>
					<?php 
					$i++;
					} ?>
				</div>
				<!-- サンクスメール -->
				<div class="lines-container" style="display:flex;">
					<div class="basic-label">
						<?php echo __('admin.inquiry.label.member_mail_template_thanks'); ?>
					</div>
					<div class="talkappi-pulldown" data-name="member_mail_template_thanks" data-value="<?php echo $post['inquiry_data']['thanks']['template']?>" data-blank="1" data-blank-text="<?php echo __('admininquiry.baseinfo.not_send_mail') ?>" style="width: 400px;" data-source='<?php echo json_encode($member_mail_template_list, JSON_UNESCAPED_UNICODE|JSON_HEX_APOS) ?>'></div>
					<p style="margin-left:10px;" class="js-edit edit_link"><?php echo __('admin.common.button.edit'); ?></p>
				</div>
				<div class="lines-container" style="display:flex;">
					<div class="basic-label">
					</div>
					<span class="survey-space-right-4" style="color: rgb(0, 0, 0);"><?php echo __('admin.inquiry.label.thanks_mail_text_1'); ?></span>
					<input type="number" name="thanks_mail_day" min="0" max="365"  class="survey-short-text-input survey-space-right-4" style="padding-right: 0px;" value="<?php  echo $post['inquiry_data']['thanks']['day']?>">
					<span style="color: rgb(61, 63, 69);margin-right:10px;"><?php echo __('admin.inquiry.label.day_after'); ?></span>
					<input type="time" class="survey-period-date-container" style="width:100px;padding:0 12px;" name="thanks_mail_time" value="<?php echo $post['inquiry_data']['thanks']['time'];?>">
					<span class="survey-space-right-4" style="color: rgb(0, 0, 0);"><?php echo __('admin.inquiry.label.thanks_mail_text_2'); ?></span>
				</div>
			<?php } ?>
			<!-- 管理者へ送信 -->
			<div class="lines-container" style="display:flex;">
				<div class="basic-label"><?php echo __('admin.inquiry.label.user_mail_template'); ?></div>
				<div class="talkappi-pulldown" data-name="user_mail_template" data-value="<?php echo $post['user_mail_template']?>" data-blank="1" data-blank-text="<?php echo __('admininquiry.baseinfo.not_send_mail') ?>" style="width: 400px;" data-source='<?php echo json_encode($user_mail_template_list, JSON_UNESCAPED_UNICODE|JSON_HEX_APOS) ?>'></div>
				<p style="margin-left:10px;" class="js-edit edit_link"><?php echo __('admin.common.button.edit'); ?></p>
			</div>
			<div class="lines-container js-select-user-container" style="display:none;">
				<div class="basic-label"></div>			
				<div class="js-add-user-container" style="display:flex;">
					<ul class="btn round light-blue pointer js-selected-users" style="list-style:none;display:none;"></ul>
					<div class="btn round light-blue pointer"><img src="./../assets/admin/css/img/icon-add.svg"><?php echo __('admin.inquiry.label.mail_users'); ?></div>
				</div>
				<input type="hidden" name="mail_users" value="<?php echo $post['mail_users']?>">
			</div>
		</div>
	</div>

	<?php if($inquiry_div != 9){ ?>
		<!-- 支払い設定 -->
		<div class="section-container js-section-container hide-section">
			<div id="receipt_setting" class="flexbox setting-header-conteiner js-fold" style="justify-content: space-between;cursor:pointer;">
				<div class="flexbox-x-axis">
				<div class="setting-header" style="width: 200px;"><?php echo __('admin.inquiry.label.payment_settings'); ?><span style="color: #A1A4AA; margin: 0 0 0 4px;"><?php echo __('inquiry.input.spl.address.roomNo')?></span></div>
					<div class="js-with-settings flexbox" style="margin: 0 0 0 12px !important; gap: 8px;flex-wrap: wrap;max-width: 600px;"></div>
				</div>
				<div class="js-fold flexbox-x-axis" style="width: 80px;">
					<div class="js-fold-text" style="margin: 24px 0 24px 0;"><?php echo __('inquiry.label.expand')?></div>
					<span class="icon-fold-open"></span>
				</div>
			</div>
			<div class="js-section-contents" style="display: none;">
				<!-- ２段階支払い -->
				<div class="lines-container" <?php echo $_role_99_only ?>>
					<div class="basic-label"><?php echo __('admin.inquiry.label.payment_2_step'); ?></div>
					<div class="talkappi-radio" data-name="2_step" data-value='<?php echo $post['inquiry_data']['payment']['2_step']?>' data-source='{"0":"<?php echo __('admin.inquiry.label.answer_limit_no'); ?>", "1":"<?php echo __('admin.inquiry.label.answer_limit_yes'); ?>"}'></div>
				</div>
				<div class="lines-container js-2-step-limit">
						<div class="basic-label"><?php echo __('admin.inquiry.label.payment_2_step.pay_in_hours'); ?></div>
						<input class="text-input" style="margin-right: 5px" name="pay_in_hours" value="<?php echo $post['inquiry_data']['payment']['pay_in_hours'] ?>" >
					</div>
				<?php if ($post['show_receipt_setting'] == 1) {?>
				<!-- 領収書設定 -->
				<div class="lines-container">
					<div class="basic-label"><?php echo __('admin.inquiry.label.receipt_setting'); ?></div>
					<div class="talkappi-radio" data-name="receipt_setting" data-value='<?php echo isset($post['receipt_setting']) ? $post['receipt_setting']: 0 ?>' data-source='{"0":"<?php echo __('admin.inquiry.label.receipt_setting_no'); ?>", "1":"<?php echo __('admin.inquiry.label.receipt_setting_yes'); ?>"}'></div>
				</div>
				<!-- 税率設定 -->
				<div class="lines-container js-tax-rate">
					<div class="basic-label"><?php echo __('admin.inquiry.label.tax_rate'); ?></div>
					<input class="text-input" style="margin-right: 5px" name="tax_rate" value="<?php echo isset($post['inquiry_data']['receipt_setting']['tax_rate']) ? $post['inquiry_data']['receipt_setting']['tax_rate'] : "" ?>" > %
				</div>
				<?php }?>
				<!-- 支払いSandbox -->
				<div class="lines-container">
					<div class="basic-label"><?php echo __('admin.inquiry.label.payment_sandbox'); ?></div>
					<div class="talkappi-radio" data-name="sandbox" data-value='<?php echo $post['inquiry_data']['payment']['sandbox']?>' data-source='{"0":"<?php echo __('admin.inquiry.label.answer_limit_no'); ?>", "1":"<?php echo __('admin.inquiry.label.answer_limit_yes'); ?>"}'></div>
				</div>
			</div>
		</div>
	<?php } ?>

	<!-- 表示・スタイル設定 -->
	<div class="section-container js-section-container hide-section">
		<div id="appearance_settings" class="flexbox setting-header-conteiner js-fold" style="justify-content: space-between;cursor:pointer;">
			<div class="flexbox-x-axis">
			<div class="setting-header" style="width: 200px;"><?php echo __('admin.inquiry.label.appearance_settings'); ?><span style="color: #A1A4AA; margin: 0 0 0 4px;"><?php echo __('inquiry.input.spl.address.roomNo')?></span></div>
				<div class="js-with-settings flexbox" style="margin: 0 0 0 12px !important; gap: 8px;flex-wrap: wrap;max-width: 600px;"></div>
			</div>
			<div class="js-fold flexbox-x-axis" style="width: 80px;">
				<div class="js-fold-text" style="margin: 24px 0 24px 0;"><?php echo __('inquiry.label.expand')?></div>
				<span class="icon-fold-open"></span>
			</div>
		</div>
		<div class="js-section-contents" style="display: none;">
			<div class="lines-container">
				<div class="basic-label"><?php echo __('admin.inquiry.label.scene_cd'); ?></div>
				<div class="talkappi-pulldown js-scene-cd" data-name="scene_cd" data-value="<?php echo $post['scene_cd']?>" data-size="longer" data-source='<?php echo json_encode($scene_list, JSON_UNESCAPED_UNICODE|JSON_HEX_APOS) ?>'></div>
				<div style="margin-left:10px;" class="talkappi-pulldown js-template-cd" data-name="template_cd" data-value="<?php echo $post['template_cd']?>" data-size="longer" data-source='<?php echo json_encode($template_list, JSON_UNESCAPED_UNICODE|JSON_HEX_APOS) ?>'></div>
				<p style="margin-left:10px;" class="js-scene-template edit_link"><?php echo __('admin.common.button.edit'); ?></p>
			</div>
		</div>
	</div>

	<!-- 公開設定 -->
	<div class="section-container js-section-container hide-section">
		<div id="public_setting" class="flexbox setting-header-conteiner js-fold" style="justify-content: space-between;cursor:pointer;">
			<div class="flexbox-x-axis">
				<div class="setting-header" style="width: 200px;"><?php echo __('admin.common.label.public_setting'); ?><span style="color: #A1A4AA; margin: 0 0 0 4px;"><?php echo __('inquiry.input.spl.address.roomNo')?></span></div>
				<div class="js-with-settings flexbox" style="margin: 0 0 0 12px !important; gap: 8px;flex-wrap: wrap;max-width: 600px;"></div>
			</div>
			<div class="js-fold flexbox-x-axis" style="width: 80px;">
				<div class="js-fold-text" style="margin: 24px 0 24px 0;"><?php echo __('inquiry.label.expand')?></div>
				<span class="icon-fold-open"></span>
			</div>
		</div>
		<div class="js-section-contents" style="display: none;">
			<!-- リダイレクト先 -->
			<div class="lines-container">
				<div class="basic-label">
					<?php echo __('admin.common.label.redirect_to'); ?>
					<span class="icon-detail" style="margin: 0 0 0 0.5rem;" title="<?php echo __('admin.common.label.redirect_to_hint'); ?>"></span>
				</div>
				<input type="text" name="redirect_url" value="<?php echo $post['redirect_url'] ?>" class="text-input-longer">
			</div>
			<!-- 限定公開・トグル -->
			<div class="lines-container">
				<div class="basic-label"><?php echo __('inquiry.login.title'); ?></div>
				<div class="talkappi-radio" data-name="secret_mode" data-value='<?php echo $post['secret_mode']?>' data-source='{"0":"<?php echo __('admin.inquiry.label.answer_limit_no'); ?>", "1":"<?php echo __('admin.inquiry.label.answer_limit_yes'); ?>"}'></div>
			</div>
			<!-- 限定公開・パスワード -->
			<div class="lines-container js-password">
				<div class="basic-label"></div>
				<input class="text-input-longer" name="password" value="<?php echo $post['password']?>" placeholder="<?php echo __('inquiry.login.description'); ?>">				
			</div>
			<!-- 公開時間制限・トグル -->
			<div class="lines-container">
				<div class="basic-label">
					<?php echo __('admin.common.label.limit_acceptance_hours'); ?>
					<span class="icon-detail" style="margin: 0 0 0 0.5rem;" title="<?php echo __('admin.common.label.limit_acceptance_hours_hint'); ?>"></span>
				</div>
				<div class="talkappi-radio" name="input_policy" data-name="input_policy" data-value='<?php if (isset($post['inquiry_data']['input_policy']) && isset($post['inquiry_data']['input_policy']['opening_time'])) { echo "1"; } else {  echo "0"; } ?>' data-source='{"0":"<?php echo __('admin.inquiry.label.answer_limit_no'); ?>", "1":"<?php echo __('admin.inquiry.label.answer_limit_yes'); ?>"}'></div>
			</div>
			<!-- 公開時間制限・時間入力 -->
			<div class="lines-container js-input-policy-opening">
				<div class="basic-label"></div>
				<input class="text-input-longer" data-name="input_policy_opening" value="<?php if (isset($post['inquiry_data']['input_policy']) && isset($post['inquiry_data']['input_policy']['opening_time'])) echo(implode(",",$post['inquiry_data']['input_policy']['opening_time']))?>" placeholder="22:00-07:00, 12:00-13:00">				
			</div>
		</div>
	</div>

	<!-- その他の設定 -->
	<div class="section-container js-section-container hide-section">
		<div id="other_settings" class="flexbox setting-header-conteiner js-fold" style="justify-content: space-between;cursor:pointer;">
			<div class="flexbox-x-axis">
				<div class="setting-header" style="width: 200px;"><?php echo __('admin.inquiry.label.advanced_settings'); ?><span style="color: #A1A4AA; margin: 0 0 0 4px;"><?php echo __('inquiry.input.spl.address.roomNo')?></span></div>
				<div class="js-with-settings flexbox" style="margin: 0 0 0 12px !important; gap: 8px;flex-wrap: wrap;max-width: 600px;"></div>
			</div>
			<div class="js-fold flexbox-x-axis" style="width: 80px;">
				<div class="js-fold-text" style="margin: 24px 0 24px 0;"><?php echo __('inquiry.label.expand')?></div>
				<span class="icon-fold-open"></span>
			</div>
		</div>
		<div class="js-section-contents" style="display: none;">
			<!-- 機能分類 -->
			<div class="lines-container category-container">
				<div class="basic-label"><?php echo __('admin.inquiry.label.class_cd'); ?></div>
				<div class="talkappi-category-select" data-name="class_cd" data-div='<?php echo $code_div ?>' data-value='<?php if ($post['class_cd'] == '') {echo('[]');} else {echo(json_encode(explode(' ', $post['class_cd'])));}?>'></div>
			</div>
			<!-- Base Ver ID設定 -->
			<div class="lines-container">
				<div class="basic-label">
				<?php echo __('admin.inquiry.label.base_ver_id'); ?>
					<span class="icon-detail" style="margin: 0 0 0 0.5rem;" title="<?php echo __('admin.inquiry.label.base_ver_id_hint'); ?>"></span>
				</div>
				<input type="text" name="base_ver_id" value="<?php echo $post['base_ver_id']; ?>" class="text-input-longer" style="width: 200px;">
			</div>
			<!-- 項目ラベル -->
			<?php if (count($labels) > 0) {?>
				<div class="lines-container">
					<div class="basic-label"><?php echo __('admin.inquiry.label.entry_label'); ?></div>
					<div class="talkappi-pulldown" data-name="label_id" data-blank="1" data-value="<?php echo $post['label_id']?>" data-size="large" data-source='<?php echo json_encode($labels, JSON_UNESCAPED_UNICODE|JSON_HEX_APOS) ?>'></div>
					<a href="/admin/labels" style="margin-left:10px;" class="edit_link"><?php echo __('admin.common.button.edit'); ?></a>
				</div>
			<?php }?>
			<!-- FAQデータ -->
			<?php if ($_bot_setting['flg_faq_site'] === '1') {?>
				<div class="lines-container">
					<div class="basic-label"><?php echo __('admin.inquiry.label.faq_scene'); ?>
					<span class="icon-detail" style="margin: 0 0 0 0.5rem;" title="<?php echo __('admin.inquiry.label.faq_scene_hit'); ?>"></span>
					</div>
					<div class="talkappi-pulldown" data-name="faq_scene" data-blank="1" data-blank-text="<?php echo __('admin.inquiry.label.faq_blank_text'); ?>" data-value="<?php echo $post['faq_scene']?>" data-size="large" data-source='<?php echo json_encode($faq_scenes, JSON_UNESCAPED_UNICODE|JSON_HEX_APOS) ?>'></div>
				</div>
			<?php }?>
			<!-- GTM設定 -->
			<div class="lines-container">
				<div class="basic-label">
					<?php echo __('admin.inquiry.label.gtm_tag_setting'); ?>
					<span class="icon-detail" style="margin: 0 0 0 0.5rem;" title="<?php echo __('admin.inquiry.label.gtm_detail') ?>"></span>
				</div>
				<input type="text" name="gtm_tag_id" value="<?php if (array_key_exists('gtm_tag_id', $post['inquiry_data'])) echo $post['inquiry_data']['gtm_tag_id']; ?>" placeholder="GTM-XXXXXX" class="text-input-longer" style="width: 200px;">
			</div>
			<!-- 受付ID設定 -->
			<div class="lines-container">
				<div class="basic-label">
					<?php echo __('admin.inquiry.label.reception_id_prefix'); ?>
					<span class="icon-detail" style="margin: 0 0 0 0.5rem;" title="<?php echo __('admin.inquiry.label.reception_id_prefix_hint'); ?>"></span>
				</div>
				<input type="text" name="receiption_id_prefix" value="<?php if (array_key_exists('receiption_id_prefix', $post['inquiry_data'])) echo $post['inquiry_data']['receiption_id_prefix']; ?>" class="text-input-longer" style="width: 200px;">
				<div class="checkbox-label js-random-receiption-id" style="margin-left:12px;">
					<input type="checkbox" id="random-receiption-id" value="1">
					<label for="random-receiption-id"><?php echo __('admin.inquiry.label.receiption_id_random') ?></label>
				</div>
			</div>
			<?php if($inquiry_div != 9){ ?>
				<!-- 回答回数設定/計 -->
				<div class="lines-container">
					<div class="basic-label"><?php echo __('admin.inquiry.label.inquiry_answer_limit'); ?></div>
					<input class="text-input" name="inquiry_answer_limit" value="<?php echo($post['inquiry_answer_limit'])?>" type="number">
					<div style="margin-left:10px;"><?php echo __('admin.inquiry.label.inquiry_answer_limit_hint'); ?></div>
				</div>
			<?php } ?>
			<!-- 回答回数設定/人 -->
			<div class="lines-container">
				<div class="basic-label"><?php echo __('admin.inquiry.label.answer_limit'); ?></div>
				<div class="talkappi-radio" data-name="answer_limit" data-value='<?php echo $post['answer_limit']?>' data-source='{"0":"<?php echo __('admin.inquiry.label.answer_limit_no'); ?>", "1":"<?php echo __('admin.inquiry.label.answer_limit_yes'); ?>"}'></div>
			</div>
			<!-- 通知バッジ表示 対応ステータス管理 -->
			<div class="lines-container">
				<div class="basic-label"><?php echo __('admin.inquiry.label.support_flg'); ?></div>
				<div class="line-content">
					<div class="talkappi-radio" data-name="support_flg" data-value="<?php echo $post['support_flg']?>" data-source='{"0":"<?php echo __('admin.inquiry.label.support_flg_no'); ?>", "1":"<?php echo __('admin.inquiry.label.support_flg_yes'); ?>"}'></div>
				</div>
			</div>
			<?php if($inquiry_div != 9){ ?>
				<!-- 合計金額カート表示 -->
				<div class="lines-container">
					<div class="basic-label"><?php echo __('admin.inquiry.label.use_cart'); ?></div>
					<div class="line-content">
						<div class="talkappi-radio" data-name="use_cart" data-value="<?php echo $post['use_cart']?>" data-source='{"0":"<?php echo __('admin.inquiry.label.support_flg_no'); ?>", "1":"<?php echo __('admin.inquiry.label.support_flg_yes'); ?>"}'></div>
					</div>
				</div>
			<?php } ?>
			<!-- 申込完了のメッセージ定義 -->
			<div class="lines-container">
				<div class="basic-label">
					<?php echo __('admin.inquiry.label.request_completion_notification_message'); ?>
					<span class="icon-detail" style="margin: 0 0 0 0.5rem;" title="<?php echo __('admin.inquiry.label.recaius_explanation'); ?>"></span>
				</div>
				<div style="width: 40%;">
					<textarea name="notification_message" class="form-control" placeholder="<?php echo __('admin.inquiry.label.notification_message_default') ?>"><?php
					if (isset($post['inquiry_data']['message']['short'])) {
						$message = $post['inquiry_data']['message']['short'];
						echo htmlspecialchars($message, ENT_QUOTES);
					}
					?></textarea>
				</div>
			</div>
			<?php if($inquiry_div != 9){ ?>
				<!-- PMS連携 -->
				<div class="lines-container">
					<div class="basic-label">
						<?php echo __('admin.inquiry.label.pms_integration'); ?>
						<span class="icon-detail" style="margin: 0 0 0 0.5rem;" title="<?php echo __('admin.inquiry.label.pms_integration_hint'); ?>"></span>
					</div>
					<div class="line-content">
						<div class="talkappi-radio" data-name="assist" data-value="<?php echo $post['assist']?>" data-source='{"0":"<?php echo __('admin.inquiry.label.support_flg_no'); ?>", "1":"<?php echo __('admin.inquiry.label.support_flg_yes'); ?>"}'></div>
					</div>
				</div>
			<?php } ?>
			<!-- メール記録 -->
			<?php if ($_user->role_cd == '99') { ?>
				<div class="lines-container">
					<div class="basic-label">
						<?php echo __('admin.inquiry.label.is_mail_logged'); ?>
					</div>
					<div class="line-content">
						<div class="talkappi-radio" data-name="is_mail_logged" data-value="<?php echo $post['is_mail_logged']?>" data-source='{"0":"<?php echo __('admin.inquiry.label.support_flg_no'); ?>", "1":"<?php echo __('admin.inquiry.label.support_flg_yes'); ?>"}'></div>
					</div>
				</div>
			<?php } ?>
			<!-- 署名設定 -->
			<div class="lines-container">
				<div class="basic-label">
					<?= __('admin.send.label.signature') ?>
				</div>
				<div class="line-content">
					<div class="talkappi-radio need_signature" data-name="need_signature" data-value="<?= $post['mail_signature'] ? '1' : '0' ?>" data-source='{"0":"OFF", "1":"ON"}'></div>
					<div class="js-mail-signature-setting" style="margin-top:10px;display:<?= $post['mail_signature'] ? 'block' : 'none' ?>">
						<div class="flexbox-x-axis" style="gap:10px">
							<div style="width:200px" class="talkappi-pulldown js-mail-signature" data-name="mail_signature" data-value="<?= $post['mail_signature'] ?? '0' ?>" data-source="<?= htmlspecialchars($mail_signature_options) ?>"></div>
							<span class="js-edit-signature pointer" style="color:#245BD6;"><?= __('admin.send.label.signature_edit') ?></span>
						</div>
						<div class="flexbox-x-axis" style="gap:10px">
							<span class="js-edit-signature-user pointer" style="color:black;">自分に割当署名(設定すればこちら優先)：</span>
							<a class="js-mail-signature-user" href="<?php echo $self_assigned_signature ? '/admin/signature?id=' . $self_assigned_signature['id'] : '/admin/signatures' ?>" target="_blank"><?php echo $self_assigned_signature ? $self_assigned_signature['sign_title'] : '未設定' ?></a>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>

	<!-- ボタン -->
	<div class="submit-btn-container" style="margin: 60px 0 0 134px;">
		<div class="btn-larger btn-blue js-action-save"><?php echo __('admin.common.button.save'); ?></div>
		<div class="btn-larger btn-gray-black">
			<!-- survey/?f=ozmall&survey_id=777001&surveyonly=1 -->
			<a class="flexbox-center height-100 width-100" href="<?php echo $verify_url ?>" target="_blank" style="color: #000;" onfocus="this.blur();"><?php echo __('admin.common.button.verify'); ?></a>
		</div>
		<div class="btn-larger btn-white js-block-ui js-action-back"><?php echo __('admin.common.button.return_to_list'); ?></div>
		<?php if($post['inquiry_cd'] !== "") {?>
			<div class="btn-larger btn-red-border js-action-delete">
				<span class="icon-delete"></span>
			</div>
		<?php }?>
	</div>
</div>

<!-- 複数選択コンポーネント -->
<script src="/assets/common/react/components/atoms/multiselect.bundle.js"></script>