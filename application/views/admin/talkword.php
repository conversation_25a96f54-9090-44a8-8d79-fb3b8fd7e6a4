			<!-- BEGIN PAGE HEADER-->
			<!-- BEGIN PAGE HEAD -->
			<div class="page-head">
				<!-- BEGIN PAGE TITLE -->
				<div class="page-title">
					<h1>FAQ編集<small style="margin-left: 20px;"></small></h1>
				</div>
				<!-- <PERSON>ND PAGE TITLE -->
			</div>
			<!-- END PAGE HEAD -->
			<!-- END PAGE HEADER-->
			<!-- BEGIN PAGE CONTENT-->
			<div class="row">
				<div class="col-md-12">
			        <!-- Page Content -->
			        <div id="page-wrapper">
			        <div class="portlet light">
			        
			              <div class="tabbable-line">
					        <ul class="nav nav-tabs ">
					        	<?php foreach($_bot_lang as $lang) {
					        		$style='';
					        		if ($lang == $lang_cd) $style='active';
					        		echo('<li class="' . $style . '"><a href="/admin/talk?cd=' . $intent_cd . '&lang=' . $lang . '&sub_cd=' . $sub_intent_cd . '">');
					        		echo($_bot_lang[$lang]);
					        		echo('</a></li>');
					        	}
					        	?>
					       		<li class="">
									<a href="/admin/talkword">
									キーボード※</a>
								</li>					        	
					        	<li class="">
									<a href="/admin/talks">
									一覧に戻る</a>
								</li>
					        </ul>
					      </div>
			        
						<div class="row">
							<div class="col-md-12">
									<input type="hidden" name="cd" value="<?php echo($intent_cd)?>" />
									<input type="hidden" name="sub_cd" value="<?php echo($sub_intent_cd)?>" />			
									<input type="hidden" name="lang" value="<?php echo($lang_cd)?>" />						
									<div class="form-body">
									</div>
									<div class="col-md-offset-4 col-md-8">
										<button type="submit" class="btn blue mr10" style="margin-left: 20px;">
										<i class="fa fa-save mr10"></i>保存</button>
										<button onclick="top.location='/admin/talks'" type="button" class="btn default">戻る</button>
									</div>
							</div>
						</div>							
			        </div>
			        </div>
			        <!-- /#page-wrapper -->				
				</div>
			</div>
			<!-- END PAGE CONTENT-->
