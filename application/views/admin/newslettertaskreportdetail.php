<?php echo HTML::style('/assets/admin/css/newsletter.css'); ?>

<style>
    
</style>

<!--  rectangle_body -->
<div class="content-container white row_sparse">
    <div class="report_header_container">
        <div class="row_item">
            <div class="txt_label_small"><?php echo __('admin.send.task.label.name') ?></div>
            <div class="txt_value title">
                <?php 
                    $task_name = $mail_task->mail_task_name;
                    if ($task_name == NULL || $task_name == '') {
                        $task_name = '[件名]'.$mail_task->title;
                    }
                    echo $task_name; 
                ?>
            </div>
        </div>
        <div id="email_object" class="row_item multi">
            <div class="txt_label_small"><?php echo __('admin.send.task.label.object') ?></div>
            <div class="column_item">
                <div class="txt_value"><?php echo str_replace( "{target_count}", ' <b class="selected_count">'.$task_member_count.'</b> ', __('admin.send.task.count.context') ) ?></div>
                <div class="txt_value">対象施設：
                    <?php 
                        $bot_member_count_text = '';
                        if ( is_array($task_bots) && count($task_bots) > 0 ) {
                            
                            foreach ( $task_bots as $bot_id=>$bot_info ) {
                                $bot_text = $bot_info['name'].'('. $bot_info['count'].')';
                                if ( $bot_member_count_text != '' ) {
                                    $bot_text = '; '.$bot_text;
                                }
                                $bot_member_count_text .= $bot_text;
                            }
                        }
                        echo $bot_member_count_text;
                    ?>
                </div>
                <div class="txt_value">タグ：
                    <?php 
                        $tag_member_count_text = '';
                        if ( is_array($tag_member_counts) && count($tag_member_counts) > 0 ) {
                            
                            foreach ( $tag_member_counts as $tag_no=>$tag_info ) {
                                $tag_text = $tag_info['name'].'('. $tag_info['count'].')';
                                if ( $tag_member_count_text != '' ) {
                                    $tag_text = '; '.$tag_text;
                                }
                                $tag_member_count_text .= $tag_text;
                            }
                        }
                        echo $tag_member_count_text;
                    ?>
                </div>
            </div>
        </div>
        <div class="row_item">
            <div class="txt_label_small"><?php echo __('admin.send.label.date')?></div>
            <div class="txt_value"><?php echo $mail_task->upd_time ?></div>
        </div>
    </div>

    <div class="button_container">
        <!-- TODO: 未実装の機能は非表示にする: メールを見る -->
        <!-- <div class="btn_w_ico pointer pale_grey"  id="show_mail">
            <span class="icon-email-blue"></span>
            <div class="txt_btn_w_ico"><?php echo __('admin.send.report.button.read_email') ?></div>
        </div> -->
        <div class="btn_w_ico pointer pale_grey" style="width: 112px;display:none">
            <span class="icon-export"></span>
            <div class="txt_btn_w_ico"><?php echo __('admin.send.addr.button.export') ?></div>
        </div>
    </div>

</div>


<div class="content-container white">

    <!-- table_wrapper_frame_1310 -->
    <table class=" table table-striped table-bordered table-hover js-data-table0 ">
        <!-- style="top: 50px;" -->
        <div class="table-header-pulldown custom-data-table-header" style="align-items: end;">
                <?php echo Form::select('status', $filter_set, $filter, array('id'=>'filter','class'=>'form-control report_filter',
                    'style' => 'background: #FFFFFF;width: 192px;'))
                ?>
        </div>

        <thead>
            <!-- grey_row_diff -->
            <!-- row_table_head_body_frame_1314  width: 100%; -->
            <tr>
                <!--  cell_head_order_diff cell_head_body_frame_216 -->
                <th style="width: 284px;"><?php echo __('admin.common.label.mail.address') ?></th>
                <?php if($counting) : ?>
                <th style="width: 120px;"><?php echo $event_count_headers[$filter] ?></th>
                <?php endif; ?>
                <?php if($is_very_bot) : ?>
                <th style="width: 180px;"><?php echo __('admin.newsletter.label.admin_user_name') ?></th>
                <th style="width: 180px;"><?php echo __('admin.newsletter.label.admin_bot_name') ?></th>
                <?php endif; ?>
                <th style="width: 180px;"><?php echo __('admin.common.label.name.human') ?></th>
                <th style="width: 278px;"><?php echo __('admin.send.addr.th.tag') ?></th>

                <th style="width: 200px;"><?php echo __('admin.common.label.last_update')?></th>
                <?php if($operation) : ?>
                <th class="operation" style="width: 200px;"><?php echo __('admin.newsletter.label.resolve_suppression')?></th>
                <?php endif; ?>
            </tr>																
        </thead>

        <tbody>

            <?php
                foreach($details as $report_detail) {
                    
                    $tags_html = '';// TODO
                    if ( isset($member_tag_mapping[$report_detail['member_id']]) && count($member_tag_mapping[$report_detail['member_id']]) > 0 ) {
                        // $tags_html = '';
                        foreach ( $member_tag_mapping[$report_detail['member_id']] as $tag_name ) {
                            $tags_html .= ' <div class="tag_basic inline">'.$tag_name.'</div>';
                        }
                    }
                    $count_html = '';
                    if($counting) {
                        $count_html = '<td style="width: 130px;"><span>'.$report_detail['count_events'].'</span></td>';
                    }
                    $operation_html = '';
                    if ( $operation ) {
                        $operation_html = 
                            '<td class="operation" style="width: 210px;" align="center">
                                <span class="icon-undo pointer" style="height: 12px;width: 12px;" data-email="'.$report_detail['receiver'].'"></span>
                            </td>';
                    }
                    echo('
            <tr class="gradeX odd " id='.$report_detail['member_id'].'>

                <td style="width: 294px;">
                    <span>'.$report_detail['receiver'].'</span>
                </td>');
                
                echo($count_html);
                if($is_very_bot) {
                    echo('
                    <td>
                        <span>'.$report_detail['admin_user_name'].'</span>
                    </td>
                    <td>
                        <span>'.$report_detail['admin_bot_name'].'</span>
                    </td>');
                }
                
                echo('
                <td style="width: 190px;">
                    <span>'.$report_detail['username'].'</span>
                </td>
                <td style="width: 288px;">
                    '.$tags_html.'
                </td>    
                <td style="width: 210px;">
                    <span>'.$report_detail['create_time'].'</span>
                </td>'
                .$operation_html.
            '</tr>');                    
                }
            ?>

        </tbody>
    </table>


    
    <div class="btn_wrapper">
        <?php 
            $href_report = '/'.$_path.'/newslettertaskreport?id='.$mail_task->mail_task_id;
            $button_html = '<div id = "back2report" class="action-button page btn-white">戻る</div>';
            $report_html = HTML::anchor($href_report, $button_html);
            echo $report_html;
        ?>        
    </div>


</div>