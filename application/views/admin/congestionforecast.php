<script src='https://cdn.jsdelivr.net/npm/rrule@2.6.4/dist/es5/rrule.min.js'></script>
<script src='https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.js'></script>
<script src='https://cdn.jsdelivr.net/npm/@fullcalendar/rrule@6.1.8/index.global.min.js'></script>

<script>
    const _weekdays = <?php echo json_encode($_codes['46'], JSON_UNESCAPED_UNICODE) ?>;
    const _congestion = <?php echo json_encode($_codes['39'], JSON_UNESCAPED_UNICODE) ?>;
    const _items = <?php echo $data === null ? "[]" : $data; ?>;
    const _lang_cd = '<?php echo $lang_cd ?>';
</script>
<style>
    .fc-h-event {
        border: none;
        background-color: initial;
        border-radius: 2px;
    }

    .fc-v-event {
        border: none;
        background-color: initial;
        border-radius: 2px;
    }

    .fc-col-header-cell {
        background: #E3E5E8;
    }

    .fc-col-header-cell-cushion {
        color: #3D3F45;
        pointer-events: none;
        font-weight: normal;
    }

    .fc-daygrid-day-number {
        color: #3D3F45;
        pointer-events: none;
    }
</style>


<div class="content-container white border">
    <p style="font-weight:bold;"><?php echo __('admin.congestionforecast.label.title') ?></p>
    <div class="section-wrapper" style="width:80%; padding:20px">
        <!-- section components -->
        <div class="talkappi-schedule-section" type='congestion' data-value='<?php echo $data === "" ? "\"\"" : $data; ?>;' data-name="items" style="display: grid;row-gap: 2rem;">
        </div>
        <!-- section components -->
    </div>
    <div id="calendar" style="width:80%; padding:20px"></div>
    <div class="form-actions">
        <div class="row">
            <div class="col-md-offset-2" style="margin-top: 48px;">
                <div class="actions-container">
                    <button type="button" class="action-button page btn-blue js-action-save"><?php echo __('admin.common.button.save') ?></button>
                    <a class="action-button page btn-white js-action-back"><?php echo __('admin.common.button.return_to_list') ?></a>
                    <span class="action-button page btn-red-border icon-only js-action-delete"><span class="icon-delete"></span></span>
                </div>
            </div>
        </div>
    </div>
</div>