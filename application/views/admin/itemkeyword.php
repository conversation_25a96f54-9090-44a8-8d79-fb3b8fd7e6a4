<!-- BEGIN PAGE CONTENT-->
<?php echo $menu ?>
<style>
#tags_0_tagsinput .tag [data-role="remove"]:after {
    content: "";
    padding: 0px 2px;
}
#tags_2_tagsinput .tag {
  background: #D3EEFF;
  color: #111111;
}
#tags_2_tagsinput .tag  a {
	color: #111111;
}
</style>

<div class="content-container white border">
		<div class="section-container">
			<div class="form-group">
				<label class="control-label col-md-2"><?php echo __('admin.item.itemdisplay.name') ?></label>
				<div class="col-md-6 readonly-input flex-x-between" style="width:500px;height:fit-content;margin-left:10px;">
					<p><?php echo $post['item_name'] ?></p>
					<a class="js-edit-desc" data-lang="<?php echo $menu->bot_local_lang?>"><?php echo __('admin.common.button.edit') ?></a>
				</div>
			</div>
			<div class="form-group">
				<label class="control-label col-md-2">システム専用のキーワード<br>(編集不可)</label>
				<div class="col-md-9">
					<div class="input-icon right">
						<input name="keyword" id="tags_0" type="text" class="form-control tags" value="<?php if ($keyword!=null) echo($keyword->keyword);?>"/>
					</div>
				</div>
			</div>
			<div class="form-group">
				<label class="control-label col-md-2">施設専用のキーワード<br>(編集可能)</label>
				<div class="col-md-9">
					<div class="input-icon right">
						<input name="keyword2" id="tags_2" type="text" class="form-control tags" value="<?php if ($keyword2!=null) echo($keyword2->keyword);?>"/>
					</div>
				</div>
			</div>																																																														
		</div>
		<div class="actions-container" style="margin: 60px 0 0 140px;">
			<?php if ($permission > 0) {?>
			<button type="submit" class="btn-larger btn-blue js-action-save">保存</button>
			<?php }?>
			<button type="button" onclick="top.location='/admin/items'" class="btn-larger btn-white">戻る</button>
		</div>
</div>
<!-- END PAGE CONTENT-->



