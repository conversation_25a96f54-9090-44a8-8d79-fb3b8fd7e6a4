<style>
    .template-area-btn:focus,
    .template-area-btn:active {
        outline: none !important;
        box-shadow: none !important;
    }

    .text-align {
        display: inline-block;
        text-align: left !important;
        width: 128px;
    }

    .js-survey-delete-category {
        width: 28px;
        height: 28px;
        border-radius: 4px;
        border: solid 1px #e53361;
        background: #fff;
        margin-left: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .settings-container .lines-container .survey-lines-languages-container .btn.default.active {
        background: #cff2d7;
        color: #000;
    }

    .settings-container .lines-container .public-url-area {
        padding: 15px;
        height: auto;
    }

    .contents-setting-container {
        display: flex;

    }

    .template-box {
        height: 188px;
        width: 278px;
        margin-bottom: 16px;
        position: relative;
    }

    .rcm-preview {
        width: inherit;
        height: inherit;
        position: absolute !important;
        border: 1px solid #e3e5e8;

    }

    .contents-left-container {
        padding-right: 48px;
    }

    .large_edit_panel {
        cursor: pointer;
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        width: 278px;
        height: 32px;
        border-radius: 4px;
        background-color: #e3e5e8;
        border: none;
        padding: unset;
        user-select: none;
    }

    .large_edit_panel:disabled {
        opacity: 0.6;
    }

    .rcm-btn-icon {
        margin-left: 16px;
        margin-right: 10px;

    }

    .template-area-btn {
        position: relative;
        background-color: #ffffff;
        border: solid 1px #e3e5e8;
        width: 628px;
        height: 44px;
        border-radius: 4px;
        margin-bottom: 12px;
    }

    .template-area-text {
        position: relative;
        width: fit-content;
        left: 24px;
    }

    .template-area-arrow {
        position: absolute;
        top: 16px;
        right: 24px;
        width: 12px;
        height: 12px;
        background-image: url("/assets/admin/css/img/icon-drop-down-close.svg");
        background-repeat: no-repeat;
    }

    button[aria-expanded="true"] {
        border: none;
        border-top: solid 1px #e3e5e8;
        border-right: solid 1px #e3e5e8;
        border-left: solid 1px #e3e5e8;
        border-radius: 4px 4px 0 0;
        margin-bottom: 0;
    }

    button[aria-expanded="true"]>div {
        /* transform: scale(1,-1); */
        background-image: url("/assets/admin/css/img/icon-drop-down-open.svg");
    }

    .template-area-container {
        margin-bottom: 12px;
        width: 628px;
        min-height: 150px;
        border-radius: 4px;
        border: solid 1px #e3e5e8;
        background-color: #ffffff;
    }

    .collapse.in>.template-area-container {
        border: none;
        border-right: solid 1px #e3e5e8;
        border-left: solid 1px #e3e5e8;
        border-bottom: solid 1px #e3e5e8;
        border-radius: 0 0 4px 4px;

    }

    .contents-right-container {
        display:-webkit-box;
        display:-ms-flexbox;
        display:flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -ms-flex-direction: column;
        flex-direction: column;
    }

    .rcm-template-menu {
        width: 140px;
        height: 14px;
        margin: 10px 109px 16px 0;
        font-family: HiraginoSans-W4;
        font-size: 14px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: normal;
        letter-spacing: normal;
        color: #000;
    }

    .rcm-box-text {
        color: #adb5bd;
        position: absolute;
        height: 100%;
        text-align: center;
        align-items: center;
        display: flex;
        border: 1px solid #e3e5e8;

    }

    .rcm-area-text-box {
        width: 181px;
    }

    .rcm-area-text-box.skill_kind {
        width: 181px;
        margin-right: 12px;
    }

    .rcm-area-text-box2 {
        width: 278px;
    }

    .rcm-table {
        position: absolute;
        text-align: center !important;
        border: solid 2px #245BD6 !important;
        width: 100% !important;
        height: 100%;
        background-color: rgba(61, 63, 69, 0.24);
        z-index: 3;
        color: #ffffff;
        font-size: 24px;
        user-select: none;

    }

    .rcm-td {
        border: solid 2px #245BD6 !important;
        cursor: pointer;
    }

    .collapsing {
        -webkit-transition: 0.01s;
        transition: 0.01s;
    }

    /* 不要な青枠を非表示 */
    .template-area-container :focus {
        box-shadow: none;
    } 
    
    /* 入力欄の青枠を表示 */
    .skill_box :focus {
        box-shadow: 0 0 0 1px #245BD6;
    }
    .tips-wrapper {
        position: relative;
    }
    .tips-wrapper .icon-detail-box {
        top: 10px;
        left: 20px;
    }
    /* #56850の対応のためCSS追加
    https://activalues.slack.com/archives/C068AGY0CN5/p1714472677732629?thread_ts=1714457366.425289&cid=C068AGY0CN5 */
    .js-talkappi-fup-modal-input-method-url{
        display:none;
    }
</style>

<input type="hidden" name="act" id="act" value="" />
<input type="hidden" id="class_div" value="" />
<input type="hidden" name="msg_id" id="msg_id" value="<?php echo $msg_id ?>" />
<input type="hidden" name="tab_msg_id" id="tab_msg_id" />
<input type="hidden" id="lang_cd" name="lang_cd" value="<?php echo $lang_cd ?>" />
<input type="hidden" name="template_cd" id="template_cd" value="<?php echo $post['template_cd'] ?>">
<input type="hidden" name="rcm_data" id="rcm_data" value='<?php echo $areas ?>'>
<script type="text/javascript">
    var existed_range = <?php echo $existed_range ?>;
    var msg_list = <?php echo json_encode($msg_list, JSON_UNESCAPED_UNICODE);?>;
</script>
<div class="edit-container">
    <!-- 基本設定 -->
    <div class="settings-container">
        <div class="setting-container">
            <input type="hidden" name="message" id="message" value="<?php echo ($message) ?>" />
            <div class="setting-header">表示設定</div>
            <div class="lines-container" style="margin-bottom: 16px;display:none;">
                <div class="basic-label">メニューコード</div>
                <input type="text" name="rcm_cd" id="rcm_cd" value="<?php echo $post['rcm_cd'] ?>" class="text-input-longer">
            </div>
            <div class="lines-container">
                <div class="basic-label">メニュー名称</div>
                <input type="text" name="rcm_name" id="rcm_name" value="<?php echo $post['rcm_name'] ?>" class="text-input-longer">
            </div>
            <div class="lines-container">
                <div class="basic-label"></div>
                <div style="margin-top: 5px;">
                    <span style="color:grey;">メニュー名称は管理画面のみで使用され、ユーザーには表示されません。</span>
                </div>
            </div>
            <div class="lines-container">
                <div class="basic-label">コピー作成</div>
                <div style="width: 100%; max-width: 500px;">
                    <div class="talkappi-pulldown js-copy_template" data-name="copy_template" data-value="0" data-source='<?php echo json_encode($copylist, JSON_UNESCAPED_UNICODE); ?>'></div>
                </div>
            </div>
            <!-- 表示期間 -->
            <div class="lines-container period-container">
                <div class="basic-label">表示期間</div>
                <div class="survey-lines-period-select flexbox-x-axis">
                    <input type="text" class="talkappi-datepicker js-date" name="start_date" value="<?php echo ($post['start_date'] == '' ? '' : substr($post['start_date'], 0, 10)) ?>" data-date-format="yyyy-mm-dd" placeholder="yyyy-mm-dd" max-length="10" autocomplete="off">
                    <input type="text" class="talkappi-timepicker js-time" name="start_time" value="<?php echo ($post['start_date'] == '' ? '00:00' : substr($post['start_date'], 11, 5)) ?>" data-time-format="hh:mm" max-length="5" autocomplete="off">
                    <span style="margin: 0 8px;">〜</span>
                    <input type="text" class="talkappi-datepicker js-date" name="end_date" value="<?php echo ($post['end_date'] == '' ? '' : substr($post['end_date'], 0, 10)) ?>" data-date-format="yyyy-mm-dd" placeholder="yyyy-mm-dd" max-length="10" autocomplete="off">
                    <input type="text" class="talkappi-timepicker js-time" name="end_time" value="<?php echo ($post['end_date'] == '' ? '00:00' : substr($post['end_date'], 11, 5)) ?>" data-time-format="hh:mm" max-length="5" autocomplete="off">
                </div>
            </div>

            <div class="lines-container" id="data_err_msg_container" style="display: none;">
                <div class="basic-label"></div>
                <div style="margin-top: 5px;">
                    <span style="color:red;" id="date_err_msg"></span>
                </div>
            </div>

            <div class="lines-container language-container">
                <div class="basic-label">ユーザー導線</div>
                <div>
                    <?php echo Form::select('scene_cd', $scenes, $post['scene_cd'], array('class' => 'form-control')); ?>
                </div>
            </div>

            <!-- 多言語表示 -->
            <div class="lines-container language-container" style="display: none;">
                <div class="basic-label">多言語表示</div>
                <div class="survey-lines-languages-container" data-toggle="buttons">
                    <?php
                    $langs = explode(',', $post['lang_display']);
                    foreach ($_bot_lang as $lang => $lang_name) {
                        if (in_array($lang, $langs)) {
                            echo ('<label class="btn default active">');
                            echo ('<input name="lang_display[]" type="checkbox" checked="true" value="' . $lang . '" class="toggle">' . $lang_name . '</label>');
                        } else {
                            echo ('<label class="btn default">');
                            echo ('<input name="lang_display[]" type="checkbox" value="' . $lang . '" class="toggle">' . $lang_name . '</label>');
                        }
                    }
                    ?>
                </div>
            </div>

            <!-- メニュー開閉 -->
            <div class="lines-container language-container">
                <div class="basic-label">
                    常に開く
                    <span class="icon-detail" style="margin: 0 0 0 0.5rem;" title="いいえを選択した場合、メニューがタップされた後に自動的に閉じられ、表示領域は広くなり、UX改善に繋がるため、おすすめです"></span>
                </div>
                <div class="talkappi-radio js-menu-open" data-name="menu_open" data-value="<?php echo(isset($post['menu_open'])? $post['menu_open']:'0') ?>" data-source='{"1":"はい", "0":"いいえ"}' ></div>
            </div>

            <!-- メニュー作成状況 -->
            <div class="lines-container language-container">
                <div class="basic-label">メニュー作成状況</div>
                <div class="survey-lines-languages-container" data-toggle="buttons">
                    <?php
                    $langs = explode(',', $post['lang_created']);
                    $display_langs = explode(',', $post['lang_display']);
                    foreach ($_bot_lang as $lang => $lang_name) {
                        // if (in_array($lang, $display_langs) ) {
                            if (in_array($lang, $langs)) {
                                echo ('<span>' . $lang_name . ' </span>' . '<span style="font-size: 14px; color: #245BD6;"><i class="fas fa-check-circle"></i>' . '  ' . '</span>　');
                            } else {
                                echo ('<span>' . $lang_name . ' </span>' . '<span style="font-size: 14px; color: grey;"><i class="fas fa-check-circle"></i>' . '  ' . '</span>　');
                            }
                        // }
                    }
                    ?>
                </div>
            </div>
        </div>
        <nav class="line-tab">
            <ul class="">
                <?php
                // $langs = explode(',', $post['lang_display']);
                foreach (array_keys($_bot_lang) as $lang) {
                    $class = '';
                    if ($lang == $lang_cd) {
                        $class = 'active';
                    }
                    $msg_id_param = '';
                    if ($msg_id != NULL) $msg_id_param = '&id=' . $msg_id;
                    echo ('<li class="' . $class . '">');
                    echo ('<a class="func-menu" href="/admin/botlinemenu?lang=' . $lang . $msg_id_param . '">' . $_bot_lang[$lang] . '</a ></li>');
                }
                ?>
            </ul>
        </nav>

        <div class="setting-container">
            <div class="setting-header">メニュー設定</div>
            <div class="lines-container">
                <div class="basic-label">メニューバーの<br>テキスト</div>
                <input type="text" name="chatBarText" id="chatBarText" value="<?php echo $post['chatBarText'] ?>" class="text-input-longer">
            </div>
            <div class="lines-container js-default-tab-container" style="display: none;">
                <div class="basic-label">デフォルトタブ</div>
                <div class="talkappi-radio js-default-tab" data-name="tab_default" data-value="<?php echo($post['tab_default']) ?>" data-source='{"1":"はい", "0":"いいえ"}' ></div>
            </div>
            <?php if (count($tabs) > 0) { ?>
            <div class="lines-container js-default-tab-container" style="display: none;">
                <div class="basic-label">タグ付き</div>
                <div class="talkappi-radio js-show-tab" data-name="tab_show" data-value="1" data-source='{"0":"しない", "1":"タグ付き"}' ></div>
            </div>
            <?php } ?>
        </div>


        <div class="setting-container">
            <div style="display:flex; justify-content:space-between">
                <div class="setting-header">コンテンツ設定</div>
            </div>
            <div class="contents-setting-container">
                <div class="contents-left-container">
                    <div class="template-box" id="rcm-append-dest">
                        <div class="rcm-box-text">テンプレートを選択して、背景画像をアップロードしてください</div>
                        <img id="rcm-img-preview" class="rcm-preview" src="<?php echo ( $preview_image_url ? $preview_image_url . '?'. time() : " ") ?>">
                        <input type="hidden" name="image_base64" value="<?php echo ( $preview_image_url ? $preview_image_url . '?'. time() : " ") ?>">
                    </div>
                    <div class="large_edit_panel js-template-pick-btn">
                        <img class="rcm-btn-icon" src="/assets/admin/css/img/rcm-template.svg">
                        テンプレートを選択
                    </div>
                    <div class="large_edit_panel js-bg-img-btn">
                        <img class="rcm-btn-icon" src="/assets/admin/css/img/rcm-bkground.svg">
                        背景画像をアップロード
                    </div>
                    <div class="large_edit_panel js-delete-lang-menu">
                        <img class="rcm-btn-icon" src="/assets/admin/css/img/icon-delete.svg">
                        この言語のメニューを削除
                    </div>

                    <div class="modal fade" id="sampleModal" tabindex="-1">
                        <div class="modal-dialog" style="width: 808px;">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h4 class="modal-title">テンプレートを選択</h4>
                                </div>
                                <!-- テンプレートモーダル　開始 -->
                                <div class="modal-body">
                                    <div class="rcm-template-menu">大メニュー</div>

                                    <svg data-panel="6" data-temp-no="1" id="rcm-template-1" width="150" height="120" viewBox="0 0 155 124" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <rect x="0.5" y="0.5" width="51" height="52" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="0.5" y="52.5" width="51" height="52" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="51.5" y="0.5" width="52" height="52" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="51.5" y="52.5" width="52" height="52" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="103.5" y="0.5" width="51" height="52" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="103.5" y="52.5" width="51" height="52" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="0.5" y="104.5" width="154" height="19" fill="white" stroke="#C8CACE" />
                                    </svg>

                                    <!-- 上は4、下は4　 -->
                                    <svg data-panel="8" data-temp-no="20" id="rcm-template-20" width="150" height="120" viewBox="0 0 155 124" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <rect x="0.5" y="0.5" width="39" height="52" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="0.5" y="52.5" width="39" height="52" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="39.5" y="0.5" width="38" height="52" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="39.5" y="52.5" width="38" height="52" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="77.5" y="0.5" width="38" height="52" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="115.5" y="0.5" width="39" height="52" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="77.5" y="52.5" width="38" height="52" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="115.5" y="52.5" width="39" height="52" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="0.5" y="104.5" width="154" height="19" fill="white" stroke="#C8CACE" />
                                    </svg>

                                    <svg data-panel="4" data-temp-no="2" id="rcm-template-2" width="150" height="120" viewBox="0 0 155 124" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <rect x="0.5" y="0.5" width="77" height="52" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="0.5" y="52.5" width="77" height="52" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="77.5" y="0.5" width="77" height="52" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="77.5" y="52.5" width="77" height="52" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="0.5" y="104.5" width="154" height="19" fill="white" stroke="#C8CACE" />
                                    </svg>

                                    <svg data-panel="4" data-temp-no="3" id="rcm-template-3" width="150" height="120" viewBox="0 0 155 124" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <rect x="0.5" y="0.5" width="154" height="52" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="0.5" y="52.5" width="51" height="52" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="51.5" y="52.5" width="52" height="52" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="103.5" y="52.5" width="51" height="52" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="0.5" y="104.5" width="154" height="19" fill="white" stroke="#C8CACE" />
                                    </svg>

                                    <svg data-panel="3" data-temp-no="4" id="rcm-template-4" width="150" height="120" viewBox="0 0 155 124" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <rect x="0.5" y="0.5" width="103" height="104" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="103.5" y="0.5" width="51" height="52" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="103.5" y="52.5" width="51" height="52" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="0.5" y="104.5" width="154" height="19" fill="white" stroke="#C8CACE" />
                                    </svg>

                                    <svg data-panel="2" data-temp-no="5" id="rcm-template-5" width="150" height="120" viewBox="0 0 155 124" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <rect x="0.5" y="0.5" width="154" height="52" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="0.5" y="52.5" width="154" height="52" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="0.5" y="104.5" width="154" height="19" fill="white" stroke="#C8CACE" />
                                    </svg>

                                    <svg data-panel="2" data-temp-no="6" id="rcm-template-6" width="150" height="120" viewBox="0 0 155 124" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <rect x="0.5" y="0.5" width="77" height="104" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="77.5" y="0.5" width="77" height="104" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="0.5" y="104.5" width="154" height="19" fill="white" stroke="#C8CACE" />
                                    </svg>

                                    <svg data-panel="1" data-temp-no="7" id="rcm-template-7" width="150" height="120" viewBox="0 0 155 124" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <rect x="0.5" y="0.5" width="154" height="104" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="0.5" y="104.5" width="154" height="19" fill="white" stroke="#C8CACE" />
                                    </svg>

                                    <div class="rcm-template-menu">小メニュー</div>

                                    <svg data-panel="3" data-temp-no="8" id="rcm-template-8" width="150" height="72" viewBox="0 0 155 72" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <rect x="0.5" y="0.5" width="51" height="52" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="51.5" y="0.5" width="52" height="52" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="103.5" y="0.5" width="51" height="52" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="0.5" y="52.5" width="154" height="19" fill="white" stroke="#C8CACE" />
                                    </svg>

                                    <svg data-panel="2" data-temp-no="9" id="rcm-template-9" width="150" height="72" viewBox="0 0 155 72" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <rect x="0.5" y="0.5" width="51" height="52" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="51.5" y="0.5" width="103" height="52" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="0.5" y="52.5" width="154" height="19" fill="white" stroke="#C8CACE" />
                                    </svg>

                                    <svg data-panel="2" data-temp-no="10" id="rcm-template-10" width="150" height="72" viewBox="0 0 155 72" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <rect x="103.5" y="0.5" width="51" height="52" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="0.5" y="0.5" width="103" height="52" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="0.5" y="52.5" width="154" height="19" fill="white" stroke="#C8CACE" />
                                    </svg>

                                    <svg data-panel="2" data-temp-no="11" id="rcm-template-11" width="150" height="72" viewBox="0 0 155 72" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <rect x="0.5" y="0.5" width="77" height="52" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="77.5" y="0.5" width="77" height="52" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="0.5" y="52.5" width="154" height="19" fill="white" stroke="#C8CACE" />
                                    </svg>

                                    <svg data-panel="1" data-temp-no="12" id="rcm-template-12" width="150" height="72" viewBox="0 0 155 72" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <rect x="0.5" y="0.5" width="154" height="52" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="0.5" y="52.5" width="154" height="19" fill="white" stroke="#C8CACE" />
                                    </svg>

                                    <div class="rcm-template-menu">タイトルバー付き</div>

                                    <svg data-panel="6" data-temp-no="13" id="rcm-template-13" width="150" height="120" viewBox="0 0 155 124" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <rect x="0.5" y="12.5" width="51" height="46" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="0.5" y="0.5" width="154" height="12" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="0.5" y="58.5" width="51" height="46" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="51.5" y="12.5" width="52" height="46" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="51.5" y="58.5" width="52" height="46" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="103.5" y="12.5" width="51" height="46" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="103.5" y="58.5" width="51" height="46" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="0.5" y="104.5" width="154" height="19" fill="white" stroke="#C8CACE" />
                                    </svg>

                                    <!-- 上は２、下は３ -->
                                    <svg data-panel="5" data-temp-no="17" id="rcm-template-17" width="150" height="120" viewBox="0 0 155 124" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <rect x="0.5" y="12.5" width="103" height="46" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="0.5" y="0.5" width="154" height="12" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="0.5" y="58.5" width="51" height="46" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="51.5" y="58.5" width="52" height="46" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="103.5" y="12.5" width="51" height="46" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="103.5" y="58.5" width="51" height="46" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="0.5" y="104.5" width="154" height="19" fill="white" stroke="#C8CACE" />
                                    </svg>

                                    <!-- 上は１、下は３ -->
                                    <svg data-panel="4" data-temp-no="18" id="rcm-template-18" width="150" height="120" viewBox="0 0 155 124" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <rect x="0.5" y="12.5" width="154" height="46" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="0.5" y="0.5" width="154" height="12" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="0.5" y="58.5" width="51" height="46" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="51.5" y="58.5" width="52" height="46" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="103.5" y="58.5" width="51" height="46" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="0.5" y="104.5" width="154" height="19" fill="white" stroke="#C8CACE" />
                                    </svg>

                                    <!-- 上は２、下は２ -->
                                    <svg data-panel="4" data-temp-no="19" id="rcm-template-19" width="150" height="120" viewBox="0 0 155 124" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <rect x="0.5" y="12.5" width="103" height="46" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="0.5" y="0.5" width="154" height="12" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="0.5" y="58.5" width="103" height="46" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="103.5" y="12.5" width="51" height="46" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="103.5" y="58.5" width="51" height="46" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="0.5" y="104.5" width="154" height="19" fill="white" stroke="#C8CACE" />
                                    </svg>

                                    <!-- 上は4、下は4 小タイトル -->
                                    <svg data-panel="8" data-temp-no="14" id="rcm-template-14" width="150" height="120" viewBox="0 0 155 124" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <rect x="0.5" y="12.5" width="39" height="46" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="0.5" y="0.5" width="154" height="12" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="0.5" y="58.5" width="39" height="46" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="39.5" y="12.5" width="38" height="46" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="39.5" y="58.5" width="38" height="46" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="77.5" y="12.5" width="38" height="46" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="115.5" y="12.5" width="39" height="46" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="77.5" y="58.5" width="38" height="46" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="115.5" y="58.5" width="39" height="46" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="0.5" y="104.5" width="154" height="19" fill="white" stroke="#C8CACE" />
                                    </svg>

                                    <!-- 上は4、下は4 大タイトル -->
                                    <svg data-panel="8" data-temp-no="15" id="rcm-template-15" width="150" height="120" viewBox="0 0 155 124" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <rect x="0.5" y="22.5" width="39" height="41" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="0.5" y="0.5" width="154" height="22" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="0.5" y="63.5" width="39" height="41" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="39.5" y="22.5" width="38" height="41" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="39.5" y="63.5" width="38" height="41" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="77.5" y="22.5" width="38" height="41" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="115.5" y="22.5" width="39" height="41" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="77.5" y="63.5" width="38" height="41" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="115.5" y="63.5" width="39" height="41" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="0.5" y="104.5" width="154" height="19" fill="white" stroke="#C8CACE" />
                                    </svg>

                                    <!-- タイトル付き、左２、中２、右３パネル -->
                                    <svg data-panel="7" data-temp-no="21" id="rcm-template-21" width="150" height="120" viewBox="0 0 155 124" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <rect x="0.5" y="12.5" width="51" height="46" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="0.5" y="0.5" width="154" height="12" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="0.5" y="58.5" width="51" height="46" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="51.5" y="12.5" width="52" height="46" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="51.5" y="58.5" width="52" height="46" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="103.5" y="12.5" width="51" height="31" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="103.5" y="43.5" width="51" height="30" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="103.5" y="73.5" width="51" height="31" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="0.5" y="104.5" width="154" height="19" fill="white" stroke="#C8CACE" />
                                    </svg>

                                    <!-- タイトル付き、左２、中２、はみ出し右３パネル -->
                                    <svg data-panel="7" data-temp-no="30" id="rcm-template-30" width="150" height="120" viewBox="0 0 155 124" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <rect x="0.5" y="12.5" width="51" height="46" fill="#F6F7F9" stroke="#C8CACE"/>
                                        <rect x="0.5" y="0.5" width="103" height="12" fill="#F6F7F9" stroke="#C8CACE"/>
                                        <rect x="0.5" y="58.5" width="51" height="46" fill="#F6F7F9" stroke="#C8CACE"/>
                                        <rect x="51.5" y="12.5" width="52" height="46" fill="#F6F7F9" stroke="#C8CACE"/>
                                        <rect x="51.5" y="58.5" width="52" height="46" fill="#F6F7F9" stroke="#C8CACE"/>
                                        <rect x="103.5" y="0.5" width="51" height="35" fill="#F6F7F9" stroke="#C8CACE"/>
                                        <rect x="103.5" y="35.5" width="51" height="34" fill="#F6F7F9" stroke="#C8CACE"/>
                                        <rect x="103.5" y="69.5" width="51" height="35" fill="#F6F7F9" stroke="#C8CACE"/>
                                        <rect x="0.5" y="104.5" width="154" height="19" fill="white" stroke="#C8CACE"/>
                                    </svg>

                                    <!-- タイトル付き、左２、中２、はみ出し右4パネル -->
                                    <svg data-panel="8" data-temp-no="31" id="rcm-template-31" width="150" height="120" viewBox="0 0 155 124" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <rect x="0.5" y="12.5" width="51" height="46" fill="#F6F7F9" stroke="#C8CACE"/>
                                        <rect x="0.5" y="0.5" width="103" height="12" fill="#F6F7F9" stroke="#C8CACE"/>
                                        <rect x="0.5" y="58.5" width="51" height="46" fill="#F6F7F9" stroke="#C8CACE"/>
                                        <rect x="51.5" y="12.5" width="52" height="46" fill="#F6F7F9" stroke="#C8CACE"/>
                                        <rect x="51.5" y="58.5" width="52" height="46" fill="#F6F7F9" stroke="#C8CACE"/>
                                        <rect x="103.5" y="0.5" width="51" height="26" fill="#F6F7F9" stroke="#C8CACE"/>
                                        <rect x="103.5" y="26.5" width="51" height="26" fill="#F6F7F9" stroke="#C8CACE"/>
                                        <rect x="103.5" y="52.5" width="51" height="26" fill="#F6F7F9" stroke="#C8CACE"/>
                                        <rect x="103.5" y="78.5" width="51" height="26" fill="#F6F7F9" stroke="#C8CACE"/>
                                        <rect x="0.5" y="104.5" width="154" height="19" fill="white" stroke="#C8CACE"/>
                                    </svg>


                                    <!-- タイトル付き、3つパネル -->
                                    <svg data-panel="3" data-temp-no="16" id="rcm-template-16" width="150" height="72" viewBox="0 0 155 72" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <rect x="0.5" y="12.5" width="51" height="40" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="51.5" y="12.5" width="52" height="40" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="103.5" y="12.5" width="51" height="40" fill="#F6F7F9" stroke="#C8CACE" />
                                        <rect x="0.5" y="52.5" width="154" height="19" fill="white" stroke="#C8CACE" />
                                        <rect x="0.5" y="0.5" width="154" height="12" fill="#F6F7F9" stroke="#C8CACE" />
                                    </svg>

                                    <!-- 11パネル　 -->
                                    <svg data-panel="11" data-temp-no="32" id="rcm-template-32" width="150" height="120" viewBox="0 0 152 122" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <rect x="1" y="13" width="31" height="44" fill="#F6F7F9" stroke="#C8CACE"/>
                                        <rect x="61" y="13" width="31" height="44" fill="#F6F7F9" stroke="#C8CACE"/>
                                        <rect x="1" y="1" width="150" height="12" fill="#F6F7F9" stroke="#C8CACE"/>
                                        <rect x="1" y="57" width="31" height="44" fill="#F6F7F9" stroke="#C8CACE"/>
                                        <rect x="61" y="57" width="31" height="44" fill="#F6F7F9" stroke="#C8CACE"/>
                                        <rect x="32" y="13" width="29" height="44" fill="#F6F7F9" stroke="#C8CACE"/>
                                        <rect x="92" y="13" width="29" height="44" fill="#F6F7F9" stroke="#C8CACE"/>
                                        <rect x="121" y="13" width="30" height="29" fill="#F6F7F9" stroke="#C8CACE"/>
                                        <rect x="121" y="42" width="30" height="30" fill="#F6F7F9" stroke="#C8CACE"/>
                                        <rect x="121" y="72" width="30" height="29" fill="#F6F7F9" stroke="#C8CACE"/>
                                        <rect x="32" y="57" width="29" height="44" fill="#F6F7F9" stroke="#C8CACE"/>
                                        <rect x="92" y="57" width="29" height="44" fill="#F6F7F9" stroke="#C8CACE"/>
                                        <rect x="1" y="101" width="150" height="20" fill="white" stroke="#C8CACE"/>
                                    </svg>

                                    <!-- 9パネル　 -->
                                    <svg data-panel="9" data-temp-no="34" id="rcm-template-34" width="150" height="120" viewBox="0 0 152 122" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <rect x="1" y="1" width="150" height="12" fill="#F6F7F9" stroke="#C8CACE"/>
                                        <rect x="1" y="13" width="60" height="44" fill="#F6F7F9" stroke="#C8CACE"/>
                                        <rect x="61" y="13" width="60" height="44" fill="#F6F7F9" stroke="#C8CACE"/>
                                        <rect x="1" y="57" width="31" height="44" fill="#F6F7F9" stroke="#C8CACE"/>
                                        <rect x="61" y="57" width="31" height="44" fill="#F6F7F9" stroke="#C8CACE"/>
                                        <rect x="121" y="13" width="30" height="29" fill="#F6F7F9" stroke="#C8CACE"/>
                                        <rect x="121" y="42" width="30" height="30" fill="#F6F7F9" stroke="#C8CACE"/>
                                        <rect x="121" y="72" width="30" height="29" fill="#F6F7F9" stroke="#C8CACE"/>
                                        <rect x="32" y="57" width="29" height="44" fill="#F6F7F9" stroke="#C8CACE"/>
                                        <rect x="92" y="57" width="29" height="44" fill="#F6F7F9" stroke="#C8CACE"/>
                                        <rect x="1" y="101" width="150" height="20" fill="white" stroke="#C8CACE"/>
                                    </svg>

                                    <div class="rcm-template-menu">タブ付きメニュー</div>

                                     <!-- 上は3、下は3　 -->
                                    <svg data-panel="8" data-temp-no="22" id="rcm-template-22" width="150" height="120" viewBox="0 0 150 120" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <rect x="0" y="12" width="50" height="44" fill="#F6F7F9" stroke="#C8CACE" stroke-linejoin="round"/>
                                        <rect x="0" y="56" width="50" height="44" fill="#F6F7F9" stroke="#C8CACE" stroke-linejoin="round"/>
                                        <rect x="50" y="12" width="50" height="44" fill="#F6F7F9" stroke="#C8CACE" stroke-linejoin="round"/>
                                        <rect x="50" y="56" width="50" height="44" fill="#F6F7F9" stroke="#C8CACE" stroke-linejoin="round"/>
                                        <rect x="100" y="12" width="50" height="44" fill="#F6F7F9" stroke="#C8CACE" stroke-linejoin="round"/>
                                        <rect x="100" y="56" width="50" height="44" fill="#F6F7F9" stroke="#C8CACE" stroke-linejoin="round"/>
                                        <rect x="0" y="100" width="150" height="20" fill="white" stroke="#C8CACE" stroke-linejoin="round"/>
                                        <rect x="75" y="0" width="75" height="12" fill="#EBEDF2" stroke="#C8CACE"/>
                                        <path d="M 0 0 H 75 V 12 H 0 V 0 Z" fill="#F6F7F9" stroke="#C8CACE"/>
                                    </svg>

                                    <!-- 上は2、下は3　 -->
                                    <svg data-panel="7" data-temp-no="33" id="rcm-template-33" width="150" height="120" viewBox="0 0 150 120" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <rect x="0" y="12" width="100" height="44" fill="#F6F7F9" stroke="#C8CACE" stroke-linejoin="round"/>
                                        <rect x="0" y="56" width="50" height="44" fill="#F6F7F9" stroke="#C8CACE" stroke-linejoin="round"/>
                                        <rect x="50" y="56" width="50" height="44" fill="#F6F7F9" stroke="#C8CACE" stroke-linejoin="round"/>
                                        <rect x="100" y="12" width="50" height="44" fill="#F6F7F9" stroke="#C8CACE" stroke-linejoin="round"/>
                                        <rect x="100" y="56" width="50" height="44" fill="#F6F7F9" stroke="#C8CACE" stroke-linejoin="round"/>
                                        <rect x="0" y="100" width="150" height="20" fill="white" stroke="#C8CACE" stroke-linejoin="round"/>
                                        <rect x="75" y="0" width="75" height="12" fill="#EBEDF2" stroke="#C8CACE"/>
                                        <path d="M 0 0 H 75 V 12 H 0 V 0 Z" fill="#F6F7F9" stroke="#C8CACE"/>
                                    </svg>


                                    <!-- 上は4、下は4　 -->
                                    <svg data-panel="10" data-temp-no="23" id="rcm-template-23" width="150" height="120" viewBox="0 0 150 120" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <rect x="0" y="12" width="38" height="44" fill="#F6F7F9" stroke="#C8CACE"/>
                                        <rect x="0" y="56" width="38" height="44" fill="#F6F7F9" stroke="#C8CACE"/>
                                        <rect x="38" y="12" width="37" height="44" fill="#F6F7F9" stroke="#C8CACE"/>
                                        <rect x="38" y="56" width="37" height="44" fill="#F6F7F9" stroke="#C8CACE"/>
                                        <rect x="75" y="12" width="37" height="44" fill="#F6F7F9" stroke="#C8CACE"/>
                                        <rect x="112" y="12" width="38" height="44" fill="#F6F7F9" stroke="#C8CACE"/>
                                        <rect x="75" y="56" width="37" height="44" fill="#F6F7F9" stroke="#C8CACE"/>
                                        <rect x="112" y="56" width="38" height="44" fill="#F6F7F9" stroke="#C8CACE"/>
                                        <rect x="0" y="100" width="150" height="20" fill="white" stroke="#C8CACE"/>
                                        <rect x="75" y="0" width="75" height="12" fill="#EBEDF2" stroke="#C8CACE"/>
                                        <path d="M 0 0 H 75 V 12 H 0 V 0 Z" fill="#F6F7F9" stroke="#C8CACE"/>
                                    </svg>

                                    <!-- 上は2、下は2　 -->
                                    <svg data-panel="6" data-temp-no="24" id="rcm-template-24" width="150" height="120" viewBox="0 0 150 120" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <rect x="0" y="12" width="75" height="44" fill="#F6F7F9" stroke="#C8CACE"/>
                                        <rect x="0" y="56" width="75" height="44" fill="#F6F7F9" stroke="#C8CACE"/>
                                        <rect x="75" y="12" width="75" height="44" fill="#F6F7F9" stroke="#C8CACE"/>
                                        <rect x="75" y="56" width="75" height="44" fill="#F6F7F9" stroke="#C8CACE"/>
                                        <rect x="0" y="100" width="150" height="20" fill="white" stroke="#C8CACE"/>
                                        <rect x="75" y="0" width="75" height="12" fill="#EBEDF2" stroke="#C8CACE"/>
                                        <path d="M 0 0 H 75 V 12 H 0 V 0 Z" fill="#F6F7F9" stroke="#C8CACE"/>
                                    </svg>

                                    <!-- 上は1、下は3　 -->
                                    <svg data-panel="6" data-temp-no="25" id="rcm-template-25" width="150" height="120" viewBox="0 0 150 120" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <rect x="0" y="12" width="150" height="44" fill="#F6F7F9" stroke="#C8CACE"/>
                                        <rect x="0" y="56" width="50" height="44" fill="#F6F7F9" stroke="#C8CACE"/>
                                        <rect x="50" y="56" width="50" height="44" fill="#F6F7F9" stroke="#C8CACE"/>
                                        <rect x="100" y="56" width="50" height="44" fill="#F6F7F9" stroke="#C8CACE"/>
                                        <rect x="0" y="100" width="150" height="20" fill="white" stroke="#C8CACE"/>
                                        <rect x="75" y="0" width="75" height="12" fill="#EBEDF2" stroke="#C8CACE"/>
                                        <path d="M 0 0 H 75 V 12 H 0 V 0 Z" fill="#F6F7F9" stroke="#C8CACE"/>
                                    </svg>

                                    <!-- 左は1、右は2　 -->
                                    <!-- when defining a Line Rich Menu template, ensure that the 'data-panel' attribute matches the corresponding number of the 'table[id]Area' as defined in the 'botlinemenu.js' file.  -->
                                    <svg data-panel="5" data-temp-no="26" id="rcm-template-26" width="150" height="120" viewBox="0 0 150 120" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <rect x="0" y="12" width="100" height="88" fill="#F6F7F9" stroke="#C8CACE"/>
                                        <rect x="100" y="12" width="50" height="44" fill="#F6F7F9" stroke="#C8CACE"/>
                                        <rect x="100" y="56" width="50" height="44" fill="#F6F7F9" stroke="#C8CACE"/>
                                        <rect x="0" y="100" width="150" height="20" fill="white" stroke="#C8CACE"/>
                                        <rect x="75" y="0" width="75" height="12" fill="#EBEDF2" stroke="#C8CACE"/>
                                        <path d="M 0 0 H 75 V 12 H 0 V 0 Z" fill="#F6F7F9" stroke="#C8CACE"/>
                                    </svg>

                                    <!-- 上は1、下は1　 -->
                                    <svg data-panel="4" data-temp-no="27" id="rcm-template-27" width="150" height="120" viewBox="0 0 150 120" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <rect x="0" y="12" width="150" height="44" fill="#F6F7F9" stroke="#C8CACE"/>
                                        <rect x="0" y="56" width="150" height="44" fill="#F6F7F9" stroke="#C8CACE"/>
                                        <rect x="0" y="100" width="150" height="20" fill="white" stroke="#C8CACE"/>
                                        <rect x="75" y="0" width="75" height="12" fill="#EBEDF2" stroke="#C8CACE"/>
                                        <path d="M 0 0 H 75 V 12 H 0 V 0 Z" fill="#F6F7F9" stroke="#C8CACE"/>
                                    </svg>

                                    <!-- 左は1、右は1　 -->
                                    <svg data-panel="4" data-temp-no="28" id="rcm-template-28" width="150" height="120" viewBox="0 0 150 120" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <rect x="0" y="12" width="75" height="88" fill="#F6F7F9" stroke="#C8CACE"/>
                                        <rect x="75" y="12" width="77" height="88" fill="#F6F7F9" stroke="#C8CACE"/>
                                        <rect x="0" y="100" width="154" height="20" fill="white" stroke="#C8CACE"/>
                                        <rect x="75" y="0" width="75" height="12" fill="#EBEDF2" stroke="#C8CACE"/>
                                        <path d="M 0 0 H 75 V 12 H 0 V 0 Z" fill="#F6F7F9" stroke="#C8CACE"/>
                                    </svg>

                                    <!-- 1のみ　 -->
                                    <svg data-panel="3" data-temp-no="29" id="rcm-template-29" width="150" height="120" viewBox="0 0 150 120" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <rect x="0" y="12" width="150" height="88" fill="#F6F7F9" stroke="#C8CACE"/>
                                        <rect x="0" y="100" width="150" height="20" fill="white" stroke="#C8CACE"/>
                                        <rect x="75" y="0" width="75" height="12" fill="#EBEDF2" stroke="#C8CACE"/>
                                        <path d="M 0 0 H 75 V 12 H 0 V 0 Z" fill="#F6F7F9" stroke="#C8CACE"/>
                                    </svg>


                                </div>
                                <div class="modal-footer" style="text-align: right;display: flex;">
                                    <button type="button" id="rcm-template-choose" data-dismiss="modal" class="btn-smaller btn-blue">OK</button>
                                    <button type="button" class="btn-smaller btn-white" data-dismiss="modal">キャンセル</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="contents-right-container">
                    <?php if (count($tabs) > 0) { ?>
                        <nav class="button-tab tabs-area" style="display: none">
                            <ul class="">
                                <?php 
                                    foreach ($tabs as $tab) {
                                        $active = $msg_id == $tab['msg_id'] ? 'active' : '';
                                        echo ('<li class="' . $active . '" data-msg_id="' . $tab['msg_id'] . '">' . $tab['msg_name'] . '</li>');
                                    }
                                ?>
                            </ul>
                            <div class="tips-wrapper">
                                <span class="icon-detail" style="margin: 0 0 0 0.5rem;" title="「エリアA」と「エリアB」のメニューエイリアスは、両方のタブで同じメニューを選択してください。"></sapn>
                            </div>
                        </nav>
                    <?php }?>
                    <button type="button" class="template-area-btn-js template-area-btn" data-toggle="collapse" data-target="#areaA">
                        <p class="template-area-text">エリアA</p>
                        <div src="" class="template-area-arrow"></div>
                    </button>
                    <div id="areaA" data-area="1" class="area-container-wrapper panel-collapse collapse">
                        <div class="template-area-container">
                            <?php echo $skillbox_areaA ?>
                        </div>
                    </div>

                    <button type="button" class="template-area-btn-js template-area-btn" data-toggle="collapse" data-target="#areaB">
                        <p class="template-area-text">エリアB</p>
                        <div src="" class="template-area-arrow"></div>
                    </button>
                    <div id="areaB" data-area="2" class="area-container-wrapper panel-collapse collapse">
                        <div class="template-area-container">
                            <?php echo $skillbox_areaB ?>
                        </div>
                    </div>

                    <button type="button" class="template-area-btn-js template-area-btn" data-toggle="collapse" data-target="#areaC">
                        <p class="template-area-text">エリアC</p>
                        <div src="" class="template-area-arrow"></div>
                    </button>
                    <div id="areaC" data-area="3" class="area-container-wrapper panel-collapse collapse">
                        <div class="template-area-container">
                            <?php echo $skillbox_areaC ?>
                        </div>
                    </div>

                    <button type="button" class="template-area-btn-js template-area-btn" data-toggle="collapse" data-target="#areaD">
                        <p class="template-area-text">エリアD</p>
                        <div src="" class="template-area-arrow"></div>
                    </button>
                    <div id="areaD" data-area="4" class="area-container-wrapper panel-collapse collapse">
                        <div class="template-area-container">
                            <?php echo $skillbox_areaD ?>
                        </div>
                    </div>

                    <button type="button" class="template-area-btn-js template-area-btn" data-toggle="collapse" data-target="#areaE">
                        <p class="template-area-text">エリアE</p>
                        <div src="" class="template-area-arrow"></div>
                    </button>
                    <div id="areaE" data-area="5" class="area-container-wrapper panel-collapse collapse">
                        <div class="template-area-container">
                            <?php echo $skillbox_areaE ?>
                        </div>
                    </div>

                    <button type="button" class="template-area-btn-js template-area-btn" data-toggle="collapse" data-target="#areaF">
                        <p class="template-area-text">エリアF</p>
                        <div src="" class="template-area-arrow"></div>
                    </button>
                    <div id="areaF" data-area="6" class="area-container-wrapper panel-collapse collapse">
                        <div class="template-area-container">
                            <?php echo $skillbox_areaF ?>
                        </div>
                    </div>

                    <button type="button" class="template-area-btn-js template-area-btn" data-toggle="collapse" data-target="#areaG">
                        <p class="template-area-text">エリアG</p>
                        <div src="" class="template-area-arrow"></div>
                    </button>
                    <div id="areaG" data-area="7" class="area-container-wrapper panel-collapse collapse">
                        <div class="template-area-container">
                            <?php echo $skillbox_areaG ?>
                        </div>
                    </div>

                    <button type="button" class="template-area-btn-js template-area-btn" data-toggle="collapse" data-target="#areaH">
                        <p class="template-area-text">エリアH</p>
                        <div src="" class="template-area-arrow"></div>
                    </button>
                    <div id="areaH" data-area="8" class="area-container-wrapper panel-collapse collapse">
                        <div class="template-area-container">
                            <?php echo $skillbox_areaH ?>
                        </div>
                    </div>

                    <button type="button" class="template-area-btn-js template-area-btn" data-toggle="collapse" data-target="#areaI">
                        <p class="template-area-text">エリアI</p>
                        <div src="" class="template-area-arrow"></div>
                    </button>
                    <div id="areaI" data-area="9" class="area-container-wrapper panel-collapse collapse">
                        <div class="template-area-container">
                            <?php echo $skillbox_areaI ?>
                        </div>
                    </div>

                    <button type="button" class="template-area-btn-js template-area-btn" data-toggle="collapse" data-target="#areaJ">
                        <p class="template-area-text">エリアJ</p>
                        <div src="" class="template-area-arrow"></div>
                    </button>
                    <div id="areaJ" data-area="10" class="area-container-wrapper panel-collapse collapse">
                        <div class="template-area-container">
                            <?php echo $skillbox_areaJ ?>
                        </div>
                    </div>
                    
                    <button type="button" class="template-area-btn-js template-area-btn" data-toggle="collapse" data-target="#areaK">
                        <p class="template-area-text">エリアK</p>
                        <div src="" class="template-area-arrow"></div>
                    </button>
                    <div id="areaK" data-area="11" class="area-container-wrapper panel-collapse collapse">
                        <div class="template-area-container">
                            <?php echo $skillbox_areaK ?>
                        </div>
                    </div>

                </div>
            </div>

            <div class="form-group" style="margin: 20px 0 0 0; width: 954px; <?php if ($lang_cd != 'ja') echo ('display:none;') ?> ">
                <label class="control-label col-md-3">多言語は日本語と同様に設定</label>
                <div class="col-md-2">
                    <input type="checkbox" name="flg_apply_all_lang" <?php if ($all_language == 1) echo ('checked') ?> value="1" class="make-switch" data-on-color="success" data-off-color="default">
                </div>
                <label class="control-label col-md-3">多言語は日本語で自動翻訳</label>
                <div class="col-md-2">
                    <input type="checkbox" name="flg_auto_translate" <?php if ($auto_translate == 1) echo ('checked') ?> value="1" class="make-switch" data-on-color="success" data-off-color="default">
                </div>
            </div>
        </div>

        <!-- ボタン -->
        <div class="submit-btn-container" style="margin: 60px 0 0 134px;">
            <div type="button" class="btn-larger btn-blue" id="saveButton">保存</div>
            <div type="button" class="btn-larger btn-gray-black" id="draftSaveButton">下書き保存</div>
            <div class="btn-larger btn-white" onclick="top.location='/admin/botline'">一覧に戻る</div>
            <div class="btn-larger btn-red-border" id="deleteButton">
                <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12">
                    <g fill="none" fill-rule="evenodd" stroke-linecap="round">
                        <g stroke="#e53361" stroke-width="2">
                            <path d="M1 2.5L11 2.5M6 2L6 1M6 8L6 6" />
                            <path stroke-linejoin="round" d="M2 6L2 11 10 11 10 6" />
                        </g>
                    </g>
                </svg>
            </div>
        </div>
    </div>
</div>
<div class="modal fade" id="date_error_modal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
            </div>

            <!-- テンプレートモーダル　開始 -->
            <div class="modal-body">
            </div>
            <div class="modal-footer" style="text-align: right;">
                <button type="button" data-dismiss="modal" class="btn btn-primary">OK</button>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
    const post_data = <?php echo json_encode($post); ?>;
    const skill_data = <?php echo ($areas); ?>;
    <?php if ($msg_id) { ?>
        const msg_id = <?php echo ($msg_id) ?>;
    <?php } ?>
</script>