<div class="flexbox">
	<div>
		<div class="category-separator">
			<div class="setting-header">基本様式設定</div>
		</div>
		<div class="lines-container">
			<div class="basic-label">ロゴ写真</div>
			<div class="" style="width:400px">
				<div style="" class="talkappi-upload" data-name="logo_file_base64" data-label="<?php echo basename($post['logo-file'])?>" data-type="img" data-ratio="1:1" data-url="<?php echo($post['logo-file'])?>"></div>
			</div>
		</div>
		<div class="lines-container">
			<div class="basic-label">ロゴ写真の遷移先URL</div>
			<div class="" style="width:600px">
				<input name="logo-url" type="text" class="form-control" placeholder="" value="<?php if ($post != NULL) echo ($post['logo-url']) ?>">
			</div>
		</div>
		<div class="lines-container">
			<div class="basic-label">フッターロゴ写真</div>
			<div class="" style="width:400px">
				<div style="" class="talkappi-upload" data-name="logo_footer_file_base64" data-label="<?php echo basename($post['logo-footer-file'])?>" data-type="img" data-ratio="1:1" data-url="<?php echo($post['logo-footer-file'])?>"></div>
			</div>
		</div>
		<div class="lines-container">
			<label class="basic-label">ページ背景色</label>
			<div class="">
				<div class="talkappi-colorpicker js-theme-color" data-name="page-bk-color" data-value="<?php if ($post != NULL) echo ($post['page-bk-color']) ?>"></div>
			</div>
		</div>
		<div class="lines-container">
			<label class="basic-label">ヘッダ文字色</label>
			<div class="">
				<div class="talkappi-colorpicker js-theme-color" data-name="header-color" data-value="<?php if ($post != NULL) echo ($post['header-color']) ?>"></div>
			</div>
		</div>
		<div class="lines-container">
			<label class="basic-label">ヘッダ背景色</label>
			<div class="">
				<div class="talkappi-colorpicker js-theme-color" data-name="header-bk-color" data-value="<?php if ($post != NULL) echo ($post['header-bk-color']) ?>"></div>
			</div>
		</div>
		<div class="lines-container">
			<label class="basic-label">フッター文字色</label>
			<div class="">
				<div class="talkappi-colorpicker js-theme-color" data-name="footer-color" data-value="<?php if ($post != NULL) echo ($post['footer-color']) ?>"></div>
			</div>
		</div>
		<div class="lines-container">
			<label class="basic-label">フッター背景色</label>
			<div class="">
				<div class="talkappi-colorpicker js-theme-color" data-name="footer-bk-color" data-value="<?php if ($post != NULL) echo ($post['footer-bk-color']) ?>"></div>
			</div>
		</div>
		<div class="lines-container">
			<label class="basic-label">メイン文字色</label>
			<div class="">
				<div class="talkappi-colorpicker js-theme-color" data-name="theme-color" data-value="<?php if ($post != NULL) echo ($post['theme-color']) ?>"></div>
			</div>
		</div>
		<div class="lines-container">
			<label class="basic-label">メイン背景色</label>
			<div class="">
				<div class="talkappi-colorpicker js-theme-color" data-name="main-bk-color" data-value="<?php if ($post != NULL) echo ($post['main-bk-color']) ?>"></div>
			</div>
		</div>
		<div class="lines-container">
			<label class="basic-label">コンテンツ文字色</label>
			<div class="">
				<div class="talkappi-colorpicker js-theme-color" data-name="item-color" data-value="<?php if ($post != NULL) echo ($post['item-color']) ?>"></div>
			</div>
		</div>
		<div class="lines-container">
			<label class="basic-label">コンテンツ背景色</label>
			<div class="">
				<div class="talkappi-colorpicker js-theme-color" data-name="item-bk-color" data-value="<?php if ($post != NULL) echo ($post['item-bk-color']) ?>"></div>
			</div>
		</div>
		<div class="lines-container">
			<div class="basic-label">favicon写真</div>
			<div class="" style="width:400px">
				<div style="" class="talkappi-upload" data-name="favicon_file_base64" data-label="<?php echo basename($post['favicon-file'])?>" data-type="img" data-ratio="1:1" data-url="<?php echo($post['favicon-file'])?>"></div>
			</div>
		</div>		
		<div class="category-separator">
			<div class="setting-header">ボタン設定</div>
		</div>
		<div class="lines-container">
			<label class="basic-label">文字色</label>
			<div class="">
				<div class="talkappi-colorpicker js-theme-color" data-name="button-font-color" data-value="<?php if ($post != NULL) echo ($post['button-font-color']) ?>"></div>
			</div>
		</div>
		<div class="lines-container">
			<label class="basic-label">背景色</label>
			<div class="">
				<div class="talkappi-colorpicker js-theme-color" data-name="button-bk-color" data-value="<?php if ($post != NULL) echo ($post['button-bk-color']) ?>"></div>
			</div>
		</div>
		<div class="form-group" style="display: none;">
			<label class="control-label col-md-3">ロゴradius</label>
			<div class="col-md-2">
				<input name="logo-radius" type="text" class="form-control" placeholder="0～100" value="<?php if ($post != NULL) echo ($post['logo-radius']) ?>">
			</div>
		</div>
		<div class="category-separator">
		<div class="setting-header">フォント設定</div>
		</div>
		<?php foreach ($_bot_lang as $k => $v) { ?>
			<div class="lines-container">
				<label class="basic-label"><?php echo ($v); ?>フォント</label>
				<input name="theme-font-family-<?php echo ($k) ?>" type="text" class="text-input-longer" value="<?php if (array_key_exists('theme-font-family-' . $k, $post)) echo (htmlspecialchars($post['theme-font-family-' . $k])) ?>">
			</div>
		<?php } ?>		
	</div>
	<div class="uf-inquiry-prev" style="display:none;">
		<div class="chatwindow-prev-contents">
			<div class="chatwindow-prev-header js-pc-prev">
				<div>
					<img style="margin: 0 10px 0px 16px" src="/assets/admin/css/img/icon-pc.svg" alt="">
					<span>プレビュー (ﾊﾟｿｺﾝ)</span>
				</div>
				<img class="prev-icon-switch js-toggle" src="/assets/admin/css/img/icon-sp.svg" alt="prev-toggle">
			</div>
			<div class="chatwindow-prev-header js-sp-prev" style="display:none">
				<div>
					<img style="margin: 0 10px 0px 16px" src="/assets/admin/css/img/icon-sp.svg" alt="">
					<span>プレビュー (ｽﾏﾎ)</span>
				</div>
				<img class="prev-icon-switch js-toggle" src="/assets/admin/css/img/icon-pc.svg" alt="prev-toggle">
			</div>
			<div class="chatwindow-prev-bot-footer">
				<div class="chatwindow-prev-bot-footer-top">
					<span>実際のUIはプレビューからご確認をお願いします</span>
				</div>

			</div>

		</div>
	</div>
</div>
