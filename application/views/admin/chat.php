<!DOCTYPE html>
<!--[if IE 8]> <html lang="en" class="ie8 no-js"> <![endif]-->
<!--[if IE 9]> <html lang="en" class="ie9 no-js"> <![endif]-->
<!--[if !IE]><!-->
<html>
<!--<![endif]-->
<!-- BEGIN HEAD -->
<head>
<meta charset="utf-8"/>
<title><?php echo $bot_name?>-<?php echo __('admin.chat.window.operator_respond') ?></title>
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<meta http-equiv="Content-type" content="text/html; charset=utf-8">
<meta content="" name="description"/>
<meta content="" name="author"/>
<!-- BEGIN GLOBAL MANDATORY STYLES -->
<link href="/assets/common/fontawesome/css/all.css" rel="stylesheet" type="text/css">
<link href="https://cdnjs.cloudflare.com/ajax/libs/simple-line-icons/2.4.1/css/simple-line-icons.min.css" rel="stylesheet" type="text/css">
<link href="<?php echo($_assets)?>global/plugins/bootstrap/css/bootstrap.min.css" rel="stylesheet" type="text/css">
<link href="<?php echo($_assets)?>global/plugins/uniform/css/uniform.default.css" rel="stylesheet" type="text/css">
<link href="<?php echo($_assets)?>global/plugins/bootstrap-switch/css/bootstrap-switch.min.css" rel="stylesheet" type="text/css"/>
<!-- END GLOBAL MANDATORY STYLES -->

<!-- BEGIN PLUGINS USED BY X-EDITABLE -->
<link rel="stylesheet" type="text/css" href="<?php echo($_assets)?>global/plugins/select2/select2.css"/>
<link rel="stylesheet" type="text/css" href="<?php echo($_assets)?>global/plugins/bootstrap-wysihtml5/bootstrap-wysihtml5.css"/>
<link rel="stylesheet" type="text/css" href="<?php echo($_assets)?>global/plugins/bootstrap-datepicker/css/datepicker.css"/>
<link rel="stylesheet" type="text/css" href="<?php echo($_assets)?>global/plugins/bootstrap-timepicker/css/bootstrap-timepicker.min.css"/>
<link rel="stylesheet" type="text/css" href="<?php echo($_assets)?>global/plugins/bootstrap-datetimepicker/css/bootstrap-datetimepicker.min.css"/>
<link rel="stylesheet" type="text/css" href="<?php echo($_assets)?>global/plugins/bootstrap-editable/bootstrap-editable/css/bootstrap-editable.css"/>
<link rel="stylesheet" type="text/css" href="<?php echo($_assets)?>global/plugins/bootstrap-editable/inputs-ext/address/address.css"/>
<!-- END PLUGINS USED BY X-EDITABLE -->

<link rel="stylesheet" type="text/css" href="<?php echo($_assets)?>global/plugins/select2/select2.css"/>
<link rel="stylesheet" type="text/css" href="<?php echo($_assets)?>global/plugins/datatables/plugins/bootstrap/dataTables.bootstrap.css"/>

<!-- BEGIN PAGE LEVEL STYLES -->
<link rel="stylesheet" type="text/css" href="<?php echo($_assets)?>global/plugins/bootstrap-select/bootstrap-select.min.css"/>
<link rel="stylesheet" type="text/css" href="<?php echo($_assets)?>global/plugins/jquery-multi-select/css/multi-select.css"/>
<link rel="stylesheet" type="text/css" href="<?php echo($_assets)?>global/plugins/clockface/css/clockface.css"/>
<link rel="stylesheet" type="text/css" href="<?php echo($_assets)?>global/plugins/bootstrap-datepicker/css/datepicker3.css"/>
<link rel="stylesheet" type="text/css" href="<?php echo($_assets)?>global/plugins/bootstrap-colorpicker/css/colorpicker.css"/>
<link rel="stylesheet" type="text/css" href="<?php echo($_assets)?>global/plugins/bootstrap-daterangepicker/daterangepicker-bs3.css"/>
<link rel="stylesheet" type="text/css" href="<?php echo($_assets)?>global/plugins/bootstrap-datetimepicker/css/bootstrap-datetimepicker.min.css"/>
<link rel="stylesheet" type="text/css" href="<?php echo($_assets)?>global/plugins/bootstrap-toastr/toastr.min.css"/>
<link href="/assets/global/plugins/month-picker/css/MonthPicker.min.css" rel="stylesheet" type="text/css" />
<!-- END PAGE LEVEL STYLES -->

<link href="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote.min.css" rel="stylesheet">

<!-- BEGIN THEME STYLES -->
<link href="<?php echo($_assets)?>global/css/components-rounded.css" id="style_components" rel="stylesheet" type="text/css"/>
<link href="<?php echo($_assets)?>global/css/plugins.css" rel="stylesheet" type="text/css"/>
<link href="<?php echo($_assets)?>admin/layout4/css/layout.css" rel="stylesheet" type="text/css"/>
<link id="style_color" href="<?php echo($_assets)?>admin/layout4/css/themes/light.css" rel="stylesheet" type="text/css"/>
<link href="<?php echo($_assets)?>admin/layout4/css/custom.css" rel="stylesheet" type="text/css"/>
<!-- END THEME STYLES -->

<link rel="stylesheet" type="text/css" href="/assets/common/talkappi-file-upload/css/talkappi-file-upload.css"/>
<link rel="stylesheet" type="text/css" href="/assets/common/talkappi-components/css/talkappi-components.css"/>
<link rel="stylesheet" type="text/css" href="/assets/common/talkappi-modal/css/talkappi-modal.css"/>
<link rel="stylesheet" type="text/css" href="/assets/common/talkappi-datetime-select/css/talkappi-datetime-select.css"/>
<link rel="stylesheet" type="text/css" href="/assets/common/talkappi-section/css/talkappi-section.css?v=<?php echo(time())?>"/>
<link rel="stylesheet" type="text/css" href="/assets/common/talkappi-category-select/css/talkappi-category-select.css"/>
<link rel="stylesheet" type="text/css" href="/assets/common/talkappi-skill-select/css/talkappi-skill-select.css"/>
<link rel="stylesheet" type="text/css" href="/assets/common/talkappi-preview/css/talkappi-preview.css"/>
<link rel="stylesheet" type="text/css" href="/assets/common/talkappi-very-preview/css/talkappi-very-preview.css"/>
<link rel="stylesheet" type="text/css" href="/assets/common/talkappi-jsoncheck/css/talkappi-jsoncheck.css?v=<?php echo(time())?>">

<link href="/assets/admin/css/chat.css?v=<?php echo(time())?>" rel="stylesheet" type="text/css"/>
<link href="/assets/admin/css/chat-v2.css?v=<?php echo(time())?>" rel="stylesheet" type="text/css"/>

<style>
a, a:hover, a:active, a:visited, a:focus {
    text-decoration:none;
}
.blue.btn {
    color: #FFFFFF;
    background-color: #aaa;
    border-color: "";
    margin:2px;
}

.row {
    margin-right: 0px;
}
.portlet {
	margin-bottom: 0px;
}
.portlet.light {
	padding:0px 0px 0px 14px;
}
.portlet > .portlet-title {
	margin-bottom:0px;
}
.portlet.light > .portlet-title > .caption {
	padding:15px 0 10px 10px
}
.chat-form {
	margin-top:5px;
}

</style>
<script type="text/javascript">
	const _message_list = <?php echo json_encode($msg_list, JSON_UNESCAPED_UNICODE)?>;
	const _admin_lang_cd = '<?php echo $_lang_cd ?>';
	const _admin_message = '';

	const _chat_enter_mode = <?php echo $enter_mode ?>;
</script>
</head>
<!-- END HEAD -->
<!-- BEGIN BODY -->
<!-- DOC: Apply "page-header-fixed-mobile" and "page-footer-fixed-mobile" class to body element to force fixed header or footer in mobile devices -->
<!-- DOC: Apply "page-sidebar-closed" class to the body and "page-sidebar-menu-closed" class to the sidebar menu element to hide the sidebar by default -->
<!-- DOC: Apply "page-sidebar-hide" class to the body to make the sidebar completely hidden on toggle -->
<!-- DOC: Apply "page-sidebar-closed-hide-logo" class to the body element to make the logo hidden on sidebar toggle -->
<!-- DOC: Apply "page-sidebar-hide" class to body element to completely hide the sidebar on sidebar toggle -->
<!-- DOC: Apply "page-sidebar-fixed" class to have fixed sidebar -->
<!-- DOC: Apply "page-footer-fixed" class to the body element to have fixed footer -->
<!-- DOC: Apply "page-sidebar-reversed" class to put the sidebar on the right side -->
<!-- DOC: Apply "page-full-width" class to the body element to have full width page without the sidebar menu -->
<body class="page-header-fixed page-sidebar-closed-hide-logo">
<audio id="audiomsg" src="/assets/chat/sound/s01.mp3"></audio>
<audio id="audiomsg2" src="/assets/chat/sound/s02.mp3"></audio>
<!-- BEGIN HEADER -->
<div class="page-header navbar navbar-fixed-top" id="chat-header">
	<!-- BEGIN HEADER INNER -->
	<div class="page-header-inner">
		<!-- BEGIN LOGO -->
		<div class="page-logo">
			<?php
			//$user = Session::instance()->get('user', NULL);
			if ($user->role_cd == '02') {
				echo('<a href="javascript:void(0);" style="display:flex; align-items:center">');
			}
			else {
				echo('<a href="/admin/top" style="display:flex; align-items:center">');
			}
			if ($logo != '') {
				echo('<img src="' . $logo . '" alt="logo" class="chat-logo-default"/>');
			} else {
				echo('<img src="/assets/common/images/logo-icon.png" alt="logo" class="chat-logo-default"/>');
			}
			echo('<h1>' . $bot->bot_name . '</h1></a>')
			?>
			<button class="filter-details-button sidebar-blue" style="<?php if ($chat_one != NULL) echo("display:none;");?>" data-toggle="modal" href="#basic">
				<?php echo __('admin.common.button.details') ?>
			</button>
		</div>
		<!-- END LOGO -->

		<div class="modal fade" id="basic" tabindex="-1" role="basic" aria-hidden="true">
			<div class="modal-dialog" style="max-width: 960px;">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
						<h4 class="modal-title"><?php echo __('admin.common.button.details') ?></h4>
					</div>
					<div class="modal-body" style="padding: 20px 40px;">
					<form action="/chat/setting" id="setting-form" class="form-horizontal" role="form" method="post">
						<div class="modal-form-container">
							<div class="modal-buttons-row">
								<label class="modal-title-group"><?php echo __('admin.common.label.lang') ?></label>
								<div class="modal-buttons-group" data-toggle="buttons">
								<?php
								foreach($bot_lang as $lang_cd=>$lang_name) {
									if (in_array($lang_cd, $user_chat_langs)) {
										echo('<label class="modal-button active">');
										echo('<input type="checkbox" name="langs[]" checked="true" class="toggle" value="' . $lang_cd . '" id="' . $lang_cd . '-lang"> ' . $lang_name . ' </label>');
									}
									else {
										echo('<label class="modal-button">');
										echo('<input type="checkbox" name="langs[]" class="toggle" value="' . $lang_cd . '" id="' . $lang_cd . '-lang"> ' . $lang_name . ' </label>');
									}
								}
								?>
								</div>
							</div>
							<div class="modal-buttons-row">
								<label class="modal-title-group"><?php echo __('admin.common.label.sns') ?></label>
								<div class="modal-buttons-group" data-toggle="buttons">
								<?php
									foreach($codes['08'] as $sns_cd=>$sns_name) {
										if (in_array($sns_cd, $user_snses)) {
											echo('<label class="modal-button active">');
											echo('<input type="checkbox" name="snses[]" checked="true" class="toggle" value="' . $sns_cd. '" id="' . $sns_cd. '"> ' . $sns_name. ' </label>');
										}
										else {
											echo('<label class="modal-button">');
											echo('<input type="checkbox" name="snses[]" class="toggle" value="' . $sns_cd. '" id="' . $sns_cd. '"> ' . $sns_name. ' </label>');
										}
									}
								?>
								</div>
							</div>
							<div class="modal-buttons-row">
								<label class="modal-title-group"><?php echo __('admin.chat.header.chat_date') ?></label>
								<div class="modal-buttons-group btn-group" data-toggle="buttons">
								<?php
									$days_src = '{';
									foreach($codes['09'] as $sns_cd=>$sns_name) {
										$days_src = $days_src . '"' . $sns_cd .'":"' . $sns_name . '"';
										if ($sns_cd != '10') {
											$days_src = $days_src . ',';
										}
									}
									$days_src = $days_src . '}';
								?>
								<div class="modal-buttons-group">
									<div class="talkappi-radio js-days" id="days" data-name="days" data-value="<?php echo $user_days ?>" data-source='<?php echo $days_src ?>'></div>
								</div>
								</div>
							</div>
							<?php if (count($user_bots) > 1) {?>
							<div class="modal-buttons-row">
								<label class="modal-title-group"><?php echo __('admin.chat.header.bot_target') ?></label>
								<div class="modal-buttons-group" data-toggle="buttons">
								<?php
									foreach($user_bots as $bot) {
										if (in_array($bot->bot_id, $chat_bots)) {
											echo('<label class="modal-button active">');
											echo('<input type="checkbox" name="bots[]" checked="true" class="toggle" value="' . $bot->bot_id . '"> ' . $bot->bot_name . ' </label>');
										}
										else {
											echo('<label class="modal-button">');
											echo('<input type="checkbox" name="bots[]" class="toggle" value="' . $bot->bot_id . '"> ' . $bot->bot_name . ' </label>');
										}
									}
								?>
								</div>
							</div>							
							<?php }?>
							
							<div class="modal-buttons-row" <?php if (count($scene_pattern) == 1) echo('style="display:none;"');?>>
								<label class="modal-title-group"><?php echo __('admin.chat.header.user_lead') ?></label>
								<div class="modal-buttons-group">
        						<?php echo Form::select('filter_scene', $scene_pattern, $filter_scene, array('id'=>'filter_scene','class'=>'form-control'))?>
								</div>
							</div>								
							<div class="modal-buttons-row">
								<label class="modal-title-group"><?php echo __('admin.chat.header.operator') ?></label>
								<div class="modal-buttons-group" data-toggle="buttons">
								<?php
									foreach($users as $it) {
										if ($select_users != NULL && in_array($it->user_id, $select_users)) {
											echo('<label class="modal-button active">');
											echo('<input type="checkbox" name="users[]" checked="true" class="toggle" value="' . $it->user_id . '"> ' . $it->name . ' </label>');
										}
										else {
											echo('<label class="modal-button">');
											echo('<input type="checkbox" name="users[]" class="toggle" value="' . $it->user_id . '"> ' . $it->name . ' </label>');
										}
									}
								?>
								</div>
							</div>
							<div class="modal-buttons-row">
								<label class="modal-title-group"><?php echo __('admin.itme.item.tag') ?></label>
								<div class="modal-buttons-group" data-toggle="buttons">
								<?php
									$all_tags = $tags;
									array_shift($all_tags);
									foreach($all_tags as $key=>$value) {
										if ($select_tags != NULL && in_array($key, $select_tags)) {
											echo('<label class="modal-button active">');
											echo('<input type="checkbox" name="tags[]" checked="true" class="toggle" value="' . $key . '"> ' . $value . ' </label>');
										}
										else {
											echo('<label class="modal-button">');
											echo('<input type="checkbox" name="tags[]" class="toggle" value="' . $key . '"> ' . $value . ' </label>');
										}
									}
								?>
								</div>
							</div>
							<div class="modal-buttons-row">
								<label class="modal-title-group"><?php echo __('admin.chat.header.username') ?></label>
								<div class="modal-buttons-group">
								<input type="text" class="form-control" name="filter_username"  maxlength="50" value="<?php echo($filter_username);?>" />
								</div>
							</div>														
							<div class="modal-buttons-row">
								<label class="modal-title-group"><?php echo __('admin.chat.header.display_range') ?></label>
								<div class="modal-buttons-group">
								<?php echo Form::select('chat_req_flg', $chat_req_flg_array, $chat_req_flg, array('id'=>'chat_req_flg','class'=>'form-control'))?>								
								</div>								
								<div class="col-md-2" style="display:none;">
								<input type="checkbox" name="chat_only" value="1" class="make-switch" <?php if ($chat_only==1) echo("checked") ?> data-on-color="danger" data-off-color="warning">
								</div>
							</div>
							<div class="modal-buttons-row">
								<label class="modal-title-group"><?php echo __('admin.chat.header.user_display') ?></label>
								<div class="modal-buttons-group">
									<div class="talkappi-radio js-sort_log_time" id="sort_log_time" data-name="sort_log_time" data-value="<?php echo $sort_log_time ?>" data-source='{"1":"<?php echo __('admin.chat.header.order_by_last_used') ?>", "0":"<?php echo __('admin.chat.header.order_by_registration') ?>"}'></div>
								</div>
							</div>							
							<div class="modal-buttons-row">
								<label class="modal-title-group"><?php echo __('admin.chat.header.history_delete') ?></label>
								<div class="modal-buttons-group">
									<div class="talkappi-radio js-modify_mode" id="modify_mode" data-name="modify_mode" data-value="<?php echo $modify_mode ?>" data-source='{"0":"<?php echo __('admin.common.label.not_allowed') ?>", "1":"<?php echo __('admin.common.label.allowed') ?>"}'></div>
								</div>
							</div>
							<div class="modal-buttons-row">
								<label class="modal-title-group"><?php echo __('admin.chat.header.information_masking') ?></label>
								<div class="modal-buttons-group">
									<div class="talkappi-radio js-mask_privacy" id="mask_privacy" data-name="mask_privacy" data-value="<?php echo $mask_privacy ?>" data-source='{"1":"<?php echo __('admin.chat.header.display_masking') ?>", "0":"<?php echo __('admin.common.label.display') ?>"}'></div>
								</div>
							</div>			
							<div class="modal-buttons-row">
								<label class="modal-title-group"><?php echo __('admin.chat.header.enter_key') ?></label>
								<div class="modal-buttons-group">
									<div class="talkappi-radio js-enter-mode" data-name="enter_mode" data-value="<?php echo $enter_mode ?>" data-source='{"1":"<?php echo __('admin.chat.header.enter_key_send') ?>", "2":"<?php echo __('admin.chat.header.enter_key_line') ?>"}'></div>
								</div>				
							</div>			
						</div>					
						<input type="hidden" name="token" value="<?php echo $token?>" />
						<div class="modal-footer">
							<button type="button" id="close-setting" class="btn default" data-dismiss="modal"><?php echo __('admin.common.button.cancel') ?></button>
							<button type="button" id="setting-button" class="btn blue active"><?php echo __('admin.common.button.save') ?></button>
						</div>
					</form>
					</div>
				</div>
				<!-- /.modal-content -->
			</div>
			<!-- /.modal-dialog -->
		</div>

		<!-- BEGIN PAGE TOP -->
		<div class="page-top">
			<!-- BEGIN TOP NAVIGATION MENU -->
			<div class="top-menu">
				<ul class="nav navbar-nav pull-right">
					<li class="separator hide">
					</li>
					<!-- BEGIN USER LOGIN DROPDOWN -->
					<!-- DOC: Apply "dropdown-dark" class after below "dropdown-extended" to change the dropdown styte -->
					<li class="dropdown dropdown-user dropdown-dark">
						<a href="#" class="dropdown-toggle" data-toggle="dropdown" data-hover="dropdown" data-close-others="true" style="padding: 0; margin-top: 25px;">
						<span class="username username-hide-on-mobile">
						<?php echo($user->name)?><?php if ($flg_operator_mode_control==1) {?><small> ・ </small><small class="username-status" id="user_status_show"><?php echo __('admin.chat.header.online') ?></small><?php }?> </span>
						<!-- DOC: Do not remove below empty space(&nbsp;) as its purposely used -->
						<img alt="" id="user_avatar" class="img-circle" src="/assets/common/images/avatar.jpg"/>
						</a>
						<ul id="user_status_menu" class="dropdown-menu dropdown-menu-default">
							<li>
								<a href="/chat/logout">
								<i class="icon-key"></i> <?php echo __('admin.template.menu.logout') ?></a>
							</li>
						</ul>
					</li>
					<!-- END USER LOGIN DROPDOWN -->
				</ul>
			</div>
			<!-- END TOP NAVIGATION MENU -->
		</div>
		<!-- END PAGE TOP -->
	</div>
	<!-- END HEADER INNER -->
	<!-- BEGIN FILTER -->
	<div class="header-filter-container" style="<?php if ($chat_one != NULL) echo("display:none;");?>" >
		<label class="header-filter-title"><?php echo __('admin.common.label.lang') ?></label>
		<?php
			foreach($bot_lang as $lang_cd=>$lang_name) {
				$button_styles = " filter-not-active";
				if (in_array($lang_cd, $user_chat_langs)) {
					$button_styles = " filter-active";
				}
				echo('<button class="header-filter-spacing' . $button_styles . '" id="filter-language" data-value="'. $lang_cd .'">' . $lang_name . '</button>');
			}
		?>
		<label class="header-filter-title"><?php echo __('admin.common.label.sns') ?></label>
		<?php
			foreach($codes['08'] as $sns_cd=>$sns_name) {
				$button_styles = " filter-not-active";
				if (in_array($sns_cd, $user_snses)) {
					$button_styles = " filter-active";
				}
				echo('<button class="header-filter-spacing' . $button_styles . '" id="filter-sns" data-value="' . $sns_cd . '">' . $sns_name . '</button>');
			}
		?>
		<label class="header-filter-title"><?php echo __('admin.common.label.period') ?></label>
		<select class="header-filter-condition header-filter-spacing" id="filter-date">
		<?php
			foreach($codes['09'] as $date_cd=>$date_name) {
				$selected_input = ($date_cd == $user_days) ? "selected" : "";
				echo('<option ' . $selected_input .' value="' . $date_cd . '">' . $date_name . '</option>');
			}
		?>
		</select>
		<div class="header-filter-spacing">
			<?php
				if ($chat_only == 1) echo('対応必要のみ表示');
			?>
		</div>
		<div class="" id="bot-chatmode" mode="<?php echo($flg_operator_mode_control) ?>">
		</div>
		<div id="token" style="display:none;"><?php echo $token?></div>
		<div id="showsetting" style="display:none;"><?php echo $showsetting?></div>
		<div id="admin_bot_id" style="display:none;"><?php echo $admin_bot_id?></div>
		<div class="" id="busitime">		
		</div>
	</div>
	<!-- END FILTER -->
</div>
<!-- END HEADER -->
<div class="clearfix">
</div>
<!-- BEGIN CONTAINER -->
<div class="page-container" style="padding: 0px;">
	<!-- BEGIN SIDEBAR -->
	<div class="page-sidebar-wrapper">
		<!-- DOC: Set data-auto-scroll="false" to disable the sidebar from auto scrolling/focusing -->
		<!-- DOC: Change data-auto-speed="200" to adjust the sub menu slide up/down speed -->
		<div class="chat-page-sidebar page-sidebar navbar-collapse" style="height: 648px;overflow-y: scroll;margin-bottom: 0;">
			<form action="/chat/searchmember" id="searchmember-form" class="form-horizontal" role="form" method="post">
			<div class="portlet-input input-inline" style="width:100%;<?php if ($chat_one != NULL) echo('display:none;');?>">
				<div class="input-icon right" style="padding:12px 0px 0px; margin:0px 12px">
					<i style="margin:10px 5px"><img src="/assets/admin/css/img/Search.svg" alt="search" class="sidebar-search-icon"/></i>
					<input type="hidden" name="token" value="<?php echo $token?>" />
					<input id="search-member" type="text" name="keyword" value="<?php echo $filter_member_no?>" class="form-control input-circle" placeholder="<?php echo __('admin.chatlist.user_search') ?>">
				</div>
			</div>
			</form>
			<!-- BEGIN SIDEBAR MENU -->
			<!-- DOC: Apply "page-sidebar-menu-light" class right after "page-sidebar-menu" to enable light sidebar menu style(without borders) -->
			<!-- DOC: Apply "page-sidebar-menu-hover-submenu" class right after "page-sidebar-menu" to enable hoverable(hover vs accordion) sub menu mode -->
			<!-- DOC: Apply "page-sidebar-menu-closed" class right after "page-sidebar-menu" to collapse("page-sidebar-closed" class must be applied to the body element) the sidebar sub menu mode -->
			<!-- DOC: Set data-auto-scroll="false" to disable the sidebar from auto scrolling/focusing -->
			<!-- DOC: Set data-keep-expand="true" to keep the submenues expanded -->
			<!-- DOC: Set data-auto-speed="200" to adjust the sub menu slide up/down speed -->
			<ul id="chat-list" log_time="<?php echo($log_time)?>" class="page-sidebar-menu " data-keep-expanded="false" data-auto-scroll="true" data-slide-speed="200">
				<?php echo($chatlist_view) ?>
			</ul>
			<!-- END SIDEBAR MENU -->
		</div>
	</div>
	<!-- END SIDEBAR -->
	<!-- BEGIN CONTENT -->
	<div class="page-content-wrapper">
		<div class="chat-page-content page-content" style="padding: 0px 0 0 0px;">
			<!-- /.modal -->
			<!-- END SAMPLE PORTLET CONFIGURATION MODAL FORM-->
			<div class="row">
                            <!-- BEGIN PORTLET-->
                            <div class="portlet light bordered">
                                <div class="portlet-title" id="member-chat-title">
                                    <div class="caption" style="display:flex; align-items:center; justify-content:space-between; color:#3D3F45; width: 100%; padding:15px 10px;">
										<div style="display:flex; align-items:center;">
											<i class="icon-bubble font-hide hide"></i>
											<p id="country" class="chat-header-title chat-header-spacing"></p>	
											<button style="display:none;" id="member-info-open" class="chat-header-button chat-header-spacing"><?php echo __('admin.chat.window.add_memo') ?></button>
											<p id="user_id" class="chat-header-text"></p>
										</div>
										<div>
											<p id="timezone" class="chat-header-text"></p>	
											<div class="caption">
											<?php
												foreach($msg_array as $msg_class_cd=>$msg_list) {
													if (!array_key_exists($msg_class_cd, $config_phrase[$bot->bot_class_cd])) continue;
													echo('<div class="btn-group" style="margin-left:5px;">');
													echo('<a href="#" class="dropdown-toggle" data-toggle="dropdown" data-hover="dropdown" data-close-others="true">');
													echo('<span class="badge badge-success">' . $config_phrase[$bot->bot_class_cd][$msg_class_cd] . '</span>');
													echo('</a>');
													echo('<ul class="dropdown-menu" role="menu">');
													foreach($msg_list as $msg_cd=>$msg) {
														if ($msg['msg_type_cd'] == 'txt') {
															$font = '<i class="fas fa-font"></i>';
															$content = $msg['content'];
														}
														else if ($msg['msg_type_cd'] == 'img') {
															$font = '<i class="far fa-image"></i>';
															$content = '';
														}
														else {
															$font = '<i class="far fa-window-maximize"></i>';
															$content = '';
														}
														echo('<li>');
														echo('<a href="#" class="msg_cd" msg_type="' . $msg['msg_type_cd'] . '" msg_id="' . $msg['msg_id'] . '" msg_cd="#' . $msg['msg_cd'] . '" msg_image="' . $msg['msg_image'] . '" content="' . $content. '">');
														echo($font . '  ');
														echo($msg['msg_name']);
														echo('</a>');
														echo('</li>');
													}
													echo('</ul>');
													echo('</div>');
												}
											?>
											</div>
										</div>
                                    </div>
                                    <div class="actions" style="display:none;">
                                        <div class="portlet-input input-inline">
                                            <div class="input-icon right">
                                                <i class="icon-magnifier"></i>
                                                <input type="text" class="form-control input-circle" placeholder="search..."> </div>
                                        </div>
                                    </div>
                                </div>
								<div id="currently-handling" style="display:none; background:#CFF2D7;">
								<div class="chat-statusbar-container">
									<div style="display:flex; align-items:center;">
										<div id="currently-handling-text"><?php echo __('admin.chat.window.operator_responding') ?></div>
										<button id="switch-mode-2" member_id="" class="input-button-right input-button-spacing" style="margin-left: 12px; display:flex; align-items:center;">
											<span style="padding-right: 5px;"><?php echo __('admin.chat.window.auto_translate') ?></span>
											<div class="switch-mode-2-label switch-label"><span class="switch-mode-2-label switch-span"></span></div>
										</button>
									</div>
									<button id="switch-mode-off" class="chat-status-button sidebar-green"><?php echo __('admin.common.button.end') ?></button>
								</div>	
                                </div>
                                <div class="portlet-title" id="member-info" style="display: none; margin-top:0px;background:#f0f0f0;">
                                    <div class="caption" id="member-info-input" style="display:flex; width:100%; justify-content:space-between; align-items:flex-start; padding:15px 10px;">
										<div class="form-group" style="display:flex; align-items:flex-start; flex:1; margin-bottom: 0;">
											<input name="name" id="name" type="text" class="form-control chat-memo-input chat-header-spacing" placeholder="<?php echo __('admin.common.label.name.human') ?>" value="">
											<?php echo Form::select('tag_1', $tags, '', array('class'=>'form-control chat-memo-input chat-header-spacing', 'id'=>'tag_1', 'width'=>'200px'))?> 
											<textarea id="remark" class="form-control chat-memo-textarea chat-header-spacing" rows="1" placeholder="<?php echo __('admin.chat.window.type_memo') ?>"></textarea>                	                                 
										</div>
										<div class="actions" id="member-info-action" style="flex:none;">
											<div class="portlet-input input-inline" style="display:flex;">
												<button type="button" id="save_member_tag" member_id="" class="chat-memo-button chat-header-spacing sidebar-blue"><?php echo __('admin.common.button.save') ?></button>
												<?php if ($support) {?>
												<button type="button" id="kintone" member_id="" class="chat-memo-button chat-memo-support-button chat-header-spacing"><?php echo __('admin.chat.window.send_support') ?></button>
												<?php }?>
												<button type="button" id="member-info-close" style="all:unset; cursor:pointer;"><img src="/assets/common/images/icon_close.svg"/></button>
											</div>
										</div>
                                    </div>
                                </div>
                                <div class="portlet-body" id="chats">
                                    <div id="chat-window" style="height: 480px;overflow-y: scroll;">
                                        <ul class="chats" id="chat-area">
											<?php echo($chatwindow_view) ?>
									    </ul>
                                    </div>
                                    <?php 
                                    $role_function = Session::instance()->get('role_function', NULL);
									$show_form = ($user->role_cd != '99' && !in_array('0201', $role_function)) ? "hide" : "show";
                                    ?>
									<input type="hidden" name="show_form" id="show_form" value="<?php echo($show_form)?>" />
									<div id="input-form-container" class="chat-form input-form-container" style="display:none; overflow: visible;">
										<textarea id="chat-message" class="form-control" rows="5" placeholder="<?php echo __('admin.chat.form.type_message') ?>"></textarea>
										<img id="uploadPreview" style="width:240px;display:none;" />
										<input type="file" name="upfile" id="upfile" style="display:none" onchange="filePreview(this)" />
										<div class="input-button-container">
											<div style="display:flex; align-items: center;" id="input-button-container-left">
												<div class="btn-group dropup" id="btn-pc">
													<button class="input-button-left" title="定型文送信" type="button" data-toggle="dropdown" id="btn-templates">
														<?php echo __('admin.chat.form.message_template') ?> <i class="fa fa-angle-up"></i>
													</button>
													<ul class="dropdown-menu quick-message" id="quick-message" role="menu">
														<p class="quick-message-title"><?php echo __('admin.chat.form.message_template_select') ?></p>
														<div class="quick-messages"></div>
													</ul>
												</div>
												<div class="input-button-left" id="btn-not-pc">
													<button style="padding:0;" id="btn-plus">
														<i class="fa fa-plus"></i>
													</button>
													<div id="plus-menu">
														<p class="plus-menu-item" data-menu="template"><?php echo __('admin.chat.form.message_template') ?> <i class="fa fa-angle-up"></i></p>
														<ul class="menu" data-menu-target="template-menu" style="overflow-y: auto; max-height: 200px;">
															<p class="quick-message-title"><?php echo __('admin.chat.form.message_template_select') ?></p>
															<div class="quick-messages"></div>
														</ul>
														<p class="plus-menu-item" data-menu="operator"><?php echo __('admin.chat.form.operator') ?> <i class="fa fa-angle-up"></i></p>
														<ul class="menu" data-menu-target="operator-menu">
															<li><?php echo __('admin.chat.form.operator_respond') ?><div class="talkappi-switch js-toggle-mode" data-name="toggle-mode" data-value="0"></div></li>
															<li class="unknown_chat"><?php echo __('admin.chat.form.ai_unknown_question') ?> <span><?php echo __('admin.chat.form.ai_unknown_question_none') ?></span></li>
														</ul>
													</div>
												</div>
												<div class="input-button-left">
													<a id="upload-btn" title="ファイル送信" member_id="">
														<i class="fa fa-file icon-white"></i>
													</a>
												</div>
											</div>

											<div style="display:flex; align-items:center" id="input-button-container-right">
												<button id="clear-btn" member_id="" class="input-button-right input-button-spacing"><img src="/assets/chat/images/icon_select_all.svg" alt="<?php echo __('admin.chat.form.respond_all') ?>" style="margin-right: 5px;" /><span><?php echo __('admin.chat.form.respond_all') ?></span></button>
												<button id="switch-mode-1" member_id="" class="input-button-right input-button-spacing"><span><?php echo __('admin.chat.window.operator_respond') ?></span></button>
												<select class="header-filter-condition header-filter-spacing input-button-spacing" id="send-option" style="display:none;">
													<option selected value="chat"><?php echo __('admin.common.label.chat') ?></option>
													<option value="mail"><?php echo __('admin.common.label.mail') ?></option>
												</select>
												<button id="chat-send" member_id="<?php if ($member!=NULL) echo $member->member_id?>" class="input-button-send sidebar-blue" sns_type_cd="<?php if ($member!=NULL) echo $member->sns_type_cd?>" lang_cd="<?php if ($member!=NULL) echo $member->lang_cd?>">
													<i class="fa fa-paper-plane icon-white fa-sm"></i>
												</button>
											</div>
										</div>
									</div>
								</div>
								<!-- END PORTLET-->
			</div>
		</div>
	</div>
	<!-- END CONTENT -->
</div>
<!-- END CONTAINER -->
</div>

<!-- REQUEST STATUS MODAL START -->
<div class="modal fade" id="request" tabindex="-1" role="request" aria-hidden="true">
	<div class="modal-dialog" style="max-width: 350px;">
		<div class="modal-content">
			<div class="modal-header request-modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
				<div>
					<h4><?php echo __('admin.servicetable.label.reception_id') ?><span class="request-modal-title"></span></h4>
					<h5 class="intent-title"></h5>
					<p class="intent-description"></p>
				</div>
			</div>
			<div class="modal-body" style="padding: 15px 20px 0px 20px;">
				<p class="request-header"><?php echo __('admin.service.label.status.change') ?></p>
				<div class="request-modal-form-container">
					<button data-status='01' type="button" class="btn request-btn request-red not-active"><?php echo __('admin.service.label.status.unhandled') ?></button>
					<button data-status='02' type="button" class="btn request-btn request-orange not-active"><?php echo __('admin.service.label.status.handling') ?></button>
					<button data-status='03' type="button" class="btn request-btn request-green not-active"><?php echo __('admin.service.label.status.completed') ?></button>
					<button data-status='04' type="button" class="btn request-btn request-gray not-active"><?php echo __('admin.service.label.status.cancelled') ?></button>
				</div>					
				<div class="modal-footer request-modal-footer" style="padding-left: 0; padding-right: 0">
					<button type="button" id="close-setting" class="btn default" data-dismiss="modal"><?php echo __('admin.common.button.cancel') ?></button>
					<button data-id='' data-status='' type="button" id="request-submit-button" class="btn blue active request-submit-button"><?php echo __('admin.common.button.save') ?></button>
				</div>
			</div>
		</div>
	</div>
</div>
<!-- REQUEST STATUS MODAL END -->

<!-- BEGIN FOOTER -->
<!-- 
<div class="page-footer">
	<div class="page-footer-inner">
		 2018-2019 &copy; Activated by Activalues.
	</div>
	<div class="scroll-to-top">
		<i class="icon-arrow-up"></i>
	</div>
</div>
 -->
<div class="modal fade" id="msg_preview" tabindex="-1" role="basic" aria-hidden="true">
	<div class="modal-dialog" id="msg_preview_box" style="max-width:960px;">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
				<h4 class="modal-title"><?php echo __('admin.chat.form.preview') ?></h4>
			</div>
			<div class="modal-body">
				<div id="msg_content">
				</div>
			</div>
			<div class="modal-footer">
					<button type="button" class="btn default" data-dismiss="modal"><?php echo __('admin.common.button.cancel') ?></button>
					<button type="submit" id="send_message" msg_cd="" class="btn green"><?php echo __('admin.common.button.send') ?></button>
			</div>
		</div>
	</div>
</div>


<!-- END FOOTER -->
<!-- BEGIN JAVASCRIPTS(Load javascripts at bottom, this will reduce page load time) -->
<!-- BEGIN CORE PLUGINS -->
<!--[if lt IE 9]>
<script src="/assets/global/plugins/respond.min.js"></script>
<script src="/assets/global/plugins/excanvas.min.js"></script>
<![endif]-->
<script src="<?php echo($_assets)?>global/plugins/jquery.min.js" type="text/javascript"></script>
<script src="<?php echo($_assets)?>global/plugins/jquery-migrate.min.js" type="text/javascript"></script>
<!-- IMPORTANT! Load jquery-ui-1.10.3.custom.min.js before bootstrap.min.js to fix bootstrap tooltip conflict with jquery ui tooltip -->
<script src="<?php echo($_assets)?>global/plugins/jquery-ui/jquery-ui-1.10.3.custom.min.js" type="text/javascript"></script>
<script src="<?php echo($_assets)?>global/plugins/bootstrap/js/bootstrap.min.js" type="text/javascript"></script>
<script src="<?php echo($_assets)?>global/plugins/bootstrap-hover-dropdown/bootstrap-hover-dropdown.min.js" type="text/javascript"></script>
<script src="<?php echo($_assets)?>global/plugins/jquery-slimscroll/jquery.slimscroll.min.js" type="text/javascript"></script>
<script src="<?php echo($_assets)?>global/plugins/jquery.blockui.min.js" type="text/javascript"></script>
<script src="<?php echo($_assets)?>global/plugins/jquery.cokie.min.js" type="text/javascript"></script>
<script src="<?php echo($_assets)?>global/plugins/uniform/jquery.uniform.min.js" type="text/javascript"></script>
<script src="<?php echo($_assets)?>global/plugins/bootstrap-switch/js/bootstrap-switch.min.js" type="text/javascript"></script>
<!-- END CORE PLUGINS -->

<!-- BEGIN PLUGINS USED BY X-EDITABLE -->
<script type="text/javascript" src="<?php echo($_assets)?>global/plugins/select2/select2.min.js"></script>
<script type="text/javascript" src="<?php echo($_assets)?>global/plugins/bootstrap-wysihtml5/wysihtml5-0.3.0.js"></script>
<script type="text/javascript" src="<?php echo($_assets)?>global/plugins/bootstrap-wysihtml5/bootstrap-wysihtml5.js"></script>
<script type="text/javascript" src="<?php echo($_assets)?>global/plugins/bootstrap-datepicker/js/bootstrap-datepicker.js"></script>
<script type="text/javascript" src="<?php echo($_assets)?>global/plugins/bootstrap-datepicker/js/locales/bootstrap-datepicker.zh-CN.js"></script>
<script type="text/javascript" src="<?php echo($_assets)?>global/plugins/bootstrap-datetimepicker/js/bootstrap-datetimepicker.min.js"></script>
<script type="text/javascript" src="<?php echo($_assets)?>global/plugins/moment.min.js"></script>
<script type="text/javascript" src="<?php echo($_assets)?>global/plugins/jquery.mockjax.js"></script>
<script type="text/javascript" src="<?php echo($_assets)?>global/plugins/bootstrap-editable/bootstrap-editable/js/bootstrap-editable.js"></script>
<script type="text/javascript" src="<?php echo($_assets)?>global/plugins/bootstrap-editable/inputs-ext/address/address.js"></script>
<script type="text/javascript" src="<?php echo($_assets)?>global/plugins/bootstrap-editable/inputs-ext/wysihtml5/wysihtml5.js"></script>
<!-- END X-EDITABLE PLUGIN -->

<!-- BEGIN PLUGINS USED BY  -->
<script type="text/javascript" src="<?php echo($_assets)?>global/plugins/select2/select2.min.js"></script>
<script type="text/javascript" src="<?php echo($_assets)?>global/plugins/datatables/media/js/jquery.dataTables.min.js"></script>
<script type="text/javascript" src="<?php echo($_assets)?>global/plugins/datatables/plugins/bootstrap/dataTables.bootstrap.js"></script>
<!-- END  PLUGIN -->

<script src="<?php echo($_assets)?>global/plugins/morris/morris.min.js" type="text/javascript"></script>
<script src="<?php echo($_assets)?>global/plugins/morris/raphael-min.js" type="text/javascript"></script>
<script src="<?php echo($_assets)?>global/plugins/jquery.sparkline.min.js" type="text/javascript"></script>

<!-- BEGIN PAGE LEVEL PLUGINS -->
<script type="text/javascript" src="<?php echo($_assets)?>global/plugins/bootstrap-datepicker/js/bootstrap-datepicker.js"></script>
<script type="text/javascript" src="<?php echo($_assets)?>global/plugins/bootstrap-timepicker/js/bootstrap-timepicker.min.js"></script>
<script type="text/javascript" src="<?php echo($_assets)?>global/plugins/clockface/js/clockface.js"></script>
<script type="text/javascript" src="<?php echo($_assets)?>global/plugins/bootstrap-daterangepicker/moment.min.js"></script>
<script type="text/javascript" src="<?php echo($_assets)?>global/plugins/bootstrap-daterangepicker/daterangepicker.js"></script>
<script type="text/javascript" src="<?php echo($_assets)?>global/plugins/bootstrap-colorpicker/js/bootstrap-colorpicker.js"></script>
<script type="text/javascript" src="<?php echo($_assets)?>global/plugins/bootstrap-datetimepicker/js/bootstrap-datetimepicker.min.js"></script>
<script type="text/javascript" src="<?php echo($_assets)?>global/plugins/bootstrap-toastr/toastr.min.js"></script>
<script type="text/javascript" src="<?php echo($_assets)?>global/plugins/bootstrap-select/bootstrap-select.min.js"></script>
<script type="text/javascript" src="<?php echo($_assets)?>global/plugins/jquery-multi-select/js/jquery.multi-select.js"></script>
<script type="text/javascript" src="<?php echo($_assets)?>global/plugins/jquery.disableAutoFill-master/src/jquery.disableAutoFill.min.js"></script>
<!-- END PAGE LEVEL PLUGINS -->
<script src="<?php echo($_assets)?>global/plugins/bootbox/bootbox.min.js" type="text/javascript"></script>

<script src="<?php echo($_assets)?>global/scripts/metronic.js" type="text/javascript"></script>
<script src="<?php echo($_assets)?>admin/layout4/scripts/layout.js" type="text/javascript"></script>
<script src="<?php echo($_assets)?>admin/layout4/scripts/demo.js" type="text/javascript"></script>
<script src="<?php echo($_assets)?>admin/pages/scripts/form-editable.js" type="text/javascript"></script>
<script src="<?php echo($_assets)?>admin/pages/scripts/table-managed.js" type="text/javascript"></script>
<script src="<?php echo($_assets)?>admin/pages/scripts/index3.js" type="text/javascript"></script>
<script src="<?php echo($_assets)?>admin/pages/scripts/components-pickers.js"></script>
<script src="<?php echo($_assets)?>admin/pages/scripts/ui-toastr.js"></script>

<script src="/assets/global/plugins/bootstrap-summernote/summernote.js" type="text/javascript"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/summernote/0.8.12/lang/summernote-ja-JP.js" type="text/javascript"></script>

<script src="/assets/common/talkappi-components/js/locales/talkappi-components-<?php echo $_lang_cd ?>.js"></script>
<script src="/assets/common/talkappi-components/js/talkappi-components.js"></script>
<script src="/assets/common/talkappi-file-upload/js/locales/talkappi-file-upload-<?php echo $_lang_cd ?>.js"></script>
<script src="/assets/common/talkappi-file-upload/js/talkappi-file-upload.js"></script>
<script src="/assets/common/talkappi-modal/js/locales/talkappi-modal-<?php echo $_lang_cd ?>.js"></script>
<script src="/assets/common/talkappi-modal/js/helper.js"></script>
<script src="/assets/common/talkappi-modal/js/talkappi-modal.js"></script>
<script src="/assets/common/talkappi-datetime-select/js/locales/talkappi-datetime-select-<?php echo $_lang_cd ?>.js"></script>
<script src="/assets/common/talkappi-datetime-select/js/talkappi-datetime-select.js"></script>
<script src="/assets/common/talkappi-section/js/locales/talkappi-section-<?php echo $_lang_cd ?>.js"></script>
<script src="/assets/common/talkappi-section/js/talkappi-section.js"></script>
<script src="/assets/common/talkappi-category-select/js/locales/talkappi-category-select-<?php echo $_lang_cd ?>.js"></script>
<script src="/assets/common/talkappi-category-select/js/talkappi-category-select.js"></script>
<script src="/assets/common/talkappi-skill-select/js/locales/talkappi-skill-select-<?php echo $_lang_cd ?>.js"></script>
<script src="/assets/common/talkappi-skill-select/js/talkappi-skill-select.js"></script>
<!-- <script src="/assets/common/talkappi-very-preview/js/locales/talkappi-very-preview-<?php echo $_lang_cd ?>.js"></script> -->
<script src="/assets/common/talkappi-preview/js/locales/talkappi-preview-<?php echo $_lang_cd ?>.js"></script>

<script src="/assets/common/talkappi-very-preview/js/talkappi-very-preview.js"></script>
<script src="/assets/common/talkappi-entry/js/talkappi-entry.js"></script>
<script src="/assets/common/talkappi-jsoncheck/js/locale/talkappi-jsoncheck-<?php echo $_lang_cd ?>.js"></script>
<script src="/assets/common/talkappi-jsoncheck/js/talkappi-jsoncheck.js"></script>

<!-- COMMON CAROUSEL COMPONENT -->
<link
  rel="stylesheet"
  href="/assets/common/talkappi-carousel/css/talkappi-carousel.css"
/>
<script src="https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.js"></script>
<script src="/assets/common/talkappi-carousel/js/talkappi-carousel.js"></script>

<script src="/assets/common/talkmgr.js"></script>
<script src="/assets/common/message/locales/admin-<?php echo $_lang_cd ?>.js"></script>

<script src="/assets/admin/chat.js"></script>
<script src="/assets/global/plugins/month-picker/js/MonthPicker.min.js"></script>

<!-- END JAVASCRIPTS -->
</body>
<!-- END BODY -->
</html>
