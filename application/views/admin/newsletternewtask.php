<input type="hidden" name="message" id="message" value="<?php echo($message)?>" />	

<script src="/assets/admin/newsletter-api.js"></script>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/1.12.4/jquery.min.js"></script>
<script type="text/javascript" src="/assets/common/react/pages/admin/signaturemodal.bundle.js"></script>

<script type="text/javascript">
	const mail_signature = <?php echo $mail_signature?>;
    const _signature_options = <?php echo ($signatureOptions) ?>;
    const _signatures_data = <?php echo ($signaturesData) ?>;
    const _lang_cd = '<?php echo $_lang_cd ?>';
    const _default_new_signature = '<?php echo $defaultNewSignature ?>';
    const _getDefaultSignatureId = function() {
        const defaultSignatureId = $('input[name="signature"]').val();
        return defaultSignatureId ? Number(defaultSignatureId) : 0; 
    };
    const _save_error_msg = '<?php echo __('admin.common.error.operation.save') ?>';

    const _afterSaveSignature = function(savedSignature) {
        if ( savedSignature.success ) {
            // update dropdown sign list and sign detail data set
            mail_signature[savedSignature.id] = savedSignature;
            const eSignaturePulldown = $('div.talkappi-pulldown[data-name="signature"]');
            let signaturePulldownSource = eSignaturePulldown.data( "source" );
            signaturePulldownSource[savedSignature.id] = savedSignature.sign_title;
            // setting signaturePulldownSource and re-render with talkappi component manager
            talkmgr.init( eSignaturePulldown, undefined, signaturePulldownSource );
        } else {
            alertmessage('error', TalkappiMessageAdmin.common.message.error, _save_error_msg);
        }
    };

    jQuery(document).ready(function($){
        window.talkappi_setupSignatureModal({
            signatureOptions: _signature_options,
            signaturesData: (_signatures_data),
            lang_cd: _lang_cd,
            defaultNewSignature: JSON.parse(_default_new_signature),
            getDefaultId: _getDefaultSignatureId,
            afterSave: _afterSaveSignature,
        });
    });

    const _project_id = <?php echo isset($project_id) && is_numeric($project_id) ? $project_id : 'null'; ?>;
    const _is_newsletter_project_enabled = <?php echo $is_newsletter_project_enabled ? 'true' : 'false'; ?>;
    const _all_extensions = <?php echo $extension_details ? json_encode($extension_details, JSON_UNESCAPED_UNICODE) : []; ?>;

</script>
<link rel="stylesheet" type="text/css" href="/assets/admin/css/newsletter.css"/>

<style>
    .csv_import_float_panel {
        /* CSV入力 */
        /* Auto layout */
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        padding: 24px;
        gap: 24px;

        position: relative;
        /* width: 854px; */
        height: 750px;
        /* pure white */
        background: #FFFFFF;
        border-radius: 4px;
    }
    .tags_container {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        padding: 0px;
        width: 588px;
    }
    .selected_tags {
        display: flex;
        gap: 8px;
        color: #A1A4AA;
        flex-wrap: wrap;
   }
    .tags_group {
        display: flex;
        padding: 0px 24px;
        gap: 12px;
        flex-wrap: wrap;
    }
    .cell_wrapper_flex {
        display: flex;
        flex-direction: row;
        align-items: center;
        flex: none;
    }
    .cell_column_diff {
        width:  auto;
        background: #FFFFFF;
    }

    .cell_column_del_diff {
        opacity: 0.2;
        border: none;
    }

    .cell_op {
        width: 35px;
        border: 1px solid #E3E5E8;
    }

    .btn_tag_w_ico {
        /* text */
        /* Auto layout */
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        padding: 1px 6px;
        gap: 2px;

        /* width: 50px; */
        height: 20px;
        /* pale grey */
        background: #EBEDF2;
        border-radius: 2px;
        /* Inside auto layout */
        flex: none;
        order: 0;
        flex-grow: 0;
    } 

    .ico-undo {
        background: url("/assets/admin/css/img/icon-undo-black.svg") no-repeat;
        height: 12px;
        width: 12px;
    }

    .ico-delete {
        background: url("/assets/admin/css/img/icon-delete.svg") no-repeat;
        height: 12px;
        width: 12px;
    }
    .tag_search {
        display: flex;
        gap: 10px;
    }

/* ------------------hidden box is above------------------------------------- */
    .form_container {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        padding: 0px;
        gap: 21px;
        width: 900px;
    }
    .form_sub_container {
        /* Frame 1281 */
        /* Auto layout */
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        padding: 0px;
        gap: 12px;
        width: 900px;
        flex: none;
        order: 0;
        align-self: stretch;
        flex-grow: 0;
    }
    .form_head {
        display: flex;
        padding: 12px 0px;
        align-self: stretch;
    }

    .head_sub_frame_start_shadow_diff {
        box-shadow: inset 0px 1px 0px #EBEDF2;
    }

    .head_sub_frame_space_between_diff {
        justify-content: space-between;
    }

    .txt_link {
        font-weight: 300;
        font-size: 12px;
        line-height: 18px;
        text-decoration-line: underline;
        color: #245BD6;
    }

    .head_sub_head {
        display: flex;
        flex-direction: row;
        align-items: flex-start;
        padding: 12px 346px 12px 0px;
        gap: 24px;
        background: #FFFFFF;
        box-shadow: inset 0px 1px 0px #EBEDF2;
    }
    .txt_head_title {
        font-weight: 500;
        font-size: 12px;
        line-height: 18px;
        color: #000000;
    }
    .txt_head_content_btn_desc {
        height: 18px;

        font-family: 'Hiragino Sans';
        font-style: normal;
        font-weight: 300;
        font-size: 12px;
        line-height: 18px;
        /* identical to box height */
        color: #000000;
        /* radio button active
        color: #FFFFFF */
        /* Inside auto layout */
        flex: none;
        flex-grow: 0;
    }
    .head_content {
        /* Frame 1280 */
        /* Auto layout */
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        padding: 0px 12px;
        gap: 16px;
    }
    .row_sub_frame {
        display: flex;
        flex-direction: row;
        align-items: center;
        /* align-items: flex-start; */
        padding: 0px;
        gap: 24px;
        position: relative;
        flex: none;
        order: 0;
        flex-grow: 0;
    }
    .label_bar_title {
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 5px 0px;
        gap: 12px;
    }
    .label_bar_title_2 {
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 0px;
        gap: 2px;
        width: 168px;
        flex: none;
        order: 0;
        flex-grow: 0;
    }
    .text_label_bar_title {
        font-weight: 300;
        font-size: 12px;
        line-height: 18px;
        /* identical to box height */
        /* pure black */
        color: #000000;
        /* Inside auto layout */
        flex: none;
        order: 0;
        flex-grow: 0;
        display: flex;
        align-items: center;
    }
    .row_sub_element_group_frame {
        /* Frame 1279 */
        /* Auto layout */
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 0px;
        gap: 12px;

        width: 355px;
        height: 28px;
        /* Frame 1288 
        align-items: flex-start;
        width: 299px; */
        /* Inside auto layout */
        flex: none;
        order: 1;
        flex-grow: 0;
    }
    .select_cell_drop_down {
        /* Cell/drop down */
        box-sizing: border-box;
        /* Auto layout */
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: flex-start;
        padding: 5px 8px;

        width: 192px;
        height: 28px;
        /* pure white */
        background: #FFFFFF;
        /* pale grey for line */
        border: 1px solid #E3E5E8;
        border-radius: 4px;
        /* Inside auto layout */
        flex: none;
        order: 1;
        flex-grow: 0;
    }
    .select_title_text {
        /* Frame 160 */
        /* Auto layout */
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 0px;
        gap: 12px;

        width: 176px;
        height: 18px;
        /* Inside auto layout */
        flex: none;
        order: 0;
        align-self: stretch;
        flex-grow: 0;
    }
    .select_cell_hint {
        /* テキスト */
        width: 152px;
        height: 18px;

        font-family: 'Hiragino Sans';
        font-style: normal;
        font-weight: 300;
        font-size: 12px;
        line-height: 18px;
        /* identical to box height */
        /* smoke grey */
        color: #A1A4AA;
        /* Inside auto layout */
        flex: none;
        order: 0;
        flex-grow: 1;
    }
    #track .icon-detail-box {
        background: lightpink;
    }
    .drop_down_cells {
        /* Cell/answer type */
        box-sizing: border-box;
        /* Auto layout */
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        padding: 0px;

        position: absolute;
        width: 314px;
        height: 128px;
        left: 228px;
        top: 113px;

        background: #FFFFFF;
        border: 1px solid #E3E5E8;
        box-shadow: 1px 2px 8px rgba(61, 63, 69, 0.240986);
        border-radius: 4px;
    }
    .frame_1294 {
        /* Frame 1294 */
        /* Auto layout */

        display: flex;
        flex-direction: row;
        align-items: flex-start;
        padding: 0px;
        gap: 12px;

        width: 432px;
        height: 28px;


        /* Inside auto layout */

        flex: none;
        order: 1;
        flex-grow: 0;
    }
    .input_text_tpl_name {
        /* input item for system */
        box-sizing: border-box;

        /* Auto layout */

        display: flex;
        flex-direction: row;
        align-items: flex-start;
        padding: 5px 12px;

        width: 228px;
        height: 28px;

        /* pure white */

        background: #FFFFFF;
        /* pale grey for line */

        border: 1px solid #E3E5E8;
        border-radius: 4px;
        flex: none;
        /* order: 1; */
        order: 2;
        flex-grow: 0;
    }
    .group_btn_label {
        /* Frame 1329 1331 */
        /* Auto layout */
        display: flex;
        flex-direction: row;
        align-items: center;
        /* align-items: flex-start; */
        padding: 0px;
        /* gap: 12px; */
        gap: 10px;

        width: 684px;
        /* width: 288px; */
        height: 28px;
        /* Inside auto layout */
        flex: none;
        order: 1;
        align-self: stretch;
        flex-grow: 0;
    }
    .btn_label_grey {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: 3px 12px;

        /* width: 102px; */
        height: 24px;

        /* pale grey */

        background: #EBEDF2;
        border-radius: 12px;

        /* Inside auto layout */

        flex: none;
        order: 0;
        flex-grow: 0;
    }
    textarea#mail_content.talkappi-textinput0 {
        height: 440px   !important;
    }
    .radio_btn_bar {
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        padding: 1px;

        background: #E3E5E8;
        border-radius: 100px;
        /* Inside auto layout */
        flex: none;
        order: 1;
        flex-grow: 0;
    }
    .input_item_calendar_time {
        /* input item for system */
        box-sizing: border-box;
        /* Auto layout */
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 5px 8px 5px 8px;
        height: 28px;
        /* pure white */
        background: #FFFFFF;
        /* pale grey for line */
        border: 1px solid #E3E5E8;
        border-radius: 4px;
        /* Inside auto layout */
        flex: none;
        order: 0;
        flex-grow: 0;
    }
    .icon-timer {
        background: url("/assets/admin/css/img/icon-timer.svg") no-repeat;
        height: 12px;
        width: 12px;
    }
    .btn_container {
        /* Frame 289 */
        /* Auto layout */
        display: flex;
        flex-direction: row;
        align-items: flex-start;
        padding: 0px;
        gap: 12px;

        /* position: absolute; */
        width: 378px;
        height: 40px;
    }
    .note-editor {
        margin: 0;
    }

    /* 今月の送信件数 */
    .quota-display {
        background-color: #f0f8ff;
        border: 2px solid #000;
        padding: 8px 12px;
        border-radius: 4px;
        color: #333;
    }

    /* react-admin-signature-modal in Signatures/index.css*/   

</style>
<!-- <input type="hidden" name="mail_title" id="mail_title" value="<?php echo($post['mail_title'])?>"> -->
<input type="hidden" name="apply_schedule" id="apply_schedule">
<input type="hidden" name="bot_id" id="bot_id" value="<?php echo($bot_id)?>">
<input type="hidden" name="mail_task_id" id="mail_task_id" value="<?php echo($post['mail_task_id'])?>">
<!-- <input type="hidden" name="email_template" id="email_template"> -->

<input type="hidden" name="alter_members" id="altmembers">
<input type="hidden" name="selected_members" id="sltmembers">

<!-- hidden/float panel BEGIN -->
<div class="modal fade" id="members_filter_box" tabindex="-1" role="basic" aria-hidden="true">
	<div class="modal-dialog" style="width: 860px;">
		<div class="modal-content">
			<div class="csv_import_float_panel">
                <div class="csv-modal-title" data-dismiss="modal" aria-hidden="true">
                    <div class="txt_title_float_panel">送信対象一覧</div>
					<img src="/assets/common/images/Icon-cancel_large.svg" class="closeImg" style="float:right;cursor:pointer;">
				</div>

                <div class="container_item_in_float_panel_frame" style="height: 366px;order: 4;">

                    <!-- Operation buttons -->
                    <div class="table_action_container  custom-data-table-header" style="padding-top: 10px;">
                        <div id="btnAddrSelect" class="btn_w_ico pointer restore vivid">
                            <i></i><?php echo __('admin.send.task.button.select_all') ?>
                        </div>
                        <!-- disabled -->
                        <div id="btnAddrExclude" class="btn_w_ico pointer delete pale_grey">
                            <i></i><?php echo __('admin.send.task.button.deselect_all') ?>
                        </div>
                    </div>
                    <!-- container_item_in_float_panel_frame height: 380px;  style="gap: normal;background: #F6F7F9;border: 1px solid #E3E5E8;"-->
                    <table id="table1members" <?php if ( isset($members_extend_columns) && $members_extend_columns ) {echo HTML::attributes(['data-extensions'=>json_encode($members_extend_columns)]);} ?> class="table table-striped table-bordered table-hover js-data-table1 mail_address_table">
                        <thead>
                            <tr>
                                <th style="width: 250px;">
                                    <!-- txt_title_name_upload_label_count_float -->
                                    <?php echo __('admin.common.label.mail.address') ?>
                                </th>
                                <th style="width: 100px;">
                                    <?php echo __('admin.common.label.name.human') ?>
                                </th>

                                <?php 
                                //  extended columns
                                    if ( isset($members_extend_columns) && $members_extend_columns ) {
                                        foreach($members_extend_columns as $extend_no=>$extend_name) {

                                            echo('
                                <th style="width: 100px;" extend_no="'.$extend_no.'">'
                                    .$extend_name.
                                '</th>');
                                        }
                                    }
                                ?>

                                <th style="width: 100px;">
                                    <?php echo __('admin.send.addr.th.regist_date') ?>
                                </th>
                                <th>
                                    <?php echo __('admin.send.addr.th.tag') ?>
                                </th>
                                <!-- padding: 13px 20px 13px 12px;width: 48px; justify-content: center;align-items: center;削除-->
                                <th style="width: 35px;">
                                    <?php echo __('admin.common.button.exclude') ?>
                                </th>
                            </tr>
                        </thead>
                        <tbody id="preview_members" >
                            <?php
                                foreach($member_options as $member) {   //  $index => 
                                    if ($member['selected']) {
                                        $op_icon = 'ico-delete';
                                        $cell_column_del_diff = '';
                                    } else {
                                        $op_icon = 'ico-undo';
                                        $cell_column_del_diff = ' cell_column_del_diff';
                                    }
                                    $htmlTags = '';
                                    if ( isset($member['tags']) ) {
                                        foreach($member['tags'] as $tag) {
                                            $htmlTags .= '<div class="tag_basic txt_column_head_content inline" style="align-items: center;display: flex;">'.$tag.'</div>';
                                        }
                                        $htmlTags = '<div class="txt_column_head_content tags" style="display: flex;gap: 6px;">'.$htmlTags.'</div>';
                                    }

                                    //extend_attributes
                                    $extend_attributes_html = '';
                                    if ( isset($members_extend_columns) && $members_extend_columns ) {
                                        foreach($members_extend_columns as $extend_no=>$extend_name) {
                                            $attribute = '';
                                            if( isset($members_extends[$member['mail_member_id']])) {
                                                $members_extend_data = $members_extends[$member['mail_member_id']];
                                                if( isset($members_extend_data[$extend_no]) ) {
                                                    $attribute = $members_extend_data[$extend_no];
                                                }
                                            }
                                            $extend_attributes_html .= 
                                        '<td class="cell_column_head_frame extend'.$cell_column_del_diff.'" style="background: #FFFFFF;">
                                            <div class="txt_column_head_content">'.$attribute.'</div>
                                        </td>';
                                        }
                                    }

                                    $trHtml = '
                                    <tr id="member_row-'.$member['mail_member_id'].'" class="" style="order: 1;">
                                        <td class=" cell_column_diff'.$cell_column_del_diff.'" style="background: #FFFFFF;">
                                            <div class="txt_column_head_content">'.$member['email'].'</div>
                                        </td>
                                        <td class=" cell_column_diff'.$cell_column_del_diff.'" style="background: #FFFFFF;">
                                            <div class="txt_column_head_content">'.$member['first_name'].$member['last_name'].'</div>
                                        </td>';

                                    $trHtml .= $extend_attributes_html;
                                    $trHtml .= 
                                        '<td class=" cell_column_diff'.$cell_column_del_diff.'" style="background: #FFFFFF;">
                                            <div class="txt_column_head_content">'.date('Y/m/d', strtotime($member['regist_time'])).'</div>   
                                        </td>
                                        <td class=" cell_column_diff'.$cell_column_del_diff.'" style="background: #FFFFFF;">'.$htmlTags.'
                                        </td>   
                                        <td mail_member_id='.$member['mail_member_id'].' class=" cell_column_diff pointer cell_op">
                                            <div class="cell_wrapper_flex" style="padding: 5px 0px 0px 8px;">
                                                <span class="'.$op_icon.'"></span>
                                            </div>
                                        </td>            
                                    </tr>';
                                    echo($trHtml);   
                                    // echo($trHtml);      
                                    // echo($trHtml);                    
                                }
                                ?>
                                <!-- PHP loop echo END -->
                            <!-- </div> -->

                        </tbody>
                    </table>
                    <div class="group_btn_large_general_frame">
                        <div id = "btnApplyMemberSelectionAlter" class="btn_large_blue"><div class="txt_btn_large txt_btn_large_highlight_diff">OK</div></div>
                        <div id = "cancelalter" class="btn_large_blue btn_white_diff"><div class="txt_btn_large"><?php echo __('admin.common.button.cancel') ?></div></div>
                    </div>
				</div>
            </div>
        </div>
    </div>
</div>


<div class="modal fade" id="filter_on_tags_box" tabindex="-1" role="basic" aria-hidden="true">
	<div class="modal-dialog">
		<div id="container1" class="modal-content modal_content_medium_float_panel" style="width: 660px;">
            <!-- head or title -->
            <div class="csv-modal-title" data-dismiss="modal" aria-hidden="true">
                <div class="txt_count_title_big_float"><?php echo __('admin.send.task.title.tag_filter') ?></div>
                <img src="/assets/common/images/Icon-cancel_large.svg" class="closeImg" style="float:right;cursor:pointer;">
            </div>

            <!-- contents -->
            <div class="tags_container">
                <!-- upper half -->
                <div class="container_item_in_float_panel_frame rows_flex top-shadow" style="padding: 16px 12px 32px;gap: 16px;">
                    <div class="tag_search">
                        <span class="icon-search"></span>
                        <div class="input_bar_text">タグ検索</div>
                    </div>
                    <div class="tags_group">
                        <?php
                            if ( isset($post['tags_filter']) && $post['tags_filter'] != '' ) {
                                // echo 'tags filter="'.$post['tags_filter'];
                                $tags_filter = json_decode($post['tags_filter']);                               
                            }
                            $css_selected = '';
                            foreach($tag_member_counts as $tag_no=>$tag_member_count) {
                                $css_selected = '';
                                if (isset($post['tags_filter']) && $post['tags_filter'] != '' && in_array($tag_no, $tags_filter) ) {
                                    $css_selected = ' active';
                                }
                                echo('<div tag_no="'.$tag_no.'" class="row_tag_opt"><span class="icon-check'.$css_selected.'"></span><div class="txt_tag_gray">'.$tag_member_count['tag_name'].'('.$tag_member_count['count_members'].')</div></div>');                    
                            }
                        ?>
                        
                    </div>
                    <!-- tag check box container -->
                </div>
                <!-- lower half -->
                <div class="container_item_in_float_panel_frame rows_flex" style="padding: 0px 0px 12px;">
                    <div class="txt_count_title_big_float">
                        <?php 
                            if (!isset($tag_filtered_members_count)) {
                                $tag_filtered_members_count = $selected_member_count;
                            }
                            echo str_replace( "{target_count}", ' <span class="selected_count">'.$tag_filtered_members_count.'</span> ', __('admin.send.task.count.context') );
                        ?>
                    </div>

                    <div class="container_item_in_float_panel_frame rows_flex">
                        <div class="txt_column_head_content"><?php echo __('admin.send.task.label.tag_filter') ?></div>
                        <div id="filter_tag" class="selected_tags" placeholder="<?php echo __('admin.send.task.input.hint.tag_filter') ?>" >
                            <!-- placeholder="タグを選択して絞り込み" -->
                        <?php
                            if ( isset($post['tags_filter']) && $post['tags_filter'] != '' ) {
                                // echo 'tags filter="'.$post['tags_filter'];
                                $tags_filter = json_decode($post['tags_filter']);
                                foreach($tags_filter as $tag_no) {
                                    if( isset($tag_member_counts[$tag_no]) ) {                                        
                                        $tag_member_count = $tag_member_counts[$tag_no];

                                        echo '<span tag_no="'.$tag_no.'" class="btn_tag_w_ico cancel round">
                                            <span class="txt_tag_gray">'.$tag_member_count['tag_name'].'('.$tag_member_count['count_members'].')</span>
                                            <span class="icon-cancel pointer"></span>
                                        </span>';
                                    } else {
                                        // hide deleted/detached tags
                                    }
                                    // echo 'tag "'.$tag_no.' count'.$tag_member_count;
                                   
                                }
                            } else echo __('admin.send.task.input.hint.tag_filter');
                        ?>
                        </div>
                    </div>
                    
                </div>
            </div>

            <!-- foot or large general(OK/Cancel) button groups -->
            <div class="group_btn_large_general_frame">
                <div id = "btnApplyTagFilter" class="btn_large_blue"><div class="txt_btn_large_highlight">OK</div></div>
                <div id = "btnCancelTagFilter" class="btn_large_blue btn_white_diff"><div class="txt_btn_large"><?php echo __('admin.common.button.cancel') ?></div></div>
            </div>
        </div>
    </div>
</div>
<!-- hidden/float panel END -->

<div class="content-container white" style="display: flex;flex-direction: column;gap: 60px;align-items: start;">
    <div class="form_container" style="padding-left: 15px;">

        <!-- batch タスク設定 -->
        <div class="form_sub_container">
            <div class="form_head">
                <div class="txt_head_title">基本設定</div>
            </div>
            <div class="head_content" style="flex-direction: row; width: 900px;">
                <div class="row_sub_frame">
                    <div class="label_bar_title">
                        <div class="label_bar_title_2">
                            <div class="text_label_bar_title"><?php echo __('admin.send.task.label.name') ?></div>
                        </div>
                    </div>
                </div>

                <!-- text input -->
                <div class="col-md-5" style="width: 100%;">
                    <input type="text" name="mail_task_name" value="<?php if ( $post != NULL && isset($post['mail_task_name']) ) echo($post['mail_task_name'])?>" class="form-control talkappi-textinput" data-max-input="200" placeholder="" />
                </div>
            </div>
            <div id="track" class="head_content" style="flex-direction: row; gap: 24px;">
                <div class="row_sub_frame">
                    <div class="label_bar_title">
                        <div class="label_bar_title_2">
                            <div class="text_label_bar_title"><?php echo __('admin.send.task.label.aws_track')?>
                                <span class="icon-detail" style="margin: 0 0 0 0.5rem;" title="<?php echo __('admin.send.task.label.aws_track_hint') ?>"></span>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- radio button -->
                <div class="talkappi-radio" data-name="is_track_event" data-value='<?php echo $post['is_track_event']?>' data-source='{"0":"<?php echo __('admin.inquiry.label.support_flg_no'); ?>", "1":"<?php echo __('admin.inquiry.label.answer_limit_yes'); ?>"}'></div>
            </div>
        </div>

        <div class="form_sub_container">
            <div class="form_head head_sub_frame_start_shadow_diff">
                <div class="txt_head_title"><?php echo __('admin.send.task.label.from_settings'); ?></div>
            </div>
            <div id="sender" class="head_content" style="flex-direction: row; width: 900px;height: 36px;">
                <div class="row_sub_frame">
                    <div class="label_bar_title">
                        <div class="label_bar_title_2">
                            <div class="text_label_bar_title"><?php echo __('admin.inquiry.label.member_mail_from')?>
                                <span class="icon-detail" style="margin: 0 0 0 0.5rem;display:inline-block;" title="<?php echo __('admin.inquiry.label.member_mail_form_hint') ?>"></span>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- text input -->
                <div class="col-md-5" style="width: 100%;">
                    <input type="text" name="sender" value="<?php if ( $post != NULL && isset($post['sender']) ) echo($post['sender']) ?>" class="form-control talkappi-textinput" data-max-input="200" placeholder= "<?php echo __('admin.inquiry.label.member_mail_from_placeholder')?>" <?php if(!$is_sender_address_customizable) echo 'disabled' ?>/>
                </div>
            </div>
            <div id="sender_display_name" class="head_content" style="flex-direction: row; width: 900px;height: 36px;">
                <div class="row_sub_frame">
                    <div class="label_bar_title">
                        <div class="label_bar_title_2">
                            <div class="text_label_bar_title"><?php echo __('admin.send.task.label.sender_display_name')?>
                                <span class="icon-detail" style="margin: 0 0 0 0.5rem;display:inline-block;" title="<?php echo __('admin.send.task.label.sender_display_name_hint') ?>"></span>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- text input -->
                <div class="col-md-5" style="width: 100%;">
                    <input type="text" name="sender_name" value="<?php if ( $post != NULL && isset($post['sender_name']) ) echo($post['sender_name']) ?>" class="form-control talkappi-textinput" data-max-input="200" placeholder="" />
                </div>
            </div>
            <div id="member_mail_replyto" class="head_content" style="flex-direction: row; width: 900px;height: 36px;">
                <div class="row_sub_frame">
                    <div class="label_bar_title">
                        <div class="label_bar_title_2">
                            <div class="text_label_bar_title"><?php echo __('admin.inquiry.label.member_mail_replyto')?>
                                <span class="icon-detail" style="margin: 0 0 0 0.5rem;display:inline-block;" title="<?php echo __('admin.inquiry.label.member_mail_replyto_hint') ?>"></span>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- text input -->
                <div class="col-md-5" style="width: 100%;">
                    <input type="text" name="reply_to" value="<?php if ( $post != NULL && isset($post['reply_to']) ) echo($post['reply_to']) ?>" class="form-control talkappi-textinput" data-max-input="200" placeholder= "<?php echo __('admin.inquiry.label.member_mail_replyto_placeholder')?>" />
                </div>
            </div>
        </div>

        <div class="form_sub_container">
            <div class="form_head head_sub_frame_start_shadow_diff head_sub_frame_space_between_diff">
                <div class="txt_head_title">送信先設定</div>
                <!-- $sending_target_count
                 -->
                <div id="filter_members" class="txt_link pointer">送信対象一覧・編集</div>
            </div> 
            <div class="head_content">
                <div class="row_sub_frame" <?php if(!$is_parent_bot) echo __(' style="display:none;"')?>>
                    <div class="label_bar_title">
                        <div class="label_bar_title_2">
                            <div class="text_label_bar_title"><?php echo __('admin.send.task.label.object') ?></div>
                        </div>
                    </div>
                    <!-- 送信先を選択/CSVから導入 removed from here -->
                    <div class="group_btn_label">
                        <div class="btn_label_grey">
                            <div class="txt_head_content_btn_desc"><?php echo $bots[$bot_id] ?></div>
                        </div>
                        <div class="btn_label_w_ico">
                            <span class="icon-add"></span>
                            <div class="txt_head_content_btn_desc">施設選択</div>
                        </div>
                    </div>
                </div>

                <!-- 送信対象: 案件 -->
                <div class="row_sub_frame" <?php if(!$project_id) echo __(' style="display:none;"')?>>
                    <div class="label_bar_title">
                        <div class="label_bar_title_2">
                            <div class="text_label_bar_title"><?php echo __('admin.newsletterproject.label.name') ?></div>
                        </div>
                    </div>
                    <span><?php if($project_id) echo $project_name?></span>
                </div>

                <div class="row_sub_frame">
                    <div class="label_bar_title">
                        <div class="label_bar_title_2">
                            <!-- ユーザータグ -->
                            <div class="text_label_bar_title"><?php echo __('admin.common.label.mail.destination') ?></div>
                        </div>
                        
                    </div>
                    <div class="group_btn_label">
                        <input type="hidden" name="tags_filter" id="iTagsFilter" value='<?php echo isset($post['tags_filter']) ? $post['tags_filter'] : ''?>'>
                        <?php 
                            // echo json_encode($tag_member_counts);
                            if ( isset($post['tags_filter']) && $post['tags_filter'] != '' ) {
                                $tags_filter = json_decode($post['tags_filter']);
                                foreach($tags_filter as $tag_no) {
                                    if( isset($tag_member_counts[$tag_no]) ) {
                                        $tag_member_count = $tag_member_counts[$tag_no];
                                        echo '<div class="btn_label_round"><div class="txt_head_content_btn_desc">'. 
                                        $tag_member_count['tag_name'].'('.$tag_member_count['count_members'].')</div></div>';
                                    } else {
                                        // hide deleted/detached tags
                                    }
                                }
                            }
                            
                        ?>
                        <div id="btnTagsFilter" class="btn_label_w_ico">
                            <span class="icon-add"></span>
                            <!-- ユーザータグで追加 -->
                            <div class="txt_head_content_btn_desc"><?php echo __('admin.send.task.title.tag_filter') ?></div>
                        </div>
                    </div>
                </div>

                <div class="row_sub_frame" style="padding: 0px 0px 0px 192px;">
                 <!-- $sending_target_count 合計 389 名（除外2名）-->
                    <div id="desc0" class="txt_head_content_btn_desc targetcount" data-selected="<?php echo($selected_member_count)?>" data-excluded="<?php echo($excluded_member_count)?>">
                        <input type="hidden" name="excluded_count" id="iCountExcluded" value='<?php echo $post['excluded_count'] ?>'>

                        <?php 
                            echo str_replace( "{target_count}", ' <span class="selected_count txt_link pointer">'.$selected_member_count.'</span> ', __('admin.send.task.count.context') ) ; 
                            if ($excluded_member_count > 0) 
                                echo '<span>'.str_replace( "{exclude_count}", '<span class="excluded_count">'.$excluded_member_count.'</span>', __('admin.send.task.count.context.exclude') ).'</span>' ;
                            else
                                echo '<span style="display:none;">'.str_replace( "{exclude_count}", '<span class="excluded_count">'.$excluded_member_count.'</span>', __('admin.send.task.count.context.exclude') ).'</span>' ;

                        ?>
                    </div>
                </div>
            </div>
        </div>
        <div class="form_sub_container" style="gap:16px;">
            <div class="head_sub_head" style="align-self: stretch;">
                <div class="txt_head_title">配信内容設定</div>
            </div>
            <div class="head_content">
                <!-- TODO: 未実装の機能は非表示にする: テンプレート -->
                <!-- <div class="row_sub_frame" style="align-items: flex-start;width: 384px;">
                    <div class="label_bar_title">
                        <div class="label_bar_title_2">
                            <div class="text_label_bar_title">テンプレート</div>
                        </div>
                    </div>
                    <div class="select_cell_drop_down">
                        <div class="select_title_text">
                            <div class="select_cell_hint">お選んでください</div>
                            <span class="drop-down-close-icon"></span>
                        </div>
                    </div>
                     , 'name'=>'email_template'
                    <div class="frame_1294">
                        <?php echo Form::select('mail_template', $member_mail_template_list, NULL, array('id'=>'msg_cd', 'class'=>'form-control msg_cd',
                            'style' => 'background: #FFFFFF;'))
                        ?>
                        <div class="input_text_tpl_name">
                            <div class="select_cell_hint">テンプレート名を入力</div>
                        </div>
                    </div>
                </div> -->
                <div class="row_sub_frame" style="align-items: flex-start;align-self: stretch;">
                    <div class="label_bar_title">
                        <div class="label_bar_title_2">
                            <div class="text_label_bar_title"><?php echo __('admin.common.label.mail_title')?>
                                <span class="icon-detail" style="margin: 0 0 0 0.5rem;" title="<?php echo __('admin.send.task.label.tpl_usage_hint') ?>"></span>
                            </div>
                        </div>
                    </div>
                    <!--  value="<?php echo($post['mail_title'])?>" -->
                    <textarea type="text" class="form-control talkappi-textinput" name="mail_title"
                        data-max-input="300" placeholder="件名を入力" id="mail_title"
                        style="min-height:74px;height:74px;overflow-y:hidden;" autocomplete="off"><?php echo($post['mail_title'])?></textarea>
                    <!-- <input type="hidden" name="mail_title" id="mail_title" value="<?php echo($post['mail_title'])?>"> -->

                </div>
                <div class="row_sub_frame" style="align-items: flex-start;width: 876px;">
                    <div class="label_bar_title">
                        <div class="label_bar_title_2">
                            <div class="text_label_bar_title"><?php echo __('admin.common.label.body')?>
                                <span class="icon-detail" style="margin: 0 0 0 0.5rem;" title="<?php echo __('admin.send.task.label.tpl_usage_hint') ?>"></span>
                            </div>
                        </div>
                    </div>
                    <!-- data-max-input="999999" rows="24" style=""-->
                    <div id="summernote" class="summernote-edit js-item-description" data-edit="summer" id="mail_content" data-name="mail_content" data-value='<?php echo(htmlspecialchars($post['mail_content'] ?? ''))?>' data-upload-image="1" data-position="bottom"></div>
                </div>
                <div class="row_sub_frame" style="width: 100%;">
                    <div class="label_bar_title" style="width: 168px;">
                    <?php echo __('admin.send.label.signature')?>
                    </div>                     
                    <div style="width: 600px;" class="talkappi-pulldown" data-name="signature" data-value="0" data-source='<?php echo htmlspecialchars($transformedSignatureData) ?>'></div>
                    <div class="react-admin-signature-modal"></div>
                </div>
                <div class="row_sub_frame">
                    <div class="label_bar_title">
                        <div class="label_bar_title_2">
                            <div class="text_label_bar_title">配信解除</div>
                        </div>
                    </div>
                    <div class="talkappi-radio radio_btn_bar js-unsubscribe_enable"
                        data-name="unsubscribe_enable"
                        data-value="<?php if (isset($post) && is_array($post)) { echo array_key_exists('unsubscribe_enable', $post) ? ($post['unsubscribe_enable'] === 'NULL' ? '0' : $post['unsubscribe_enable']) : '0'; } else { echo '0'; }?>"
                        data-source='{"0":"OFF", "1":"ON"}'></div> 
                </div>
            </div>
        </div>
        <div class="content-container form_sub_container" style="gap:16px;box-shadow: inset 0px 1px 0px #EBEDF2">

            <div class="head_sub_head">
                <div class="txt_head_title">配信設定</div>
            </div>

            <div class="head_content">
                <!-- 今月の送信件数 -->
                <?php if (isset($send_quota)): ?>
                    <div class="row_sub_frame">
                        <div class="quota-display">
                            <p>
                                <?php 
                                    echo sprintf(
                                        __('admin.newsletternewtask.quota_display.message'), 
                                        $current_sent, 
                                        $send_quota, 
                                        $remaining_quota
                                    ); 
                                ?>
                            </p>
                        </div>
                    </div>
                <?php endif; ?>

                <div class="row_sub_frame">
                    <div class="label_bar_title">
                        <div class="label_bar_title_2">
                            <div class="text_label_bar_title"><?php echo __('admin.send.label.timing')?></div>
                            <!-- <p class="readonly-input-title text_label_bar_title"></p> -->
                            <!-- 配信方法(日時) -->
                        </div>
                    </div>
                        
                    <div class="talkappi-radio radio_btn_bar js-task-date" data-name="task_date_cd" data-value="<?php echo($send_date_setting_code)?>" data-source='<?php echo json_encode($_codes['282']) ?>'></div> 


                </div>
                <div id="senddateplan" class="row_sub_frame" style="<?php echo($hide_send_date_picker_switcher[$send_date_setting_code])?>">
                    <div class="label_bar_title">
                        <div class="label_bar_title_2">
                            <!-- 配信予定日時 -->
                            <div class="text_label_bar_title"><?php echo __('admin.send.label.date.plan')?></div>
                        </div>
                    </div>
                    <div class="row_sub_element_group_frame">
                        <!-- <div class="input_item_calendar_time">
                            -- <?php echo __('admin.maximumcalendar.large_view') ?> --
                            <?php if ( isset($post['start_date']) ) echo($post['start_date'])?>
                        </div> -->
                        <input name="send_date" id="send_date" value="<?php if ($post != NULL) echo($post['send_date'])?>" style="float:left;" class="talkappi-datepicker" type="text"/>
                        <div class="input_item_calendar_time" style="order: 1;">
                            <span class="icon-timer"></span>
                            <!-- <?php echo __('admin.maximumcalendar.large_view') ?> -->
                            <!-- <?php if ( $post != NULL && isset( $post['start_date'] ) ) echo($post['start_time'])?> -->
                            <input name="send_time" id="send_time" type="text" class="form-control timepicker timepicker-24" value="<?php if ($post != NULL) echo($post['send_time'])?>" style="border:none;border-top: 1px solid #e5e5e5;border-bottom: 1px solid #e5e5e5;border-radius: 0px;" >
                        </div>
                    </div>
                </div>
                <div class="row_sub_frame js-test-addr" style="width:900px;">
                    <div class="label_bar_title">
                        <div class="label_bar_title_2">
                            <div class="text_label_bar_title"><?php echo __('admin.send.label.test_addresses')?></div>
                        </div>
                    </div>
                    <div class="col-md-5" style="padding:0;width: 100%;">
                        <input type="text" name="test_mail_addresses" value="<?php if ( $post != NULL && isset($post['test_mail_addresses']) ) echo($post['test_mail_addresses'])?>" class="form-control talkappi-textinput" data-max-input="300" placeholder="<EMAIL>,<EMAIL>" />
                    </div>
                    <!-- hide if already sent? -->
                    <span class="btn round send_test"><?php echo __('admin.send.button.test')?></span>
                </div>


            </div>
        </div>

        <!--hide for PR -- display: none; -->
        <div class="form_sub_container" style="display: none;">
            <div class="form_head head_sub_frame_start_shadow_diff">
                <div class="txt_head_title">送信結果通知</div>
            </div>
            <div class="head_content" style="flex-direction: row; width: 900px;">
                <div class="row_sub_frame">
                    <div class="label_bar_title">
                        <div class="label_bar_title_2">
                            <div class="text_label_bar_title">通知先設定</div>
                        </div>
                    </div>
                </div>
                <div class="group_btn_label">
                    <div class="btn_label_w_ico">
                        <span class="icon-add"></span>
                        <div class="txt_head_content_btn_desc">通知先選択</div>
                    </div>
                </div>
            </div>
        </div>

    </div>
    <div class="form_container" style="align-items: center;">
        <div class="btn_container">
            <div id = "draftsave" class="btn_large_blue op" <?php if ($read_only) echo 'style="display:none;"';?> ><div class="txt_btn_large txt_btn_large_highlight_diff">下書き保存</div></div>
            <div id = "sendorschedule" class="btn_large_blue btn_white_diff op" <?php if ($read_only) echo 'style="display:none;"';?> ><div class="txt_btn_large"><?php echo($apply_btn_name_options[$send_date_setting_code])?></div></div>
            <?php 
                if ( isset($del_btn) && !empty($del_btn) ) {
                    echo('<div id = "btnDeleteTask" class="btn_large_blue delete op"><i></i><div class="txt_btn_large">'.$del_btn.'</div></div>');
                }
            ?>
            <div id = "btnBack" class="btn_large_blue btn_white_diff"><div class="txt_btn_large"><?php echo __('admin.common.button.return_to_list') ?></div></div>
        </div>
    </div>
</div>
