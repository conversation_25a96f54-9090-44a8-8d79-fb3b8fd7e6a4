<style>
.flex.check-wrapper {
  justify-content: space-between;
  align-items: center;
  gap: 4px;
}
span.checkflg {
  width: 14px;
  height: 14px;
  display: inline-block;
  background-size: contain;
  background-repeat: no-repeat;
}
.checkflg.flg-0 {
  background-image: url('/assets/common/images/icon_fail.svg');
}
.checkflg.flg-1 {
  background-image: url('/assets/common/images/icon_success.svg');
}
.search-wrapper {
  display: flex;
  gap: 20px;
}
</style>

<div class="content-container">
  <div class="flex-x-between">
    <div>契約管理 - 取引先一覧</div>
  </div>
</div>

<div class="content-container light-gray">
  <div class="search-wrapper">
    <label class="control-label">担当者</label>
    <div class="">
      <select class="form-control select2me" name="representative" style="width:200px;">
        <option value="">-</option>
        <?php foreach($admin_users as $user_id => $user_name) { ?>
        <option value="<?php echo $user_id; ?>" <?php echo (isset($post['representative']) && $post['representative'] == $user_id) ? 'selected' : '' ?>><?php echo $user_name; ?></option>
        <?php } ?>
      </select>
    </div>
    <label class="control-label">チェック状態</label>
    <div class="">
      <select class="form-control " name="check_status" style="width:200px;">
        <option value="">-</option>
        <option value="0" <?php echo (isset($post['check_status']) && $post['check_status'] == '0') ? 'selected' : '' ?>>未チェック</option>
        <option value="1" <?php echo (isset($post['check_status']) && $post['check_status'] == '1') ? 'selected' : '' ?>>チェック済</option>
      </select>
    </div>
  </div>
  <div class="search-wrapper" style="align-items:center;margin-top:10px">
    <input type="checkbox" name="show_invalid" id="show_invalid" <?php echo $post['show_invalid'] == 1 ? 'checked' : '' ?> value="1">
    <label for="show_invalid" style="margin-bottom:0">無効済みの取引先</label>
    <input type="checkbox" name="show_delete" id="show_delete" <?php echo $post['show_delete'] == 1 ? 'checked' : '' ?>  value="1">
    <label for="show_delete" style="margin-bottom:0">削除済みの取引先</label>
    <span class="btn-smaller btn-blue js-search"><i class="fa fa-search mr10"></i>
      <?php echo __('admin.common.button.search') ?>
    </span>
  </div>
</div>

<div class="content-container">
  <div class="flex-x-between">
    <div></div>
    <div class="btn-smaller btn-blue js-new-client">
      <span class="icon-add-white"></span>
      新規作成
    </div>
  </div>
</div>

<div class="content-container white">
  <table class="table table-striped table-bordered table-hover js-data-table">
    <thead>
      <tr class="odd">
        <th>取引先コード</th>
        <th>取引先名</th>
        <th>取引先名（検索用）</th>
        <th>取引先部門名</th>
        <th>取引先部門名（検索用）</th>
        <th>取引国</th>
        <th>セグメント</th>
        <th>チェック状態</th>
        <th>最終のチェック日</th>
        <th>操作</th>
        <th>状態</th>
      </tr>
    </thead>
    <tbody>
      <?php foreach ($clients as $client) {?>
      <tr class="gradeX odd" role="row">
        <td><a class="link-animate" href="<?php echo '/admincontract/client?id=' . $client['client_code'] ?>"><?php echo $client['client_code']; ?></a></td>
        <td><?php echo $client['client_name']; ?></td>
        <td><?php echo $client['client_name_for_search']; ?></td>
        <td><?php echo $client['department_name']; ?></td>
        <td><?php echo $client['department_name_for_search']; ?></td>
        <td><?php echo $client['country']; ?></td>
        <td><?php echo $client['segment']; ?></td>
        <td>
          <div class="flex" style="flex-direction:column;gap:4px;">
          <?php 
            $check_text = '';
            $check_status = $client['check_status'];
            if ($check_status != NULL) {
              $cs_checkflg = $check_status['cs_checkflg'];
              if ($cs_checkflg != NULL) {
                $check_text .= "<div class='flex check-wrapper'>";
                $check_text .= "<span>CS: </span>";
                $check_text .= "<span class='checkflg flg-" . $cs_checkflg . "'></span>";
                $check_text .= "</div>";
              }
              $sales_checkflg = $check_status['sales_checkflg'];
              if ($sales_checkflg != NULL) {
                $check_text .= "<div class='flex check-wrapper'>";
                $check_text .= "<span>営業: </span>";
                $check_text .= "<span class='checkflg flg-" . $sales_checkflg . "'></span>";
                $check_text .= "</div>";
              }
              $accountant_checkflg = $check_status['accountant_checkflg'];
              if ($accountant_checkflg != NULL) {
                $check_text .= "<div class='flex check-wrapper'>";
                $check_text .= "<span>経理: </span>";
                $check_text .= "<span class='checkflg flg-" . $accountant_checkflg . "'></span>";
                $check_text .= "</div>";
              }
              $admin_checkflg = $check_status['admin_checkflg'];
              if ($admin_checkflg != NULL) {
                $check_text .= "<div class='flex check-wrapper'>";
                $check_text .= "<span>管理者: </span>";
                $check_text .= "<span class='checkflg flg-" . $admin_checkflg . "'></span>";
                $check_text .= "</div>";
              }
            } else {
              $check_text = 'チェック履歴なし';
            }
            echo $check_text;
          ?>
          </div>
        </td>
        <td>
          <?php echo is_null($client['last_check_time']) ? 'なし' : $client['last_check_time']  ?>
        </td>
        <td>
          <div class="flex">
            <span data-url="<?php echo '/admincontract/client?id=' . $client['client_code']; ?>" class="btn round image edit js-edit-client">編集</span>
            <span data-url="<?php echo '/admincontract/contractattachment?client_code=' . $client['client_code'] ?>" class="btn round image result js-contractattachment">別紙Bへ</span>
          </div>
        </td>
        <!-- 状態 -->
        <td>
          <?php if ($client['invalid_flg'] == 1) {
            echo '<span class="btn light-gray">無効済み</span>';
            echo '<br>';
            echo $client['invalid_time'];
          } else if ($client['delete_flg'] == 1) {
            echo '<span class="btn light-red">削除済み</span>';
            echo '<br>';
            echo $client['delete_time'];
          } else {
            echo 'なし';
          }
          ?>
        </td>
      </tr>
      <?php } ?>
    </tbody>
  </table>
</div>