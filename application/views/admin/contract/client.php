<style>
.basic-label {
  width: 140px;
}
.js-sales_representative-user, .js-cs_representative-user {
  cursor: pointer;
}
.selected_users_name_wrapper {
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 4px;
}
input.text-input-longer:read-only {
  background-color: #f0f0f0;
}
.js-client-diff {
  display: flex;
  flex-direction: column;
}
.js-client-diff .diff-title {
  font-size: 13px;
  font-weight: normal;
}
.diff-container {
  border: 1px solid #DDD;
  border-radius: 4px;
  background-color: white;
  padding: 6px;
  box-sizing: border-box;
  width: 300px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  overflow-y: auto;
}
.diff-container .diff-item {
  word-break: break-all;
}
.diff-container .diff-header {
  font-weight: bold;
  margin-bottom: 4px;
}
</style>

<script>
const _segment_maps = <?php echo json_encode($segment_maps, JSON_UNESCAPED_UNICODE) ?>;
const _segment_options = <?php echo json_encode($segment_options, JSON_UNESCAPED_UNICODE); ?>;
const _segment = "<?php echo $post['segment'] ?>";
const _client_code = "<?php echo $client_code ?>";
const _user_options = <?php echo json_encode($user_options, JSON_UNESCAPED_UNICODE); ?>;
const _client_diff = <?php echo json_encode($client_diff, JSON_UNESCAPED_UNICODE); ?>;
const _check_id = "<?php echo is_null($check_status) ? NULL : $check_status['id']; ?>";
</script>
<script src="/assets/admin/contract/check.js"></script>
<script src="/assets/admin/contract/diff.js"></script>

<div class="content-container">
  <div class="flex-x-between">
    <div><a class="link-animate" href="/admincontract/clients">取引先一覧</a> > 取引先編集</div>
  </div>
</div>

<?php 
  $can_edit = $client_code == NULL ? true : false;
?>

<div class="content-container white border">
  <div class="flex" style="justify-content:space-between;align-items:baseline;">
    <div class="section-container" style="width:700px;">
      <!-- 取引先コード -->
      <div class="lines-container">
        <div class="basic-label">取引先コード</div>
        <input type="text" <?php echo $can_edit ? '' : 'readonly'  ?> value="<?php echo htmlspecialchars($post['client_code']) ?>" name="client_code" placeholder="取引先コード" class="text-input-longer">
      </div>
      <!-- 取引先名 -->
      <div class="lines-container">
        <div class="basic-label">取引先名</div>
        <input type="text" value="<?php echo htmlspecialchars($post['client_name']) ?>" name="client_name" placeholder="取引先名" class="text-input-longer">
      </div>
      <!-- 取引先名（検索用） -->
      <div class="lines-container">
        <div class="basic-label">取引先名（検索用）</div>
        <input type="text" value="<?php echo htmlspecialchars($post['client_name_for_search']) ?>" name="client_name_for_search" placeholder="取引先名（検索用）" class="text-input-longer">
      </div>
      <!-- 取引先部門名 -->
      <div class="lines-container">
        <div class="basic-label">取引先部門名</div>
        <input type="text" value="<?php echo htmlspecialchars($post['department_name']) ?>" name="department_name" placeholder="取引先部門名" class="text-input-longer">
      </div>
      <!-- 取引先部門名（検索用） -->
      <div class="lines-container">
        <div class="basic-label">取引先部門名（検索用）</div>
        <input type="text" value="<?php echo htmlspecialchars($post['department_name_for_search']) ?>" name="department_name_for_search" placeholder="取引先部門名（検索用）" class="text-input-longer">
      </div>
      <!-- 取引国 -->
      <div class="lines-container">
        <div class="basic-label">取引国</div>
        <div>
          <div class="talkappi-pulldown" data-name="country" data-value="<?php echo $post['country'] ?>" data-source="<?php echo htmlspecialchars(json_encode($country_options, JSON_UNESCAPED_UNICODE)) ?>"></div>
        </div>
      </div>
      <!-- セグメント -->
      <div class="lines-container">
        <div class="basic-label">セグメント</div>
        <div class="js-segment" style="display:flex;gap:10px;"></div>
      </div>
    </div>
  </div>

  <div class="section-container">
    <div class="actions-container" style="margin: 60px 0 0 140px">
      <div class="btn-larger btn-blue x-first js-action-save"><?php echo __('admin.common.button.save'); ?></div>
      <div class="btn-larger btn-white js-action-back"><?php echo __('admin.common.button.return_to_list'); ?></div>
      <div class="btn-larger btn-white js-goto-checklist">チェックリスト画面へ</div>
      <?php if ($_user->role_cd == '99' && $client_code) { 
        $type = '';
        if ($can_delete) {
          $type = 'delete';
        } else {
          $type = 'invalid';
        }
      ?>
        <div class="btn-larger btn-white js-goto-contractattachment">別紙Bへ</div>
        <div class="btn-larger btn-red js-action-delete" data-type="<?php echo $type ?>"><?php echo __("admin.common.button.$type") ?></div>
      <?php } ?>
    </div>
  </div>

  <div class="section-container">
    <!-- チェック状態 -->
    <div class="js-checkcontainer"></div>
  </div>
  
  <div class="section-container js-diff-container">
    <!-- DIFF -->
    <div class="setting-header">編集履歴</div>
  </div>
</div>