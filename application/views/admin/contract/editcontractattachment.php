<script>
const _client_code = '<?php echo $client_code ?>';
const _version = '<?php echo $version ?>';
const _contracts = <?php echo json_encode($contract_data, JSON_UNESCAPED_UNICODE); ?>;
const _diffs = <?php echo json_encode($diff_data, JSON_UNESCAPED_UNICODE); ?>;
const _create_date = '<?php echo $common_data['create_date'] ?>';
</script>

<style>
textarea.diff_remark {
  max-width: 100%;
  width: 400px;
}
.js-diffs {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.js-diffs .diffs-items-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.js-diffs .item-wrapper {
  display: flex;
  align-items: center;
  gap: 6px;
  background-color: #FFFFFF;
}
.js-diffs .item-wrapper .talkappi-datepicker-container {
  margin-right: 0;
}
.js-diffs .item-wrapper::before {
  content: '';
  display: block;
  background-image: url(/assets/admin/css/img/icon-drag.svg);
  background-size: contain;
  width: 16px;
  height: 16px;
  cursor: move;
}
</style>

<div class="content-container white border">
  <div class="section-container">
    <div class="setting-header">基本情報</div>
    <div class="lines-container">
      <div class="basic-label">添付日</div>
      <input class="form-control" style="width: 200px;" readonly type="text" name="attach_date" value="<?php echo $common_data['attach_date'] ?>">
    </div>
    <div class="lines-container">
      <div class="basic-label">新規作成日</div>
      <input class="form-control" style="width: 200px;" readonly type="text" name="create_date" value="<?php echo $common_data['create_date'] ?>">
    </div>
    <div class="lines-container">
      <div class="basic-label">備考</div>
      <textarea class="form-control diff_remark" name="remark"><?php echo htmlspecialchars($common_data['remark']) ?></textarea>
    </div>
    <div class="lines-container">
      <div class="basic-label">Very: 備考</div>
      <textarea class="form-control diff_remark" name="very_remark"><?php echo htmlspecialchars($common_data['very_remark']) ?></textarea>
    </div>
  </div>

  <div class="section-container">
    <div class="setting-header">編集履歴</div>
    <div class="js-diffs"></div>
  </div>

  <div class="section-container">
    <div class="actions-container">
      <div class="btn-larger btn-blue js-action-save">保存</div>
      <div class="btn-larger btn-white js-action-back">戻る</div>
      <div class="btn-larger btn-red js-action-delete">削除</div>
    </div>
  </div>
</div>