<style>
.flex.check-wrapper {
  justify-content: space-between;
  align-items: center;
  gap: 4px;
}
span.checkflg {
  width: 14px;
  height: 14px;
  display: inline-block;
  background-size: contain;
  background-repeat: no-repeat;
}
.checkflg.flg-0 {
  background-image: url('/assets/common/images/icon_fail.svg');
}
.checkflg.flg-1 {
  background-image: url('/assets/common/images/icon_success.svg');
}
.search-wrapper {
  display: flex;
  gap: 20px;
}
</style>

<div class="content-container">
  <div class="flex-x-between">
    <div>契約管理 - 請求先一覧</div>
  </div>
</div>

<div class="content-container light-gray">
  <div class="search-wrapper">
    <label class="control-label">担当者</label>
    <div class="">
      <select class="form-control select2me" name="representative" style="width:200px;">
        <option value="">-</option>
        <?php foreach($admin_users as $user_id => $user_name) { ?>
        <option value="<?php echo $user_id; ?>" <?php echo (isset($post['representative']) && $post['representative'] == $user_id) ? 'selected' : '' ?>><?php echo $user_name; ?></option>
        <?php } ?>
      </select>
    </div>
    <label class="control-label">チェック状態</label>
    <div class="">
      <select class="form-control " name="check_status" style="width:200px;">
        <option value="">-</option>
        <option value="0" <?php echo (isset($post['check_status']) && $post['check_status'] == '0') ? 'selected' : '' ?>>未チェック</option>
        <option value="1" <?php echo (isset($post['check_status']) && $post['check_status'] == '1') ? 'selected' : '' ?>>チェック済</option>
      </select>
    </div>
  </div>
  <div class="search-wrapper" style="align-items:center;margin-top:10px">
    <input type="checkbox" name="show_invalid" id="show_invalid" <?php echo $post['show_invalid'] == 1 ? 'checked' : '' ?> value="1">
    <label for="show_invalid" style="margin-bottom:0">無効済みの請求先</label>
    <input type="checkbox" name="show_delete" id="show_delete" <?php echo $post['show_delete'] == 1 ? 'checked' : '' ?>  value="1">
    <label for="show_delete" style="margin-bottom:0">削除済みの請求先</label>
    <!-- <input type="checkbox" name="show_expired" id="show_expired" <?php echo $post['show_expired'] == 1 ? 'checked' : '' ?>  value="1">
    <label for="show_expired" style="margin-bottom:0">過去の請求先</label> -->
    <span class="btn-smaller btn-blue js-search"><i class="fa fa-search mr10"></i>
      <?php echo __('admin.common.button.search') ?>
    </span>
  </div>
</div>

<div class="content-container">
  <div class="flex-x-between">
    <div></div>
    <div class="btn-smaller btn-blue js-new-invoice">
      <span class="icon-add-white"></span>
      新規作成
    </div>
  </div>
</div>

<div class="content-container white">
  <table class="table table-striped table-bordered table-hover js-data-table">
    <thead>
      <tr class="odd">
        <th>請求先コード</th>
        <th>請求先名</th>
        <th>請求先部門名</th>
        <th>請求書送付先TO</th>
        <th>請求書送付先CC</th>
        <th>請求書記載住所</th>
        <th>送付方法</th>
        <th>請求書送付タイミング</th>
        <th>請求スパン</th>
        <th>支払タイミング</th>
        <th>支払方法</th>
        <th>チェック状態</th>
        <th>最終のチェック日</th>
        <th>操作</th>
        <th>状態</th>
      </tr>
    </thead>
    <tbody>
      <?php foreach ($invoices as $invoice) {  ?>
      <tr class="gradeX odd" role="row">
        <!-- 請求先コード（世帯情報） -->
        <td>
          <a class="link-animate" href="<?php echo '/admincontract/invoice?id=' . $invoice['invoice_code'] . '&seq=' . $invoice['seq'] ?>"><?php echo $invoice['invoice_code'] ?></a>
          <div style="display:flex;flex-direction:column;gap:4px;">
            <div class="btn light-orange">第<?php echo $invoice['seq'] ?>世代</div>
            <div style="font-size:9px">
              <?php if ($invoice['ver_start_date'] != null) echo "開始日: " . $invoice['ver_start_date'] ?>
              <?php if ($invoice['ver_end_date'] != null) echo "終了日: " . $invoice['ver_end_date'] ?>
            </div>
          </div>
        </td>
        <!-- 請求先名 -->
        <td><?php echo $invoice['invoice_name'] ?></td>
        <!-- 請求先部門名 -->
        <td><?php echo $invoice['department_name'] ?></td>
        <!-- 請求書送付先TO -->
        <td>
          <?php 
            $invoice_recipient_to = json_decode($invoice['invoice_recipient_to'], true);
            if ($invoice_recipient_to && is_array($invoice_recipient_to) && count($invoice_recipient_to) > 0) {
              echo "<div style='display:flex;flex-wrap:wrap;gap:4px;'>";
              foreach ($invoice_recipient_to as $recipient) {
                echo "<span class='btn round light-blue'>". htmlspecialchars($recipient) . "</span>";
              }
              echo "</div>";
            } else {
              echo "なし";
            }
          ?>
        </td>
        <!-- 請求書送付先CC -->
        <td>
          <?php 
            $invoice_recipient_cc = json_decode($invoice['invoice_recipient_cc'], true);
            if ($invoice_recipient_cc && is_array($invoice_recipient_cc) && count($invoice_recipient_cc) > 0) {
              echo "<div style='display:flex;flex-wrap:wrap;gap:4px;'>";
              foreach ($invoice_recipient_cc as $recipient) {
                echo "<span class='btn round light-blue'>". htmlspecialchars($recipient) . "</span>";
              }
              echo "</div>";
            } else {
              echo "なし";
            }
          ?>
        </td>
        <!-- 請求書記載住所 -->
        <td>
          <?php 
            $invoice_address = json_decode($invoice['invoice_address'], true);
            if ($invoice_address && is_array($invoice_address)) {
              echo "<div>";
              foreach ($invoice_address as $key => $address) {
                if ($key == 'postal_code') echo "〒";
                echo htmlspecialchars($address) . "<br>";
              }
              echo "</div>";
            }
          ?>
        </td>
        <!-- 送付方法 -->
        <td><?php echo $send_methods[$invoice['send_method']] ?></td>
        <!-- 請求書送付タイミング -->
        <td><?php echo $invoice_send_timings[$invoice['invoice_send_timing']] ?></td>
        <!-- 請求スパン -->
        <td><?php echo $invoice_spans[$invoice['invoice_span']] ?></td>
        <!-- 支払タイミング -->
        <td><?php echo $payment_settings[$invoice['payment_timing']] ?></td>
        <!-- 支払方法 -->
        <td><?php echo $payment_methods[$invoice['payment_method']] ?></td>
        <!-- チェック状態 -->
        <td>
          <div class="flex" style="flex-direction:column;gap:4px;">
          <?php 
            $check_text = '';
            // $check_status = $invoice['check_status'];
            $check_status = $invoice['check_status'];
            if ($check_status != NULL) {
              $cs_checkflg = $check_status['cs_checkflg'];
              if ($cs_checkflg != NULL) {
                $check_text .= "<div class='flex check-wrapper'>";
                $check_text .= "<span>CS: </span>";
                $check_text .= "<span class='checkflg flg-" . $cs_checkflg . "'></span>";
                $check_text .= "</div>";
              }
              $sales_checkflg = $check_status['sales_checkflg'];
              if ($sales_checkflg != NULL) {
                $check_text .= "<div class='flex check-wrapper'>";
                $check_text .= "<span>営業: </span>";
                $check_text .= "<span class='checkflg flg-" . $sales_checkflg . "'></span>";
                $check_text .= "</div>";
              }
              $accountant_checkflg = $check_status['accountant_checkflg'];
              if ($accountant_checkflg != NULL) {
                $check_text .= "<div class='flex check-wrapper'>";
                $check_text .= "<span>経理: </span>";
                $check_text .= "<span class='checkflg flg-" . $accountant_checkflg . "'></span>";
                $check_text .= "</div>";
              }
              $admin_checkflg = $check_status['admin_checkflg'];
              if ($admin_checkflg != NULL) {
                $check_text .= "<div class='flex check-wrapper'>";
                $check_text .= "<span>管理者: </span>";
                $check_text .= "<span class='checkflg flg-" . $admin_checkflg . "'></span>";
                $check_text .= "</div>";
              }
            } else {
              $check_text = 'チェック履歴なし';
            }
            echo $check_text;
          ?>
          </div>
        </td>
        <td>
          <?php echo is_null($invoice['last_check_time']) ? 'なし' : $invoice['last_check_time']  ?>
        </td>
        <!-- 操作 -->
        <td>
          <div class="flex" style="flex-wrap:wrap;gap:4px">
            <span data-url="<?php echo '/admincontract/invoice?id=' . $invoice['invoice_code'] . '&seq=' . $invoice['seq']; ?>" class="btn round image edit js-edit-invoice">編集</span>
            <?php if ($_user->role_cd == '99' || $_user->role_cd == '08') { ?>
            <span 
              class="btn round image copy js-new-generation" 
              data-invoice_code="<?php echo $invoice['invoice_code'] ?>"
              data-start_date="<?php echo $invoice['ver_start_date'] ?? '' ?>"
              data-end_date="<?php echo $invoice['ver_end_date'] ?? '' ?>"
            >
              世代作成
            </span>
            <?php } ?>
          </div>
        </td>
        <!-- 状態 -->
        <td>
          <?php if ($invoice['invalid_flg'] == 1) {
            echo '<span class="btn light-gray">無効済み</span>';
            echo '<br>';
            echo $invoice['invalid_time'];
          } else if ($invoice['delete_flg'] == 1) {
            echo '<span class="btn light-red">削除済み</span>';
            echo '<br>';
            echo $invoice['delete_time'];
          } else {
            echo 'なし';
          }
          ?>
        </td>
      </tr>
      <?php } ?>
    </tbody>
</div>