<div class="content-container">
  <div class="flex-x-between">
    <div>請求書</div>
  </div>
</div>

<?php 
	if (strlen($_active_menu) > 4) {
		$tab_menu = View::factory ('admin/menutab');
		echo($tab_menu);
	}
?>

<div class="content-container white">
  <table class="table table-striped table-bordered table-hover js-data-table">
    <thead>
      <t class="odd">
        <th>請求書番号</th>
        <th>請求日</th>
        <th>請求期間</th>
        <th>お支払期限</th>
        <th>請求金額(税込)</th>
        <th>送付方法</th>
        <th>請求書PDF</th>
      </tr>
    </thead>
    <tbody>
      <?php foreach($invoice_payments as $payment) { 
        $total_amount = 0;
        $billing_start_date = '';
        $billing_end_date = '';
        $invoice_details = json_decode($payment['invoice_details'], true);
        foreach ($invoice_details as $detail) {
          $amount = intval($detail['amount']);
          $number = intval($detail['number']);
          $tax = intval($detail['tax']);
          $total_amount += $amount * $number * (100 + $tax);
          if ($billing_start_date == '') $billing_start_date = $detail["billing_start_date"];
          if ($billing_end_date == '') $billing_end_date = $detail["billing_end_date"];
        }
        $total_amount = $total_amount / 100;
        $contract_needcheck = false;
        if (isset($contract_checks[$payment['contract_id']])) {
          $contract_check = $contract_checks[$payment['contract_id']];
          $contract_needcheck = ($contract_check['cs_checkflg'] === "0" || $contract_check['sales_checkflg'] === "0" || $contract_checks['accountant_checkflg'] === "0" || $contract_check['admin_checkflg'] === "0");
        }
        $invoice_needcheck = false;
        if (isset($invoice_checks[$payment['invoice_code']]) && $invoice_checks[$payment['invoice_code']]['seq'] == $payment['seq']) {
          $invoice_check = $invoice_checks[$payment['invoice_code']];
          $invoice_needcheck = ($invoice_check['cs_checkflg'] === "0" || $invoice_check['sales_checkflg'] === "0" || $invoice_check['accountant_checkflg'] === "0" || $invoice_check['admin_checkflg'] === "0");
        }
      ?>
        <tr class="gradeX odd" role="row">
          <td>
            <div style="display:flex;flex-direction:column;gap:4px;">
            <?php echo $payment['invoice_number'] ?>
            <?php if ($payment['invalid_flg'] == 1) {
              echo '<span class="btn light-gray">無効</span>';
            } ?>
            </div>
          </td>
          <td><?php echo $payment['invoice_date'] ?></td>
          <td><?php echo $billing_start_date . ' 〜 ' . $billing_end_date?></td>
          <td><?php echo $payment['payment_due_date'] ?></td>
          <td style="white-space:nowrap;"><?php echo "¥".number_format($total_amount) ?></td>
          <td>
            <?php 
              $send_method = $payment['send_method'];
              if ($send_method == 'mail') {
                echo 'メール';
              } else if ($send_method == 'postal') {
                echo '郵送';
              } else if ($send_method == 'informart') {
                echo 'インフォマート';
              }
            ?>
          </td>
          <td>
            <div class="flex" style="flex-wrap:wrap;gap:6px">
              <span class="btn round image detail js-pdf" data-id="<?php echo $payment['invoice_id'] ?>">PDF</span>
            </div>
          </td>
        </tr>
      <?php } ?>
    </tbody>
  </table>
</div>