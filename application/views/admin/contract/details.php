<style>
.flex.check-wrapper {
  justify-content: space-between;
  align-items: center;
  gap: 4px;
}
span.checkflg {
  width: 14px;
  height: 14px;
  display: inline-block;
  background-size: contain;
  background-repeat: no-repeat;
}
.checkflg.flg-0 {
  background-image: url('/assets/common/images/icon_fail.svg');
}
.checkflg.flg-1 {
  background-image: url('/assets/common/images/icon_success.svg');
}
.search-wrapper {
  display: flex;
  gap: 20px;
}
.common-modal-container>.common-modal-content {
  height: auto;
}
</style>

<div class="content-container">
  <div class="flex-x-between">
    <div>契約内容</div>
  </div>
</div>

<?php 
	if (strlen($_active_menu) > 4) {
		$tab_menu = View::factory ('admin/menutab');
		echo($tab_menu);
	}
?>

<div class="content-container white">
  <table class="table table-striped table-bordered table-hover js-data-table">
    <thead>
      <tr class="odd">
        <th style="width: 150px;">サービス名</th>
        <th>契約施設</th>
        <th>費用種類</th>
        <th>費用（税抜）</th>
        <th>請求開始日</th>
        <th>請求終了日</th>
        <th>CS担当者</th>
        <th>営業担当者</th>
      </tr>
    </thead>
    <tbody>
      <?php foreach($contracts as $contract) { ?>
      <tr class="gradeX odd" role="row">
        <td><?php echo $contract['item_name']; ?></td>
        <td>
          <div style="display:flex;gap:6px;flex-wrap:wrap;">
          <?php 
            $billing_name = explode(',', $contract['billing_account_name']); 
            foreach ($billing_name as $name) {
              echo "<span class='btn round light-blue' style='user-select: text;'>{$name}</span>";
            }
          ?>
          </div>
        </td>
        <td><?php echo isset($cost_type_options[$contract['cost_type']]) ? $cost_type_options[$contract['cost_type']] : '' ?></td>
        <td style="white-space:nowrap;">¥<?php echo number_format($contract['cost']); ?></td>
        <td><?php echo $contract['billing_start_date']; ?></td>
        <td><?php echo $contract['billing_end_date']; ?></td>
        <td><?php echo ($contract['cs_representative'] && isset($admin_users[$contract['cs_representative']])) ? $admin_users[$contract['cs_representative']] : '未設定' ?></td>
        <td><?php echo ($contract['sales_representative'] && isset($admin_users[$contract['sales_representative']])) ? $admin_users[$contract['sales_representative']] : '未設定' ?></td>
      <?php } ?>
    </tbody>
  </table>
</div>