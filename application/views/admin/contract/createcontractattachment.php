<script>
const _client_code = '<?php echo $client_code ?? '' ?>';
const _latest_version = <?php echo json_encode($lastest_version ?? []) ?>;
</script>
<style>
.actions-container {
  gap: 16px;
}
</style>
<div class="content-container">
  <div class="flex-x-between">
    <div>別紙B発行</div>
  </div>
</div>

<div class="content-container white border">
  <div class="section-container">
    <div class="setting-header">基本情報</div>
    <div class="lines-container">
      <div class="basic-label">申込日</div>
      <input class="talkappi-datepicker" type="text" name="attach_date" value="">
    </div>
    <div class="lines-container">
      <div class="basic-label">添付契約種類</div>
      <?php 
      $attachment_type_source = [
        [
          "code" => "1",
          "text" => "「talkappi」サービス利用申込書"
        ],
        [
          "code" => "2",
          "text" => "契約終了及び新規約適用に関する覚書"
        ]
        ];
      ?>
      <div class="talkappi-pulldown" data-label="attachment_type" data-name="attachment_type" data-value="1" data-source='<?php echo htmlspecialchars(json_encode($attachment_type_source, JSON_UNESCAPED_UNICODE)) ?>' ></div>
    </div>
  </div>

  <div class="section-container">
    <div class="actions-container">
      <div class="btn-larger btn-blue x-first js-action-save"><?php echo __('admin.common.button.save'); ?></div>
      <div class="btn-larger btn-white x-first js-action-return">戻る</div>
    </div>
  </div>
</div>