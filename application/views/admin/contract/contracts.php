<style>
.flex.check-wrapper {
  justify-content: space-between;
  align-items: center;
  gap: 4px;
}
span.checkflg {
  width: 14px;
  height: 14px;
  display: inline-block;
  background-size: contain;
  background-repeat: no-repeat;
}
.checkflg.flg-0 {
  background-image: url('/assets/common/images/icon_fail.svg');
}
.checkflg.flg-1 {
  background-image: url('/assets/common/images/icon_success.svg');
}
.search-wrapper {
  display: flex;
  gap: 20px;
}
.common-modal-container>.common-modal-content {
  height: auto;
}
</style>

<div class="content-container">
  <div class="flex-x-between">
    <div>契約管理 - 契約一覧</div>
  </div>
</div>

<div class="content-container light-gray">
  <div class="search-wrapper">
    <label class="control-label">担当者</label>
    <div class="">
      <select class="form-control select2me" name="representative" style="width:200px;">
        <option value="">-</option>
        <?php foreach($admin_users as $user_id => $user_name) { ?>
        <option value="<?php echo $user_id; ?>" <?php echo (isset($post['representative']) && $post['representative'] == $user_id) ? 'selected' : '' ?>><?php echo $user_name; ?></option>
        <?php } ?>
      </select>
    </div>
    <label class="control-label">チェック状態</label>
    <div class="">
      <select class="form-control " name="check_status" style="width:200px;">
        <option value="">-</option>
        <option value="0" <?php echo (isset($post['check_status']) && $post['check_status'] == '0') ? 'selected' : '' ?>>未チェック</option>
        <option value="1" <?php echo (isset($post['check_status']) && $post['check_status'] == '1') ? 'selected' : '' ?>>チェック済</option>
      </select>
    </div>
  </div>
  <div class="search-wrapper" style="align-items:center;margin-top:10px">
    <input type="checkbox" name="show_invalid" id="show_invalid" <?php echo $post['show_invalid'] == 1 ? 'checked' : '' ?> value="1">
    <label for="show_invalid" style="margin-bottom:0">無効済みの契約</label>
    <input type="checkbox" name="show_delete" id="show_delete" <?php echo $post['show_delete'] == 1 ? 'checked' : '' ?>  value="1">
    <label for="show_delete" style="margin-bottom:0">削除済みの契約</label>
    <span class="btn-smaller btn-blue js-search"><i class="fa fa-search mr10"></i>
      <?php echo __('admin.common.button.search') ?>
    </span>
  </div>
</div>

<div class="content-container">
  <div class="flex-x-between">
    <div></div>
    <div class="flex" style="align-items: center;gap: 4px;">
      <div class="action-button btn-smaller btn-blue js-new-contract"><span class="icon-add-white"></span>新規作成</div>
      <div class="action-button btn-smaller btn-white js-export-sales"><span class="icon-export"></span>売上出力</div>
    </div>
  </div>
</div>

<div class="content-container white">
  <table class="table table-striped table-bordered table-hover js-data-table">
    <thead>
      <tr class="odd">
        <th>契約ID</th>
        <th style="width: 85px;">品目</th>
        <th>取引先名</th>
        <th>セグメント</th>
        <th>請求先名</th>
        <th>契約施設</th>
        <th>費用種類</th>
        <th>費用（税抜）</th>
        <th>請求開始日</th>
        <th>請求終了日</th>
        <th>CS担当者</th>
        <th>営業担当者</th>
        <th>チェック状態</th>
        <th>最終のチェック日</th>
        <th>操作</th>
        <th>状態</th>
      </tr>
    </thead>
    <tbody>
      <?php foreach($contracts as $contract) { ?>
      <tr class="gradeX odd" role="row">
        <td><a class="link-animate" href="<?php echo '/admincontract/contract?id=' . $contract['contract_id'] ?>"><?php echo $contract['contract_id']; ?></a></td>
        <td><?php echo $contract['item_name']; ?></td>
        <td><a class="link-animate" href="<?php echo $contract['client_link'] ?>"><?php echo $contract['client_name']; ?></a></td>
        <td><?php echo $contract['segment']; ?></td>
        <td>
          <?php if ($contract['invoice_name']) { ?>
            <a class="link-animate" href="<?php echo $contract['invoice_link'] ?>"><?php echo $contract['invoice_name']; ?></a>
          <?php } ?>
        </td>
        <td>
          <div style="display:flex;gap:6px;flex-wrap:wrap;">
          <?php 
            $billing_name = explode(',', $contract['billing_account_name']); 
            foreach ($billing_name as $name) {
              echo "<span data-url='/admincontract/contract?id={$contract['contract_id']}' class='btn round edit light-blue js-account-name' style='user-select: text;'>{$name}</span>";
            }
          ?>
          </div>
        </td>
        <td><?php echo isset($cost_type_options[$contract['cost_type']]) ? $cost_type_options[$contract['cost_type']] : '' ?></td>
        <td style="white-space:nowrap;">¥<?php echo number_format($contract['cost']); ?></td>
        <td><?php echo $contract['billing_start_date']; ?></td>
        <td><?php echo $contract['billing_end_date']; ?></td>
        <td><?php echo ($contract['cs_representative'] && isset($admin_users[$contract['cs_representative']])) ? $admin_users[$contract['cs_representative']] : '未設定' ?></td>
        <td><?php echo ($contract['sales_representative'] && isset($admin_users[$contract['sales_representative']])) ? $admin_users[$contract['sales_representative']] : '未設定' ?></td>
        <td>
          <div class="flex" style="flex-direction:column;gap:4px;">
          <?php 
            $check_text = '';
            $check_status = $contract['check_status'];
            if ($check_status != NULL) {
              $cs_checkflg = $check_status['cs_checkflg'];
              if ($cs_checkflg != NULL) {
                $check_text .= "<div class='flex check-wrapper'>";
                $check_text .= "<span>CS: </span>";
                $check_text .= "<span class='checkflg flg-" . $cs_checkflg . "'></span>";
                $check_text .= "</div>";
              }
              $sales_checkflg = $check_status['sales_checkflg'];
              if ($sales_checkflg != NULL) {
                $check_text .= "<div class='flex check-wrapper'>";
                $check_text .= "<span>営業: </span>";
                $check_text .= "<span class='checkflg flg-" . $sales_checkflg . "'></span>";
                $check_text .= "</div>";
              }
              $accountant_checkflg = $check_status['accountant_checkflg'];
              if ($accountant_checkflg != NULL) {
                $check_text .= "<div class='flex check-wrapper'>";
                $check_text .= "<span>経理: </span>";
                $check_text .= "<span class='checkflg flg-" . $accountant_checkflg . "'></span>";
                $check_text .= "</div>";
              }
              $admin_checkflg = $check_status['admin_checkflg'];
              if ($admin_checkflg != NULL) {
                $check_text .= "<div class='flex check-wrapper'>";
                $check_text .= "<span>管理者: </span>";
                $check_text .= "<span class='checkflg flg-" . $admin_checkflg . "'></span>";
                $check_text .= "</div>";
              }
            } else {
              $check_text = 'チェック履歴なし';
            }
            echo $check_text;
          ?>
          </div>
        </td>
        <td>
          <?php echo is_null($contract['last_check_time']) ? '未チェック' : substr($contract['last_check_time'],0,16)  ?>
        </td>
        <td>
          <div class="flex">
            <span data-url="<?php echo '/admincontract/contract?id=' . $contract['contract_id']; ?>" class="btn round image edit js-edit-contract">編集</span>
          </div>
        </td>
        <!-- 状態 -->
        <td>
          <?php if ($contract['invalid_flg'] == 1) {
            echo '<span class="btn light-gray">無効済み</span>';
            echo '<br>';
            echo $contract['invalid_time'];
          } else if ($contract['delete_flg'] == 1) {
            echo '<span class="btn light-red">削除済み</span>';
            echo '<br>';
            echo $contract['delete_time'];
          } else {
            echo 'なし';
          }
          ?>
        </td>
      </tr>
      <?php } ?>
    </tbody>
  </table>
</div>