<div class="content-container">
  <div class="flex-x-between">
    <div>契約管理 - 請求書管理</div>
  </div>
</div>

<div class="content-container">
  <div class="flex-x-between">
    <div></div>
    <div class="flex" style="align-items: center;gap: 4px;">
      <div class="action-button btn-smaller btn-white js-export-invoicepayments-csv"><span class="icon-export"></span>CSV出力</div>
    </div>
  </div>
</div>

<div class="content-container white">
  <table class="table table-striped table-bordered table-hover js-data-table">
    <thead>
      <t class="odd">
        <th>請求ID</th>
        <th>バージョン</th>
        <th>請求書番号</th>
        <th>契約ID</th>
        <th>送付ステータス</th>
        <th>入金ステータス</th>
        <th>請求先</th>
        <th>請求日</th>
        <th>お支払期限</th>
        <th>請求金額(税込)</th>
        <th>送付方法</th>
        <th>操作</th>
        <th>削除・無効</th>
      </tr>
    </thead>
    <tbody>
      <?php foreach($invoice_payments as $payment) { 
        $total_amount = 0;
        $invoice_details = json_decode($payment['invoice_details'], true);
        foreach ($invoice_details as $detail) {
          $amount = intval($detail['amount']);
          $number = intval($detail['number']);
          $tax = intval($detail['tax']);
          $total_amount += $amount * $number * (100 + $tax);
        }
        $total_amount = $total_amount / 100;
        $contract_needcheck = false;
        if (isset($contract_checks[$payment['contract_id']])) {
          $contract_check = $contract_checks[$payment['contract_id']];
          $contract_needcheck = ($contract_check['cs_checkflg'] === "0" || $contract_check['sales_checkflg'] === "0" || $contract_checks['accountant_checkflg'] === "0" || $contract_check['admin_checkflg'] === "0");
        }
        $invoice_needcheck = false;
        if (isset($invoice_checks[$payment['invoice_code']]) && $invoice_checks[$payment['invoice_code']]['seq'] == $payment['seq']) {
          $invoice_check = $invoice_checks[$payment['invoice_code']];
          $invoice_needcheck = ($invoice_check['cs_checkflg'] === "0" || $invoice_check['sales_checkflg'] === "0" || $invoice_check['accountant_checkflg'] === "0" || $invoice_check['admin_checkflg'] === "0");
        }
      ?>
        <tr class="gradeX odd" role="row">
          <td>
            <?php echo $payment['invoice_id'] ?>
          </td>
          <td><?php echo $payment['version'] ?></td>
          <td>
            <div style="display:flex;flex-direction:column;gap:4px;">
            <?php echo $payment['invoice_number'] ?>
            <?php if ($payment['invalid_flg'] == 1) {
              echo '<span class="btn light-gray">無効</span>';
            } ?>
            </div>
          </td>
          <td>
            <div style="display:flex;flex-direction:column;gap:4px;">
            <a class="link-animate" href="<?php echo '/admincontract/contract?id=' . $payment['contract_id'] ?>"><?php echo $payment['contract_id'] ?></a>
            <?php 
              if ($contract_needcheck) {
                echo '<a class="btn round light-red image detail" href="/admincontract/contract?id=' . $payment['contract_id'] . '">チェック</a>';
              }
            ?>
            </div>
          </td>
          <td>
            <div style="display:flex;flex-direction:column;gap:2px;">
              <?php $status = $payment['status'] == '0' ? '未送付' : ($payment['status'] == 1 ? '送付済' : '送付失敗') ?>
              <span class="btn round <?php echo $payment['status'] == '0' ? 'light-blue' : ($payment['status'] == '1' ? 'light-green' : 'light-red') ?>"><?php echo $status ?></span>
              <span class="btn round js-change-sendstatus" style="font-size:9px" data-id="<?php echo $payment['invoice_id'] ?>">送付済みにする</span>
            </div>
          </td>
          <td>
            <div style="display:flex;flex-direction:column;gap:2px;">
              <?php $status = $payment['receipt_flg'] == '0' ? '未入金' : '入金済み' ?>
              <span class="btn round <?php echo $payment['receipt_flg'] == '0' ? 'light-blue' : 'light-green' ?>"><?php echo $status ?></span>
              <span class="btn round js-change-receiptstatus" style="font-size:9px" data-id="<?php echo $payment['invoice_id'] ?>">入金済みにする</span>
            </div>
          </td>
          <td>
            <div style="display:flex;flex-direction:column;gap:4px;">
            <a class="link-animate" href="<?php echo '/admincontract/invoice?id=' . $payment['invoice_code'] . '&seq=' . $payment['seq'] ?>">
              <?php echo $payment['invoice_name'] . '（' . $payment['department_name'] . '）' ?>
            </a>
            <?php
              if ($invoice_needcheck) {
                echo '<a class="btn round light-red image detail" href="/admincontract/invoice?id=' . $payment['invoice_code'] . '&seq=' . $payment['seq'] . '">チェックする</a>';
              }
            ?>
            </div>
          </td>
          <td><?php echo $payment['invoice_date'] ?></td>
          <td><?php echo $payment['payment_due_date'] ?></td>
          <td style="white-space:nowrap;"><?php echo "¥".number_format($total_amount) ?></td>
          <td>
            <?php 
              $send_method = match ($payment['send_method']) {
                'mail' => 'メール',
                'postal' => '郵送',
                'informart' => 'インフォマート',
                'special' => '特殊対応',
                default => $payment['send_method'],
              };
              echo $send_method;
            ?>
          </td>
          <td>
            <div class="flex" style="flex-wrap:wrap;gap:6px">
              <span class="btn round image detail js-pdf-download" data-id="<?php echo $payment['invoice_id'] ?>">PDFダウンロード</span>
              <span class="btn round image preview js-pdf-preview" data-id="<?php echo $payment['invoice_id'] ?>">PDFプレビュー</span>
              <?php if ($payment['send_method'] == 'mail') { ?>
              <span class="btn round image send js-send-mail" data-id="<?php echo $payment['invoice_id'] ?>">送付</span>
              <?php }?>
              <!-- <span class="btn round js-regenerate" data-id="<?php echo $payment['invoice_id'] ?>">再作成</span> -->
            </div>
          </td>
          <td>
            <div class="flex" style="flex-wrap:wrap;gap:6px">
              <?php if ($payment['status'] != '1') { ?>
                <span class="btn round image delete js-delete" data-id="<?php echo $payment['invoice_id'] ?>">削除</span>
              <?php } ?>
              <?php if ($payment['status'] == '1') { ?>
                <span class="btn round image public js-invalid" data-id="<?php echo $payment['invoice_id'] ?>">無効</span>
              <?php } ?>
            </div>
          </td>
        </tr>
      <?php } ?>
    </tbody>
  </table>
</div>