<style>
.table.table-bordered thead > tr > th {
  vertical-align: middle;
}
.icon-action-section {
  margin-left: 0;
}
</style>

<div class="content-container">
  <div class="flex-x-between">
    <div><a class="link-animate" href="/admincontract/clients">取引先一覧</a> > <?php echo $client['client_name'] ?> - 別紙B</div>
  </div>
</div>

<div class="content-container white">
  <h4>Talkappi系【月額費用】</h4>
  <?php foreach ($attachment_table['monthly_items_table'] as $segment => $monthly_items_table) { ?>
    <div class="monthly-item-table-section">
      <div style="display:flex;gap:10px;align-items:center;margin-bottom:10px;">
        <span class="js-section-toggle-icon icon-action-section toggle"></span> <span style="font-weight:bold;font-size:18px;"><?= $segment ?></span>
      </div>
      <div class="monthly-item-table-wrapper">
        <table class="table table-striped table-bordered table-hover js-data-table-unsort">
          <thead>
            <tr class="odd">
              <th rowspan="3" style="width:240px">【月額利用料】</th>
              <th colspan="<?php echo count($monthly_items_table['monthly_items_header']) + 1 ?>" style="border-bottom: 1px solid #ddd;"><span>契約⾦額</span> <span></span>単位：円（税抜）</span></th>
              <th rowspan="3" style="border-left:1px solid #ddd;width:110px">システム提供日</th>
            </tr>
            <tr>
              <th rowspan="2">talkappi計</th>
              <th colspan="<?php echo count($monthly_items_table['monthly_items_header']) ?>" style="border-bottom: 1px solid #ddd;">内訳</th>
            </tr>
            <tr>
              <?php foreach($monthly_items_table['monthly_items_header'] as $item) { ?>
                <th><?php echo $item ?></th>
              <?php } ?>
            </tr>
          </thead>
          <tbody>
            <?php echo $monthly_items_table['monthly_items_body'] ?>
          </tbody>
        </table>
      </div>
    </div>
  <?php } ?>
</div>

<?php if ($attachment_table['initial_items_body'] !== '') { ?>
<div class="content-container white" style="margin-top: 20px;">
  <h4>【初期費用】</h4>
  <table class="table table-striped table-bordered table-hover">
    <thead>
      <tr class="odd">
        <th rowspan="2"><div style="width:110px">【初期費用】</div></th>
        <th colspan="<?php echo count($attachment_table['initial_items_header']) ?>" style="border-bottom: 1px solid #ddd;"><span>契約⾦額</span> <span>単位：円（税抜）</span></th>
        <th rowspan="2" style="border-left:1px solid #ddd;"><div style="width:110px">請求年月</div></th>
      </tr>
      <tr>
        <?php foreach($attachment_table['initial_items_header'] as $item) { ?>
          <th><?php echo $item ?></th>
        <?php } ?>
      </tr>
    </thead>
    <tbody>
      <?php echo $attachment_table['initial_items_body'] ?>
    </tbody>
  </table>
</div>
<?php } ?>

<?php if (isset($attachment_table['chatbot_items_table'])) { ?>
  <div class="content-container white" style="margin-top: 20px;margin-left: 20px">
    <h4>Chatbot</h4>
    <?php foreach($attachment_table['chatbot_items_table'] as $segment => $chatbot_item_table) { ?>
      <div class="monthly-item-table-section">
        <div style="display:flex;gap:10px;align-items:center;margin-bottom:10px;">
          <span class="js-section-toggle-icon icon-action-section toggle"></span> <span style="font-weight:bold;font-size:18px;"><?= $segment ?></span>
        </div>
        <div class="monthly-item-table-wrapper">
          <table class="table table-striped table-bordered table-hover">
            <thead>
              <tr class="odd">
                <th rowspan="3"><div style="width:110px">CHATBOT</div></th>
                <th colspan="<?php echo count($chatbot_item_table['items_header']) + 1 ?>" style="border-bottom: 1px solid #ddd;">単位：円（税抜）</th>
                <th rowspan="3" style="border-left:1px solid #ddd;"><div style="width:110px">システム提供日</div></th>
              </tr>
              <tr>
                <th rowspan="2">合計</th>
                <th colspan="<?php echo count($chatbot_item_table['items_header']) ?>" style="border-bottom: 1px solid #ddd;">内訳</th>
              </tr>
              <tr>
                <?php foreach($chatbot_item_table['items_header'] as $item) { ?>
                  <th><?php echo $item ?></th>
                <?php } ?>
              </tr>
            </thead>
            <tbody>
              <?php echo $chatbot_item_table['items_body'] ?>
            </tbody>
          </table>
        </div>
      </div>
    <?php } ?>
  </div>
<?php } ?>

<?php if (isset($attachment_table['very_items_table'])) { ?>
<div class="content-container white" style="margin-top: 20px;margin-left: 20px">
  <h4>VERY</h4>
  <?php foreach($attachment_table['very_items_table'] as $segment => $very_items_table) { ?>
    <div class="monthly-item-table-section">
      <div style="display:flex;gap:10px;align-items:center;margin-bottom:10px;">
        <span class="js-section-toggle-icon icon-action-section toggle"></span> <span style="font-weight:bold;font-size:18px;"><?= $segment ?></span>
      </div>
      <div class="monthly-item-table-wrapper">
        <table class="table table-striped table-bordered table-hover">
          <thead>
            <tr class="odd">
              <th rowspan="3"><div style="width:110px">VERY</div></th>
              <th colspan="<?php echo count($very_items_table['items_header']) + 1 ?>" style="border-bottom: 1px solid #ddd;">単位：円（税抜）</th>
              <th rowspan="3" style="border-left:1px solid #ddd;"><div style="width:110px">システム提供日</div></th>
            </tr>
            <tr>
              <th rowspan="2">合計</th>
              <th colspan="<?php echo count($very_items_table['items_header']) ?>" style="border-bottom: 1px solid #ddd;">内訳</th>
            </tr>
            <tr>
              <?php foreach($very_items_table['items_header'] as $item) { ?>
                <th><?php echo $item ?></th>
              <?php } ?>
            </tr>
          </thead>
          <tbody>
            <?php echo $very_items_table['items_body'] ?>
          </tbody>
        </table>
      </div>
    </div>
  <?php } ?>
</div>
<?php } ?>

<div class="content-container white" style="margin-top: 20px;">
  <h4>編集履歴</h4>
  <table class="table table-striped table-bordered table-hover js-data-table">
    <thead>
      <tr class="odd">
        <th>日付</th>
        <th>履歴</th>
      </tr>
    </thead>
    <tbody>
      <?php foreach($diff_messages as $diff_message) {
      ?>
        <tr class="gradeX odd" role="row">
          <td><?php echo date('Y年m月d日', strtotime($diff_message['date'])) ?></td>
          <td><?php echo $diff_message['message'] ?></td>
        </tr>
    <?php } ?>
    </tbody>
  </table>
</div>

<div class="content-container white" style="margin-top: 20px;">
  <h4>発行した別紙B</h4>
  <span class="btn-smaller btn-blue js-create" style="display:inline-flex" data-href="/admincontract/createcontractattachment?client_code=<?php echo $client_code ?>">
    <span class="icon-add-white"></span>
    発行
  </span>
  <table class="table table-striped table-bordered table-hover js-data-table-unsort">
    <thead>
      <tr class="odd">
        <th>バージョン</th>
        <th>申込日</th>
        <th>操作</th>
      </tr>
    </thead>
    <tbody>
      <?php foreach($existed_attachments as $attachment) { ?>
        <tr class="gradeX odd" role="row">
          <td><?php echo $attachment['version'] ?></td>
          <td><?php echo $attachment['attach_date'] ?></td>
          <td>
            <div class="flex attachment-info" style="flex-wrap:wrap;gap:6px" data-client_code="<?php echo $attachment['client_code'] ?>" data-version="<?php echo $attachment['version'] ?>">
              <span class="btn round image edit js-edit">編集</span>
              <span class="btn round image preview js-preview">プレビュー</span>
              <span class="btn round image detail js-download">ダウンロード</span>
            </div>
          </td>
        </tr>
      <?php } ?>
    </tbody>
  </table>
</div>