<style>
.flex.check-wrapper {
  justify-content: space-between;
  align-items: center;
  gap: 4px;
}
span.checkflg {
  width: 14px;
  height: 14px;
  display: inline-block;
  background-size: contain;
  background-repeat: no-repeat;
}
.checkflg.flg-0 {
  background-image: url('/assets/common/images/icon_fail.svg');
}
.checkflg.flg-1 {
  background-image: url('/assets/common/images/icon_success.svg');
}
.search-wrapper {
  display: flex;
  gap: 20px;
  align-items: center;
}
</style>

<div class="content-container">
  <div class="flex-x-between">
    <div>契約管理 - 未チェック一覧</div>
  </div>
</div>
<div class="content-container light-gray" style="margin-bottom: 20px">
  <div class="search-wrapper">
    <label class="control-label">担当者</label>
    <div class="">
      <select class="form-control select2me" name="representative" style="width:200px;">
        <option value="">-</option>
        <?php foreach($admin_users as $user_id => $user_name) { ?>
        <option value="<?php echo $user_id; ?>" <?php echo ($post['representative'] !== NULL && $post['representative'] == $user_id) ? 'selected' : '' ?>><?php echo $user_name; ?></option>
        <?php } ?>
      </select>
    </div>
    <input type="checkbox" name="exclude_admin" id="exclude_admin" <?php echo $post['exclude_admin'] == true ? 'checked' : '' ?> value="1">
    <label for="exclude_admin" style="margin-bottom:0">管理者のみ</label>
    <input type="checkbox" name="exclude_other" id="exclude_other" <?php echo $post['exclude_other'] == true ? 'checked' : '' ?>  value="1">
    <label for="exclude_other" style="margin-bottom:0">経理・管理者のみ</label>
    <span class="btn-smaller btn-blue js-search"><i class="fa fa-search mr10"></i>
      <?php echo __('admin.common.button.search') ?>
    </span>
  </div>
</div>

<div class="content-container white">
  <table class="table table-striped table-bordered table-hover js-data-table">
    <thead>
      <tr class="odd">
        <th>コード</th>
        <th>種類</th>
        <th>取引先名</th>
        <th>請求先企業名</th>
        <th>請求対象アカウント</th>
        <th>品目</th>
        <th>費用</th>
        <th>請求開始日</th>
        <th>チェック状態</th>
      </tr>
    </thead>
    <tbody>
      <?php foreach ($checklist as $check) { ?>
        <tr class="gradeX odd" role="row">
          <td>
            <?php 
              $type = $check['type'];
              $code = $check['code'];
              $seq = $check['seq'];
              $url = '/admincontract/' . $type . '?id=' . $code;
              if ($type == 'invoice') {
                $url .= '&seq=' . $seq;
              }
            ?>
            <a class="link-animate" href="<?php echo $url; ?>"><?php echo $code; ?></a>
          </td>
          <td>
            <?php
              $type = match ($check['type']) {
                'contract' => '契約',
                'client' => '取引先',
                'invoice' => '請求先',
              };
              echo $type;
            ?>
          </td>
          <td>
            <?php echo $check['client_name'] ?? '-' ?>
          </td>
          <td>
            <?php echo $check['depart'] ?? '-' ?>
          </td>
          <td>
            <div style="display:flex;gap:6px;flex-wrap:wrap;">
            <?php
              if (!isset($check['billing_account'])) echo '-';
              foreach ($check['billing_account'] as $account) {
                echo "<span class='btn round edit light-blue js-account-name' style='user-select: text;'>{$account}</span>";
              }
            ?>
            </div>
          </td>
          <td>
            <?php echo $check['item_name'] ?? '-' ?>
          </td>
          <td>
            <?php echo $check['cost'] ?? '-' ?>
          </td>
          <td>
            <?php echo $check['billing_start_date'] ?? '-' ?>
          </td>
          <td>
            <div class="flex" style="flex-direction:column;gap:4px">
              <?php 
                $check_status = $check['check_status'];
                $check_text = '';
                if (!is_null($check_status['cs_checkflg'])) {
                  $check_text .= "<div class='flex check-wrapper'>";
                  $check_text .= "<span>CS: </span>";
                  $check_text .= "<span class='checkflg flg-" . $check_status['cs_checkflg'] . "'></span>";
                  $check_text .= "</div>";
                }
                
                if (!is_null($check_status['sales_checkflg'])) {
                  $check_text .= "<div class='flex check-wrapper'>";
                  $check_text .= "<span>営業: </span>";
                  $check_text .= "<span class='checkflg flg-" . $check_status['sales_checkflg'] . "'></span>";
                  $check_text .= "</div>";
                }
                if (!is_null($check_status['accountant_checkflg'])) {
                  $check_text .= "<div class='flex check-wrapper'>";
                  $check_text .= "<span>経理: </span>";
                  $check_text .= "<span class='checkflg flg-" . $check_status['accountant_checkflg'] . "'></span>";
                  $check_text .= "</div>";
                }
                if (!is_null($check_status['admin_checkflg'])) {
                  $check_text .= "<div class='flex check-wrapper'>";
                  $check_text .= "<span>管理者: </span>";
                  $check_text .= "<span class='checkflg flg-" . $check_status['admin_checkflg'] . "'></span>";
                  $check_text .= "</div>";
                }
                echo $check_text;
              ?>
            </div>
          </td>
        </tr>
      <?php } ?>
    </tbody>
  </table>
</div>