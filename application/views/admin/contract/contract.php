<style>
.basic-label {
  width: 140px;
}
.billing_account-wrapper {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 4px;
  flex-wrap: wrap;
}
.seq-sections-wrapper {
  margin-top: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  align-items: flex-start;
}
.memo-wrapper {
  border: 1px solid #999;
  border-radius: 2px;
  padding: 4px;
  width: 500px;
  max-width: 100%;
  min-height: 100px;
}
.js-contract-diff {
  display: flex;
  flex-direction: column;
}
.js-contract-diff .diff-title {
  font-size: 13px;
  font-weight: normal;
}
.diff-container {
  border: 1px solid #DDD;
  border-radius: 4px;
  background-color: white;
  padding: 6px;
  box-sizing: border-box;
  width: 300px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  overflow-y: auto;
}
.diff-container .diff-item {
  word-break: break-all;
}
.diff-container .diff-header {
  font-weight: bold;
  margin-bottom: 4px;
}
.cost-detail {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.cost-detail .cost-detail-item {
  display: flex;
  align-items: center;
  gap: 10px;
}
.contract-set-item {
  display: flex;
  align-items: center;
  gap: 10px;
}
.js-parent-item, .js-children-item {
  position: relative;
}
.js-parent-item.disabled::before, .js-children-item.disabled::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
  z-index: 1;
  border-radius: 4px;
}
</style>

<script>
const _client_options = <?php echo json_encode($client_options, JSON_UNESCAPED_UNICODE); ?>;
const _invoice_options = <?php echo json_encode($invoice_options, JSON_UNESCAPED_UNICODE); ?>;
const _item_options = <?php echo json_encode($item_options, JSON_UNESCAPED_UNICODE); ?>;
const _bot_options = <?php echo json_encode($bot_options, JSON_UNESCAPED_UNICODE); ?>;
const _cost_type_options = <?php echo json_encode($cost_type_options, JSON_UNESCAPED_UNICODE); ?>;
const _datas = <?php echo json_encode($contract_data['items'], JSON_UNESCAPED_UNICODE); ?>;
const _servicein_datas = <?php echo json_encode($servicein_data, JSON_UNESCAPED_UNICODE); ?>;
const _contract_diff = <?php echo json_encode($contract_diff, JSON_UNESCAPED_UNICODE); ?>;
const _user_options = <?php echo json_encode($user_options, JSON_UNESCAPED_UNICODE); ?>;
const _seq_index = <?php echo $seq_index; ?>;
const _contract_set = <?php echo json_encode($contract_set, JSON_UNESCAPED_UNICODE); ?>;
</script>
<script src="/assets/admin/contract/check.js"></script>
<script src="/assets/admin/contract/diff.js"></script>

<div class="content-container">
  <div class="flex-x-between">
    <div><a class="link-animate" href="/admincontract/contracts">契約一覧</a> > 契約編集</div>
  </div>
</div>

<div class="content-container white border">
  <div class="flex" style="justify-content:space-between;align-items:baseline">
    <div class="section-container js-contract-container">
      <div class="setting-header">
        <?php if ($contract_id) { 
          echo '契約ID: ' . $contract_id;
        } else {
          echo '新規作成';
        } ?>
      </div>
      <!-- 契約ID -->
      <?php if ($contract_id) { ?>
      <input type="hidden" name="contract_id" value="<?php echo $contract_id ?>">
      <?php } ?>
      <!-- 共通項目 -->
      <div class="lines-container">
        <div class="basic-label">取引先コード</div>
        <select class="form-control select2me" style="width: 800px;" name="client_code">
          <option value=""></option>
          <?php foreach($client_options as $client_option) { ?>
            <option value="<?php echo $client_option['code'] ?>" <?php echo $client_option['code'] == $contract_data['client_code'] ? 'selected' : '' ?>><?php echo $client_option['code'] . ' - ' . $client_option['text'] . '（' . $client_option['depart'] . '）' ?></option>
          <?php } ?>
        </select>
        <a style="margin-left:10px" class="js-goto-client hide" href="/admincontract/client?id=<?php echo $contract_data['client'] ?>" target="_blank"><img src="/assets/common/images/icon_link.svg" alt=""></a>
      </div>
      <div class="lines-container">
        <div class="basic-label">請求先コード</div>
        <select class="form-control select2me" style="width: 800px;" name="invoice_code">
          <option value=""></option>
          <?php foreach($invoice_options as $invoice_option) { ?>
            <option value="<?php echo $invoice_option['code'] ?>" <?php echo $invoice_option['code'] == $contract_data['invoice_code'] ? 'selected' : '' ?>><?php echo $invoice_option['code'] . ' - ' . $invoice_option['text'] . '（' . $invoice_option['depart'] . '）'  ?></option>
          <?php } ?>
        </select>
        <a style="margin-left:10px" class="js-goto-invoice hide" href="/admincontract/invoice?id=<?php echo $contract_data['invoice_code'] ?>" target="_blank"><img src="/assets/common/images/icon_link.svg" alt=""></a>
      </div>
      <div class="lines-container">
        <div class="basic-label">CS担当者</div>
        <div class="js-cs_representative-user">
          <div class="selected_users_name_wrapper" data-type="cs">
            <?php if ($contract_data['cs_representative']) {
              $cs_user = [$contract_data['cs_representative']];
              foreach ($cs_user as $user_id) {
                foreach ($user_options as $user) {
                  if ($user['code'] == $user_id) {
                    echo "<span class='btn round light-blue js-user-select'>". htmlspecialchars($user['name']) . "</span>";
                  }
                }
              }
            }?>
            <span class="btn round light-blue js-user-select">設定</span>
          </div>
        </div>
        <input type="hidden" name="cs_representative" value="<?php echo $contract_data['cs_representative'] ?>">
      </div>
      <div class="lines-container">
        <div class="basic-label">営業担当者</div>
        <div class="js-sales_representative-user">
          <div class="selected_users_name_wrapper" data-type="sales">
            <?php if ($contract_data['sales_representative']) {
                $sales_user = [$contract_data['sales_representative']];
                foreach ($sales_user as $user_id) {
                  foreach ($user_options as $user) {
                    if ($user['code'] == $user_id) {
                      echo "<span class='btn round light-blue js-user-select'>". htmlspecialchars($user['name']) . "</span>";
                    }
                  }
                }
              }?>
              <span class="btn round light-blue js-user-select">設定</span>
          </div>
        </div>
        <input type="hidden" name="sales_representative" value="<?php echo $contract_data['sales_representative'] ?>" />
      </div>
      <div class="lines-container">
        <div class="basic-label">消費税率(%)</div>
        <input class="text-input-longer" type="number" name="tax_rate" value="<?php echo $contract_data['tax_rate'] ?>">
      </div>
      <div class="lines-container">
        <div class="basic-label">請求書品目表示用備考</div>
        <div class="talkappi-switch js-show_item_remarks" data-value="0"></div>
      </div>
      <div class="lines-container">
        <div class="basic-label">セットを設定</div>
        <div class="js-contract_set-container" style="display: flex; flex-direction: column; gap: 10px;">
          <?php if (is_array($contract_set)) {
            foreach ($contract_set as $set) {
              echo "<div class='contract-set-item'>";
              echo "<span class='btn round light-blue js-contract-set' data-setdata='". htmlspecialchars(json_encode($set, JSON_UNESCAPED_UNICODE)) ."'>セット: " . $set['setName'] . "</span>";
              echo "<span class='btn round image delete js-contract-set-delete'>削除</span>";
              echo "</div>";
            }
          } ?>
          <div class="btn round light-blue js-contract-set">セットを追加</div>
        </div>
      </div>

      <div class="seq-sections-wrapper js-seq-sections"></div>

      <div class="lines-container">
        <div class="basic-label">メモ</div>
        <textarea class="form-control" name="memo" rows="4"><?php echo $contract_data['memo'] ?></textarea>
      </div>
    </div>
  </div>

  <div class="section-container">
    <div class="actions-container" style="margin: 60px 0 0 140px">
      <div class="btn-larger btn-blue x-first js-action-save"><?php echo __('admin.common.button.save'); ?></div>
      <div class="btn-larger btn-white js-action-back"><?php echo __('admin.common.button.return_to_list'); ?></div>
      <div class="btn-larger btn-white js-goto-checklist">チェックリスト画面へ</div>
      <?php if ($_user->role_cd == '99' && $contract_id) { 
        $type = '';
        if ($can_delete) {
          $type = 'delete';
        } else {
          $type = 'invalid';
        }  
      ?>
        <div class="btn-larger btn-red js-action-delete" data-type="<?php echo $type ?>"><?php echo __("admin.common.button.$type"); ?></div>
      <?php } ?>
    </div>
  </div>

  <div class="section-container">
    <!-- チェック状態 -->
    <div class="js-checkcontainer"></div>
  </div>

  <div class="section-container js-diff-container">
    <!-- DIFF -->
    <div class="setting-header">編集履歴</div>
  </div>
</div>