<style>
.basic-label {
  width: 140px;
}
.js-sales_representative-user, .js-cs_representative-user {
  cursor: pointer;
}
.selected_users_name_wrapper {
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 4px;
}
.js-invoice-diff {
  display: flex;
  flex-direction: column;
}
.js-invoice-diff .diff-title {
  font-size: 13px;
  font-weight: normal;
}
.diff-container {
  border: 1px solid #DDD;
  border-radius: 4px;
  background-color: white;
  padding: 6px;
  box-sizing: border-box;
  width: 300px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  overflow-y: auto;
}
.diff-container .diff-item {
  word-break: break-all;
}
.diff-container .diff-header {
  font-weight: bold;
  margin-bottom: 4px;
}
input[name="invoice_code"][readonly] {
  background-color: #EEE;
}
</style>

<script>
const _existed_codes = <?php echo json_encode($existed_codes, JSON_UNESCAPED_UNICODE) ?>;
const _invoice_code = "<?php echo $invoice_code ?>";
const _seq = "<?php echo $post['seq'] ?>";
const _send_method_options = <?php echo json_encode($send_methods, JSON_UNESCAPED_UNICODE) ?>;
const _payment_timing_options = <?php echo json_encode($payment_timings, JSON_UNESCAPED_UNICODE) ?>;
const _payment_method_options = <?php echo json_encode($payment_methods, JSON_UNESCAPED_UNICODE) ?>;
const _invoice_diff = <?php echo json_encode($invoice_diff, JSON_UNESCAPED_UNICODE) ?>;
const _generations = <?php echo json_encode($generations, JSON_UNESCAPED_UNICODE) ?>;
</script>
<script src="/assets/admin/contract/check.js"></script>
<script src="/assets/admin/contract/diff.js"></script>
<script src="/assets/admin/contract/autocomplete.js"></script>

<div class="content-container">
  <div class="flex-x-between">
    <div><a class="link-animate" href="/admincontract/invoices">請求先一覧</a> > 請求先編集</div>
  </div>
</div>

<input type="hidden" name="seq" value="<?php echo $post['seq'] ?>">

<div class="content-container white border">
  <div class="flex" style="justify-content:space-between;align-items:baseline;" >
    <div style="width:70%">
      <div class="section-container">
        <!-- 世帯 -->
        <?php if (!empty($generation_options)) { ?>
          <div class="lines-container">
            <div class="talkappi-pulldown js-generation-pd" data-name="generation" data-value="<?php echo $post['seq'] ?>" data-source="<?php echo htmlspecialchars(json_encode($generation_options, JSON_UNESCAPED_UNICODE)) ?>"></div>
          </div>
        <?php } ?> 
        <!-- 世代開始日 -->
        <div class="lines-container">
          <div class="basic-label">世代開始日</div>
          <input class="talkappi-datepicker" data-name="ver_start_date" value="<?php echo $post['ver_start_date'] ?>" />
        </div>
        <!-- 世代終了日 -->
        <div class="lines-container">
          <div class="basic-label">世代終了日</div>
          <input class="talkappi-datepicker" data-name="ver_end_date" value="<?php echo $post['ver_end_date'] ?>" />
        </div>
        <!-- 請求先コード -->
        <div class="lines-container ">
          <div class="basic-label">請求先コード</div>
          <input type="text" value="<?php echo htmlspecialchars($post['invoice_code']) ?>" name="invoice_code" placeholder="請求先コード" class="text-input-longer" <?php if (!$can_delete) echo 'readonly' ?>>
        </div>
        <!-- 請求先名 -->
        <div class="lines-container">
          <div class="basic-label">請求先名</div>
          <input type="text" value="<?php echo htmlspecialchars($post['invoice_name']) ?>" name="invoice_name" placeholder="請求先名" class="text-input-longer">
        </div>
        <!-- 請求先部門名 -->
        <div class="lines-container">
          <div class="basic-label">請求先部門名</div>
          <input type="text" value="<?php echo htmlspecialchars($post['department_name']) ?>" name="department_name" placeholder="請求先部門名" class="text-input-longer">
        </div>
        <!-- 請求書送付先TO -->
        <div class="lines-container">
          <div class="basic-label">請求書送付先TO</div>
          <div class="multi-input js-multi-input" data-name="invoice_recipient_to" data-value="<?php echo htmlspecialchars($post['invoice_recipient_to']) ?>"></div>
        </div>
        <!-- 請求書送付先CC -->
        <div class="lines-container">
          <div class="basic-label">請求書送付先CC</div>
          <div class="multi-input js-multi-input" data-name="invoice_recipient_cc" data-value="<?php echo htmlspecialchars($post['invoice_recipient_cc']) ?>"></div>
        </div>
      </div>

      <div class="section-container">
        <div class="setting-header">請求書記載住所</div>
        <!-- 請求書記載住所 -->
        <div class="address-input js-address-input" data-name="invoice_address" data-value="<?php echo htmlspecialchars($post['invoice_address']) ?>"></div>
      </div>

      <div class="section-container">
        <div class="setting-header">送付</div>
        <!-- 送付方法 -->
        <div class="lines-container">
          <div class="basic-label">送付方法</div>
          <div>
            <div class="talkappi-pulldown" style="width: 200px;" data-name="send_method" data-value="<?php echo htmlspecialchars($post['send_method']) ?>" data-source="<?php echo htmlspecialchars(json_encode($send_methods, JSON_UNESCAPED_UNICODE)) ?>"></div>
          </div>
        </div>
        <!-- 請求書送付タイミング -->
        <div class="lines-container">
          <div class="basic-label">請求書送付タイミング</div>
          <div>
            <?php if($can_delete) { ?>
              <div class="talkappi-pulldown" style="width:200px;" data-name="invoice_send_timing" data-value="<?php echo htmlspecialchars($post['invoice_send_timing']) ?>" data-source="<?php echo htmlspecialchars(json_encode($invoice_send_timings, JSON_UNESCAPED_UNICODE)) ?>"></div>
            <?php } else { 
              $timing_name = '';
              foreach ($invoice_send_timings as $timing) {
                if ($timing['code'] == $post['invoice_send_timing']) {
                  $timing_name = $timing['text'];
                  break;
                }
              }
            ?>
              <div>
                <?php echo $timing_name ?>
                <input type="hidden" name="invoice_send_timing" value="<?php echo $post['invoice_send_timing'] ?>">
              </div>
            <?php } ?>
          </div>
        </div>
        <!-- 請求スパン -->
        <div class="lines-container">
          <div class="basic-label">請求スパン</div>
          <div>
            <?php if ($can_delete) { ?>
              <div class="talkappi-pulldown" style="width: 200px;" data-name="invoice_span" data-value="<?php echo htmlspecialchars($post['invoice_span']) ?>" data-source="<?php echo htmlspecialchars(json_encode($invoice_spans, JSON_UNESCAPED_UNICODE)) ?>"></div>
            <?php } else { 
              $span_name = '';
              foreach ($invoice_spans as $span) {
                if ($span['code'] == $post['invoice_span']) {
                  $span_name = $span['text'];
                  break;
                }
              }  
            ?>
              <div>
                <?php echo $span_name ?>
                <input type="hidden" name="invoice_span" value="<?php echo $post['invoice_span'] ?>">
              </div>
            <?php } ?>
          </div>
        </div>
        <!-- 支払タイミング -->
        <div class="lines-container">
          <div class="basic-label">支払タイミング</div>
          <div>
            <div class="talkappi-pulldown" style="width:200px" data-name="payment_timing" data-value="<?php echo $post['payment_timing'] ?>" data-source="<?php echo htmlspecialchars(json_encode($payment_timings, JSON_UNESCAPED_UNICODE)) ?>"></div>
          </div>
        </div>
        <!-- 支払方法 -->
        <div class="lines-container">
          <div class="basic-label">支払方法</div>
          <div>
            <div class="talkappi-pulldown" style="width:200px" data-name="payment_method" data-value="<?php echo $post['payment_method'] ?>" data-source="<?php echo htmlspecialchars(json_encode($payment_methods, JSON_UNESCAPED_UNICODE)) ?>"></div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="section-container">
    <div class="actions-container" style="margin: 60px 0 0 140px">
      <div class="btn-larger btn-blue x-first js-action-save"><?php echo __('admin.common.button.save'); ?></div>
      <div class="btn-larger btn-white js-action-back"><?php echo __('admin.common.button.return_to_list'); ?></div>
      <div class="btn-larger btn-white js-goto-checklist">チェックリスト画面へ</div>
      <?php if ($_user->role_cd == '99' && $invoice_code) { 
        $type = '';
        if ($can_delete) {
          $type = 'delete';
        } else {
          $type = 'invalid';
        }  
      ?>
        <div class="btn-larger btn-red js-action-delete" data-type="<?php echo $type ?>"><?php echo __("admin.common.button.$type"); ?></div>
      <?php } ?>
    </div>
  </div>

  <div class="section-container">
    <!-- チェック状態 -->
    <div class="js-checkcontainer"></div>
  </div>

  <div class="section-container js-diff-container">
    <!-- DIFF -->
    <div class="setting-header">編集履歴</div>
  </div>
</div>