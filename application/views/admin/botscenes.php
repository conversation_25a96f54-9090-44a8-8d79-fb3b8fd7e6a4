<input type="hidden" name="message" id="message" value="<?php echo ($message) ?>" />
<input type="hidden" name="act" id="act" value="" />
<input type="hidden" name="scene_cd" id="scene_cd" value="" />
<input type="hidden" name="func_type" id="func_type" value="" />

<div class="content-container white">

	<div class="flex-x-between" style="margin:12px 0px;">
		<div>導線一覧</div>
		<span class="btn-smaller btn-blue js-new-scene">
			<span class="icon-add-white"></span>
			新規作成
		</span>
	</div>

	<table class="table table-striped table-bordered table-hover js-data-table">
		<thead>
			<tr>
				<th>名称</th>
				<th>コード</th>
				<th>カスタマイズ状況</th>
				<th>デザイン設定（アイコン、吹き出し、色、フォントなど）</th>
				<th style="width: 60px;">操作</th>
			</tr>
		</thead>

		<tbody>
			<?php
			foreach ($scenes as $scene) {
			?>
				<tr class="gradeX odd" role="row">
					<!-- 導線名 -->
					<td style="vertical-align: middle;line-height: 24px;">
						<?php
						$scene_data = json_decode($scene->scene_data, true);
						echo ('<a class="js-basic-edit" scene_name="' . $scene->scene_name . '">' . $scene->label . '</a>');
						if ($post['default_scene_cd'] == $scene->scene_name) {
							echo ('<span class="badge badge-success" style="margin: 5px;">デフォルト導線</span>');
						}
						if ($post['very_scene_cd'] == $scene->scene_name) {
							echo ('<span class="badge badge-info" style="margin: 5px;">VERY旅中案内導線</span>');
						}
						?>
					</td>
					<!-- コード -->
					<td style="vertical-align: middle;">
						<?php echo ($scene->scene_name) ?><?php if ($scene->ref_scene_cd != '') echo ('<small style="margin-left:10px;color:#888;">継承 ' . $scene->ref_scene_cd . '</small>') ?><br />
					</td>
					<!-- カスタマイズ状況 -->
					<td style="vertical-align: middle;">
						<?php
						foreach ($scene_func_types as $k => $v) {
							if ($k == 'bot') {
								if (is_array($scene_data) && array_key_exists('bot_theme_cd', $scene_data)) {
									$theme_file = $scene_path . $scene->scene_name . '/' . $k . '/' . $scene_data['bot_theme_cd'] .  "/config.json";
								} else {
									$theme_file = $scene_path . $scene->scene_name . '/' . $k . "/A01/config.json";
								}
							} else if ($k == 'bubbles') {
								if (is_array($scene_data) && array_key_exists('bot_theme_cd', $scene_data)) {
									$theme_file = $scene_path . $scene->scene_name . '/' . 'bot' . '/' . $scene_data['bot_theme_cd'] .  "/config.json";
								} else {
									$theme_file = $scene_path . $scene->scene_name . '/' . 'bot' . "/A01/config.json";
								}
							} else if ($k == 'otapricecompare') {
								if (is_array($scene_data) && array_key_exists('bot_theme_cd', $scene_data)) {
									$theme_file = $scene_path . $scene->scene_name . '/' . 'bot' . '/' . $scene_data['bot_theme_cd'] .  "/config.json";
								} else {
									$theme_file = $scene_path . $scene->scene_name . '/' . 'bot' . "/A01/config.json";
								}
							} else {
								if ($_bot_setting['flg_faq_site'] == 0 && $k == 'faq') continue;
								if ($_bot_setting['flg_accept_order'] == 0 && $k == 'reserve') continue;
								if ($k == 'site') {
									$theme_file = $scene_path . $scene->scene_name . '/' . $k . "/template";
								} else {
									$theme_file = $scene_path . $scene->scene_name . '/' . $k . "/config.json";
								}
							}
							if (file_exists($theme_file)) {
								$color = $v['color'];
								$color = 'primary';
							} else {
								$color = "grey-steel";
								$color = 'default';
							}
							echo ('<span class="label label-' . $color . '" style="margin-left:5px;font-size:11px;">' . $v['label'] . '</span>');
						}
						?>
					</td>
					<!-- デザイン設定（アイコン、吹き出し、色、フォントなど） -->
					<td style="vertical-align: middle;line-height: 24px;">
						<?php
						echo ('<div class="btn round image setting js-basic-edit" scene_name="' . $scene->scene_name . '">' . '基本設定' . '</div>');
						foreach ($scene_func_types as $k => $v) {
							if ($k == 'bot' && $scene->use_bot_flg == 1) echo ('<div class="btn round image setting js-bot-edit" scene_name="' . $scene->scene_name . '">' . $v['label'] . '</div>');
							if ($k == 'faq' && $scene->use_faq_flg == 1) echo ('<div class="btn round image setting js-faq-edit" scene_name="' . $scene->scene_name . '">' . $v['label'] . '</div>');
							if ($k == 'survey' && $scene->use_survey_flg == 1) echo ('<div class="btn round image setting js-survey-edit" scene_name="' . $scene->scene_name . '">' . $v['label'] . '</div>');
							if ($k == 'inquiry' && $scene->use_inquiry_flg == 1) echo ('<div class="btn round image setting js-inquiry-edit" scene_name="' . $scene->scene_name . '">' . $v['label'] . '</div>');
							if ($k == 'site' && $scene->use_page_flg == 1) echo ('<div class="btn round image setting js-page-edit" scene_name="' . $scene->scene_name . '">' . $v['label'] . '</div>');
						}
						?>
					</td>
					<td style="vertical-align: middle;text-align: center;">
						<div class="btn round image delete js-delete" style="margin-top:10px;" scene_name="<?php echo ($scene->scene_name) ?>">削除</div>
					</td>
				</tr>
			<?php } ?>
		</tbody>
	</table>
	<br />
	<div class="form-group">
		<?php if ($_user->role_cd == "99") { ?>
			<label class="control-label col-md-2 label-fix-8">※管理者一括登録</label>
			<div class="col-md-3">
				<div class="fileinput fileinput-new" data-provides="fileinput">
					<div class="input-group input-large">
						<div class="form-control uneditable-input span3" data-trigger="fileinput">
							<i class="fa fa-file fileinput-exists"></i>&nbsp; <span class="fileinput-filename">
							</span>
						</div>
						<span class="input-group-addon btn default btn-file">
							<span class="fileinput-new">
								Select file </span>
							<span class="fileinput-exists">
								Change </span>
							<input id="scenedata" type="file" name="scenedata">
						</span>
						<a href="#" class="input-group-addon btn red fileinput-exists" data-dismiss="fileinput">
							Remove </a>
					</div>
				</div><br />
			</div>
			<div class="col-md-1">
				<a href="/docs/samples/scene.csv">サンプルDL</a>
			</div>
			<div class="col-md-1">
				<button type="button" class="btn red js-action-import">登録</button>
			</div>
		<?php } ?>
	</div>
</div>