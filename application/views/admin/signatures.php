<input type="hidden" name="signature_id" id="signature_id" value="" />
<div style="margin: 10px;">
    <div class="flex-x-between">
        <div></div>
        <span class="btn-smaller btn-blue js-new-signature">
            <span class="icon-add-white"></span>
            <?php echo __('admin.common.button.create_new') ?>
        </span>
    </div>
</div>
<div class="content-container white">
    <div class="react-admin-signatures" data-path="<?php echo $_path ?>"></div>
</div>

<?php
$columnItems = [];

foreach ($signatures as $signature) {
    // カラムのアイテム
    $columnItems[] = [
        "path"=> $_path,
        "signature_id" => $signature["id"],
        "signature_title" =>  $signature["sign_title"],
        "signature_detail" => $signature["sign_detail"],
        "last_updated" => $signature["upd_user_name"] . "\n" . date('Y/m/d H:i', strtotime($signature["upd_time"])),
        "operation" => [
            "edit" => true,
            "copy" => true,
            "delete" => true
        ],
    ];
}

// ヘッダー
$headers_for_table = [
    [
        'Header' => __('admin.signature.label.title'),
        'accessor' => 'signature_title',
        'style' => ['width' => '10rem', 'wordBreak' => 'normal']
    ],
    [
        'Header' => __('admin.signature.label.detail'),
        'accessor' => 'signature_detail',
        'style' => ['width' => '20rem', 'wordBreak' => 'normal']
    ],
    [
        'Header' => __('admin.common.label.edit'),
        'accessor' => 'last_updated',
        'style' => ['width' => '10rem', 'whiteSpace' => 'nowrap']
    ],
    [
        'Header' => __('admin.common.label.operation'),
        'accessor' => 'operation',
        'style' => ['textAlign' => '-webkit-center', 'width' => '5rem']
    ]
];
?>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/1.12.4/jquery.min.js"></script>
<script type="text/javascript" src="/assets/common/react/pages/admin/signatures.bundle.js"></script>

<script type="text/javascript">
const _columns_for_table = <?php echo json_encode($columnItems) ?>;
const _headers_for_table = <?php echo json_encode($headers_for_table) ?>;
const _lang_cd = '<?php echo $_lang_cd ?>';
jQuery(document).ready(function($){
    window.talkappi_setupSignaturesPage({
        tableColumns: _headers_for_table,
        tableData: Object.values(_columns_for_table),
        lang_cd: _lang_cd
    });
});
</script>