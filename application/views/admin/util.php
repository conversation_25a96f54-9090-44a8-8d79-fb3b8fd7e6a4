			<!-- BEGIN PAGE HEADER-->

			<div class="page-head">
				<!-- BEGIN PAGE TITLE -->
				<div class="page-title">
					<h1>補助ツール<small></small></h1>
				</div>
				<!-- END PAGE TITLE -->
			</div>
			<!-- END PAGE HEAD -->
			<!-- END PAGE HEADER-->
			<!-- BEGIN PAGE CONTENT-->
			<div class="row">
				<div class="col-md-12">
			        <!-- Page Content -->
			        <div id="page-wrapper">
			        <div class="portlet light">
						<div class="tabbable-line">
							<ul class="nav nav-tabs ">
								<li class="<?php if ($type=='util') echo('active')?>">
									<a href="/admin/util">
									※データ整理</a>
								</li>
							</ul>
						</div>
						<div class="portlet box">
							<div class="portlet-body">
								<input type="hidden" name="buttonType" id="buttonType" value="" />
										<div class="form-group">
											<label class="control-label col-md-3" style="text-align: left;font-weight:bold;">MenuGroupから多言語メッセージ移行</label>
										</div>
										<div class="form-group">
											<label class="control-label col-md-2">現在BOTのmenugroup</label>
											<div class="col-md-3">
												<?php echo Form::select('menu_group_id', $menu_groups, '', array('id'=>'menu_group_id','class'=>'form-control'))?>
											</div>
											<label class="control-label col-md-2">移行先メッセージCD</label>
											<div class="col-md-3">
												<input name="msg_cd" id="msg_cd" type="text" class="form-control" placeholder="" value="">
											</div>
											<div class="col-md-2">
												<button type="button" id="menugroup" class="btn blue util">メニュー移行</button>
											</div>										
										</div>
										<br>
										<div class="form-group">
											<label class="control-label col-md-3" style="text-align: left;font-weight:bold;">Wechatコマンド実行</label>
										</div>
										<div class="form-group">
											<label class="control-label col-md-2">機能選択</label>
											<div class="col-md-2">
												<?php echo Form::select('wechat_cmd', $wechat_cmds, '', array('id'=>'wechat_cmd','class'=>'form-control'))?>
											</div>
											<div class="col-md-6">
												<textarea name="command" id="command" rows="2" class="form-control" placeholder="JSON"></textarea>
											</div>
											<div class="col-md-1">
												<button type="button" id="wechatexec" class="btn blue util">実行</button>
											</div>										
										</div>
										<br>
										<div class="form-group">
											<label class="control-label col-md-3" style="text-align: left;font-weight:bold;">QRコード一括作成ツール</label>
										</div>										
										<div class="form-group">
											<label class="control-label col-md-2">QRファイル</label>
											<div class="col-md-4">
												<div class="fileinput fileinput-new" data-provides="fileinput">
													<div class="input-group input-large">
														<div class="form-control uneditable-input span3" data-trigger="fileinput">
															<i class="fa fa-file fileinput-exists"></i>&nbsp; <span class="fileinput-filename">
															</span>
														</div>
														<span class="input-group-addon btn default btn-file">
														<span class="fileinput-new">
														Select file </span>
														<span class="fileinput-exists">
														Change </span>
														<input id="qrdata" type="file" name="qrdata">
														</span>
														<a href="#" class="input-group-addon btn red fileinput-exists" data-dismiss="fileinput">
														Remove </a>
													</div>
												</div><br/>
											</div>
											<div class="col-md-1">
												<button type="button" id="qrcode" class="btn blue util">QR作成</button>
											</div>
											<div class="col-md-1">
												<a href="/docs/samples/qr.txt" download>サンプル</a>
											</div>
										</div>
										<br>								
										
										<div class="form-group">
											<label class="control-label col-md-2">TFAQ作成CSV</label>
											<div class="col-md-4">
												<div class="fileinput fileinput-new" data-provides="fileinput">
													<div class="input-group input-large">
														<div class="form-control uneditable-input span3" data-trigger="fileinput">
															<i class="fa fa-file fileinput-exists"></i>&nbsp; <span class="fileinput-filename">
															</span>
														</div>
														<span class="input-group-addon btn default btn-file">
														<span class="fileinput-new">
														Select file </span>
														<span class="fileinput-exists">
														Change </span>
														<input id="faqdata" type="file" name="faqdata">
														</span>
														<a href="#" class="input-group-addon btn red fileinput-exists" data-dismiss="fileinput">
														Remove </a>
													</div>
												</div><br/>
											</div>
											<div class="col-md-1">
												<button type="button" id="faqcsv" class="btn blue util">CSV作成</button>
											</div>
										</div>
										<br>
										
										<div class="form-group">
											<label class="control-label col-md-2">ユーザパスワード暗号化</label>
											<div class="col-md-4">
												<div class="fileinput fileinput-new" data-provides="fileinput">
													<div class="input-group input-large">
														<div class="form-control uneditable-input span3" data-trigger="fileinput">
															<i class="fa fa-file fileinput-exists"></i>&nbsp; <span class="fileinput-filename">
															</span>
														</div>
														<span class="input-group-addon btn default btn-file">
														<span class="fileinput-new">
														Select file </span>
														<span class="fileinput-exists">
														Change </span>
														<input id="userdata" type="file" name="userdata">
														</span>
														<a href="#" class="input-group-addon btn red fileinput-exists" data-dismiss="fileinput">
														Remove </a>
													</div>
												</div><br/>
											</div>
											<div class="col-md-1">
												<button type="button" id="usercsv" class="btn blue util">暗号化作成</button>
											</div>
										</div>
										<br>

										<div class="form-group">
											<label class="control-label col-md-3" style="text-align: left;font-weight:bold;">その他ツール</label>
										</div>
										<div class="form-group">
											<label class="control-label col-md-2">機能選択</label>
											<div class="col-md-4">
												<?php echo Form::select('functions', $functions, '', array('id'=>'functions','class'=>'form-control'))?>
											</div>
											<div class="col-md-1">
												<button type="button" id="others" class="btn blue others">実行</button>
											</div>
										</div>

										<!-- <button type="button" id="shortAnswer" class="btn blue util">FAQ URLの翻訳と短縮</button> -->
										<!-- <button type="button" id="shortItemDesc" class="btn blue util" style="display: none;">コンテンツURLの翻訳と短縮</button><br><br><br> -->
										<!-- 
										<button type="button" id="autokeyword" class="btn blue util">
										コンテンツKeyword作成</button>										
										<button type="button" id="clearcache" class="btn yellow util">
										該当BOTのCDNファイル最新化</button>	
										
										<button type="button" id="tasteosaka" class="btn yellow util">
										tasteosaka</button>
										<button type="button" id="tosatrippost" class="btn yellow util">
										tosatrip投稿</button>										
										<button type="button" id="tosatriparea" class="btn yellow util">
										tosatripエリアコード作成</button>
										<button type="button" id="tosatripareaitem" class="btn yellow util">
										tosatripおすすめエリアコード分類</button><br><br><br>
										<button type="button" id="otshotel" class="btn green util">
										OTS hotel</button>	
										<button type="button" id="otsmedia" class="btn green util">
										OTS media</button>			
										<button type="button" id="otsrentcar" class="btn green util">
										OTS RentCar</button><br><br><br>	
										<button type="button" id="sushi" class="btn yellow util">
										寿司</button><br><br><br>			
										<button type="button" id="atin-nagoya" class="btn green util">
										atin nagoya</button>			
										<button type="button" id="atin-tokyo" class="btn green util">
										atin tokyo</button><br><br><br>	
										<button type="button" id="venusfort" class="btn green util" style="display: none;">
										venusfort</button>
										<button type="button" id="venusfortmap" class="btn green util" style="display: none;">
										venusfort map</button>
										<button type="button" id="tsunagujapan" class="btn green util" style="display: none;">
										tsunagu japan</button>
										<button type="button" id="itemrelation" class="btn yellow util" style="display: none;">
										itemrelation</button>	
										<button type="button" id="test" class="btn yellow util">
										test </button>	
										<button type="button" id="msgid" class="btn yellow util" style="display: none;">
										msgid</button>
										<button type="button" id="linemenu" class="btn yellow util">
										linemenu</button>
										<button type="button" id="keyword" class="btn yellow util">
										keyword</button>		
										<button type="button" id="classcode" class="btn yellow util">
										classcode</button>
										<button type="button" id="contact" class="btn yellow util" style="display: none;">
										contact</button>		
										<button type="button" id="welcomeimagewechat" class="btn yellow util" style="display: none;">
										welcomeimagewechat</button>
										<button type="button" id="wechatdata" class="btn yellow util" style="display: none;">
										wechatdata</button>			
										<button type="button" id="settingclear" class="btn yellow util" >
										settingclear</button>
										<button type="button" id="defaultscene" class="btn yellow util" >
										defaultscene</button>
										<button type="button" id="clearLog" class="btn blue util">
										有人対応ログのクリア</button>										
										 -->	
												
							</div>
						</div>
					</div>
			        </div>
			        <!-- /#page-wrapper -->
				</div>
			</div>
			<?php if ($error_list != NULL) {?>
			<div class="row">
				<div class="col-md-12">
			        <!-- Page Content -->
			        <div id="page-wrapper">
			        <div class="portlet light">
						<div class="portlet box">
							<div class="portlet-body">
							<table class="table table-striped table-bordered table-hover js-data-table">
							<thead>
							<tr>
								<th>
									
								</th>
								<th>
									 
								</th>
								<th>
									 エラー
								</th>
								<th>
								</th>
								<th>
								</th>
								<th>
								</th>
							</tr>
							</thead>

							<tbody>
							<?php
								foreach ($error_list["error_enter"] as $err) {
							?>
							<tr class="gradeX odd" role="row">
								<td class="sorting_1">
									 <a href="/admin/item?id=<?php echo($err['item_id'])?>" ><?php echo($err['item_name'])?></a>
								</td>
								<td>
									<?php echo($err['lang_cd'])?>
								</td>
								<td>
									電話番号やURLの前後改行が入った
								</td>
								<td>
									tel=<?php echo($err['tel'])?>
								</td>
								<td>
									url=<?php echo($err['description'])?>
								</td>
								<td>
								</td>
							</tr>
							<?php } ?>
							<?php
								foreach ($error_list["error_quotation"] as $err) {
							?>
							<tr class="gradeX odd" role="row">
								<td class="sorting_1">
									 <a href="/admin/item?id=<?php echo($err['item_id'])?>" ><?php echo($err['item_name'])?></a>
								</td>
								<td>
									<?php echo($err['lang_cd'])?>
								</td>
								<td>
									"などがタイトルや説明分に入った
								</td>
								<td>
									item_name=<?php echo($err['item_name'])?>
								</td>
								<td>
									sell_point=<?php echo($err['sell_point'])?>
								</td>
								<td>
									sell_point_line=<?php echo($err['sell_point_line'])?>
								</td>
							</tr>
							<?php } ?>
							<?php
								foreach ($error_list["error_undefine"] as $err) {
							?>
							<tr class="gradeX odd" role="row">
								<td class="sorting_1">
									 <a href="/admin/item?id=<?php echo($err['item_id'])?>" ><?php echo($err['item_name'])?></a>
								</td>
								<td>
									<?php echo($err['lang_cd'])?>
								</td>
								<td>
									sell_pointまたはsell_point_lineが未設定の場合
								</td>
								<td>
									sell_point=<?php echo($err['sell_point'])?>
								</td>
								<td>
									sell_point_line=<?php echo($err['sell_point_line'])?>
								</td>
								<td>
								</td>
							</tr>
							<?php } ?>
							<?php
								foreach ($error_list["error_keyword"] as $err) {
							?>
							<tr class="gradeX odd" role="row">
								<td class="sorting_1">
								</td>
								<td>
									<?php echo($err['find_id'])?>
								</td>
								<td>
									「, 」のように半角スペースが入る
								</td>
								<td>
									sell_point=<?php echo($err['keyword'])?>
								</td>
								<td>
									
								</td>
								<td>
								</td>
							</tr>
							<?php } ?>
							<?php
								foreach ($error_list["error_itemkeyword"] as $err) {
							?>
							<tr class="gradeX odd" role="row">
								<td class="sorting_1">
									 <a href="/admin/item?id=<?php echo($err['item_id'])?>" ><?php echo($err['item_id'])?></a>
								</td>
								<td>
									<?php echo($err['bot_id'])?>
								</td>
								<td>
									「, 」のように半角スペースが入る
								</td>
								<td>
									sell_point=<?php echo($err['keyword'])?>
								</td>
								<td>
									
								</td>
								<td>
								</td>
							</tr>
							<?php } ?>										
							</tbody>
							</table>
							</div>
						</div>
					</div>
			        </div>
			        <!-- /#page-wrapper -->
				</div>
			</div>
			<?php }?>
			<!-- END PAGE CONTENT-->
