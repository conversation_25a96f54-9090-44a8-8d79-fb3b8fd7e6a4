<input type="hidden" name="act" id="act" value="" />
<input type="hidden" name="sitepage_cd" id="sitepage_cd" value="" />

<div class="content-container" style="margin-top: -12px;">
    <div class="flex-x-between">
        <div></div>
        <a href="/<?php echo $_path?>/sitepage">
        <span class="btn-smaller btn-blue js-new-maximum">
          <span class="icon-add-white"></span>
          <?php echo __('admin.common.button.create_new') ?>
        </a>
    </div>
</div>

<div class="content-container white">
  <table class="table table-striped table-bordered table-hover js-data-table">
    <thead>
      <tr>
        <th><?php echo __('admin.sitepages.label.title') ?></th>
        <th><?php echo __('admin.common.label.access_count') ?></th>
        <th><?php echo __('admin.common.label.last_update') ?></th>
        <th style="width:140px;"><?php echo __('admin.common.label.operation') ?></th>
      </tr>
    </thead>
    <tbody>
      <?php
        foreach ($items as $item) {
      ?>
        <tr class="gradeX odd" role="row">
          <!-- ページタイトル -->
          <td class="">
            <a class="link-animate" href="/<?php echo $_path?>/sitepage?id=<?php echo ($item['page_cd']) ?>" class="js-name"><?php echo($item['page_name']) ?></a>  
          </td>
          <!-- PV数 -->
          <td style="text-align:center;">
            <?php if(array_key_exists($item['page_cd'], $access_count)) {
              echo $access_count[$item['page_cd']];
            }?>
          </td>
          <!-- 更新日時とユーザー -->
          <td class=""><?php echo($item['upd_time']."<br>".$item['name']) ?></td>
          <!-- 詳細 -->
          <td>
            <a class="link-animate" href="/<?php echo $_path?>/sitepage?id=<?php echo ($item['page_cd']) ?>"><div style="margin-top: 2px;" class="btn round image edit js-memo"><?php echo __('admin.common.button.modify') ?></div></a>
            <div style="margin-top: 2px;" class="btn round image copy js-copy" data-sitepage_cd="<?php echo ($item['page_cd']) ?>"><?php echo __('admin.common.button.clone') ?></div>
            <?php
            if ($item["scene_cd"] != NULL) {
              $item["scene_cd"] = $item["scene_cd"];
            } else {
              $item["scene_cd"] = $facility_cd;
            }
            $verify_url = $sitepage_url . '?f=' . $item["scene_cd"] . '&id=' . $item["page_cd"];
            ?>
            <div style="margin-top: 2px;" class="btn round image link js-link" data-clipboard-text="<?php echo ($verify_url) ?>"><?php echo __('admin.common.button.copy_public_url') ?></div>
            <a href=<?php echo($verify_url)?> target="_blank"><div style="margin-top: 2px;" class="btn round image preview js-preview"><?php echo __('admin.common.button.preview') ?></div></a>
          </td>
        </tr>
      <?php } ?>
    </tbody>
  </table>
</div>

