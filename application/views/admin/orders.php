
 <?php echo $menu?>
 <div class="content-container white border">
	<input type="hidden" name="service_id" id="service_id" value="" />
	<div class="form-body">
		<div class="form-group">
			<label class="control-label col-md-2">期間</label>		
			<div class="col-md-2">
				<?php echo Form::select('date_cond', ['reserve'=>'予約日時','check'=>'宿泊期間', 'all'=>'全期間'], $post['date_cond'], array('id'=>'date_cond','class'=>'form-control','onchange'=>'changePeriod()'))?>
			</div>
			<div class="col-md-5 flex" id="date_range">
				<input name="start_date" id="start_date" value="<?php echo($post['start_date'])?>" class="talkappi-datepicker" type="text"/>
				<input name="end_date" id="end_date" value="<?php echo($post['end_date'])?>" class="talkappi-datepicker" type="text"/>
			</div>																													
		</div>
		<div class="form-group">	
			<div id="order_no_field">
				<label class="control-label col-md-2">受付番号</label>
				<div class="col-md-2">
					<input name="order_no" id="order_no" value="<?php echo($post['order_no'])?>" class="form-control" type="text"/>
				</div>
			</div>
			<div class="col-md-1" id="spacing"></div>
			<label id="status_label" class="control-label col-md-1 text-nowrap">ステータス</label>
			<div class="col-md-2">
				<?php echo Form::select('status_cd', $status_array, $post['status_cd'], array('id'=>'status_cd','class'=>'form-control'))?>
			</div>
			<div id="search_button" class="col-md-3" style="padding-left: 60px;">
				<button type="submit" class="btn yellow">
				<i class="fa fa-search mr10"></i>検索</button>
			</div>
		</div>
	</div>	

	<div id="service-table" class="portlet-body">
		<table class="table table-striped table-bordered table-hover js-data-table">
			<thead>
				<tr>
					<th style="width:120px;">予約番号</th>
					<th>ステータス</th>								
					<th>プラン</th>
					<th style="width:80px;">予約者</th>
					<th>宿泊期間</th>								
					<th>予約日時</th>	
					<th>詳細</th>																					
				</tr>
			</thead>
			<tbody id="experience-body">						
				<?php echo $servicetable ?>
			</tbody>
		</table>							
	</div>
 </div>
<?php echo $talkbox ?>