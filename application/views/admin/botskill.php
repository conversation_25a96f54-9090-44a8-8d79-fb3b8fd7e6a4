<?php 
	if (strlen($_active_menu) > 4) {
		$tab_menu = View::factory ('admin/menutab');
		echo($tab_menu);
	}
?>
<div class="content-container white border">
	<div class="form-body">
		<?php
		foreach($intents as $intent){
			$color = 'success';
			echo('<div class="form-group">');
			$key = $intent['intent_cd'] . "#" . $intent['sub_intent_cd'];
			if (array_key_exists($key, $bot_skill)) {
				$row = $bot_skill[$key];
			}
			else {
				$row = ['intent_name'=>$intent['intent_name'], 'intent_cd'=>$intent['intent_cd'], 'sub_intent_cd'=>$intent['sub_intent_cd'], 
						'intent_class_cd'=>$intent['intent_class_cd']];
			}
			if ($_bot->bot_id == 0) $row['intent_name'] = $row['intent_name'] . "(" . $bot_class[$row['intent_class_cd']] . ")";
			if (array_key_exists($key, $bot_skill_self) && $_bot->bot_id != 0) {
				$row = $bot_skill_self[$key];
				echo('<label class="control-label col-md-2" style="color:red;">' . $row['intent_name'] . '</label>');
				$color = 'danger';
			}
			else {
				echo('<label class="control-label col-md-2"">' .  $row['intent_name'] . '</label>');
			}
			echo('<div class="col-md-10" intent_cd="' . $row['intent_cd'] . '" sub_intent_cd="' . $row['sub_intent_cd']. '" style="line-height:2;word-break: break-all;">');
			if (array_key_exists($key, $skills)) {	
				echo('<div class="talkappi-skill-select" data-value=\'' . json_encode($skills[$key], JSON_UNESCAPED_UNICODE) . '\' data-mode="skills"></div>');
			}
			else {
				echo('<div class="talkappi-skill-select" data-value="[]" data-mode="skills"></div>');
			}			
			echo('</div>');	
			echo('</div>');	
		}
		?>
	</div>
	<div class="form-actions">
		<div class="row" style="display:none;">
			<div class="col-md-offset-5 col-md-6">
				<button type="submit" id="saveButton" class="btn green mr10" <?php if ($bot->bot_status_cd == '04' && $_user->role_cd != "99") echo("disabled"); ?>>
				<i class="fa fa-save mr10"></i>保存</button>
				<button type="button" id="resetButton" class="btn yellow">リセット</button>
			</div>
		</div>
	</div>
</div>

