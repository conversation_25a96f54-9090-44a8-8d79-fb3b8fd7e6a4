<div class="flexbox">
	<div>
		<div class="category-separator">
			<div class="setting-header"><?php echo __('user_flow.survey.label.show.setting'); ?></div>
		</div>
		<div class="lines-container">
			<div class="basic-label"><?php echo __('user_flow.faq.label.logo'); ?></div>
			<div class="" style="width:400px">
				<div style="" class="talkappi-upload" data-name="logo_file_base64" data-label="<?php echo basename($post['logo-file'])?>" data-type="img" data-ratio="1:1" data-url="<?php echo($post['logo-file'])?>"></div>
			</div>
		</div>
		<div class="lines-container" style="align-items: center;">
			<label class="basic-label"><?php echo __('user_flow.survey.label.theme.color'); ?></label>
			<div class="">
				<div class="talkappi-colorpicker js-theme-color" data-name="theme-bk-color" data-value="<?php if ($post != NULL) echo ($post['theme-bk-color']) ?>"></div>
			</div>
		</div>
		<div class="lines-container" style="align-items: center;">
			<label class="basic-label"><?php echo __('user_flow.survey.label.text.label.color'); ?></label>
			<div class="">
				<div class="talkappi-colorpicker js-theme-color" data-name="theme-color" data-value="<?php if ($post != NULL) echo ($post['theme-color']) ?>"></div>
			</div>
		</div>
		<div class="lines-container">
			<div class="basic-label"><?php echo __('user_flow.survey.label.favicon'); ?></div>
			<div class="" style="width:400px">
				<div style="" class="talkappi-upload" data-name="favicon_file_base64" data-label="<?php echo basename($post['favicon-file'])?>" data-type="img" data-ratio="1:1" data-url="<?php echo($post['favicon-file'])?>"></div>
			</div>
		</div>		
		<div class="category-separator">
			<div class="setting-header"><?php echo __('user_flow.faq.label.button.setting'); ?></div>
		</div>
		<div class="lines-container" style="align-items: center;">
			<label class="basic-label"><?php echo __('user_flow.survey.label.close.color'); ?></label>
			<div class="">
				<div class="talkappi-colorpicker js-theme-color" data-name="close-button-color" data-value="<?php if ($post != NULL) echo ($post['close-button-color']) ?>"></div>
			</div>
		</div>
		<div class="form-group" style="display: none;">
			<label class="control-label col-md-3"><?php echo __('user_flow.survey.label.logo.radius'); ?></label>
			<div class="col-md-2">
				<input name="logo-radius" type="text" class="form-control" placeholder="0～100" value="<?php if ($post != NULL) echo ($post['logo-radius']) ?>">
			</div>
		</div>
		<div class="flexbox" style="flex-wrap:wrap">
			<div class="device-setting-container flexbox-space-top">
				<div class="font-standard font-family-v3 device-setting-container-title"><?php echo __('user_flow.faq.label.pc'); ?></div>
				<div class="font-standard font-family-v3 device-setting-container-title"><?php echo __('user_flow.survey.label.button1'); ?></div>
				<div class="flexbox-x-axis flexbox-space-top">
					<label class="basic-label"><?php echo __('user_flow.survey.label.button.color'); ?></label>
					<div class="">
						<div class="talkappi-colorpicker js-theme-color" data-name="primary-btn-bk-color" data-value="<?php if ($post != NULL) echo ($post['primary-btn-bk-color']) ?>"></div>
					</div>
					<div class="" style="margin-left:15px;">
						<div class="talkappi-colorpicker js-theme-color" data-name="primary-btn-bd-color" data-value="<?php if ($post != NULL) echo ($post['primary-btn-bd-color']) ?>"></div>
					</div>
				</div>
				<div class="flexbox-x-axis  flexbox-space-top">
					<div class="basic-label"><?php echo __('user_flow.survey.label.text.color'); ?></div>
					<div class="">
						<div class="talkappi-colorpicker js-theme-color" data-name="primary-btn-color" data-value="<?php if ($post != NULL) echo ($post['primary-btn-color']) ?>"></div>
					</div>
				</div>
				<div class="flexbox-space-top separator" style="margin-left: 25px;"></div>
				<div class="font-standard font-family-v3 device-setting-container-title"><?php echo __('user_flow.survey.label.button2'); ?></div>
				<div class="flexbox-x-axis flexbox-space-top">
					<label class="basic-label"><?php echo __('user_flow.survey.label.button.color'); ?></label>
					<div class="">
						<div class="talkappi-colorpicker js-theme-color" data-name="secondary-btn-bk-color" data-value="<?php if ($post != NULL) echo ($post['secondary-btn-bk-color']) ?>"></div>
					</div>
					<div class="" style="margin-left:15px;">
						<div class="talkappi-colorpicker js-theme-color" data-name="secondary-btn-bd-color" data-value="<?php if ($post != NULL) echo ($post['secondary-btn-bd-color']) ?>"></div>
					</div>
				</div>
				<div class="flexbox-x-axis flexbox-space-top">
					<div class="basic-label"><?php echo __('user_flow.survey.label.text.color'); ?></div>
					<div class="">
						<div class="talkappi-colorpicker js-theme-color" data-name="secondary-btn-color" data-value="<?php if ($post != NULL) echo ($post['secondary-btn-color']) ?>"></div>
					</div>
				</div>
			</div>
			<div class="device-setting-container flexbox-space-top">
				<div class="font-standard font-family-v3 device-setting-container-title"><?php echo __('user_flow.faq.label.mobile'); ?></div>
				<div class="font-standard font-family-v3 device-setting-container-title"><?php echo __('user_flow.survey.label.button1'); ?></div>
				<div class="flexbox-x-axis flexbox-space-top">
					<label class="basic-label"><?php echo __('user_flow.survey.label.button.color'); ?></label>
					<div class="">
						<div class="talkappi-colorpicker js-theme-color" data-name="m-primary-btn-bk-color" data-value="<?php if ($post != NULL) echo ($post['m-primary-btn-bk-color']) ?>"></div>
					</div>
					<div class="" style="margin-left:15px;">
						<div class="talkappi-colorpicker js-theme-color" data-name="m-primary-btn-bd-color" data-value="<?php if ($post != NULL) echo ($post['m-primary-btn-bd-color']) ?>"></div>
					</div>
				</div>
				<div class="flexbox-x-axis flexbox-space-top">
					<div class="basic-label"><?php echo __('user_flow.survey.label.text.color'); ?></div>
					<div class="">
						<div class="talkappi-colorpicker js-theme-color" data-name="m-primary-btn-color" data-value="<?php if ($post != NULL) echo ($post['m-primary-btn-color']) ?>"></div>
					</div>
				</div>
				<div class="flexbox-space-top separator" style="margin-left: 25px;"></div>
				<div class="font-standard font-family-v3 device-setting-container-title"><?php echo __('user_flow.survey.label.button2'); ?></div>
				<div class="flexbox-x-axis flexbox-space-top">
					<label class="basic-label"><?php echo __('user_flow.survey.label.button.color'); ?></label>
					<div class="">
						<div class="talkappi-colorpicker js-theme-color" data-name="m-secondary-btn-bk-color" data-value="<?php if ($post != NULL) echo ($post['m-secondary-btn-bk-color']) ?>"></div>
					</div>
					<div class="" style="margin-left:15px;">
						<div class="talkappi-colorpicker js-theme-color" data-name="m-secondary-btn-bd-color" data-value="<?php if ($post != NULL) echo ($post['m-secondary-btn-bd-color']) ?>"></div>
					</div>
				</div>
				<div class="flexbox-x-axis flexbox-space-top">
					<div class="basic-label"><?php echo __('user_flow.survey.label.text.color'); ?></div>
					<div class="">
						<div class="talkappi-colorpicker js-theme-color" data-name="m-secondary-btn-color" data-value="<?php if ($post != NULL) echo ($post['m-secondary-btn-color']) ?>"></div>
					</div>
				</div>
			</div>
		</div>
	</div>
	<div class="uf-inquiry-prev">
		<div class="chatwindow-prev-contents">
			<div class="chatwindow-prev-header js-pc-prev">
				<div>
					<img style="margin: 0 10px 0px 16px" src="/assets/admin/css/img/icon-pc.svg" alt="">
					<span><?php echo __('user_flow.survey.label.preview.pc'); ?></span>
				</div>
				<img class="prev-icon-switch js-toggle" src="/assets/admin/css/img/icon-sp.svg" alt="prev-toggle">
			</div>
			<div class="chatwindow-prev-header js-sp-prev" style="display:none">
				<div>
					<img style="margin: 0 10px 0px 16px" src="/assets/admin/css/img/icon-sp.svg" alt="">
					<span><?php echo __('user_flow.survey.label.preview.mobile'); ?></span>
				</div>
				<img class="prev-icon-switch js-toggle" src="/assets/admin/css/img/icon-pc.svg" alt="prev-toggle">
			</div>
			<div class="chatwindow-prev-bot-main js-pc-prev" style="padding:24px 30px 30px 30px;">
				<div class="uf-inquiry-survey-title uf-inquiry-survey-border-top js-prev-theme-bk-color-border" style="border-top: <?php if ($post['theme-bk-color'] != NULL) echo ("solid 6px " . $post['theme-bk-color']) ?>;">
					<div class="title-container flexbox-x-axis">
						<img class="desc-preview-logo" id="logo_file_base64_prev" src="<?php echo ($post['logo-file']) ?>?t=<?php echo (time()) ?>">
						<h4 class="font-standard font-family-v3 font-size-v2 js-preview-title" style="white-space: pre-wrap;word-wrap: break-word;"><?php echo __('user_flow.survey.label.ex.title'); ?></h4>
					</div>
					<p style="margin-top:20px;margin-bottom:10px"><?php echo __('user_flow.survey.label.ex.desc'); ?></p>
				</div>
				<div class="uf-inquiry-survey-title">
					<p style="font-size: 14px;margin-bottom:20px"><?php echo __('user_flow.survey.label.question'); ?></p style="">
					<div>
						<img width="20px" src="/assets/admin/css/img/icon-form-single-option-off.svg" alt="">
						<span style="height: 20px;vertical-align: middle;"><?php echo __('user_flow.survey.label.option'); ?></span>
					</div>

				</div>
				<div class="uf-inquiry-survey-title uf-inquiry-survey-border-top js-prev-theme-bk-color-border"><?php echo __('user_flow.survey.label.free_space'); ?></div>
				<div class="flexbox">
					<div class="uf-inquiry-survey-main-btn js-prev-primary-btn-bk-color js-prev-primary-btn-bd-color js-prev-primary-btn-color" 
					style="background-color:<?php if ($post != NULL)  echo ($post['primary-btn-bk-color'].'; '.'border:solid 1px'.$post['primary-btn-bd-color'].'; '.'color:'.$post['primary-btn-color'].'; ') ?>"><?php echo __('user_flow.survey.label.button1'); ?></div>
					<div 
					class="uf-inquiry-survey-sub-btn js-prev-secondary-btn-bk-color js-prev-secondary-btn-bd-color js-prev-secondary-btn-color"
					style="background-color:<?php if ($post != NULL)  echo ($post['secondary-btn-bk-color'].'; '.'border:solid 1px'.$post['secondary-btn-bd-color'].'; '.'color:'.$post['secondary-btn-color'].'; ') ?>">
					<?php echo __('user_flow.survey.label.button2'); ?>
				</div>
				</div>
			</div>
			<!-- ------sp preview------ -->
			<div class="chatwindow-prev-bot-main js-sp-prev" style="padding:24px 30px 30px 30px;display:none">
				<div class="uf-inquiry-survey-title uf-inquiry-survey-border-top js-prev-theme-bk-color-border"  style="border-top: <?php if ($post != NULL) echo ("solid 6px " . $post['theme-bk-color']) ?>;">
					<div class="uf-inquiry-survey-title-top">
						<img class="round-prev medium" src="<?php echo ($post['logo-file']) ?>?t=<?php echo (time()) ?>" alt="">
						<span><?php echo __('user_flow.survey.label.title'); ?></span>
					</div>
					<p style="margin-top:20px;margin-bottom:10px"><?php echo __('user_flow.survey.label.ex.desc'); ?></p>
				</div>
				<div class="uf-inquiry-survey-title">
					<p style="font-size: 14px;margin-bottom:20px"><?php echo __('user_flow.survey.label.question'); ?></p style="">
					<div>
						<img width="20px" src="/assets/admin/css/img/icon-form-single-option-off.svg" alt="">
						<span style="height: 20px;vertical-align: middle;"><?php echo __('user_flow.survey.label.option'); ?></span>
					</div>

				</div>
				<div class="uf-inquiry-survey-title uf-inquiry-survey-border-top js-prev-theme-bk-color-border"><?php echo __('user_flow.survey.label.free_space'); ?></div>
				<div class="flexbox">
					<div class="uf-inquiry-survey-main-btn js-prev-m-primary-btn-bk-color js-prev-m-primary-btn-bd-color js-prev-m-primary-btn-color" 
					style="background-color:<?php if ($post != NULL)  echo ($post['m-primary-btn-bk-color'].'; '.'border:solid 1px'.$post['m-primary-btn-bd-color'].'; '.'color:'.$post['m-primary-btn-color'].'; ') ?>"><?php echo __('user_flow.survey.label.button1'); ?></div>
					<div 
					class="uf-inquiry-survey-sub-btn js-prev-m-secondary-btn-bk-color js-prev-m-secondary-btn-bd-color js-prev-m-secondary-btn-color"
					style="background-color:<?php if ($post != NULL)  echo ($post['m-secondary-btn-bk-color'].'; '.'border:solid 1px'.$post['m-secondary-btn-bd-color'].'; '.'color:'.$post['m-secondary-btn-color'].'; ') ?>">
					<?php echo __('user_flow.survey.label.button2'); ?>
				</div>
				</div>
			</div>
			<div class="chatwindow-prev-bot-footer">
				<div class="chatwindow-prev-bot-footer-top">
					<span><?php echo __('user_flow.survey.label.check'); ?></span>
				</div>

			</div>

		</div>
	</div>
</div>
<div class="category-separator">
<div class="setting-header"><?php echo __('user_flow.faq.label.font.setting'); ?></div>
</div>
<?php foreach ($_bot_lang as $k => $v) { ?>
	<div class="lines-container">
		<label class="basic-label"><?php echo ($v); ?></label>
		<input name="theme-font-family-<?php echo ($k) ?>" type="text" class="text-input-longer" value="<?php if (array_key_exists('theme-font-family-' . $k, $post)) echo (htmlspecialchars($post['theme-font-family-' . $k])) ?>">
	</div>
<?php } ?>