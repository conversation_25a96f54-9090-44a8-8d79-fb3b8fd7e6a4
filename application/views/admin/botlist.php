<!DOCTYPE html>
<!--[if IE 8]> <html lang="en" class="ie8 no-js"> <![endif]-->
<!--[if IE 9]> <html lang="en" class="ie9 no-js"> <![endif]-->
<!--[if !IE]><!-->
<html>

<head>
	<meta charset="utf-8" />
	<title>ilovetalkappibot </title>
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta content="width=device-width, initial-scale=1.0" name="viewport" />
	<meta http-equiv="Content-type" content="text/html; charset=utf-8">
	<meta content="" name="description" />
	<meta content="" name="author" />
	<meta name="robots" content="noindex" />
	<meta name="googlebot" content="noindex" />

	<style>
		.login {
			background-color: #666 !important;
		}

		.login .logo {
			margin: 60px auto 20px auto;
			padding: 15px;
			text-align: center;
		}

		.login .content {
			background: url(../img/bg-white-lock.png) repeat;
			width: 360px;
			margin: 0 auto;
			margin-bottom: 0px;
			padding: 30px;
			padding-top: 20px;
			padding-bottom: 15px;
			-webkit-border-radius: 7px;
			-moz-border-radius: 7px;
			-ms-border-radius: 7px;
			-o-border-radius: 7px;
			border-radius: 7px;
		}

		.login .content h3 {
			color: #eee;
		}

		.login .content h4 {
			color: #eee;
		}

		.login .content p,
		.login .content label {
			color: #fff;
		}

		.login .content .login-form,
		.login .content .forget-form {
			padding: 0px;
			margin: 0px;
		}

		.login .content .form-control {
			background-color: #fff;
		}

		.login .content .forget-form {
			display: none;
		}

		.login .content .register-form {
			display: none;
		}

		.login .content .form-title {
			font-weight: 300;
			margin-bottom: 25px;
		}

		.login .content .form-actions {
			background-color: transparent;
			clear: both;
			border: 0px;
			padding: 0px 30px 25px 30px;
			margin-left: -30px;
			margin-right: -30px;
		}

		.login .content .form-actions .checkbox {
			margin-left: 0;
			padding-left: 0;
		}

		.login .content .forget-form .form-actions {
			border: 0;
			margin-bottom: 0;
			padding-bottom: 20px;
		}

		.login .content .register-form .form-actions {
			border: 0;
			margin-bottom: 0;
			padding-bottom: 0px;
		}

		.login .content .form-actions .checkbox {
			margin-top: 8px;
			display: inline-block;
		}

		.login .content .form-actions .btn {
			margin-top: 1px;
		}

		.login .content .forget-password {
			margin-top: 25px;
		}

		.login .content .create-account {
			border-top: 1px dotted #eee;
			padding-top: 10px;
			margin-top: 15px;
		}

		.login .content .create-account a {
			display: inline-block;
			margin-top: 5px;
		}

		/* select2 dropdowns */
		.login .content .select2-container i {
			display: inline-block;
			position: relative;
			color: #ccc;
			z-index: 1;
			top: 1px;
			margin: 4px 4px 0px -1px;
			width: 16px;
			height: 16px;
			font-size: 16px;
			text-align: center;
		}

		.login .content .has-error .select2-container i {
			color: #b94a48;
		}

		.login .content .select2-container a span {
			font-size: 13px;
		}

		.login .content .select2-container a span img {
			margin-left: 4px;
		}

		/* footer copyright */
		.login .copyright {
			text-align: center;
			margin: 0 auto;
			padding: 10px;
			color: #eee;
			font-size: 13px;
		}

		@media (max-width: 480px) {

			/***
  Login page
  ***/
			.login .logo {
				margin-top: 10px;
			}

			.login .content {
				padding: 30px;
				width: 300px;
			}

			.login .content h3 {
				font-size: 22px;
			}

			.login .checkbox {
				font-size: 13px;
			}
		}

		a,
		a:hover,
		a:active,
		a:visited,
		a:focus {
			color: #FFF;
			text-decoration: none;
		}

		.row {
			display: flex;
			align-items: center;
			width: 4000px;
			height: 28px;
			border-bottom: 0px dashed #888;
			color: #FFF;
			font-size: 12px;
		}

		.logo-image {
			width: 40px;
			border-radius: 50%;
			margin-right: 12px;
		}
	</style>
</head>

<body class="login">
	<div class="logo" style="margin-top: 0px;margin-bottom:0px;">
		<img src="/assets/common/images/logo-big.png" title="<?php echo count($bots) ?>" width="300px" />
	</div>
	<?php
	foreach ($bots as $bot) {
		if ($bot->bot_id >= 201000 && $bot->bot_id < 299999) {
			if ($bot->bot_id % 1000 > 0) {
				continue;
			}
		}
		if ($bot->bot_id >= 2001000 && $bot->bot_id < 2999999) {
			if ($bot->bot_id % 1000 > 0) {
				continue;
			}
		}
		echo ('<div class="row">');
		$path = $bot->facility_cd;
		if (array_key_exists($bot->bot_id, $logos)) {
			$path = $logos[$bot->bot_id];
		}
		if (file_exists(dirname(__FILE__) . "/../../../assets/f/" . $path . "/webchat/logo.png")) {
			echo ('<a href="' . $base_url . 'bot?id=' . $bot->facility_cd . '" rel="noopener noreferrer" target="_blank"> <img src="' . $base_url . 'assets/f/' . $path . '/webchat/logo.png" class="logo-image" /> </a>');
		} else if (file_exists(dirname(__FILE__) . "/../../../assets/f/" . $path . "/webchat/logo.jpg")) {
			echo ('<a href="' . $base_url . 'bot?id=' . $bot->facility_cd . '" rel="noopener noreferrer" target="_blank"> <img src="' . $base_url . 'assets/f/' . $path . '/webchat/logo.jpg" class="logo-image" /> </a>');
		} else if (file_exists(dirname(__FILE__) . "/../../../assets/f/" . $path . "/webchat/logo.jpeg")) {
			echo ('<a href="' . $base_url . 'bot?id=' . $bot->facility_cd . '" rel="noopener noreferrer" target="_blank"> <img src="' . $base_url . 'assets/f/' . $path . '/webchat/logo.jpeg" class="logo-image" /> </a>');
		} else if (file_exists(dirname(__FILE__) . "/../../../assets/f/" . $path . "/webchat/logo.svg")) {
			echo ('<a href="' . $base_url . 'bot?id=' . $bot->facility_cd . '" rel="noopener noreferrer" target="_blank"> <img src="' . $base_url . 'assets/f/' . $path . '/webchat/logo.svg" class="logo-image" /> </a>');
		}
		// echo('<a href="' . $base_url . 'booking?facility_cd=' . $bot->facility_cd . '" rel="noopener noreferrer" target="_blank">　予約　</a>');
		echo ('<a href="' . $base_url . 'bot?id=' . $bot->facility_cd . '" rel="noopener noreferrer" target="_blank" style="font-size:14px;font-weight:bold;">' . $bot->bot_name . '[' . $bot->bot_id . ']</a>');
		foreach ($scenes[strval($bot->bot_id)] as $scene) {
			echo ("・");
			if ($scene->use_faq_flg == 1) {
				echo ('<a href="' . $base_url . 'faq?facility_cd=' . $scene->scene_name . '" rel="noopener noreferrer" target="_blank">' . $scene->label . '</a>');
			} else {
				echo ('<a href="' . $base_url . 'bot?id=' . $scene->scene_name . '" rel="noopener noreferrer" target="_blank">' . $scene->label . '</a>');
			}
			if ($scene->url != '') echo ('<a href="' . $scene->url . '" rel="noopener noreferrer" target="_blank">' . '<img src="' . $base_url . 'assets/webchat/images/channel_select_web.png" style="margin-left:5px;width:24px;" />' . '</a>');
		}
		echo ('</div>');
		echo ('<br/>');
	}
	?>
	<div class="copyright">
		talkappi
		2025 &copy; ActiValues, Inc.
	</div>
	<script src="/assets/global/plugins/jquery.min.js" type="text/javascript"></script>
	<script src="/assets/global/plugins/jquery-migrate.min.js" type="text/javascript"></script>
	<script src="/assets/global/plugins/backstretch/jquery.backstretch.min.js" type="text/javascript"></script>
	<script src="/assets/webchat/js/botpage.js" type="text/javascript"></script>
</body>

</html>