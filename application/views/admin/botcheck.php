			<!-- B<PERSON>IN PAGE HEADER-->
			<!-- BEGIN PAGE HEAD -->
			<div class="page-head">
				<!-- BEGIN PAGE TITLE -->
				<div class="page-title">
					<h1><?php echo($_active_menu_name)?><small></small></h1>
				</div>
				<!-- <PERSON>ND PAGE TITLE -->
			</div>
			<!-- END PAGE HEAD -->
			<!-- END PAGE HEADER-->
			<!-- BEGIN PAGE CONTENT-->
			        <!-- Page Content -->
			        <div id="page-wrapper">
						<?php 
							if (strlen($_active_menu) > 4) {
								$tab_menu = View::factory ('admin/menutab');
								echo($tab_menu);
							}
						?>
						<div class="edit-container">
							<div class="settings-container">
								<div class="row">
									<div class="col-md-10">
										<input type="hidden" name="act" />
										<div class="form-body">
											<div class="form-group">
												<label class="control-label col-md-3" <?php echo $post['bot_name_flg'] ?>>多言語名前</label>
												<div class="col-md-8">
													<div class="alert alert-<?php echo $post['bot_name_must'] ?>" style="padding:5px;">
													<a href="/admin/sysmsg?id=<?php echo $post['bot_name_msgid']?>&back=botcheck"><span> <?php echo $post['bot_name']?> </span></a>
													</div>
												</div>
											</div>
											<div class="form-group">
												<label class="control-label col-md-3" <?php echo $post['bot_description_flg'] ?>>多言語紹介</label>
												<div class="col-md-8">
													<div class="alert alert-<?php echo $post['bot_description_must'] ?>" style="padding:5px;">
													<a href="/admin/sysmsg?id=<?php echo $post['bot_description_msgid']?>&back=botcheck"><span> <?php echo nl2br($post['bot_description'])?> </span></a>
													</div>
												</div>
											</div>
											<div class="form-group">
												<label class="control-label col-md-3" <?php echo $post['bot_contact_flg'] ?>>多言語連絡情報</label>
												<div class="col-md-8">
													<div class="alert alert-<?php echo $post['bot_contact_must'] ?>" style="padding:5px;">
													<a href="/admin/sysmsg?id=<?php echo $post['bot_contact_msgid']?>&back=botcheck"><span> <?php echo nl2br($post['bot_contact'])?> </span></a>
													</div>
												</div>
											</div>
											<div class="form-group">
												<label class="control-label col-md-3" <?php echo $post['default_scene_cd_flg'] ?>>デフォルト導線</label>
												<div class="col-md-4">
													<div class="alert alert-<?php echo $post['default_scene_cd_must'] ?>" style="padding:5px;">
													<a href="/admin/botsetting"><span> <?php echo $post['default_scene_cd']?> </span></a>
													</div>
												</div>
											</div>												
											<div class="form-group">
												<label class="control-label col-md-3" <?php echo $post['location_lat_flg'] ?>>経緯度</label>
												<div class="col-md-2">
													<div class="alert alert-<?php echo $post['location_lat_must'] ?>" style="padding:5px;">
													<a href="/admin/botsetting"><span> <?php echo $post['location_lat']?> </span></a>
													</div>
												</div>
											</div>
											<div class="form-group">
												<label class="control-label col-md-3" <?php echo $post['welcome_image_flg'] ?>>歓迎写真</label>
												<div class="col-md-2">
													<div class="alert alert-<?php echo $post['welcome_image_must'] ?>" style="padding:5px;">
													<a href="/admin/sysmsg?id=<?php echo $post['welcome_image_msgid']?>&back=botcheck"><span> <?php echo $post['welcome_image']?> </span></a>
													</div>
												</div>
											</div>
											<div class="form-group">
												<label class="control-label col-md-3" <?php echo $post['welcome_image_wechat_flg'] ?>>ボット歓迎写真（WeChat）</label>
												<div class="col-md-2">
													<div class="alert alert-<?php echo $post['welcome_image_wechat_must'] ?>" style="padding:5px;">
													<a href="/admin/sysmsg?id=<?php echo $post['welcome_image_wechat_msgid']?>&back=botcheck"><span> <?php echo $post['welcome_image_wechat']?> </span></a>
													</div>
												</div>
											</div>												
											<div class="form-group">
												<label class="control-label col-md-3" <?php echo $post['talkappi_greeting_0_flg'] ?>>ボットの吹き出し文字</label>
												<div class="col-md-8">
													<div class="alert alert-<?php echo $post['talkappi_greeting_0_must'] ?>" style="padding:5px;">
													<a href="/admin/sysmsg?id=<?php echo $post['talkappi_greeting_0_msgid']?>&back=botcheck"><span> <?php echo nl2br($post['talkappi_greeting_0'])?> </span></a>
													</div>
												</div>
											</div>					
											<?php if ($_bot->bot_class_cd == '01') {?>							
											<div class="form-group">											
												<label class="control-label col-md-3" <?php echo $post['hotel_reservation_txt_flg'] ?>>予約誘導</label>
												<div class="col-md-3">
													<div class="alert alert-<?php echo $post['hotel_reservation_txt_must'] ?>" style="padding:5px;">
													<a href="/admin/sysmsg?id=<?php echo $post['hotel_reservation_txt_msgid']?>&back=botcheck"><span> <?php echo nl2br($post['hotel_reservation_txt'])?> </span></a>
													</div>
												</div>												
												<div class="col-md-3">
													<div class="alert alert-<?php echo $post['hotel_reservation_hp_car_must'] ?>" style="padding:5px;">
													<a href="/admin/sysmsg?id=<?php echo $post['hotel_reservation_hp_car_msgid']?>&back=botcheck"><span> <?php echo $post['hotel_reservation_hp_car']?> </span></a>
													</div>
												</div>																							
											</div>
											<?php }?>
										</div>
										<div class="form-actions">
											<div class="form-group">	
												<span class="col-md-offset-3 col-md-9">※ピンク色の未設定は必ず設定ください。黄色の未設定はカスタマイズする場合設定可能です。</span>
											</div>												
											<div class="form-group">	
												<div class="col-md-offset-3 col-md-6">
												<button type="submit" id="saveButton" class="btn green mr10">設定内容エンジンに反映</button>
												</div>
											</div>
										</div>
									</div>
									<div class="col-md-2">

									</div>
								</div>
							</div>
						</div>
					</div>

			<!-- END PAGE CONTENT-->



