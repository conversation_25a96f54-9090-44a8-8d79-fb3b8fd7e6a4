<?php
function formatSize($size, $precision = 2) {
	$units = ['B', 'KB', 'MB', 'GB', 'TB', 'PB'];
	for ($i = 0; $size > 1024; $i++) {
			$size /= 1024;
	}
	return round($size, $precision) . ' ' . $units[$i];
}
$model = Model::factory('chatmodel');
$chat_mode = 0;
if ($start_time != NULL) {
	echo ('<li class="out" id="more_log" style="text-align:center;">');
	echo ('<a href="javascript:;" class="more_log" start_time="' . $start_time . '" member_id="' . $member_id . '">' . __('admin.chat.window.see_more') . '</a>');
	echo ('</li>');
}
$noread_mail = true;
$email = NULL;
$sns_type_cd = '';
$no_chat = 1;
$bot_id = '';
$ext_array = $settings['support_image_type'];
if ($chats != NULL) foreach ($chats as $chat) {
	if ($chat['intent_cd'] === "set_admin_member_info") continue;
	// autoanswer_complete used for connecting request messages
	if ($chat['intent_cd'] === "autoanswer_complete" && isset($chat['link_log_id'])) {
		echo ('<li class="autoanswer_complete" style="display: none;" ><div data-link-id="' . $chat['link_id'] . '" data-link-type="' . $chat['link_type'] . '" data-link-log-id="' . $chat['link_log_id'] . '" ></div></li>');
	};

	$comment_class = 'message';
	if ($chat['member_msg'] == '' && $chat['bot_msg'] == '') continue;
	$email = $chat['email'];
	$bot_id = $chat['bot_id'];
	$sns_type_cd = $chat['sns_type_cd'];
	$chat['log_time'] = substr($chat['log_time'], 0, 19);
	$chat_mode = $chat['chat_mode'];
	$show_msg = true;
	$wait_answer = '';

	if ($chat['intent_cd'] == 'mail_chatuser') $noread_mail = true;
	if ($chat['member_msg'] == '') {
		$send_type = 'out';
		if (strpos($chat['intent_cd'], "input.chat") === 0 && $chat['user_id'] != null) {
			$avatar_url = "/assets/common/images/icon_round_comp.svg";
			$name = $chat['username'];
		} else {
			$name = $chat_bot_dict[strval($chat['bot_id'])]['bot_name'];
			//icon
			$avatar_url = $chat_bot_dict[strval($chat['bot_id'])]['talkappi_image'];
			if ($chat['scene_cd'] != '') {
				$path = APPPATH . "../assets/f/" . $chat['scene_cd'] . '/webchat/';
				foreach ($ext_array as $ext) {
					if (file_exists($path . 'logo' . '.' . $ext) == true) {
						$avatar_url = '/assets/f/' . $chat['scene_cd'] . '/webchat/' . 'logo' . '.' . $ext;
						break;
					}
				}
			}
		}
		$message = $chat['bot_msg'];
		if ($mask_privacy == 1) {
			$tmp = json_decode($chat['bot_msg'], true);
			if (!is_array($tmp)) $message = $model->mask_privacy_info($chat['bot_msg']);
		}
		if ($chat['sns_type_cd'] == 'wb') {
			if ($chat['log_time'] > $member_last_access_time) {
				$wait_answer = __('admin.chat.window.unread');
			}
		}
		if ($chat['sns_type_cd'] == 'fb') {
			if ($eu_restict == 0 && $chat['log_time'] > $member_last_access_time) {
				$wait_answer = __('admin.chat.window.unread');
			}
		}
		if (
			$chat['intent_cd'] == 'change_chatmode.0' ||
			$chat['intent_cd'] == 'change_chatmode.1' ||
			$chat['intent_cd'] == 'change_chatmode.2' ||
			$chat['intent_cd'] == 'clear_memberlog.all' ||
			$chat['intent_cd'] == 'clear_memberlog.1' ||
			$chat['intent_cd'] == 'update_membertag' ||
			$chat['intent_cd'] == 'mail_chatuser' ||
			$chat['intent_cd'] == 'mail_chatuser_error' ||
			$chat['intent_cd'] == 'skill_start_chat' ||
			$chat['intent_cd'] == 'change_chatuser'
		) {
			$send_type = 'comment';
			if (strpos($chat['intent_cd'], 'error') !== false) {
				$comment_class = 'error';
			}
		} else {
			$no_chat = 0;
		}
	} else {
		if ($chat['intent_cd'] == 'welcome') {
			if ($chat['sns_type_cd'] == 'wc' || $chat['sns_type_cd'] == 'fb' || $chat['sns_type_cd'] == 'ln') {
				$user = Session::instance()->get('user', NULL);
				if ($user->role_cd == '99') {
					$chat['member_msg'] = 'Start' . ' [' . $chat['member_msg'] . ']';
				} else {
					$chat['member_msg'] = 'Start';
				}
			}
		}
		$send_type = 'in';
		if ($chat['avatar'] == null || $chat['avatar'] == '' || $chat['avatar'] == 'undefined' || $chat['sns_type_cd'] == 'fb') {
			if ($mobile == '1') {
				$avatar_url = "/assets/common/images/icon_round_mobile.svg";
			} else {
				$avatar_url = "/assets/common/images/icon_round_comp.svg";
			}
		} else {
			$avatar_url = $chat['avatar'];
		}

		if (($chat['intent_cd'] == 'input.unknown' || $chat['intent_cd'] == 'request_operator_yes') && $chat['member_msg'] != '') {
			if ($chat['log_flg'] == '1') {
				$wait_answer = __('admin.chat.window.already_responded');
			}
		}
		$name = $chat['name'] != NULL ? $chat['name'] : $chat['last_name'] . ' ' . $chat['first_name'];
		if ($mask_privacy == 1) {
			$message = $model->mask_privacy_info($chat['member_msg']);
		} else {
			$message = $chat['member_msg'];
		}
		if ($chat['intent_cd'] == 'member_start_chat' || $chat['intent_cd'] == 'member_end_chat' || $chat['intent_cd'] == 'member_click_link1') {
			$send_type = 'comment';
		} else {
			$no_chat = 0;
		}

		if (strpos($chat['intent_cd'], 'action_tl_') === 0) {
			$send_type = 'comment';
			$ex = json_decode($message);
			if ($ex != null) {
				if (isset($ex->date_from)) {
					$message = '空室検索条件入力';
				} else if (isset($ex->tel)) {
					$message = '予約者情報入力';
				}
			} else {
				$message = '';
			}
		}
	}
	$ex = null;

	if ($chat['score'] >= 0) {
		//$message = str_replace(["\\\\\\n","\r\n", "\r", "\n"], PHP_EOL, $message);
		//$message = str_replace(["\\\\\\n","\\\\n","\r\n","\r","\n"], '<br />', $message);
		$message = str_replace(['\r\n', '\\\n', '\\n', '\n', '\r'], '<br />', $message);
		$ex = json_decode($message);
		if (!isset($ex->type)) $ex = null;
		if ($ex != null) {
			if ($ex->type == 'card') {
				$message = '<div class="talkappi-carousel">';
				$message = $message . '<div class="swiper-wrapper">';
				for ($cc = 0; $cc <= count($ex->items) - 1; $cc++) {
					$item = $ex->items[$cc];
					$style = "";
					$style1 = "";
					// if ($item->image == null || $item->image =='') $style ='style="height:0px;"';
					// if ($item->content == null || $item->content =='') $style1 ='style="height:0px;"';
					$message = $message . '<div class="swiper-slide">';
					$message = $message . '<div class="card">';
					$message = $message . '<a href="' . $item->url . '" target="blank"><img class="image" ' . $style . ' alt="" src="' . $item->image . '" /></a>';
					$message = $message . '<div class="item-caption">' . nl2br($item->title) . '</div>';
					$message = $message . '<div class="item-description" ' . $style1 . ' >' . nl2br($item->content) . '</div>';
					$message = $message . '<div class="item-button-horizontal">';
					foreach ($item->buttons as $button) {
						$col = 'button col3';
						if (count($item->buttons) == 2) $col = 'button col2';
						if (count($item->buttons) == 1) $col = 'button';
						if (isset($button->type)) {
							if ($button->type == 'url')
								$message = $message . '<a class="' . $col . '" href="' . $button->url . '" target="blank">' . $button->title . '</a>';
							else
								$message = $message . '<a class="' . $col . '" href="#">' . $button->title . '</a>';
						} else {
							$message = $message . '<a class="' . $col . '" href="' . $button->url . '" target="blank">' . $button->title . '</a>';
						}
					}
					$message = $message . '</div>';
					$message = $message . '</div>';
					$message = $message . '</div>';
				}
				$message = $message . '</div>';
				$message = $message . '</div>';
			} else if ($ex->type == 'image') {
				$message = '';
				if (isset($ex->title)) {
					$message = $message . $ex->title . '<br>';
				}
				if (isset($ex->image)) {
					$message = $message . '<img class="message-image" alt="" src="' . $ex->image . '" />';
				} else {
					$message = $message . '<img class="message-image" alt="" src="" />';
				}
			} else if ($ex->type == 'file') {
				$message = '';
				if (isset($ex->title)) {
					$message = $message . $ex->title . '<br>';
				}
				if (isset($ex->file)) {
					$file_name = '';
					if (isset($ex->name)) {
						$file_name = $ex->name;
					} else {
						$file_name = end(explode('/', trim($ex->url)));
					}
					$ext_name = end(explode('.', $file_name));
					$file_type = 'unsupport';
					switch (strtolower($ext_name)) {
						case 'doc':
						case 'docx':
							$file_type = 'word';
							break;
						case 'csv':
						case 'xls':
						case 'xlsx':
							$file_type = 'excel';
							break;
						case 'pdf':
							$file_type = 'pdf';
							break;
						default:
							$file_type = 'unsupport';
							break;
					}
					$message = $message . '<a download target="_blank" rel="noopener noreferrer" class="file" style="word-break:break-all;" href="' . $ex->url . '">';
					$message = $message . '<img src="/assets/chat/images/icon_file_' . $file_type . '.svg" alt="' . htmlspecialchars($file_name) . '" />';
					$message = $message . '<div>';
					$message = $message . '<span class="filename">' . htmlspecialchars($file_name) . '</span>';
					if (isset($ex->size)) {
						$message = $message . '<span class="filesize">' . formatSize($ex->size, 0) . '</span>';
					}
					$message = $message . '</div>';
					$message = $message . '</a>';
				}
			} else if ($ex->type == 'video') {
				$message = '';
				$message = $message . '<video controls="controls" src="' . $ex->url . '" />';
			} else if ($ex->type == 'button' || $ex->type == 'list') {
				$message =  '<span style="line-height:32px;">';
				if ($ex->title != '') $message = $message . nl2br($ex->title) . '<br/>';
				foreach ($ex->items as $item) {
					if ($item->content == '') continue;
					$item_type = "postback";
					if (is_object($item)) {
						if (isset($item->type)) $item_type = $item->type;
						$item_title = $item->title;
						$item_content = $item->content;
					} else {
						$item_title = $item;
						$item_content = $item;
					}
					$item_content = str_replace('"', '%22', $item_content);
					$message = $message . '<a href="javascript:;" class="webchat-list" type="' . $item_type . '" title="' . $item_title . '" content="' . $item_content  . '">';
					if ($ex->type == 'list') {
						$message = $message . '<span class="badge badge-success" style="width:fit-content; max-width:100%; overflow:hidden; text-overflow:ellipsis; height:auto; min-height:18px; margin-right:10px; background-color:#e0e0e0; color:#333; font-size:20px;">' . nl2br($item_title) . '</span></a>';
						$message = $message . '<br/>';
					} else {
						$message = $message . '<span class="badge badge-success" style="width:fit-content; max-width:100%; overflow:hidden; text-overflow:ellipsis; height:auto; min-height:18px; display:inline-block; margin-right:6px; background-color:#ffffff; color: #57606f; border: solid 1px #57606f; font-size:20px; white-space: pre;">' . nl2br($item_title) . '</span></a>';
					}
				}
				$message = $message . '</span>';
			} else if ($ex->type == 'grid') {
				$message =  '<span class="body" style="height:380px;">';
				$web_width = 380;
				$message = $message . '<div class="grid-menu" style="width:' . $web_width . 'px">';
				$item_count = count($ex->items);
				$rows = intval(sqrt($item_count));
				$item_width = $web_width / $rows;
				foreach ($ex->items as $item) {
					$message = $message . '<div class="grid-item" style="width:' . $item_width . 'px;height:' . $item_width . 'px">';
					$item_type = "postback";
					$item_title = "";
					if (isset($item->type)) {
						$item_type = $item->type;
					}
					if (isset($item->title)) {
						$item_title = $item->title;
					}
					$content = str_replace('"', '%22', $item->content);
					$message = $message . '<a href="javascript:;" class="webchat-list" type="' . $item_type . '" title="' . $item_title . '" content="' . $content . '">';
					$message = $message . '<img class="menu-item" style="margin:0px;width:' . $item_width . 'px;height:' . $item_width . 'px"; src="' . $item->image . '" /></a>';
					$message = $message . '</div>';
				}
				$message = $message . '</div>';
				$message = $message . '</span>';
			}
		} else {
			if ($chat['intent_cd'] == 'member_click_link') {
				$message = $chat['member_msg'] . '<a style="margin-left:5px;" target="_blank" rel="noopener noreferrer" href="' . $chat['member_msg_t'] . '">link</a>';
			} else {
				if ($chat['bot_msg_t'] != NULL && $chat['bot_msg_t'] != "") {
					$message = $chat['bot_msg_t'] . PHP_EOL . "※原文：" . $message;
				}
				if ($chat['member_msg'] != NULL) {
					if ($chat['member_msg_t'] == "") {
						$message = htmlspecialchars($message);
					} else {
						$message = htmlspecialchars($chat['member_msg_t']) . PHP_EOL . "※原文：" . htmlspecialchars($message);
					}
				}
			}
		}
	}
?>
	<?php
	if ($show_msg) {
		if (strpos($send_type, 'comment') === 0) {
			if ($chat['intent_cd'] != 'member_start_chat' && $chat['intent_cd'] != 'member_end_chat' && $chat['intent_cd'] != 'member_click_link1') {
				$message = $model->get_bot_txt_message($chat['bot_id'], $chat['intent_cd'], $chat['lang_cd']);
			}
			if ($chat['intent_cd'] == 'member_start_chat') {
				$message = __('admin.chat.window.start_chat');
			}
			if ($chat['intent_cd'] == 'member_end_chat') {
				$message = __('admin.chat.window.end_chat');
			}
			echo ('<li class="' . $send_type . '"><div class="' . $comment_class . '">');
			echo ($message . ' ' . $chat['log_time']);
			echo ('</div></li>');
		} else {
	?>
			<li class="<?php echo ($send_type) ?>" id="<?php echo ($chat['link_id'] ? 'scroll-' . $chat['link_id'] : "") ?>">
				<?php
				$background_color = "#fff";
				$name_color = "#245BD6";
				$arrow_style = "border-left: 8px solid #3D3F45;";
				$border_style = "border-right: 2px solid #3D3F45;";

				$sent_mail = 0;
				foreach ($mail_logs as $log_id) {
					if ($log_id["link_id"] == $chat['log_id']) {
						$sent_mail = 1;
					}
				}

				if ($send_type == "out" && $sent_mail == 1) {
					$background_color = "#D3EEFF";
				}

				if ($send_type == "in") {
					if ($wait_answer == __('admin.chat.window.already_supported')) {
						$background_color = "#FFDCE5";
						$name_color = "#E53361";
						$arrow_style = "border-right: 8px solid #E53361;";
						$border_style = "border-left: 2px solid #E53361;";
					} else {
						$arrow_style = "border-right: 8px solid #245BD6;";
						$border_style = "border-left: 2px solid #245BD6;";
					}
				}

				?>
				<img class="avatar" alt="" src="<?php echo ($avatar_url) ?>" />
				<div class="message" id="log-<?php echo ($chat['log_id']) ?>" style="<?php echo ($border_style) ?>">
					<span class="arrow" style="<?php echo ($arrow_style) ?>"></span>
					<div style="display: flex; justify-content: space-between; padding: 5px 12px; background-color: <?php echo ($background_color) ?>">
						<div style="display: flex; align-items: center; gap: 4px;">
							<?php
							if ($send_type == "out" && $sent_mail == 1) {
								echo ('<img src="/assets/chat/images/icon_email.svg" />');
								echo ('<span>' . __('admin.common.label.mail') . '</span>');
							}
							?>
							<a style="color: <?php echo ($name_color) ?>" href="/admin/memberinfo?member_id=<?php echo ($chat['member_id']) ?>" target="_blank" class="name" member_id="<?php echo ($chat['member_id']) ?>"> <?php echo ($name) ?> </a>
							<span class="datetime"> <?php echo ($chat['log_time']) ?> </span>
							<?php
							if ($wait_answer == __('admin.chat.window.unread')) {
								$noread_mail = false;
								echo ('<span class="noread" style="color: #FF9551;" log_time="' . $chat['log_time'] . '">' . $wait_answer . '</span>');
							}
							?>
						</div>
						<div style="flex: none;">
							<?php
							if ($wait_answer == __('admin.chat.window.already_supported')) {
								echo ('<a href="javascript:;" style="margin-left: 4px;" class="clear_chat" log_id="' . $chat['log_id'] . '">' . $wait_answer . '</a>');
							}
							?>
						</div>
					</div>
					<span class="body"> <?php echo (nl2br($message)) ?> </span>
				</div>
				<div class="message-bottom">
					<div style="display: flex; gap: 4px;">
						<?php
						if ($ex == null && $chat['lang_cd'] != "ja" && ($chat['member_msg_t'] . $chat['bot_msg_t'] == NULL || $chat['member_msg_t'] . $chat['bot_msg_t'] == '')) {
							echo ('<a href="javascript:;" class="message-bottom-tag message-bottom_blue translate_chat" log_id="' . $chat['log_id'] . '">' . __('admin.common.label.translate') . '</a>');
						}
						if ($email != NULL && $chat['user_id'] != null && $send_type == 'out' && $sent_mail == 0) {
							echo ('<a href="javascript:;" class="message-bottom-tag message-bottom_blue send_mail_message" member_id="' . $member_id . '" log_id="' . $chat['log_id'] . '">' . __('admin.chat.window.send_mail') . '</a>');
						}
						// request tag
						if ($chat['link_type'] == 'service' && isset($chat['service_status_cd']) && $send_type == 'out') {
							$request_status_style = '';
							$request_status_title = '';
							if ($chat['service_status_cd'] == '01') {
								$request_status_style = 'message-bottom_red';
								$request_status_title = __('admin.service.label.status.unhandled');
							} else if ($chat['service_status_cd'] == '02') {
								$request_status_style = 'message-bottom_orange';
								$request_status_title = __('admin.service.label.status.handling');
							} else if ($chat['service_status_cd'] == '03') {
								$request_status_style = 'message-bottom_green';
								$request_status_title = __('admin.service.label.status.completed');
							} else if ($chat['service_status_cd'] == '04') {
								$request_status_style = 'message-bottom_gray';
								$request_status_title = __('admin.service.label.status.cancelled');
							}
							echo ('<a data-title="' . $chat['intent_name'] . '" data-description="' . $chat['request_member_msg'] . '" data-id="' . $chat['link_id'] . '" data-status="' . $chat['service_status_cd'] . '" class="request-status-tag message-bottom-tag ' . $request_status_style . '" member_id="' . $member_id . '" id="request-' . $chat['link_id'] . '">'
								. $request_status_title . ' | ' . __('admin.servicetable.label.reception_id') . $chat['link_id']
								. '</a>');
						}
						?>
					</div>
					<div style="flex: none;">
						<?php
						if ($modify_mode == 1) {
							echo ('<a href="javascript:;" class="message-bottom-tag message-bottom_red delete_chat" log_id="' . $chat['log_id'] . '">' . __('admin.chat.window.delete_chat') . '</a>');
						}
						?>
					</div>
				</div>
			</li>
<?php
		}
	}
}
?>
<?php if ($chats != NULL && count($chat) > 0) { ?>
	<input type="hidden" name="chat_mode" id="chat_mode" value="<?php echo ($chat_mode) ?>" />
	<input type="hidden" name="chat_user" id="chat_user" value="<?php echo ($chat_user) ?>" />
	<input type="hidden" name="member_lang_cd" id="member_lang_cd" value="<?php echo ($member_lang_cd) ?>" />
	<input type="hidden" name="bot_id" id="bot_id" value="<?php echo ($bot_id) ?>" />
<?php } ?>
<input type="hidden" name="member_tags" id="member_tags" value="<?php echo ($member_tags) ?>" />
<input type="hidden" name="member_remark" id="member_remark" value="<?php echo ($member_remark) ?>" />
<input type="hidden" name="member_name" id="member_name" value="<?php echo ($member_name) ?>" />
<input type="hidden" name="member_label" id="member_label" value="<?php echo ($member_label) ?>" />
<input type="hidden" name="member_number" id="member_number" value="<?php echo ($member_number) ?>" />
<input type="hidden" name="member_timezone" id="member_timezone" value="<?php echo ($member_timezone) ?>" />
<input type="hidden" name="member_last_access_time" id="member_last_access_time" value="<?php echo ($member_last_access_time) ?>" />
<input type="hidden" name="member_no_chat" id="member_no_chat" value="<?php echo ($no_chat) ?>" />

<?php if ($chats != NULL && count($chat) > 0) { 
	$has_email = $email != NULL ? 1 : 0;
?>
	<input type="hidden" name="member_email" id="member_email" value="<?php echo ($has_email) ?>" />
<?php } ?>
