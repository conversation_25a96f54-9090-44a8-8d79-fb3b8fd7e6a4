<div class="flexbox">
    <div style="width:500px">
        <div class="category-separator">
            <div class="setting-header">吹出（スタイル）</div>
        </div>
        <div class="lines-container" style="align-items: center;">
            <div class="basic-label">文字色</div>
            <div>
                <div class="talkappi-colorpicker js-theme-color" data-name="otaprice-greeting-txtcolor" data-value="<?php if ($post != NULL) echo ($post['otaprice-greeting-txtcolor']) ?>"></div>
            </div>
        </div>
        <div class="lines-container" style="align-items: center;">
            <div class="basic-label">背景色</div>
            <div>
                <div class="talkappi-colorpicker js-theme-color" data-name="otaprice-greeting-bkcolor" data-value="<?php if ($post != NULL) echo ($post['otaprice-greeting-bkcolor']) ?>"></div>
            </div>
        </div>
        <div class="lines-container" style="align-items: center;">
            <div class="basic-label">アクセント</div>
            <div>
                <div class="talkappi-colorpicker js-theme-color" data-name="otaprice-greeting-bdcolor" data-value="<?php if ($post != NULL) echo ($post['otaprice-greeting-bdcolor']) ?>"></div>
            </div>
        </div>
        <div class="lines-container" style="align-items: center;">
            <div class="basic-label">閉じるボタン</div>
            <div>
                <div class="talkappi-colorpicker js-theme-color" data-name="otaprice-close-icon-color" data-value="<?php if ($post != NULL) echo ($post['otaprice-close-icon-color']) ?>"></div>
            </div>
        </div>
        <div class="lines-container" style="align-items: center;">
            <div class="basic-label">ヘッダー背景</div>
            <div>
                <div class="talkappi-colorpicker js-theme-color" data-name="otaprice-header-bkcolor" data-value="<?php if ($post != NULL) echo ($post['otaprice-header-bkcolor']) ?>"></div>
            </div>
        </div>
        <div class="flexbox" style="flex-wrap:wrap">
            <div class="device-setting-container flexbox-space-top">
                <div class="font-standard font-family-v3 device-setting-container-title">パソコン</div>
                <div class="flexbox-x-axis flexbox-space-top">
                    <div class="basic-label">サイズ(px)</div>
                    <div class="device-setting-size-container">
                        <input name="otaprice-greeting-width" type="text" class="device-setting-size-input" placeholder="幅" value="<?php echo ($post['otaprice-greeting-width']) ?>">
                        <p class="device-setting-size-letter">W</p>
                    </div>
                    <div class="device-setting-size-container">
                        <input name="otaprice-greeting-height" type="text" class="device-setting-size-input" placeholder="高さ" id="otaprice-greeting-height" value="<?php echo ($post['otaprice-greeting-height']) ?>">
                        <p class="device-setting-size-letter">H</p>
                    </div>
                </div>
                <div class="flexbox-x-axis  flexbox-space-top">
                    <div class="basic-label">表示位置</div>
                    <div class="device-setting-size-container">
                        <input name="otaprice-greeting-bottom" type="text" class="device-setting-size-input" placeholder="下" value="<?php echo ($post['otaprice-greeting-bottom']) ?>">
                        <p class="device-setting-size-letter">Y</p>
                    </div>
                    <div class="device-setting-size-container">
                        <input name="otaprice-greeting-right" type="text" class="device-setting-size-input" placeholder="右" id="otaprice-greeting-right" value="<?php echo ($post['otaprice-greeting-right']) ?>">
                        <p class="device-setting-size-letter">X</p>
                    </div>
                    <div class="col-md-1" style="padding-right:0px;">
                        <?php echo Form::select('otaprice-greeting-posi', $posilist, $post['otaprice-greeting-posi'], array('style' => 'width:80px;', 'class' => 'form-control', 'id' => 'otaprice-greeting-posi')) ?>
                    </div>
                </div>
            </div>
            <div class="device-setting-container flexbox-space-top">
                <div class="font-standard font-family-v3 device-setting-container-title">モバイル</div>
                <div class="flexbox-x-axis  flexbox-space-top">
                    <div class="basic-label">サイズ(px)</div>
                    <div class="device-setting-size-container">
                        <input name="otaprice-greeting-width-m" type="text" class="device-setting-size-input" placeholder="幅" value="<?php echo ($post['otaprice-greeting-width-m']) ?>">
                        <p class="device-setting-size-letter">W</p>
                    </div>
                    <div class="device-setting-size-container">
                        <input name="otaprice-greeting-height-m" type="text" class="device-setting-size-input" placeholder="高さ" id="otaprice-greeting-height-m" value="<?php echo ($post['otaprice-greeting-height-m']) ?>">
                        <p class="device-setting-size-letter">H</p>
                    </div>
                </div>
                <div class="flexbox-x-axis  flexbox-space-top">
                    <div class="basic-label">表示位置</div>
                    <div class="device-setting-size-container">
                        <input name="otaprice-greeting-bottom-m" type="text" class="device-setting-size-input" placeholder="下" value="<?php echo ($post['otaprice-greeting-bottom-m']) ?>">
                        <p class="device-setting-size-letter">Y</p>
                    </div>
                    <div class="device-setting-size-container">
                        <input name="otaprice-greeting-right-m" type="text" class="device-setting-size-input" placeholder="右" id="otaprice-greeting-right-m" value="<?php echo ($post['otaprice-greeting-right-m']) ?>">
                        <p class="device-setting-size-letter">X</p>
                    </div>
                    <div class="col-md-1" style="padding-right:0px;">
                        <?php echo Form::select('otaprice-greeting-posi-m', $posilist, $post['otaprice-greeting-posi-m'], array('style' => 'width:80px;', 'class' => 'form-control', 'id' => 'otaprice-greeting-posi-m')) ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- その他項目 -->
<div style="display:none;">
    <input type="hidden" name="z-index" value="<?php if ($post != NULL) echo ($post['z-index']) ?>">
    <input type="checkbox" name="show_notification" <?php if ($post['show_notification'] == 1) echo ('checked ') ?> value="1" class="make-switch" data-on-color="success" data-off-color="warning">
    <input type="hidden" name="delay_show" value="<?php echo ($post['delay_show']) ?>">
    <input type="hidden" name="scroll_show" value="<?php echo ($post['scroll_show']) ?>">
    <input type="hidden" name="close_icon_with_greeting" value="<?php echo $post['close_icon_with_greeting'] ?>">
    <input type="hidden" name="chatbot_icon_animation" value="<?php echo $post['chatbot_icon_animation'] ?>">
    <input type="hidden" name="talkappi_reshow" value="<?php echo ($post['talkappi_reshow']) ?>">
    <input type="hidden" name="talkappi_reshow_mobile" value="<?php echo ($post['talkappi_reshow_mobile']) ?>">
    <input type="hidden" name="width" value="<?php echo ($post['width']) ?>">
    <input type="hidden" name="height" value="<?php echo ($post['height']) ?>">
    <input type="hidden" name="width-m" value="<?php echo ($post['width-m']) ?>">
    <input type="hidden" name="height-m" value="<?php echo ($post['height-m']) ?>">
    <input type="hidden" name="bottom" value="<?php echo ($post['bottom']) ?>">
    <input type="hidden" name="right" value="<?php echo ($post['right']) ?>">
    <input type="hidden" name="icon-posi" value="<?php echo ($post['icon-posi']) ?>">
    <input type="hidden" name="bottom-m" value="<?php echo ($post['bottom-m']) ?>">
    <input type="hidden" name="right-m" value="<?php echo ($post['right-m']) ?>">
    <input type="hidden" name="icon-posi-m" value="<?php echo ($post['icon-posi-m']) ?>">
    <input type="hidden" name="close-icon-size" value="<?php echo ($post['close-icon-size']) ?>">
    <input type="hidden" name="close-icon-bkcolor" value="<?php if ($post != NULL) echo ($post['close-icon-bkcolor']) ?>">
    <input type="hidden" name="icon-bdcolor" value="<?php if ($post != NULL) echo ($post['icon-bdcolor']) ?>">
    <input type="hidden" name="radius" value="<?php echo ($post['radius']) ?>">
    <input type="hidden" name="h-shadow" value="<?php echo ($post['h-shadow']) ?>">
    <input type="hidden" name="v-shadow" value="<?php echo ($post['v-shadow']) ?>">
    <input type="hidden" name="blur" value="<?php echo ($post['blur']) ?>">
    <input type="hidden" name="spread" value="<?php echo ($post['spread']) ?>">
    <input type="hidden" name="talkappi-file" value="<?php echo ($post['talkappi-file']) ?>">
    <input type="hidden" name="talkappi-mobile-file" value="<?php echo ($post['talkappi-mobile-file']) ?>">
    <input type="hidden" name="minimized-on" value="<?php echo $post['minimized-on'] ?>">
    <input type="hidden" name="minimized-icon" value="<?php echo $post['minimized-icon'] ?>">
    <div class="talkappi-multitext" data-type="input" data-name="minimized-text" data-language='<?php echo json_encode(explode(',', $_bot->support_lang), JSON_UNESCAPED_UNICODE)?>' data-value='<?php echo $post['minimized-text'] ?>' data-max-input="20" title=""></div>
    <input type="hidden" name="minimized-color" value="<?php if ($post != NULL) echo ($post['minimized-color']) ?>">
    <input type="hidden" name="minimized-bkcolor" value="<?php if ($post != NULL) echo ($post['minimized-bkcolor']) ?>">
    <input type="hidden" name="minimized-icon-posi" value="<?php echo ($post['minimized-icon-posi']) ?>">
    <input type="hidden" name="minimized-icon-offset" value="<?php echo ($post['minimized-icon-offset']) ?>">
    <input type="hidden" name="minimized-icon-posi-m" value="<?php echo ($post['minimized-icon-posi-m']) ?>">
    <input type="hidden" name="minimized-icon-offset-m" value="<?php echo ($post['minimized-icon-offset-m']) ?>">
    <input type="hidden" name="minimized-default" value="<?php echo $post['minimized-default'] ?>">
    <input type="hidden" name="minimized-default-m" value="<?php echo $post['minimized-default-m'] ?>">
    <input type="hidden" name="w-bottom" value="<?php if ($post != NULL) echo ($post['w-bottom']) ?>">
    <input type="hidden" name="w-right" value="<?php if ($post != NULL) echo ($post['w-right']) ?>">
    <input type="hidden" name="t-w-right" value="<?php echo ($post['t-w-right']) ?>">
    <input type="hidden" name="w-height" value="<?php if ($post != NULL) echo ($post['w-height']) ?>">
    <input type="hidden" name="w-width" value="<?php if ($post != NULL) echo ($post['w-width']) ?>">
	<div class="talkappi-upload" data-name="talkappi_file_base64" data-label="<?php echo basename($post['talkappi-file']) ?>" data-type="img" data-ratio="1:1" data-url="<?php echo ($post['talkappi-file']) ?>"></div>
    <div class="talkappi-upload" data-name="talkappi_m_file_base64" data-label="<?php echo basename($post['talkappi-mobile-file']) ?>" data-type="img" data-ratio="1:1" data-url="<?php echo ($post['talkappi-mobile-file']) ?>"></div>
</div>

<!-- Hidden fields from botthemebubbles.php -->
<div style="display:none;">
    <input type="hidden" name="greeting-color" value="<?php if ($post != NULL) echo ($post['greeting-color']) ?>">
    <input type="hidden" name="greeting-bkcolor" value="<?php if ($post != NULL) echo ($post['greeting-bkcolor']) ?>">
    <input type="hidden" name="greeting-bdcolor" value="<?php if ($post != NULL) echo ($post['greeting-bdcolor']) ?>">
    <input type="hidden" name="close-icon-color" value="<?php if ($post != NULL) echo ($post['close-icon-color']) ?>">
    <input type="hidden" name="greeting-font-size" value="<?php echo ($post['greeting-font-size']) ?>">
    <input type="hidden" name="greeting-font-size-m" value="<?php echo ($post['greeting-font-size-m']) ?>">
    <input type="hidden" name="greeting-bottom" value="<?php echo ($post['greeting-bottom']) ?>">
    <input type="hidden" name="greeting-right" value="<?php echo ($post['greeting-right']) ?>">
    <input type="hidden" name="greeting-posi" value="<?php echo ($post['greeting-posi']) ?>">
    <input type="hidden" name="greeting-bottom-m" value="<?php echo ($post['greeting-bottom-m']) ?>">
    <input type="hidden" name="greeting-right-m" value="<?php echo ($post['greeting-right-m']) ?>">
    <input type="hidden" name="greeting-posi-m" value="<?php echo ($post['greeting-posi-m']) ?>">
    <input type="hidden" name="greeting-width" value="<?php echo ($post['greeting-width']) ?>">
    <input type="hidden" name="greeting-h-shadow" value="<?php echo ($post['greeting-h-shadow']) ?>">
    <input type="hidden" name="greeting-v-shadow" value="<?php echo ($post['greeting-v-shadow']) ?>">
    <input type="hidden" name="greeting-blur" value="<?php echo ($post['greeting-blur']) ?>">
    <input type="hidden" name="greeting-spread" value="<?php echo ($post['greeting-spread']) ?>">
    <input type="hidden" name="greeting-radius" value="<?php echo ($post['greeting-radius']) ?>">
</div>