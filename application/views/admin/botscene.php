<style>
	.section-wrapper .talkappi-section {
		margin-left: 0 !important;
	}
</style>

<?php echo $menu ?>
<input type="hidden" name="message" id="message" value="<?php echo ($message) ?>" />
<input type="hidden" name="func_type" id="func_type" value="" />
<input type="hidden" name="channels" id="channels" value="" />
<input type="hidden" name="skill_set" id="skill_set" value='<?php echo (is_array($post['welcome_skill'])) ? json_encode($post['welcome_skill']) : $post['welcome_skill'] ?>' />
<div class="content-container white border">
	<div class="setting-container">
		<div class="setting-header"></div>
		<!-- 導線名 -->
		<div class="lines-container">
			<div class="basic-label">導線名</div>
			<input name="label" id="label" type="text" class="text-input-longer" value="<?php echo ($post['label']) ?>">
		</div>
		<!-- 導線コード -->
		<div class="lines-container">
			<div class="basic-label">導線コード</div>
			<input name="scene_name_show" id="scene_name_show" type="text" maxlength="100" class="text-input-longer" value="<?php echo ($scene_cd) ?>">
		</div>
		<div class="lines-container">
			<div class="basic-label"></div>
			<div class="checkbox-label">
				<input type="checkbox" name="default_scene_cd" id="default_scene_cd" <?php if ($post['default_scene_cd'] == $scene_cd) echo ('checked disabled') ?> value="1">
				<label for="default_scene_cd" style="margin-right:10px">デフォルト導線にする</label>
			</div>
			<div class="checkbox-label">
				<input type="checkbox" name="very_scene_cd" id="very_scene_cd" <?php if ($post['very_scene_cd'] == $scene_cd && $post['very_scene_cd'] != '') echo ('checked') ?> value="1">
				<label for="very_scene_cd">VERY導線にする</label>
			</div>
		</div>
		<div class="lines-container">
			<div class="basic-label">継承する導線</div>
			<?php echo Form::select('ref_scene_cd', $ref_scene_cd_list, $post['ref_scene_cd'], array('id' => 'ref_scene_cd', 'class' => 'dropdown-container dropdown-shorter')) ?>
		</div>
		<div class="lines-container" style="display:none;">
			<div class="basic-label">CHATBOT起動時の表示内容</div>
			<div scene_name="<?php echo ($post['ref_scene_cd']) ?>">
				<div class="talkappi-skill-select" data-name="welcome_skill" data-value='<?php echo (is_array($post['welcome_skill'])) ? json_encode($post['welcome_skill']) : $post['welcome_skill']?>' data-mode="skill"></div>
			</div>
		</div>
		<div class="lines-container" style="display:none;">
			<div class="basic-label">チャンネル表示の順番<span class="icon-detail" title="スマートフォンでチャットボットを開いたときに表示するチャンネルの順番を設定できます。ドラッグ & ドロップで順番の変更が可能です。"></span></div>
			<nav class="button-tab" style="padding:0;">
				<ul class="js-channels">
					<?php 
					foreach($post['channels'] as $k) {
						echo('<li class="js-channel active" data-value="' . $k . '"><a href="javascript:void(0);">' . $_codes['08'][$k] . '</a></li>');
					}
					?>
				</ul>
			</nav>
		</div>
		<div class="lines-container">
			<div class="basic-label">利用機能</div>
			<div class="talkappi-checkbox" data-name="func" data-value='<?php echo(json_encode($post['func'])) ?>' data-source='<?php echo (json_encode($_codes['35'], JSON_UNESCAPED_UNICODE)) ?>'></div>
		</div>
		<div class="lines-container">
			<div class="basic-label"></div>
			<label style="color:red;text-align:left;">※変更後QRコード再作成と周知が必要です。</label>
		</div>
		<div class="lines-container" style="display:none;">
			<div class="basic-label">WebChat自動リセット</div>
			<div class="talkappi-switch js-webchat_reset_flg" data-name="webchat_reset_flg" data-value="<?php echo ($post['webchat_reset_flg']) ?>"></div>
			<input name="webchat_auto_reset_intval" style="display:none;margin-left:20px;" class="text-input js-webchat_auto_reset_intval" type="number" maxlength="10" class="text-input" value="<?php echo ($post['webchat_auto_reset_intval']) ?>">
			<label style="display:none;margin-left:8px;" class="js-webchat_auto_reset_intval" >秒後、チャットボット内の表示が自動でリセット</label>
		</div>

		<?php if ($_user->role_cd == "99") { ?>
		<!-- タグ名 -->
		<div class="lines-container">
			<div class="basic-label">タグ※</div>
			<input name="tag" id="tag" type="text" maxlength="50" class="text-input-longer" value="<?php echo ($post['tag']) ?>">
		</div>

		<!--ソート順 -->
		<div class="lines-container">
			<div class="basic-label">ソート順※</div>
			<?php
			$sort_array = [];
			for ($i = 1; $i <= 20; $i++) {
				$sort_array[$i] = $i;
			}
			echo Form::select('sort_no', $sort_array, $post['sort_no'], array('id' => 'sort_no', 'class' => 'dropdown-container dropdown-shorter'));
			?>
		</div>
		<?php } ?>
	</div>
	<div class="setting-container" style="display:none;">
		<div class="setting-header">プレビュー</div>
		<?php
		foreach ($_bot_lang as $k => $v) {
			echo ('<div class="lines-container">');
			echo ('<div class="basic-label">' . $v . '</div>');
			$val = '';
			if (isset($post['url'][$k])) {
				$val = $post['url'][$k];
			}
			echo ('<input name="url_' . $k . '" id="label" type="text" class="text-input-longer" placeholder="https://example.com" value="' . $val . '">');
			echo ('<div class="col-md-4">');
			echo ('<button data-href="https://talkappi-preview.com/chatbot/?fid=' . $scene_cd . '&admin=0&lang=' . $k . '" type="button" class="js-url-open btn-smaller btn-white">プレビュー</button>');
			echo ('</div>');
			echo ('</div>');
		}
		?>
	</div>

	<div class="setting-container" style="display:none;">
		<div class="setting-header">QRコード設定</div>
		<div class="lines-container">
			<div class="basic-label">QRコード作成<span class="icon-detail" title="ONにして保存すると、QRコードが作成されます。"></span></div>
			<div class="talkappi-switch" data-name="qr_create_flg" data-value="0"></div>
		</div>
		<div class="lines-container">
			<div class="basic-label">QRコード</div>
			<?php
			$bot_snses = explode(',', $_bot->sns_cd);
			if (in_array('wb', $bot_snses)) {
				$qr_file = $scene_path . $scene_cd . "/wb-" . $scene_cd . ".png";
				if (file_exists($qr_file)) {
					echo ('<a href="' . $scene_url_path . $scene_cd . '/wb-t-' . $scene_cd . '.png" target="_blank"><button type="button" class="btn grey action mr10 qr-code-btn"><img class="icon-download" src="/assets/admin/css/img/icon-import-blue.svg">汎用コード</button></a>');
					echo ('<a href="' . $scene_url_path . $scene_cd . '/wb-' . $scene_cd . '.png" target="_blank"><button type="button" class="btn grey action mr10 qr-code-btn"><img class="icon-download" src="/assets/admin/css/img/icon-import-blue.svg">Web</button></a>');
				} else {
					echo ('<button type="button" style="cursor:initial" class="btn grey mr10 qr-code-btn"><img class="icon-download" src="/assets/admin/css/img/icon-import-grey.svg">汎用コード</button>');
					echo ('<button style="cursor:initial" type="button" class="btn grey mr10 qr-code-btn"><img class="icon-download" src="/assets/admin/css/img/icon-import-grey.svg">Web</button>');
				}
			}
			?>
			<?php
			$bot_snses = explode(',', $_bot->sns_cd);
			foreach ($bot_snses as $sns_type_cd) {
				if ($sns_type_cd != 'wb') {
					$path = $scene_cd;
					$qr_file = $scene_path . $scene_cd . "/$sns_type_cd-" . $scene_cd . ".png";
					if (file_exists($qr_file)) {
						echo ('<a href="' . $scene_url_path . $scene_cd . '/' . $sns_type_cd . '-' . $scene_cd . '.png" target="_blank"><button type="button" class="btn grey action mr10 qr-code-btn"><img class="icon-download" src="/assets/admin/css/img/icon-import-blue.svg">' . $_codes['16'][$sns_type_cd] . '</button></a>');
					} else {
						//echo ('<button type="button" class="btn grey mr10">' . $_codes['16'][$sns_type_cd] . '</button>');
						echo ('<button style="cursor:initial" type="button" class="btn grey mr10 qr-code-btn"><img class="icon-download" src="/assets/admin/css/img/icon-import-grey.svg">' . $_codes['16'][$sns_type_cd] . '</button>');
					}
				}
			}
			//VERY導線のトグルONの場合のみ、veryのQRコードを表示
			if ($scene_cd == $post['very_scene_cd']){
				$qr_file = $scene_path . $scene_cd . "/vt-" . $scene_cd . ".png";
				if (file_exists($qr_file)) {
					echo ('<a href="' . $scene_url_path . $scene_cd . '/vt-' . $scene_cd . '.png" target="_blank"><button type="button" class="btn grey action mr10 qr-code-btn"><img class="icon-download" src="/assets/admin/css/img/icon-import-blue.svg">' . 'very' . '</button></a>');
				} else {
					echo ('<button style="cursor:initial" type="button" class="btn grey mr10 qr-code-btn"><img class="icon-download" src="/assets/admin/css/img/icon-import-grey.svg">' . 'very' . '</button>');
				}
			}
			?>
		</div>
		<?php
		/*
			foreach($scene_func_types as $k=>$v) {
				if ($k == 'bot') {
					echo('<div class="lines-container">');
					echo('<label class="control-label col-md-2">' .  $v['label'] . '</label>');
					echo('<div class="col-md-3">');
					echo Form::select('bot_theme_cd', $bot_theme_cd, $post['bot_theme_cd'], array('id'=>'bot_theme_cd','class'=>'form-control'));
					echo('</div>');
					$theme_file = $scene_path . $scene_cd . '/' . $k . '/' . $post['bot_theme_cd'] .  "/config.json";
				}
				else {
					if ($_bot_setting['flg_faq_site'] == 0 && $k == 'faq') continue;
					if ($_bot_setting['flg_accept_order'] == 0 && $k == 'reserve') continue;
					$theme_file = $scene_path . $scene_cd . '/' . $k . "/config.json";
					echo('<div class="lines-container">');
					echo('<label class="control-label col-md-2">' .  $v['label'] . '</label>');
				}
				
				if(file_exists($theme_file)) {
					$color = $v['color'];
				}
				else {
					$color = "grey-steel";
				}
				echo('<div class="col-md-4"><button type="button" class="btn '. $color . ' mr10 action" act="theme" func="' . $k . '" scene_name="' . $scene_cd . '">' . 'カスタマイズ設定' . '</button>');
				if(file_exists($theme_file)) echo('<button type="button" func="' . $k . '" act="default" class="btn red action">設定を削除</button>');
				echo('</div>');
				echo('</div>');

			}
			*/
		?>
	</div>
	<div class="setting-container" style="display: none;">
		<div class="setting-header">掲載設定</div>
		<div class="lines-container">
			<div class="basic-label">表示スタイル</div>
			<?php echo Form::select('bot_theme_cd', $bot_theme_cd, $post['bot_theme_cd'], array('id' => 'bot_theme_cd', 'class' => 'dropdown-container dropdown-shorter')); ?>
		</div>
		<div class="form-group" style="display:none;">
			<div class="basic-label"></div>
			<div class="col-md-5">
				<label class="control-label"><?php if ($post != NULL) echo ($post['scene_data']) ?></label>
			</div>
		</div>
	</div>
	<div class="setting-container" style="border-bottom: none;">
		<div class="lines-container">
			<div class="basic-label">カスタマイズ設定</div>
			<!-- section components -->
			<div class="section-wrapper" style="width:100%">
			<div class="talkappi-section" data-value='<?php echo json_encode($scene_tag_settings, JSON_UNESCAPED_UNICODE | JSON_HEX_TAG | JSON_UNESCAPED_SLASHES | JSON_HEX_AMP); ?>' data-name="scene_tag_settings" data-type="botscenesetting" style="display: grid;row-gap: 2rem;margin: 0px 64px;"></div>
			</div>
			<!-- section components -->
		</div>
	</div>
	<div class="submit-btn-container" style="margin: 60px 0 0 134px;">
		<div type="button" class="btn-larger btn-blue js-action-save">保存</div>
		<div type="button" class="btn-larger btn-white" onclick="top.location='/admin/botscenes'">一覧に戻る</div>
		<div type="button" class="btn-larger btn-red-border js-action-delete"><span class="icon-delete"></span></div>
	</div>
</div>

<script>
	const _scene_cd = '<?php echo $scene_cd ?>';
	const _settings = <?php echo json_encode($scene_tag_settings, JSON_UNESCAPED_UNICODE) ?>;
	const _lang_array = <?php echo json_encode($lang_array, JSON_UNESCAPED_UNICODE) ?>;
</script>