<?php
	$maximum_remains = array_reverse($remains);
?>
<script src="<?php echo($_assets)?>global/plugins/fullcalendar-scheduler/lib/main.js" type="text/javascript"></script>
<script src="<?php echo($_assets)?>global/plugins/fullcalendar-scheduler/lib/locales-all.js" type="text/javascript"></script>
<link rel="stylesheet" type="text/css" href="<?php echo($_assets)?>global/plugins/fullcalendar-scheduler/lib/main.css"/>

<script>
	const js_calendar_array = <?php echo(json_encode($maximum_remains)) ?>;
	const js_bot_support_lang = <?php echo(json_encode(explode(',', $_bot->support_lang))) ?>;
	const js_maximum_order_sum = <?php echo(json_encode($orders)) ?>;
	const _maximum_id = '<?php echo $menu->maximum_id?>';
	const _extra_data = <?php echo(json_encode($post['extra_data'], JSON_UNESCAPED_UNICODE)) ?>;
	const _reminder_data = <?php echo json_encode($reminder_data, JSON_UNESCAPED_UNICODE) ?>;
	const _is_admin = <?php echo $_user->role_cd === '99' ? 'true' : 'false' ?>;
	const _comment_required = <?= $comment_required ? 'true' : 'false' ?>;
</script>

<style>
	input:invalid {
		border: 1px solid red;
	}

	.category-plan-image {
		margin-left:16px;
		width: 12px;
		height: 12px;
		background-repeat: no-repeat;
		background-size: 12px;
		background-image: url(/assets/admin/css/img/icon-photo-unactive.svg);
		cursor: pointer;
	}

	.table-scrollable {
		width: 100%;
		overflow-x: auto;
		overflow-y: visible;
		border: 1px solid #dddddd;
		margin: 20px 0 !important;
		height: 172px;
	}
	.category-plan-image.select {
		background-image: url(/assets/admin/css/img/icon-photo.svg);
	}
</style>
<script>
	var _role_cd = <?php echo $_user->role_cd?>
</script>
<?php echo $menu ?>	
<div class="content-container white border">
	<input type="hidden" name="maximum_data" id="maximum_data" value='<?php echo($post['maximum_data']) ?>' />
	<input type="hidden" name="extra_data" id="extra_data" value='<?php echo(json_encode($post['extra_data'], JSON_UNESCAPED_UNICODE)) ?>' />
	<input type="hidden" name="id" id="id" value='<?php echo($post['id']) ?>' />
		<!-- 基本設定　開始 -->
		<?php if($_user->role_cd != '63'){?>
			<div class="section-container bottom-line">
				<h2><?php echo __('admin.common.label.setting') ?></h2>
				<div class="form-body">
					<div class="form-group">
						<label class="control-label col-md-1"><?php echo __('admin.maximums.stock_name') ?></label>
						<div class="col-md-4">
							<input type="text" name="name" value="<?php echo $post['name']?>" class="form-control js-name-input">
						</div>
					</div>
					<div class="form-group">
						<label class="control-label col-md-1"><?php echo __('admin.common.label.code') ?></label>
						<div class="col-md-4">
						<input type="text" class="code-readonly" value="<?php echo $post['id']?>" placeholder="<?php echo __('admin.maximums.code_placeholder') ?>" readonly>
						</div>
					</div>
					<div class="form-group">
						<label class="control-label col-md-1"><?php echo __('admin.maximums.stock_type') ?></label>
						<div class="col-md-4">
						<div class="talkappi-pulldown" <?php if ($post['id'] != '') echo ('disabled="disabled"'); ?> data-name="span" data-value="<?php echo $post['span']?>" data-size="longer" data-source='<?php echo json_encode($span_codes, JSON_UNESCAPED_UNICODE) ?>'></div>
						</div>
					</div>
					<div class="form-group">
						<label class="control-label col-md-1"><?php echo __('admin.maximums.stock_label') ?></label>
						<div class="col-md-2">
							<div class="talkappi-pulldown js-out-stock" data-name="out_stock_label" data-value="<?php echo $post['extra_data']['stock_label']['out_stock']?>" data-source='<?php echo json_encode($post['stock_label']['LABEL_OUT_STOCK'], JSON_UNESCAPED_UNICODE) ?>'></div>
						</div>
						<label class="control-label col-md-1"><?php echo __('admin.maximums.stock_remain_few_label') ?></label>
						<div class="col-md-2">
							<div class="talkappi-pulldown js-low-stock" data-name="low_stock_label" data-value="<?php echo $post['extra_data']['stock_label']['low_stock']?>" data-source='<?php echo json_encode($post['stock_label']['LABEL_LOW_STOCK'], JSON_UNESCAPED_UNICODE) ?>'></div>
						</div>
					</div>
					<div class="form-group">
						<label class="control-label col-md-1"><?php echo __('admin.maximums.tax_label') ?></label>
						<div class="col-md-2">
							<div class="talkappi-pulldown js-tax-label" data-name="price_type" data-value="<?php echo $post['extra_data']['price_type']?>" data-source='<?php echo json_encode(['tax'=>__('inquiry.common.label.order.tax.included'),'tax-service'=>__('inquiry.common.label.order.tax.service.included'), 'tax-none'=>__('inquiry.common.label.order.tax.service.none')], JSON_UNESCAPED_UNICODE) ?>'></div>
						</div>
					</div>
					<div class="form-group">
						<label class="control-label col-md-1"><?php echo __('admin.maximums.maximum_num_switch') ?></label>
						<div class="col-md-4">
						<div class="talkappi-switch js-maximum-num-switch" style="margin-top:8px;" data-value="0"></div>
						</div>
					</div>
					<div class="form-group">
						<label class="control-label col-md-1"><?php echo __('admin.maximums.category') ?></label>
						<span style="margin-left:8px; padding: 8px;background: #f6f7f9;"><?php echo __('admin.maximums.category_master_description') ?></span>
					</div>
					<div class="form-group js-category-area">
						<label class="control-label col-md-1"></label>
						<div class="col-md-8 js-category-plans" style="padding-top: 6px;">
							<div class="image-action-group js-add-category-plan" style="margin-left:8px;">
								<img src="./../assets/admin/css/img/icon-add.svg" width="12" height="12">
								<span><?php echo __('admin.maximums.category_add') ?></span>
							</div>
						</div>
					</div>
				</div>
			</div>
			<!-- 基本設定　終了 -->
			<!-- 詳細設定 開始-->
			<div class="section-container">
				<h2><?php echo __('admin.maximums.stock_advanced_settings') ?></h2>
				<div class="js-setting-container"></div>
			</div>
			<!-- 詳細設定 終了-->
			
			<div class="section-container" style="display:none;">
				<table class="table table-striped table-bordered table-hover js-data-table">
					<thead>
						<tr>
							<th style="display:none;">seq</th>
							<th><?php echo __('admin.common.label.date') ?></th>
							<th><?php echo __('admin.common.label.time') ?></th>	
							<th><?php echo __('admin.maximums.remaning_stock') ?></th>
							<th style="display:none;">threshold</th>
						</tr>
					</thead>
					<tbody>
						<?php
						$remains = array_reverse($remains, true);
						foreach ($remains as $remain) {
							?>
							<tr class="gradeX odd" role="row">
								<!-- seq -->
								<td class="js-seq" style="display:none;"><?php echo($remain['seq']) ?></td>
								<!-- 日付 -->
								<td class="js-day"><?php echo($remain['day']) ?></td>
								<!-- 時間帯 -->
								<td><?php echo($remain['time']) ?></td>
								<!-- 残枠数 -->
								<td>
									<span class="js-remains-count"><?php echo($remain['maximum']) ?></span>
                    				<span style="margin:0 0 0 10px;" class="font-standard font-color-v1 pointer js-change-remains">変更</span>
								</td>
								<!-- threshold -->
								<td style="display:none;"><?php echo($remain['threshold']) ?></td>
							</tr>
						<?php } ?>
					</tbody>
				</table>
			</div>
		<?php }?>

		<div id='maximum_calendar' style="padding: 16px 12px 8px 12px;<?php if($post['span'] == "all") echo('display:none;')?>"></div>

		<?php if($_user->role_cd != '63'){?>
			<div class="section-container">
				<div class="form-group">
					<label class="control-label col-md-1"><?php echo __('admin.common.label.alert.mail') ?></label>
					<div class="col-md-8 js-category-plans" style="padding-top: 6px;">
						<div class="js-alertmailsetting"></div>
					</div>
				</div>
			</div>

			<!-- ボタン -->
			<div class="submit-btn-container" style="margin: 60px 0 0 134px;">
				<div class="btn-larger btn-blue js-action-save"><?php echo __('admin.common.button.save') ?></div>
				<?php if ($post['span'] != 'all') echo('<div class="btn-larger btn-yellow js-action-csv">' . __('admin.maximums.stock_csv_export') . '</div>') ?>
				<div class="btn-larger btn-white js-action-back"><?php echo __('admin.common.button.return_to_list') ?></div>
				<?php if($post['id'] !== "") {?>
					<?php if ($_user->role_cd == '99') { ?>
					<div class="btn-larger btn-red js-action-reset"><?php echo __('admin.maximums.reset') ?></div>
					<div class="btn-larger btn-red js-action-recalc"><?php echo __('admin.maximums.consistent_remaining') ?></div>
					<?php } ?>
					<div class="btn-larger btn-red-border js-action-delete"><span class="icon-delete"></span></div>
				<?php }?>
			</div>
		<?php }?>
</div>

<!-- template -->
<!-- モーダル背景 -->
<div class="modal-background js-modal-background" style="display: none;"></div>
<!-- template -->
<!-- 在庫数変更モーダル -->
<div class="modal-image-container js-modal-remains-container js-modal" style="display: none;height: 220px; min-height: 200px;">
	<form class="relative" style="min-height: 100%;">
		<!-- タイトル -->
		<div class="flexbox relative">
			<h4 class="font-standard font-size-v5 font-family-v2" style="margin: 0;">
    		<span class="js-max-modal-name"></span><?php echo __('admin.maximums.stock_change') ?></h4>
			<span class="js-close-modal icon-cancel-large survey-position-absolute-v2 pointer"></span>
		</div>
		<!-- 期限 -->
		<div class="survey-space-top-2 flexbox-x-axis" style="margin-bottom:10px;">
			<div style="padding-right:12px;"><?php echo __('admin.common.label.date') ?></div>
			<span class="js-remains-date" style="padding:0 60px 0 0;"></span>
			<span class="js-remains-time"></span>
			<div style="padding-right:12px;"><?php echo __('admin.maximums.number_of_stock') ?></div>
			<input type="number" min="0" required class="survey-period-date-container js-remains-input" style="width:100px; padding:0 12px; margin-right:60px" placeholder="">
			<div style="padding-right:12px;"><?php echo __('admin.maximums.stop_selling') ?></div>
			<div class="talkappi-switch js-selling-status-toggle" data-value="0"></div>
		</div>
		<!-- コメント -->
		<div class="js-comment-container flexbox-x-axis" style="margin-bottom:10px;">
			<div style="padding-right:12px;width:120px;">コメント<?php if ($comment_required) echo '(必須)' ?></div>
			<input type="text" class="form-control talkappi-textinput js-remains-comment" data-max-input="100" placeholder="" />
		</div>
		<!-- 変更履歴 -->
		<div class="table-scrollable" style="display:none;">
			<table id="table1members" class="table table-striped table-bordered table-hover js-data-table1 mail_address_table dataTable no-footer" role="grid" ready="true" style="width: 100%;">
				<thead>
					<tr role="row">
						<th style="width: 110px;" class="sorting_disabled" rowspan="1" colspan="1">
							<!-- txt_title_name_upload_label_count_float -->
							変更時刻
						</th>
						<th style="width: 120px;" class="sorting_disabled" rowspan="1" colspan="1">
							変更者
						</th>
						<th style="width: 100px;" class="sorting_disabled" rowspan="1" colspan="1">
							在庫数(変更後)
						</th>
						<th style="width: 100px;" class="sorting_disabled" rowspan="1" colspan="1">
							販売中止(変更後)
						</th>
						<th style="width: 100px;" class="sorting_disabled" rowspan="1" colspan="1">
							コメント
						</th>
					</tr>
				</thead>
				<tbody class="js_maximum_edit_history_table">
				</tbody>
			</table>
		</div>
		<!-- SUBMIT -->
		<div class="checkbox-label js-chk-force-update">
			<input type="checkbox" id="chk-force-update" value="1">
			<label for="chk-force-update" style="color:red;"><?php echo __('admin.maximums.force_update') ?></label>
		</div>
		<div class="flexbox" style="justify-content:space-between;align-items:baseline;padding-bottom:10px;">
			<div class="submit-btn-container">
				<button class="flexbox-center btn-smaller btn-blue js-save-remains" style="margin:0 12px 0 0;" type="submit">OK</button>
				<button class="flexbox-center btn-smaller btn-white js-close-modal" type="button"><?php echo __('admin.common.button.cancel') ?></button>
			</div>
			<div style="display:flex;flex-direction:column;align-items:flex-end;gap:5px;">
				<div class="js-display-history" style="display: none;">
					<div style="font-size: 10px;display: flex;">変更履歴を表示する
					<div class="talkappi-switch js-maximum-eidt-toggle" data-value="1" style="margin-left: 10px;">
						<label class="js-switch-select switch-label"><span class="switch-span off"></span></label></div>
					</div>
				</div>
				<div class="js-last-update" style="font-size: 10px;"></div>
			</div>
		</div>
	</form>
</div>

<!-- 複数在庫数変更モーダル -->
<div class="modal-image-container js-modal-all-days-maximum js-modal" style="display: none; height: 600px; padding-bottom:100px;">
	<form class="relative" style="min-height: 100%;">
		<!-- タイトル -->
		<div class="flexbox relative">
			<div>
				<h4 class="font-standard font-size-v5 font-family-v2" style="margin: 0;">
					<span class="js-max-modal-name"></span><?php echo __('admin.maximums.stock_change') ?>
				</h4>
				<p class="js-max-modal-date"></p>
			</div>
			<span class="js-close-modal icon-cancel-large survey-position-absolute-v2 pointer"></span>
		</div>
		<!-- 期限 -->
		<div class="all-days-maximum-body" style="height:420px;overflow-y:auto;margin-bottom:20px;"></div>
		<div class="checkbox-label js-chk-force-update-all">
			<input type="checkbox" id="chk-force-update-all" value="1">
			<label for="chk-force-update-all" style="color:red;"><?php echo __('admin.maximums.force_update') ?></label>
		</div>		
		<!-- SUBMIT -->
		<div class="submit-btn-container modal-image-button" style="bottom:-40px;">
			<button class="flexbox-center btn-smaller btn-blue js-save-all-day-remains" style="margin:0 12px 0 0;" type="submit">OK</button>
			<button class="flexbox-center btn-smaller btn-white js-close-modal" type="button"><?php echo __('admin.common.button.cancel') ?></button>
		</div>
	</form>
</div>

<script type="text/javascript">
    const bot_id = <?php echo json_encode($bot_id); ?>;
</script>