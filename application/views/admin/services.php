<script type="text/javascript">
	<?php //echo("var products ='" . json_encode($products) . "';");
	?>
	<?php //echo("var seat_types ='" . json_encode($seat_types) . "';");
	?>
const _user_id = <?php echo $_user->user_id; ?>;
const _clink_url = '<?php echo $clink_url; ?>';
const _inquiry_setting = <?php echo json_encode($inquiry_setting, JSON_UNESCAPED_UNICODE) ?>;
</script>
<!-- BEGIN PAGE HEADER-->
<!-- BEGIN PAGE HEAD -->
<div class="page-head">
	<!-- BEGIN PAGE TITLE -->
	<div class="page-title">
		<?php if ($action == 'servicelist') { ?>
			<h1><?php echo __('admin.servicelist.title.servicelist') ?><small></small></h1>
		<?php } else { ?>
			<h1><?php echo __('admin.servicelist.title.service') ?><small>※直近１ヶ月間のリクエストが表示されます。（画面が<?php echo $num_request_refresh_interval ?>秒間隔で自動更新）</small></h1>
		<?php } ?>
	</div>
	<!-- END PAGE TITLE -->
</div>
<!-- END PAGE HEAD -->
<!-- END PAGE HEADER-->
<!-- BEGIN PAGE CONTENT-->
<div class="row">
	<div class="col-md-12">
		<!-- Page Content -->
		<div id="page-wrapper">
			<div class="top-nav font-standard">
				<?php if ($action == 'servicelist') { ?>
					<ul>
						<li class="<?php if ($_action == 'servicelist') echo ('active'); ?>">
							<a href="/admin/servicelist">
								<?php echo __('admin.servicelist.label.servicelist') ?></a>
						</li>
						<?php
						if ($_user->role_cd == "99") {
						?>
							<li class="<?php if ($_action == 'service') echo ('active'); ?>">
								<a href="/admin/service">
									<?php echo __('admin.servicelist.label.service') ?></a>
							</li>
						<?php } ?>
					</ul>
				<?php } ?>
			</div>
			<div class="edit-container">
				<div class="settings-container">
					<input type="hidden" name="form_action" id="form_action" value="<?php echo $action ?>" />
					<input type="hidden" name="intval" id="intval" value="<?php echo $_bot_setting['num_request_refresh_interval'] ?>" />
					<input type="hidden" name="service_id" id="service_id" value="" />
					<input type="hidden" name="act" id="act" value="" />
					<input type="hidden" name="bot_class_cd" id="bot_class_cd" value="<?php echo $_bot->bot_class_cd ?>" />
					<input type="hidden" name="last_service_id" id="last_service_id" value="<?php echo ($last_service_id) ?>" />
					<input type="hidden" name="user_lang_cd" id="user_lang_cd" value="<?php echo ($user_lang_cd) ?>" />
					<input type="hidden" name="status_cd" id="status_cd" value="<?php echo $status_cd ?>" />
					<div class="form-body">
						<div class="form-group">
							<?php if ($action == 'servicelist') { ?>
								<label class="control-label col-md-1"><?php echo __('admin.servicelist.table.period') ?></label>
								<div class="col-md-4">
									<input name="start_date" id="start_date" value="<?php echo ($start_date) ?>" style="float:left;" class="form-control form-control-inline input-small date-picker" size="16" data-date-format="yyyy-mm-dd" type="text" />
									<input name="end_date" id="end_date" value="<?php echo ($end_date) ?>" style="float:left; margin-left:10px;" class="form-control form-control-inline input-small date-picker" size="16" data-date-format="yyyy-mm-dd" type="text" />
								</div>
							<?php } ?>
						</div>
						<div class="form-group">
							<label class="control-label col-md-1"><?php echo __('admin.servicelist.table.condition') ?></label>
							<div class="col-md-2">
								<?php echo Form::select('intent_cd', $service_intent, $intent_cd, array('id' => 'intent_cd', 'class' => 'form-control')) ?>
							</div>
							<div class="col-md-2">
								<select class="bs-select form-control" multiple data-text="<?php echo $status_text ?>">
									<?php
									foreach ($service_status as $k => $v) {
										echo ('<option value="' . $k . '">' . $v . '</option>');
									}
									?>
								</select>
							</div>
							<?php if ($action == 'services') { ?>
								<label class="control-label col-md-1"><?php echo __('admin.servicelist.table.notification') ?></label>
								<div class="col-md-2">
									<div class="talkappi-switch" style="margin-top:6px;" data-name="music_flg" data-value="1"></div>
								</div>
							<?php } ?>
							<?php if ($action == 'servicelist') { ?>
								<div class="col-md-1" style="width: initial;">
									<button type="button" id="searchButton" class="btn yellow">
										<i class="fa fa-search mr10"></i><?php echo __('admin.servicelist.table.search') ?></button>
								</div>
							<?php } ?>
							<?php if ($action == 'servicelist') { ?>
								<div class="col-md-1" style="width: initial;">
									<button type="button" id="servicecsv" class="btn green">
										<?php echo __('admin.servicelist.table.csv_export') ?></button>
								</div>
							<?php } ?>
							<label class="control-label col-md-1"><?php echo __('admin.servicelist.table.deleted_display') ?></label>
							<div class="col-md-2">
								<div class="talkappi-switch js-delete_flg" style="margin-top:6px;" data-name="delete_flg" data-value="<?php echo $delete_flg?>"></div>
							</div>							
						</div>
					</div>
					<div id="service-table">
						<?php echo $servicetable ?>
					</div>
				</div>
			</div>
		</div>
		<!-- /#page-wrapper -->
	</div>
</div>
<?php echo $talkbox ?>
<?php echo $servicebox ?>
<?php echo $memo_box ?>
<!-- END PAGE CONTENT-->
<script src="/assets/common/react/components/blocks/ResultSupportMailModal.bundle.js"></script>