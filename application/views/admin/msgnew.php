<style>
    /* basebotから引き継いだ分類は変更不可　begin */
    /* .only_hover .talkappi-dropdown-container {
        background-color: #eee;
        cursor: not-allowed;
        pointer-events: auto;
    }
    .only_hover:active .talkappi-dropdown-container {
        pointer-events: none;
    } */
    /* basebotから引き継いだ分類は変更不可　end */
    /* 言語選択の下線　begin */
    .select-lang ul  {
        gap: 6px 0px;
    }
    .select-lang li.current-lang  {
        position: relative;
    }
    .select-lang li.current-lang::after {
        content: '';
        height: 1px;
        display: inline-block;
        background: #245BD6;
        position: absolute;
        bottom: -1px;
        left: 0;
        right: 0; 
    }
    /* 言語選択の下線  end*/
    .lines-container {
        background: inherit;
    }
    .basic-label {
        width: 120px !important;
    }
    .talkappi-preview {
        padding: 0 !important;
    }
    .talkappi-dropdown-options {
        width: fit-content;
    }
    .filename-url {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    .talkappi-upload {
        overflow: hidden;
    }
    .talkappi-pulldown {
        min-width: 200px;
    }
    .talkappi-preview table {
        width: 300px;
        margin: 0 auto 0 0;
}
.btn-primary {
    color: #FFFFFF;
}
.talkappi-pulldown.js-translation-type.auto-translation .talkappi-dropdown-container.pulldown{
	background-color: #FFE6D6;
}
.talkappi-pulldown.js-translation-type .talkappi-dropdown-container.pulldown{
	background-color: #E3E5E8;
}
</style>
<?php 
$model = new Model_Adminmodel();
?>

<script>
    const _preview_data = <?php echo json_encode($preview_data, JSON_UNESCAPED_UNICODE) ?>;
    const _preview_data_ja = <?php echo json_encode($preview_data_ja, JSON_UNESCAPED_UNICODE) ?>;
	const _lang_edit = '<?php echo $lang_edit ?>';
    const _native_support_lang = <?php echo json_encode((isset($native_support_lang) && is_array($native_support_lang)) ? $native_support_lang : [], JSON_UNESCAPED_UNICODE) ?>;
	const _msg_type_cd = '<?php echo $msg_type_cd ?>';
	const _msg_class_cd = '<?php echo $msg_class_cd ?>';
	const _results = <?php echo json_encode($results) ?>;
	const _action_skills = <?php echo json_encode($action_skills) ?>;
	const _btn_select = <?php echo json_encode($btn_select) ?>;
	const _btn_select_edit = <?php echo json_encode($btn_select_edit) ?>;
	const _bot_snses = <?php echo json_encode($bot_snses) ?>;
	const _url_lang_cd = <?php echo json_encode($url_lang_cd) ?>;
    const _max_length = <?php echo $max_length ?>;
    let _image_option = '<?php echo $image_option ?>';
    const _msg_id = "<?php echo $msg_id ?>";
    let _translation_type = "<?php echo $translation_type ?>";
    const _msg_data = <?php echo json_encode($post['msg_data']) ?>;
<?php 
if ($temp_bot_id !== NULL) {
    echo('const _temp_bot_id = ' . $temp_bot_id . ';');
}
?>
</script>

<input type="hidden" id="message" value="<?php echo $message?>" />
<input type="hidden" name="lang" value="<?php echo $lang_edit?>" />
<input type="hidden" id="act" name="act" value="" />
<input type="hidden" id="msg" name="msg" value="" />
<input type="hidden" id="range" name="range" value="" />

<div style="position: absolute; right: 250px; top: 15px;display:none;">
    <a href=<?php echo '"/admin/' . $msg_action_type . 'msg?id='. $msg_id .'&lang_cd='. $lang_edit . '"' ?> type="button" id="newButton" class="btn blue surveys-btn" style="background-color: #245BD6; border-radius: 4px;">
        <?php echo __('admin.common.label.edit.old_screen') ?>
    </a>
</div>
<div class="content-container white border">
    <div class="setting-header"><?php echo __('admin.common.label.setting') ?></div>
	<div class="form-body">
		<?php if ($_bot_id == 0) { ?>
		<div class="lines-container">
			<label class="basic-label"><?php echo __('admin.common.label.industry') ?></label>
			<div>
				<div class="talkappi-pulldown" data-name="bot_class_cd" style="width: 400px;" 
                     data-value="<?php echo $post['bot_class_cd']?>" data-source='<?php echo json_encode($bot_class, JSON_UNESCAPED_UNICODE) ?>'></div>
			</div>
		</div>	
		<?php }?>
		<div class="lines-container">
			<label class="basic-label"><?php echo __('admin.common.label.classification') ?></label>
            <?php 
                if ($msg_id == NULL && $_bot_id > 0) {
                    unset($msg_class['98']);
                    unset($msg_class['99']);
                }
            ?>
            <div class="talkappi-category-select pulldown-only inline js-msg_class_cd" data-name="msg_class_cd" data-div="<?php echo $_bot_setting['div_item_class_6'] ?>" data-value='<?php echo $post['msg_class_cd']?>'></div>                     
		</div>								
        <?php if ($_user->role_cd == '99') { ?>			
		<div class="lines-container">
			<label class="basic-label"><?php echo __('admin.common.label.code') ?></label>
			<input name="msg_cd" id="msg_cd" maxlength="60" type="text" class="form-control" style="width:400px;"
             <?php if ($mode != 'self' && $_bot_id > 0) echo('readonly');?> value="<?php if ($post != NULL) echo($post['msg_cd'])?>">
		</div>
        <?php }?>
		<div class="lines-container">
			<label class="basic-label"><?php echo __('admin.common.label.name') ?></label>
            <input name="msg_name" id="msg_name" type="text" class="form-control" style="width:400px;" 
            <?php if ($mode != 'self' && $_bot_id > 0) echo('readonly');?> value="<?php if ($post != NULL) echo($post['msg_name'])?>">
		</div>
		<div class="lines-container" <?php if ($msg_menu_cd == 'sys') echo ('style="display:none;"');?>>
			<label class="basic-label"><?php echo __('admin.common.label.listing_period') ?></label>
			<div class="talkappi-datepicker-range" data-name="" 
            data-value='<?php echo json_encode($post['range']) ?>' data-time-format="hh:mm"></div>																		
		</div>
		<?php 
		if ($msg_type_cd != 'rcm') {
            foreach($item_data_def as $key=>$def) {
                if ($def['type'] == 'sel' || $def['type'] == 'opt' || $def['type'] == 'chk') {
                    $def['list'] = $model->get_customize_list($def);
                    if (count($def['list']) == 0) continue;
                }
            ?>
                <div class="lines-container">
                    <label class="basic-label" style="min-width: 120px;"><?php echo $def['title'] ?></label>
                    <?php 
                        if (!array_key_exists($key, $item_data)) $item_data[$key] = '';
                        if ($def['type'] == 'opt') { ?>
                            <div class="talkappi-radio" data-name=<?php echo $key ?>
                                data-value="<?php 
                                                if (array_key_exists('normal', $def['list'])) {
                                                    echo array_key_exists($item_data[$key], $def['list']) ? $item_data[$key] : 'normal';
                                                } else {
                                                    echo array_key_exists($item_data[$key], $def['list']) ? $item_data[$key] : '';
                                                }
                                            ?>"
                                data-source=<?php echo json_encode($def['list'], JSON_UNESCAPED_UNICODE) ?>></div>
                        <?php } else if ($def['type'] == 'sel') { ?>
                            <div class="talkappi-pulldown" data-name=<?php echo $key ?>
                                data-value=<?php echo $item_data[$key] ?> 
                                data-source=<?php echo json_encode($def['list'], JSON_UNESCAPED_UNICODE) ?>></div>
                        <?php } else if ($def['type'] == 'chk') { ?>
                            <div class="talkappi-checkbox" data-name=<?php echo $key ?>
                                data-value=<?php echo json_encode($item_data[$key]) ?> 
                                data-source=<?php echo json_encode($def['list'], JSON_UNESCAPED_UNICODE) ?>></div>
                        <?php } else if ($def['type'] == 'num' || $def['type'] == 'txt') {
                            $style = '';
                            if ($def['type'] == 'num') {
                                $style = 'style="width:100px;text-align: right;"';
                            } 
                            if (array_key_exists($key, $item_data)) {
                                $value = $item_data[$key];
                            }
                            else {
                                $value = '';
                            } ?>
                        <input name=<?php echo $key ?> type="text" class="form-control" <?php echo $style ?> value="<?php echo $value ?>">
                        <?php } else if ($def['type'] == 'txa') { 
                            if (array_key_exists($key, $item_data)) {
                                $value = $item_data[$key];
                            }
                            else {
                                $value = '';
                            } ?>
                            <textarea name=<?php echo $key ?> id=<?php echo $key ?> class="form-control auto-resize" rows="3" style="height:80px; overflow-y:hidden; resize: vertical;"><?php echo $value ?></textarea>
                    <?php } ?>
                </div>
            <?php }
		} else {							
			if ($post['msg_data']!= '' && $_user->role_cd=='99') { ?>
			<div class="form-group">
				<label class="control-label col-md-2 label-fix-6"><?php echo __('admin.common.label.related_data') ?>※</label>
				<label class="control-label col-md-10" style="text-align:left;"><?php echo $post['msg_data']?></label>
			</div>
			<?php }
		} ?>
        <div class="js-input-container-no-lang"></div>
	</div>
    <!-- メインコンテンツ -->
    <div class="flexbox js-main">
        <div id="<?php if ($msg_type_cd == 'txt') echo "js-sort-content" ?>" style="flex-grow: 10; width: 50%;">
            <div style="display:flex;justify-content:space-between;align-items:flex-start;margin-top:30px;gap:20px;">
            <!-- 言語切り替えタブ -->
            <nav class="select-lang">
                <ul class="flexbox" style="margin: 0; padding-inline-start: 0px; flex-flow: wrap;">
                    <?php 
                        $lang = NULL;
                        if ($msg_id != NULL) foreach($_bot_lang as $lang_cd=>$lang_name) {
                            if($lang_cd === $lang_edit){
                                $lang = $lang_name;
                                echo('<li class="current-lang">');
                            } else {
                                echo('<li>');
                            }
                            $url = '/admin/' . $msg_action_type . 'msgnew?id='. $msg_id .'&lang_cd='. $lang_cd;
                            if ($is_admin) {
                                $url .= '&admin=1';
                            }
                            echo('<a class="func-menu" href="' . $url . '">' . $lang_name . '</a></li>');
                        }
                    ?> 
                </ul>
            </nav>
            <!-- 翻訳タイプ -->
            <?php 
                $hide_translation_type = false;
                if ($lang_edit == 'ja') {
                    $hide_translation_type = true;
                }
            ?>
            <div class="talkappi-pulldown js-translation-type <?php echo $translation_type == '1' ? "auto-translation" : "" ?> <?php echo $hide_translation_type ? 'hide' : '' ?>" data-value="<?php echo $translation_type ?>" data-source='[{"code":"0","text":"<?php echo __('admin.common.label.native_translation') ?>"},{"code":"1","text":"<?php echo __('admin.common.label.auto_translation') ?>"}]'></div>
            </div>
            <div class="js-input-container" style="padding: 25px 25px 25px 0;"></div>
            <!-- 多言語反映 -->
			<?php if (count($_bot_lang) > 1) { ?>
            <div class="lines-container">
                <div class="">
                    <label class="basic-label" style="width: fit-content; margin-right: 24px !important;"><?php echo __('admin.common.label.reflect.all_lang') ?><?php echo $lang ?></label>
                </div>
                <div class="">
                    <div class="checkbox-label">
                        <input type="radio" name="translate" id="translate_no" value='translate_no'>
                        <label for="translate_no"><?php echo __('admin.common.label.not_reflect') ?></label>
                    </div>
                    <div class="checkbox-label">
                        <input type="radio" name="translate" id="translate_copy"  value='translate_copy'>
                        <label for="translate_copy"><?php echo __('admin.common.label.reflect.not.translate') ?></label>
                    </div>
                    <div class="checkbox-label js-translate_auto">
                        <input type="radio" name="translate" id="translate_auto" value='translate_auto'>
                        <label for="translate_auto"><?php echo __('admin.common.label.reflect.translate') ?></label>
                    </div>
                    <?php if ($flg_native_translate == "1"  && $lang_edit == 'ja') { ?>
                        <div class="checkbox-label js-request">
                            <input type="radio" name="translate" id="translate_native" value='translate_native'>
                            <label for="translate_native"><?php echo __('admin.common.label.reflect.native.translate') ?></label>
                        </div>
                    <?php } ?>
                </div>
            </div>
            <div class="lines-container js-trans-content" style="margin: 16px 0px 0px 200px; display: none;">
                <div class="edit-menu-container" style="width: auto; display: flex; gap: 8px; flex-direction: column; font-weight:300; min-width: auto;">
                    <div>
                        <p><?php echo __('admin.common.native.translation.desc') ?></p>
                        <p><?php echo __('admin.common.native.translation.explain') ?></p>
                    </div>
                    <div class="flexbox-x-axis" style="gap: 12px;">
                        <p><?php echo __('admin.common.label.expect.lang') ?></p>
                        <div class="flexbox-x-axis" style="gap: 8px; flex-wrap: wrap;">
                        <?php
                            foreach($_bot_lang as $k=>$v) {
                                $style='';
                                if ($k == $lang_edit || ($k == 'js' || $k == 'th' || $k == 'vi' || $k == 'ne')) $style='display: none;';
                                if (!in_array($k, $native_support_lang)) {
                                    $style='display: none;';
                                }
                                echo('<div class="checkbox-label translate-lang flexbox-x-axis" style="' . $style . '">');
                                echo('<input type="checkbox" value="' . $k . '" name="translate_lang[]" id="translate_to_' . $k.'">');
                                echo('<label for="translate_to_'.$k.'" style="margin: 0; white-space: nowrap;">'. $_bot_lang[$k] .'</label>');
                                echo('</div>');
                            }
                        ?>
                        </div>
                    </div>
                    <div style="display:flex">
                        <div class="talkappi-pulldown js-trans-priority" style="width: fit-content;" data-name="trans-priority" data-value="2" data-source='{"4":"<?php echo __('admin.common.label.priority') ?>：<?php echo __('admin.common.label.priority.high') ?>", "2":"<?php echo __('admin.common.label.priority') ?>：<?php echo __('admin.common.label.priority.normal') ?>", "1":"<?php echo __('admin.common.label.priority') ?>：<?php echo __('admin.common.label.priority.low') ?>"}'></div>
                        <div style="align-self: center; margin-left: 1em;" class="js-trans-estimate"></div>
                    </div>
                    <textarea class="fullwidth-textarea menu-text" style="white-space: pre-wrap;word-wrap: break-word;display:block;max-width:100%;" name="translate_comment" placeholder="<?php echo __('admin.common.native.translation.placeholder') ?>"></textarea>
                </div>
            </div>
            <?php } ?>
        </div>
        <div id="msg-preview" style="margin:35px 0 0 48px;">
            <div class="talkappi-preview js-msg-preview preview-sticky" data-type="message" style="margin: 0 0 0 auto; flex-basis: 30%; max-width:320px;">
            </div>
        </div>
    </div>
    <!-- 下部ボタン類 -->
	<div class="form-actions js-template-check" style="margin: 60px 0 0 0;" data-msgcd="<?php echo $post['msg_cd']?>" data-classcd="<?php echo $post['msg_class_cd']?>">
		<div class="row">
			<label class="control-label col-md-2 label-fix-6"></label>
			<div class="col-md-10 flex">
				<?php 
					if ($_bot_id == 0) {
						echo('<button type="button" class="btn-larger btn-blue js-action-save">' . __('admin.common.button.save') . '</button>');
					}
					else {
						if ($mode == 'inherit') {
                            echo('<button type="button" class="btn-larger btn-blue js-action-save">' . __('admin.common.button.save') . '</button>');
							//echo('<button type="button" class="btn-larger btn-blue js-action-save">カスタマイズ</button>');
						}
						else {
							echo('<button type="button" class="btn-larger btn-blue js-action-save">' . __('admin.common.button.save') . '</button>');
						}
					}
					if ($msg_type_cd == 'rcm' && $msg_id != null && $mode != 'inherit') {
						echo('<button type="button" class="btn-larger btn-white js-action-line">' . __('admin.common.label.create.line.menu') . '</button>');
						echo('<button type="button" class="btn-larger btn-white js-action-default">' . __('admin.common.label.default.menu.setting') . '</button>');
					}
				?>
                <?php if ($mode != 'inherit') {?>
                    <!-- 該当言語のみ削除 -->
                    <button type="button" class="btn-larger btn-red-border js-action-delete-lang" style="color: black;"
                    >
                        <span class="icon-delete" style="margin-right: 8px !important;"></span>
                        <?php
                        if ($lang_edit === 'ja') {
                            echo __('admin.common.button.delete_ja');
                        } elseif ($lang_edit === 'js') {
                            echo __('admin.common.button.delete_js');
                        } elseif ($lang_edit === 'en') {
                            echo __('admin.common.button.delete_en');
                        } elseif ($lang_edit === 'cn') {
                            echo __('admin.common.button.delete_cn');
                        } elseif ($lang_edit === 'tw') {
                            echo __('admin.common.button.delete_tw');
                        } elseif ($lang_edit === 'kr') {
                            echo __('admin.common.button.delete_kr');
                        } elseif ($lang_edit === 'th') {
                            echo __('admin.common.button.delete_th');
                        } elseif ($lang_edit === 'vi') {
                            echo __('admin.common.button.delete_vi');
                        } elseif ($lang_edit === 'ne') {
                            echo __('admin.common.button.delete_ne');
                        } elseif ($lang_edit === 'it') {
                            echo __('admin.common.button.delete_it');
                        } elseif ($lang_edit === 'es') {
                            echo __('admin.common.button.delete_es');
                        } elseif ($lang_edit === 'de') {
                            echo __('admin.common.button.delete_de');
                        } elseif ($lang_edit === 'fr') {
                            echo __('admin.common.button.delete_fr');
                        } elseif ($lang_edit === 'pt') {
                            echo __('admin.common.button.delete_pt');
                        } elseif ($lang_edit === 'ru') {
                            echo __('admin.common.button.delete_ru');
                        } elseif ($lang_edit === 'id') {
                            echo __('admin.common.button.delete_id');
                        } else {
                            echo __('admin.common.button.delete_other');
                        }
                        ?>
                    </button>
                <?php }?>
                <?php if ($mode != 'self' && $_bot_id > 0){ ?>
                    <!-- 全言語を削除 -->
                    <?php if ($mode != 'inherit') {?>
                        <button type="button" class="btn-larger btn-gray-black js-action-reset"><?php echo __('admin.common.label.reset_to_default_all')?></button>
                    <?php }?>
                <?php } else { ?>
                    <!-- 全言語を削除 -->
                    <button type="button" class="btn-larger btn-red-border js-action-delete" style="color: black;"
                    >
                        <span class="icon-delete" style="margin-right: 8px !important;"></span><?php echo __('admin.common.button.delete_all') ?>
                    </button>
                <?php } ?>
				<button type="button" onclick="top.location='/admin/<?php echo $back_action?>'" class="btn-larger btn-white js-action-back"><?php echo __('admin.itme.itemmeu.back') ?></button>
			</div>
		</div>
	</div>
</div>