			<!-- BEGIN PAGE HEADER-->
			<!-- BEGIN PAGE HEAD -->
			<div class="page-head">
				<!-- BEGIN PAGE TITLE -->
				<div class="page-title">
					<h1><?php echo($_active_menu_name)?><small></small></h1>
				</div>
				<!-- END PAGE TITLE -->
			</div>
			<!-- END PAGE HEAD -->
			<!-- END PAGE HEADER-->
			<!-- BEGIN PAGE CONTENT-->
			        <!-- Page Content -->
					<div id="page-wrapper">
						<?php if ($div_class !== '99'){ ?>
						<div style="position: absolute; right: 250px; top: 15px;">
							<a href=<?php echo '"/admin/botclasscodeeditnew?id=' . $code_div . '"' ?> type="button" id="newButton" class="btn blue surveys-btn" style="background-color: #245BD6;">
								新画面でCODE設定を編集する
							</a>
						</div>
						<?php } ?>
						<div class="portlet light">
						<?php 
							if (strlen($_active_menu) > 4) {
								$tab_menu = View::factory ('admin/menutab');
								echo($tab_menu);
							}
						?>
					<div class="portlet box">
						<div class="portlet-body">
							<div class="row">	
								<div class="form-group">
									<label class="control-label col-md-1">親コード</label>
									<div class="col-md-2">
										<input name="class_cd_cond" id="class_cd_cond" value="<?php if (array_key_exists('class_cd_cond', $post)) echo($post['class_cd_cond'])?>" class="form-control" type="text" placeholder=""/>
									</div>								
									<div class="col-md-2">
										<button type="button" id="searchClassCd" class="btn yellow mr10">
										<i class="fa fa-search mr10"></i>検索</button>										
									</div>
								</div>											
							</div>						
							<input type="hidden" name="class_cd_sel" id="class_cd_sel" value="<?php if ($post != NULL) echo($post['class_cd_sel'])?>" />
							<input type="hidden" id="isedit" value="<?php echo($isEdit)?>" />
							<input type="hidden" name="message" id="message" value="<?php echo($message)?>" />
							<table class="table table-striped table-bordered table-hover js-data-table">
							<thead>
							<tr>
								<th>表示順</th>							
								<th><?php echo($div_title['class_cd']);?></th>
								<th><?php echo($div_title['parent_cd']);?></th>
								<th>名称</th>
								<th>説明</th>
								<th>全件表示</th>
								<th>ボット専用</th>
								<th>アイコン写真</th>
								<th style="width: 200px;">操作</th>
							</tr>
							</thead>

							<tbody>
							<?php
								foreach ($data as $item) {
							?>
							<tr class="gradeX odd" role="row">
								<td><?php echo($item['sort'])?></td>
								<td><?php echo($item['class_cd'])?></td>
								<td><?php echo($item['parent_cd'])?></td>								
								<td><?php echo($item['ja'])?></td>
								<td><?php echo(htmlspecialchars($item['ja.word']))?></td>
								<td><?php echo($item['show_items_flg'])?></td>
								<td><?php echo($item['bot_id'])?></td>
								<td><?php echo($item['grid_pic_url'])?></td>
								<td>
									<button type="button" class="btn yellow action" act="01" class_cd="<?php echo($item['class_cd'])?>">編集</button>
									<button type="button" class="btn blue action" act="03" class_cd="<?php echo($item['class_cd'])?>">コピー</button>
									<button type="button" class="btn red action" act="02" class_cd="<?php echo($item['class_cd'])?>">削除</button>
								</td>
							</tr>
							<?php } ?>
							</tbody>
							</table>
							<br/>
							<button type="button" class="btn green-meadow action" act="00"><i class="fa fa-file mr10"></i>新規</button>
							<button type="button" class="btn gray action" act="04">戻る</button>
							<?php if ($isEdit) { ?>
							<h4 class="form-section"></h4>
								<div class="form-body">
									<div class="form-group">
										<label class="control-label col-md-2">表示順／<?php echo($div_title['class_cd']);?></label>
										<div class="col-md-1">
											<input name="sort" id="sort" type="text" class="form-control" value="<?php if ($post != NULL) echo($post['sort'])?>">
										</div>
										<div class="col-md-2">
											<input name="class_cd" id="class_cd" maxlength="8" type="text" class="form-control" value="<?php if ($post != NULL) echo($post['class_cd'])?>">
										</div>
									</div>
									<div class="form-group">
										<label class="control-label col-md-2"><?php echo($div_title['parent_cd']);?></label>
										<div class="col-md-2">
											<input name="parent_cd" id="parent_cd" type="text" maxlength="8" class="form-control" value="<?php if ($post != NULL) echo($post['parent_cd'])?>">
										</div>
									</div>
									<div class="form-group">
										<label class="control-label col-md-2">全件表示</label>
										<div class="col-md-2">
											<input name="show_items_flg" id="show_items_flg" type="text" maxlength="8" class="form-control" value="<?php if ($post != NULL) echo($post['show_items_flg'])?>">
										</div>
									</div>
									<div class="form-group">
										<label class="control-label col-md-2">ボット専用</label>
										<div class="col-md-2">
											<input name="bot_id" id="bot_id" type="text" maxlength="8" class="form-control" value="<?php if ($post != NULL) echo($post['bot_id'])?>">
										</div>
									</div>									
									<?php foreach($_bot_lang as $k=>$v) {?>											
									<div class="form-group">
										<label class="control-label col-md-2"><?php echo($v)?>／説明</label>
										<div class="col-md-3">
											<textarea name="name_<?php echo($k)?>" id="name_<?php echo($k)?>" class="form-control" maxlength="100"><?php if ($post != NULL) echo($post['name_' . $k])?></textarea>
										</div>
										<div class="col-md-6">
											<textarea name="word_<?php echo($k)?>" id="word_<?php echo($k)?>" class="form-control" maxlength="1000"><?php if ($post != NULL) echo(htmlspecialchars($post['word_' . $k]))?></textarea>
										</div>										
									</div>
									<?php }?>
									<div class="form-group">
										<label class="control-label col-md-2">アイコン写真</label>
										<div class="col-md-8">
											<div class="input-icon right">
												<input name="grid_pic_url" id="grid_pic_url" type="text" class="form-control" placeholder="" value="<?php if ($post != NULL) echo(htmlspecialchars($post['grid_pic_url']))?>" >
											</div>
										</div>
									</div>
								</div>
								<div class="form-actions">
									<div class="row">
										<div class="col-md-offset-2 col-md-9">
											<button type="button" id="saveBaseButton" class="btn blue mr10">
											<i class="fa fa-save mr10"></i>保存</button>
											<button type="reset" class="btn gray">リセット</button>
										</div>
									</div>
								</div>
								<?php } ?>
							</div>
							</div>
						</div>
			        </div>
			        <!-- /#page-wrapper -->

