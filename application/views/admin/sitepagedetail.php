<link href="https://cdn.jsdelivr.net/npm/fullcalendar@5.11.3/main.min.css" rel="stylesheet">
<script src='https://cdn.jsdelivr.net/npm/rrule@2.8.1/dist/es5/rrule.min.js'></script>
<script src="https://cdn.jsdelivr.net/npm/fullcalendar@5.11.3/main.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/fullcalendar-scheduler@5.11.3/main.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/@fullcalendar/rrule@5.11.3/main.global.min.js"></script>
<script>
    <?php $model = new Model_Adminmodel(); ?>
    const _item_style = <?php echo $item_style?>;
    const _page_cd = <?php echo $page_cd?>;
    const _display_divs = <?php echo $itemdivs?>;
    const _page_widget_category = JSON.parse(<?php echo json_encode($page_widget_category[0]['content']); ?>);
    const _items_class = <?php echo json_encode([
        '1'=>$_bot_setting['div_item_class_1'], 
        '2'=>$_bot_setting['div_item_class_2'], 
        '6'=>$_bot_setting['div_item_class_6'], 
        '7'=>$_bot_setting['div_item_class_7'], 
        '8'=>$_bot_setting['div_item_class_8'], 
    ])?>;
    const _items = <?php echo json_encode($items, JSON_UNESCAPED_UNICODE) ?>;
    const template_cd = <?php echo "'" . $template_cd . "'"?>;
    const _weekdays = <?php echo json_encode($_codes['46'], JSON_UNESCAPED_UNICODE)?>;
    const _lang_cd_admin = '<?php echo $lang_cd_admin ?>';
</script>
<input type="hidden" name='lang_cd' value='<?php echo $lang_cd; ?>'>
<input type="hidden" name='page_id' value='<?php echo $page_id; ?>'>
<input type="hidden" name='page_cd' value='<?php echo $page_cd; ?>'>
<style>
    .js-items-pulldown {
        margin-top: 12px;
    }

    /* カレンダー */
    .calendar {
        border: 1px solid var(--pale-grey-for-line, #E3E5E8);
        padding-top: 10px !important;
    }
    .fc {
        background-color: white;
    }
    .fc-view-harness {
        min-height: 70vh;
        min-width: 100vh;
    }
    .fc .fc-col-header-cell-cushion,
    .fc .fc-daygrid-day-number {
        color: black;
    }
    .fc .fc-toolbar .fc-button {
        background-color: #FFFFFF;
        color: #000000;
        border: 1px solid #3D3F45;
        padding: 0.4em 0.65em;
    }
    .calendar-title-bar .fc-button {
        background-color: #FFFFFF;
        color: #000000;
        border: 1px solid #3D3F45;
        padding: 0.4em 0.65em;
    }
    .calendar-title-bar .fc-button.active {
        background-color: #000000;
        color: #FFFFFF;
    } 
    .fc .fc-toolbar .fc-prev-button,
    .fc .fc-toolbar .fc-next-button {
        background-color: #FFFFFF;
        border: none;
        width: 30px;
        box-shadow: none;
        outline: none;
    }
    .fc .fc-toolbar .fc-prev-button:focus,
    .fc .fc-toolbar .fc-next-button:focus {
        background-color: #FFFFFF !important;
        border: none !important;
        box-shadow: none !important;
        outline: none !important;
    }
    .fc .fc-button-primary:disabled {
        background-color: #FFFFFF;
        color: #555555;
        border-color: #DDDDDD;
    }
    .fc .fc-button-primary:focus {
        box-shadow: none !important;
    }
    .fc .fc-toolbar.fc-header-toolbar {
        margin-bottom: 10px;
    }
    .fc-header-toolbar.fc-toolbar {
        display: flex;
        justify-content: space-between;
    }
    .fc-header-toolbar .fc-toolbar-chunk:first-child,
    .fc-header-toolbar .fc-toolbar-chunk:last-child {
        flex-grow: 0;
        flex-shrink: 0;
    }
    .fc-header-toolbar .fc-toolbar-chunk:nth-child(2) {
        flex-grow: 1;
        text-align: left;
    }
    .fc-header-toolbar .fc-button {
        border-radius: 0.25em !important;
    }
    .fc-header-toolbar .fc-button-group {
        display: inline-flex;
    }
    .fc-header-toolbar .fc-button-group > button:not(:first-child) {
        margin-left: 0.75em !important;
    }
    .fc .fc-toolbar-title {
        font-size: 14px;
        margin-left: 15px;
    }
    .fc .fc-col-header-cell {
        font-weight: 300;
        background-color: var(--grey-white, #F6F7F9);
    }
    .fc .fc-daygrid-day-top {
        flex-direction: row; 
        justify-content: flex-start;
        display: flex;
        padding-left: 4px;
    }
    .fc-event {
        opacity: 1 !important;
        overflow: hidden; 
    }
    .fc .fc-event-title {
        white-space: normal;
        word-break: break-word;
    }
    .hide-date-cells .fc-daygrid-day-number {
        display: none;
    }
    .fc-timegrid-slot-label-frame {
        display: flex;
        align-items: flex-start;
        justify-content: center;
    }
    .fc .fc-timegrid-axis-frame {
        justify-content: center;
    }
    .fc-timegrid-axis-cushion {
        align-self: flex-start;
    }
    .holiday-background {
        background-color: #FFDCE5 !important;
    }
    .fc .right-align {
        text-align: right;
    }
    .event-daytime-round {
        width: 8px;
        height: 8px;
        display: inline-block;
        border-radius: 50%;
        margin-right: 4px;
    }
    .event-text-style {
        font-family: 'Hiragino Sans', sans-serif;
        font-size: 10px;
    }
    .fc-timegrid-slot-label-frame {
        display: flex;
        align-items: flex-start;
        justify-content: center;
        min-height: 50px;
    }

    /* カレンダーのモーダル */
    .js-schedule-modal-container {
        width: 800px;
        max-height: 80%;
        padding: 30px 40px;
        margin: 0;
        border-radius: 4px;
        overflow-y: auto;
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 999;
        background: #fff;
        display: flex;
        flex-direction: column;
    }
    .js-section-title-edit {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    .js-section-title-edit .form-control {
        margin-right: 30px;
        flex-grow: 1;
    }
    .modal-item {
        display: flex;
        align-items: center;
        margin-top: 10px;
    }
    .modal-item .control-label {
        width: 100px;
    }
    .modal-header {
        border-bottom: none;
    }
    #repetition:focus {
        outline: none;
        border: 1px solid blue;
    }
    .js-repetition-pulldown {
        margin-left: 10px;
    }
    .modal-item {
        display: flex;
        align-items: center;
        margin-top: 20px;
    }
    .modal-item .control-label {
        width: 100px;
    }
    .modal-header {
        border-bottom: none;
    }
    #repetition:focus {
        outline: none;
        border: 1px solid blue;
    }
    .js-repetition-pulldown {
        margin-left: 10px;
    }

    /* モーダル内の色指定 */
    .color-check {
        position: relative;
        padding-left: 28px;
        cursor: pointer;
        user-select: none;
        bottom: 10px;
        left: 10px;
    }
    .color-check input {
        display: none;
    }
    .color-mark {
        position: absolute;
        top: 0;
        left: 0;
        height: 22px;
        width: 22px;
        border-radius: 50%;
        box-sizing: border-box;
    }
    .color-mark:after {
        content: "✔︎";
        position: absolute;
        border-radius: 50%;
        top: 3px;
        right: 5px;
        opacity: 0;
    }
    .color-check input:checked + .color-mark:after {
        opacity: 1;
    }

    /* カレンダー説明 */    
    .calendar-description {
        padding: 10px 12px;
        border-top: none;
        border-left: 1px solid var(--pale-grey-for-line, #E3E5E8);
        border-right: 1px solid var(--pale-grey-for-line, #E3E5E8);
        border-bottom: 1px solid var(--pale-grey-for-line, #E3E5E8);
        width: 100%;
        min-height: 38px;
        color: var(--talkappi-dark-grey, #3D3F45);
        font-size: 12px;
        font-weight: 300;
        overflow-y: hidden;
    }
    .calendar-description.scroll-on {
        height: 188px!important;
        overflow-y: scroll!important;
    }

    /* カレンダータイトル、文字サイズ設定 */    
    .calendar-title-bar {
        padding: 10px 20px;
        background-color: white;
        border-top: 1px solid var(--pale-grey-for-line, #E3E5E8);
        border-left: 1px solid var(--pale-grey-for-line, #E3E5E8);
        border-right: 1px solid var(--pale-grey-for-line, #E3E5E8);
    }
    .calendar-title {
        display: flex;
        align-items: center;
    }
</style>
<nav class="top-nav">
    <ul class="">
        <li><a href="/admin/sitepage?id=<?php echo $page_cd ?>"><?php echo __('admin.common.label.setting') ?></a></li>
        <li class="active"><a href="/admin/sitepagedetail?id=<?php echo $page_cd ?>"><?php echo __('admin.site.setting') ?></a></li>
        <li><a href="/admin/pagerefer?id=<?php echo $page_cd ?>"><?php echo __('admin.site.refer') ?></a></li>
    </ul>
</nav>
<div class="content-container white border">
    <div id="react-adminlangtabs"></div>
    <div class="section-container bottom-line" style="padding:0px 54px;">
        <div class="form-group">
            <label class="col-md-2"><?php echo __('admin.site.title') ?></label>
            <div class="col-md-10">
                <input type="text" class="form-control" name="page_title" value="<?php if (isset($page['page_title'])) { echo htmlspecialchars($page['page_title']); } else { echo ''; } ?>">
            </div>
        </div>
        <div class="form-group">
            <label class="col-md-2"><?php echo __('admin.site.desc') ?></label>
            <div class="col-md-10">
                <div class="summernote-edit js-summer-sample" data-name="page_description" data-value='<?php if (isset($page['title'])) { echo $model->summernote_encode($page['title']); } else { echo ''; } ?>' title="<?php echo __('admin.site.desc') ?>" data-upload-image="1" data-position="bottom">
                    <input type="text" class="form-control" value='<?php if (isset($page['title'])) { echo $model->summernote_encode($page['title']); } else { echo ''; } ?>' />
                </div>
            </div>
        </div>
        <div class="form-group">
            <label class="col-md-2"><?php echo __('admin.site.footer') ?></label>
            <div class="col-md-10">
                <div class="summernote-edit js-summer-sample" data-name="page_footer" data-value='<?php if (isset($page['page_footer'])) { echo $model->summernote_encode($page['page_footer']); } else { echo ''; } ?>' title="<?php echo __('admin.site.footer') ?>" data-position="bottom">
                    <input type="text" class="form-control" value='<?php if (isset($page['page_footer'])) { echo $model->summernote_encode($page['page_footer']); } else { echo ''; } ?>' />
                </div>
            </div>
        </div>
        <div class="form-group">
            <label class="col-md-2"><?php echo __('admin.site.image') ?></label>
            <div class="col-md-10">
                <div class="talkappi-upload" data-name="page_image" data-type="img" data-label="<?php if (isset($page['image'])) { echo $page['image']; } else { echo ''; } ?>" data-url="<?php if (isset($page['image'])) { echo $page['image']; } else { echo ''; } ?>" data-max-size="2" data-ratio="<?php echo __('admin.site.main_image.ratio') ?>" data-upload-now="page"></div>
            </div>
        </div>
        <div class="form-group">
            <label class="col-md-2"><?php echo __('admin.site.items_background_image') ?></label>
            <div class="col-md-10">
                <div class="talkappi-upload" data-name="items_background_image" data-type="img" data-label="<?php if (isset($page['items_background_image'])) { echo $page['items_background_image']; } else { echo ''; } ?>" data-url="<?php if (isset($page['items_background_image'])) { echo $page['items_background_image']; } else { echo ''; } ?>" data-max-size="5" data-upload-now="page"></div>
            </div>
        </div>
    </div>
    <!-- section components -->
    <div class="talkappi-section" data-value='<?php echo htmlspecialchars(json_encode($items, JSON_UNESCAPED_UNICODE | JSON_HEX_TAG | JSON_UNESCAPED_SLASHES | JSON_HEX_AMP), ENT_QUOTES, 'UTF-8'); ?>' data-name="items" data-type="site" style="display: grid;row-gap: 2rem;margin: 14px 64px;"></div>
    <!-- 翻訳セクション -->
     <div id="react-multilingualreflect" style="margin-top: 20px;"></div>
    <!-- section components -->
    <div class="form-actions">
        <div class="row">
        <div class="col-md-offset-2" style="margin-top: 48px;">
                <div class="actions-container">
                    <button type="button" class="action-button page btn-blue js-action-save"><?php echo __('admin.common.button.save') ?></button>
                    <a class="action-button page btn-white js-action-verify" href="<?php echo $verify_url; ?>" target="_blank"><?php echo __('admin.common.button.verify') ?></a>
                    <a class="action-button page btn-white js-action-back"><?php echo __('admin.common.button.return_to_list') ?></a>
                    <span class="action-button page btn-red-border icon-only js-action-delete"><span class="icon-delete"></span></span>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/1.12.4/jquery.min.js"></script>
<script src="/assets/common/react/components/blocks/adminlangtabs.bundle.js"></script>
<script src="/assets/common/react/components/blocks/multilingualreflect.bundle.js"></script>
<script type="text/javascript">
    jQuery(document).ready(function($) {
        // 表示言語
        const displayedLangs = Object.values(<?php echo json_encode($display_lang_cd) ?>);
        // サポート言語
        const allLangs = Object.keys(<?php echo json_encode($support_lang_cd) ?>);
        // 非表示言語
        const undisplayedLangs = allLangs.filter(key => !displayedLangs.includes(key));

        window.talkappi_admin_setupAdminLangTabs({
            displayed: displayedLangs,
            undisplayed: undisplayedLangs,
            url: `/admin/sitepagedetail?id=<?php echo $page_cd ?>&lang=`,
            active: '<?php echo $lang_cd ?>',
        });

        const to_lang_cds = Object.entries(<?php echo json_encode($support_lang_cd) ?>).map(([lang_cd, lang]) => ({
            lang_cd,
            lang,
        }));
        window.talkappi_admin_setupMultilingualReflect({
            from_lang_cd: '<?php echo $lang_cd ?>',
            to_lang_cds,
            has_reflect_without_translating: false,
            has_native_translate: false,
            admin_lang: _lang_cd_admin
        })
    });
</script>