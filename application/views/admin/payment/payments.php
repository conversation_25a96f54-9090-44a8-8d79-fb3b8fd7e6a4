<div class="content-container">
    <div class="flex-x-between">
        <div><?php echo __('admin.payment.common.label.list'); ?></div>
        <?php 
            if($_user->role_cd == '99') {
                echo ('<span class="btn-smaller btn-blue js-new-payment">');
                echo ('<span class="icon-add-white"></span>');
                echo __('admin.common.button.create_new');
                echo('</span>');
            }
        ?>
    </div>
</div>

<div class="content-container white">
    
    <input type="hidden" name="client_id" id="client_id" value="" />
    <input type="hidden" name="act" id="act" value="" />

    <table class="table table-striped table-bordered table-hover js-data-table">
        <thead>
            <tr class="odd">
                <th><?php echo __('admin.payment.label.client_id'); ?></th>
                <th><?php echo __('admin.payment.label.name'); ?></th>
                <th><?php echo __('admin.payment.label.address'); ?></th>
                <th><?php echo __('admin.payment.label.tel'); ?></th>
                <th><?php echo __('admin.payment.lable.person_in_charge'); ?></th>
                <th><?php echo __('admin.payment.label.bots_name'); ?></th>
                <th><?php echo __('admin.payment.label.account_number'); ?></th>
                <th><?php echo __('admin.pay.transfer.fee') ?><br>(<?php echo __('admin.pay.transfer.fee.less_than_three'); ?>)</th>
                <th><?php echo __('admin.pay.transfer.fee') ?><br>(<?php echo __('admin.pay.transfer.fee.more_than_three'); ?>)</th>
                <th><?php echo __('admin.payment.label.contract_date'); ?></th>
                <th><?php echo __('admin.payment.lable.handling.period'); ?></th>
                <th><?php echo __('admin.payment.lable.due_date'); ?></th>
                <th><?php echo __('admin.payment.label.payment_date'); ?></th>
                <th><?php echo __('admin.payment.label.acti_commision'); ?></th>
                <th><?php echo __('admin.payment.label.acti_commision2'); ?></th>
                <th style="width: 100px;"><?php echo __('admin.common.label.details'); ?></th>
                <?php 
                    if ($_user->role_cd == '99') echo('<th>' . __('admin.payment.label.operation') . '</th>');
                ?>
            </tr>
        </thead>

        <tbody>
            <?php 
            foreach ($result as $item) {
                $id = $item['client_id'];
                $payment_term = json_decode($item['payment_term'], true);
                $monthList = [
                    __('admin.payment.common.label.payment_month_1'), 
                    __('admin.payment.common.label.payment_month_2'), 
                    __('admin.payment.common.label.payment_month_3')
                ];
            ?>
            <tr class="gradeX odd" role="row">
                <!-- 取引先ID -->
                <td>
                    <?php echo $id; ?>
                </td>
                <!-- 取引名 -->
                <td>
                    <a class="link-animate" href="/<?php echo $_path ?>/payment?id=<?php echo $id ?>"><?php echo htmlspecialchars($item['name']) ?></a>
                </td>
                <!-- 住所 -->
                <td>
                    <?php echo ($item['postal_code'] == '' ? '' : '〒' . $item['postal_code']) . ' ' . htmlspecialchars($item['address']) ?>
                </td>
                <!-- 電話番号 -->
                <td>
                    <?php echo $item['tel'] ?>
                </td>
                <!-- 担当者 -->
                <td>
                    <?php echo htmlspecialchars($item['person_in_charge']) ?>
                </td>
                <!-- 取引施設 -->
                <td>
                    <?php echo $item['bots'] ?>
                </td>
                <!-- 口座番号 -->
                <td>
                    <?php echo $item['account_number'] ?>
                </td>
                <?php 
                    if (isset($item['transfer_fee'])) {
                        $transfer_fee_detail = json_decode($item['transfer_fee'], true);
                    } else {
                        $transfer_fee_detail = null;
                    }
                ?>
                <!-- 振込手数料(３万円未満) -->
                <td>
                    <?php 
                        if (isset($transfer_fee_detail["less_than_three"])) {
                            echo htmlspecialchars($transfer_fee_detail["less_than_three"]);
                        } else {
                            echo "";
                        }
                    ?>
                </td>
                <!-- 振込手数料(３万円以上) -->
                <td>
                    <?php 
                        if (isset($transfer_fee_detail["more_than_three"])) {
                            echo htmlspecialchars($transfer_fee_detail["more_than_three"]);
                        } else {
                            echo "";
                        }
                        
                    ?>
                </td>
                <!-- 契約日 -->
                <td style="white-space: nowrap;">
                    <?php echo $item['contract_date'] ?>
                </td>
                <td>
                    <?php for ($x = 0; $x < count($payment_term); $x++) { ?>
                    <?php
                        $from_day =  $payment_term[$x]['from_day'] == '-1' ? __('admin.payment.common.label.last_day') : $payment_term[$x]['from_day']. __('admin.payment.common.label.day');
                        $to_day =  $payment_term[$x]['to_day'] == '-1' ? __('admin.payment.common.label.last_day') : $payment_term[$x]['to_day']. __('admin.payment.common.label.day');
                        ?>
                    <!-- 取扱期間 -->   
                    <p><?php echo $from_day ?>〜<?php echo $to_day ?></p>
                    <!-- 売上締切日 -->
                    <?php } ?>
                </td>
                <td>
                    <?php for ($x = 0; $x < count($payment_term); $x++) { ?>
                    <?php 
                        if ($payment_term[$x]['due_day'] == '-1') {
                            echo '<p>'. __('admin.payment.common.label.last_day'). '</p>';
                        } else {
                            echo '<p>'.$payment_term[$x]['due_day'] . __('admin.payment.common.label.day'). '</p>';
                        }
                    ?>
                    <?php } ?>
                </td>
                <!-- 入金日 -->
                <td style="white-space: nowrap;">
                    <?php for ($x = 0; $x < count($payment_term); $x++) { ?>
                    <?php $payment_date = $payment_term[$x]['payment_date']; ?>
                    <?php 
                        if ($_lang_cd == 'en') {
                            echo '<p>'. $payment_date['day'] . ' of ' . strtolower($monthList[$payment_date['month'] - 1]). '</p>';
                        } else {
                            if ($payment_date['day'] == '-1') {
                                echo '<p>'. $monthList[$payment_date['month'] - 1] . __('admin.payment.common.label.last_day'). '</p>';
                            } else {
                                echo '<p>'. $monthList[$payment_date['month'] - 1] . $payment_date['day'] . __('admin.payment.common.label.day'). '</p>';
                            }
                        }
                    ?>
                    <?php } ?>
                </td>
                <!-- 手数料率１ -->
                <td>
                    <?php echo $item['acti_commision'] ?>%
                </td>
                <!-- 手数料率2 -->
                <td>
                    <?php echo $item['acti_commision2'] ?>%
                </td>
                <!-- 取引 -->
                <td style="white-space: nowrap;">
                    <a class="link-animate" href="/<?php echo $_path ?>/transactions?id=<?php echo $id ?>"><?php echo __('admin.payment.label.operation.transactions'); ?></a>
                    <br>
                    <a class="link-animate" href="/<?php echo $_path ?>/monthlyreport?id=<?php echo $id ?>"><?php echo __('admin.payment.label.operation.monthlyreport'); ?></a>
                </td>
                <!-- 操作 -->
                <?php 
                    if ($_user->role_cd == '99') {
                        echo('<td>');
                        echo('<a class="link-animate" style="color: black;" href="/' . $_path . '/payment?id=' . $id . '">');
                        echo('<div class="btn round image edit js-memo" style="margin-bottom: 4px;">');
                        echo __('admin.common.button.modify');
                        echo('</a></div>');
                        echo('<div class="btn round image delete js-memo" data-client_id="' . $id . '">' . __('admin.common.button.delete.full') . '</div>');
                        echo('</td>');
                    }
                ?>
            </tr>
            <?php 
            }
            ?>
        </tbody>
    </table>
</div>