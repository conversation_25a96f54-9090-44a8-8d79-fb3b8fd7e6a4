<style>
    div.dataTables_filter label {
    float: left;
    margin-left: -10px;
    }
    .col-md-6 {
    width: auto;
    }
    .content-title {
        margin-top: -20px;
        margin-left: -20px;
        display:flex;
        align-items: center;
    }

    .setting-icon {
        display:inline-block;
        padding-left:4px;
    }
    .talkappi-month-picker-container > input {
        width: 100px;
    }
    .talkappi-month-picker-container {
        display: flex;
        column-gap: 3px;
        align-items: center;
    }
    .month-picker-month-table .ui-button {
        width: auto;
    }
    .custom-data-table-header {
        display: flex;
        right: 20px;
        align-items: center;
    }
    iframe {
        width:100%;
        height:80vh;
    }
</style>

<input type="hidden" name="client_id" val=<?php echo $client_id; ?> />
<input type="hidden" name="start_date" value=<?php echo $start_date; ?> />
<input type="hidden" name="end_date" value=<?php echo $end_date; ?> />

<div class="content-container content-title">
    <div><?php echo __('admin.pay.transaction.client') ?></div>
    <select name="client" class="form-control js-client" style='width:192px;margin-left:8px;' <?php if (count($client) === 1) echo 'disabled="disabled"' ?>>
        <?php if (count($client) > 1) { ?>
            <option value="">-</option>
        <?php }  ?>
        <?php foreach ($client as $val) { ?>
        <option value="<?php echo $val['client_id'] ?>" <?php if ($val['client_id'] === $client_id) echo 'selected="selected"' ?>><?php echo $val['name'] ?></option>
        <?php } ?>
    </select>
</div>

<div class="content-container white" style="position:relative;">
    <table class="table table-striped table-bordered table-hover js-data-table" id="data-table" style="height: 100%">
        <div class="custom-data-table-header" style="top: 16px; display: flex">
                <div style="margin:0 8px;"><?php echo __('admin.pay.aggregation.period') ?></div>
                <div class="talkappi-datepicker-range" data-name='date' data-value=<?php echo json_encode(["startDate" => $post['start_date'],"endDate" => $post['end_date']]) ?>></div>
                <div class="action-button section btn-white exportCsv" data-tableid="data-table" data-title="<?php echo __('admin.payment.label.operation.monthlyreport') ?>"><span class="icon-export"></span><?php echo __('admin.common.button.csv_export') ?></div>
        </div>
        <thead>
            <tr>
                <th style="display:none;">取引先ID</th>
                <th><?php echo __('admin.pay.transaction.client') ?></th>
                <th><?php echo __('admin.pay.client.name') ?></th>
                <th><?php echo __('admin.pay.aggregation.period') ?></th> 
                <th><?php echo __('admin.pay.aggregation.date') ?></th>
                <th><?php echo __('admin.pay.sales.amount') ?></th>
                <th><?php echo __('admin.pay.transaction.fee') ?></th>
                <?php if ($_user->role_cd=='99'):?>
                <th><?php echo __('admin.pay.agency.commision') ?></th>
                <?php endif; ?> 
                <th><?php echo __('admin.pay.talkappi.commision') ?></th>
                <th><?php echo __('admin.pay.tax') ?></th>
                <th><?php echo __('admin.pay.transfer.fee') ?></th>
                <th><?php echo __('admin.pay.transfer.amount') ?></th>
                <?php 
                    if ($_user->role_cd == '99') echo('<th>' . __('admin.payment.label.operation') . '</th>');
                ?>
                <!-- 一旦、対応不要 -->
                <!-- <th><?php echo __('admin.common.label.operation') ?></th> -->
            </tr>
        </thead>
        <tbody>
            <?php
            foreach ($items as $item) {
            ?>
            <?php 
                $amount_type_style = '';
                if ($item['sales_amount'] >= 0) {
                    $amount_type_style = 'color: #000000';
                } else {
                    $amount_type_style = 'color: #E53361';
                }
            ?>
            <tr class="gradeX odd js-reception-table" role="row">
                <td style="display: none;">
                    <?php echo $item['client_id'] ?>
                </td>
                <td style="width: 240px;">
                    <?php echo $item['name'] ?>
                </td>
                <td>
                    <?php if (array_key_exists('bots', $item)) echo $item['bots'] ?>
                </td>
                <td style="word-break: break-word;">
                    <?php echo $item['due_date_from'] . ' - ' . $item['due_date_to'] ?>
                </td>
                <td style="word-break: break-word;">
                    <?php echo $item['upd_time'] ?>
                </td>
                <td style="text-decoration: underline;text-align: end;">
                    <?php 
                    $startDate = isset($item['due_date_from']) ? $item['due_date_from'] : '';  
                    $endDate = isset($item['due_date_to']) ? $item['due_date_to'] :  '';  
                    ?>
                    <a href="transactions?id=<?php echo $item['client_id'] ?>&start=<?php echo $startDate ?>&end=<?php echo $endDate ?>" style="<?php echo $amount_type_style ?>">
                    <?php echo '￥'.number_format(($item['sales_amount'])); ?>
                    </ahref=style=>
                </td>
                <td style="text-align: end;">
                    <?php echo '￥'.number_format(($item['transaction_fee'])); ?>
                </td>
                <?php if ($_user->role_cd=='99'):?>
                <td style="text-align: end;">
                    <?php echo '￥'.number_format(abs($item['agency_commision'])); ?>
                </td>
                <?php endif; ?> 
                <td style="text-align: end;">
                <?php echo '￥'.number_format(abs($item['acti_commision'])); ?>
                </td>
                <td style="text-align: end;">
                        <?php echo '￥'.number_format(abs($item['tax'])); ?>
                </td>
                <td style="text-align: end;">
                        <?php echo $item['transfer_money'] < 0 ? '￥0' : '￥'.number_format(($item['transfer_fee'])); ?>
                </td>
                <td style="text-align: end; <?php echo $amount_type_style ?>">
                        <?php echo '￥'.number_format(($item['transfer_money'])); ?>
                </td>
                <!-- 操作 -->
                <?php 
                    if ($_user->role_cd == '99') {
                        echo('<td style="text-align: center;">');
                            echo('<div class="btn round image send js-pdf-preview" style="margin-bottom: 4px;"
                                    data-month-report-id="' . htmlspecialchars($item['id']) . '"
                                    data-client-id="' . htmlspecialchars($item['client_id']) . '">
                                        PDFプレビュー
                                    </div>
                                ');
                            if ($item['send_time'] === null) {
                                echo('<div class="btn round image send js-send-mail" style="margin-bottom: 4px;"
                                        data-month-report-id="' . htmlspecialchars($item['id']) . '"
                                        data-client-id="' . htmlspecialchars($item['client_id']) . '">');
                                    echo __('admin.pay.mail.auto_send');
                                echo('</div>');
                                echo('<div class="btn round image send js-send-mail-manual" style="margin-bottom: 4px;"
                                        data-month-report-id="' . htmlspecialchars($item['id']) . '"
                                        data-client-id="' . htmlspecialchars($item['client_id']) . '">');
                                    echo '送付済みにする';
                                echo('</div>');
                            }
                            if ($item['send_time'] !== null) {
                                $send_time = new DateTime($item['send_time']);
                                $send_time_complete = $send_time->format('H:i') . ' ' . __('admin.pay.mail.complete');
                                if(isset($item['send_flg']) && $item['send_flg'] === 'manual'){
                                    echo('<div style="text-align: center; font-size: 10px;">' . $send_time->format('Y-m-d') . '<br>
                                            <span style="white-space: nowrap;">' . $send_time_complete . "(手作業)" . ' 
                                            </span>
                                        </div>'
                                    );
                                } else {
                                    // $item['send_flg'] = NULL or auto
                                    echo('<div style="text-align: center; font-size: 10px;">' . $send_time->format('Y-m-d') . '<br>
                                            <span style="white-space: nowrap;">' . $send_time_complete . '
                                            </span>
                                        </div>'
                                    );
                                }
                            } else {
                                echo('<div style="text-align: center; color: #E53361; font-size: 10px;">' . __('admin.pay.mail.incomplete') . '</div>');
                            }
                        echo('</td>');
                    }
                ?>
                <!-- <td>
                    <div class="btn round js-transaction-detail" >
                        <img src="/assets/admin/css/img/icon-output.svg" alt="icon-output" height=12 width=12 />
                        <span class="setting-icon"><?php echo __('admin.pay.transaction.detail.csv') ?></span>
                    </div>
                </td> -->
            </tr>
            <?php } ?>
        </tbody>
    </table>
</div>

<?php  
function _getAggregationPeriod($date1, $date2) {
    // Extract year and month from first date
    $year1 = substr($date1, 0, 4);
    $month1 = substr($date1, 4, 2);
    
    // Calculate start and end dates based on second date
    $start_date = new DateTime("$year1-$month1-01");
    $end_date = clone $start_date;
    $end_date->modify("$date2 days")->modify('-1 day');
    
    // Modify start date to last day of previous month
    $start_date->modify('first day of previous month')->modify('last day of previous month');
    $start_date->modify('+' . ($end_date->format('d') + 1) . ' days');
    return ['start'=>$start_date, 'end'=>$end_date];
}
?>

<?php 
function createDateRange($date1, $date2) {
    if (!$date1 || !$date2) return '';

    $aggregationPeriod = _getAggregationPeriod($date1, $date2);
    
    // Format and return date range
    $formatted_start_date = $aggregationPeriod['start']->format('Y/m/d');
    $formatted_end_date = $aggregationPeriod['end']->format('Y/m/d');
    return "$formatted_start_date-$formatted_end_date";
}
?>

<?php 
function getConfirmDate($date1, $date2) {
    if (!$date1 || !$date2) return '';

    $aggregationPeriod = _getAggregationPeriod($date1, $date2);
    
    $formatted_start_date = $aggregationPeriod['start']->format('Ymd');
    $formatted_end_date = $aggregationPeriod['end']->format('Ymd');
    return [intval($formatted_start_date), intval($formatted_end_date)];
}
?>
