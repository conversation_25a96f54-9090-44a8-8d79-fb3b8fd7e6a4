<style>
    .talkappi-dropdown-options {
        max-height: 180px;
    }
    .basic-label {
        min-width: 120px;
    }
</style>

<nav class="top-nav">
    <ul class="">
        <li><a href="/admin/payments"><?php echo __('admin.payment.common.label.list') ?></a></li>
        <li class="active"><a href="#"><?php echo __('admin.payment.common.label.detail') ?></a></li>
    </ul>
</nav>

<input type="hidden" name="act" id="act" value="" />
<input type="hidden" name="client_id" value='<?php echo $client_id?>'>

<div class="content-container white border">
    <div class="section-container">
        <div class="setting-header"><?php echo __('admin.payment.label.basic_information') ?></div>
        <!-- 取引先ID -->
        <div class="lines-container">
            <div class="basic-label"><?php echo __('admin.payment.label.client_id') ?></div>
            <input type="text" class="code-readonly" value="<?php echo $post['client_id'] ?>" placeholder="<?php echo __('admin.payment.lable.auto_generate') ?>"  readonly class="text-input-longer">
        </div>
        <!-- 取引先名 -->
        <div class="lines-container">
            <div class="basic-label"><?php echo __('admin.payment.label.name') ?></div>
            <input type="text" name="name" value="<?php echo htmlspecialchars($post['name']) ?>" class="text-input-longer">
        </div>
        <!-- 郵便番号 -->
        <div class="lines-container">
            <div class="basic-label"><?php echo __('admin.payment.label.postal_code') ?></div>
            <input type="text" name="postal_code" value="<?php echo $post['postal_code'] ?>" class="text-input-longer" placeholder="***-****">
        </div>
        <!-- 住所 -->
        <div class="lines-container">
            <div class="basic-label"><?php echo __('admin.payment.label.address') ?></div>
            <input type="text" name="address" value="<?php echo htmlspecialchars($post['address']) ?>" class="text-input-longer">
        </div>
        <!-- 電話番号 -->
        <div class="lines-container">
            <div class="basic-label"><?php echo __('admin.payment.label.tel') ?></div>
            <input type="text" name="tel" value="<?php echo $post['tel'] ?>" class="text-input-longer" placeholder="01-2345-6789">
        </div>
        <!-- 担当者 -->
        <div class="lines-container">
            <div class="basic-label"><?php echo __('admin.payment.lable.person_in_charge') ?></div>
            <input type="text" name="person_in_charge" value="<?php echo htmlspecialchars($post['person_in_charge'])?>" class="text-input-longer">
        </div>
        <!-- 精算担当者 -->
        <div class="lines-container">
            <div class="basic-label"><?php echo __('admin.payment.lable.accounting_person_in_charge') ?></div>
            <input type="text" name="accounting_person_name" value="<?php echo htmlspecialchars(json_decode($post['accounting_person'], true)['name']) ?>" class="text-input-longer">
        </div>
        <!-- 精算担当メールアドレス -->
        <div class="lines-container">
        <div class="basic-label"><?php echo __('admin.payment.lable.accounting_person_in_charge') ?><br><?php echo __('index.mail') ?></div>
        <input type="text" name="accounting_person_email" value="<?php echo htmlspecialchars(json_decode($post['accounting_person'], true)['email']) ?>" class="text-input-longer">
        </div>
        <!-- 取引施設 -->
        <div class="lines-container js-select-user-container" style="display:flex;">
            <div class="basic-label"><?php echo __('admin.payment.label.bots_name') ?></div>
            <div class="js-add-user-container" style="display:flex;flex-direction:column;">
                <ul class="btn round light-blue pointer js-selected-users" style="display:flex;flex-wrap:wrap;height:auto;margin-bottom:8px;list-style:none;display:none;"></ul>
                <div class="btn round light-blue pointer"><img src="./../assets/admin/css/img/icon-add.svg"><?php echo __('admin.payment.lable.add_bots') ?></div>
            </div>
            <input type="hidden" name="bot" value='<?php echo $post['bot']?>'>
            <input type="hidden" name="bots" value='<?php echo json_encode($post['bots'], JSON_UNESCAPED_UNICODE|JSON_HEX_APOS) ?>'>
        </div>
    </div>
    <div class="section-container">
        <div class="setting-header"><?php echo __('admin.payment.label.dealings_information') ?></div>
        <div class="lines-container">
            <div class="basic-label"><?php echo __('admin.payment.label.account_type') ?></div>
            <?php 
            $account_type_list_after = [];
            foreach ($account_type_list as $key => $value) {
                $account_type_list_after[$key] = __('admin.payment.label.account_type_' . $key);
            }
            ?>
            <div class="talkappi-pulldown" style="width:300px;" data-name="account_type" data-value="<?php echo $post['account_type'] ?>" data-value="01" data-source='<?php echo json_encode($account_type_list_after, JSON_UNESCAPED_UNICODE|JSON_HEX_APOS) ?>'></div>
        </div>
        <div class="lines-container">
            <div class="basic-label"><?php echo __('admin.payment.label.account_number') ?></div>
            <input type="text" name="account_number" value="<?php echo $post['account_number'] ?>" placeholder="****-***-*******" class="text-input-longer">
        </div>
        <?php $transfer_fee_detail = json_decode($post['transfer_fee'], true) ?>
        <div class="lines-container">
            <div class="basic-label"><?php echo __('admin.pay.transfer.fee') ?><br>(<?php echo __('admin.pay.transfer.fee.less_than_three') ?>)</div>
            <input type="text" name="transfer_fee_less_than_three" value="<?php if(isset($transfer_fee_detail["less_than_three"])) echo htmlspecialchars($transfer_fee_detail["less_than_three"]) ?>" class="text-input-longer">
        </div>
        <div class="lines-container">
            <div class="basic-label"><?php echo __('admin.pay.transfer.fee') ?><br>(<?php echo __('admin.pay.transfer.fee.more_than_three') ?>)</div>
            <input type="text" name="transfer_fee_more_than_three" value="<?php if(isset($transfer_fee_detail["more_than_three"])) echo htmlspecialchars($transfer_fee_detail["more_than_three"]) ?>" class="text-input-longer">
        </div>
        <div class="lines-container">
            <div class="basic-label"><?php echo __('admin.payment.label.contract_date') ?></div>
            <input type="text" class="talkappi-datepicker js-date" name="contract_date" value="<?php echo $post['contract_date'] ?>" />
        </div>
        <div class="lines-container">
            <div class="basic-label">
                <?php echo __('admin.payment.label.acti_commision') ?>
                <span class="icon-detail" style="margin: 0 0 0 0.5rem;" title="Visa, Mastercard"></span>
            </div>
            <input type="text" name="acti_commision" value="<?php echo $post['acti_commision'] ?>" class="text-input-longer">
        </div>
        <div class="lines-container">
            <div class="basic-label">
                <?php echo __('admin.payment.label.acti_commision2') ?>
                <span class="icon-detail" style="margin: 0 0 0 0.5rem;" title="<?php echo __('admin.payment.label.acti_commision2.tooltip') ?>"></span>
            </div>
            <input type="text" name="acti_commision2" value="<?php echo $post['acti_commision2'] ?>" class="text-input-longer">
        </div>

        <!-- 支払ターム設定 -->
        <div class="lines-container">
            <div class="basic-label"><?php echo __('admin.payment.label.term_information'); ?></div>
            <div class="talkappi-radio" data-name="payment_term" data-value='<?php echo count($post['payment_term']) ?>' data-source='{"1":"<?php echo __('admin.payment.label.term_1time'); ?>", "2":"<?php echo __('admin.payment.label.term_2time'); ?>"}'></div>
        </div>

        <?php for ($x = 0; $x < 2; $x++) { ?>
            <?php 
                $payment_term = (count($post['payment_term']) > $x) ? $post['payment_term'][$x] : null;
            ?>
            <?php if($payment_term != null) { ?>
                <div class="<?php echo __('payment_term_') . ($x+1) ?>">
                <div class="lines-container">
                    <div class="basic-label"><?php echo __('admin.payment.lable.handling.period'). ($x+1) ?></div>
                    <div class="talkappi-pulldown" style="width:120px;" data-name="from_day_<?php echo $x ?>" data-value="<?php echo $payment_term['from_day'] ?>" data-source='<?php echo json_encode($dateArray); ?>'></div>
                    <p style="margin: 0 12px;">〜</p>
                    <div class="talkappi-pulldown" style="width:120px;" data-name="to_day_<?php echo $x ?>" data-value="<?php echo $payment_term['to_day'] ?>" data-source='<?php echo json_encode($dateArray); ?>'></div>
                </div>
                <div class="lines-container">
                    <div class="basic-label"><?php echo __('admin.payment.lable.due_date'). ($x+1) ?></div>
                    <div class="talkappi-pulldown" style="width:120px;margin-right:12px;" data-name="due_month" data-value="1" data-source='{"1":"<?php echo __('admin.payment.common.label.payment_month_1') ?>"}'></div>
                    <div class="talkappi-pulldown" style="width:120px;" data-name="due_day_<?php echo $x ?>" data-value="<?php echo $payment_term['due_day'] ?>" data-source='<?php echo json_encode($dateArray); ?>'></div>
                </div>
                <div class="lines-container">
                    <div class="basic-label"><?php echo __('admin.payment.label.payment_date'). ($x+1) ?></div>
                    <div class="talkappi-pulldown" style="width:120px;margin-right:12px;" data-name="payment_month_<?php echo $x ?>" data-value='<?php echo $payment_term['payment_date']['month'] ?>' data-source='{"1":"<?php echo __('admin.payment.common.label.payment_month_1') ?>", "2":"<?php echo __('admin.payment.common.label.payment_month_2') ?>", "3":"<?php echo __('admin.payment.common.label.payment_month_3') ?>"}'></div>
                    <div class="talkappi-pulldown js-payment-date" style="width:120px;" data-name="payment_day_<?php echo $x ?>" data-value="<?php echo $payment_term['payment_date']['day'] ?>" data-source='<?php echo json_encode($dateArray); ?>'></div>
                </div>
            <?php } else { ?>
                <div class="<?php echo __('payment_term_') . ($x+1) ?>" style="display:none;">
                <div class="lines-container">
                    <div class="basic-label"><?php echo __('admin.payment.lable.handling.period'). ($x+1) ?></div>
                    <div class="talkappi-pulldown" style="width:120px;" data-name="from_day_<?php echo $x ?>" data-value="" data-source='<?php echo json_encode($dateArray); ?>'></div>
                    <p style="margin: 0 12px;">〜</p>
                    <div class="talkappi-pulldown" style="width:120px;" data-name="to_day_<?php echo $x ?>" data-value="" data-source='<?php echo json_encode($dateArray); ?>'></div>
                </div>
                <div class="lines-container">
                    <div class="basic-label"><?php echo __('admin.payment.lable.due_date'). ($x+1) ?></div>
                    <div class="talkappi-pulldown" style="width:120px;margin-right:12px;" data-name="due_month" data-value="" data-source='{"1":"<?php echo __('admin.payment.common.label.payment_month_1') ?>"}'></div>
                    <div class="talkappi-pulldown" style="width:120px;" data-name="due_day_<?php echo $x ?>" data-value="" data-source='<?php echo json_encode($dateArray); ?>'></div>
                </div>
                <div class="lines-container">
                    <div class="basic-label"><?php echo __('admin.payment.label.payment_date'). ($x+1) ?></div>
                    <div class="talkappi-pulldown" style="width:120px;margin-right:12px;" data-name="payment_month_<?php echo $x ?>" data-value='' data-source='{"1":"<?php echo __('admin.payment.common.label.payment_month_1') ?>", "2":"<?php echo __('admin.payment.common.label.payment_month_2') ?>", "3":"<?php echo __('admin.payment.common.label.payment_month_3') ?>"}'></div>
                    <div class="talkappi-pulldown js-payment-date" style="width:120px;" data-name="payment_day_<?php echo $x ?>" data-value="" data-source='<?php echo json_encode($dateArray); ?>'></div>
                </div>
            <?php } ?>
            </div>
        <?php } ?>
    </div>

    <div class="section-container">
        <div class="actions-container" style="margin: 60px 0 0 140px">
            <div class="btn-larger btn-blue x-first js-action-save"><?php echo __('admin.common.button.save'); ?></div>
            <div class="btn-larger btn-white js-action-back"><?php echo __('admin.common.button.return_to_list'); ?></div>
			<?php if($post['client_id'] !== "") {?>
				<div class="btn-larger btn-red-border js-action-delete">
					<span class="icon-delete"></span>
				</div>
			<?php }?>
        </div>
    </div>
</div>
<script>
    const _bot_array = <?php echo json_encode($botsArray, JSON_UNESCAPED_UNICODE); ?>;
</script>