<style>
    div.dataTables_filter label {
    float: left;
    margin-left: -10px;
    }
    .col-md-6 {
    width: auto;
    }
    .content-title {
        margin-top: -20px;
        margin-left: -20px;
        display:flex;
        align-items: center;
    }
    .custom-data-table-header {
        display: flex;
        right: 20px;
        align-items: center;
    }
    .talkappi-datepicker-range-container {
        margin:0;
    }
</style>

<input type="hidden" name="client_id" value=<?php echo $client_id; ?> />
<input type="hidden" name="start_date" value=<?php echo $start_date; ?> />
<input type="hidden" name="end_date" value=<?php echo $end_date; ?> />
<input type="hidden" name="transaction_type" value=<?php echo $post['transaction_type']; ?> />
<input type="hidden" name="result_cd" value=<?php echo $post['result_cd']; ?> />

<div class="content-container content-title">
    <div><?php echo __('admin.waiting.fasilities.lists') ?></div>
    <div>
        <select name="client" class="form-control js-client js-search" style='width:192px;margin-left:8px;' <?php if (count($client) === 1) echo 'disabled="disabled"' ?>>
            <?php if (count($client) > 1) { ?>
                <option value='0'>すべての施設</option>
            <?php }  ?>
            <?php foreach ($client as $val) { ?>
            <option value="<?php echo $val['client_id'] ?>" <?php if ($val['client_id'] === $client_id) echo 'selected="selected"' ?>><?php echo $val['name'] ?></option>
            <?php } ?>
        </select>
    </div>
</div>
<div class="content-container content-title" style="margin-top: -10px;">
    <div><?php echo __('admin.pay.result.cd') ?></div>
    <div>
        <select name="transaction_type" class="form-control js-transaction-type js-search" style='width:192px;margin-left:8px;' ?>
            <?php $all_transaction_type = array(''=>"すべての取引状態") + $_codes['47'];?>
            <?php foreach ($all_transaction_type as $key => $val) { ?>
            <option value="<?php echo $key ?>" <?php if ($post['transaction_type'] === $key) echo 'selected="selected"' ?>><?php echo $val ?></option>
            <?php } ?>
        </select>
    </div>
    <div style="margin-left: 12px;"><?php echo __('admin.pay.transaction.result') ?></div>
    <div>
        <select name="result_cd" class="form-control js-result-cd js-search" style='width:192px;margin-left:8px;' ?>
            <?php $all_result_cd = array(''=>"すべての取引結果") + $_codes['48'];?>
            <?php foreach ($all_result_cd as $key => $val) { ?>
            <option value="<?php echo $key ?>" <?php if ($post['result_cd'] === $key) echo 'selected="selected"' ?>><?php echo $val ?></option>
            <?php } ?>
        </select>
    </div>
</div>

<div class="content-container white" style="position:relative;">
    <table class="table table-striped table-bordered table-hover js-data-table" id="data-table" style="height: 100%">
        <div class="custom-data-table-header" style="top: 16px; display: flex">
            <div style="margin:0 8px;"><?php echo __('admin.pay.charge.fix.date') ?></div>
            <div class="talkappi-datepicker-range" data-name='date' data-value=<?php echo json_encode(["startDate" => "$start_date","endDate" => "$end_date"]) ?>></div>
            <div class="action-button section btn-white exportCsv" data-tableid="data-table" data-title="<?php echo __('admin.pay.transaction.detail') ?>"><span class="icon-export"></span><?php echo __('admin.common.button.csv_export') ?></div>
        </div>
        <thead>
            <tr>
                <th><?php echo __('admin.pay.client.id') ?></th>
                <th><?php echo __('admin.pay.client.name') ?></th>
                <th><?php echo __('admin.pay.service.type') ?></th>
                <th style="width: 100px;"><?php echo __('admin.pay.service.name') ?></th>
                <th><?php echo __('admin.pay.link.id') ?></th>
                <th><?php echo __('admin.pay.result.cd') ?></th>
                <th><?php echo __('admin.pay.transaction.time') ?></th>
                <th><?php echo __('admin.pay.payment.type') ?></th>
                <th><?php echo __('admin.pay.payment.type.detail') ?></th>
                <th><?php echo __('admin.pay.payment.amount') ?></th>
                <th><?php echo __('admin.pay.payment.commision') ?></th>
                <th><?php echo __('admin.pay.transaction.fee') ?></th>
                <th><?php echo __('admin.pay.transaction.result') ?></th>
                <th style="width: 200px;"><?php echo __('admin.pay.transaction.error') ?></th>
            </tr>
        </thead>
        <tbody>
            <?php
            foreach ($items as $item) {
            ?>
            <?php 
                $transaction_type_style = '';
                if ($item['transaction_type'] == '02') {
                    $transaction_type_style = 'color: #FF9551';
                } else if ($item['transaction_type'] == '03') {
                    $transaction_type_style = 'color: #E53361';
                }
                $result_cd_style = '';
                if ($item['result_cd'] == '02') {
                    $result_cd_style = 'color: #E53361';
                }
            ?>
            <tr class="gradeX odd js-reception-table" role="row">
                <td>
                    <?php echo trim($item['order_id'],'"') ?>
                </td>
                <td style="width: 240px; word-break: keep-all;">
                    <?php echo $item['bot_name'] ?>
                </td>
                <td>
                    <?php echo $_codes['53'][$item['service_type']] ?>
                </td>
                <td style="word-break: keep-all;">
                    <?php echo $item['service_name'] ?>
                </td>
                <!-- 処理ID -->
                <?php  if ($item['result_cd'] == '01') { ?>
                    <td style="text-decoration: underline;text-align: end;">
                        <a href= <?php echo $admin_url . "admininquiry/inquiryresultdetail?id=" . $item['link_id'] ?>&facility_cd=<?php echo $item['facility_cd'] ?> style="color: #000000;"><?php echo $item['link_id']; ?></ahref=style=>
                    </td>
                <?php } else {?>
                    <td>
                        <?php echo $item['link_id'] ?>
                    </td>
                <?php } ?>
                <td style="text-align: center; <?php echo $transaction_type_style ?>">
                    <?php echo $_codes['47'][$item['transaction_type']] ?>
                </td>
                <td style="white-space: nowrap;">
                    <?php 
                        if ($item['transaction_type'] == '00') {
                            if ($item['charge_date']) echo date_format(date_create($item['charge_date']), 'Y/m/d H:i:s');
                        } else if ($item['transaction_type'] == '01') {
                            if ($item['charge_fix_date']) echo date_format(date_create($item['charge_fix_date']), 'Y/m/d H:i:s');
                        } else if ($item['transaction_type'] == '02') {
                            if ($item['change_date']) echo date_format(date_create($item['change_date']), 'Y/m/d H:i:s');
                        } else if ($item['transaction_type'] == '03') {
                            if ($item['cancel_date']) echo date_format(date_create($item['cancel_date']), 'Y/m/d H:i:s');
                        } 
                    ?>
                </td>
                <td>
                    <?php echo $_codes['52'][$item['pay_type_cd']]?>
                </td>
                <td>
                    <?php echo $_codes['54'][$item['pay_type_detail']] ?: '-' ?>
                </td>
                <td style="text-align: end; white-space: nowrap; <?php echo $transaction_type_style ?>">
                    <?php 
                        if ($item['transaction_type'] == '03') {
                            echo '-￥'.number_format(($item['amount']));
                        }
                        else {
                            echo '￥'.number_format(($item['amount']));
                        }
                    ?>
                </td>
                <td style="text-align: end;">
                    <?php echo ($item['acti_commision']). '%' ?>
                </td>
                <td style="text-align: end;">
                    <?php if($item['bill_target']=="1" && $item['result_cd'] != '02') echo '¥15' ?>
                </td>
                <td style="text-align: center; <?php echo $result_cd_style ?>">
                    <?php echo $_codes['48'][$item['result_cd']] ?>
                </td>
                <td>
                    <?php 
                        if ($item['result_cd'] != '01') {
                            echo $item['exec_msg_ja'] . $item['alter_msg_ja'] . $item['3d_exec_msg_ja'];
                        }
                    ?>
                </td>
            </tr>
            <?php } ?>
        </tbody>
    </table>
</div>