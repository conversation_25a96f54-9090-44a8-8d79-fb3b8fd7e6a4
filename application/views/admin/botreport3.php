<style type='text/css'>
	.ui-datepicker-calendar,.ui-datepicker-month { display: none; }
	.portlet-body { 
		padding: 10px !important;
	}
</style>
	<div class="page-head">
		<div class="page-title">
			<h1>利用状況レポート<small></small></h1>
		</div>
	</div>
	<!-- BEGIN PAGE CONTENT-->
	<div class="row">
		<div class="col-md-12">
			<div id="page-wrapper">
				<div class="portlet light">
					<?php echo $reportmenu ?>
					<div class="portlet box" style="margin: 0px; padding: 0px;">
						<div class="portlet-body">
							<div class="form-body">
								<div class="form-group">
									<label class="control-label col-md-1"></label>
									<div class="flex">
										<button type="button" onclick="location.href='/admin/report3'" class="btn white link-animate">旧画面へ</button>
									</div>
								</div>		
								<div class="form-group">
									<label class="control-label col-md-1"><?php echo __('admin.common.label.period') ?></label>
									<div class="flex">
										<input name="start_date" id="start_date" value="<?php echo $start_date !== "" ? date('Y-m', strtotime($start_date)) : ""; ?>" style="float:left;" class="talkappi-datepicker" size="16" data-date-format="yyyy-mm" type="text"/>
										<input name="end_date" id="end_date" value="<?php echo $end_date !== "" ? date('Y-m', strtotime($end_date)) : ""; ?>" style="float:left; margin-left:10px;" class="talkappi-datepicker" size="16" data-date-format="yyyy-mm" type="text"/>
										<button type="button" id="searchButton" class="btn yellow" style="margin-left:20px;">
											<i class="fa fa-search mr10"></i><?php echo __('admin.common.button.search') ?>
										</button>
										<button type="button" data-tableId="sample_3" data-title="月別統計" class="btn green exportCsv" style="margin-left:20px;">
											<?php echo __('admin.common.button.csv_export') ?>
										</button>
									</div>
								</div>
							</div>								
						</div>							
					</div>
					<div class="portlet-body">
						<table class="table table-striped table-bordered table-hover" id="sample_3">
							<thead>
								<tr>
									<th><?php echo __('admin.common.label.month') ?></th>
									<th><?php echo __('admin.botreport3.label.new_user_not_asked') ?></th>
									<th><?php echo __('admin.botreport3.label.number_of_not_asked') ?></th>
									<th><?php echo __('admin.botreport3.label.number_of_repeat_user') ?></th>
									<th><?php echo __('admin.botreport3.label.new_conversation_number') ?></th>
									<th><?php echo __('admin.botreport3.label.total_conversation_number') ?></th>
								</tr>
							</thead>
							<tbody>
								<?php foreach ($report_data as $row): ?>
								<tr class="gradeX odd" role="row">
									<td class="sorting_1" style="text-align: center">
										<?php echo date('Y-m', strtotime($row['report_date'])) ?>
									</td>
									<td style="text-align: right">
										<?php echo (int)$row['new_member'] ?>
									</td>
									<td style="text-align: right">
										<?php echo (int)$row['no_ask_member'] ?>
									</td>
									<td style="text-align: right">
										<?php echo (int)$row['repeat_member'] ?>
									</td>
									<td style="text-align: right">
										<?php echo (int)$row['new_conversation'] ?>
									</td>
									<td style="text-align: right">
										<?php echo (int)$row['member_conversation'] ?>
									</td>
								</tr>
								<?php endforeach; ?>
							</tbody>
						</table>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
