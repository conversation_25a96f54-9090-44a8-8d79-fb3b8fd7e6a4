			<!-- BEGIN PAGE HEADER-->
			<!-- BEGIN PAGE HEAD -->
			<div class="page-head">
				<!-- BEGIN PAGE TITLE -->
				<div class="page-title">
				<?php 
					$next_year = $year;
					$next_month = $month+1;
					$last_year = $year;
					$last_month = $month-1;
					if ($month==12) {
						$next_year = $year + 1;
						$next_month = "1";
					}
					else if ($month==1) {
						$last_year = $year - 1;
						$last_month = "12";
					}
				?>
					<h1><?php echo($stock->stock_name) ?> <small style="margin-left: 40px;">
					<a href="/admin/stockday?id=<?php echo($stock_id)?>&&year=<?php echo($last_year)?>&&month=<?php echo($last_month)?>" style="margin-right: 20px;">前月</a>
					<?php echo($year) ?>年<?php echo($month) ?>月
					<a href="/admin/stockday?id=<?php echo($stock_id)?>&&year=<?php echo($next_year)?>&&month=<?php echo($next_month)?>" style="margin-left: 20px;">次月</a>
					<a href="/admin/stocklist" style="margin-left: 40px;">一覧に戻る</a>
					</small></h1>
				</div>
			</div>
			<!-- END PAGE HEAD -->
			<!-- END PAGE HEADER-->
			<!-- BEGIN PAGE CONTENT-->
			<div class="row">
				<div class="col-md-12">
						<input type="hidden" name="stock_id" id="stock_id" value="<?php echo($stock_id) ?>">
						<input type="hidden" name="year" id="year" value="<?php echo($year) ?>">
						<input type="hidden" name="month" id="month" value="<?php echo($month) ?>">
						<input type="hidden" name="day" id="day" value="">
						<input type="hidden" name="operation" id="operation" value="">
						<div class="form-body">
							<div class="portlet box">
							<div class="portlet-body">
							<table class="table table-striped table-bordered table-hover" id="">
								<thead>
									<tr>
									<th>曜日</th>
									<th>操作</th>
									<th>予約状況</th>
									</tr>
								</thead>	
								<tbody>				
								<?php
									$last_day = date('t', mktime(0, 0, 0, $month + 1, 0, $year));
									// 月末日までループ
									for ($i = 1; $i < $last_day + 1; $i++) {
										$day = str_pad($i, 2, "0", STR_PAD_LEFT);
										$desc = "";
										$stocknum = 0;
										if (array_key_exists($day, $stocks)) {
											$desc = $stocks[$day]->description;
											$stocknum = $stocks[$day]->stock;
										}
										// 曜日を取得
										$week = date('N', mktime(0, 0, 0, $month, $i, $year));
										
										if ($week == 7) $dayweek = $i . "(日)";
										if ($week == 1) $dayweek = $i . "(月)";
										if ($week == 2) $dayweek = $i . "(火)";
										if ($week == 3) $dayweek = $i . "(水)";
										if ($week == 4) $dayweek = $i . "(木)";
										if ($week == 5) $dayweek = $i . "(金)";
										if ($week == 6) $dayweek = $i . "(土)";
										
										if ($week == 7 || $week == 6) {
											echo('<tr class="warning">');
										}
										else {
											echo('<tr>');
										}
										echo("<td width=50>$dayweek</td>");
										$stock_date = date('Y-m-d', mktime(0, 0, 0, $month, $i, $year));
										if ($stock->stock_type_cd == "01") {
											if ($stock_date <= date('Y-m-d')) {
												if ($stocknum== 0) {
													echo('<td width=100>ー</td>');
												}
												else if($stocknum== 1) {
													echo('<td width=100>〇</td>');
												}
											}
											else {
												if ($stocknum== 0) {
													echo('<td width=100>ー<button type="button" class="btn green-meadow action" style="margin-left:10px;" operation="1" day="' . $i . '">〇</button></td>');
												}
												else if($stocknum== 1) {
													echo('<td width=100>〇<button type="button" class="btn gray action" style="margin-left:10px;" operation="0" day="' . $i . '">ー</button></td>');
												}
											}
										}
										else {
											echo('<td width=100><button type="button" class="btn gray action" operation="" day="' . $i . '">設定</button></td>');
											//echo('<td width=100><button type="button" class="btn gray action" onclick="top.location=\'/admin/stocktime?id=' . $stock->stock_id . '&data=' . $stock_date . '">設定</button></td></tr>');
										}
										echo("<td>" .  $desc . "</td></tr>");
									}
								?>
								</tbody>
							</table>																
							</div>
							</div>
						</div>
				</div>
			</div>

