<?php if ($chats != NULL) foreach($chats as $chat) {
	$send_type = 'in';
	$name = '';
	$message = $chat['member_msg'];
	$ex = null;
	if ($chat['member_msg'] == '') {
		$send_type = 'out';
		if (strpos($chat['intent_cd'], "input.chat")===0) {
			$name = '';
		}
		else {
			$name = '';
		}
		$message = $chat['bot_msg'];
		if ($chat['score'] == 0) {
			$message = str_replace(array("\r\n", "\r", "\n"),"\\n", $message);
			$ex = json_decode($message);
		}
	}
?>
	<li class="<?php echo($send_type)?>">
		<div class="message">
			<span class="arrow"> </span>
			<a href="javascript:;" class="name"> <?php echo($name)?> </a>
			<span class="datetime">  <?php echo(substr($chat['log_time'],5,11))?> </span>

			<?php
			if ($ex == null) {
				$message = str_replace("\\n","<br/>", $message);
				echo('<span class="body">');
				$matchs = null;
				preg_match_all('/\b(?:(?:https?|ftp):\/\/|www\.)[-a-z0-9+&@#\/%?=~_|!:,.;]*[-a-z0-9+&@#\/%=~_|]/i', $message, $matchs);
				$pos = 0;
				foreach($matchs[0] as $match) {
					$pos = strpos($message, $match, $pos);
					if ($pos >= 0) {
						$message = substr($message, 0, $pos) . '<a href="' . $match . '" target="blank"><p>' . $match . '</p></a>' .
								substr($message, $pos + strlen($match));
								$pos++;
					}
				}
				$message = str_replace(array("\r\n", "\r", "\n", "\\n"),"<br/>", $message);
				//$message = nl2br($message);
				
				if ($chat['bot_msg_t'] != NULL && $chat['bot_msg_t'] != "") {
					$message = $chat['bot_msg_t'] . "\n※原文：". $message;
				}
				if ($chat['member_msg_t'] != NULL && $chat['member_msg_t'] != "") {
					$message = $chat['member_msg_t'] . "\n※原文：". $message;
				}
				echo($message);
			}
			else {
				if ($ex->type == 'card') {
					echo('<div class="xscroll-wrapper">');
					foreach ($ex->items as $item) {
						$style="";
						$style1="";
						if ($item->image == null || $item->image =='') $style ='style="height:0px;"';
						if ($item->content == null || $item->content =='') $style1 ='style="height:0px;"';
						echo('<div class="card">');
						echo('<a href="' . $item->url . '" target="blank"><img class="image" ' . $style . ' alt="" src="' . $item->image . '" /></a>');
						echo('<div class="item-caption">' . $item->title . '</div>');
						echo('<div class="item-description" ' . $style1 . ' >' . $item->content . '</div>');
						echo('<div class="item-button">');
						foreach ($item->buttons as $button) {
							echo('<a class="button" href="' . $button->url . '" target="blank">' . $button->title . '</a>');
						}
						echo('</div>');
						echo('</div>');
					}
					echo('</div>');
				}
				else if ($ex->type == 'image'){
					echo('<span class="body">');
					if ($ex->url == '')
						echo('<img class="talkappi-img-circle" alt="" src="' . $ex->image . '" />');
					else
						echo('<a href="' . $ex->url . '" target="blank"><img class="talkappi-img-circle" alt="" src="' . $ex->image . '" /></a>');
					echo('</span>');
				}
				else {
					echo('<span class="body" style="text-align:left;line-height:32px;">');
					if ($ex->title!='') echo(nl2br($ex->title) . '<br/>');
					foreach ($ex->items as $item) {
						$item_type = "postback";
						if (isset($item->type)) {
							$item_type = $item->type;
						}
						if ($item->title!='') $item->title = str_replace("\\n","<br>", $item->title);
						echo('<a href="javascript:;" class="webchat-list" type="' . $item_type . '" title="' . $item->title . '" content="' . $item->content  . '">');
						if ($ex->type == 'list') {
							echo('<span class="badge badge-success" style="margin-right:10px;background-color:#e0e0e0;color:#333;font-size:20px;">' . $item->title . '</span></a>');
							echo('<br/>');
						}
						else {
							echo('<span class="badge badge-success" style="display:inline-block;margin-right:6px;background-color:#ffffff;color: #57606f;border: solid 1px #57606f;font-size:20px;white-space: pre;">' . $item->title . '</span></a>');
						}
					}
					echo('</span>');
				}
			}
			?>
		</div>
	</li>
<?php }?>
<span id="chat-last-id" style="display:none;"><?php echo($log_id)?></span>
