							<?php 
							if (substr($_action, 0, 4) == 'task') {
								$page_type = 'task';
							}
							else {
								$page_type = 'pushmsgtask';
							}
							?>
							<div class="top-nav">
							<ul>
								<li class="<?php if ($_action == $page_type) echo('active')?>">
									<a class="func-menu" href="/admin/<?php echo $page_type ?>?id=<?php echo($task_id)?>">
									タスク情報</a>
								</li>
								<li class="<?php if ($_action == $page_type . 'result') echo('active')?>">
									<a class="func-menu" href="/admin/<?php echo $page_type ?>result?id=<?php echo($task_id)?>">
									実行結果</a>
								</li>								
								<li class="<?php if ($_action == $page_type . 'log') echo('active')?>">
									<a class="func-menu" href="/admin/<?php echo $page_type ?>log?id=<?php echo($task_id)?>">
									実行ログ</a>
								</li>								
								<li class="">
									<a class="func-menu" href="/admin/<?php echo $page_type ?>s">
									一覧に戻る</a>
								</li>							
							</ul>
							</div>
