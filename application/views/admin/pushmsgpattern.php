
			<!-- BEGIN PAGE HEADER-->
			<!-- BEGIN PAGE HEAD -->
			<div class="page-head">
				<!-- BEGIN PAGE TITLE -->
				<div class="page-title">
					<h1>メッセージ送信<small></small></h1>
				</div>
				<!-- <PERSON>ND PAGE TITLE -->
			</div>
			<!-- END PAGE HEAD -->
			<!-- END PAGE HEADER-->
			<!-- BEGIN PAGE CONTENT-->
			<div class="row">
				<div class="col-md-12">
			        <!-- Page Content -->
			        <div id="page-wrapper">
			        <div class="portlet light">
						<div class="tabbable-line"> 
							<ul class="nav nav-tabs ">
								<li class="<?php if ($_action == 'pushmsg') echo('active'); ?>">
									<a href="/admin/pushmsg">
									プッシュ送信</a>
								</li>
							</ul>
						</div>
							<input type="hidden" name="service_id" id="service_id" value="" />
							<div class="form-body">
								<div class="row">
								<br/>
								</div>
								<div class="form-group">
									<label class="control-label col-md-2">パターン</label>
									<div class="col-md-5">
										<select class="form-control input-xlarge select2me" name="select_query" id="select_query" data-placeholder="Select...">
											<option value=""></option>
											<?php
												$i = 0;
												foreach($querys as $query) {
												echo('<option value="' . $query->query_cd . '">' . $query->query_name . '</option>');
												$i++;
											}?>
										</select>
									</div>
									<div class="col-md-4">
										<button type="submit" class="btn green" id="btn_select_query">
										&nbsp;&nbsp;選&nbsp;定&nbsp;&nbsp;</button>
									</div>											
								</div>																
								<div class="form-group">
									<label class="control-label col-md-2">メッセージ</label>
									<div class="col-md-5">
										<select class="form-control input-xlarge select2me" name="select_message" id="select_message" data-placeholder="Select...">
											<option value=""></option>
											<?php
												$i = 0;
												foreach($msgs as $msg) {
												echo('<option value="' . $msg->msg_cd . '">' . $msg->msg_name . '</option>');
												$i++;
											}?>													
										</select>
									</div>
									<div class="col-md-4">
										<button type="submit" class="btn green" id="btn_select_message">
										&nbsp;&nbsp;選&nbsp;定&nbsp;&nbsp;</button>
									</div>											
								</div>		

								<div class="form-group">
									<label class="control-label col-md-2">タイトル</label>
									<div class="col-md-8">
										<div class="input-icon right">
											<input name="item_name" type="text" class="form-control" placeholder="" 
												value="<?php if ($post != NULL) echo($post['title'])?>">
										</div>
									</div>
								</div>
								<div class="form-group">
									<label class="control-label col-md-2">内容</label>
									<div class="col-md-8">
										<div class="input-icon right">
											<textarea name="sell_point_line" class="form-control" maxlength="225" rows="2" placeholder=""><?php if ($post != NULL) echo($post['content'])?></textarea>
										</div>
									</div>
								</div>		
								<div class="form-group">
									<label class="control-label col-md-2">ボタン１</label>
									<div class="col-md-1">
										<input name="btn1_name" type="text" class="form-control" placeholder="" 
												value="<?php if ($post != NULL) echo($post['btn1_name'])?>">
									</div>
									<label class="control-label col-md-1">リンク</label>
									<div class="col-md-6">
										<input name="btn1_url" type="text" class="form-control" placeholder="" value="<?php if ($post != NULL) echo($post['btn1_url'])?>">
									</div>									
								</div>	
								<div class="form-group">
									<label class="control-label col-md-2">ボタン２</label>
									<div class="col-md-1">
										<input name="btn2_name" type="text" class="form-control" placeholder="" 
												value="<?php if ($post != NULL) echo($post['btn2_name'])?>">
									</div>
									<label class="control-label col-md-1">リンク</label>
									<div class="col-md-6">
										<input name="btn2_url" type="text" class="form-control" placeholder="" value="<?php if ($post != NULL) echo($post['btn2_url'])?>">
									</div>									
								</div>
								<div class="form-group">
									<label class="control-label col-md-2">ボタン３</label>
									<div class="col-md-1">
										<input name="btn3_name" type="text" class="form-control" placeholder="" 
												value="<?php if ($post != NULL) echo($post['btn3_name'])?>">
									</div>
									<label class="control-label col-md-1">リンク</label>
									<div class="col-md-6">
										<input name="btn3_url" type="text" class="form-control" placeholder="" value="<?php if ($post != NULL) echo($post['btn3_url'])?>">
									</div>									
								</div>	
								<div class="form-group last">
									<label class="control-label col-md-2">写真</label>
									<div class="col-md-10">
										<div class="fileinput fileinput-new" data-provides="fileinput">
											<div class="input-group input-large">
												<div class="form-control uneditable-input span3" data-trigger="fileinput">
													<i class="fa fa-file fileinput-exists"></i>&nbsp; <span class="fileinput-filename">
													</span>
												</div>
												<span class="input-group-addon btn default btn-file">
												<span class="fileinput-new">
												Select file </span>
												<span class="fileinput-exists">
												Change </span>
												<input type="file" name="image">
												</span>
												<a href="#" class="input-group-addon btn red fileinput-exists" data-dismiss="fileinput">
												Remove </a>
											</div>
										</div>
									</div>
								</div>									
								<div class="row">
									<div class="form-group">
										<label class="control-label col-md-2"></label>
										<div class="col-md-6">
											<button type="submit" class="btn blue" id="btn_new_message">
											<i class="fa fa-send"></i>&nbsp;&nbsp;送&nbsp;信&nbsp;&nbsp;</button>
										</div>
									</div>																	
								</div>	
																							
							</div>
					</div>
			        </div>
			        <!-- /#page-wrapper -->					
				</div>
			</div>
			<!-- END PAGE CONTENT-->