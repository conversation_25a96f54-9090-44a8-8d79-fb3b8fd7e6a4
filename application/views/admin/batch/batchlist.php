<script>
const _paging = <?php echo json_encode($paging) ?>;
</script>

<!-- CSS Styles for Batch-specific Elements -->
<style>
.js-data-table td, .js-data-table th {
    vertical-align: middle !important;
}

/* Batch Details Card Layout */
.child-row-content {
    padding: 20px;
    background-color: #f6f7f9;
    border-left: 3px solid #245BD6;
}

.batch-details-container {
    max-width: 100%;
}

.batch-details-row {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    margin-bottom: 16px;
}

.batch-details-row:last-child {
    margin-bottom: 0;
}

.batch-detail-card {
    flex: 1;
    min-width: 300px;
    max-width: calc(50% - 8px);
    background: #ffffff;
    border: 1px solid #e3e5e8;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.08);
    overflow: hidden;
}

.batch-detail-card .card-header {
    background: #b8bcc8;
    color: white;
    padding: 10px 14px;
    font-weight: 400;
    font-size: 13px;
    border-bottom: 1px solid #e3e5e8;
}

.batch-detail-card .card-header i {
    margin-right: 6px;
    opacity: 0.9;
}

.batch-detail-card .card-body {
    padding: 14px;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 6px 0;
    border-bottom: 1px solid #f1f3f4;
    gap: 10px;
}

.detail-item:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.detail-label {
    font-weight: 400;
    color: #6c757d;
    font-size: 12px;
    min-width: 120px;
    flex-shrink: 0;
}

.detail-value {
    color: #495057;
    font-size: 12px;
    text-align: right;
    flex-grow: 1;
    word-break: break-word;
}

/* Record Statistics */
.record-stats {
    display: flex;
    flex-direction: column;
    gap: 4px;
    text-align: right;
}

.stat-item {
    display: block;
    font-size: 11px;
}

.text-success {
    color: #6c757d !important;
    font-weight: 500;
}

.text-danger {
    color: #dc3545 !important;
    font-weight: 500;
}

/* Error Item Styling */
.error-item {
    background-color: #fff5f5;
    border-left: 3px solid #dc3545;
    padding-left: 12px;
    margin-left: -12px;
    border-radius: 4px;
}

.error-message {
    color: #dc3545;
    font-size: 11px;
}

.error-code {
    font-weight: bold;
    margin-right: 8px;
    color: #dc3545;
}

.error-text {
    display: block;
    margin-top: 4px;
    color: #dc3545;
}

/* Retry Count Styling */
.retry-count {
    font-weight: 500;
    color: #6c757d;
}

.max-retries {
    color: #adb5bd;
}

/* Responsive Design */
@media (max-width: 768px) {
    .batch-details-row {
        flex-direction: column;
    }

    .batch-detail-card {
        max-width: 100%;
        min-width: 100%;
    }

    .detail-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }

    .detail-label {
        min-width: auto;
    }

    .detail-value {
        text-align: left;
    }

    .record-stats {
        text-align: left;
    }
}

@media (max-width: 1200px) {
    .batch-detail-card {
        min-width: 280px;
    }
}
</style>

<!-- Filter Section -->
<div class="content-container light-gray">
        <div class="form-group">
            <label class="control-label col-md-1" style="width: 80px;"><?php echo __('admin.batch.label.execution_date_range') ?></label>
            <div class="col-md-4" style="width: 270px;">
                <input name="start_date" id="start_date" value="<?php echo HTML::chars($filters['start_date']); ?>"
                    style="float:left;height: 28px;" class="form-control form-control-inline input-small date-picker"
                    size="16" data-date-format="yyyy-mm-dd" type="text" />
                <input name="end_date" id="end_date" value="<?php echo HTML::chars($filters['end_date']); ?>"
                    style="height: 28px; float:left; margin-left:10px;"
                    class="form-control form-control-inline input-small date-picker" size="16" data-date-format="yyyy-mm-dd"
                    type="text" />
            </div>
            <div class="col-md-2">
                <select id="batch_type" name="batch_type" class="form-control">
                    <?php foreach ($batch_type_options as $value => $label): ?>
                        <option value="<?php echo HTML::chars($value); ?>" 
                                <?php echo ($filters['batch_type'] == $value) ? 'selected' : ''; ?>>
                            <?php echo HTML::chars($label); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-2">
                <select id="execution_status" name="execution_status" class="form-control">
                    <?php foreach ($execution_status_options as $value => $label): ?>
                        <option value="<?php echo HTML::chars($value); ?>" 
                                <?php echo ($filters['execution_status'] == $value) ? 'selected' : ''; ?>>
                            <?php echo HTML::chars($label); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
        </div>
        
        <div class="form-group">
        <label class="control-label col-md-1" style="width: 80px;">
            <?php echo __('admin.common.label.keyword') ?>
        </label>
            <div class="col-md-4">
                <input name="keyword" id="keyword" value="<?php echo HTML::chars($filters['keyword']); ?>"
                       class="form-control" type="text" placeholder="<?php echo __('admin.batch.label.keyword_placeholder') ?>" />
            </div>
            <div class="col-md-1 ml20">
                <span class="btn-smaller btn-yellow" id="searchButton"><i class="fa fa-search mr10"></i>
                    <?php echo __('admin.common.button.search') ?>
                </span>
            </div>
        </div>
</div>

<!-- Manual Execution Section -->
<div class="content-container" style="padding-left: 0;">
    <div style="display: flex;justify-content: end;">
        <div>
            <div class="col-md-2" style="width: auto; min-width: 250px;">
                <select id="manual_batch_type" name="manual_batch_type" class="form-control" required style="width: 100%;">
                    <option value=""><?php echo __('admin.batch.label.batch_select') ?></option>
                    <?php foreach ($batch_type_options as $value => $label): ?>
                        <?php if (!empty($value)): ?>
                            <option value="<?php echo HTML::chars($value); ?>">
                                <?php echo HTML::chars($label); ?>
                            </option>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-1" style="width: auto;">
                <button type="button" class="btn-smaller btn-red" id="manualExecuteButton" style="white-space: nowrap; padding: 5px 12px;">
                    <i class="fa fa-play mr10"></i><?php echo __('admin.batch.button.run.manual') ?>
                </button>
            </div>
            
        </div>
    </div>
</div>


<!-- Data Table -->
<div class="content-container white border">
    <div class="portlet-body">
        <table class="table table-striped table-bordered table-hover js-data-table">
            <thead>
                <tr>
                    <th width="15%"><?php echo __('admin.batch.label.execution_date') ?></th>
                    <th width="20%"><?php echo __('admin.batch.label.batch_name') ?></th>
                    <th width="20%"><?php echo __('admin.batch.label.type') ?></th>
                    <th width="15%"><?php echo __('admin.batch.label.status') ?></th>
                    <th width="15%"><?php echo __('admin.batch.label.priority') ?></th>
                    <th width="15%"><?php echo __('admin.batch.label.details') ?></th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($executions as $execution): ?>
                    <tr class="execution-row" data-execution-id="<?php echo $execution['id']; ?>" data-execution-details="<?php echo HTML::chars(json_encode($execution)); ?>">
                        <td class="text-center"><?php echo HTML::chars($execution['execution_date']); ?></td>
                        <td><?php echo HTML::chars($execution['batch_name'] ?? '-'); ?></td>
                        <td>
                            <?php 
                            echo HTML::chars($execution['batch_type']);
                            ?>
                        </td>
                        <td class="text-center">
                            <span class="status-badge <?php echo $execution['status_info']['class']; ?>">
                                <?php echo HTML::chars($execution['status_info']['label']); ?>
                            </span>
                        </td>
                        <td class="text-center"><?php echo HTML::chars($execution['priority_label']); ?></td>
                        <td class="text-center">
                            <button class="btn btn-blue btn-info toggle-details" data-target="details-<?php echo $execution['id']; ?>">
                                <i class="fa fa-plus"></i>
                            </button>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        
    </div>
</div>



