<link rel="stylesheet" type="text/css" href="/assets/admin/css/newsletter.css"/>

<style>
    .txt_task_name {
        font-size: 14px;
        margin-bottom: 12px;
    }
    td .tag_inline {
        border-radius: 2px;
        background: #EBEDF2;
        margin: unset !important;
    }

    td>.wrapper_tags {
        display: flex;
        flex-wrap: wrap;
        gap: 3px;
    }

    .btn_log {
    }
</style>

<div class="content-container white">
    <div class="flex-x-between">
        <!-- talkappi hotel：GOTOキャンペーンの最新情報 -->
        <div class="txt_task_name">
            <?php 
                $task_name = $mail_task->mail_task_name;
                if ($task_name == NULL || $task_name == '') {
                    $task_name = '[件名]'.$mail_task->title;
                }
                echo $task_name; 
            ?>
        </div>
        <!-- <div id="log" class="btn_log">
            <div>実行ログ</div>
        </div> -->
        <button type="button" id="csvexport" class="btn-smaller btn-white" style="background-color: #EBEDF2; border: none; margin: 0;">
            <span class="icon-export"></span>
            <?php echo __('admin.common.button.csv_export'); ?>
        </button>
    </div>
    <table id="table0list" class=" table table-striped table-bordered table-hover js-data-table0 ">
        <!-- style="top: 50px;" -->
        <div class="table-header-pulldown custom-data-table-header">
            <div>
                <?php echo Form::select('status', $status_set, $filter_status, array('id'=>'filter_status','class'=>'form-control task_status',
                    'style' => 'background: #FFFFFF;width: 192px;'))
                ?>
            </div>
        </div>

        <thead>
            <tr>
                <th style="width: 200px;"><?php echo __('admin.send.label.date')?></th>
                <th style="width: 284px;"><?php echo __('admin.common.label.mail.address') ?></th>
                <th style="width: 180px;"><?php echo __('admin.common.label.name.human') ?></th>
                <th style="width: 278px;"><?php echo __('admin.send.addr.th.tag') ?></th>
                <th style="width: 120px;"><?php echo __('admin.push.result') ?></th>
            </tr>																
        </thead>

        <tbody>

            <?php
                foreach($results as $index=>$result) {
                    $status_setting = $status_settings[$result['send_status']];
                    $status_txt = $status_setting['txt'];
                    $mail_member_id = $result['mail_member_id'];
                    if ( $result['send_status'] == 2 && isset($negative_results[$mail_member_id]) ) {
                        $status_txt .= '('.$negative_results[$mail_member_id].')';
                        if ( $negative_results[$mail_member_id] == 'Bounce' ) {
                            //anchor
                            $action_html .= '<div class="operation" data-operation="try_resolve_bounce" data-email="'.$result['email'].'" style="color: #245BD6; cursor: pointer;">'.__('admin.newsletter.label.resolve_suppression').'</div>';
                        }
                    }
                    $css_txt_color = '';
                    if ( isset($status_setting['color']) ) { 
                        $css_txt_color = ' style="color:'.$status_setting['color'].';"';
                    }
                    $tags_html = '';
                    if ( isset($member_tag_mapping[$mail_member_id]) && count($member_tag_mapping[$mail_member_id]) > 0 ) {
                        // $tags_html = '';
                        foreach ( $member_tag_mapping[$mail_member_id] as $tag_name ) {
                            $tags_html .= ' <div class="btn light-gray tag_inline">'.$tag_name.'</div>';
                        }
                    }
                    echo('
            <tr class="gradeX odd" id='.$mail_member_id.'>
                <td style="width: 210px;">
                    <span>'.$result['send_time'].'</span>
                </td>
                <td style="width: 294px;">
                    <span>'.$result['email'].'</span>
                </td>
                <td style="width: 190px;">
                    <span>'.$result['first_name'].'</span>
                </td>
                <td style="width: 288px;">
                    <div class="wrapper_tags">'.$tags_html.'</div>
                </td>    
                <td style="width: 130px;">
                    <span'.$css_txt_color.'>'.$status_txt.'</span>'.$action_html.'   
                </td>           
            </tr>');                    
                }
            ?>

        </tbody>
    </table>


    
    <div style="width: fit-content;">
        <?php 
            if ($is_newsletter_project_enabled && isset($project_id)) {
                $href_mam = '/'.$_path.'/newslettermam?proj='.$project_id;
            } else {
                $href_mam = '/'.$_path.'/newslettermam';
            }
            $button_html = '<div id = "back2mam" class="action-button page btn-white" style="width: fit-content; margin:20px 0; ">戻る</div>';
            $mam_html = HTML::anchor($href_mam, $button_html);
            echo $mam_html;
        ?>  
    </div>


</div>
