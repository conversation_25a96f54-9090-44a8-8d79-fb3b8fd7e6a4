<link rel="stylesheet" type="text/css" href="/assets/admin/css/newsletter.css"/>
<style>
.content-container .content-wrapper {
  padding: 12px 6%;
  display:flex;
  flex-direction:column;
  gap:24px;
}

.icon-undo {
  height: 12px;
  width: 12px;
}

</style>


<div class="content-container white">
  <div class="content-wrapper">

    <!-- bot選択 一旦非表示　実装待ち-->
    <div style="display:flex;flex-direction:column;gap:6px;">
      <div><?php echo __('admin.newsletterimport.label.select_bot') ?></div>
      <div class="talkappi-pulldown js-bot" data-name="bot_id" data-value="" <?php echo HTML::attributes(['data-source'=>$bots]) ?>></div>
    </div>

    <!-- extend指定　実装待ち -->
    <div id="label-select" class="content-container light-gray hide" style="display:flex;flex-direction:column;gap:6px;">
      <div><?php echo __('admin.newsletterimport.label.select_label') ?></div>
      <div style="display:flex;align-items:center;">
        <label style="width:120px;margin-bottom:0;"><?php echo __('admin.common.label.name.human') ?></label>
        <div style="width:100%" class="talkappi-pulldown js-extend" data-name="extend"></div>
      </div>
    </div>

    <div style="display:flex;">
      <button class="btn-smaller page btn-blue js-previewimport" style="margin-left:0"><?php echo __('admin.newsletterimport.btn.preview') ?></button>
    </div>

    <div style="display:flex;flex-direction:column;gap:6px;">
      <div class="result_data_count">
        <?php echo __('admin.common.label.total') ?><span></span>
        <?php echo __('admin.newsletterimport.label.pcount') ?></div>
      <div>
        <table class="table table-striped table-bordered table-hover js-data-table">
          <thead>
            <tr>
              <th><?php echo __('admin.common.label.mail.address') ?></th>
              <th><?php echo __('admin.common.label.name.human') ?></th>
              <th><?php echo __('admin.newsletterimport.label.tag') ?></th>
              <th style="width:40px!important"><?php echo __('admin.newsletterimport.button.exclude') ?></th>
            </tr>
          </thead>
          <tbody></tbody>
        </table>
      </div>
    </div>

    <div class="flex">
      <button class="action-button page btn-blue js-applyimport" style="margin-left:0"><?php echo __('admin.newsletterimport.btn.apply') ?></button>
      <a href="/admin/newsletteraddressmam<?php echo isset($project_id) && $project_id ? '?proj=' . $project_id : '' ?>" class="action-button page btn-white js-action-back"><?php echo __('admin.common.button.return_to_list') ?></a>
    </div>
  </div>
</div>

<script>
const _result_datas = '<?php echo $result_datas ?>';
const _project_id = <?php echo isset($project_id) && $project_id && is_numeric($project_id) ? $project_id : 'null'; ?>;
</script>