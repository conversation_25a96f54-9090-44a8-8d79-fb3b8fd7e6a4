<style>
  .button-tab ul li {
    padding: 0px;
  }
  .button-tab ul li a{
    padding: 5px 10px;
    display : block;
  }

  .button-tab ul li a:focus{
    box-shadow: none;
  }
</style>

<!-- BEGIN PAGE HEADER-->
<!-- BEGIN PAGE HEAD -->
<div class="page-head">
	<!-- BEGIN PAGE TITLE -->
	<div class="page-title">
		<h1><?php echo($_active_menu_name)?><small></small></h1>
	</div>
	<!-- END PAGE TITLE -->
</div>
<!-- END PAGE HEAD -->
<!-- END PAGE HEADER-->
<!-- BEGIN PAGE CONTENT-->
<div class="row">
	<div class="col-md-12">
        <!-- Page Content -->
      <div id="page-wrapper">
	        <!-- <div class="portlet light"> -->
			<?php 
				if (strlen($_active_menu) > 4) {
					$tab_menu = View::factory ('admin/menutab');
					echo($tab_menu);
				}
			?>
      <?php 
        $tag_cd = isset($_GET['tag']) ? $_GET['tag'] : "" ;
      ?>
      <div class="edit-container">
        <div class="settings-container">
          <nav class="button-tab">
              <ul class="">
                  <li class="<?php if ($tag_cd=="") {echo "active"; }?>"><a href="?"><?php echo $_codes['29']['01']?></a></li>
                  <li class="<?php if ($tag_cd=="01") {echo "active"; }?>"><a href="?tag=01"><?php echo $_codes['40']['01']?></a></li>
                  <li class="<?php if ($tag_cd=="02") {echo "active"; }?>"><a href="?tag=02"><?php echo $_codes['40']['02']?></a></li>
                  <li class="<?php if ($tag_cd=="05") {echo "active"; }?>"><a href="?tag=05"><?php echo $_codes['40']['05']?></a></li>
                  <li class="<?php if ($tag_cd=="03") {echo "active"; }?>"><a href="?tag=03"><?php echo $_codes['40']['03']?></a></li>
                  <li class="<?php if ($tag_cd=="04") {echo "active"; }?>"><a href="?tag=04"><?php echo $_codes['40']['04']?></a></li>
              </ul>	
          </nav>     
    <!-- BEGIN PORTLET-->
      <!-- <div class="portlet-body"> -->
        <div class="table-scrollable table-scrollable-borderless">
          <table class="table table-hover table-light">
          <thead>
          <tr class="uppercase">
            <th>タイトル</th>
            <th style="width:120px;">種類</th>
            <th style="width:120px;">最終更新日</th>
            <th style="width:120px;">登録者</th>
            <?php if ($_user->role_cd == "99") echo ('<th style="width:160px;">表示状態</th>') ?>
          </tr>
          </thead>
          <tbody>
          <?php
          $count = 0;
          foreach ($data as $item) {
            $color = "light-blue";
            if ($item['ticket_tag_cd'] == "02") $color = "light-yellow";
            if ($item['ticket_tag_cd'] == "03") $color = "light-green";
            if ($item['ticket_tag_cd'] == "04") $color = "light-purple";
            if ($item['ticket_tag_cd'] == "05") $color = "light-red";
            echo('<tr>');
            echo('<td>');
            if (isset($_codes['40'][$item['ticket_tag_cd']])) echo('<div class="btn ' . $color . '">' . $_codes['40'][$item['ticket_tag_cd']] . '</div>');
            echo('<a href="/admin/ticketreply?id=' . $item['ticket_id'] . '">');
			      echo($item['title']);
            if ($_user->role_cd == "99") echo('<span style="margin-left:5px; color:#9ca8b0;"><small>' . $item['bot_name'] . '※</small></span>');
            if (!in_array($_user->user_id, explode(',', $item['content']))) echo('<span style="margin-left:5px;" class="unread-dot"></span>');
            echo('</a></td>');
            echo('<td style="width:120px;text-align:center;">');
            if (isset($_codes['40'][$item['ticket_tag_cd']])) echo $_codes['40'][$item['ticket_tag_cd']];
            echo('</td>');
            echo('<td style="width:120px;text-align:center;">');
            echo(substr($item['create_time'],0,10));
            echo('</td>');
            echo('<td style="width:120px;text-align:center;">');
            echo($item['name']);
            echo('');
            echo('</td>');
            if ($_user->role_cd == "99"):
            echo('<td style="width:120px;">');
            echo('<div style="display:inline-block" class="talkappi-switch" data-value="'. $item['display_flg'] . '"></div> ');
            echo('<span style="margin-left:10px;" ticket_id="' . $item['ticket_id'] . '">' . ($item['display_flg'] == 1 ? "表示中" : "非表示") . '</span>');
            echo('</td>');
            endif;
            echo('</tr>');
          }
          ?>
          </tbody></table>
        </div>
      <!-- </div> -->
      </div>
      </div>
    </div>
    <!-- END PORTLET-->
    <!-- END PORTLET-->
  </div>
</div>
</div>
<!-- END PAGE CONTENT-->
