			<!-- BEGIN PAGE HEADER-->
			<!-- BEGIN PAGE HEAD -->
			<div class="page-head">
				<!-- BEGIN PAGE TITLE -->
				<div class="page-title">
					<h1>体験・ツアー管理<small></small></h1>
				</div>
				<!-- END PAGE TITLE -->
			</div>
			<!-- END PAGE HEAD -->
			<!-- END PAGE HEADER-->
			<!-- BEGIN PAGE CONTENT-->
			<div class="row">
				<div class="col-md-12">
			        <!-- Page Content -->
			        <div id="page-wrapper">
				        <div class="portlet light">
							<div class="tabbable-line">
								<ul class="nav nav-tabs ">
									<li class="">
										<a href="/admin/experiences">
										体験・ツアー一覧</a>
									</li>
									<li class="active">
										<a href="/admin/experience">
										体験・ツアー新規</a>
									</li>
								</ul>
							</div>
							<br/>
							<form id="itemForm" action="/admin/item" class="form-horizontal" role="form" method="post">
								<div class="form-body">									
									<div class="form-group">
										<label class="control-label col-md-3">施設タイプ</label>
										<div class="col-md-3">
												<select name="intent_cd" class="form-control">
													<?php 
													foreach ($_classes as $class_cd=>$class) {
														echo('<option value="'. $class_cd .'">' . $class[0] . '</option>');
													}	
													?>
												</select>
										</div>
									</div>																							
									<div class="form-group">
										<label class="control-label col-md-3">施設コード</label>
										<div class="col-md-3">
											<div class="input-icon right">
												<i class="fa fa-user"></i>
												<input name="item_cd" type="text" class="form-control" placeholder="">
											</div>
										</div>
									</div>			
								</div>
								<div class="form-actions">
									<div class="row">
										<div class="col-md-offset-3 col-md-9">
											<button type="button" id="saveBaseButton" class="btn blue">
											<i class="fa fa-save"></i>&nbsp;&nbsp;基本情報保存</button>
											<button type="button" onclick="top.location='/admin/items'" class="btn grey-steel">戻る</button>
										</div>
									</div>
								</div>
													
								<div class="tabbable-line"> 
									<ul class="nav nav-tabs ">
										<?php foreach($_bot_lang as $lang_cd=>$lang_name) {
											echo('<li class="active">');
											echo('<a href="/admin/item">' . $lang_name . '</a></li>');
										}?>
									</ul>
								</div>	
								<br/>
								<div class="form-body">			
									<div class="form-group">
										<label class="control-label col-md-3">施設名</label>
										<div class="col-md-3">
											<div class="input-icon right">
												<i class="fa fa-user"></i>
												<input name="item_name" type="text" class="form-control" placeholder="">
											</div>
										</div>
									</div>
									<div class="form-group">
										<label class="control-label col-md-3">詳細</label>
										<div class="col-md-3">
											<div class="input-icon right">
												<i class="fa fa-user"></i>
												<input name="item_description" type="text" class="form-control" placeholder="">
											</div>
										</div>
									</div>												
								</div>
								<div class="form-actions">
									<div class="row">
										<div class="col-md-offset-3 col-md-9">
											<button type="button" id="saveButton" class="btn blue">
											<i class="fa fa-save"></i>&nbsp;&nbsp;詳細保存</button>
											<button type="button" onclick="top.location='/admin/items'" class="btn grey-steel">戻る</button>
										</div>
									</div>
								</div>														
							</form>								
						</div>
			        </div>
			        <!-- /#page-wrapper -->				
				</div>
			</div>
			<!-- END PAGE CONTENT-->



