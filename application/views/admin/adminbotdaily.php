			<!-- BEGIN PAGE HEADER-->
			<!-- BEGIN PAGE HEAD -->
			<div class="page-head">
				<!-- BEGIN PAGE TITLE -->
				<div class="page-title">
					<h1>ボット利用状況<small></small></h1>
				</div>
				<!-- END PAGE TITLE -->
			</div>
			<!-- END PAGE HEAD -->
			<!-- END PAGE HEADER-->
			<!-- BEGIN PAGE CONTENT-->
			<div class="row">
				<div class="col-md-12">
			        <!-- Page Content -->
			        <div id="page-wrapper">
			        <div class="portlet light">
						<div class="tabbable-line"> 
							<ul class="nav nav-tabs ">
								<li class="active">
									<a href="/admin/adminbotreport">
									日別</a>
								</li>
							</ul>
						</div>
						<div class="portlet box">
							<div class="portlet-body">
							<div class="form-body">		
								<div class="form-group">
									<label class="control-label col-md-1" style="padding-left:15px; padding-right:15px;">条件</label>
									<div class="col-md-4">
										<input name="start_date" id="start_date" value="<?php echo($start_date)?>" style="float:left;" class="form-control form-control-inline input-small date-picker" size="16" data-date-format="yyyy-mm-dd"  type="text"/>
										<input name="end_date" id="end_date" value="<?php echo($end_date)?>" style="float:left; margin-left:10px;" class="form-control form-control-inline input-small date-picker" size="16" data-date-format="yyyy-mm-dd"  type="text"/>
									</div>
									<div class="col-md-1">
										<button type="submit" id="searchButton" class="btn blue">
										<i class="fa fa-search mr10"></i>検索</button>
									</div>									
								</div>							
							</div>		
							<input type="hidden" name="bot_id" id="bot_id" value="" />
							<table class="table table-striped table-bordered table-hover js-data-table">
							<thead>
							<tr>
								<th>
									ボット名
								</th>
								<th>
									新規ユーザー数
								</th>
								<th>
									新規会話数
								</th>			
								<th>
									新規予約数
								</th>												
							</tr>
							</thead>

							<tbody>
							<?php
								foreach ($result as $k=>$v) {
							?>	
							<tr class="gradeX odd" role="row">
								<td>
									 <a class="link" bot_id="<?php echo $k?>" ><?php echo($v['bot_name'])?></a> 
								</td>
								<td>
									<?php echo($v['members']);?>
								</td>							
								<td>
									<?php echo($v['logs']);?>									 
								</td>			
								<td>
									<?php echo($v['orders']);?>									 
								</td>																				
							</tr>
							<?php } ?>
							</tbody>
							</table>
							</div>
						</div>
					</div>
			        </div>
			        <!-- /#page-wrapper -->					
				</div>
			</div>
			<!-- END PAGE CONTENT-->
