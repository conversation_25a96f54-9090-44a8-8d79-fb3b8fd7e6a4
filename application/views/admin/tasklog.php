<style type="text/css">
.btn.default:active, .btn.default.active {
  background-image: none;
  background-color: #45b6af;
  color: #fff;
}
</style>	
			<!-- BEGIN PAGE HEADER-->
			<!-- BEGIN PAGE HEAD -->
			<div class="page-head">
				<!-- BEGIN PAGE TITLE -->
				<div class="page-title">
					<h1>タスク管理<small></small></h1>
				</div>
				<!-- END PAGE TITLE -->
			</div>
			<!-- END PAGE HEAD -->
			<!-- END PAGE HEADER-->
			<!-- BEGIN PAGE CONTENT-->
			<div class="row">
				<div class="col-md-12">
			        <!-- Page Content -->
			        <div id="page-wrapper">
						<?php echo $menu ?>
						<div class="edit-container">
						<div class="settings-container">
								<input type="hidden" id="act" name="act" value="" />
								<div class="form-body">
									<div class="form-group">
										<label class="control-label col-md-2">レベル</label>
										<div class="col-md-3">
											<?php echo Form::select('log_level', array(""=>"全て", "1"=>"1", "2"=>"2","3"=>"3"), $post['log_level'], array('id'=>'log_level','class'=>'form-control'))?>
										</div>
									</div>
							<table class="table table-striped table-bordered table-hover js-data-table">
							<thead>
							<tr>
								<th>
								ログ時刻
								</th>
								<th>
								レベル
								</th>
								<th>
								内容
								</th>														
							</tr>
							</thead>

							<tbody>
							<?php
								foreach ($logs as $log) {
							?>	
							<tr class="gradeX odd" role="row">
								<td>
									 <?php echo($log['log_time'])?>
								</td>
								<td>
									 <?php echo($log['log_level'])?>
								</td>
								<td>
									 <?php echo($log['log_content'])?>
								</td>										
							</tr>
							<?php } ?>
							</tbody>
							</table>																										
								</div>
								<div class="form-actions">
									<div class="row">
										<div class="col-md-offset-2 col-md-9">
											<button type="button" onclick="top.location='/admin/tasks'" class="btn grey-steel">戻る</button>
										</div>
									</div>
								</div>
							</div>
						</div>
			        </div>
			        <!-- /#page-wrapper -->
				</div>
			</div>
			<!-- END PAGE CONTENT-->



