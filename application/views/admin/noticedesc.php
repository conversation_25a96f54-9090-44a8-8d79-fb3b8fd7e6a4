<style>
	.note-editor {
		margin: 0;
	}
</style>

<input type="hidden" name="notice_id" value=<?php echo $notice_id ?>>
<input type="hidden" name="content" id="content" value=""/>
<input type="hidden" name="buttons" id="buttons" value=""/>
<input type="hidden" name="display_timing_type" id="display_timing_type" value=""/>

<nav class="top-nav">
    <ul class="">
		<li>
			<a href="/<?php echo $_path?>/notice?id=<?php echo $notice_id?>"><?php echo __('admin.notice.basic_setting') ?></a>
		</li>
		<?php if ($notice_id != NULL) { ?>
			<li class="active">
				<a href="/<?php echo $_path?>/noticedesc?id=<?php echo $notice_id?>"><?php echo __('admin.notice.contents') ?></a>
			</li>
		<?php }?>
    </ul>
</nav>
<?php $isHalfModal = $post['notice_type_cd'] == '01'; ?>

<div class="content-container white border">
    <div class="section-container">
		<nav class="line-tab">
				<ul>
					<?php
						$display_lang_arr = explode(',', $_bot->support_lang);
						foreach($display_lang_arr as $k=>$v) {
							if (!in_array($v, $very_support_lang)) continue;
							if ($v == $lang_edit) {
								echo('<li class=" active">');
							}
							else {
								$translate_from_lang[$v] = $_codes['02'][$v];
								echo('<li>');
							}
							echo('<a class="func-menu" href="/admin/noticedesc?id=' . $notice_id . '&lang='. $v . '">' . $_codes['02'][$v] . '</a ></li>');
						}
					?>
				</ul>
		</nav>
		<div style="display: flex;gap:12px;">
			<div class="form-body" style="width:70%;">
				<div class="form-group">
					<label class="control-label col-md-1" style="white-space: nowrap;"><?php echo __('admin.common.label.title') ?></label>
					<div class="col-md-9">
						<input name="title" id="title" type="text" class="form-control js-noticedesc-title" style="width:100%;" placeholder="" value="<?php echo(htmlspecialchars($post['title']))?>">
						<?php if ($isHalfModal) { ?>
							<p style="color: #245BD6; font-size: 12px;"><?php echo __('admin.notice.halfmodal.title.helper') ?></p>
						<?php } ?>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label col-md-1"><?php echo __('admin.common.label.content') ?></label>
					<div class="col-md-9">
						<div id="summernote" class=".js-noticedesc-content"> <?php echo $post['content']?></div>
						<?php if ($isHalfModal) { ?>
							<p style="color: #245BD6; font-size: 12px;"><?php echo __('admin.notice.halfmodal.description.helper') ?></p>
						<?php } ?>
					</div>
				</div>		
				<?php if ($isHalfModal) { ?>
				<div class="form-group">
					<label class="control-label col-md-1" style="white-space: nowrap;"><?php echo __('admin.common.label.button') ?></label>
					<div class="col-md-9">
						<input name="button_name" id="button_name" type="text" class="form-control js-halfmodal-button-title" style="width:100%;" value="<?php echo(htmlspecialchars($post['main_button']['text']) ?? "")?>">
					</div>
				</div>
				<div class="form-group">
					<label class="control-label col-md-1" style="white-space: nowrap;"><?php echo __('admin.inquirydesc.label.destination_url') ?></label>
					<div class="col-md-9">
						<input name="button_url" id="button_url" type="text" class="form-control" style="width:100%;" value="<?php echo(htmlspecialchars($post['main_button']['action']['params']['url']) ?? "")?>">
					</div>
				</div>
				<?php } ?>
			</div>
			<div id="very-preview" style="max-width:320px;">
				<div class="talkappi-very-preview js-very-preview"></div>
			</div>
		</div>
    </div>
	<?php 
		$to_lang_cds = array_filter($translate_to_lang, function($key) use ($lang_edit) {
			return $key != $lang_edit;
		}, ARRAY_FILTER_USE_KEY);
		if (count($to_lang_cds) > 0) {
	?>
	<div id="react-multilingualreflect"></div>
	<?php } ?>
    <div class="form-actions">
        <div class="row">
            <div class="col-md-offset-2 col-md-9">
                <div class="actions-container">
                    <button type="button" class="action-button page btn-blue js-action-save"><?php echo __('admin.common.button.save'); ?></button>
                    <a class="action-button page btn-white js-action-back"><?php echo __('admin.common.button.return_to_list'); ?></a>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
	const noticedescData = {
		title: <?php echo json_encode($post['title'])?>,
		publish_time: <?php echo isset($post['display_start_date']) ? json_encode($post['display_start_date']) : 'null' ?>,
		content: <?php echo json_encode($post['content']) ?>,
		buttonText: <?php echo isset($post['main_button']['text']) ? json_encode($post['main_button']['text']) : 'null' ?>,
	};
	const _from_lang_cd = '<?php echo $lang_edit ?>';
	const _to_lang_cds = <?php echo json_encode($translate_to_lang, JSON_UNESCAPED_UNICODE) ?>;
	const notice_type_cd = '<?php echo $post['notice_type_cd'] ?>';
</script>
<script src="/assets/common/react/components/blocks/multilingualreflect.bundle.js"></script>