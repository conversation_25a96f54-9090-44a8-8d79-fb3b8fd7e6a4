<!DOCTYPE html>
<!--[if IE 8]> <html class="ie8 no-js"> <![endif]-->
<!--[if IE 9]> <html class="ie9 no-js"> <![endif]-->
<!--[if !IE]><!-->
<html>
<!--<![endif]-->
<!-- BEGIN HEAD -->
<head>
<meta charset="utf-8"/>
<title><?php echo __('admin.login.head.title') ?></title>
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<meta http-equiv="Content-type" content="text/html; charset=utf-8">
<meta content="" name="description"/>
<meta content="" name="author"/>
<meta name="robots" content="noindex" />
<meta name="googlebot" content="noindex" />

<!-- <PERSON><PERSON><PERSON> GLOBAL MANDATORY STYLES -->
<link href="/assets/common/fontawesome/css/all.css" rel="stylesheet" type="text/css">
<link href="https://cdnjs.cloudflare.com/ajax/libs/simple-line-icons/2.4.1/css/simple-line-icons.min.css" rel="stylesheet" type="text/css">
<link href="<?php echo($_assets)?>global/plugins/bootstrap/css/bootstrap.min.css" rel="stylesheet" type="text/css">
<link href="<?php echo($_assets)?>global/plugins/bootstrap-switch/css/bootstrap-switch.min.css" rel="stylesheet" type="text/css"/>
<!-- END GLOBAL MANDATORY STYLES -->

<!-- BEGIN PLUGINS USED BY X-EDITABLE -->
<link rel="stylesheet" type="text/css" href="<?php echo($_assets)?>global/plugins/select2/select2.css"/>
<link rel="stylesheet" type="text/css" href="<?php echo($_assets)?>global/plugins/bootstrap-wysihtml5/bootstrap-wysihtml5.css"/>
<link rel="stylesheet" type="text/css" href="<?php echo($_assets)?>global/plugins/bootstrap-datepicker/css/datepicker.css"/>
<link rel="stylesheet" type="text/css" href="<?php echo($_assets)?>global/plugins/bootstrap-colorpicker/css/colorpicker.css"/>
<link rel="stylesheet" type="text/css" href="<?php echo($_assets)?>global/plugins/bootstrap-timepicker/css/bootstrap-timepicker.min.css"/>
<link rel="stylesheet" type="text/css" href="<?php echo($_assets)?>global/plugins/bootstrap-datetimepicker/css/bootstrap-datetimepicker.min.css"/>
<link rel="stylesheet" type="text/css" href="<?php echo($_assets)?>global/plugins/bootstrap-editable/bootstrap-editable/css/bootstrap-editable.css"/>
<link rel="stylesheet" type="text/css" href="<?php echo($_assets)?>global/plugins/bootstrap-editable/inputs-ext/address/address.css"/>
<!-- END PLUGINS USED BY X-EDITABLE -->

<!-- BEGIN PAGE LEVEL STYLES -->
<link rel="stylesheet" type="text/css" href="<?php echo($_assets)?>global/plugins/jquery-multi-select/css/multi-select.css"/>
<link rel="stylesheet" type="text/css" href="<?php echo($_assets)?>global/plugins/jquery-tags-input/jquery.tagsinput.css"/>
<link rel="stylesheet" type="text/css" href="<?php echo($_assets)?>global/plugins/bootstrap-select/bootstrap-select.min.css"/>
<link rel="stylesheet" type="text/css" href="<?php echo($_assets)?>global/plugins/bootstrap-datepicker/css/datepicker3.css"/>
<link rel="stylesheet" type="text/css" href="<?php echo($_assets)?>global/plugins/bootstrap-daterangepicker/daterangepicker-bs3.css"/>
<link rel="stylesheet" type="text/css" href="<?php echo($_assets)?>global/plugins/bootstrap-toastr/toastr.min.css"/>
<link rel="stylesheet" type="text/css" href="<?php echo($_assets)?>global/plugins/bootstrap-fileinput/bootstrap-fileinput.css"/>
<link rel="stylesheet" type="text/css" href="<?php echo($_assets)?>global/plugins/clockface/css/clockface.css"/>
<link rel="stylesheet" type="text/css" href="<?php echo($_assets)?>global/plugins/datatables/plugins/bootstrap/dataTables.bootstrap.css"/>

<link rel="stylesheet" type="text/css" href="<?php echo($_assets)?>global/css/plugins.css"/>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.12.1/jquery-ui.min.css">
<link href="/assets/global/plugins/month-picker/css/MonthPicker.min.css" rel="stylesheet" type="text/css" />
<!-- END PAGE LEVEL STYLES -->

<?php if ($_js == 'talknew.js') { ?>
	<!-- summernote-0.8.20 -->
	<link href="<?php echo($_assets)?>global/plugins/bootstrap-summernote-0.8.20/summernote.min.css" rel="stylesheet">
<?php } else { ?>
	<link href="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote.min.css" rel="stylesheet">
<?php }?>
<link rel="stylesheet" type="text/css" href="/assets/global/plugins/bootstrap-summernote/summernote.css">

<!-- BEGIN THEME STYLES -->
<link rel="stylesheet" type="text/css" href="<?php echo($_assets)?>global/css/components-rounded.css" id="style_components"/>
<link rel="stylesheet" type="text/css" href="/assets/admin/css/diffview.css?v=<?php echo(time())?>"/>

<link rel="stylesheet" type="text/css" href="/assets/admin/layout4/css/themes/light.css"/>
<link rel="stylesheet" type="text/css" href="/assets/admin/layout4/css/layout.css"/>

<link rel="stylesheet" type="text/css" href="/assets/common/talkappi-file-upload/css/talkappi-file-upload.css?v=<?php echo(time())?>"/>
<link rel="stylesheet" type="text/css" href="/assets/common/talkappi-components/css/talkappi-components.css?v=<?php echo(time())?>"/>
<link rel="stylesheet" type="text/css" href="/assets/common/talkappi-modal/css/talkappi-modal.css?v=<?php echo(time())?>"/>
<link rel="stylesheet" type="text/css" href="/assets/common/talkappi-datetime-select/css/talkappi-datetime-select.css?v=<?php echo(time())?>"/>
<link rel="stylesheet" type="text/css" href="/assets/common/talkappi-category-select/css/talkappi-category-select.css?v=<?php echo(time())?>"/>
<link rel="stylesheet" type="text/css" href="/assets/common/talkappi-section/css/talkappi-section.css?v=<?php echo(time())?>"/>
<link rel="stylesheet" type="text/css" href="/assets/common/talkappi-schedule-section/css/talkappi-schedule-section.css?v=<?php echo(time())?>"/>
<link rel="stylesheet" type="text/css" href="/assets/common/talkappi-skill-select/css/talkappi-skill-select.css?v=<?php echo(time())?>"/>
<link rel="stylesheet" type="text/css" href="/assets/common/talkappi-preview/css/talkappi-preview.css?v=<?php echo(time())?>"/>
<link rel="stylesheet" type="text/css" href="/assets/common/talkappi-very-preview/css/talkappi-very-preview.css?v=<?php echo(time())?>"/>
<link rel="stylesheet" type="text/css" href="/assets/common/talkappi-entry-selection/css/talkappi-entry-selection.css?v=<?php echo(time())?>">
<link rel="stylesheet" type="text/css" href="/assets/common/talkappi-jsoncheck/css/talkappi-jsoncheck.css?v=<?php echo(time())?>">
<link rel="stylesheet" type="text/css" href="/assets/common/talkappi-alert-mail-setting/css/talkappi-alert-mail-setting.css?v=<?php echo(time())?>">
<link rel="stylesheet" type="text/css" href="/assets/common/talkappi-policy/css/talkappi-policy.css">

<?php if ($_js == 'components.js') { ?>
<link rel="stylesheet" type="text/css" href="/assets/common/talkappi-entry/css/talkappi-entry.css?v=<?php echo(time())?>"/>
<?php } ?>
<link rel="stylesheet" type="text/css" href="/assets/admin/css/chat.css?v=<?php echo(time())?>"/>
<link rel="stylesheet" type="text/css" href="/assets/admin/css/add_ono.css?v=<?php echo(time())?>"/>

<link rel="stylesheet" type="text/css" href="/assets/admin/css/admin.css?v=<?php echo(time())?>"/>
<link rel="stylesheet" type="text/css" href="/assets/admin/css/admin-v2.css?v=<?php echo(time())?>"/>

<?php if ($_js != 'msgdesc.js') { ?>
<link rel="stylesheet" type="text/css" href="/assets/admin/css/edit_faq.css?v=<?php echo(time())?>"/>
<link rel="stylesheet" type="text/css" href="/assets/admin/css/survey.css?v=<?php echo(time())?>"/>
<?php }?>
<!-- END THEME STYLES -->

<script type="text/javascript">
	<?php if (($_bot_id != '')) {?>
	const _mgt_bot_id = <?php echo $_bot_id ?>;
	const _num_admin_refresh_interval = <?php echo $_bot_setting['num_admin_refresh_interval']?>;
	const _item_divs = <?php echo json_encode(['1'=>$_bot_setting['div_item_class_1'], '2'=>$_bot_setting['div_item_class_2'], '3'=>$_bot_setting['div_item_class_3'], '4'=>$_bot_setting['div_item_class_4'], '5'=>$_bot_setting['div_item_class_5'], '6'=>$_bot_setting['div_item_class_6'], '7'=>$_bot_setting['div_item_class_7'], '8'=>$_bot_setting['div_item_class_8'], '9'=>$_bot_setting['div_item_class_9'], '10'=>$_bot_setting['div_item_class_10'], '11'=>$_bot_setting['div_item_class_11'], '12'=>$_bot_setting['div_item_class_12'], '13'=>$_bot_setting['div_item_class_13'], '14'=>$_bot_setting['div_item_class_14'], '15'=>$_bot_setting['div_item_class_15'], '16'=>$_bot_setting['div_item_class_16'], '17'=>$_bot_setting['div_item_class_17']])?>;
	<?php } else { ?>
	const _num_admin_refresh_interval = -1;
	<?php }?>
	const _admin_message = '<?php echo $_message ?>';
	const _admin_lang_cd = '<?php echo $_lang_cd ?>';
	const _admin_path = '<?php echo $_path ?>';
	const _admin_uri = '<?php echo $_uri ?>';
	const _service_url = '<?php echo $_service_url ?>';
	const _admin_params = {};
</script>

</head>
<!-- END HEAD -->
<!-- BEGIN BODY -->
<!-- DOC: Apply "page-header-fixed-mobile" and "page-footer-fixed-mobile" class to body element to force fixed header or footer in mobile devices -->
<!-- DOC: Apply "page-sidebar-closed" class to the body and "page-sidebar-menu-closed" class to the sidebar menu element to hide the sidebar by default -->
<!-- DOC: Apply "page-sidebar-hide" class to the body to make the sidebar completely hidden on toggle -->
<!-- DOC: Apply "page-sidebar-closed-hide-logo" class to the body element to make the logo hidden on sidebar toggle -->
<!-- DOC: Apply "page-sidebar-hide" class to body element to completely hide the sidebar on sidebar toggle -->
<!-- DOC: Apply "page-sidebar-fixed" class to have fixed sidebar -->
<!-- DOC: Apply "page-footer-fixed" class to the body element to have fixed footer -->
<!-- DOC: Apply "page-sidebar-reversed" class to put the sidebar on the right side -->
<!-- DOC: Apply "page-full-width" class to the body element to have full width page without the sidebar menu -->
<body class="page-header-fixed page-sidebar-closed-hide-logo">

<div class="clearfix">
</div>
<!-- BEGIN CONTAINER -->
<div class="sidebar-container">
	<!-- BEGIN SIDEBAR -->
    <div class="page-sidebar-wrapper" 
	<?php if (strpos($_uri, "fullview=true") !== false) {
		$content_margin = '0';
		echo('style="display:none"'); 
	} else{
		$content_margin = '276';
		echo('style="z-index:1"'); 
	}?>>
		<!-- DOC: Set data-auto-scroll="false" to disable the sidebar from auto scrolling/focusing -->
		<!-- DOC: Change data-auto-speed="200" to adjust the sub menu slide up/down speed -->
		<nav class="page-sidebar navbar-collapse collapse" style="min-height: 100vh;transition: .7s;">
			<!-- BEGIN SIDEBAR MENU -->
			<!-- DOC: Apply "page-sidebar-menu-light" class right after "page-sidebar-menu" to enable light sidebar menu style(without borders) -->
			<!-- DOC: Apply "page-sidebar-menu-hover-submenu" class right after "page-sidebar-menu" to enable hoverable(hover vs accordion) sub menu mode -->
			<!-- DOC: Apply "page-sidebar-menu-closed" class right after "page-sidebar-menu" to collapse("page-sidebar-closed" class must be applied to the body element) the sidebar sub menu mode -->
			<!-- DOC: Set data-auto-scroll="false" to disable the sidebar from auto scrolling/focusing -->
			<!-- DOC: Set data-keep-expand="true" to keep the submenues expanded -->
			<!-- DOC: Set data-auto-speed="200" to adjust the sub menu slide up/down speed -->
			<ul class="page-sidebar-menu" data-keep-expanded="false" data-auto-scroll="true" data-slide-speed="200" >
				<div class="sidebar-title-container" style="height:100px;">
					<div class="page-logo">
						<a href="/admin/top" onfocus="this.blur();">
							<?php if ($_bot != NULL && $_logo_url != '') echo('<img src="' . $_logo_url . '" alt="logo" class="logo-default"/>'); ?>
						</a>
					</div>
					<div class="sidebar-bot-title flexiblebox" <?php if ($_logo_url == '') echo('style="margin-left:20px;"') ?>>
						<div id="bot-title" class="bot-title" title="<?php if ($_bot != NULL) echo($_bot->bot_id)?>">
							<?php if ($_bot != NULL) echo($_bot->bot_name)?>
						</div>
					</div>
					<div>
						<div class="sidebar-click menu-toggler sidebar-toggler">
							<img src="/assets/admin/images/icon-shrink.svg" alt="click to open/close slide menu" class="icon-shrink" id="menu-navi-icon" >
						</div>
					</div>
				</div>
				<!-- BEGIN RESPONSIVE MENU TOGGLER -->
				<a href="javascript:;" class="menu-toggler responsive-toggler" data-toggle="collapse" data-target=".navbar-collapse" onfocus="this.blur();">
				</a>
				<!-- END RESPONSIVE MENU TOGGLER -->
			<?php 
			$model = new Model_Adminmodel();
			if ($_bot != null) {
				$msg_menu_cd = Session::instance()->get('msg_menu_cd', NULL);
				$item_div = Session::instance()->get('item_div', NULL);
				$product_div = Session::instance()->get('product_div', NULL);
				$order_div = Session::instance()->get('order_div', NULL);
				echo('<ul class="page-sidebar-menu " data-keep-expanded="false" data-auto-scroll="true" data-slide-speed="200">');
				$menu_setting = [
						'admin/services'=>'flg_accept_request',
						'admin/servicelist'=>'flg_accept_request',
						'admin/orders'=>'flg_accept_order',
						'admin/surveys'=>'flg_next_survey',
						'admin/inquirys'=>'flg_next_inquiry',
						'admin/coupons'=>'flg_next_survey',
						'admin/newsletterprojects'=>'flg_newsletter',
						'admin/newsletteraddressmam'=>'flg_newsletter',
						'admin/newslettermam'=>'flg_newsletter',
						'admin/signatures'=>'flg_newsletter',
						'admin/faqs'=>'flg_faq_site',
						'admin/faqpage'=>'flg_faq_site',
						'admin/faqlog'=>'flg_faq_site',
						'admin/stocklist'=>'flg_stock_manage',
						'admin/postlist'=>'txt_facebook_post_token',
						'admin/botsublistreport'=>'div_item_class_3',
						'admin/items?div=1'=>'div_item_class_1',
						'admin/items?div=2'=>'div_item_class_2',
						'admin/items?div=3'=>'div_item_class_3',
						'admin/items?div=4'=>'div_item_class_4',
						'admin/products?div=5'=>'div_item_class_5',
						'admin/products?div=7'=>'div_item_class_7',
						'admin/corps'=>'json_reserve_settings.corp_func',
						'admin/couponsnew'=>'flg_next_coupon',
						'adminvery/laundry' => 'flg_laundry',
				];
				foreach($_sys_menu as $k=>$v) {
					if (($k == '10' || $k == '20') && $_user->role_cd != '99') {
						continue;
					}
					if (($k == '02' || $k == '03' || $k == '11' || $k == '14') && $_bot_setting['flg_ai_bot'] != '1' && $_user->role_cd != '99') {
						continue;
					}
					if ($k == '09' && $_bot_setting['flg_ai_bot'] != '1' && $_user->role_cd != '99') {
						unset(($v["children"])['0902']);
						unset(($v["children"])['0906']);
						unset(($v["children"])['0908']);
						unset(($v["children"])['0910']);
					}
					if ($k == '05' && $_bot_setting['flg_accept_order'] != '1') {
						continue;
					}
					if ($k == '07' && $_bot_setting['flg_faq_site'] != '1') {
						continue;
					}
					if ($k == '08' && $_bot_setting['flg_next_survey'] != '1') {
						continue;
					}
					if ($k == '12' && $_bot_setting['flg_next_inquiry'] != '1') { //  $k == '17' PAY　判断ロジック取り出しました
						continue;
					}
					if ($k == '17') {//PAY(事前決済)
						$client = $model->get_pay_clients_by_bot_id($_bot_id);
						if (count($client) <= 0) {
							continue;
						} 
					}
					if ($k == '13' && $_bot_setting['flg_very'] != '1') {
						continue;
					}
					if ($k == '15' && $_bot_setting['flg_newsletter'] != '1') {
						continue;
					}
					if (($k == '24') && $_bot_setting['flg_ticket'] != '1') {
						continue;
					}
					/**
					 * Warning: Undefined array key "flg_page" in mgt/application/views/template/metronic.php on line 221
					 * Warning: Undefined array key "flg_member" in mgt/application/views/template/metronic.php on line 224
					 */
					if ($k == '16') {	//	hide sitepage in sidebar when flg_page not ON
						if  ( ! isset($_bot_setting['flg_page']) || $_bot_setting['flg_page'] == 0 ) {
							continue;
						}
					}
					if ($k == '19') {
						if  ( ! isset($_bot_setting['flg_engage']) || $_bot_setting['flg_engage'] != '1' ) {
							continue;
						}
					}
					if ($k == '23') {
						if  ( ! isset($_bot_setting['flg_member']) || $_bot_setting['flg_member'] != '1' ) {
							continue;
						}
					}
					$class = '';
					$menu = $v['parent'];
					$pos = strpos($menu['word'], '?');
					if ($pos === false) {
						$menu_url = $menu['word'];
					}
					else {
						$menu_url = substr($menu['word'], 0, $pos);
					}

					if ($menu['word'] !='' && strpos($_uri, '/' . $menu['word']) === 0) {
						$class = 'active cl';
					}
					$sub_menu_string = '';
					if (count($v['children']) > 0) {
						foreach($v['children'] as $sub_menu) {
							// chat special
							if ($sub_menu['word'] == 'chat') {
								$role_function = Session::instance()->get('role_function', NULL);
								if ($_user->role_cd != '99' && !in_array('0201', $role_function)) {
									continue;
								}
							}
							if ($sub_menu['word'] == 'admin/items?div=3') {
								$grp_bot_id = $model->get_grp_bot_id($_bot_id);
								if ($grp_bot_id != 0) continue;
							}
							$sub_class = '';
							$pos = strpos($sub_menu['word'], '?');
							if ($pos === false) {
								$sub_menu_action = $sub_menu['word'];
							}
							else {
								$sub_menu_action = substr($sub_menu['word'], 0, $pos);
							}
							$pos = strpos($_uri, '?');
							if ($pos === false) {
								$uri_action = $_uri;
							}
							else {
								$uri_action = substr($_uri, 0, $pos);
							}
							// item special
							if ($uri_action == '/admin/items' || $uri_action == '/admin/item' || $uri_action == '/admin/itemdesc') {
								$_uri = '/admin/items?div=' . $item_div;
							}
							else if ($uri_action == '/admin/products' || $uri_action == '/admin/product' || $uri_action == '/admin/productdesc') {
								$_uri = '/admin/products?div=' . $product_div;
							}
							else if ($uri_action == '/admin/surveys' || $uri_action == '/admin/survey' || $uri_action == '/admin/surveydesc'
									|| $uri_action == '/admin/surveyentry' || $uri_action == '/admin/surveyresult'
									|| $uri_action == '/admin/surveyreport' || $uri_action == '/admin/surveyorders' ) {
								$_uri = '/admin/surveys';
							}
							else if ($uri_action == '/admin/orders' || $uri_action == '/admin/order' || $uri_action == '/admin/orderreport') {
								$_uri = '/admin/orders?div=' . $order_div;
							}
							//get the substr of URL before ?
							$str_length = strpos($_uri, '?') ? strpos($_uri, '?') : mb_strlen($_uri); 
							if (strcmp($_uri, '/' . $sub_menu['word']) === 0) {
							//if ($_uri == '/' . $sub_menu['word']) {
								$class = 'active open';
								if ($sub_menu['word'] == 'admin/bot') {
									if ($uri_action == '/admin/botscenes' || $uri_action == '/admin/botscene' || $uri_action == '/admin/bottheme'  || 
										$uri_action == '/admin/botreserve' || $uri_action == '/admin/botline' || $uri_action == '/admin/botlinemenu') {
								   		$sub_class = '';
									}
									else {
										$sub_class = 'active';
									}
								}
								else {
									$sub_class = 'active';
								}
							}
							else if (strcmp(substr($_uri,0,$str_length), '/' . $sub_menu['word']) === 0) {
								$class = 'active open';
								$sub_class = 'active';
							}
							else {
								if ($sub_menu['word'] == 'admin/botscenes') {
									if ($uri_action == '/admin/botscenes' || $uri_action == '/admin/botscene' || $uri_action == '/admin/bottheme') {
										$class = 'active open';
										$sub_class = 'active';
									}
								}
								else if ($sub_menu['word'] == 'admin/corps') {
									if ($uri_action == '/admin/corps' || $uri_action == '/admin/corp') {
										$class = 'active open';
										$sub_class = 'active';
									}
								}
							}
							foreach($_tab_menu[$sub_menu['class_cd']]['children'] as $m) {
								if ($uri_action == '/' . $m['word']) {
									$class = 'active open';
									$sub_class = 'active';
									break;
								}
							}
							// authority check
							if ($_user->role_cd != '99') {
								if ($sub_menu['word'] != '' && strpos($sub_menu['word'], "http") !== 0) {
									$has_authority = false;
									foreach($_user_function as $f) {
										if (substr($f, -1, 1) == "*") {
											if (strpos($sub_menu_action, substr($f, 0, strlen($f) - 1)) === 0) {
												$has_authority = true;
												break;
											}
										}
										else {
											if ($sub_menu_action == $f) {
												$has_authority = true;
												break;
											}
										}
									}
									if ($has_authority == false) {
										continue;
									}
								}
							}

							// botsetting check
							if (array_key_exists($sub_menu['word'], $menu_setting)) {
								$segs = explode('.', $menu_setting[$sub_menu['word']]);
								if (count($segs) > 1) {
									if (substr($_bot_setting[$segs[0]],0,1) == '[') {
										$sub_setting = json_decode($_bot_setting[$segs[0]], true);
										$son_setting = false;
										foreach($sub_setting as $json) {
											if (array_key_exists($segs[1], $json) && $json[$segs[1]] == '1') {
												$son_setting = true;
												break;
											}
										}
										if ($son_setting == false) continue;
									}
									else {
										$sub_setting = json_decode($_bot_setting[$segs[0]], true);
										if (!is_array($sub_setting) || !array_key_exists($segs[1], $sub_setting) || $sub_setting[$segs[1]] == '0' || $sub_setting[$segs[1]] == '') {
											continue;
										}
									}
								}
								else {
									if ($_bot_setting[$menu_setting[$sub_menu['word']]] == '0' || $_bot_setting[$menu_setting[$sub_menu['word']]] == '') {
										continue;
									}
								}
							}

							$sub_menu_string = $sub_menu_string . '<li class="' . $sub_class . '">';
							// chat
							if ($sub_menu['word']== 'chat' || $sub_menu['word']== 'admin/congestion_user') {
								$sub_menu_string = $sub_menu_string . '<a target="_blank" class="detailed-dashbord" rel="noopener noreferrer" href="/' . $sub_menu['word'] . '?token=' . time() . '">';
							}
							else if ($sub_menu['word']== 'admin/services' && $_service_count > 0) {
								$sub_menu_string = $sub_menu_string . '<a class="func-menu detailed-dashbord" href="/' . $sub_menu['word'] . '"><span class="badge badge-danger">' . $_service_count . '</span>';
							}
							else if ($sub_menu['word']== 'admininquiry/inquirys' && $_inquiry_unsupport_count > 0) {
								$sub_menu_string = $sub_menu_string . '<a class="func-menu detailed-dashbord" href="/' . $sub_menu['word'] . '"><span class="badge badge-danger">' . $_inquiry_unsupport_count . '</span>';
							}
							else if ($sub_menu['word']== 'admin/newsletterprojects') {
								if ( !isset($_bot_setting['flg_newsletter']) || $_bot_setting['flg_newsletter'] != '1') {
									continue;
								} else if ( !isset($_bot_setting['json_newsletter_setting']) || ! $_bot_setting['json_newsletter_setting'] ) {
									continue;
								} else {
									$newsletter_setting = json_decode($_bot_setting['json_newsletter_setting']);
									if ( !isset($newsletter_setting->project_management) || $newsletter_setting->project_management != 1) {
										continue;
									}
								}
								$sub_menu_string = $sub_menu_string . '<a class="func-menu detailed-dashbord" href="/' . $sub_menu['word'] . '">';
							}
							else if (strpos($sub_menu['word'], "http") === 0) {
								$sub_menu_string = $sub_menu_string . '<a target="_blank" class="detailed-dashbord" rel="noopener noreferrer" href="' . $sub_menu['word'] . '">';
							}
							else {
								$sub_menu_string = $sub_menu_string . '<a class="func-menu detailed-dashbord" href="/' . $sub_menu['word'] . '">';
							}
							$sub_menu_string = $sub_menu_string . $sub_menu['name'];
							$sub_menu_string = $sub_menu_string . '</a></li>';
						}
					}
					if (count($v['children']) > 0 && $sub_menu_string == '') continue;
					if ($menu['class_cd'] == '02') {
						echo('<li class="service-section-name service-section-heading">');
						echo('<img src="/assets/admin/images/icon-sys_menu-chatbot.svg" alt="CHATBOT" class="service-section-icon">');
						echo('<span>CHATBOT</span>');
						echo('</li>');
					} else if ($menu['class_cd'] == '07') {
						echo('<li class="service-section-name service-section-heading">');
						echo('<img src="/assets/admin/images/icon-sys_menu-faq.svg" alt="FAQ" class="service-section-icon">');
						echo('<span>FAQ</span>');
						echo('</li>');
					} else if ($menu['class_cd'] == '13') {
						echo('<li class="service-section-name service-section-heading">');
						echo('<img src="/assets/admin/images/icon-sys_menu-very.svg" alt="VERY" class="service-section-icon">');
						echo('<span>VERY</span>');
						echo('</li>');
					} else if ($menu['class_cd'] == '12') {
						echo('<li class="service-section-name service-section-heading">');
						echo('<img src="/assets/admin/images/icon-sys_menu-inquiry.svg" alt="INQUIRY" class="service-section-icon">');
						echo('<span>INQUIRY</span>');
						echo('</li>');
					} else if ($menu['class_cd'] == '08') {
						echo('<li class="service-section-name service-section-heading">');
						echo('<img src="/assets/admin/images/icon-sys_menu-survey.svg" alt="SURVEY" class="service-section-icon">');
						echo('<span>SURVEY</span>');
						echo('</li>');
					} else if ($menu['class_cd'] == '15') {
						echo('<li class="service-section-name service-section-heading">');
						echo('<img src="/assets/admin/images/icon-sys_menu-newsletter.svg" alt="NEWSLETTER" class="service-section-icon">');
						echo('<span>NEWSLETTER</span>');
						echo('</li>');
					} else if ($menu['class_cd'] == '16') {
						echo('<li class="service-section-name service-section-heading">');
						echo('<img src="/assets/admin/images/icon-sys_menu-page.svg" alt="PAGE" class="service-section-icon">');
						echo('<span>PAGE</span>');
						echo('</li>');
					} else if ($menu['class_cd'] == '17') {
						echo('<li class="service-section-name service-section-heading">');
						echo('<img src="/assets/admin/images/icon-sys_menu-pay.svg" alt="PAY" class="service-section-icon">');
						echo('<span>PAY</span>');
						echo('</li>');
					} else if ($menu['class_cd'] == '19') {
						echo('<li class="service-section-name service-section-heading">');
						echo('<img src="/assets/admin/images/icon-sys_menu-engage.svg" alt="ENGAGE" class="service-section-icon">');
						echo('<span>ENGAGE</span>');
						echo('</li>');
					} else if ($menu['class_cd'] == '23') {
						echo('<li class="service-section-name service-section-heading">');
						echo('<img src="/assets/admin/images/icon-sys_menu-member.svg" alt="MEMBER" class="service-section-icon">');
						echo('<span>MEMBER</span>');
						echo('</li>');
					} else if ($menu['class_cd'] == '24') {
						echo('<li class="service-section-name service-section-heading">');
						echo('<img src="/assets/admin/images/icon-sys_menu-ticket.svg" alt="TICKET" class="service-section-icon">');
						echo('<span>TICKET</span>');
						echo('</li>');
					} else if ($menu['class_cd'] == '09') {
						echo('<li class="service-section-name service-section-heading">');
						echo('<img src="/assets/admin/images/icon-sys_menu-setting.svg" alt="SETTING" class="service-section-icon">');
						if ($_lang_cd == 'ja') {
							echo('<span>設定</span>');
						}
						else if($_lang_cd == 'en'){
							echo('<span>Settings</span>');
						}
						
						echo('</li>');
					} 
					echo('<li class="' . $class . '">');
					if (count($v['children']) > 0) {
						echo('<a href="javascript:void(0);" class="sub-menu-link">');
						echo('<span class="sub-menu-name">');
						echo($menu['grid_pic_url']);
						if ($menu['class_cd'] == '02' && $_service_count > 0) {
							echo('<span class="title sub-menu-title">' . $menu['name'] . '<span class="unread-dot" style="margin-left:10px; margin-bottom: 5px;"></span></span>');
						}
						else if ($menu['class_cd'] == '12' && $_inquiry_unsupport_count > 0) {
							echo('<span class="title sub-menu-title">' . $menu['name'] . '<span class="unread-dot" style="margin-left:10px; margin-bottom: 5px;"></span></span>');
						}
						else {
							echo('<span class="title sub-menu-title">' . $menu['name'] . '</span>');
						}
						echo('</span>');
						echo('<span class="arrow"></span>');
						echo('</a>');
						echo('<ul class="sub-menu">');
						echo($sub_menu_string);
						echo('</ul>');
					}
					else { // there is sub menu
						echo('<a class="func-menu detailed-dashbord sub-menu-link" href="/' . $menu['word'] . '">');
						echo('<span class="sub-menu-name">');
						echo($menu['grid_pic_url']);
						echo('<span class="title sub-menu-title">' . $menu['name'] . '</span>');
						echo('</span>');
						echo('</a>');
					}
					echo('</li>');
				}
				echo('</ul>');
				} ?>
			</ul>
			<!-- END SIDEBAR MENU -->
		</nav>
	</div>
	<!-- END SIDEBAR -->
	<!-- BEGIN CONTENT -->
	<div class="page-content-wrapper">
		<?php if(isset($_disaster['_is_setting_flg'])){ ?>
			<div class="flexbox-center page-content-disaster-header" style="height: 48px; background: #FFDCE5;">
				<div style="color:#E53361; font-size: 18px;"><?php echo __('admin.disaster.warning.now_applying')?></div>
				<?php if (strpos($_uri, "/admin/disaster") === false){ ?>
					<a href="/admin/disastersetting" class="flexbox-x-axis pointer" style="display: inline-flex; align-items: center; border-radius: 999px; background: #FFFFFF; height: 40px; padding: 0 20px; text-decoration: none; color: inherit;">
						<?php echo __('admin.disaster.warning.link_to_cancel')?>
						<div class="next-btn" style="width: 12px; height: 12px;"></div>
					</a>
				<?php }?>
			</div>
		<?php }?>
		<div class="page-content-header" <?php if (strpos($_uri, "fullview=true") !== false)  echo('style="display:none"')?>>
			<a href="javascript:void(0);" class="menu-toggler responsive-toggler" data-toggle="collapse" data-target=".navbar-collapse" onfocus="this.blur();"></a>
			<?php
			echo('<h1 class="page-content-title">' . $_active_menu_name . '</h1>');
			?>
			<!-- BEGIN PAGE TOP -->
			<div class="page-top">
				<!-- BEGIN TOP NAVIGATION MENU -->
				<div class="top-menu">
					<ul class="nav navbar-nav pull-right">
						<li class="separator hide">
						</li>
						<!-- BEGIN USER LOGIN DROPDOWN -->
						<!-- DOC: Apply "dropdown-dark" class after below "dropdown-extended" to change the dropdown styte -->
						<li class="dropdown dropdown-user dropdown-dark">
							<a href="#" class="dropdown-toggle" data-toggle="dropdown" data-close-others="true">
							<!-- DOC: Do not remove below empty space(&nbsp;) as its purposely used -->
							<div class="dropdown-avatar">
								<span class="username username-hide-on-mobile"><?php echo($_user->name)?> </span>
								<img alt="user icon" class="img-circle content-header-icon" src="/assets/common/images/avatar.jpg" />
							</div>
							</a>
							<ul class="dropdown-menu dropdown-menu-default">
							<?php
							$_bot_array = Session::instance()->get('bot_array');
							if (count($_bot_array) > 1 || (count($_bot_array) == 1 && Session::instance()->get('has_grp_bot'))) {
							?>
								<li>
									<a href="javascript:void(0);" id="show_bot_box">
									<i class="icon-home"></i> <?php echo __('admin.template.menu.change_bot') ?>  <span class="badge badge-success">
									<?php echo(count($_bot_array))?> </span>
									</a>
								</li>
							<?php } ?>
						<li class="divider">
							<?php if ($_bot != null && $_bot_setting['flg_ai_bot'] == 1) { ?>
							<li>
								<a target="_blank" href="https://talkappi-preview.com/chatbot/?fid=<?php echo($_bot_setting['default_scene_cd'])?>">
								<i class="icon-speech"></i> <?php echo __('admin.template.menu.check_bot') ?>
								</a>
							</li>
							<?php }?>
							<?php if ($_bot != null && $_bot_setting['flg_faq_site'] == 1) { 
								if (isset($_faq_scenes)) foreach($_faq_scenes as $k=>$v) {
							?>
							<li>
								<a target="_blank" href="<?php echo $_base_url ?>faq?facility_cd=<?php echo($k)?>">
								<i class="icon-question"></i> <?php echo __('admin.template.menu.check_faq') ?>(<?php echo($v)?>)
								</a>
							</li>
							<?php }}?>
								<?php
								if ($_bot != null) {
									if ($model->get_grp_bot_id($_bot->bot_id) >= 0) {
										if ($_user->role_cd == '99' || $_user->role_cd == '80') {
											if ($_bot->bot_id % 1000 == 0) {
												echo('<li class="divider">');
												echo('<li>');
												echo('<a class="func-menu" href="/admin/botsublist">');
												echo('<i class="icon-list"></i> ' . __('admin.template.menu.group_bot_list'));
												echo('</a>');
												echo('</li>');
											}
											else {
												echo('<li class="divider">');
												echo('<li>');
												echo('<a class="func-menu" href="/admin/backgrpbot">');
												echo('<i class="icon-action-undo"></i> ' . __('admin.template.menu.back_to_portal'));
												echo('</a>');
												echo('</li>');
											}
										}
									}
								}
								?>
								
								<?php
								if ($_user->role_cd == "99" || $_user->role_cd == "80") {
									echo('<li class="divider">');
									echo('<li>');
									echo('<a class="switch-bot func-menu" bot_id="0" href="#">');
									echo('<i class="icon-settings"></i><small>');
									echo(' ' . __('admin.template.menu.reference_facility_setting'));
									echo('</small>');
									echo('</a>');
									echo('</li>');
									echo('<li>');
									echo('<a href="/ilovetalkappibot" target="_blank"><i class="icon-film"></i><small> ' . __('admin.template.menu.introduction_facility_list') . '</small></a>');
									echo('</li>');
									if ($_user->role_cd == "99") {
										echo('<li>');
										echo('<a class="func-menu" href="/admin/bot?mode=new" id="show_bot_box">');
										echo('<i class="icon-note"></i><small> ' . __('admin.template.menu.new_bot') .'</small>');
										echo('</li>');
									}
								}
								// copy paste
								if ($_user->role_cd == "99" || $_user->role_cd == "09") {
									$copy_uri = $_uri;
									$uri_pos = strpos($copy_uri, '?');
									if ($uri_pos !== false) $copy_uri = substr($copy_uri, 0, $uri_pos);
									if (array_key_exists($copy_uri, $_can_copied_action)) {
										echo('<li class="divider">');
										echo('<li>');
										echo('<a href="javascript:void(0);" class="js-content-copy"><i class="icon-copy"></i><small> ' . __('admin.template.menu.content_copy') . '</small></a>');
										echo('</li>');
									}
									if (array_key_exists($copy_uri, $_can_pasted_action)) {
										$obj = Session::instance()->get('item_copy');
										if ($obj != NULL && $_can_pasted_action[$copy_uri]['item_div'] == $obj['item_div']) {
											echo('<li class="divider">');
											echo('<li>');
											echo('<a href="javascript:void(0);" class="js-content-paste" title="' . $obj['item_name'] . '"><i class="icon-paste"></i><small> ' . __('admin.template.menu.content_paste') . '</small></a>');
											echo('</li>');
										}
									}
								}
								// VERY copy paste
								if (($_user->role_cd == "99") && false !== strpos($_uri, '/adminvery/verytop')) {
									$obj = Session::instance()->get('verytop_copy');
									
									echo('<li class="divider">');
									echo('<li>');
									echo('<a href="javascript:void(0);" class="js-verytop-copy"><i class="far fa-copy" style="margin-right:0.25em"></i><small>' . __('admin.template.menu.very_setting_copy') . '</small></a>');
									echo('</li>');
									if ($obj != NULL) {
										echo('<li>');
										echo('<a href="javascript:void(0);" class="js-verytop-paste" title="' . $obj['bot_id'] . '"><i class="fas fa-paste" style="margin-right:0.25em"></i><small>' . __('admin.template.menu.very_setting_paste') . '</small></a>');
										echo('</li>');
									}
								
								}
								// 最近利用BOT
								$latest_bot = Session::instance()->get('user_latest_bot');
								if ($latest_bot != null && count($latest_bot) > 1) {
									echo('<li class="divider">');
									foreach($latest_bot as $key=>$val) {
										echo('<li>');
										echo('<a class="switch-bot func-menu" bot_id="' . $key . '" href="#">');
										echo('<small><i class="icon-arrow-right"></i> ');
										echo($val);
										echo('</small>');
										echo('</a>');
										echo('</li>');
									}
									echo('<li class="divider">');
								}
								?>
								<!--
								<li>
									<a target="_blank" href="/chat">
									<i class="icon-earphones-alt"></i> 有人対応  <span class="badge badge-danger">
									new </span>
									</a>
								</li>
								-->
								<li>
									<a href="https://activalues.notion.site/talkappi-e55f61a508954b6e97bfe5ce976be7da" target="_blank">
									<i class="icon-book-open"></i> <?php echo __('admin.template.menu.manual') ?> </a>
								</li>
								<li class="divider">
								</li>
								<li id="passkey-setting">
									<a href="javascript:void(0);" onclick="window.talkappi_admin_openpasskeymodal({lang_cd: '<?php echo $_lang_cd; ?>'});">
										<i class="icon-key"></i> <?php echo __('admin.template.menu.passkey_setting') ?>
									</a>	
								</li>
								<li class="divider" id="passkey-setting-divider">
								</li>
								<li>
									<a href="/admin/logout">
									<i class="icon-logout"></i> <?php echo __('admin.template.menu.logout') ?> </a>
								</li>
							</ul>
						</li>
						<!-- END USER LOGIN DROPDOWN -->
					</ul>
				</div>
				<!-- END TOP NAVIGATION MENU -->
			</div>
		<!-- END PAGE TOP -->
		</div>
		<form id="metroForm" class="form-horizontal" role="form" method ="post" enctype="multipart/form-data">
		<input type="hidden" name="bot_id_token" value="<?php echo($_bot_id)?>" />
		<input type="hidden" name="page_action" id="page_action" value="" />
		
		<!-- BEGAIN BANNER -->
		<?php
		if ($_bot != null) {
			$banner_contract = [];
			if (isset($_bot_setting['flg_ai_bot'])) {
				if($_bot_setting['flg_ai_bot'] == 0) {
					array_push($banner_contract, 'chatbot');
				} 
				if ($_bot_setting['flg_faq_site'] == 0) {
					array_push($banner_contract, 'faq');
				} 
				if ($_bot_setting['flg_next_survey'] == 0) {
					array_push($banner_contract, 'survey');
				} 
				if ($_bot_setting['flg_next_inquiry'] == 0) {
					array_push($banner_contract, 'inquiry');
				} 
				if ($_bot_setting['flg_very'] == 0) {
					array_push($banner_contract, 'very');
				}
			}
			$banners = [];
			$dashboard_pr_banner = $model->get_bot_img_message_list($_bot_id, 'dashboard_pr_banner', $_lang_cd);
			$msg_orm = ORM::factory('botmsg')->where('bot_id', '=', 0)->where('msg_cd', '=', 'dashboard_pr_banner')->find();
			$ts = strtotime($msg_orm->upd_time);
			foreach($dashboard_pr_banner as $d) {
				$skill = $model->parse_skill($d['url']);
				if ($skill != null && $skill['skill'] == 'URL_TRANSITION') {
					$d['url'] = $skill['params']['url'];
				}
				else {
					continue;
				}
				if ($d['msg_image'] == null) continue;
				if ($d['title'] == '') {
					$banners[] = $d;
				}
				else {
					if (in_array($d['title'], $banner_contract)) {
						$banners[] = $d;
					}
				}
			}
			if($_SERVER['REQUEST_URI'] === "/admin/top" && count($banners) != 0) {
				$closeTime = "";
				if (array_key_exists('closeTime', $_COOKIE)) {
					$closeTime  = $_COOKIE["closeTime"];
				}
				if(strtotime($closeTime) < strtotime("now") ){
			?>
			<div class="page-content" style="margin-left: <?php echo $content_margin?>px;transition: all 0.7s ease 0s; padding:0px;">
				<div id="banner-carousel" class="carousel slide" data-ride="carousel">
					<div class="carousel-inner" role="listbox">
						<?php 
						$style = ' active';
						for ($i = 0; $i < count($banners); $i++) { ?>
						<div class="item<?php echo $style ?>">
							<a href="<?php echo($banners[$i]['url'])?>" target="_blank">
							<img src="<?php echo($banners[$i]['msg_image'] . "?$ts")?>">
							</a>
						</div>
						<?php 
						$style = '';
						} 
						?>
						<button type="button" id="close-banner" class="close" aria-label="Close">
							<span aria-hidden="true">×</span>
						</button>
					</div>
				</div>
			</div>
			<?php 
				}
			}
		}
		?>
		<!-- END BANNER -->
		<div class="page-content" style="margin-left: <?php echo $content_margin?>px;transition: all 0.7s ease 0s;">
		<audio id="audiomsg" src="/assets/admin/sound/msg.mp3"></audio>
		<?php if (isset($page_navi)) {echo('<div class="page-navi">'); echo($page_navi); echo('</div>');}?>
			<!-- BEGAIN FUNCTION PAGE -->
			<?php echo($content); ?>
			<!-- END FUNCTION PAGE -->	
		</div>
		</form>
	</div>
	<!-- END CONTENT -->
</div>

<div class="modal fade" id="bot_box" tabindex="-1" role="basic" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
				<h4 class="modal-title"><?php echo __('admin.template.menu.change_bot') ?></h4>
			</div>
			<div class="modal-body">
				<div id="full_sub" style="display:none;"></div>
				<div class="row" style="padding: 10px;">
					<div class="form-group flex">
						<label class="control-label col-md-2" style="top: 5px;"><?php echo __('admin.template.menu.select_bot') ?></label>
						<div class="col-md-10">
							<select class="form-control input-xlarge select2me" id="bot_select" data-placeholder="Select...">
								<option value=""></option>
								<?php
									$i = 0;
									$now_bot_id = $_bot_id;
									if ($model->get_grp_bot_id($_bot_id) >= 0) {
										$now_bot_id = intval($_bot_id / 1000) * 1000;
									}
									foreach($_bot_array as $bot) {
										$selected = "";
										if ($now_bot_id == $bot[0]) $selected = " selected ";
										echo('<option value="' . $bot[0] . '"' . $selected . '>' . $bot[1] . '['  . $bot[0] . ']' . '</option>');
									$i++;
								}?>
							</select>
						</div>
					</div>
				</div>
				<div id="sub_bot_select" class="row" style="display:none;padding: 10px;">
					<div class="form-group flex">
						<label class="control-label col-md-2" style="top: 5px;"><?php echo __('admin.template.menu.select_child_bot') ?></label>
						<div class="col-md-10">
							<select class="form-control input-xlarge select2me" id="bot_select_ext" data-placeholder="Select...">
								<option value=""></option>
							</select>
						</div>
					</div>
				</div>
			</div>
			<div class="modal-footer">
				<button type="button" class="btn default" data-dismiss="modal">キャンセル</button>
				<button type="button" class="btn green" id="bot_select_btn">OK</button>
			</div>
		</div>
		<!-- /.modal-content -->
	</div>
	<!-- /.modal-dialog -->
</div>


<!-- END CONTAINER -->
<!-- BEGIN FOOTER -->
<div class="page-footer">
	<div class="page-footer-inner">
		 2025 &copy; ActiValues, Inc.
	</div>
	<div class="scroll-to-top">
		<i class="icon-arrow-up"></i>
	</div>
	<div class="scroll-to-bottom">
		<i class="icon-arrow-down"></i>
	</div>
</div>

<!-- END FOOTER -->
<!-- BEGIN JAVASCRIPTS(Load javascripts at bottom, this will reduce page load time) -->
<!-- BEGIN CORE PLUGINS -->
<!--[if lt IE 9]>
<script src="/assets/global/plugins/respond.min.js"></script>
<script src="/assets/global/plugins/excanvas.min.js"></script>
<![endif]-->
<script src="https://cdnjs.cloudflare.com/ajax/libs/clipboard.js/2.0.8/clipboard.min.js" integrity="sha512-sIqUEnRn31BgngPmHt2JenzleDDsXwYO+iyvQ46Mw6RL+udAUZj2n/u/PGY80NxRxynO7R9xIGx5LEzw4INWJQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<script src="<?php echo($_assets)?>global/plugins/jquery.min.js" type="text/javascript"></script>
<script src="<?php echo($_assets)?>global/plugins/jquery-migrate.min.js" type="text/javascript"></script>
<!-- IMPORTANT! Load jquery-ui-1.10.3.custom.min.js before bootstrap.min.js to fix bootstrap tooltip conflict with jquery ui tooltip -->
<script src="<?php echo($_assets)?>global/plugins/jquery-ui/jquery-ui-1.10.3.custom.min.js" type="text/javascript"></script>
<script src="<?php echo($_assets)?>global/plugins/bootstrap/js/bootstrap.min.js" type="text/javascript"></script>
<script src="<?php echo($_assets)?>global/plugins/bootstrap-hover-dropdown/bootstrap-hover-dropdown.min.js" type="text/javascript"></script>
<script src="<?php echo($_assets)?>global/plugins/jquery-slimscroll/jquery.slimscroll.min.js" type="text/javascript"></script>
<script src="<?php echo($_assets)?>global/plugins/jquery.blockui.min.js" type="text/javascript"></script>
<script src="<?php echo($_assets)?>global/plugins/jquery.cokie.min.js" type="text/javascript"></script>
<script src="<?php echo($_assets)?>global/plugins/bootstrap-switch/js/bootstrap-switch.min.js" type="text/javascript"></script>
<!-- END CORE PLUGINS -->

<!-- BEGIN PLUGINS USED BY X-EDITABLE -->
<script type="text/javascript" src="<?php echo($_assets)?>global/plugins/moment.min.js"></script>
<script type="text/javascript" src="<?php echo($_assets)?>global/plugins/jquery.mockjax.js"></script>
<script type="text/javascript" src="<?php echo($_assets)?>global/plugins/select2/select2.min.js"></script>
<script type="text/javascript" src="<?php echo($_assets)?>global/plugins/bootstrap-wysihtml5/wysihtml5-0.3.0.js"></script>
<script type="text/javascript" src="<?php echo($_assets)?>global/plugins/bootstrap-wysihtml5/bootstrap-wysihtml5.js"></script>

<script type="text/javascript" src="<?php echo($_assets)?>global/plugins/bootstrap-datepicker/js/bootstrap-datepicker.js"></script>
<script type="text/javascript" src="<?php echo($_assets)?>global/plugins/bootstrap-timepicker/js/bootstrap-timepicker.min.js"></script>
<script type="text/javascript" src="<?php echo($_assets)?>global/plugins/bootstrap-datetimepicker/js/bootstrap-datetimepicker.min.js"></script>
<script type="text/javascript" src="<?php echo($_assets)?>global/plugins/clockface/js/clockface.js"></script>
<script type="text/javascript" src="<?php echo($_assets)?>global/plugins/bootstrap-daterangepicker/moment.min.js"></script>
<script type="text/javascript" src="<?php echo($_assets)?>global/plugins/bootstrap-daterangepicker/daterangepicker.js"></script>
<script type="text/javascript" src="<?php echo($_assets)?>global/plugins/bootstrap-datepicker/js/locales/bootstrap-datepicker.ja.js"></script>

<script type="text/javascript" src="/assets/global/plugins/bootstrap-colorpicker/js/bootstrap-colorpicker.js"></script>

<script type="text/javascript" src="<?php echo($_assets)?>global/plugins/bootstrap-editable/bootstrap-editable/js/bootstrap-editable.js"></script>
<script type="text/javascript" src="<?php echo($_assets)?>global/plugins/bootstrap-editable/inputs-ext/address/address.js"></script>
<script type="text/javascript" src="<?php echo($_assets)?>global/plugins/bootstrap-editable/inputs-ext/wysihtml5/wysihtml5.js"></script>
<!-- END X-EDITABLE PLUGIN -->

<!-- BEGIN PLUGINS USED BY  -->
<script type="text/javascript" src="<?php echo($_assets)?>global/plugins/datatables/media/js/jquery.dataTables.min.js"></script>
<script type="text/javascript" src="<?php echo($_assets)?>global/plugins/datatables/plugins/bootstrap/dataTables.bootstrap.js"></script>
<!-- END  PLUGIN -->

<script src="<?php echo($_assets)?>global/plugins/morris/morris.min.js" type="text/javascript"></script>
<script src="<?php echo($_assets)?>global/plugins/morris/raphael-min.js" type="text/javascript"></script>
<script src="<?php echo($_assets)?>global/plugins/jquery.sparkline.min.js" type="text/javascript"></script>

<!-- BEGIN PAGE LEVEL PLUGINS -->

<script type="text/javascript" src="<?php echo($_assets)?>global/plugins/bootstrap-toastr/toastr.min.js"></script>
<!-- <script type="text/javascript" src="<?php echo($_assets)?>global/plugins/fullcalendar/fullcalendar.min.js"></script> -->
<script type="text/javascript" src="<?php echo($_assets)?>global/plugins/bootstrap-fileinput/bootstrap-fileinput.js"></script>
<script type="text/javascript" src="<?php echo($_assets)?>global/plugins/bootbox/bootbox.min.js"></script>
<script type="text/javascript" src="<?php echo($_assets)?>global/plugins/jquery.blockui.min.js"></script>
<script type="text/javascript" src="<?php echo($_assets)?>global/plugins/jquery-tags-input/jquery.tagsinput.min.js"></script>
<script type="text/javascript" src="<?php echo($_assets)?>global/plugins/bootstrap-select/bootstrap-select.min.js"></script>
<script type="text/javascript" src="<?php echo($_assets)?>global/plugins/jquery-multi-select/js/jquery.multi-select.js"></script>
<script type="text/javascript" src="<?php echo($_assets)?>global/plugins/jquery.disableAutoFill-master/src/jquery.disableAutoFill.min.js"></script>

<script src="<?php echo($_assets)?>global/plugins/bootstrap-markdown/lib/markdown.js" type="text/javascript"></script>
<script src="<?php echo($_assets)?>global/plugins/bootstrap-markdown/js/bootstrap-markdown.js" type="text/javascript"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/jquery.validate.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/additional-methods.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-searcher/0.3.0/jquery.searcher.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/encoding-japanese/2.0.0/encoding.min.js"></script>


<?php if ($_js == 'talknew.js') { ?>
	<!-- summernote-0.8.20 -->
	<script src="<?php echo($_assets)?>global/plugins/bootstrap-summernote-0.8.20/summernote.min.js" type="text/javascript"></script>
	<script src="<?php echo($_assets)?>global/plugins/bootstrap-summernote-0.8.20/lang/summernote-<?php echo $_lang_cd ?>.min.js" type="text/javascript"></script>
	<!-- summernote-image-attributes -->
	<script src="<?php echo($_assets)?>global/plugins/bootstrap-summernote-image-attributes/summernote-image-attributes.js" type="text/javascript"></script>
	<script src="<?php echo($_assets)?>global/plugins/bootstrap-summernote-image-attributes/lang/ja-JP.js" type="text/javascript"></script>
<?php } else { ?>
	<script src="/assets/global/plugins/bootstrap-summernote/summernote.js" type="text/javascript"></script>
	<!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/summernote/0.8.12/lang/summernote-<?php echo $_lang_cd ?>.js" type="text/javascript"></script> -->
	<script src="<?php echo($_assets)?>global/plugins/bootstrap-summernote-0.8.20/lang/summernote-<?php echo $_lang_cd ?>.js" type="text/javascript"></script>
<?php }?>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.qrcode/1.0/jquery.qrcode.min.js"></script> 
<!-- END PAGE LEVEL PLUGINS -->

<script src="<?php echo($_assets)?>global/scripts/metronic.js" type="text/javascript"></script>
<script src="<?php echo($_assets)?>admin/layout4/scripts/layout.js" type="text/javascript"></script>
<script src="<?php echo($_assets)?>admin/layout4/scripts/demo.js" type="text/javascript"></script>
<script src="<?php echo($_assets)?>admin/pages/scripts/form-editable.js" type="text/javascript"></script>
<script src="<?php echo($_assets)?>admin/pages/scripts/table-managed.js" type="text/javascript"></script>
<script src="<?php echo($_assets)?>admin/pages/scripts/index3.js" type="text/javascript"></script>
<script src="<?php echo($_assets)?>admin/pages/scripts/components-pickers.js" type="text/javascript"></script>
<script src="<?php echo($_assets)?>admin/pages/scripts/ui-toastr.js" type="text/javascript"></script>
<script src="<?php echo($_assets)?>admin/pages/scripts/calendar.js" type="text/javascript"></script>

<script type="text/javascript" src="/assets/admin/layout4/scripts/layout.js"></script>

<script src="/assets/common/talkappi-components/js/locales/talkappi-components-<?php echo $_lang_cd ?>.js"></script>
<script src="/assets/common/talkappi-components/js/talkappi-components.js"></script>
<script src="/assets/common/talkappi-file-upload/js/locales/talkappi-file-upload-<?php echo $_lang_cd ?>.js"></script>
<script src="/assets/common/talkappi-file-upload/js/talkappi-file-upload.js"></script>
<script src="/assets/common/talkappi-modal/js/locales/talkappi-modal-<?php echo $_lang_cd ?>.js"></script>
<script src="/assets/common/talkappi-modal/js/helper.js"></script>
<script src="/assets/common/talkappi-modal/js/talkappi-modal.js"></script>
<script src="/assets/common/talkappi-datetime-select/js/locales/talkappi-datetime-select-<?php echo $_lang_cd ?>.js"></script>
<script src="/assets/common/talkappi-datetime-select/js/talkappi-datetime-select.js"></script>
<script src="/assets/common/talkappi-section/js/locales/talkappi-section-<?php echo $_lang_cd ?>.js"></script>
<script src="/assets/common/talkappi-section/js/talkappi-section.js"></script>
<script src="/assets/common/talkappi-schedule-section/js/locales/talkappi-schedule-section-<?php echo $_lang_cd ?>.js"></script>
<script src="/assets/common/talkappi-schedule-section/js/talkappi-schedule-section.js"></script>
<script src="/assets/common/talkappi-category-select/js/locales/talkappi-category-select-<?php echo $_lang_cd ?>.js"></script>
<script src="/assets/common/talkappi-category-select/js/talkappi-category-select.js"></script>
<script src="/assets/common/talkappi-skill-select/js/locales/talkappi-skill-select-<?php echo $_lang_cd ?>.js"></script>
<script src="/assets/common/talkappi-skill-select/js/talkappi-skill-select.js"></script>
<script src="/assets/common/talkappi-preview/js/locales/talkappi-preview-<?php echo $_lang_cd ?>.js"></script>
<script src="/assets/common/talkappi-preview/js/talkappi-preview.js"></script>
<script src="/assets/common/talkappi-very-preview/js/locales/talkappi-very-preview-<?php echo $_lang_cd ?>.js"></script>
<script src="/assets/common/talkappi-very-preview/js/talkappi-very-preview.js"></script>
<script src="/assets/common/talkappi-entry/js/talkappi-entry.js"></script>
<script src="/assets/common/talkappi-entry-selection/js/talkappi-entry-selection.js"></script>
<script src="/assets/common/talkappi-jsoncheck/js/locale/talkappi-jsoncheck-<?php echo $_lang_cd ?>.js"></script>
<script src="/assets/common/talkappi-jsoncheck/js/talkappi-jsoncheck.js"></script>
<script src="/assets/common/talkappi-alert-mail-setting/js/locales/<?php echo $_lang_cd ?>.js"></script>
<script src="/assets/common/talkappi-alert-mail-setting/js/talkappi-alert-mail-setting.js"></script>
<script src="/assets/common/talkappi-policy/js/locales/talkappi-policy-<?php echo $_lang_cd ?>.js"></script>
<script src="/assets/common/talkappi-policy/js/talkappi-policy.js"></script>

<script src="/assets/common/message/locales/admin-<?php echo $_lang_cd ?>.js"></script>

<script src="/assets/common/talkmgr.js"></script>
<script src="/assets/admin/admin.js"></script>

<?php
$js_path = 'admin'; 
if ($_path != 'admin') {
	if ($_path == 'adminticket') {
		$js_path = 'admin/survey';
	} else if ($_path == 'adminorder') {
		$js_path = 'admin/inquiry';
	}  else {
		$js_path = 'admin/' . str_replace('admin', '', $_path);
	}
}
?>
<script src="/assets/<?php echo $js_path ?>/<?php echo($_js);?>"></script>
<?php if ($_js == 'botlinemenu.js') { ?>
	<script src="/assets/admin/skillbox2.js"></script>
<?php } else {?>
	<script src="/assets/admin/skillbox.js"></script>
	<script src="/assets/admin/classbox.js"></script>
<?php } ?>
<script src="/assets/admin/talkbox.js"></script>
<script src="/assets/admin/jsonbox.js"></script>

<?php if ($_js == 'productdesc.js') { ?>
	<script src="/assets/admin/difflib.js"></script>
	<script src="/assets/admin/diffview.js"></script>
<?php }?>
<?php if ($_js == 'order.js') { ?>
	<script src='https://unpkg.com/vue@2.6.0/dist/vue.js'></script>
	<script src='https://unpkg.com/v-calendar@1.0.0/lib/v-calendar.umd.min.js'></script>
	<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.24.0/moment.min.js" defer></script>
<?php }?>
<?php if ($_js == 'maximumorder.js') { ?>
<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.12.1/jquery-ui.min.js"></script>
<script src="/assets/inquiry/js/jqueryui-datepicker-i18n/datepicker-ja.js"></script>
<?php }?>
<script src="/assets/global/plugins/month-picker/js/MonthPicker.min.js"></script>
<?php if (!strstr($_SERVER['REQUEST_URI'], "/adminvery/reception_operation") && !strstr($_SERVER['REQUEST_URI'], "/admin/congestion_user") && $_user->role_cd != '73' && $_user->role_cd != '74') {?>
	<script src='https://bot.talkappi.com/assets/talkappi/talkappi.js?fid=talkappi_cs&def_skill=[{"skill":"SET_ADMIN_MEMBER_INFO","params":{"user_id":<?php echo $_user->user_id?>,"member_id":"<?php echo $_admin_member ?>"}}]'></script>
	<script>
		$(function() {
			TalkappiApp.loaded(function() {
				if (window.self !== window.top) {
					$('.talkappibot').hide();
				}
			});
		});
	</script>
<?php }?>
<script src="/assets/common/react/components/blocks/passkeymodal.bundle.js"></script>
<!-- END JAVASCRIPTS -->
</body>
<!-- END BODY -->
</html>
