<!DOCTYPE html>
<!--[if IE 8]>          <html class="ie ie8"> <![endif]-->
<!--[if IE 9]>          <html class="ie ie9"> <![endif]-->
<!--[if gt IE 9]><!-->  <html> <!--<![endif]-->
<head>
    <!-- Meta Tags -->
    <meta charset="utf-8">
    <meta name="keywords" content="HTML5 Template" />
    <meta name="description" content="Travelo - Travel, Tour Booking HTML5 Template">
    <meta name="author" content="SoapTheme">

    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fiteee</title>
	<link rel="shortcut icon" href="favicon.ico" type="image/x-icon">
    
    <!-- Theme Styles -->
    <link rel="stylesheet" href="/html/css/bootstrap.min.css">
    <link rel="stylesheet" href="/html/css/font-awesome.min.css">
    <link href='http://fonts.googleapis.com/css?family=Lato:300,400,700' rel='stylesheet' type='text/css'>
    <link rel="stylesheet" href="/html/css/animate.min.css">
    
    <!-- Current Page Styles -->
    <link rel="stylesheet" type="text/css" href="/html/components/revolution_slider/css/settings.css" media="screen" />
    <link rel="stylesheet" type="text/css" href="/html/components/revolution_slider/css/style.css" media="screen" />
    <link rel="stylesheet" type="text/css" href="/html/components/jquery.bxslider/jquery.bxslider.css" media="screen" />
    <link rel="stylesheet" type="text/css" href="/html/components/flexslider/flexslider.css" media="screen" />
    
    <!-- Main Style -->
    <link id="main-style" rel="stylesheet" href="/html/css/style.css">
    
    <!-- Updated Styles -->
    <link rel="stylesheet" href="/html/css/updates.css">

    <!-- Custom Styles -->
    <link rel="stylesheet" href="/html/css/custom.css">
    
    <!-- Responsive Styles -->
    <link rel="stylesheet" href="/html/css/responsive.css">
    
    <!-- CSS for IE -->
    <!--[if lte IE 9]>
        <link rel="stylesheet" type="text/css" href="/html/css/ie.css" />
    <![endif]-->
    
    
    <!-- HTML5 shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
      <script type='text/javascript' src="http://html5shiv.googlecode.com/svn/trunk/html5.js"></script>
      <script type='text/javascript' src="http://cdnjs.cloudflare.com/ajax/libs/respond.js/1.4.2/respond.js"></script>
    <![endif]-->

</head>
<body>
    <div id="page-wrapper">
        <header id="header" class="navbar-static-top">
            <div class="topnav hidden-xs">
                <div class="container">
                    <ul class="quick-menu pull-left">
                        <li><a href="/member">My Account</a></li>
                        <li class="ribbon">
                            <a href="#"><?php echo $_languages[$lang_cd] ?></a>
                            <ul class="menu mini">
                            <?php foreach ($_languages as $key=>$value) { ?>
                            	<li <?php echo (($key==$lang_cd)?'class="active"' : '') ?>> <a class="lang" href="javascript:void(0);" title="<?php echo $key ?>"><?php echo $value ?></a></li>
                            <?php }?>
                            </ul>
                        </li>
                    </ul>
                    <ul class="quick-menu pull-right">
                    <?php if ($_member == NULL) { ?>
                        <li><a href="#travelo-login" class="soap-popupbox" id='login_menu'>LOGIN</a></li>
                        <li id='signup_menu'><a href="#travelo-signup" class="soap-popupbox">SIGNUP</a></li>
                    <?php } else { ?>
                    	<li><a href="javascript:void(0);" id='logout_menu'>LOGOUT</a></li>
                    <?php }?>
                        <li class="ribbon currency">
                            <a href="#" title=""><?php echo $currency_cd ?></a>
                            <ul class="menu mini">
                            <?php foreach ($_currencies as $key=>$value) { ?>
                            	<li <?php echo (($value==$currency_cd)?'class="active"' : '') ?>> <a class="currency" href="javascript:void(0);" title="<?php echo $value ?>"><?php echo $value ?></a></li>
                            <?php }?>
                            </ul>
                        </li>
                    </ul>
                </div>
            </div>
            
            <div class="main-header">
                
                <a href="#mobile-menu-01" data-toggle="collapse" class="mobile-menu-toggle">
                    Mobile Menu Toggle
                </a>

                <div class="container">
                    <h1 class="logo navbar-brand">
                        <a href="/" title="Travelo - home">
                            <img src="/html/images/logo.png" alt="Travelo HTML5 Template" />
                        </a>
                    </h1>

                    <nav id="main-menu" role="navigation">
                        <ul class="menu">
                            <li class="menu-item-has-children">
                                <a href="/"><?php echo __('menu.home') ?></a>
                            </li>
                            <?php foreach ($_classes as $key=>$value) { ?>
                            <li class="menu-item-has-children">
                                <a href="/list?id=<?php echo($key) ?>"><?php echo($value) ?></a>
                                <ul>
                                    <li><a href="/list?id=<?php echo($key) ?>"><?php echo($value) ?></a></li>
                                </ul>
                            </li>
                            <?php } ?>
                        </ul>
                    </nav>
                                        
                </div>
                <nav id="mobile-menu-01" class="mobile-menu collapse">
                    <ul id="mobile-primary-menu" class="menu">
                        <li class="menu-item-has-children">
                            <a href="index.html">Home</a>
                            <ul>
                                <li><a href="index.html">Home Layout 1</a></li>
                            </ul>
                        </li>
                        <?php foreach ($_classes as $key=>$value) { ?>
                        <li class="menu-item-has-children">
                            <a href="/list?id=<?php echo($key) ?>"><?php echo($value) ?></a>
                            <ul>
                                <li><a href="/list?id=<?php echo($key) ?>"><?php echo($value) ?></a></li>
                            </ul>
                        </li>
                        <?php } ?>
                    </ul>
                </nav>            
            </div>
          
            <div id="travelo-signup" class="travelo-signup-box travelo-box">
                <div class="login-social">
                    <a href="#" class="button login-facebook"><i class="soap-icon-facebook"></i>Login with Facebook</a>
                    <a href="#" class="button login-googleplus"><i class="soap-icon-googleplus"></i>Login with Google+</a>
                </div>
                <div class="seperator"><label>OR</label></div>
                <div class="simple-signup">
                    <div class="text-center signup-email-section">
                        <a href="#" class="signup-email"><i class="soap-icon-letter"></i>Sign up with Email</a>
                    </div>
                    <p class="description">By signing up, I agree to Travelo's Terms of Service, Privacy Policy, Guest Refund olicy, and Host Guarantee Terms.</p>
                </div>
                <div class="email-signup">
                    <form id="registForm">
                        <div class="form-group">
                            <input type="text" id="name" class="input-text full-width" placeholder="nick name">
                        </div>
                        <div class="form-group">
                            <select id="country"  class="full-width">
                            <?php foreach ($_countries as $key=>$value) { ?>
								<option value="<?php echo $key ?>"><?php echo $value?></option>
							<?php } ?>
                            </select>
                        </div>
                        <div class="form-group">
                            <input type="text" id="email" class="input-text full-width" placeholder="email address">
                        </div>
                        <div class="form-group">
                            <input type="password" id="password" class="input-text full-width" placeholder="password">
                        </div>
                        <div class="form-group">
                            <input type="password" id="rpassword" class="input-text full-width" placeholder="confirm password">
                        </div>
                        <div class="form-group">
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox"> Tell me about Travelo news
                                </label>
                            </div>
                        </div>
                        <div class="form-group">
                            <p class="description">By signing up, I agree to Travelo's Terms of Service, Privacy Policy, Guest Refund Policy, and Host Guarantee Terms.</p>
                        </div>
                        <a href="javascript:void(0);" id="regist" class="button full-width btn-medium">SIGNUP</a>
                    </form>
                </div>
                <div class="seperator"></div>
                <p>Already a Travelo member? <a href="#travelo-login" class="goto-login soap-popupbox">Login</a></p>
            </div>
            <div id="travelo-login" class="travelo-login-box travelo-box">
                <div class="login-social">
                    <a href="#" class="button login-facebook"><i class="soap-icon-facebook"></i>Login with Facebook</a>
                    <a href="#" class="button login-googleplus"><i class="soap-icon-googleplus"></i>Login with Google+</a>
                </div>
                <div class="seperator"><label>OR</label></div>
                <form id="loginForm"  method="POST" action="/ajax/login">
                    <div class="form-group">
                        <input type="text" id="login_email" name="email" class="input-text full-width" placeholder="email address">
                    </div>
                    <div class="form-group">
                        <input type="password" id="login_password" name="password" class="input-text full-width" placeholder="password">
                    </div>
                    <div class="form-group">
                        <a href="#" class="forgot-password pull-right">Forgot password?</a>
                        <div class="checkbox checkbox-inline">
                            <label>
                                <input type="checkbox"> Remember me
                            </label>
                        </div>
                    </div>
                    <a href="javascript:void(0);" id="login" class="button full-width btn-medium">Login</a>
                </form>
                <div class="seperator"></div>
                <p>Don't have an account? <a href="#travelo-signup" class="goto-signup soap-popupbox">Sign up</a></p>
            </div>
        </header>
        
		<?php if ($_slider_show) { ?>        
        <div id="slideshow">
            <div class="fullwidthbanner-container">
                <div class="revolution-slider rev_slider" style="height: 0; overflow: hidden;">
                    <ul>    <!-- SLIDE  -->
                        <!-- Slide1 -->
                        <li data-transition="zoomin" data-slotamount="7" data-masterspeed="1500">
                            <!-- MAIN IMAGE -->
                            <img src="http://placehold.it/2080x646" alt="">
                        </li>
                        
                        <!-- Slide2 -->
                        <li data-transition="zoomout" data-slotamount="7" data-masterspeed="1500">
                            <!-- MAIN IMAGE -->
                            <img src="http://placehold.it/2080x646" alt="">
                        </li>
                        
                        <!-- Slide3 -->
                        <li data-transition="slidedown" data-slotamount="7" data-masterspeed="1500">
                            <!-- MAIN IMAGE -->
                            <img src="http://placehold.it/2080x646" alt="">
                        </li>
                    </ul>
                </div>
            </div>
        </div>
		<?php }  else {?>

        <div class="page-title-container">
            <div class="container">
            <?php if ($_class_cd == null) { ?>   
                <div class="page-title pull-left">
                    <h2 class="entry-title"><?php echo $_page_title ?></h2>
                </div>
            <?php }  else {?>
                <div class="page-title pull-left">
                    <h2 class="entry-title"><?php echo $_classes[$_class_cd] ?> Search Results</h2>
                </div>
                <ul class="breadcrumbs pull-right">
                    <li><a href="#">HOME</a></li>
                    <li class="active"><?php echo $_classes[$_class_cd] ?> Search Results</li>
                </ul>
             <?php } ?>
            </div>
        </div>
        <?php } ?>
        
        <section id="content">
        
		<?php echo $content ?>
		 
        </section>
        
        <footer id="footer">
            <div class="footer-wrapper">
                <div class="container">
                    <div class="row">
                        <div class="col-sm-6 col-md-3">
                            <h2>Discover</h2>
                            <ul class="discover triangle hover row">
                                <li class="col-xs-6"><a href="#">Safety</a></li>
                                <li class="col-xs-6"><a href="#">About</a></li>
                                <li class="col-xs-6"><a href="#">Travelo Picks</a></li>
                                <li class="col-xs-6"><a href="#">Latest Jobs</a></li>
                                <li class="active col-xs-6"><a href="#">Mobile</a></li>
                                <li class="col-xs-6"><a href="#">Press Releases</a></li>
                                <li class="col-xs-6"><a href="#">Why Host</a></li>
                                <li class="col-xs-6"><a href="#">Blog Posts</a></li>
                                <li class="col-xs-6"><a href="#">Social Connect</a></li>
                                <li class="col-xs-6"><a href="#">Help Topics</a></li>
                                <li class="col-xs-6"><a href="#">Site Map</a></li>
                                <li class="col-xs-6"><a href="#">Policies</a></li>
                            </ul>
                        </div>
                        <div class="col-sm-6 col-md-3">
                            <h2>Travel News</h2>
                            <ul class="travel-news">
                                <li>
                                    <div class="thumb">
                                        <a href="#">
                                            <img src="http://placehold.it/63x63" alt="" width="63" height="63" />
                                        </a>
                                    </div>
                                    <div class="description">
                                        <h5 class="s-title"><a href="#">Amazing Places</a></h5>
                                        <p>Purus ac congue arcu cursus ut vitae pulvinar massaidp.</p>
                                        <span class="date">25 Sep, 2013</span>
                                    </div>
                                </li>
                                <li>
                                    <div class="thumb">
                                        <a href="#">
                                            <img src="http://placehold.it/63x63" alt="" width="63" height="63" />
                                        </a>
                                    </div>
                                    <div class="description">
                                        <h5 class="s-title"><a href="#">Travel Insurance</a></h5>
                                        <p>Purus ac congue arcu cursus ut vitae pulvinar massaidp.</p>
                                        <span class="date">24 Sep, 2013</span>
                                    </div>
                                </li>
                            </ul>
                        </div>
                        <div class="col-sm-6 col-md-3">
                            <h2>Mailing List</h2>
                            <p>Sign up for our mailing list to get latest updates and offers.</p>
                            <br />
                            <div class="icon-check">
                                <input type="text" class="input-text full-width" placeholder="your email" />
                            </div>
                            <br />
                            <span>We respect your privacy</span>
                        </div>
                        <div class="col-sm-6 col-md-3">
                            <h2>About Travelo</h2>
                            <p>Nunc cursus libero purus ac congue arcu cursus ut sed vitae pulvinar massaidp nequetiam lore elerisque.</p>
                            <br />
                            <address class="contact-details">
                                <span class="contact-phone"><i class="soap-icon-phone"></i> 1-800-123-HELLO</span>
                                <br />
                                <a href="#" class="contact-email"><EMAIL></a>
                            </address>
                            <ul class="social-icons clearfix">
                                <li class="twitter"><a title="twitter" href="#" data-toggle="tooltip"><i class="soap-icon-twitter"></i></a></li>
                                <li class="googleplus"><a title="googleplus" href="#" data-toggle="tooltip"><i class="soap-icon-googleplus"></i></a></li>
                                <li class="facebook"><a title="facebook" href="#" data-toggle="tooltip"><i class="soap-icon-facebook"></i></a></li>
                                <li class="linkedin"><a title="linkedin" href="#" data-toggle="tooltip"><i class="soap-icon-linkedin"></i></a></li>
                                <li class="vimeo"><a title="vimeo" href="#" data-toggle="tooltip"><i class="soap-icon-vimeo"></i></a></li>
                                <li class="dribble"><a title="dribble" href="#" data-toggle="tooltip"><i class="soap-icon-dribble"></i></a></li>
                                <li class="flickr"><a title="flickr" href="#" data-toggle="tooltip"><i class="soap-icon-flickr"></i></a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bottom gray-area">
                <div class="container">
                    <div class="logo pull-left">
                        <a href="index.html" title="Travelo - home">
                            <img src="/html/images/logo.png" alt="Travelo HTML5 Template" />
                        </a>
                    </div>
                    <div class="pull-right">
                        <a id="back-to-top" href="#" class="animated" data-animation-type="bounce"><i class="soap-icon-longarrow-up circle"></i></a>
                    </div>
                    <div class="copyright pull-right">
                        <p>&copy; 2014 Travelo</p>
                    </div>
                </div>
            </div>
        </footer>
    </div>

    <!-- Javascript -->
    <script type="text/javascript" src="/html/js/jquery-1.11.1.min.js"></script>
    <script type="text/javascript" src="/html/js/jquery.noconflict.js"></script>
    <script type="text/javascript" src="/html/js/modernizr.2.7.1.min.js"></script>
    <script type="text/javascript" src="/html/js/jquery-migrate-1.2.1.min.js"></script>
    <script type="text/javascript" src="/html/js/jquery.placeholder.js"></script>
    <script type="text/javascript" src="/html/js/jquery-ui.1.10.4.min.js"></script>
    
    <!-- Twitter Bootstrap -->
    <script type="text/javascript" src="/html/js/bootstrap.min.js"></script>
    
    <!-- load revolution slider scripts -->
    <script type="text/javascript" src="/html/components/revolution_slider/js/jquery.themepunch.tools.min.js"></script>
    <script type="text/javascript" src="/html/components/revolution_slider/js/jquery.themepunch.revolution.min.js"></script>
    
    <!-- load BXSlider scripts -->
    <script type="text/javascript" src="/html/components/jquery.bxslider/jquery.bxslider.min.js"></script>

    <!-- Flex Slider -->
    <script type="text/javascript" src="/html/components/flexslider/jquery.flexslider.js"></script>

    <!-- parallax -->
    <script type="text/javascript" src="/html/js/jquery.stellar.min.js"></script>
    
    <!-- parallax -->
    <script type="text/javascript" src="/html/js/jquery.stellar.min.js"></script>

    <!-- waypoint -->
    <script type="text/javascript" src="/html/js/waypoints.min.js"></script>

    <!-- load page Javascript -->
    <script type="text/javascript" src="/html/js/theme-scripts.js"></script>
    <script type="text/javascript" src="/html/js/scripts.js"></script>
    
	<?php foreach ($scripts as $file) echo HTML::script($file), PHP_EOL ?>
    
    <script type="text/javascript">
	    tjq(function(){
		    tjq('.lang').click(function(){
				tjq.ajax({
					type : "GET",
					url : "/ajax/language",
					contentType : "application/json; charset=utf-8",
					dataType : "json",
					data : "id=" + tjq(this).attr('title'),
					error : function(XMLHttpRequest, textStatus, errorThrown) {
					},
					success : function(data) {
						if (data.error) {
						} else {
							location.reload();
						}
					}
				}); 
		    });
		    tjq('.currency').click(function(){
				tjq.ajax({
					type : "GET",
					url : "/ajax/currency",
					contentType : "application/json; charset=utf-8",
					dataType : "json",
					data : "id=" + tjq(this).attr('title'),
					error : function(XMLHttpRequest, textStatus, errorThrown) {
					},
					success : function(data) {
						if (data.error) {
						} else {
							location.reload();
						}
					}
				}); 
		    });

		    tjq('#logout_menu').click(function(){
				tjq.ajax({
					type : "GET",
					url : "/ajax/logout",
					contentType : "application/json; charset=utf-8",
					dataType : "json",
					data : "",
					error : function(XMLHttpRequest, textStatus, errorThrown) {
					},
					success : function(data) {
						if (data.error) {
						} else {
							location.reload();
						}
					}
				}); 
		    });
		    
	    	tjq('#login').click(function(){
	    		//tjq(this).unbind("click");
	    	    var username = tjq('#login_email').attr('value'); 
	    	    var password = tjq('#login_password').attr('value');
	    	    if (username && password) {
	    	        tjq.ajax({
	    	          type: "GET",
	    	          url: "/ajax/login", 
	    	          contentType: "application/json; charset=utf-8",
	    	          dataType: "json",
	    	          data: "email=" + username + "&password=" + password,
	    	          error: function(XMLHttpRequest, textStatus, errorThrown) { 
	    	        	  alert('ajax error');
	    	          }, 
	    	          success: function(data){
	    	            if (data.error) { 
	    	            }
	    	            else {
		    	            if (data == 'error') {
		    		    		alert('login error.');
		    	            }
		    	            else {
	    	            		location.reload();
		    	            }
	    	            	/*
	    	            	tjq("body").removeClass("overlay-open");
	    	            	tjq("html").css("overflow", "");
	    	            	tjq("html").css("margin-right", "");
	    	            	tjq('#travelo-login').hide();
	    	            	tjq('signup_menu').remove();
	    	            	tjq('#login_menu').html('LOGOUT');
	    	            	tjq('#login_menu').attr('id', 'logout_menu');
	    	            	*/
	    	            }
	    	          }
	    	        }); 
	    	      }
	    	});

	    	tjq('#regist').click(function(){
	    		//tjq(this).unbind("click");
	    	    var email = tjq('#email').attr('value'); 
	    	    var password = tjq('#password').attr('value');
	    	    var name = tjq('#name').attr('value');
	    	    var country = tjq('#country').attr('value');
	    	    if (email && password && name && country) {
	    	        tjq.ajax({
	    	          type: "GET",
	    	          url: "/ajax/regist", 
	    	          contentType: "application/json; charset=utf-8",
	    	          dataType: "json",
	    	          data: "email=" + email + "&password=" + password + "&name=" + name + "&country=" + country,
	    	          error: function(XMLHttpRequest, textStatus, errorThrown) { 
	    	        	  alert(textStatus);
	    	          }, 
	    	          success: function(data){
	    	            if (data.error) { 
	    		    		alert('regist error.');
	    	            }
	    	            else {
		    	            if (data == 'error') {
		    		    		alert('regist error.');
		    	            }
		    	            else {
	    	            		location.reload();
		    	            }
	    	            }
	    	          }
	    	        }); 
	    	      }
	    	});
	    });
    
        tjq(document).ready(function() {
            tjq('.revolution-slider').revolution(
            {
                sliderType:"standard",
				sliderLayout:"auto",
				dottedOverlay:"none",
				delay:9000,
				navigation: {
					keyboardNavigation:"off",
					keyboard_direction: "horizontal",
					mouseScrollNavigation:"off",
					mouseScrollReverse:"default",
					onHoverStop:"on",
					touch:{
						touchenabled:"on",
						swipe_threshold: 75,
						swipe_min_touches: 1,
						swipe_direction: "horizontal",
						drag_block_vertical: false
					}
					,
					arrows: {
						style:"default",
						enable:true,
						hide_onmobile:false,
						hide_onleave:false,
						tmp:'',
						left: {
							h_align:"left",
							v_align:"center",
							h_offset:20,
							v_offset:0
						},
						right: {
							h_align:"right",
							v_align:"center",
							h_offset:20,
							v_offset:0
						}
					}
				},
				visibilityLevels:[1240,1024,778,480],
				gridwidth:1170,
				gridheight:646,
				lazyType:"none",
				shadow:0,
				spinner:"spinner4",
				stopLoop:"off",
				stopAfterLoops:-1,
				stopAtSlide:-1,
				shuffle:"off",
				autoHeight:"off",
				hideThumbsOnMobile:"off",
				hideSliderAtLimit:0,
				hideCaptionAtLimit:0,
				hideAllCaptionAtLilmit:0,
				debugMode:false,
				fallbacks: {
					simplifyAll:"off",
					nextSlideOnWindowFocus:"off",
					disableFocusListener:false,
				}
            });
        });
    </script>

</body>
</html>

