<script type="text/javascript">
</script>
<input type="hidden" name="act" id="act" value="" />

<!-- トップタブ -->
<nav class="top-nav">
    <ul>
        <li class="active"><a href="#">基本設定</a> </li>
        <li><a href="#">詳細設定</a></li>
        <li><a href="#">発行・利用状況</a></li>
        <li><a href="#">一覧に戻る</a></li>
    </ul>
</nav>
<!-- メインエリア -->
<div class="content-container white border">
    <nav class="line-tab">
        <ul class="">
            <li class="active"><a href="#">日本語</a></li>
            <li><a href="#">英語</a></li>
        </ul>
    </nav>

    <div class="form-body">
        <div class="section-container bottom-line">
            <h2>基本情報設定</h2>
            <div class="form-group">
                <label class="control-label col-md-2">アンケート名</label>
                <div class="col-md-5">
                    <div class="readonly-input flex-x-between">
                        <span>talkappi demoアンケート</span>
                        <a class="" href="#">編集</a>
                    </div>
                </div>
            </div>
        </div>
        <div class="section-container bottom-line">
            <h2>回答後のメール設定</h2>
            <div class="form-group">
                <label class="control-label col-md-2">アンケート名</label>
                <div class="col-md-5">
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-2">管理者送信</label>
                <div class="col-md-5">
                    <ul class="btn round light-blue pointer js-selected-users" style="list-style:none;"></ul>
                    <div class="btn round light-blue pointer"><img src="./../assets/admin/css/img/icon-add.svg">送信先</div>
                </div>
                <input type="hidden" name="mail_users" value="1422,1470">
            </div>
        </div>
        <div class="section-container">
            <h2>回答履歴</h2>
            <nav class="button-tab">
                <ul class="">
                    <li><a href="#">前の月</a></li>
                    <li class="active"><a href="#">今月</a></li>
                    <li><a href="#">次の月</a></li>
                </ul>
            </nav>
            <div class="flex-x-between">
                <div>履歴一覧</div>
                <span class="btn-smaller btn-blue">
                    <span class="icon-add-white"></span>新規作成
                </span>
            </div>
            <div style="position: relative;margin-top:10px;">
                <table class="table table-striped table-bordered table-hover js-data-table">
                    <div style="display:flex;position: absolute; top: 4px;">
                        <div class="pulldown">
                            <div class="talkappi-pulldown js-answer_type" style="margin-right:10px;" data-name="answer_type" data-value="01" data-source='{"01":"全ての登録状態", "02":"登録済み", "03":"未登録"}'></div>
                        </div>
                        <div class="pulldown">
                            <div class="talkappi-pulldown js-required" data-name="required" data-value="02" data-source='{"01":"全ての登録種類", "02":"必須登録", "03":"任意登録"}'></div>
                        </div>
                    </div>
                    <thead>
                        <tr>
                            <th style="width:180px;">番号</th>
                            <th>タイトル</th>
                            <th>詳細</th>
                            <th style="width:50px;">編集</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="form-actions">
        <div class="row">
            <div class="col-md-offset-2 col-md-9">
                <div class="actions-container">
                    <button type="button" class="action-button page btn-blue js-action-save">保存</button>
                    <a class="action-button page btn-gray js-action-verify" href="javascript:void(0);">検証する</a>
                    <a class="action-button page btn-white js-action-back">一覧に戻る</a>
                    <span class="action-button page btn-red-border icon-only js-action-delete"><span class="icon-delete"></span></span>
                </div>
            </div>
        </div>
    </div>
</div>