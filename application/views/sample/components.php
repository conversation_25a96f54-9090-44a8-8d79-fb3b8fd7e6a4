<div class="content-container">
    <div class="section-container">
        <h4>content-container transparent</h4>
    </div>
    <div class="flex-x-between">
        <div>アンケート一覧</div>
        <span class="btn-smaller btn-blue">
            <span class="icon-add-white"></span>新規作成
        </span>
    </div>
</div>

<div class="content-container light-gray">
    <div class="section-container">
        <h4>content-container light-gray</h4>
    </div>
    <div class="js-section" style="background-color:#FFF;padding:8px;margin-bottom:10px;">
        <div style="display:flex;justify-content:space-between;padding:10px;">
            <div style="display:flex;">
                <p style="font-weight:bold;margin-right:20px;">時間帯設定</p>
                <div class="js-time-group-setting-label" style="display:none;"></div>
            </div>
            <div style="display:flex;">
                <div class="" title="セクションを削除する">
                    <span class="js-section-delete-icon icon-action-section delete"></span>
                </div>
                <div class="" title="セクションを折り畳む">
                    <span class="js-section-toggle-icon icon-action-section toggle"></span>
                </div>
            </div>
        </div>
        <div class="form-body js-section-content">
            <div class="form-group">
                <label class="control-label col-md-2">日付</label>
                <div class="col-md-8">
                    <input type="text" class="talkappi-datepicker js-date" name="start_date" value="2022-03-01" />
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-2">時間</label>
                <div class="col-md-8">
                    <input type="text" class="talkappi-timepicker js-time" name="start_time" value="12:34" />
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-2">期間</label>
                <div class="col-md-8">
                    <div class="talkappi-datepicker-range" data-min-date="2024-07-01" data-max-date="2024-08-01"></div>
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-2">期間(時間付き)</label>
                <div class="col-md-8">
                    <div class="talkappi-datepicker-range" data-time-format="hh:mm"></div>
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-2">Color Picker</label>
                <div class="col-md-5">
                    <div class="talkappi-colorpicker js-color" data-name="mycolor" data-value="#5088a1"></div>
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-2">Color Picker2</label>
                <div class="col-md-5">
                    <div class="talkappi-colorpicker js-color" data-name="mycolor2" data-value="#5088a1"></div>
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-2">各種ラベル</label>
                <div class="col-md-8">
                    <label class="btn round light-gray">灰色</label>
                    <label class="btn round light-red">赤</label>
                    <label class="btn round light-green">緑</label>
                    <label class="btn round light-blue">青</label>
                    <label class="btn round light-yellow">黄</label>
                    <label class="btn round light-purple">紫</label>
                    <label class="btn round light-orange">Orange</label>
                    <label class="btn light-orange">Orange</label>
                    <label class="btn light-blue">青</label>
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-2">情報パネル</label>
                <div class="col-md-3">
                    <div class="small-table-pannel js-memo">
                        <div class="pannel-close js-memo-delete"><span></span></div>
                        2022-02-03 11:46 ActiValues) 戚<br>
                        期間限定でご購入いただけるギフト券をご用意しています。ご宿泊やダイニングにお使いいただけます。
                        クラブマリオットや他の特典とも組み合わせてご利用可能ですので、この機会にぜひご活用ください。
                        また、お申込み前には、利用規約を必ずお読みください。
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-2">ファイルアップロード共通</label>
                <div class="col-md-8">
                    <div class="talkappi-upload js-image" data-name="image_base64" data-type="img" data-label="https://dxxxxlf5ovtpt.cloudfront.net/17/product/17088_ja.jpg" data-url="https://dxxxxlf5ovtpt.cloudfront.net/17/product/17088_ja.jpg" data-max-size="2"></div>
                </div>
            </div>
        </div>
    </div>
</div>
<br />
<nav class="top-nav">
    <ul class="">
        <li class="active"><a href="#">基本設定</a></li>
        <li><a href="#">概要情報設定</a></li>
        <li><a href="#">問合せフォーム作成</a></li>
        <li><a href="#">問合せ回収結果</a></li>
        <li><a href="#">一覧に戻る</a></li>
    </ul>
</nav>
<div class="content-container white border">
    <div class="section-container bottom-line">
        <h4>content-container white</h4>
        <div style="display:flex;">
            <nav class="line-tab">
                <ul class="">
                    <li class="active">タブ１</li>
                    <li>タブ２</li>
                </ul>
            </nav>
            <nav class="line-tab">
                <ul class="">
                    <li class="active"><a href="#">タブ１</a></li>
                    <li><a href="#">タブ２</a></li>
                </ul>
            </nav>
            <nav class="button-tab">
                <ul class="">
                    <li class="active"><a href="#">タブ１</a></li>
                    <li><a href="#">タブ２</a></li>
                </ul>
            </nav>
        </div>
        <!--
        <div class="form-body">
            <div class="form-group">
                <label class="control-label col-md-2 fix-2">一二</label>
                <div class="col-md-5">
                    fix-2
                </div>           
            </div>
            <div class="form-group">
                <label class="control-label col-md-2 fix-4">一二三四</label>
                <div class="col-md-5">
                    fix-4
                </div>           
            </div>
            <div class="form-group">
                <label class="control-label col-md-2 fix-6">一二三四五六</label>
                <div class="col-md-5">
                    fix-6
                </div>           
            </div>
            <div class="form-group">
                <label class="control-label col-md-2 fix-8">一二三四五六七八</label>
                <div class="col-md-5">
                    fix-8
                </div>           
            </div>
            <div class="form-group">
                <label class="control-label col-md-2 fix-10">一二三四五六七八九十</label>
                <div class="col-md-5">
                    fix-10
                </div>           
            </div>
            <div class="form-group">
                <label class="control-label col-md-2 fix-12">一二三四五六七八九十一二</label>
                <div class="col-md-5" style="background-color:grey;">
                    fix-12
                </div>           
            </div>
        </div>
-->
        <div class="form-body">
            <div class="form-group">
                <label class="control-label col-md-2" style="width:120px;">アンケート名</label>
                <div class="col-md-5">
                    <div class="readonly-input flex-x-between">
                        <span>talkappi demoアンケート</span>
                        <a class="" href="#">編集</a>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-2" style="width:120px;">入力ボックス</label>
                <div class="col-md-5">
                    <input type="text" class="form-control talkappi-textinput" data-max-input="20" placeholder="" />
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-2" style="width:120px;">入力エリア</label>
                <div class="col-md-5">
                    <textarea type="text" class="form-control talkappi-textinput" data-max-input="200" placeholder=""></textarea>
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-2" style="width:120px;">プルダウン選択</label>
                <div class="col-md-5">
                    <div class="talkappi-pulldown js-present" data-name="present" data-value="01" data-source='{"01":"ケーキ", "02":"ビル一本", "03":"クーポン"}'></div>
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-2" style="width:120px;">プルダウンMENU</label>
                <div class="col-md-5">
                    <div class="talkappi-pulldown js-menu" data-type="menu" data-action="注文する" data-name="menu" data-value="01" data-source='{"01":"ケーキ", "02":"ビル一本", "03":"クーポン"}'></div>
                </div>           
            </div>
            <div class="form-group">
                <label class="control-label col-md-2" style="width:120px;">プルダウンMENU-NEW</label>
                <div class="col-md-5">
                    <div class="talkappi-menu" data-action="注文する" data-name="menu" data-value="01" data-source='{"01":{"icon":"./../assets/admin/css/img/icon-add.svg","text":"よく使う項目","submenu":{"0101":{"icon":"./../assets/admin/css/img/icon-add.svg","text":"お名前"},"0102":{"icon":"./../assets/admin/css/img/icon-add.svg","text":"住所"}}},"02":{"icon":"./../assets/admin/css/img/icon-add.svg","text":"長文" }, "03":{"text":"短文"}, "04":{"text":"短文","submenu":{"0401":{"icon":"./../assets/admin/css/img/icon-add.svg","text":"生年月日"},"0402":{"icon":"./../assets/admin/css/img/icon-add.svg","text":"電話"}}}}'></div>
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-2" style="width:120px;">スイッチ(Label)</label>
                <div class="col-md-5">
                    <div class="talkappi-switch js-spam" data-name="spam" data-value="1" data-label="チャット"></div>
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-2" style="width:120px;">スイッチ(Label disabled)</label>
                <div class="col-md-5">
                    <div class="talkappi-switch js-spam" disabled=true data-name="spam" data-value="0" data-label="チャット"></div>
                </div>           
            </div>            
            <div class="form-group">
                <label class="control-label col-md-2" style="width:120px;">スイッチ</label>
                <div class="col-md-5">
                    <div class="talkappi-switch" data-value="1"></div>
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-2" style="width:120px;">Radio選択</label>
                <div class="col-md-5">
                    <div class="talkappi-radio js-payment" data-name="payment" data-value="01" data-source='{"01":"現金", "02":"クレジットカード", "03":"QRコード"}'></div>
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-2" style="width:120px;">Checkbox選択</label>
                <div class="col-md-5">
                    <div class="talkappi-checkbox js-language" data-name="language" data-value='["ja","cn"]' data-source='{"ja":"日本語", "cn":"中国語", "en":"英語"}'></div>
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-2" style="width:120px;">Checkbox基準</label>
                <div class="col-md-5">
                    <div class="checkbox-label">
                        <input type="checkbox" name="default_scene_cd" id="default_scene_cd" checked value="1">
                        <label for="default_scene_cd">デフォルト導線にする</label>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-2" style="width:120px;">アップロード</label>
                <div class="col-md-5">
                    <div class="talkappi-upload js-upload" data-name="image" data-url='https://cdn.talkappi.com/35/item/350101_ja.png' data-label="350101_ja.png" data-type='img'></div>
                </div>           
            </div>            
            <div class="form-group">
                <label class="control-label col-md-2" style="width:120px;">アイコンボタン</label>
                <div class="col-md-8 warp-line">
                    <div class="btn round image add js-memo">追加</div>
                    <div class="btn round image edit js-memo">編集</div>
                    <div class="btn round image copy js-memo">コピー</div>
                    <div class="btn round image delete js-memo">削除</div>
                    <div class="btn round image public js-memo">公開</div>
                    <div class="btn round image result js-memo">結果</div>
                    <div class="btn round image detail js-memo">詳細</div>
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-2" style="width:120px;">Summernote</label>
                <div class="col-md-5">
                    <div class="summernote-edit js-summer-sample" data-name="summer_sample" data-value='default setting' title="概要">
                        <input type="text" class="form-control" value='default setting' />
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-2" style="width:120px;">Summernote</label>
                <div class="col-md-5">
                    <div class="summernote-edit js-summer-sample" data-name="summer_sample" data-value='tip position bottom with image upload' title="概要" data-upload-image="1" data-position="bottom">
                        <textarea rows="2" class="form-control" value='tip position bottom with image upload' ></textarea>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-2" style="width:120px;">Summernote Input</label>
                <div class="col-md-5">
                    <div class="summernote-edit js-summer-input" data-edit="summer" data-name="summer_input" data-value='直接入力 with image upload' title="概要" data-upload-image="1" data-position="bottom">
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-2" style="width:120px;">skill</label>
                <div class="col-md-5">
                    <div class="talkappi-skill-select" data-name="myskill" data-value='[]' title="概要"></div>
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-2" style="width:120px;">多言語設定</label>
                <div class="col-md-5">
                    <div class="talkappi-multitext" data-name="myttext" data-language='["ja","en"]' data-value='{"ja":"学生", "en":"student"}' data-max-input="50" title="多言語設定"></div>
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-2" style="width:120px;">グループボタン</label>
                <div class="col-md-8 flex">
                    <div class="action-button section btn-blue js-button-ok">OK</div>
                    <div class="action-button section btn-white js-button-cancel">キャンセル</div>
                    <div class="action-button section btn-gray-black js-button-search">検索</div>
                    <div class="action-button section btn-white js-button-csv"><span class="icon-export"></span>CSV出力</div>
                    <div class="action-button section btn-white js-button-user-prop">ユーザー属性</div>
                    <div class="action-button section btn-white js-button-user-select">ユーザー選択</div>
                    <div class="action-button section btn-white js-button-select-dialog">選択共通</div>
                    <div class="action-button section btn-red-border icon-only js-button-delete">
                        <span class="icon-delete"></span>
                    </div>
                </div>
            </div>
            <!-- メールアドレスの検証 -->
            <div class="form-group">
                <label class="control-label col-md-2" style="width:120px;">メールアドレス</label>
                <div class="col-md-5">
                    <input type="text" class="form-control talkappi-textinput" data-max-input="20" placeholder="" />
                </div>
                <div class="col-md-5">
                    <div class="action-button section btn-white js-verify-email" style="width:80px;">検証</div>
                </div>
            </div>
            <div class="image-action-group js-survey-add">
                <img src="./../assets/admin/css/img/icon-add.svg" width="12" height="12">
                <span>質問を追加する</span>
            </div>
        </div>
    </div>
    <div class="section-container">
        <h2>Bootstrapless</h2>
        <div class="lines-container">
            <div class="basic-label">アンケート名</div>
            <input type="text" name="survey_name" value="" class="text-input-longer">
        </div>
        <div class="lines-container">
            <label class="basic-label">動的追加</label>
            <div class="line-content">
                <div class="btn round image add js-add" data-type="switch">switch</div>
                <div class="btn round image add js-add" data-type="radio">radio</div>
                <div class="btn round image edit js-add" data-type="pulldown">pulldown</div>
                <div class="btn round image edit js-add" data-type="checkbox">checkbox</div>
                <div class="btn round image copy js-add" data-type="datepicker">datepicker</div>
                <div class="btn round image copy js-add" data-type="timepicker">timepicker</div>
                <div class="btn round image delete js-add" data-type="datepickerrange">datepickerrange</div>
                <div class="btn round image delete js-add" data-type="datetimepickerrange">datetimepickerrange</div>
                <div class="btn round image result js-add" data-type="summernote">summernote</div>
                <div class="btn round image result js-add" data-type="category">category</div>
                <div class="btn round image result js-add" data-type="colorpicker">colorpicker</div>
                <div class="btn round image result js-add" data-type="textinput">textinput</div>
                <div class="btn round image light-red result js-add" data-type="entry-txt">entry txt</div>
                <div class="btn round image light-yellow result js-add" data-type="entry-opt">entry opt</div>
            </div>
        </div>
        <div class="lines-container">
            <label class="basic-label">値を取得</label>
            <div class="line-content">
                <div class="btn round image add js-data" data-type="switch">switch</div>
                <div class="btn round image add js-data" data-type="radio">radio</div>
                <div class="btn round image edit js-data" data-type="pulldown">pulldown</div>
                <div class="btn round image edit js-data" data-type="checkbox">checkbox</div>
                <div class="btn round image copy js-data" data-type="datepicker">datepicker</div>
                <div class="btn round image copy js-data" data-type="timepicker">timepicker</div>
                <div class="btn round image delete js-data" data-type="datepickerrange">datepickerrange</div>
                <div class="btn round image delete js-data" data-type="datetimepickerrange">datetimepickerrange</div>
                <div class="btn round image result js-data" data-type="summernote">summernote</div>
                <div class="btn round image result js-data" data-type="category">category</div>
                <div class="btn round image result js-data" data-type="colorpicker">colorpicker</div>
                <div class="btn round image result js-data" data-type="textinput">textinput</div>
                <div class="btn round image light-red result js-entry-data" data-type="entry-txt">entry</div>
                <div class="btn round image light-red result js-entry-toggle">toggle entry</div>
            </div>
        </div>
        <h2>動的追加結果</h2>
    </div>
    <div class="form-actions">
        <div class="row">
            <div class="col-md-offset-2 col-md-9">
                <div class="actions-container">
                    <button type="button" class="action-button page btn-blue js-action-save">保存</button>
                    <a class="action-button page btn-gray js-action-verify" href="javascript:void(0);">検証する</a>
                    <a class="action-button page btn-white js-action-back">一覧に戻る</a>
                    <span class="action-button page btn-red-border icon-only js-action-delete"><span class="icon-delete"></span></span>
                </div>
            </div>
        </div>
    </div>
    <div class="section-container">
        <h2>React Components</h2>
        <div class="lines-container">
            <div class="basic-label">Iframe Popup</div>
            <div class="btn round image add js-iframe-popup" data-url="http://localhost/admin/msgnew?id=170175&lang_cd=ja">iframe popup(カスタマイズコンテンツ)</div>
        </div>
    </div>
</div>

<script src="/assets/common/react/components/atoms/IframePopup.bundle.js"></script>